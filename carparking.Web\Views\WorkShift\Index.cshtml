﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>交班记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?1" rel="stylesheet" />
    <style>
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>交班记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <select data-placeholder="交班人" class="layui-input" id="WorkShift_OffAccount" name="WorkShift_OffAccount" lay-search>
                                    <option value="">交班人</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="WorkShift_OffName" id="WorkShift_OffName" autocomplete="off" placeholder="交班人" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="接班人" class="layui-input" id="WorkShift_OnAccount" name="WorkShift_OnAccount" lay-search>
                                    <option value="">接班人</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="交班状态" class="form-control chosen-select" id="WorkShift_status" name="WorkShift_status" lay-search>
                                    <option value="">交班状态</option>
                                    <option value="1">未交班</option>
                                    <option value="2" selected>已交班</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="WorkShift_OnName" id="WorkShift_OnName" autocomplete="off" placeholder="接班人" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="WorkShift_AddTime0" id="WorkShift_AddTime0" autocomplete="off" placeholder="交班时间起" value='' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="WorkShift_AddTime1" id="WorkShift_AddTime1" autocomplete="off" placeholder="交班时间止" value='' />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm  layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var comtable = null;
        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            if (conditionParam.WorkShift_AddTime0 != null)
                conditionParam.WorkShift_AddTime0 = new Date(conditionParam.WorkShift_AddTime0).Format("yyyy-MM-dd 00:00:00");
            if (conditionParam.WorkShift_AddTime1 != null)
                conditionParam.WorkShift_AddTime1 = new Date(conditionParam.WorkShift_AddTime1).Format("yyyy-MM-dd 00:00:00");
            pager.conditionParam = conditionParam;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'WorkShift_ID', title: 'ID', hide: true }
                , { field: 'WorkShift_No', title: '编码', hide: true }
                , { field: 'WorkShift_ParkNo', title: '车场编码', hide: true }
                , { field: 'WorkShift_OffAccount', title: '交班人账号', hide: true }
                , { field: 'WorkShift_OffName', title: '交班人', width: 100 }
                , { field: 'WorkShift_OnTime', title: '上班时间', width: 170 }
                , { field: 'WorkShift_OffTime', title: '下班时间', width: 170 }
                , { field: 'WorkShift_PayableMoney', title: '停车应收' }
                , { field: 'WorkShift_Money', title: '停车实收' }
                // , { field: 'WorkShift_InCount', title: '入场总次', hide: true }
                , { field: 'WorkShift_OutCount', title: '出场总次' }
                // , { field: 'WorkShift_InTempCount', title: '临停入场总次', hide: true }
                , { field: 'WorkShift_OutTempCount', title: '临停出场总次' }
                // , { field: 'WorkShift_StoreMoney', title: '储值车扣费' }
                , { field: 'WorkShift_SoftOpenGateCount', title: '人工开闸' }
                , { field: 'WorkShift_ChargeCarCount', title: '收费车辆数' }
                , { field: 'WorkShift_FreeCarCount', title: '免费车辆数' }
                , { field: 'WorkShift_IntoFreeCarCount', title: '发生免费车辆数', hide: true }
                , { field: 'WorkShift_AddTime', title: '交班时间', width: 170 }
                , {
                    field: 'WorkShift_status', title: '交班状态', templet: function (d) {
                        if (d.WorkShift_Status == '1') {
                            return ' <span class="layui-badge layui-bg-green ">未交班</span>';
                        } else {
                            return '<span class="layui-badge layui-bg-blue ">已交班</span>';
                        }
                    }
                }
                , { field: 'WorkShift_OnName', title: '接班人' }
                , { field: 'WorkShift_OnAccount', title: '接班人账号', hide: true }
                , { field: 'WorkShift_Btn', title: '操作', templet: function (d) { return '<div class="layui-btn layui-btn-xs" onclick="OnOpenDetail(\'' + d.WorkShift_No + '\')">详情</div>'; } }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/WorkShift/WorkShiftList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    pager.dataCount = d.count;
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                        if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                            if (conditionParam.WorkShift_AddTime0 == null || conditionParam.WorkShift_AddTime1 == null) { layer.msg("请选择交班起止日期", { icon: 0 }); return; }
                            if (_DATE.diffDay(new Date(conditionParam.WorkShift_AddTime0), new Date(conditionParam.WorkShift_AddTime1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                        }
                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });
                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;
                                    if (conditionParam.WorkShift_AddTime0 != null)
                                        conditionParam.WorkShift_AddTime0 = new Date(conditionParam.WorkShift_AddTime0).Format("yyyy-MM-dd 00:00:00");
                                    if (conditionParam.WorkShift_AddTime1 != null)
                                        conditionParam.WorkShift_AddTime1 = new Date(conditionParam.WorkShift_AddTime1).Format("yyyy-MM-dd 00:00:00");

                                    //实现Ajax下载文件
                                    $.fileDownload('/WorkShift/ExportExcel?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                };
            });

            tb_row_checkbox();

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            conditionParam: null,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindData: function (index) {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                if (conditionParam.WorkShift_AddTime0 != null)
                    conditionParam.WorkShift_AddTime0 = new Date(conditionParam.WorkShift_AddTime0).Format("yyyy-MM-dd 00:00:00");
                if (conditionParam.WorkShift_AddTime1 != null)
                    conditionParam.WorkShift_AddTime1 = new Date(conditionParam.WorkShift_AddTime1).Format("yyyy-MM-dd 00:00:00");
                pager.conditionParam = conditionParam;

                comtable.reload({
                    url: '/WorkShift/WorkShiftList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
                layer.closeAll();
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["WorkShift_AddTime0", "WorkShift_AddTime1"], { type: "date", Range: true });

                $.post("SltAdminList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Admins_Account + '">' + d.Admins_Name + '</option>';
                            $("#WorkShift_OffAccount").append(option);
                            $("#WorkShift_OnAccount").append(option);
                        });
                        layui.form.render('select');
                    }
                }, "json");
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            }
        }

        var OnOpenDetail = function (WorkShift_No) {
            layer.open({
                type: 2,
                title: "交班详情",
                content: "Detail?WorkShift_No=" + WorkShift_No,
                area: getIframeArea(["980px", "560px"]),
                btn: ["关闭"],
                maxmin: false
            });
        }
    </script>
</body>
</html>
