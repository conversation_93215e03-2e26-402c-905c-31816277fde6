﻿
using System;
using System.Collections.Generic;
using System.Text;
using carparking.BLL.Cache;
using carparking.Common;
using carparking.Library;
using Newtonsoft.Json;
namespace carparking.SentryBox
{
    /// <summary>
    /// 无入场记录出口识别
    /// </summary>
    public class NoRecordLPR
    {
        public NoRecordLPR() { }
        public NoRecordLPR(Model.ResultPass data)
        {
            Model.ResultPass rs = JsonConvert.DeserializeObject<Model.ResultPass>(JsonConvert.SerializeObject(data));
            this.carno = rs.passres.carno;
            this.cartype = rs.passres.cartype.CarType_Name;
            this.carcardtype = rs.passres.carcardtype.CarCardType_Name;
            this.carcardtypeno = rs.passres.carcardtype.CarCardType_No;
            this.passwayno = rs.passres.passway.Passway_No;
            this.passcode = rs.passres.code.ToString();
            this.passmsg = rs.errmsg;
            this.outtime = rs.time.Value.ToString("yyyy-MM-dd HH:mm:ss");
            this.outimg = Util.LPRTools.GetSentryHostImg(rs.passres.localimage);
            rs.passres.img = Util.LPRTools.GetSentryHostImg(rs.passres.localimage);
            this.payedamount = rs?.payres?.payedamount.ToString() ?? (rs.resorder?.resOut?.minmoney.ToString() ?? "0");
            this.expday = rs.passres.expday > 0 ? rs.passres.expday.ToString() : "0";
            if (rs.payres?.inTime.ToString("yyyy-MM-dd HH:mm:ss") == "0001-01-01 00:00:00" && rs.resorder?.resOut?.parkorder != null)
            {
                rs.payres.inTime = rs.resorder.resOut.parkorder.ParkOrder_EnterTime != null ? rs.resorder.resOut.parkorder.ParkOrder_EnterTime.Value : DateTime.Now;
                rs.payres.calctime = rs.resorder.resOut.parkorder.ParkOrder_OutTime != null ? rs.resorder.resOut.parkorder.ParkOrder_OutTime.Value : DateTime.Now;
            }
            this.data = rs;
            this.name = rs.passres?.owner?.Owner_Name ?? rs.passres?.car?.Car_OwnerName;
            if (rs.payres != null)
            {
                var ts = TimeSpan.FromMinutes(rs.payres.parktimemin);
                this.parktime = $"{ts.Days}天{ts.Hours}小时{ts.Minutes}分钟";

                DateTime? enterTime = rs.resorder?.resOut?.parkorder?.ParkOrder_EnterTime.Value ?? null;
                var outTime = Common.Utils.StrToDateTime(DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"));

                if (rs.resorder?.resOut?.onenter == 0)
                {
                    DateTime end, start;
                    if (rs.resorder.resOut.onmachorder == 1)
                    {
                        enterTime = rs.resorder?.resOut?.noRecordOrder?.ParkOrder_EnterTime.Value ?? null;
                        outTime = rs.time.Value;
                        DateTime.TryParse(outTime.ToString("yyyy-MM-dd HH:mm:ss"), out end);
                        DateTime.TryParse(enterTime.Value.ToString("yyyy-MM-dd HH:mm:ss"), out start);
                    }
                    else
                    {
                        DateTime.TryParse(outTime.ToString("yyyy-MM-dd HH:mm:ss"), out end);
                        int min = -5;
                        if (AppBasicCache.GetPolicyPark.PolicyPark_NoRecordRangTime > 0) { min = -AppBasicCache.GetPolicyPark.PolicyPark_NoRecordRangTime.Value; }
                        if (end != null && enterTime == null) enterTime = end.AddMinutes(min);
                        DateTime.TryParse(enterTime.Value.ToString("yyyy-MM-dd HH:mm:ss"), out start);
                    }

                    rs.payres.parktimemin = (end - start).TotalMinutes;
                    this.parktime = Common.Utils.DateDiff(enterTime.Value, outTime);
                }
            }

            if (rs.passres?.owner?.Owner_CardType == 2)
            {
                decimal dMoney = 0;
                decimal deductionAmount = 0;

                decimal? dl = data.payres?.chuzhiremainingamount ?? data.passres.owner?.Owner_Balance;
                decimal dBalance = dl != null ? dl.Value : 0;
                if (data.passres.car != null && data.passres.car.Car_Category == "3657" && data.payres != null)
                {
                    dMoney = data.payres.chuzhiremainingamount ?? dBalance;
                    deductionAmount = data.payres.chuzhiamount;
                }

                this.balance = dMoney.ToString();
                this.deduction = deductionAmount.ToString();
            }
        }

        /// <summary>
        /// 识别车牌
        /// </summary>
        public string carno { get; set; }

        /// <summary>
        /// 车牌颜色
        /// </summary>
        public string cartype { get; set; }

        /// <summary>
        /// 车牌类型名称
        /// </summary>
        public string carcardtype { get; set; }
        /// <summary>
        /// 车牌类型编号
        /// </summary>
        public string carcardtypeno { get; set; }

        /// <summary>
        /// 通行车道编码
        /// </summary>
        public string passwayno { get; set; }

        /// <summary>
        /// 通行结果：0[禁止通行],1[自动放行],2[确认放行],3[排队等候],4[最低收费缴费通行]
        /// </summary>
        public string passcode { get; set; }

        /// <summary>
        /// 通行信息
        /// </summary>
        public string passmsg { get; set; }

        /// <summary>
        /// 识别时间
        /// </summary>
        public string outtime { get; set; }

        /// <summary>
        /// 出场图片
        /// </summary>
        public string outimg { get; set; }

        /// <summary>
        /// 待缴费用
        /// </summary>
        public string payedamount { get; set; }

        /// <summary>
        /// 月卡剩余有效期（单位：/天）
        /// </summary>
        public string expday { get; set; }

        /// <summary>
        /// 通行检测结果
        /// </summary>
        public Model.ResultPass data { get; set; }
        /// <summary>
        /// 车主姓名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 储值车余额
        /// </summary>
        public string balance { get; set; } = "";
        /// <summary>
        /// 储值车抵扣金额
        /// </summary>
        public string deduction { get; set; } = "";
        /// <summary>
        /// 停车时长
        /// </summary>
        public string parktime { get; set; }
    }
}
