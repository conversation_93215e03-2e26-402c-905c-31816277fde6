﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>支付方式</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>支付方式</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <blockquote class="layui-elem-quote">这里的支付方式启用/禁用并不影响用户支付。禁用后，在报表查询结果、记录查询条件中不会显示。</blockquote>
                    </div>
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="PayType_No" id="PayType_No" autocomplete="off" placeholder="编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="PayType_Name" id="PayType_Name" autocomplete="off" placeholder="名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <select class="layui-select" lay-search id="PayType_Enable" name="PayType_Enable">
                                    <option value="">启用状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                @*<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                    <button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>*@
                                <button class="layui-btn layui-btn-sm" id="Enable" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>启用</t></button>
                                <button class="layui-btn layui-btn-sm" id="Disable" lay-event="Disable"><i class="fa fa-ban"></i><t>禁用</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var comtable = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'PayType_No', title: '编码', hide: true }
                , { field: 'PayType_Name', title: '名称' }
                , { field: 'PayType_Nickname', title: '别称' }
                , {
                    field: 'PayType_Enable', title: '状态', templet: function (d) {
                        if (d.PayType_Enable == 1) return tempBar(1, "启用");
                        else return tempBar(3,"禁用");
                    }
                }
                , { field: 'PayType_UpdateTime', title: '更新时间' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/PayType/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增支付方式",
                            content: '/PayType/Edit?Act=Add',
                            area: getIframeArea(['600px', '420px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑支付方式",
                            content: '/PayType/Edit?Act=Update&PayType_No=' + data[0].PayType_No,
                            area: getIframeArea(['600px', '420px']),
                            maxmin: true
                        });
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var nos = [];
                        data.forEach((item, index) => { nos.push(item.PayType_No); });
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定启用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/PayType/Enable", { PayType_No: JSON.stringify(nos) }, function (json) {
                                    if (json.success)
                                        layer.msg("启用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var nos = [];
                        data.forEach((item, index) => { nos.push(item.PayType_No); });
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定禁用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/PayType/Disabled", { PayType_No: JSON.stringify(nos) }, function (json) {
                                    if (json.success)
                                        layer.msg("禁用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_checkbox();

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/PayType/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            }
        }
    </script>
</body>
</html>
