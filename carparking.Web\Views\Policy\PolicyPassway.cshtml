﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>功能策略设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <script src="~/Static/plugins/carnopicker/carnopicker.js?1" asp-append-version="true"></script>
    <style>
        html, body { background-color: #fff !important; margin: 0; }
        .layui-tab-title { padding-left: 2rem; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }
        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }
        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title { padding-left: 2rem; }

        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }

        .layui-tab-title li { padding-left: 2rem; }

        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }

        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }

        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }

        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }

        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }

        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }

        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_park1.svg'); }

        .layui-tab-title li.layui-this.type5::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-content { padding: 2rem; }

        .layui-inline .layui-form-select .layui-input { width: 182px; }

        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }

        .layui-disabled { background-color: #eee; opacity: 1; }

        .layui-form-select dl { box-shadow: 0 0 6px; }

        input[value='自动放行'] { color: #1ab394 !important; }

        input[value='禁止通行'] { color: red !important; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }

        select[multiple] + .layui-form-select dd { padding: 0; }

        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }

        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }

        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }

        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }

        .multiSelect a { padding: 2px 5px; background: #0094ff; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }

        .multiSelect a span { float: left; }

        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }

        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }

        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }

        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }

        .multiSelect a i:hover { background-color: #545556; }

        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }

        .multiOption:hover { color: #5FB878 }

        .simplehide { display: none; }

        .moresetting { display: none; }

        .headmoresetting { cursor: pointer; color: #1e9fff; }

        .headmoresetting:hover { font-weight: 600; }

        .otherdesc { display: none; }

        .descicon { cursor: pointer; font-size: 1.1rem; }

        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }

        .help-btn { position: absolute; width: 20px; margin-left: 7px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .bottomButton { position: fixed; bottom: 0; left: 0; width: 100%; background: #fff; padding: 10px; z-index: 9999; }
        .layui-table { margin-bottom: 50px;  }
        tr td:first-child { min-width: 100px; }
        tr td:nth-child(2) { min-width: 150px; }
    </style>
</head>
<body>

    <div class="layui-form" style="background-color:#fff !important;">
        <!--车道设置-->

        <div class="layui-row">
            <div class="layui-inline">
                <select class="layui-select" lay-search id="PolicyPassway_PasswayNo" name="PolicyPassway_PasswayNo">
                    <option value="">出入通道</option>
                </select>
            </div>
            <div class="layui-inline">
                <button class="layui-btn BatchSetPassway " id="BatchSetPassway">批量设置</button>
            </div>
        </div>
        <table class="layui-table">
            <thead>
                <tr>
                    <th>功能类目</th>
                    <th>功能方案</th>
                    <th></th>
                    <th>注释</th>
                </tr>
            </thead>
            <tbody data-key="passway" id="passwaypanl">
                <tr>
                    <td>车道通行控制</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_IsAuthorize" name="PolicyPassway_IsAuthorize">
                            <option value="1">开启</option>
                            <option value="0">关闭</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>适用于某车道在指定时间点内仅允许通行月租车的场景，需要先【通行控制】中增加规则，然后开启车道通行控制。</td>
                </tr>
                <tr class="simple">
                    <td>默认车牌类型</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_DefaultCarCardType" name="PolicyPassway_DefaultCarCardType">
                            <option value="">不设置</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>适用于临时车在不同入口车道按指定的车牌类型入场，出场按该车牌类型进行收费的场景。</td>
                </tr>
                <tr class="simple">
                    <td>默认车牌颜色</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_DefaultCarType" name="PolicyPassway_DefaultCarType">
                            <option value="">不设置</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>适用于临时车在不同入口车道按指定的车牌颜色入场，出场按该车牌颜色进行收费的场景。</td>
                </tr>
                <tr class="simple">
                    <td>车牌模糊匹配</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_CarMatch" name="PolicyPassway_CarMatch">
                            <option value="1">完全匹配</option>
                            <option value="2">仅允许首字符错误</option>
                            <option value="3">允许任意一位字符错误</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">适用于车牌有污损或遮挡时，系统对接收到的车牌果，按照设定的规则进行匹配 <i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            【完全匹配】：全字符匹配，以实际识别结果为准；<br />
                            【仅允许首字符错误】：允许车牌省份汉字错误；<br />
                            【允许任意一位字符错误】：允许车牌号任意一位字符错误；<br />
                        </span>
                        <t style="color:red;">注意：模糊匹配时，会优先完全匹配场内记录，无场内记录才会根据当前设置查找（模糊匹配/完全匹配）固定车记录。</t>
                    </td>
                </tr>
                <tr>
                    <td>显示播报</td>
                    <td>
                        <select class="layui-input" id="PolicyPassway_ShowOption" name="PolicyPassway_ShowOption">
                            <option value="1">欢迎光临/一路顺风</option>
                            <option value="2">您好/一路平安</option>
                            <option value="3">欢迎光临/一路平安</option>
                            <option value="4">您好/一路顺风</option>
                            <option value="5">自定义语音</option>
                        </select>
                        <table class="layui-table layui-hide" id="PolicyPassway_Show_Table">
                            <tbody>
                                <tr>
                                    <td style="padding: 3px;line-height: 18px;min-height: 18px !important;"><input type="text" class="layui-input" id="PolicyPassway_Show_Entrance" name="PolicyPassway_Show_Entrance" placeholder="入口自定义语音 " /></td>
                                    <td style="padding: 3px;line-height: 18px;min-height: 18px !important;"><input type="text" class="layui-input" id="PolicyPassway_Show_Exit" name="PolicyPassway_Show_Exit" placeholder="出口自定义语音 " /></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>设置不同的入出口车道车辆开闸放行后，按设定的礼貌用语显示播报。</td>
                </tr>

                <tr>
                    <td colspan="4">
                        <div class="layui-row headmoresetting"><t class="content">更多设置</t>&nbsp;<i class="layui-icon layui-icon-down"></i></div>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>车道尾号限行控制</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_IsNumAuthorize" name="PolicyPassway_IsNumAuthorize">
                            <option value="0">关闭</option>
                            <option value="1">开启</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>默认"关闭"。开启后，【尾号限行】中设置规则限行，举例某车道在指定时间点内禁止尾号7的车牌限行，就需要先【尾号限行】中增加设置规则，然后开启车道尾号限行控制</td>
                </tr>
                <tr class="simple moresetting">
                    <td>无效车牌过滤</td>
                    <td>
                        <input type="text" class="layui-input" id="PolicyPassway_CarInvalid" name="PolicyPassway_CarInvalid" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        适用于过滤现场识别区域存在井盖/护栏/广告牌等物体导致摄像机误触发识别输出车牌结果的场景。例如"LLLLL"、"77777"和"TTTTT"等无效车牌结果。<br />
                        <t style="color:red;">注意：存在多个时，使用英文输入法下的逗号隔开。例如：LLLLL,77777,TTTTT</t>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>播报并显示停车时长</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_Broadparktime" name="PolicyPassway_Broadparktime">
                            <option value="0">关闭</option>
                            <option value="1" selected>开启</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置出口设备是否显示播报停车时长，关闭后将不显示播报停车时长。</td>
                </tr>
                <tr class="simple moresetting">
                    <td>显示发布信息</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_Broadpushinfo" name="PolicyPassway_Broadpushinfo">
                            <option value="0">关闭</option>
                            <option value="1">开启</option>
                        </select>
                        <input type="text" class="layui-input layui-hide" id="PolicyPassway_Broadpushtext" name="PolicyPassway_Broadpushtext" placeholder="请输入发布内容" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置入出口设备是否显示发布通知信息，关闭后将默认广告显示。</td>
                </tr>
                <tr class=" moresetting">
                    <td>显示剩余车位</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_Broadspacenum" name="PolicyPassway_Broadspacenum">
                            <option value="0">关闭</option>
                            <option value="1">开启</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置入口设备是否显示剩余车位信息，关闭后若是余位模式时，将不更新车位信息。</td>
                </tr>
                <tr class="simple moresetting">
                    <td>显示车位号</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_Broadspace" name="PolicyPassway_Broadspace">
                            <option value="0">关闭</option>
                            <option value="1">开启</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置入口设备是否显示车位号信息，关闭后将不显示车位号信息。</td>
                </tr>

                <tr class=" moresetting">
                    <td>无牌车扫码</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_Scan" name="PolicyPassway_Scan">
                            <option value="1">有车允许扫码</option>
                            <option value="2">有无车均可扫码</option>
                            <option value="3">识别无牌车可扫码</option>
                            <option value="4">识别无牌车+有车可扫码</option>
                            <option value="0">有无车均禁止扫码</option>

                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置无牌车入出口扫车道二维码的判断规则，系统默认按"有无车均可扫码"设置，推荐按"有车允许扫码"设置并搭配车辆检测器使用。<i class="help-btn">?</i></span>
                        <span class="otherdesc">
                            【有车允许扫码】:车辆压地感才允许扫码；<br />
                            【有无车均可扫码】：车辆有无压地感都允许扫码；<br />
                            【识别无牌车可扫码】：相机识别无牌车后才允许扫码；<br />
                            【识别无牌车+有车可扫码】:相机识别无牌车后且车辆有压地感才允许扫码；<br />
                            【有无车均禁止扫码】:屏蔽无牌车扫码功能，禁止扫码；
                        </span>
                    </td>
                </tr>
                @* <tr class="simple moresetting">
                <td>无牌车计数器</td>
                <td>
                <select class="layui-select" id="PolicyPassway_HasCount" name="PolicyPassway_HasCount">
                <option value="0">禁用</option>
                <option value="1">启用</option>
                </select>
                </td>
                <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                <td>无牌车扫码通行计数功能</td>
                </tr> *@
                <tr class="simple moresetting">
                    <td>无牌车播报</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_BroadcastNoCar" name="PolicyPassway_BroadcastNoCar">
                            <option value="0">禁用</option>
                            <option value="1" selected>启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置系统接收到无牌车时，入出口设备是否显示播报"无牌车请扫码"</td>
                </tr>
                <tr class=" moresetting">
                    <td>倒车事件处理模式</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_BackCarMode" name="PolicyPassway_BackCarMode">
                            <option value="1">智能处理</option>
                            <option value="2" selected>人工处理</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">该设置需搭配智慧道闸（车道控制器）或数智相机使用，用于设置系统接收到倒车事件后的处理方式。<i class="help-btn">?</i></span>
                        <span class="otherdesc">
                            【智能处理】：产生的倒车事件，系统自动判断为倒车；<br />
                            【人工处理】：产生的倒车事件，需前往"事件管理"模块人工判断是否倒车。
                        </span>
                    </td>
                </tr>
                <tr class=" moresetting">
                    <td>倒车事件处理车道弹窗</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_ClosePasswayWin" name="PolicyPassway_ClosePasswayWin">
                            <option value="0">不处理</option>
                            <option value="1" selected>自动关闭车道弹窗</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>该设置需搭配智慧道闸（车道控制器）或数智相机使用，用于设置系统接收到倒车事件后是否自动关闭对应的车道弹框。</td>
                </tr>
                <tr class=" moresetting">
                    <td>限时等待确认放行</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_WaitTimeOut" name="PolicyPassway_WaitTimeOut">
                            <option value="1">1分钟</option>
                            <option value="2">2分钟</option>
                            <option value="3">3分钟</option>
                            <option value="4">4分钟</option>
                            <option value="5">5分钟</option>
                            <option value="6">6分钟</option>
                            <option value="7">7分钟</option>
                            <option value="8">8分钟</option>
                            <option value="9">9分钟</option>
                            <option value="10">10分钟</option>
                            <option value="11">11分钟</option>
                            <option value="12">12分钟</option>
                            <option value="13">13分钟</option>
                            <option value="14">14分钟</option>
                            <option value="15">15分钟</option>
                            <option value="30">30分</option>
                            <option value="-1">不限时</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置系统弹出确认放行框，限时等待值班人员处理弹框的时间，系统默认为5分钟，一般不建议低于5分钟。</td>
                </tr>
                <tr class=" moresetting">
                    <td>确认框逾期未处理</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_TimeOutHandle" name="PolicyPassway_TimeOutHandle">
                            <option value="0">当取消放行处理</option>
                            <option value="1">当确认放行处理</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置系统弹出确认放行框超时未处理，按设定方式自动处理。<i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            【当确认放行处理】：弹框超时后将按确认放行处理且不执行开闸；<br />
                            【当取消放行处理】：弹框超时后将关闭弹框且不处理识别记录，若系统设置了出口弹框禁止取消时该设置无效。
                        </span>
                    </td>
                </tr>
                <tr class=" moresetting">
                    <td>启用优先保存记录</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_TakeRecord" name="PolicyPassway_TakeRecord">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置入出口确认放行时，是否直接保存记录。启用时，优先保存车辆通行记录，系统不会自动开闸，需由人工遥控开闸。<br />
                        <t style="color:red;">注意：出口启用该功能，将无法使用电子支付。</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>遥控开闸放行</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_OpenGateForOrder" name="PolicyPassway_OpenGateForOrder">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置入出口存在弹框时，操作车道遥控开闸后是否关闭对应车道弹框并写通行记录；<br />
                        <t style="color:red;">注意：需道闸的开到位接到相机的G+IN2端口且端口有信号输入，功能才生效。</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>通行需要补缴欠费金额</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_IsPayClearing" name="PolicyPassway_IsPayClearing">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置入出口识别欠费车牌后，是否需要补缴欠费费用后才允许通行。<br />
                        <t style="color:red;">注意：入口启用该设置项时，需搭配自助停车设备使用；</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>车辆出场播报语音</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_IsInoutSound" name="PolicyPassway_IsInoutSound">
                            <option value="0">禁用</option>
                            <option value="1" selected>启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置当车道出口弹窗缴费时是否播报语音。<i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            【启用】：出口弹框时，自动显示播报收费信息；<br />
                            【禁用】：出口弹框时，由人工操作弹框后才播报收费信息。
                        </span>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>出入口设备显示二维码</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_S017UQR" name="PolicyPassway_S017UQR">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        该设置需搭配支持显示二维码的设备使用，设置入出口是否显示二维码。<br />
                        <t style="color:red;">注意：禁用无牌车播报后，该功能不会显示无牌车相关二维码。</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>指定车牌颜色开双闸</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_DoubleGateColorCodes" name="PolicyPassway_DoubleGateColorCodes" lay-filter="doubleGateColors" multiple lay-search>
                            <option value="">请选择车牌颜色</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>指定车牌颜色开双闸，默认不设置，不设置时，所有车都是单开。设置后，识别到指定颜色后进行双开。颜色支持设置多个，例如：黄牌、黄绿牌</td>
                </tr>
                <tr class="moresetting">
                    <td>双开关联相机</td>
                    <td>
                        <select class="layui-select" id="PolicyPassway_DoubleGateLinkCameraNo" name="PolicyPassway_DoubleGateLinkCameraNo" multiple lay-search>
                            <option value="">默认该车道主相机</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>双开关联相机，默认该车道的主相机（给out1和out2同时发信号）。选择其它相机时，给这个车道的主相机和联动的相机同时发开闸信号。支持选择多个相机</td>
                </tr>
            </tbody>
        </table>

        <div class="layui-row savebtn">
            <button class="layui-btn layui-btn-sm saveAll" data-id="passway">保存全部</button>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}" data-type="${CarCardType_Type}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplrule">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">以 ${CarCardType_Name} 计费规则计费</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1.9" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        if ('@carparking.Config.PubVar.iParkingType' == "1") {
            $("#PolicyPark_MaxDiscount").attr("disabled", true);
        }

        myVerify.init();
        var cp = new CarnoPicker("#PolicyPark_CarPrefix", { ischar: false });
        cp.init();
        layui.use(['element', 'form', 'laydate'], function () {
            pager.init();
        })


        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//访客车类型

        var pager = {
            parkareas: null,
            passways: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            drives: null,       //设备型号列表
            links: null,        //通道关联区域列表
            Province: [],
            City: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PolicyCarCard_DelayMaxDate"], { type: 'date' });

                $.post("GetAllPasswayAndCarCardType3", {}, function (json) {
                    if (json.success) {
                        pager.passways = parent.pager.passways;
                        pager.carCardTypes = parent.pager.carCardTypes;
                        pager.links = parent.pager.links;

                        pager.carTypes = json.data.carTypes;
                        pager.drives = json.data.drives;

                        $("#PolicyPass_PasswayNo").html($("#tmplpassway").tmpl(json.data.passways));
                        $("#PolicyPass_CarCardTypeNo").html($("#tmplcarcardtype").tmpl(json.data.carCardTypes));

                        // 添加车牌颜色选项
                        $("#PolicyPassway_DoubleGateColorCodes").append($("#tmplcartype").tmpl(json.data.carTypes));
                        if (json.data.PolicyPassway_DoubleGateColorCodes) {
                            var selectedColors = JSON.parse(decodeURIComponent(json.data.PolicyPassway_DoubleGateColorCodes));
                            // 过滤掉空值
                            selectedColors = selectedColors.filter(function(color) {
                                return color !== "" && color !== null && color !== undefined;
                            });
                            $("#PolicyPassway_DoubleGateColorCodes").val(selectedColors);
                        }
                        else {
                            $("#PolicyPassway_DoubleGateColorCodes").val([]);
                        }
                    } else {
                        console.log(json.msg);
                    }
                }, "json");



                $("#PolicyPassway_PasswayNo").html($("#tmplpassway").tmpl(pager.passways));

                var data = [], tempcar = [];
                pager.carCardTypes.forEach(function (item, index) {
                    //data[data.length] = item;
                    if (item.CarCardType_Type != 5) { data[data.length] = item; }//过滤访客车
                    if (item.CarCardType_Type == 1) { tempcar[tempcar.length] = item; }
                });
                $("#PolicyPassway_DefaultCarCardType").append($("#tmplcarcardtype").tmpl(tempcar));
                $("#PolicyPassway_DefaultCarType").append($("#tmplcartype").tmpl(pager.carTypes));

                layui.form.render();

                $("td").hover(function () {
                    //判断td里有headdesc样式
                    if ($(this).find("span.headdesc").length > 0) {
                        var $td = $(this).find("span.headdesc").siblings(".otherdesc");
                        var $div = $('<div>').append($td.contents().clone());
                        layer.tips($div.html(), this, {
                            tips: [1, '#090a0c'],
                            time: 0,
                            area: '50wh'  // 设置宽度为300px
                        });
                    }
                }, function () {
                    layer.closeAll('tips');
                });
            },
            bindData: function () {
                policy.passway.onload();
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    //console.log(data.elem); //得到select原始DOM对象
                    //console.log(data.elem.id); //得到select原始DOM对象
                    //console.log(data.value); //得到被选中的值
                    //console.log(data.othis); //得到美化后的DOM对象

                    var val = data.value;
                    //开闸方式切换通道
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        policy.pass.onload();
                    }
                    //开闸方式切换车牌类型
                    else if (data.elem.id == "PolicyPass_CarCardTypeNo") {
                        policy.pass.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyPassway_PasswayNo") {
                        policy.passway.onload();
                        // 重新加载该车道的相机列表
                        policy.loadCameraList();
                    }
                    //车牌策略切换类型
                    else if (data.elem.id == "PolicyCarCard_CarCardTypeNo") {
                        policy.card.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyArea_ParkAreaNo") {
                        policy.area.onload();
                    }
                    //自定义语音设置
                    else if (data.elem.id == "PolicyPassway_ShowOption") {
                        policy.voicediy(data.elem.id, val);
                    }
                    //开闸方式-未找到入场记录最低收费标准
                    else if (data.elem.id == "PolicyPass_NoFundEnter") {
                        policy.minpayed(data.elem.id, val);
                    }
                    //发布内容
                    else if (data.elem.id == "PolicyPassway_Broadpushinfo") {
                        policy.pushinfo(data.elem.id, val);
                    }
                    //防疫设置
                    else if (data.elem.id == "PolicyArea_EPEnable") {
                        policy.fymodal(data.elem.id, val);
                    }
                    //车主车位已满后其余车辆
                    else if (data.elem.id == "PolicyArea_MoreCar") {
                        policy.opengate(data.elem.id, val);
                    }
                })

                $("button.save").click(function () {

                    if (!myVerify.check()) return;

                    var param = {};
                    $(this).parent().siblings().find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            var v = $(this).val();
                            if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                if (v == null || v == '') v = [];
                                v = JSON.stringify(v);
                            }

                            param[$(this).attr('id')] = v;
                        }
                    });

                    var datakey = $(this).parent().parent().parent().attr("data-key");
                    pager.onSave(datakey, param);
                });

                $("button.saveAll").click(function () {
                    if (!myVerify.check()) return;

                    var param = {};
                    var datakey = $(this).attr("data-id");
                    $("tbody[data-key='" + datakey + "']").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide")) {
                                var v = $(this).val();
                                if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                    if (v == null || v == '') v = [];
                                    v = JSON.stringify(v);
                                }

                                param[$(this).attr('id')] = v;
                            }
                        }
                    });


                    // 处理PolicyPassway_DoubleGateColorCodes的值，过滤掉空值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    if (selectedColors && selectedColors.length > 0) {
                        selectedColors = selectedColors.filter(function(color) {
                            return color !== "" && color !== null && color !== undefined;
                        });
                    } else {
                        selectedColors = [];
                    }
                    param.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));

                    pager.onSave(datakey, param);
                });

                $("#BatchSetPassway").unbind("click").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/BatchSetPassWay",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $("#EditBatch").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/EditPassBatch",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $(".headmoresetting").unbind("click").click(function () {
                    var table = $(this).parent().parent().parent().find(".moresetting");
                    if ($(table).last().is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                        $(this).find("t").text("隐藏更多设置");
                        $(".savebtn").addClass("bottomButton");
                        var versionType1 = localStorage.getItem("versionType");
                        if (versionType1 == "simple") {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide").addClass("layui-hide").addClass("versionHide");
                        } else {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide");
                            if ($("#PolicyArea_EPEnable").val() == "1")
                                $(".fymodal").removeClass("layui-hide");
                            else
                                $(".fymodal").removeClass("layui-hide").addClass("layui-hide");

                        }
                    } else {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                        $(this).find("t").text("更多设置");
                        $(".savebtn").removeClass("bottomButton");
                    }
                    $(table).toggle("fast");
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                        $("button.saveAll").removeClass("layui-hide");
                    }

                    if (pagePower['EditBatch'] == 'true') {
                        $("#BatchSetPassway").removeClass("layui-hide");
                    }
                });
            },
            onSave: function (datakey, param) {
                //开闸方式保存
                if (datakey == 'pass') {
                    var PolicyPass_PasswayNo = $("#PolicyPass_PasswayNo").val();
                    var PolicyPass_CarCardTypeNo = $("#PolicyPass_CarCardTypeNo").val();
                    var obj = { PolicyPass_PasswayNo: PolicyPass_PasswayNo, PolicyPass_CarCardTypeNo: PolicyPass_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);
                    debugger
                    $.post("SavePolicyPass", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车道设置保存
                else if (datakey == 'passway') {
                    var PolicyPassway_PasswayNo = $("#PolicyPassway_PasswayNo").val();
                    var obj = { PolicyPassway_PasswayNo: PolicyPassway_PasswayNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    if ($("#PolicyPassway_ShowOption").val() == 5) {
                        var PolicyPassway_Show_Entrance = $("#PolicyPassway_Show_Entrance").val();
                        var PolicyPassway_Show_Exit = $("#PolicyPassway_Show_Exit").val();

                        if (PolicyPassway_Show_Entrance == '') { layer.tips('入口自定义语音不能为空', '#PolicyPassway_Show_Entrance'); return; }
                        if (PolicyPassway_Show_Exit == '') { layer.tips('出口自定义语音不能为空', '#PolicyPassway_Show_Exit'); return; }

                        obj.PolicyPassway_Show = PolicyPassway_Show_Entrance + "/" + PolicyPassway_Show_Exit;
                    }

                    // 处理PolicyPassway_DoubleGateColorCodes的值，过滤掉空值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    if (selectedColors && selectedColors.length > 0) {
                        selectedColors = selectedColors.filter(function(color) {
                            return color !== "" && color !== null && color !== undefined;
                        });
                    } else {
                        selectedColors = [];
                    }
                    obj.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));

                    // 处理双开关联相机，支持多选
                    var selectedCameras = $("#PolicyPassway_DoubleGateLinkCameraNo").val();
                    if (selectedCameras && selectedCameras.length > 0) {
                        selectedCameras = selectedCameras.filter(function(camera) {
                            return camera !== "" && camera !== null && camera !== undefined;
                        });
                    } else {
                        selectedCameras = [];
                    }
                    obj.PolicyPassway_DoubleGateLinkCameraNo = encodeURIComponent(JSON.stringify(selectedCameras));

                    console.log(obj)
                    $.post("SavePolicyPassway", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车牌类型策略保存
                else if (datakey == 'card') {
                    var PolicyCarCard_CarCardTypeNo = $("#PolicyCarCard_CarCardTypeNo").val();
                    var obj = { PolicyCarCard_CarCardTypeNo: PolicyCarCard_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyCarCard", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车场策略保存
                else if (datakey == 'park') {
                    var obj = {};
                    //obj[key] = val;
                    Object.assign(obj, param);
                    $.post("SavePolicyPark", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //区域策略保存
                else if (datakey == 'area') {
                    var obj = {};
                    //obj[key] = val;
                    var obj = { PolicyArea_ParkAreaNo: $("#PolicyArea_ParkAreaNo").val() };
                    Object.assign(obj, param);
                    console.log(obj)
                    $.post("SavePolicyArea", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.open({
                                icon: 0,
                                content: json.msg,
                                area: ["180px", "auto"],
                                btn: ["我知道了"],
                                closeBtn: false,
                            });
                            //layer.msg(json.msg);
                        }
                    }, "json");
                }
            }
        }

        var linkObj = {
            //获取车道出入口类型
            getGateType: function (passwayno) {
                var gate = 0;//默认出口
                if (pager.links == null || pager.links.length == 0)
                    return gate;

                pager.links.forEach(function (item, index) {
                    if (item.PasswayLink_PasswayNo == passwayno) {
                        gate = item.PasswayLink_GateType;
                        if (gate == 1) return gate;
                    }
                });

                return gate;
            }
        }

        var policy = {
            passway: {
                onload: function () {
                    var PolicyPassway_PasswayNo = $("#PolicyPassway_PasswayNo").val();
                    $.post("GetPolicyPassway", { PolicyPassway_PasswayNo: PolicyPassway_PasswayNo }, function (json) {
                        if (json.success) {
                            if (json.data.PolicyPassway_IsNumAuthorize == null) json.data.PolicyPassway_IsNumAuthorize = 0;
                            $("#passwaypanl").fillForm(json.data, function (data) { });
                            $("#PolicyPassway_DefaultCarCardType").val(json.data.PolicyPassway_DefaultCarCardType);
                            $("#PolicyPassway_DefaultCarType").val(json.data.PolicyPassway_DefaultCarType);
                            if (json.data.PolicyPassway_DoubleGateColorCodes) {
                                var selectedColors = JSON.parse(decodeURIComponent(json.data.PolicyPassway_DoubleGateColorCodes));
                                // 过滤掉空值
                                selectedColors = selectedColors.filter(function(color) {
                                    return color !== "" && color !== null && color !== undefined;
                                });
                                $("#PolicyPassway_DoubleGateColorCodes").val(selectedColors);
                            }
                            else {
                                $("#PolicyPassway_DoubleGateColorCodes").val([]);
                            }

                            // 修复：先加载相机列表，成功后再设置选中值
                            policy.loadCameraList(function () {
                                if (json.data.PolicyPassway_DoubleGateLinkCameraNo) {
                                    try {
                                        var selectedCameras = JSON.parse(decodeURIComponent(json.data.PolicyPassway_DoubleGateLinkCameraNo));
                                        // 过滤掉空值
                                        selectedCameras = selectedCameras.filter(function (camera) {
                                            return camera !== "" && camera !== null && camera !== undefined;
                                        });
                                        $("#PolicyPassway_DoubleGateLinkCameraNo").val(selectedCameras);
                                    } catch (e) {
                                        // 兼容旧格式（单个相机编号）
                                        if (json.data.PolicyPassway_DoubleGateLinkCameraNo !== "") {
                                            $("#PolicyPassway_DoubleGateLinkCameraNo").val([json.data.PolicyPassway_DoubleGateLinkCameraNo]);
                                        }
                                    }
                                } else {
                                    $("#PolicyPassway_DoubleGateLinkCameraNo").val([]);
                                }
                                layui.form.render();
                            });
                            if (json.data != null) {
                                policy.voicediy("", json.data.PolicyPassway_ShowOption);
                                policy.pushinfo("", json.data.PolicyPassway_Broadpushinfo);

                                if (json.data.PolicyPassway_ShowOption == 5 && json.data.PolicyPassway_Show && json.data.PolicyPassway_Show != '') {
                                    $("#PolicyPassway_Show_Entrance").val(json.data.PolicyPassway_Show.split("/")[0]);
                                    $("#PolicyPassway_Show_Exit").val(json.data.PolicyPassway_Show.split("/")[1]);
                                }
                            }
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            },
            voicediy: function (id, val) {
                if (val == 5)
                    $("#PolicyPassway_Show_Table").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Show_Table").removeClass("layui-hide").addClass("layui-hide");
            },
            pushinfo: function (id, val) {
                if (val == 1)
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide").addClass("layui-hide");
            },
            minpayed: function (id, val) {
                if (val == 4)
                    $("#PolicyPass_MinAmount").removeClass("layui-hide");
                else
                    $("#PolicyPass_MinAmount").removeClass("layui-hide").addClass("layui-hide");
            },
            fymodal: function (id, val) {
                if (!$(".fymodal").hasClass("versionHide")) {
                    if (val == 1)
                        $(".fymodal").removeClass("layui-hide");
                    else
                        $(".fymodal").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            opengate: function (id, val) {
                if (!$(".opengate").hasClass("versionHide")) {
                    if (val != 3)
                        $(".opengate").removeClass("layui-hide");
                    else
                        $(".opengate").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            loadCameraList: function (callback) {
                // 获取当前选中的车道编号
                var passwayNo = $("#PolicyPassway_PasswayNo").val();
                if (!passwayNo) {
                    $("#PolicyPassway_DoubleGateLinkCameraNo").html('<option value="">请先选择车道</option>');
                    layui.form.render('select');
                    if (callback) callback();
                    return;
                }

                // 加载指定车道的车牌识别相机列表
                $.post("/Device/GetCameraList", { passwayNo: passwayNo }, function (json) {
                    if (json.success && json.data) {
                        var cameraOptions = '<option value="">默认该车道主相机</option>';
                        json.data.forEach(function (camera) {
                            cameraOptions += '<option value="' + camera.Device_No + '">' + camera.Device_Name + '</option>';
                        });
                        $("#PolicyPassway_DoubleGateLinkCameraNo").html(cameraOptions);
                        layui.form.render('select');
                    } else {
                        $("#PolicyPassway_DoubleGateLinkCameraNo").html('<option value="">该车道无可用相机</option>');
                        layui.form.render('select');
                    }
                    if (callback) callback();
                }, "json");
            }
        }
    </script>
</body>
</html>
