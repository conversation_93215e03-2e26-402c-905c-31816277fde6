﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>续费延期</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/
        .layui-form-select .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #eee; opacity: 1; color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        .changetime { border-color: #60c735; color: #de163b !important; font-weight: 600; }
        .layui-form-select dl dd { white-space: normal; max-width: 550px; }

        .red{color:#de163b;}
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>

            <div class="layui-row Rule_Type">
                <div class="layui-col-xs3 edit-label">充值方式</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Rule_Type">
                        <ul>
                            <li data-value="0" class="select">充值规则</li>
                            <li data-value="1">自定义</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车牌号</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="10" id="Car_CarNo" name="Car_CarNo" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车牌颜色</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <select id="Car_VehicleTypeNo" name="Car_VehicleTypeNo" lay-search disabled>
  
                    </select>
                </div>
            </div>
            <div class="layui-row month">
                <div class="layui-col-xs3 edit-label ">当前开始日期</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null time" maxlength="10" id="Car_BeginTime" name="Car_BeginTime" autocomplete="off" disabled />
                </div>
            </div>
            <div class="layui-row month">
                <div class="layui-col-xs3 edit-label ">当前结束日期</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null time" maxlength="10" id="Car_EndTime" name="Car_EndTime" disabled />
                </div>
            </div>

            <div class="layui-row month">
                <div class="layui-col-xs3 edit-label ">续费截止日期</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null time" maxlength="10" id="Car_NEndTime" name="Car_NEndTime" autocomplete="off" disabled />
                </div>
            </div>

            <div class="month">
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">支付类型</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <select data-placeholder="选择支付类型" class="form-control chosen-input v-null" id="payType" name="payType" lay-search>
                            <option value="0">现金支付</option>
                            <option value="1">POS机</option>
                            <option value="2">其他</option>
                        </select>
                    </div>
                </div>
                <div class="layui-row rule">
                    <div class="layui-col-xs3 edit-label ">充值规则</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <select data-placeholder="选择充值规则" class="form-control chosen-input v-null" id="payZQ" name="payZQ" lay-search lay-filter="payZQ">
                            <option value="">选择充值规则</option>
                        </select>
                    </div>
                </div>

            </div>

            <div class="chuzhi hidden">
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">支付类型</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <select data-placeholder="选择支付类型" class="form-control chosen-input " id="payType_3" name="payType_3" lay-search>
                            <option value="0">现金支付</option>
                            <option value="1">POS机</option>
                            <option value="2">其他</option>
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">充值金额</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="input-group mar-btm">
                            <input type="text" class="layui-input v-null v-floatLimit" id="Car_Balance" name="Car_Balance" maxlength="50" value="0.00" />
                            <div class="input-group-btn">
                                <button data-toggle="dropdown" class="btn btn-default dropdown-toggle" style=" padding: 8px 12px;" type="button" aria-expanded="false"><i class="dropdown-caret"></i></button>
                                <ul class="dropdown-menu dropdown-menu-right " id="EmoneyList">
                                    <li><a href="#" onclick="pager.ShowCZFree(this);" data-value="100"><span style="font-size: 1.1em;">充值100元，支付100元</span></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">支付金额</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-floatLimit v-null" maxlength="10" id="Car_PayMoney" name="Car_PayMoney" value="0.00" placeholder="请输入支付金额（元）" autocomplete="off" />
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label">&nbsp;</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplCarType">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplMonthRule">
        <option value="${MonthRule_No}" data-cycle="${MonthRule_Cycle}" data-money="${MonthRule_Money}" data-unit="${MonthRule_Unit}" data-addunit="${MonthRule_AddUnit}" data-addcycle="${MonthRule_AddCycle}" data-addenable="${MonthRule_AddEnable}" data-type="${MonthRule_Type}">${MonthRule_Name} - ${MonthRule_Cycle}{{if MonthRule_Unit==1}}天{{else MonthRule_Unit==2}}月{{else MonthRule_Unit==3}}年{{/if}}：${MonthRule_Money}元{{if MonthRule_AddEnable==1}}（赠送${MonthRule_AddCycle}{{if MonthRule_AddUnit==1}}天{{else MonthRule_AddUnit==2}}月{{else MonthRule_AddUnit==3}}年{{/if}}）{{/if}}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplFree">
        <li>
            <a href="#" onclick="pager.ShowCZFree(this);" data-value="${MonthRule_No}" data-emoney="${MonthRule_EMoney}" data-money="${MonthRule_Money}">
                <span style="font-size: 1.1em;">充值${MonthRule_EMoney}元，支付${MonthRule_Money}元</span>
            </a>
        </li>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?2.0" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/carnopicker/carnopicker.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function () {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");

            _DATE.bind(layui.laydate, ["Car_BeginTime", "Car_NEndTime"], { type: "date", range: true });

            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramCarNo = $.getUrlParam("Car_No");
        var carType = $.getUrlParam("Car_Type");

        var PayFreeType = 0;
        var dt = new Date().Format("yyyy-MM-dd");

        if (carType == "3657") {//储值车
            $(".chuzhi").removeClass("hidden");
            $(".month,.Rule_Type").addClass("hidden");
        } else {
            $("#Car_PayMoney").attr("disabled", true);
        }

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var nowDate = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var pager = {
            data: null,
            rules: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                layform.on('select(payZQ)', function (data) {
                    var dt = pager.data ? pager.data.Car_EndTime : null;
                    if (!dt) dt = nowDate; else dt = new Date(dt);
                    var monthNum = data.value;
                    var cycle = $("#payZQ").find("option:selected").attr("data-cycle");
                    var unit = $("#payZQ").find("option:selected").attr("data-unit");
                    var money = $("#payZQ").find("option:selected").attr("data-money");

                    var addcycle = $("#payZQ").find("option:selected").attr("data-addcycle");
                    var addunit = $("#payZQ").find("option:selected").attr("data-addunit");
                    var addenable = $("#payZQ").find("option:selected").attr("data-addenable");
                    var type = $("#payZQ").find("option:selected").attr("data-type");

                    if (type == 1) {
                        dt = new Date(nowDate.getFullYear(), nowDate.getMonth(), 1).Format("yyyy-MM-dd");
                    } else if (type == 2) {
                        dt = nowDate.Format("yyyy-MM-dd");
                    } else {
                        dt = new Date(dt.setDate(dt.getDate())).Format("yyyy-MM-dd");
                    }
                    if (!pager.data || !pager.data.Car_BeginTime)
                        $("#Car_BeginTime").val(dt);
                    else
                        $("#Car_BeginTime").val(new Date(pager.data.Car_BeginTime).Format("yyyy-MM-dd"));

                    if (monthNum != "") {
                        var d2 = onEndDateTime(dt, cycle, unit)
                        if (addenable == 1) {
                            d2 = onEndDateTime(d2, addcycle, addunit)
                        }
                        $("#Car_NEndTime").val(d2.Format("yyyy-MM-dd"));
                    }
                    if (money) {
                        $(".time").addClass("changetime");
                        $("#Car_PayMoney").val(money);
                    }
                    else {
                        $(".time").removeClass("changetime");
                        $("#Car_PayMoney").val("0.00")
                    }
                });

                $.getJSON("/DropList/GetCarType", {}, function (json) {
                    if (json.Success) {
                        $("#Car_VehicleTypeNo").append($("#tmplCarType").tmpl(json.Data))
                        layform.render("select");
                    }
                });

                $.post("GetMonthRuleList", {}, function (json) {
                    if (json.Success) {
                        pager.rules = json.Data;
                        var data = [];
                        var data2 = [];
                        json.Data.forEach(function (d, i) {
                            if (",3652,3653,3654,3655,3661,3662,3663,3664,3656,".indexOf(carType) >= 0 && carType == d.MonthRule_Category)
                                data[data.length] = d;
                            else if (d.MonthRule_Category == 3657)
                                data2[data2.length] = d;
                        });

                        $("#payZQ").append($("#tmplMonthRule").tmpl(data));
                        if (data2.length == 0) {
                            $("#EmoneyList").html('<li><a href = "#" onclick="pager.ShowCZFree(this);" data-value="0" data-emoney="0" data-money="0"><span style="font-size: 1.1em;">未设置充值规则</span></a></li>');
                        } else
                            $("#EmoneyList").html($("#tmplFree").tmpl(data2));

                        layform.render("select")
                    } else {

                    }
                }, "json");


            },
            //数据绑定
            bindData: function () {
                $.getJSON("/Car/GetCarExt", { Car_No: paramCarNo }, function (json) {
                    if (json.Success) {
                        pager.data = json.Data;
                        $("#verifyCheck").fillForm(json.Data, function (data) { });
                        $("#Car_Balance").val('0.00');
                        layform.render("select");
                      
                        var dt = $("#Car_EndTime").val();
                        if (dt && dt != "") {
                            dt = new Date(dt);
                            var now = new Date(dt.setDate(dt.getDate())).Format("yyyy-MM-dd")
                            $("#Car_EndTime").val(dt.Format("yyyy-MM-dd"));

                            if (!pager.data || !pager.data.Car_BeginTime)
                                $("#Car_BeginTime").val(now);
                            else
                                $("#Car_BeginTime").val(new Date(pager.data.Car_BeginTime).Format("yyyy-MM-dd"));

                            var end = new Date(new Date(now).setMonth(new Date(now).getMonth() + 1)).Format("yyyy-MM-dd");
                            $("#Car_NEndTime").val(end);
                        } else {
                            $("#Car_EndTime").val(nowDate.Format("yyyy-MM-dd"));
                        }
                    }
                });

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;

                    if (carType != "3657") {//月租车
                        if (config.Rule_Type == 0) {
                            if ($("#payZQ").val() == "") {
                                layer.msg("请选择充值规则", { icon: 0 });
                                return;
                            }
                        }
                    } else {
                        var paymoney = parseFloat($("#Car_Balance").val());
                        if (paymoney <= 0) {
                            layer.msg("请输入大于0的充值金额", { icon: 0 });
                            return;
                        }
                    }
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.PayFreeType = PayFreeType;
                        data.Car_No = paramCarNo;
                        data.Car_TypeNo = carType;
                        data.Car_EndTime = $("#Car_NEndTime").val();
                        data.Rule_Type = config.Rule_Type;
                        return data;
                    });

                    var money = $("#payZQ").find("option:selected").attr("data-money");//月租选择金额

                    if (carType != "3657") {//月租车
                        param.payType = $("#payType").val();
                        param.payZQ = $("#payZQ").val();

                        var bt = new Date(param.Car_BeginTime);
                        var ed = new Date(param.Car_NEndTime);
                        if (bt > ed) {
                            layer.msg("当前开始日期不能大于续费截止日期", { icon: 0 });
                            return;
                        }

                    } else {//储值车
                        param.payType = $("#payType_3").val();
                        param.Car_Balance = $("#Car_Balance").val();
                        param.Car_PayMoney = $("#Car_PayMoney").val();
                    }

                    var msg = "当前充值车牌【<t class='red'>" + $("#Car_CarNo").val() + "</t>】，<br/>";
                    if (carType != "3657") {
                        msg += "月租结束日期：<t class='red'>" + $("#Car_EndTime").val() + "</t>，<br/>";
                        msg += "月租充值时段：<t class='red'>" + $("#Car_BeginTime").val() + "</t>～<t class='red'>" + $("#Car_NEndTime").val() + "</t>，<br/>";
                        msg += "支付金额：<t class='red'>" + $("#Car_PayMoney").val() + "</t> 元，<br/>";
                    } else {
                        msg += "充值金额：<t class='red'>" + $("#Car_Balance").val() + "</t> 元，<br/>";
                        msg += "支付金额：<t class='red'>" + $("#Car_PayMoney").val() + "</t> 元，<br/>";
                    }


                    $("#Save").attr("disabled", true);
                    layer.open({
                        type: 0,
                        title: "充值提示",
                        btn: ["确定", "取消"],
                        content: msg + "确定充值吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $(this).attr("disabled", true);
                            $.getJSON("/Car/PayTime", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.Success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.Message.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageIndex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); }
                    })
                });
                $(".btnCombox ul li").click(function () {
                    if ($(this).hasClass("select")) return;
                    var idName = $(this).parent().parent().attr("id");
                    $(this).siblings().removeClass("select");
                    $(this).addClass("select");
                    config[idName] = $(this).attr("data-value");

                    onEventCombox(idName);
                });
            },
            ShowCZFree: function (e) {
                var free = $(e).attr("data-value");
                var emoney = $(e).attr("data-emoney");
                var money = $(e).attr("data-money");
                if (free) {
                    $("#Car_Balance").val(emoney)
                    $("#Car_PayMoney").val(money)
                }
            }
        };

        var onEndDateTime = function (start, cycle, unit) {
            var d = new Date(start);
            if (unit == 1)
                d = new Date(d.setDate(d.getDate() + parseInt(cycle)));
            if (unit == 2)
                d = new Date(d.setMonth(d.getMonth() + parseInt(cycle)));
            if (unit == 3)
                d = new Date(d.setFullYear(d.getFullYear() + parseInt(cycle)));
            return d;
        }
    </script>
    <script type="text/javascript">
        //设备参数配置[仅选项按钮]默认值
        var config = {
            Rule_Type: 0,
        };

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == "Rule_Type") {
                if (config.Rule_Type == 1) {
                    $(".rule").slideUp("fast");
                    $("#payZQ").val("");
                    $("#Car_BeginTime,#Car_NEndTime,#Car_PayMoney").removeAttr("disabled");
                    $("#Car_PayMoney").val("");
                    layform.render("select");
                } else {
                    $(".rule").slideDown("fast");
                    $("#Car_BeginTime,#Car_NEndTime,#Car_PayMoney").attr("disabled", true);
                }
            }
        }
    </script>
</body>
</html>
