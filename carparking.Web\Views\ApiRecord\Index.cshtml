﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>记录管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>记录管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <select class="layui-input" id="ApiRecord_Type" name="ApiRecord_Type" lay-search>
                                    <option value="">类型</option>
                                    <option value="1">服务器下发</option>
                                    <option value="2">客户端上传</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="ApiRecord_Name" id="ApiRecord_Name" autocomplete="off" placeholder="名称" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="ApiRecord_Content" id="ApiRecord_Content" autocomplete="off" placeholder="内容" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="ApiRecord_Time1" id="ApiRecord_Time1" autocomplete="off" placeholder="起始时间" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="ApiRecord_Time2" id="ApiRecord_Time2" autocomplete="off" placeholder="截止时间" value="" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmpltype">
        {{# if(d.ApiRecord_Type==1){ }}
        <span class="layui-badge layui-bg-cyan">服务器下发</span>
        {{# }else if(d.ApiRecord_Type==2){ }}
        <span class="layui-badge layui-bg-orange">客户端上传</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.ApiRecord_Status==1){ }}
        <span class="layui-badge layui-bg-blue">发送成功</span>
        {{# }else if(d.ApiRecord_Status==0){ }}
        <span class="layui-badge layui-bg-orange">发送失败</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.use(['table', 'form', 'laydate'], function () {
            pager.init();

            var table = layui.table;

            //$("#ApiRecord_Time1").val(new Date().Format("yyyy-MM-dd"));
            //$("#ApiRecord_Time2").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["ApiRecord_Time1", "ApiRecord_Time2"], { type: 'date', range: true });

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ApiRecord/GetApiRecordList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: [[
                    { type: 'numbers' }
                    , { field: 'ApiRecord_Type', title: '类型', toolbar:"#tmpltype" }
                    , { field: 'ApiRecord_Name', title: '名称' }
                    , { field: 'ApiRecord_Event', title: '事件' }
                    , { field: 'ApiRecord_Status', title: '状态', toolbar: "#tmplstatus" }
                    , { field: 'ApiRecord_Res', title: '结果' }
                    , { field: 'ApiRecord_Time', title: '时间' }
                ]]
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
            });

            tb_row_radio(table);
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/ApiRecord/GetApiRecordList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }

    </script>
</body>
</html>
