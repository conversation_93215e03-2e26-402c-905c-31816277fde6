﻿@using carparking.BLL.Cache

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>数据备份</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>维护管理</cite></a>
                <a><cite>数据备份</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="SysFileInfo_Name" id="SysFileInfo_Name" autocomplete="off" placeholder="文件名" />
                            </div>
                            <div class="layui-inline form-group">
                                <select class="layui-select" lay-search id="SysFileInfo_Type" name="SysFileInfo_Type">
                                    <option value="">文件类型</option>
                                    <option value="1004">全库备份</option>
                                    <option value="1005">基础资料</option>
                                    <option value="1006">车辆信息</option>
                                    <option value="1007">停车订单</option>
                                    <option value="1008">核心数据</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                @if (!AppBasicCache.IsWindows)
                                {
                                    <button class="layui-btn" id="Create" style="line-height: 0;"><i class="layui-icon layui-icon-android inbtn"></i><t>手动备份</t></button>
                                }
                                @if (AppBasicCache.IsWindows)
                                {
                                    <button class="layui-btn" id="UnBind" style="line-height: 0;"><i class="layui-icon layui-icon-android inbtn"></i><t>手动还原</t></button>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                @if (!AppBasicCache.IsWindows)
                                {
                                                        <button class="layui-btn layui-btn-sm" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                                                        <button class="layui-btn layui-btn-sm" id="Bind" lay-event="Bind"><i class="fa fa-retweet"></i><t>数据还原</t></button>
                                                            <button class="layui-btn layui-btn-sm" id="Download" lay-event="Download"><i class="fa fa-download"></i><t>下载备份文件</t></button>
                                }

                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var comtable = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , {
                    field: 'SysFileInfo_Name', title: '文件名', templet: function (d) {
                        return '<a href="/Backup/' + d.SysFileInfo_FullName + '" id="download-temp" download="" class="lan-label">' + d.SysFileInfo_Name + '</a>';
                    }
                }
                , { field: 'SysFileInfo_Size', title: '文件大小' }
                , {
                    field: 'SysFileInfo_Type', title: '文件类型', templet: function (d) {
                        if (d.SysFileInfo_Type == "1004") return tempBar(1, "全库备份");
                        else if (d.SysFileInfo_Type == "1005") return tempBar(2, "基础资料");
                        else if (d.SysFileInfo_Type == "1006") return tempBar(3, "车辆信息");
                        else if (d.SysFileInfo_Type == "1007") return tempBar(4, "停车订单");
                        else if (d.SysFileInfo_Type == "1008") return tempBar(5, "核心数据");
                        else if (d.SysFileInfo_Type == "1009") return tempBar(6, "云端备份");
                        else return tempBar(5, "其它");
                    }
                }
                , { field: 'SysFileInfo_WriteTime', title: '文件时间' }
                , { field: 'SysFileInfo_AdminName', title: '创建人' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/DataBackup/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var nos = [];
                        data.forEach((item, index) => { nos.push(item.SysFileInfo_FullName); });
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除备份?(核心数据无法删除)",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/DataBackup/Delete", { DataBackup_No: JSON.stringify(nos) }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex, true); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 2500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Bind':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        pager.filename = data[0].SysFileInfo_FullName;
                        pager.showfilename = data[0].SysFileInfo_Name;
                        layer.open({
                            type: 2, id: 1,
                            title: "数据还原",
                            content: '/DataBackup/DataReStore?Act=Add',
                            area: getIframeArea(['900px', '620px']),
                            maxmin: true
                        });
                    case 'Download':
                        layer.open({
                            type: 2, id: 1,
                            title: "下载备份文件",
                            content: '/DataBackup/DownFile?Act=Add',
                            area: getIframeArea(['900px', '620px']),
                            maxmin: true
                        });

                        //layer.open({
                        //    type: 0,
                        //    title: "消息提示",
                        //    btn: ["确定", "取消"],
                        //    content: "执行数据还原（<span style='color:red;'>现有数据将会被清除无法恢复，数据会还原至该备份节点</span>）确定吗?",
                        //    yes: function (res) {
                        //        layer.msg("处理中", { icon: 16, time: 0 });
                        //        $.getJSON("/DataBackup/Bind", { filename: filename }, function (json) {
                        //            if (json.success)
                        //                layer.msg("还原成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                        //            else
                        //                layer.msg(json.msg, { icon: 0, time: 2500 });
                        //        });
                        //    },
                        //    btn2: function () { }
                        //})
                        break;
                };
            });

            tb_row_checkbox();

            pager.init();
        });

    </script>
    <script>
        var pager = {
            showfilename: "",//显示的名称
            filename: "",//实际文件名
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index, close) {
                if (close) layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/DataBackup/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1, true); });

                $("#UnBind").click(function () {
                    layer.open({
                        type: 2, id: 1,
                        title: "手动还原",
                        content: '/DataBackup/ReStore?Act=Add',
                        area: getIframeArea(['900px', '620px']),
                        maxmin: true
                    });
                });

                $("#Create").click(function () {
                    layer.open({
                        type: 2, id: 1,
                        title: "手动备份",
                        content: '/DataBackup/Backup?Act=Add',
                        area: getIframeArea(['900px', '620px']),
                        maxmin: true
                    });
                    //$.getJSON("/DataBackup/HandBackup", {}, function (json) {
                    //    if (json.success)
                    //        layer.msg("备份成功", { icon: 1, time: 1500 }, function () { });
                    //    else
                    //        layer.msg(json.msg, { icon: 0, time: 1500 });
                    //});
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            }
        }
    </script>
</body>
</html>
