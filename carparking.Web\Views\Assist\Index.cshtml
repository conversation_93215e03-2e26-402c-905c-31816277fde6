﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>处理记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa {
            margin: 6px 4px;
            float: left;
            font-size: 16px;
        }

        .layui-bg0-status {
            background-color: #00E5EE;
        }

        .layui-bg1-status {
            background-color: #96CDCD;
        }

        .layui-bg2-status {
            background-color: #FFA07A;
        }

        .layui-bg5-status {
            background-color: #023378;
        }

        .layui-bg6-status {
            background-color: #1f5141;
        }

        .layui-bg7-status {
            background-color: #7E95FB;
        }

        .layui-bg8-status {
            background-color: #969696;
        }

        .layui-bg9-status {
            background-color: #66FF99;
        }

        .layui-bg10-status {
            background-color: #FF9900;
        }

        .layui-bg11-status {
            background-color: #A48277;
        }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>处理记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">

                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_ParkOrderNo" id="Assist_ParkOrderNo" autocomplete="off" placeholder="停车订单号" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_CarNo" id="Assist_CarNo" autocomplete="off" placeholder="车牌号" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_DeviceTalkNo" id="Assist_DeviceTalkNo" autocomplete="off" placeholder="值班中心识别编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_DeviceTalkName" id="Assist_DeviceTalkName" autocomplete="off" placeholder="值班中心名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_AccountName" id="Assist_AccountName" autocomplete="off" placeholder="操作员" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_DeviceNo" id="Assist_DeviceNo" autocomplete="off" placeholder="设备编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_DeviceName" id="Assist_DeviceName" autocomplete="off" placeholder="设备名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_OwnerName" id="Assist_OwnerName" autocomplete="off" placeholder="车主名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="BeginTime" id="BeginTime" autocomplete="off" placeholder="处理时间起" value="" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="EndTime" id="EndTime" autocomplete="off" placeholder="处理时间止" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Assist_CallRecordNo" id="Assist_CallRecordNo" autocomplete="off" placeholder="通话记录编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.Assist.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.Assist.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.Assist.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/html" id="TmplCallStatus">
        {{#  if(d.Assist_Status=='0'){ }}
        <span class="layui-badge layui-bg0-status lan-status">未接听</span>
        {{#  } else if (d.Assist_Status=='1') { }}
        <span class="layui-badge layui-bg1-status lan-status">拒绝接听</span>
        {{#  } else if (d.Assist_Status=='2') { }}
        <span class="layui-badge layui-bg2-status lan-status">处理中</span>
        {{#  } else if (d.Assist_Status=='3') { }}
        <span class="layui-badge layui-bg-blue lan-status">处理完成</span>
        {{#  } else if (d.Assist_Status=='4') { }}
        <span class="layui-badge layui-bg-blue lan-status">取消处理</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-cyan lan-status">其它</span>
        {{#  } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        s_carno_picker.init("Assist_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "Assist_CarNo") {
                $("#Assist_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['table', 'form', 'laydate'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'Assist_No', title: '记录编号' , hide: true}
                , { field: 'Assist_CallRecordNo', title: '通话编号', hide: true }
                , { field: 'Assist_DeviceTalkNo', title: '值班中心识别编码', hide: true }
                , { field: 'Assist_DeviceTalkName', title: '值班中心名称' }
                , { field: 'Assist_Account', title: '操作账号', hide: true }
                , { field: 'Assist_AccountName', title: '操作员' }
                , { field: 'Assist_DeviceNo', title: '设备编码', hide: true }
                , { field: 'Assist_DeviceName', title: '设备名称' }
                , { field: 'Assist_PassWayNo', title: '车道编码', hide: true }
                , { field: 'Assist_PassWayName', title: '车道名称' }
                , { field: 'Assist_ParkOrderNo', title: '停车订单号', hide: true }
                , { field: 'Assist_CarNo', title: '车牌号' }
                , { field: 'Assist_OwnerNo', title: '车主编码', hide: true }
                , { field: 'Assist_OwnerName', title: '车主名称' }
                , { field: 'Assist_Time', title: '处理时间' }
                , { field: 'Assist_Status', title: '处理状态', toolbar: '#TmplCallStatus' }
                , { field: 'Assist_Content', title: '其他备注' }

            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Assist/GetAssistList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {

                };
            });

            tb_row_radio(table);

            pager.init();

            layui.form.render("select");
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["BeginTime", "EndTime"], { type: 'datetime', range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Assist/GetAssistList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
