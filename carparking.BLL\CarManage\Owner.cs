﻿using carparking.BLL.Cache;
using carparking.ChargeModels;
using carparking.Common;
using carparking.Model;
using carparking.Model.API;
using Dapper;
using FastDeepCloner;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static carparking.Model.API.PushResultParse;

namespace carparking.BLL
{
    public class Owner
    {
        static DAL.Owner dal = new DAL.Owner();

        #region 模板生成

        public static bool checkParam(Model.Owner model, List<Model.CarExt> cars, ref List<Model.StopSpace> spaces, ref List<Model.Car> carList, ref List<Model.Car> delCarList,
            ref List<string> newCarNoList, ref string errmsg, out bool isEditType, out Model.CarCardType cct, out Model.PayOrder payOrder, bool isEdit = false, decimal? Owner_Money = 0,
            decimal? Owner_PayedMoney = 0, Model.AdminSession lgAdmin = null)
        {
            payOrder = null;
            cct = null;
            isEditType = false;

            if (string.IsNullOrWhiteSpace(model.Owner_Space)) { errmsg = "系统车位号不能为空"; return false; }
            if (string.IsNullOrWhiteSpace(model.Owner_CardTypeNo)) { errmsg = "车牌类型不能为空"; return false; }
            if (!isEdit && Utils.IsLastCharLetter(model.Owner_Space)) { errmsg = "系统车位号不能为空"; return false; }

            if (!string.IsNullOrEmpty(model.Owner_ParkSpace)) { model.Owner_ParkSpace = Utils.ClearRiskSQL(model.Owner_ParkSpace); }
            if (!string.IsNullOrEmpty(model.Owner_ParkSpace) && Encoding.Default.GetBytes(model.Owner_ParkSpace).Length > 32) { errmsg = $"车场车位号不能超过32字符"; return false; }

            //if (model.Owner_SpaceNum == null) { errmsg = "车位数量不允许为空"; return false; }
            List<Model.Car> carBind = new List<Model.Car>();
            Model.Parking parking = BLL.Parking.GetEntity(model.Owner_ParkNo);
            cct = BLL.CarCardType.GetEntity(model.Owner_CardTypeNo);
            model.Owner_CardType = Common.CarTypeHelper.GetCarTypeIndex(cct.CarCardType_Category);

            if (!isEdit)
            {
                model.Owner_EnableOffline = cct.CarCardType_WhiteEnable ?? 0;
            }

            if (cct.CarCardType_Type != 5 && cct.CarCardType_Type != 6)
            {
                if (!isEdit && model.Owner_Balance < Owner_PayedMoney) { errmsg = $"支付金额不能大于应付金额"; return false; }
            }

            if (cct.CarCardType_Type == 2)
            {
                spaces = null;
            }
            else
            {
                if (!isEdit && model.Owner_StartTime > model.Owner_EndTime) { errmsg = $"有效期起【{model.Owner_StartTime.Value.ToString("yyyy-MM-dd 00:00:00")}】必须小于或等于有效期止【{model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59")}】"; return false; }
            }


            List<Model.Car> oldCarList = null;
            List<Model.Car> exist = null;
            if (cars != null && cars.Count > 0)
            {
                foreach (var car in cars)
                {
                    car.Car_CarNo = car.Car_CarNo.ToUpper().Trim();
                }

                //判断车牌号是否重复
                var carGroup = cars.GroupBy(x => x.Car_CarNo).Where(x => x.Count() > 1).ToList();
                if (carGroup.Count > 0) { errmsg = $"车牌号【{string.Join(",", carGroup.Select(x => x.Key))}】重复，请修改"; return false; }

                //List<string> carcardtypeList = cars.Select(x => x.Car_TypeNo).Distinct().ToList();
                Model.CarCardType card = BLL.CarCardType.GetEntity("CarCardType_No,CarCardType_Name,CarCardType_IsMoreCar,CarCardType_Category", $"CarCardType_No ='{model.Owner_CardTypeNo}'");//$"CarCardType_No in ('{string.Join("','", carcardtypeList)}')"

                List<string> errList = new List<string>();
                exist = BLL.Car.GetAllEntity("*", $"Car_CarNo in ('{string.Join("','", cars.Select(x => x.Car_CarNo).ToArray())}')");
                oldCarList = exist.Copy();

                if (exist != null && exist.Count > 0)
                {
                    foreach (var c in exist)
                    {
                        if (!string.IsNullOrWhiteSpace(c.Car_OwnerNo))
                        {
                            if (!isEdit)
                                errList.Add($"车牌号[{c.Car_CarNo}]已绑定其他车主");
                            else if (c.Car_OwnerNo != model.Owner_No)
                                errList.Add($"车牌号[{c.Car_CarNo}]已绑定其他车主");
                        }
                    }
                }
                if (errList.Count > 0) { errmsg = string.Join("<br />", errList); return false; }


                if (!isEdit)
                {
                    foreach (var car in cars)
                    {

                        car.Car_CarNo = car.Car_CarNo.ToUpper();
                        var current = cars.Find(x => !string.IsNullOrWhiteSpace(car.Car_CardNo) && x.Car_CardNo == car.Car_CardNo && x.Car_CarNo != car.Car_CarNo);
                        if (current != null) { errmsg = $"车牌号[{car.Car_CarNo} {current.Car_CarNo}]卡号不能重复登记"; return false; }

                        if (!string.IsNullOrWhiteSpace(car.Car_CardNo))
                        {
                            if (car.Car_CardNo.Length > 50) { errmsg = $"车牌号[{car.Car_CarNo}]卡号不能超过50个字符"; return false; }
                            else
                            {
                                var cardno = car.Car_CardNo;
                                car.Car_CardNo = Utils.RemoveInvalidStr(ref cardno);
                                var isExist2 = BLL.Car.GetEntityByCardNo(cardno);
                                if (isExist2 != null) { errmsg = $"车牌号[{car.Car_CarNo}]卡号已登记，不能重复登记"; return false; }
                            }
                        }
                    }
                }


                foreach (var x in cars)
                {
                    if (!string.IsNullOrEmpty(x.Car_CardNo) && x.Car_CardNo.Length > 50) { errmsg = $"车牌号[{x.Car_CarNo}]卡号不能超过50个字符"; return false; }

                    var car = exist.Find(item => item.Car_CarNo == x.Car_CarNo);
                    if (car == null)
                    {
                        car = new Model.Car()
                        {
                            Car_No = x.Car_No,
                            Car_CarNo = x.Car_CarNo,
                            Car_ParkingNo = model.Owner_ParkNo,
                            Car_AddTime = DateTimeHelper.GetNowTime(),
                            Car_Balance = 0,
                            Car_OnLine = 1,
                            Car_EnableOffline = model.Owner_EnableOffline,
                            Car_Status = 1,
                            Car_Remark = x.Car_Remark
                        };

                        newCarNoList.Add(car.Car_CarNo);
                    }

                    car.Car_OwnerSpace = model.Owner_Space;
                    car.Car_Colour = x.Car_Colour;
                    car.Car_License = x.Car_License;
                    car.Car_Model = x.Car_Model;
                    car.Car_VehicleTypeNo = x.Car_VehicleTypeNo;
                    car.Car_TypeNo = model.Owner_CardTypeNo;
                    car.Car_OwnerNo = model.Owner_No;
                    car.Car_OwnerName = model.Owner_Name;
                    car.Car_Status = 1;
                    car.Car_Remark = x.Car_Remark;
                    car.Car_Category = card?.CarCardType_Category;
                    car.Car_EnableOffline = model.Owner_EnableOffline;
                    car.Car_CardNo = x.Car_CardNo;
                    car.Car_Balance = 0;
                    car.Car_BeginTime = Utils.StrToDateTime(model.Owner_StartTime.Value.ToString("yyyy-MM-dd 00:00:00"));
                    car.Car_EndTime = Utils.StrToDateTime(model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                    car.Car_IsMoreCar = card?.CarCardType_IsMoreCar ?? 0;
                    car.Car_AddID = lgAdmin?.Admins_ID;
                    car.Car_AddTime = DateTimeHelper.GetNowTime();
                    carList.Add(car);

                    if (!isEditType && isEdit)
                    {
                        Model.Car oldCar = BLL.Car.GetEntityByCarNo(x.Car_CarNo);
                        isEditType = oldCar != null && (x.Car_VehicleTypeNo != oldCar.Car_VehicleTypeNo || x.Car_TypeNo != oldCar.Car_TypeNo);
                    }
                }
            }

            if (isEdit)
            {
                //Owner_Space系统车位号、Owner_ParkSpace车场车位号判断
                var oldOwner = BLL.Owner.GetEntity("*", $"Owner_Space='{model.Owner_Space}'");
                if (oldOwner != null && oldOwner.Owner_No != model.Owner_No) { errmsg = $"车位号[{model.Owner_Space}]已存在，不能重复登记"; return false; }

                if (!string.IsNullOrEmpty(model.Owner_ParkSpace))
                {
                    var oldOwner2 = BLL.Owner.GetEntity("Owner_ParkSpace", $"Owner_ParkSpace='{model.Owner_ParkSpace}' and Owner_No!='{model.Owner_No}'");
                    if (oldOwner2 != null) { errmsg = $"车场车位号[{model.Owner_ParkSpace}]已存在，不能重复登记"; return false; }
                }

                //查询车主已绑定的车辆
                carBind = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{model.Owner_No}'");
                //编辑时，如果解除车辆绑定，则需要修改车辆
                if (carBind != null && carBind.Count > 0)
                {
                    foreach (var c in carBind)
                    {
                        var have = cars == null ? null : cars.Find(x => x.Car_CarNo == c.Car_CarNo);
                        if (have == null)
                        {
                            delCarList.Add(c);
                        }
                    }
                }

                if (cars != null)
                {
                    foreach (var car in cars)
                    {
                        if (delCarList.Find(x => x.Car_CarNo.ToUpper() == car.Car_CarNo.ToUpper()) == null)
                        {
                            var current = cars.Find(x => !string.IsNullOrWhiteSpace(car.Car_CardNo) && x.Car_CardNo == car.Car_CardNo && x.Car_CarNo != car.Car_CarNo);
                            if (current != null) { errmsg = $"车牌号[{car.Car_CarNo} {current.Car_CarNo}]卡号不能重复登记"; return false; }
                        }

                        var old = oldCarList?.Find(x => x.Car_CarNo == car.Car_CarNo);
                        if (!string.IsNullOrWhiteSpace(car.Car_CardNo) && car.Car_CardNo != old?.Car_CardNo)
                        {
                            if (delCarList.Find(x => x.Car_CardNo == car.Car_CardNo) == null)
                            {
                                var isExistCar = BLL.Car.GetEntityByCardNo(car.Car_CardNo);
                                if (isExistCar != null && isExistCar.Car_CarNo != car.Car_CarNo) { errmsg = $"车牌号[{car.Car_CarNo}]卡号已存在，不能重复登记"; return false; }
                            }
                        }
                    }
                }


                bool changeType = false;
                bool checkSpace = true;
                if (oldOwner.Owner_CardType != model.Owner_CardType) changeType = true;
                if (!changeType)
                {
                    if (oldOwner.Owner_CardType == 2) checkSpace = false;
                }

                if (checkSpace)
                {
                    //判断车位数是否修改
                    var oldSpace = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "StopSpace_Type,StopSpace_AreaNo,StopSpace_Number", $"StopSpace_OwnerNo='{model.Owner_No}'");
                    if (spaces == null && oldSpace.Count > 0 || spaces.Count > 0 && oldSpace.Count == 0) isEditType = true;
                    else
                    {
                        if (spaces.Count > 0 && oldSpace.Count > 0)
                        {
                            if (spaces.Count != oldSpace.Count) isEditType = true;
                            else
                            {
                                foreach (var item in spaces)
                                {
                                    var old = oldSpace.Find(x => x.StopSpace_AreaNo == item.StopSpace_AreaNo);
                                    if (old == null) { isEditType = true; break; }
                                    else if (old.StopSpace_Number != item.StopSpace_Number) { isEditType = true; break; }
                                }

                                if (!isEditType)
                                {
                                    foreach (var item in oldSpace)
                                    {
                                        var old = spaces.Find(x => x.StopSpace_AreaNo == item.StopSpace_AreaNo);
                                        if (old == null) { isEditType = true; break; }
                                        else if (old.StopSpace_Number != item.StopSpace_Number) { isEditType = true; break; }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else
            {

                var oldOwner = BLL.Owner.GetEntity("Owner_Space", $"Owner_Space='{model.Owner_Space}'");
                if (oldOwner != null) { errmsg = $"车位号[{model.Owner_Space}]已存在，不能重复登记"; return false; }

                if (!string.IsNullOrEmpty(model.Owner_ParkSpace))
                {
                    var oldOwner2 = BLL.Owner.GetEntity("Owner_ParkSpace", $"Owner_ParkSpace='{model.Owner_ParkSpace}'");
                    if (oldOwner2 != null) { errmsg = $"车场车位号[{model.Owner_ParkSpace}]已存在，不能重复登记"; return false; }
                }

                //创建支付订单
                payOrder = BLL.PayOrder.CreateAddCarOrder(model.Owner_CardType.Value, parking, carList, model, Owner_Money, Owner_PayedMoney, cct, lgAdmin);
            }

            if (spaces?.Count > 0)
            {
                foreach (var item in spaces)
                {
                    item.StopSpace_ParkNo = model.Owner_ParkNo;
                    item.StopSpace_OwnerNo = model.Owner_No;
                    item.StopSpace_AddTime = DateTimeHelper.GetNowTime();
                }
            }
            else
            {
                spaces = spaces ?? new List<Model.StopSpace>();
                spaces?.Add(new Model.StopSpace()
                {
                    StopSpace_No = Utils.CreateNumber,
                    StopSpace_ParkNo = model.Owner_ParkNo,
                    StopSpace_OwnerNo = model.Owner_No,
                    StopSpace_Type = 0,
                    StopSpace_Number = cct.CarCardType_Type == 2 ? 0 : 1,
                    StopSpace_AddTime = DateTimeHelper.GetNowTime()
                });
            }

            return true;
        }

        /// <summary>
        /// 批量授权
        /// </summary>
        /// <param name="spaceJson">可停区域Json</param>
        /// <param name="ownerNoes">车主编码字符串，多个则逗号分隔</param>
        /// <returns></returns>
        public static bool BathAuthArea(string spaceJson, string ownerNoes, out List<Model.Owner> editOwnerList)
        {
            editOwnerList = null;
            List<Model.StopSpace> spaces = Utils.ClearListModelRiskSQL<Model.StopSpace>(spaceJson) ?? new List<Model.StopSpace>();
            var parameters = new { Owner_No = ownerNoes.Split(',').ToList() };
            List<Model.Owner> ownerList = BLL.Owner.GetAllEntity("*", $"Owner_No in @Owner_No", parameters);

            if (spaces.Count == 0 || ownerList.Count == 0) { return false; }

            List<string> sqlList = new List<string>();


            List<string> noesList = Utils.GetRandomLst(ownerList.Count * spaces.Count + 5);
            int? sumSpaceNum = spaces?.Sum(x => x.StopSpace_Number);

            foreach (var model in ownerList)
            {
                //判断车位数是否修改
                bool isEditType = false;
                var oldSpace = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "StopSpace_Type,StopSpace_AreaNo,StopSpace_Number", $"StopSpace_OwnerNo='{model.Owner_No}'");
                if (spaces == null && oldSpace.Count > 0 || spaces.Count > 0 && oldSpace.Count == 0) isEditType = true;
                else
                {
                    if (spaces.Count > 0 && oldSpace.Count > 0)
                    {
                        if (spaces.Count != oldSpace.Count) isEditType = true;
                        else
                        {
                            foreach (var item in spaces)
                            {
                                var old = oldSpace.Find(x => x.StopSpace_AreaNo == item.StopSpace_AreaNo);
                                if (old == null) { isEditType = true; break; }
                                else if (old.StopSpace_Number != item.StopSpace_Number) { isEditType = true; break; }
                            }

                            if (!isEditType)
                            {
                                foreach (var item in oldSpace)
                                {
                                    var old = spaces.Find(x => x.StopSpace_AreaNo == item.StopSpace_AreaNo);
                                    if (old == null) { isEditType = true; break; }
                                    else if (old.StopSpace_Number != item.StopSpace_Number) { isEditType = true; break; }
                                }
                            }
                        }
                    }
                }

                if (isEditType)
                {
                    string sql = $"DELETE FROM StopSpace WHERE StopSpace_OwnerNo ='{model.Owner_No}';";
                    sqlList.Add(sql);

                    foreach (var item in spaces)
                    {
                        var current = item.Copy();
                        current.StopSpace_No = noesList.Last();
                        noesList.Remove(noesList.Last());
                        current.StopSpace_ParkNo = model.Owner_ParkNo;
                        current.StopSpace_OwnerNo = model.Owner_No;
                        current.StopSpace_AddTime = DateTimeHelper.GetNowTime();
                        if (current.StopSpace_AreaNo == "[\"0\"]") current.StopSpace_Type = 0; else current.StopSpace_Type = 1;
                        string rSql = BLL.BaseBLL._GetAddSql(current);
                        sqlList.Add(rSql);
                    }
                }

                if (model.Owner_SpaceNum != sumSpaceNum)
                {
                    model.Owner_SpaceNum = sumSpaceNum;
                    editOwnerList = editOwnerList ?? new List<Model.Owner>();
                    editOwnerList.Add(model);
                    string rSql = BLL.BaseBLL._GetAddOrUpdateSql(model);
                    sqlList.Add(rSql);
                }
                else
                {
                    if (isEditType)
                    {
                        editOwnerList = editOwnerList ?? new List<Model.Owner>();
                        editOwnerList.Add(model);
                    }
                }
            }

            var r = BLL.BaseBLL._ExecuteTrans(sqlList);
            if (r >= 0)
            {
                ownerList.ForEach(x =>
                {
                    x = AES.Convert(x, false); AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x);
                });
            }
            return r > 0;
        }

        /// <summary>
        /// 增加一条数据,返回数据主键编号
        /// </summary>
        public static int Add(Model.Owner model)
        {
            if (!string.IsNullOrWhiteSpace(model.Owner_Phone) && model.Owner_Phone?.Length > 4)
                model.Owner_PhoneLastFour = model.Owner_Phone.Substring(model.Owner_Phone.Length - 4);
            return dal.Add(model);
        }


        public static int Insert(Model.Owner model, List<Model.Car> carList, List<Model.Car> delCarList, List<Model.StopSpace> spaces, Model.PayColl payColl, AdminSession lgAdmins = null, List<Model.Ledger> ledgerList = null)
        {
            List<Model.CarUnbound> unboundList = new List<Model.CarUnbound>();

            if (delCarList != null && delCarList.Count > 0)
            {
                foreach (var item in delCarList)
                {
                    unboundList.Add(new Model.CarUnbound()
                    {
                        CarUnbound_No = Utils.CreateNumberWith("UC"),
                        CarUnbound_CarNo = item.Car_CarNo,
                        CarUnbound_OwnerNo = model.Owner_No,
                        CarUnbound_ParkNo = model.Owner_ParkNo,
                        CarUnbound_AddTime = DateTimeHelper.GetNowTime(),
                        CarUnbound_OwnerName = item.Car_OwnerName,
                        CarUnbound_OwnerSpace = item.Car_OwnerSpace,
                        CarUnbound_CarCardTypeNo = item.Car_TypeNo,
                        CarUnbound_AdminName = lgAdmins?.Admins_Name,
                        CarUnbound_BeginTime = model.Owner_StartTime,
                        CarUnbound_EndTime = model.Owner_EndTime,
                        CarUnbound_CarTypeNo = item.Car_VehicleTypeNo,
                        CarUnbound_IsMoreCar = item.Car_IsMoreCar,
                        CarUnbound_RegTime = model.Owner_AddTime ?? item.Car_AddTime,
                    });
                }
            }
            if (!string.IsNullOrWhiteSpace(model.Owner_Phone) && model.Owner_Phone?.Length > 4)
                model.Owner_PhoneLastFour = model.Owner_Phone.Substring(model.Owner_Phone.Length - 4);
            else if (model.Owner_Phone != null)
                model.Owner_PhoneLastFour = "";

            model = AES.Convert(model);

            carList?.Remove(null);
            delCarList?.Remove(null);
            spaces?.Remove(null);
            unboundList?.Remove(null);

            var ret = dal.Insert(model, carList, delCarList, spaces, unboundList, payColl, ledgerList);
            if (ret > 0 && (AppBasicCache.ReadWriteCache))
            {
                model = AES.Convert(model, false);
                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, model.Owner_No, model);
                carList?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
                delCarList?.ForEach(x => { AppBasicCache.DeleteElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }

            return ret;
        }


        public static int Insert(Model.Owner model, List<Model.Car> carList, List<Model.Car> delCarList, List<Model.StopSpace> spaces, AdminSession lgAdmins = null, List<Model.Ledger> ledgerList = null)
        {
            List<Model.CarUnbound> unboundList = new List<Model.CarUnbound>();

            if (delCarList != null && delCarList.Count > 0)
            {
                foreach (var item in delCarList)
                {
                    unboundList.Add(new Model.CarUnbound()
                    {
                        CarUnbound_No = Utils.CreateNumberWith("UC"),
                        CarUnbound_CarNo = item.Car_CarNo,
                        CarUnbound_OwnerNo = model.Owner_No,
                        CarUnbound_ParkNo = model.Owner_ParkNo,
                        CarUnbound_AddTime = DateTimeHelper.GetNowTime(),
                        CarUnbound_OwnerName = item.Car_OwnerName,
                        CarUnbound_OwnerSpace = model.Owner_Space,
                        CarUnbound_CarCardTypeNo = item.Car_TypeNo,
                        CarUnbound_AdminName = lgAdmins?.Admins_Name,
                        CarUnbound_BeginTime = model.Owner_CardType != 2 ? model.Owner_StartTime : null,
                        CarUnbound_EndTime = model.Owner_CardType != 2 ? model.Owner_EndTime : null,
                        CarUnbound_CarTypeNo = item.Car_VehicleTypeNo,
                        CarUnbound_IsMoreCar = item.Car_IsMoreCar,
                        CarUnbound_RegTime = model.Owner_AddTime ?? item.Car_AddTime,
                    });
                }
            }
            if (!string.IsNullOrWhiteSpace(model.Owner_Phone) && model.Owner_Phone?.Length > 4)
                model.Owner_PhoneLastFour = model.Owner_Phone.Substring(model.Owner_Phone.Length - 4);
            else if (model.Owner_Phone != null)
                model.Owner_PhoneLastFour = "";

            model = AES.Convert(model);

            carList?.Remove(null);
            delCarList?.Remove(null);
            spaces?.Remove(null);
            unboundList?.Remove(null);
            var ret = dal.Insert(model, carList, delCarList, spaces, unboundList, null, ledgerList);
            if (ret > 0 && (AppBasicCache.ReadWriteCache))
            {
                model = AES.Convert(model, false);
                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, model.Owner_No, model);
                carList?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
                delCarList?.ForEach(x => { AppBasicCache.DeleteElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }

            return ret;
        }

        /// <summary>
        /// 删除车主信息，同时删除多车位多车 车辆信息
        /// </summary>
        /// <param name="model"></param>
        /// <param name="carList"></param>
        /// <returns></returns>
        public static int Delete(Model.Owner model, List<Model.Car> carList, List<Model.StopSpace> spaces)
        {
            return dal.Delete(model, carList, spaces);
        }

        /// <summary>
        /// 删除车主信息，同时删除多车位多车 车辆信息
        /// </summary>
        /// <param name="model"></param>
        /// <param name="carList"></param>
        /// <returns></returns>
        public static int DeleteList(List<Model.Owner> ownerList, List<Model.Car> carList, List<Model.StopSpace> spaces, AdminSession lgAdmins = null)
        {
            var ret = dal.Delete(ownerList, carList, spaces, lgAdmins);
            if (ret > 0 && (AppBasicCache.ReadWriteCache))
            {
                ownerList?.ForEach(x => { AppBasicCache.DeleteElement(AppBasicCache.GetOwner, x.Owner_No, x); });
                carList?.ForEach(x => { AppBasicCache.DeleteElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }

            return ret;
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public static Model.Owner GetEntity(string Owner_No, bool ReadCeche = true)
        {
            if (string.IsNullOrWhiteSpace(Owner_No)) return null;

            if (ReadCeche)
            {
                var model = AppBasicCache.GetElement(AppBasicCache.GetOwner, Owner_No);
                if (model != null) return (Model.Owner)DeepCloner.Clone(model); else return null;
            }
            else
            {
                var model = dal.GetEntity(Owner_No);
                model = AES.Convert(model, false);
                return model;
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public static Model.OwnerExt GetOwnerExt(string fields, string selectWhere, object parameters = null)
        {
            var model = dal.GetExtEntity(fields, selectWhere, parameters);
            model = AES.Convert(model, false);
            return model;
        }


        public static Model.Owner GetEntityBySpaceNo(string Owner_Space)
        {
            var model = dal.GetEntity("*", $"Owner_Space='{Owner_Space}'");
            model = AES.Convert(model, false);
            return model;
        }

        public static Model.Owner GetEntity(string showFileds, string selectWhere, object parameters = null)
        {
            var model = dal._GetEntityByWhere(new Model.Owner(), showFileds, selectWhere, parameters);
            model = AES.Convert(model, false);
            return model;
        }

        public static Model.OwnerExt GetExtEntity(string showFileds, string selectWhere)
        {
            var model = dal._GetEntityByWhere(new Model.OwnerExt(), showFileds, selectWhere);
            model = AES.Convert(model, false);
            return model;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.Owner> GetList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord)
        {
            var lst = dal.GetList(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord);
            lst = AES.Convert(lst, false);
            return lst;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.OwnerExt> GetExtList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord, object parameters = null)
        {
            var lst = dal.GetExtList(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters);
            lst = AES.Convert(lst, false);
            return lst;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.OwnerExt> GetExtList(string showFields, string selectWhere, int pageIndex, int pageSize, string sortField, int sortType, out int pageCount, out int totalRecord, object parameters = null)
        {
            var lst = dal.GetExtList(showFields, selectWhere, pageIndex, pageSize, sortField, sortType, out pageCount, out totalRecord, parameters);
            lst = AES.Convert(lst, false);
            return lst;
        }

        /// <summary>
        /// 获取少量实体
        /// </summary>
        /// <param name="showFields">字段</param>
        /// <param name="selectWhere">查询条件</param>
        /// <returns></returns>
        public static List<Model.Owner> GetAllEntity(string showFields, string selectWhere, object parameters = null)
        {
            var lst = dal.GetAllEntity(showFields, selectWhere, parameters);
            lst = AES.Convert(lst, false);
            return lst;
        }

        #endregion

        #region 其他方法

        public static int Insert(List<Model.Owner> models)
        {
            return dal._Insert(models);
        }

        public static int PersonPush(Model.Owner model, Model.StopSpace space)
        {
            if (!string.IsNullOrWhiteSpace(model.Owner_Phone) && model.Owner_Phone?.Length > 4)
                model.Owner_PhoneLastFour = model.Owner_Phone.Substring(model.Owner_Phone.Length - 4);
            else if (model.Owner_Phone != null)
                model.Owner_PhoneLastFour = "";

            model = AES.Convert(model);

            var ret = dal.PersonPush(model, space);
            if (ret > 0 && (AppBasicCache.ReadWriteCache))
            {
                AES.Convert(model, false);
                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, model.Owner_No, model);
            }
            return ret;
        }

        public static int OnSpacePayCharge(Model.Owner model, Model.PayColl payColl, List<Model.Car> cars)
        {
            model = AES.Convert(model);
            var ret = dal.OnSpacePayCharge(model, payColl, cars);
            if (ret > 0 && (AppBasicCache.ReadWriteCache))
            {
                AES.Convert(model, false);
                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, model.Owner_No, model);
                cars?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }
            return ret;
        }

        public static int OnSpacePayChargeList(List<Model.Owner> models, Model.PayColl payColl, List<Model.Car> cars, List<Model.Ledger> ledgerList = null)
        {
            models.ForEach(x => { x = AES.Convert(x); });
            var ret = dal.OnSpacePayCharge(models, payColl, cars, ledgerList);
            if (ret > 0 && (AppBasicCache.ReadWriteCache))
            {
                models?.ForEach(x => { x = AES.Convert(x, false); AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x); });
                cars?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }
            return ret;
        }

        /// <summary>
        /// 检查车辆修改，一位多车的情况
        /// </summary>
        /// <param name="model"></param>
        /// <param name="carList"></param>
        public static void CheckOwnerMoreCar(Model.Owner model, List<Model.Car> carList)
        {

        }


        /// <summary>
        /// 生成停车场车位编号（自定义序列编号格式：'A' + 该停车场已存在的CarSpace包含的最大数字 + 1）
        /// </summary>
        /// <param name="Parking_Key"></param>
        /// <returns></returns>
        public static List<string> GeneralCarSpaceNoList(string Parking_No, int RowCount)
        {
            int tempNum = 0;
            string resultCarSpaceNo = "00001", CarSpaceNo = string.Empty;
            List<string> carSpaceNoList = new List<string>();

            Model.Owner csCarSpace = dal.GetLastCarSpace(Parking_No);
            CarSpaceNo = csCarSpace != null ? csCarSpace.Owner_Space : "S00000";

            for (int i = 0; i < RowCount; i++)
            {
                tempNum = 0;
                if (int.TryParse(CarSpaceNo.Substring(1, 5), out tempNum))
                {
                    tempNum += 1; //递增+1
                    resultCarSpaceNo = Utils.Fill0ToStr(tempNum.ToString(), 5);
                }
                else
                {
                    return null;
                }
                CarSpaceNo = "S" + resultCarSpaceNo;
                carSpaceNoList.Add(CarSpaceNo);
            }

            return carSpaceNoList;
        }


        public static Model.Owner GetLast()
        {
            return dal.GetLast();
        }

        public static Model.Owner GetJKLast()
        {
            return dal.GetJKLast();
        }

        /// <summary>
        /// 创建或修改车主信息
        /// </summary>
        /// <param name="parkno">车场编号</param>
        /// <param name="userNo">云平台用户编号-对应车位号</param>
        /// <param name="userName">姓名</param>
        /// <param name="sex">性别，男：女</param>
        /// <param name="mobNumber">手机号</param>
        /// <param name="homeAddress">住址</param>
        /// <param name="carSpalcesNum">车位数量</param>
        /// <param name="sPasswayLinks">授权车道区域</param>
        /// <returns></returns>
        public static Model.Owner NewOwner(string cardTypeNo, int? cardType, string parkno, string userNo, string userName, string sex, string mobNumber,
            string homeAddress, string carSpalcesNum, string sPasswayLinks, int? whiteEnable, out Model.StopSpace space)
        {
            space = null;
            var model = BLL.Owner.GetEntity("*", $"Owner_Space='{userNo}'");
            if (model == null)
            {
                model = new Model.Owner();
                model.Owner_No = userNo;
                model.Owner_ParkNo = parkno;
                model.Owner_Name = userName;
                model.Owner_Sex = sex == "男" ? 1 : (sex == "女" ? 2 : 0);
                model.Owner_Address = homeAddress;
                model.Owner_Phone = mobNumber;
                model.Owner_SpaceNum = Utils.ObjectToInt(carSpalcesNum, 1);
                model.Owner_AddTime = DateTimeHelper.GetNowTime();
                model.Owner_Space = userNo;
                model.Owner_StartTime = DateTimeHelper.GetNowTime().AddDays(-1);
                model.Owner_EndTime = DateTimeHelper.GetNowTime().AddDays(-1);
                model.Owner_CardTypeNo = cardTypeNo;
                model.Owner_CardType = cardType;
                model.Owner_EnableOffline = whiteEnable ?? 1;
                space = getSpace(carSpalcesNum, sPasswayLinks, model);
            }
            else
            {
                model.Owner_Name = !string.IsNullOrWhiteSpace(userName) ? userName : model.Owner_Name;
                model.Owner_Address = !string.IsNullOrWhiteSpace(homeAddress) ? homeAddress : model.Owner_Address;
                model.Owner_Phone = !string.IsNullOrWhiteSpace(mobNumber) ? mobNumber : model.Owner_Phone;
                model.Owner_SpaceNum = Utils.ObjectToInt(carSpalcesNum, model.Owner_SpaceNum ?? 1);
                model.Owner_Space = userNo;
                model.Owner_Sex = !string.IsNullOrWhiteSpace(sex) ? (sex == "男" ? 1 : (sex == "女" ? 2 : 0)) : model.Owner_Sex;

                var spaceData = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{model.Owner_No}'");
                //没有添加区域车位
                if (spaceData == null)
                {
                    space = getSpace(carSpalcesNum, sPasswayLinks, model);
                }
                //只有一个区域车位
                else if (spaceData.Count == 1)
                {
                    var space1 = getSpace(carSpalcesNum, sPasswayLinks, model);
                    spaceData[0].StopSpace_Number = Utils.StrToInt(carSpalcesNum, 1);
                    spaceData[0].StopSpace_Type = space1.StopSpace_Type;
                    spaceData[0].StopSpace_AreaNo = space1.StopSpace_AreaNo;
                    spaceData[0].StopSpace_AreaName = space1.StopSpace_AreaName;
                    spaceData[0].StopSpace_Content = space1.StopSpace_Content;
                    space = spaceData[0];
                }
            }

            return model;
        }

        public static StopSpace getSpace(string carSpalcesNum, string sPasswayLinks, Model.Owner model, List<Model.ParkArea> parkareas = null, List<Model.PasswayLink> allpasslinks = null)
        {
            StopSpace space;
            var pNoes = sPasswayLinks.Split(',');
            if (allpasslinks == null) allpasslinks = AppBasicCache.ReadWriteCache ? AppBasicCache.GetAllPasswayLink.Values.Where(x => pNoes.Contains(x.PasswayLink_PasswayNo)).ToList() : BLL.PasswayLink.GetAllEntity("*", $"PasswayLink_PasswayNo IN('{string.Join("','", sPasswayLinks.Split(','))}')");
            var passlinks = allpasslinks.Where(x => pNoes.Contains(x.PasswayLink_PasswayNo)).ToList();
            //var f = passlinks.GroupBy(m => m.PasswayLink_ParkAreaNo);
            //入场区域才需要添加车位数
            //var f = passlinks?.GroupBy(m => m.PasswayLink_ParkAreaNo);
            if (passlinks != null && passlinks.Count() != 0)
            {
                if (parkareas == null) parkareas = AppBasicCache.ReadWriteCache ? AppBasicCache.GetParkAreas.Values.ToList() : BLL.ParkArea.GetAllEntity("*", $"1=1");
                List<Model.PasswayLinkExt> linkExts = new List<PasswayLinkExt>(0);
                foreach (var f1 in passlinks)
                {
                    if (!linkExts.Exists(a => a.PasswayLink_ParkAreaNo == f1.PasswayLink_ParkAreaNo))
                    {
                        var area = parkareas.Where(x => x.ParkArea_No == f1.PasswayLink_ParkAreaNo).FirstOrDefault();
                        if ((area?.ParkArea_Level == 0 && !passlinks.Exists(c => c.PasswayLink_PasswayNo == f1.PasswayLink_PasswayNo && c.PasswayLink_GateType != f1.PasswayLink_GateType)) || area?.ParkArea_Level > 0)
                        {
                            Model.PasswayLinkExt passwayExt = TyziTools.Json.ToObject<Model.PasswayLinkExt>(TyziTools.Json.ToString(f1));
                            passwayExt.ParkArea_Name = area?.ParkArea_Name;
                            linkExts.Add(passwayExt);
                        }
                    }
                }

                space = new Model.StopSpace()
                {
                    StopSpace_No = Utils.CreateNumber,
                    StopSpace_Number = Utils.StrToInt(carSpalcesNum, 0),
                    StopSpace_ParkNo = model.Owner_ParkNo,
                    StopSpace_OwnerNo = model.Owner_No,
                    StopSpace_Type = 1,
                    StopSpace_AddTime = DateTimeHelper.GetNowTime(),
                    StopSpace_AreaNo = Common.TyziTools.Json.ToString(linkExts.Select(m => m.PasswayLink_ParkAreaNo)),
                    StopSpace_AreaName = string.Join("、", linkExts.Select(m => m.ParkArea_Name)),
                    StopSpace_Content = "平台下发授权区域信息"
                };
            }
            else
            {
                space = new Model.StopSpace()
                {
                    StopSpace_No = Utils.CreateNumber,
                    StopSpace_Number = Utils.StrToInt(carSpalcesNum, 0),
                    StopSpace_ParkNo = model.Owner_ParkNo,
                    StopSpace_OwnerNo = model.Owner_No,
                    StopSpace_Type = 0,
                    StopSpace_AddTime = DateTimeHelper.GetNowTime()
                };
            }

            return space;
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public static int UpdateByList(List<Model.Owner> modelList)
        {
            var ret = dal.UpdateByList(modelList);
            return ret;
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public static int UpdateByList(List<Model.Owner> modelList, List<Model.StopSpace> ssList)
        {
            var ret = dal.UpdateByList(modelList, ssList);
            return ret;
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public static int UpdateByList(List<Model.Owner> modelList, List<Model.Car> carList, List<Model.StopSpace> ssList = null)
        {
            var ret = dal.UpdateByList(modelList, carList, ssList);
            if (ret > 0)
            {
                modelList?.ForEach(item =>
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, item.Owner_No, item);
                });
                carList?.ForEach(item =>
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, item.Car_CarNo, item);
                });
            }
            return ret;
        }

        /// <summary>
        /// 充值延期时，查询场内车辆修改订单（一位多车需要计算是否占用车位）
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="cars"></param>
        public static void ChangeInParkCarByPay(Model.Owner owner, List<Model.Car> cars, ref List<Model.API.PushResultParse.CarOwnerPayDate> data)
        {
            data = new List<CarOwnerPayDate>();
            List<Model.StopSpace> stopsapceList = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{owner?.Owner_No}'");
            if (stopsapceList?.Count > 0)
            {
                //判断是否有车位
                var spacesNum = stopsapceList.Sum(x => x.StopSpace_Number);
                if (spacesNum > 0)
                {
                    //查询场内车
                    List<Model.InCar> inCarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "*", $"Incar_Status=200 and Incar_CarNo in ('{string.Join("','", cars?.Select(x => x.Car_CarNo))}')");
                    if (inCarList?.Count > 0)
                    {
                        //只有一辆车在场内，直接做车位升降处理，占用车位
                        if (inCarList.Count == 1)
                        {
                            List<Model.OrderDetail> newDetailList = new List<Model.OrderDetail>();
                            Model.ParkOrder parkOrder = BLL.ParkOrder.GetEntity(inCarList[0].InCar_ParkOrderNo);
                            if (parkOrder != null)
                            {
                                List<Model.OrderDetail> odList = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo='{parkOrder.ParkOrder_No}'");
                                if (odList?.Count > 0)
                                {
                                    //直接改升降状态，计费时会根据该状态计费。如果是1，则不计费了。
                                    if (parkOrder.ParkOrder_IsLift == 1) parkOrder.ParkOrder_IsLift = 2;

                                    List<string> orderNoList = Utils.GetRandomLst(odList.Count);
                                    bool havaSpace = false;
                                    foreach (var detail in odList)
                                    {
                                        //车位配置判断
                                        if (stopsapceList.Find(x => x.StopSpace_Type == 0 && x.StopSpace_Number > 0) != null) havaSpace = true;
                                        if (!havaSpace && stopsapceList.Find(x => x.StopSpace_Type == 1 && x.StopSpace_AreaNo == detail.OrderDetail_ParkAreaNo && x.StopSpace_Number > 0) != null) havaSpace = true;

                                        string OrderDetail_No = BLL.OrderDetail.NewOrderNo(detail.OrderDetail_CarNo, orderNoList.First());
                                        orderNoList.RemoveAt(0);
                                        BLL.Owner.CarTimeChange(owner, detail, ref newDetailList, ref parkOrder, OrderDetail_No, havaSpace);
                                    }

                                    //data.Add(new CarOwnerPayDate() { Item4 = new List<Model.ParkOrder>() { parkOrder }, Item5 = newDetailList });
                                }
                            }
                        }
                        else
                        //多辆车在场内
                        {
                            List<Model.OrderDetail> newDetailList = new List<Model.OrderDetail>();
                            List<Model.OrderDetail> odList = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')");
                            odList = odList?.OrderBy(x => x.OrderDetail_EnterTime).ToList();


                            List<Model.ParkOrder> poList = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in ('{string.Join("','", inCarList.Select(x => x.InCar_ParkOrderNo))}')");
                            List<string> orderNoList = Utils.GetRandomLst(odList.Count);
                            foreach (var detail in odList)
                            {
                                detail.orderdetail_IsCharge = 0;
                                Model.ParkOrder parkOrder = poList.Find(x => x.ParkOrder_No == detail.OrderDetail_ParkOrderNo);
                                if (parkOrder != null)
                                {
                                    string OrderDetail_No = BLL.OrderDetail.NewOrderNo(detail.OrderDetail_CarNo, orderNoList.First());
                                    orderNoList.RemoveAt(0);
                                    BLL.Owner.CarTimeChange(owner, detail, ref newDetailList, ref parkOrder, OrderDetail_No, false);
                                }
                            }
                            newDetailList = newDetailList?.OrderBy(x => x.OrderDetail_EnterTime).ToList();

                            //Dictionary<string, int> useAreaDic = new Dictionary<string, int>(); //区域车位占用数:key-区域编码，value-已用车位数
                            var areaList = odList?.GroupBy(x => x.OrderDetail_ParkAreaNo).Select(x => x.Key).ToList() ?? new List<string>();
                            int allTypeNum = stopsapceList.FindAll(x => x.StopSpace_Type == 0)?.Sum(x => x.StopSpace_Number) ?? 0;//全部区域的车位数
                            //int oldAllTypeNum = allTypeNum.Copy();

                            //遍历各个区域的场内车辆情况
                            for (int j = 0; j < areaList.Count; j++)
                            {
                                var recordList = newDetailList?.FindAll(x => x.OrderDetail_ParkAreaNo == areaList[j])?.ToList();
                                if (recordList?.Count > 0)
                                {
                                    //当前区域的车位数
                                    int currentAreaTypeNum = stopsapceList.FindAll(x => x.StopSpace_Type == 1 && x.StopSpace_AreaNo == areaList[j])?.Sum(x => x.StopSpace_Number) ?? 0;
                                    for (var i = 0; i < recordList.Count; i++)
                                    {
                                        Model.ParkOrder parkOrder = poList.Find(x => x.ParkOrder_No == recordList[i].OrderDetail_ParkOrderNo);
                                        if (parkOrder != null)
                                        {
                                            //当前区域的场内车辆
                                            var list = recordList.FindAll(x => x.OrderDetail_EnterTime < recordList[i].OrderDetail_EnterTime && x.OrderDetail_OutTime == null ||
                                                                 x.OrderDetail_EnterTime < recordList[i].OrderDetail_EnterTime && x.OrderDetail_OutTime > recordList[i].OrderDetail_EnterTime);
                                            //当前区域的场内车辆数量
                                            var inCarNum = list?.GroupBy(x => x.OrderDetail_CarNo).Count() ?? 0;

                                            //有车位
                                            if (allTypeNum > 0 || currentAreaTypeNum > 0)
                                            {
                                                if (allTypeNum + currentAreaTypeNum > inCarNum)
                                                {
                                                    if (parkOrder.ParkOrder_IsLift == 1) parkOrder.ParkOrder_IsLift = 2;
                                                    recordList[0].orderdetail_IsCharge = 1;
                                                    if (allTypeNum > 0) allTypeNum--;
                                                }
                                            }
                                            else
                                            //无车位
                                            {
                                                if (parkOrder.ParkOrder_IsLift == 1) parkOrder.ParkOrder_IsLift = 2;
                                                recordList[0].orderdetail_IsCharge = 0;
                                            }
                                            //var item = data.Find(x => x.Item4.Find(m => m.ParkOrder_No == parkOrder.ParkOrder_No) != null);
                                            //if (item != null)
                                            //{
                                            //    item.Item5.Add(recordList[i]);
                                            //}
                                            //else
                                            //{
                                            //    data.Add(new CarOwnerPayDate() { Item4 = new List<Model.ParkOrder>() { parkOrder }, Item5 = new List<Model.OrderDetail>() { recordList[i] } });
                                            //}
                                        }
                                    }
                                }
                            };
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 根据固定车有效期，停车明细的拆分与状态修改
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="detail"></param>
        /// <param name="newDetailList"></param>
        /// <param name="parkOrder"></param>
        public static void CarTimeChange(Model.Owner owner, Model.OrderDetail detail, ref List<Model.OrderDetail> newDetailList, ref Model.ParkOrder parkOrder, string newNo = "", bool haveSpace = true)
        {
            if (detail.OrderDetail_EnterTime < owner.Owner_StartTime)
            {
                if (detail.OrderDetail_OutTime > owner.Owner_EndTime.Value.AddSeconds(1))
                {
                    Model.OrderDetail newDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                    newDetail.OrderDetail_No = string.IsNullOrWhiteSpace(newNo) ? detail.OrderDetail_No : newNo;
                    newDetail.OrderDetail_EnterTime = owner.Owner_EndTime.Value.AddSeconds(1);
                    newDetail.orderdetail_IsCharge = 0;
                    newDetailList.Add(newDetail);

                    if (detail.OrderDetail_StatusNo < 202) detail.OrderDetail_StatusNo = 201;
                    detail.OrderDetail_OutTime = owner.Owner_StartTime;
                    detail.orderdetail_IsCharge = 0;
                    newDetailList.Add(detail);
                    parkOrder.ParkOrder_IsLift = 0;
                }
                else
                {
                    if (detail.OrderDetail_OutTime > owner.Owner_StartTime)
                    {
                        Model.OrderDetail newDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                        newDetail.OrderDetail_No = string.IsNullOrWhiteSpace(newNo) ? detail.OrderDetail_No : newNo;
                        newDetail.OrderDetail_EnterTime = owner.Owner_StartTime.Value.AddSeconds(1);
                        newDetail.orderdetail_IsCharge = 0;
                        newDetailList.Add(newDetail);

                        if (detail.OrderDetail_StatusNo < 202) detail.OrderDetail_StatusNo = 201;
                        detail.OrderDetail_OutTime = owner.Owner_StartTime;
                        detail.orderdetail_IsCharge = 0;
                        newDetailList.Add(detail);
                        parkOrder.ParkOrder_IsLift = 0;
                    }
                    else
                    {
                        parkOrder.ParkOrder_IsLift = 0;
                        detail.orderdetail_IsCharge = 0;
                        newDetailList.Add(detail);
                    }
                }
            }
            else
            {
                if (detail.OrderDetail_EnterTime > owner.Owner_EndTime.Value.AddSeconds(1))
                {
                    parkOrder.ParkOrder_IsLift = 0;
                    detail.orderdetail_IsCharge = 0;
                    newDetailList.Add(detail);
                }
                else
                {
                    if (detail.OrderDetail_OutTime > owner.Owner_EndTime.Value.AddSeconds(1))
                    {
                        Model.OrderDetail newDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                        newDetail.OrderDetail_No = string.IsNullOrWhiteSpace(newNo) ? detail.OrderDetail_No : newNo;
                        newDetail.OrderDetail_OutTime = owner.Owner_EndTime;
                        newDetail.orderdetail_IsCharge = haveSpace ? 1 : 0;
                        newDetailList.Add(newDetail);

                        parkOrder.ParkOrder_IsLift = 0;
                        if (detail.OrderDetail_StatusNo < 202) detail.OrderDetail_StatusNo = 201;
                        detail.OrderDetail_EnterTime = owner.Owner_EndTime.Value.AddSeconds(1);
                        detail.orderdetail_IsCharge = 0;
                        newDetailList.Add(detail);
                    }
                    else
                    {
                        newDetailList.Add(detail);
                    }
                }
            }
        }

        /// <summary>
        /// 自定义延期修改了车主有效开始时间，检测
        /// </summary>
        /// <param name="model"></param>
        /// <param name="param"></param>
        public static bool ModifyOwnerStartTime(Model.Owner model, Model.SpaceCharge param, List<Model.Car> cars, out string errorMsg, out bool isReadCarTime, out bool isExpireSpace, out bool isInPark)
        {
            isReadCarTime = false;
            isExpireSpace = false;
            isInPark = false;
            errorMsg = string.Empty;

            var isSucess = true;
            //修改了开始日期
            if (model.Owner_EndTime.Value.AddDays(1).ToString("yyyy-MM-dd") != param.Owner_StartTimeYQ.Value.ToString("yyyy-MM-dd"))
            {
                if (param.InCar_EnterTime != null && param.Owner_StartTimeYQ.Value.ToString("yyyy-MM-dd") == param.InCar_EnterTime.Value.ToString("yyyy-MM-dd"))
                {
                    isReadCarTime = true;
                    //return true;//读取了场内车辆的入场时间
                }

                if (model.Owner_EndTime > DateTime.Now)
                {
                    errorMsg = "车位未过期，请不要修改续期开始日期";
                    //return false;//未过期
                    if (!isReadCarTime) isSucess = false;
                }
                else
                {
                    isExpireSpace = true;
                }

                var list = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_CarNo", $"InCar_Status=200 and InCar_CarNo in('{string.Join("','", cars?.Select(x => x.Car_CarNo))}')");
                if (list?.Count > 0)
                {
                    isInPark = true;
                    errorMsg = $"车辆[{string.Join(",", list.Select(x => x.InCar_CarNo))}]在场内，请不要修改续期开始日期";
                    //return false;//有车在场内
                    if (!isReadCarTime) isSucess = false;
                }

            }
            return isSucess;
        }

        /// <summary>
        /// 增加白名单管理记录
        /// </summary>
        /// <param name="carList"></param>
        public static void InsertWhiteRecord(List<Model.Car> carList)
        {
            //if (carList?.Count > 0)
            //{
            //    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
            //    {
            //        try
            //        {
            //            var devices = BLL.Device.GetAllEntity("Device_No,Device_SentryHostNo", "Device_Category=1 and Device_SentryHostNo!=''");
            //            var sentrys = BLL.SentryHost.GetAllEntity();

            //            List<Model.WhiteRecord> wrList = new List<WhiteRecord>();

            //            sentrys?.ForEach(item =>
            //            {
            //                if (item != null)
            //                {
            //                    var subDevices = devices.Where(x => x.Device_SentryHostNo == item.SentryHost_No).ToList();
            //                    subDevices.ForEach(device =>
            //                    {
            //                        if (device != null)
            //                        {
            //                            carList.ForEach(car =>
            //                            {
            //                                if (car.Car_EnableOffline == 1)
            //                                    wrList.Add(new Model.WhiteRecord()
            //                                    {
            //                                        WhiteRecord_CarNo = car.Car_CarNo,
            //                                        WhiteRecord_DeviceNo = device.Device_No,
            //                                        WhiteRecord_Time = DateTime.Now
            //                                    });
            //                            });
            //                        }
            //                    });
            //                }
            //            });

            //            if (wrList.Count > 0)
            //            {
            //                List<string> sqlList = new List<string>();
            //                wrList.ForEach(x => { sqlList.Add(BLL.BaseBLL._GetAddOrUpdateSql(x)); });

            //                var r = BLL.BaseBLL._ExecuteTrans(sqlList);
            //                if (r < 0)
            //                {
            //                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "添加白名单管理记录执行SQL错误");
            //                }
            //            }
            //        }
            //        catch (Exception ex)
            //        {

            //            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "添加白名单管理记录异常：" + ex.ToString());
            //        }
            //        return Task.CompletedTask;
            //    });
            //}
        }
        #endregion

        #region 清缴费用
        /// <summary>
        /// 清缴场内车辆费用
        /// </summary>
        /// <param name="Parking_No"></param>
        /// <param name="cct"></param>
        /// <param name="AllCars">当前车主所有绑定的车辆</param>
        /// <param name="cars">当前需要处理的车辆</param>
        /// <param name="addInCar"></param>
        /// <param name="lgAdmins"></param>
        /// <param name="errmsg"></param>
        /// <param name="orderHandleResult"></param>
        /// <returns></returns>
        public static string PayedInParkCarFee(string Parking_No, Model.Owner model, Model.CarCardType cct,
            List<Model.CarExt> AllCars, List<Model.CarExt> cars, List<Model.StopSpace> spaces, ref List<Model.AddInParkTempCar> addInCar, AdminSession lgAdmins,
            out string errmsg, out bool orderHandleResult, bool isChangeDate = false)
        {
            errmsg = "";
            orderHandleResult = false;

            if (cct != null && cars != null && cars.Count > 0)
            {
                List<Model.ParkOrder> parkOrderList = BLL.ParkOrder.GetParkOrderByOwnerCarNo("*", Parking_No, null, AllCars.Select(x => x.Car_CarNo).ToList(), null, 200);//cars[0].Owner_No

                List<Model.InCar> incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo in('{string.Join("','", AllCars.Select(c => c.Car_CarNo))}') and InCar_Status=200 ");
                var allDetailList = new List<Model.OrderDetail>();
                if (incarList.Count > 0)
                {
                    allDetailList = BLL.OrderDetail.GetAllEntity("*", $"orderdetail_ParkOrderNo in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}') and orderdetail_StatusNo=200");
                }

                if (parkOrderList.Count > 0 && isChangeDate)
                {

                    foreach (var order in parkOrderList)
                    {
                        if (order.ParkOrder_EnterTime < model.Owner_StartTime)//过期了
                        {

                            var car = AllCars.Find(x => x.Car_CarNo == order.ParkOrder_CarNo);
                            Model.CarType carType = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                            var currDetails = BLL.OrderDetail.GetAllEntity(order.ParkOrder_No);
                            List<Model.ParkOrder> orders1 = null;
                            List<Model.OrderDetail> details = null;

                            orderHandleResult = BLL.Car.NewParkOrderAndCloseOldOrder(order.ParkOrder_No, car, cct, carType, currDetails, out orders1, out details, out errmsg, lgAdmins, order.ParkOrder_IsLift == 1, true);
                            if (!orderHandleResult) return TyziTools.Json.ToString(new { success = false, msg = errmsg });

                            addInCar.Add(new AddInParkTempCar() { orders = orders1, details = details });
                        }
                    }

                    return "";
                }

                //根据停车订单parkOrderList的入场时间来排序车辆cars
                cars = cars.OrderBy(x => parkOrderList.Find(y => y.ParkOrder_CarNo == x.Car_CarNo)?.ParkOrder_EnterTime).ToList();

                //遍历车辆
                foreach (var car in cars)
                {
                    List<Model.ParkOrder> orders1 = null;
                    List<Model.OrderDetail> details = null;
                    Model.CarType carType = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                    Model.ParkOrder parkOrder = parkOrderList.Where(x => x.ParkOrder_CarNo == car.Car_CarNo).FirstOrDefault();
                    if (parkOrder != null)
                    {
                        var sumSpace = spaces?.Sum(x => x.StopSpace_Number) ?? 0;
                        var otherSpace = (parkOrderList?.Where(x => x.ParkOrder_No != parkOrder.ParkOrder_No && x.ParkOrder_IsLift > 0).ToList() ?? new List<Model.ParkOrder>()).Count;

                        var currDetails = BLL.OrderDetail.GetAllEntity(parkOrder.ParkOrder_No);
                        var inDetail = currDetails.Find(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);

                        if (currDetails.Count == 0) currDetails = null;

                        int parkOrderIsLift = 1;
                        if (cct.CarCardType_Type != 2)
                        {
                            BLL.Pass.IsSpaceCarBySpace(
                                TyziTools.Json.ToObject<List<Model.Car>>(TyziTools.Json.ToString(AllCars, true)),
                                TyziTools.Json.ToObject<Model.Car>(TyziTools.Json.ToString(car, true)),
                                model, cct, spaces, true, inDetail?.OrderDetail_ParkAreaNo, null, out var outdata, out var msg, allDetailList: allDetailList?.Copy(), allParkOrderList: parkOrderList?.Copy());

                            outdata.details?.RemoveAll(x => x.OrderDetail_CarNo == car.Car_CarNo || x.orderdetail_IsCharge == 0 || x.orderdetail_IsCharge == null);
                            //车主在入场区域无车位或车位已停满，当前车辆需要计费
                            if (outdata.spacenum == 0 || outdata.details?.Count >= outdata.spacenum)
                            {
                                parkOrderIsLift = 0;
                            }
                        }
                        else
                        {
                            parkOrderIsLift = 0;
                        }

                        orderHandleResult = BLL.Car.NewParkOrderAndCloseOldOrder(parkOrder.ParkOrder_No, car, cct, carType, currDetails, out orders1, out details, out errmsg, lgAdmins, parkOrderIsLift == 1);
                        if (!orderHandleResult) return TyziTools.Json.ToString(new { success = false, msg = errmsg });

                        if (orders1 != null && orders1.Find(x => x.ParkOrder_StatusNo == EnumParkOrderStatus.In) != null)
                        {
                            parkOrderList.Remove(parkOrder);
                            parkOrderList.Add(orders1.Find(x => x.ParkOrder_StatusNo == EnumParkOrderStatus.In));

                            var oldDetails = allDetailList.FindAll(x => x.OrderDetail_ParkOrderNo == parkOrder.ParkOrder_No)?.ToList();
                            if (oldDetails != null)
                            {
                                allDetailList.RemoveAll(x => oldDetails.Contains(x));

                                var newDetail = details.FindAll(x => x.OrderDetail_StatusNo == EnumParkOrderStatus.In)?.ToList();
                                if (newDetail != null) allDetailList.AddRange(newDetail);
                            }
                        }

                        addInCar.Add(new AddInParkTempCar() { orders = orders1, details = details });
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 导入车辆信息
        /// </summary>
        /// <param name="lgAdmins">操作员</param>
        /// <param name="carList">车辆信息</param>
        /// <param name="ownerList">车主信息</param>
        /// <param name="stopList">区域信息</param>
        /// <param name="errmsg">错误信息</param>
        /// <returns></returns>
        public static void ImportCars(
          AdminSession lgAdmins, List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> stopList,
          out string errmsg, out List<Model.ParkOrder> retOrders, out List<Model.OrderDetail> retDetails)
        {
            errmsg = "";
            retOrders = null;
            retDetails = null;
            //关闭订单
            List<Model.ParkOrder> closeOrders = new List<Model.ParkOrder>();
            List<Model.OrderDetail> closeDetails = new List<Model.OrderDetail>();
            //重建订单
            List<Model.ParkOrder> createOrders = new List<Model.ParkOrder>();
            List<Model.OrderDetail> createDetails = new List<Model.OrderDetail>();
            //不用改的订单
            List<Model.ParkOrder> oldOrders = new List<Model.ParkOrder>();
            List<Model.OrderDetail> oldDetails = new List<Model.OrderDetail>();

            //导入当前车主的所有车辆
            List<Model.Car> allCar = new List<Model.Car>();
            foreach (var owner in ownerList)
            {
                var oldcars = AppBasicCache.GetCar.Values.Where(x => x.Car_OwnerNo == owner.Owner_No);
                foreach (var car in oldcars)
                {
                    if (carList.Find(x => x.Car_CarNo == car.Car_CarNo) == null)
                        allCar.Add(car);
                }
            }
            allCar.AddRange(carList);

            List<Model.InCar> incarList = null;
            if (allCar.Count < 1000)
                incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo,InCar_CarNo", $"InCar_Status=200 and InCar_CarNo in ('{string.Join("','", allCar.Select(x => x.Car_CarNo))}')");
            else
            {
                incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo,InCar_CarNo", $"InCar_Status=200");
                var nolist = allCar.Select(x => x.Car_CarNo).ToList();
                incarList = incarList.Where(x => nolist.Contains(x.InCar_CarNo)).ToList();
            }

            List<Model.ParkOrder> orderList = null;
            List<Model.OrderDetail> detailList = null;
            if (incarList.Count > 0)
            {
                orderList = BLL.ParkOrder.GetParkOrderByOwnerCarNo("*", "", null, incarList.Select(x => x.InCar_CarNo).ToList(), null, 200).OrderBy(x => x.ParkOrder_EnterTime).ToList();
                detailList = BLL.OrderDetail.GetAllEntity("*", $"orderdetail_ParkOrderNo in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}')");
                //carList = carList.OrderBy(x => orderList.Find(y => y.ParkOrder_CarNo == x.Car_CarNo)?.ParkOrder_EnterTime).ToList();

                var orderTime = DateTime.Now;

                //对场内订单处理
                for (int i = 0; i < orderList.Count; i++)
                {
                    var order = orderList[i];

                    if (carList.Find(x => x.Car_CarNo == order.ParkOrder_CarNo) != null)
                    {
                        var newCar = carList.Find(x => x.Car_CarNo == order.ParkOrder_CarNo);
                        var newOwner = ownerList.Find(x => x.Owner_No == newCar.Car_OwnerNo);

                        var oldCar = AppBasicCache.GetCar.Values.FirstOrDefault(x => x.Car_CarNo == order.ParkOrder_CarNo);
                        var oldOwner = AppBasicCache.GetOwner.Values.FirstOrDefault(x => x.Owner_No == oldCar?.Car_OwnerNo);

                        //新增车辆信息 或者 编辑车辆的车牌类型
                        if (oldCar == null || (oldOwner != null && newOwner.Owner_CardTypeNo != oldOwner.Owner_CardTypeNo))
                        {
                            var oldIn = detailList.Find(x => x.OrderDetail_ParkOrderNo == order.ParkOrder_No && x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);
                            if (oldIn == null) { continue; }

                            #region 重组订单

                            Model.CarType carType = BLL.CarType.GetEntity(newCar.Car_VehicleTypeNo);

                            //新停车订单
                            var nItem = TyziTools.Json.ToObject<Model.ParkOrder>(TyziTools.Json.ToString(order));
                            var curIn = new Model.OrderDetail();
                            var curNoList = BLL.OrderDetail.NewOrderNo(newCar.Car_CarNo, 2);
                            var oRemark = "固定车登记,自动离场";
                            var nRemark = "固定车登记,重新入场";

                            order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
                            order.ParkOrder_OutTime = orderTime;
                            order.ParkOrder_OutType = 2;
                            order.ParkOrder_Remark = oRemark;
                            order.ParkOrder_IsSettle = 1;
                            order.ParkOrder_OutAdminAccount = lgAdmins?.Admins_Account;
                            order.ParkOrder_OutAdminName = lgAdmins?.Admins_Name;
                            order.ParkOrder_TotalAmount = 0;
                            order.ParkOrder_TotalPayed = 0;

                            //新停车订单
                            nItem.ParkOrder_No = curNoList[0];
                            curNoList.RemoveAt(0);
                            nItem.ParkOrder_EnterTime = orderTime;
                            nItem.ParkOrder_OutTime = DateTime.Parse("1900-01-01 00:00:00");
                            nItem.ParkOrder_EnterAdminAccount = lgAdmins?.Admins_Account;
                            nItem.ParkOrder_EnterAdminName = lgAdmins?.Admins_Name;
                            nItem.ParkOrder_OutPasswayNo = "";
                            nItem.ParkOrder_OutPasswayName = "";
                            nItem.ParkOrder_OutType = 0;
                            nItem.ParkOrder_CarCardType = newOwner.Owner_CardTypeNo;
                            nItem.ParkOrder_CarCardTypeName = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, newOwner.Owner_CardTypeNo)?.CarCardType_Name;
                            nItem.ParkOrder_CarType = carType.CarType_No;
                            nItem.ParkOrder_CarTypeName = carType.CarType_Name;
                            nItem.ParkOrder_Remark = nRemark;
                            nItem.ParkOrder_TotalAmount = 0;
                            nItem.ParkOrder_TotalPayed = 0;
                            nItem.ParkOrder_CouponNum = 0;

                            closeOrders.Add(order);
                            createOrders.Add(nItem);

                            //新订单明细
                            curIn = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(oldIn));

                            //旧订单明细
                            oldIn.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                            oldIn.OrderDetail_OutTime = orderTime;
                            oldIn.OrderDetail_OutType = 2;
                            oldIn.OrderDetail_IsSettle = 1;
                            oldIn.orderdetail_IsCharge = 1;
                            oldIn.OrderDetail_Remark = oRemark;
                            oldIn.OrderDetail_OutPasswayName = "固定车登记离场";
                            oldIn.OrderDetail_OutAdminAccount = lgAdmins?.Admins_Account;
                            oldIn.OrderDetail_OutAdminName = lgAdmins?.Admins_Name;

                            //新订单明细
                            curIn.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                            curIn.OrderDetail_EnterTime = orderTime;
                            curIn.OrderDetail_OutTime = DateTime.Parse("1900-01-01 00:00:00");
                            curIn.OrderDetail_No = curNoList[0];
                            curIn.OrderDetail_ParkOrderNo = nItem.ParkOrder_No;
                            curIn.OrderDetail_CarCardType = nItem.ParkOrder_CarCardType;
                            curIn.OrderDetail_CarCardTypeName = nItem.ParkOrder_CarCardTypeName;
                            curIn.OrderDetail_CarType = carType.CarType_No;
                            curIn.OrderDetail_CarTypeName = carType.CarType_Name;

                            curIn.OrderDetail_OutType = 0;
                            curIn.OrderDetail_IsSettle = 0;
                            curIn.OrderDetail_Remark = nRemark;
                            curIn.OrderDetail_EnterPasswayName = "固定车登记入场";
                            curIn.OrderDetail_EnterAdminAccount = lgAdmins?.Admins_Account;
                            curIn.OrderDetail_EnterAdminName = lgAdmins?.Admins_Name;

                            closeDetails.Add(oldIn);
                            createDetails.Add(curIn);

                            #endregion
                        }
                        else
                        {
                            oldOrders.Add(order);
                            oldDetails.AddRange(detailList.Where(x => x.OrderDetail_ParkOrderNo == order.ParkOrder_No));
                        }
                    }
                    else
                    {
                        oldOrders.Add(order);
                        oldDetails.AddRange(detailList.Where(x => x.OrderDetail_ParkOrderNo == order.ParkOrder_No));
                    }
                }

                //多车多位智能升降
                oldOrders.AddRange(createOrders);
                oldDetails.AddRange(createDetails);

                BLL.ParkOrder.ImportCarToChangeOrder(allCar, ownerList, stopList, AppBasicCache.GetCarcardTypes.Values.ToList(), oldOrders, oldDetails, incarList, out retOrders, out retDetails);
                retOrders.AddRange(closeOrders);
                retDetails.AddRange(closeDetails);
            }
        }
        #endregion

        #region 注销车辆，车位升降

        /// <summary>
        /// 注销车辆修改场内订单并且对其它车辆进行车位升降
        /// </summary>
        /// <param name="model"></param>
        /// <param name="existCarList"></param>
        /// <param name="delCarList"></param>
        /// <param name="cct"></param>
        /// <param name="spaces"></param>
        public static void DelCarUseSpace(Model.Owner model, List<Model.Car> existCarList, List<Model.Car> delCarList, Model.CarCardType cct = null, List<Model.StopSpace> spaces = null)
        {
            if (delCarList == null || delCarList.Count == 0) return;
            if (cct == null) cct = BLL.CarCardType.GetEntity(model.Owner_CardTypeNo);
            if (spaces == null) spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{model.Owner_No}'");
            if (existCarList == null)
            {
                existCarList = BLL.Car.GetAllEntity("Car_CarNo", $"Car_OwnerNo='{model.Owner_No}'");
                if (existCarList.Count > 0)
                {
                    existCarList = existCarList.Where(x => delCarList.Find(m => m.Car_CarNo == x.Car_CarNo) == null).ToList();
                }
            }

            //注销车辆的订单
            string sqlCarNo = string.Join("','", delCarList.Select(x => x.Car_CarNo));
            List<Model.ParkOrder> orders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_StatusNo={Model.EnumParkOrderStatus.In} AND ParkOrder_CarNo in ('{sqlCarNo}')");
            orders?.ForEach(x =>
            {
                x.ParkOrder_OwnerNo = model.Owner_No;
                x.ParkOrder_OwnerName = model.Owner_Name;
            });

            //占用车位的订单数
            var isLiftCount = orders?.FindAll(x => x.ParkOrder_IsLift == 1)?.Count ?? 0;

            //修改注销车辆的订单
            BLL.ParkOrder.UpdateByLogoutCar(model.Owner_ParkNo, orders, out var datas);

            //其它车辆升降车位
            if (isLiftCount > 0 && cct.CarCardType_Type != 2 && cct.CarCardType_IsMoreCar == 1 && (existCarList.Count + delCarList.Count) > 1)
            {
                //获取当前车主的所有在场车辆
                var incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo in('{string.Join("','", existCarList.Select(x => x.Car_CarNo))}') and InCar_Status=200");
                if (incarList.Count > 0)
                {
                    //修改后的订单集合
                    var modifyOrders = new List<Model.ParkOrder>();
                    var modifyDetails = new List<Model.OrderDetail>();

                    //获取当前车主的所有在场车辆的订单
                    List<string> incarOrderNos = incarList.Select(x => x.InCar_ParkOrderNo).ToList();
                    List<string> delOrders = orders.Select(x => x.ParkOrder_No).ToList();
                    delOrders.AddRange(incarOrderNos);

                    //获取当前车主的所有在场车辆的订单（不包含注销车辆的订单）
                    var orders2 = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_StatusNo={Model.EnumParkOrderStatus.In} AND ParkOrder_No in ('{string.Join("','", incarOrderNos)}')");
                    //获取当前车主的所有在场车辆的订单明细（包含注销车辆的明细）
                    var detail2 = BLL.OrderDetail.GetAllEntity("*", $"orderdetail_ParkOrderNo in ('{string.Join("','", delOrders)}')");

                    //占用车位的订单,将占用的车位升降到其它订单
                    var liftOrders = orders?.FindAll(x => x.ParkOrder_IsLift > 0);
                    var listDetails = detail2?.FindAll(x => liftOrders.Find(m => m.ParkOrder_No == x.OrderDetail_ParkOrderNo) != null);
                    var liftOrderNos = liftOrders?.Select(x => x.ParkOrder_No).ToList();
                    var otherOrders = orders2?.FindAll(x => x.ParkOrder_IsLift == 0);
                    var otherOrderNos = otherOrders?.Select(x => x.ParkOrder_No).ToList();

                    if (liftOrderNos?.Count > 0 && otherOrderNos?.Count > 0)
                    {
                        //按照进场时间排序
                        otherOrders = otherOrders.OrderBy(x => x.ParkOrder_EnterTime).ToList();
                        var nowCarsList = existCarList.Copy();
                        nowCarsList.AddRange(delCarList);
                        for (var i = 0; i < otherOrders.Count; i++)
                        {
                            //占用车位的订单数
                            if (isLiftCount <= 0) break;

                            //占用车位的订单
                            var outOrder = liftOrders[isLiftCount - 1];

                            //判断车场设置是否允许智能升降
                            var policyParkArea = BLL.PolicyArea.GetEntity(outOrder.ParkOrder_ParkAreaNo);
                            if (policyParkArea == null) continue;
                            if (policyParkArea.PolicyArea_MoreCar != 1) continue;

                            //占用车位的订单(修改成出场)
                            outOrder.ParkOrder_OutTime = DateTime.Now.AddSeconds(-1);
                            outOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;

                            //占用车位的订单的所有明细（修改成出场）
                            var outDetail = detail2.FindAll(x => x.OrderDetail_ParkOrderNo == outOrder.ParkOrder_No);
                            outDetail.ForEach(x => { x.OrderDetail_OutTime = outOrder.ParkOrder_OutTime; x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out; });

                            //未占用车位的第一辆车订单
                            var item2 = otherOrders[i];
                            //未占用车位的第一辆车订单的所有明细
                            var details = detail2.FindAll(x => x.OrderDetail_ParkOrderNo == otherOrders[i].ParkOrder_No);
                            details = details.OrderBy(x => x.OrderDetail_EnterTime).ToList();

                            //占用车位的所有明细
                            var sumDetails = detail2.FindAll(x => liftOrders.Find(m => m.ParkOrder_No == x.OrderDetail_ParkOrderNo) != null);
                            //将占用车位的订单的明细添加到未占用车位的第一辆车订单的明细中
                            sumDetails.AddRange(outDetail);
                            //将占用车位的订单明细添加到未占用车位的订单中
                            orders2.Add(outOrder);

                            //将注销车辆添加进场记录
                            incarList.Add(new InCar()
                            {
                                InCar_CarNo = outOrder.ParkOrder_CarNo,
                                InCar_ParkOrderNo = outOrder.ParkOrder_No,
                                InCar_Status = 200,
                                InCar_CarCardTypeNo = outOrder.ParkOrder_CarCardType,
                                InCar_EnterTime = outOrder.ParkOrder_EnterTime,
                                InCar_ParkAreaNo = outOrder.ParkOrder_ParkAreaNo,
                            });

                            //将在场占用车位的订单明细进行车位升降（改出场）
                            var lastDeatils = details.Last();
                            lastDeatils.OrderDetail_OutTime = DateTime.Now;
                            lastDeatils.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;

                            //未占用车位的订单进行车位升降（新增入场）
                            var newOrderDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(lastDeatils));
                            newOrderDetail.OrderDetail_No = "OC" + Utils.CreateNumber;
                            newOrderDetail.OrderDetail_EnterTime = DateTime.Now;
                            newOrderDetail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                            newOrderDetail.OrderDetail_Remark = $"[{outOrder.ParkOrder_CarNo}]注销，由[{item2.ParkOrder_CarNo}]占用车位";

                            //当前车辆新增入场的明细（当作车辆新入场）
                            List<Model.OrderDetail> nowDetails = new List<Model.OrderDetail>();
                            nowDetails.Add(newOrderDetail);

                            //修改订单
                            BLL.ParkOrder.ModifyAnyToChangeOrder(existCarList.Find(x => x.Car_CarNo == otherOrders[i].ParkOrder_CarNo), model, cct, ref item2, ref nowDetails, incarList, orders2, sumDetails, spaces, nowCarsList);

                            //如果新增入场的明细未能占到车位，则将改出场的明细改成入场
                            var modifyDetail = nowDetails.Find(x => x.OrderDetail_No == newOrderDetail.OrderDetail_No);
                            if (modifyDetail.orderdetail_IsCharge == 0)
                            {
                                lastDeatils.OrderDetail_OutTime = null;
                                lastDeatils.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                            }
                            else
                            {
                                details.AddRange(nowDetails);
                                modifyOrders.Add(item2);
                                modifyDetails.AddRange(details);
                            }

                            isLiftCount--;
                        }
                    }

                    if (modifyOrders.Count > 0)
                    {
                        var ret = BLL.ParkOrder.CarInComplete(modifyOrders, modifyDetails);
                        if (ret > 0)
                        {
                            _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                            {
                                try
                                {
                                    BLL.PushResult.AddPushRecord(Model.API.PushAction.Edit, null, new List<PushResultParse.UpdateParkOrder>() { new Model.API.PushResultParse.UpdateParkOrder() { Item1 = modifyOrders, Item2 = modifyDetails } }, AppBasicCache.GetParking?.Parking_Secret, "carin", out var msg);
                                    if (!string.IsNullOrEmpty(msg))
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"注销车辆操作车位升降异常：{string.Join(",", delCarList)}，{msg}", LogLevel.Error);
                                    }
                                }
                                catch (Exception e)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"注销车辆操作车位升降异常：{string.Join(",", delCarList)},{e.ToString()}", LogLevel.Error);
                                }
                                return Task.CompletedTask;
                            });
                        }
                    }
                }
            }

            //修改注销车辆的场内订单
            if (datas?.Count > 0)
            {
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                {
                    datas.ForEach(item =>
                    {
                        try
                        {
                            BLL.PushResult.AddPushRecord(Model.API.PushAction.Edit, null, new List<ResBodyDataIn>() { item }, AppBasicCache.GetParking?.Parking_Secret, "carin", out var msg);
                            if (!string.IsNullOrEmpty(msg))
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"注销车辆时更新场内记录异常：{item?.Item1?.FirstOrDefault()?.ParkOrder_CarNo}，{msg}", LogLevel.Error);
                            }
                            Task.Delay(10).Wait();
                        }
                        catch (Exception e)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"注销车辆时更新场内记录异常：{item?.Item1?.FirstOrDefault()?.ParkOrder_CarNo},{e.ToString()}", LogLevel.Error);
                        }
                    });
                    return Task.CompletedTask;
                });
            }
        }

        #endregion

        /// <summary>
        /// 返回新增内容
        /// </summary>
        /// <param name="newModel"></param>
        /// <param name="oldModel"></param>
        /// <returns></returns>
        public static string GetUpdateLogs(Model.Owner newModel, List<Model.Car> cars = null, List<Model.StopSpace> spaces = null
            , List<Model.Car> delCarList = null, List<Model.StopSpace> oldSpaces = null, Model.Owner oldModel = null)
        {
            string content = "";
            content += $"车主编码：{newModel.Owner_No}；";
            content += $"车位号：{newModel.Owner_Space}；";
            if (oldModel != null && oldModel.Owner_CardTypeNo != newModel.Owner_CardTypeNo)
            {
                var cardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, oldModel.Owner_CardTypeNo);
                var ncardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, newModel.Owner_CardTypeNo);
                content += $"车牌类型：（{cardType.CarCardType_No},{cardType.CarCardType_Name}）=>（{ncardType.CarCardType_No},{ncardType.CarCardType_Name}）；";
            }
            else
            {
                var cardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, newModel.Owner_CardTypeNo);
                content += $"车牌类型：（{cardType.CarCardType_No},{cardType.CarCardType_Name}）；";
            }

            if (oldModel != null && newModel != null)
            {
                if (oldModel.Owner_ParkSpace != newModel.Owner_ParkSpace)
                {
                    content += $"车场车位号：（{oldModel.Owner_ParkSpace}=>{newModel.Owner_ParkSpace}）；";
                }
            }


            if (cars?.Count > 0)
            {
                var addCars = cars.Where(x => x.Car_ID == null).ToList();
                if (addCars?.Count > 0)
                    content += $"新增车牌号：[{string.Join("','", addCars.Select(x => GetCarInfo(x)))}]；";
            }
            if (delCarList?.Count > 0)
            {
                content += $"删除车牌号：[{string.Join("','", delCarList.Select(x => GetCarInfo(x)))}]；";
            }
            if (oldSpaces?.Count > 0 && spaces?.Count > 0)
            {
                string nochangespaces = "";
                string changespaces = "";
                string addspaces = "";
                foreach (var item in spaces)
                {
                    var old = oldSpaces.Where(x => x.StopSpace_No == item.StopSpace_No)?.FirstOrDefault();
                    if (old == null)
                    {
                        addspaces += $"({item.StopSpace_AreaName},{item.StopSpace_Number}),";
                    }
                    else if (old.StopSpace_AreaNo != item.StopSpace_AreaNo || old.StopSpace_Number != item.StopSpace_Number)
                    {
                        changespaces += $"{{({old.StopSpace_AreaName},{old.StopSpace_Number})=>({item.StopSpace_AreaName},{item.StopSpace_Number})}}";
                    }
                    else
                    {
                        nochangespaces += $"({item.StopSpace_AreaName},{item.StopSpace_Number}),";
                    }
                }
                if (!string.IsNullOrEmpty(nochangespaces)) content += $"可停区域：[{nochangespaces.TrimEnd(',')}]；";
                if (!string.IsNullOrEmpty(changespaces)) content += $"变更可停区域：[{changespaces.TrimEnd(',')}]；";
                if (!string.IsNullOrEmpty(addspaces)) content += $"新增可停区域：[{addspaces.TrimEnd(',')}]；";
                List<string> nos = spaces.Select(x => x.StopSpace_No).ToList();
                var dels = oldSpaces.Where(x => !nos.Contains(x.StopSpace_No)).ToList();
                if (dels?.Count > 0)
                {
                    content += $"删除可停区域：[{string.Join("','", dels.Select(x => $"({x.StopSpace_AreaName},{x.StopSpace_Number})"))}]";
                }
            }
            else if (spaces?.Count > 0)
            {
                content += $"新增可停区域：[{string.Join("','", spaces.Select(x => $"({x.StopSpace_AreaName},{x.StopSpace_Number})"))}]";
            }
            return content;
        }
        /// <summary>
        /// 添加删除日志
        /// </summary>
        /// <param name="owners"></param>
        /// <param name="spaces"></param>
        public static string GetDelLogs(Model.AdminSession lgAdmin, List<Model.Owner> owners, List<Model.StopSpace> spaces)
        {
            string content = "";
            List<Model.UserLogs> logs = new List<Model.UserLogs>() { };
            foreach (var owner in owners)
            {
                content += $"车主编码：{owner.Owner_No}；";
                content += $"车位号：{owner.Owner_Space}；";
                var dels = spaces.Where(x => x.StopSpace_OwnerNo == owner.Owner_No).ToList();
                if (dels?.Count > 0)
                {
                    content += $"可停区域：[{string.Join(";", dels.Select(x => $"({x.StopSpace_AreaName},{x.StopSpace_Number})"))}]";
                }
            }
            return content;
        }

        /// <summary>
        /// 添加充值日志
        /// </summary>
        /// <param name="owners"></param>
        /// <param name="spaces"></param>
        public static string GetChargeLogs(Model.Owner model, Model.Owner old, List<Model.Car> cars)
        {
            string logText = $"车主编码：{model.Owner_No}；";
            logText += $"车位号：{model.Owner_Space}；";
            logText += $"车牌号：[{string.Join(",", cars.Select(x => x.Car_CarNo))}]；";
            if (old.Owner_CardType != (int)CarTypeEnum.Store)
            {
                logText += $"有效期：[{old.Owner_StartTime.Value.ToString("yyyy-MM-dd")},{old.Owner_EndTime.Value.ToString("yyyy-MM-dd")}]=>" +
               $"[{model.Owner_StartTime.Value.ToString("yyyy-MM-dd")},{model.Owner_EndTime.Value.ToString("yyyy-MM-dd")}]";
            }
            else
            {
                logText += $"储值余额：{old.Owner_Balance}=>{model.Owner_Balance}";
            }
            return logText;
        }

        /// <summary>
        /// 批量延期日志
        /// </summary>
        /// <param name="oldModel"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public static string GetVaildLog(Model.Owner oldModel, DateTime start, DateTime end)
        {
            string content = $"车主编码：{oldModel.Owner_No}；";
            content += $"车位号：{oldModel.Owner_Space}；";
            content += $"有效期起：{oldModel.Owner_StartTime?.ToString("yyyy-MM-dd 00:00:00")}=>{start.ToString("yyyy-MM-dd 00:00:00")}；";
            content += $"有效期止：{oldModel.Owner_EndTime?.ToString("yyyy-MM-dd 23:59:59")}=>{end.ToString("yyyy-MM-dd 23:59:59")}；";
            return content;
        }

        public static string GetCarInfo(Model.Car car)
        {
            if (string.IsNullOrEmpty(car.Car_License))
            {
                return car.Car_CarNo;
            }
            else
            {
                return $"({car.Car_CarNo},{car.Car_License})";
            }
        }

        /// <summary>
        /// 检查车辆是否已登记
        /// </summary>
        /// <param name="carList">检查的车牌号列表</param>
        /// <param name="msg">返回被登记的车牌号信息</param>
        /// <param name="isCheckOwner">是否检查车主</param>
        /// <param name="isCheckBusiness">是否检查商家车辆</param>
        /// <param name="isCheckReserve">是否检查访客车辆</param>
        /// <returns></returns>
        public static bool CheckRegCar(List<string> carList, ref string msg, bool isCheckOwner = false, bool isCheckBusiness = false, bool isCheckReserve = false)
        {
            bool hasAnyReg = false;
            var carStatusDict = new Dictionary<string, List<string>>();

            foreach (var carNo in carList.Distinct())
            {
                List<string> statusList = new List<string>();

                if (isCheckOwner)
                {
                    bool isOwner = AppBasicCache.GetCar.Any(x => x.Value.Car_CarNo == carNo);
                    if (isOwner)
                        statusList.Add("车主");
                }

                if (isCheckBusiness)
                {
                    bool isBusiness = AppBasicCache.GetBusinessCar.Any(x =>
                        x.Value.BusinessCar_CarNo == carNo &&
                        (x.Value.BusinessCar_Status == 0 || x.Value.BusinessCar_Status == 1) &&
                        x.Value.BusinessCar_EndTime > DateTime.Now);
                    if (isBusiness)
                        statusList.Add("商家车辆");
                }

                if (isCheckReserve)
                {
                    var reserves = AppBasicCache.GetReserve.Where(x =>
                        x.Value.Reserve_CarNo == carNo &&
                        (x.Value.Reserve_Status == 0 || x.Value.Reserve_Status == 1 || x.Value.Reserve_Status == 4) &&
                        x.Value.Reserve_EndTime > DateTime.Now).ToList();

                    if (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes != 1)
                    {
                        reserves = reserves.Where(x => x.Value.Reserve_Status != 4).ToList();
                    }

                    if (reserves.Count > 0)
                        statusList.Add("访客车辆");
                }

                if (statusList.Count > 0)
                {
                    msg += $"请注意：{carNo} 已登记{string.Join("、", statusList)}。\n";
                    hasAnyReg = true;
                }
            }

            return hasAnyReg;
        }

        /// <summary>
        /// 支付成功后，生成储值余额支付记录
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="orderno"></param>
        /// <param name="owner"></param>
        /// <param name="oldOwner"></param>
        /// <returns></returns>
        public static List<Model.Ledger> GetLedgerByPaySucceess(string carno, string orderno, Model.Owner owner, Model.Owner oldOwner)
        {
            List<Model.Ledger> leadgerList = null;

            //更新储值车余额
            if (owner != null && owner.Owner_CardType == 2)
            {
                if (oldOwner == null) oldOwner = AppBasicCache.GetElement(AppBasicCache.GetOwner, owner.Owner_No);
                if (oldOwner == null) return leadgerList;

                if (owner.Owner_Balance != oldOwner.Owner_Balance)
                {

                    Model.Ledger ledger = null;
                    var remainingMoney = Utils.ObjectToDecimal(oldOwner.Owner_Balance, 0) - Utils.ObjectToDecimal(owner.Owner_Balance, 0);
                    if (remainingMoney > 0)
                    {
                        ledger = new Model.Ledger()
                        {
                            Ledger_CarNo = carno,
                            Ledger_Space = owner.Owner_Space,
                            Ledger_Type = 2,
                            Ledger_CardType = 2,
                            Ledger_Code = 2,
                            Ledger_Money = remainingMoney,
                            Ledger_BeforeMoeny = oldOwner.Owner_Balance,
                            Ledger_AfterMoeny = owner.Owner_Balance,
                            Ledger_Time = DateTime.Now,
                            Ledger_ParkOrderNo = orderno,
                        };
                    }
                    leadgerList = new List<Ledger> { ledger };
                }
            }

            return leadgerList;
        }
    }
}
