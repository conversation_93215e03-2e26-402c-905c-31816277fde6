﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量延期</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/
        .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #eee; opacity: 1; color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        .red { color: #de163b; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <input type="hidden" id="OwnerNoList" name="OwnerNoList"/>
            <div class="layui-row form-group"></div>
            <div class="layui-row chuzhi">
                <div class="layui-col-xs3 edit-label ">变更开始时间</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input  v-null" maxlength="80" id="Car_ChangeBeginTime" name="Car_ChangeBeginTime" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">变更结束时间</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="10" id="Car_ChangeTime" name="Car_ChangeTime" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车位号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="10" id="spaceNoList" name="spaceNoList" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label "></div>
                <div class="layui-col-xs7">
                    <div style=" font-size: 13px; color:#5d9cf5 ;"> 温馨提示：请谨慎设置，若车辆已入场，开始时间比车辆入场时间大，计费时判定这一段时间车辆过期。<br /> 仅修改车位下一位多车的车辆。<br />时间格式为 yyyy-MM-dd HH:mm:ss，例如12月1日至12月31日，开始时间：2021-12-01 00:00:00，结束时间：2021-12-31 23:59:59。</div>
                </div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function () {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramCarNo = parent.pager.carNoList;
        var paramType = $.getUrlParam("type");
        var PayFreeType = 0;
        var dt = new Date().Format("yyyy-MM-dd hh:mm:ss");
        var dt2 = new Date().Format("yyyy-MM-dd hh:mm:ss");
        var index = parent.layer.getFrameIndex(window.name);
        var isOpen = true;


        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindData();
                dt2 = new Date($("#Car_BeginTime").val()).Format("yyyy-MM-dd hh:mm:ss");
                this.bindSelect();
                this.bindEvent();
                $("#OwnerNoList").val(parent.pager.ownerNoList);
                $("#spaceNoList").val(parent.pager.spaceNoList);
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                pager.ShowDateTime("#Car_ChangeBeginTime", "#Car_ChangeTime");
            },
            //数据绑定
            bindData: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });

                    var carStr = $("#spaceNoList").val();
                    if (carStr.length > 100) carStr = carStr.substring(0, 100) + "..." + " 共" + carStr.split(",").length + "个车位号";

                    var msg = "车位号【<t class='red'>" + carStr + "</t>】<br/>"
                        + "变更有效期为：<t class='red'>"
                        + $("#Car_ChangeBeginTime").val()
                        + "</t>～<t class='red'>"
                        + $("#Car_ChangeTime").val()
                        + "</t><br/>"
                    layer.open({
                        type: 0,
                        title: "批量延期提示",
                        btn: ["确定", "取消"],
                        content: msg + "确定批量修改吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Save").attr("disabled", true);
                            $.getJSON("/Owner/BathValidCar", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.Success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.Message.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); }
                    })
                });

            },
            ShowDate: function (id1) {
                var enterend = {
                    elem: id1, format: "yyyy-MM-dd hh:mm:ss", start: dt, min: "2021-01-01", max: "2099-06-16", lang: "cn",
                    istime: true, istoday: true, choose: function (datas) { }, position: 'abolute', trigger: 'click',
                    done: function (value, date) {
                    }
                };

                var enterendDate = laydate.render(enterend);
            },
            ShowDateTime: function (id1, id2) {
                var enterstart = {
                    elem: id1, type: 'date', start: dt2, min: "2021-01-01", max: "2099-06-16", lang: "cn",
                    istime: true, istoday: true, choose: function (datas) { enterend.min = datas; enterend.start = new Date(datas).Format("yyyy-MM-dd hh:mm:ss"); },
                    done: function (value, date) {
                        dateEntryEnd.config.min = {
                            year: date.year,
                            month: date.month - 1,
                            date: date.date,
                        };
                        // 作为 结束选择 的 开始时间
                        dateEntryEnd.config.value = value;
                        dt2 = value;
                    }
                };
                var enterend = {
                    elem: id2, type: 'date', start: dt2, min: "2021-01-01", max: "2099-06-16", lang: "cn",
                    istime: true, istoday: true, choose: function (datas) { enterstart.max = datas },
                    done: function (value, date) {
                        dateEntryStart.config.max = {
                            year: date.year,
                            month: date.month - 1,
                            date: date.date,
                        };
                        dateEntryStart.config.value = value;
                    }
                };

                var dateEntryStart = laydate.render(enterstart);
                var dateEntryEnd = laydate.render(enterend);
            },
        };


    </script>
</body>
</html>
