﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>尾号限行</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }

        .layui-tab { margin: 0; background: #fff; height: 100%; position: relative; }

        .layui-tab-title { padding-left: 2rem; padding-top: 15px; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .5rem; left: .8rem; top: .75rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_type.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_type1.svg'); }

        .layui-tab-content { padding: 0; position: absolute; bottom: 0; top: 60px; left: 0; right: 0; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab-item.full { width: 100%; height: 100%; }
        .layui-tab-item.full iframe { width: 100%; height: 100%; border: 0; }

        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }


        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>尾号限行</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input " name="EndNumAuth_No" id="EndNumAuth_No" autocomplete="off" placeholder="限行编码" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="EndNumAuth_Num" id="EndNumAuth_Num" autocomplete="off" placeholder="限行尾号,多个逗号隔开" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="EndNumAuth_Remark" id="EndNumAuth_Remark" autocomplete="off" placeholder="限行备注" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="启用状态" class="form-control chosen-select " id="EndNumAuth_Status" name="EndNumAuth_Status" lay-search>
                                    <option value="">启用状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="语音提醒" class="form-control chosen-select " id="EndNumAuth_IsVoice" name="EndNumAuth_IsVoice" lay-search>
                                    <option value="">语音提醒</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.EndNumAuth.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.EndNumAuth.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.EndNumAuth.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                                {{# if(Power.EndNumAuth.Enable){}} <button class="layui-btn layui-btn-sm" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>启用</t></button>{{# } }}
                                {{# if(Power.EndNumAuth.Disable){}}<button class="layui-btn layui-btn-sm" lay-event="Disable"><i class="fa fa-ban"></i><t>禁用</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TmplIsMoreCar">
        {{# if(d.EndNumAuth_IsMoreCar==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>
    <script type="text/html" id="tmplTypeName">
        {{# if(d.EndNumAuth_Type==1){ }}
        <span>{{d.CarCardType_Name}}</span>
        {{# }else if(d.EndNumAuth_Type==2){ }}
        <span>{{d.CarType_Name}}</span>
        {{# } }}
    </script>
    <script type="text/html" id="tmplIsAccess">
        {{# if(d.EndNumAuth_IsAccess==1){ }}
        <span class="layui-badge layui-bg-green">允许通行</span>
        {{# }else if(d.EndNumAuth_IsAccess==2){}}
        <span class="layui-badge layui-bg-orange">禁止单号通行</span>
        {{# }else if(d.EndNumAuth_IsAccess==3){}}
        <span class="layui-badge layui-bg-orange">禁止双号通行</span>
        {{# }else{}}
        <span class="layui-badge layui-bg-red">禁止通行</span>
        {{# } }}
    </script>
    <script type="text/html" id="tmpldatetype">
        {{# if(d.EndNumAuth_DateType==1){ }}
        <span class="layui-badge layui-bg-green">每天</span>
        {{# }else if(d.EndNumAuth_DateType==2){ }}
        <span class="layui-badge layui-bg-blue">每周</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-cyan">日期</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplEnable">
        {{#  if(d.EndNumAuth_Status==1){ }}
        <span class="layui-badge layui-bg-blue ">启用</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-orange ">禁用</span>
        {{#  } }}
    </script>
    <script type="text/html" id="TmplVovice">
        {{#  if(d.EndNumAuth_IsVoice==1){ }}
        <span class="layui-badge layui-bg-blue ">启用</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-orange ">禁用</span>
        {{#  } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'EndNumAuth_No', title: '限行编码' , hide: true}
                , { field: 'EndNumAuth_DateType', title: '授权类型', toolbar: "#tmpldatetype" }
                , { field: 'EndNumAuth_Num', title: '限行尾号' }
                , { field: 'EndNumAuth_IsVoice', title: '语音提醒', toolbar: '#TmplVovice' }
                , { field: 'EndNumAuth_Status', title: '启用状态', toolbar: '#TmplEnable' }
                , { field: 'Admins_Name', title: '添加人' }
                , { field: 'EndNumAuth_Remark', title: '限行备注', hide: true }
            ]];
            cols = tb_page_cols(cols);
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/EndNumAuth/GetEndNumAuthList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                var type = $(".layui-this").index() + 1;

                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增出入权限",
                            content: 'Edit?Act=Add&Type=' + type,
                            area: getIframeArea(['830px', '90%']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑出入权限",
                            content: 'Edit?Act=Update&EndNumAuth_No=' + data[0].EndNumAuth_No + '&Type=' + type,
                            area: getIframeArea(['830px', '90%']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { EndNumAuth_No: data[0].EndNumAuth_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定启用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Enable", { EndNumAuth_No: data[0].EndNumAuth_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("启用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定禁用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Disable", { EndNumAuth_No: data[0].EndNumAuth_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("禁用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;

                };
            });

            tb_row_radio(table);

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        var option = '';
                        json.data.forEach(function (d, i) {
                            option += '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                        });
                        $("#EndNumAuth_CarCardTypeNo").append(option);
                        layuiForm.render("select");
                    }
                }, 'json');

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        var option = '';
                        json.data.forEach(function (d, i) {
                            option += '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                        });
                        $("#EndNumAuth_CarTypeNo").append(option);
                        layuiForm.render("select");
                    }
                }, 'json');
            },
            bindData: function (index, type) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                if (type == null || type == "")
                    type = $(".layui-this").index() + 1;
                conditionParam.EndNumAuth_Type = type;
                comtable.reload({
                    url: '/EndNumAuth/GetEndNumAuthList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
