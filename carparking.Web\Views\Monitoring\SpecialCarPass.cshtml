﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>特殊车辆放行记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        .layui-fluid { padding: 0; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; }
        ::-webkit-scrollbar-thumb { border-radius: 10px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }
        .layui-tab-title { background-color: #5868e0 !important; }
        .layui-input { border-color: #ddd !important; font-size: .9rem; }
        .layui-btn { font-size: .9rem; }
        .layui-table-cell { font-size: .9rem; }

        .layui-table-click { background-color: #f2f3f3; color: #2F4056; font-weight: bold; }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <select class="layui-input" lay-search name="PassRecord_PasswayNo" id="PassRecord_PasswayNo" placeholder="通道名称">
                                    <option value="">通道名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PassRecord_PassTime1" id="PassRecord_PassTime1" autocomplete="off" placeholder="通行时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PassRecord_PassTime2" id="PassRecord_PassTime2" autocomplete="off" placeholder="通行时间止" />
                            </div>



                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>

                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select data-placeholder="操作员" class="layui-input" id="PassRecord_Account" name="PassRecord_Account" lay-search>
                                    <option value="">操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PassRecord_Name" id="PassRecord_Name" autocomplete="off" placeholder="操作员" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">

                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmploutimg">
        {{# if (d.PassRecord_ImgPath != null && d.PassRecord_ImgPath != ""){}}
        <a href="{{decodeURIComponent(d.PassRecord_ImgPath)}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看"><i class="layui-icon layui-icon-picture"></i>预览</a>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">无图片</span>
        {{# } }}
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v20230620" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var Parking_Key = '@ViewBag.Parking_Key';
        var isFrpUrl = IsFrpURLOpenWeb(Parking_Key);
        topBar.init();

        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'PassRecord_No', title: '记录编号', hide: true, minWidth: 100 }
                , { field: 'PassRecord_PasswayNo', title: '通道编码', hide: true, minWidth: 100 }
                , { field: 'PassRecord_PasswayName', title: '通道名称', minWidth: 120 }
                , { field: 'PassRecord_SpecialNo', title: '特殊车辆编码', hide: true, minWidth: 120 }
                , { field: 'PassRecord_SpecialName', title: '特殊车辆名称', minWidth: 140 }
                , { field: 'PassRecord_DeviceNo', title: '相机编码', hide: true, minWidth: 120 }
                , { field: 'PassRecord_DeviceName', title: '相机名称', hide: true, minWidth: 120 }
                , {
                    field: 'PassRecord_ImgPath', title: '抓拍图', minWidth: 100, templet: function (d) {
                        var img = d.PassRecord_ImgPath;
                        if (isFrpUrl) {
                            img = replaceFirstPathSegment(img);
                        }
                        return tempImg(img);
                    }
                }
                , { field: 'PassRecord_PassTime', title: '通行时间', minWidth: 185 }
                , { field: 'PassRecord_Account', title: '操作员账号', hide: true, minWidth: 120 }
                , { field: 'PassRecord_Name', title: '操作员', minWidth: 120 }
                , { field: 'PassRecord_Remark', title: '备注', minWidth: 120 }
                , { field: 'PassRecord_AddTime', title: '创建时间', hide: true, minWidth: 185 }
            ]];
            cols = tb_page_cols(cols, "SpecialCarPass");

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetSpecialCarPassList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                //, totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d, "SpecialCarPass");
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {

                };
            });
            // 监听行点击事件
            table.on('row(com-table-base)', function (obj) {
                // 移除所有行的选中样式
                obj.tr.siblings().removeClass('layui-table-click');
                // 添加当前行的选中样式
                obj.tr.addClass('layui-table-click');
            });

            tb_row_checkbox()
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PassRecord_PassTime1", "PassRecord_PassTime2"], { type: 'datetime', range: true });

                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                            $("#PassRecord_PasswayNo").append(option);
                            layui.form.render('select');
                        })
                    }
                }, "json");

                $.post("SltAdminList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Admins_Account + '">' + d.Admins_Name + '</option>';
                            $("#PassRecord_Account").append(option);
                        });
                        layui.form.render('select');
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Monitoring/GetSpecialCarPassList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {

            }
        }
    </script>
</body>
</html>
