﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增跟车记录</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-card {
            box-shadow: none;
        }

        a:hover {
            text-decoration: none;
        }

        .layui-row {
            margin-bottom: 10px;
        }

        .edit-label {
            position: relative;
            width: calc(100% - 10px);
            text-align: right;
            font-weight: normal;
        }

        .layui-com-upload {
            position: relative;
            width: 100%;
        }

        /* 修改输入框的字体颜色 */
        .layui-disabled {
            color: #3b3b3b !important;
        }

        /* 修改鼠标移入时输入框的字体颜色 */
        .layui-disabled:hover {
            color: #3b3b3b !important;
        }
    </style>
</head>

<body>
    <div class="layui-card layui-form">
        <div class="layui-card-header">
        </div>
        <div class="layui-card-body" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">车牌号</label></div>
                <div class="layui-col-sm3">
                    <input class="layui-input v-submit" id="ControlEvent_CarNo" autocomplete="off"
                        name="ControlEvent_CarNo" maxlength="8" />
                </div>
                <div class="layui-col-sm2"><label class="edit-label">事件类型</label></div>
                <div class="layui-col-sm3">
                    <select class="layui-select v-null" id="ControlEvent_Type" name="ControlEvent_Type" lay-search>
                        <option value="1">跟车事件</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">出入车道</label></div>
                <div class="layui-col-sm3">
                    <select class="layui-select v-null" id="ControlEvent_PasswayNo" name="ControlEvent_PasswayNo"
                        lay-filter="passway" lay-search>
                        <option value="">请选择出入车道</option>
                    </select>
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
                <div class="layui-col-sm2"><label class="edit-label">事件时间</label></div>
                <div class="layui-col-sm3">
                    <input class="layui-input v-null v-datetime v-submit" id="ControlEvent_Time"
                        name="ControlEvent_Time" />
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">图片</label></div>
                <div class="layui-col-sm8">
                    <div class="layui-com-upload" com-input="ControlEvent_BigImg" url="/SystemSetting/UploadImage" accept="images" data="ControlEvent_BigImg" auto="true"></div> 
                    <span class="desc" style="color:#aaa;padding-top:5px;font-size:13px;">图片大小建议不要超出100KB</span>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">视频</label></div>
                <div class="layui-col-sm8">
                    <div style="position: relative; width: 100%;">
                        <input class="layui-input layui-com-input layui-disabled" disabled="" name="ControlEvent_Video"
                            id="ControlEvent_Video" style="width: calc(100% - 86px); float: left;">
                        <button class="layui-btn layui-upload-button " id="upBtn_ControlEvent_Video"
                            style="border-radius:0;">
                            <i class="layui-icon layui-icon-upload-drag"></i>上传
                        </button>
                    </div>

                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">备注</label></div>
                <div class="layui-col-sm8">
                    <input class="layui-input" id="ControlEvent_Remark" name="ControlEvent_Remark" value="跟车出入场" />
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-sm2"><label>&nbsp;</label></div>
                <div class="layui-col-sm8">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> 保存</button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/com.file.input.js?v=1" asp-append-version="true"></script>
    <script>
        myVerify.visible = true;
        myVerify.init();
        comFileUpload.init();
        s_carno_picker.init("ControlEvent_CarNo", function (text, carno) {
            $("#ControlEvent_CarNo").val(carno.join(''))
        }, "web").bindkeyup();

        layui.use(['form', 'element'], function () {
            pager.init()
        });
        layui.use(['upload', 'jquery'], function () {
            var upload = layui.upload;
            //执行实例
            var uploadInst = upload.render({
                elem: '#upBtn_ControlEvent_Video'                //绑定元素
                , url: '/ControlEvent/UploadVideo'      //上传接口
                //*********************传输限制
                , size: 60 * 1024                   //传输大小60M
                , auto: true
                , accept: 'video'              //video audio images
                //****************传输操作相关设置
                , multiple: false                             //多文件上传
                , before: function (e) {
                    layer.msg("上传中", { icon: 16, time: 0 });
                }
                , done: function (res) {                      //传输完成的回调
                    tb_page_set(res);
                    layer.closeAll();
                    if (res.Success == "1") {
                        $('#ControlEvent_Video').val(res.Src);
                    }
                    else {
                        layer.msg(res.Msg, { time: 1500, icon: 0 });
                    }
                }
                , error: function () {                         //传输失败的回调
                    //请求异常回调
                    layer.closeAll();
                }
            });
        });
        var pager = {
            model: null,
            passwayList: [],
            init: function () {
                $.ajaxSettings.async = false;
                //this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $("#ControlEvent_Time").val(new Date().Format("yyyy-MM-dd hh:mm:ss"));

                //$.post("SltParkAreaList", {}, function (json) {
                //    if (json.success) {
                //        json.data.forEach(function (item, index) {
                //            var selected = '';
                //            if (item.ParkArea_Type == "0") {
                //                var option = '<option value="' + item.ParkArea_No + '" ' + selected + '>' + item.ParkArea_Name + '</option>';
                //                $("#ControlEvent_ParkAreaNo").append(option);
                //            }
                //        });
                //    }
                //}, "json");

                $.post("SltOutGatePasswayList", {}, function (json) {
                    if (json.success) {
                        pager.passwayList = json.data;
                        var option = '<option value="">请选择出入车道</option>';
                        json.data.forEach(function (item, index) {
                            option += '<option value="' + item.Passway_No + '" data-gate="' + item.Passway_GateType + '">' + item.Passway_Name + '</option>';
                        });
                        $("#ControlEvent_PasswayNo").html(option);
                        layui.form.render("select");
                    }
                }, "json");

                layui.form.render("select");

                _DATE.bind(layui.laydate, ["ControlEvent_Time"], { type: "datetime" });
            },
            bindEvent: function () {
                $("#Save").click(function () {
                    $("#Save").attr("disabled", true);
                    if (!myVerify.check()) { $("#Save").removeAttr("disabled"); return; }
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.ControlEvent_Remark = $("#ControlEvent_Remark").val();
                        data.ControlEvent_Gate = $("#ControlEvent_PasswayNo").find("option:selected").attr("data-gate");
                        return data;
                    });
                    param.ControlEvent_BigImg = comFileUpload.data.ControlEvent_BigImg;
                    param.ControlEvent_Video = $('#ControlEvent_Video').val();
                    layer.msg("处理中", { icon: 16, time: 0 });

                    $.post("/ControlEvent/AddFollow", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { time: 1500, icon: 1 }, function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            });
                        } else {
                            layer.msg(json.msg, { time: 1500, icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    }, "json").fail(function () { $("#Save").removeAttr("disabled"); });
                });

                $("#Cancel").click(function () { parent.layer.closeAll(); });

            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        };
    </script>
</body>

</html>
