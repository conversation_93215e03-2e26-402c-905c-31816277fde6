﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>屏显模板管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa {
            margin: 6px 4px;
            float: left;
            font-size: 16px;
        }
        .layui-layer-iframe iframe {
            border-radius: 0 !important;
        }
    </style>
</head>

<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>屏显模板</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input" name="DisplayTemplate_Name" id="DisplayTemplate_Name" autocomplete="off"
                                    placeholder="模板名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i
                                        class="layui-icon layui-icon-search inbtn"></i>
                                    <t>搜索</t>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.DisplayTemplate.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.DisplayTemplate.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.DisplayTemplate.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'DisplayTemplate_Id', title: 'ID', hide: true }
                , { field: 'DisplayTemplate_Name', title: '模板名称' }
                , { field: 'DisplayTemplate_Width', title: '模组数量', width: 160, templet: function(d){ 
                    // 如果坐标和尺寸都为0,说明未设置区域
                    if(d.DisplayTemplate_X == 0 && d.DisplayTemplate_Y == 0 && 
                       d.DisplayTemplate_Width == 0 && d.DisplayTemplate_Height == 0) {
                        return '未设置';
                    }
                    // 计算模组数量 - 宽度 * 高度
                    var moduleCount = d.DisplayTemplate_Width * d.DisplayTemplate_Height;
                    return moduleCount + '个模组';
                  } }
                , { field: 'DisplayTemplate_Status', title: '模板状态', width: 120, templet: function(d){
                    switch(d.DisplayTemplate_Status) {
                        case 0:
                            return '<span class="layui-badge layui-bg-gray">创建缓存</span>';
                        case 1:
                            return '<span class="layui-badge layui-bg-green">配置完成</span>'; 
                        case 2:
                            return '<span class="layui-badge layui-bg-orange">编辑未完成</span>';
                        default:
                            return '<span class="layui-badge layui-bg-gray">未知状态</span>';
                    }
                }}
                , { field: 'DisplayTemplate_CreateUser', title: '创建用户' }
                , { field: 'DisplayTemplate_CreateTime', title: '创建时间', templet: function(d){ return layui.util.toDateString(d.DisplayTemplate_CreateTime, 'yyyy-MM-dd HH:mm:ss') } }
                , { field: 'DisplayTemplate_UpdateTime', title: '更新时间', templet: function(d){ return layui.util.toDateString(d.DisplayTemplate_UpdateTime, 'yyyy-MM-dd HH:mm:ss') } }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/DisplayTemplate/GetDisplayTemplateList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                var data = checkStatus.data;
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增屏显模板(一个单元格表示一个模组)", 
                            content: 'Edit?act=Add',
                            area: getIframeArea(['1024px', '768px']),
                            maxmin: false,
                            cancel: function(index, layero){ // 添加cancel回调
                                const iframeName = layero.find('iframe')[0]['name']; // 获取iframe name
                                const contentWindow = window.frames[iframeName]; // 获取iframe window对象
                                if(contentWindow.currentStep !== 4){ // 如果步骤不是第4步表示未保存
                                    layer.confirm('当前模板配置未保存，是否确认退出？', {
                                        btn: ['确定','取消'],
                                        icon: 3
                                    }, function(){ // 点击确定按钮的回调
                                        // 获取模板ID并清除编辑进度
                                        if(contentWindow.pager && contentWindow.pager.displayTemplate && contentWindow.pager.displayTemplate.DisplayTemplate_Id){
                                            $.post('/DisplayTemplate/ClearEditProgress', {
                                                displayTemplateId: contentWindow.pager.displayTemplate.DisplayTemplate_Id
                                            }, function(res){
                                                // 无论清除是否成功都关闭窗口
                                                layer.close(index);
                                            });
                                        } else {
                                            layer.close(index);
                                        }
                                    });
                                    return false; // 阻止自动关闭
                                }
                            },
                            end: function() {
                                pager.bindData(pager.pageIndex);
                            }
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑屏显模板(一个单元格表示一个模组)",
                            content: 'Edit?act=Update&Id=' + data[0].DisplayTemplate_Id,
                            area: getIframeArea(['1024px', '768px']),
                            maxmin: false,
                            cancel: function(index, layero){ // 添加cancel回调
                                const iframeName = layero.find('iframe')[0]['name']; // 获取iframe name
                                const contentWindow = window.frames[iframeName]; // 获取iframe window对象
                                if(contentWindow.currentStep !== 4){ // 如果步骤不是第4步表示未保存
                                    layer.confirm('当前模板配置未保存，是否确认退出？', {
                                        btn: ['确定','取消'],
                                        icon: 3
                                    }, function(){ // 点击确定按钮的回调
                                        // 获取模板ID并清除编辑进度
                                        if(contentWindow.pager && contentWindow.pager.displayTemplate && contentWindow.pager.displayTemplate.DisplayTemplate_Id){
                                            $.post('/DisplayTemplate/ClearEditProgress', {
                                                displayTemplateId: contentWindow.pager.displayTemplate.DisplayTemplate_Id
                                            }, function(res){
                                                // 无论清除是否成功都关闭窗口
                                                layer.close(index);
                                            });
                                        } else {
                                            layer.close(index);
                                        }
                                    });
                                    return false; // 阻止自动关闭
                                }
                            },
                            end: function() {
                                pager.bindData(pager.pageIndex);
                            }
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除？将删除该模板下的所有节目区域和节目区域功能！",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.ajax({
                                    url: '/DisplayTemplate/DeleteTemplate',
                                    type: 'POST', 
                                    data: { displayTemplateId: data[0].DisplayTemplate_Id },
                                    success: function(res) {
                                        if (res.success)
                                            layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                        else
                                            layer.msg(res.msg || "删除失败", { icon: 0, time: 1500 });
                                    }
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                }
            });

            tb_row_radio(table);

            pager.init();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/DisplayTemplate/GetDisplayTemplateList'
                    , where: { conditionParam: JSON.stringify(conditionParam) }
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>

</html>
