﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>黑白名单</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        .desc { display: block; font-size: 14px; text-align: left; width: calc(100% - 1px); margin-left: 1px;margin-top:5px; }
        li { display: inline-block; margin-right: 10px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>黑白名单</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input" name="WhiteRecord_CarNo" id="WhiteRecord_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                            </div>
                            <div class="layui-inline">
                                <select id="WhiteRecord_Type" name="WhiteRecord_Type" lay-search>
                                    <option value="">名单类型</option>
                                    <option value="0">白名单</option>
                                    <option value="1">黑名单</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select id="WhiteRecord_DeviceStatus" name="WhiteRecord_DeviceStatus" lay-search>
                                    <option value="">名单状态</option>
                                    <option value="0">未同步</option>
                                    <option value="1">已启用</option>
                                    <option value="2">已注销</option>
                                </select>
                            </div>
                            
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                            <div class="layui-row search-more layui-hide">
                                <div class="layui-inline">
                                    <select id="WhiteRecord_Status" name="WhiteRecord_Status" lay-search>
                                        <option value="">操作状态</option>
                                        <option value="0">未处理</option>
                                        <option value="1">已处理</option>
                                        @*  <option value="2">添加失败</option>
                                        <option value="3">删除成功</option>
                                        <option value="4">删除失败</option>
                                        <option value="5">清空成功</option>
                                        <option value="6">清空失败</option> *@
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="WhiteRecord_DeviceNo" id="WhiteRecord_DeviceNo" autocomplete="off" placeholder="设备编码" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="WhiteRecord_DeviceName" id="WhiteRecord_DeviceName" autocomplete="off" placeholder="设备名称" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="WhiteRecord_Remark" id="WhiteRecord_Remark" autocomplete="off" placeholder="备注" value="" />
                                </div>
                            </div>
                        </div>
                        
                    </div>
                   
                    <div class="layui-card-header" style="font-size: 16px;">
                        <ul>
                            <li><span class="totaldesc"> <span class="layui-badge layui-bg-blue">已启用</span>：</span><span class="totalenable"></span></li>
                            <li><span class="totaldesc"> <span class="layui-badge layui-bg-cyan">已注销</span>：</span><span class="totalcancel"></span></li>
                            <li><span class="totaldesc"> <span class="layui-badge layui-bg-orange">未同步</span>：</span><span class="totalsync"></span></li>
                            <li><div class="desc"><t style="color:red;">已启用：</t>表示相机已成功加入该名单；   <t style="color:red;">已注销：</t>表示相机已注销该名单；   <t style="color:red;">未同步：</t>表示未向相机下发该名单记录；       </div></li>
                        </ul>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                               <button class="layui-btn layui-btn-sm layui-hide" id="Send" lay-event="Send"><i class="fa fa-send"></i><t>同步名单</t></button>
                                 <button class="layui-btn layui-btn-sm layui-hide" id="Bind" lay-event="SyncWhite"><i class="fa fa-recycle"></i><t>一键同步白名单</t></button>
                                 <button class="layui-btn layui-btn-sm layui-hide" id="Audit" lay-event="SyncBlack"><i class="fa fa-recycle"></i><t>一键同步黑名单</t></button>

                                 <button class="layui-btn layui-btn-sm layui-hide" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除记录</t></button>
                                 <button class="layui-btn layui-btn-sm layui-hide" id="Reject" lay-event="ClearAll"><i class="fa fa-recycle"></i><t>一键注销黑白名单</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?20220830" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        topBar.init();

        layui.use(['table', 'form'], function () {

            var table = layui.table;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'WhiteRecord_CarNo', title: '车牌号码' }
                , { field: 'WhiteRecord_DeviceNo', title: '设备编码', hide: true }
                , { field: 'WhiteRecord_DeviceName', title: '设备名称' }

                , {
                    field: 'WhiteRecord_DeviceStatus', title: '名单状态', templet: function (d) {
                        if (d.WhiteRecord_DeviceStatus == 1) return tempBar(1, "已启用");
                        else if (d.WhiteRecord_DeviceStatus == 2) return tempBar(4, "已注销");
                        else return tempBar(3, "未同步");
                    }
                }
                , {
                    field: 'WhiteRecord_Type', title: '名单类型', templet: function (d) {
                        if (d.WhiteRecord_Type == 1) return tempBar(4, "黑名单");
                        else return tempBar(1, "白名单");
                    }
                }
                , { field: 'WhiteRecord_Remark', title: '备注' }
                , {
                    field: 'WhiteRecord_Status', title: '操作状态', templet: function (d) {
                        if (d.WhiteRecord_Status == 1 || d.WhiteRecord_Status == 2 || d.WhiteRecord_Status == 3 || d.WhiteRecord_Status == 4 || d.WhiteRecord_Status == 5 || d.WhiteRecord_Status == 6)
                            return tempBar(1, "已处理");
                        else
                            return tempBar(5, "未处理");
                        // if (d.WhiteRecord_Status == 1) return tempBar(1, "添加成功");
                        // else if (d.WhiteRecord_Status == 2) return tempBar(4, "添加失败");
                        // else if (d.WhiteRecord_Status == 3) return tempBar(2, "删除成功");
                        // else if (d.WhiteRecord_Status == 4) return tempBar(4, "删除失败");
                        // else if (d.WhiteRecord_Status == 5) return tempBar(6, "清空成功");
                        // else if (d.WhiteRecord_Status == 6) return tempBar(4, "清空失败");
                        // else return tempBar(5, "未处理");
                    }
                }
                , { field: 'WhiteRecord_Time', title: '操作时间', width: 170 }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: 'GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100, 1000]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                    pager.GetCarWhiteCount();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();

                switch (obj.event) {
                    case 'Send':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].WhiteRecord_ID);
                        }
                        layer.open({
                            id: 2,
                            type: 0,
                            title: false,
                            btn: ["确定", "取消"],
                            content: "确认重新同步已勾选的车牌数据吗?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Send", { WhiteRecord_IDArray: JSON.stringify(NoArray) }, function (json) {
                                    if (json.success) {

                                        layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    } else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].WhiteRecord_ID);
                        }

                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "删除当前名单状态记录，不会影响相机名单，确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { NoArray: JSON.stringify(NoArray) }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'SyncWhite':
                        layer.open({
                            id: 2,
                            type: 0,
                            title: false,
                            btn: ["确定", "取消"],
                            content: "一键同步所有白名单车辆到所有相机，确定吗?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("SyncWhite", {}, function (json) {
                                    if (json.success) {
                                        layer.msg("正在执行，可刷新当前记录查看状态", { icon: 1, time: 3000 }, function () { });
                                    } else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'ClearAll':
                        layer.open({
                            id: 2,
                            type: 0,
                            title: false,
                            btn: ["确定", "取消"],
                            content: "一键注销所有连接相机的黑白名单，确定吗?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("ClearAll", {}, function (json) {
                                    if (json.success) {
                                        layer.msg("正在执行，可刷新当前记录查看状态", { icon: 1, time: 3000 }, function () { });
                                    } else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'SyncBlack':
                        layer.open({
                            id: 2,
                            type: 0,
                            title: false,
                            btn: ["确定", "取消"],
                            content: "一键同步所有黑名单车辆到所有相机，确定吗?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("SyncBlack", {}, function (json) {
                                    if (json.success) {
                                        layer.msg("正在执行，可刷新当前记录查看状态", { icon: 1, time: 3000 }, function () { });
                                    } else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    default: break;
                }

            });

            // // 监听列选择操作
            // table.on('checkbox(com-table-base)', function (obj) {
            //     debugger
            //     // 保存用户选择的列的 ID 到 localStorage
            //     var checkedCols = obj.checked ? obj.data : table.checkStatus('com-table-base').data;
            //     var checkedColsIds = checkedCols.map(function (col) {
            //         return col.field;
            //     });
            //     localStorage.setItem('cols', JSON.stringify(checkedColsIds));
            // });

            tb_row_checkbox();

            pager.init();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["WhiteRecord_Time0", "WhiteRecord_Time1"], { type: 'datetime', range: true });
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: 'GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            },
            GetCarWhiteCount: function () {
                $.getJSON("GetCarWhiteCount", {}, function (json) {
                    if (json.success) {
                        $(".totalenable").text(json.data.enableCount)
                        $(".totalcancel").text(json.data.cancelCount)
                        $(".totalsync").text(json.data.syncCount)
                    }
                });
            }
        }
    </script>
</body>
</html>
