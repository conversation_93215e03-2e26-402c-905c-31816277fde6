﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>临停计费规则测试</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .layui-form label { padding: 9px 9px 0; float: right; text-align: right; }
        .layui-form .layui-row { margin-bottom: 15px; }
        .layui-body { padding: 30px 0; overflow: auto; position: absolute; left: 0; top: 0; right: 0; bottom: 0; }
        .layui-select-title input { color: #0094ff; }
        .help-btn { z-index: 999; position: absolute; width: 20px; margin-left: 7px; margin-top: 6px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }
        .help-tmpl { width: 100%; height: auto; min-height: 100px; position: relative; line-height: 24px; }
        .help-tmpl ul { margin: 10px; padding: 0; list-style: none; }
        .help-tmpl p { margin: 10px; padding: 0; text-indent: 0; }
        .help-tmpl .help-footer { margin: 10px; }
        .help-tmpl ul img { box-shadow: 0px 1px 10px #bbb; max-width: 100%; }
        .lbl { margin-top: 2px; }
        .lbl label { float: left !important; }
        .layui-form-checkbox span, i { padding-top: 4px; padding-bottom: 4px; }
    </style>
</head>
<body>
    <div id="verifyCheck" class="layui-body layui-form">
        @*<div class="layui-row">
                <div class="layui-col-xs2"><label>车牌类型</label></div>
                <div class="layui-col-xs3">
                    <select class="layui-input" id="Debug_CarCardType" name="Debug_CarCardType">
                    </select>
                </div>
                <div class="layui-col-xs2"><label>车辆/收费类型</label></div>
                <div class="layui-col-xs3">
                    <select class="layui-select" id="Debug_CarType" name="Debug_CarType">
                    </select>
                </div>
            </div>*@
        <!--<div class="layui-row">
        <div class="layui-col-xs2"><label>折扣/优惠</label></div>
        <div class="layui-col-xs3">
            <select class="layui-input" id="Debug_ParkDiscountSet" name="Debug_ParkDiscountSet">
            </select>
        </div>-->
        @*<div class="layui-col-xs2"><label>车牌有效期(用于判断过期收费)</label></div>
            <div class="layui-col-xs3">
                <input type="text" class="layui-input" id="Debug_ExpDate" name="Debug_ExpDate" autocomplete="off" />
            </div>*@
        <!--</div>-->
        <div class="layui-row">
            <div class="layui-col-xs2"><label>当前周期计费结束时间</label></div>
            <div class="layui-col-xs3">
                <input type="text" class="layui-input" id="Debug_StartTime" name="Debug_StartTime" autocomplete="off" />
            </div>
            <div><i class="help-btn" data-key="Debug_StartTime">?</i></div>
            <div class="layui-col-xs2"><label>周期已累计金额</label></div>
            <div class="layui-col-xs3">
                <input type="text" class="layui-input v-null" id="Debug_Amounted" name="Debug_Amounted" autocomplete="off" value="0" />
            </div>
            <div><i class="help-btn" data-key="Debug_Amounted">?</i></div>
        </div>
        <div class="layui-row">
            <div class="layui-col-xs2"><label>车辆入场通道</label></div>
            <div class="layui-col-xs3">
                <select class="layui-input" id="Debug_Passway1" name="Debug_Passway1">
                </select>
            </div>
            <div class="layui-col-xs2"><label>车辆入场时间</label></div>
            <div class="layui-col-xs3 inTime">
                <input type="text" class="layui-input v-null time v-datetime" id="Debug_EnterTime" name="Debug_EnterTime" autocomplete="off" />
            </div>
            <div class="layui-col-xs1">
                <div class="layui-input-block" style="margin-left:auto !important;">
                    <input type="checkbox" name="time" id="inTime" title="手输">
                </div>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-xs2"><label>车辆离场通道</label></div>
            <div class="layui-col-xs3">
                <select class="layui-input" id="Debug_Passway2" name="Debug_Passway2">
                </select>
            </div>
            <div class="layui-col-xs2"><label>车辆离场时间</label></div>
            <div class="layui-col-xs3 outTime">
                <input type="text" class="layui-input v-null time v-datetime" id="Debug_OutTime" name="Debug_OutTime" autocomplete="off" />
            </div>
            <div class="layui-col-xs1">
                <div class="layui-input-block" style="margin-left:auto !important;">
                    <input type="checkbox" name="time" id="outTime" title="手输">
                </div>
            </div>
        </div>
        <hr class="layui-border-blue" />
        <div class="layui-row">
            <div class="layui-col-xs2"><label>&nbsp;</label></div>
            <div class="layui-col-xs6">
                &nbsp;
                @*<span style="color:red;font-weight:bold;font-size:2rem;" class="countMoney">0.00</span>*@
            </div>
            <div class="layui-col-xs2">
                <button class="layui-btn layui-btn-sm" style="float:right;" id="Calc">开始计算</button>
            </div>
        </div>
        <div class="layui-bg-gray layui-hide clac" style="padding: 30px;">
            <div class="layui-row layui-col-space15">
                <div class="layui-col-md6">
                    <div class="layui-card">
                        <div class="layui-card-header">计费结果</div>
                        <div class="layui-card-body">
                            <div class="layui-row">
                                <div class="layui-col-xs3"><label class="control-label">入场时间：</label></div>
                                <div class="layui-col-xs8 lbl"><label id="begintime"></label></div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-xs3"><label class="control-label">离场时间：</label></div>
                                <div class="layui-col-xs8 lbl"><label id="endtime"></label></div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-xs3"><label class="control-label">停车时长：</label></div>
                                <div class="layui-col-xs8 lbl"><label id="parktimemin"></label></div>
                            </div>
                            <div class="layui-row">
                                <div class="layui-col-xs3"><label class="control-label">应付金额：</label></div>
                                <div class="layui-col-xs8 lbl" style="margin-top: -2px; "><label style="color:red;font-weight:bold;font-size:2rem; margin-bottom:5px;" class="countMoney">0</label></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v=1.0" asp-append-version="true"></script>
    <script type="text/javascript">
        var paramChargeRulesNo = $.getUrlParam("ChargeRules_No");

        var layuiForm = null;
        layui.use(['element', 'form'], function () {
            layuiForm = layui.form;
            laydate = layui.laydate;
            myVerify.init();
            pager.init();


            //<input type="text" class="layui-input v-null time" id="Debug_EnterTime" name="Debug_EnterTime" autocomplete="off" />
            // <input type="text" class="layui-input v-null time" id="Debug_OutTime" name="Debug_OutTime" autocomplete="off" />
            // pager.ShowDate("Debug_EnterTime", "Debug_OutTime");
            //单选框事件
            layuiForm.on('checkbox', function (obj) {
                var Debug_EnterTime = $("#Debug_EnterTime").val();
                var Debug_OutTime = $("#Debug_OutTime").val();
                $(".time").remove();
                $(".inTime").html('<input type="text" class="layui-input v-null time v-datetime" id="Debug_EnterTime" name="Debug_EnterTime" autocomplete="off" />');
                $(".outTime").html('<input type="text" class="layui-input v-null time v-datetime" id="Debug_OutTime" name="Debug_OutTime" autocomplete="off" />');
                if (obj.elem.checked == true) {
                    if (obj.elem.id == "inTime") {
                        var outTimeCk = false;
                        $('input[id="outTime"]:checked').each(function (index, obj) {
                            outTimeCk = true;
                        });
                        if (!outTimeCk) {
                            pager.initDate("#Debug_OutTime");
                        }
                    }
                    if (obj.elem.id == "outTime") {
                        var inTimeCk = false;
                        $('input[id="inTime"]:checked').each(function (index, obj) {
                            inTimeCk = true;
                        });
                        if (!inTimeCk) {
                            pager.initDate("#Debug_EnterTime");
                        }
                    }
                } else {
                    if (obj.elem.id == "inTime") {
                        var outTimeCk = false;
                        $('input[id="outTime"]:checked').each(function (index, obj) {
                            outTimeCk = true;
                        });
                        if (!outTimeCk) {
                            pager.ShowDate("Debug_EnterTime", "Debug_OutTime");
                        } else {
                            pager.initDate("#Debug_EnterTime", "");
                        }
                    }
                    if (obj.elem.id == "outTime") {
                        var inTimeCk = false;
                        $('input[id="inTime"]:checked').each(function (index, obj) {
                            inTimeCk = true;
                        });
                        if (!inTimeCk) {
                            pager.ShowDate("Debug_EnterTime", "Debug_OutTime");
                        } else {
                            pager.initDate("#Debug_OutTime");
                        }
                    }
                }

                if (Debug_EnterTime != "") {
                    $("#Debug_EnterTime").val(Debug_EnterTime);
                }
                if (Debug_OutTime != "") {
                    $("#Debug_OutTime").val(Debug_OutTime);
                }
                laydate.render();
            });
        })

        var dt = new Date($.ajax({ async: false }).getResponseHeader("Date")).Format("yyyy-MM-dd hh:mm:ss");
        var pager = {
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                bindTip();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                //$.post("SltParkDiscountSetList", {}, function (json) {
                //    if (json.success) {
                //        $("#Debug_ParkDiscountSet").html('<option value="">请选择优惠</option>')
                //        json.data.forEach(function (d, i) {
                //            var value = "";
                //            if (d.ParkDiscountSet_Type == 101) value = '优惠' + d.ParkDiscountSet_Amount + '元';
                //            if (d.ParkDiscountSet_Type == 102) value = '优惠' + d.ParkDiscountSet_Duration + '分钟';
                //            if (d.ParkDiscountSet_Type == 103) value = '折扣比例' + d.ParkDiscountSet_Ratio + '%';
                //            if (d.ParkDiscountSet_Type == 104) value = '免费到' + d.ParkDiscountSet_AppointHour;

                //            var option = '<option value="' + d.ParkDiscountSet_No + '">' + d.ParkDiscountSet_Name + '[' + value + ']' + '</option>';
                //            $("#Debug_ParkDiscountSet").append(option)
                //        });
                //    }
                //}, "json");

                $.post("SltPasswayList2", { ChargeRules_No: paramChargeRulesNo }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            if (d.PasswayLink_GateType == 1)
                                $("#Debug_Passway1").append(option);
                            else
                                $("#Debug_Passway2").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select")
                pager.ShowDate("Debug_EnterTime", "Debug_OutTime");
                pager.initDate("#Debug_ExpDate");
                pager.initDate("#Debug_StartTime");
            },
            bindData: function () {

            },
            bindEvent: function () {
                $("#Calc").click(function () {

                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.ChargeRules_No = paramChargeRulesNo;
                        return data;
                    });

                    var beginTime = new Date(param.Debug_EnterTime);
                    var endTime = new Date(param.Debug_OutTime);
                    if (beginTime > endTime) {
                        layer.tips("车辆进场时间不能大于车辆离场时间", "#Debug_EnterTime", { time: 2000 });
                        return;
                    }

                    layer.msg("计算中", { icon: 16, time: 0 });

                    $.post("Calc", { jsonModel: JSON.stringify(param) }, function (json) {
                        $(".clac").removeClass("layui-hide");
                        $("#begintime").html($("#Debug_EnterTime").val());
                        $("#endtime").html($("#Debug_OutTime").val());
                        var zhTimes = _DATE.getZhTimes(new Date($("#Debug_EnterTime").val()), new Date($("#Debug_OutTime").val()));
                        $("#parktimemin").html(zhTimes);
                        if (json.success) {
                            if (json.data) {
                                if (json.data.payed != 2)
                                    $(".countMoney").text(json.data.payedamount);
                                else
                                    $(".countMoney").text(json.data.payedmsg);
                            } else {
                                $(".countMoney").text("无数据");
                            }
                            layer.msg("计算成功", { icon: 1, time: 1500 }, function () {

                            });
                        } else {
                            $(".countMoney").text("计费失败");
                            layer.msg(json.msg, { icon: 5, time: 1500 }, function () {

                            });
                        }
                    }, "json");
                })
            },
            bindPower: function () {
                //window.parent.parent.global.getBtnPower(window, function (pagePower) {
                //    if (pagePower['Save']) {
                //        $("button.save").removeClass("layui-hide");
                //    }
                //});
            },
            ShowDate: function (id1, id2) {
                if (id1 != "" && id2 != "")
                    _DATE.bind(layui.laydate, [id1, id2], { type: 'datetime', range: true });
                else
                    _DATE.bind(layui.laydate, [id1], { type: 'datetime' });
            },
            initDate: function (id1) {
                var enterstart = {
                    elem: id1, type: 'datetime', start: dt, min: "2021-01-01 00:00:00", max: "2099-06-16 00:00:00", lang: "cn",
                    istime: true, istoday: true, choose: function (datas) { },
                    done: function (value, date) {
                    }
                };
                var dateEntryStart = laydate.render(enterstart);
            },
        }
    </script>

    <script>
        function bindTip() {
            $(".help-btn").hover(function () {
                var key = $(this).attr("data-key");
                var content = getHelpContent(key);
                layer.tips(content, this, { time: 0, tips: [3, '#090a0c'] })
            }, function () {
                layer.closeAll();
            });
        }

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return "";
            return data.Description;
        }
        //提示信息数据
        var HelpData = [
            {
                key: "Debug_StartTime",
                Description: ["当前周期计费结束时间：停车周期时长可多次进出场累积；若计费规则开启了周期累计，这里可指定当前周期计费到某个时间点结束累计。"],
            }, {
                key: "Debug_Amounted",
                Description: ["周期已累计金额：停车周期计费用可多次进出场累积；若计费规则开启了周期累计，这里可指定周期累计的费用达到了多少。"],
            }
        ];
    </script>
</body>
</html>
