﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>储值车扣费记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>储值车扣费记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_No" id="PayOrder_No" autocomplete="off" placeholder="支付订单号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_CarNo" id="PayOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付状态" class="layui-select" id="PayOrder_Status" name="PayOrder_Status" lay-search>
                                    <option value="">支付状态</option>
                                    <option value="0">未支付</option>
                                    <option value="1">支付成功</option>
                                    <option value="2">支付失败</option>
                                    <option value="3">用户取消</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付方式" class="layui-select" id="PayOrder_PayTypeCode" name="PayOrder_PayTypeCode" lay-search>
                                    <option value="">支付方式</option>
                                    <option value="79001">线下现金</option>
                                    <option value="79002">平台现金支付</option>
                                    <option value="79003">微信支付</option>
                                    <option value="79004">Android端支付</option>
                                    <option value="79006">终端设备支付</option>
                                    <option value="79007">支付宝支付</option>
                                    <option value="79009">第三方支付</option>
                                    <option value="79010">线下微信</option>
                                    <option value="79011">线下支付宝</option>
                                    <option value="79014">建行支付</option>
                                    <option value="79015">招行一网通支付</option>
                                    <option value="79016">银联无感支付</option>
                                    <option value="79017">建行无感支付</option>
                                    <option value="79012">支付宝无感支付</option>
                                    <option value="79013">微信无感支付</option>
                                    <option value="79018">威富通聚合支付</option>
                                    <option value="79019">招行无感支付</option>
                                    <option value="79020">工行无感支付</option>
                                    <option value="79021">工行支付</option>
                                    <option value="79022">农行无感支付</option>
                                    <option value="79023">农行支付</option>
                                    <option value="79024">ETC支付</option>
                                    <option value="79025">中行支付</option>
                                    <option value="79026">中行无感支付</option>
                                    <option value="79027">乐聚合支付</option>
                                    <option value="79039">随行付</option>
                                    <option value="79028">银联商务</option>
                                    <option value="99999">其他支付</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_PayedTime0" id="PayOrder_PayedTime0" autocomplete="off" placeholder="支付时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_PayedTime1" id="PayOrder_PayedTime1" autocomplete="off" placeholder="支付时间止" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">

                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplimg">
        <a href="{{d.CarRecog_Img}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看"><i class="layui-icon layui-icon-picture"></i>预览</a>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.PayOrder_Status==0){}}
        <span class="layui-badge layui-bg-orange">未支付</span>
        {{# }else if(d.PayOrder_Status==1){ }}
        <span class="layui-badge layui-bg-green">支付成功</span>
        {{# }else if(d.PayOrder_Status==2){ }}
        <span class="layui-badge layui-bg-red">支付失败</span>
        {{# }else if(d.PayOrder_Status==3){ }}
        <span class="layui-badge layui-bg-cyan">用户取消</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplordertype">
        {{# if(d.PayOrder_OrderTypeNo==5905){}}
        <span class="layui-badge layui-bg-blue">储值车扣费</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpaytypecode">
        {{# if(d.PayOrder_PayTypeCode==79001){}}
        <span class="layui-badge layui-bg-red">线下现金</span>
        {{# }else if(d.PayOrder_PayTypeCode==79002){ }}
        <span class="layui-badge layui-bg-red">平台现金支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79003){ }}
        <span class="layui-badge layui-bg-wxgreen">微信支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79010){ }}
        <span class="layui-badge layui-bg-wxgreen">线下微信</span>
        {{# }else if(d.PayOrder_PayTypeCode==79013){ }}
        <span class="layui-badge layui-bg-wxgreen">微信无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79007){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79011){ }}
        <span class="layui-badge layui-bg-alipayblue">线下支付宝</span>
        {{# }else if(d.PayOrder_PayTypeCode==79012){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79004){ }}
        <span class="layui-badge layui-bg-cyan">Android端支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79006){ }}
        <span class="layui-badge layui-bg-cyan">终端设备支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79009){ }}
        <span class="layui-badge layui-bg-cyan">第三方支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79014){ }}
        <span class="layui-badge layui-bg-ylblue">建行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79015){ }}
        <span class="layui-badge layui-bg-ylblue">招行一网通支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79016){ }}
        <span class="layui-badge layui-bg-ylblue">银联无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79017){ }}
        <span class="layui-badge layui-bg-ylblue">建行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79018){ }}
        <span class="layui-badge layui-bg-ylblue">威富通聚合支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79019){ }}
        <span class="layui-badge layui-bg-ylblue">招行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79020){ }}
        <span class="layui-badge layui-bg-ylblue">工行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79021){ }}
        <span class="layui-badge layui-bg-ylblue">工行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79022){ }}
        <span class="layui-badge layui-bg-ylblue">农行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79023){ }}
        <span class="layui-badge layui-bg-ylblue">农行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79024){ }}
        <span class="layui-badge layui-bg-ylblue">ETC支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79025){ }}
        <span class="layui-badge layui-bg-ylblue">中行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79026){ }}
        <span class="layui-badge layui-bg-ylblue">中行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79027){ }}
        <span class="layui-badge layui-bg-ylblue">乐聚合支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79039){ }}
        <span class="layui-badge layui-bg-ylblue">随行付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79028){ }}
        <span class="layui-badge layui-bg-ylblue">银联商务</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmpltime">
        {{# if(d.PayOrder_TempTimeCount!=null){}}
        <span>{{d.PayOrder_TempTimeCount}}分钟</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'PayOrder_ID', title: 'ID', hide: true }
                , { field: 'PayOrder_No', title: '支付订单号', hide: true }
                , { field: 'PayOrder_CarNo', title: '车牌号' }
                , { field: 'PayOrder_Status', title: '支付状态', toolbar: "#tmplstatus" }
                , { field: 'PayOrder_Money', title: '应收金额', totalRow: true }
                , { field: 'PayOrder_PayedMoney', title: '实收金额', totalRow: true }
                , { field: 'PayOrder_PayedTime', title: '支付时间' }
                , { field: 'PayOrder_PayTypeCode', title: '支付方式', toolbar: "#tmplpaytypecode" }
                , { field: 'PayOrder_OrderTypeNo', title: '订单类型', toolbar: "#tmplordertype" }
                , { field: 'PayOrder_TempTimeCount', title: '停车时长', toolbar: "#tmpltime" }
                , { field: 'PayOrder_TimeCountDesc', title: '时长描述', hide: true }
                , { field: 'PayOrder_OperatorName', title: '操作员' }
                , { field: 'PayOrder_DiscountMoney', title: '优惠金额', totalRow: true }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/StoreRecord/GetPayOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {

                };
            });

            tb_row_checkbox()
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PayOrder_PayedTime0", "PayOrder_PayedTime1"], { type: "datetime", range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/StoreRecord/GetPayOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("PayOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "PayOrder_CarNo") {
                        $("#PayOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }
    </script>
</body>
</html>
