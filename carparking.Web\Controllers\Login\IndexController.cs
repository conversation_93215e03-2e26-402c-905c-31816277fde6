﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Cache.Web;
using carparking.CloudLink;
using carparking.Common;
using carparking.Config;
using carparking.DirectCloudMQTT;
using carparking.DirectCloudMQTT.Model;
using carparking.Library;
using carparking.Model;
using carparking.PassTool;
using carparking.SentryBox;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using TcpConnPools.Camera;
using TcpConnPools.Camera.Camera06;
using TcpConnPools.Camera.Camera15;

namespace carparking.Web.Controllers
{
    public class IndexController : BaseController
    {
        private ResultBase rb = new ResultBase();

        public IActionResult Index()
        {
            //if (!Powermanage.PowerCheck("Index", PowerEnum.View.ToString(), false, true, lgAdmins))
            //    return new EmptyResult();

            string webTitle = "停车场后台管理系统";
            try
            {
                ViewBag.VersionType = lgAdmins?.Admins_VersionType;

                Model.SysConfig model = BLL.SysConfig._GetEntityByWhere(new Model.SysConfig(), "*", $"1=1");

                if (model?.SysConfig_Content != null)
                {
                    model.SysConfig_Content = BLL.SysConfig.GetUrlDecode(model.SysConfig_Content);
                    Model.SysConfigContent content = TyziTools.Json.ToModel<Model.SysConfigContent>(model.SysConfig_Content);
                    if (Startup.GetPlModelBase != null && !string.IsNullOrWhiteSpace(Startup.GetPlModelBase.WebTitle))
                    {
                        content.SysConfig_DIYEnable = 1;
                        content.SysConfig_DIYName = Startup.GetPlModelBase.WebTitle;
                    }

                    if (content.SysConfig_DIYEnable == 1)
                    {
                        model.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(content));
                    }

                    if (content != null && content.SysConfig_DIYEnable == 1)
                    {
                        ViewBag.SysConfig_DIYEnable = 1;
                        webTitle = content.SysConfig_DIYName ?? "停车场后台管理系统";
                    }
                }

                ViewBag.model = model == null ? "" : System.Web.HttpUtility.UrlEncode(TyziTools.Json.ToString(model));

                GetSqlConnectByConfig(out var dbdata);
                ViewBag.DBIP = dbdata?.IP ?? "";
                ViewBag.DBName = dbdata?.DbName ?? "";

                ViewBag.SysConfig_DIYName = webTitle;
                ViewBag.lgAdmins = lgAdmins;
                ViewBag.Parking = parking;
                ViewBag.ApiVersion_FB = AppSettingConfig.ApiVersion_FB;
                ViewBag.ApiVersion = AppSettingConfig.ApiVersion;
                ViewBag.IsWindows = AppBasicCache.IsWindows;

                AppBasicCache.ConnectionParking_CToken = null;
                AppBasicCache.ConnectionParking_HttpToken = null;

            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex);
            }

            return View();
        }

        public IActionResult IndexEs()
        {
            return View();
        }


        public IActionResult Home()
        {
            //if (!Powermanage.PowerCheck("Index", PowerEnum.Home.ToString(), false, true, lgAdmins))
            //    return new EmptyResult();
            ViewBag.lgAdmins = lgAdmins;
            return View();
        }

        public IActionResult HomeN()
        {
            //if (!Powermanage.PowerCheck("Index", PowerEnum.Home.ToString(), false, true, lgAdmins))
            //    return new EmptyResult();
            try
            {
                ViewBag.OnSiteVehicles = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.OnSiteVehicles.ToString(), lgAdmins);
                ViewBag.TodayCharge = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.TodayCharge.ToString(), lgAdmins);
                ViewBag.FixedNumberVehicles = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.FixedNumberVehicles.ToString(), lgAdmins);
                ViewBag.OnlineDevices = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.OnlineDevices.ToString(), lgAdmins);

                var code = Utils.CreateNumber_SnowFlake;
                DataCache.WebAdmin.Set(code, lgAdmins, false);
                ViewBag.Code = code;
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "首页异常：" + ex.ToString());
            }

            return View();
        }

        public ActionResult ChangePassword()
        {
            //if (!Powermanage.PowerCheck("Index", PowerEnum.ChangePwd.ToString()))
            //    return new EmptyResult();

            return View();
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="jsonModel"></param>
        public void PasswordUpdate(string encrypted)
        {
            string jsonModel = "";
            try
            {
                if (lgAdmins == null)
                {
                    rb.Success = false;
                    rb.Message = "登录失效，请重新登录";
                }


                if (string.IsNullOrWhiteSpace(encrypted))
                {
                    MiniResponse.ResponseResult($"密码信息错误，请重试", false);
                    return;
                }

                var logMsg = RSAHelper.Decrypt(encrypted);

                var jo = TyziTools.Json.ToObject<JObject>(logMsg);
                if (jo == null || !jo.ContainsKey("jsonModel"))
                {
                    MiniResponse.ResponseResult($"密码信息错误，请重试", false);
                    return;
                }

                jsonModel = jo["jsonModel"].ToString();
                if (string.IsNullOrWhiteSpace(jsonModel))
                {
                    MiniResponse.ResponseResult($"密码信息错误，请重试", false);
                    return;
                }

                JObject obj = (JObject)JsonConvert.DeserializeObject(jsonModel);

                if (obj["Admins_Pwd_Old"] != null && obj["Admins_Pwd_New"] != null && obj["Admins_Pwd_New_Again"] != null)
                {

                    if (AppBasicCache.CurrentSysConfigContent.SysConfig_PwdType == 1)
                    {
                        if (!Utils.IsStrongPassword(obj["Admins_Pwd_New"].ToString()))
                        {
                            MiniResponse.ResponseResult("您的密码为弱密码，存在安全隐患，请修改", false, null); return;
                        }
                    }

                    string Admins_Pwd_Old = Utils.MD5Encrypt(obj["Admins_Pwd_Old"] + Utils.passwordMD5String, Encoding.UTF8);
                    string Admins_Pwd_New = Utils.MD5Encrypt(obj["Admins_Pwd_New"] + Utils.passwordMD5String, Encoding.UTF8);

                    if (BLL.Admins.Exists("Admins_ID = '" + lgAdmins.Admins_ID +
                                          "' AND Admins_Name = '" + lgAdmins.Admins_Name +
                                          "' AND Admins_Pwd = '" + Admins_Pwd_Old + "'"))
                    {
                        if (BLL.Admins.UpdateByModel(new Model.Admins()
                        {
                            Admins_ID = lgAdmins.Admins_ID,
                            Admins_Name = lgAdmins.Admins_Name,
                            Admins_Pwd = Admins_Pwd_New
                        }) > 0)
                        {
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "修改密码", $"修改密码[{lgAdmins.Admins_Account}]:");
                            rb.Success = true;
                            rb.Code = "0000";
                            rb.Message = "ok";
                        }
                    }
                    else
                    {
                        rb.Success = false;
                        rb.Message = "原密码错误";
                    }
                }
                else
                {
                    rb.Success = false;
                    rb.Message = "参数错误";
                }

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.ModifyPassword, $"修改密码：{(rb.Success ? "成功" : "失败")}", SecondIndex.IndexData);

                Response.WriteAsync(JsonConvert.SerializeObject(rb));
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.ModifyPassword, $"修改密码发生异常：{ex.Message}", SecondIndex.IndexData);
                MiniResponse.ResponseResult("异常错误", false, null, "异常错误");
            }
        }

        /// <summary>
        /// 退出登录
        /// </summary>
        public IActionResult ExitLogin()
        {
            try
            {
                string key = $"{Sessions.AdminSession}:{lgAdmins?.Admins_ID}";

                LocalCache.Del(key);
                var ret = new Cookies().delCookie(Sessions.AdminSession);

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Login, $"{lgAdmins?.Admins_Account}退出登录", SecondIndex.IndexData);
                return Json(new { Success = true, Message = "" });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Login, $"{lgAdmins?.Admins_Account}退出登录异常：{ex.Message}", SecondIndex.IndexData);
                return Json(new { Success = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// 获取场内车辆、剩余车位数
        /// </summary>
        /// <returns></returns>
        public IActionResult CarSpaceAnalysis(string code = "")
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                List<Model.ParkOrder> orders = BLL.ParkOrder.GetAllEntity("ParkOrder_No,ParkOrder_CarNo,ParkOrder_EnterTime,ParkOrder_IsNoInRecord,ParkOrder_IsEpCar", $"ParkOrder_ParkNo='{parking.Parking_No}' AND ParkOrder_StatusNo='{Model.EnumParkOrderStatus.In}'");
                orders = orders?.FindAll(x => x.ParkOrder_IsNoInRecord == 0);

                //List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity("OrderDetail_ParkOrderNo", $"OrderDetail_ParkNo='{parking.Parking_No}' AND OrderDetail_StatusNo='{Model.EnumParkOrderStatus.In}'");
                //var detailNoList = details?.Select(x => x.OrderDetail_ParkOrderNo);

                ////无明细订单则是无入场记录,不占用车位数
                //orders?.RemoveAll(x => !detailNoList.Contains(x.ParkOrder_No));

                //过期不占用车位
                List<Model.ParkOrder> orderExp = null;
                var policy = BLL.PolicyPark.GetEntity(parking.Parking_No);
                if (policy != null && policy.PolicyPark_OccupyDay > 0)
                    orderExp = orders?.FindAll(x => x.ParkOrder_EnterTime < DateTime.Parse(DateTimeHelper.GetNowTime().AddDays(-policy.PolicyPark_OccupyDay.Value).ToString("yyyy-MM-dd")));

                List<Model.ParkArea> areas = BLL.ParkArea.GetAllEntity("ParkArea_SpaceNum", $"ParkArea_ParkNo='{parking.Parking_No}'");

                long inParkCount = (orders?.Count ?? 0) - (orderExp?.Count ?? 0);
                long spaceNum = areas?.Sum(x => x.ParkArea_SpaceNum).Value ?? 0;
                long haveNum = spaceNum - inParkCount;
                long parkCarCount = orders?.FindAll(x => x.ParkOrder_IsEpCar == 1)?.Count ?? 0;

                return ResOk(true, "success", new { inParkCount = inParkCount, spaceNum = spaceNum, haveNum = haveNum, parkCarCount = parkCarCount });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取场内车辆、剩余车位数异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 车流量分析
        /// </summary>
        /// <returns></returns>
        public IActionResult TrafficAnalysis(int type, string code = "")
        {
            try
            {
                var IsTodayCars = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.TodayCars.ToString(), lgAdmins);
                var curr = DateTimeHelper.GetNowTime();
                var selectWhere = new StringBuilder();
                selectWhere.Append($" ParkOrder_ParkNo='{parking.Parking_No}' ");
                selectWhere.Append($" AND ParkOrder_StatusNo in (200,201,202) ");
                var xData = new List<string>();
                var yDataCarIn = new List<string>();
                var yDataCarOut = new List<string>();
                var iCount = 0;
                if (type == 0)
                {
                    while (iCount < 24)
                    {
                        xData.Add(iCount.ToString().PadLeft(2, '0'));
                        iCount++;
                    }

                    if (!IsTodayCars)
                    {
                        for (var i = 0; i < xData.Count; i++)
                        {
                            yDataCarIn.Add("0");
                            yDataCarOut.Add("0");
                        }
                    }
                }

                if (!IsTodayCars)
                    return ResOk(true, "success", new { xData = xData, yDataCarIn = yDataCarIn, yDataCarOut = yDataCarOut });

                switch (type)
                {
                    //今日车流量分析
                    case 0:
                        selectWhere.Append($" AND (ParkOrder_EnterTime Between '{curr:yyyy-MM-dd 00:00:00}' AND '{curr:yyyy-MM-dd 23:59:59}' OR ParkOrder_OutTime Between '{curr:yyyy-MM-dd 00:00:00}' AND '{curr:yyyy-MM-dd 23:59:59}') ");
                        break;
                    //一周内车流量分析
                    case 7:
                        {
                            selectWhere.Append($" AND (ParkOrder_EnterTime Between '{curr.AddDays(-6).ToString("yyyy-MM-dd 00:00:00")}' AND '{curr.ToString("yyyy-MM-dd 23:59:59")}' OR ParkOrder_OutTime Between '{curr.AddDays(-6).ToString("yyyy-MM-dd 00:00:00")}' AND '{curr.ToString("yyyy-MM-dd 23:59:59")}') ");

                            var dt = curr.AddDays(-6);
                            while (DateTime.Parse(dt.ToString("yyyy-MM-dd")) <= DateTime.Parse(curr.ToString("yyyy-MM-dd")))
                            {
                                xData.Add(dt.ToString("yyyy-MM-dd"));
                                dt = dt.AddDays(1);
                            }

                            break;
                        }
                    //30天内车流量分析
                    case 30:
                        {
                            selectWhere.Append($" AND (ParkOrder_EnterTime Between '{curr.AddDays(-29).ToString("yyyy-MM-dd 00:00:00")}' AND '{curr.ToString("yyyy-MM-dd 23:59:59")}' OR ParkOrder_OutTime Between '{curr.AddDays(-29).ToString("yyyy-MM-dd 00:00:00")}' AND '{curr.ToString("yyyy-MM-dd 23:59:59")}') ");

                            var dt = curr.AddDays(-29);
                            while (DateTime.Parse(dt.ToString("yyyy-MM-dd")) <= DateTime.Parse(curr.ToString("yyyy-MM-dd")))
                            {
                                xData.Add(dt.ToString("yyyy-MM-dd"));
                                dt = dt.AddDays(1);
                            }

                            break;
                        }
                }

                var orderList = BLL.BaseBLL._GetAllEntity(new Model.ParkOrder(), "ParkOrder_EnterTime,ParkOrder_OutTime", selectWhere.ToString());

                if (type == 0)
                {
                    var curInOrder = orderList.FindAll(x => x.ParkOrder_EnterTime != null && x.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd") == curr.ToString("yyyy-MM-dd"));
                    var curOutOrder = orderList.FindAll(x => x.ParkOrder_OutTime != null && x.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd") == curr.ToString("yyyy-MM-dd"));
                    foreach (var t in xData)
                    {
                        var inNum = curInOrder.FindAll(x => x.ParkOrder_EnterTime != null && x.ParkOrder_EnterTime.Value.ToString("HH") == t).Count;
                        var outNum = curOutOrder.FindAll(x => x.ParkOrder_OutTime != null && x.ParkOrder_OutTime.Value.ToString("HH") == t).Count;
                        yDataCarIn.Add(inNum.ToString());
                        yDataCarOut.Add(outNum.ToString());
                    }
                }
                else
                {
                    foreach (var t in xData)
                    {
                        var inNum = orderList.FindAll(x => x.ParkOrder_EnterTime != null && x.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd") == t).Count;
                        var outNum = orderList.FindAll(x => x.ParkOrder_OutTime != null && x.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd") == t).Count;
                        yDataCarIn.Add(inNum.ToString());
                        yDataCarOut.Add(outNum.ToString());
                    }
                }

                return ResOk(true, "success", new { xData = xData, yDataCarIn = yDataCarIn, yDataCarOut = yDataCarOut });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.TrafficAnalysis, $"车流量分析异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取场内车辆数/剩余车位数
        /// </summary>
        /// <returns></returns>
        public IActionResult OrderNumber(string code = "")
        {
            try
            {
                bool IsOnSiteVehicles = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.OnSiteVehicles.ToString(), lgAdmins);
                int spaceCarCount = 0;
                if (IsOnSiteVehicles)
                {
                    var spaceData = MonitorHelperBiz.GetAllSpaceItems(parking.Parking_No, null, null);
                    //taotalCount = spaceData.Item1;
                    spaceCarCount = spaceData.Item2;
                }

                List<int> countList = BLL.BaseBLL._Execute<int>("select count(1)  from incar WHERE InCar_Status=200");

                var data = new
                {
                    num1 = countList == null ? 0 : countList[0],
                    num2 = spaceCarCount
                };

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取场内车辆数/剩余车位数异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取今日收费/昨日收费
        /// </summary>
        /// <returns></returns>
        public IActionResult PayNumber(string code = "")
        {
            try
            {
                bool IsChargeData = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.TodayCharge.ToString(), lgAdmins);

                List<Model.PayOrder> yespays = null;
                List<Model.PayOrder> curpays = null;
                if (IsChargeData)
                {
                    DateTime curdt = DateTimeHelper.GetNowTime();
                    DateTime yesdt = curdt.AddDays(-1);
                    var yesdtStart = yesdt.ToString("yyyy-MM-dd 00:00:00");
                    var yesdtEnd = yesdt.ToString("yyyy-MM-dd 23:59:59");
                    var curdtStart = curdt.ToString("yyyy-MM-dd 00:00:00");
                    var curdtEnd = curdt.ToString("yyyy-MM-dd 23:59:59");

                    yespays = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_PayedMoney", $"PayOrder_Status=1 AND PayOrder_PayedTime BETWEEN '{yesdtStart}' AND '{yesdtEnd}'");
                    curpays = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_PayedMoney", $"PayOrder_Status=1 AND PayOrder_PayedTime BETWEEN '{curdtStart}' AND '{curdtEnd}'");
                }

                var yesMoney = yespays?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                var curMoney = curpays?.Sum(x => x.PayOrder_PayedMoney) ?? 0;

                var data = new
                {
                    num1 = curMoney,
                    num2 = yesMoney
                };

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取今日收费/昨日收费异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取固定车数量/本月新增固定车数量
        /// </summary>
        /// <returns></returns>
        public IActionResult CarNumber(string code = "")
        {
            try
            {
                bool IsFixedNumberVehicles = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.FixedNumberVehicles.ToString(), lgAdmins);

                List<Model.Car> cars = null;
                List<Model.Car> curadd = null;
                if (IsFixedNumberVehicles)
                {
                    cars = BLL.Car.GetAllEntity("Car_CarNo,Car_AddTime", $"1=1");

                    var curdt = DateTime.Parse(DateTimeHelper.GetNowTime().ToString("yyyy-MM-01"));
                    curadd = cars.FindAll(x => x.Car_AddTime >= curdt);
                }

                var data = new
                {
                    num1 = cars?.Count ?? 0,
                    num2 = curadd?.Count ?? 0
                };

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取固定车数量/本月新增固定车数量异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取在线设备数量/离线设备数量
        /// </summary>
        /// <returns></returns>
        public IActionResult DeviceNumber(string code = "")
        {
            try
            {
                bool IsOnlineDevices = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.OnlineDevices.ToString(), lgAdmins);
                var onlinenum = 0;
                var unlinenum = 0;

                if (IsOnlineDevices)
                {
                    var devices = BLL.Device.GetAllEntity("Device_No,Device_SentryHostNo", $"1=1");
                    try
                    {
                        var deviceStatus = new List<object>();
                        foreach (var dv in AppBasicCache.GetSentryDeviceLinking.Values)
                        {
                            var statu = 0;
                            try
                            {
                                var device = TcpConnPools.DevicePool.Instance.GetDevice(dv.Device_IP);
                                if (device != null)
                                {
                                    if (device.Model.IsConnected)
                                    {
                                        onlinenum += 1;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFileException(ex, $"[{dv?.Device_IP}]获取设备状态异常");
                                statu = 0;
                            }

                            deviceStatus.Add(new
                            {
                                deviceno = dv.Device_No,
                                online = statu
                            });
                        }

                        unlinenum = (devices?.Count() ?? 0) - onlinenum;
                    }
                    catch (Exception ex)
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取设备状态异常：{ex.Message}", SecondIndex.IndexData);
                    }
                }

                var data = new
                {
                    num1 = onlinenum,
                    num2 = unlinenum
                };

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取在线设备数量/离线设备数量异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取场内重点防疫地区车辆数
        /// </summary>
        /// <returns></returns>
        public IActionResult FyInNumber(int day, string code = "")
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }
                DateTime curdt = DateTimeHelper.GetNowTime();
                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" ParkOrder_StatusNo={Model.EnumParkOrderStatus.In} ");
                sqlwhere.Append($" AND ParkOrder_IsEpCar=1 ");
                if (day == 0)
                    sqlwhere.Append($" AND ParkOrder_EnterTime BETWEEN '{curdt.ToString("yyyy-MM-dd 00:00:00")}' AND '{curdt.ToString("yyyy-MM-dd 23:59:59")}' ");
                else if (day == 7)
                    sqlwhere.Append($" AND ParkOrder_EnterTime BETWEEN '{curdt.AddDays(-6).ToString("yyyy-MM-dd 00:00:00")}' AND '{curdt.ToString("yyyy-MM-dd 23:59:59")}' ");
                else if (day == 15)
                    sqlwhere.Append($" AND ParkOrder_EnterTime BETWEEN '{curdt.AddDays(-14).ToString("yyyy-MM-dd 00:00:00")}' AND '{curdt.ToString("yyyy-MM-dd 23:59:59")}' ");

                List<Model.ParkOrder> orders = BLL.ParkOrder.GetAllEntity("ParkOrder_CarNo", sqlwhere.ToString());

                List<string> carnoList = orders?.Select(x => x.ParkOrder_CarNo).ToList() ?? new List<string>();

                var province = BLL.Province.GetAllEntity("*", "1=1");
                var city = BLL.City.GetAllEntity("*", "1=1");

                List<EpModel> mul = new List<EpModel>();
                foreach (var carno in carnoList)
                {
                    if (string.IsNullOrEmpty(carno)) continue;
                    if (carno.Length < 2) continue;

                    string p = carno.Substring(0, 1);
                    string c = carno.Substring(1, 1);
                    string key = $"{p}{c}";

                    var pi = province.Find(x => x.Province_ShortName == p);
                    var ci = city.Find(x => x.City_ShortName == c && x.Province_No == pi?.Province_No);

                    #region 直辖市特殊处理

                    if (BLL.City.CGCITY.Contains(pi.Province_ShortName))
                    {
                        key = p;
                        ci = city.Find(x => x.Province_No == pi?.Province_No);
                    }

                    #endregion

                    var item = mul.Find(x => x.key == key);
                    if (item != null)
                    {
                        item.count += 1;
                    }
                    else
                    {
                        item = new EpModel
                        {
                            key = key,
                            province = pi?.Province_Name,
                            city = ci?.City_Name,
                            count = 1
                        };
                        mul.Add(item);
                    }
                }

                mul = mul?.OrderByDescending(x => x.count).ToList();
                var data = new
                {
                    data = mul,
                    num = orders?.Count ?? 0
                };

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取场内重点防疫地区车辆数量异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取本周/上周收费数据
        /// </summary>
        /// <returns></returns>
        public IActionResult PayReport(string code = "")
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }
                DateTime curdt = DateTimeHelper.GetNowTime();
                int weekday = (int)curdt.DayOfWeek;

                DateTime start = curdt.AddDays((weekday == 0 ? -6 : (1 - weekday))); //本周开始日期
                DateTime end = curdt.AddDays((weekday == 0 ? 0 : (7 - weekday))); //本周结束日期
                DateTime start0 = start.AddDays(-7); //上周开始日期
                DateTime end0 = start.AddDays(-1); //上周结束日期


                bool IsChargeData = Powermanage.PowerOnlyCheck("HeadPage", PowerEnum.ChargeData.ToString(), lgAdmins);
                //本周
                List<string> curWeek = new List<string>();
                List<decimal> curWeekData = new List<decimal>();
                //上周
                List<string> yesWeek = new List<string>();
                List<decimal> yesWeekData = new List<decimal>();

                if (IsChargeData)
                {
                    //读取上周开始到本周结束的支付记录
                    List<Model.PayOrder> pays = BLL.PayOrder.GetAllEntity("PayOrder_PayedTime,PayOrder_PayedMoney",
                        $"PayOrder_Status=1 AND PayOrder_PayedTime BETWEEN '{start0.ToString("yyyy-MM-dd 00:00:00")}' AND '{end.ToString("yyyy-MM-dd 23:59:59")}'");

                    while (start <= end)
                    {
                        string key = $"{start.ToString("yyyy-MM-dd")}";
                        curWeek.Add(Utils.GetWeekName(start));

                        decimal v = pays.FindAll(x => x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == key)?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        curWeekData.Add(v);

                        start = start.AddDays(1);
                    }

                    while (start0 <= end0)
                    {
                        string key = $"{start0.ToString("yyyy-MM-dd")}";
                        yesWeek.Add(Utils.GetWeekName(start0));

                        decimal v = pays.FindAll(x => x.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd") == key)?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        yesWeekData.Add(v);

                        start0 = start0.AddDays(1);
                    }
                }
                else
                {
                    while (start <= end)
                    {
                        curWeek.Add(Utils.GetWeekName(start));
                        curWeekData.Add(0);
                        start = start.AddDays(1);
                    }

                    while (start0 <= end0)
                    {
                        yesWeek.Add(Utils.GetWeekName(start0));
                        yesWeekData.Add(0);
                        start0 = start0.AddDays(1);
                    }
                }

                var data = new
                {
                    curWeek = curWeek,
                    curWeekData = curWeekData,
                    yesWeek = yesWeek,
                    yesWeekData = yesWeekData
                };

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取本周/上周收费数据异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取今日系统警告记录
        /// </summary>
        /// <returns></returns>
        public IActionResult SyswarnRecord(string code = "")
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                DateTime curdt = DateTimeHelper.GetNowTime();
                StringBuilder sqlWhere = new StringBuilder();
                sqlWhere.Append($" Syswarn_Status=0 and Syswarn_Level in(10,100,1000)");
                sqlWhere.Append($" AND Syswarn_Time BETWEEN '{curdt.ToString("yyyy-MM-dd 00:00:00")}' AND '{curdt.ToString("yyyy-MM-dd 23:59:59")}' ");
                sqlWhere.Append($" ORDER BY Syswarn_ID DESC");

                List<Model.Syswarn> models = BLL.BaseBLL._GetAllEntity(new Model.Syswarn(), "Syswarn_Level", sqlWhere.ToString());
                var enventCount = BLL.ControlEvent.GetCount($"ControlEvent_Status=0 and ControlEvent_AddTime>'{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd 00:00:00")}'");

                return ResOk(true, "", new { total = models.Count, eventcount = enventCount, one = models.Where(x => x.Syswarn_Level == 1000).Count(), two = models.Where(x => x.Syswarn_Level == 100).Count(), three = models.Where(x => x.Syswarn_Level == 10).Count() });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取今日系统警告记录异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 设备登录的版本类型
        /// </summary>
        public IActionResult SetVersionType(string versionType)
        {
            try
            {
                if (lgAdmins != null)
                {
                    lgAdmins.Admins_VersionType = versionType == "standard" ? 1 : 0;
                    Model.Admins model = new Model.Admins();
                    model.Admins_ID = lgAdmins.Admins_ID;
                    model.Admins_VersionType = lgAdmins.Admins_VersionType;
                    BLL.Admins._UpdateByModelByID(model);
                }

                return Json(new { Success = true, Message = "" });
            }
            catch (Exception ex)
            {
                return Json(new { Success = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// 获取版本状态
        /// </summary>
        /// <returns></returns>
        public IActionResult GetVersionStatus(string code = "")
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                var versions = BLL.BaseBLL._GetEntityByWhere(new Model.Version(), "*", "Version_DataStatus=1 limit 1");
                if (versions == null)
                {
                    return ResOk(true, "ok");
                }

                return ResOk(false, "ok");
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 更新版本状态
        /// </summary>
        /// <returns></returns>
        public IActionResult UpdateVersionStatus()
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                var versions = BLL.BaseBLL._GetEntityByWhere(new Model.Version(), "*", "1=1 order by Version_ID limit 2");

                if (versions != null)
                {
                    versions.Version_DataStatus = 1;
                    BLL.BaseBLL._UpdateByModelByID(versions);
                }

                return ResOk(true, "ok");
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }


        public IActionResult EditPark()
        {
            if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            ViewBag.SentryMode = AppSettingConfig.SentryMode;

            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
            {
                return Ok(oModel);
            }

            ViewBag.Park = TyziTools.Json.ToString(parking, true);
            return View();
        }

        public IActionResult EditCloudPark()
        {
            if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            ViewBag.SentryMode = AppSettingConfig.SentryMode;
            if (AppSettingConfig.SentryMode != VersionEnum.CloudServer)
            {
                return Ok(oModel);
            }

            ViewBag.Park = TyziTools.Json.ToString(parking, true);
            ViewBag.DeviceSN = AppSettingConfig.DeviceSN;
            return View();
        }

        /// <summary>
        /// 保存车场信息
        /// </summary>
        public async Task<IActionResult> SaveParking(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                Model.Parking model = Utils.ClearModelRiskSQL<Model.Parking>(jsonModel);
                if (string.IsNullOrEmpty(model.Parking_Name)) return ResOk(false, "车场名称不能为空");
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    model.Parking_Key = parking.Parking_Key;
                }
                else
                {
                    if (model.Parking_EnableNet == 1 && string.IsNullOrEmpty(model.Parking_Key)) return ResOk(false, "停车场KEY不能为空");
                }

                model.Parking_No = parking.Parking_No;
                var res = BLL.Parking.UpdateByModel(model);
                if (res > 0)
                {
                    // 在清空缓存之前保存旧的配置信息
                    var oldParking = AppBasicCache.GetParking;

                    if (oldParking?.Parking_Mode != model.Parking_Mode)
                    {
                        CloudTask.InitCloudParkSuccess = false;
                    }

                    AppBasicCache.GetParking = null;
                    Push(Model.API.PushAction.Edit, model, dataType: DataTypeEnum.ParkArea, Desc: $"更新{model.Parking_Name}");
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "修改车场信息", $"{LogHelper.GetEntityCotent(model)}", SecondIndex.IndexData);

                    if (AppSettingConfig.SentryMode != VersionEnum.CloudServer)
                    {
                        if (model.Parking_EnableNet != 1) CloudTask.InitCloudParkSuccess = false;

                        // 检查是否有影响中间件连接的关键字段发生变化
                        bool needRestartMiddleware = false;

                        if (oldParking != null)
                        {
                            // 只有这些关键字段变化时才需要重启中间件
                            needRestartMiddleware =
                                oldParking.Parking_EnableNet != model.Parking_EnableNet ||
                                oldParking.Parking_Mode != model.Parking_Mode ||
                                oldParking.Parking_Platform != model.Parking_Platform ||
                                oldParking.Parking_Key != model.Parking_Key ||
                                oldParking.Parking_ApiUrl != model.Parking_ApiUrl;
                        }
                        else
                        {
                            // 如果是首次配置，且启用了网络，则需要启动中间件
                            needRestartMiddleware = model.Parking_EnableNet == 1;
                        }

                        if (needRestartMiddleware)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "检测到影响中间件连接的配置变更，重启中间件服务");

                            if (model.Parking_EnableNet == 1)
                            {
                                await CloudTask.ManagerMiddle(1, "SaveParking.EnableNet");
                            }
                            else
                            {
                                await CloudTask.ManagerMiddle(0, "SaveParking.DisableNet");
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "车场配置更新完成，无需重启中间件服务");
                        }
                    }

                    if (AppBasicCache.GetParking.Parking_TestMode?.ToString() == "1")
                    {
                        try
                        {
                            TestServerChannel.Close();
                            if (AppBasicCache.GetParking?.Parking_EnableNet == 1)
                            {
                                await TestServerChannel.RunServerAsyn();
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "TCP测试服务启动异常：" + ex.Message, LogLevel.Error);
                        }

                        try
                        {
                            CustomThreadPool.PictureTaskPool?.Dispose();
                            CustomThreadPool.PictureTaskPool = new CustomThreadPool("测试模式", 10);
                            CustomThreadPool.PictureTaskPool.Start();
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"启用测试模式异步处理线程数：{10}", SecondIndex.IndexData);
                        }
                        catch (Exception e)
                        {
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"启用测试模式异常：{e.Message}", SecondIndex.IndexData);
                        }
                    }
                    return ResOk(true, "保存成功", 1);
                }
                else
                {
                    return ResOk(false, "保存失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"修改车场信息发生异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取云车场信息
        /// </summary>
        /// <returns></returns>
        public IActionResult GetCloudInfor(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                Model.Parking model = Utils.ClearModelRiskSQL<Model.Parking>(jsonModel);
                if (model.Parking_EnableNet == 1 && string.IsNullOrEmpty(model.Parking_Key)) return ResOk(false, "停车场KEY不能为空");

                if (model.Parking_EnableNet == 1 && model.Parking_Platform == 2)
                {
                    model.AppVersion = AppBasicCache.GetParking.AppVersion;

                    // 直接调用GetParkingInfo，它内部会处理冷却逻辑
                    // 如果上次注册成功且在冷却期内，会直接返回成功
                    // 如果上次注册失败且在冷却期内，会返回失败并提示等待
                    ResultBase rb = BLL.CloudApi.GetParkingInfo(model);
                    if (!rb.Success)
                    {
                        // 检查是否是冷却相关的错误
                        bool isCooldownError = rb.Message.Contains("车场注册操作过于频繁") || rb.Message.Contains("请等待");

                        return Json(new
                        {
                            success = false,
                            msg = rb.Message,
                            data = new { isCooldownError = isCooldownError }
                        });
                    }

                    CloudTask.InitCloudParkSuccess = true;
                    CloudTask.InitCloudParkTime = DateTime.Now;

                    return ResOk(true, "获取云车场信息成功！", rb.Data);
                }

                return ResOk(false, "获取云车场信息失败！");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取云车场信息发生异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        #region 云平台功能设置

        /// <summary>
        /// 获取车场注册冷却剩余时间
        /// </summary>
        /// <returns></returns>
        public IActionResult GetRegistrationCooldownInfo()
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                var parking = AppBasicCache.GetParking;
                if (parking == null || string.IsNullOrWhiteSpace(parking.Parking_Key))
                {
                    return ResOk(false, "停车场信息不完整");
                }

                var remainingMinutes = BLL.CloudApi.GetRegistrationCooldownRemainingMinutes(parking.Parking_Key);

                return ResOk(true, "获取成功", new
                {
                    RemainingMinutes = remainingMinutes,
                    IsInCooldown = remainingMinutes > 0,
                    Message = remainingMinutes > 0 ? $"车场注册冷却中，还需等待 {remainingMinutes} 分钟" : "可以进行车场注册"
                });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取注册冷却信息发生异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取车场连接状态
        /// </summary>
        /// <returns></returns>
        public IActionResult GetParkingState()
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                //var res = BLL.ParkApi.GetParkingState(parking);
                var dogStatus = 0;
                var sdog = "";
                if (DogModel.DogExist)
                {
                    dogStatus = 1;
                    if (DogModel.DogRealExist)
                    {
                        string version = "当前软件版本：" + AppSettingConfig.ApiVersion + DogCommon.strRevision + (DogCommon.bAuthorization == true ? "_专用" : "") + (string.IsNullOrEmpty(AppSettingConfig.ApiVersion_FB) ? "" : "." + AppSettingConfig.ApiVersion_FB);
                        sdog = version + " 加密狗信息：" + DogCommon.strDogType + "-" + DogCommon.strSoftType + "-" + DogCommon.strDogVideoType + "-" + DogCommon.strDogNum + "-" + DogCommon.strDogPrintNum + "-" + DogCommon.strTmpInTime;
                    }
                    else
                    {
                        dogStatus = 2;
                        sdog = "试用期剩余" + DogModel.RemainingDay + "天";
                    }
                }

                if (AppSettingConfig.SentryMode != VersionEnum.CloudServer)
                {
                    if (AppBasicCache.GetParking.Parking_EnableNet != 1)
                    {
                        return ResOk(true, "", new { parkstatus = 2, dogstatus = dogStatus, dogMsg = sdog });
                    }

                    if (Library.RSocket.OnlineMid)
                        return ResOk(true, "", new { parkstatus = 1, dogstatus = dogStatus, dogMsg = sdog });
                    else
                        return ResOk(true, "", new { parkstatus = 0, dogstatus = dogStatus, dogMsg = sdog });
                }
                else
                {
                    var isEmergency = "0";
                    var ConnMode = "0";
                    var status = MqttClient.Instance == null ? 0 : MqttClient.Instance.IsOnline ? 1 : 0;
                    isEmergency = (MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0).ToString();
                    ConnMode = (MqttClient.Instance == null ? 0 : CameraGlobal.ConnMode ?? 0).ToString();
                    return ResOk(true, "", new { parkstatus = status, isEmergency = isEmergency, ConnMode = ConnMode });
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"获取车场连接状态异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, ex.Message, new { parkstatus = 0, dogstatus = 0, dogMsg = "" });
            }
        }

        /// <summary>
        /// 重启中间件
        /// </summary>
        /// <returns></returns>
        public IActionResult ReStartCloud()
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                if (AppBasicCache.GetParking?.Parking_EnableNet != 1)
                {
                    return ResOk(false, "未启用云平台！请先启用云平台");
                }

                if (CloudTask.ReStartMiddle == null) CloudTask.ReStartMiddle = DateTime.Now;
                else
                {
                    if (Math.Abs(DateTime.Now.Subtract(CloudTask.ReStartMiddle.Value).TotalMinutes) < 2)
                    {
                        return ResOk(false, "请稍候，频繁重连云平台，2分钟后再试");
                    }
                }

                // 传入调用源信息，标识这是从EditPark.cshtml重新连接按钮触发的
                _ = CloudTask.ManagerMiddle(1, "EditPark.ReStartCloud");

                return ResOk(true, "重启中间件服务命令已下发，请稍等...");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"重启中间件异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, "重启中间件服务失败：" + ex.Message);
            }
        }


        /// <summary>
        /// 切换车场模式
        /// </summary>
        /// <returns></returns>
        public IActionResult SwitchParkingMode()
        {
            try
            {
                if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                int mode = AppBasicCache.CurrentSysConfigContent.SysConfig_CloudBoxLprEmergency ? 1 : 0;
                if (mode == 1) mode = 0;
                else mode = 1;

                var sysconfigModel = AppBasicCache.CurrentSysConfig.Copy();

                if (AppSettingConfig.SentryMode == "2" && AppBasicCache.CurrentSysConfigContent.SysConfig_ConnMode == 1)
                {
                    var sysconfig = AppBasicCache.CurrentSysConfig.Copy();
                    var sysconfigContent = AppBasicCache.CurrentSysConfigContent.Copy();
                    sysconfigContent.SysConfig_CloudBoxLprEmergency = mode == 1;
                    sysconfig.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(sysconfigContent));
                    var res = BaseBLL._Insert(sysconfig);
                    if (res >= 0)
                    {
                        CameraGlobal.IsEmergency = AppBasicCache.CurrentSysConfigContent.SysConfig_CloudBoxLprEmergency = mode == 1;
                        MqttCache.SysConfigContent = AppBasicCache.CurrentSysConfigContent;

                        var devices = new List<Model.DeviceExt>();

                        foreach (var item in AppBasicCache.GetSentryPasswayDic)
                        {
                            devices.AddRange(AppBasicCache.GetAllDeivces.Where(x => x.Value.Device_PasswayNo == item.Value.Passway_No && x.Value.Device_Category == 1
                                                                                                                                    && (CameraDeviceType.driveNameList06.Contains(x.Value.Drive_Name) || CameraDeviceType.driveNameList15.Contains(x.Value.Drive_Name))).Select(x => x.Value).ToList());
                        }

                        return ResOk(true, "切换成功", devices);
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"切换车场模式执行SQL失败！！{TyziTools.Json.ToString(sysconfigModel)}", SecondIndex.IndexData);
                        return ResOk(false, "切换失败");
                    }
                }

                var data = new SwitchMode { mode = mode, account = lgAdmins?.Admins_Account, name = lgAdmins?.Admins_Name };
                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "开始切换车场模式:" + TyziTools.Json.ToString(data));
                var rst = CloudAPIRequest.SwitchMode(AppSettingConfig.DeviceSN, data);
                if (rst != null && rst.Code == 1)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "切换车场模式结果：" + TyziTools.Json.ToString(rst));
                    AppBasicCache.CurrentSysConfigContent.SysConfig_CloudBoxLprEmergency = CameraGlobal.IsEmergency = MqttCache.SysConfigContent.SysConfig_CloudBoxLprEmergency = mode == 1;

                    CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        sysconfigModel.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(AppBasicCache.CurrentSysConfigContent));
                        var res = BaseBLL._Insert(sysconfigModel);
                        if (res < 0)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "切换车场模式执行SQL失败！！" + TyziTools.Json.ToString(sysconfigModel));
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "切换成功", 1);
                }
                else
                {
                    return ResOk(false, "切换失败 " + rst?.Message);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"切换车场模式异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, "切换车场模式失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 发送设备云平台功能开关
        /// </summary>
        /// <returns></returns>
        public IActionResult SendDeviceEnableClouds(string deviceNos, int parkingMode)
        {
            try
            {
                if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                if (string.IsNullOrEmpty(deviceNos)) return ResOk(false, "参数不能为空");

                _ = CustomThreadPool.WebTaskPool.QueueTask(null, async () =>
                {
                    DataCache.CameraSendCache.Clear();
                    var devices = AppBasicCache.GetAllDeivces.Values.Where(x => deviceNos.Contains(x.Device_No)).ToList();
                    if (devices is { Count: > 0 })
                    {
                        devices.ForEach(async device =>
                        {
                            if (CameraDeviceType.driveNameList06.Contains(device.Drive_Name))
                            {
                                var ret = CameraController.GetCamera(device.Device_IP) is CameraOf06 camera && await camera.SendCloudEnableAsync(parkingMode);
                                if (!ret)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"发送设备云平台功能开关失败！！deviceNo:{device.Device_No},parkingMode:{parkingMode}");
                                }

                                DataCache.CameraSendCache.Set(device.Device_No, ret ? 1 : 2);
                                //return ResOk(ret, "已发送");
                            }
                            else if (CameraDeviceType.driveNameList15.Contains(device.Drive_Name) && CameraController.GetCamera(device.Device_IP) is CameraOf15 cameraOf15)
                            {
                                var ret = await cameraOf15.SendCloudEnableAsync(parkingMode);
                                if (ret) //15系列相机设置成功后要重启才生效
                                {
                                    await cameraOf15.SendRebootAsync();
                                }

                                if (!ret)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"发送设备云平台功能开关失败！！deviceNo:{device.Device_No},parkingMode:{parkingMode}");
                                }

                                DataCache.CameraSendCache.Set(device.Device_No, ret ? 1 : 2);
                                //return ResOk(true, "已发送");
                            }
                            else
                            {
                                DataCache.CameraSendCache.Set(device.Device_No, 2);
                            }

                            await Task.Delay(100);
                        });
                    }
                });

                return ResOk(true, "发送成功");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"发送设备云平台功能开关异常！！deviceNo:{deviceNos},parkingMode:{parkingMode}，{ex.ToString()}", SecondIndex.IndexData);
                return ResOk(false, "发送设备云平台功能开关失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 查询指令执行状态
        /// </summary>
        /// <returns></returns>
        public IActionResult QueryDeviceStatus(string deviceNo)
        {
            try
            {
                if (!Powermanage.PowerCheck("HeadPage", PowerEnum.UpdatePark.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                var ret = DataCache.CameraSendCache.Get(deviceNo);
                if (ret == 1 || ret == 2) DataCache.CameraSendCache.Del(deviceNo);
                return ResOk((ret == 1 || ret == 2), "已发送", ret);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"查询设备云平台功能开关状态异常：{ex.Message}", SecondIndex.IndexData);
                return ResOk(false, "查询设备云平台功能开关状态失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 运行脚本
        /// </summary>
        /// <param name="AppFullPath">脚本路径</param>
        /// <returns></returns>
        private bool RunBat(string AppFullPath)
        {
            try
            {
                System.Diagnostics.Process p;

                p = new System.Diagnostics.Process();
                p.StartInfo.FileName = AppFullPath;
                p.StartInfo.WorkingDirectory = System.IO.Path.GetDirectoryName(AppFullPath);
                p.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                p.Start();
                p.WaitForExit();
                p.Close();

                return true;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"数据库安装失败(RunBat)：{ex.Message}\r\n{ex.StackTrace}", SecondIndex.IndexData);
                return false;
            }
        }

        private void GetSqlConnectByConfig(out BackData data)
        {
            try
            {
                data = new BackData();
                string constring = Config.AppSettingConfig.WriteDBConnectionString;
                List<string> objs = constring.Split(';').ToList();
                foreach (var o in objs)
                {
                    List<string> ss = o.Split('=').ToList();
                    if (ss.Count > 1)
                    {
                        if (ss[0].Trim().ToLower() == "database") data.DbName = ss[1].Trim('\'');
                        if (ss[0].Trim().ToLower() == "data source") data.IP = ss[1].Trim('\'');
                        if (ss[0].Trim().ToLower() == "user id") data.User = ss[1].Trim('\'');
                        if (ss[0].Trim().ToLower() == "port") data.Port = ss[1].Trim('\'');
                        if (ss[0].Trim().ToLower() == "password") data.Pwd = ss[1].Trim('\'');
                    }
                }
            }
            catch (Exception ex)
            {
                data = null;
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.GetIndexData, $"读取数据库连接异常：{ex.Message}", SecondIndex.IndexData);
            }
        }

        /// <summary>
        /// 设置系统测试模式
        /// </summary>
        /// <returns></returns>
        public IActionResult SetTestMode(int testMode)
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                AppBasicCache.GetParking.Parking_TestMode = testMode;
                var ret = BLL.BaseBLL._UpdateByModelByNo(AppBasicCache.GetParking);
                if (ret < 0)
                {
                    return ResOk(false, "设置系统测试模式失败");
                }

                Task.Run(() =>
                {
                    //重启服务
                    BLL.LinuxApi.SendServiceReboot(ServicesEnum.Web);
                });

                return ResOk(true, "设置成功");
            }
            catch (Exception ex)
            {
                return ResOk(false, "设置系统测试模式失败：" + ex.Message);
            }
        }

        #endregion
    }

    public class EpModel
    {
        public string key;
        public string province;
        public string city;
        public int count;
    }
}