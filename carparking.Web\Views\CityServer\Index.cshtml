﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>城市平台上报</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        ::-webkit-input-placeholder {
            color: #ccc
        }

        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        ::-webkit-scrollbar-track {
            background-color: #f9f9f9;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 10px;
            border-style: dashed;
            border-color: transparent;
            border-width: 2px;
            background-color: rgba(125, 125, 125, .3);
            background-clip: padding-box;
        }

        ::-webkit-scrollbar-thumb:hover {
            background-clip: border-box;
            background-color: rgba(125, 125, 125, .6);
        }

        .fa {
            margin: 6px 4px;
            float: left;
            font-size: 16px;
        }

        .layui-form-select .layui-input {
            width: 182px;
        }

        .layui-side-child {
            position: initial !important;
            width: 100%;
        }

        .layui-nav .layui-nav-item a {
            color: #000 !important;
        }

        .layui-nav-tree .layui-this {
            background-color: #f3f3f3 !important;
        }

        .layui-nav-tree .layui-this .layui-nav-title {
            color: #f3f3f3 !important;
        }

        .layui-nav-tree .layui-nav-bar {
            background-color: #c3c3c355 !important;
        }

        .layui-side .layui-nav-tree .layui-nav-bar {
            width: 100% !important;
        }

        .city-card {
            box-shadow: 0 4px 9px 0 rgb(0 0 0 / 5%);
            transition: all .6s ease;
            border-radius: 8px;
            cursor: pointer;
            margin-left: 5%;
        }

        .city-card:hover {
            transform: translate(0px, 5px);
            transition-duration: .6s;
        }

        .city-name {
            position: absolute;
            bottom: 2.95rem;
            text-align: center;
            color: #fff;
            font-size: 14px;
            opacity: .8;
            background-color: #393d49;
            word-spacing: normal;
            word-break: keep-all;
            margin: 1px auto !important;
            padding-left: 5px;
            padding-right: 5px;
        }

        .layui-btn i {
            vertical-align: middle !important;
        }
    </style>
</head>

<body>
    <div class="layui-fluid animated">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>城市服务</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12" style="padding-left:0px;">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <select data-placeholder="省市" class="form-control layui-select" id="Province_No"
                                    name="Province_No" lay-search>
                                    <option value="">所有省市</option>
                                    <option value="1">北京市</option>
                                    <option value="2">天津市</option>
                                    <option value="3">河北省</option>
                                    <option value="4">山西省</option>
                                    <option value="5">内蒙古自治区</option>
                                    <option value="6">辽宁省</option>
                                    <option value="7">吉林省</option>
                                    <option value="8">黑龙江省</option>
                                    <option value="9">上海市</option>
                                    <option value="10">江苏省</option>
                                    <option value="11">浙江省</option>
                                    <option value="12">安徽省</option>
                                    <option value="13">福建省</option>
                                    <option value="14">江西省</option>
                                    <option value="15">山东省</option>
                                    <option value="16">河南省</option>
                                    <option value="17">湖北省</option>
                                    <option value="18">湖南省</option>
                                    <option value="19">广东省</option>
                                    <option value="20">广西壮族自治区</option>
                                    <option value="21">海南省</option>
                                    <option value="22">重庆市</option>
                                    <option value="23">四川省</option>
                                    <option value="24">贵州省</option>
                                    <option value="25">云南省</option>
                                    <option value="26">西藏自治区</option>
                                    <option value="27">陕西省</option>
                                    <option value="28">甘肃省</option>
                                    <option value="29">青海省</option>
                                    <option value="30">宁夏回族自治区</option>
                                    <option value="31">新疆维吾尔自治区</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="CityServer_Name" id="CityServer_Name"
                                    autocomplete="off" placeholder="上报平台关键字" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="上报状态" class="form-control layui-select"
                                    id="CityParking_Status" name="CityParking_Status">
                                    <option value="">上报状态</option>
                                    <option value="1">已上报</option>
                                    <option value="0">已关闭</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i>
                                    <t>搜索</t>
                                </button>
                                @*<button class="layui-btn" id="Open"><i class="layui-icon layui-icon-upload-circle
                                inbtn"></i><t><t>上报城市平台</t></button>*@
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <div class="layui-none layui-hide" style="text-align:center;line-height:60px;">无数据</div>

                        <div class="monitor layui-row" style="position: relative;">
                            <!--城市服务-->
                        </div>

                        <div id="page_over"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--城市服务-->
    <script type="text/x-jquery-tmpl" id="tmplserver">
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md4 layui-col-lg3" id="${CityServer_ID}" title="${CityServer_Name}" style="margin-bottom:3vh; ">
            <div class="city-card">
                <div class="item-main" style="width: 100%;">
                    <div class="layui-row videobox" id="${CityServer_ID}" style="height:10rem;width:100%;" onclick="javascript:location.href='../CityServer/Record?CityServer_No=${CityServer_No}'" >
                        {{if CityServer_CoverImg!=null && CityServer_CoverImg!=""}}
                        <img src="../Static/img/cityserver/${CityServer_CoverImg}" style="width:100%;height:100%;" />
                        {{else}}
                        <img src="../Static/img/cityserver/default.jpg" style="width:100%;height:100%;" />
                        {{/if}}
                        {{if CityParking_Status==1}}
                        <img src="../Static/img/cityserver/isupload.png" style="position:absolute;right:0;height:70px;border-radius:3px;" />
                        {{else}}
                        {{/if}}
                        <div class="city-name">${CityServer_Name}</div>
                    </div>
                </div>
                <div class="item-btns" style="width: 100%; overflow: hidden;">
                    <div style="width:100%;height:3rem;line-height:3rem;text-align:left; ">                    
                        <a href="../CityServer/Record?CityServer_No=${CityServer_No}" style="float:left;padding-left: 5%;color:#1ab394;">进入平台</a>
                        <a href="${CityServer_DocUrl}" target="_blank" style= "float:right;padding-right: 5%;color:#1ab394;">查看对接文档</a>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var pageIndex = 1, pageSize = 12, pageCount = 0, isFirst = true, condParam = null;
        var laypage;

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            laypage = layui.laypage;
            condParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            $.ajaxSettings.async = false;
            pager.bindData(pageIndex, pageSize, condParam);
            laypage.render({
                elem: 'page_over'
                , count: pageCount
                , limit: pageSize
                , limits: [12, 36, 72]
                , layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
                , jump: function (obj) {
                    console.log(obj);
                    pageIndex = obj.curr;
                    pageSize = obj.limit;
                    if (!isFirst) {
                        pager.bindData(pageIndex, pageSize, condParam);
                    }
                    isFirst = false;
                }
            });
            $.ajaxSettings.async = true;
        });

        //绑定查询事件
        $(function () {
            $("#Search").click(function () {
                isFirst = true;
                condParam = $("#searchForm").formToJSON(true, function (data) { return data; });

                $.ajaxSettings.async = false;
                pager.bindData(1, pageSize, condParam);
                laypage.render({
                    elem: 'page_over'
                    , count: pageCount
                    , limit: pageSize
                    , limits: [12, 36, 72]
                    , layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
                    , jump: function (obj) {
                        console.log(obj);
                        pageIndex = obj.curr;
                        pageSize = obj.limit;
                        if (!isFirst) {
                            pager.bindData(pageIndex, pageSize, condParam);
                        }
                        isFirst = false;
                    }
                });
                $.ajaxSettings.async = true;
            });
            $("#Open").click(function () {
                layer.open({
                    type: 2,
                    title: "上报城市平台",
                    content: '/CityServer/Parking',
                    area: getIframeArea(['80%', '80%']),
                    maxmin: true
                });
            });
        });

    </script>
    <script>
        var pager = {
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index, size, param) {
                $.post("GetCityServerList", { pageIndex: index, pageSize: size, conditionParam: JSON.stringify(param) }, function (json) {
                    if (json.code == 0) {
                        $(".monitor").html($("#tmplserver").tmpl(json.data));
                        pageCount = json.count;

                        if (json.data.length > 0) { $(".layui-none").addClass("layui-hide"); $("#page_over").removeClass("layui-hide"); }
                        else { $(".layui-none").removeClass("layui-hide"); $("#page_over").addClass("layui-hide"); }
                    } else {
                        layer.msg("读取失败", { icon: 0, time: 2000 });
                    }
                }, "json");
            }
        }
    </script>
    <script>
        $(function () {
            pager.bindSelect();
        });
    </script>


</body>

</html>
