<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车牌特殊处理 - 新增/编辑</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-form-item {
            margin-bottom: 15px;
        }

        .layui-form-label {
            width: 100px;
            text-align: right;
            padding: 9px 15px;
        }

        .layui-input-block {
            margin-left: 130px;
        }

        .layui-form-radio {
            margin: 6px 10px 0 0;
        }

        .type-hint {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
            font-size: 14px;
            background-color: #f8f8f8;
            border: 1px solid #e6e6e6;
            color: #666;
        }

        .layui-card {
            border: none;
            box-shadow: none;
        }

        .layui-card-body {
            padding: 20px;
            padding-right: 60px;
            border-bottom: none;
        }

        /* 添加以下新样式 */
        select:disabled {
            background-color: #f2f2f2;
            color: #666;
            /* 更深的颜色以提高可读性 */
            cursor: not-allowed;
            font-weight: bold;
            /* 加粗字体 */
        }

        .layui-disabled,
        .layui-disabled:hover {
            color: #666 !important;
            cursor: not-allowed !important;
        }

        .disabled-select-wrapper {
            position: relative;
        }

        .disabled-select-wrapper::after {
            content: "不可修改";
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #666;
            pointer-events: none;
            font-weight: bold;
        }

        /* 添加新样式 */
        .disabled-select-wrapper select {
            text-shadow: 0 0 0 #666;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" id="specialPlateForm" lay-filter="specialPlateForm">
                    <input type="hidden" name="SpecialPlate_No" />

                    <div class="layui-form-item">
                        <label class="layui-form-label">处理方式</label>
                        <div class="layui-input-block">
                            <select class="layui-input v-null" name="SpecialPlate_Type" id="SpecialPlateType"
                                lay-filter="SpecialPlateType" lay-search>
                                <option value="1">按指定车牌处理</option>
                                <option value="2">车牌+车牌颜色匹配</option>
                                <option value="3">全字母车牌不处理</option>
                                <option value="4">车牌完全一样不处理</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item" id="plateRecognitionItem" style="display: none;">
                        <label class="layui-form-label">识别结果包含</label>
                        <div class="layui-input-block">
                            <input type="text" name="SpecialPlate_Recognition" id="SpecialPlate_Recognition"
                                lay-verify="plateRecognition" placeholder="请输入识别结果包含的内容(3-6位)" autocomplete="off"
                                class="layui-input" maxlength="6">
                        </div>
                    </div>

                    <div class="layui-form-item" id="plateColorItem" style="display: none;">
                        <label class="layui-form-label">车牌颜色</label>
                        <div class="layui-input-block">
                            <select name="SpecialPlate_PlateColor" id="SpecialPlate_PlateColor" lay-search>
                                <option value="1">蓝色车牌</option>
                                <option value="2">黄色车牌</option>
                                <option value="3">白色车牌</option>
                                <option value="4">黑色车牌</option>
                                <option value="5">绿色车牌</option>
                                <option value="6">黄绿色车牌</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item" id="plateItem">
                        <label class="layui-form-label" id="plateLabel">车牌号码</label>
                        <div class="layui-input-block">
                            <input type="text" name="SpecialPlate_CarNo" id="SpecialPlate_CarNo" placeholder="请输入车牌号码"
                                autocomplete="off" class="layui-input" maxlength="9">
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="SpecialPlate_Remark" placeholder="请输入备注" class="layui-textarea"
                                style="resize: none;"></textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <div class="type-hint" id="hint-1">车牌识别结果中包含XXX（3-6位内容），按指定车牌XXX处理</div>
                            <div class="type-hint" id="hint-2">车牌+车牌颜色完全匹配，如粤A12345 蓝色车牌，设置后识别入场标记车牌为粤A12345蓝，固定车也需登记为粤A12345蓝。</div>
                            <div class="type-hint" id="hint-3">全字母车牌不处理，例如粤AAABBB</div>
                            <div class="type-hint" id="hint-4">车牌号完全一样不处理，例如粤AAAAAA和粤111111</div>
                        </div>
                    </div>

                    <div class="edit-ipt-ban">
                        <div class="layui-input-block">
                            <button class="btn btn-primary" type="button" id="saveButton"><i class="fa fa-check"></i>
                                <t>保存</t>
                            </button>
                            <button type="button" class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i>
                                <t>取消</t>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?t=@DateTime.Now.Ticks"
        asp-append-version="true"></script>
    <script>
        // 限制输入长度和转换为大写
        $("#SpecialPlate_CarNo, #SpecialPlate_Recognition").on('input', function () {
            var value = $(this).val().toUpperCase();
            var maxLength = $(this).attr('maxlength');
            if (value.length > maxLength) {
                value = value.substr(0, maxLength);
            }
            $(this).val(value);
        });

        // 车牌号码选择器
        s_carno_picker.init("SpecialPlate_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "SpecialPlate_CarNo") {
                $("#SpecialPlate_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        // 表单提交
        layui.use(['form', 'layer'], function () {
            var form = layui.form,
                layer = layui.layer,
                $ = layui.jquery;

            // 获取URL参数
            var getUrlParam = function (name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]); return null;
            }

            var act = getUrlParam('Act');
            var specialPlateNo = getUrlParam('SpecialPlate_No');

            // 处理方式选择
            form.on('select(SpecialPlateType)', function (data) {
                showTypeHint(data.value);
            });

            // 获取详情
            if (act === 'Update' && specialPlateNo) {
                $.getJSON("/SpecialPlate/GetDetail", { SpecialPlate_No: specialPlateNo }, function (res) {
                    if (res.success) {
                        form.val('specialPlateForm', res.data);
                        if (res.data.SpecialPlate_Type) {
                            showTypeHint(res.data.SpecialPlate_Type.toString());
                            $('#SpecialPlateType').prop('disabled', true);
                            $('#SpecialPlateType').parent().addClass('disabled-select-wrapper');
                            form.render('select');
                        }
                    } else {
                        layer.open({
                            title: '错误',
                            content: res.msg,
                            icon: 2,
                            btn: ['确定']
                        });
                    }
                });
            }
            else {
                showTypeHint($('#SpecialPlateType').val());
            }
            // 显示处理方式提示
            function showTypeHint(value) {
                $('.type-hint').hide();
                if (value) {
                    $('#hint-' + value).show();
                }

                $('#plateRecognitionItem').hide();
                $('#plateColorItem').hide();
                $('#plateItem').hide();

                if (value === '1') {
                    $('#plateRecognitionItem').show().insertBefore('#plateItem');
                    $('#plateItem').show();
                    $('#plateLabel').text('按指定车牌处理');
                    $('#SpecialPlate_CarNo').attr('placeholder', '请输入指定处理的车牌');
                } else if (value === '2') {
                    $('#plateColorItem').show();
                    $('#plateItem').show();
                    $('#plateLabel').text('车牌号码');
                    $('#SpecialPlate_CarNo').attr('placeholder', '请输入车牌号码');
                }
                layui.form.render();
            }

            // 保存按钮点击事件
            $('#saveButton').on('click', function () {
                var formData = form.val('specialPlateForm');

                // 如果是编辑模式，确保使用原始的处理方式
                if (act === 'Update') {
                    formData.SpecialPlate_Type = $('#SpecialPlateType').val();
                }

                // 数据验证和处理
                formData.SpecialPlate_CarNo = formData.SpecialPlate_CarNo.toUpperCase();
                formData.SpecialPlate_Recognition = formData.SpecialPlate_Recognition.toUpperCase();

                switch (formData.SpecialPlate_Type) {
                    case '1':
                        var recognition = formData.SpecialPlate_Recognition;
                        if (recognition.trim() === '') {
                            layer.msg('请输入识别结果包含的内容', { icon: 2 });
                            return;
                        }
                        if (recognition.length < 3 || recognition.length > 6) {
                            layer.msg('识别结果包含的内容必须是3-6位', { icon: 2 });
                            return;
                        }
                        if (formData.SpecialPlate_CarNo.trim() === '') {
                            layer.msg('请输入指定处理的车牌', { icon: 2 });
                            return;
                        }
                        break;
                    case '2':
                        if (formData.SpecialPlate_CarNo.trim() === '') {
                            layer.msg('请输入车牌号码', { icon: 2 });
                            return;
                        }
                        if (formData.SpecialPlate_PlateColor === '') {
                            layer.msg('请选择车牌颜色', { icon: 2 });
                            return;
                        }
                        break;
                    case '3':
                    case '4':
                        break;
                    default:
                        layer.msg('请选择处理方式', { icon: 2 });
                        return;
                }

                var jsonModel = JSON.stringify(formData);

                var url = '@Url.Action("Add", "SpecialPlate")';
                if (formData.SpecialPlate_No) {
                    url = '@Url.Action("Update", "SpecialPlate")';
                }
                var loadIndex = layer.load(2);
                $.post(url, { jsonModel: jsonModel }, function (res) {
                    debugger
                    layer.close(loadIndex);
                    if (res.success) {
                        layer.msg(res.msg, { icon: 1, time: 1000 }, function () {
                            parent.layui.table.reload('com-table-base');
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.open({
                            title: '错误提示',
                            content: res.msg,
                            icon: 2,
                            btn: ['确定']
                        });
                    }
                }, 'json').fail(function () {
                    layer.close(loadIndex);
                    layer.open({
                        title: '错误提示',
                        content: '操作失败，请重试',
                        icon: 2,
                        btn: ['确定']
                    });
                });
            });

            $('#Cancel').on('click', function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
        });

        $(document).ready(function () {
            var act = getUrlParam('Act');
            if (act === 'Update') {
                $('#SpecialPlateType').prop('disabled', true);
                $('#SpecialPlateType').parent().addClass('disabled-select-wrapper');
                $('#SpecialPlateType').css('opacity', '1'); // 确保禁用状态下不会变得半透明
                layui.form.render('select');
            }
        });
    </script>
</body>

</html>
