﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车辆登记</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v1.0" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }
        .help-btn { z-index: 2; }
        .help-btn { position: absolute; width: 20px; height: 20px; right: 5px; top: 2px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车辆管理</cite></a>
                <a><cite>车辆登记</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">

                            <div class="layui-row">
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_CarNo" id="Car_CarNo" autocomplete="off" placeholder="车牌号" />
                                </div>
                                <div class="layui-inline">
                                    <select data-placeholder="车牌类型" class="form-control chosen-select " id="Car_TypeNo" name="Car_TypeNo" lay-search>
                                        <option value="">车牌类型</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <select data-placeholder="车牌颜色" class="form-control chosen-select " id="Car_VehicleTypeNo" name="Car_VehicleTypeNo" lay-search>
                                        <option value="">车牌颜色</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_Space" id="Owner_Space" autocomplete="off" placeholder="系统车位号" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_ParkSpace" id="Owner_ParkSpace" autocomplete="off" placeholder="车场车位号" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_OwnerName" id="Car_OwnerName" autocomplete="off" placeholder="车主姓名" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_PhoneLastFour" id="Owner_PhoneLastFour" autocomplete="off" placeholder="手机号后四位" maxlength="4" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input v-number" maxlength="4" name="Car_EndDay" id="Car_EndDay" autocomplete="off" placeholder="剩余天数" />
                                    <i class="help-btn" data-key="Car_EndDay">?</i>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_BeginTime0" id="Car_BeginTime0" autocomplete="off" placeholder="开始时间起" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_BeginTime1" id="Car_BeginTime1" autocomplete="off" placeholder="开始时间止" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_EndTime0" id="Car_EndTime0" autocomplete="off" placeholder="结束时间起" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_EndTime1" id="Car_EndTime1" autocomplete="off" placeholder="结束时间止" />
                                </div>
                                <div class="layui-inline">
                                    <select data-placeholder="一位多车" class="form-control chosen-select " id="Car_IsMoreCar" name="Car_IsMoreCar" lay-search>
                                        <option value="">一位多车</option>
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_AddTime0" id="Car_AddTime0" autocomplete="off" placeholder="创建时间起" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_AddTime1" id="Car_AddTime1" autocomplete="off" placeholder="创建时间止" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_CardNo" id="Car_CardNo" autocomplete="off" placeholder="卡号" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_Remark" id="Car_Remark" autocomplete="off" placeholder="备注" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_Address" id="Owner_Address" autocomplete="off" placeholder="车主地址" />
                                </div>
                                <div class="layui-inline form-group">
                                    <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                @*   <button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>*@
                                <button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>注销</t></button>
                                @*  <button class="layui-btn layui-btn-sm" lay-event="Retweet"><i class="fa fa-rmb"></i><t>续费延期</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Vaild"><i class="fa fa-calendar"></i><t>修改有效期</t></button>*@
                                <button class="layui-btn layui-btn-sm" lay-event="BathVaild"><i class="fa fa-crosshairs"></i><t>批量修改有效期</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>白名单启用</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Disable"><i class="fa fa-ban"></i><t>白名单禁用</t></button>
                                @*  <button class="layui-btn layui-btn-sm" lay-event="Import"><i class="fa fa-upload"></i><t>导入</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>*@
                                @*<button class="layui-btn layui-btn-sm" lay-event="Charge"><i class="fa fa-money"></i><t>储值车退费</t></button>*@
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TmplOnLine">
        {{# if(d.Car_OnLine==1){ }}
        <span class="layui-badge layui-bg-blue ">启用</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">禁用</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplStatus">
        {{# if(d.Car_Status==1){ }}
        <span class="layui-badge layui-bg-blue ">正常</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">停用</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplCategory">
        {{# if(d.CarCardType_Name!=null&&d.CarCardType_Name!=""){ }}
        <span class="layui-badge layui-bg-cyan ">{{d.CarCardType_Name}}</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplCarType">
        {{# if(d.CarType_Name!=null&&d.CarType_Name!=""){ }}
        {{d.CarType_Name}}
        {{# } }}
    </script>
    <script type="text/html" id="TmplBalance">
        {{# if(d.CarCardType_Category==3657){ }}
        {{d.Car_Balance}}
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstime">
        {{# if(getStatus(d.Car_BeginTime,d.Car_EndTime)==1){}}
        <span class="layui-badge layui-bg-orange">{{d.Car_BeginTime}}</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">{{d.Car_BeginTime}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmpltime">
        {{# if( getStatus(d.Car_BeginTime,d.Car_EndTime)==3){}}
        <span class="layui-badge layui-bg-red">{{d.Car_EndTime}}</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">{{d.Car_EndTime}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplday">
        {{_DATE.getZhDays(new Date(new Date(new Date().setDate(new Date().getDate()-1)).Format("yyyy-MM-dd 23:59:59")), new Date(d.Car_EndTime))}}
    </script>
    <script type="text/html" id="morecar">
        {{# if(d.Car_IsMoreCar==1){ }}
        <span class="layui-badge layui-bg-blue">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">否</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>

    <script>
        myVerify.init();

        var arrtemp = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var arrmonth = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664', '3656'];//月租车类型
        var arrfree = ['3656'];//免费车类型
        var arrprepaid = ['3657'];//储值车类型
        var arrvisitor = ['3658'];//免费车类型

        var Power = window.parent.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"));
        s_carno_picker.init("Car_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "Car_CarNo") {
                $("#Car_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['table', 'element', 'form'], function () {
            var table = layui.table, layuiForm = layui.form;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                if (data.Car_EndDay) {
                    if (isNaN(data.Car_EndDay)) {
                        data.Car_EndDay = null;
                        $("#Car_EndDay").val("");
                    }
                }

                return data;
            });

            if (parent.Tools.ownerno != null) conditionParam.Car_OwnerNo = parent.Tools.ownerno;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Car_CarNo', width: 120, title: '车牌号', sort: true }
                , { field: 'Car_OwnerNo', title: '车主编码', hide: true }
                , { field: 'Owner_Space', title: '系统车位号', sort: true, width: 100 }
                , { field: 'Owner_ParkSpace', title: '车场车位号', sort: true, hide: true }
                , { field: 'Car_OwnerName', title: '车主姓名', hide: true }
                , { field: 'CarCardType_Name', title: '车牌类型', toolbar: "#TmplCategory", width: 100 }
                , { field: 'CarType_Name', title: '车牌颜色', toolbar: "#TmplCarType", hide: true }
                , {
                    field: 'Car_EnableOffline', title: '下发白名单', width: 100, templet: function (d) {
                        if (d.Car_EnableOffline == 1) return tempBar(1, "已启用");
                        else return tempBar(3, "未启用");
                    }
                }
                , {
                    field: 'Car_Balance', title: '账户余额', sort: true, width: 80, templet: function (d) {
                        if (d.CarCardType_Category == 3657) {
                            return d.Car_Balance ?? 0; //if (d.Car_IsMoreCar == 1) { if (d.Owner_Balance == null || d.Owner_Balance == "") { return "0.00" } else { return d.Owner_Balance; } } else { return d.Car_Balance; }
                        } else {
                            return "0.00";
                        }
                    }
                }
                , { field: 'Car_BeginTime', title: '开始时间', toolbar: "#tmplstime", sort: true, width: 155 }
                , { field: 'Car_EndTime', title: '结束时间', toolbar: "#tmpltime", sort: true, width: 155 }
                , { field: 'Owner_Phone', title: '手机号码', hide: true }
                , { field: 'Car_OnlyDay', title: '剩余天数', toolbar: "#tmplday" }
                , {
                    field: 'Car_IsMoreCar', title: '一位多车', hide: true, templet: function (d) {
                        if (d.Car_IsMoreCar == 1) return tempBar(1, "是");
                        else return tempBar(3, "否");
                    }
                }
                , { field: 'Car_CardNo', title: '卡号', hide: true }
                , { field: 'Car_Remark', title: '备注', hide: true }
                , { field: 'Car_AddTime', title: '创建时间', hide: true }
                , { field: 'Owner_Address', title: '车主住址', hide: true }
            ]];
            pager.bindSelect();

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Car/GetCarExtList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100, 1000]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageindex = $(".layui-laypage-curr").text();
                pager.pageIndex = pageindex;
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "车辆登记",
                            content: '/Car/Add?Act=Add',
                            area: getIframeArea(['900px', '95%']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 2000 }); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑车辆",
                            content: '/Car/Update?Act=Update&Car_No=' + data[0].Car_No,
                            area: getIframeArea(['900px', '570px']),
                            maxmin: true
                        });
                        break;
                    case 'Retweet':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 2000 }); return; }
                        var type = data[0].CarCardType_Category;
                        if (arrmonth.indexOf(type) < 0 && arrprepaid.indexOf(type) < 0) { layer.msg("请选择月租车牌、免费车牌或储值车牌"); return; }
                        if (data[0].CarCardType_IsMoreCar == 1) { layer.open({ title: "消息提示", content: "此车牌已关联多车位多车<br/>请前往[<b>车主车位</b>]进行续期", btn: ["我知道了"], time: 0 }); return; }

                        var height = "740px";
                        if (arrprepaid.indexOf(type) >= 0) { height = "540px"; }

                        pager.carParam = { Action: 'Retweet', Car_No: data[0].Car_No, Car_Type: type, height: height };
                        layer.msg("正在查询车辆是否需要清缴费用...", { icon: 16, time: 0 });
                        $.post("CheckNeedPayee", { Car_CarNo: data[0].Car_CarNo }, function (json) {
                            layer.closeAll();
                            if (json.success) {
                                var payResult = json.data.payResult;
                                var parkOrder = json.data.parkOrder;
                                if (payResult.payed == 1 && payResult.orderamount > 0) {
                                    layer.open({
                                        type: 0,
                                        title: "消息提示",
                                        btn: ["去清缴费用", "取消"],
                                        shade: 0,
                                        content: json.msg,
                                        yes: function (res) {
                                            layer.open({
                                                title: "停车支付",
                                                type: 2, id: 1,
                                                area: getIframeArea(['95%', '95%']),
                                                maxmin: true,
                                                content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(parkOrder.ParkOrder_No) + "&callBack=1",
                                                end: function () { }
                                            });
                                        }
                                    });
                                } else {
                                    console.log(data[0].Car_CarNo + "计费0元");
                                    pager.OpenRetweet(pager.carParam);
                                }
                            } else {
                                console.log(data[0].Car_CarNo + "无需缴费");
                                pager.OpenRetweet(pager.carParam);
                            }
                        }, "json");
                        break;
                    case 'Vaild':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 2000 }); return; }
                        var type = data[0].CarCardType_Category;
                        if (data[0].CarCardType_IsMoreCar == 1) { layer.open({ title: "消息提示", content: "此车牌已关联多车位多车<br/>请前往[<b>车主车位</b>]修改有效期", btn: ["我知道了"], time: 0 }); return; }

                        pager.carParam = { Action: 'Vaild', Car_No: data[0].Car_No, Car_Type: type };
                        layer.msg("正在查询车辆是否需要清缴费用...", { icon: 16, time: 0 });
                        $.post("CheckNeedPayee", { Car_CarNo: data[0].Car_CarNo }, function (json) {
                            layer.closeAll();
                            if (json.success) {
                                var payResult = json.data.payResult;
                                var parkOrder = json.data.parkOrder;
                                if (payResult.payed == 1 && payResult.orderamount > 0) {
                                    layer.open({
                                        type: 0,
                                        title: "消息提示",
                                        btn: ["去清缴费用", "取消"],
                                        shade: 0,
                                        content: json.msg,
                                        yes: function (res) {
                                            layer.open({
                                                title: "停车支付",
                                                type: 2, id: 1,
                                                area: getIframeArea(['95%', '95%']),
                                                maxmin: true,
                                                content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(parkOrder.ParkOrder_No) + "&callBack=1",
                                                end: function () { }
                                            });
                                        }
                                    });
                                } else {
                                    console.log(data[0].Car_CarNo + "计费0元");
                                    pager.OpenRetweet(pager.carParam);
                                }
                            } else {
                                console.log(data[0].Car_CarNo + "无需缴费");
                                pager.OpenRetweet(pager.carParam);
                            }
                        }, "json");
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var carNoList = [];
                        $.each(data, function (k, v) {
                            carNoList.push(v.Car_No);
                        })
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "1.若车已在场内,注销后停车订单类型改为临时车类型.<br/>2.具体车牌类型按入口车道设置的为准.<br/>3.计费开始时间改为注销时间开始计费.<br/>确定注销?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/Car/DeleteCar", { Car_No: carNoList.join(",") }, function (json) {
                                    if (json.success)
                                        layer.msg("注销成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pageindex); } });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Export':
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定导出车辆信息吗?",
                            yes: function (res) {
                                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                    return data;
                                });
                                var field = pager.sortField == null ? "" : pager.sortField;
                                var order = pager.orderField == null ? "" : pager.orderField;
                                //实现Ajax下载文件
                                $.fileDownload('/Car/ExportExcel?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order, {
                                    httpMethod: 'GET',
                                    headers: {},
                                    data: null,
                                    prepareCallback: function (url) {
                                        $("#Export").attr("disabled", true);
                                        layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                    },
                                    successCallback: function (url) {
                                        $("#Export").attr("disabled", false);
                                        layer.msg('导出成功');
                                    },
                                    failCallback: function (html, url) {
                                        $("#Export").attr("disabled", false);
                                        layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                    }
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Import':
                        layer.open({
                            type: 2, id: 1,
                            title: "导入车辆信息",
                            content: '/Car/Import?r=' + Math.random(),
                            area: getIframeArea(['500px', '380px']),
                            maxmin: true
                        });
                        break;
                    case 'BathVaild':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }

                        var chkCarType = true;
                        var carNoList = [];
                        var carCarNoList = [];
                        var type = "";
                        $.each(data, function (k, v) {
                            //if (v.CarCardType_IsMoreCar == 1) {
                            //    chkCarType = false; layer.open({ title: "消息提示", content: v.Car_CarNo + " 已关联多车位多车<br/>请前往[<b>车主车位</b>]延期", btn: ["我知道了"], time: 0 }); return false;
                            //}
                            //if (type == "") { type = v.CarCardType_Category; }
                            //else if (type != v.CarCardType_Category) {
                            //    chkCarType = false; layer.open({ title: "消息提示", content: "选中的车辆包含多种车牌类型<br/>批量修改有效期请选择同一车牌类型", btn: ["我知道了"], time: 0 }); return false;
                            //}
                            carNoList.push(v.Car_No);
                            carCarNoList.push(v.Car_CarNo);
                        })
                        if (!chkCarType) { return; }

                        pager.carNoList = carNoList.join(",");
                        pager.carCarNoList = carCarNoList.join(",");
                        layer.open({
                            type: 2, id: 1,
                            title: "批量修改有效期",
                            content: '/Car/BathVaild?Act=Update&Car_No=' + data[0].Car_No + "&type=" + type,
                            area: getIframeArea(['680px', '510px']),
                            maxmin: true
                        });
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }

                        var carCarNoList = [];
                        $.each(data, function (k, v) { carCarNoList.push(v.Car_CarNo); });

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定启用选中车辆白名单功能?<br/>白名单启用成功后才可以在岗亭软件中下载到相机.",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/Car/EnableWhiteList", { Car_CarNoList: JSON.stringify(carCarNoList) }, function (json) {
                                    if (json.success)
                                        layer.msg("启用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pageindex); } });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }

                        var carCarNoList = [];
                        $.each(data, function (k, v) { carCarNoList.push(v.Car_CarNo); });

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定禁用选中车辆白名单功能?<br/>白名单禁用成功后会从相机白名单中删除.",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/Car/DisableWhiteList", { Car_CarNoList: JSON.stringify(carCarNoList) }, function (json) {
                                    if (json.success)
                                        layer.msg("禁用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pageindex); } });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Charge':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var type = data[0].CarCardType_Category;
                        if (arrprepaid.indexOf(type) < 0) { layer.msg("请选择储值车牌"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "储值车退费",
                            content: '/Car/Refund?Car_CarNo=' + data[0].Car_CarNo + "&Car_No=" + data[0].Car_No,
                            area: getIframeArea(['680px', '510px']),
                            maxmin: true
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox(table);
        });

        //绑定查询事件
        $(function () {
            $("#BtnSearch").click(function () { pager.bindData(1); });
        });

        var pager = {
            sortField: null,
            orderField: null,
            carCarNoList: [],
            carNoList: [],
            carParam: {},
            pageIndex: 1,
            //重新加载数据
            bindSelect: function () {
                $.ajaxSettings.async = false;
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            //排除商家车&访客车
                            if ("5,6".indexOf(d.CarCardType_Type) < 0) {
                                var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                                $("#Car_TypeNo").append(option)
                            }
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#Car_VehicleTypeNo").append(option)
                        });
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["Car_BeginTime0", "Car_BeginTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["Car_EndTime0", "Car_EndTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["Car_AddTime0", "Car_AddTime1"], { type: 'datetime', range: true });

                $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
                    var key = $(this).attr("data-key");
                    var data = getHelpContent(key);
                    if (data) {
                        layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
                    }
                }, function () {
                    layer.closeAll();
                });

                $.ajaxSettings.async = true;
                layui.form.render();
            },
            bindData: function (index, offclose) {
                if (!offclose) layer.closeAll();
                pager.GetData(index);
            },
            GetData: function (index) {
                now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
                nowDate = new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"));
                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                    if (data.Car_EndDay) {
                        if (isNaN(data.Car_EndDay)) {
                            data.Car_EndDay = null;
                            $("#Car_EndDay").val("");
                        }
                    }
                    return data;
                });
                if (parent.Tools.ownerno != null) conditionParam.Car_OwnerNo = parent.Tools.ownerno;
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/Car/GetCarExtList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            Continue: function (ParkOrderNo) {
                console.log("支付成功，继续执行续费延期");
                layer.closeAll();
                //layer.msg("支付成功", { icon: 16, time: 0 });
                pager.OpenRetweet(pager.carParam);
            },
            OpenRetweet: function (carParam) {
                console.log(carParam)
                if (carParam.Action == "Retweet") {
                    layer.open({
                        type: 2,
                        title: "续费延期",
                        content: '/Car/Retweet?Act=Update&Car_No=' + carParam.Car_No + '&Car_Type=' + carParam.Car_Type,
                        area: getIframeArea(['550px', carParam.height]),
                        maxmin: true
                    });
                } else {
                    layer.open({
                        type: 2,
                        title: "修改有效期",
                        content: '/Car/Valid?Act=Update&Car_No=' + carParam.Car_No + "&type=" + carParam.Car_Type,
                        area: getIframeArea(['650px', '510px']),
                        maxmin: true
                    });
                }
            }
        }


        function getStatus(ChargeRules_BeginTime, ChargeRules_EndTime) {
            if (ChargeRules_BeginTime && ChargeRules_EndTime) {

                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd hh:mm:ss"));
                ChargeRules_EndTime = new Date(new Date(ChargeRules_EndTime).Format("yyyy-MM-dd hh:mm:ss"));

                if (ChargeRules_EndTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return null;
            return data;
        }

        //提示信息数据
        var HelpData = [
            {
                key: "Car_EndDay",
                Description: ["剩余天数：车辆已过期，并且该车辆的结束时间过期超出*天 或者 车辆即将过期，并且该车辆的结束时间在未来*天即将过期"],
            }
        ];
    </script>




</body>
</html>
