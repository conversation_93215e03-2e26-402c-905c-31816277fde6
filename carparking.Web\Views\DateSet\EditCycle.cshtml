﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        span.ss { font-size: 12px; text-align: justify; word-break: break-all; color: #888; background-color: lemonchiffon; width: calc(100% - 10px); float: left; padding: 3px 5px; }
        .layui-input[readonly]{background-color:initial !important;}
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">日期范围</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" id="DateSet_Start" name="DateSet_Start" readonly />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">工作日</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="checkbox" name="t1" title="周一" lay-skin="primary" checked />
                    <input type="checkbox" name="t2" title="周二" lay-skin="primary" checked />
                    <input type="checkbox" name="t3" title="周三" lay-skin="primary" checked />
                    <input type="checkbox" name="t4" title="周四" lay-skin="primary" checked />
                    <input type="checkbox" name="t5" title="周五" lay-skin="primary" checked />
                    <input type="checkbox" name="t6" title="周六" lay-skin="primary" disabled />
                    <input type="checkbox" name="t0" title="周日" lay-skin="primary" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">节假日</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="checkbox" name="st1" title="周一" lay-skin="primary" disabled />
                    <input type="checkbox" name="st2" title="周二" lay-skin="primary" disabled />
                    <input type="checkbox" name="st3" title="周三" lay-skin="primary" disabled />
                    <input type="checkbox" name="st4" title="周四" lay-skin="primary" disabled />
                    <input type="checkbox" name="st5" title="周五" lay-skin="primary" disabled />
                    <input type="checkbox" name="st6" title="周六" lay-skin="primary" checked />
                    <input type="checkbox" name="st0" title="周日" lay-skin="primary" checked />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">备注</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <textarea class="layui-textarea" maxlength="255" id="DateSet_Desc" name="DateSet_Desc"></textarea>
                </div>
            </div>
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <span class="ss"><b>温馨提示：</b>对同一天多次设置时，将会覆盖上一次设置的日期。例如[设置2021-04-01为节假日并保存成功后，再次对2021-04-01设置为工作日，则表示2021-04-01为工作日；后设置的将覆盖上一次的设置]</span>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();

        layui.use(["form", "laydate"], function () {
            pager.init()
        });

    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("DateSet_No");
        var year = $.getUrlParam("year");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layui.laydate.render({ elem: "#DateSet_Start", range: true });

                layui.form.on('checkbox', function (data) {
                    if (data.elem.checked) {
                        for (var i = 0; i < 7; i++) {
                            if (data.elem.name == ("t" + i)) { $("input[name='st" + i + "']").removeAttr("disabled").attr("disabled", true); }
                            if (data.elem.name == ("st" + i)) { $("input[name='t" + i +"']").removeAttr("disabled").attr("disabled", true); }
                        }                        
                    } else {
                        for (var i = 0; i < 7; i++) {
                            if (data.elem.name == ("t" + i)) { $("input[name='st" + i + "']").removeAttr("disabled"); }
                            if (data.elem.name == ("st" + i)) { $("input[name='t" + i + "']").removeAttr("disabled"); }
                        }
                    }
                    layui.form.render("checkbox");
                });
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("GetDateSetDetail", { DateSet_No: paramNo }, function (json) {
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data, function (data) { });                           
                        }
                    });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = {};
                    param.DateSet_Start = $("#DateSet_Start").val().split(' - ')[0];
                    param.DateSet_End = $("#DateSet_Start").val().split(' - ')[1];
                    param.DateSet_Desc = $("#DateSet_Desc").val();

                    var week = [];
                    var holiday = [];
                    for (var i = 0; i < 7; i++) {
                        if ($("input[name='t" + i + "']")[0].checked) {
                            week[week.length] = i;
                        }
                        if ($("input[name='st" + i + "']")[0].checked) {
                            holiday[holiday.length] = i;
                        }
                    }

                    param.week = JSON.stringify(week);
                    param.holiday = JSON.stringify(holiday);

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.getJSON("SetCycleDate", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                    window.parent.pager.GetCalendar();
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            },
        };
    </script>
</body>
</html>
