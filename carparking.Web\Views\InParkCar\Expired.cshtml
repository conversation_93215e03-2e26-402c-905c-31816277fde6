﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入停车订单</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .layui-card { box-shadow: none; }
        a.temp-link { text-decoration: underline; cursor: pointer; user-select: none; }
        .row-1 { padding-top: 10px; }
    </style>
</head>
<body>
    <div class="layui-form">
        <div class="layui-row" id="searchForm">
            <div class="layui-col-xs8" style="margin-top:10px;">
                <div class="layui-row row-1">
                    <div class="layui-col-xs3"><label class="layui-form-label">截止日期</label></div>
                    <div class="layui-col-xs4">
                        <input type="text" class="layui-input" id="date" name="date" value="@Html.Raw(ViewBag.ExpireDate)" autocomplete="off" />
                    </div>
                    <div class="layui-col-xs4" style="padding:10px 0 0 6px">
                        <input class="layui-input" type="checkbox" id="chkExp" name="chkExp" title="仅按截止日期筛选逾期停车记录" lay-skin="primary">
                    </div>
                </div>
                <div class="layui-row row-1">
                    <div class="layui-col-xs3"><label class="layui-form-label">选择模板</label></div>
                    <div class="layui-col-xs8">
                        <div class="layui-col-xs9">
                            <input type="text" placeholder="" id="iptFilePath" class="layui-input" readonly="readonly">
                        </div>
                        <div class="layui-col-xs3">
                            <label title="选择" for="inputFile" class="layui-btn layui-btn-fluid">
                                <input type="file" accept=".xlsx" name="file" id="inputFile" class="hide" onchange="chooseFile(this);"><i class="fa fa-folder-open-o"></i> <t class="lan-label" data-lan="Select">选择</t>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="layui-row row-1">
                    <div class="layui-col-xs3"><label class="layui-form-label">&nbsp;</label></div>
                    <div class="layui-col-xs8">
                        <a class="temp-link" id="btnDownload"><icon class="layui-icon layui-icon-file-b"></icon>下载模板</a>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs10 layui-col-xs-offset1">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" id="Comparison" lay-event="Comparison"><i class="fa fa-check-square-o"></i> <t>比对车牌</t></button>
                                <button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa fa-download"></i> <t>导出记录</t></button>
                                <button class="layui-btn layui-btn-sm" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i> <t>删除车牌</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
            <div class="layui-col-xs4">
                <dl style="padding: 1rem; text-align: justify; word-break: break-all; word-wrap: break-word; margin: 1rem; background: #F2F2D4; ">
                    <dt>温馨提示：</dt>
                    <dd>(1) 逾期车辆处理之前，建议先备份数据库</dd>
                    <dd>(2) 使用该功能的前提是需人工将截止日前之前的场内实际停车车牌号码抄录下来，用于与系统记录做比较</dd>
                </dl>
                <dl style="padding: 1rem; text-align: justify; word-break: break-all; word-wrap: break-word; margin: 1rem; background: #F2F2D4; ">
                    <dt>操作流程：</dt>
                    <dd>步骤1：人工抄录场内车辆车牌号码</dd>
                    <dd>步骤2：下载模板，并将人工抄录的信息编辑至模板中</dd>
                    <dd>步骤3：选择模板文件路径，选择比对数据的截止日期</dd>
                    <dd>步骤4：比对车牌，软件生成比对结果</dd>
                    <dd>步骤5：选择需要处理的逾期车辆记录</dd>
                    <dd>步骤6：导出以及删除选中的逾期车辆记录</dd>
                </dl>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/ajaxfileupload2.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var comtable = null;
        layui.use(['table', 'form', 'laydate', 'element'], function () {
            var table = layui.table;
            var cols = [[
                { type: 'checkbox' }
                , { field: 'ParkOrder_No', title: '订单号', hide: true }
                , { field: 'ParkOrder_CarNo', title: '车牌号' }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型' }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色' }
                , { field: 'ParkOrder_EnterTime', title: '入场时间' }
                , { field: 'ParkOrder_EnterImgPath', title: '入场图片', templet: function (d) { return tempImg(d.ParkOrder_EnterImgPath); } }
            ]];

            var conditionParam = { date: "1970-01-01 00:00:00"};
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/InParkCar/GetExpiredList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , data: []
                , limits: [10, 20, 50, 100, 200, 500, 1000]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Comparison':
                        pager.bindData(pager.pageIndex);
                        break;
                    case 'Export':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].ParkOrder_No);
                        }
                        //实现Ajax下载文件
                        $.fileDownload('/InParkCar/Export', {
                            httpMethod: 'POST',
                            data: { ordernos: NoArray.join("','") },
                            prepareCallback: function (url) {
                                $("#Export").attr("disabled", true);
                                layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                            },
                            successCallback: function (url) {
                                $("#Export").attr("disabled", false);
                                layer.msg('导出成功');
                            },
                            failCallback: function (html, url) {
                                $("#Export").attr("disabled", false);
                                layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                            }
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].ParkOrder_No);
                        }
                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "<font style='color:red;'>您正在删除停车记录，请谨慎操作。</font><br/>确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/InParkCar/Delete", { ParkOrder_NoArray: JSON.stringify(NoArray) }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    default: break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox(table);

            pager.init();
        });
    </script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        function chooseFile(iptFile) {
            $("#iptFilePath").val(iptFile.value);

            ajaxFileUpload();
        }
        function ajaxFileUpload() {
            $("#Import").attr("disabled", true);
            layer.msg("耗时较长,请等待..", { icon: 16, time: 0 });
            $.ajaxFileUpload({
                url: '/InParkCar/GetExcelCarNos', //用于文件上传的服务器端请求地址
                type: 'post',
                data: {}, //此参数非常严谨，写错一个引号都不行
                secureuri: false, //一般设置为false
                fileElementId: ['inputFile'], //文件上传空间的id属性
                dataType: 'json', //返回值类型 一般设置为json
                success: function (data) //服务器成功响应处理函数
                {
                    layer.closeAll();
                    console.log(data);
                    if (data.success) {
                        pager.carnos = data.data;
                    } else {
                        pager.carnos = [];
                        layer.msg("读取模板失败：" + data.msg, { icon: 0, time: 2000 });
                    }
                },
                complete: function () {

                },
                error: function (data, status, e) //服务器响应失败处理函数
                {
                    pager.carnos = [];
                    console.log("[" + e.message + "]" + data.responseText)
                    layer.msg(status);
                }
            });
            return false;
        }
        var pager = {
            carnos: [],
            pageIndex: 1,
            pageSize: 10,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["date"], { type: "datetime" });
            },
            bindData: function (index) {
                var conditionParam = {
                    date: $("#date").val(),
                    chkExp: $("#chkExp")[0].checked,
                    carnos: pager.carnos.join(",")
                };

                comtable.reload({
                    url: '/InParkCar/GetExpiredList'
                    , where: { conditionParam: JSON.stringify(conditionParam) }
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });

                $("#btnDownload").click(function () {
                    this.href = "../../Data/expire_template.xlsx";
                });
            }
        };
    </script>
</body>
</html>
