﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>门禁管理-门禁列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 8px 5px; float: left; }
    </style>
</head>
<body>

    <div class="layui-fluid">
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="Owner_Space" id="Owner_Space" autocomplete="off" placeholder="车位号" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Owner_Name" id="Owner_Name" autocomplete="off" placeholder="车主姓名" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" name="Owner_Phone" id="Owner_Phone" autocomplete="off" placeholder="手机号码（完整手机号）" />
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t class="lan-btnbar" data-lan="BtnSearch">搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                    </div>
                    <script type="text/html" id="toolbar_btns">
                        <div class="layui-btn-container">
                            {{# if(Power.Car.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>保存</t></button>{{# } }}
                        </div>
                    </script>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>

    <script>
        var Power = window.parent.parent.parent.global.formPower;
        var index = parent.layer.getFrameIndex(window.name);
        var comtable = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            var cols = [[
                { type: 'radio' }
                , { field: 'Owner_Space', width: 140, title: '车位号' }
                , { field: 'Owner_Name', title: '车主姓名' }
                , { field: 'Owner_IDCard', title: '身份证号' }
                , { field: 'Owner_License', title: '驾驶证号'}
                , { field: 'Owner_Phone', title: '手机号码' }
                , { field: 'Owner_Email', title: '电子邮箱'}
                , { field: 'Owner_Address', title: '车主住址'}
                , { field: 'Owner_Remark', title: '备注信息', hide: true }
            ]];
        
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Car/GetOwnerList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (res) {
                    tb_page_set(res);
                }

            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageindex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        parent.pager.SetOwner(data[0].Owner_No, data[0].Owner_Name, data[0].Owner_IDCard, data[0].Owner_License, data[0].Owner_Phone, data[0].Owner_Email, data[0].Owner_Sex, data[0].Owner_Address, data[0].Owner_Space, data[0]);
                        parent.layer.close(index);

                        break;
                };
            });

            tb_row_radio(table);
        });

        //绑定查询事件
        $(function () {
            $("#BtnSearch").click(function () { pager.bindData(1); });
        });
    </script>
    <script>
        var pager = {
            bindPeople: null,
            pageIndex: 1,
            bindSelect: function () {

            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Car/GetOwnerList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            }
        }

      
    </script>
</body>
</html>
