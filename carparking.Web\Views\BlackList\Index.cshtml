﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>黑名单管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa {
            margin: 6px 4px;
            float: left;
            font-size: 16px;
        }
    </style>
</head>

<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车辆管理</cite></a>
                <a><cite>黑名单管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input " name="BlackList_CarNo" id="BlackList_CarNo"
                                    autocomplete="off" placeholder="车牌号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="BlackList_Name" id="BlackList_Name" autocomplete="off"
                                    placeholder="联系人" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="BlackList_Phone" id="BlackList_Phone"
                                    autocomplete="off" placeholder="手机号" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-input" name="BlackList_AccessControl" id="BlackList_AccessControl"
                                    lay-verify="">
                                    <option value="">请选择通行控制</option>
                                    <option value="0">不可入不可出</option>
                                    <option value="1">可入但不可出</option>
                                    <option value="2">不可入但可出</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i>
                                    <t>搜索</t>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.BlackList.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.BlackList.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.BlackList.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                                {{# if(Power.BlackList.DownLoadCarList){}}<button class="layui-btn layui-btn-sm" lay-event="DownLoadCarList"><i class="fa fa-send"></i><t>相机黑名单</t></button>{{# } }}
                                {{# if(Power.BlackList.Import){}}<button class="layui-btn layui-btn-sm" lay-event="Import"><i class="fa fa-download"></i><t>导入</t></button>{{# } }}
                                {{# if(Power.BlackList.Export){}}<button class="layui-btn layui-btn-sm" lay-event="Export"><i class="fa fa-upload"></i><t>导出</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        s_carno_picker.init("BlackList_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "BlackList_CarNo") {
                $("#BlackList_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();
        layui.use(['table', 'jquery', 'form'], function () {
            pager.init();

            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'BlackList_CarNo', width: 140, title: '车牌号码' }
                , { field: 'BlackList_BeginTime', title: '开始时间' }
                , { field: 'BlackList_EndTime', title: '结束时间' }
                , {
                    field: 'BlackList_AccessControl', title: '通行控制', templet: function (d) {
                        var color, text;
                        switch (parseInt(d.BlackList_AccessControl)) {
                            case 0:
                                color = '#ff5722';
                                text = '不可入不可出';
                                break;
                            case 1:
                                color = '#2f363c';
                                text = '可入但不可出';
                                break;
                            case 2:
                                color = '#a233c6';
                                text = '不可入但可出';
                                break;
                            default:
                                return '未设置';
                        }
                        return '<span class="layui-badge" style="background-color:' + color + '">' + text + '</span>';
                    }
                }
                , { field: 'BlackList_Name', title: '联系人' }
                , { field: 'BlackList_Phone', title: '手机号码' }
                , { field: 'BlackList_AddTime', title: '添加时间' }
                , { field: 'BlackList_Remark', title: '备注' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/BlackList/GetBlackListList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增黑名单",
                            content: '/BlackList/Edit?Act=Add',
                            area: getIframeArea(['800px', '510px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑黑名单",
                            content: '/BlackList/Edit?Act=Update&BlackList_No=' + data[0].BlackList_No,
                            area: getIframeArea(['800px', '510px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var blackListNoList = [];
                        $.each(data, function (k, v) {
                            blackListNoList.push(v.BlackList_No);
                        })
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除该黑名单吗?",
                            area: ["300px"],
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/BlackList/DeleteBlackList", { blackListNoArray: blackListNoList.join(',') }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 0, btn: ["确定"] });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'DownLoadCarList':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        pager.carnoes = [];
                        $.each(data, function (k, v) {
                            pager.carnoes.push(v.BlackList_CarNo);
                        })

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "相机黑名单",
                            content: '/BlackList/WhiteRecord?Act=Update&t=' + Math.random(),
                            area: getIframeArea(['1100px', '800px']),
                            maxmin: false
                        });
                        break;
                    case 'Export':
                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;

                                    //实现Ajax下载文件
                                    $.fileDownload('/BlackList/ExportBlackList', {
                                        httpMethod: 'GET',
                                        data: {
                                            conditionParam: JSON.stringify(conditionParam),
                                            field: field,
                                            order: order,
                                            chkfield: JSON.stringify(pager.dataField)
                                        },
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                    case 'Import':
                        layer.open({
                            type: 2, id: 1,
                            title: "导入黑名单信息",
                            content: '/BlackList/Import?r=' + Math.random(),
                            area: getIframeArea(['500px', '445px']),
                            maxmin: true
                        });
                        break;
                };
            });

            tb_row_checkbox(table);
        });
    </script>
    <script>
        var pager = {
            carnoes: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                pager.bindPower();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/BlackList/GetBlackListList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });

                // 添加通行控制下拉框的change事件
                $("#BlackList_AccessControl").change(function () {
                    pager.bindData(1);
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
    </script>
</body>

</html>