@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧道闸参数设置</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-collapse {
            margin-bottom: 65px;
        }

        .layui-colla-content {
            padding-top: 60px;
        }
    </style>
</head>

<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div>
            <div style="overflow:hidden;height:0;">
                <!--防止浏览器密码后自动填充-->
                <input type="password" />
                <input type="text" />
                <input type="text" name="email" />
            </div>
        </div>
        <div style="padding: 0px">
            <div style="padding-bottom: 15px;">
                <div
                    style="padding: 10px; background-color: #ecf8ff; border-radius: 0px!important; border:0px solid #1e9fff; color: #1e9fff;">
                    <i class="layui-icon layui-icon-tips" style="margin-right: 8px; font-size: 16px;"></i>
                    <span style="font-size: 14px;">请注意：请先点击"读取参数"按钮获取当前设备配置，然后再进行参数设置。</span>
                </div>
            </div>
        </div>
        <div id="verifyCheck" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-col-sm2 edit-label">设备名称</label>
                <div class="layui-col-xs9 edit-ipt-ban">
                    <input type="text" class="layui-input v-minlen" data-minlen="2" maxlength="22" id="LaneCtrl_Name"
                        name="LaneCtrl_Name" placeholder="" readonly="readonly" />
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-col-sm2 edit-label">道闸类型</label>
                <div class="layui-col-xs9 edit-ipt-ban">
                    <select name="BrakeType" id="BrakeType" lay-verify="required" lay-search>
                        <option value="1" selected>AK</option>
                        <option value="2">WJ</option>
                        <option value="3">RTK</option>
                        <option value="4">QJ</option>
                        <option value="5">ZMT</option>
                        <option value="6">HY</option>
                        <option value="7">HX</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-col-sm2 edit-label">通道类型</label>
                <div class="layui-input-block">
                    <div class="btnCombox" id="ChannelType">
                        <ul>
                            <li data-value="1">同进同出(单通道)</li>
                            <li data-value="2" class="select">入口或出口(双通道)</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-form-item xj">
                <label class="layui-col-sm2 edit-label">遥控器使能</label>
                <div class="layui-input-block">
                    <div class="btnCombox" id="controlOpen" name="controlOpen">
                        <ul>
                            <li data-value="00">禁用</li>
                            <li data-value="01" class="select">启用</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-collapse" style="padding:0px 40px;">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title layui-col-xs12">更多</h2>
                    <div class="layui-colla-content">
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">状态变化主动上报</label>
                            <div class="layui-input-block">
                                <div class="btnCombox" id="EnIoStataUpdata">
                                    <ul>
                                        <li data-value="0">禁用</li>
                                        <li data-value="1" class="select">启用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">地感1号</label>
                            <div class="layui-input-block">
                                <div class="btnCombox" id="EnT120Genser1">
                                    <ul>
                                        <li data-value="0" class="select">禁用</li>
                                        <li data-value="1">启用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item layui-hide channel">
                            <label class="layui-col-sm2 edit-label">地感3号</label>
                            <div class="layui-input-block">
                                <div class="btnCombox" id="EnT120Genser3">
                                    <ul>
                                        <li data-value="0" class="select">禁用</li>
                                        <li data-value="1">启用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">电动车抢闸判定</label>
                            <div class="layui-input-block">
                                <div class="btnCombox" id="EnRobBrakeJudge">
                                    <ul>
                                        <li data-value="0" class="select">禁用</li>
                                        <li data-value="1">启用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">防砸限时倒车</label>
                            <div class="layui-col-xs9 edit-ipt-ban">
                                <input type="number" id="ShortCarBackDelay" name="ShortCarBackDelay" data-max="16"
                                    data-min="3" data-multiple="1" min="3" max="16" value="8"
                                    class="layui-input multiple">
                                <label class="label mylabel">当车辆离开抓拍地感后，防砸地感限时内未检测到有车，上报倒车事件，取值范围[3,16]
                                    单位(秒)</label>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">抓拍限时倒车</label>
                            <div class="layui-col-xs9 edit-ipt-ban">
                                <input type="number" id="NocarBackDelay" name="NocarBackDelay" data-max="65"
                                    data-min="5" data-multiple="1" min="5" max="65" value="15"
                                    class="layui-input multiple">
                                <label class="label mylabel">识别车牌后，抓拍地感限时内未检测到有车，上报倒车事件，取值范围[5,65] 单位(秒)</label>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">延时自动落杆</label>
                            <div class="layui-col-xs9 edit-ipt-ban">
                                <input type="number" id="NoCarCloseDelay" name="NoCarCloseDelay" data-max="310"
                                    data-min="10" data-multiple="1" min="10" max="310" value="30"
                                    class="layui-input multiple">
                                <label class="label mylabel">道闸打开后，抓拍地感限时内未检测到有车，动关闸，取值范围[10,310] 单(秒)</label>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">车辆滞留上报</label>
                            <div class="layui-col-xs9 edit-ipt-ban">
                                <input type="number" id="LongTimeStopDelay" name="LongTimeStopDelay" data-max="300"
                                    data-min="10" data-multiple="1" min="10" max="300" value="300"
                                    class="layui-input multiple">
                                <label class="label mylabel">抓拍地感、防砸地感长时间有车滞留上报相关事件，取值范围[10,300] 单位(秒)</label>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">开关闸遇阻</label>
                            <div class="layui-col-xs9 edit-ipt-ban">
                                <input type="number" id="delayTime" name="delayTime" data-max="32" data-min="5"
                                    data-multiple="1" min="5" max="32" value="12" class="layui-input multiple">
                                <label class="label mylabel">限时内道闸未开关到位，上报开关闸遇阻事件，取值范围[5,32] 单位(秒)</label>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-col-sm2 edit-label">道闸机号</label>
                            <div class="layui-col-xs9 edit-ipt-ban">
                                <input type="number" id="BrakeDrvno" name="BrakeDrvno" data-max="30" data-min="1"
                                    data-multiple="1" min="1" max="30" value="1" class="layui-input multiple" readonly>
                                <label class="label mylabel">设备道闸机号不能修改</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row" style="padding-bottom: 20px;">
            <div class="layui-col-xs3 layui-col-sm2 edit-label">&nbsp;</div>
            <div class="layui-col-xs9 layui-col-sm10 edit-ipt-ban">
                <button class="btn btn-primary" id="ReadParams"><i class="fa fa-refresh"></i> 读取参数</button>
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> 设置参数</button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> 取消</button>
            </div>
        </div>

    </div>

    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form', 'element'], function () {
            laydate = layui.laydate;
            layform = layui.form;

            pager.init();
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramDeviceNo = parent.pager.SelDeviceNoes;
        var paramDeviceName = parent.pager.SelDeviceNames;
        var index = parent.layer.getFrameIndex(window.name);
        $("#LaneCtrl_Name").val(paramDeviceName);

        var pager = {
            paramsLoaded: false,
            init: function () {
                this.bindPower();
                this.bindEvent();
                this.bindData();
                // 确保"读取参数"按钮可见
                $("#ReadParams").removeClass("layui-hide");
                // 初始禁用保存按钮
                $("#Save").attr("disabled", true);
            },
            bindData: function () {
                var loadIndex = layer.msg('读取参数中', {
                    icon: 16,
                    shade: 0.01,
                    time: 0  // 设置为0，表示不自动关闭
                });

                $.ajax({
                    url: "GetLanDeviceInfo",
                    data: { no: paramDeviceNo },
                    dataType: "json",
                    success: function (json) {
                        if (json.success) {
                            pager.paramsLoaded = true;
                            $("#Save").removeAttr("disabled");
                            LoadDeviceConfig(json.data);
                            layer.msg("参数加载成功", { icon: 1, time: 1500 });
                        } else {
                            $("#Save").attr("disabled", true);
                            layer.msg(json.msg, { icon: 2, time: 2000 });
                        }
                    },
                    error: function () {
                        $("#Save").attr("disabled", true);
                        layer.msg("加载参数失败，请重试", { icon: 2, time: 2000 });
                    },
                    complete: function () {
                        layer.close(loadIndex);
                    }
                });

                // 设置超时处理
                setTimeout(function () {
                    layer.close(loadIndex);
                    if (!pager.paramsLoaded) {
                        layer.msg("读取参数超时，请重试", { icon: 2, time: 2000 });
                        $("#Save").attr("disabled", true);
                    }
                }, 30000);  // 30秒超时
            },
            bindEvent: function () {
                $("#Cancel").click(function () {
                    parent.layer.close(index);
                });

                $("#Save").click(function () {
                    if (!pager.paramsLoaded) {
                        layer.msg("请先读取参数", { icon: 2, time: 2000 });
                        return;
                    }

                    if (!myVerify.check()) return;

                    // 防重复提交
                    if ($(this).data('submitting')) return;
                    $(this).data('submitting', true);

                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });

                    // 添加按钮组的值
                    $(".btnCombox").each(function () {
                        var idName = $(this).attr("id");
                        param[idName] = $(this).find("li.select").attr("data-value");
                    });

                    param.DeviceNo = paramDeviceNo;

                    $("#Save").attr("disabled", true);

                    // 使用Promise处理异步操作
                    new Promise((resolve, reject) => {
                        $.post("SetLanDevice", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                resolve(json);
                            } else {
                                reject(json);
                            }
                        }, "json").fail(function (xhr, status, error) {
                            reject({ msg: "请求失败: " + error });
                        });
                    }).then(json => {
                        // 成功回调，只执行一次
                        layer.msg("已下发", { icon: 1, time: 1500 }, function () {
                            $("#Save").removeAttr("disabled");
                        });
                    }).catch(error => {
                        // 错误处理
                        layer.msg(error.msg || "设置失败", { icon: 2, time: 2000 });
                        console.error("设置失败:", error);
                    }).finally(() => {
                        // 重置提交状态
                        $("#Save").removeAttr("disabled").data('submitting', false);
                    });
                });

                $("#ReadParams").click(function () {
                    pager.bindData(); // 使用优化后的 bindData 方法
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            }
        }
    </script>
    <script>
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");

                $(this).siblings().removeClass("select");
                $(this).addClass("select");

                if (idName == "ChannelType") {
                    if ($("#ChannelType").find("li.select").attr("data-value") == "1") {
                        $(".channel").removeClass("layui-hide");
                    } else {
                        $(".channel").addClass("layui-hide");
                    }
                }
            });

            $('input[type=number].multiple').on("blur",
                function (e) {
                    var multipleVal = typeof ($(this).attr("data-multiple")) == "undefined"
                        ? 15
                        : parseInt($(this).attr("data-multiple"));
                    var value = $(this).val();
                    var max = parseInt($(this).attr("data-max"));
                    var min = parseInt($(this).attr("data-min"));
                    var defaultVal = typeof ($(this).attr("data-val")) == "undefined"
                        ? $(this).attr("min")
                        : parseInt($(this).attr("data-val"));
                    if (value > max) value = max;
                    if (value < min) value = min;
                    if (value % multipleVal != 0) {
                        value = defaultVal;
                    }
                    $(this).val(Math.floor(value));
                    $(this).removeClass("border-red");
                });

            $('input[type=number].multiple').on("mousewheel DOMMouseScroll",
                function (e) {
                    var multipleVal = typeof ($(this).attr("data-multiple")) == "undefined"
                        ? 1
                        : parseInt($(this).attr("data-multiple"));
                    var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) ||
                        // chrome & ie
                        (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                    var value = $(this).val();
                    if (delta > 0) {
                        // 向上滚
                        value = Math.floor(value) + multipleVal - 1;
                        console.log("向上滚" + $(this).val());

                    } else if (delta < 0) {
                        // 向下滚
                        value = Math.floor(value) - multipleVal + 1;
                        console.log("向下滚" + $(this).val());

                    }
                    var max = parseInt($(this).attr("data-max"));
                    var min = parseInt($(this).attr("data-min"));

                    if (value > max) value = max;
                    if (value < min) value = min;
                    $(this).val(Math.floor(value));
                    $(this).removeClass("border-red");
                });
        });

        var LoadDeviceConfig = function (data) {
            // 使用批量更新来减少DOM操作
            var updates = {
                "#ShortCarBackDelay": data.ShortCarBackDelay,
                "#NocarBackDelay": data.NocarBackDelay,
                "#NoCarCloseDelay": data.NoCarCloseDelay,
                "#LongTimeStopDelay": data.LongTimeStopDelay,
                "#delayTime": data.delayTime,
                "#BrakeDrvno": data.BrakeDrvno,
                "#BrakeType": data.BrakeType
            };

            Object.keys(updates).forEach(function (selector) {
                $(selector).val(updates[selector]);
            });

            // 更新按钮组
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                var value = idName === "controlOpen" ? (data[idName] === "01" ? "01" : "00") : data[idName];
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value='" + value + "']").addClass("select");
            });

            // 处理 ChannelType 的特殊情况
            if ($("#ChannelType").find("li.select").attr("data-value") == "1") {
                $(".channel").removeClass("layui-hide");
            } else {
                $(".channel").addClass("layui-hide");
            }

            layform.render('select'); // 重新渲染表单，使下拉框生效
        }
    </script>
    <script>
        // 在文档加载完成后执行
        $(document).ready(function () {
            pager.init();
        });
    </script>
</body>

</html>
