﻿using Aop.Api.Domain;
using carparking.AutoSentryBox;
using carparking.BLL.Cache;
using carparking.Cache.Web;
using carparking.Charge;
using carparking.Common;
using carparking.Config;
using carparking.Model;
using carparking.Passthrough485;
using carparking.PassTool;
using carparking.SentryBox;
using carparking.SentryBox.BarrierDevice;
using carparking.SentryBox.Command;
using carparking.SentryBox.Device;
using carparking.SentryBox.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using TcpConnPools;
using TcpConnPools.Camera;
using TcpConnPools.Controller;
using TcpConnPools.Controller.NonMotorized;
using TcpConnPools.SceneVideo;
using static carparking.SentryBox.Device.CameraCarList;

namespace carparking.Web.Controllers
{
    /// <summary>
    /// 监控中心【岗亭】
    /// </summary>
    public class MonitoringController : SentryBaseController
    {
        #region **【获取车场信息】【获取停车场策略】【获取车道关联区域信息】【获取系统配置】【车牌颜色】【获取停车场设置的优惠列表】【获取云平台连接状态】...

        /// <summary>
        /// 获取岗亭信息
        /// </summary>
        /// <returns></returns>
        public Model.SentryHost GetSentryHost()
        {
            Model.SentryHost model = (AppSettingConfig.SentryMode == VersionEnum.CloudServer ? (BLL.SentryHost.GetAllEntity("*", "")?.Find(x => x.SentryHost_No == AppSettingConfig.DeviceSN)) : BLL.SentryHost.GetEntity(ConfigurationMap.GetModel.SentryHostNo)) ?? new Model.SentryHost();
            return model;
        }

        /// <summary>
        /// 获取车场信息
        /// </summary>
        /// <returns></returns>
        public Model.Parking GetParking(string Parking_No = null)
        {
            if (string.IsNullOrEmpty(Parking_No))
                Parking_No = GetSentryHost()?.SentryHost_ParkNo;

            Model.Parking parking = BLL.Parking._GetAllEntity(new Model.Parking(), "*", $"1=1 and  Parking_No='{Parking_No}'").FirstOrDefault() ?? new Model.Parking();
            return parking;
        }

        /// <summary>
        /// 获取停车场策略参数
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetPolicyPark(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var model = BLL.PolicyPark.GetEntity(GetParking().Parking_No);
                var isSuccess = model != null;
                return ResOk(isSuccess, isSuccess ? "获取停车场策略参数成功" : "获取停车场策略参数失败", model);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetPolicyPark]获取停车场策略参数异常");
                return ResOk(false, "获取停车场策略参数：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取车道关联区域信息
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetPasswayLinkExt(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                Model.Parking parking = GetParking();
                List<Model.PasswayLinkExt> models = BLL.PasswayLink.GetAllEntityExt("*", $"PasswayLink_ParkNo='{parking.Parking_No}'");

                return ResOk(true, "获取成功", models);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetCarType]获取车道关联区域信息异常");
                return ResOk(false, "获取车道关联区域信息：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取系统配置(主要取得入场备注项，免费原因项)
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetSysConfig(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                Model.Parking parking = GetParking();

                Model.SysConfig config = BLL.SysConfig.GetEntity(parking.Parking_No);
                Model.SysConfigContent content = Model.SysConfigContent.GetIntance(config);

                return ResOk(true, "获取成功", new { config = config, content = content });
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetCarType]获取系统配置异常");
                return ResOk(false, "获取系统配置：" + ex.Message);
            }
        }

        /// <summary>
        /// 车牌颜色
        /// </summary>
        public async Task<IActionResult> SltCarTypeList(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.CarType> models = BLL.CarType.GetAllEntity("CarType_ID,CarType_No,CarType_Name", $"CarType_ParkNo='{parking.Parking_No}'");

                return ResOk(true, "", models);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取停车场设置的优惠列表
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SltParkDiscountSet(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = BLL.Parking.GetEntity(null);

                List<Model.CouponRecordIntExt> couponSetList = Calc.GetOutCarCoupon(parking.Parking_No, null, null, lgAdmin?.Admins_ID);

                return ResOk(true, "", couponSetList);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取停车场设置的优惠列表异常");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取云平台连接状态
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetParkingState(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && DirectCloudMQTT.MqttClient.Instance?.IsOnline == true)
                {
                    return ResOk(true, "云平台", 1);
                }

                var parking = GetParking();

                if (parking.Parking_EnableNet == 0 || string.IsNullOrEmpty(parking.Parking_Key))
                    return ResOk(true, "未启用云平台", -1);

                if (AppSettingConfig.SentryMode != VersionEnum.CloudServer)
                {
                    if ((DateTimeHelper.GetNowTime() - Library.RSocket.MidUpdateLastTime).TotalSeconds <= 30)
                        return ResOk(true, "云平台在线", 1);
                }
                else
                {
                    return ResOk(true, "云平台", DirectCloudMQTT.MqttClient.Instance == null ? 0 : DirectCloudMQTT.MqttClient.Instance.IsOnline ? 1 : 0);
                }

                return ResOk(true, "云平台离线", 0);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取云平台连接状态异常");
                return ResOk(false, ex.Message, 0);
            }
        }

        /// <summary>
        /// 获取车场连接状态
        /// </summary>
        /// <returns></returns>
        public IActionResult GetHostState()
        {
            try
            {
                //if (ServerChannel.LastHeartTime == null || ServerChannel.LastHeartTime < DateTimeHelper.GetNowTime().AddSeconds(-45))
                //    return ResOk(true, "主机离线", 0);
                //else
                //    return ResOk(true, "主机在线", 1);

                return ResOk(true, "云平台", 1);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取车场连接状态异常");
                return ResOk(false, ex.Message, 0);
            }
        }

        /// <summary>
        /// 获取车牌类型【临时车优先】
        /// </summary>
        public async Task<IActionResult> GetCarCardType(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }
                Model.Parking parking = GetParking();
                List<Model.CarCardType> data = BLL.CarCardType.GetAllEntity("*", $"CarCardType_ParkNo='{parking.Parking_No}'");

                data = BLL.CarCardType.CardOrderBy(data, 2);

                return ResOk(true, "读取成功", data);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetCarCardType]获取车牌类型异常");
                return ResOk(false, "获取车牌类型异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 车牌类型【月租车优先】
        /// </summary>
        public async Task<IActionResult> SltCarCardTypeList(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = GetParking();

                List<Model.CarCardType> carCardTypes = BLL.CarCardType.GetAllEntity("CarCardType_ID,CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_IsMoreCar,CarCardType_Type", $"CarCardType_ParkNo='{parking.Parking_No}'");

                var data = CardOrderBy(carCardTypes, 1);

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[SltCarCardTypeList]获取车牌类型异常");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取车牌颜色
        /// </summary>
        public async Task<IActionResult> GetCarType(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.CarType> data = BLL.CarType.GetAllEntity("CarType_ID,CarType_No,CarType_Name", $"");

                return ResOk(true, "读取成功", data);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetCarType]获取车牌颜色异常");
                return ResOk(false, "获取车牌颜色异常：" + ex.Message);
            }
        }

        ///// <summary>
        ///// 车牌颜色
        ///// </summary>
        //public async Task<IActionResult> SltCarTypeList()
        //{
        //    try
        //    {
        //        var parking = GetParking();
        //        List<Model.CarType> models = BLL.CarType.GetAllEntity("CarType_ID,CarType_No,CarType_Name", $"CarType_ParkNo='{parking.Parking_No}'");

        //        return ResOk(true, "", models);
        //    }
        //    catch (Exception ex)
        //    {
        //        LogManagementMap.WriteToFileException(ex, "[GetCarType]获取车牌颜色异常");
        //        return ResOk(false, "获取车牌颜色异常：" + ex.Message);
        //    }
        //}

        /// <summary>
        /// 出入通道
        /// </summary>
        public async Task<IActionResult> SltPasswayList(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = GetParking();
                string sql = "";
                List<string> lanes = new List<string>();
                if (lgAdmin == null && !string.IsNullOrEmpty(code)) { lgAdmin = DataCache.Admin.Get(code); }

                if (!string.IsNullOrWhiteSpace(lgAdmin?.Admins_PasswayNo))
                {
                    lanes = JsonConvert.DeserializeObject<List<string>>(lgAdmin.Admins_PasswayNo);
                    if (lanes != null && lanes.Count > 0)
                    {
                        sql = $" and Passway_No in ('{string.Join("','", lanes)}')";
                    }
                }

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("Passway_ID,Passway_No,Passway_Name,Passway_SameInOut,Passway_OutNo", $"Passway_ParkNo='{parking.Parking_No}'");

                return ResOk(true, "", passways);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取出入通道列表异常");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 车牌类型
        /// </summary>
        public async Task<IActionResult> SltCarCardTypeList2(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.CarCardType> carCardTypes = BLL.CarCardType.GetAllEntity("CarCardType_ID,CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_IsMoreCar,CarCardType_Type", $"CarCardType_ParkNo='{parking.Parking_No}'");

                var data = CardOrderBy(carCardTypes, 2);

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 区域列表
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SltParkAreaList(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = GetParking();
                List<Model.ParkArea> models = BLL.ParkArea.GetAllEntity("ParkArea_ID,ParkArea_No,ParkArea_Name,ParkArea_Type", $"ParkArea_ParkNo='{parking.Parking_No}'");

                return ResOk(true, "", models);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取区域列表异常");
                return ResOk(false, ex.Message);
            }
        }
        /// <summary>
        /// 出入通道（区分入口|出口）
        /// </summary>
        public async Task<IActionResult> SltGatePasswayList(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("Passway_ID,Passway_No,Passway_Name,Passway_SameInOut,Passway_OutNo", $"Passway_ParkNo='{parking.Parking_No}'");
                List<Dictionary<string, object>> data = TyziTools.Json.ToObject<List<Dictionary<string, object>>>(TyziTools.Json.ToString(passways));
                List<Model.PasswayLink> links = BLL.PasswayLink.GetAllEntity(parking.Parking_No);

                foreach (var item in data)
                {
                    var gate = BLL.Passway.GetPasswayGateType(item["Passway_No"].ToString(), links, AppBasicCache.GetParkAreas.Values.ToList());
                    if (gate == 1 || gate == 2)
                    {
                        item.Add("Passway_GateType", 1);
                    }
                    else
                    {
                        item.Add("Passway_GateType", 0);
                    }

                    var l = links?.FindAll(x => x.PasswayLink_PasswayNo == item["Passway_No"].ToString());
                    item.Add("Passway_Area", string.Join(',', l?.Select(x => x.PasswayLink_ParkAreaNo)));
                }

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 抓拍图片
        /// </summary>
        /// <param name="passwayno"></param>
        /// <returns></returns>
        public async Task<IActionResult> SnapShootToJpeg(string passwayno, bool IsApi = false, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkPower(lgAdmin, "SnapShootToJpeg"))
                {
                    LogManagementMap.WriteToFileException(null, $"[{passwayno}]抓拍图片,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                Model.Passway passway = BLL.Passway.GetEntity(passwayno);
                if (passway == null) return ResOk(false, "无车道信息");

                if (passway.Passway_Type == 1)
                {
                    Model.Device device = BLL.Device.GetEntityByPasswayNo(passwayno);
                    if (device == null)
                    {
                        return ResOk(false, "无车牌识别相机");
                    }

                    string tempImage = string.Empty;
                    string base64 = string.Empty;
                    string errmsg = string.Empty;

                    Stopwatch watch = new Stopwatch();
                    watch.Start();
                    var ffaa = await LPRTools.GetSnapShootToJpeg(passwayno, tempImage, base64, errmsg);
                    watch.Stop();
                    LogManagementMap.WriteToFile(LoggerEnum.CallCenterLog, $"LPRTools.GetSnapShootToJpeg抓取图片总耗时:{watch.Elapsed.Milliseconds}");

                    var imgRet = ffaa.Item1;
                    tempImage = ffaa.Item2;
                    base64 = ffaa.Item3;
                    errmsg = ffaa.Item4;
                    if (!imgRet)
                    {
                        return ResOk(false, errmsg);
                    }

                    //if (AppBasicCache.CurrentSysConfigContent != null && AppBasicCache.CurrentSysConfigContent.SysConfig_EnableImgPath == 1 && AppBasicCache.CurrentSysConfigContent.SysConfig_ImagePath != AppBasicCache.CurrentSysConfigContent.SysConfig_SentryImagePath)
                    //{
                    //    PassHelperBiz.ImageSaveByBase64(passway.Passway_ParkNo, base64, null, passway.Passway_SentryHostNo, System.IO.Path.GetFileName(tempImage));
                    //    //PassTool.PassHelperBiz.ImageSave(device.Device_ParkNo, tempImage);
                    //}
                    string displayImg = LPRTools.GetSentryHostImg(tempImage);
                    return ResOk(true, "抓拍成功", new { tempImage = tempImage, displayImg = displayImg });
                }
                else if (passway.Passway_Type == 3)
                {
                    return ResOk(true, "无车牌识别相机", new { tempImage = "", displayImg = "" });
                }

                return ResOk(false, "无车牌识别相机");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CapturePictures, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} 触发识别异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, "识别车牌异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取视频信息
        /// </summary>
        /// <param name="passwayno"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetVideo(string passwayno, int sequence = 0)
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false)) { return ResOk(false, "无权限"); }

                if (AppBasicCache.CurrentSysConfigContent?.SysConfig_OpenCameraVideo == "2")
                {
                    return ResOk(false, "不支持视频播放");
                }

                Model.Device device = null;

                if (sequence == 0)
                    device = BLL.Device.GetEntityByPasswayNo(passwayno);
                else
                {
                    List<Model.DeviceExt> deviceList = BLL.Device.GetSubEntityByPasswayNo(passwayno);
                    if (deviceList.Count >= (sequence - 1))
                    {
                        device = deviceList[sequence - 1];
                    }
                }

                if (device == null) return ResOk(false, "无车牌识别相机");

                Model.Drive drive = BLL.BaseBLL._GetEntityByWhere(new Model.Drive(), "*", $"Drive_Category=1 and  Drive_No='{device.Device_DriveNo}'");
                if (drive != null)
                {
                    bool is06Camera = Common.CameraDeviceType.driveNameList06.Contains(drive.Drive_Name);
                    bool is11Camera = Common.CameraDeviceType.driveNameList11.Contains(drive.Drive_Name);
                    var is13Camera = Common.CameraDeviceType.driveNameList13.Contains(drive.Drive_Name);

                    if (drive.Drive_Code == Model.DriveCode.ZC100 && (!is06Camera || (is06Camera && device.Device_VideoMode != 1)) && !is11Camera && !is13Camera)
                    {
                        return ResOk(false, "不支持视频播放");
                    }
                    else if (drive.Drive_Code == Model.DriveCode.ZC100 && (is06Camera && device.Device_VideoMode == 1 || is11Camera))
                    {
                        var data = new
                        {
                            ip = device.Device_IP,
                            username = device.Device_Account,
                            password = device.Device_Pwd,
                            driveno = device.Device_No,
                            type = 2,
                            mode = is11Camera ? 1 : (device.Device_VideoMode ?? 0),
                            camera = is11Camera ? "11" : "06"
                        };
                        return ResOk(true, "获取视频成功", data);
                    }
                    else if (drive.Drive_Code == Model.DriveCode.ZS101)
                    {
                        var data = new
                        {
                            ip = device.Device_IP,
                            username = device.Device_Account,
                            password = device.Device_Pwd,
                            driveno = device.Device_No,
                            type = 2,
                            mode = 2,
                            camera = "15"
                        };
                        return ResOk(true, "获取视频成功", data);
                    }
                    else if (is13Camera)
                    {
                        var data = new
                        {
                            ip = device.Device_IP,
                            username = device.Device_Account,
                            password = device.Device_Pwd,
                            driveno = device.Device_No,
                            type = 2,
                            mode = 1,
                            camera = "13"
                        };
                        return ResOk(true, "获取视频成功", data);
                    }
                }

                return ResOk(false, "不支持的设备型号");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetVideo, $"获取视频异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, "获取视频异常：" + ex.Message);
            }
        }

        public async Task<IActionResult> GetRTSPUrl(string deviceno)
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false, false)) { return ResOk(false, "无权限"); }

                var device = AppBasicCache.GetAllDeivces.FirstOrDefault(m => m.Value.Device_No == deviceno);
                if (device.Value == null) return ResOk(false, "无设备信息");

                //var camera = CameraController.GetCamera(device.Value.Device_IP);
                //if (camera == null) return ResOk(false, "无摄像头信息");

                //if (AppBasicCache.GetParking?.Parking_EnableNet != 1) { return ResOk(false, "未启用云平台"); }

                await SentryBox.CommHelper.GetDeviceRTSPUrl(deviceno);

                var rtspVideoPlay = AppBasicCache.GetElement(AppBasicCache.RTSPVideoPlay, deviceno);
                if (rtspVideoPlay == null) return ResOk(false, "无RTSP视频播放信息");

                var url = rtspVideoPlay.Item2;

                return ResOk(true, "获取RTSP视频播放地址成功", url);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取RTSP视频播放地址异常");
                return ResOk(false, ex.Message);
            }
        }

        #endregion

        #region **【出口弹窗-订单详情】

        /// <summary>
        /// 订单详情视图
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> ParkOrderDetail()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString()))
            {
                LogManagementMap.WriteToFileException(null, $"加载订单详情页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        public async Task<IActionResult> GetOrderDetailByNo(string ParkOrder_No)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString()))
                    return ResOk(false, "无权限");

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                var time = DateTime.Parse("1900-01-01 00:00:00");
                if (model != null && model.ParkOrder_OutTime == time) { model.ParkOrder_OutTime = null; }

                List<Model.PayOrder> payOrders = BLL.PayOrder.GetAllEntity("*", $"PayOrder_ParkOrderNo='{ParkOrder_No}'");

                List<Model.OrderDetail> detailList = BLL.OrderDetail.GetAllEntity(ParkOrder_No);
                if (detailList != null) detailList = detailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                detailList.ForEach(x =>
                {
                    if (x != null && x.OrderDetail_OutTime == time) { x.OrderDetail_OutTime = null; }
                });

                List<Model.CouponRecord> couponList = null;
                if (model != null && model.ParkOrder_StatusNo != EnumParkOrderStatus.Follow) couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $"CouponRecord_ParkOrderNo='{ParkOrder_No}' and CouponRecord_Status=1");

                List<Model.CalcDetail> calcList = BLL.BaseBLL._GetAllEntity(new Model.CalcDetail(), "*", $"CalcDetail_ParkOrderNo='{ParkOrder_No}'");

                List<Model.DetentionPenalty> penaltyList = BLL.BaseBLL._GetAllEntity(new Model.DetentionPenalty(), "*", $"DetentionPenalty_ParkOrderNo='{ParkOrder_No}'");

                return ResOk(true, "", new { model = model, payOrders = payOrders, parking = AppBasicCache.GetParking, detail = detailList, coupon = couponList, calclist = calcList, penaltylist = penaltyList });
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmin, LogEnum.Backend, "查询停车订单详情", "查询停车订单详情发生异常:" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }
        #endregion

        #region **【出入口弹窗-多车多位】

        /// <summary>
        /// 多车多位【出入口弹窗】
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> MultiCar()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString()))
            {
                LogManagementMap.WriteToFileException(null, $"加载订单详情页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 多车多位【出入口弹窗】
        /// </summary>
        public async Task<IActionResult> GetMultiCarByNo(string ParkOrder_No)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString()))
                    return ResOk(false, "无权限");

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                var time = DateTime.Parse("1900-01-01 00:00:00");
                if (model != null && model.ParkOrder_OutTime == time) { model.ParkOrder_OutTime = null; }

                List<Model.PayOrder> payOrders = BLL.PayOrder.GetAllEntity("*", $"PayOrder_ParkOrderNo='{ParkOrder_No}'");

                List<Model.OrderDetail> detailList = BLL.OrderDetail.GetAllEntity(ParkOrder_No);
                if (detailList != null) detailList = detailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                detailList.ForEach(x =>
                {
                    if (x != null && x.OrderDetail_OutTime == time) { x.OrderDetail_OutTime = null; }
                });

                List<Model.CouponRecord> couponList = null;
                if (model != null && model.ParkOrder_StatusNo != EnumParkOrderStatus.Follow) couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $"CouponRecord_ParkOrderNo='{ParkOrder_No}' and CouponRecord_Status=1");

                List<Model.CalcDetail> calcList = BLL.BaseBLL._GetAllEntity(new Model.CalcDetail(), "*", $"CalcDetail_ParkOrderNo='{ParkOrder_No}'");

                List<Model.DetentionPenalty> penaltyList = BLL.BaseBLL._GetAllEntity(new Model.DetentionPenalty(), "*", $"DetentionPenalty_ParkOrderNo='{ParkOrder_No}'");

                return ResOk(true, "", new { model = model, payOrders = payOrders, parking = AppBasicCache.GetParking, detail = detailList, coupon = couponList, calclist = calcList, penaltylist = penaltyList });
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmin, LogEnum.Backend, "查询停车订单详情", "查询停车订单详情发生异常:" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }
        #endregion

        #region **【获取当班收费】【获取当前车位数据】【获取车道列表】【场内记录查询】【获取车道通行策略】

        public IActionResult PaymentDetail()
        {
            return View();
        }

        public IActionResult InCar()
        {
            ViewBag.BusinessCache = AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache;
            return View();
        }

        public IActionResult Emergency()
        {
            ViewBag.Emergency = CameraGlobal.IsEmergency ? "已启用" : "已禁用";
            return View();
        }

        public IActionResult DisplayPart()
        {
            return View();
        }

        /// <summary>
        /// 视图
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Index()
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index"))
                {
                    LogManagementMap.WriteToFileException(null, $"加载岗亭页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                    HttpHelper.HttpContext.Response.Redirect(@"/Gt/Index");
                    return new EmptyResult();
                }

#if DEBUG
                ViewBag.Debug = true;
#endif

                LogManagementMap.WriteToFile(LoggerEnum.ManualOperation, $"[{lgAdmin?.Admins_Account}]进入岗亭值守");
                Model.SentryHost host = GetSentryHost();
                Model.Parking parking = GetParking(host.SentryHost_ParkNo);

                string ws = Request.Host.Value.Contains(":") ? AppSettingConfig.WebSocketUrl : $"ws://{AppBasicCache.Ip}:{AppSettingConfig.WebSocketPort}";
                ViewBag.WebSocketUrl = ws;
                ViewBag.SentryHost = host;
                ViewBag.WebSocketKey = Guid.NewGuid();
                ViewBag.parking = parking;
                ViewBag.Account = Utils.UrlEncode(lgAdmin?.Admins_Account);
                ViewBag.admin = lgAdmin;
                lgAdmin.PowerGroup_Value = BLL.PowerGroup.GetEntity(lgAdmin.PowerGroup_ID)?.PowerGroup_Value ?? lgAdmin.PowerGroup_Value;
                ViewBag.WinFormMonitor = JsonConvert.DeserializeObject<Dictionary<string, object>>(lgAdmin?.PowerGroup_Value)["WinFormMonitor"];
                var parking_Key = parking?.Parking_Key;
                ViewBag.Parking_Key = string.IsNullOrEmpty(parking_Key) ? "parkingkey" : parking_Key;
                if (lgAdmin != null && string.IsNullOrEmpty(lgAdmin?.Admins_LoginTime))
                {
                    Model.Admins admins = new Model.Admins();
                    admins.Admins_ID = lgAdmin?.Admins_ID;
                    admins.Admins_LoginTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");
                    BLL.Admins.UpdateByModel(admins);
                    lgAdmin.Admins_LoginTime = admins.Admins_LoginTime;
                }

                Model.SysConfigContent content = null;
                try
                {
                    Model.SysConfig model = BLL.SysConfig._GetEntityByWhere(new Model.SysConfig(), "*", $"1=1");
                    if (model?.SysConfig_Content != null)
                    {
                        content = TyziTools.Json.ToModel<Model.SysConfigContent>(BLL.SysConfig.GetUrlDecode(model.SysConfig_Content));
                        if (Startup.GetPlModelBase != null && !string.IsNullOrWhiteSpace(Startup.GetPlModelBase.BsTitle))
                        {
                            content.SysConfig_DIYEnable = 1;
                            content.SysConfig_DIYSoftName = Startup.GetPlModelBase.BsTitle;
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex);
                }

                if (content != null && content.SysConfig_DIYEnable == 1)
                {
                    ViewBag.SysConfig_DIYEnable = 1;
                    ViewBag.SysConfig_DIYName = string.IsNullOrWhiteSpace(content.SysConfig_DIYSoftName) ? "停车场岗亭管理服务" : content.SysConfig_DIYSoftName;
                }
                else
                {
                    ViewBag.SysConfig_DIYName = "停车场岗亭管理服务";
                }

                ViewBag.OpenCameraVideo = AppBasicCache.CurrentSysConfigContent?.SysConfig_OpenCameraVideo;
                ViewBag.sysconfig_playerType = AppBasicCache.CurrentSysConfigContent?.SysConfig_PlayerType ?? 1;

                AppBasicCache.ConnectionParking_CToken = null;
                AppBasicCache.ConnectionParking_HttpToken = null;
                ViewBag.sysconfig_playerType = AppBasicCache.CurrentSysConfigContent?.SysConfig_PlayerType ?? 2;

                return View();
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Login, $"进入岗亭值守异常退回登录页面:[" + ex.Message + "]", SecondIndex.Monitoring);
                return RedirectToAction("Index", "Gt");
            }
        }

        /// <summary>
        /// 获取车道通行策略
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetPolicyPassway(string passwayno)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }
                var policy = AppBasicCache.GetAllPolicyPassway.Values.Where(x => x.PolicyPassway_PasswayNo == passwayno).FirstOrDefault();
                return ResOk(true, "读取成功", policy);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPolicyPassway, $"获取车道列表异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取当班收费
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetCurrentWorkShift(string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                LogManagementMap.WriteToFile(LoggerEnum.ManualOperation, $"[{lgAdmin?.Admins_Account}]获取当班信息");
                decimal cashMoneys = 0;
                decimal jmMoneys = 0;
                decimal ysMoneys = 0;
                decimal ssMoneys = 0;
                decimal electronicMoneys = 0; //电子支付

                bool hasPassway = false;
                var sqlWhere = $"PayOrder_PayedTime>='{lgAdmin.Admins_LoginTime}' and PayOrder_PayedTime<='{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss")}' and PayOrder_Status ='1' ";
                List<string> passnoList = new List<string>();
                if (!string.IsNullOrEmpty(lgAdmin.Admins_PasswayNo) && lgAdmin.Admins_PasswayNo != "[]")
                {
                    lgAdmin.Admins_PasswayNo = string.IsNullOrEmpty(lgAdmin.Admins_PasswayNo) ? "[]" : lgAdmin.Admins_PasswayNo;
                    passnoList = TyziTools.Json.ToObject<List<string>>(lgAdmin.Admins_PasswayNo);
                    if (passnoList == null || passnoList.Count == 0)
                    {
                        sqlWhere += $" and PayOrder_Account='{lgAdmin.Admins_Account}'";
                    }
                    else
                    {
                        for (var i = 0; i < passnoList.Count; i++)
                        {
                            if (string.IsNullOrEmpty(passnoList[i]))
                            {
                                passnoList.RemoveAt(i);
                            }
                        }

                        if (passnoList.Count > 0)
                            hasPassway = true;
                        else
                            sqlWhere += $" and PayOrder_Account='{lgAdmin.Admins_Account}'";
                    }
                }

                var payList = BLL.PayOrder.GetAllEntity("PayOrder_PayedMoney,PayOrder_Money,PayOrder_DiscountMoney,PayOrder_PayTypeCode,PayOrder_PassWayNo,PayOrder_PayScene,PayOrder_Account", sqlWhere);
                var sumMoney = payList.Where(m => m.PayOrder_Account == lgAdmin.Admins_Account);
                cashMoneys = sumMoney.Where(m => m.PayOrder_PayTypeCode == "79001").Sum(m => m.PayOrder_PayedMoney) ?? 0;
                jmMoneys = sumMoney.Sum(m => m.PayOrder_DiscountMoney) ?? 0;
                ysMoneys = sumMoney.Sum(m => m.PayOrder_Money) ?? 0;
                ssMoneys = sumMoney.Sum(m => m.PayOrder_PayedMoney) ?? 0;

                if (hasPassway)
                {
                    electronicMoneys = payList.Where(m => (m.PayOrder_PayScene == 2 || m.PayOrder_PayScene == 4) && passnoList.Contains(m.PayOrder_PassWayNo)).Sum(m => m.PayOrder_PayedMoney) ?? 0;
                }
                else
                {
                    electronicMoneys = payList.Where(m => m.PayOrder_PayScene == 2 || m.PayOrder_PayScene == 4).Sum(m => m.PayOrder_PayedMoney) ?? 0;
                }

                int spNums = 0;
                int speosonNum = 0;
                var records = BLL.BaseBLL._GetAllEntity(new Model.PassRecord { }, "*", $"PassRecord_Account='{lgAdmin.Admins_Account}' and PassRecord_PassTime>='{lgAdmin.Admins_LoginTime}' and PassRecord_PassTime<='{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss")}'");
                spNums = records.Where(m => m.PassRecord_Type == 2).Count();
                speosonNum = records.Where(m => m.PassRecord_Type == 1).Count();

                var data = new
                {
                    cashMoneys = $"{cashMoneys:0.00}",
                    name = string.IsNullOrEmpty(lgAdmin.Admins_Name) ? lgAdmin.Admins_Account : lgAdmin.Admins_Name,
                    time = $"{lgAdmin.Admins_LoginTime}",
                    jmMoneys = $"{jmMoneys:0.00}",
                    ysMoneys = $"{ysMoneys:0.00}",
                    ssMoneys = $"{ssMoneys:0.00}",
                    spNums = $"{spNums:0}",
                    speosonNum = $"{speosonNum:0}",
                    electronicMoneys = $"{electronicMoneys:0.00}",
                };

                return ResOk(true, "获取当班收费", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCurrentWorkShift, $"获取当班信息异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取当前车位数据
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetCurrentCarSpace(string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = GetParking();
                var spaceData = PassTool.MonitorHelper.GetAllSpaceItems(parking.Parking_No);

                return ResOk(true, "获取当前车位数据", spaceData);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCurrentCarSpace, $"获取当前车位数据异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取车道列表
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetPasswayData()
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false, false)) { return ResOk(false, "无权限"); }
                if (lgAdmin == null) return ResOk(false, "账号异常");

                Model.Parking parking = GetParking();
                string sql = "";
                List<string> lanes = new List<string>();
                if (!string.IsNullOrWhiteSpace(lgAdmin.Admins_PasswayNo))
                {
                    lanes = JsonConvert.DeserializeObject<List<string>>(lgAdmin.Admins_PasswayNo);
                    if (lanes != null && lanes.Count > 0)
                    {
                        sql = $" and Passway_No in ('{string.Join("','", lanes)}')";
                    }
                }

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("*", $"Passway_ParkNo='{parking.Parking_No}' and Passway_SentryHostNo='{AppBasicCache.SentryHostInfo?.SentryHost_No}' {sql} ");
                List<Model.PasswayLinkExt> areas = BLL.PasswayLink.GetAllEntityExt("*", $"PasswayLink_ParkNo='{parking.Parking_No}'");

                List<Model.PasswayAndParkArea> data = TyziTools.Json.ToObject<List<Model.PasswayAndParkArea>>(TyziTools.Json.ToString(passways, true));

                List<Model.Device> devices = BLL.Device.GetAllEntity("Device_ID,Device_No,Device_Name,Device_PasswayNo,Device_ScreenNum", "Device_Category=1");
                devices = devices.OrderBy(x => x.Device_ScreenNum).ThenBy(x => x.Device_ID).ToList();

                var orderedPasswayNos = devices.Select(d => d.Device_PasswayNo).Distinct().ToList();
                var passwayOrderMap = orderedPasswayNos.Select((passwayNo, index) => new { passwayNo, index }).ToDictionary(x => x.passwayNo, x => x.index);
                data = data.OrderBy(d => passwayOrderMap.ContainsKey(d.Passway_No) ? passwayOrderMap[d.Passway_No] : int.MaxValue).ToList();

                foreach (var item in data)
                {
                    item.Passway_Links = areas.FindAll(x => x.PasswayLink_PasswayNo == item.Passway_No)?.OrderBy(x => x.PasswayLink_GateType).ToList();
                    if (item.Passway_Links != null)
                    {
                        if (item.Passway_Links.Count == 1)
                        {
                            item.Passway_GateType = item.Passway_Links.First().PasswayLink_GateType;
                            item.Passway_AreaCallName = $"{(item.Passway_Links.First().PasswayLink_GateType == 0 ? "[出]" : "[入]")}{item.Passway_Links.First().ParkArea_Name}";
                        }
                        else if (item.Passway_Links.Count == 2)
                        {
                            item.Passway_GateType = 2;
                            List<string> names = new List<string>();
                            item.Passway_Links.ForEach(x => { names.Add($"{(x.PasswayLink_GateType == 0 ? "[出]" : "[入]")}{x.ParkArea_Name}"); });
                            item.Passway_AreaCallName = string.Join('/', names);

                            var o = item.Passway_Links.Find(x => x.PasswayLink_GateType == 0);
                            var i = item.Passway_Links.Find(x => x.PasswayLink_GateType == 1);

                            var oA = areas.Find(x => x.PasswayLink_ParkAreaNo == o.PasswayLink_ParkAreaNo);
                            var iA = areas.Find(x => x.PasswayLink_ParkAreaNo == i.PasswayLink_ParkAreaNo);

                            //出场区域的层级大于入场区域的层级，则表示从内场离开进入外场
                            if (oA.ParkArea_Level > iA.ParkArea_Level)
                            {
                                item.Passway_GateType = 3;
                            }
                        }
                        else
                            item.Passway_GateType = -1;

                        item.IsLongOpen = AppSettingConfig.GetLongOpen(item.Passway_No);
                        if (string.IsNullOrWhiteSpace(item.IsLongOpen))
                        {
                            item.IsLongOpen = "0";
                        }

                        item.Passway_DeviceCount = devices?.Where(x => x.Device_PasswayNo == item.Passway_No).Count() ?? 0;
                    }
                }

                return ResOk(true, "读取成功", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPasswayData, $"获取车道列表异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 场内记录查询
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetInParkCarList(int pageIndex, int pageSize, string conditionParam, string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and InCar_Status = {Model.EnumParkOrderStatus.In} ");
                //sqlwhere.Append($" and ParkOrder_EnterPasswayNo is not null ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and InCar_CarNo like @ParkOrder_CarNo ");

                object parameters = new
                {
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? "%" + model.ParkOrder_CarNo + "%" : null
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.InCar> incarlst = BLL.ParkOrder._GetList<Model.InCar>("InCar_ParkOrderNo", sqlwhere.ToString(), pageIndex, pageSize, "InCar_EnterTime", 0, out pageCount, out totalRecord, parameters: parameters);
                List<Model.ParkOrder> lst = new List<Model.ParkOrder>();
                if (incarlst.Count > 0)
                {
                    lst = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in ('{string.Join("','", incarlst.Select(x => x.InCar_ParkOrderNo))}') ORDER BY ParkOrder_EnterTime DESC");
                }

                lst.ForEach(x =>
                {
                    string enterimg = LPRTools.GetSentryHostImg(HttpUtility.UrlDecode(x.ParkOrder_EnterImg));
                    if (!string.IsNullOrWhiteSpace(enterimg) && enterimg.Contains(AppBasicCache.SentryHostInfo?.SentryHost_No ?? "*"))
                    {
                        x.ParkOrder_EnterImgPath = enterimg;
                    }

                    string outimg = LPRTools.GetSentryHostImg(HttpUtility.UrlDecode(x.ParkOrder_OutImg));
                    if (!string.IsNullOrWhiteSpace(outimg) && outimg.Contains(AppBasicCache.SentryHostInfo?.SentryHost_No ?? "*"))
                    {
                        x.ParkOrder_OutImgPath = outimg;
                    }
                });
                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                oModel.code = 4;
                oModel.msg = $"场内记录查询异常：{ex.Message}";
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetInParkCarList, $"场内记录查询异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return Ok(oModel.ParseJson());
            }
        }

        #endregion

        #region **【车辆查询】

        /// <summary>
        /// 视图
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Car()
        {
            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryMonthlyCard.ToString(), true, lgAdmin))
            {
                LogManagementMap.WriteToFileException(null, $"加载车辆查询页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 车辆查询
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetCarList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                string sqlwhere = "";
                Model.OwnerWhere model = Utils.ClearModelRiskSQL<Model.OwnerWhere>(conditionParam);
                if (model == null)
                {
                    oModel.code = 4;
                    oModel.msg = $"参数错误";
                    return Ok(oModel.ParseJson());
                }

                if (!string.IsNullOrEmpty(model?.code))
                {
                    lgAdmin = DataCache.Admin.Get(model.code);
                }

                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryMonthlyCard.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"车辆查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (!string.IsNullOrEmpty(model.Owner_CardTypeNo))
                    sqlwhere += string.Format(" and Owner_CardTypeNo = @Owner_CardTypeNo ");
                if (!string.IsNullOrEmpty(model.Owner_Space))
                    sqlwhere += string.Format(" and Owner_Space like @Owner_Space ");
                if (!string.IsNullOrEmpty(model.Owner_ParkSpace))
                    sqlwhere += string.Format("and Owner_ParkSpace like @Owner_ParkSpace ");

                if (!string.IsNullOrEmpty(model.Owner_Name))
                    sqlwhere += string.Format(" and Owner_Name like @Owner_Name ");
                if (!string.IsNullOrEmpty(model.Owner_IDCard))
                    sqlwhere += string.Format("and Owner_IDCard = @Owner_IDCard ");
                if (!string.IsNullOrEmpty(model.Owner_Phone))
                    sqlwhere += string.Format("and Owner_Phone = @Owner_Phone ");
                if (!string.IsNullOrWhiteSpace(model.Owner_PhoneLastFour))
                    sqlwhere += string.Format("and Owner_PhoneLastFour = @Owner_PhoneLastFour ");
                if (!string.IsNullOrEmpty(model.Owner_Address))
                    sqlwhere += string.Format(" and Owner_Address like @Owner_Address ");
                if (!string.IsNullOrEmpty(model.Owner_Remark))
                    sqlwhere += string.Format(" and Owner_Remark like @Owner_Remark ");

                if (model.Owner_StartTime0 != null && model.Owner_StartTime1 != null)
                    sqlwhere += $" and Owner_StartTime BETWEEN @Owner_StartTime0 AND @Owner_StartTime1 ";
                else if (model.Owner_StartTime0 != null)
                    sqlwhere += $" and Owner_StartTime >= @Owner_StartTime0 ";
                else if (model.Owner_StartTime1 != null)
                    sqlwhere += $" and Owner_StartTime <= @Owner_StartTime1 ";

                if (model.Owner_EndTime0 != null && model.Owner_EndTime1 != null)
                    sqlwhere += $" and Owner_EndTime BETWEEN @Owner_EndTime0 AND @Owner_EndTime1 ";
                else if (model.Owner_EndTime0 != null)
                    sqlwhere += $" and Owner_EndTime >= @Owner_EndTime0 ";
                else if (model.Owner_EndTime1 != null)
                    sqlwhere += $" and Owner_EndTime <= @Owner_EndTime1 ";

                if (model.Owner_AddTime0 != null && model.Owner_AddTime1 != null)
                    sqlwhere += $" and Owner_AddTime BETWEEN @Owner_AddTime0 AND @Owner_AddTime1 ";
                else if (model.Owner_AddTime0 != null)
                    sqlwhere += $" and Owner_AddTime >= @Owner_AddTime0 ";
                else if (model.Owner_AddTime1 != null)
                    sqlwhere += $" and Owner_AddTime <= @Owner_AddTime1 ";

                if (model.Owner_StartTime0 != null || model.Owner_StartTime1 != null || model.Owner_EndTime0 != null || model.Owner_EndTime1 != null)
                {
                    sqlwhere += $" and Owner_CardType NOT IN (2)";
                }

                if (model.SearchType != 0)
                {
                    string curdt = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");
                    string dtto7 = DateTimeHelper.GetNowTime().AddDays(7).ToString("yyyy-MM-dd HH:mm:ss");
                    switch (model.SearchType)
                    {
                        case 1:
                            sqlwhere += $" and (Owner_CardType=2 or '{curdt}' BETWEEN Owner_StartTime AND Owner_EndTime) ";
                            break;
                        case 2:
                            sqlwhere += $" and Owner_CardType<>2 and Owner_EndTime BETWEEN '{curdt}' AND '{dtto7}' ";
                            break;
                        case 3:
                            sqlwhere += $" and Owner_CardType<>2 and Owner_EndTime <'{curdt}' ";
                            break;
                        default: break;
                    }
                }

                #region 车辆信息条件

                if (!string.IsNullOrEmpty(model.Owner_CarCardNo))
                {
                    List<Model.Car> car = BLL.Car.GetAllEntity("Car_OwnerNo", $"Car_CarNo like @Owner_CarCardNo", new { Owner_CarCardNo = "%" + model.Owner_CarCardNo.Trim() + "%" });
                    var ownerNoList = car?.Select(x => x.Car_OwnerNo).ToList() ?? new List<string>();
                    sqlwhere += $" and Owner_No in ('{string.Join("','", ownerNoList)}') ";
                }

                if (!string.IsNullOrEmpty(model.Car_Remark))
                    sqlwhere += (string.Format(" and Owner_No in (SELECT Car_OwnerNo  from car where Car_Remark like @Car_Remark) "));
                if (!string.IsNullOrEmpty(model.Car_CardNo))
                    sqlwhere += string.Format(" and Owner_No in (SELECT Car_OwnerNo  from car where Car_CardNo like @Car_CardNo) ");

                #endregion

                object parameters = new
                {
                    Owner_CarCardNo = !string.IsNullOrEmpty(model.Owner_CarCardNo) ? "%" + model.Owner_CarCardNo + "%" : null,
                    Car_Remark = !string.IsNullOrEmpty(model.Car_Remark) ? "%" + model.Car_Remark + "%" : null,
                    Car_CardNo = !string.IsNullOrEmpty(model.Car_CardNo) ? "%" + model.Car_CardNo + "%" : null,
                    Owner_StartTime0 = model.Owner_StartTime0 != null ? model.Owner_StartTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Owner_StartTime1 = model.Owner_StartTime1 != null ? model.Owner_StartTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Owner_EndTime0 = model.Owner_EndTime0 != null ? model.Owner_EndTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Owner_EndTime1 = model.Owner_EndTime1 != null ? model.Owner_EndTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Owner_AddTime0 = model.Owner_AddTime0 != null ? model.Owner_AddTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Owner_AddTime1 = model.Owner_AddTime1 != null ? model.Owner_AddTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Owner_CardTypeNo = model.Owner_CardTypeNo,
                    Owner_Space = !string.IsNullOrEmpty(model.Owner_Space) ? "%" + model.Owner_Space + "%" : null,
                    Owner_ParkSpace = !string.IsNullOrEmpty(model.Owner_ParkSpace) ? "%" + model.Owner_ParkSpace + "%" : null,
                    Owner_Name = !string.IsNullOrEmpty(model.Owner_Name) ? "%" + model.Owner_Name + "%" : null,
                    Owner_IDCard = !string.IsNullOrEmpty(model.Owner_IDCard) ? AESHelper.AesEncrypt_DB(model.Owner_IDCard) : null,
                    Owner_Phone = !string.IsNullOrEmpty(model.Owner_Phone) ? AESHelper.AesEncrypt_DB(model.Owner_Phone) : null,
                    Owner_PhoneLastFour = model.Owner_PhoneLastFour,
                    Owner_Address = !string.IsNullOrEmpty(model.Owner_Address) ? "%" + model.Owner_Address + "%" : null,
                    Owner_Remark = !string.IsNullOrEmpty(model.Owner_Remark) ? "%" + model.Owner_Remark + "%" : null,
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.OwnerExt> lst = BLL.Owner.GetExtList("*", sqlwhere, pageIndex, pageSize, "Owner_ID", 0, out pageCount, out totalRecord, parameters: parameters);

                //读取车场配置，判断是否保密手机号码
                var PolicyPark_PhoneSecret = 1;
                var parkinfor = BLL.Parking.GetAllEntity().FirstOrDefault();
                if (parkinfor != null)
                {
                    var policyPark = BLL.PolicyPark.GetEntity(parkinfor.Parking_No);
                    if (policyPark != null)
                    {
                        PolicyPark_PhoneSecret = policyPark.PolicyPark_PhoneSecret ?? 1;
                    }
                }

                //设置车主手机号码是否保密
                lst.ForEach(m => m.Owner_IsSecretPhone = PolicyPark_PhoneSecret);

                #region 获取车主绑定的车牌号码

                List<Model.Admins> adminList = BLL.Admins.GetAllEntity("Admins_ID,Admins_Name", "1=1");
                List<Model.CarCardType> cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name", "");
                var ownerNos = lst?.Select(x => x.Owner_No).ToList();
                if (ownerNos?.Count > 0)
                {
                    var cars = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerNo", $"Car_OwnerNo in ('{string.Join("','", ownerNos)}')");
                    lst.ForEach(item =>
                    {
                        var carno = cars?.FindAll(x => x.Car_OwnerNo == item.Owner_No)?.Select(x => x.Car_CarNo).ToList();
                        if (carno?.Count > 0)
                            item.Owner_CarCardNo = string.Join(",", carno);

                        item.Owner_CardName = cctList?.Find(x => x.CarCardType_No == item.Owner_CardTypeNo)?.CarCardType_Name ?? "";
                        item.Owner_AddName = adminList?.Find(x => x.Admins_ID == item.Owner_AddID)?.Admins_Name ?? "";
                    });
                }

                #endregion

                oModel.code = 0;
                oModel.data = lst;
                oModel.msg = "ok";
                oModel.count = totalRecord;

                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                oModel.code = 4;
                oModel.msg = $"固定车查询异常：{ex.Message}";
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCarList, $"固定车查询异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return Ok(oModel.ParseJson());
            }
        }

        #endregion

        #region **【事件提醒】

        #region 视图
        /// <summary>
        /// 视图
        /// </summary>
        /// <returns></returns>
        public IActionResult Syswarn()
        {
            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            if (lgAdmin == null)
            {
                LogManagementMap.WriteToFileException(null, $"加载事件提醒页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.code = code;

            return View("ControlEvent/Index");
        }

        public IActionResult AddEvent()
        {
            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            if (lgAdmin == null)
            {
                LogManagementMap.WriteToFileException(null, $"加载新增事件提醒新增跟车记录页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.code = code;

            return View("ControlEvent/AddEvent");
        }

        public IActionResult AddInpark()
        {
            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            if (lgAdmin == null)
            {
                LogManagementMap.WriteToFileException(null, $"加载新增事件提醒新增入场页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.code = code;

            return View("ControlEvent/Add");
        }

        public IActionResult CheckOrder()
        {
            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            if (lgAdmin == null)
            {
                LogManagementMap.WriteToFileException(null, $"加载新增事件提醒查询核对页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.code = code;

            string ControlEvent_No = Request.Query["ControlEvent_No"];
            var model = BLL.ControlEvent.GetEntity(ControlEvent_No) ?? new Model.ControlEvent();

            ViewBag.start = model?.ControlEvent_Time.Value.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.end = model?.ControlEvent_Time.Value.AddMinutes(1).ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.passwayno = model?.ControlEvent_PasswayNo;
            ViewBag.gate = model?.ControlEvent_Gate;
            return View("ControlEvent/CheckOrder");
        }

        /// <summary>
        /// 视频监控视图
        /// </summary>
        /// <returns></returns>
        public IActionResult Monitor()
        {
            string url = string.Empty;
            try
            {
                string code = Request.Query["code"];
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (lgAdmin == null)
                {
                    LogManagementMap.WriteToFileException(null, $"加载新增事件提醒视频监控页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                ViewBag.code = code;

                url = Request.Query["url"];
                ViewBag.MonitorUrl = url;
                return View("ControlEvent/Monitor");
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmin, "视频设备监控视图", $"视频设备监控视图加载发生异常:[{url}]" + ex.ToString());
                HttpHelper.HttpContext.Response.Redirect(@"/Static/html/AccessError.html");
                return new EmptyResult();
            }
        }


        public IActionResult SelectOrder()
        {
            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            if (lgAdmin == null)
            {
                LogManagementMap.WriteToFileException(null, $"加载新增事件提醒订单核查页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.code = code;

            return View("ControlEvent/SelectOrder");
        }

        #endregion

        /// <summary>
        /// 仅查询外场区域的车道
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SltOutGatePasswayList(string code = "")
        {
            try
            {
                code = string.IsNullOrEmpty(code) ? CMSRequest.GetQueryString("code", true) : code;
                if (!await checkPower(lgAdmin, "", false, false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.PasswayLink> links = BLL.PasswayLink.GetAllEntity(parking.Parking_No);

                List<Model.ParkArea> area = BLL.ParkArea.GetAllEntity("*", $"ParkArea_Type=0");
                List<string> sildeAreaNos = area.Select(x => x.ParkArea_No)?.ToList() ?? new List<string>();
                List<Model.PasswayLink> sideLinks = new List<Model.PasswayLink>();

                foreach (var item in links)
                {
                    if (!sildeAreaNos.Contains(item.PasswayLink_ParkAreaNo)) continue;

                    var count = links.FindAll(x => x.PasswayLink_PasswayNo == item.PasswayLink_PasswayNo).Count;
                    if (count == 1)
                    {
                        sideLinks.Add(item);
                    }
                }

                List<string> passwayNos = sideLinks.Select(x => x.PasswayLink_PasswayNo)?.ToList() ?? new List<string>();

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("Passway_ID,Passway_No,Passway_Name,Passway_SameInOut,Passway_OutNo"
                    , $"Passway_ParkNo='{parking.Parking_No}' AND Passway_No in ('{string.Join("','", passwayNos)}')");

                List<Dictionary<string, object>> data = TyziTools.Json.ToObject<List<Dictionary<string, object>>>(TyziTools.Json.ToString(passways));

                foreach (var item in data)
                {
                    var l = links?.FindAll(x => x.PasswayLink_PasswayNo == item["Passway_No"].ToString());

                    if (l == null || l?.Count == 0)
                        item.Add("Passway_GateType", 1);
                    else if (l?.Count == 1)
                        item.Add("Passway_GateType", l.First().PasswayLink_GateType);
                    else
                        item.Add("Passway_GateType", 1);

                    item.Add("Passway_Area", string.Join(',', l?.Select(x => x.PasswayLink_ParkAreaNo)));

                }

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 事件查询
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetSyswarnList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                string sqlwhere = "";
                Model.SyswarnWhere obj = Utils.ClearModelRiskSQL<Model.SyswarnWhere>(conditionParam);

                if (!string.IsNullOrEmpty(obj?.code))
                {
                    lgAdmin = DataCache.Admin.Get(obj.code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }


                if (!string.IsNullOrEmpty(obj.Syswarn_HostNo))
                    sqlwhere += $" AND Syswarn_HostNo like @Syswarn_HostNo ";
                if (!string.IsNullOrEmpty(obj.Syswarn_HostIP))
                    sqlwhere += $" AND Syswarn_HostIP like @Syswarn_HostIP ";
                if (obj.Syswarn_Level != null)
                    sqlwhere += $" AND Syswarn_Level=@Syswarn_Level ";

                if (obj.Syswarn_Time0 != null && obj.Syswarn_Time1 != null)
                    sqlwhere += $" AND Syswarn_Time BETWEEN @Syswarn_Time0 AND @Syswarn_Time1 ";
                else if (obj.Syswarn_Time0 != null)
                    sqlwhere += $" AND Syswarn_Time>=@Syswarn_Time0 ";
                else if (obj.Syswarn_Time1 != null)
                    sqlwhere += $" AND Syswarn_Time<=@Syswarn_Time1 ";

                object parameters = new
                {
                    Syswarn_HostNo = !string.IsNullOrEmpty(obj.Syswarn_HostNo) ? "%" + obj.Syswarn_HostNo + "%" : null,
                    Syswarn_HostIP = !string.IsNullOrEmpty(obj.Syswarn_HostIP) ? "%" + obj.Syswarn_HostIP + "%" : null,
                    Syswarn_Level = obj.Syswarn_Level,
                    Syswarn_Time0 = obj.Syswarn_Time0 != null ? obj.Syswarn_Time0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    Syswarn_Time1 = obj.Syswarn_Time1 != null ? obj.Syswarn_Time1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.Syswarn> lst = BLL.BaseBLL._GetList<Model.Syswarn>("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                var data = TyziTools.Json.ToObject<List<Model.SyswarnExt>>(TyziTools.Json.ToString(lst));

                oModel.code = 0;
                oModel.data = data;
                oModel.count = totalRecord;
                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                oModel.code = 4;
                oModel.msg = $"事件查询异常：{ex.Message}";
                LogManagementMap.WriteToFileException(ex, $"[{conditionParam}]事件查询异常");
                return Ok(oModel.ParseJson());
            }
        }

        public async Task<IActionResult> SyswarnUpdate(string Syswarn_NoArray, string code)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    return ResOk(false, "无权限处理");

                List<string> Syswarn_NoList = null;
                if (Syswarn_NoArray == "0")
                {
                    DateTime curdt = DateTimeHelper.GetNowTime();
                    StringBuilder sqlWhere = new StringBuilder();
                    sqlWhere.Append($" Syswarn_Status=0 and Syswarn_Level in(10,100,1000)");
                    sqlWhere.Append($" AND Syswarn_Time BETWEEN '{curdt.ToString("yyyy-MM-dd 00:00:00")}' AND '{curdt.ToString("yyyy-MM-dd 23:59:59")}' ");
                    sqlWhere.Append($" ORDER BY Syswarn_ID DESC");
                    var r = BLL.BaseBLL._UpdateByWhere<Model.Syswarn>("Syswarn_Status=1", sqlWhere.ToString());
                    if (r > 0)
                    {
                        return ResOk(true, "处理成功");
                    }
                    else
                    {
                        return ResOk(false, "处理失败");
                    }
                }
                else
                {
                    Syswarn_NoList = TyziTools.Json.ToObject<List<string>>(Syswarn_NoArray);
                    if (Syswarn_NoList == null) return ResOk(false, "请选择");
                }

                //List<Model.Syswarn> warnes = BLL.BaseBLL._GetAllEntity(new Model.Syswarn(), "Syswarn_No", $"Syswarn_No in ('{string.Join("','", Syswarn_NoList)}')");
                //if (warnes?.Count == 0) { return ResOk(false, "没有需要处理的报警消息"); }

                var res = BLL.BaseBLL._UpdateByWhere<Model.Syswarn>($"Syswarn_Status=1,Syswarn_Account='{lgAdmin?.Admins_Account}',Syswarn_ToTime='{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}'", $"Syswarn_Status=0 and Syswarn_No in @Syswarn_NoList", new { Syswarn_NoList = Syswarn_NoList });
                if (res >= 0)
                {
                    return ResOk(true, "处理成功");
                }
                else
                {
                    return ResOk(false, "处理失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmin, "处理报警记录", $"处理报警异常：{ex.ToString()}");
                return ResOk(false, "处理失败");
            }
        }


        /// <summary>
        /// 查询跟车事件
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public async Task<IActionResult> GetControlEventList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                Model.ControlEventWhere model = Utils.ClearModelRiskSQL<Model.ControlEventWhere>(conditionParam);
                if (model == null)
                    return Ok(new Model.PageResult(-1, "", 0, null));


                if (pageSize > 1000)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                if (!string.IsNullOrEmpty(model?.code))
                {
                    lgAdmin = DataCache.Admin.Get(model.code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkPower(lgAdmin, "Index", false))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }


                StringBuilder sqlwhere = new StringBuilder();
                if (!string.IsNullOrEmpty(model.ControlEvent_No))
                    sqlwhere.Append($" and ControlEvent_No = @ControlEvent_No ");
                if (!string.IsNullOrEmpty(model.ControlEvent_CarNo))
                    sqlwhere.Append($" and ControlEvent_CarNo like @ControlEvent_CarNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_ParkAreaNo))
                    sqlwhere.Append($" and ControlEvent_ParkAreaNo = @ControlEvent_ParkAreaNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_PasswayNo))
                    sqlwhere.Append($" and ControlEvent_PasswayNo = @ControlEvent_PasswayNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_DeviceNo))
                    sqlwhere.Append($" and ControlEvent_DeviceNo = @ControlEvent_DeviceNo ");
                if (model.ControlEvent_Status != null)
                    sqlwhere.Append($" and ControlEvent_Status = @ControlEvent_Status ");
                if (model.ControlEvent_Type != null)
                    sqlwhere.Append($" and ControlEvent_Type = @ControlEvent_Type ");

                if (model.ControlEvent_Time0 != null && model.ControlEvent_Time1 != null)
                    sqlwhere.Append($" and ControlEvent_Time between @ControlEvent_Time0 AND @ControlEvent_Time1 ");
                else if (model.ControlEvent_Time0 != null)
                    sqlwhere.Append($" and ControlEvent_Time >= @ControlEvent_Time0 ");
                else if (model.ControlEvent_Time1 != null)
                    sqlwhere.Append($" and ControlEvent_Time <= @ControlEvent_Time1 ");

                object parameters = new
                {
                    ControlEvent_No = model.ControlEvent_No,
                    ControlEvent_CarNo = !string.IsNullOrEmpty(model.ControlEvent_CarNo) ? $"%{model.ControlEvent_CarNo}%" : null,
                    ControlEvent_ParkAreaNo = model.ControlEvent_ParkAreaNo,
                    ControlEvent_PasswayNo = model.ControlEvent_PasswayNo,
                    ControlEvent_DeviceNo = model.ControlEvent_DeviceNo,
                    ControlEvent_Status = model.ControlEvent_Status,
                    ControlEvent_Type = model.ControlEvent_Type,
                    ControlEvent_Time0 = model.ControlEvent_Time0,
                    ControlEvent_Time1 = model.ControlEvent_Time1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ControlEvent> lst = BLL.BaseBLL._GetList<Model.ControlEvent>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception)
            {
                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return Ok(oModel);
        }

        public async Task<IActionResult> GetControlEvent(string code, string ControlEvent_No)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限");
                }

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);

                return ResOk(true, "", model);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 查询跟车事件
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public IActionResult GetFollowList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), false, true, lgAdmin))
                    return Ok(oModel);

                if (pageSize > 1000)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                Model.ControlEventWhere model = Utils.ClearModelRiskSQL<Model.ControlEventWhere>(conditionParam);
                if (model == null)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                StringBuilder sqlwhere = new StringBuilder();
                if (!string.IsNullOrEmpty(model.ControlEvent_No))
                    sqlwhere.Append($" and ControlEvent_No = @ControlEvent_No ");
                if (!string.IsNullOrEmpty(model.ControlEvent_CarNo))
                    sqlwhere.Append($" and ControlEvent_CarNo like @ControlEvent_CarNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_ParkAreaNo))
                    sqlwhere.Append($" and ControlEvent_ParkAreaNo = @ControlEvent_ParkAreaNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_PasswayNo))
                    sqlwhere.Append($" and ControlEvent_PasswayNo = @ControlEvent_PasswayNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_DeviceNo))
                    sqlwhere.Append($" and ControlEvent_DeviceNo = @ControlEvent_DeviceNo ");
                if (model.ControlEvent_Status != null)
                    sqlwhere.Append($" and ControlEvent_Status = @ControlEvent_Status ");
                if (model.ControlEvent_Type != null)
                    sqlwhere.Append($" and ControlEvent_Type = @ControlEvent_Type ");

                if (model.ControlEvent_Time0 != null && model.ControlEvent_Time1 != null)
                    sqlwhere.Append($" and ControlEvent_Time between @ControlEvent_Time0 AND @ControlEvent_Time1 ");
                else if (model.ControlEvent_Time0 != null)
                    sqlwhere.Append($" and ControlEvent_Time >= @ControlEvent_Time0 ");
                else if (model.ControlEvent_Time1 != null)
                    sqlwhere.Append($" and ControlEvent_Time <= @ControlEvent_Time1 ");

                object parameters = new
                {
                    ControlEvent_No = model.ControlEvent_No,
                    ControlEvent_CarNo = !string.IsNullOrEmpty(model.ControlEvent_CarNo) ? $"%{model.ControlEvent_CarNo}%" : null,
                    ControlEvent_ParkAreaNo = model.ControlEvent_ParkAreaNo,
                    ControlEvent_PasswayNo = model.ControlEvent_PasswayNo,
                    ControlEvent_DeviceNo = model.ControlEvent_DeviceNo,
                    ControlEvent_Status = model.ControlEvent_Status,
                    ControlEvent_Type = model.ControlEvent_Type,
                    ControlEvent_Time0 = model.ControlEvent_Time0,
                    ControlEvent_Time1 = model.ControlEvent_Time1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ControlEvent> lst = BLL.BaseBLL._GetList<Model.ControlEvent>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception)
            {
                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return Ok(oModel);
        }

        public async Task<IActionResult> AddFollow(string jsonModel)
        {
            try
            {
                Model.ControlEventWhere model = Utils.ClearModelRiskSQL<Model.ControlEventWhere>(jsonModel);
                if (model == null) { return ResOk(false, "操作失败请重试"); }

                if (!string.IsNullOrEmpty(model?.code))
                {
                    lgAdmin = DataCache.Admin.Get(model.code);
                }

                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限");
                }

                Model.Passway pass = BLL.Passway.GetEntity(model.ControlEvent_PasswayNo);
                if (pass == null) { return ResOk(false, "车道信息不存在"); }
                var device = BLL.Device.GetEntityByPasswayNo(pass.Passway_No);
                if (device == null) return ResOk(false, "车道未设置主相机设备");
                var area = BLL.ParkArea.GetAllEntity("*", $"ParkArea_Type=0 AND ParkArea_No in (select PasswayLink_ParkAreaNo from PasswayLink where PasswayLink_PasswayNo='{pass.Passway_No}')")?.FirstOrDefault();
                if (area == null) return ResOk(false, "读取车道关联的外场区域失败");

                if (!string.IsNullOrWhiteSpace(model.ControlEvent_CarNo))
                {
                    if (model.ControlEvent_CarNo.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                    if (!Utils.IsZhNumEn(model.ControlEvent_CarNo.Trim()))
                    {
                        return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                    }
                    model.ControlEvent_CarNo = model.ControlEvent_CarNo.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", ""); //字母O替换数字0
                    model.ControlEvent_CarNo = model.ControlEvent_CarNo.ToUpper();
                }

                model.ControlEvent_No = Utils.CreateNumber;
                model.ControlEvent_AddTime = DateTime.Now;
                model.ControlEvent_AdminAccount = lgAdmin.Admins_Account;
                model.ControlEvent_AdminName = lgAdmin.Admins_Name;
                model.ControlEvent_PasswayName = pass.Passway_Name;
                model.ControlEvent_PasswayNo = pass.Passway_No;
                model.ControlEvent_Status = 0;
                model.ControlEvent_Type = 1;
                model.ControlEvent_ParkNo = parking.Parking_No;
                model.ControlEvent_ParkName = parking.Parking_Name;
                model.ControlEvent_DeviceNo = device?.Device_No;
                model.ControlEvent_DeviceName = device?.Device_Name;
                model.ControlEvent_ParkAreaName = area?.ParkArea_Name;
                model.ControlEvent_ParkAreaNo = area?.ParkArea_No;

                if (!string.IsNullOrEmpty(model.ControlEvent_BigImg) && !model.ControlEvent_BigImg.Contains("http://") && !model.ControlEvent_BigImg.Contains("https://"))
                {
                    string base64String = model.ControlEvent_BigImg;
                    string photoExtension = null;
                    if (base64String.Contains("base64,")) { photoExtension = base64String.Substring(0, base64String.IndexOf(";")).Substring(base64String.IndexOf("/") + 1); }

                    string filename = $"{Utils.CreateNumber}.{photoExtension ?? "jpg"}";
                    string path = string.Empty;
                    path = $"http://{AppBasicCache.Ip}:{AppSettingConfig.SiteDomain_WebPort}/CameraCaptures/{DateTime.Now.ToString("yyyyMM")}/{DateTime.Now.ToString("dd")}/{filename}";

                    model.ControlEvent_BigImg = LPRTools.OnSaveByDate(base64String, filename, BLL.ImageTools.LocalFilePath, !string.IsNullOrEmpty(BLL.ImageTools.LocalFilePath), device.Device_SentryHostNo);
                }

                var res = BLL.BaseBLL._Insert<Model.ControlEvent>(TyziTools.Json.ToObject<Model.ControlEvent>(TyziTools.Json.ToString(model)));
                if (res > 0)
                {
                    //内场区域的跟车不发送到智慧停车平台
                    if (area != null && area.ParkArea_Type == 0)
                        BLL.PushEvent.CarFollowAdd(parking.Parking_Key, model);

                    BLL.UserLogs.AddLog(lgAdmin, "新增跟车记录", $"新增跟车记录成功:{TyziTools.Json.ToString(model)}");
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, "新增跟车记录", $"新增跟车记录失败:{jsonModel}");
                    return ResOk(false, "插入数据失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmin, "新增跟车记录", $"新增跟车记录异常:{jsonModel}");
                return ResOk(false, $"保存失败：{ex.Message}");
            }
        }

        public async Task<IActionResult> AddParkOrder(string jsonModel, string ControlEvent_No, string code)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"事件查询新增入场,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限");
                }

                Model.ControlEvent evt = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (evt == null) { return ResOk(false, "操作失败请重试"); }
                if (evt.ControlEvent_Type != 1) { return ResOk(false, "操作失败请重试"); }
                if (evt.ControlEvent_Gate != 1) { return ResOk(false, "操作失败请重试"); }
                if (evt.ControlEvent_Status == 2 || evt.ControlEvent_Status == 3) { return ResOk(false, "操作失败请重试"); }

                Model.ParkOrder query = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (string.IsNullOrEmpty(query?.ParkOrder_CarNo)) { return ResOk(false, "车牌号不能为空"); }
                if (query?.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间不能为空"); }

                var exist = BLL.ParkOrder.GetParkOrder("*", parking.Parking_No, query.ParkOrder_CarNo);
                if (exist != null) { return ResOk(false, "车辆已在场内，不允许重复添加"); }

                var area = BLL.ParkArea.GetEntity(query?.ParkOrder_ParkAreaNo);
                if (area == null) { return ResOk(false, "区域不存在"); }

                var passway = BLL.Passway.GetEntity(query?.ParkOrder_EnterPasswayNo);
                if (passway == null) { return ResOk(false, "车道不存在"); }

                var card = BLL.CarCardType.GetEntity(query?.ParkOrder_CarCardType);
                if (card == null) { return ResOk(false, "车牌类型不存在"); }

                var carType = BLL.CarType.GetEntity(query?.ParkOrder_CarType);
                if (carType == null) { return ResOk(false, "车牌颜色不存在"); }

                var car = BLL.Car.GetEntityByCarNo(query.ParkOrder_CarNo);

                query.ParkOrder_ParkAreaName = area.ParkArea_Name;
                query.ParkOrder_EnterPasswayName = passway.Passway_Name;
                query.ParkOrder_CarCardTypeName = card.CarCardType_Name;
                query.ParkOrder_CarTypeName = carType.CarType_Name;

                Model.ParkOrder model = BLL.ParkOrder.CreateParkOrder(parking.Parking_No, query.ParkOrder_ParkAreaNo, query.ParkOrder_ParkAreaName, query.ParkOrder_CarNo, query.ParkOrder_CarCardType, query.ParkOrder_CarCardTypeName, query.ParkOrder_CarType, query.ParkOrder_CarTypeName, query.ParkOrder_EnterTime.Value, query.ParkOrder_EnterPasswayNo, query.ParkOrder_EnterPasswayName, 0, 0, car?.Car_OwnerNo, car?.Car_OwnerName);
                model.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                model.ParkOrder_EnterAdminAccount = lgAdmin?.Admins_Account;
                model.ParkOrder_EnterAdminName = lgAdmin?.Admins_Name;
                model.ParkOrder_EnterRemark = query.ParkOrder_EnterRemark;
                model.ParkOrder_EnterImgPath = evt.ControlEvent_BigImg;

                Model.OrderDetail detail = BLL.OrderDetail.CreateOrderDetail(model.ParkOrder_No, parking.Parking_No, query.ParkOrder_ParkAreaNo, query.ParkOrder_ParkAreaName, query.ParkOrder_CarNo, query.ParkOrder_CarCardType, query.ParkOrder_CarCardTypeName, query.ParkOrder_CarType, query.ParkOrder_CarTypeName, query.ParkOrder_EnterTime.Value, query.ParkOrder_EnterPasswayNo, query.ParkOrder_EnterPasswayName);
                detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                detail.OrderDetail_EnterAdminAccount = lgAdmin?.Admins_Account;
                detail.OrderDetail_EnterAdminName = lgAdmin?.Admins_Name;
                detail.orderdetail_EnterRemark = query.ParkOrder_EnterRemark;

                evt.ControlEvent_Status = 3;
                evt.ControlEvent_AdminAccount = lgAdmin?.Admins_Account;
                evt.ControlEvent_AdminName = lgAdmin?.Admins_Name;
                evt.ControlEvent_OkTime = DateTime.Now;
                evt.ControlEvent_ParkOrderNo = model.ParkOrder_No;
                evt.ControlEvent_CarNo = model.ParkOrder_CarNo;

                //var payData = carparking.Charge.Calc.GetChargeByCar(model, evt.ControlEvent_Time, null);
                //evt.ControlEvent_Money = payData?.orderamount ?? 0;
                BLL.ParkOrder.EpParkOrder(ref model, null);

                var details = new List<Model.OrderDetail> { detail };
                var res = BLL.ControlEvent.Handle(evt, (model, details), out string errmsg);
                if (res > 0)
                {
                    var ds = new Model.API.PushResultParse.ControlEvent()
                    {
                        Item1 = evt,
                        Item2 = model,
                        Item3 = details
                    };
                    BLL.PushEvent.EnterCar(parking.Parking_Key, model);
                    BLL.PushEvent.CarFollowUpdate(
                        parking.Parking_Key
                        , evt.ControlEvent_No
                        , evt.ControlEvent_CarNo
                        , evt.ControlEvent_ParkOrderNo
                        , evt.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , evt.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , BLL.CarFollowUpdate_Status.BindOrder
                        , lgAdmin.Admins_Name
                        , evt.ControlEvent_BigImg ?? ""
                        , evt.ControlEvent_Video ?? ""
                        , evt.ControlEvent_Remark
                        , "0");

                    BLL.SystemLogs.AddLog(lgAdmin, "新增停车记录", $"新增停车记录成功:{TyziTools.Json.ToString((model, detail), true)}");
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.SystemLogs.AddLog(lgAdmin, "新增停车记录", $"新增停车记录失败:{jsonModel}.{errmsg}");
                    return ResOk(false, errmsg);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmin, "新增停车记录", $"新增停车记录异常:{jsonModel}");
                return ResOk(false, $"保存失败：{ex.Message}");
            }
        }

        public async Task<IActionResult> GetOrderList(int pageIndex, int pageSize, string conditionParam, string ControlEvent_No)
        {
            try
            {
                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                if (model == null) { return ResOk(false, "参数错误"); }

                if (!string.IsNullOrEmpty(model.code))
                {
                    lgAdmin = DataCache.Admin.Get(model.code);
                }

                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    return ResOk(false, "无权限处理");

                Model.ControlEvent conEvt = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (conEvt == null || conEvt.ControlEvent_Time == null) return ResOk(false, "事件不存在");

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and ParkOrder_EnterTime<='{conEvt.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")}' ");
                sqlwhere.Append($" and ParkOrder_StatusNo NOT IN (199,201,204) ");//过滤 预入场、已出场、欠费出场 的停车订单
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");

                if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                    sqlwhere.Append($" and ParkOrder_EnterPasswayNo = @ParkOrder_EnterPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                    sqlwhere.Append($" and ParkOrder_OutPasswayNo = @ParkOrder_OutPasswayNo ");

                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime between @ParkOrder_EnterTime0 AND @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime <= @ParkOrder_EnterTime1 ");

                var parameters = new
                {
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? $"%{model.ParkOrder_CarNo}%" : null,
                    ParkOrder_EnterPasswayNo = model.ParkOrder_EnterPasswayNo,
                    ParkOrder_OutPasswayNo = model.ParkOrder_OutPasswayNo,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder._GetList<Model.ParkOrder>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;

                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                oModel.code = -1;
                oModel.msg = ex.Message;
                return Ok(oModel.ParseJson());
            }
        }

        public async Task<IActionResult> BindOrder(string code, string ControlEvent_No, string ParkOrder_No)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) { return ResOk(false, "事件不存在"); }
                else if (model.ControlEvent_Status == 2) { return ResOk(false, "事件已处理"); }
                else if (model.ControlEvent_Status == 3) { return ResOk(false, "事件已处理"); }
                else if (model.ControlEvent_Status == 4) { return ResOk(false, "事件已处理"); }

                var ret = BLL.ControlEvent.bindOrder(ref model, ParkOrder_No, out var order, out var detail, out var errmsg);
                if (!ret) return ResOk(false, errmsg);

                var po = order.Item1 ?? new Model.ParkOrder();
                var payData = carparking.Charge.Calc.GetChargeByCar(order.Item1, model.ControlEvent_Time, null, null, false, "", "", null, order.Item2, po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow ? true : false);

                model.ControlEvent_AdminAccount = lgAdmin?.Admins_Account;
                model.ControlEvent_AdminName = lgAdmin?.Admins_Name;
                model.ControlEvent_Money = payData?.orderamount ?? 0;
                var res = BLL.ControlEvent.Handle(model, order, out errmsg);
                if (res > 0)
                {
                    BLL.UserLogs.AddLog(lgAdmin, "跟车事件关联订单", $"跟车事件关联订单成功:{TyziTools.Json.ToString((model), true)}");
                    if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                        //关闭弹窗
                        PasswayConfirmReleaseUtil.RemoveResultByOrderNo(model.ControlEvent_ParkOrderNo);
                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                    }

                    //计费详情
                    BLL.CommonBLL.CreateCalcDetail(payData, order.Item1);

                    var ds = new Model.API.PushResultParse.ControlEvent()
                    {
                        Item1 = model,
                        Item2 = order.Item1,
                        Item3 = order.Item2
                    };
                    //Push(Model.API.PushAction.Edit, ds, new List<Model.SentryHost>(), "carfollowhandle", dataType: DataTypeEnum.ControlEvent, Desc: $"绑定{model.ControlEvent_CarNo}");

                    BLL.PushEvent.CarFollowUpdate(
                        parking.Parking_Key
                        , model.ControlEvent_No
                        , model.ControlEvent_CarNo
                        , model.ControlEvent_ParkOrderNo
                        , model.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , model.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , BLL.CarFollowUpdate_Status.BindOrder
                        , lgAdmin?.Admins_Name
                        , model.ControlEvent_BigImg ?? ""
                        , model.ControlEvent_Video ?? ""
                        , model.ControlEvent_Remark
                        , model.ControlEvent_Money.Value.ToString());

                    return ResOk(true, "关联订单成功");
                }
                else
                {
                    return ResOk(false, errmsg);
                }
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 忽略事件
        /// </summary>
        /// <param name="ControlEvent_No"></param>
        /// <param name="ControlEvent_Remark">备注</param>
        /// <returns></returns>
        public async Task<IActionResult> IgnoreControlEvent(string code, string ControlEvent_No, string ControlEvent_Remark)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) { return ResOk(false, "事件不存在"); }
                if (model.ControlEvent_Status == 1) { return ResOk(true, "处理成功"); }
                if (model.ControlEvent_Status == 4) return ResOk(false, "事件已缴费");

                var ret = BLL.ControlEvent.Ingore(ref model, ControlEvent_Remark, out var order, out var errmsg);
                if (!ret) return ResOk(false, errmsg);

                model.ControlEvent_AdminAccount = lgAdmin?.Admins_Account;
                model.ControlEvent_AdminName = lgAdmin?.Admins_Name;

                var res = BLL.ControlEvent.Handle(model, order, out errmsg);
                if (res > 0)
                {
                    if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                    }

                    var ds = new Model.API.PushResultParse.ControlEvent()
                    {
                        Item1 = model,
                        Item2 = order.Item1,
                        Item3 = order.Item2
                    };

                    //Push(Model.API.PushAction.Edit, ds, new List<Model.SentryHost>(), "carfollowhandle", dataType: DataTypeEnum.ControlEvent, Desc: $"忽略{model.ControlEvent_CarNo}");

                    BLL.PushEvent.CarFollowUpdate(
                        parking.Parking_Key
                        , model.ControlEvent_No
                        , model.ControlEvent_CarNo ?? ""
                        , model.ControlEvent_ParkOrderNo ?? ""
                        , (model.ControlEvent_Time == null ? "" : model.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss"))
                        , model.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , BLL.CarFollowUpdate_Status.Ignore
                        , lgAdmin?.Admins_Name
                        , model.ControlEvent_BigImg ?? ""
                        , model.ControlEvent_Video ?? ""
                        , model.ControlEvent_Remark
                        , "0");

                    BLL.UserLogs.AddLog(lgAdmin, "忽略跟车", $"忽略跟车成功:{TyziTools.Json.ToString((model), true)}");

                    return ResOk(true, "处理成功");
                }
                else
                {
                    return ResOk(false, errmsg);
                }
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 处理倒车事件
        /// </summary>
        /// <param name="ControlEvent_No"></param>
        /// <returns></returns>
        public async Task<IActionResult> CompleteBackEvent(string code, string ControlEvent_No)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) { return ResOk(false, "事件不存在"); }
                if (model.ControlEvent_Status == 5) { return ResOk(true, "处理成功"); }
                if (model.ControlEvent_Status == 4) return ResOk(false, "事件已缴费");

                Model.API.apiBackCar apiModel = TyziTools.Json.ToObject<Model.API.apiBackCar>(System.Web.HttpUtility.UrlDecode(model.ControlEvent_Content));

                var device = BLL.Device.GetEntity(model.ControlEvent_DeviceNo);
                if (device == null) return ResOk(false, "相机信息不存在");
                var passway = BLL.Passway.GetEntity(device.Device_PasswayNo);
                if (passway == null) return ResOk(false, "车道信息不存在");

                List<Model.ParkOrder> orders = new List<Model.ParkOrder>();
                List<Model.OrderDetail> details = new List<Model.OrderDetail>();
                BLL.BackCar.BackHandle(ref apiModel, ref orders, ref details, out var errmsg);

                if (orders != null && orders.Count > 0)
                {
                    var res = BLL.ParkOrder.CarInComplete(orders, details);
                    if (res > 0)
                    {
                        if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                        {
                            CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                            //关闭弹窗
                            PasswayConfirmReleaseUtil.RemoveResultByOrderNo(model.ControlEvent_ParkOrderNo);
                            //刷新弹窗
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                        }

                        var order = orders.First();
                        BLL.BackCar.Add(apiModel, device, order, passway, out var backCar);

                        model.ControlEvent_Status = 5;
                        BLL.ControlEvent.Handle(model, lgAdmin, order);

                        _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                        {
                            BLL.PushEvent.CarBack(parking.Parking_Key, backCar.BackCar_No, backCar.BackCar_CarNo, backCar.BackCar_OrderNo, backCar.BackCar_GateType == 0 ? "2" : "1", backCar.BackCar_Time.Value.ToString("yyyy-MM-dd HH:mm:ss"), backCar.BackCar_PasswayNo, backCar.BackCar_Remark);

                            if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out)
                            {
                                //发送异常出场到智慧停车平台
                                BLL.PushEvent.OutCar(parking.Parking_Key, order, order.ParkOrder_Remark);
                                BLL.PushEvent.SendParkSpace(parking.Parking_No);
                            }
                            else if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                            {
                                BLL.PushEvent.CarOrderStatus(parking.Parking_Key, order.ParkOrder_CarNo, order.ParkOrder_No, "200", apiModel.time, order.ParkOrder_Remark);
                            }

                            //收到线下软件上传的订单后，转发给其他岗亭
                            if (AppBasicCache.IsSendTcp) Library.PushTools.SendToClient(Model.API.PushAction.Edit, null, (orders, details), parking.Parking_Secret, "carin", DataTypeEnum.InParkRecord, $"倒车事件{order?.ParkOrder_CarNo}");
                            return Task.CompletedTask;
                        });

                        BLL.UserLogs.AddLog(lgAdmin, "确认倒车", $"确认倒车成功:{TyziTools.Json.ToString((model), true)}");

                        return ResOk(true, "倒车处理订单成功", parking.Parking_No);
                    }
                    else
                    {
                        return ResOk(false, "倒车处理订单失败", parking.Parking_No);
                    }
                }
                else
                {
                    return ResOk(true, "未生成入场订单，无需处理", parking.Parking_No);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(null, "倒车处理订单异常", ex.ToString());
                return ResOk(false, $"倒车处理订单失败：{ex.Message}", "");
            }
        }

        /// <summary>
        /// 忽略倒车事件
        /// </summary>
        /// <param name="ControlEvent_No"></param>
        /// <returns></returns>
        public async Task<IActionResult> IngoreBackEvent(string code, string ControlEvent_No)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) return ResOk(false, "事件不存在");
                if (model.ControlEvent_Type != 3) return ResOk(false, "不是倒车事件");
                if (model.ControlEvent_Status == 1) return ResOk(true, "处理成功");
                if (model.ControlEvent_Status == 5) return ResOk(false, "事件已处理");
                if (model.ControlEvent_Status == 4) return ResOk(false, "事件已缴费");

                model.ControlEvent_Status = 1;
                model.ControlEvent_OkTime = DateTime.Now;
                model.ControlEvent_AdminAccount = lgAdmin?.Admins_Account;
                model.ControlEvent_AdminName = lgAdmin?.Admins_Name;

                var res = BLL.ControlEvent._UpdateByModelByNo(model);
                if (res > 0)
                {
                    if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(null, model.ControlEvent_ParkOrderNo, CloseNoInPark: false);
                    }

                    BLL.UserLogs.AddLog(lgAdmin, "忽略倒车", $"忽略倒车成功:{TyziTools.Json.ToString((model), true)}");
                    return ResOk(true, "处理成功");
                }
                else
                {
                    return ResOk(false, "处理失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, "忽略倒车事件", ex.ToString());
                return ResOk(false, ex.Message);
            }
        }

        public async Task<IActionResult> GetCheckOrderList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                if (model == null)
                {
                    oModel.code = -1;
                    oModel.msg = "参数错误";
                    return Ok(oModel.ParseJson());
                }
                if (!string.IsNullOrEmpty(model?.code))
                {
                    lgAdmin = DataCache.Admin.Get(model.code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return ResOk(false, "无权限");
                    }
                }

                StringBuilder sqlwhere = new StringBuilder();

                if (!string.IsNullOrEmpty(model.ParkOrder_No))
                    sqlwhere.Append($" and ParkOrder_No=@ParkOrder_No ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                    sqlwhere.Append($" and ParkOrder_EnterPasswayNo=@ParkOrder_EnterPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                    sqlwhere.Append($" and ParkOrder_OutPasswayNo=@ParkOrder_OutPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");

                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime between @ParkOrder_EnterTime0 AND @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime <= @ParkOrder_EnterTime1 ");

                if (model.ParkOrder_OutTime0 != null && model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime between @ParkOrder_OutTime0 AND @ParkOrder_OutTime1 ");
                else if (model.ParkOrder_OutTime0 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 ");
                else if (model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime <= @ParkOrder_OutTime1 ");

                var parameters = new
                {
                    ParkOrder_No = model.ParkOrder_No,
                    ParkOrder_EnterPasswayNo = model.ParkOrder_EnterPasswayNo,
                    ParkOrder_OutPasswayNo = model.ParkOrder_OutPasswayNo,
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? "%" + model.ParkOrder_CarNo + "%" : null,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1,
                    ParkOrder_OutTime0 = model.ParkOrder_OutTime0,
                    ParkOrder_OutTime1 = model.ParkOrder_OutTime1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder._GetList<Model.ParkOrder>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;

                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                oModel.code = -1;
                oModel.msg = ex.Message;
                return Ok(oModel.ParseJson());
            }
        }

        public async Task<ActionResult> UploadVideo(string code)
        {
            try
            {
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (lgAdmin == null)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandleEvent.ToString(), true, lgAdmin))
                    {
                        LogManagementMap.WriteToFileException(null, $"事件查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                        return Json(new { Success = 0, Msg = "无权限", Src = "" });
                    }
                }

                if (HttpContext.Request.Form.Files.Count > 0)
                {
                    var formFiles = HttpContext.Request.Form.Files;
                    if (!Utils.IsOnlyExcelFiles(formFiles))
                    {
                        return Json(new { Success = 0, Msg = "只允许上传视频文件", Src = "" });
                    }

                    IFormFile aFile = formFiles[0];
                    string imgPath = BLL.ImageTools.LocalFilePath;
                    string fileExtension = aFile.FileName.Substring(aFile.FileName.LastIndexOf('.') + 1);

                    string filename = $"{Utils.CreateNumber}.{fileExtension ?? "mp4"}";

                    string src = Common.LocalFile.FileOnSaveByDate(aFile, filename, imgPath, !string.IsNullOrEmpty(imgPath));

                    return Json(new { Success = 1, Msg = "上传成功", Src = src });
                }
                else
                {
                    return Json(new { Success = 0, Msg = "请选择视频文件", Src = "" });
                }
            }
            catch (Exception ex)
            {
                return Json(new { Success = 0, Msg = ex.Message, Src = "" });
            }
        }

        #endregion

        #region **【记录查询】

        /// <summary>
        /// 视图
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> Record()
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"加载记录查询页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                return View();
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Record, $"加载记录查询页面异常退回登录页面:[" + ex.Message + "]", SecondIndex.Monitoring);
                return RedirectToAction("Index", "Login");
            }
        }

        #region 停车记录

        /// <summary>
        /// 停车记录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> InParkRecord()
        {
            ViewBag.Parking_Key = AppCache.LoadSentryHostInfo()?.Parking_Key;

            string code = Request.Query["code"];
            if (!string.IsNullOrEmpty(code))
            {
                lgAdmin = DataCache.Admin.Get(code);
            }

            string inouttype = Request.Query["inouttype"];
            if (string.IsNullOrEmpty(inouttype)) inouttype = "0";

            if (inouttype != "1" && inouttype != "2")
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
                    return new EmptyResult();
            }
            else
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandlePreRecord.ToString(), true, lgAdmin))
                    return new EmptyResult();
            }

            ViewBag.InOutType = inouttype;

            return View();
        }

        /// <summary>
        /// 停车记录查询
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetParkOrderList(int pageIndex, int pageSize, string conditionParam, string field, string order, string code = "")
        {
            try
            {
                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                if (model == null)
                {
                    oModel.code = 4;
                    oModel.msg = $"参数异常，请刷新重试";
                    return Ok(oModel.ParseJson());
                }

                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }
                else if (!string.IsNullOrEmpty(model.code))
                {
                    lgAdmin = DataCache.Admin.Get(model.code);
                }

                if (model.PageInOutType != "1" && model.PageInOutType != "2")
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
                        return new EmptyResult();
                }
                else
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.HandlePreRecord.ToString(), true, lgAdmin))
                        return new EmptyResult();
                }

                StringBuilder sqlwhere = new StringBuilder();


                if (model.ParkOrder_StatusNo != null)
                {
                    if (model.ParkOrder_StatusNo != 0)
                        sqlwhere.Append($" and ParkOrder_StatusNo = @ParkOrder_StatusNo ");
                    else
                        sqlwhere.Append($" and ParkOrder_StatusNo='{Model.EnumParkOrderStatus.In}' and ParkOrder_OutType=1 ");
                }

                if (model.ParkOrder_Lock != null)
                    sqlwhere.Append($" and ParkOrder_Lock = @ParkOrder_Lock ");
                if (model.ParkOrder_OutType != null)
                    sqlwhere.Append($" and ParkOrder_OutType = @ParkOrder_OutType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarCardType))
                    sqlwhere.Append($" and ParkOrder_CarCardType = @ParkOrder_CarCardType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarType))
                    sqlwhere.Append($" and ParkOrder_CarType = @ParkOrder_CarType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                    sqlwhere.Append($" and ParkOrder_EnterPasswayNo = @ParkOrder_EnterPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                    sqlwhere.Append($" and ParkOrder_OutPasswayNo = @ParkOrder_OutPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterAdminAccount))
                    sqlwhere.Append($" and ParkOrder_EnterAdminAccount = @ParkOrder_EnterAdminAccount ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutAdminAccount))
                    sqlwhere.Append($" and ParkOrder_OutAdminAccount = @ParkOrder_OutAdminAccount ");

                if (!string.IsNullOrEmpty(model.ParkOrder_ParkAreaNo))
                    sqlwhere.Append($" and ParkOrder_ParkAreaNo = @ParkOrder_ParkAreaNo ");
                if (model.ParkOrder_IsNoInRecord != null)
                    sqlwhere.Append($" and ParkOrder_IsNoInRecord = @ParkOrder_IsNoInRecord ");
                if (model.ParkOrder_IsEpCar != null)
                    sqlwhere.Append($" and ParkOrder_IsEpCar = @ParkOrder_IsEpCar ");


                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 AND ParkOrder_EnterTime< @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime < @ParkOrder_EnterTime1 ");

                if (model.ParkOrder_OutTime0 != null && model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 AND ParkOrder_OutTime< @ParkOrder_OutTime1 ");
                else if (model.ParkOrder_OutTime0 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 ");
                else if (model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime < @ParkOrder_OutTime1 ");


                if (!string.IsNullOrEmpty(model.ParkOrder_No))
                    sqlwhere.Append($" and ParkOrder_No like @ParkOrder_No ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");
                else if (!string.IsNullOrEmpty(model.ParkOrder_CarNo1))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo1 ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutAdminName))
                    sqlwhere.Append($" and ParkOrder_OutAdminName like @ParkOrder_OutAdminName ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarLogo))
                    sqlwhere.Append($" and ParkOrder_CarLogo like @ParkOrder_CarLogo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterAdminName))
                    sqlwhere.Append($" and ParkOrder_EnterAdminName like @ParkOrder_EnterAdminName ");

                bool isSearchOwner = false;
                var ownerNoes = new List<string>();
                if (!string.IsNullOrEmpty(model.ParkOrder_OwnerName))
                {
                    isSearchOwner = true;
                    var owners = BLL.Owner.GetAllEntity("Owner_No", $"Owner_Name like @ParkOrder_OwnerName", new { ParkOrder_OwnerName = "%" + model.ParkOrder_OwnerName.Trim() + "%" });
                    if (owners != null && owners.Count > 0)
                    {
                        ownerNoes = owners.Select(x => x.Owner_No).ToList();
                    }
                }

                if (!string.IsNullOrEmpty(model.ParkOrder_OwnerSpace))
                {
                    var owners = BLL.Owner.GetAllEntity("Owner_No", $"Owner_Space like @ParkOrder_OwnerSpace", new { ParkOrder_OwnerSpace = "%" + model.ParkOrder_OwnerSpace.Trim() + "%" });
                    if (owners != null && owners.Count > 0)
                    {
                        isSearchOwner = true;
                        ownerNoes.AddRange(owners.Select(x => x.Owner_No).ToList());
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(model.ParkOrder_OwnerName)) sqlwhere.Append($" and ParkOrder_OwnerName like @ParkOrder_OwnerName ");
                    }

                    if (ownerNoes.Count == 0)
                    {
                        sqlwhere.Append($" and ParkOrder_OwnerNo='0' ");
                    }
                }

                if (ownerNoes.Count > 0)
                {
                    ownerNoes = ownerNoes.Distinct().ToList();
                    sqlwhere.Append($" and ParkOrder_OwnerNo in @ParkOrder_OwnerNoList ");
                }
                else
                {
                    if (isSearchOwner)
                    {
                        oModel.code = 0;
                        oModel.data = new List<Model.ParkOrder>();
                        oModel.count = 0;
                        return Ok(oModel);
                    }
                }


                field = string.IsNullOrEmpty(field) ? "ParkOrder_EnterTime" : field;
                order = string.IsNullOrEmpty(order) ? "0" : (order == "asc" ? "1" : "0");

                int pageCount = 0, totalRecord = 0;


                object parameters = new
                {
                    ParkOrder_No = !string.IsNullOrEmpty(model.ParkOrder_No) ? "%" + model.ParkOrder_No.Trim() + "%" : null,
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? model.ParkOrder_CarNo.Trim() : null,
                    ParkOrder_CarNo1 = !string.IsNullOrEmpty(model.ParkOrder_CarNo1) ? "%" + model.ParkOrder_CarNo1.Trim() + "%" : null,
                    ParkOrder_OutAdminName = !string.IsNullOrEmpty(model.ParkOrder_OutAdminName) ? "%" + model.ParkOrder_OutAdminName.Trim() + "%" : null,
                    ParkOrder_CarLogo = !string.IsNullOrEmpty(model.ParkOrder_CarLogo) ? "%" + model.ParkOrder_CarLogo.Trim() + "%" : null,
                    ParkOrder_EnterAdminName = !string.IsNullOrEmpty(model.ParkOrder_EnterAdminName) ? "%" + model.ParkOrder_EnterAdminName.Trim() + "%" : null,
                    ParkOrder_OwnerName = !string.IsNullOrEmpty(model.ParkOrder_OwnerName) ? "%" + model.ParkOrder_OwnerName.Trim() + "%" : null,
                    ParkOrder_OwnerNoList = ownerNoes,
                    ParkOrder_OwnerSpace = !string.IsNullOrEmpty(model.ParkOrder_OwnerSpace) ? "%" + model.ParkOrder_OwnerSpace.Trim() + "%" : null,
                    ParkOrder_StatusNo = model.ParkOrder_StatusNo,
                    ParkOrder_Lock = model.ParkOrder_Lock,
                    ParkOrder_OutType = model.ParkOrder_OutType,
                    ParkOrder_CarCardType = !string.IsNullOrEmpty(model.ParkOrder_CarCardType) ? model.ParkOrder_CarCardType.Trim() : null,
                    ParkOrder_CarType = !string.IsNullOrEmpty(model.ParkOrder_CarType) ? model.ParkOrder_CarType.Trim() : null,
                    ParkOrder_EnterPasswayNo = !string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo) ? model.ParkOrder_EnterPasswayNo.Trim() : null,
                    ParkOrder_OutPasswayNo = !string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo) ? model.ParkOrder_OutPasswayNo.Trim() : null,
                    ParkOrder_EnterAdminAccount = !string.IsNullOrEmpty(model.ParkOrder_EnterAdminAccount) ? model.ParkOrder_EnterAdminAccount.Trim() : null,
                    ParkOrder_OutAdminAccount = !string.IsNullOrEmpty(model.ParkOrder_OutAdminAccount) ? model.ParkOrder_OutAdminAccount.Trim() : null,
                    ParkOrder_ParkAreaNo = !string.IsNullOrEmpty(model.ParkOrder_ParkAreaNo) ? model.ParkOrder_ParkAreaNo.Trim() : null,
                    ParkOrder_IsNoInRecord = model.ParkOrder_IsNoInRecord,
                    ParkOrder_IsEpCar = model.ParkOrder_IsEpCar,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0 != null ? model.ParkOrder_EnterTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1 != null ? model.ParkOrder_EnterTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_OutTime0 = model.ParkOrder_OutTime0 != null ? model.ParkOrder_OutTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_OutTime1 = model.ParkOrder_OutTime1 != null ? model.ParkOrder_OutTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                };

                List<Model.ParkOrder> lst = BLL.ParkOrder._GetList<Model.ParkOrder>("*", sqlwhere.ToString(), pageIndex, pageSize, field, Utils.ObjectToInt(order, 0), out pageCount, out totalRecord, commtimeout: 80, parameters: parameters);
                lst.ForEach(x =>
                {
                    string enterimg = LPRTools.GetSentryHostImg(HttpUtility.UrlDecode(x.ParkOrder_EnterImg));
                    if (!string.IsNullOrWhiteSpace(enterimg) && enterimg.Contains(AppBasicCache.SentryHostInfo?.SentryHost_No))
                    {
                        x.ParkOrder_EnterImgPath = enterimg;
                    }

                    string outimg = LPRTools.GetSentryHostImg(HttpUtility.UrlDecode(x.ParkOrder_OutImg));
                    if (!string.IsNullOrWhiteSpace(outimg) && outimg.Contains(AppBasicCache.SentryHostInfo?.SentryHost_No))
                    {
                        x.ParkOrder_OutImgPath = outimg;
                    }
                });
                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                oModel.code = 4;
                oModel.msg = $"停车记录查询异常";
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetParkOrderList, $"停车记录查询异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return Ok(oModel.ParseJson());
            }
        }

        /// <summary>
        /// 删除停车订单
        /// </summary>
        /// <param name="ParkOrder_NoArray"></param>
        /// <returns></returns>
        public async Task<IActionResult> DeleteParkOrder(string ParkOrder_NoArray)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString()))
                    return ResOk(false, "无权限");

                List<string> ParkOrder_NoList = TyziTools.Json.ToObject<List<string>>(ParkOrder_NoArray);
                if (ParkOrder_NoList == null) return ResOk(false, "请选择");

                List<Model.ParkOrder> parkOrders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in @ParkOrder_NoList", parameters: new { ParkOrder_NoList = ParkOrder_NoList });
                //parkOrders?.RemoveAll(x => x.ParkOrder_StatusNo != Model.EnumParkOrderStatus.In);//过滤不是在车场内的记录
                if (parkOrders?.Count == 0)
                {
                    return ResOk(false, "没有需要删除的记录");
                }

                var res = BLL.ParkOrder.CloseList(parkOrders, parameters: new { ParkOrder_NoList = ParkOrder_NoList });
                if (res > 0)
                {
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                    {
                        await Task.Delay(1);
                        var parking = GetParking();
                        parkOrders.ForEach(async item =>
                        {
                            BLL.ParkApi.DelParkOrder(item);
                            if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                            {
                                item.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                                BLL.PushEvent.CloseCar(parking.Parking_Key, item, "场内关闭");
                            }

                            await Task.Delay(100);
                        });
                        BLL.PushEvent.SendParkSpace(parking.Parking_No);
                    });

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Delete, $"删除停车记录：{ParkOrder_NoArray}", SecondIndex.Monitoring);
                    return ResOk(true, "删除成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Delete, $"删除停车记录失败：{ParkOrder_NoArray}", SecondIndex.Monitoring);
                    return ResOk(false, "删除失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Delete, $"删除停车记录异常：{ex.ToString()}", SecondIndex.Monitoring);
                return ResOk(false, "删除失败");
            }
        }

        /// <summary>
        /// 预入出场处理
        /// </summary>
        /// <param name="ParkOrder_NoArray">停车订单编号</param>
        /// <param name="InOutType">1-变更为预入场，2-变更为预出场</param>
        /// <returns></returns>
        public async Task<IActionResult> HandlePreRecord(string ParkOrder_NoArray, string InOutType)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.HandlePreRecord.ToString(), true, lgAdmin))
                    return ResOk(false, "无权限");

                if (InOutType != "1" && InOutType != "2")
                {
                    return ResOk(false, "参数错误");
                }

                List<string> ParkOrder_NoList = TyziTools.Json.ToObject<List<string>>(ParkOrder_NoArray);
                if (ParkOrder_NoList == null) return ResOk(false, "请选择");

                List<Model.ParkOrder> parkOrders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in @ParkOrder_NoList", parameters: new { ParkOrder_NoList = ParkOrder_NoList });
                //parkOrders?.RemoveAll(x => x.ParkOrder_StatusNo != Model.EnumParkOrderStatus.In);//过滤不是在车场内的记录
                if (parkOrders?.Count == 0)
                {
                    return ResOk(false, "没有需要变更的记录");
                }

                List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo in @OrderNoList", parameters: new { OrderNoList = parkOrders.Select(x => x.ParkOrder_No).ToList() });

                if (InOutType == "1")
                {
                    parkOrders.ForEach(x => { x.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In; });
                    details.ForEach(x =>
                    {
                        if (x.OrderDetail_StatusNo == 199) x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                    });
                }
                else if (InOutType == "2")
                {
                    parkOrders.ForEach(x => { x.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out; });
                    details.ForEach(x =>
                    {
                        if (x.OrderDetail_StatusNo == 200) x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                    });
                }

                var res = BLL.OrderDetail.UpdateByList(parkOrders, details);
                if (res)
                {
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                    {
                        await Task.Delay(1);
                        var parking = GetParking();
                        parkOrders.ForEach(async item =>
                        {
                            BLL.MiddlewareApi.UpdateOrderDetail(item, details.FindAll(x => x.OrderDetail_ParkOrderNo == item.ParkOrder_No));

                            if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                            {
                                BLL.PushEvent.EnterCar(parking.Parking_Key, item);
                            }
                            else if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out)
                            {
                                BLL.PushEvent.OutCar(parking.Parking_Key, item);
                            }

                            await Task.Delay(100);
                        });
                        BLL.PushEvent.SendParkSpace(parking.Parking_No);
                    });

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.HandlePreRecord, $"预入出场处理列表(变更{(InOutType == "1" ? "入场" : "出场")}：{ParkOrder_NoArray}", SecondIndex.Monitoring);
                    return ResOk(true, "处理成功");
                }
                else
                {
                    return ResOk(false, "处理失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.HandlePreRecord, $"预入出场处理异常：{ex.ToString()}", SecondIndex.Monitoring);
                return ResOk(false, "处理失败");
            }
        }

        #endregion

        #region 特殊车辆放行记录

        /// <summary>
        /// 特殊车辆放行记录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> SpecialCarPass()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
            {
                LogManagementMap.WriteToFileException(null, $"加载记录查询页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.Parking_Key = AppCache.LoadSentryHostInfo()?.Parking_Key;

            return View();
        }

        /// <summary>
        /// 查询特殊车辆放行列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public async Task<IActionResult> GetSpecialCarPassList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"特殊车辆查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                Model.PassRecordWhere model = Utils.ClearModelRiskSQL<Model.PassRecordWhere>(conditionParam);
                if (model == null)
                {
                    return ResOk(false, "参数错误");
                }

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and PassRecord_Type=2");

                if (!string.IsNullOrEmpty(model.PassRecord_PasswayNo))
                    sqlwhere.Append($" and PassRecord_PasswayNo = @PassRecord_PasswayNo ");
                if (!string.IsNullOrEmpty(model.PassRecord_Account))
                    sqlwhere.Append($" and PassRecord_Account = @PassRecord_Account ");
                if (!string.IsNullOrEmpty(model.PassRecord_Name))
                    sqlwhere.Append($" and PassRecord_Name like @PassRecord_Name ");

                if (model.PassRecord_PassTime1 != null && model.PassRecord_PassTime2 != null)
                    sqlwhere.Append($" and PassRecord_PassTime between @PassRecord_PassTime1 AND @PassRecord_PassTime2 ");
                else if (model.PassRecord_PassTime1 != null)
                    sqlwhere.Append($" and PassRecord_PassTime >= @PassRecord_PassTime1 ");
                else if (model.PassRecord_PassTime2 != null)
                    sqlwhere.Append($" and PassRecord_PassTime <= @PassRecord_PassTime2 ");

                object parameters = new
                {
                    PassRecord_PasswayNo = model.PassRecord_PasswayNo,
                    PassRecord_Account = model.PassRecord_Account,
                    PassRecord_Name = !string.IsNullOrEmpty(model.PassRecord_Name) ? "%" + model.PassRecord_Name + "%" : null,
                    PassRecord_PassTime1 = model.PassRecord_PassTime1 != null ? model.PassRecord_PassTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    PassRecord_PassTime2 = model.PassRecord_PassTime2 != null ? model.PassRecord_PassTime2.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                };
                int pageCount = 0, totalRecord = 0;
                List<Model.PassRecord> lst = BLL.BaseBLL._GetList<Model.PassRecord>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);
                lst.ForEach(x => { x.PassRecord_ImgPath = LPRTools.GetSentryHostImg(HttpUtility.UrlDecode(x.PassRecord_Img)); });

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetSpecialCarList, $"特殊车辆查询异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                oModel.code = 4;
                oModel.msg = "异常错误";
            }

            return Ok(oModel.ParseJson());
        }

        #endregion

        #region 人工开闸记录

        /// <summary>
        /// 人工开闸记录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> AbnorOrder()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
            {
                LogManagementMap.WriteToFileException(null, $"加载记录查询页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            ViewBag.Parking_Key = AppCache.LoadSentryHostInfo()?.Parking_Key;

            return View();
        }

        /// <summary>
        /// 查询异常放行列表(人工开闸)
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public async Task<IActionResult> GetAbnorOrderList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"异常放行记录查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                Model.PassRecordWhere model = Utils.ClearModelRiskSQL<Model.PassRecordWhere>(conditionParam);

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and (PassRecord_Type='1' OR PassRecord_Type='3' OR PassRecord_Type='4' OR PassRecord_Type='6')");

                if (!string.IsNullOrEmpty(model.PassRecord_PasswayNo))
                    sqlwhere.Append($" and PassRecord_PasswayNo = @PassRecord_PasswayNo ");
                if (!string.IsNullOrEmpty(model.PassRecord_Account))
                    sqlwhere.Append($" and PassRecord_Account = @PassRecord_Account ");
                if (!string.IsNullOrEmpty(model.PassRecord_Name))
                    sqlwhere.Append($" and PassRecord_Name like @PassRecord_Name ");

                if (model.PassRecord_PassTime1 != null && model.PassRecord_PassTime2 != null)
                    sqlwhere.Append($" and PassRecord_PassTime between @PassRecord_PassTime1 AND @PassRecord_PassTime2 ");
                else if (model.PassRecord_PassTime1 != null)
                    sqlwhere.Append($" and PassRecord_PassTime >= @PassRecord_PassTime1 ");
                else if (model.PassRecord_PassTime2 != null)
                    sqlwhere.Append($" and PassRecord_PassTime <= @PassRecord_PassTime2 ");

                object parameters = new
                {
                    PassRecord_PasswayNo = model.PassRecord_PasswayNo,
                    PassRecord_Account = model.PassRecord_Account,
                    PassRecord_Name = !string.IsNullOrEmpty(model.PassRecord_Name) ? "%" + model.PassRecord_Name + "%" : null,
                    PassRecord_PassTime1 = model.PassRecord_PassTime1 != null ? model.PassRecord_PassTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    PassRecord_PassTime2 = model.PassRecord_PassTime2 != null ? model.PassRecord_PassTime2.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.PassRecord> lst = BLL.BaseBLL._GetList<Model.PassRecord>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);
                lst.ForEach(x =>
                {
                    string img = LPRTools.GetSentryHostImg(HttpUtility.UrlDecode(x.PassRecord_Img));
                    if (img.Contains(AppBasicCache.SentryHostInfo?.SentryHost_No))
                        x.PassRecord_ImgPath = img;
                });
                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetAbnorOrderList, $"异常放行查询异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                oModel.code = 4;
                oModel.msg = "异常错误";
            }

            return Ok(oModel);
        }

        #endregion

        #region 缴费记录

        /// <summary>
        /// 缴费记录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> PayOrder()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
            {
                LogManagementMap.WriteToFileException(null, $"加载记录查询页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 缴费记录查询
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        /// <returns></returns>
        public async Task<IActionResult> GetPayOrderList(int pageIndex, int pageSize, string conditionParam, string field, string order)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"缴费记录查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                var parking = GetParking();
                Model.PayOrderWhere model = Utils.ClearModelRiskSQL<Model.PayOrderWhere>(conditionParam);

                StringBuilder sqlwhere = new StringBuilder();

                if (model.PayOrder_Status != null)
                    sqlwhere.Append($" and PayOrder_Status = @PayOrder_Status ");
                if (!string.IsNullOrEmpty(model.PayOrder_Account))
                    sqlwhere.Append($" and PayOrder_Account = @PayOrder_Account ");
                if (model.PayOrder_PayTypeCode != null)
                    sqlwhere.Append($" and PayOrder_PayTypeCode = @PayOrder_PayTypeCode ");
                if (model.PayOrder_PayType != null)
                    sqlwhere.Append($" and PayOrder_PayType = @PayOrder_PayType ");
                if (!string.IsNullOrEmpty(model.PayOrder_OrderTypeNo))
                {
                    if (model.PayOrder_OrderTypeNo != "0")
                        sqlwhere.Append($" and PayOrder_OrderTypeNo = @PayOrder_OrderTypeNo ");
                    else
                        sqlwhere.Append($" and PayOrder_OrderTypeNo NOT IN (5901,5902,5903,5904,5905,5910,5919) ");
                }

                if (!string.IsNullOrEmpty(model.PayOrder_CarCardTypeNo))
                    sqlwhere.Append($" and PayOrder_CarCardTypeNo=@PayOrder_CarCardTypeNo ");
                if (!string.IsNullOrEmpty(model.PayOrder_CarTypeNo))
                    sqlwhere.Append($" and PayOrder_CarTypeNo=@PayOrder_CarTypeNo ");

                if (model.PayOrder_PayedTime0 != null && model.PayOrder_PayedTime1 != null)
                    sqlwhere.Append($" and PayOrder_PayedTime between @PayOrder_PayedTime0 AND @PayOrder_PayedTime1 ");
                else if (model.PayOrder_PayedTime0 != null)
                    sqlwhere.Append($" and PayOrder_PayedTime >= @PayOrder_PayedTime0 ");
                else if (model.PayOrder_PayedTime1 != null)
                    sqlwhere.Append($" and PayOrder_PayedTime <= @PayOrder_PayedTime1 ");

                if (!string.IsNullOrEmpty(model.PayOrder_No))
                    sqlwhere.Append($" and PayOrder_No like @PayOrder_No ");
                if (!string.IsNullOrEmpty(model.PayOrder_CarNo))
                    sqlwhere.Append($" and PayOrder_CarNo like @PayOrder_CarNo ");
                if (!string.IsNullOrEmpty(model.PayOrder_OperatorName))
                    sqlwhere.Append($" and PayOrder_OperatorName like @PayOrder_OperatorName ");
                if (!string.IsNullOrEmpty(model.PayOrder_ParkOrderNo))
                    sqlwhere.Append($" and PayOrder_ParkOrderNo like @PayOrder_ParkOrderNo ");

                object parameters = new
                {
                    PayOrder_Status = model.PayOrder_Status,
                    PayOrder_Account = model.PayOrder_Account,
                    PayOrder_PayTypeCode = model.PayOrder_PayTypeCode,
                    PayOrder_OrderTypeNo = model.PayOrder_OrderTypeNo,
                    PayOrder_PayType = model.PayOrder_PayType,
                    PayOrder_No = !string.IsNullOrEmpty(model.PayOrder_No) ? "%" + model.PayOrder_No + "%" : null,
                    PayOrder_CarNo = !string.IsNullOrEmpty(model.PayOrder_CarNo) ? "%" + model.PayOrder_CarNo + "%" : null,
                    PayOrder_OperatorName = !string.IsNullOrEmpty(model.PayOrder_OperatorName) ? "%" + model.PayOrder_OperatorName + "%" : null,
                    PayOrder_ParkOrderNo = !string.IsNullOrEmpty(model.PayOrder_ParkOrderNo) ? "%" + model.PayOrder_ParkOrderNo + "%" : null,
                    PayOrder_PayedTime0 = model.PayOrder_PayedTime0 != null ? model.PayOrder_PayedTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    PayOrder_PayedTime1 = model.PayOrder_PayedTime1 != null ? model.PayOrder_PayedTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    PayOrder_CarCardTypeNo = model.PayOrder_CarCardTypeNo,
                    PayOrder_CarTypeNo = model.PayOrder_CarTypeNo,
                };
                int pageCount = 0, totalRecord = 0;
                field = string.IsNullOrEmpty(field) ? "PayOrder_PayedTime" : field;
                order = string.IsNullOrEmpty(order) ? "0" : (order == "asc" ? "1" : "0");

                List<Model.PayOrderQuery> lst = BLL.PayOrder.GetListExt("*", sqlwhere.ToString(), pageIndex, pageSize, field, Utils.ObjectToInt(order, 0), out pageCount, out totalRecord, 80, parameters: parameters);

                #region 关联车辆车主信息

                var carnos = lst?.Where(a => a.PayOrder_OrderTypeNo != Model.EnumOrderType.OwnerCharge.ToString())?.Select(x => x.PayOrder_CarNo).ToList();
                if (carnos != null && carnos.Count > 0)
                {
                    var carList = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerNo,Car_OwnerName,Car_OwnerSpace", $"Car_CarNo in ('{string.Join("','", carnos)}')");
                    carList?.ForEach(car =>
                    {
                        lst?.FindAll(x => x.PayOrder_CarNo == car.Car_CarNo)?.ForEach(x =>
                        {
                            x.PayOrder_OwnerNo = car.Car_OwnerNo;
                            x.PayOrder_OwnerName = car.Car_OwnerName;
                            //x.PayOrder_OwnerSpace = car.Car_OwnerSpace;
                        });
                    });
                }

                #endregion

                #region 车位续期关联车辆车主信息

                var owners = lst?.Where(a => a.PayOrder_OrderTypeNo == Model.EnumOrderType.OwnerCharge.ToString())?.Select(x => x.PayOrder_CarNos?.Replace(",", "','")).ToList();
                if (owners != null && owners.Count > 0)
                {
                    var ownerList = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerNo,Car_OwnerName,Car_OwnerSpace", $"Car_CarNo in ('{string.Join("','", owners)}')");
                    ownerList?.ForEach(owner =>
                    {
                        lst?.FindAll(x => x.PayOrder_CarNos == null ? false : x.PayOrder_CarNos.Contains(owner.Car_CarNo))?.ForEach(x =>
                        {
                            if (!string.IsNullOrWhiteSpace(x.PayOrder_CarNos))
                            {
                                x.PayOrder_CarNo = x.PayOrder_CarNos;
                            }
                        });
                    });
                }

                #endregion

                #region 读取车牌类型&车牌颜色

                var cards = BLL.CarCardType.GetAllEntity("*", $"CarCardType_ParkNo='{parking.Parking_No}'");
                var carType = BLL.CarType.GetAllEntity("*", $"CarType_ParkNo='{parking.Parking_No}'");
                lst?.ForEach(item =>
                {
                    item.PayOrder_CarTypeNo = carType?.Find(x => x.CarType_No == item.PayOrder_CarTypeNo)?.CarType_Name ?? "";
                    item.PayOrder_CarCardTypeNo = cards?.Find(x => x.CarCardType_No == item.PayOrder_CarCardTypeNo)?.CarCardType_Name ?? "";
                });

                #endregion

                #region 总合计金额
                DateTime? dataTime = model.PayOrder_PayedTime0 ?? model.PayOrder_PayedTime1 ?? DateTime.Now;
                StringBuilder totalString = new StringBuilder();
                totalString.Append("sum(PayOrder_Money) as PayOrder_Money,");
                totalString.Append("sum(PayOrder_PayedMoney) as PayOrder_PayedMoney,");
                totalString.Append("sum(PayOrder_DiscountMoney) as PayOrder_DiscountMoney,");
                totalString.Append("sum(PayOrder_StoredMoney) as PayOrder_StoredMoney,");
                totalString.Append("sum(PayOrder_SelfMoney) as PayOrder_SelfMoney,");
                totalString.Append("sum(PayOrder_OutReduceMoney) as PayOrder_OutReduceMoney");
                Model.PayOrder total = BLL.PayOrder.GetEntity(totalString.ToString(), $"1=1 {sqlwhere.ToString()}", model.DataType, dataTime, parameters);

                #endregion

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                oModel.msg = TyziTools.Json.ToString(total);
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                oModel.code = 4;
                oModel.msg = $"缴费记录查询异常：{ex.Message}";
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPayOrderList, $"缴费记录查询异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return Ok(oModel.ParseJson());
            }
        }

        /// <summary>
        /// 缴费详情
        /// </summary>
        /// <returns></returns>
        public ActionResult PayOrderDetail()
        {
            return View();
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        public async Task<IActionResult> GetPayOrderByNo(string PayOrder_No)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }
                //if (!await checkWinPower("WinFormMonitor", PowerEnum.QueryRecord.ToString()))
                //    return new EmptyResult();

                Model.PayOrder model = BLL.PayOrder._GetEntityByNo(new Model.PayOrder(), PayOrder_No);

                List<Model.CouponRecord> couponList = null;
                if (model != null)
                {
                    if (!string.IsNullOrEmpty(model.PayOrder_CouponRecordNo))
                        couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $"CouponRecord_Status=1 and CouponRecord_No in('{string.Join("','", model.PayOrder_CouponRecordNo.Split(','))}')  ");
                }

                List<Model.PayPart> paypartList = null;
                if (model != null)
                {
                    paypartList = BLL.BaseBLL._GetAllEntity(new Model.PayPart(), "*", $"PayPart_Status=1 and PayPart_PayOrderNo='{model.PayOrder_No}'  ");
                }

                return ResOk(true, "查询成功", new { model = model, coupon = couponList, paypart = paypartList });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPayOrderByNo, $"订单详情获取异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 自助缴费明细
        /// </summary>
        /// <returns></returns>
        public ActionResult CashDetail()
        {
            return View();
        }

        /// <summary>
        /// 查询支付订单自助缴费明细列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public async Task<IActionResult> GetCashDetailList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }

                Model.Paymethod model = Utils.ClearModelRiskSQL<Model.Paymethod>(conditionParam);
                if (model == null)
                {
                    oModel.code = 4;
                    oModel.msg = "异常错误";
                    return Ok(oModel);
                }
                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" AND Paymethod_OrderNo=@Paymethod_OrderNo");
                object parameters = new
                {
                    Paymethod_OrderNo = model.Paymethod_OrderNo,
                };
                int pageCount = 0, totalRecord = 0;
                List<Model.Paymethod> lst = BLL.BaseBLL._GetList<Model.Paymethod>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCashDetailList, $"查询支付订单自助缴费明细列表发生异常:" + ex.ToString(), SecondIndex.Monitoring);

                oModel.code = 4;
                oModel.msg = "异常错误";
            }

            return Ok(oModel);
        }

        #endregion

        /// <summary>
        /// 视图
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> EditInParkRecord()
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifyRecord.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"加载修改记录页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                return View();
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EditInParkRecord, $"加载修改记录页面异常退回登录页面:[" + ex.Message + "]", SecondIndex.Monitoring);
                return RedirectToAction("Index", "Login");
            }
        }

        #endregion

        #region **【退出登录】【后台管理免密登录】【播放设置】【字体设置】【关于岗亭软件】

        /// <summary>
        /// 后台管理免密登录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetLoginAuthCode(int page = 0, string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = GetParking();
                var res = BLL.ParkApi.GetLoginCode(parking.Parking_No, lgAdmin.Admins_Account, lgAdmin.Admins_Pwd, "1", page == 1 ? "ControlEvent" : "");
                if (res.success)
                {
                    string code2 = JsonConvert.DeserializeObject<string>(res.data);
                    var url = $"http://{AppBasicCache.Ip}:{AppSettingConfig.SiteDomain_WebPort}/Login/WMLogin?code={code2}";
                    return ResOk(true, "获取授权码成功", url);
                }
                else
                {
                    return ResOk(false, res.errmsg);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetLoginAuthCode, $"获取后台免密登录异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 退出登录
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> LogOut(string code = "")
        {
            try
            {
                if (!string.IsNullOrEmpty(code)) lgAdmin = DataCache.Admin.Get(code);
                if (!await checkPower(lgAdmin, "", false) && lgAdmin == null) { return ResOk(false, "无权限"); }

                //删除客户端cookie
                if (lgAdmin != null)
                {
                    Model.Admins admins = new Model.Admins();
                    admins.Admins_ID = lgAdmin.Admins_ID;
                    admins.Admins_LoginTime = "";
                    BLL.Admins.UpdateByModel(admins);

                    var old = BLL.BaseBLL._GetEntityByWhere(new Model.WorkShift(), "*", $"WorkShift_Status=1 and WorkShift_OffAccount='{lgAdmin.Admins_Account}'");
                    if (old != null)
                    {
                        BLL.BaseBLL._DeleteByNo<Model.WorkShift>(old.WorkShift_No);
                    }

                    BLL.UserLogs.AddLog(lgAdmin, "退出登录", "未交班，记录数据：" + (old != null ? TyziTools.Json.ToString(old) : "无"));
                }

                Cookies cookies = new Cookies();
                cookies.delCookie(Sessions.BSAdminSession);
                Sessions.BSDelAdminSession(lgAdmin);
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Login, $"{lgAdmin?.Admins_Account}退出登录", SecondIndex.Monitoring);

                string loginIp = HttpHelper.GetLoginIp(HttpContext, AppBasicCache.Ip);
                DogUtil.InitDevice(loginIp, null, lgAdmin?.Admins_Account);

                return ResOk(true, "退出岗亭登录");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Login, $"退出岗亭登录异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 播放设置
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> VideoPlayMode()
        {
            if (!await checkPower(lgAdmin, "VideoPlayMode"))
            {
                LogManagementMap.WriteToFileException(null, $"加载播放设置页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 字体设置
        /// </summary>
        /// <returns></returns>
        public IActionResult FrontSetting()
        {
            return View();
        }

        /// <summary>
        /// 关于岗亭软件
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> MonitoringVersion()
        {
            if (!await checkPower(lgAdmin, "Index")) { return new EmptyResult(); }

            var parking = GetParking();
            ViewData["AppVersion"] = parking.AppVersion;
            ViewBag.ApiVersion = AppSettingConfig.ApiVersion;
            ViewBag.ApiVersion_FB = AppSettingConfig.ApiVersion_FB;
            ViewBag.Dog = DogCommon.strRevision + (DogCommon.bAuthorization == true ? "_专用" : "");
            string sdog = DogCommon.strDogType + "-" + DogCommon.strSoftType + "-" + DogCommon.strDogVideoType + "-" + DogCommon.strDogNum + "-" + DogCommon.strDogPrintNum + "-" + DogCommon.strTmpInTime;
            ViewBag.DogMsg = $"{(string.IsNullOrEmpty(sdog) ? "未读取加密狗信息" : sdog)}";
            ViewData["SDKTitle"] = "";
            ViewData["SDKVersion"] = "";
            return View();
        }

        /// <summary>
        /// 是否抢登检测[WS断开重连处理]
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> ScrambleCheck()
        {
            try
            {
                string code = Request.Query["code"];
                if (!string.IsNullOrEmpty(code))
                {
                    lgAdmin = DataCache.Admin.Get(code);
                }

                if (!await checkPower(lgAdmin, writetimeout: false))
                {
                    return ResOk(true, "退出登录");
                }
                else
                {
                    return ResOk(false, "WS重连");
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"[{lgAdmin?.Admins_Account}]抢登处理异常");
                return ResOk(true, ex.Message);
            }
        }
        #endregion

        #region **【交班下班】

        /// <summary>
        /// 交班下班
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> OffWorkChange()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.ChangeShifts.ToString(), true, lgAdmin))
            {
                LogManagementMap.WriteToFileException(null, $"加载交接班页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 获取交班数据
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetWorkShiftAnData(string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var parking = GetParking();
                var dt = DateTimeHelper.GetNowTime();
                var model = BLL.WorkShift.CreateWorkShift(parking.Parking_No, lgAdmin.Admins_Account, lgAdmin.Admins_Name, Convert.ToDateTime(lgAdmin.Admins_LoginTime), dt);

                model.WorkShift_OffName = lgAdmin.Admins_Name;
                model.WorkShift_OffAccount = lgAdmin.Admins_Account;
                model.WorkShift_OnTime = Convert.ToDateTime(lgAdmin.Admins_LoginTime);
                model.WorkShift_OffTime = dt;

                Model.WorkShiftAnalysis analysis = TyziTools.Json.ToObject<Model.WorkShiftAnalysis>(model.WorkShift_AnalysisJson);

                List<Model.OrderCoutLabel> OrderCoutLabel = new List<Model.OrderCoutLabel>();
                List<Model.PayModeLabel> PayModeLabel = new List<Model.PayModeLabel>();
                if (analysis != null)
                {
                    OrderCoutLabel = analysis.OrderCoutData();
                    PayModeLabel = analysis.PayModeData();
                }

                return ResOk(true, "", new { model = model, OrderCoutLabel = OrderCoutLabel, PayModeLabel = PayModeLabel });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetWorkShiftAnData, $"交班数据获取异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取可换班的账号
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetAdminsSelect(string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                var data = BLL.Admins.GetAllEntity("*", "Admins_Enable='1'");
                //排除当前账号
                data.RemoveAll(x => x.Admins_Account == lgAdmin?.Admins_Account);

                return ResOk(true, "ok", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetAdminsSelect, $"交班账号获取异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 确定交班
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> ChangeWorkShift(string jsonModel, string code = "")
        {
            try
            {
                if (!string.IsNullOrEmpty(code)) lgAdmin = DataCache.Admin.Get(code);
                if (!await checkPower(lgAdmin, "", false) && lgAdmin == null) { return ResOk(false, "无权限"); }

                JObject obj = Utils.ClearModelRiskSQL(jsonModel);
                if (obj["start"] == null || obj["end"] == null)
                {
                    return ResOk(false, "未能获取交班时间");
                }

                if (obj["onaccount"] == null)
                {
                    return ResOk(false, "未能获取接班人账号");
                }

                string onaccount = Convert.ToString(obj["onaccount"]);
                string onpwd = Convert.ToString(obj["onpwd"]);

                string md5Pwd = Utils.MD5Encrypt(onpwd + Utils.passwordMD5String, Encoding.UTF8);

                var onAdmin = BLL.Admins.GetEntity("*", onaccount, md5Pwd);
                if (onAdmin == null) return ResOk(false, "账号被禁用或账号与密码信息不匹配");

                DateTime.TryParse(obj["start"].ToString(), out var startTime);
                DateTime.TryParse(obj["end"].ToString(), out var endTime);

                var old = BLL.BaseBLL._GetEntityByWhere(new Model.WorkShift(), "*", $"WorkShift_Status=1 and WorkShift_OffAccount='{lgAdmin.Admins_Account}'");

                var parking = GetParking();
                var model = BLL.WorkShift.CreateWorkShift(parking.Parking_No, lgAdmin?.Admins_Account, lgAdmin?.Admins_Name, startTime, endTime);
                model.WorkShift_No = old == null ? Utils.CreateNumber : old.WorkShift_No;
                model.WorkShift_OffName = lgAdmin?.Admins_Name;
                model.WorkShift_OffAccount = lgAdmin?.Admins_Account;
                model.WorkShift_OnTime = startTime;
                model.WorkShift_OffTime = endTime;
                model.WorkShift_OnAccount = onAdmin.Admins_Account;
                model.WorkShift_OnName = onAdmin.Admins_Name;
                model.WorkShift_AddTime = DateTimeHelper.GetNowTime();
                model.WorkShift_Status = 2;

                var res = BLL.WorkShift._Insert(model);
                if (res > 0)
                {
                    var old2 = BLL.BaseBLL._GetEntityByWhere(new Model.WorkShift(), "*", $"WorkShift_Status=1 and WorkShift_OffAccount='{onAdmin.Admins_Account}'");
                    var ret = BLL.WorkShift._Insert(new Model.WorkShift()
                    {
                        WorkShift_No = old2 != null ? old2.WorkShift_No : Utils.CreateNumber,
                        WorkShift_OnTime = DateTimeHelper.GetNowTime(),
                        WorkShift_Status = 1,
                        WorkShift_OffAccount = onAdmin.Admins_Account,
                        WorkShift_OffName = onAdmin.Admins_Name
                    });
                    if (ret != 1)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{onAdmin.Admins_Account}]写入登录记录失败，从而影响换班时间");
                    }

                    string loginIp = HttpHelper.GetLoginIp(HttpContext, AppBasicCache.Ip);
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ChangeWorkShift, $"[{loginIp}]交班登录:{lgAdmin.Admins_Account}->{onAdmin.Admins_Account}", SecondIndex.Monitoring);

                    #region 更换登录账号

                    Model.AdminSession session = JsonConvert.DeserializeObject<Model.AdminSession>(JsonConvert.SerializeObject(onAdmin));
                    var powergroup = BLL.PowerGroup.GetEntity(onAdmin.Admins_PowerNo);
                    session.Admins_IPAddress = loginIp;
                    session.token = Guid.NewGuid().ToString("N");
                    session.PowerGroup_ID = powergroup.PowerGroup_ID;
                    session.PowerGroup_Name = powergroup.PowerGroup_Name;
                    session.PowerGroup_Value = powergroup.PowerGroup_Value;
                    session.Admins_SentryHostNo = AppBasicCache.GetSentryHost()?.SentryHost_No;
                    session.Admins_LoginTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");

                    bool bl = Sessions.BSSetAdminSession(session);
                    LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{onAdmin.Admins_Account}]写入登录缓存：[{bl}]");
                    if (Response != null)
                        Response.Cookies.Append(Sessions.BSAdminSession, Cookies.Encryption(session.token + "," + session.Admins_Account),
                               new CookieOptions()
                               {
                                   IsEssential = true, // 这个 Cookie 必须设置，不受用户隐私设置影响
                                   Expires = DateTimeHelper.GetNowTime().AddDays(365), // 1年过期
                               });
                    else
                        HttpHelper.Response.Cookies.Append(Sessions.BSAdminSession, Cookies.Encryption(session.token + "," + session.Admins_Account.ToString()),
                               new CookieOptions()
                               {
                                   IsEssential = true, // 这个 Cookie 必须设置，不受用户隐私设置影响
                                   Expires = DateTimeHelper.GetNowTime().AddDays(365), // 1年过期
                               });
                    LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{onAdmin.Admins_Account}]写入cookie");

                    #region 将登陆账号的车道权限写入到WS缓存

                    try
                    {
                        List<string> lanes = new List<string>();
                        if (!string.IsNullOrWhiteSpace(session.Admins_PasswayNo))
                            lanes = JsonConvert.DeserializeObject<List<string>>(session.Admins_PasswayNo);
                        WebSocketHandler.AcccountLaneCache.TryRemove(session.Admins_Account, out var d2);
                        WebSocketHandler.AcccountLaneCache.TryAdd(session.Admins_Account, lanes);
                        WebSocketHandler.AcccountLaneCache.TryRemove(lgAdmin?.Admins_Account, out var d1);
                    }
                    catch (Exception e)
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ChangeWorkShift, $"[{onAdmin.Admins_Account}]交班WS缓存异常：[{e.ToString()}]", SecondIndex.Monitoring);
                    }

                    #endregion

                    #endregion

                    BLL.PushEvent.PutWorkHandover(parking.Parking_Key, model);

                    if (obj["token"] != null)
                    {
                        DataCache.Admin.Del(obj["token"].ToString());
                    }

                    if (!string.IsNullOrWhiteSpace(session.Admins_PasswayNo))
                    {
                        try
                        {
                            var passList = TyziTools.Json.ToObject<string[]>(session.Admins_PasswayNo)?.ToList();
                            List<Model.AdminsLink> alList = new List<Model.AdminsLink>();
                            passList?.ForEach(x =>
                            {
                                if (!string.IsNullOrEmpty(x))
                                {
                                    string key = x.ToString();
                                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetPasswayadmin, key, onAdmin);
                                    alList.Add(new Model.AdminsLink() { AdminsLink_Account = session.Admins_Account, AdminsLink_PasswayNo = key, AdminsLink_LoginTime = DateTime.Now });
                                }
                            });
                            if (alList.Count > 0) BLL.BaseBLL._AddOrUpdateModel(alList);
                        }
                        catch (Exception e)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"登录[{lgAdmin?.Admins_Account}]写入车道关联账号信息异常:" + e.ToString());
                        }
                    }

                    AppBasicCache.CurrentAdmins = session;

                    // 换班登录成功后自动解除账号锁定状态
                    string lockKey = $"LoginLock_{onaccount}";
                    Cache.Web.LocalCache.Del(lockKey);
                    LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{onAdmin.Admins_Account}]换班登录成功，解除账号锁定状态");

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ChangeWorkShift, $"交班成功:{lgAdmin.Admins_Account}->{onAdmin.Admins_Account}", SecondIndex.Monitoring);
                    return ResOk(true, "交班成功", obj["login"]);
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ChangeWorkShift, $"交班失败:{lgAdmin.Admins_Account}->{onAdmin.Admins_Account}", SecondIndex.Monitoring);
                    return ResOk(false, "交班失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ChangeWorkShift, $"确认交班异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        public IActionResult OffWorkView()
        {
            return View();
        }

        #endregion

        #region **【同步黑白名单】

        /// <summary>
        /// 同步黑白名单
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> WhiteRecord()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.DownLoadCarList.ToString(), true, lgAdmin))
            {
                LogManagementMap.WriteToFileException(null, $"加载同步黑白名单页面,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        /// <summary>
        /// 获取车场黑白名单列表
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetCarWhiteList()
        {
            try
            {
                if (!await checkPower(lgAdmin, "GetCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"黑白名单记录查询,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                var parking = GetParking();
                //获取白名单列表
                List<Model.Car> whiteCarlist = BLL.Car.GetAllEntity("*", $"Car_ParkingNo='{parking.Parking_No}'");

                //获取黑名单列表
                List<Model.BlackList> blackList = BLL.BaseBLL._GetAllEntity(new Model.BlackList(), "*", $"BlackList_ParkNo='{parking.Parking_No}'");

                //注销的白名单列表
                List<Model.CarUnbound> unCarList = BLL.BaseBLL._GetAllEntity(new Model.CarUnbound(), "*", $"CarUnbound_ParkNo='{parking.Parking_No}'");

                var data = new
                {
                    whiteCarlist = whiteCarlist,
                    whiteEnableCount = whiteCarlist?.FindAll(x => x.Car_EnableOffline == 1).Count ?? 0,
                    whiteLayoutCount = whiteCarlist?.FindAll(x => x.Car_EnableOffline == 0).Count ?? 0,
                    unCarList = unCarList,
                    unCarCount = unCarList?.Count ?? 0,
                    blackList = blackList,
                    blackEnableCount = blackList?.FindAll(x => x.Blacklist_Status == 1).Count ?? 0,
                    blackLayoutCount = blackList?.FindAll(x => x.Blacklist_Status == 0).Count ?? 0,
                };
                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCarWhiteList, $"黑白名单列表获取异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 【弃用】同步车辆黑白名单
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> SyncCarWhiteList()
        {
            try
            {
                if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"黑白名单同步,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                var parking = GetParking();
                string SentryHost_No = AppBasicCache.SentryHostInfo?.SentryHost_No;

                List<CameraCarList.SyncCarListResult> result = new List<CameraCarList.SyncCarListResult>();

                //获取白名单列表
                List<Model.Car> whiteCarlist = BLL.Car.GetAllEntity("*", $"");
                for (int i = 0; i < whiteCarlist.Count; i++)
                {
                    var car = whiteCarlist[i];
                    List<SyncCarListResult> res = new List<SyncCarListResult>();
                    string msg = string.Empty;
                    var d = await carparking.SentryBox.Device.CameraCarList.DownloadCarForList(SentryHost_No, car, res, msg);
                    car = d.Item2;
                    res = d.Item3;
                    msg = d.Item4;
                    result.AddRange(res);
                }

                //获取黑名单列表
                List<Model.BlackList> blackList = BLL.BaseBLL._GetAllEntity(new Model.BlackList(), "*", $"");
                for (int i = 0; i < blackList.Count; i++)
                {
                    var black = blackList[i];
                    List<SyncCarListResult> res = new List<SyncCarListResult>();
                    string msg = string.Empty;
                    var d = await carparking.SentryBox.Device.CameraCarList.DownloadBlackForList(SentryHost_No, black, res, msg);
                    black = d.Item2;
                    res = d.Item3;
                    msg = d.Item4;
                    result.AddRange(res);
                }

                //注销的白名单列表
                List<Model.CarUnbound> unCarList = BLL.BaseBLL._GetAllEntity(new Model.CarUnbound(), "*", $"");
                for (int i = 0; i < unCarList.Count; i++)
                {
                    var car = new Model.Car
                    {
                        Car_CarNo = unCarList[i].CarUnbound_CarNo,
                        Car_Status = 3,
                        Car_ParkingNo = unCarList[i].CarUnbound_ParkNo,
                        Car_EnableOffline = 1,
                        Car_BeginTime = DateTime.Now.AddDays(-100),
                        Car_EndTime = DateTime.Now.AddDays(-100),
                    };
                    if (whiteCarlist.Exists(x => x.Car_CarNo == car.Car_CarNo)) continue; //车牌号在白名单中则不执行注销

                    List<SyncCarListResult> res = new List<SyncCarListResult>();
                    string msg = string.Empty;

                    var d = await carparking.SentryBox.Device.CameraCarList.DeleteCarForList(SentryHost_No, car, res, msg);
                    res = d.Item3;
                    msg = d.Item4;

                    result.AddRange(res);
                }

                BLL.UserLogs.AddLog(lgAdmin, $"同步黑白名单指令发送成功", $"{SentryHost_No},{AppBasicCache.SentryHostInfo?.SentryHost_Name}");
                return ResOk(true, "同步黑白名单成功", result);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"黑白名单列表同步异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(true, "同步黑白名单指令发送异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 同步白名单
        /// </summary>
        /// <param name="WhiteRecord_IDArray"></param>
        /// <returns></returns>
        public async Task<IActionResult> Send(string WhiteRecord_IDArray)
        {
            try
            {
                if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"一键同步白名单,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (string.IsNullOrEmpty(WhiteRecord_IDArray)) return ResOk(false, "请选择");

                var WhiteRecord_IDList = TyziTools.Json.ToObject<List<string>>(WhiteRecord_IDArray);
                if (WhiteRecord_IDList == null) return ResOk(false, "请选择");

                var recordList = BLL.BaseBLL._GetAllEntity(new Model.WhiteRecord(), "*", $"WhiteRecord_ID in @WhiteRecord_IDList", new { WhiteRecord_IDList = WhiteRecord_IDList });
                var sentrys = BLL.SentryHost.GetAllEntity();
                var devices = BLL.Device.GetAllEntity("Device_No,Device_SentryHostNo", $"Device_No in @recordList and Device_SentryHostNo!=''", parameters: new { recordList = recordList.Select(x => x.WhiteRecord_DeviceNo).ToList() });

                sentrys.ForEach(sentry =>
                {
                    var devices_sub = devices.Where(x => x.Device_SentryHostNo == sentry.SentryHost_No).ToList();
                    var devicenoes = devices_sub.Select(x => x.Device_No).ToList();

                    var records = recordList.Where(x => devicenoes.Contains(x.WhiteRecord_DeviceNo)).ToList();
                    double count = records.Count;
                    for (int i = 1; i <= Math.Ceiling(Utils.ObjectToDouble(count / 10, 0)); i++)
                    {
                        var num = 10 > records.Count ? records.Count : 10;
                        var takeList = records.Take(num).ToList();
                        records.RemoveRange(0, num);

                        HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), takeList, "syncwhiterecord");

                        Task.Delay(10).Wait();
                    }
                });

                var devicenoes = devices.Select(x => x.Device_No).ToList();
                var recordList2 = recordList.Where(x => !devicenoes.Contains(x.WhiteRecord_DeviceNo)).ToList();
                if (recordList2.Count > 0)
                {
                    recordList2.ForEach(x =>
                    {
                        x.WhiteRecord_Remark = "设备未关联岗亭，无法同步处理";
                        x.WhiteRecord_DeviceStatus = 0;
                        x.WhiteRecord_Status = 0;
                    });
                    BLL.BaseBLL._AddOrUpdateModel(recordList2);
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"同步白名单处理成功:{string.Join(",", WhiteRecord_IDList)}", SecondIndex.Monitoring);
                return ResOk(true, "处理成功");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"同步白名单异常：{ex.ToString()}", SecondIndex.Monitoring);
                return ResOk(false, "处理失败");
            }
        }

        /// <summary>
        /// 一键同步白名单
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> SyncWhite()
        {
            try
            {
                if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"一键同步白名单,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (AppBasicCache.GetCar.Count == 0)
                {
                    return ResOk(false, "未找到车辆登记数据，无需同步");
                }

                if (CustomThreadPool.SyncTaskPool.IsNoTaskRunning2("SyncWhite"))
                {
                    BLL.UserLogs.AddLog(lgAdmin, "一键同步白名单", "一键同步白名单");
                    _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                    {
                        HandleCommon.SyncCarWhite(AppBasicCache.GetOwner.Values.ToList(), AppBasicCache.SentryHostInfo?.SentryHost_No, AppBasicCache.GetCar.Values.ToList());
                        return Task.CompletedTask;
                    });
                }
                else
                {
                    return ResOk(false, "当前已有一键同步白名单任务正在执行，请稍候再试");
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, "一键同步白名单", SecondIndex.Monitoring);
                return ResOk(true, "一键同步白名单");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"一键同步白名单异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(true, "一键同步白名单指令发送异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 删除记录
        /// </summary>
        /// <param name="NoArray"></param>
        /// <returns></returns>
        public async Task<IActionResult> Delete(string NoArray)
        {
            try
            {
                if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"删除记录白名单记录,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                List<string> NoList = TyziTools.Json.ToObject<List<string>>(NoArray);
                if (NoList == null) return ResOk(false, "请选择");


                string idString = string.Join(",", NoList);
                var res = BLL.BaseBLL._ExceteBySql($"DELETE FROM whiterecord WHERE WhiteRecord_ID in @idString ", new { idString = NoList });
                if (res > 0)
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"删除白名单记录：{idString}", SecondIndex.Monitoring);
                    return ResOk(true, "删除成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"删除白名单记录失败：{idString}", SecondIndex.Monitoring);
                    return ResOk(false, "删除失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"删除白名单记录异常：{ex.ToString()}", SecondIndex.Monitoring);
                return ResOk(false, "删除失败");
            }
        }

        /// <summary>
        /// 一键注销黑白名单
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> ClearAll()
        {
            try
            {
                if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"一键注销黑白名单,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (CustomThreadPool.SyncTaskPool.IsNoTaskRunning2("ClearAllWhite"))
                {
                    BLL.UserLogs.AddLog(lgAdmin, "一键注销黑白名单", "一键注销黑白名单");
                    _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                    {
                        HandleCommon.SyncClearCarBlcakWhite(AppBasicCache.SentryHostInfo?.SentryHost_No);
                        return Task.CompletedTask;
                    });
                }
                else
                {
                    return ResOk(false, "当前已有一键注销黑白名单任务正在执行，请稍候再试");
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, "一键注销黑白名单", SecondIndex.Monitoring);
                return ResOk(true, "一键注销黑白名单");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"一键注销黑白名单异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(true, "一键注销黑白名单指令发送异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 一键同步黑名单
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> SyncBlack()
        {
            try
            {
                if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
                {
                    LogManagementMap.WriteToFileException(null, $"一键同步黑名单,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (AppBasicCache.GetBlackList.Values.Count == 0)
                {
                    return ResOk(false, "当前未设置黑名单，无需同步黑名单");
                }

                if (CustomThreadPool.SyncTaskPool.IsNoTaskRunning2("SyncBlack"))
                {
                    BLL.UserLogs.AddLog(lgAdmin, "一键同步黑名单", "一键同步黑名单");
                    _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                    {
                        HandleCommon.SyncCarBlcak(AppBasicCache.GetBlackList.Values.ToList());
                        return Task.CompletedTask;
                    });
                }
                else
                {
                    return ResOk(false, "当前已有一键同步黑名单任务正在执行，请稍候再试");
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, "一键同步黑名单", SecondIndex.Monitoring);
                return ResOk(true, "一键同步黑名单");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.SyncCarWhiteList, $"一键同步黑名单异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(true, "一键同步黑名单指令发送异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取车场白名单统计
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> GetCarWhiteCount()
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }

                var enableCount = BLL.BaseBLL._Query<int>("SELECT count(1) FROM whiterecord WHERE WhiteRecord_DeviceStatus=1");
                var cancelCount = BLL.BaseBLL._Query<int>("SELECT count(1) FROM whiterecord WHERE WhiteRecord_DeviceStatus=2");
                var syncCount = BLL.BaseBLL._Query<int>("SELECT count(1) FROM whiterecord WHERE WhiteRecord_DeviceStatus!=1 and WhiteRecord_DeviceStatus!=2  or WhiteRecord_DeviceStatus is NULL");
                var data = new
                {
                    enableCount = enableCount,
                    cancelCount = cancelCount,
                    syncCount = syncCount,
                };
                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCarWhiteCount, $"获取车场白名单统计异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 白名单记录列表
        /// </summary>
        public async void GetList(int pageIndex, int pageSize, string conditionParam)
        {
            if (!await checkPower(lgAdmin, "SyncCarWhiteList"))
            {
                LogManagementMap.WriteToFileException(null, $"查询白名单记录,[{lgAdmin?.Admins_Account}]账号检测失败");
                await Response.WriteAsync(oModel.ParseJson());
                return;
            }

            await Response.WriteAsync(SearchList(pageIndex, pageSize, conditionParam).ParseJson());
        }

        /// <summary>
        /// 查询白名单记录列表
        /// </summary>
        private Model.PageResult SearchList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                string sqlwhere = "";
                Model.WhiteRecordExt obj = Utils.ClearModelRiskSQL<Model.WhiteRecordExt>(conditionParam);

                if (!string.IsNullOrEmpty(obj.WhiteRecord_CarNo))
                    sqlwhere += $" AND WhiteRecord_CarNo like @WhiteRecord_CarNo ";
                if (!string.IsNullOrEmpty(obj.WhiteRecord_DeviceNo))
                    sqlwhere += $" AND WhiteRecord_DeviceNo like @WhiteRecord_DeviceNo ";
                if (!string.IsNullOrEmpty(obj.WhiteRecord_Remark))
                    sqlwhere += $" AND WhiteRecord_Remark like @WhiteRecord_Remark ";

                if (!string.IsNullOrEmpty(obj.WhiteRecord_DeviceName))
                {
                    var devices = BLL.Device.GetAllEntity("Device_No,Device_Name", $"Device_SentryHostNo!='' and Device_Name like @WhiteRecord_DeviceName ", parameters: new { WhiteRecord_DeviceName = obj.WhiteRecord_DeviceName });
                    if (devices.Count > 0)
                        sqlwhere += $" AND WhiteRecord_DeviceNo in ('{string.Join("','", devices.Select(x => x.Device_No))}') ";
                    else
                        sqlwhere += $" AND WhiteRecord_DeviceStatus='100' ";
                }

                if (obj.WhiteRecord_DeviceStatus != null)
                {
                    if (obj.WhiteRecord_DeviceStatus == 0)
                        sqlwhere += $" AND IFNULL(WhiteRecord_DeviceStatus,0)=0 ";
                    else
                        sqlwhere += $" AND WhiteRecord_DeviceStatus=@WhiteRecord_DeviceStatus ";
                }

                if (obj.WhiteRecord_Status != null)
                {
                    if (obj.WhiteRecord_Status == 0)
                        sqlwhere += $" AND IFNULL(WhiteRecord_Status,0)=0 ";
                    else
                        sqlwhere += $" AND WhiteRecord_Status=@WhiteRecord_Status ";
                }

                if (obj.WhiteRecord_Type != null)
                {
                    sqlwhere += $" AND WhiteRecord_Type=@WhiteRecord_Type ";
                }

                object parameters = new
                {
                    WhiteRecord_CarNo = !string.IsNullOrEmpty(obj.WhiteRecord_CarNo) ? "%" + obj.WhiteRecord_CarNo + "%" : null,
                    WhiteRecord_DeviceNo = !string.IsNullOrEmpty(obj.WhiteRecord_DeviceNo) ? "%" + obj.WhiteRecord_DeviceNo + "%" : null,
                    WhiteRecord_Remark = !string.IsNullOrEmpty(obj.WhiteRecord_Remark) ? "%" + obj.WhiteRecord_Remark + "%" : null,
                    WhiteRecord_DeviceName = !string.IsNullOrEmpty(obj.WhiteRecord_DeviceName) ? "%" + obj.WhiteRecord_DeviceName + "%" : null,
                    WhiteRecord_DeviceStatus = obj.WhiteRecord_DeviceStatus,
                    WhiteRecord_Status = obj.WhiteRecord_Status,
                    WhiteRecord_Type = obj.WhiteRecord_Type,
                };


                int pageCount = 0, totalRecord = 0;
                List<Model.WhiteRecord> lst = BLL.BaseBLL._GetList<Model.WhiteRecord>("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);
                var data = TyziTools.Json.ToObject<List<Model.WhiteRecordExt>>(TyziTools.Json.ToString(lst));
                if (data.Count > 0)
                {
                    var devices = BLL.Device.GetAllEntity("Device_No,Device_Name", "Device_SentryHostNo!=''");
                    data.ForEach(x => { x.WhiteRecord_DeviceName = devices?.Find(m => m.Device_No == x.WhiteRecord_DeviceNo)?.Device_Name; });
                }

                oModel.code = 0;
                oModel.data = data;
                oModel.count = totalRecord;
                return oModel;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCarWhiteList, $"查询白名单记录异常:[" + ex.Message + "]", SecondIndex.Monitoring);

                oModel.code = 4;
                oModel.msg = ex.Message;
            }

            return oModel;
        }

        #endregion

        #region **【确认入场】【识别车牌】【开关闸】

        /// <summary>
        /// 识别车牌
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        public async Task<IActionResult> PlateRecognition(string passwayno, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.ManualTrigger.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"识别车牌,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayno, out var passwayModel))
                {
                    return ResOk(false, $"识别车牌失败，未找到车道信息");
                }

                if (passwayModel.Passway_Type == 1)
                {
                    var device = BLL.Device.GetEntityByPasswayNo(passwayno);
                    if (device == null) return ResOk(false, $"识别车牌失败，未找到识别相机");
                    //手动向相机发送指令
                    var result = await CameraController.TriggerAsync(device.Device_IP);
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CapturePictures, $"[{lgAdmin?.Admins_Account}][{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}]触发识别,结果:{(result ? "触发成功" : "识别失败")}", SecondIndex.Monitoring);
                    if (result)
                        return ResOk(true, "触发成功");
                    else
                        return ResOk(false, "识别失败");
                }
                else
                {
                    return ResOk(false, "无车牌识别相机");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CapturePictures, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} 触发识别异常:[" + ex.Message + "]", SecondIndex.Monitoring);
                return ResOk(false, "识别车牌异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 开关闸
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <param name="code">1-开闸,0-关闸</param>
        /// <returns></returns>
        public async Task<IActionResult> OpenCloseGate(string passwayno, int code, string token = "")
        {
            bool isLongOpen = code == 2; //是否道闸常开
            if (DataCache.Admin.Get(token) == null && !await checkWinPower("WinFormMonitor", PowerEnum.GateSwitch.ToString(), false, lgAdmin))
            {
                if (isLongOpen)
                {
                    if (!await checkWinPower("WinFormMonitor", PowerEnum.GateCancel.ToString()))
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {(code == 2 ? "道闸常开" : "")} {(code == 0 ? "关闸" : "开闸")},[{lgAdmin?.Admins_Account}]账号检测失败", SecondIndex.Monitoring);
                        return new EmptyResult();
                    }
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {(code == 2 ? "道闸常开" : "")} {(code == 0 ? "关闸" : "开闸")},[{lgAdmin?.Admins_Account}]账号检测失败", SecondIndex.Monitoring);
                    return new EmptyResult();
                }
            }

            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
            {
                if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                {
                    if (!CameraGlobal.IsEmergency)
                    {
                        return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                    }
                }
            }

            if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

            GateCmd.ActionEnum action = code == 0 ? GateCmd.ActionEnum.Close : GateCmd.ActionEnum.Open;
            var evtname = action == GateCmd.ActionEnum.Close ? "关闸" : "开闸";
            try
            {
                if (!AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayno, out var passwayModel))
                {
                    return ResOk(false, $"人工{evtname}失败，未找到车道信息");
                }

                //判断是否非机动车道
                if (passwayModel.Passway_Type is (2 or 3))
                {
                    var (isSuccess, Message) = await ControllerHelper.SendOpenGateByMotoAsync(passwayModel.Passway_No,
                        passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo,
                        isLongOpen ? NonMotorizedOperation.Openlong : (action == GateCmd.ActionEnum.Close ? NonMotorizedOperation.Close : NonMotorizedOperation.Open), null, null, true, null);

                    if (isSuccess)
                    {
                        var ttvoice = action == GateCmd.ActionEnum.Open ? "请通行" : string.Empty;
                        if (isLongOpen)
                        {
                            var device = ControllerHelper.GetMotoControler(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
                            if (device.Model is ControllerModel c)
                            {
                                if (!c.GateLongOpen)
                                {
                                    ttvoice = string.Empty;
                                }
                            }
                        }

                        //判断是否需要播报语音
                        if (!string.IsNullOrWhiteSpace(ttvoice))
                        {
                            await ControllerHelper.SendOpenGateByMotoAsync(passwayModel.Passway_No,
                                passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo,
                                NonMotorizedOperation.Other, ttvoice, ttvoice, true, null);

                            #region 非机动车车道控制器关联相机播报
                            try
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"开始处理非机动车车道控制器关联相机播报，车道号：{passwayModel.Passway_No}，播报内容：{ttvoice}");

                                //查找车道对应的非机动车控制器
                                var controller = DevicePool.Instance.GetAllDevices()
                                    .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));

                                if (controller != null && controller is NonMotorizedOf Non && Non.Model.IsConnected)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"找到非机动车控制器，设备号：{Non.Model.DeviceNo}，连接状态：{Non.Model.IsConnected}，车道号：{passwayModel.Passway_No}");

                                    //从缓存读取设备的最新信息
                                    var device = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                                    if (device != null)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"获取到设备缓存信息，设备号：{device.Device_No}，关联相机号：{device.Device_FNo}，相机使用状态：{device.Device_CameraUsage}");

                                        //判断是否关联识别相机并播报
                                        if (!string.IsNullOrWhiteSpace(device.Device_FNo) && device.Device_FNo != "0" && device.Device_CameraUsage == 1)
                                        {
                                            //获取关联识别相机
                                            var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == device.Device_FNo);
                                            if (camera != null)
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"非机动车控制器关联相机播报：车道[{passwayModel.Passway_No}] -> 相机[{camera.Device_IP}:{camera.Device_Com}]，播报内容：{ttvoice}");

                                                var voice = Passthrough485Util.InstantDisplay(ttvoice);
                                                _ = CameraController.SendDataBy485Async(camera.Device_IP, (SerialIndexType)(camera.Device_Com ?? 0), voice, nameof(Passthrough485Util.InstantDisplay));
                                            }
                                            else
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"未找到关联识别相机，相机设备号：{device.Device_FNo}，车道号：{passwayModel.Passway_No}");
                                            }
                                        }
                                        else
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"设备未配置关联相机或相机未启用，设备号：{device.Device_No}，关联相机号：{device.Device_FNo}，相机使用状态：{device.Device_CameraUsage}");
                                        }
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"未找到非机动车控制器设备缓存信息，设备号：{Non.Model.DeviceNo}，车道号：{passwayModel.Passway_No}");
                                    }
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"未找到对应的非机动车控制器或设备未连接，车道号：{passwayModel.Passway_No}，出场车道号：{passwayModel.Passway_OutNo}");
                                }
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFileException(ex, $"非机动车车道控制器关联相机播报失败，车道号：{passwayModel?.Passway_No}，播报内容：{ttvoice}");
                            }
                            #endregion
                        }

                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{code}]人工{evtname}成功", SecondIndex.Monitoring);

                        return ResOk(true, Message);
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{code}]人工{evtname}失败", SecondIndex.Monitoring);
                        return ResOk(false, Message);
                    }
                }
                else
                {
                    var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice, $"人工{evtname}失败");
                    if (acionRes != null)
                    {
                        return acionRes;
                    }

                    var camera = CameraController.GetCamera(mainDevice.Device_IP);
                    if (camera == null)
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{code}]人工{evtname}失败，未找到相机", SecondIndex.Monitoring);
                        return ResOk(false, $"人工{evtname}失败，未找到相机");
                    }

                    #region 来车事件通知

                    if (passwayModel.Passway_IsBackCar == 1 && passwayModel.Passway_EnableBoard == 1 && code == 1)
                    {
                        string scode1 = Common.Utils.CreateAuthStr(8).ToUpper();
                        string orderno = $"{DateTimeHelper.GetNowTime():yyyyMMddHHmmssfff}-{scode1}";
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{code}]人工{evtname}失败，未找到相机", SecondIndex.Monitoring);
                        BarrierDeviceUtilsl.SendCarOrder(passwayModel.Passway_No, orderno, 0, passwayModel.Passway_SameInOut, passwayModel.Passway_OutNo);
                    }

                    #endregion

                    #region 开闸

                    var gateResult = await GateCmd.ExecuteAsync(mainDevice, action, true, isLongOpen);

                    #endregion

                    if (gateResult.Success && gateResult.RealOpen && !gateResult.Msg.Contains("请取消道闸常开"))
                    {
                        var msg = evtname;
                        if (isLongOpen)
                        {
                            if (gateResult.Msg.Contains("道闸常开")) { msg = "道闸常开"; }
                            if (gateResult.Msg.Contains("取消常开")) { msg = "取消常开"; }
                        }

                        if (code == 1)
                        {
                            #region 语音播报

                            BroadcastUtil.OpenGate(mainDevice, $"人工开闸成功，请通行");

                            #endregion
                        }

                        var rlt = PassTool.PassHelper.OpenGatePass(new Model.SpecialCarPass
                        {
                            time = DateTimeHelper.GetNowTime(),
                            camerano = mainDevice.Device_No,
                            img = gateResult.SnapImage.Path,
                            account = lgAdmin.Admins_Account,
                            name = lgAdmin.Admins_Name,
                            parkno = AppBasicCache.SentryHostInfo?.SentryHost_ParkNo,
                            type = action == GateCmd.ActionEnum.Close ? 3 : code == 2 ? 4 : 1,
                            sRemark = gateResult.Msg
                        });
                        if (rlt.success)
                        {
                            #region 通知岗亭

                            string imgUrl = SentryBox.Util.LPRTools.GetSentryHostImg(gateResult.SnapImage.Path);
                            WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                                $"[{lgAdmin.Admins_Name}]{msg}成功", DateTimeHelper.GetNowTime().ToString("G"));

                            #endregion

                            #region 记录上报

                            //if (BLL.AppBasicCache.IsWindows)
                            SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                            {
                                PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                PostRecordHandle_CarPlate = string.Empty,
                                PostRecordHandle_Datas = rlt.data,
                                PostRecordHandle_ToType = 3,
                                PostRecordHandle_Status = 0,
                                PostRecordHandle_ReturnMsg = string.Empty,
                                PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                            });

                            #endregion

                            BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{lgAdmin?.Admins_Account}]{msg}成功", SecondIndex.Monitoring);
                            return ResOk(true, gateResult.Msg, null, Convert.ToString(((CameraModel)camera.Model).GateLongOpen ? 1 : 0));
                        }
                        else
                        {
                            BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{lgAdmin?.Admins_Account}]{msg}成功,记录保存失败", SecondIndex.Monitoring);

                            #region 通知岗亭

                            string imgUrl = SentryBox.Util.LPRTools.GetSentryHostImg(gateResult.SnapImage.Path);
                            WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                                $"[{lgAdmin.Admins_Name}]{msg}成功 记录保存失败", DateTimeHelper.GetNowTime().ToString("G"));

                            #endregion

                            return ResOk(true, gateResult.Msg, null, Convert.ToString(((CameraModel)camera.Model).GateLongOpen ? 1 : 0));
                        }
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{lgAdmin?.Admins_Account}]{evtname}异常:[" + gateResult.Msg + "]", SecondIndex.Monitoring);
                        return ResOk(false, gateResult.Msg);
                    }
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.OpenCloseGate, $"[{lgAdmin?.Admins_Account}][{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{code}]{evtname}异常", SecondIndex.Monitoring);
                return ResOk(false, $"{evtname}异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 确认放行-弹窗确认入场
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="passwayno"></param>
        /// <param name="cartypename"></param>
        public async Task<IActionResult> ManualEntrance(string jsonModel, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.ConfirmPass.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"入场确认放行,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }
                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null)
                {
                    if (param == null) { return ResOk(false, "参数不正确，请检查输入的参数"); }
                }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                string carno = param.ParkOrder_CarNo.Trim();
                string passwayno = param.ParkOrder_EnterPasswayNo;
                string tempImage = param.ParkOrder_EnterImgPath;
                string carType = param.ParkOrder_CarType;
                string carCardType = param.ParkOrder_CarCardType;
                string remark = param.ParkOrder_EnterReamrk;
                DateTime PassTime = DateTimeHelper.GetNowTime();

                if (string.IsNullOrEmpty(carno) || carno.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                carno = carno.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", ""); //字母O替换数字0
                carno = carno.ToUpper();

                Model.CarType cartype = BLL.CarType.GetEntity(param.ParkOrder_CarType);
                if (cartype == null) return ResOk(false, "确认通行不成功，车牌颜色不存在");

                Model.CarCardType carcardtype = BLL.CarCardType.GetEntity(param.ParkOrder_CarCardType);
                if (carcardtype == null)
                {
                    if (string.IsNullOrEmpty(code))
                    {
                        return ResOk(false, "确认通行不成功，车牌类型不存在");
                    }
                }

                string cartypelist = TyziTools.Json.ToString(new string[] { cartype.CarType_Name });

                bool isModifyCarType = false;
                bool isModifyCarCardType = false;
                bool isModifyMode = false;
                Model.ResultPass data = null;
                BLL.ConfirmRelease.Results.TryGetValue(passwayno, out data);
                var policy = BLL.PolicyPassway.GetEntityByPasswayNo(passwayno);

                if (data != null)
                {
                    if (carcardtype == null && !string.IsNullOrEmpty(code))
                    {
                        carcardtype = data.passres?.carcardtype;
                    }

                    if (data.passres != null && (data.passres.carno != carno || data.passres.cartype?.CarType_No != carType || data.passres.carcardtype?.CarCardType_No != carCardType))
                    {
                        if (data.passres.cartype?.CarType_No != carType)
                        {
                            isModifyCarType = true;
                        }
                        if (data.passres.carcardtype?.CarCardType_No != carCardType)
                        {
                            isModifyCarCardType = true;
                        }

                        isModifyMode = true;
                    }
                }
                else
                {
                    if (policy != null)
                    {
                        if (!string.IsNullOrEmpty(policy.PolicyPassway_DefaultCarCardType) && policy.PolicyPassway_DefaultCarCardType != carCardType) isModifyCarCardType = true;
                        if (!string.IsNullOrEmpty(policy.PolicyPassway_DefaultCarType) && policy.PolicyPassway_DefaultCarType != carType) isModifyCarType = true;
                        if (isModifyCarCardType || isModifyCarType) isModifyMode = true;
                    }
                }

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }


                if (data != null && policy != null && policy.PolicyPassway_TakeRecord == 1)
                {
                    #region 开闸
                    //是否开双闸
                    var (isDoubleGate, linkCameraNoList) = GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);
                    var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);

                    // 更新识别记录的开闸状态
                    if (data.recog != null)
                    {
                        //更新执行开闸的结果
                        data.recog.CarRecog_OpenStatus = gateResult.Success ? 1 : -1;
                        BLL.CarRecog.GetInstance(data.recog.CarRecog_Time ?? DateTime.Now)
                            .UpdateOpenStatus(data.recog.CarRecog_No, (int)data.recog.CarRecog_OpenStatus);
                    }
                    #endregion

                    #region 语音播报

                    var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passwayno);
                    if (data.passres.gate == 1 || data.passres.gate == 2) // if (areaLink.Count() > 1)
                    {
                        BroadcastUtil.AutoReleaseEnter(areaLink.FirstOrDefault().Value.PasswayLink_ParkAreaNo, data, mainDevice, handEnter: true);
                    }
                    else
                    {
                        BroadcastUtil.AutoReleaseOut(data, mainDevice);
                    }

                    #endregion

                    #region 清理车道缓存

                    PasswayConfirmReleaseUtil.RemoveResult(passwayno);

                    #endregion

                    #region 岗亭通知

                    //WebSocketUtil.SendWS(data);

                    #endregion

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"[{lgAdmin?.Admins_Account}][{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{carno}]车辆确认入场成功", SecondIndex.Monitoring);
                    return ResOk(true, $"[{carno}]车辆确认入场成功", new EnterLPR(data));
                }


                bool needCheck = true;
                if (data != null && data.passres != null && data.passres.carno == carno)
                {
                    if (param.mode != 6 && !isModifyMode) needCheck = false;
                }

                if (param.mode == 6 || isModifyMode)
                {
                    if (needCheck)
                    {
                        data = PassTool.PassHelper.OnCheckCarPass(new Model.ParkCarInOut
                        {
                            carno = carno,
                            cartype = cartypelist,
                            changeCarType = isModifyCarType,
                            changeCarCardType = isModifyCarCardType,
                            carcardtype = carCardType,
                            time = (data?.passres?.useDeviceTime ?? false) ? data.time : PassTime,
                            parkno = mainDevice.Device_ParkNo,
                            camerano = mainDevice.Device_No,
                            img = tempImage,
                            mode = isModifyMode ? 8 : 6,
                            useDeviceTime = data?.passres?.useDeviceTime ?? false,
                            rOrderno = data?.passres?.parkorderno
                        }, AppBasicCache.GetBasicCache);
                    }
                }
                else if (param.mode == 2 && data.passres?.gate == 1)
                {
                    string msg = "";
                    var isOk = await PasswayConfirmReleaseUtil.ScanConfirmEnter(data, msg, lgAdmin);
                    return ResOk(isOk.Item1, isOk.Item2);
                }

                if (data == null) return ResOk(true, "车道已无识别车辆");

                if (data != null && data.success)
                {
                    //carno = data.passres.carno;
                    //是否允许通行，1 - 自动放行，2 - 弹窗确认放行，0 - 禁止通行,3 - 排队等候,4 - 最低收费缴费通行(出口时未找到记录)
                    if (data.passres.code != 0)
                    {
                        var bl = await PasswayConfirmReleaseUtil.ConfirmEnter(data, param.ParkOrder_EnterReamrk,
                            new Model.Admins() { Admins_Account = lgAdmin?.Admins_Account, Admins_Name = lgAdmin?.Admins_Name },
                            saveCache: false, modifyCarno: carno, modifyCarTypeNo: carType, modifyCarTypeName: cartype?.CarType_Name, handEnter: true, Scan: param.mode == 2);

                        if (bl.Item1 && (isModifyCarType || isModifyCarCardType))
                        {

                            BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"{carno} {data.passres?.parkorderno} {cartype?.CarType_Name} 人工修改入场订单{(isModifyCarType ? "车牌颜色" : "车牌类型")}！", SecondIndex.Monitoring);
                            var ret = BLL.ManualRecord.Add(new Model.ManualRecord() { ManualRecord_CarNo = carno, ManualRecord_ParkOrderNo = data.passres?.parkorderno, ManualRecord_Account = lgAdmin?.Admins_Account });
                            if (ret < 0)
                            {
                                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"{carno} {data.passres?.parkorderno} {cartype?.CarType_Name} 人工修改入场订单{(isModifyCarType ? "车牌颜色" : "车牌类型")}，标识记录增加失败！", SecondIndex.Monitoring);
                            }
                        }

                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认入场成功", SecondIndex.Monitoring);

                        var configuration = AppBasicCache.PrintSetting;
                        if (configuration != null)
                        {
                            if (configuration.mode == 1)
                            {
                                _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                                {
                                    try
                                    {
                                        Receipt receipt = new Receipt();
                                        receipt.Title = configuration.title;
                                        receipt.SubTitle = configuration.subtitle;
                                        receipt.CarNo = carno;
                                        receipt.CarCardType = "";
                                        receipt.InTime = PassTime.ToString("yyyy年MM月dd日 HH:mm:ss");
                                        receipt.TotalAmount = "0.00元";
                                        receipt.PayedAmount = "0.00元";
                                        receipt.Operator = lgAdmin?.Admins_Name;
                                        receipt.End = configuration.endcontent;

                                        BLL.ReceiptPrinter receiptPrinter = new BLL.ReceiptPrinter(receipt);
                                        receiptPrinter.PrintReceipt();
                                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{carno}自动打印收费小票!");
                                    }
                                    catch (Exception ex)
                                    {
                                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.PrintReceiptSetting, $"{carno}自动打印小票异常：{ex.Message}", SecondIndex.Monitoring);
                                    }
                                    return Task.CompletedTask;
                                });
                            }
                        }

                        return ResOk(bl.Item1, bl.Item2, bl.Item1 ? new EnterLPR(data) : null);
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认入场不成功,{data.passres.errmsg}", SecondIndex.Monitoring);
                        BroadcastUtil.NoEntry(data, mainDevice);
                        return ResOk(false, "确认通行不成功，输出信息:" + data.passres.errmsg);
                    }
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认入场不成功,{data.errmsg}", SecondIndex.Monitoring);
                    BroadcastUtil.NoEntry(data, mainDevice);
                    return ResOk(false, "确认通行不成功，输出信息:" + data.errmsg);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmEnter, $"车辆入场异常[{jsonModel}]{ex.ToString()}", SecondIndex.Monitoring);
                return ResOk(false, "车辆入场异常:" + ex.Message);
            }
        }
        #endregion

        #region ** 出场弹窗【确认放行】【免费放行】【语音播报】【取消放行】【获取车辆预出场信息】

        /// <summary>
        /// 获取车辆预出场信息
        /// </summary>
        /// <param name="jsonModel">传入的json数据</param>
        public async Task<IActionResult> GetParkOrder(string jsonModel, string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }
                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null) { return ResOk(false, "参数错误"); }
                string carno = param.ParkOrder_CarNo.Trim();
                string passwayno = param.ParkOrder_OutPasswayNo;
                string tempImage = param.ParkOrder_OutImgPath;
                int OutNoEnter = param.OutNoEnter;
                DateTime PassTime = DateTimeHelper.GetNowTime();
                var logtxt = "";

                if (string.IsNullOrEmpty(carno) || carno.Trim().Length < 7) return ResOk(false, "获取停车数据失败：车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "获取停车数据失败：车牌号仅支持(中文、字母、数字)组成");
                }
                carno = carno.Replace('o', '0').Replace('O', '0'); //字母O替换数字0

                carno = carno.ToUpper();
                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }

                string parkno = mainDevice.Device_ParkNo;

                Model.CarType cartype = BLL.CarType.GetEntity(param.ParkOrder_CarType);
                if (cartype == null) return ResOk(false, "获取停车数据失败，车牌颜色不存在");
                Model.CarCardType carcardtype = BLL.CarCardType.GetEntity(param.ParkOrder_CarCardType);
                if (carcardtype == null) return ResOk(false, "获取停车数据失败，车牌类型不存在");
                string cartypelist = TyziTools.Json.ToString(new string[] { cartype.CarType_No });
                string cartypename = cartype.CarType_Name;

                if (mainDevice != null)
                {
                    bool changeCarType = false;
                    bool changeCarCardType = false;
                    BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var oldData);

                    var orderoutCarType = oldData != null && oldData.resorder != null && oldData.resorder.resOut != null && oldData.resorder.resOut.parkorder != null
                        && oldData.resorder.resOut.parkorder.ParkOrder_CarType != cartype.CarType_No;
                    if (orderoutCarType) logtxt += $"车辆颜色：{oldData.resorder.resOut.parkorder.ParkOrder_CarTypeName}=>{cartype.CarType_Name}，";


                    var orderoutCarCardType = oldData != null && oldData.resorder != null && oldData.resorder.resOut != null && oldData.resorder.resOut.parkorder != null
                        && oldData.resorder.resOut.parkorder.ParkOrder_CarCardType != carcardtype.CarCardType_No;
                    if (orderoutCarCardType) logtxt += $" 车牌类型：{oldData.resorder.resOut.parkorder.ParkOrder_CarCardTypeName}=>{carcardtype.CarCardType_Name}，";

                    if (!orderoutCarType)
                    {
                        if (oldData != null && oldData.passres?.cartype != null && oldData.resorder?.resOut?.parkorder == null && oldData.passres?.cartype?.CarType_No != cartype.CarType_No) changeCarType = true;
                        if (changeCarType) logtxt += $" 车辆颜色：{oldData.passres.cartype.CarType_Name}=>{cartype.CarType_Name}";
                    }

                    if (orderoutCarCardType || orderoutCarType)
                    {
                        if (orderoutCarType) changeCarType = true;
                        if (orderoutCarCardType) changeCarCardType = true;
                    }

                    bool changeCoupon = false;
                    if (oldData == null)
                    {
                        changeCoupon = true;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(param.CouponList) && param.CouponList != "[]" && param.CouponList != "\"[]\"")
                        {
                            //changeCoupon = true;
                            var CouponNos = Common.Utils.ClearListModelRiskSQL<Model.CouponRecordIntExt>(param.CouponList);
                            if (CouponNos != null && CouponNos.Count > 0)
                            {
                                if (oldData.payres.uselist?.Count != CouponNos.Count)
                                    changeCoupon = true;
                                else
                                {
                                    foreach (var coupon in CouponNos)
                                    {
                                        if (coupon.CouponRecord_Set == 1)
                                        {
                                            if (oldData.payres.recordlist.Find(x => x.CouponRecord_Set == 1 && x.CouponRecord_CouponCode == coupon.CouponRecord_CouponCode
                                            && x.CouponRecord_Value == coupon.CouponRecord_Value) == null ||
                                            oldData.payres.uselist.Find(x => x.CouponRecord_Value == null && x.CouponRecord_CouponCode == coupon.CouponRecord_CouponCode) == null)
                                            {
                                                changeCoupon = true; break;
                                            }
                                        }
                                        else
                                        {
                                            if (oldData.payres.uselist.Find(x => x.CouponRecord_No == coupon.CouponRecord_No) == null) { changeCoupon = true; break; }
                                        }
                                    }
                                    ;
                                }
                            }
                            else
                            {
                                if (oldData.payres.uselist?.Count > 0) changeCoupon = true;
                            }
                        }
                        else
                        {
                            if (oldData != null && oldData.payres.uselist?.Count > 0) changeCoupon = true;
                        }
                    }

                    if (oldData == null || oldData.passres == null || oldData.passres.carno != carno || changeCoupon || changeCarType || changeCarCardType
                        || (oldData?.passres?.carcardtype != null && oldData.passres.carcardtype.CarCardType_No != carcardtype.CarCardType_No)
                        || (oldData?.passres?.cartype != null && oldData.passres.cartype.CarType_No != cartype.CarType_No))
                    {

                        if (param.IsApi != true && changeCarType)
                        {
                            if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifyTypeColor.ToString(), false, lgAdmin))
                            {
                                return ResOk(false, "无权限修改车牌颜色");
                            }

                        }
                        if (param.IsApi != true && changeCarCardType)
                        {
                            if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifyType.ToString(), false, lgAdmin))
                            {
                                return ResOk(false, "无权限修改车辆类型");
                            }

                        }

                        if (oldData != null && oldData?.resorder?.resOut?.onenter == 0 && !string.IsNullOrEmpty(oldData.passres.carno) && oldData.passres.carno != carno)
                        {
                            //string oldParkorderno = oldData.passres.parkorderno;
                            //if (!string.IsNullOrEmpty(oldParkorderno))
                            //{
                            //    var oldOrder = BLL.ParkOrder.GetEntity(oldParkorderno);
                            //    if (oldOrder != null)
                            //    {
                            //        oldOrder.ParkOrder_StatusNo = EnumParkOrderStatus.Close;
                            //        var ret = BLL.ParkOrder._UpdateByModelByNo(oldOrder);
                            //    }
                            //}
                        }

                        if (oldData != null && oldData?.resorder?.resOut?.onenter == 0 && changeCoupon)
                        {
                            if (!string.IsNullOrEmpty(param.CouponList) && param.CouponList != "[]" && param.CouponList != "\"[]\"")
                            {
                                var CouponNos = Common.Utils.ClearListModelRiskSQL<Model.CouponRecordIntExt>(param.CouponList);
                                if (CouponNos != null && CouponNos.Count > 0 && CouponNos.Find(x => x.CouponRecord_Set == 1) != null)
                                {
                                    return ResOk(false, "很抱歉，无入场记录暂时不支持直接使用优惠设置折扣");
                                }
                            }
                        }

                        PasswayConfirmReleaseUtil.RemoveResult(passwayno, true);
                        oldData = LPRTools.GetResultPass(parkno, passwayno, carno, cartype.CarType_Name, tempImage, param.CouponList, 6, changeCarType, param.ParkOrder_CarCardType, changeCarCardType);
                        if (oldData != null && oldData.payres == null)
                        {
                            oldData.payres = new ChargeModels.PayResult()
                            {
                                payed = 0,
                                orderamount = 0,
                                payedamount = 0
                            };
                        }

                        if (oldData != null && oldData.payres != null)
                        {
                            var coupons = Calc.GetCouponByOrderNo(oldData.payres.orderNo, oldData.time);
                            if (coupons.Count > 0)
                            {
                                var newCoupons = TyziTools.Json.ToObject<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(coupons));
                                if (oldData.payres.recordlist == null) oldData.payres.recordlist = new List<ChargeModels.CouponRecordIntExt>();
                                newCoupons.ForEach(item =>
                                {
                                    if (oldData.payres.recordlist.Find(x => x.CouponRecord_No == item.CouponRecord_No) == null)
                                    {
                                        oldData.payres.recordlist.Add(item);
                                    }
                                });
                            }
                        }

                        oldData.calcdetail ??= BLL.CommonBLL.GetCalcDetail(oldData.payres, oldData?.resorder?.resOut?.parkorder);

                        if (changeCarType) { oldData.changeCarType = param.ParkOrder_CarType; }
                        if (changeCarCardType) { oldData.changeCarCardType = param.ParkOrder_CarCardType; }
                    }


                    if (oldData.success)
                    {
                        if (oldData.passres != null && oldData.passres.code != 0)
                        {
                            //判断是出口，并且没有关联订单时才创建无入场记录
                            if (oldData?.resorder?.resOut?.onenter == 0 && oldData?.resorder?.resIn?.parkorder == null && oldData?.resorder?.resOut?.parkorder == null)//&& oldData?.resorder?.resOut?.noRecordOrder == null
                            {
                                //创建提前五分钟的入场记录，并上传到云平台
                                var parkOrder = PassTool.PassHelper.AddNoRecordParkOrder(parkno, ref oldData, lgAdmin?.Admins_Account, lgAdmin?.Admins_Name);
                                if (parkOrder != null && !string.IsNullOrEmpty(oldData.UnpaidRecord_No))
                                {
                                    string name = oldData.time.Value.ToString("yyyyMM");
                                    string tableName = "UnpaidRecord_" + name;
                                    BLL.UnpaidRecord.GetInstance(oldData.time.Value).ExecuteSql($"UPDATE {tableName} SET UnpaidRecord_ParkOrderNo='{parkOrder.ParkOrder_No}' WHERE UnpaidRecord_No='{oldData.UnpaidRecord_No}'");
                                }

                                //   BLL.PushEvent.CloseCar(parking.Parking_Key, item, item.ParkOrder_Remark);


                                var tempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { parkOrder }, null);
                                var temprlt = BLL.ParkOrderApi.CarIn(mainDevice.Device_ParkNo, tempdata);
                                if (!temprlt.success)
                                {
                                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPreParkOrder, $"无入场记录{parkOrder.ParkOrder_CarNo}预出场记录同步失败，将不能扫码缴费！", SecondIndex.Monitoring);
                                }

                                if (oldData.passres != null && string.IsNullOrEmpty(oldData.passres.parkorderno)) oldData.passres.parkorderno = parkOrder.ParkOrder_No;
                                if (oldData != null && oldData.payres == null)
                                {
                                    oldData.payres = new ChargeModels.PayResult()
                                    {
                                        payed = 0,
                                        orderamount = 0,
                                        payedamount = 0
                                    };
                                }
                            }

                            //llm 20220830  修改无入场纪录最低收费出场弹出收费界面，切换车牌颜色，界面会提示车辆不在场内
                            //原--if (data.resorder == null && data.resorder.resOut.onenter == 0)
                            if (oldData.resorder == null && oldData.resorder.resOut.onenter == 0 && OutNoEnter == 0)
                            {
                                return ResOk(false, "车辆不在场内");
                            }

                            if (oldData.passres.code == 1)
                            {
                                oldData.passres.code = 2;
                            }

                            #region 语音播报

                            BroadcastUtil.ConfirmRelease(oldData, mainDevice, true, param.IsSound != 0);

                            #endregion

                            PasswayConfirmReleaseUtil.AddOrUpdateResult(oldData);
                            OutLPR d = new OutLPR(oldData);
                            BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPreParkOrder, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {oldData?.passres?.carno} {(!string.IsNullOrEmpty(logtxt) ? "弹窗修改：" + logtxt : "")}", SecondIndex.Monitoring);
                            return ResOk(true, "获取成功", d);
                        }
                        else
                        {
                            BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPreParkOrder, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {oldData?.passres?.carno}  {(!string.IsNullOrEmpty(logtxt) ? "弹窗修改：" + logtxt : "")}", SecondIndex.Monitoring);
                            string errmsg = oldData.passres == null ? "获取停车数据失败，禁止通行" : oldData.passres.errmsg;
                            PasswayConfirmReleaseUtil.RemoveResult(passwayno);
                            BroadcastUtil.NoEntry(oldData, mainDevice);
                            return ResOk(false, errmsg);
                        }
                    }
                    else
                    {
                        BroadcastUtil.NoEntry(oldData, mainDevice);
                        return ResOk(false, oldData.errmsg);
                    }
                }
                else
                {
                    return ResOk(false, "无车牌识别相机");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetPreParkOrder, $"[{jsonModel}]获取停车数据异常", SecondIndex.Monitoring);
                return ResOk(false, "获取停车数据异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 选择优惠券计费
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="cartypename"></param>
        /// <param name="passwayno"></param>
        /// <param name="parkno"></param>
        /// <param name="tempImage"></param>
        /// <param name="CouponIDes"></param>
        public async Task<IActionResult> DiscountCharging(string parkno, string passwayno, string carno, string cartypename, string CouponIDes)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }
                Model.ResultPass result = LPRTools.GetResultPass(parkno, passwayno, carno.Trim(), cartypename, "", CouponIDes);
                var outdata = new
                {
                    confirm_NetReceipts = result.payres.payedamount,
                    confirm_DiscountMoney = result.payres.couponamount
                };
                PasswayConfirmReleaseUtil.AddOrUpdateResult(result);
                return ResOk(true, "获取计费成功", outdata);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetDiscountCharging, $"[{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{carno}][{cartypename}][{CouponIDes}]获取优惠券计费数据异常", SecondIndex.Monitoring);
                return ResOk(false, "获取计费数据异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 确认放行-缴费出场
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="parkno"></param>
        /// <param name="passwayno"></param>
        public async Task<IActionResult> ConfirmRelease(string jsonModel, string code = "")
        {
            string carno = string.Empty;
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.ConfirmPass.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"出场确认放行,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null)
                {
                    return ResOk(false, "参数不正确，请检查输入的参数");
                }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                carno = param.ParkOrder_CarNo.Trim();
                string passwayno = param.ParkOrder_OutPasswayNo;
                DateTime PassTime = DateTimeHelper.GetNowTime();
                param.ParkOrder_FreeReason = "";

                if (string.IsNullOrEmpty(carno) || carno.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                carno = carno.Replace('o', '0').Replace('O', '0'); //字母O替换数字0
                carno = carno.ToUpper();

                BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data);

                #region 出口信息检测

                if (data == null || data.passres.carno != carno || data?.resorder?.resOut == null) return ResOk(false, "请先查询车辆停车状态");
                if (!data.success || data.passres?.code == null || data.passres?.code == 0)
                {
                    return ResOk(false, $"禁止通行：{data.passres?.errmsg ?? data.errmsg}");
                }

                if (data.payres != null && data.payres.payed != 2)
                {
                    decimal unpaidMoeny = 0;
                    if (data != null && data.unpaidresult != null)
                    {
                        unpaidMoeny = data.unpaidresult?.Sum(m => m.payres?.orderamount) ?? 0;
                    }

                    if (param.PayedMoney > data.payres.orderamount + unpaidMoeny)
                    {
                        return ResOk(false, $"支付错误：实收金额不能大于应收金额");
                    }

                    if (param.PayedMoney != data.payres.payedamount + unpaidMoeny)
                    {
                        if (unpaidMoeny > 0)
                        {
                            if (param.PayedMoney >= unpaidMoeny)
                            {
                                data.payres.payedamount = param.PayedMoney.Value - unpaidMoeny;
                            }
                            else
                            {
                                data.payres.payedamount = 0;
                                var diffAmount = unpaidMoeny - param.PayedMoney.Value;
                                foreach (var up in data.unpaidresult)
                                {
                                    var orderAmount = up.payres.orderamount;
                                    var payedAmount = up.payres.payedamount;

                                    if (diffAmount <= 0)
                                    {
                                        break; // 没有多余的金额需要冲销了
                                    }

                                    // 从 payedAmount 中倒扣
                                    var deduct = Math.Min(diffAmount, payedAmount); // 最多不能扣成负数
                                    up.payres.payedamount -= deduct;
                                    diffAmount -= deduct;
                                }
                            }
                        }
                        else
                        {
                            data.payres.payedamount = param.PayedMoney.Value;
                        }
                    }
                }

                var CouponList = Utils.ClearListModelRiskSQL<Model.CouponRecordIntExt>(param.CouponList);
                CouponList?.ForEach(x =>
                {
                    x.CouponRecord_ParkOrderNo = data.passres.parkorderno;
                    x.CouponRecord_IssueCarNo = data.passres.carno;
                });

                #endregion

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }

                var policy = BLL.PolicyPassway.GetEntityByPasswayNo(passwayno);
                if (data != null && policy != null && policy.PolicyPassway_TakeRecord == 1)
                {
                    #region 开闸
                    //是否开双闸
                    var (isDoubleGate, linkCameraNoList) = GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);
                    var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);

                    #endregion

                    #region 语音播报

                    var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passwayno);
                    if (data.passres.gate == 1 || data.passres.gate == 2) // if (areaLink.Count() > 1)
                    {
                        BroadcastUtil.AutoReleaseEnter(areaLink.FirstOrDefault().Value.PasswayLink_ParkAreaNo, data, mainDevice, handEnter: true);
                    }
                    else
                    {
                        BroadcastUtil.AutoReleaseOut(data, mainDevice);
                    }

                    #endregion

                    #region 清理车道缓存

                    PasswayConfirmReleaseUtil.RemoveResult(passwayno);

                    #endregion

                    data.passres.type = Model.EnumParkOrderStatus.Out;

                    #region 岗亭通知

                    WebSocketUtil.SendWS(data);

                    #endregion

                    if (!string.IsNullOrEmpty(data.resorder.resOut.parkorder?.ParkOrder_No))
                    {
                        if (data.resorder.resOut.parkorder?.ParkOrder_OutTime != null)
                        {
                            BLL.UnpaidRecord.UpdateStatus(data.resorder.resOut.parkorder.ParkOrder_OutTime, data.resorder.resOut.parkorder.ParkOrder_No, data.resorder.resOut.parkorder.ParkOrder_CarNo);
                        }
                    }

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmOut, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认出场成功", SecondIndex.Monitoring);

                    return ResOk(true, $"{carno} 车辆确认出场成功", new EnterLPR(data));
                }


                var bl = await PasswayConfirmReleaseUtil.ConfirmPaymentCompletedRelease(data, mainDevice, param.PayedMoney, CouponList,
                    new Model.Admins() { Admins_Account = lgAdmin?.Admins_Account, Admins_Name = lgAdmin?.Admins_Name }, saveCache: false, handWin: true);

                var logtxt = $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认出场{(bl.Item1 ? "成功" : "失败")}";
                if (data != null && data.payres != null)
                {
                    if (!string.IsNullOrEmpty(data.changeCarType)) { logtxt += $"，修改计费车牌颜色为：{AppBasicCache.GetElement(AppBasicCache.GetCarTypes, data.changeCarType)?.CarType_Name}"; }
                    if (!string.IsNullOrEmpty(data.changeCarCardType)) { logtxt += $"，修改计费车牌类型为：{AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, data.changeCarCardType)?.CarCardType_Name}"; }
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmOut, logtxt, SecondIndex.Monitoring);
                return ResOk(bl.Item1, bl.Item2, bl.Item1 ? new OutLPR(data) : null);
            }
            catch (Exception ex)
            {
                if (!string.IsNullOrEmpty(carno)) LocalCache.Del(carno);
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmOut, $"[{jsonModel}]确认出场放行异常", SecondIndex.Monitoring);
                return ResOk(false, "确认缴费放行异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 免费放行
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="parkno"></param>
        /// <param name="passwayno"></param>
        /// <param name="reason"></param>
        public async Task<IActionResult> FreeRelease(string jsonModel, string code = "")
        {
            string carno = string.Empty;
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.FreeOpen.ToString(), false, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"免费放行,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限免费放行");
                }

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null) { return ResOk(false, "参数不正确，请检查输入的参数"); }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                carno = param.ParkOrder_CarNo.Trim();
                string passwayno = param.ParkOrder_OutPasswayNo;
                string tempImage = param.ParkOrder_OutImgPath;
                string reason = param.ParkOrder_FreeReason;
                if (string.IsNullOrEmpty(carno) || carno.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                carno = carno.Replace('o', '0').Replace('O', '0'); //字母O替换数字0

                BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data);
                if (data == null || data.passres.carno != carno) return ResOk(false, "当前车道已存在车辆");

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }

                var policy = BLL.PolicyPassway.GetEntityByPasswayNo(passwayno);
                if (data != null && policy != null && policy.PolicyPassway_TakeRecord == 1)
                {
                    #region 开闸
                    //是否开双闸
                    var (isDoubleGate, linkCameraNoList) = GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);
                    var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate,linkCameraNoList:linkCameraNoList);

                    #endregion

                    #region 语音播报

                    var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passwayno);
                    if (data.passres.gate == 1 || data.passres.gate == 2) // if (areaLink.Count() > 1)
                    {
                        BroadcastUtil.AutoReleaseEnter(areaLink.FirstOrDefault().Value.PasswayLink_ParkAreaNo, data, mainDevice, handEnter: true);
                    }
                    else
                    {
                        BroadcastUtil.AutoReleaseOut(data, mainDevice);
                    }

                    #endregion

                    #region 清理车道缓存

                    PasswayConfirmReleaseUtil.RemoveResult(passwayno);

                    #endregion

                    #region 岗亭通知

                    WebSocketUtil.SendWS(data);

                    #endregion

                    var orderno = data.resorder?.resOut?.parkorder?.ParkOrder_No ?? data.passres?.parkorderno;
                    if (!string.IsNullOrEmpty(orderno))
                    {
                        if (data.time != null)
                        {
                            BLL.UnpaidRecord.UpdateStatus(data.time, orderno, carno);
                        }
                    }

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.FreeOut, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认出场成功", SecondIndex.Monitoring);

                    return ResOk(true, $"{carno} 车辆确认出场成功", new EnterLPR(data));
                }

                var bl = await PasswayConfirmReleaseUtil.FreeRelease(data, mainDevice, reason,
                    new Model.Admins() { Admins_Account = lgAdmin?.Admins_Account, Admins_Name = lgAdmin?.Admins_Name });

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.FreeOut, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆确认出场{(bl.Item1 ? "成功" : "失败")}", SecondIndex.Monitoring);

                return ResOk(bl.Item1, bl.Item2, bl.Item1 ? new OutLPR(data) : null);
            }
            catch (Exception ex)
            {
                if (!string.IsNullOrEmpty(carno)) LocalCache.Del(carno);
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.FreeOut, $"[{jsonModel}]免费放行异常", SecondIndex.Monitoring);
                return ResOk(false, "免费放行异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 语音播报
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> BroadVoice(string jsonModel, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.ManualTrigger.ToString()))
                {
                    LogManagementMap.WriteToFileException(null, $"语音播报,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null) { return ResOk(false, "参数不正确，请检查输入的参数"); }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                string passwayno = param.ParkOrder_OutPasswayNo;
                if (BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data))
                {
                    //string carno = param.ParkOrder_CarNo.Trim();
                    //decimal dmoney1 = param.PayedMoney.Value;
                    //if (data.passres != null) data.passres.carno = carno;
                    //if (data.payres != null) data.payres.payedamount = dmoney1;

                    //if (dmoney1 > 0 && data.passres.code == 2 && data.resorder.resOut.onenter == 0 && data.resorder.resOut.parkorder == null)
                    //{
                    //    if (data.payres == null)
                    //    {
                    //        data.payres = new ChargeModels.PayResult()
                    //        {
                    //            payed = 1,
                    //            orderamount = dmoney1,
                    //            payedamount = dmoney1
                    //        };
                    //    }
                    //    string parkno = BLL.AppBasicCache.SentryHostInfo?.SentryHost_ParkNo;
                    //    PassTool.PassHelper.AddNoRecordParkOrder(parkno, ref data, lgAdmin?.Admins_Account, lgAdmin?.Admins_Name);//无入场记录创建订单
                    //}
                    //PasswayConfirmReleaseUtil.AddOrUpdateResult(data);

                    var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice, "语音播报不成功");
                    if (acionRes != null)
                    {
                        return acionRes;
                    }

                    if (data.passres.code == 4)
                    {
                        BroadcastUtil.MinimumCharge(data, mainDevice);
                    }
                    else if (data.passres.code == 3)
                    {
                        BroadcastUtil.WaitInLine(data, mainDevice);
                    }
                    else
                    {
                        BroadcastUtil.ConfirmRelease(data, mainDevice);
                    }
                }
                else
                {
                    return ResOk(false, "当前车道已无车辆");
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.BroadVoice, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {data?.passres?.carno} 车辆语音播报成功", SecondIndex.Monitoring);

                return ResOk(true, "语音播报成功");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.BroadVoice, $"[{jsonModel}]语音播报异常", SecondIndex.Monitoring);
                return ResOk(false, "语音播报失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 取消放行
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> CancelPass(string passwayno, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.CancelCharge.ToString(), false, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"取消放行,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限取消放行");
                }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                var gate = BLL.Passway.GetPasswayGateType(passwayno);
                if (gate == 0 || gate == 3)
                {
                    var parking = GetParking();
                    Model.PolicyPark policy = BLL.PolicyPark.GetEntity(parking.Parking_No);
                    if (policy.PolicyPark_CollectAllowCancel == 0) return ResOk(false, "已设置禁止取消放行");
                }

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice, "取消放行不成功");
                if (acionRes != null)
                {
                    return acionRes;
                }

                if (!BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data))
                {
                    return ResOk(true, $"当前车道没有需要取消放行的车辆");
                }

                string orderno = data.resorder?.resOut?.parkorder?.ParkOrder_No ?? data.passres?.parkorderno;
                Model.ParkOrder po = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), orderno);
                if (po != null)
                {
                    if (po.ParkOrder_StatusNo > EnumParkOrderStatus.In)
                    {
                        return ResOk(true, $"当前车道没有需要取消放行的车辆");
                    }

                    List<Model.PayOrder> payorderList = BLL.PayOrder.GetAllEntity("PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{po.ParkOrder_No}' and PayOrder_Status=1");
                    var newOrder = po.Copy();
                    newOrder.ParkOrder_TotalAmount = payorderList?.Sum(x => x.PayOrder_Money) ?? 0;
                    newOrder.ParkOrder_TotalPayed = payorderList?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                    if (newOrder.ParkOrder_TotalAmount != po.ParkOrder_TotalAmount || newOrder.ParkOrder_TotalPayed != po.ParkOrder_TotalPayed)
                    {
                        var result = BLL.BaseBLL._ExceteBySql($"UPDATE parkorder SET ParkOrder_TotalAmount={newOrder.ParkOrder_TotalAmount}" +
                                                              $",ParkOrder_TotalPayed={newOrder.ParkOrder_TotalPayed} WHERE ParkOrder_No='{newOrder.ParkOrder_No}'"); // BLL.ParkOrder._UpdateByModelByNo(newOrder);
                    }

                    if (gate == 3)
                    {
                        var detail = BLL.OrderDetail.GetEntity("orderdetail_no", $"orderdetail_ParkOrderNo='{orderno}' and orderdetail_statusno=199 and orderdetail_EnterPasswayNo='{passwayno}'");
                        if (detail != null)
                        {
                            var result2 = BLL.BaseBLL._ExceteBySql($"UPDATE orderdetail SET orderdetail_statusno=202,orderdetail_remark='取消放行' WHERE orderdetail_no='{detail.OrderDetail_No}'");
                        }
                    }
                }

                PasswayConfirmReleaseUtil.ConfirmCancelRelease(passwayno, orderno, data.time?.ToString("G"), true, true, data, mainDevice, true, ClosePreInPark: true, isUpdateOutType: true);

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CancelPass, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {data?.passres?.carno} 车辆取消放行成功", SecondIndex.Monitoring);
                return ResOk(true, $"取消放行成功");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CancelPass, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} 取消放行异常", SecondIndex.Monitoring);
                return ResOk(false, "取消放行异常：" + ex.Message);
            }
        }

        #region 无入场记录处理

        /// <summary>
        /// 人工启用无入场记录
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> EnableNoEnterRecord(string jsonModel, string code = "")
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }
                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null) { return ResOk(false, "很抱歉，参数异常"); }

                string carno = param.ParkOrder_CarNo.Trim();
                if (string.IsNullOrEmpty(carno) || carno.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                carno = carno.Replace('o', '0').Replace('O', '0'); //字母O替换数字0
                carno = carno.ToUpper();


                string passwayno = param.ParkOrder_OutPasswayNo;
                string tempImage = param.ParkOrder_OutImgPath;
                DateTime PassTime = DateTimeHelper.GetNowTime();

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }

                Model.CarType cartype = BLL.CarType.GetEntity(param.ParkOrder_CarType);
                if (cartype == null) return ResOk(false, "确认通行不成功，车牌颜色不存在");
                string cartypelist = TyziTools.Json.ToString(new string[] { cartype.CarType_No });
                string parkno = mainDevice.Device_ParkNo;

                BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data);
                if (data == null || data.passres == null || data.passres.carno != carno)
                {
                    PasswayConfirmReleaseUtil.RemoveResult(passwayno, true);
                    data = LPRTools.GetResultPass(parkno, passwayno, carno, cartype.CarType_Name, tempImage, param.CouponList, param.mode.Value);
                    if (data != null && data.payres == null)
                    {
                        data.payres = new ChargeModels.PayResult()
                        {
                            payed = 0,
                            orderamount = 0,
                            payedamount = 0
                        };
                    }

                    if (data.calcdetail == null) data.calcdetail = BLL.CommonBLL.GetCalcDetail(data.payres, data?.resorder?.resOut?.parkorder);
                    //if (data.payres != null && data.passres != null) CalcCache.Set("OrderPrice:" + data.passres.parkorderno, data.payres);
                }

                //Model.ResultPass data = LPRTools.GetResultPass(parkno, passwayno, carno, cartype.CarType_Name, tempImage, param.CouponList, param.mode.Value);

                if (!data.success || data.passres?.code == null || data.passres?.code == 0)
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EnableNoEnterRecord, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆禁止通行", SecondIndex.Monitoring);
                    return ResOk(false, $"禁止通行：{data.passres?.errmsg ?? data.errmsg}");
                }

                string errmsg = string.Empty;
                if (data?.resorder?.resOut?.onenter == 0)
                {
                    //判断是出口，并且没有关联订单时才创建无入场记录
                    if (data?.resorder?.resIn?.parkorder == null && data?.resorder?.resOut?.parkorder == null)
                    {
                        //创建提前五分钟的入场记录，并上传到云平台
                        var parkOrder = PassTool.PassHelper.AddNoRecordParkOrder(parkno, ref data, lgAdmin?.Admins_Account, lgAdmin?.Admins_Name);
                        if (parkOrder != null && !string.IsNullOrEmpty(data.UnpaidRecord_No))
                        {
                            string name = data.time.Value.ToString("yyyyMM");
                            string tableName = "UnpaidRecord_" + name;
                            BLL.UnpaidRecord.GetInstance(data.time.Value).ExecuteSql($"UPDATE {tableName} SET UnpaidRecord_ParkOrderNo='{parkOrder.ParkOrder_No}' WHERE UnpaidRecord_No='{data.UnpaidRecord_No}'");
                        }

                        var tempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { parkOrder }, null);
                        var temprlt = BLL.ParkOrderApi.CarIn(mainDevice.Device_ParkNo, tempdata);
                        if (!temprlt.success)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"无入场记录{parkOrder.ParkOrder_CarNo}预出场记录同步失败，将不能扫码缴费！");
                        }

                        if (data.passres != null && string.IsNullOrEmpty(data.passres.parkorderno)) data.passres.parkorderno = parkOrder.ParkOrder_No;
                        if (data != null && data.payres == null)
                        {
                            data.payres = new ChargeModels.PayResult()
                            {
                                payed = 0,
                                orderamount = 0,
                                payedamount = 0
                            };
                        }
                    }

                    #region 更新车道缓存

                    PasswayConfirmReleaseUtil.AddOrUpdateResult(data);

                    #endregion

                    #region 语音播报

                    if (data.passres.code == 4)
                    {
                        BroadcastUtil.MinimumCharge(data, mainDevice, false, true);
                    }
                    else
                    {
                        #region 语音播报

                        BroadcastUtil.ConfirmRelease(data, mainDevice, true, true);

                        #endregion
                    }

                    #endregion

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EnableNoEnterRecord, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆人工启用无入场记录成功", SecondIndex.Monitoring);

                    return ResOk(true, errmsg, new OutLPR(data));
                }
                else
                {
                    return ResOk(false, $"车辆已有场内记录，不能以无入场记录放行！");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EnableNoEnterRecord, $"[{jsonModel}]人工启用无入场记录异常", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 人工取消无入场记录
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> CancelNoEnterRecord(string jsonModel, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.CancelCharge.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"取消无入场记录,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }
                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null) { return ResOk(false, "参数不正确，请检查输入的参数"); }

                string carno = param.ParkOrder_CarNo.Trim();
                string passwayno = param.ParkOrder_OutPasswayNo;
                string tempImage = param.ParkOrder_OutImgPath;
                DateTime PassTime = DateTimeHelper.GetNowTime();

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }

                BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data);
                if (data == null)
                {
                    return ResOk(true, "队列已被释放");
                }

                if (data?.resorder?.resOut?.onenter == 0)
                {
                    var order = data?.resorder?.resOut?.parkorder;
                    if (order == null)
                    {
                        #region 取消放行处理

                        PasswayConfirmReleaseUtil.ConfirmCancelRelease(passwayno, order?.ParkOrder_No, data.time?.ToString("G"), data: data, mainDevice: mainDevice, sendVoice: true);

                        #endregion

                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CancelNoEnterRecord, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆无入场记录取消放行成功", SecondIndex.Monitoring);
                        return ResOk(true, $"无入场记录取消放行成功！");
                    }

                    order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                    order.ParkOrder_Remark = "无入场记录取消放行关闭订单";
                    Model.OrderDetail detail = null;
                    //20220830 llm 修改系统设置取消放行弹框，依旧可以放行。
                    Model.PolicyPark policy = BLL.PolicyPark.GetEntity(order.ParkOrder_ParkNo);
                    if (policy.PolicyPark_CollectAllowCancel == 0) return ResOk(false, "已设置禁止取消放行");
                    var res = BLL.ParkOrder.CarInComplete(order, detail);
                    if (res > 0)
                    {
                        #region 取消放行处理

                        PasswayConfirmReleaseUtil.ConfirmCancelRelease(passwayno, order.ParkOrder_No, data.time?.ToString("G"), data: data, mainDevice: mainDevice, sendVoice: true);

                        #endregion

                        #region 记录上传

                        var tempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { order }, null);
                        BLL.ParkOrderApi.CarIn(mainDevice.Device_ParkNo, tempdata);

                        #endregion

                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CancelNoEnterRecord, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆无入场记录取消放行成功", SecondIndex.Monitoring);
                        return ResOk(true, $"无入场记录取消放行成功！");
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CancelNoEnterRecord, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆无入场记录取消放行失败", SecondIndex.Monitoring);
                        return ResOk(false, $"无入场记录取消放行失败！");
                    }
                }
                else
                {
                    return ResOk(false, $"车辆已有场内记录，不能以无入场记录放行！");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CancelNoEnterRecord, $"[{jsonModel}]人工取消无入场记录异常", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 无入场记录确认放行
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> ConfirmReleaseNothingRecord(string jsonModel, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.PassCarNo.ToString(), true, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"无入场记录确认放行,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null) { return ResOk(false, "参数不正确，请检查输入的参数"); }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }


                string carno = param.ParkOrder_CarNo.Trim();
                carno = carno.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");//字母O替换数字0
                carno = carno.ToUpper();
                string passwayno = param.ParkOrder_OutPasswayNo;
                string tempImage = param.ParkOrder_OutImgPath;
                DateTime PassTime = DateTimeHelper.GetNowTime();

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice);
                if (acionRes != null)
                {
                    return acionRes;
                }

                Model.CarType cartype = BLL.CarType.GetEntity(param.ParkOrder_CarType);
                if (cartype == null) return ResOk(false, "确认通行不成功，车牌颜色不存在");
                string cartypelist = TyziTools.Json.ToString(new string[] { cartype.CarType_No });

                if (carno.Length < 7)
                {
                    return ResOk(false, "车牌号不允许为空且长度最小为7");
                }

                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }

                BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data);
                if (data == null || data.passres.carno != carno || data?.resorder?.resOut == null) return ResOk(false, "请先查询车辆停车状态");
                if (!data.success || data.passres?.code == null || data.passres?.code == 0)
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmNoEnterRecord, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 车辆无入场记录禁止通行", SecondIndex.Monitoring);
                    return ResOk(false, $"禁止通行：{data.passres?.errmsg ?? data.errmsg}");
                }

                if (data.payres != null && data.payres.payed != 2)
                {
                    decimal unpaidMoeny = 0;
                    if (data != null && data.unpaidresult != null)
                    {
                        unpaidMoeny = data.unpaidresult?.Sum(m => m.payres?.orderamount) ?? 0;
                    }

                    if (param.PayedMoney > data.payres.orderamount + unpaidMoeny)
                    {
                        return ResOk(false, $"支付错误：实收金额不能大于应收金额");
                    }

                    if (param.PayedMoney != data.payres.payedamount + unpaidMoeny)
                    {
                        if (unpaidMoeny > 0)
                        {
                            if (param.PayedMoney >= unpaidMoeny)
                            {
                                data.payres.payedamount = param.PayedMoney.Value - unpaidMoeny;
                            }
                            else
                            {
                                data.payres.payedamount = 0;
                                var diffAmount = unpaidMoeny - param.PayedMoney.Value;
                                foreach (var up in data.unpaidresult)
                                {
                                    var orderAmount = up.payres.orderamount;
                                    var payedAmount = up.payres.payedamount;

                                    if (diffAmount <= 0)
                                    {
                                        break; // 没有多余的金额需要冲销了
                                    }

                                    // 从 payedAmount 中倒扣
                                    var deduct = Math.Min(diffAmount, payedAmount); // 最多不能扣成负数
                                    up.payres.payedamount -= deduct;
                                    diffAmount -= deduct;
                                }
                            }
                        }
                        else
                        {
                            data.payres.payedamount = param.PayedMoney.Value;
                        }
                    }
                }

                var Success = false;
                var ErrMessage = "";

                //查询无入场记录订单
                var order = BLL.ParkOrder.GetNoRecordOrderByCarNo(carno, DateTime.Now.AddMinutes(-((AppBasicCache.GetPolicyPark.PolicyPark_NoRecordRangTime ?? 10) + 360)).ToString("yyyy-MM-dd HH:mm:ss"));
                if (order.Item1 != null)
                {
                    if (order.Item2 == null || order.Item2.Count == 0 || order.Item1?.Last()?.ParkOrder_StatusNo == 199)
                    {
                        #region 控制板来车事件通知

                        BarrierDeviceUtilsl.SendCarOrder(data, false);

                        #endregion

                        #region 开闸
                        //获取双开信息
                        var (isDoubleGate, linkCameraNoList) = GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);
                        var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);

                        #endregion

                        if (!gateResult.Success) //开闸失败
                        {
                            if (data.recog != null)
                            {
                                data.recog.CarRecog_OpenStatus = -1;
                            }
                            BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmNoEnterRecord, $"开闸失败,参数[{jsonModel}]，{gateResult.Msg}", SecondIndex.Monitoring);
                        }
                        else
                        {
                            if (data.recog != null)
                            {
                                data.recog.CarRecog_OpenStatus = 1;
                            }
                        }

                        //if (data.payres != null && data.payres.payed == 1)
                        //    data.payres.payedamount = param.PayedMoney.Value;

                        #region 语音播报

                        BroadcastUtil.AutoReleaseOut(data, mainDevice, data.recog?.CarRecog_Mode == 5);

                        #endregion

                        #region 记录上传

                        bool takeRecordResult = false; //优先保存记录是否处理成功：true-处理成功，false-处理失败
                        var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passwayno);
                        if (data.policy != null && data.policy.takerecord == 1 || (data.policy != null && data.policy.autotakerecord == 1))
                        {
                            if (areaLink.Count() > 0)
                            {
                                takeRecordResult = true;
                            }
                        }
                        var policypass = AppBasicCache.GetElementsByValues(AppBasicCache.GetAllPolicyPassway, passwayno, "PolicyPassway_PasswayNo")?.FirstOrDefault();
                        if (policypass?.PolicyPassway_TakeRecord != 1)
                        {
                            var result = PassTool.PassHelper.CarOutNoRecord(data, lgAdmin.Admins_Account, lgAdmin.Admins_Name, (order.Item1?.Count > 0 ? order.Item1?.Last() : null), (takeRecordResult ? true : false));
                            //if (BLL.AppBasicCache.IsWindows)
                            SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                            {
                                PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                PostRecordHandle_CarPlate = carno,
                                PostRecordHandle_Datas = result.data,
                                PostRecordHandle_ToType = 4,
                                PostRecordHandle_Status = 0,
                                PostRecordHandle_ReturnMsg = string.Empty,
                                PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                            });
                        }
                        #endregion

                        #region 清除车道缓存

                        PasswayConfirmReleaseUtil.RemoveResult(passwayno);

                        #endregion

                        Success = true;
                    }
                    else
                    {
                        ErrMessage = "场内车辆不能作为无入场放行";
                    }
                }
                else
                {


                    Model.ParkOrder parkorder = PassTool.PassHelper.AddNoRecordParkOrder(mainDevice.Device_ParkNo, ref data, lgAdmin.Admins_Account, lgAdmin.Admins_Name); //无入场记录创建订单
                    if (parkorder != null && !string.IsNullOrEmpty(data.UnpaidRecord_No))
                    {
                        string name = data.time.Value.ToString("yyyyMM");
                        string tableName = "UnpaidRecord_" + name;
                        BLL.UnpaidRecord.GetInstance(data.time.Value).ExecuteSql($"UPDATE {tableName} SET UnpaidRecord_ParkOrderNo='{parkorder.ParkOrder_No}' WHERE UnpaidRecord_No='{data.UnpaidRecord_No}'");
                    }
                    #region 控制板来车事件通知

                    BarrierDeviceUtilsl.SendCarOrder(data, false);

                    #endregion

                    #region 开闸
                    //是否开双闸
                    var (isDoubleGate, linkCameraNoList) = GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);
                    var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);

                    #endregion

                    if (!gateResult.Success) //开闸失败
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"无入场记录通行开闸失败[{jsonModel}]，{gateResult.Msg}");
                    }

                    #region 语音播报

                    BroadcastUtil.AutoReleaseOut(data, mainDevice, data.recog?.CarRecog_Mode == 5);

                    #endregion

                    #region 记录上报

                    bool takeRecordResult = false; //优先保存记录是否处理成功：true-处理成功，false-处理失败
                    var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passwayno);
                    if (data.policy != null && data.policy.takerecord == 1 || (data.policy != null && data.policy.autotakerecord == 1))
                    {
                        if (areaLink.Count() > 0)
                        {
                            takeRecordResult = true;
                        }
                    }

                    var result = PassTool.PassHelper.CarOutNoRecord(data, lgAdmin.Admins_Account, lgAdmin.Admins_Name, parkorder, (takeRecordResult ? true : false));
                    //if (BLL.AppBasicCache.IsWindows)
                    SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                    {
                        PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                        PostRecordHandle_CarPlate = carno,
                        PostRecordHandle_Datas = result.data,
                        PostRecordHandle_ToType = 4,
                        PostRecordHandle_Status = 0,
                        PostRecordHandle_ReturnMsg = string.Empty,
                        PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                    });

                    #endregion

                    #region 清除车道缓存

                    PasswayConfirmReleaseUtil.RemoveResult(passwayno);

                    #endregion

                    Success = true;
                }

                if (Success)
                {
                    var orderno = data.resorder?.resOut?.parkorder?.ParkOrder_No ?? data.passres?.parkorderno ?? data.resorder?.resOut?.noRecordOrder?.ParkOrder_No;

                    #region 岗亭通知
                    CloseLPR res = new CloseLPR()
                    {
                        evt = 2,
                        code = 1,
                        passwayno = passwayno,
                        orderno = orderno
                    };
                    string actionName = "UpdateLPR";
                    WebSocketHandler.SendAllClick(passwayno, actionName, Common.TyziTools.Json.ToString(res), $"[{passwayno}][{orderno}][确定放行]");
                    #endregion

                    if (!string.IsNullOrEmpty(orderno))
                    {
                        if (data.time != null)
                        {
                            BLL.UnpaidRecord.UpdateStatusByCarno(data.time, orderno, carno);
                        }
                    }


                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmNoEnterRecord, $"{carno} 车辆无入场记录通行成功", SecondIndex.Monitoring);


                    return ResOk(true, "无入场记录通行成功", new NoRecordLPR(data));
                }
                else
                {
                    return ResOk(false, ErrMessage);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ConfirmNoEnterRecord, $"通行异常[{jsonModel}]，{ex}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        #endregion

        #endregion

        #region **【获取车道相机设备】【获取车道相机状态】【控制板获取道闸状态】【修改价格】【获取场内车停车订单】【修改车牌】

        /// <summary>
        /// 获取车道相机设备
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <param name="mainDevice">设备</param>
        /// <param name="logHeader">日志头</param>
        /// <returns></returns>
        private IActionResult GetDevice(string passwayno, out Model.DeviceExt mainDevice, string logHeader = "确认通行不成功", string code = "")
        {
            mainDevice = null;
            //if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

            AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayno, out var passway);
            if (passway == null)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetDevice, $"{logHeader},未找到车道缓存：{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}", SecondIndex.Monitoring);
                return ResOk(false, "获取停车数据失败，未找到车道缓存");
            }

            if (passway.Passway_Type != 3)
            {
                mainDevice = BLL.Device.GetEntityByPasswayNo(passwayno);
                if (mainDevice == null)
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetDevice, $"{logHeader},未配置主相机：{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}", SecondIndex.Monitoring);
                    return ResOk(false, "获取停车数据失败，未配置主相机");
                }
            }
            else
            {
                mainDevice = BLL.Device.GetMotoDeviceByPasswayNo(passwayno);
                if (mainDevice == null)
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetDevice, $"{logHeader},未配置车道控制器：{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}", SecondIndex.Monitoring);
                    return ResOk(false, "获取停车数据失败，未配置车道控制器");
                }
            }

            return null;
        }


        /// <summary>
        /// 获取车道相机状态
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <param name="mainDevice">设备</param>
        /// <param name="logHeader">日志头</param>
        /// <returns></returns>
        public async Task<IActionResult> GetDeviceStatus(string authID, string passwayno1, string passwayno2)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }
                AppBasicCache.AddOrUpdateElement(AppBasicCache.FrontPasswayMap, authID, new List<string>() { passwayno1, passwayno2 });
                return ResOk(true, "ok");
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "[GetDeviceStatus]设备状态设置异常：" + ex.ToString());
                return ResOk(true, "ok");
            }
        }

        /// <summary>
        /// 控制板获取道闸状态、是否有车
        /// </summary>
        /// <param name="p"></param>
        /// <param name="online"></param>
        /// <param name="iOpenGate"></param>
        /// <param name="ihascar"></param>
        private async Task<(int, int)> GetLanDeviceStatus(Model.DeviceExt p, int online, int opengate, int iOpenGate, int ihascar)
        {
            iOpenGate = 0;
            ihascar = 0; //是否有车
            try
            {
                if (p.Device_IO == 1 && AppBasicCache.GetSentryPasswayDic.TryGetValue(p.Device_PasswayNo, out var pway) && pway.Passway_EnableBoard == 1)
                {
                    iOpenGate = TcpConnPools.Controller.ControllerHelper.GetStatusByY312(pway.Passway_No, TcpConnPools.Controller.BarrierY312.BarrierOfY312StatusIndex.BarrierStatus, pway.Passway_SameInOut ?? 0, pway.Passway_OutNo);

                    //读取相机回路状态，是否有车
                    if (online == 1)
                    {
                        int xxss = await CameraController.ReadGPIOInStatusAsync(p.Device_IP);
                        if (xxss > 0)
                        {
                            ihascar = 1;
                        }
                    }

                    if (ihascar == 0)
                    {
                        //读取地感判断是否有车
                        if (pway.Passway_IsBackCar == 1 && pway.Passway_EnableBoard == 1)
                        {
                            var g1 = TcpConnPools.Controller.ControllerHelper.GetStatusByY312(pway.Passway_No, TcpConnPools.Controller.BarrierY312.BarrierOfY312StatusIndex.GroundSense1, pway.Passway_SameInOut ?? 0, pway.Passway_OutNo);
                            var g2 = TcpConnPools.Controller.ControllerHelper.GetStatusByY312(pway.Passway_No, TcpConnPools.Controller.BarrierY312.BarrierOfY312StatusIndex.GroundSense2, pway.Passway_SameInOut ?? 0, pway.Passway_OutNo);
                            var g3 = TcpConnPools.Controller.ControllerHelper.GetStatusByY312(pway.Passway_No, TcpConnPools.Controller.BarrierY312.BarrierOfY312StatusIndex.GroundSense3, pway.Passway_SameInOut ?? 0, pway.Passway_OutNo);

                            if (g1 > 0 || g2 > 0 || g3 > 0)
                            {
                                ihascar = 1;
                            }
                        }
                    }
                }
                else
                {
                    iOpenGate = opengate;
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetDevice, $"读取设备状态失败：{p?.Device_Name}，{ex}", SecondIndex.Monitoring);
            }

            return (iOpenGate, ihascar);
        }


        /// <summary>
        /// 修改价格
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public async Task<IActionResult> ModifyMoney(string jsonModel, string code = "")
        {
            decimal sumMoney = 0;
            string carno = "";
            decimal dmoney1 = 0;

            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkWinPower("WinFormMonitor", PowerEnum.ModifyMoney.ToString(), false, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"修改价格,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限修改价格");
                }

                if (!AppBasicCache.IsSendOrder) return ResOk(false, "很抱歉，当前模式不允许操作");

                HtmlParameter.ConfirmPassOut param = Utils.ClearModelRiskSQL<HtmlParameter.ConfirmPassOut>(jsonModel);
                if (param == null)
                {
                    return ResOk(false, $"改价失败");
                }

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (DirectCloudMQTT.MqttClient.Instance?.IsOnline ?? false)
                    {
                        if (!CameraGlobal.IsEmergency)
                        {
                            return ResOk(false, "很抱歉，当前云平台在线，不允许操作");
                        }
                    }
                }

                string passwayno = param.ParkOrder_OutPasswayNo;
                if (string.IsNullOrEmpty(passwayno))
                {
                    return ResOk(false, $"未找到车道信息");
                }

                if (param.PayedMoney == null)
                {
                    return ResOk(false, $"请输入改价金额");
                }

                if (BLL.ConfirmRelease.Results.TryGetValue(passwayno, out var data))
                {
                    carno = param.ParkOrder_CarNo.Trim();
                    dmoney1 = param.PayedMoney.Value;

                    var unpaidPayedamount = data.unpaidresult?.Sum(m => m.payres?.orderamount) ?? 0; //追缴金额
                    if (unpaidPayedamount > 0 && dmoney1 < unpaidPayedamount)
                    {
                        return ResOk(false, $"改价不能小于追缴金额，当前追缴金额为：{unpaidPayedamount}元");
                    }


                    var chuzhiMoney = data.payres?.chuzhiamount ?? 0;
                    sumMoney = data.payres?.orderamount ?? 0 + unpaidPayedamount;

                    if (dmoney1 + chuzhiMoney > sumMoney)
                    {
                        var msg = $"改价不能大于应收金额，当前应收金额为：{sumMoney}元";
                        if (chuzhiMoney > 0) { msg += $"，已收储值金额：{chuzhiMoney}元"; }
                        return ResOk(false, msg);
                    }

                    dmoney1 = dmoney1 - unpaidPayedamount;

                    if (data.passres != null) data.passres.carno = carno;
                    if (data.payres != null) data.payres.payedamount = dmoney1;

                    if (dmoney1 > 0 && (data.passres.code == 2 || data.passres.code == 4) && data.resorder.resOut.onenter == 0)
                    {
                        if (data.resorder.resOut.parkorder == null && data.resorder.resOut.noRecordOrder == null)
                        {
                            if (data.payres == null)
                            {
                                data.payres = new ChargeModels.PayResult()
                                {
                                    payed = 1,
                                    orderamount = dmoney1,
                                    payedamount = dmoney1
                                };
                            }
                            else
                            {
                                if (dmoney1 > data.payres.orderamount)
                                {
                                    return ResOk(false, $"实收金额不能大于应收金额");
                                }
                            }

                            string parkno = AppBasicCache.SentryHostInfo?.SentryHost_ParkNo;
                            PassTool.PassHelper.AddNoRecordParkOrder(parkno, ref data, lgAdmin?.Admins_Account, lgAdmin?.Admins_Name, isUpdateCoudle: true); //无入场记录创建订单
                        }
                        else
                        {
                            if (data.payres == null)
                            {
                                data.payres = new ChargeModels.PayResult()
                                {
                                    payed = 1,
                                    orderamount = dmoney1,
                                    payedamount = dmoney1
                                };
                            }
                            else
                            {
                                if (dmoney1 > data.payres.orderamount)
                                {
                                    return ResOk(false, $"实收金额不能大于应收金额");
                                }
                            }
                        }
                    }

                    PasswayConfirmReleaseUtil.AddOrUpdateResult(data);

                    var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice, "语音播报不成功");
                    if (acionRes != null)
                    {
                        return acionRes;
                    }

                    if (data.passres.code == 4)
                    {
                        BroadcastUtil.MinimumCharge(data, mainDevice);
                    }
                    else if (data.passres.code == 3)
                    {
                        BroadcastUtil.WaitInLine(data, mainDevice);
                    }
                    else
                    {
                        BroadcastUtil.ConfirmRelease(data, mainDevice);
                    }
                }
                else
                {
                    return ResOk(false, "当前车道已无车辆");
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ModifyMoney, $"{carno}，原价{sumMoney}，改价{dmoney1}", SecondIndex.Monitoring);

                return ResOk(true, "改价成功");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ModifyMoney, $"{carno}，原价{sumMoney}，改价{dmoney1}，异常：{ex}", SecondIndex.Monitoring);
                return ResOk(false, "改价失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 获取场内车辆信息列表
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <returns></returns>
        public async Task<IActionResult> GetInCarList(string carno, string code = "")
        {
            try
            {
                if (DataCache.Admin.Get(code) == null && !await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }

                if (!string.IsNullOrEmpty(carno)) carno = carno.Trim();
                if (string.IsNullOrEmpty(carno))
                {
                    return ResOk(true, "成功");
                }

                var poList = BLL.ParkOrder.GetInCarNo(carno);

                if (poList.Count() == 0)
                {
                    var newCarno = Regex.Replace(carno, "[a-zA-Z]", "");
                    if (newCarno != carno && !string.IsNullOrEmpty(newCarno))
                    {
                        poList = BLL.ParkOrder.GetInCarNo(newCarno);
                    }
                }

                return ResOk(true, "成功", poList.Select(x => new { carno = x.InCar_CarNo }).ToList());
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetInCarList, $"{carno}，异常：{ex}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
                ;
            }
        }

        /// <summary>
        /// 获取场内车停车订单
        /// </summary>
        /// <param name="orderno">订单编码</param>
        /// <returns></returns>
        public async Task<IActionResult> GetCarOrder(string orderno)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }

                var order = BLL.ParkOrder.GetEntity(orderno);
                return ResOk(true, "成功", order);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCarOrder, $"订单号：{orderno}，异常：{ex}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 更新当前权限组信息
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> UpdatePowerGroup()
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }

                if (lgAdmin != null)
                {
                    var powergroup = BLL.PowerGroup.GetEntity(lgAdmin.Admins_PowerNo);
                    if (powergroup == null)
                    {
                        Response.Cookies.Delete(Sessions.BSAdminSession);
                        return ResOk(false, "权限组不存在");
                    }

                    if (powergroup.PowerGroup_Enable != 1)
                    {
                        Response.Cookies.Delete(Sessions.BSAdminSession);
                        return ResOk(false, "权限已被禁用");
                    }

                    JObject objControl = JObject.Parse(powergroup.PowerGroup_Value);
                    var powerValueStr = objControl["WinFormMonitor"]; //获取到当前控制器下的所有权限

                    JObject objControl2 = JObject.Parse(lgAdmin.PowerGroup_Value);
                    var powerValueStr2 = objControl2["WinFormMonitor"]; //获取到当前控制器下的所有权限

                    if (powerValueStr.ToString() != powerValueStr2.ToString())
                    {
                        lgAdmin.PowerGroup_Name = powergroup.PowerGroup_Name;
                        lgAdmin.PowerGroup_Value = powergroup.PowerGroup_Value;

                        string tokenAndId = new Cookies().getCookie(Sessions.BSAdminSession);
                        if (string.IsNullOrEmpty(tokenAndId))
                        {
                            var code = HttpHelper.HttpContext.Request.Headers["Authorization"];
                            DataCache.Admin.Set(code, lgAdmin);
                        }

                        Sessions.BSDelAdminSession(lgAdmin);
                        Response.Cookies.Delete(Sessions.BSAdminSession);

                        Sessions.BSSetAdminSession(lgAdmin);

                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{lgAdmin.Admins_Account}]写入登录缓存：[{lgAdmin.token}][{lgAdmin.Admins_Account}]");
                        //Response.Cookies.Append(Sessions.BSAdminSession, Cookies.Encryption(lgAdmin.token + "," + lgAdmin.Admins_ID), new CookieOptions() { IsEssential = true });
                        Response.Cookies.Append(Sessions.BSAdminSession, Cookies.Encryption(lgAdmin.token + "," + lgAdmin.Admins_Account),
                               new CookieOptions()
                               {
                                   IsEssential = true, // 这个 Cookie 必须设置，不受用户隐私设置影响
                                   Expires = DateTimeHelper.GetNowTime().AddDays(365), // 1年过期
                               });
                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{lgAdmin.Admins_Account}]写入cookie");

                        AppBasicCache.CurrentAdmins = lgAdmin;

                        return ResOk(true, "ok", 1);
                    }
                }
                else
                {
                    return ResOk(false, "请登录", 0);
                }

                return ResOk(true, "ok", 0);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.UpdatePowerGroup, $"异常：{ex}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
                ;
            }
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        public async Task<IActionResult> GetParkOrderByNo(string ParkOrder_No)
        {
            try
            {
                if (!await checkPower(lgAdmin, "Index", false)) { return ResOk(false, "无权限"); }

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);

                return ResOk(true, "", new { model = model });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetCarOrder, $"订单号：{ParkOrder_No}，异常：{ex}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }
        /// <summary>
        /// 修改车牌
        /// </summary>
        public async Task<IActionResult> UpdateCarNo(string jsonModel)
        {
            string ParkOrder_No = "";
            string NewCarNo = "";
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifyRecord.ToString(), false, lgAdmin))
                {
                    LogManagementMap.WriteToFileException(null, $"修改场内记录,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return ResOk(false, "无权限修改场内记录");
                }

                Model.ParkOrder obj = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (obj == null) { return ResOk(false, "参数错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_No)) { return ResOk(false, "订单号错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_CarNo)) { return ResOk(false, "车牌号错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_CarCardType)) { return ResOk(false, "车牌类型错误"); }
                if (obj.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间错误"); }
                if (obj.ParkOrder_EnterTime > DateTimeHelper.GetNowTime()) { return ResOk(false, "入场时间不能大于当前时间"); }

                ParkOrder_No = obj.ParkOrder_No;
                NewCarNo = obj.ParkOrder_CarNo;
                if (string.IsNullOrEmpty(NewCarNo) || NewCarNo.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(NewCarNo))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                NewCarNo = NewCarNo.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");//字母O替换数字0
                NewCarNo = NewCarNo.ToUpper();
                string NewCarCardType = obj.ParkOrder_CarCardType;
                DateTime? NewEnterTime = obj.ParkOrder_EnterTime;

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                if (model == null) { return ResOk(false, "停车订单不存在"); }

                var res = BLL.ParkOrder.UpdateOrder(model, NewCarNo, NewCarCardType, NewEnterTime, model.ParkOrder_StatusNo, obj.ParkOrder_Remark, true, CheckSpaceNumByEnterTime: false, isSentrymodeify: true, lgAdmins: lgAdmin);
                if (res.code == 1)
                {
                    DataCache.ParkOrder.Set(model.ParkOrder_No, model);

                    var confirmOrder = BLL.ConfirmRelease.Results.Where(kv => kv.Value.passres?.parkorderno == ParkOrder_No).FirstOrDefault();
                    if (confirmOrder.Value != null)
                    {
                        if (confirmOrder.Value.passres != null)
                        {
                            confirmOrder.Value.passres.carno = NewCarNo;
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(NewCarNo, ParkOrder_No, CloseNoInPark: false, mode: 6);
                        }
                    }

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.UpdateCarNo, $"成功，{NewCarNo} {ParkOrder_No}，车牌类型：{NewCarCardType}，{jsonModel}", SecondIndex.Monitoring);
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.UpdateCarNo, $"失败，{NewCarNo} {ParkOrder_No}，车牌类型：{NewCarCardType}，{jsonModel}，异常：{res.msg}", SecondIndex.Monitoring);
                    return ResOk(false, res.msg);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.UpdateCarNo, $"异常，{NewCarNo} {ParkOrder_No}，{jsonModel}，异常：{ex.Message}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取多位多车列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public async void GetMultiCarList(int pageIndex, int pageSize, string orderno, string carno)
        {
            await Response.WriteAsync((await SearchMultiCarList(pageIndex, pageSize, orderno, carno)).ParseJson());
        }

        /// <summary>
        /// 获取多位多车列表
        /// </summary>
        public async Task<Model.PageResult> SearchMultiCarList(int pageIndex, int pageSize, string orderno, string carno)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.Login.ToString()))
                {
                    oModel.code = 4;
                    oModel.msg = "无权限";
                    return oModel;
                }

                if (string.IsNullOrEmpty(orderno))
                {
                    oModel.code = 4;
                    oModel.msg = "订单号不允许为空";
                    return oModel;
                }

                var po = BLL.ParkOrder.GetEntity(orderno);
                if (po == null)
                {
                    var incar = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "*", $"InCar_CarNo='{carno}' and InCar_Status='200'");
                    if (incar != null)
                    {
                        po = BLL.ParkOrder.GetEntity(incar.InCar_ParkOrderNo);
                    }

                    if (po == null)
                    {
                        oModel.code = 4;
                        oModel.msg = "订单不存在";
                        return oModel;
                    }
                }

                var car = AppBasicCache.GetElement(AppBasicCache.GetCar, po.ParkOrder_CarNo);
                if (car == null)
                {
                    oModel.code = 4;
                    oModel.msg = "未找到多位多车信息";
                    return oModel;
                }

                //车主信息
                var owner = AppBasicCache.GetElement(AppBasicCache.GetOwner, car.Car_OwnerNo);
                if (owner == null)
                {
                    oModel.code = 4;
                    oModel.msg = "未找到车主信息";
                    return oModel;
                }

                var cars = AppBasicCache.GetElementsByValues(AppBasicCache.GetCar, car.Car_OwnerNo, "Car_OwnerNo");

                var allList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "*", $" InCar_Status=200 and InCar_CarNo in ({string.Join(",", cars.Select(m => $"'{m.Car_CarNo}'"))})");

                //车位剩余信息
                var spaceDic = new Dictionary<string, string>();

                var spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{car.Car_OwnerNo}'");
                string nochangespaces = "";

                var incarNum = allList.Count;
                var countedCars = new HashSet<string>();
                var allspace = AppBasicCache.GetParkAreas.Values.Select(x => x.ParkArea_No).ToArray();
                foreach (var item in spaces)
                {
                    if (item.StopSpace_AreaName == null) continue;

                    if (!string.IsNullOrEmpty(item.StopSpace_AreaName)) nochangespaces += $" {item.StopSpace_AreaName} : {item.StopSpace_Number}  ";

                    if (item.StopSpace_Number > 0 && incarNum > 0)
                    {
                        var areaNumbers = item.StopSpace_AreaNo == "[\"0\"]" ? allspace : item.StopSpace_AreaNo.Split(',');
                        Array.Sort(areaNumbers, (a, b) => a.Length.CompareTo(b.Length));

                        var inCarCount = 0;
                        foreach (var car1 in allList)
                        {
                            if (countedCars.Contains(car1.InCar_CarNo))
                                continue;

                            if (areaNumbers.Any(area => area.Contains(car1.InCar_ParkAreaNo)))
                            {
                                inCarCount++;
                                if (inCarCount < item.StopSpace_Number)
                                {
                                    countedCars.Add(car1.InCar_CarNo);
                                }
                            }
                        }

                        if (inCarCount > item.StopSpace_Number) inCarCount = item.StopSpace_Number.Value;

                        if (incarNum > inCarCount)
                        {
                            incarNum = incarNum - inCarCount;
                        }
                        else
                        {
                            incarNum = 0;
                        }

                        item.StopSpace_Number = item.StopSpace_Number - inCarCount;
                        if (item.StopSpace_Number < 0) item.StopSpace_Number = 0;

                        if (spaceDic.TryGetValue(item.StopSpace_AreaName, out var _))
                        {
                            spaceDic[item.StopSpace_AreaName] = item.StopSpace_Number.ToString();
                        }
                        else
                        {
                            spaceDic.TryAdd(item.StopSpace_AreaName, item.StopSpace_Number.ToString());
                        }
                    }
                    else
                    {
                        if (!spaceDic.TryGetValue(item.StopSpace_AreaName, out var _))
                        {
                            spaceDic.TryAdd(item.StopSpace_AreaName, item.StopSpace_Number.ToString());
                        }
                        else
                        {
                            spaceDic[item.StopSpace_AreaName] = item.StopSpace_Number.ToString();
                        }
                    }
                }
                nochangespaces = nochangespaces.TrimEnd(',');

                var remainspaces = string.Join("    ", spaceDic.Select(m => $"{m.Key} : {m.Value}"));

                var msgJson = TyziTools.Json.ToString(new { ownerName = owner.Owner_Name, parkingSpot = owner.Owner_Space, parkingArea = nochangespaces, carCount = cars.Count, ownerCarCount = allList.Count, remainspaces = " " + remainspaces });

                var list = allList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();
                list.ForEach(m =>
                {
                    var area = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, m.InCar_ParkAreaNo);
                    m.InCar_ParkAreaNo = area.ParkArea_Name;
                });

                oModel.code = 0;
                oModel.data = list;
                oModel.count = allList.Count;
                oModel.msg = msgJson;
                return oModel;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.Search, $"查询多位多车异常：{ex.Message}", SecondIndex.Monitoring);
                oModel.code = 4;
                oModel.msg = ex.Message;
                return oModel;
            }
        }
        #endregion

        #region **【自定义语音】
        /// <summary>
        /// 自定义语音视图
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> CustomBroadVoice()
        {
            if (!await checkWinPower("WinFormMonitor", PowerEnum.Login.ToString()))
            {
                LogManagementMap.WriteToFileException(null, $"加载自定义语音视图,[{lgAdmin?.Admins_Account}]账号检测失败");
                return new EmptyResult();
            }

            return View();
        }

        // 添加获取播报内容列表的方法
        public IActionResult GetBroadContentList()
        {
            try
            {
                var content = AppBasicCache.GetPolicyPark.PolicyPark_BroadContent;
                if (content == null) { return ResOk(true, "获取成功"); }

                var list = content.Split('|').ToList();

                return ResOk(true, "获取成功", list);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.GetBroadContent, $"获取自定义语音播报内容列表异常：{ex.Message}", SecondIndex.Monitoring);
                return ResOk(false, "获取失败");
            }
        }

        // 播报自定义语音
        public async Task<IActionResult> BroadCustomVoice(string content, string passwayno)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.ManualTrigger.ToString()))
                {
                    LogManagementMap.WriteToFileException(null, $"语音播报,[{lgAdmin?.Admins_Account}]账号检测失败");
                    return new EmptyResult();
                }

                var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice, "语音播报不成功");
                if (acionRes != null)
                {
                    return acionRes;
                }

                if (mainDevice.Device_Category == 9)
                {
                    BroadcastUtil.SendMotoOrder(mainDevice.Device_PasswayNo, content, content);

                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.BroadVoice, $"[{lgAdmin?.Admins_Account}][{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{content}]", SecondIndex.Monitoring);

                    return ResOk(true, "播报成功");
                }

                var data485 = Passthrough485.Passthrough485Util.InstantDisplay(content);
                var ret = await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, nameof(Passthrough485.Passthrough485Util.InstantDisplay));

                if (ret)
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.BroadCustomVoice, $"[{lgAdmin?.Admins_Account}][{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{content}]", SecondIndex.Monitoring);
                    return ResOk(true, "播报成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.BroadCustomVoice, $"播报失败：[{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}][{content}]", SecondIndex.Monitoring);
                    return ResOk(false, "播报失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.BroadCustomVoice, $"播报自定义语音异常：{ex.Message}", SecondIndex.Monitoring);
                return ResOk(false, "播报失败");
            }
        }

        #endregion

        #region **打印小票

        public async Task<IActionResult> PrintReceiptSetting()
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.PrintReceipt.ToString(), false, lgAdmin))
                {
                    return new EmptyResult();
                }

                var configuration = new BLL.ReceiptPrinter().GetPrintSetting<Model.PrintConfigurations>("print");
                if (configuration != null)
                {
                    var json = string.IsNullOrWhiteSpace(configuration.ConfigValue)
                                ? TyziTools.Json.ToString(new Model.PrintConfigDetail(), true)
                                : configuration.ConfigValue;

                    var config = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                    var model2 = new Dictionary<string, string>();
                    model2["enable"] = config.ContainsKey("enable") ? config["enable"] : "";
                    model2["mode"] = config.ContainsKey("mode") ? config["mode"] : "";
                    model2["title"] = config.ContainsKey("title") ? config["title"] : "";
                    model2["subtitle"] = config.ContainsKey("subtitle") ? config["subtitle"] : "";
                    model2["endcontent"] = config.ContainsKey("endcontent") ? config["endcontent"] : "";
                    return View("PrintReceiptSetting/PrintReceiptSetting", model2);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"加载打印设置异常：{ex.Message},{ex.StackTrace}");
            }
            var model = new Dictionary<string, string>();
            model["enable"] = "0";
            model["mode"] = "0";
            model["title"] = "";
            model["subtitle"] = "";
            model["endcontent"] = "";
            return View("PrintReceiptSetting/PrintReceiptSetting", model);
        }

        [HttpPost]
        public async Task<IActionResult> SavePrintSettings([FromForm] Dictionary<string, string> model)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.PrintReceipt.ToString(), false, lgAdmin))
                {
                    return Json(new { success = false, message = "无权限" });
                }

                Model.PrintConfigurations configuration = new Model.PrintConfigurations();
                configuration.ConfigKey = "PrintSettings";
                configuration.ConfigValue = JsonConvert.SerializeObject(model);
                configuration.Description = "打印设置";
                configuration.UpdatedAt = DateTime.Now;

                Model.PrintConfigurations result = new BLL.ReceiptPrinter().SetPrintSetting<Model.PrintConfigurations>("print", configuration);
                if (result == null)
                {
                    return Json(new { success = false, message = "保存失败，请重试" });
                }
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.PrintReceiptSetting, $"启用打印：{(model["enable"] == "1" ? "已启用" : "已禁用")}，模式：{(model["mode"] == "1" ? "自动" : "手动")}，标题：{model["title"]}", SecondIndex.Monitoring);


                return Json(new { success = true, message = "保存成功" });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.PrintReceiptSetting, $"小票打印保存异常：{ex.Message}", SecondIndex.Monitoring);
            }
            return Json(new { success = false, message = "保存失败" });
        }

        public async Task<IActionResult> PrintReceipt()
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.PrintReceipt.ToString(), false, lgAdmin))
                {
                    HttpHelper.HttpContext.Response.Redirect(@"/Static/html/nopower.html");
                    return new EmptyResult();
                }

                string carNo = CMSRequest.GetQueryString("CarNo", false);
                string passwayNo = CMSRequest.GetQueryString("passwayNo", false);
                string type = CMSRequest.GetQueryString("type", false);
                string carCardTypeNo = CMSRequest.GetQueryString("carCardType", false);

                Model.CarCardType carCardType = BLL.CarCardType.GetEntity(carCardTypeNo);

                var configuration = new BLL.ReceiptPrinter().GetPrintSetting<Model.PrintConfigurations>("print");
                if (configuration == null)
                {
                    var model2 = new Dictionary<string, string>();
                    model2["enable"] = "0";
                    model2["mode"] = "0";
                    model2["title"] = "";
                    model2["subtitle"] = "";
                    model2["endcontent"] = "";
                    return View("PrintReceiptSetting/PrintReceiptSetting", model2);
                }

                var json = string.IsNullOrWhiteSpace(configuration.ConfigValue)
                                ? TyziTools.Json.ToString(new Model.PrintConfigDetail(), true)
                                : configuration.ConfigValue;

                var model = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                if (model == null || model["enable"] != "1")
                {
                    var model2 = new Dictionary<string, string>();
                    model2["enable"] = "0";
                    model2["mode"] = "0";
                    model2["title"] = "";
                    model2["subtitle"] = "";
                    model2["endcontent"] = "";
                    return View("PrintReceiptSetting/PrintReceiptSetting", model2);
                }

                if (type == "in")
                {
                    model["CarNo"] = carNo;
                    model["CarCardType"] = carCardType.CarCardType_Name;

                    var enterTime = CMSRequest.GetQueryString("enterTime", false);
                    if (string.IsNullOrEmpty(enterTime) || enterTime == "undefined") enterTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                    model["InTime"] = string.IsNullOrWhiteSpace(enterTime) ? "" : Convert.ToDateTime(enterTime).ToString("yyyy年MM月dd日 HH:mm:ss");
                    model["OutTime"] = "";
                    model["PayedAmount"] = "0.00";
                    model["Operator"] = lgAdmin?.Admins_Name;
                    if (!model.ContainsKey("title")) model.Add("title", "");
                    if (!model.ContainsKey("subtitle")) model.Add("subtitle", "");
                    if (!model.ContainsKey("endcontent")) model.Add("endcontent", "");
                    return View("PrintReceiptSetting/PrintReceipt", model);
                }
                if (type == "out")
                {
                    model["CarNo"] = carNo;
                    model["CarCardType"] = carCardType.CarCardType_Name;

                    var enterTime = CMSRequest.GetQueryString("enterTime", false);
                    var outTime = CMSRequest.GetQueryString("outTime", false);
                    var payedMoney = CMSRequest.GetQueryString("payedMoney", false);
                    if (string.IsNullOrEmpty(enterTime) || enterTime == "undefined") enterTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    if (string.IsNullOrEmpty(outTime) || outTime == "undefined") outTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                    model["InTime"] = string.IsNullOrWhiteSpace(enterTime) ? "" : Convert.ToDateTime(enterTime).ToString("yyyy年MM月dd日 HH:mm:ss");
                    model["OutTime"] = string.IsNullOrWhiteSpace(outTime) ? "" : Convert.ToDateTime(outTime).ToString("yyyy年MM月dd日 HH:mm:ss");
                    model["PayedAmount"] = payedMoney;//data.payres.payedamount.ToString("0.00");
                    model["Operator"] = lgAdmin?.Admins_Name;
                    if (!model.ContainsKey("title")) model.Add("title", "");
                    if (!model.ContainsKey("subtitle")) model.Add("subtitle", "");
                    if (!model.ContainsKey("endcontent")) model.Add("endcontent", "");
                    return View("PrintReceiptSetting/PrintReceipt", model);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.PrintReceiptSetting, $"手动打印小票异常：{ex.Message}", SecondIndex.Monitoring);
            }
            return View("PrintReceiptSetting/PrintReceipt", null);
        }

        #endregion

        #region **修改余位

        public async Task<IActionResult> ModifyParkSpace()
        {
            var model = new Dictionary<string, string>();
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifySpace.ToString(), false, lgAdmin))
                {
                    return new EmptyResult();
                }

            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"加载打印设置异常：{ex.Message},{ex.StackTrace}");
            }

            return View("ModifyParkSpace/Index", model);
        }

        public async Task<IActionResult> SaveParkingSpaces(string updates)
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifySpace.ToString(), false, lgAdmin))
                {
                    return Json(new { success = false, message = "无权限" });
                }

                //updates.push({
                //           areaName: areaName,
                //           currentValue: currentValue,
                //           newValue: newValue
                //       });

                var updatesList = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(updates);
                if (updatesList == null || updatesList.Count == 0)
                {
                    return Json(new { success = false, message = "没有需要保存的修改" });
                }

                //检验updates
                foreach (var update in updatesList)
                {
                    var areaName = update["areaName"];
                    var currentValue = Utils.ObjectToInt(update["currentValue"]);
                    var newValue = Utils.ObjectToInt(update["newValue"]);
                    if (newValue < 0)
                    {
                        return Json(new { success = false, message = $"{areaName} 修改余位数必须大于等于0" });
                    }
                    //修改余位数不能大于车位数
                    var area = AppBasicCache.GetParkAreas.Values.FirstOrDefault(x => x.ParkArea_Name == areaName);
                    if (area != null)
                    {
                        if (newValue > area.ParkArea_SpaceNum)
                        {
                            return Json(new { success = false, message = $"{area.ParkArea_Name} 修改余位数不能大于车位数" });
                        }
                    }
                }

                var areaRemark = "";

                List<Model.ParkArea> areas = new List<Model.ParkArea>();
                // 更新车位余位
                foreach (var update in updatesList)
                {
                    var areaName = update["areaName"];
                    var currentValue = Utils.ObjectToInt(update["currentValue"]);
                    var newValue = Utils.ObjectToInt(update["newValue"]);

                    var area = AppBasicCache.GetParkAreas.Values.FirstOrDefault(x => x.ParkArea_Name == areaName)?.Copy();
                    if (area != null)
                    {
                        if (area.ParkArea_RemainSpace == null) area.ParkArea_RemainSpace = 0;
                        area.ParkArea_RemainSpace += (newValue - currentValue);
                        areaRemark += $"{area.ParkArea_Name}： 余位 {currentValue}，修改余位 {newValue}；";
                        areas.Add(area);
                    }
                }

                if (areas.Count > 0)
                {
                    var r = BLL.ParkArea._AddOrUpdateModel(areas);
                    if (r >= 0)
                    {
                        var parkares = BLL.ParkArea.GetAllEntity("*", "");
                        parkares.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetParkAreas, item.ParkArea_No, item); });

                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ModifySpaces, $"{areaRemark}", SecondIndex.Monitoring);

                        _ = CustomThreadPool.ScheduledTaskPool?.QueueTask(null, () =>
                        {
                            ParkSpaceUtil.UpdateDate();
                            return Task.CompletedTask;
                        });

                        return Json(new { success = true, message = "保存成功" });
                    }
                    else
                    {
                        return Json(new { success = false, message = "保存失败，请重试" });
                    }
                }

                return Json(new { success = true, message = "保存成功" });
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"修改车位余位异常：{ex.Message},{ex.StackTrace}");
                return Json(new { success = false, message = "保存失败" });
            }
        }
        /// <summary>
        /// 重置
        /// </summary>
        /// <returns></returns>
        public async Task<IActionResult> ReSetParkingSpaces()
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.ModifySpace.ToString(), false, lgAdmin))
                {
                    return Json(new { success = false, message = "无权限" });
                }

                var areas = BLL.ParkArea.GetAllEntity("*", "");
                areas.ForEach(x =>
                {
                    x.ParkArea_RemainSpace = 0;
                });

                if (areas.Count > 0)
                {
                    var r = BLL.ParkArea._AddOrUpdateModel(areas);
                    if (r >= 0)
                    {
                        areas.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetParkAreas, item.ParkArea_No, item); });

                        BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.ModifySpaces, $"重置余位修改数", SecondIndex.Monitoring);

                        return Json(new { success = true, message = "重置成功" });
                    }
                    else
                    {
                        return Json(new { success = false, message = "重置失败，请重试" });
                    }
                }

                return Json(new { success = true, message = "重置成功" });
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"重置车位余位异常：{ex.Message},{ex.StackTrace}");
                return Json(new { success = false, message = "重置失败" });
            }
        }

        #endregion

        #region **【工具测试】

        /// <summary>
        /// 出入通道
        /// </summary>
        public async Task<IActionResult> SltPasswayList2()
        {
            try
            {
                var code = CMSRequest.GetQueryString("code", true);
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("Passway_ID,Passway_No,Passway_Name,Passway_SameInOut,Passway_OutNo", $"Passway_SentryHostNo='{AppBasicCache.SentryHostInfo?.SentryHost_No}'");

                return ResOk(true, "", passways);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取出入通道列表异常");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 出入通道
        /// </summary>
        public async Task<IActionResult> SltPasswayList3()
        {
            try
            {
                var code = CMSRequest.GetQueryString("code", true);
                if (!await checkPower(lgAdmin, "", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("Passway_ID,Passway_No,Passway_Name,Passway_SameInOut,Passway_OutNo", $"Passway_SentryHostNo='{lgAdmin?.Admins_SentryHostNo}'");

                return ResOk(true, "", passways);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取出入通道列表异常");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 车牌识别
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <returns></returns>
        public async Task<IActionResult> InCarByPassNo(string passwayno, string carno, string time, string cartype = "")
        {
            try
            {
                if (!await checkWinPower("WinFormMonitor", PowerEnum.DebugTool.ToString()))
                {
                    LogManagementMap.WriteToFileException(null, $"调试工具,[{lgAdmin?.Admins_Account}]账号检测无权限");
                    return ResOk(false, "无权限");
                }

                if (string.IsNullOrEmpty(carno))
                {
                    return ResOk(false, "请输入车牌号码");
                }

                if (string.IsNullOrEmpty(carno) || carno.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(carno.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                carno = carno.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");//字母O替换数字0
                carno = carno.ToUpper();

                if (string.IsNullOrEmpty(time))
                {
                    return ResOk(false, "请输入识别时间");
                }

                if (string.IsNullOrEmpty(passwayno))
                {
                    return ResOk(false, "请选择识别车道");
                }

                if (string.IsNullOrEmpty(cartype)) cartype = "蓝牌车";

                LogManagementMap.WriteToFile(LoggerEnum.ManualOperation, $"岗亭后台人工车牌识别【{passwayno}】【{carno}】");
                Model.Device device = BLL.Device.GetEntityByPasswayNo(passwayno);
                if (device == null)
                {
                    return ResOk(false, "未设置车道主相机");
                    ;
                }

                var tempImage = CameraImageHelper.ImageSaveHSPathBig(carno, device.Device_SentryHostNo);
                //await CameraController.GetSnapshotAsync(device.Device_IP, tempImage, device.Device_SentryHostNo, carno);
                string base64 = string.Empty;
                string errmsg = string.Empty;
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                {
                    var ffaa = await LPRTools.GetSnapShootToJpeg(passwayno, tempImage, base64, errmsg);
                });

                System.Threading.Thread.Sleep(100);

                var m = new CarPlateInfo
                {
                    CarPlate = carno,
                    TriggerTime = string.IsNullOrEmpty(time) ? DateTimeHelper.GetNowTime() : DateTime.Parse(time),
                    BigJPGPath = tempImage,
                    CarmeraIP = device?.Device_IP,
                    CarTypeMode = cartype,
                    DeviceNo = device?.Device_No,
                    IsRealLicensePlate = true,
                    TriggerMode = 0,
                    PasswayNo = passwayno,
                    CarTypeNameLogo = "",
                    CarPlateColor = LPRTools.LicensePlateColor(cartype),
                    MixModel = cartype,
                    IsUnlicensedCar = false,
                    Mode = 1,
                    UseDeviceTime = true,
                    IsSupplement = (AppSettingConfig.SentryMode == VersionEnum.CloudServer && DirectCloudMQTT.MqttClient.Instance.IsOnline && (AppBasicCache.CurrentSysConfigContent?.SysConfig_CloudBoxLprEmergency ?? false ? false : true)) ? false : true,
                    CarType = LPRTools.LicensePlateColor(cartype)
                };

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CapturePictures, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno}", SecondIndex.Monitoring);

                //AppBasicCache.GetAllPassway.AsParallel().ForAll(p =>
                //{
                //    if (DeviceServerHandle.LicensePlateQueue.TryGetValue(p.Key, out var temp2))
                //    {
                //        temp2.Enqueue(m);
                //    }
                //});

                if (DeviceServerHandle.LicensePlateQueue.TryGetValue(passwayno, out var temp))
                {
                    temp.Enqueue(m);
                }
                else
                {
                    var queue = new ConcurrentQueue<CarPlateInfo>();
                    queue.Enqueue(m);
                    DeviceServerHandle.LicensePlateQueue.TryAdd(passwayno, queue);
                }

                return ResOk(true, "成功");
                ;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.CapturePictures, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno}，异常：{ex.Message}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
                ;
            }
        }

        /// <summary>
        /// 道闸控制板异常信息上报
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <returns></returns>
        public async Task<IActionResult> EventAbnormal(string passwayno, string carno, string time, string parkorderno)
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false)) { return ResOk(false, "无权限"); }

                var device = DeviceCommonUtil.GetPasswayMainDevice(passwayno);
                if (device != null)
                {
                    string sType = "003"; //Convert.ToString(reqPush["EventAbnormal"]["typeNo"]);

                    string remark = "道闸控制板异常事件记录";
                    //var ft = reqPush["EventAbnormal"];
                    //var fv = ft.Children().FirstOrDefault(m => m is JProperty p && p.Name == "msgData");
                    //if (fv != null && fv.HasValues)
                    //{
                    //    remark = Convert.ToString(((JProperty)fv).Value);
                    //}

                    //警报异常状态
                    int itype = 2; //未定义异常
                    switch (sType)
                    {
                        case "001":
                            itype = 5;
                            break; //遥控开闸
                        case "002":
                            itype = 6;
                            break; //遥控关闸
                        case "003":
                            itype = 7;
                            break; //长时间不落杆
                        case "004":
                            itype = 9;
                            break; //长时间停车
                        case "006":
                            itype = 8;
                            break; //道闸通信异常
                    }

                    var tempImage = CameraImageHelper.ImageSaveHSPathBig("Abnormal", device?.Device_SentryHostNo);
                    var imgUrl = LPRTools.GetSentryHostImg(tempImage);

                    string cNo = Common.Utils.CreateNumberWith();
                    string Url = BLL.ControlEvent.CreateDir(cNo);
                    bool isCache = BLL.ControlEvent.GetCacheSendVideo(ref Url, DateTime.Now);


                    bool isEzvizDevice = false;
                    var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(passwayno) && m.Value.Drive_Code == "10103");
                    if (ezvizDevice.Value != null)
                    {
                        isEzvizDevice = BLL.EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "跟车记录");
                    }

                    if (!isEzvizDevice)
                    {
                        var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(passwayno) && m.Value.Drive_Code == "10123");
                        if (vizCloudDevice.Value != null)
                        {
                            using var _vizCloudDevice = new TcpConnPools.SceneVideo.VziCloud.VziCloudService(accessKeyId: vizCloudDevice.Value.Device_AppKey, accessKeySecret: vizCloudDevice.Value.Device_Secret);
                            var startTime = DateTimeOffset.Now.AddSeconds(45).ToUnixTimeSeconds();
                            var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                            var quickUrl = await _vizCloudDevice.QuickGetRecordPlaybackUrlAsync(vizCloudDevice.Value.Device_No, startTime, endTime, cNo, vizCloudDevice.Value.Device_VideoChannelNo);
                            if (!string.IsNullOrEmpty(quickUrl))
                            {
                                Url = quickUrl;
                                isEzvizDevice = true;
                            }
                        }
                    }

                    var pushString = "{\"EventAbnormal\":{\"deviceNo\":\"" + device.Device_No + "\",\"orderNoOut\":\"" + parkorderno + "\",\"eventNo\":\"" + ("DA" + Utils.CreateNumber) + "\",\"msgData\":\"{\\\"GndSener1-H\\\":\\\"没有压上地感1\\\",\\\"GndSener1-L\\\":\\\"由于没有压上地感,所以没有离开地感1时间\\\",\\\"GndSener2-H\\\":\\\"没有压上地感2\\\",\\\"GndSener2-L\\\":\\\"由于没有压上地感2,所以没有离开地感2时间\\\",\\\"Reason\\\":\\\"车牌:-" + carno + "\\\\r\\\\n\\\"}\",\"timeStamp\":\"" + Utils.GetTimeStamp() + "\"}}";


                    var res = BLL.ParkApi.Follow(AppBasicCache.SentryHostInfo?.SentryHost_ParkNo, new Model.API.apiFollow
                    {
                        no = cNo,
                        bigimg = imgUrl,
                        cameraip = device.Device_IP,
                        camerano = device.Device_No,
                        content = pushString, //reqPush.ToString(),
                        remark = remark,
                        type = itype,
                        time = DateTime.Now,
                        video = Url
                    }, out var temp1);
                    if (res.success)
                    {
                        if (!isEzvizDevice)
                        {
                            var ffv = SceneVideoControler.GetDevice(passwayno, DeviceType.SceneVideo01);
                            if (ffv != null && ffv is TcpConnPools.SceneVideo.SceneVideo01.SceneVideoOf01 deviceSC)
                            {
                                if (!isCache)
                                {
                                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () => { await deviceSC.SendRecordVideoAsync(cNo); });
                                }

                                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () => { await deviceSC.SendCameraPhotoAsync(cNo); });
                            }
                            else
                            {
                                _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EventAbnormal, $"未找到场景相机，或场景相机设备未连接，车道：{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name}，车牌：{carno}", SecondIndex.Monitoring);
                                return ResOk(false, $"跟车记录=> 车道{passwayno}未找到场景相机，或场景相机设备未连接");
                            }
                        }
                        else
                        {
                            _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                        }
                    }
                }

                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EventAbnormal, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno}", SecondIndex.Monitoring);

                return ResOk(true, "成功");
                ;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmin, LogEnum.Sentry, SecondOption.EventAbnormal, $"{AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno)?.Passway_Name} {carno} 异常：{ex.Message}", SecondIndex.Monitoring);
                return ResOk(false, ex.Message);
                ;
            }
        }

        /// <summary>
        /// 道闸控制板跟车信息
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <returns></returns>
        public async Task<IActionResult> EventFollowing(string passwayno, string carno, string time, string parkorderno)
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false)) { return ResOk(false, "无权限"); }

                var device = DeviceCommonUtil.GetPasswayMainDevice(passwayno);
                if (device != null)
                {
                    //string tempImage = CameraImageHelper.ImageSaveHSPathBig("Following");
                    //Video.VideoConnect.VideoSnapShootToJpegOfPath(device.Device_IP, tempImage);

                    string cNo = Common.Utils.CreateNumberWith();
                    string Url = BLL.ControlEvent.CreateDir(cNo);
                    bool isCache = BLL.ControlEvent.GetCacheSendVideo(ref Url, DateTime.Now);

                    bool isEzvizDevice = false;
                    var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Drive_Code == "10103" && m.Value.Device_PasswayNo.Contains(passwayno));
                    if (ezvizDevice.Value != null)
                    {
                        isEzvizDevice = BLL.EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "跟车记录");
                    }

                    if (!isEzvizDevice)
                    {
                        var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(passwayno) && m.Value.Drive_Code == "10123");
                        if (vizCloudDevice.Value != null)
                        {
                            using var _vizCloudDevice = new TcpConnPools.SceneVideo.VziCloud.VziCloudService(accessKeyId: vizCloudDevice.Value.Device_AppKey, accessKeySecret: vizCloudDevice.Value.Device_Secret);
                            var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                            var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                            var quickUrl = await _vizCloudDevice.QuickGetRecordPlaybackUrlAsync(vizCloudDevice.Value.Device_No, startTime, endTime, cNo, vizCloudDevice.Value.Device_VideoChannelNo);
                            if (!string.IsNullOrEmpty(quickUrl))
                            {
                                Url = quickUrl;
                                isEzvizDevice = true;
                            }
                        }
                    }

                    string remark = "道闸控制板车辆跟车事件记录";
                    //var ft = reqPush["EventFollowing"];
                    //var fv = ft.Children().FirstOrDefault(m => m is JProperty p && p.Name == "msgData");
                    //if (fv != null && fv.HasValues)
                    //{
                    //    remark = Convert.ToString(((JProperty)fv).Value);
                    //}

                    var pushString = "{\"EventAstern\":{\"deviceNo\":\"" + device.Device_No + "\",\"orderNoOut\":\"" + parkorderno + "\",\"eventNo\":\"" + ("DA" + Utils.CreateNumber) + "\",\"msgData\":\"{\\\"GndSener1-H\\\":\\\"没有压上地感1\\\",\\\"GndSener1-L\\\":\\\"由于没有压上地感,所以没有离开地感1时间\\\",\\\"GndSener2-H\\\":\\\"没有压上地感2\\\",\\\"GndSener2-L\\\":\\\"由于没有压上地感2,所以没有离开地感2时间\\\",\\\"Reason\\\":\\\"车牌:-" + carno + "\\\\r\\\\n\\\"}\",\"timeStamp\":\"" + Utils.GetTimeStamp() + "\"}}";

                    var tempImage = CameraImageHelper.ImageSaveHSPathBig(cNo, device?.Device_SentryHostNo, true);
                    var imgUrl = LPRTools.GetSentryHostImg(tempImage);
                    var data = BLL.ParkApi.Follow(AppBasicCache.SentryHostInfo?.SentryHost_ParkNo, new Model.API.apiFollow
                    {
                        no = cNo,
                        bigimg = imgUrl, //tempImage
                        cameraip = device.Device_IP,
                        camerano = device.Device_No,
                        content = pushString, //reqPush.ToString(),
                        remark = remark,
                        type = 1,
                        time = DateTime.Now,
                        video = Url
                    }, out var temp1);

                    if (data.success)
                    {
                        if (!isEzvizDevice)
                        {
                            var ffv = SceneVideoControler.GetDevice(passwayno, DeviceType.SceneVideo01);
                            if (ffv != null && ffv is TcpConnPools.SceneVideo.SceneVideo01.SceneVideoOf01 deviceSC)
                            {
                                if (!isCache)
                                {
                                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () => { await deviceSC.SendRecordVideoAsync(Guid.NewGuid().ToString().Replace("_", "")); });
                                }

                                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () => { await deviceSC.SendCameraPhotoAsync(Guid.NewGuid().ToString().Replace("_", "")); });
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"跟车记录=> 车道{passwayno}未找到场景相机，或场景相机设备未连接");
                                _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                                //return ResOk(false, $"跟车记录=> 车道{passwayno}未找到场景相机，或场景相机设备未连接");
                            }
                        }
                        else
                        {
                            _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                        }
                    }
                    else
                    {
                        return ResOk(false, $"上传跟车事件失败");
                    }
                }

                return ResOk(true, "成功");
                ;
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
                ;
            }
        }

        /// <summary>
        /// 道闸控制板倒车
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <returns></returns>
        public async Task<IActionResult> EventAstern(string passwayno, string carno, string time, string parkorderno)
        {
            string sPassNo = passwayno;
            try
            {
                if (!await checkPower(lgAdmin, "", false)) { return ResOk(false, "无权限"); }

                if (AppBasicCache.GetAllPassway.TryGetValue(passwayno, out var pass))
                {
                    if (pass.Passway_IsBackCar == 1)
                    {
                        string sOrderNo = parkorderno;

                        var key = string.Empty;

                        // 优先使用车牌号生成缓存键（与存储时保持一致）
                        if (!string.IsNullOrWhiteSpace(carno))
                        {
                            key = carparking.SentryBox.BarrierDevice.BarrierDeviceUtilsl.GenerateBackCarCacheKey(sPassNo, carno);
                            if (!string.IsNullOrWhiteSpace(key))
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录使用车牌号生成缓存键：{key}");
                            }
                        }
                        // 如果没有车牌号，尝试使用订单号后缀
                        if (string.IsNullOrWhiteSpace(key) && !string.IsNullOrWhiteSpace(sOrderNo))
                        {
                            var nos = sOrderNo.Split('-');
                            if (nos.Length > 1 && !string.IsNullOrWhiteSpace(nos.Last()))
                            {
                                key = $"{sPassNo}{nos.Last()}";
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录使用订单号生成缓存键：{key}");
                            }
                        }

                        if (string.IsNullOrWhiteSpace(key))
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"接收倒车记录(缓存主键为空，跳过处理)：{passwayno}/{carno}/{parkorderno}");
                            return ResOk(false, "缓存主键为空，跳过处理");
                        }

                        Model.ParkOrder innerParkOrder = null;

                        if (AppBasicCache.GetMemoryCache.TryGetValue(key, out var temp1) && temp1 != null && temp1 is Model.ParkOrder temp2)
                        {
                            // 如果提供了订单号，验证订单号是否匹配；如果没有提供订单号，则直接使用缓存中的订单
                            if (string.IsNullOrWhiteSpace(sOrderNo) || temp2.ParkOrder_No == sOrderNo)
                            {
                                innerParkOrder = temp2;
                                AppBasicCache.GetMemoryCache.Remove(key);
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录从缓存中找到订单：{temp2.ParkOrder_No}");
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录缓存中订单号不匹配，期望：{sOrderNo}，实际：{temp2.ParkOrder_No}");
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录缓存中未找到数据，缓存键：{key}，temp1值：{JsonConvert.SerializeObject(temp1)}");
                        }

                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"Web倒车缓存数据处理结果 - 缓存数据：{JsonConvert.SerializeObject(temp1)}，最终订单：{innerParkOrder?.ParkOrder_No ?? "null"}");

                        //同进同出
                        if (innerParkOrder == null && pass.Passway_SameInOut == 1)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录尝试同进同出车道查找，出车道：{pass.Passway_OutNo}");

                            // 优先使用车牌号生成缓存键（与存储时保持一致）
                            if (!string.IsNullOrWhiteSpace(carno))
                            {
                                key = carparking.SentryBox.BarrierDevice.BarrierDeviceUtilsl.GenerateBackCarCacheKey(pass.Passway_OutNo, carno);
                                if (!string.IsNullOrWhiteSpace(key))
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录同进同出使用车牌号生成缓存键：{key}");
                                }
                            }
                            // 如果没有车牌号，尝试使用订单号后缀
                            if (string.IsNullOrWhiteSpace(key) && !string.IsNullOrWhiteSpace(sOrderNo))
                            {
                                var nos = sOrderNo.Split('-');
                                if (nos.Length > 1 && !string.IsNullOrWhiteSpace(nos.Last()))
                                {
                                    key = $"{pass.Passway_OutNo}{nos.Last()}";
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录同进同出使用订单号生成缓存键：{key}");
                                }
                            }

                            if (string.IsNullOrWhiteSpace(key))
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"接收倒车记录同进同出(缓存主键为空，跳过处理)：{passwayno}/{carno}/{parkorderno}");
                                return ResOk(false, "缓存主键为空，跳过处理");
                            }

                            if (AppBasicCache.GetMemoryCache.TryGetValue(key, out var temp3) && temp3 != null && temp3 is Model.ParkOrder temp4)
                            {
                                // 如果提供了订单号，验证订单号是否匹配；如果没有提供订单号，则直接使用缓存中的订单
                                if (string.IsNullOrWhiteSpace(sOrderNo) || temp4.ParkOrder_No == sOrderNo)
                                {
                                    sPassNo = pass.Passway_OutNo;
                                    innerParkOrder = temp4;
                                    AppBasicCache.GetMemoryCache.Remove(key);
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录同进同出从缓存中找到订单：{temp4.ParkOrder_No}");
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录同进同出缓存中订单号不匹配，期望：{sOrderNo}，实际：{temp4.ParkOrder_No}");
                                }
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"Web倒车记录同进同出缓存中未找到数据，缓存键：{key}，temp3值：{JsonConvert.SerializeObject(temp3)}");
                            }
                        }


                        if (innerParkOrder != null)
                        {
                            var device = DeviceCommonUtil.GetPasswayMainDevice(sPassNo);
                            if (device != null)
                            {

                                bool isCancel = false;
                                DynamicItems<int, string> dynamicItems = null;

                                #region 取消车道弹框
                                if (BLL.ConfirmRelease.Results.TryGetValue(sPassNo, out var oldData))
                                {
                                    string orderno = oldData.resorder?.resOut?.parkorder?.ParkOrder_No ?? oldData.passres?.parkorderno;
                                    if (orderno == sOrderNo)
                                    {
                                        isCancel = PasswayConfirmReleaseUtil.ConfirmCancelRelease(sPassNo, sOrderNo, "", false);
                                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录取消车道弹框成功：{key}");
                                    }
                                }
                                #endregion

                                if (!isCancel)
                                {
                                    //string tempImage = CameraImageHelper.ImageSaveHSPathBig("Astern");
                                    //Video.VideoConnect.VideoSnapShootToJpegOfPath(device.Device_IP, tempImage);

                                    var policyway = AppBasicCache.GetAllPolicyPassway.FirstOrDefault(x => x.Value.PolicyPassway_PasswayNo == sPassNo);
                                    var paramData = new Model.API.apiBackCar
                                    {
                                        cameraip = device.Device_IP,
                                        camerano = device.Device_No,
                                        count = 1,
                                        orderno = sOrderNo,
                                        imgbig = "" /*tempImage*/,
                                        time = $"{DateTime.Now:G}",
                                        carno = innerParkOrder.ParkOrder_CarNo,
                                    };

                                    Model.API.ResBody res = null;
                                    bool isCache = false;
                                    bool isEzvizDevice = false;
                                    string Url = "";
                                    //LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录=> 车道{model.PasswayNo}查找萤石设备");
                                    var ezvizDevice = AppBasicCache.GetAllDeivces.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(sPassNo) && m.Value.Drive_Code == "10103");
                                    if (ezvizDevice.Value != null)
                                    {
                                        isEzvizDevice = BLL.EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "倒车记录");

                                        paramData.no = Common.Utils.CreateNumberWith("DC");
                                        paramData.video = Url;
                                    }

                                    if (!isEzvizDevice)
                                    {
                                        var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(passwayno) && m.Value.Drive_Code == "10123");
                                        if (vizCloudDevice.Value != null)
                                        {
                                            using var _vizCloudDevice = new TcpConnPools.SceneVideo.VziCloud.VziCloudService(accessKeyId: vizCloudDevice.Value.Device_AppKey, accessKeySecret: vizCloudDevice.Value.Device_Secret);
                                            var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                                            var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                                            paramData.no = Common.Utils.CreateNumberWith("DC");
                                            var quickUrl = await _vizCloudDevice.QuickGetRecordPlaybackUrlAsync(vizCloudDevice.Value.Device_No, startTime, endTime, paramData.no, vizCloudDevice.Value.Device_VideoChannelNo);
                                            if (string.IsNullOrEmpty(quickUrl))
                                            {
                                                Url = quickUrl;
                                                paramData.video = Url;
                                                isEzvizDevice = true;
                                            }
                                        }
                                    }

                                    if (policyway.Value != null && policyway.Value.PolicyPassway_BackCarMode == 1)
                                    {
                                        res = BLL.ParkApi.CloseBackCarOrder(AppBasicCache.SentryHostInfo?.SentryHost_ParkNo, paramData);
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"上报倒车事件:{TyziTools.Json.ToString(paramData)}", LogLevel.Info);
                                        res = BLL.ParkApi.BackCarEvent(AppBasicCache.SentryHostInfo?.SentryHost_ParkNo, paramData, out isCache, isEzvizDevice);
                                    }
                                    //string imgUrl = Util.LPRTools.GetSentryHostImg(tempImage);
                                    if (res.success)
                                    {
                                        string stempNo = string.Empty;
                                        if (!string.IsNullOrWhiteSpace(res.data))
                                        {
                                            JObject pairs = JObject.Parse(res.data);
                                            stempNo = pairs.ContainsKey("ControlEvent_No") ? Convert.ToString(pairs["ControlEvent_No"]) : string.Empty;
                                            if (string.IsNullOrEmpty(stempNo)) stempNo = pairs.ContainsKey("BackCar_No") ? Convert.ToString(pairs["BackCar_No"]) : string.Empty;
                                        }
                                        if (!string.IsNullOrWhiteSpace(stempNo))
                                        {
                                            #region 倒车场景相机抓拍
                                            if (!isEzvizDevice)
                                            {
                                                var ffv = SceneVideoControler.GetDevice(passwayno, DeviceType.SceneVideo01);
                                                if (ffv != null && ffv is TcpConnPools.SceneVideo.SceneVideo01.SceneVideoOf01 deviceSC)
                                                {
                                                    if (!isCache)
                                                    {
                                                        _ = deviceSC.SendRecordVideoAsync(Guid.NewGuid().ToString().Replace("_", "")).ConfigureAwait(false);
                                                    }


                                                    _ = deviceSC.SendCameraPhotoAsync(Guid.NewGuid().ToString().Replace("_", "")).ConfigureAwait(false);
                                                }
                                                else
                                                {
                                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录=> 车道{sPassNo}未找到场景相机，或场景相机设备未连接");
                                                }
                                            }
                                            #endregion

                                            if (!string.IsNullOrWhiteSpace(parkorderno))
                                            {
                                                WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                    $"车辆{innerParkOrder.ParkOrder_CarNo}出场倒车,请注意确认信息！", DateTime.Now.ToString("G"));
                                            }
                                            else
                                            {
                                                WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                       $"车辆{innerParkOrder.ParkOrder_CarNo}入场倒车,请注意确认信息！", DateTime.Now.ToString("G"));
                                            }
                                        }
                                        else
                                        {
                                            WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                          $"{res.errmsg}", DateTime.Now.ToString("G"));
                                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录处理返回：{sOrderNo}///{JsonConvert.SerializeObject(res)}");
                                        }
                                    }
                                    else
                                    {
                                        if (dynamicItems != null)
                                        {
                                            if (dynamicItems.Item1 == 1)
                                            {
                                                WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                       $"车辆{dynamicItems.Item2}出场倒车,确认窗口关闭！", DateTime.Now.ToString("G"));
                                            }
                                            else
                                            {
                                                WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                          $"车辆{dynamicItems.Item2}入场倒车，确认窗口关闭！", DateTime.Now.ToString("G"));
                                            }
                                        }
                                        else
                                        {
                                            if (!string.IsNullOrWhiteSpace(parkorderno))
                                            {
                                                WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                          $"车辆{innerParkOrder.ParkOrder_CarNo}出场倒车,确认窗口关闭！", DateTime.Now.ToString("G"));
                                            }
                                            else
                                            {
                                                WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                          $"车辆{innerParkOrder.ParkOrder_CarNo}入场倒车，确认窗口关闭！", DateTime.Now.ToString("G"));
                                            }
                                        }
                                    }

                                }
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"{sPassNo}车道未关联主相机！");
                                return ResOk(false, $"{sPassNo}车道未关联主相机！");
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"{sPassNo}车道未找到缓存的倒车订单！");
                            return ResOk(false, $"{sPassNo}车道未找到缓存的倒车订单！");
                        }
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"{sPassNo}编号的车道未启用倒车功能！");
                        return ResOk(false, $"{sPassNo}编号的车道未启用倒车功能！");
                    }
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车处理失败，{sPassNo}编号的车道未找到缓存记录！");
                    return ResOk(false, $"倒车处理失败，{sPassNo}编号的车道未找到缓存记录！"); ;
                }

                return ResOk(true, "成功");
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }


        /// <summary>
        /// 启用应急模式
        /// </summary>
        /// <param name="passwayno">车道编码</param>
        /// <returns></returns>
        public async Task<IActionResult> IsEmergency()
        {
            try
            {
                if (!await checkPower(lgAdmin, "", false, false)) { return ResOk(false, "无权限"); }

                LogManagementMap.WriteToFile(LoggerEnum.ManualOperation, $"[{lgAdmin?.Admins_Account}]岗亭后台人工启用应急模式");
                CameraGlobal.IsEmergency = true;

                return ResOk(true, "成功");
                ;
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
                ;
            }
        }

        #endregion
    }
}