﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { width: 100%; height: 100%; padding: 0; margin: 0; }
        .layui-row { margin-bottom: 15px; }
        .m-label { padding: 9px 0 0 10px; font-weight: bold; }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .layui-card { box-shadow: none; margin-top: 30px; }
        .layui-card-body { height: 330px; overflow-y: auto; }
        .parkmsg { margin-bottom: 10px; }
        .parkrow { padding-left: 60px; }
        .layui-collapse { border: 0; height: auto; position: relative; }
        .layui-colla-content.layui-show { border: 1px solid #ebebeb; }
        .footer { position: fixed; margin: auto; bottom: 0; right: 0; background: #fff; padding: 1px 20px; text-align: right; box-shadow: 0 -1px 2px 0 rgb(0 0 0 / 5%); z-index: 1000; }
        .parkstatus { height: 35px; line-height: 35px; }
        .restart-div { text-align: right; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-card">
        <div class="layui-card-body layui-form" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs3 m-label txtParking_Name">停车场名称</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="14" id="Parking_Name" name="Parking_Name" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">停车场KEY</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-numen" maxlength="8" id="Parking_Key" name="Parking_Key" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">云平台</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox falsemodify" id="Parking_EnableNet">
                        <ul class="flex">
                            <li data-value="1">启用</li>
                            <li data-value="0" class="select">禁用</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="enablenet layui-hide layui-row">
                <div class="layui-col-xs3 m-label">连接状态</div>
                <div class="layui-col-xs4 edit-ipt-ban parkstatus">
                    <label class="lb-cloud-sts" title="未知">未知</label>
                </div>
                <div class="layui-col-xs4 restart-div layui-hide">
                    <button id="ReStartCloud" class="btn btn-primary"><t>重新连接</t></button>
                </div>
            </div>

            <div class="enablenet layui-hide layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title layui-col-xs12">更多</h2>
                    <div class="layui-colla-content">
                        <div class="layui-row">
                            <div class="layui-col-xs3 m-label">连接环境</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox falsemodify" id="Parking_Mode">
                                    <ul class="flex">
                                        <li data-value="1">测试环境</li>
                                        <li data-value="2" class="select">正式环境</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs3 m-label">连接平台</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox falsemodify" id="Parking_Platform">
                                    <ul class="flex">
                                        <li data-value="1">插件平台</li>
                                        <li data-value="2" class="select">智慧停车</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs3 m-label">请求地址</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input" maxlength="255" id="Parking_ApiUrl" name="Parking_ApiUrl" readonly autocomplete="off" />
                            </div>
                        </div>
                        <div class="layui-row hide">
                            <div class="layui-col-xs3 m-label">运营模式</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox falsemodify" id="Parking_IsTrusteeship">
                                    <ul class="flex">
                                        <li data-value="1">启用</li>
                                        <li data-value="0" class="select">禁用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            @if (carparking.Config.AppSettingConfig.SentryMode == "1" && carparking.Config.AppSettingConfig.InstallType == "7")
            {
                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">授权KEY</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <input type="text" class="layui-input" maxlength="250" id="Parking_AuthKey" name="Parking_AuthKey" autocomplete="off" />
                    </div>
                </div>
            }

        </div>

        <div class="footer">
            <div>
                <div class="layui-col-xs8">
                    <div style="text-align:left; line-height: 24px; margin: 8px 0px; padding: 0 10px; background-color: #fff6e0;">
                        <b>注意</b>：若【启用】云平台，保存后自动连接云平台，如果连接失败，则需等待10分钟后自动重连.
                    </div>
                </div>
                <div class="layui-col-xs4" style="margin-top: 20px;margin-bottom: 10px;">
                    <button class="btn btn-primary layui-hide" id="SavePark"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-close"></i> <t>关闭</t></button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="park_online">
        {{if online==1 }}
        <label class="layui-badge layui-bg-blue" title="已连接云平台">已连接云平台</label>
        {{else online==2 }}
        <label class="layui-badge layui-bg-black" title="未启用云平台">未启用云平台</label>
        {{else online==-1 }}
        <label class="layui-badge layui-bg-black" title="未知">未知</label>
        {{else}}
        <label class="layui-badge layui-bg-orange" title="未连接云平台">未连接云平台</label>
        {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        layui.use(["form"], function () {
            pager.init()
        });

        var parking = @Html.Raw(ViewBag.Park);
        var parkmode = '@ViewBag.SentryMode';

        // 云车场冷却状态辅助函数
        function checkCloudCooldownStatus() {
            return $.getJSON("/Index/GetRegistrationCooldownInfo").then(function(json) {
                if (json.success && json.data && json.data.IsInCooldown) {
                    // 如果在冷却期，显示友好提示
                    layer.msg(json.data.Message, {
                        icon: 0,
                        time: 5000,
                        area: ['400px', '120px']
                    });
                    return true;
                }
                return false;
            }).fail(function() {
                console.warn("获取冷却状态失败");
                return false;
            });
        }
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var index = parent.layer.getFrameIndex(window.name);

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //数据绑定
            bindData: function () {
                $("#verifyCheck").fillForm(parking, function (data) { });
                LoadDeviceConfig(parking)
            },
            bindEvent: function () {

                $("#Cancel").click(function () {
                    window.parent.layer.closeAll();
                })

                $("#SavePark").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg('<div style="padding: 5px; line-height: 22px; background-color: #fff; padding-left:22px;">正在处理...</div>', { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Parking_Key = $("#Parking_Key").val();
                        data.Parking_ApiUrl = $("#Parking_ApiUrl").val();
                        data.Parking_AuthKey = $("#Parking_AuthKey").val();
                        return data;
                    });

                    param.Parking_EnableNet = config.Parking_EnableNet;
                    param.Parking_Mode = config.Parking_Mode;
                    param.Parking_Platform = config.Parking_Platform;
                    param.Parking_IsTrusteeship = config.Parking_IsTrusteeship;

                    $("#SavePark").attr("disabled", true);
                    param.Parking_No = parking.Parking_No;
                    param.Parking_ID = parking.Parking_ID;

                    // 统一检查所有字段变化情况
                    var hasAnyChange =
                        parking.Parking_Name != param.Parking_Name ||
                        parking.Parking_EnableNet != param.Parking_EnableNet ||
                        parking.Parking_Mode != param.Parking_Mode ||
                        parking.Parking_Platform != param.Parking_Platform ||
                        parking.Parking_Key != param.Parking_Key ||
                        parking.Parking_ApiUrl != param.Parking_ApiUrl ||
                        parking.Parking_Secret != param.Parking_Secret ||
                        parking.Parking_Add != param.Parking_Add ||
                        parking.Parking_Linkman != param.Parking_Linkman ||
                        parking.Parking_Tel != param.Parking_Tel;

                    // 检查关键字段是否发生变化
                    var hasCriticalChange =
                        parking.Parking_EnableNet != param.Parking_EnableNet ||
                        parking.Parking_Mode != param.Parking_Mode ||
                        parking.Parking_Platform != param.Parking_Platform ||
                        parking.Parking_Key != param.Parking_Key ||
                        parking.Parking_ApiUrl != param.Parking_ApiUrl;

                    // 是否需要调用GetCloudInfor
                    var needGetCloudInfo = hasCriticalChange && param.Parking_EnableNet == 1 && param.Parking_Platform == 2;

                    if (needGetCloudInfo) {
                        $.post("/Index/GetCloudInfor", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                var d = JSON.parse(json.data);
                                //layer.msg(json.data);
                                var content = '<div style="padding: 30px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">'
                                    + '<div class="parkmsg">请确认以下车场信息，并在启用云平台后进行收款账号支付验证，确认收款账号正确：</div>'
                                    + '<div class="parkmsg"><span class="parkrow">车场名称：</span><span>' + d.Parking_Name + '</span></div>'
                                    + '<div class="parkmsg"><span class="parkrow">车场地址：</span><span>' + d.Parking_Add + '</span></div>'
                                    + '<div class="parkmsg"><span class="parkrow">联系人：</span><span>' + d.Parking_Linkman + '</span></div>'
                                    + '<div class="parkmsg"><span class="parkrow">联系电话：</span><span>' + d.Parking_Tel + '</span></div>'
                                    + '</div>';
                                LAYER_OPEN_TYPE_0(content, res => {
                                    LAYER_LOADING('<div style="padding: 5px; line-height: 22px; background-color: #fff; padding-left:22px;">正在保存并重启中间件程序...</div>');
                                    $.post("/Index/SaveParking", { jsonModel: JSON.stringify(param) }, function (json) {
                                        $("#SavePark").removeAttr("disabled")
                                        window.parent.global.parkStatusRetryInterval = 2000;
                                        if (json.success) {
                                            layer.msg('<div style="padding: 20px; line-height: 22px;">保存成功</div>', { time: 1500 }, function () { window.parent.closeParkWin(); });
                                            // window.parent.location.reload();
                                        } else {
                                            $("#SavePark").removeAttr("disabled")
                                            layer.msg(json.msg);
                                        }
                                    }, "json");
                                }, res => {
                                    $("#SavePark").removeAttr("disabled");
                                    layer.closeAll();
                                })
                            } else {
                                $("#SavePark").removeAttr("disabled")
                                layer.closeAll();

                                // 处理错误信息
                                var errorMsg = json.msg || '操作失败';
                                var displayMsg = errorMsg;

                                // 检查是否是冷却相关的错误（通过后端返回的标识）
                                var isCooldownError = json.data && json.data.isCooldownError === true;

                                // 如果不是冷却相关的错误，添加"请稍候再试"
                                if (!isCooldownError) {
                                    displayMsg = errorMsg + '，请稍候再试';
                                }

                                layer.open({
                                    type: 1
                                    , title: "云平台温馨提示："
                                    , closeBtn: false
                                    , area: '500px;'
                                    , shade: 0
                                    , id: 'LAY_layuipro1'
                                    , resize: false
                                    , btn: ['确定']
                                    , btnAlign: 'c'
                                    , moveType: 0
                                    , content: '<div style="padding: 30px; line-height: 24px; background-color: #393D49; color: #fff; font-weight: 300; word-wrap: break-word;">' + displayMsg + '</div>'
                                    , success: function (layero) {
                                    }
                                });
                            }
                        }, "json");
                    } else {
                        layer.closeAll();

                        if (!hasAnyChange) {
                            // 如果没有任何修改，直接提示成功并关闭窗口
                            layer.msg("保存成功", { time: 1500 }, function () { window.parent.closeParkWin(); });
                        } else if (hasAnyChange && !hasCriticalChange) {
                            // 如果只是修改停车场名称，直接保存，不需要确认
                            LAYER_LOADING('<div style="padding: 5px; line-height: 22px; background-color: #fff; padding-left:22px;">正在保存...</div>');
                            $.post("/Index/SaveParking", { jsonModel: JSON.stringify(param) }, function (json) {
                                $("#SavePark").removeAttr("disabled")
                                if (json.success) {
                                    layer.msg("保存成功", { time: 1500 }, function () { window.parent.closeParkWin(); });
                                } else {
                                    $("#SavePark").removeAttr("disabled")
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        } else {
                            // 其他情况需要确认
                            var content = '<div style="padding: 30px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">确定禁用车场云平台吗?</div>';
                            if (param.Parking_EnableNet == 1) {
                                content = '<div style="padding: 30px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">'
                                    + '<div class="parkmsg">请确认以下车场信息：</div>'
                                    + '<div class="parkmsg"><span class="parkrow">车场key：</span><span>' + param.Parking_Key + '</span></div>'
                                    + '<div class="parkmsg"><span class="parkrow">云平台：</span><span>' + (param.Parking_EnableNet == 1 ? "启用" : "禁用") + '</span></div>'
                                    + '<div class="parkmsg"><span class="parkrow">运营模式：</span><span>' + (param.Parking_Mode == 1 ? "测试环境" : "正式环境") + '</span></div>'
                                    + '<div class="parkmsg"><span class="parkrow">连接平台：</span><span>' + (param.Parking_Platform == 1 ? "插件平台" : "智慧停车") + '</span></div>'
                                    + '</div>';
                            }
                            LAYER_OPEN_TYPE_0(content, res => {
                                LAYER_LOADING('<div style="padding: 5px; line-height: 22px; background-color: #fff; padding-left:22px;">正在处理...</div>');
                                $.post("/Index/SaveParking", { jsonModel: JSON.stringify(param) }, function (json) {
                                    $("#SavePark").removeAttr("disabled")
                                    window.parent.global.parkStatusRetryInterval = 2000;
                                    if (json.success) {
                                        layer.msg("保存成功", { time: 1500 }, function () { window.parent.closeParkWin(); });
                                    } else {
                                        $("#SavePark").removeAttr("disabled")
                                        layer.msg(json.msg);
                                    }
                                }, "json");
                            }, res => {
                                layer.closeAll();
                                $("#SavePark").removeAttr("disabled")
                            })
                        }
                    }
                });


                $("#ReStartCloud").click(function () {
                    //确认弹窗
                    layer.confirm('确定要重连云平台吗？', {
                        btn: ['确定', '取消']
                    }, function () {

                        $.ajaxSettings.async = true;
                        $("#ReStartCloud").attr("disabled", true);
                        layer.msg("正在重连...", { icon: 16, time: 0 });
                        $.post("ReStartCloud", {}, function (json) {
                            $("#ReStartCloud").removeAttr("disabled")
                            window.parent.global.parkStatusRetryInterval = 2000;
                            if (json.success) {
                                layer.msg(json.msg, { icon: 1, time: 1500 }, function () { window.parent.closeParkWin(); });
                            } else {
                                layer.msg(json.msg, { icon: 0, time: 1500 });
                            }
                        });
                        $.ajaxSettings.async = false;

                    });
                });

            },
            bindPower: function () {
                var pagePower = window.parent.global.formPower['HeadPage'];
                if (pagePower["SavePark"]) {
                    $("#SavePark").removeClass("layui-hide");
                }
                if (pagePower["ReStartCloud"]) {
                    $(".restart-div").removeClass("layui-hide");
                }
            },
            getParkStatus: function () {
                $.post("/Index/GetParkingState", {}, function (json) {
                    if (json.success) {
                        var parkStatus = json.data.parkstatus;
                        if (parkmode != "2") {
                            $(".parkstatus").html($("#park_online").tmpl([{ online: parkStatus }]));
                        } else {
                            $(".parkstatus").html($("#park_online").tmpl([{ online: 1 }]));
                        }
                    } else {
                        $(".parkstatus").html($("#park_online").tmpl([{ online: -1 }]));
                    }

                });
            }

        };
    </script>

    <script>
        //设备参数配置[仅选项按钮]默认值
        var config = {
            Parking_EnableNet: 0,
            Parking_Mode: 1,
            Parking_Platform: 2,
            Parking_IsTrusteeship: 0
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName);
            });
        });

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == "Parking_EnableNet") {
                if (config[idName] == 1) {
                    $(".enablenet").removeClass("layui-hide");
                    pager.getParkStatus();
                } else {
                    $(".enablenet").removeClass("layui-hide").addClass("layui-hide");
                }
            }

            onSetUrl();
        }

        var onSetUrl = function () {
            var apiurl = "";
            if (config.Parking_Mode == 1) {
                if (config.Parking_Platform == 1) {
                    apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_DevelopApiDebug";
                } else {
                    apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_ParkApiDebug";
                }
            } else {
                if (config.Parking_Platform == 1) {
                    apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_DevelopApi";
                } else {
                    apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_ParkApi";
                }
            }
            $("#Parking_ApiUrl").val(apiurl);
        }

        var clickCount = 0;
        var timer = null;
        $(".txtParking_Name").click(function (event) {
            if (!event.ctrlKey && !event.metaKey) return;

            clickCount++;
            if (clickCount == 5) {
                timer = setInterval(function () {
                    clickCount = 0;
                    clearInterval(timer);
                }, 5000);

                layer.open({
                    title: "系统测试模式"
                    , content: "确定切换系统测试模式吗？切换后，当前车场数据将进入系统测试模式并且不可恢复，请谨慎操作"
                    , btn: ["开启", "关闭", "测试配置", "取消"]
                    , btnAlign: 'center'
                    , yes: function (index, layero) {
                        layer.close(index);
                        $.post("/Index/SetTestMode", { testMode: 1 }, function (json) {
                            if (json.success) {
                                layer.msg("开启系统测试模式成功,即将重启服务", { time: 1500 });
                            }
                        });
                    },
                    //关闭
                    btn2: function (index, layero) {
                        layer.close(index);
                        $.post("/Index/SetTestMode", { testMode: 0 }, function (json) {
                            if (json.success) {
                                layer.msg("关闭系统测试模式成功,即将重启服务", { time: 1500 });
                            }
                        });
                    },
                    //测试配置
                    btn3: function (index, layero) {
                        //layui打开TestTools界面
                        window.parent.global.openTestTools();
                        return false; // 防止关闭当前弹窗
                    },
                    btn4: function (index, layero) {
                        layer.close(index); // 关闭弹窗
                    }

                });
            }
        });
    </script>
</body>
</html>
