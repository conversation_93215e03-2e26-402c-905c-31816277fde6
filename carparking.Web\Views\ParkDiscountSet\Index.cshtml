﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>优惠设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>商家优惠</cite></a>
                <a><cite>优惠设置</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <blockquote class="layui-elem-quote">
                            <strong> 温馨提示：</strong>此处为线下软件的优惠方案，与云平台上的优惠券方案没有关联。
                        </blockquote>
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="ParkDiscountSet_No" id="ParkDiscountSet_No" autocomplete="off" placeholder="优惠编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="ParkDiscountSet_Name" id="ParkDiscountSet_Name" autocomplete="off" placeholder="优惠标题" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="优惠方式" class="form-control chosen-select" id="ParkDiscountSet_Type" name="ParkDiscountSet_Type" lay-search>
                                    <option value="">优惠方式</option>
                                    <option value="101">优惠金额</option>
                                    <option value="102">优惠时长</option>
                                    <option value="103">优惠比例</option>
                                    <option value="104">免费到指定时间</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="使用场景" class="form-control chosen-select" id="ParkDiscountSet_Scene" name="ParkDiscountSet_Scene" lay-search>
                                    <option value="">使用场景</option>
                                    <option value="0">只用于场内打折</option>
                                    <option value="1">只用于出口打折</option>
                                    <option value="2">出口与场内打折</option>
                                 
                                </select>
                            </div>

                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.ParkDiscountSet.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.ParkDiscountSet.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.ParkDiscountSet.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TmplType">
        {{# if(d.ParkDiscountSet_Type==101){ }}
        <span class="layui-badge layui-bg-blue ">优惠金额</span>
        {{# }else if(d.ParkDiscountSet_Type==102){ }}
        <span class="layui-badge layui-bg-green ">优惠时长</span>
        {{# }else if(d.ParkDiscountSet_Type==103){ }}
        <span class="layui-badge layui-bg-cyan ">优惠比例</span>
        {{# }else if(d.ParkDiscountSet_Type==104){ }}
        <span class="layui-badge layui-bg-green ">免费到指定时间</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplScene">
        {{# if(d.ParkDiscountSet_Scene==2){ }}
        <span class="layui-badge layui-bg-blue ">出口与场内打折</span>
        {{# }else if(d.ParkDiscountSet_Scene==1){ }}
        <span class="layui-badge layui-bg-orange ">只用于出口打折</span>
        {{# }else if(d.ParkDiscountSet_Scene==0){ }}
        <span class="layui-badge layui-bg-green ">只用于场内打折</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplValue">
        {{# if(d.ParkDiscountSet_Type==101){ }}
        <span>{{d.ParkDiscountSet_Amount}}元</span>
        {{# }else if(d.ParkDiscountSet_Type==102){ }}
        <span>{{d.ParkDiscountSet_Duration}}分钟</span>
        {{# }else if(d.ParkDiscountSet_Type==103){ }}
        <span>{{d.ParkDiscountSet_Ratio}}折</span>
        {{# }else if(d.ParkDiscountSet_Type==104){ }}
            {{# if(getStatus(d.ParkDiscountSet_AppointHour.replace("T"," "))==3){}}
            <span class="layui-badge layui-bg-red">{{d.ParkDiscountSet_AppointHour.replace("T"," ")}}</span>
            {{# }else{ }}
            <span class="layui-badge layui-bg-gray">{{d.ParkDiscountSet_AppointHour.replace("T"," ")}}</span>
            {{# } }}
        {{# } }}
    </script>
   
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;

        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"));

        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var admin = layui.admin, table = layui.table;
        
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
          
            var cols = [[
                { type: 'radio' }
                , { field: 'ParkDiscountSet_No', width: 140, title: '优惠编码' , hide: true}
                , { field: 'ParkDiscountSet_Name', title: '优惠标题' }
                , { field: 'ParkDiscountSet_Type', title: '优惠方式', toolbar: '#TmplType' }
                , { field: 'ParkDiscountSet_Value', title: '优惠额度', toolbar: '#TmplValue' }
                , { field: 'ParkDiscountSet_Scene', title: '使用场景', toolbar: '#TmplScene' }
                , { field: 'ParkDiscountSet_AddTime', title: '注册时间' }
                , { field: 'ParkDiscountSet_AddAccount', title: '操作员' }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ParkDiscountSet/GetParkDiscountSetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增折扣",
                            content: 'Edit?Act=Add',
                            area: getIframeArea(['600px', '555px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑折扣",
                            content: 'Edit?Act=Update&ParkDiscountSet_No=' + data[0].ParkDiscountSet_No,
                            area: getIframeArea(['600px', '555px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { ParkDiscountSet_No: data[0].ParkDiscountSet_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();

            layui.form.render("select");
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/ParkDiscountSet/GetParkDiscountSetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }

         function getStatus(ChargeRules_BeginTime) {
            if (ChargeRules_BeginTime) {
                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd hh:mm:ss"));
                if (ChargeRules_BeginTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }
    </script>
</body>
</html>
