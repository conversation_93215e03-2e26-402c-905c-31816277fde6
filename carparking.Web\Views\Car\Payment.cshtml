﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车支付</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        span { color: black; line-height: 33px; }
        label { line-height: 15px; }
        .ibox-content { padding: 15px; overflow: auto; }
        .input-group { position: relative; display: table; border-collapse: separate; }
        .btnUnit { border-color: #f2eeee !important; border-top-right-radius: 5px; border-bottom-right-radius: 5px; color: #5db587 !important; margin-left: 0 !important; background-color: transparent !important; }

        xm-select > .xm-tips { padding: 0 7px !important; }
        .xtime { color: #de0c3d; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="layui-row layui-form">
                <div class="layui-col-xs5 ">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">订单号：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_No"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">车牌号：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_CarNo"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">订单状态：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_StatusNo"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">车牌颜色：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_CarType"></span></div>
                    </div>
                    @*<div class="layui-row">
            <div class="layui-col-xs3"><span class="control-label">停车场：</span></div>
            <div class="layui-col-xs7"><span id="ParkOrder_ParkName"></span></div>
        </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">入场时间：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_EnterTime"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">停车时长：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_SumTime"></span>&nbsp;<span class="spantime hide">（<t class="xtime">60</t>秒后刷新）</span></div>
                    </div>
                   @* <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">计费时长：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_ChargeTime"></span></div>
                    </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠券：</span></div>
                        <div class="layui-col-xs7" style="margin-left: -8px;">
                            <div id="CouponRecord" class="v-null"></div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠限制：</span></div>
                        <div class="layui-col-xs7"><span id="Coupon_Count">0</span>张</div>
                    </div>
                    @*<div class="layui-row">
            <div class="layui-col-xs3"><span class="control-label">优惠额度：</span></div>
            <div class="layui-col-xs7"><span id="CouponSolution_Value">0</span>元</div>
        </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">应付金额：</span></div>
                        <div class="layui-col-xs7" id="errorLoadPrice">
                            <span id="ParkOrder_TotalAmount">0</span>元
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠金额：</span></div>
                        <div class="layui-col-xs7"><span id="CouponSolution_MoneyValue">0</span>元</div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">储值抵扣：</span></div>
                        <div class="layui-col-xs7"><span id="chuzhiamount">0</span>元</div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label" style="color:red; font-weight:800;">现金实收：</span></div>
                        <div class="layui-col-xs7" id="verifyCheck">
                            <div class="input-group" style="margin-left: -8px;">
                                <input type="number" class="layui-input v-null" id="ParkOrder_PayedAccount" name="ParkOrder_PayedAccount" autocomplete="off" placeholder="请填写收费金额（元）" style="padding-left: 8px !important;">
                                <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                            </div>
                            <label class="focus valid"></label>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs7">
                    <div class="alert alert-info layui-col-xs12">
                        提示：点击图片查看大图
                    </div>
                    <div>
                        <a id="linkEnterImgPath" href="javascript:;" target="_blank"><img src="../../Static/img/nophoto.jpg" style="width: 100%; max-height: 360px;" /></a>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-xs5">
                <div class="layui-col-xs12 layui-col-xs-offset3">
                    <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> 清缴费用</button>&nbsp;&nbsp;
                    <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplstatusno">
        {{if ParkOrder_StatusNo==199}}
        <span>预入场</span>
        {{else ParkOrder_StatusNo==200}}
        <span>已入场</span>
        {{else ParkOrder_StatusNo==201}}
        <span>已出场</span>
        {{else ParkOrder_StatusNo==202}}
        <span>已关闭</span>
        {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script type="text/javascript">
        var selCoupon = null;
        var xmSelect = null;
        var layuiForm = null;
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['element', 'form', 'xmSelect'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
        });
    </script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        var ParkOrder_No = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var model = decodeURIComponent($.getUrlParam("model"));
        var interval = null;
        var timeInterval = null;
        var time = 60;

        var pager = {
            couponIDes: "",
            couponIDes2: "",
            uselist: null,
            detaillist:null,
            chuzhiamount: 0,
            couponList: null,
            parktimemin: null,
            init: function () {
                this.bindData();
                this.bindEvent();
                this.bindPower();
                this.loadPrice();
            },

            //数据绑定
            bindData: function (index) {
                layer.msg('计费中...', { icon: 16, time: 0 });
                $(".spantime").addClass("hide");
                pager.countTime();
                if (ParkOrder_No != null) {
                    $.ajax({
                        type: 'post',
                        url: '/Car/GetOrderPrice',
                        dataType: 'json',
                        data: { ParkOrder_No: ParkOrder_No, couponIDes: pager.couponIDes },
                        success: function (json) {
                            if (json.success) {
                                $(".spantime").removeClass("hide");
                                var data = json.data.order;
                                if (data) {
                                    $("#ParkOrder_No").html(data.ParkOrder_No);
                                    $("#ParkOrder_CarNo").html(data.ParkOrder_CarNo);
                                    $("#ParkOrder_StatusNo").html($("#tmplstatusno").tmpl(data));
                                    $("#ParkOrder_CarType").html(data.ParkOrder_CarTypeName);
                                    $("#ParkOrder_ParkName").html(data.ParkOrder_ParkName);
                                    $("#ParkOrder_EnterTime").html(data.ParkOrder_EnterTime);

                                    if (data.ParkOrder_EnterImgPath != null && data.ParkOrder_EnterImgPath != "") {
                                        $("#linkEnterImgPath").attr("href", data.ParkOrder_EnterImgPath)[0].children[0].src = decodeURIComponent(data.ParkOrder_EnterImgPath);
                                    }

                                    if (data.ParkOrder_TotalAmount == null) {
                                        $("#Save").attr("disabled", true);
                                        $("#errorLoadPrice").html("获取价格失败,请联系管理员!");
                                        $("#errorLoadPrice").wrapInner("<span><font color='red'/></span>");
                                    } else {
                                        $("#errorLoadPrice").html('<span id="ParkOrder_TotalAmount">' + data.ParkOrder_TotalAmount + '</span>元'); //总共需支付金额

                                        var policyCount = json.data.policy;
                                        pager.detaillist = json.data.detaillist;
                                        if (policyCount == null) policyCount = 0;
                                        var couponmoney = 0;
                                        var coupon = json.data.coupon;
                                        if (coupon && coupon.money && coupon.money > 0) {
                                            pager.uselist = coupon.uselist;
                                            couponmoney = coupon.money;
                                            $("#CouponSolution_MoneyValue").html(coupon.money);
                                        } else {
                                            pager.uselist = null;
                                            $("#CouponSolution_MoneyValue").html(0);
                                        }

                                        if (json.data.chuzhiamount != null) pager.chuzhiamount = json.data.chuzhiamount; else pager.chuzhiamount = 0;
                                        $("#chuzhiamount").html(pager.chuzhiamount);

                                        $("#ParkOrder_PayedAccount").val(json.data.payedamount);

                                        if (index != "1") {
                                            $("#Coupon_Count").html(policyCount);
                                            var CouponInfoList = json.data.couponinfolist;
                                            //绑定优惠方式
                                            if (CouponInfoList && CouponInfoList != null && CouponInfoList.length > 0) {
                                                pager.couponList = CouponInfoList;
                                                if (pager.couponList && pager.couponList.length > 0) {
                                                    var newlist = [];
                                                    for (var i = 0; i < pager.couponList.length; i++) {
                                                        newlist[i] = {
                                                            "name": pager.couponList[i].CouponRecord_Name,
                                                            "value": pager.couponList[i].CouponRecord_ID
                                                        };
                                                    }
                                                    selCoupon = xmSelect.render({
                                                        el: '#CouponRecord',
                                                        name: 'CouponRecord',
                                                        layVerify: 'required',
                                                        layVerType: 'msg',
                                                        filterable: true,
                                                        height: '130px',
                                                        max: policyCount,
                                                        data: newlist,
                                                        on: function (data) {
                                                            $("#CouponSolution_MoneyValue").val(0);
                                                            clearInterval(interval);
                                                            interval = null;
                                                            setTimeout(function () {
                                                                var arr = data.arr;
                                                                pager.couponIDes = "";
                                                                var index = 0;
                                                                $.each(arr, function (n, v) {
                                                                    pager.couponIDes += v.value + ",";
                                                                    index++;
                                                                })
                                                                if (index > policyCount) {
                                                                    $("#Save").attr("disabled", true);
                                                                    layer.msg("优惠券一次允许使用" + policyCount + "张，勿超过策略使用限制!", { icon: 0, btn: ['确定'], time: 0 });
                                                                    return;
                                                                }
                                                                pager.couponIDes = pager.couponIDes.substring(0, pager.couponIDes.length - 1);
                                                                pager.couponIDes2 = pager.couponIDes;
                                                                pager.bindData("1");
                                                                setTimeout(function () { pager.couponIDes = ""; pager.loadPrice(); }, 2000);
                                                            }, 100)
                                                        },
                                                    })
                                                }
                                            } else {
                                                pager.couponList = "";
                                                selCoupon = xmSelect.render({
                                                    el: '#CouponRecord',
                                                    name: 'CouponRecord',
                                                    layVerify: 'required',
                                                    layVerType: 'msg',
                                                    autoRow: true,
                                                    data: []
                                                })
                                            }
                                        } else {
                                            pager.couponList = "";
                                        }

                                        layui.form.render("select");

                                        //根据入场时间计算停车时长
                                        var zhTimes = _DATE.getZhTimes(new Date(data.ParkOrder_EnterTime), new Date($.ajax({ async: false }).getResponseHeader("Date")));
                                        $("#ParkOrder_SumTime").html(zhTimes);
                                        if (json.data.parktimemin != null) {
                                            pager.parktimemin = json.data.parktimemin;
                                            var endDate = new Date(new Date(data.ParkOrder_EnterTime).setMinutes(new Date(data.ParkOrder_EnterTime).getMinutes() + pager.parktimemin));
                                            $("#ParkOrder_ChargeTime").html(_DATE.getZhTimes(new Date(data.ParkOrder_EnterTime), endDate));
                                        }
                                    }
                                } else {
                                    pager.detaillist = null;
                                }

                                $("#ParkOrder_PayedAccount").focus();
                                layer.closeAll();
                            } else {
                                layer.msg('加载失败：' + json.msg, { icon: 5 });
                            }

                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('订单ID无效', { icon: 0 });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    //验证表单
                    if (!verifyCheck._click()) return;
                    if ($("#ParkOrder_PayedAccount").val() < 0) {
                        layer.msg("请输入正确金额");
                        return;
                    }

                    var carParam = parent.pager.carParam;
                    carParam.NoCharge = 1;

                    //询问框
                    layer.confirm('确定要缴费？', {
                        title: "提示",
                        icon: "3",
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        $("#Save").attr("disabled", true);
                        layer.msg('保存中...', { icon: 16, time: 0 });
                        $.ajax({ //线上缴费下发通知（注销使用的优惠券）
                            type: 'post',
                            url: '/Car/SendNoticeForPayed',
                            dataType: 'json',
                            data: {
                                ParkOrder_No: ParkOrder_No, receAmount: $("#ParkOrder_TotalAmount").text(), CouponRecord_ID: JSON.stringify(selCoupon.getValue('value')), paidAmount: $("#ParkOrder_PayedAccount").val(), parktimemin: pager.parktimemin, uselist: pager.uselist, chuzhiamount: pager.chuzhiamount, carParam: JSON.stringify(carParam), detaillist: pager.detaillist, model: model
                            },
                            success: function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 2000 }, function () {
                                        parent.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                }
                                else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { window.parent.pager.bindData(window.parent.pager.pageIndex); } });
                                }
                            },
                            error: function () {
                                layer.msg('系统错误', { icon: 2 });
                            }
                        });
                    }, function () { });

                });
                $("#CouponRecord").change(function () {
                    $("#CouponSolution_MoneyValue").html($('#CouponRecord').find('option:selected').attr('mv')); //优惠金额
                    $("#ParkOrder_PayedAccount").val($('#CouponRecord').find('option:selected').attr('pa')); //最终需支付金额
                    $("#CouponType_Code").html($('#CouponRecord').find('option:selected').attr('tv'));
                    $("#CouponSolution_Value").html($('#CouponRecord').find('option:selected').attr('cv'));

                });
            },
            bindPower: function () {
                $(".btnUnit").removeClass("layui-hide")
                //window.parent.parent.global.getBtnPower(window, function (pagePower) {
                //    $(".btnUnit").removeClass("layui-hide")
                //});
            },
            loadPrice: function () {
                if (interval != null) {
                    clearInterval(interval);
                    interval = null;
                }
                interval = setInterval(function () { pager.bindData(); }, 60 * 1000);
            },
            countTime: function () {
                time = 60;
                if (timeInterval != null) {
                    clearInterval(timeInterval);
                    timeInterval = null;
                }
                timeInterval = setInterval(function () {
                    if (time > 0) time--;
                    else { time = 60; }
                    $(".xtime").html(time);
                }, 1000);
            }
        };

        $(function () { pager.init() });

    </script>
</body>
</html>
