﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
        }

        .layui-row {
            margin-bottom: 15px;
        }

        .m-label {
            padding: 9px 0 0 10px;
            font-weight: bold;
        }

        .padding-15 {
            padding: 1rem;
        }

        .pan-title {
            font-size: 1.5rem;
        }

        .layui-card {
            box-shadow: none;
        }

        .help-btn {
            z-index: 999;
            position: absolute;
            width: 20px;
            margin-left: 7px;
            margin-top: 6px;
            height: 20px;
            text-align: center;
            line-height: 22px;
            background-color: #f2f2f2;
            cursor: pointer;
            border-radius: 20px;
            font-style: normal;
            color: #999;
        }

            .help-btn:after {
                font-weight: bold;
            }

            .help-btn:hover {
                background-color: #1ab394;
                color: #fff;
                box-shadow: 0px 1px 10px #1080d4;
            }

        .help-btn-tip {
            z-index: 999;
            position: absolute;
            width: 20px;
            right: -28px;
            margin-top: -27px !important;
            height: 20px;
            text-align: center;
            line-height: 22px;
            background-color: #f2f2f2;
            cursor: pointer;
            border-radius: 20px;
            font-style: normal;
            color: #999;
        }

            .help-btn-tip:after {
                font-weight: bold;
            }

            .help-btn-tip:hover {
                background-color: #1ab394;
                color: #fff;
                box-shadow: 0px 1px 10px #1080d4;
            }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-card">
        <div class="layui-card-body layui-form" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">车道编码</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null " id="Passway_No" name="Passway_No" value="@ViewBag.PassNo" maxlength="32" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">车道名称</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-name v-null" id="Passway_Name" name="Passway_Name" maxlength="32" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">车道类型</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Passway_Type">
                        <ul>
                            <li data-value="1">机动车道</li>
                            <li data-value="3">非机动车道</li>
                        </ul>
                    </div>
                    @*<i class="help-btn" data-key="Passway_Type">?</i>*@
                </div>
            </div>
            <div class="nonVehicle">
                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">值守模式</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_DutyMode">
                            <ul>
                                <li data-value="1">有人值守</li>
                                <li data-value="0">无人值守</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">相机模式</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_CameraMode">
                            <ul>
                                <li data-value="1">单相机模式</li>
                                <li data-value="2">双相机模式</li>
                                <li data-value="3">多相机模式</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">相机识别间隔(秒)</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <input type="text" class="layui-input v-number" id="Passway_IdInterval" name="Passway_IdInterval" maxlength="3" value="3" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">启用车道事件</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_EnableBoard">
                            <ul>
                                <li data-value="0">禁用</li>
                                <li data-value="1">启用</li>
                            </ul>
                        </div>
                        <span class="label-desc">该功能搭配数智相机或智慧道闸（Y312）使用，启用后才接收倒车相关事件。</span>
                    </div>
                </div>
                <div class="layui-row Passway_EnableBoard layui-hide layui-anim layui-anim-downbit">
                    <div class="layui-col-xs3 m-label">启用防倒车</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_IsBackCar">
                            <ul>
                                <li data-value="0">禁用</li>
                                <li data-value="1">启用</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-hide">
                    <div class="layui-col-xs3 m-label">无牌车扫码判断地感</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_Sense">
                            <ul>
                                <li data-value="1" class="select">是</li>
                                <li data-value="0">否</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">启用同进同出</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_SameInOut">
                            <ul>
                                <li data-value="0">禁用</li>
                                <li data-value="1">启用</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row Passway_SameInOut layui-hide layui-anim layui-anim-downbit">
                    <div class="layui-col-xs3 m-label">同进同出车道类型</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_SameInOutType">
                            <ul>
                                <li data-value="0">入口类型</li>
                                <li data-value="1">出口类型</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row Passway_SameInOut layui-hide">
                    <div class="layui-col-xs3 m-label">同进同出关联车道</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <select id="Passway_OutNo" name="Passway_OutNo" class="layui-select" lay-search>
                            <option value="">请选择关联的车道</option>
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 m-label">开启开闸和播报</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <div class="btnCombox" id="Passway_OpenGateAndVoice">
                            <ul>
                                <li data-value="0">禁用</li>
                                <li data-value="1">启用</li>
                            </ul>
                        </div>
                        <span class="label-desc">该功能禁用后，识别车辆，将只保存记录，不发开闸和播报相关指令</span>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">启用收费</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Passway_IsCharge">
                        <ul>
                            <li data-value="0">禁用</li>
                            <li data-value="1">启用</li>
                        </ul>
                    </div>
                    <span class="label-desc">场内出场支付，仅支持场内二维码支付。</span>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">所属岗亭</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="Passway_SentryHostNo" name="Passway_SentryHostNo" lay-search>
                        <option value="">以后再设置</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">所属区域</div>
                <div class="layui-col-xs8">
                    <table lay-filter="table" id="stable">
                    </table>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <span class="label-desc">变更车道出入口类型时请谨慎操作！！！车辆通行高峰期内或者车道已弹出缴费窗口，则有可能导致车辆缴费后不开闸。</span>
                </div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <button class="btn btn-primary layui-hide" id="Save"><i class="fa fa-save"></i> <t>保存</t></button>
                    <button class="btn btn-primary layui-hide" id="SaveNext"><i class="fa fa-save"></i> <t>保存并继续</t></button>
                    <button class="btn btn-primary layui-bg-orange layui-hide" id="Cancel"><i class="fa fa-close"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="ParkAreaType">
        {{# if(d.ParkArea_Type == 0){}}
        <span class="layui-badge layui-bg-blue">外场</span>
        {{# }else{}}
        <span class="layui-badge layui-bg-green">内场</span>
        {{# } }}
        {{d.ParkArea_Name}}
    </script>
    <script type="text/html" id="GateType">
        <input type="radio" name="radio{{d.ParkArea_No}}" value="0" title="出口" checked disabled />
        <input type="radio" name="radio{{d.ParkArea_No}}" value="1" title="入口" disabled />
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?v1.0" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layform = null;
        var laytable = null;
        layui.use(['form'], function () {
            layform = layui.form;
            laytable = layui.table;
            pager.init()
        });

        var timespan = new Date().getTime();
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("Passway_No");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            stlArea: null,
            allArea: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllSentryHost", {}, function (json) {
                    if (json.success) {
                        var areas = json.data.areas;
                        pager.allArea = areas;
                        var sentryHosts = json.data.sentryHosts;
                        var option = '';
                        sentryHosts.forEach(function (item, index) {
                            var selected = index == 0 ? "selected" : "";
                            option += '<option value="' + item.SentryHost_No + '" ' + selected + '>' + item.SentryHost_Name + '</option>';
                        });
                        $("#Passway_SentryHostNo").append(option);
                        layform.render("select")

                        //转换静态表格
                        var cols = [[
                            { type: 'checkbox' }
                            , { field: 'ParkArea_Name', title: '区域名称', toolbar: "#ParkAreaType" }
                            , { field: 'PasswayLink_GateType', title: '出入口类型', toolbar: "#GateType" }
                        ]];

                        laytable.render({ elem: '#stable', cols: cols, data: areas, limit: 1000 });
                        $('input[lay-filter="layTableAllChoose"]').parent().html("")
                        laytable.on('checkbox(table)', function (obj) {
                            if (obj.type == "one") {//选择一行
                                if (obj.checked) {
                                    $("input[name='radio" + obj.data.ParkArea_No + "']").removeAttr("disabled");
                                } else {
                                    $("input[name='radio" + obj.data.ParkArea_No + "']").removeAttr("disabled").attr("disabled", true);
                                }
                            } else {
                                if (obj.checked)
                                    $("input[type='radio']").removeAttr("disabled");
                                else
                                    $("input[type='radio']").removeAttr("disabled").attr("disabled", true);
                            }

                            layform.render()
                        });
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json")

                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            if (paramAct == "Add" && item.Passway_SameInOut != 1) {
                                var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                                $("#Passway_OutNo").append(option);
                            }

                            if (paramAct == "Update" && item.Passway_No != paramNo && (item.Passway_SameInOut != 1 || item.Passway_OutNo == paramNo)) {
                                var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                                $("#Passway_OutNo").append(option);
                            }
                        });
                    }
                }, "json");

                layform.render("select")
            },
            //数据绑定
            bindData: function () {
                $.post("GetPasswayByNo", { Passway_No: paramNo }, function (json) {
                    if (json.success) {
                        var model = json.data.model;
                        var linkList = json.data.linkList;
                        //$(".layui-table-header").addClass("layui-hide");
                        if (paramAct == "Update") {
                            $("#verifyCheck").fillForm(model, function (data) { });
                            $("#Passway_No").attr("disabled", true);
                            SetCheckedTr(linkList)
                            LoadDeviceConfig(model)
                        } else {
                            // $("#Passway_No").val(timespan);
                            LoadDeviceConfig(config)
                        }
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");

            },
            bindEvent: function () {

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data["Passway_SentryHostNo"] = $("#Passway_SentryHostNo").val()
                        return data;
                    });
                    var linkData = getLinkData();
                    param.Passway_Type = config.Passway_Type;
                    param.Passway_DutyMode = config.Passway_DutyMode;
                    param.Passway_CameraMode = config.Passway_CameraMode;
                    param.Passway_IsBackCar = config.Passway_IsBackCar;
                    param.Passway_IsCharge = config.Passway_IsCharge;
                    param.Passway_Sense = config.Passway_Sense;
                    param.Passway_SameInOut = config.Passway_SameInOut;
                    param.Passway_SameInOutType = config.Passway_SameInOutType;
                    param.Passway_EnableBoard = config.Passway_EnableBoard;
                    param.Passway_OpenGateAndVoice=config.Passway_OpenGateAndVoice;

                    //当前版本不支持小车场出口启用收费
                    //if (linkData.length > 1 && param.Passway_IsCharge == 1) { param.Passway_IsCharge = 0; layer.msg("当前版本不支持小车场出口启用收费", { title: "消息提示", btn: ['我知道了'], icon: 0, area: ["320px", "auto"], time: 0 }); return; }

                    var isSure = gateOutDisabledCharge(param, linkData);
                    if (!isSure) {
                        LAYER_OPEN_TYPE_0("<b style='color:red;'>当前出口车道未启用收费,确定保存?</b>", res => {
                            pager.OnSave(param, linkData);
                        });
                    } else {
                        pager.OnSave(param, linkData);
                    }
                });

                $("#SaveNext").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data["Passway_SentryHostNo"] = $("#Passway_SentryHostNo").val()
                        return data;
                    });
                    var linkData = getLinkData();
                    param.Passway_Type = config.Passway_Type;
                    param.Passway_DutyMode = config.Passway_DutyMode;
                    param.Passway_CameraMode = config.Passway_CameraMode;
                    param.Passway_IsBackCar = config.Passway_IsBackCar;
                    param.Passway_IsCharge = config.Passway_IsCharge;
                    param.Passway_Sense = config.Passway_Sense;
                    param.Passway_SameInOut = config.Passway_SameInOut;
                    param.Passway_SameInOutType = config.Passway_SameInOutType;
                    param.Passway_EnableBoard = config.Passway_EnableBoard;
                    param.Passway_OpenGateAndVoice = config.Passway_OpenGateAndVoice;

                    //当前版本不支持小车场出口启用收费
                    //if (linkData.length > 1 && param.Passway_IsCharge == 1) { param.Passway_IsCharge = 0; layer.msg("当前版本不支持小车场出口启用收费", { title: "消息提示", btn: ['我知道了'], icon: 0, area: ["320px", "auto"], time: 0 }); return; }

                    var isSure = gateOutDisabledCharge(param, linkData);
                    if (!isSure) {
                        LAYER_OPEN_TYPE_0("<b style='color:red;'>当前出口车道未启用收费,确定保存?</b>", res => {
                            pager.OnSave(param, linkData, true);
                        });
                    } else {
                        pager.OnSave(param, linkData, true);
                    }
                });

                $("#Cancel").click(function () {
                    parent.layer.closeAll()
                })
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (paramAct == "Update") {
                        $("#SaveNext").removeClass("layui-hide").addClass("layui-hide");
                    }
                });
            },
            OnSave: function (param, linkData, isfrash) {
                $("#Save").attr("disabled", true);
                if (paramAct == "Add") {
                    LAYER_OPEN_TYPE_0("确定新增车道?", res => {
                        LAYER_LOADING("处理中...")
                        $.post("AddPassway", { jsonModel: JSON.stringify(param), linkModel: JSON.stringify(linkData) }, function (json) {
                            $("#Save").removeAttr("disabled")
                            if (json.success) {
                                var passwayNo = json.data;
                                if (!isfrash)
                                    window.parent.pager.bindData(parent.pager.pageIndex);
                                else {
                                    window.parent.pager.bindData(parent.pager.pageIndex, true);
                                    window.location.reload();
                                }
                                layer.msg("保存成功", { time: 1000 })
                            } else {
                                layer.msg(json.msg, { title: "消息提示", btn: ['我知道了'], icon: 0, area: ["320px", "auto"], time: 0 });
                            }
                        }, "json");
                    }, res => {
                        $("#Save").removeAttr("disabled")
                    })
                }
                else {
                    LAYER_OPEN_TYPE_0("确定修改车道?", res => {
                        LAYER_LOADING("处理中...")
                        param.Passway_No = paramNo;
                        $.post("UpdatePassway", { jsonModel: JSON.stringify(param), linkModel: JSON.stringify(linkData) }, function (json) {
                            $("#Save").removeAttr("disabled")
                            if (json.success) {
                                if (!isfrash)
                                    window.parent.pager.bindData(parent.pager.pageIndex);
                                else {
                                    window.parent.pager.bindData(parent.pager.pageIndex, true);
                                    window.location.reload();
                                }
                                layer.msg("保存成功", { time: 1000 })
                            } else {
                                layer.msg(json.msg, { title: "消息提示", btn: ['我知道了'], icon: 0, area: ["320px", "auto"], time: 0 });
                            }
                        }, "json");
                    }, res => {
                        $("#Save").removeAttr("disabled")
                    })
                }
            }
        };

        //判断出口是否启用收费
        var gateOutDisabledCharge = function (data, link) {
            if (data.Passway_IsCharge == 0) {
                if (link.length == 1 && link[0].PasswayLink_GateType == 0) {
                    return false;
                }
            }
            return true;
        }
    </script>

    <script>
        //设备参数配置[仅选项按钮]默认值
        var config = {
            Passway_Type: 1,
            Passway_DutyMode: 1,
            Passway_CameraMode: 1,
            Passway_IsBackCar: 0,
            Passway_IsCharge: 0,
            Passway_OpenGateAndVoice: 1,
            Passway_Sense: 1,
            Passway_SameInOut: 0,
            Passway_SameInOutType: 0,
            Passway_EnableBoard: 0
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName)
            });

            $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
                var key = $(this).attr("data-key");
                var data = getHelpContent(key);
                if (data) {
                    layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
                }
            }, function () {
                layer.closeAll();
            });
        });

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return null;
            return data;
        }
        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName)
            });
        }

        var onEventCombox = function (idName) {
            //if (idName == "Passway_CameraMode") {
            //    if (config[idName] == "2" || config[idName] == "3") {
            //        $(".Passway_CameraMode").removeClass("layui-hide");
            //    } else {
            //        $(".Passway_CameraMode").removeClass("layui-hide").addClass("layui-hide");
            //    }
            //}

            if (idName == "Passway_Type") {
                if (config[idName] == "1") {
                    $(".nonVehicle").removeClass("layui-hide");
                } else {
                    $(".nonVehicle").removeClass("layui-hide").addClass("layui-hide");
                }
            }


            if (idName == "Passway_SameInOut") {
                if (config[idName] == "1") {
                    $(".Passway_SameInOut").removeClass("layui-hide");
                } else {
                    $(".Passway_SameInOut").removeClass("layui-hide").addClass("layui-hide");
                }
            }

            if (idName == "Passway_EnableBoard") {
                var elem = $(".Passway_EnableBoard");
                if (config[idName] == "1") {
                    $(elem).removeClass("layui-hide");
                } else {
                    $(elem).removeClass("layui-hide").addClass("layui-hide");
                }
            }
        }

        //表单赋值，编辑时设置所属区域及出入口
        var SetCheckedTr = function (defdata) {
            for (var i = 0; i < defdata.length; i++) {
                pager.allArea.forEach(function (item, index) {
                    if (item.ParkArea_No == defdata[i].PasswayLink_ParkAreaNo) {
                        var trcheckbox = $("tr[data-index='" + index + "'] input[type='checkbox']");
                        trcheckbox.attr("checked", true);

                        var radio = $("tr[data-index='" + index + "'] input[type='radio'][value='" + defdata[i].PasswayLink_GateType + "']");
                        $("input[name='radio" + defdata[i].PasswayLink_ParkAreaNo + "']").removeAttr("disabled");
                        radio.attr("checked", true);
                    }
                });
            }
            layform.render()
        }

        //读取当前关联区域列表
        var getLinkData = function () {
            var data = [];
            pager.allArea.forEach(function (item, index) {
                var trcheckbox = $("tr[data-index='" + index + "'] input[type='checkbox']");
                if (trcheckbox[0].checked) {
                    var radioValue = 0;
                    var radio = $("input[name='radio" + item.ParkArea_No + "']");
                    radio.each(function () {
                        if ($(this)[0].checked)
                            radioValue = $(this).val();
                    })
                    var link = {
                        PasswayLink_ParkAreaNo: item.ParkArea_No,
                        PasswayLink_GateType: radioValue
                    };
                    data[data.length] = link;
                }
            })
            console.log(data)
            return data;
        }

        //提示信息数据
        var HelpData = [
            {
                key: "Passway_Type",
                Description: ["非机动车道：不支持云支付，只支持反扫微信或支付宝付款二维码支付"],
            }]
    </script>
</body>
</html>
