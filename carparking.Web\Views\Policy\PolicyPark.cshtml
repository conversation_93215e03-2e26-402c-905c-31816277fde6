﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>功能策略设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <script src="~/Static/plugins/carnopicker/carnopicker.js?1" asp-append-version="true"></script>
    <style>
        html, body { background-color: #fff !important; margin: 0; }
        .layui-tab-title { padding-left: 2rem; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }
        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }
        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }
        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_park1.svg'); }
        .layui-tab-title li.layui-this.type5::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-content { padding: 2rem; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }
        .layui-disabled { background-color: #eee; opacity: 1; }
        .layui-form-select dl { box-shadow: 0 0 6px; }

        input[value='自动放行'] { color: #1ab394 !important; }
        input[value='禁止通行'] { color: red !important; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }
        select[multiple] + .layui-form-select dd { padding: 0; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }
        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }
        .multiSelect a { padding: 2px 5px; background: #0094ff; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }
        .multiSelect a span { float: left; }
        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }
        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }
        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }
        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }
        .multiSelect a i:hover { background-color: #545556; }
        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }
        .multiOption:hover { color: #5FB878 }
        .simplehide { display: none; }
        .moresetting { display: none; }
        .headmoresetting { cursor: pointer; color: #1e9fff; }
        .headmoresetting:hover { font-weight: 600; }
        .otherdesc { display: none; }
        .descicon { cursor: pointer; font-size: 1.1rem; }
        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }
        .help-btn { position: absolute; width: 20px; margin-left: 7px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }

        .firsttime-table { width: 100%; }
        .firsttime-table td { padding: 1px; text-align: center; }
        input[name='BroadContent_Text'] { width: 100%; border: 0 !important; }
        .dot { display: inline-block; width: 7px; height: 7px; background-color: red; border-radius: 50%; }
        .bottomButton { position: fixed; bottom: 0; left: 0; width: 100%; background: #fff; padding: 10px; z-index: 9999; }
        .layui-table { margin-bottom: 50px; }
        tr:not(.firsttime-tr) > td:first-child { min-width: 100px; }
        tr:not(.firsttime-tr) > td:nth-child(2) { min-width: 200px; }
    </style>
</head>
<body>

    <div class="layui-form" style="background-color:#fff !important;">
        <!--停车场设置-->
        <table class="layui-table">
            <thead>
                <tr>
                    <th>功能类目</th>
                    <th>功能方案</th>
                    <th width="100"></th>
                    <th>注释</th>
                </tr>
            </thead>
            <tbody data-key="park" id="parkpanl">
                <tr class="simple">
                    <td>系统自动释放车位</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_OccupyDay" name="PolicyPark_OccupyDay" maxlength="3" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置当车辆停车超过设定天数后仍未出场，系统在不删除场内记录的情况下自动释放车位，单位为"天"。</td>

                </tr>
                <tr>
                    <td>车场所在的省份或直辖市的简称</td>
                    <td>
                        <input type="text" class="layui-input" id="PolicyPark_CarPrefix" name="PolicyPark_CarPrefix" value="粤" maxlength="1" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置入出场弹框或车辆登记时的默认省份或直辖市的简称，例如广东设置为"粤"。</td>
                </tr>
                <tr class="simple">
                    <td>收费框可取消</td>
                    <td>

                        <select class="layui-select" id="PolicyPark_CollectAllowCancel" name="PolicyPark_CollectAllowCancel">
                            <option value="1">允许取消操作</option>
                            <option value="0">禁止取消操作</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置出口弹出收费框后是否允许取消。</td>
                </tr>
                <tr class="simple">
                    <td>金额小数位数</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_Floatlen" name="PolicyPark_Floatlen">
                            <option value="0">无小数位</option>
                            <option value="1">1位小数</option>
                            <option value="2">2位小数</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置计费过程中车牌识别设备显示小数的场景，例如3.50元。</td>
                </tr>
                <tr class="simple">
                    <td>不同车道识别间隔时长</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_RememberTime" name="PolicyPark_RememberTime" maxlength="4" value="5" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置不同车道识别相同车牌在X秒内不处理。举例：同进同出的场景，单位为"秒"。</td>
                </tr>
                <tr class="simple">
                    <td>相同车道识别间隔时长</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_RememberTime0" name="PolicyPark_RememberTime0" maxlength="4" value="3" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置相同车道识别同一个车牌在X秒内不处理。举例:在同一个车道车辆重复识别的情况，单位为"秒"。</td>
                </tr>
                <tr class="simple">
                    <td>无入场记录查找最近出场记录</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_FeeNoRecord" name="PolicyPark_FeeNoRecord">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置无入场记录时，是否以最近一次的出场时间作为本次停车的入场时间进行计费。<br /><t style="color:red;">注意：1、若设置无入场记录禁止通行时，将不会查找最近的出场记录；2、当匹配到记录后不会生成异常放行记录。</t></td>
                </tr>
                <tr class="simple">
                    <td>无入场记录查找最近出场记录的时长范围</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_FeeNoRecordTime" name="PolicyPark_FeeNoRecordTime" maxlength="4" value="24" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置无入场记录时匹配出场记录的时间范围，举例：填写24，仅匹配24小时内的最近一条出场记录，单位为"小时"。</td>
                </tr>
                <tr class="simple">
                    <td>车辆登记/延期是否启用费用清缴</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_PayedCharge" name="PolicyPark_PayedCharge">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置对场内车辆操作登记或延期时，是否清缴已产生的停车费用。<br /><t style="color:red;">注意：该配置项仅对线下软件登记车牌或延期时生效。</t></td>
                </tr>
                <tr>
                    <td colspan="4">
                        <div class="layui-row headmoresetting"><t class="content">更多设置</t>&nbsp;<i class="layui-icon layui-icon-down"></i></div>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>白牌车识别自动开闸</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_WhiteCar" name="PolicyPark_WhiteCar">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置识别白牌车是否自动开闸。<br>
                        <t style="color:red;">注意：启用后白牌车开闸优先级最高，不受其它设置影响。</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>军警车识别自动开闸</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_MilitaryPoliceCar" name="PolicyPark_MilitaryPoliceCar">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置识别军警车是否自动开闸。<br>
                        <t style="color:red;">注意：启用后军警车开闸优先级最高，不受其它设置影响。</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>离线支付</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_OfflinePay" name="PolicyPark_OfflinePay">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置软件和平台断开连接后，车主自助扫车道离线二维码完成缴费后离场。<br>
                        <t style="color:red;">注意：车道设备必须有蓝牙功能且支持显示二维码。</t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>临时车播报汉字</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_TempChineseBroadcast" name="PolicyPark_TempChineseBroadcast">
                            <option value="0">否</option>
                            <option value="1">是</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置临时车进出场是否显示播报省份汉字。<br>
                        <t style="color:red;">注意：若设置为否，则显示播报【临】字。</t>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>场内缴费限时出场</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_MaxStayTime" name="PolicyPark_MaxStayTime" maxlength="4" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置车主在场内提前缴费后，在场内的免费停留时间。举例：设置15分钟，则代表缴费后在15分钟内免费出场，单位为"分钟"。</td>
                </tr>

                @* <tr>
                <td>计费0元是否自动放行</td>
                <td>
                <select class="layui-select" id="PolicyPark_PayedConfirm" name="PolicyPark_PayedConfirm">
                <option value="0">否</option>
                <option value="1">是</option>
                </select>
                </td>
                <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                <td>计费0元或车主场内已缴费用，在出口处是否需要确认放行。</td>
                </tr>*@
                <tr class="simple moresetting">
                    <td>无感支付是否需确认开闸</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_NoPwdPayedConfirm" name="PolicyPark_NoPwdPayedConfirm">
                            <option value="0">否</option>
                            <option value="1">是</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置系统启用无感支付，车辆在出口无感支付完成是否需确认开闸放行。</td>
                </tr>
                <tr class="simple moresetting">
                    <td>优惠是否支持叠加次/张</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_MaxDiscount" name="PolicyPark_MaxDiscount">
                            <option value="0">0张</option>
                            <option value="1">1张</option>
                            <option value="2">2张</option>
                            <option value="3">3张</option>
                            <option value="4">4张</option>
                            <option value="5">5张</option>
                            <option value="6">6张</option>
                            <option value="7">7张</option>
                            <option value="8">8张</option>
                            <option value="9">9张</option>
                            <option value="10">10张</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置车辆缴费时，是否支持多张优惠券叠加使用，0张则表示默认不使用优惠券。</td>
                </tr>
                <tr class="simple moresetting">
                    <td>优惠券使用限额</td>
                    <td>
                        <input type="text" class="layui-input v-float v-null" id="PolicyPark_MaxUseAmount" name="PolicyPark_MaxUseAmount" maxlength="10" value="0" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置停车费用超过设定金额后，限制使用优惠券。一般用于系统启用了折扣优惠或全免优惠的使用场景。<br>
                        <t style="color:red;">注意：岗亭出口弹窗缴费时，可手动选择优惠券进行减免，不受该设置限制。</t>
                    </td>
                </tr>


                <tr class="simple moresetting">
                    <td>无入场记录显示停车时长</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_NoRecordRangTime" name="PolicyPark_NoRecordRangTime" maxlength="4" value="5" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置车辆无入场记录按最低收费时，车牌识别设备显示播报的停车时长，单位为"分钟"。</td>
                </tr>
                <tr class="simple moresetting">
                    <td>无牌车扫码获取价格顺序</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_NoRecordGetMoney" name="PolicyPark_NoRecordGetMoney">
                            <option value="0">优先无牌车</option>
                            <option value="1">优先有牌车</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置车主在出口扫码付款时，优先按有牌车或无牌车获取停车费用。<i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            <b>【优先有牌车】</b>：当有牌车在岗亭出口车道弹窗进行缴费时，出口扫码识别的停车订单<br />默认关联该有牌车辆，确保放行的是当前正在缴费的车辆。<br /><br />
                            <b>【优先无牌车】</b>：若设置为优先无牌车，即使有牌车正在进行弹窗缴费，出口扫码时系<br />统仍优先获取无牌车的停车订单。此设置可能导致系统误将无牌车的订单作为放行依据，<br />从而出现有牌车辆尚未完成缴费出场、但闸机已放行的情况。
                        </span>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>无牌车入场指定车牌颜色</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_DefaultNoneCarType" name="PolicyPark_DefaultNoneCarType">
                            <option value="">不设置</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置无牌车入口扫码按指定车牌颜色计费。<br>
                        <t style="color:red;">注意：若未指定车牌颜色，则默认按【车场管理】--【车牌颜色】模块下的"无牌车"类型计费。</t>
                    </td>
                </tr>

                <tr class="simple moresetting">
                    <td>消防联动开闸</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_FireOpen" name="PolicyPark_FireOpen">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置当有任意一个相机接收到消防报警信号时，系统将所有道闸常开（T30默认开启该相机所在岗亭绑定的车道）。<br>
                        <t style="color:red;">
                            注意：
                            1、需将消防开关量信号接入到相机的IN2端子；
                            2、该设置项与"非法开闸"功能互斥。
                        </t>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>非法开闸忽略时间</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_IllegalGateIgnoreTime" name="PolicyPark_IllegalGateIgnoreTime" maxlength="3" value="10" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置软件下发开闸后，在指定秒数内收到非法开闸信号不处理，避免正常过车时产生非法开闸信号。单位为"秒"，默认10秒。<br>
                        <t style="color:red;">注意：设置为0表示不忽略非法开闸信号。</t>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>试运营</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_AutoTakerecord" name="PolicyPark_AutoTakerecord">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于车场未正式收费前，检验系统的运行情况。当启用后，优先保存车辆通行记录并自动开闸放行。<br>
                        <t style="color:red;">注意：启用后，出口无法使用电子支付。</t>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>手机号码保密</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_PhoneSecret" name="PolicyPark_PhoneSecret">
                            <option value="0">不保密</option>
                            <option value="1">保密</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置系统中的手机号是否脱敏显示</td>
                </tr>

                @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.CloudServer)
                {
                    <tr class=" moresetting">
                        <td>嵌套区域有进场无出场记录匹配</td>
                        <td>
                            <select class="layui-select" id="PolicyPark_NestedRecords" name="PolicyPark_NestedRecords">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                        <td>
                            用于设置车辆在内场区域有入场记录，但无出场记录时，从内场入场时间到外场出场时间段系统按内场或者外场的计费标准收费。<br>
                            <t style="color:red;">
                                注意：
                                1、若存在多层嵌套，以此类推按照上层区域的出场时间计费。
                                2、内场区域出口启用收费时内场停车时长会直接按外场收费。
                            </t>
                        </td>
                    </tr>

                    <tr class=" moresetting">
                        <td>创建支付0元的订单</td>
                        <td>
                            <select class="layui-select" id="PolicyPark_CreateZero" name="PolicyPark_CreateZero">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </td>
                        <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                        <td>用于设置收费为0元时，是否在【缴费记录】新增支付订单并上传平台。（一般用于城市平台对接或需查看车辆车时长的场）</td>
                    </tr>
                }

                @*  <tr class=" moresetting">
                <td>车场业务数据缓存</td>
                <td>
                <select class="layui-select" id="PolicyPark_BusinessCache" name="PolicyPark_BusinessCache">
                <option value="0">禁用</option>
                <option value="1">启用</option>
                </select>
                </td>
                <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                <td>
                用于设置车场业务数据读取的方式，启用后读取缓存数据，禁用后读取数据库数据，启用后有利于降低数据库资源，提高系统的运行速度。<br>
                <t style="color:red;">注意：禁用后对电脑有一定的配置要求，否则将影响执行效率。</t>
                </td>
                </tr> *@

                <tr class=" moresetting">
                    <td>以进外场时创建订单的车牌类型为准</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_EnterCarCardType" name="PolicyPark_EnterCarCardType">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置车辆进出内外场的车牌类型或车牌颜色不一致时，是否以进外场时的车牌类型为准。<br>
                        例如：车辆从外场进入为蓝牌或临时车A，进入内场时为绿牌或临时车B，启用后车辆出场将会按蓝牌类型出场。
                    </td>
                </tr>
                @{
                    if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.WindowsStandard)
                    {
                        <tr class=" moresetting">
                            <td>识别未支付记录</td>
                            <td>
                                <select class="layui-select" id="PolicyPark_UnpaidRecord" name="PolicyPark_UnpaidRecord">
                                    <option value="0" selected>禁用</option>
                                    <option value="1">启用</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>用于设置车辆在出口产生费用但未缴费时，是否在【识别未支付】报表中生成一条识别未支付记录。</td>
                        </tr>
                    }
                }


                <tr class="moresetting">
                    <td>出口识别放行</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_OutRecordFindEnble" name="PolicyPark_OutRecordFindEnble">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        当出口识别到无入场记录的车辆时，系统将根据“出口识别放行时间”的设置，自动查询车辆在该时间范围内的出场记录，并执行自动放行。
                        查询时间范围为：当前出场时间起，向前回溯设定的分钟数。若匹配成功，将直接放行，<t style="color:red;">
                            但不会生成停车订单详情。
                        </t>
                    </td>
                </tr>
                <tr class="moresetting PolicyPark_OutRecordFindEnble layui-hide">
                    <td>出口识别放行时间</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyPark_OutRecordFindMin" name="PolicyPark_OutRecordFindMin" maxlength="4" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        单位为"分钟"。<br /><t style="color:red;">⚠️ 注意：1、由于系统每日凌晨 2:00 至 4:00 会执行数据清理任务，此设置仅对清理任务执行后新生成的出场记录生效。<br /><p style="text-indent: 4.7em;">2、只会匹配上次正常进出的记录，如果上次记录是无入场记录，则不会进行匹配。</p></t>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>自动追缴设置</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_AutoChase" name="PolicyPark_AutoChase">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        启用后，出口触发跟车事件后，系统将自动关联场内停车订单记录生成追缴订单。
                    </td>
                </tr>

                <tr class="moresetting">
                    <td>教练车按指定车牌类型和颜色入场</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_CoachCar" name="PolicyPark_CoachCar">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置车辆入场时，检测车牌号"学"字，则按照指定车牌类型和颜色入场。当该车有登记成固定车或访客车，则不生效，按登记的车牌类型和颜色为准
                    </td>
                </tr>
                <tr class="moresetting PolicyPark_CoachCar layui-hide">
                    <td>教练车入场车牌类型</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_CoachCarType" name="PolicyPark_CoachCarType">
                            <option value="">不设置</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                    </td>
                </tr>
                <tr class="moresetting PolicyPark_CoachCar layui-hide">
                    <td>教练车入场车牌颜色</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_CoachCarColor" name="PolicyPark_CoachCarColor">
                            <option value="">不设置</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>无入场记录出场查找预入场记录</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_NoEnterRecordSearch" name="PolicyPark_NoEnterRecordSearch">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        启用时，无入场记录出场时，查找预入场记录，当匹配到预入场记录需正常计费离场<br /><t style="color:red;">注意：若设置无入场记录禁止通行，启用该配置后，出场也会查找预入场记录进行收费。</t>
                    </td>
                </tr>
                <tr class="moresetting PolicyPark_BroadContent">
                    <td>车道常用提示语音</td>
                    <td>
                        <table class="firsttime-table">
                            <tbody class="tbody">
                                <tr class="firsttime-tr">
                                    <td>
                                        <div class="layui-col-xs12">
                                            <input type="text" class="layui-input" id="BroadContent_Text" name="BroadContent_Text" placeholder="播报内容,最长15个字符" maxlength="15" />
                                        </div>
                                    </td>
                                    <td>
                                        <i class="layui-icon layui-icon-add-1 btnadd" style="cursor:pointer"></i>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置入出口，值班人员需对车道进行下发语音提示时，可快捷选择预设的车道提示语音。
                    </td>
                </tr>
                <tr class=" moresetting">
                    <td>超时计费以应收金额为准</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_OverTimePay" name="PolicyPark_OverTimePay">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置车辆出场计费时，检测缴费记录，以应收金额大于0元为准。</td>
                </tr>
                <tr class="moresetting">
                    <td>访客车辆预约时间内可进出次数</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_VisitorTimes" name="PolicyPark_VisitorTimes">
                            <option value="0">单次</option>
                            <option value="1">多次</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        访客车辆预约时间内可进出次数：用于设置访客预约车在预约时间段内，可进出的次数；
                    </td>
                </tr>
                <!--商家车辆可往前登记开始时间-->
                <tr class="moresetting">
                    <td>商家车辆可往前登记开始时间</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_BusinessStartTime" name="PolicyPark_BusinessStartTime">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        商家车辆可往前登记开始时间：用于设置软件上登记商家车辆时，开始时间是否支持小于当前时间。<br /><t style="color:red;">注意：该功能仅对线下登记商家车辆生效，启用后如果往前登记的开始时间早于车辆入场时间，出场将不收费；</t>
                    </td>
                </tr>
                <!--支付成功播报-->
                <tr class="moresetting">
                    <td>支付成功播报</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_PayedSuccessBroad" name="PolicyPark_PayedSuccessBroad">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        启用后，出口设备将对支付成功进行统一分类语音播报：<br />
                        • 无感支付：统一播报"无感支付XX元，一路顺风"（不区分具体支付类型）<br />
                        • 扫车道码支付：统一播报"扫车道码支付XX元，一路顺风"（包含微信、支付宝、乐聚合、随行付等）<br />
                        • ETC支付：播报"ETC支付XX元，一路顺风"<br />
                        <t style="color:red;">⚠️注意：1、启用了卡类自定义语言或出口自定义语音后，该配置不生效；2、扫车道码支付播报场景，仅对临时车生效，固定车不生效；</t>
                    </td>
                </tr>
                <tr class=" moresetting">
                    <td>同区域合并停车时长计费</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_MergeyHours" name="PolicyPark_MergeyHours">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        当车辆出场时，如存在多个区域且每个区域包含多个停车时段，则合并同一区域内的所有停车时段，统一计算该区域的停车费用。<br />
                        <t style="color:red;"> ⚠️注意：计费规则若按周期计费，多区域合并会存在停车时段重叠，将导致周期费用重复计入，极易触发封顶逻辑，需特别关注周期规则与时间重叠的交叉影响。</t>
                    </td>
                </tr>

                <tr class="moresetting">
                    <td>扫码登记入场自动开闸放行</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_ScanEnter" name="PolicyPark_ScanEnter">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于车辆进场需要《确认放行》时，扫码登记后自动开闸放行。
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>第三方账单追缴</td>
                    <td>
                        <select class="layui-select" id="PolicyPark_EnbleOverdueBill" name="PolicyPark_EnbleOverdueBill">
                            <option value="0">禁用</option>
                            <option value="1">启用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于对第三方平台下发的停车账单在车辆进出场时进行追缴。<t style="color:red;"> ⚠️注意： 只支持外场出口车道。</t>
                    </td>
                </tr>
                @* <tr>
                <td>临时车脱机开闸</td>
                <td>
                <select class="layui-select" id="PolicyPark_OfflineOpening" name="PolicyPark_OfflineOpening">
                <option value="0">禁用</option>
                <option value="1">启用</option>
                </select>
                </td>
                <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                <td>是否启用临时车脱机自动开闸，启用请填写脱机时长。</td>
                </tr>
                <tr>
                <td>离线多长时间认定为脱机</td>
                <td>
                <input type="text" class="layui-input v-number v-null" id="PolicyPark_OfflineTime" name="PolicyPark_OfflineTime" maxlength="6" value="5" />
                </td>
                <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                <td>(单位：秒)</td>
                </tr>*@
            </tbody>
        </table>
        <div class="layui-row savebtn">
            <button class="layui-btn layui-btn-sm saveAll" data-id="park">保存全部</button>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}" data-type="${CarCardType_Type}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplrule">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">以 ${CarCardType_Name} 计费规则计费</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1.9" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        if ('@carparking.Config.PubVar.iParkingType' == "1") {
            $("#PolicyPark_MaxDiscount").attr("disabled", true);
        }

        myVerify.init();
        var cp = new CarnoPicker("#PolicyPark_CarPrefix", { ischar: false });
        cp.init();
        layui.use(['element', 'form', 'laydate'], function () {
            pager.init();
        })


        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//访客车类型

        var pager = {
            parkareas: null,
            passways: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            drives: null,       //设备型号列表
            links: null,        //通道关联区域列表
            Province: [],
            City: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PolicyCarCard_DelayMaxDate"], { type: 'date' });

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        pager.carTypes = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                pager.passways = parent.pager.passways;
                pager.carCardTypes = parent.pager.carCardTypes;
                pager.drives = parent.pager.drives;
                pager.links = parent.pager.links;

                var data = [], tempcar = [];
                pager.carCardTypes.forEach(function (item, index) {
                    //data[data.length] = item;
                    if (item.CarCardType_Type != 5) { data[data.length] = item; }//过滤访客车
                    if (item.CarCardType_Type == 1) { tempcar[tempcar.length] = item; }
                });
                $("#PolicyPark_DefaultNoneCarType").append($("#tmplcartype").tmpl(pager.carTypes));

                $("#PolicyPark_CoachCarType").html($("#tmplcarcardtype").tmpl(tempcar));
                $("#PolicyPark_CoachCarColor").html($("#tmplcartype").tmpl(pager.carTypes));

                $.post("SltProvinceList", {}, function (json) {
                    if (json.success) {
                        pager.Province = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("SltCityList", {}, function (json) {
                    if (json.success) {
                        pager.City = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                pager.Province.forEach((p, i1) => {
                    if (p.Province_ShortName != null && p.Province_ShortName != '') {
                        var options = '';
                        pager.City.forEach((c, i2) => {
                            if (c.Province_No == p.Province_No)
                                options += '<option value="' + (p.Province_ShortName + c.City_ShortName) + '" >' + c.City_Name + '</option>';
                        });
                        $("#PolicyArea_EPAddress").append(options);
                    }
                });

                layui.form.render();

                $("td").hover(function () {
                    //判断td里有headdesc样式
                    if ($(this).find("span.headdesc").length > 0) {
                        var $td = $(this).find("span.headdesc").siblings(".otherdesc");
                        var $div = $('<div>').append($td.contents().clone());
                        layer.tips($div.html(), this, {
                            tips: [1, '#090a0c'],
                            time: 0,
                            area: '50wh'  // 设置宽度为300px
                        });
                    }
                }, function () {
                    layer.closeAll('tips');
                });
            },
            bindData: function () {
                policy.park.onload();
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    //console.log(data.elem); //得到select原始DOM对象
                    //console.log(data.elem.id); //得到select原始DOM对象
                    //console.log(data.value); //得到被选中的值
                    //console.log(data.othis); //得到美化后的DOM对象

                    var val = data.value;
                    //开闸方式切换通道
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        policy.pass.onload();
                    }
                    //开闸方式切换车牌类型
                    else if (data.elem.id == "PolicyPass_CarCardTypeNo") {
                        policy.pass.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyPassway_PasswayNo") {
                        policy.passway.onload();
                    }
                    //车牌策略切换类型
                    else if (data.elem.id == "PolicyCarCard_CarCardTypeNo") {
                        policy.card.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyArea_ParkAreaNo") {
                        policy.area.onload();
                    }
                    //自定义语音设置
                    else if (data.elem.id == "PolicyPassway_ShowOption") {
                        policy.voicediy(data.elem.id, val);
                    }
                    //开闸方式-未找到入场记录最低收费标准
                    else if (data.elem.id == "PolicyPass_NoFundEnter") {
                        policy.minpayed(data.elem.id, val);
                    }
                    //发布内容
                    else if (data.elem.id == "PolicyPassway_Broadpushinfo") {
                        policy.pushinfo(data.elem.id, val);
                    }
                    //防疫设置
                    else if (data.elem.id == "PolicyArea_EPEnable") {
                        policy.fymodal(data.elem.id, val);
                    }
                    //车主车位已满后其余车辆
                    else if (data.elem.id == "PolicyArea_MoreCar") {
                        policy.opengate(data.elem.id, val);
                    } else if (data.elem.id == "PolicyPark_CoachCar") {
                        policy.coachcar(data.elem.id, val);
                    } else if (data.elem.id == "PolicyPark_OutRecordFindEnble") {
                        policy.openoutrecord(data.elem.id, val)
                    }
                })

                $("button.save").click(function () {

                    if (!myVerify.check()) return;

                    var param = {};
                    $(this).parent().siblings().find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            var v = $(this).val();
                            if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                if (v == null || v == '') v = [];
                                v = JSON.stringify(v);
                            }
                            // 对于播报内容特殊处理
                            if ($(this).closest('tr').hasClass('PolicyPark_BroadContent')) {
                                v = boadcastcontent.get();
                            }
                            param[$(this).attr('id')] = v;
                        }
                    });

                    var datakey = $(this).parent().parent().parent().attr("data-key");
                    pager.onSave(datakey, param);
                });

                $("button.saveAll").click(function () {
                    if (!myVerify.check()) return;

                    var param = {};
                    var datakey = $(this).attr("data-id");
                    $("tbody[data-key='" + datakey + "']").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide")) {
                                var v = $(this).val();
                                if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                    if (v == null || v == '') v = [];
                                    v = JSON.stringify(v);
                                }
                                // 对于播报内容特殊处理
                                if ($(this).closest('tr').hasClass('PolicyPark_BroadContent')) {
                                    v = boadcastcontent.get();
                                }
                                param[$(this).attr('id')] = v;
                            }
                        }
                    });
                    pager.onSave(datakey, param);
                });

                $("#BatchSetPassway").unbind("click").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/BatchSetPassWay",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $("#EditBatch").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/EditPassBatch",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $(".headmoresetting").unbind("click").click(function () {
                    var table = $(this).parent().parent().parent().find(".moresetting");
                    if ($(table).last().is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                        $(this).find("t").text("隐藏更多设置");
                        $(".savebtn").addClass("bottomButton");
                        var versionType1 = localStorage.getItem("versionType");
                        if (versionType1 == "simple") {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide").addClass("layui-hide").addClass("versionHide");
                        } else {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide");
                            if ($("#PolicyPark_CoachCar").val() == "1")
                                $(".PolicyPark_CoachCar").removeClass("layui-hide");
                            else
                                $(".PolicyPark_CoachCar").removeClass("layui-hide").addClass("layui-hide");

                        }
                    } else {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                        $(this).find("t").text("更多设置");
                        $(".savebtn").removeClass("bottomButton");
                    }
                    $(table).toggle("fast");
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                        $("button.saveAll").removeClass("layui-hide");
                    }

                    if (pagePower['EditBatch'] == 'true') {
                        $("#BatchSetPassway").removeClass("layui-hide");
                    }
                });
            },
            onSave: function (datakey, param) {
                //开闸方式保存
                if (datakey == 'pass') {
                    var PolicyPass_PasswayNo = $("#PolicyPass_PasswayNo").val();
                    var PolicyPass_CarCardTypeNo = $("#PolicyPass_CarCardTypeNo").val();
                    var obj = { PolicyPass_PasswayNo: PolicyPass_PasswayNo, PolicyPass_CarCardTypeNo: PolicyPass_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyPass", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车道设置保存
                else if (datakey == 'passway') {
                    var PolicyPassway_PasswayNo = $("#PolicyPassway_PasswayNo").val();
                    var obj = { PolicyPassway_PasswayNo: PolicyPassway_PasswayNo };
                    //obj[key] = val;
                    Object.assign(obj, param);
                    // 处理PolicyPassway_DoubleGateColorCodes的值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    obj.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));
                    console.log(obj)


                    $.post("SavePolicyPassway", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车牌类型策略保存
                else if (datakey == 'card') {
                    var PolicyCarCard_CarCardTypeNo = $("#PolicyCarCard_CarCardTypeNo").val();
                    var obj = { PolicyCarCard_CarCardTypeNo: PolicyCarCard_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyCarCard", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车场策略保存
                else if (datakey == 'park') {
                    var obj = {};
                    //obj[key] = val;
                    Object.assign(obj, param);
                    obj.PolicyPark_BroadContent = boadcastcontent.get();
                    $.post("SavePolicyPark", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //区域策略保存
                else if (datakey == 'area') {
                    var obj = {};
                    //obj[key] = val;
                    var obj = { PolicyArea_ParkAreaNo: $("#PolicyArea_ParkAreaNo").val() };
                    Object.assign(obj, param);
                    console.log(obj)
                    $.post("SavePolicyArea", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.open({
                                icon: 0,
                                content: json.msg,
                                area: ["180px", "auto"],
                                btn: ["我知道了"],
                                closeBtn: false,
                            });
                            //layer.msg(json.msg);
                        }
                    }, "json");
                }
            }
        }

        var policy = {
            park: {
                onload: function () {
                    $.post("GetPolicyPark", {}, function (json) {
                        if (json.success) {
                            $("#parkpanl").fillForm(json.data, function (data) { });
                            policy.openoutrecord("PolicyPark_OutRecordFindEnble", $("#PolicyPark_OutRecordFindEnble").val())
                            policy.coachcar("PolicyPark_CoachCar", $("#PolicyPark_CoachCar").val())
                            layui.form.render();
                            if (json.data.PolicyPark_BroadContent && json.data.PolicyPark_BroadContent != '') boadcastcontent.init(json.data.PolicyPark_BroadContent);
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            },
            voicediy: function (id, val) {
                if (val == 5)
                    $("#PolicyPassway_Show").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Show").removeClass("layui-hide").addClass("layui-hide");
            },
            pushinfo: function (id, val) {
                if (val == 1)
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide").addClass("layui-hide");
            },
            minpayed: function (id, val) {
                if (val == 4)
                    $("#PolicyPass_MinAmount").removeClass("layui-hide");
                else
                    $("#PolicyPass_MinAmount").removeClass("layui-hide").addClass("layui-hide");
            },
            fymodal: function (id, val) {
                if (!$(".fymodal").hasClass("versionHide")) {
                    if (val == 1)
                        $(".fymodal").removeClass("layui-hide");
                    else
                        $(".fymodal").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            opengate: function (id, val) {
                if (!$(".opengate").hasClass("versionHide")) {
                    if (val != 3)
                        $(".opengate").removeClass("layui-hide");
                    else
                        $(".opengate").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            openoutrecord: function (id, val) {
                if (val == 1)
                    $(".PolicyPark_OutRecordFindEnble").removeClass("layui-hide");
                else
                    $(".PolicyPark_OutRecordFindEnble").removeClass("layui-hide").addClass("layui-hide");
            },
            coachcar: function (id, val) {
                if (val == 1)
                    $(".PolicyPark_CoachCar").removeClass("layui-hide");
                else
                    $(".PolicyPark_CoachCar").removeClass("layui-hide").addClass("layui-hide");
            }

        }

        var boadcastcontent = {
            init: function (contents) {
                $('.tbody').empty();

                var contentArray = contents ? contents.split('|').filter(x => x) : [];

                // 如果没有内容，添加一个空行
                if (contentArray.length == 0) {
                    var emptyRow = `
                                                                                    <tr class="firsttime-tr">
                                                                                        <td>
                                                                                            <div class="layui-col-xs12">
                                                                                                <input type="text" class="layui-input" name="BroadContent_Text"
                                                                                                        placeholder="播报内容，最长15个字符" maxlength="15" />
                                                                                            </div>
                                                                                        </td>
                                                                                        <td>
                                                                                            <i class="layui-icon layui-icon-add-1 btnadd" style="cursor:pointer"></i>
                                                                                        </td>
                                                                                    </tr>
                                                                                `;
                    $('.firsttime-table').append(emptyRow);
                    return;
                }

                // 添加现有内容
                contentArray.forEach(function (content, index) {
                    if (content == '') return;
                    var newRow = `
                                                                                    <tr class="firsttime-tr">
                                                                                        <td>
                                                                                            <div class="layui-col-xs12">
                                                                                                <input type="text" class="layui-input" name="BroadContent_Text"
                                                                                                        placeholder="播报内容，最长15个字符" maxlength="15" value="${content}" />
                                                                                            </div>
                                                                                        </td>
                                                                                        <td>
                                                                                            ${index === contentArray.length - 1 ?
                            '<i class="layui-icon layui-icon-add-1 btnadd" style="cursor:pointer"></i>' :
                            '<i class="layui-icon layui-icon-delete btndelete" style="cursor:pointer; color: #FF5722;"></i>'}
                                                                                        </td>
                                                                                    </tr>
                                                                                `;
                    $('.firsttime-table').append(newRow);
                });

                boadcastcontent.bind();
            }
            , add: function (d) {
                //最多输入10条
                if ($('.firsttime-table tr').length >= 10) {
                    layer.msg('车道常用提示语音最多输入10条');
                    return;
                }
                // 弹出输入框
                layer.open({
                    type: 1,
                    title: '新增播报内容',
                    content: `
                                                                            <div class="layui-form" style="padding: 20px;">
                                                                                <div class="layui-form-item">
                                                                                    <label class="layui-form-label">播报内容</label>
                                                                                    <div class="layui-input-block">
                                                                                        <input type="text" id="newBroadContent" class="layui-input" placeholder="请输入播报内容，最长15个字符" maxlength="15">
                                                                                    </div>
                                                                                </div>
                                                                            </div>`,
                    area: ['500px', '200px'],
                    btn: ['确定', '取消'],
                    yes: function (index, layero) {
                        // 获取输入的内容
                        var content = $('#newBroadContent').val().trim();
                        if (!content) {
                            layer.msg('请输入播报内容');
                            return;
                        }

                        // 添加新行到表格
                        var newRow = `
                                                                                    <tr class="firsttime-tr">
                                                                                        <td>
                                                                                            <div class="layui-col-xs12">
                                                                                                <input type="text" class="layui-input" name="BroadContent_Text"
                                                                                                        placeholder="播报内容，最长15个字符" maxlength="15" value="${content}" />
                                                                                            </div>
                                                                                        </td>
                                                                                        <td>
                                                                                            <i class="layui-icon layui-icon-delete btndelete" style="cursor:pointer; color: #FF5722;"></i>
                                                                                        </td>
                                                                                    </tr>
                                                                                `;

                        // 在最后一行之前插入新行
                        $('.firsttime-table tr:last').before(newRow);

                        layer.close(index);
                        layer.msg('添加成功');
                        boadcastcontent.bind();
                    }
                });

                boadcastcontent.bind();
            }
            , del: function (d) {
                var tr = $(d).closest('tr');
                var totalRows = $('.firsttime-table tr').length;//firsttime-table

                // 如果删除后只剩一行，将删除按钮改为添加按钮
                if (totalRows <= 1) { // 2 = 当前行 + 添加按钮行
                    var input = tr.find('input').val();
                    var newRow = `
                                                                                    <tr class="firsttime-tr">
                                                                                        <td>
                                                                                            <div class="layui-col-xs12">
                                                                                                <input type="text" class="layui-input" name="BroadContent_Text"
                                                                                                        placeholder="播报内容，最长15个字符" maxlength="15" value="${input}" />
                                                                                            </div>
                                                                                        </td>
                                                                                        <td>
                                                                                            <i class="layui-icon layui-icon-add-1 btnadd" style="cursor:pointer"></i>
                                                                                        </td>
                                                                                    </tr>
                                                                                `;
                    tr.replaceWith(newRow);
                } else {
                    tr.remove();
                }
                boadcastcontent.bind();
            }
            , get: function (d) {
                var contents = $('.firsttime-table input[name="BroadContent_Text"]').map(function () {
                    return $(this).val().trim();
                }).get();
                return contents.join('|');
            }
            , bind: function () {
                // 修改添加按钮的点击事件
                $('.btnadd').off('click').on('click', function () {
                    boadcastcontent.add(this);
                });

                // 修改删除按钮的事件处理
                $('.btndelete').off('click').on('click', function () {
                    boadcastcontent.del(this);
                });
            }
        }
    </script>
</body>
</html>
