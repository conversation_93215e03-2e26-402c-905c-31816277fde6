﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>支付方式</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }


        .site-demo-laytpl div span { display: inline-block; text-align: center; background: #101010; color: #fff; }
        .site-demo-laytpl textarea { height: 100px; border: none; background-color: #3F3F3F; color: #E3CEAB; font-family: Courier New; resize: none; }
        .site-demo-laytpl textarea, .site-demo-laytpl div span { width: 98%; padding: 15px; margin: 0 15px; }
        button, input, optgroup, option, select, textarea { font-family: inherit; font-size: inherit; font-style: inherit; font-weight: inherit; outline: 0; }
        .layui-table-body .layui-none { line-height: 26px; padding: 30px 15px; text-align: center; color: #efeded; background-color: #3F3F3F; }

        .row { margin-right: -15px; margin-left: -15px;margin-top:-10px; }
        .page-box { border-Top: 1px solid #DDD; margin: 0px 15px 0px 15px; padding: 15px 0px 0px 0px; }
        .page-box .page-size { float: left; display: inline-block; padding-top: 5px; margin-right: 20px; }
        .no-padding-y { padding-right: 0px; padding-left: 0px; }
        .m-b-xs { margin-bottom: 5px; }
        .page-box select { font-weight: normal; width: auto; height: 24px; border-radius: 2px; border: 1px solid #e5e6e7; color: inherit; font-family: inherit; text-transform: none; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>维护管理</cite></a>
                <a><cite>数据查询</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">

                <div class="layui-bg-gray" style="padding: 5px;">

                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md12">
                            <div class="layui-panel">
                                <div style="padding: 5px 15px;">

                                    <div class="site-demo-laytpl">

                                        <div>
                                            <span>执行数据库语句(当前功能仅供技术人员使用，执行数据库语句会直接影响系统数据，请谨慎使用)</span>
                                            @*  <span>模板</span>*@
                                        </div>

                                        <textarea class="site-demo-text" id="textSql"></textarea>

                                        @* <textarea class="site-demo-text" id="tpl"></textarea>*@
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12">
                            <div class="layui-panel">
                                <div style="padding: 5px 15px;">
                                    <div class="layui-card-body">
                                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                                        <script type="text/html" id="toolbar_btns">
                                            <div class="layui-btn-container">
                                                <button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa  fa-download"></i><t>导出</t></button>
                                            </div>
                                        </script>
                                    </div>
                                    <div class="row">
                                        <div class="ibox-content page-box" style="display: inline-flex;">
                                            <div class="col-sm-5 page-size no-padding-y">
                                                共 <span id="item-sum">0</span> 项
                                              @*  显示<span id="item-begin"> 0</span> 到 <span id="item-end">0</span> 项，共 <span id="item-sum">0</span> 项，*@
                                              @*  每页显示
                                                <select id="page-size">
                                                    <option value="10">10</option>
                                                    <option value="25">25</option>
                                                    <option value="50">50</option>
                                                    <option value="100">100</option>
                                                </select>
                                                条记录*@
                                            </div>
                                            <div class="col-sm-7 no-padding-y">
                                                <div class=" m-b-xs fa-pull-right text-right dataTables_paginate paging_simple_numbers" id="page-btm"> </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/laypage-v1.3/laypage/laypage.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js" asp-append-version="true"></script>
    <script>
        var comtable = null;

        var table;
        layui.use(['table', 'jquery', 'form'], function () {
            table = layui.table;
        });

    </script>
    <script>

        var pager = {
            dataField: null,
            sortField: null,
            orderField: null,
            pageIndex: 1,
            pageSize: 10,

            bindData: function () {
                layer.msg("查询中", { icon: 16, time: 0 });
                $.ajax({
                    url: '/SQLExecutor/GetList',
                    type: 'POST',
                    dataType: 'json',
                    data: { pageIndex: pager.pageIndex, pageSize: pager.pageSize, sql: $("#textSql").val() },
                    success: function (data) {
                        layer.msg("查询成功", { icon: 1, time: 500 }, function () {
                            if (data.code != 0 && data.code != 2) { layer.msg(data.msg, { icon: 0 }); return; }
                            if (data.data != null && data.data.length > 0) {
                                var columns = Object.keys(data.data[0]);
                                var cols = [];
                                var col = {};
                                for (var i = 0; i < columns.length; i++) {
                                    col = {
                                        field: columns[i],
                                        title: columns[i],
                                        width: 100
                                    };
                                    cols.push(col);
                                }

                                var totalpage = Math.ceil(data.count / pager.pageSize);

                                // 渲染表格
                                table.render({
                                    elem: '#com-table-base',
                                    cols: [cols], // 注意cols要用二维数组形式传递
                                    data: data.data,
                                    page: false,
                                    limits: [10, 20, 50, 100],
                                    request: { pageName: 'pageIndex', limitName: 'pageSize' },
                                    toolbar: '#toolbar_btns',
                                    defaultToolbar: ["filter"],
                                    cellMinWidth: 90
                                });

								//头工具栏事件
								table.on('toolbar(com-table-base)', function (obj) {
									var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
									var data = checkStatus.data;  //获取选中行数据
									pager.pageIndex = $(".layui-laypage-curr").text();
									switch (obj.event) {
										case 'Export':
											pager.dataField = [];
											obj.config.cols[0].forEach((item) => {
												if (item.title)
													pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
											});
											layer.open({
												id: "x_edit_iframe",
												type: 2,
												title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
												content: '/ExportExcel/Index?Act=Update&Owner_No=',
												area: getIframeArea(['1100px', '400px']),
												maxmin: false,
												end: function () {
													if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                                        var conditionParam = { sql: $("#textSql").val() };
														var field = pager.sortField == null ? "" : pager.sortField;
														var order = pager.orderField == null ? "" : pager.orderField;

														//实现Ajax下载文件
                                                        $.fileDownload('/SQLExecutor/ExportExcel?' + "conditionParam=" +$("#textSql").val() + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
															httpMethod: 'GET',
															headers: {},
															data: null,
															prepareCallback: function (url) {
																$("#Export").attr("disabled", true);
																layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
															},
															successCallback: function (url) {
																$("#Export").attr("disabled", false);
																layer.msg('导出成功');
															},
															failCallback: function (html, url) {
																$("#Export").attr("disabled", false);
																layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
															}
														});
													}
												}
											});
											break;
									};
								});


                                //翻页组件
                                laypage({
                                    cont: 'page-btm', //容器。值支持id名、原生dom对象，jquery对象,
                                    pages: totalpage, //总页数
                                    curr: pager.pageIndex, //当前页
                                    skin: 'molv', //皮肤
                                    skip: true, //是否开启跳页
                                    first: 1, //将首页显示为数字1,。若不显示，设置false即可
                                    last: totalpage, //将尾页显示为总页数。若不显示，设置false即可
                                    prev: '<', //若不显示，设置false即可
                                    next: '>', //若不显示，设置false即可

                                    jump: function (e, first) { //触发分页后的回调
                                        if (!first) { //一定要加此判断，否则初始时会无限刷新
                                            pager.pageIndex = e.curr;
                                            pager.bindData();
                                        }
                                    }
                                });

                                //设置统计项显示
                                $("#item-begin").html(pager.pageIndex == 1 ? 1 : (pager.pageIndex - 1) * pager.pageSize);
                                $("#item-end").html(data.count < pager.pageIndex * pager.pageSize ? data.count : pager.pageIndex * pager.pageSize);
                                $("#item-sum").html(data.count);

                            } else {
                                layer.msg("无数据", { icon: 1, time: 1500 })
                            }
                        });

                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        layer.msg(textStatus + ': ' + errorThrown, { icon: 0 });
                    }
                });
            }
        }

        $(function () {
            var enterCount = 0;
            $(document).on('keydown', function (event) {
                if (event.which === 13) {
                    enterCount++;
                    if (enterCount === 3) {
                        enterCount = 0;
                        pager.bindData();
                    }
                } else {
                    enterCount = 0;
                }
            });
        });
    </script>
</body>
</html>
