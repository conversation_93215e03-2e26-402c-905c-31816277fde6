﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>倒车记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }

        /* 修复加载提示样式 */
        .layui-layer-loading .layui-layer-content {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            color: #666;
        }

        /* 自定义加载提示样式 */
        .custom-loading {
            text-align: center;
            padding: 30px 20px;
            font-size: 14px;
            color: #666;
            background: #fff;
            border-radius: 6px;
        }

        .custom-loading .loading-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1E9FFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
            vertical-align: middle;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>倒车记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input" name="BackCar_CarNo" id="BackCar_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车道名称" class="layui-select" id="BackCar_PasswayNo" name="BackCar_PasswayNo" lay-search>
                                    <option value="">车道名称</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input" name="BackCar_Time0" id="BackCar_Time0" autocomplete="off" placeholder="倒车时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="BackCar_Time1" id="BackCar_Time1" autocomplete="off" placeholder="倒车时间止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select data-placeholder="区域名称" class="layui-select" id="BackCar_ParkAreaNo" name="BackCar_ParkAreaNo" lay-search>
                                    <option value="">区域名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="layui-select" id="BackCar_CardNo" name="BackCar_CardNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="layui-select" id="BackCar_CarTypeNo" name="BackCar_CarTypeNo" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">

                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="myVideo" class="layui-hide" style="position:relative;overflow:hidden;">
        <video src="#" controls="controls" autoplay style="margin-left:2px;margin-top:2px;">
            <span>当前浏览器不支持video标签</span>
        </video>
    </div>

    <script type="text/x-jquery-tmpl" id="gate">
        {{# if(d.BackCar_GateType==1) {}}
        <span class="layui-badge layui-bg-blue">入口</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange">出口</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        topBar.init();

        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'BackCar_ID', title: 'ID', hide: true }
                , { field: 'BackCar_No', title: '记录编号', hide: true }
                , { field: 'BackCar_ParkNo', title: '车场编码', hide: true }
                , { field: 'BackCar_CarNo', title: '车牌号' }
                , { field: 'BackCar_Time', title: '倒车时间' }
                , { field: 'BackCar_DeviceNo', title: '相机编号', hide: true }
                , { field: 'BackCar_DeviceName', title: '相机名称' }
                , { field: 'BackCar_ParkAreaNo', title: '区域编号', hide: true }
                , { field: 'BackCar_ParkAreaName', title: '区域名称' }
                , { field: 'BackCar_PasswayNo', title: '车道编号', hide: true }
                , { field: 'BackCar_PasswayName', title: '车道名称' }
                , { field: 'BackCar_GateType', title: '出入口类型', toolbar: "#gate" }
                , { field: 'BackCar_CarTypeNo', title: '车牌颜色编号', hide: true }
                , { field: 'BackCar_CarTypeName', title: '车牌颜色名称' }
                , { field: 'BackCar_CardNo', title: '车牌类型编号', hide: true }
                , { field: 'BackCar_CardName', title: '车牌类型名称' }
                , {
                    field: 'BackCar_Video', title: '视频', templet: function (d) {
                        if (d.BackCar_Video == null || d.BackCar_Video == '') {
                            return '暂无';
                        } else {
                            var videoUrl = d.BackCar_Video;
                            // 判断是否为YM01回放链接（支持标识URL和真实URL）
                            if (videoUrl.startsWith('vzicloud://playback') || (videoUrl.includes('vzicloud.com') && videoUrl.includes('playback.flv'))) {
                                return "<div class='layui-btn layui-btn-xs' onclick='playVizCloudVideo(\"" + videoUrl + "\",\"" + d.BackCar_PasswayNo + "\")'>查看回放</div>";
                            }
                            // 判断是否为萤石云链接
                            else if (videoUrl.indexOf('open.ys7') != -1) {
                                return "<div class='layui-btn layui-btn-xs' onclick='openVideoView1(\"" + videoUrl + "\")'>查看</div>";
                            }
                            // 其他本地视频链接
                            else {
                                return "<div class='layui-btn layui-btn-xs' onclick='openVideoView(\"" + d.BackCar_ID + "\")'>查看</div>";
                            }
                        }
                    }
                }
                , { field: 'BackCar_OrderNo', title: '关联订单号' }
                , { field: 'BackCar_Remark', title: '备注' }
                , { field: 'BackCar_AddTime', title: '注册时间' }

            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/BackCar/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                //, totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data);
                    pager.data = data.data;
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Payment':
                    case 'Detail':
                        break;
                };
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            data: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                            $("#BackCar_ParkAreaNo").append(option)
                            layui.form.render("select");
                        });
                    }
                }, "json");

                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#BackCar_CardNo").append(option)
                            layui.form.render("select");
                        });
                    }
                }, "json");

                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#BackCar_PasswayNo").append(option);
                            layui.form.render("select");
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#BackCar_CarTypeNo").append(option);
                            layui.form.render("select");
                        });
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["BackCar_Time0", "BackCar_Time1"], { type: "datetime", range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/BackCar/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("BackCar_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "BackCar_CarNo") {
                        $("#BackCar_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }

        function openVideoView(id) {
            var videoUrl = pager.data.find((item, index) => { return item.BackCar_ID == id; }).BackCar_Video;
            $("#myVideo video").attr("src", PathCheck(videoUrl));
        }
        function openVideoView1(url) {
            layer.open({
                type: 2,
                title: false,
                content: 'Monitor?url=' + url,
                area: getIframeArea(['778px', '443px']),
                maxmin: false
            });
        }

        // 播放YM01视频（支持标识URL和真实URL）
        function playVizCloudVideo(url, passwayNo) {
            if (!url) {
                layer.msg("无效的视频地址", { icon: 2 });
                return;
            }

            // 如果是标识URL，需要先获取真实播放地址
            if (url.startsWith('vzicloud://playback')) {
                // 显示自定义加载提示
                var loadingIndex = layer.open({
                    type: 1,
                    title: false,
                    content: '<div class="custom-loading"><div class="loading-icon"></div>正在获取视频播放地址...</div>',
                    shade: 0.3,
                    shadeClose: false,
                    closeBtn: 0,
                    area: ['300px', '120px'],
                    offset: 'auto'
                });

                // 调用后端API获取真实播放地址
                $.ajax({
                    type: 'POST',
                    url: '/BackCar/GetVizCloudPlaybackUrl',
                    data: {
                        identifierUrl: url,
                        passwayNo: passwayNo
                    },
                    success: function (response) {
                        layer.close(loadingIndex);
                        if (response.success && response.data) {
                            openVizCloudPlaybackView(response.data, passwayNo);
                        } else {
                            layer.msg(response.message || "获取视频播放地址失败", { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.close(loadingIndex);
                        layer.msg("获取视频播放地址失败", { icon: 2 });
                    }
                });
            } else {
                // 直接播放真实URL
                openVizCloudPlaybackView(url, passwayNo);
            }
        }

        // 打开YM01视频回放窗口
        function openVizCloudPlaybackView(url, passwayNo) {
            if (!url) {
                layer.msg("无效的视频地址", { icon: 2 });
                return;
            }
            // 对 URL 进行编码，防止特殊字符导致问题
            var encodedUrl = encodeURIComponent(url);
            layer.open({
                type: 2, // iframe 层
                title: 'YM01视频回放', // 窗口标题
                // 注意：确保 Controller 中有对应的 Action: /BackCar/PlayVizCloudPlayback
                content: '/BackCar/PlayVizCloudPlayback?videoUrl=' + encodedUrl + '&passwayNo=' + passwayNo,
                area: getIframeArea(['800px', '600px']), // 设置弹窗大小，可根据需要调整
                maxmin: true // 允许最大化最小化
            });
        }

        var video = document.querySelector('video');
        //自适应宽高
        video.addEventListener('canplay', function () {
            var width = this.videoWidth;
            var height = this.videoHeight;
            console.log(width)
            console.log(height)
            if (width > height) {
                while (width > 900) {
                    width = width * 0.8;
                    height = height * 0.8;
                }
            } else {
                while (height > 600) {
                    width = width * 0.8;
                    height = height * 0.8;
                }
            }
            this.width = width - 5;
            this.height = height - 5;

            var html = $("#myVideo").html();
            layer.open({
                id: 'video',
                type: 1,
                title: false,
                content: html,
                area: getIframeArea([width + "px", height + "px"]),
                close: function () {
                    video.pause();
                }
            })
        });
        var isfirst = true;
        video.addEventListener("error", function (e) {
            if (!isfirst) {
                switch (video.error.code) {
                    case 1:
                        layer.msg("中止获取该视频");
                        break;
                    case 2:
                        layer.msg("该视频下载时发生错误");
                        break;
                    case 3:
                        layer.msg("该视频解码时发生错误");
                        break;
                    case 4:
                        layer.msg("该视频地址加载失败.");
                        break;
                    default:
                        layer.msg("该视频地址加载失败");
                        break;
                }
            }
            isfirst = false;
        });
    </script>
</body>
</html>
