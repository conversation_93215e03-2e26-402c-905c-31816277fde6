﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>账本查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 2px 4px; float: left; font-size: 14px; font: normal normal normal 14px/1 FontAwesome; }
        .layui-form-select .layui-input { width: 182px; }
        .layui-btn { line-height: normal !important; padding: 0 12px; }
        .layui-bg-wxgreen { background-color: #04BE02 !important; }
        .layui-bg-alipayblue { background-color: #1678ff !important; }
        .layui-bg-ylblue { background-color: #1678ff !important; }
        .colorRed { color: red; }
        .ss { color: #26817f; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>储值余额明细</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="Ledger_Space" id="Ledger_Space" autocomplete="off" placeholder="车位号" maxlength="30" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Ledger_CarNo" id="Ledger_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Ledger_PayedTime0" id="Ledger_PayedTime0" autocomplete="off" placeholder="收支时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Ledger_PayedTime1" id="Ledger_PayedTime1" autocomplete="off" placeholder="收支时间止" />
                            </div>

                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select data-placeholder="收支类型" class="layui-select" id="Ledger_Type" name="Ledger_Type" lay-search>
                                    <option value="">收支类型</option>
                                    <option value="4">登记</option>
                                    <option value="1">充值</option>
                                    <option value="2">停车缴费</option>
                                    <option value="3">退费</option>
                                    <option value="5">其他</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="收支状态" class="layui-select" id="Ledger_Code" name="Ledger_Code" lay-search>
                                    <option value="">收支状态</option>
                                    <option value="1">收入</option>
                                    <option value="2">支出</option>
                                </select>
                            </div>

                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.Ledger.Export){}}<button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                    <div class="layui-col-xs9"><span class="ss"><b>温馨提示：</b>储值余额明细中显示的车牌号，若为一位车主绑定多辆车，仅展示其中第一辆车的车牌号码。</span></div>
                </div>

            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20241109" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?t=233" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        topBar.init();

        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");

            searchFile.bindData(2);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            pager.conditionParam = conditionParam;

            var cols = [[
                { type: 'radio' }
                , { field: 'Ledger_ID', title: 'ID', hide: true }
                , { field: 'Ledger_Space', title: '车位号', width: 110 }
                , { field: 'Ledger_CarNo', title: '车牌号', width: 110 }
                , { field: 'Ledger_BeforeMoeny', title: '消费前余额' }
                , { field: 'Ledger_AfterMoeny', title: '消费后余额' }
                , { field: 'Ledger_Money', title: '收支金额' }
                , {
                    field: 'Ledger_Type', title: '收支类型', templet: function (d) {
                        switch (d.Ledger_Type) {
                            case 4: return "<span class='layui-badge layui-bg-green'>登记</span>";
                            case 1: return "<span class='layui-badge layui-bg-green'>充值</span>";
                            case 2: return "<span class='layui-badge layui-bg-blue'>停车缴费</span>";
                            case 3: return "<span class='layui-badge layui-bg-orange'>退费</span>";
                            default: return "<span class='layui-badge layui-bg-gray'>其他</span>";
                        }
                    }
                }
                , {
                    field: 'Ledger_Code', title: '收支状态', templet: function (d) {
                        return d.Ledger_Code == 1 ? "<span class='layui-badge layui-bg-green'>收入</span>" : "<span class='layui-badge layui-bg-blue'>支出</span>";
                    }
                }
                , { field: 'Ledger_Time', title: '收支时间' }
                , { field: 'Ledger_Remark', title: '收支描述' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Ledger/GetLedgerList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: false
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    pager.dataCount = data.count;
                    tb_page_set(data);
                    if (data.code == 0) {
                        //追加总计行，需要查询时在msg中返回总计数据
                        // var total = JSON.parse(data.msg);
                        // if (total) {
                        //     var tr = $(".layui-table-total table tbody").find("tr").first();
                        //     tr.find('td[data-key="1-0-0"] div').text("合计");

                        //     $(".layui-table-total table tbody").append('<tr>' + tr.html() + '</tr>');
                        //     tr = $(".layui-table-total table tbody").find("tr").last();
                        //     tr.find('td[data-key="1-0-0"] div').text("总计").css({ "color": "red" });

                        //     tr.find('td[data-field="Ledger_BeforeMoeny"] div').text(total.Ledger_BeforeMoeny.toFixed(2)).css({ "color": "red" });
                        //     tr.find('td[data-field="Ledger_AfterMoeny"] div').text(total.Ledger_AfterMoeny.toFixed(2)).css({ "color": "red" });
                        //     tr.find('td[data-field="Ledger_Money"] div').text(total.Ledger_Money.toFixed(2)).css({ "color": "red" });
                        // }
                    }
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var orderno = data[0].Ledger_No;
                        layer.open({
                            title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 支付详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'Detail?Ledger_No=' + encodeURIComponent(orderno)
                        });
                        break;
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                        if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                            if (conditionParam.Ledger_PayedTime0 == null) { layer.msg("请选择支付时间起", { icon: 0 }); return; }
                            if (conditionParam.Ledger_PayedTime1 == null) { layer.msg("请选择支付时间止", { icon: 0 }); return; }
                            if (_DATE.diffDay(new Date(conditionParam.Ledger_PayedTime0), new Date(conditionParam.Ledger_PayedTime1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                        }
                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/Ledger/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            sortField: null,
            orderField: null,
            dataField: null,
            pageIndex: 1,
            conditionParam: null,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["Ledger_PayedTime0", "Ledger_PayedTime1"], { type: "datetime", range: true });

                $.post("SltCarCardTypeList2", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#Ledger_CarCardTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#Ledger_CarTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltPayTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            if (item.PayType_Enable == 1) {
                                var option = '<option value="' + item.PayType_No + '">' + item.PayType_Name + '</option>';
                                $("#Ledger_PayTypeCode").append(option);
                            }
                        });
                    }
                }, "json");

                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            var option = '<option value="' + item.ParkArea_No + '">' + item.ParkArea_Name + '</option>';
                            $("#Ledger_ParkAreaNo").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/Ledger/GetLedgerList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });

                s_carno_picker.init("Ledger_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "Ledger_CarNo") {
                        $("#Ledger_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }

    </script>
</body>
</html>
