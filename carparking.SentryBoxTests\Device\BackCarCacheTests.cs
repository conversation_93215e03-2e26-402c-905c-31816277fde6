using Microsoft.VisualStudio.TestTools.UnitTesting;
using carparking.SentryBox.BarrierDevice;
using carparking.BLL.Cache;
using carparking.Model;
using carparking.SentryBoxTests.TestHelpers;
using Microsoft.Extensions.Caching.Memory;
using System;

namespace carparking.SentryBoxTests.Device
{
    /// <summary>
    /// 倒车缓存功能测试
    /// </summary>
    [TestClass]
    public class BackCarCacheTests
    {
        private const string TestPasswayNo = "P001";
        private const string TestCarNo = "粤A12345";
        private const string TestOrderNo = "123456789-A12345";

        [TestInitialize]
        public void Setup()
        {
            MockHelper.InitializeBasicCache();
        }

        [TestCleanup]
        public void Cleanup()
        {
            MockHelper.CleanupCache();
        }

        /// <summary>
        /// 测试缓存键值生成方法
        /// </summary>
        [TestMethod]
        public void GenerateBackCarCacheKey_ValidInputs_ShouldReturnCorrectKey()
        {
            // Arrange
            var passwayNo = TestPasswayNo;
            var carNo = TestCarNo;
            var expectedKey = $"{passwayNo}{carNo.Substring(1)}"; // P001A12345

            // Act
            var actualKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(passwayNo, carNo);

            // Assert
            Assert.AreEqual(expectedKey, actualKey);
        }

        /// <summary>
        /// 测试空车牌号的处理
        /// </summary>
        [TestMethod]
        public void GenerateBackCarCacheKey_EmptyCarNo_ShouldReturnEmpty()
        {
            // Arrange
            var passwayNo = TestPasswayNo;
            var carNo = "";

            // Act
            var actualKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(passwayNo, carNo);

            // Assert
            Assert.AreEqual(string.Empty, actualKey);
        }

        /// <summary>
        /// 测试单字符车牌号的处理
        /// </summary>
        [TestMethod]
        public void GenerateBackCarCacheKey_SingleCharCarNo_ShouldReturnEmpty()
        {
            // Arrange
            var passwayNo = TestPasswayNo;
            var carNo = "A";

            // Act
            var actualKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(passwayNo, carNo);

            // Assert
            Assert.AreEqual(string.Empty, actualKey);
        }

        /// <summary>
        /// 测试空车道号的处理
        /// </summary>
        [TestMethod]
        public void GenerateBackCarCacheKey_EmptyPasswayNo_ShouldReturnEmpty()
        {
            // Arrange
            var passwayNo = "";
            var carNo = TestCarNo;

            // Act
            var actualKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(passwayNo, carNo);

            // Assert
            Assert.AreEqual(string.Empty, actualKey);
        }

        /// <summary>
        /// 测试缓存存储和读取的一致性
        /// </summary>
        [TestMethod]
        public void BackCarCache_StoreAndRetrieve_ShouldBeConsistent()
        {
            // Arrange
            var parkOrder = new ParkOrder
            {
                ParkOrder_No = TestOrderNo,
                ParkOrder_CarNo = TestCarNo,
                ParkOrder_EnterTime = DateTime.Now,
                ParkOrder_StatusNo = 200
            };

            var cacheKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, TestCarNo);
            
            var cacheOptions = new MemoryCacheEntryOptions()
            {
                SlidingExpiration = TimeSpan.FromMinutes(15),
                Priority = CacheItemPriority.High,
                Size = 1
            };

            // Act - 存储到缓存
            AppBasicCache.GetMemoryCache.Set(cacheKey, parkOrder, cacheOptions);

            // Act - 从缓存读取
            var retrieved = AppBasicCache.GetMemoryCache.TryGetValue(cacheKey, out var cachedValue);

            // Assert
            Assert.IsTrue(retrieved, "应该能从缓存中读取到数据");
            Assert.IsNotNull(cachedValue, "缓存值不应该为null");
            Assert.IsInstanceOfType(cachedValue, typeof(ParkOrder), "缓存值应该是ParkOrder类型");
            
            var cachedOrder = (ParkOrder)cachedValue;
            Assert.AreEqual(parkOrder.ParkOrder_No, cachedOrder.ParkOrder_No, "订单号应该匹配");
            Assert.AreEqual(parkOrder.ParkOrder_CarNo, cachedOrder.ParkOrder_CarNo, "车牌号应该匹配");
        }

        /// <summary>
        /// 测试不同车牌号格式的缓存键生成
        /// </summary>
        [TestMethod]
        public void GenerateBackCarCacheKey_DifferentCarNoFormats_ShouldHandleCorrectly()
        {
            // Arrange & Act & Assert
            var testCases = new[]
            {
                new { CarNo = "粤A12345", Expected = $"{TestPasswayNo}A12345" },
                new { CarNo = "京B88888", Expected = $"{TestPasswayNo}B88888" },
                new { CarNo = "沪C99999", Expected = $"{TestPasswayNo}C99999" },
                new { CarNo = "临A12345", Expected = $"{TestPasswayNo}A12345" },
                new { CarNo = "WJ12345", Expected = $"{TestPasswayNo}J12345" },
            };

            foreach (var testCase in testCases)
            {
                var actualKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, testCase.CarNo);
                Assert.AreEqual(testCase.Expected, actualKey, $"车牌号 {testCase.CarNo} 的缓存键生成错误");
            }
        }

        /// <summary>
        /// 测试订单号后缀与车牌号的匹配
        /// </summary>
        [TestMethod]
        public void BackCarCache_OrderNoSuffixMatching_ShouldWork()
        {
            // Arrange
            var orderNo = "123456789-A12345";
            var carNo = "粤A12345";
            var passwayNo = TestPasswayNo;

            // 使用车牌号生成的缓存键
            var cacheKeyByCarNo = BarrierDeviceUtilsl.GenerateBackCarCacheKey(passwayNo, carNo);
            
            // 使用订单号后缀生成的缓存键
            var orderSuffix = orderNo.Split('-')[1]; // A12345
            var cacheKeyByOrderSuffix = $"{passwayNo}{orderSuffix}";

            // Assert
            Assert.AreEqual(cacheKeyByCarNo, cacheKeyByOrderSuffix, 
                "使用车牌号和订单号后缀生成的缓存键应该相同");
        }
    }
}
