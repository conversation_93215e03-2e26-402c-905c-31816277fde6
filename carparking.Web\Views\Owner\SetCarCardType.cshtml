﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改车牌类型</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet" />
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-btn-warm { background-color: #ec971f; border-color: #d58512; }
        .layui-row { margin-bottom: 15px; }
        .layadmin-warning {line-height: 24px;font-size: 12px;text-align: justify;word-break: break-all;color: #888;background-color: lemonchiffon;float: left;padding: 3px 5px;letter-spacing: 0.6px; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div>
            <div>&nbsp;</div>
            <div style="overflow:hidden;height:0;">
                <!--防止浏览器保存密码后自动填充-->
                <input type="password" />
                <input type="text" />
                <input type="text" name="email" />
            </div>
        </div>
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">修改车牌类型</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select data-placeholder="车牌类型" class="form-control chosen-select " id="Owner_CardTypeNo" name="Owner_CardTypeNo" lay-search>
                        <option value="">车牌类型</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-form-checkbox" type="checkbox" id="clearfee" name="clearfee" checked title="场内车辆不生效" lay-skin="primary">
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-form-label">&nbsp;</div>
                <div class="layui-col-xs9 layadmin-warning">
                    <b>温馨提示：</b><br />
                    1、勾选【场内车辆不生效】，当车位下有车辆在场内时，该车位的车牌类型不会修改，只会变更没有在场的车位；<br />
                    2、<b>若不勾选，车辆在场内时，更改车牌类型会按操作时间重新生成入场订单并重新计算费用，请谨慎设置</b>。<br />
                </div>
            </div>
        </div>
        <div class="layui-footer">
            <button class="layui-btn layui-btn-md" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
            <button class="layui-btn layui-btn-md layui-btn-warm" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20241109" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();
        layui.use(['laydate', 'form'], function () {
            pager.init()
        });
    </script>
    <script>

        var paramNo = parent.pager.ownerNoList;

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            model: null,
            rules: [],
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            if (d.CarCardType_Category != "3658" && d.CarCardType_Type!=6) {
                                var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                                $("#Owner_CardTypeNo").append(option)
                            }
                        });
                    }
                }, "json");

                layui.form.render("select");
            },
            //数据绑定
            bindData: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    
                    var carcardtype = $("#Owner_CardTypeNo").val();
                    var carcardtypename = $("#Owner_CardTypeNo").find("option:selected").text();
                    if(carcardtype == ""){
                        layer.msg("请选择需要修改的车牌类型", { icon: 0 });
                        return;
                    }

                    var checkincar = $("#clearfee").is(":checked");

                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);
                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "即将修改车牌类型为：" + carcardtypename + "，" + (checkincar ? "已勾选【场内车辆不生效】，" : "未勾选【场内车辆不生效】，") + "<t style='color:red;'>修改后，不可恢复</t>，确定修改吗？",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Save").attr("disabled", true);
                            $.post("SaveSetCarCardType", { ownerNoList: paramNo, carcardtype: carcardtype, chkincar: checkincar }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 0, btn: ['确定'] }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () {  } });
                                    $("#Save").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); },
                        end: function () { $("#Save").removeAttr("disabled"); }
                    })
                });
            }
        };

    </script>
</body>
</html>
