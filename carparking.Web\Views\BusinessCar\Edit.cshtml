﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>新增/编辑商家车辆</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-card { box-shadow: none; }
        a:hover { text-decoration: none; }
        .layui-row { margin-bottom: 10px; }
        .edit-label { padding-left: 10px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="layui-card layui-form">
        <div class="layui-card-body" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">开始时间</label></div>
                <div class="layui-col-xs8">
                    <input class="layui-input v-null v-datetime v-submit" id="BusinessCar_StartTime" name="BusinessCar_StartTime" value="@Html.Raw(DateTime.Now.AddMinutes(30).ToString("yyyy-MM-dd HH:mm:00"))" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">结束时间</label></div>
                <div class="layui-col-xs8">
                    <input class="layui-input v-null v-datetime v-submit" id="BusinessCar_EndTime" name="BusinessCar_EndTime" value="@Html.Raw(DateTime.Now.AddHours(1.5).ToString("yyyy-MM-dd HH:mm:00"))" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">车牌号</label></div>
                <div class="layui-col-xs8">


                    <div class="input-group">
                        <input class="layui-input v-null v-carno v-submit" id="BusinessCar_CarNo" name="BusinessCar_CarNo" maxlength="8" autocomplete="off" value="@ViewBag.CarPrefix" />
                        <span class="input-group-btn"><button class="layui-btn layui-btn-outline after" id="GetInCarTime">查询场内车</button></span>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">手机号</label></div>
                <div class="layui-col-xs8">
                    <input class="layui-input v-phone" id="BusinessCar_Phone" name="BusinessCar_Phone" value="" maxlength="11" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">备注</label></div>
                <div class="layui-col-xs8">
                    <input class="layui-input" id="BusinessCar_Remark" name="BusinessCar_Remark" value="" maxlength="255" />
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-xs3"><label>&nbsp;</label></div>
                <div class="layui-col-xs8">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>确定</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <blockquote class="layui-col-xs11 layui-elem-quote" style="font-size:13px;">
                1、商家车辆在有效期内进出免费；<br />
                2、商家车辆在有效期前进出场或在有效期后进出场（不与有效期范围产生时段交集的情况进出场），按停车订单的类型收费（车道默认类型）；<br />
                3、商家车有效期与停车时间交集的情况，只收商家车有效期外的停车时间段费用（按商家车过期策略指定的计费规则收费）。
            </blockquote>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.visible = true;
        myVerify.init();

        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("No");

        s_carno_picker.init("BusinessCar_CarNo", function (text, carno) {
            $("#BusinessCar_CarNo").val(carno.join(''))
        }, "web").bindkeyup();

        layui.use(['form', 'element'], function () {
            pager.init()
        });


        var pager = {
            incartime: "",
            isHand: false,
            model: null,
            passwayList: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["BusinessCar_StartTime", "BusinessCar_EndTime"], { type: "datetime", range: true });
            },
            bindData: function () {
                if (paramAct == "Update") {
                    layer.msg("正在读取数据...", { icon: 16, time: 0 });
                    $.post("/BusinessCar/GetBusinessCar", { BusinessCar_No: paramNo }, function (json) {
                        layer.closeAll();
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data, function (data) { });
                            $("#BusinessCar_StartTime").attr("disabled", true);
                        }
                    }, "json");
                }
            },
            bindEvent: function () {
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.BusinessCar_StartTime = $("#BusinessCar_StartTime").val();
                        data.BusinessCar_EndTime = $("#BusinessCar_EndTime").val();
                        data.BusinessCar_Phone = $("#BusinessCar_Phone").val();
                        data.BusinessCar_Remark = $("#BusinessCar_Remark").val();
                        return data;
                    });
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);

                    if (paramAct == "Add") {
                        $.post("/BusinessCar/AddBusinessCar", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                var msg = json.msg;
                                var isReg = json.data;
                                layer.msg(msg, { time: isReg ? 1500 : 2500, icon: 1 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.msg, { time: 1500, icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        }, "json");
                    } else if (paramAct == "Update") {
                        param.BusinessCar_No = paramNo;
                        $.post("/BusinessCar/UpdateBusinessCar", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                var msg = json.msg;
                                var isReg = json.data;
                                layer.msg(msg, { time: isReg ? 1500 : 2500, icon: 1 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.msg, { time: 1500, icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        }, "json");
                    }
                });

                $("#Cancel").click(function () { parent.layer.closeAll(); });

                $("#GetInCarTime").click(function () {

                    if ($("#BusinessCar_CarNo").val() == "") {
                        layer.msg("请输入车牌号", { icon: 0, time: 1500, end: function () { } });
                        return;
                    }

                    $("#GetInCarTime").attr("disabled", true);
                    layer.msg("查询中", { icon: 16, time: 0 });
                    $.getJSON("GetInCarTime", { carno: $("#BusinessCar_CarNo").val() }, function (json) {
                        if (json.success) {
                            if (json.data != "" && json.data != null) {
                                var layerFrmIndex = layer.confirm(json.msg, {
                                    title: "提示",
                                    icon: "3",
                                    btn: ['确定', '取消'] //按钮
                                }, function () {
                                    pager.incartime = json.data;
                                    $("#BusinessCar_StartTime").val(json.data);
                                    layer.close(layerFrmIndex);
                                }, function () { });
                            } else {
                                layer.msg(json.msg, { icon: 0, time: 2500, end: function () { } });
                            }
                        } else {
                            pager.incartime = "";
                            layer.msg(json.msg, { icon: 0, time: 1500, end: function () { } });
                        }
                        $("#GetInCarTime").removeAttr("disabled");
                    });
                    pager.isHand = true;
                });

            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    $("#GetInCarTime").removeClass("layui-hide")
                });
            }
        };
    </script>
</body>
</html>
