﻿@model Dictionary<string, string>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>收费小票</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .container {
            max-width: 800px;
            margin: auto;
        }

        .centered {
            text-align: center;
        }

        #title {
            font-size: larger
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-group centered">
            <label id="title">@Model["title"]</label>
        </div>

        <!-- 小票副标题设置 -->
        <div class="form-group centered">
            <label id="subtitle">@Model["subtitle"]</label>
        </div>

        <div class="form-group">
            <table>
                <tr>
                    <td style="text-align:right">车牌号：</td>
                    <td>@Model["CarNo"]</td>
                </tr>
                <tr>
                    <td style="text-align:right">车牌类型：</td>
                    <td>@Model["CarCardType"]</td>
                </tr>
                <tr>
                    <td style="text-align:right">入场时间：</td>
                    <td>@Model["InTime"]</td>
                </tr>
                @if (!string.IsNullOrEmpty(@Model["OutTime"]))
                {
                    <tr>
                        <td style="text-align:right">出场时间：</td>
                        <td>@Model["OutTime"]</td>
                    </tr>
                }
                <tr>
                    <td style="text-align:right">缴费金额：</td>
                    <td>@Model["PayedAmount"]元</td>
                </tr>
                <tr>
                    <td style="text-align:right">操作员：</td>
                    <td>@Model["Operator"]</td>
                </tr>
            </table>
        </div>

        <div class="form-group centered">
            <label id="endcontent">@Model["endcontent"]</label>
        </div>
    </div>
    <script>
        window.onload = function () {
            window.print();
        };

        var beforePrint = function () {
            console.log('beforePrint');
        };

        var afterPrint = function () {
            console.log('afterPrint');
            parent.window.CloseIndex(parent.window.frmPrintIndex)
        };


        if (window.matchMedia) {   //返回一个新的 MediaQueryList 对象，表示指定的媒体查询字符串解析后的结果。
            var mediaQueryList = window.matchMedia('print');
            mediaQueryList.addListener(function (mql) {
                if (mql.matches) {
                    beforePrint();
                } else {
                    afterPrint();
                }
            });
        }

        window.onbeforeprint = beforePrint;
        window.onafterprint = afterPrint;
    </script>
</body>
</html>