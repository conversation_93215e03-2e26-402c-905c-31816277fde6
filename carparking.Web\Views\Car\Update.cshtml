﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑车辆</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/

        .layui-disabled { color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        #Car_CarNo[disabled] { background-color: #eee !important; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">车牌类型</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <select data-placeholder="车牌类型" lay-verify="" lay-filter="Car_TypeNo" id="Car_TypeNo" name="Car_TypeNo" lay-search>
                        <option value="">车牌类型</option>
                    </select>
                    <script type="text/x-jquery-tmpl" id="Tmpl_CarType">
                        <option value="${PowerGroup_ID}">${PowerGroup_Name}</option>
                    </script>
                </div>
                <div class="layui-col-sm2 edit-label ">车牌颜色</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <select data-placeholder="车牌颜色" class="form-control chosen-input " id="Car_VehicleTypeNo" name="Car_VehicleTypeNo" lay-search>
                        <option value="">车牌颜色</option>
                    </select>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">车牌号码</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" disabled maxlength="10" id="Car_CarNo" name="Car_CarNo" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-sm2 edit-label ">行驶证号</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="30" id="Car_License" name="Car_License" />
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">车辆品牌</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Car_Model" name="Car_Model" maxlength="50" />
                </div>
                <div class="layui-col-sm2 edit-label ">车辆颜色</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Car_Colour" name="Car_Colour" maxlength="20" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">卡号</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Car_CardNo" name="Car_CardNo" maxlength="50" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label other">开始时间</div>
                <div class="layui-col-sm3 edit-ipt-ban other">
                    <input type="text" class="layui-input" disabled id="Car_BeginTime" name="Car_BeginTime" />
                </div>
                <div class="layui-col-sm2 edit-label other">结束时间</div>
                <div class="layui-col-sm3 edit-ipt-ban other">
                    <input type="text" class="layui-input" disabled id="Car_EndTime" name="Car_EndTime" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label"><label>白名单</label></div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <div class="btnCombox" id="Car_EnableOffline">
                        <ul>
                            <li data-value="1">启用</li>
                            <li data-value="0" class="select">禁用</li>
                        </ul>
                    </div>
                    <div class="label-desc" style="clear:both;">启用后车辆信息可下发到相机白名单。禁用后，车辆将从相机白名单中删除。</div>
                </div>
                <div class="layui-col-sm2 edit-label Car_Balance layui-hide">账户余额</div>
                <div class="layui-col-sm3 edit-ipt-ban Car_Balance  layui-hide">
                    <input type="text" class="layui-input v-float" id="Car_Balance" name="Car_Balance" maxlength="10" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label">备注</div>
                <div class="layui-col-sm8 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Car_Remark" name="Car_Remark" maxlength="250" />
                </div>
            </div>
            <div class="layui-row layui-hide">
                <div class="layui-col-sm2 edit-label ">车位号</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" disabled id="Owner_Space" name="Owner_Space" />
                </div>
                <div class="layui-col-sm2 edit-label ">车主名称</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" disabled id="Car_OwnerName" name="Car_OwnerName" />
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label">&nbsp;</div>
                <div class="layui-col-sm6 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplCarType">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplCarCardType">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function () {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");
            pager.init()
        });
        var PayFreeType = 0;

        var dt = new Date().Format("yyyy-MM-dd");

        s_carno_picker.init("Car_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "Car_CarNo") {
                $("#Car_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramCarNo = $.getUrlParam("Car_No");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var isOpen = true;

        var pager = {
            carParam: null,
            Car_Balance: 0,
            Car_Category: 0,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.getJSON("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            //排除商家车&访客车
                            if ("5,6".indexOf(item.CarCardType_Type) < 0) {
                                var option = '<option value="' + item.CarCardType_No + '" data-category="' + item.CarCardType_Category + '">' + item.CarCardType_Name + '</option>';
                                $("#Car_TypeNo").append(option);
                            }
                        });

                        //$("#Car_TypeNo").append($("#tmplCarCardType").tmpl(json.Data))
                    }
                });

                $.getJSON("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        $("#Car_VehicleTypeNo").append($("#tmplCarType").tmpl(json.data))
                    }
                });

                layform.render("select");
            },
            //数据绑定
            bindData: function () {
                $.getJSON("/Car/GetCarExt", { Car_No: paramCarNo }, function (json) {
                    if (json.Success) {
                        $("#verifyCheck").fillForm(json.Data, function (data) { });
                        if (json.Data && json.Data.CarCardType_Category == 1)
                            $("#Car_TypeNo").attr("disabled", "disabled");
                        if (json.Data && json.Data.CarCardType_Type == 2)
                            $(".Car_Balance").removeClass("layui-hide")
                        if (json.Data.CarCardType_Category == 3657) $(".other").addClass("hidden");
                        pager.Car_Balance = json.Data.Car_Balance;
                        pager.Car_Category = json.Data.Car_Category;



                        layform.render("select");

                        LoadDeviceConfig(json.Data);
                    }
                });

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.PayFreeType = PayFreeType;
                        data.Car_No = paramCarNo;
                        return data;
                    });

                    param.Car_CarNo = $("#Car_CarNo").val();
                    param.Car_License = $("#Car_License").val();
                    param.Car_Space = $("#Car_Space").val();
                    param.Car_Model = $("#Car_Model").val();
                    param.Car_Colour = $("#Car_Colour").val();
                    param.Car_Remark = $("#Car_Remark").val();
                    param.Car_CardNo = $("#Car_CardNo").val();
                    param.Car_CardNo = $("#Car_CardNo").val();
                    param.Car_Balance = $("#Car_Balance").val();

                    pager.carParam = param;

                    var current_category = $("#Car_TypeNo").find("option:selected").attr("data-category");
                    $("#Save").attr("disabled", true);
                    if (pager.Car_Category == 3657 && current_category != pager.Car_Category && pager.Car_Balance > 0) {
                        LAYER_OPEN_TYPE_0("该车辆余额 " + pager.Car_Balance + " 元，修改车牌类型会导致余额清零，确定吗?", res => {
                            LAYER_LOADING("处理中...");
                            $.getJSON("/Car/UpdateCar", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    if (json.data && json.data != null && json.data != "") {
                                        pager.OpenPayment(json.data, json.msg);
                                    } else {
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0 });
                                    }
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    } else {
                        $.getJSON("/Car/UpdateCar", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                if (json.data && json.data != null && json.data != "") {
                                    pager.OpenPayment(json.data, json.msg);
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { window.parent.pager.bindData(window.parent.pager.pageIndex); } });
                                }
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            },
            SaveCar: function () {
                $.getJSON("/Car/UpdateCar", { jsonModel: JSON.stringify(pager.carParam) }, function (json) {
                    if (json.success) {
                        layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                            window.parent.pager.bindData(window.parent.pager.pageIndex);
                        });
                    } else {
                        if (json.data && json.data != null && json.data != "") {
                            pager.OpenPayment(json.data, json.msg);
                        } else {
                            layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageIndex); } });
                        }
                        $("#Save").removeAttr("disabled");
                    }
                });
            },
            OpenPayment: function (ParkOrder_No, Msg) {
                layer.open({
                    type: 0,
                    title: "消息提示",
                    btn: ["去清缴费用", "取消"],
                    shade: 0,
                    content: Msg,
                    yes: function (res) {
                        layer.open({
                            title: "停车支付",
                            type: 2, id: 1,
                            area: getIframeArea(['95%', '95%']),
                            maxmin: true,
                            content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(ParkOrder_No) + "&callBack=2",
                            end: function () {

                            }
                        });
                    },
                    btn2: function (res) {

                    }
                });
            }
        };


    </script>
    <script>
        var config = {
            Car_EnableOffline: 0
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                //if (!onDisabledCom(idName)) { return; }
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName);
            });
        });

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {

        }
    </script>
</body>
</html>
