﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="~/Static/admin/layui/css/layui.css" media="all">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/Static/admin/style/admin.css" media="all">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <style>
        @@media screen and (max-width: 1920px) { }

        @@media screen and (max-width: 1440px) {
            html, body { font-size: 14px; }
        }
        @@media screen and (max-width: 640px) {
            .fastempty { display: none; }
        }

        ::-webkit-scrollbar { width: 0.5rem; height: 0.5rem; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 5px; }
        ::-webkit-scrollbar-thumb { border-radius: 5px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }

        .layui-fluid { padding: 5px 15px; }

        .chartbox { height: 26rem; background-color: #fff; border-radius: 0.5rem; padding: 0 0.5rem; }
        .trfchartbox { height: 23rem; background-color: #fff; border-radius: 0.5rem; padding: 0 0.5rem; }

        .counts { min-height: 2rem; }
        .counts div { overflow: hidden; padding: 0 1rem 0 0; color: #fff; max-width: 30rem; margin-bottom: 0.5rem; display: inline-block; }
        .counts div span { border-radius: 1rem; display: block; padding: 0 1rem; }
        .counts ht { height: 3rem; line-height: 3rem; display: block; }
        .counts ct { height: 4rem; line-height: 4rem; display: block; font-size: 2.5rem; }
        .counts st { height: 3rem; line-height: 1rem; display: block; }

        .counts div:nth-child(1) span { background-color: rgb(134,184,245); }
        .counts div:nth-child(2) span { background-color: rgb(255,182,151); }
        .counts div:nth-child(3) span { background-color: rgb(118,215,199); }
        .counts div:nth-child(4) span { background-color: rgb(255,162,162); }

        .fycount { min-height: 13rem; margin: 1rem 1rem 0 0; padding: 1rem 1rem 1rem 0; border-radius: 0.5rem; background-color: #fff; }
        .fycount .data { display: inline-block; min-width: 20rem; }
        .fycount .fy-hd { height: 2rem; line-height: 2rem; font-size: 1rem; color: #999; padding: 0 1rem; padding-bottom: 1rem; }
        .fycount .d-btns { text-align: center; color: rgb(0,150,136); }
        .fycount .d-btns li { height: 3rem; line-height: 3rem; font-size: 1rem; user-select: none; cursor: pointer; margin: 0 1rem; }
        .fycount .d-btns li:hover { text-decoration: underline; }
        .fycount .d-btns li.active { background-color: rgb(0,150,136); color: #fff; position: relative; }
        .fycount .d-btns li.active .arrowRight { position: absolute; right: -1.5rem; top: 0; border-top: 1.5rem solid #fff; border-left: 1.5rem solid rgb(0,150,136); border-right: 1.5rem solid #fff; border-bottom: 1.5rem solid #fff; }
        .fycount .item span { display: block; text-align: center; }
        .fycount .item span ht { display: block; height: 3rem; font-size: 1rem; color: #999; }
        .fycount .item span ct { font-size: 5rem; font-weight: bold; line-height: 5rem; color: rgb(0,150,136); text-align: center; font-family: FangSong; }
        .fycount .item span t { font-size: 1rem; padding: 0; font-family: Arial; }
        .fycount .item:hover { user-select: none; cursor: pointer; }
        .fycount .item:hover .num { color: rgb(0,180,136); }

        .fastmode { background-color: #fff; border-radius: 0.5rem; margin: 1rem 1rem 0 0; height: 23rem; }
        .fastmode ul { overflow: auto; }
        .fastmode li { padding: .5rem; text-align: center; display: inline-block; }
        .fastmode li:hover { background-color: rgba(0,0,0,0.05); color: #000; }
        .fastmode li a { line-height: 4rem; display: block; }
        .fastmode li a img { height: 3rem; }
        .fastmode li cite { height: 2rem; line-height: 2rem; text-align: center; display: block; color: #666; }

        .sysmsg { margin: 1rem 0 0 0; border-radius: .5rem; }
        .sysmsg ul { height: 14rem; overflow: auto; }
        .sysmsg ul li { padding-bottom: .3rem; text-align: justify; word-break: break-all; word-wrap: break-word; }
        .sysmsg ul li:hover { font-weight: bold; cursor: default; }
        .sysmsg ul li:last-child { padding: 0; }
        #SyswarnMore:hover { cursor: pointer; text-decoration: underline; font-weight: bold; }
        #SyswarnMore::after { content: ">>"; font-family: FangSong; padding: 0 1rem; position: absolute; right: 0; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row" style="margin-top: 10px;">
            <div class="layui-col-md8">
                <div class="layui-row counts">
                    <div class="layui-col-md3">
                        <span id="OrderNumber">
                            <ht>场内车辆</ht>
                            <ct class="num1">0</ct>
                            <st>剩余车位数：<t class="num2">0</t>个</st>
                        </span>
                    </div>
                    <div class="layui-col-md3">
                        <span id="PayNumber">
                            <ht>今日收费</ht>
                            <ct class="num1">0</ct>
                            <st>昨日收费：<t class="num2">0</t>元</st>
                        </span>
                    </div>
                    <div class="layui-col-md3">
                        <span id="CarNumber">
                            <ht>固定车数量</ht>
                            <ct class="num1">0</ct>
                            <st>本月新增 ：<t class="num2">0</t>辆</st>
                        </span>
                    </div>
                    <div class="layui-col-md3">
                        <span id="DeviceNumber">
                            <ht>在线设备数</ht>
                            <ct class="num1">0</ct>
                            <st>离线设备 ：<t class="num2">0</t>台</st>
                        </span>
                    </div>
                </div>
                <div class="layui-row fycount">
                    <div class="layui-col-md12">
                        <div class="fy-hd">重点信息</div>
                    </div>
                    <div class="layui-col-md3 data">
                        <ul class="d-btns" id="ctype">
                            <li data-day="7">7天内<div class="arrowRight"></div></li>
                            <li data-day="0" class="active">今天<div class="arrowRight"></div></li>
                            <li data-day="15">15天内<div class="arrowRight"></div></li>
                        </ul>
                    </div>
                    <div class="item layui-col-md3 data" data-key="">
                        <span id="ZDNumber">
                            <ht>重点车辆</ht>
                            <ct><d class="num">0</d><t>辆</t></ct>
                        </span>
                    </div>
                    <div class="layui-col-md6" id="zdlist">
                    </div>
                </div>
            </div>
            <div class="layui-col-md4 chartbox">
                <div id="echart_cc" style="width: auto; height:100%;padding:10px 0;"></div>
            </div>
        </div>
        <div class="layui-row topmode">
            <div class="layui-col-md8">
                <div class="layui-card fastmode">
                    <div class="layui-card-header">快捷方式</div>
                    <div class="layui-card-body">
                        <ul>
                            @{
                                if (carparking.Config.AppSettingConfig.SentryMode != "2")
                                {
                                    <li class="layui-col-sm2">
                                        <a lay-href="FastGuide/Index">
                                            <img src="~/Static/img/home/<USER>" />
                                            <cite><t>快速配置</t></cite>
                                        </a>
                                    </li>
                                }
                            }

                            <li class="layui-col-md2">
                                <a lay-href="Policy/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>车场设置</t></cite>
                                </a>
                            </li>
                            <li class="layui-col-md2">
                                <a lay-href="Passway/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>车道管理</t></cite>
                                </a>
                            </li>
                            <li class="layui-col-md2">
                                <a lay-href="Device/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>设备管理</t></cite>
                                </a>
                            </li>
                            <li class="layui-col-md2">
                                <a lay-href="BillingRule/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>计费规则</t></cite>
                                </a>
                            </li>
                            <li class="layui-col-md2">
                                <a lay-href="PayOrder/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>缴费记录</t></cite>
                                </a>
                            </li>
                            <li class="layui-col-md2">
                                <a lay-href="Owner/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>车辆登记</t></cite>
                                </a>
                            </li>
                            <li class="layui-col-md2">
                                <a lay-href="InParkRecord/Index">
                                    <img src="~/Static/img/home/<USER>" />
                                    <cite><t>出入场记录</t></cite>
                                </a>
                            </li>
                            @{
                                if (carparking.Config.AppSettingConfig.SentryMode != "2")
                                {
                                    <li class="layui-col-md2">
                                        <a lay-href="CouponRecord/Index">
                                            <img src="~/Static/img/home/<USER>" />
                                            <cite><t>优惠券记录</t></cite>
                                        </a>
                                    </li>
                                    <li class="layui-col-md2">
                                        <a lay-href="WorkShift/Index">
                                            <img src="~/Static/img/home/<USER>" />
                                            <cite><t>交班记录</t></cite>
                                        </a>
                                    </li>
                                }
                            }
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4 trfchartbox">
                <div id="echart_trf" style="width: auto; height:100%;padding:10px 0;"></div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="zdlisttemp">
        <div class="item layui-col-md6" data-key="${key}">
            <span>
                <ht>${city}</ht>
                <ct><d class="num">${count}</d><t>辆</t></ct>
            </span>
        </div>
    </script>
    <script type="text/x-jquery-tmpl" id="syswarntemp">
        <li>
            {{if Syswarn_Level==0 }}
            <span class="layui-badge layui-bg-green">${Syswarn_Level_Text}</span>
            {{else Syswarn_Level==10}}
            <span class="layui-badge layui-bg-orange">${Syswarn_Level_Text}</span>
            {{else Syswarn_Level==100}}
            <span class="layui-badge layui-bg-purple">${Syswarn_Level_Text}</span>
            {{else Syswarn_Level==1000}}
            <span class="layui-badge layui-bg-red">${Syswarn_Level_Text}</span>
            {{/if}}
            <span>[${Syswarn_Time}]</span>
            <span>[${Syswarn_HostName}]${Syswarn_Content}</span>
        </li>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=0" asp-append-version="true"></script>
    <script src="~/Static/js/echarts.min.js" asp-append-version="true"></script>
    <script>
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index'], function () {
            pager.init();

        });

        var mychart;
        var showCahrt = function (data) {
            if (data == null)
                data = { curWeekData: [0, 0, 0, 0, 0, 0, 0], yesWeekData: [0, 0, 0, 0, 0, 0, 0] };

            if (mychart != null && mychart != "" && mychart != undefined) {
                mychart.dispose();
            }
            mychart = echarts.init(document.getElementById('echart_cc'));
            var option = createOption(data);
            mychart.setOption(option, true);
            window.onresize = function () {
                mychart.resize();
            };
        }

        var mytrfchart;
        var showtrfCahrt = function (data) {
            if (data != null) {
                if (mytrfchart != null && mytrfchart != "" && mytrfchart != undefined) {
                    mytrfchart.dispose();
                }
                mytrfchart = echarts.init(document.getElementById('echart_trf'));
                var trfoption = createtrfOption(data);
                mytrfchart.setOption(trfoption, true);
                window.onresize = function () {
                    mytrfchart.resize();
                };
            }
        }

    </script>

    <script>
        var OnSiteVehicles = '@ViewBag.OnSiteVehicles';
        var TodayCharge = '@ViewBag.TodayCharge';
        var FixedNumberVehicles = '@ViewBag.FixedNumberVehicles';
        var OnlineDevices = '@ViewBag.OnlineDevices';
        var code = '@ViewBag.Code';

        var pager = {
            init: function () {
                //$.ajaxSettings.async = false;
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                //$.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            bindData: function () {
                this.getOrderNumber();
                this.getPayNumber();
                this.getCarNumber();
                this.getDeviceNumber();
                this.getFyInNumber(0);
                this.getPayReport();
                this.getTrfReport();
                //this.getSyswarnRecord();
            },
            bindEvent: function () {
                $("#ctype li").click(function () {
                    $("#ctype li").removeClass("active");
                    $(this).addClass("active");

                    var day = $(this).attr("data-day");
                    pager.getFyInNumber(day);
                });

                $("#SyswarnMore").click(function () {
                    window.parent.global.gotoPage('Syswarn/Index');
                });
            },
            getOrderNumber: function () {
                if (OnSiteVehicles == 'True') {
                    $.getJSON("OrderNumber", { code: code }, function (json) {
                        if (json.success) {
                            $("#OrderNumber .num1").text(json.data.num1);
                            $("#OrderNumber .num2").text(json.data.num2);
                        }
                    });
                } else {
                    $("#OrderNumber .num1").text('*');
                    $("#OrderNumber .num2").text('*');
                }
            },
            getPayNumber: function () {
                if (TodayCharge == 'True') {
                    $.getJSON("PayNumber", { code: code }, function (json) {
                        if (json.success) {
                            $("#PayNumber .num1").text(json.data.num1);
                            $("#PayNumber .num2").text(json.data.num2);
                        }
                    });
                } else {
                    $("#PayNumber .num1").text('*');
                    $("#PayNumber .num2").text('*');
                }
            },
            getCarNumber: function () {
                if (FixedNumberVehicles == 'True') {
                    $.getJSON("CarNumber", { code: code }, function (json) {
                        if (json.success) {
                            $("#CarNumber .num1").text(json.data.num1);
                            $("#CarNumber .num2").text(json.data.num2);
                        }
                    });
                } else {
                    $("#CarNumber .num1").text('*');
                    $("#CarNumber .num2").text('*');
                }
            },
            getDeviceNumber: function () {
                if (OnlineDevices == 'True') {
                    $.getJSON("DeviceNumber", { code: code }, function (json) {
                        if (json.success) {
                            $("#DeviceNumber .num1").text(json.data.num1);
                            $("#DeviceNumber .num2").text(json.data.num2);
                        }
                    });
                } else {
                    $("#DeviceNumber .num1").text('*');
                    $("#DeviceNumber .num2").text('*');
                }
            },
            getFyInNumber: function (day) {
                $.getJSON("FyInNumber", { day: day, code: code }, function (json) {
                    if (json.success) {
                        $("#ZDNumber .num").text(json.data.num);
                        var data = [];
                        if (json.data.data.length > 2) {
                            data[0] = json.data.data[0];
                            data[1] = json.data.data[1];
                        } else {
                            data = json.data.data;
                        }

                        $("#zdlist").html($("#zdlisttemp").tmpl(data));

                        $(".fycount .item").unbind("click").on("click", function () {
                            var key = $(this).attr("data-key");
                            var start = _DATE.getSpan(new Date(), { date: (day != 0 ? (1 - day) : 0) }, 'd') + " 00:00:00";
                            var end = new Date().Format("yyyy-MM-dd 23:59:59");

                            var gotod = {
                                ParkOrder_CarNo: key
                                , ParkOrder_IsEpCar: 1
                                , ParkOrder_StatusNo: 200
                                , ParkOrder_EnterTime0: start
                                , ParkOrder_EnterTime1: end
                            };
                            localStorage.setItem("gotoInParkRecord", JSON.stringify(gotod));
                            window.parent.global.gotoPage('InParkRecord/Index');
                        })
                    }
                });
            },
            getPayReport: function () {
                $.getJSON("PayReport", { code: code }, function (json) {
                    if (json.success) {
                        showCahrt(json.data);
                    }
                });
            },
            getTrfReport: function () {
                $.getJSON("TrafficAnalysis", { type: 0, code: code }, function (json) {
                    if (json.success) {
                        showtrfCahrt(json.data);
                    }
                });
            },
        }

        var createOption = function (data) {
            var data1 = data.curWeekData;
            var data2 = data.yesWeekData;
            var option = {
                title: { text: '收费数据', subtext: "单位:元" },
                tooltip: { trigger: 'axis' },
                legend: { data: ['本周', '上周'] },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    splitLine: { show: false }
                },
                yAxis: {
                    type: 'value',
                    splitLine: { show: false }
                },
                series: [
                    {
                        name: '本周',
                        data: data1,
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: 'rgb(56, 172, 161)' },
                        lineStyle: { color: 'rgb(56, 172, 161)' },
                        areaStyle: {
                            opacity: 0.8,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgb(56, 172, 161)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgb(255, 255, 255)'
                                }
                            ])
                        }
                    },
                    {
                        name: '上周',
                        data: data2,
                        type: 'line',
                        smooth: true,
                        itemStyle: { color: 'rgb(126, 154, 242)' },
                        lineStyle: { color: 'rgb(126, 154, 242)' },
                        areaStyle: {
                            opacity: 0.8,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: 'rgb(126, 154, 242)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgb(255, 255, 255)'
                                }
                            ])
                        }
                    }
                ]
            };

            return option;
        }

        var createtrfOption = function (data) {
            var legends = ["入车流量", "出车流量"];
            var xdata = data.xData;
            var seriesData = [];

            var series = {
                name: "入车流量", type: "line", smooth: true,
                label: { show: true, position: 'top' },
                barMaxWidth: 30,
                barMinWidth: 20,
                data: data.yDataCarIn
            };
            seriesData.push(series);

            var series2 = {
                name: "出车流量", type: "line", smooth: true,
                label: { show: true, position: 'top' },
                barMaxWidth: 30,
                barMinWidth: 20,
                data: data.yDataCarOut
            };
            seriesData.push(series2);

            var option = {
                title: { text: "今日车流量" },
                legend: { show: true, data: legends, top: 40 },
                tooltip: { trigger: "axis" },
                grid: { show: false, top: 100 },
                toolbox: {
                    show: true,
                    orient: 'vertical',
                    y: 'center',
                },
                xAxis: [{
                    type: "category", boundaryGap: false, splitLine: { show: false }, boundaryGap: ['50', '50'],
                    data: xdata
                }],
                yAxis: [{ type: "value", splitLine: { show: true, lineStyle: { color: "#f2f2f2" } } }],
                series: seriesData
            }
            return option;
        };

        //数字跳动效果
        var numHand = {
            roll: function (total, idname, step) {
                let n = 0;
                return function () {
                    n = (n + step) >= total ? total : (n + step);
                    if (n <= total) {
                        document.getElementById(idname).innerHTML = n;
                    }
                }
            },
            //total 最终显示的数字，idname 标签id，step 跳动的速度，runtime 跳动的总时长
            start: function (total, idname, step, runtime = 1000) {
                let rolling = numHand.roll(total, idname, step)
                runtime = (runtime >= 300) ? runtime : 1000;
                for (let i = 0; i < (total / step); i++) {
                    let timer = setTimeout(rolling, (runtime / total) * i * step)
                }
                //clearTimeout(timer);
            }
        }
    </script>
</body>
</html>