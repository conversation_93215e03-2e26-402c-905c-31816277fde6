﻿﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        /* layui 时间选择器 不要秒的选项 */
        .layui-laydate-content > .layui-laydate-list { padding-bottom: 0px; overflow: hidden; }

        .layui-laydate-main > .layui-laydate-content > .layui-laydate-list > li { width: 50% }

        .chktime div { float: left; width: 5rem; margin-bottom: 0.1rem; }

        .layui-table td, .layui-table th { border: 0; padding: 0; }

        .layui-table tr { height: 45px; line-height: 45px; }

        .layui-table td .layui-input { height: 32px; line-height: 32px; width: calc(50% - 30px); float: left; border: 1px solid #D7D8D9; border-radius: 0px; }

        .input-group-addon { width: 30px; float: left; height: 30px; line-height: 32px; padding: 0; border: 1px solid #D7D8D9; border-radius: 0px; }

        .layui-btn-sm { border-radius: 0px; }

        span.ss { font-size: 12px; text-align: justify; word-break: break-all; color: #888; background-color: lemonchiffon; width: calc(100% - 10px); float: left; padding: 3px 5px; }


        input[readonly] { background-color: #fff !important; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row" id="CarCardTypeDiv">
                <div class="layui-col-xs3 edit-label ">授权对象</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select data-placeholder="车牌类型" id="AccessAuth_CarCardTypeNo" name="AccessAuth_CarCardTypeNo" class="layui-input v-null" lay-search>
                        @*<option value="">请选择</option>*@
                    </select>
                    <script type="text/x-jquery-tmpl" id="tmplCarCardType">
                        <option value="${CarCardType_No}">${CarCardType_Name}</option>
                    </script>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row" id="CarTypeNoDiv">
                <div class="layui-col-xs3 edit-label ">授权对象</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select data-placeholder="车牌颜色" id="AccessAuth_CarTypeNo" name="AccessAuth_CarTypeNo" class="layui-input v-null" lay-search>
                        @*<option value="">请选择</option>*@
                    </select>
                    <script type="text/x-jquery-tmpl" id="tmplCarType">
                        <option value="${CarType_No}">${CarType_Name}</option>
                    </script>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">授权通道</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div id="AccessAuth_PasswayNo" class="v-null"></div>
                    <div class="label-desc">需要在[车场管理>车场设置>车道设置]下开启车道通行控制</div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">授权权限</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select id="AccessAuth_IsAccess" name="AccessAuth_IsAccess" class="layui-input v-null" lay-search>
                        <option value="0">禁止通行</option>
                        <option value="1">允许通行</option>
                        <option value="4">弹框确认</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">授权时间范围</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select data-placeholder="授权时间范围" id="AccessAuth_DateType" name="AccessAuth_DateType" class="layui-input v-null" lay-search>
                        <option value="1">每天</option>
                        <option value="2">每周</option>
                        <option value="3">自定义日期范围</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div id="DayContentDiv">
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <table class="layui-table layui-form sorttable">
                            <tbody id="Day_Time_Limit">
                                <tr>
                                    <td>
                                        <input type="text" value="00:00" class="layui-input inputtiem v-null" readonly>
                                        <span class="input-group-addon">至</span>
                                        <input type="text" value="23:59" class="layui-input inputtiem v-null" readonly>
                                    </td>
                                    <td>
                                        <button class="layui-btn layui-btn-sm" id="OnTimeLimitDay">
                                            <t>新增时段</t>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <script type="text/x-jquery-tmpl" id="Day_Time_Limit_Tmp">
                            <tr>
                                <td>
                                    <input type="text" value="${start}" class="layui-input inputtiem v-null" readonly>
                                    <span class="input-group-addon">至</span>
                                    <input type="text" value="${end}" class="layui-input inputtiem v-null" readonly>
                                </td>

                                <td>
                                    {{if no==0}}
                                    <button class="layui-btn layui-btn-sm" id="OnTimeLimitDay"><t>新增时段</t></button>
                                    {{else no !=0}}
                                    <button class="layui-btn layui-btn-sm" onclick="DelTimeLimit(this)"><t>删除时段</t></button>
                                    {{/if}}
                                </td>
                            </tr>
                        </script>
                    </div>
                </div>
            </div>

            <div style="display:none" id="WeekContentDiv">
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban layui-form chktime">
                        <div><input type="checkbox" class="layui-form-checkbox " title="周一" lay-skin="primary" id="Monday" name="Monday" lay-filter="Monday"></div>
                        <div><input type="checkbox" class="layui-form-checkbox " title="周二" lay-skin="primary" id="Tuesday" name="Tuesday" lay-filter="Tuesday"></div>
                        <div><input type="checkbox" class="layui-form-checkbox " title="周三" lay-skin="primary" id="Wednesday" name="Wednesday" lay-filter="Wednesday"></div>
                        <div><input type="checkbox" class="layui-form-checkbox " title="周四" lay-skin="primary" id="Thursday" name="Thursday" lay-filter="Thursday"></div>
                        <div><input type="checkbox" class="layui-form-checkbox " title="周五" lay-skin="primary" id="Friday" name="Friday" lay-filter="Friday"></div>
                        <div><input type="checkbox" class="layui-form-checkbox " title="周六" lay-skin="primary" id="Saturday" name="Saturday" lay-filter="Saturday"></div>
                        <div><input type="checkbox" class="layui-form-checkbox " title="周日" lay-skin="primary" id="Sunday" name="Sunday" lay-filter="Sunday"></div>
                    </div>
                    <label class="focus valid"></label>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <table class="layui-table layui-form sorttable">
                            <tbody id="Week_Time_Limit">
                                <tr>
                                    <td>
                                        <input type="text" value="00:00" class="layui-input inputweek v-null" readonly>
                                        <span class="input-group-addon">至</span>
                                        <input type="text" value="23:59" class="layui-input inputweek v-null" readonly>
                                    </td>
                                    <td>
                                        <button class="layui-btn layui-btn-sm" id="OnTimeLimitWeek">
                                            <t>新增时段</t>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <script type="text/x-jquery-tmpl" id="Week_Time_Limit_Tmp">
                            <tr>
                                <td>
                                    <input type="text" value="${start}" class="layui-input inputweek v-null" readonly>
                                    <span class="input-group-addon">至</span>
                                    <input type="text" value="${end}" class="layui-input inputweek v-null readonly>
                                </td>
                                <td>
                                    {{if no==0}}
                                    <button class="layui-btn layui-btn-sm" id="OnTimeLimitWeek"><t>新增时段</t></button>
                                    {{else no !=0}}
                                    <button class="layui-btn layui-btn-sm" onclick="DelTimeLimit(this)"><t>删除时段</t></button>
                                    {{/if}}
                                </td>
                            </tr>
                        </script>
                    </div>
                </div>
            </div>

            <div style="display:none" id="DataContentDiv">
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <table class="layui-table layui-form sorttable">
                            <tbody id="Date_Time_Limit">
                                <tr>
                                    <td>
                                        <input type="text" value="" class="layui-input inputdate v-null" readonly>
                                        <span class="input-group-addon">至</span>
                                        <input type="text" value="" class="layui-input inputdate v-null" readonly>
                                    </td>
                                    <td>
                                        <button class="layui-btn layui-btn-sm" id="OnTimeLimitDate">
                                            <t>新增时段</t>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <script type="text/x-jquery-tmpl" id="Date_Time_Limit_Tmp">
                            <tr>
                                <td>
                                    <input type="text" value="${start}" class="layui-input inputdate v-null" readonly>
                                    <span class="input-group-addon">至</span>
                                    <input type="text" value="${end}" class="layui-input inputdate v-null" readonly>
                                </td>
                                <td>
                                    {{if no==0}}
                                    <button class="layui-btn layui-btn-sm" id="OnTimeLimitDate"><t>新增时段</t></button>
                                    {{else no !=0}}
                                    <button class="layui-btn layui-btn-sm" onclick="DelTimeLimit(this)"><t>删除时段</t></button>
                                    {{/if}}
                                </td>
                            </tr>
                        </script>
                    </div>
                </div>
            </div>
        </div>
        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>

        <div class="layui-row cartype">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <span class="ss"><b>温馨提示：</b>当前通行控制，只限制临时车，不限制固定车.</span>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?v=1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>

    <script>
        var AccessAuth_PasswayNo;
        //加载组件
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: 'layui/lay/modules/xm-select'
        }).use(['xmSelect'], function () {
            var xmSelect = layui.xmSelect;

            $.getJSON("/DropList/GetPassway", {}, function (json) {
                if (json.Success) {
                    var data = [];
                    for (var i = 0; i < json.Data.length; i++) {
                        data[i] = {
                            "name": json.Data[i].Passway_Name,
                            "value": json.Data[i].Passway_No
                        };
                    }
                    AccessAuth_PasswayNo = xmSelect.render({
                        el: '#AccessAuth_PasswayNo',
                        name: 'AccessAuth_PasswayNo',
                        layVerify: 'required',
                        layVerType: 'msg',
                        toolbar: { show: true, icon: false },
                        filterable: true,
                        paging: true,
                        pageSize: 10,
                        data: data
                    })
                }
            });
        })
    </script>
    <script>
        $(function () {

            if (paramType == 2) {
                $("#CarCardTypeDiv").hide();
                $("#CarTypeNoDiv").show();
                $(".cartype").show();
            } else if (paramType == 1) {
                $("#CarCardTypeDiv").show();
                $("#CarTypeNoDiv").hide();
                $(".cartype").hide();
            }
        });
    </script>

    <script>
        myVerify.init();
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use('index', function () {
            pager.init();
            var form = layui.form;

            layui.laydate.render({ elem: '.inputtiem', type: 'time', format: 'HH:mm' });
            layui.laydate.render({ elem: '.inputweek', type: 'time', format: 'HH:mm' });

            layui.laydate.render({ elem: '.inputdate', type: 'datetime', format: 'yyyy-MM-dd HH:mm' });
            if (paramAct != "Update") {
                var myDate = new Date();
                $(".inputdate").eq(0).val(new Date().Format("yyyy-MM-dd 00:00"));
                $(".inputdate").eq(1).val(new Date().Format("yyyy-MM-dd 23:59"));
            }

        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("AccessAuth_No");
        var paramType = $.getUrlParam("Type");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var inputtimeno = 0;
        var inputdateno = 0;
        var inputweekno = 0;

        var AccessAuth_DateType = 0;

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layui.form.on("select", function (data) {
                    if (data.elem.id == "AccessAuth_DateType") {
                        if (data.value == 1) {
                            $("#WeekContentDiv").hide();
                            $("#DayContentDiv").show();
                            $("#DataContentDiv").hide();
                        } else if (data.value == 2) {
                            $("#WeekContentDiv").show();
                            $("#DayContentDiv").hide();
                            $("#DataContentDiv").hide();
                        } else if (data.value == 3) {
                            $("#WeekContentDiv").hide();
                            $("#DayContentDiv").hide();
                            $("#DataContentDiv").show();
                        }
                    }
                });

                //$("#AccessAuth_DateType").change(function () {
                //    var datetype = $(this).val();
                //    if (datetype == 1) {
                //        $("#WeekContentDiv").hide();
                //        $("#DayContentDiv").show();
                //        $("#DataContentDiv").hide();
                //    }
                //    else if (datetype == 2) {
                //        $("#WeekContentDiv").show();
                //        $("#DayContentDiv").hide();
                //        $("#DataContentDiv").hide();
                //    }
                //    else if (datetype == 3) {
                //        $("#WeekContentDiv").hide();
                //        $("#DayContentDiv").hide();
                //        $("#DataContentDiv").show();
                //    }
                //});

                dw_com_select.init('/DropList/GetCarCardType', 'AccessAuth_CarCardTypeNo', {}, 'tmplCarCardType');
                dw_com_select.init('/DropList/GetCarType', 'AccessAuth_CarTypeNo', {}, 'tmplCarType');
                layui.form.render("select")
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("GetDetail", { AccessAuth_No: paramNo }, function (json) {
                        if (json.Success) {
                            $("#verifyCheck").fillForm(json.Data, function (data) { });

                            paramNo = json.Data.AccessAuth_No;
                            $("#AccessAuth_CarCardTypeNo").trigger("chosen:updated");
                            $("#AccessAuth_CarTypeNo").trigger("chosen:updated");
                            $("#AccessAuth_IsAccess").trigger("chosen:updated");
                            $("#AccessAuth_DateType").trigger("chosen:updated");
                            AccessAuth_DateType = json.Data.AccessAuth_Type;
                            if (json.Data.AccessAuth_Type == 1) {
                                $("#CarCardTypeDiv").show();
                                $("#CarTypeNoDiv").hide();
                            } else {
                                $("#CarCardTypeDiv").hide();
                                $("#CarTypeNoDiv").show();
                            }

                            if (json.Data.AccessAuth_DateType == 1) {
                                pager.setRules("#Day_Time_Limit", json.Data.AccessAuth_DayContent);
                                $("#DayContentDiv").show();
                                $("#WeekContentDiv").hide();
                                $("#DataContentDiv").hide();
                            }
                            else if (json.Data.AccessAuth_DateType == 2) {
                                var time = JSON.parse(json.Data.AccessAuth_WeekContent);
                                pager.setRules("#Week_Time_Limit", time[0].times);
                                $("#DayContentDiv").hide();
                                $("#WeekContentDiv").show();
                                $("#DataContentDiv").hide();
                                pager.bindweek(time[0].week);
                            }
                            else {
                                console.log(json.Data.AccessAuth_DayContent)
                                pager.setRules("#Date_Time_Limit", json.Data.AccessAuth_DayContent);
                                $("#DayContentDiv").hide();
                                $("#WeekContentDiv").hide();
                                $("#DataContentDiv").show();
                            }

                            setTimeout(
                                function () {
                                    AccessAuth_PasswayNo.setValue(JSON.parse(json.Data.AccessAuth_PasswayNo));
                                    layui.form.render();
                                }, 100
                            )
                        }
                    });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    if (!judegeall()) {
                        layer.msg("范围重叠或截止时间小于开始时间");
                        return;
                    };

                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {

                        data.AccessAuth_Type = paramType;
                        data.AccessAuth_PasswayNo = AccessAuth_PasswayNo.getValue('value');

                        if ($("#AccessAuth_DateType").val() == 1) {
                            data.AccessAuth_DayContent = pager.getRules("#Day_Time_Limit");
                            data.AccessAuth_WeekContent = "";
                        }
                        else if ($("#AccessAuth_DateType").val() == 2) {
                            var week = [];
                            if ($("#Monday").is(':checked')) week.push(1);
                            if ($("#Tuesday").is(':checked')) week.push(2);
                            if ($("#Wednesday").is(':checked')) week.push(3);
                            if ($("#Thursday").is(':checked')) week.push(4);
                            if ($("#Friday").is(':checked')) week.push(5);
                            if ($("#Saturday").is(':checked')) week.push(6);
                            if ($("#Sunday").is(':checked')) week.push(0);

                            var str = [{ week: JSON.stringify(week), times: pager.getRules("#Week_Time_Limit") }];
                            data.AccessAuth_WeekContent = JSON.stringify(str);
                            data.AccessAuth_DayContent = "";
                        }
                        else if ($("#AccessAuth_DateType").val() == 3) {
                            data.AccessAuth_DayContent = pager.getRules("#Date_Time_Limit");
                            data.AccessAuth_WeekContent = "";
                        }
                        return data;
                    });
                    if (param.AccessAuth_DateType == 2 && JSON.parse(param.AccessAuth_WeekContent)[0].week == null) {
                        layer.msg("请指定星期", { icon: 0 });
                        return false;
                    }

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.getJSON("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                if (json.Data == "1") {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        var confirmIndex = layer.confirm("是否开启车道的通行控制？", {
                                            icon: 1, title: '提示', btn: ['开启', '不开启'],
                                            cancel: function () {
                                                layer.msg("保存成功,未开启车道通行控制", { icon: 1, time: 1500 }, function () {
                                                    window.parent.pager.bindData(1);
                                                });
                                            }
                                        }, function (index) {
                                            $.getJSON("UpdatePolicyPassway", { PasswayNoArray: JSON.stringify(param.AccessAuth_PasswayNo) }, function (json1) {
                                                if (json1.Success) {
                                                    layer.msg(json1.Message, { icon: 1, time: 1500 }, function () {
                                                        layer.close(confirmIndex);
                                                        window.parent.pager.bindData(1);
                                                    });
                                                } else {
                                                    layer.msg(json1.Message, { icon: 0, time: 0, btn: ['确定'] });
                                                }
                                            });
                                        }, function () {
                                            layer.msg("保存成功,未开启车道通行控制", { icon: 1, time: 1500 }, function () {
                                                window.parent.pager.bindData(1);
                                            });
                                        });
                                    });
                                } else {
                                    window.parent.pager.bindData(1);
                                }
                            } else {
                                layer.msg(json.Message, { icon: 0, time: 0, btn: ['确定'] });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {
                        param.AccessAuth_No = paramNo;
                        $.getJSON("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                if (json.Data == "1") {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        var confirmIndex = layer.confirm("是否开启车道的通行控制？", {
                                            icon: 1, title: '提示', btn: ['开启', '不开启'], cancel: function () {
                                                layer.msg("保存成功,未开启车道通行控制", { icon: 1, time: 1500 }, function () {
                                                    window.parent.pager.bindData(1);
                                                });
                                            }
                                        }, function (index) {
                                            $.getJSON("UpdatePolicyPassway", { PasswayNoArray: JSON.stringify(param.AccessAuth_PasswayNo) }, function (json1) {
                                                if (json1.Success) {
                                                    layer.msg(json1.Message, { icon: 1, time: 1500 }, function () {
                                                        layer.close(confirmIndex);
                                                        window.parent.pager.bindData(1);
                                                    });
                                                } else {
                                                    layer.msg(json1.Message, { icon: 0, time: 0, btn: ['确定'] });
                                                }
                                            });
                                        }, function () {
                                            layer.msg("保存成功,未开启车道通行控制", { icon: 1, time: 1500 }, function () {
                                                window.parent.pager.bindData(1);
                                            });
                                        });
                                    });
                                } else {
                                    window.parent.pager.bindData(1);
                                }
                            } else {
                                layer.msg(json.Message, { icon: 0, time: 0, btn: ['确定'] });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });
                $("#OnTimeLimitWeek").click(function () {
                    var id = "#Week_Time_Limit";
                    var start = $(".inputweek").eq(0).val();
                    var end = $(".inputweek").eq(1).val();
                    if ($.trim(start) == '' || $.trim(end) == '') {
                        return;
                    }
                    start = $.trim(start);
                    end = $.trim(end);

                    if (start >= end) {
                        layer.msg("开始时间必须小于结束时间");
                        return;
                    }
                    var len = $(id).find("tr").length;
                    if (len >= 11) {
                        layer.msg("超过限制");
                        return;
                    }
                    var htm = pager.getTimeTrWeek("", "");
                    $(id).append(htm);
                    layui.use('form', function () { layui.form.render(); });

                    layui.laydate.render({
                        elem: '.inputweek_' + inputweekno, type: 'time', format: 'HH:mm'
                    });
                });
                $("#OnTimeLimitDay").click(function () {
                    var id = "#Day_Time_Limit";
                    var start = $(".inputtiem").eq(0).val();
                    var end = $(".inputtiem").eq(1).val();
                    if ($.trim(start) == '' || $.trim(end) == '') {
                        return;
                    }
                    start = $.trim(start);
                    end = $.trim(end);

                    if (start >= end) {
                        layer.msg("开始时间必须小于结束时间");
                        return;
                    }
                    var len = $(id).find("tr").length;
                    if (len >= 11) {
                        layer.msg("超过限制");
                        return;
                    }
                    var htm = pager.getTimeTrDay("", "");
                    $(id).append(htm);
                    layui.use('form', function () { layui.form.render(); });
                    layui.laydate.render({
                        elem: '.inputtiem_' + inputtimeno, type: 'time', format: 'HH:mm'
                    });
                });
                $("#OnTimeLimitDate").click(function () {
                    var id = "#Date_Time_Limit";
                    var start = $(".inputdate").eq(0).val();
                    var end = $(".inputdate").eq(1).val();

                    if ($.trim(start) == '' || $.trim(end) == '') {
                        return;
                    }
                    start = $.trim(start);
                    end = $.trim(end);

                    if (start >= end) {
                        layer.msg("开始时间必须小于结束时间");
                        return;
                    }
                    var len = $(id).find("tr").length;
                    if (len >= 11) {
                        layer.msg("超过限制");
                        return;
                    }
                    var htm = pager.getTimeTrDate("", "");
                    $(id).append(htm);
                    layui.use('form', function () { layui.form.render(); });
                    layui.laydate.render({
                        elem: '.inputdate_' + (inputdateno - 1), type: 'datetime', format: 'yyyy-MM-dd HH:mm'
                    });
                    layui.laydate.render({
                        elem: '.inputdate_' + inputdateno, type: 'datetime', format: 'yyyy-MM-dd HH:mm'
                    });
                });
            },
            addTimeTr: function (value, id) {
                var htm = pager.getTimeTr(value);
                $(id).append(htm);
            },
            getTimeTrWeek: function (start, end) {

                inputweekno++;

                var htm = '';
                htm += '<tr style="border-top: 1px solid #D7D8D9;">';
                htm += '<td><input type="text" value="' + start + '" class="layui-input inputweek_' + ++inputweekno + ' v-null" readonly>';
                htm += '<span class="input-group-addon">至</span>';
                htm += '<input type="text" value="' + end + '" class="layui-input inputweek_' + inputweekno + ' v-null" readonly></td>';

                htm += '<td><button class="layui-btn layui-btn-sm" onclick="DelTimeLimit(this)"><t>删除时段</t></button></td>';
                htm += '</tr > ';
                return htm;
            },
            getTimeTrDay: function (start, end) {

                var htm = '';
                htm += '<tr style="border-top: 1px solid #D7D8D9;">';
                htm += '<td><input type="text" value="' + start + '" class="layui-input inputtiem_' + ++inputtimeno + ' v-null" readonly>';
                htm += '<span class="input-group-addon">至</span>';
                htm += '<input type="text" value="' + end + '" class="layui-input inputtiem_' + inputtimeno + ' v-null" readonly></td>';

                htm += '<td><button class="layui-btn layui-btn-sm" onclick="DelTimeLimit(this)"><t>删除时段</t></button></td>';
                htm += '</tr > ';
                return htm;
            },
            getTimeTrDate: function (start, end) {

                var htm = '';
                htm += '<tr style="border-top: 1px solid #D7D8D9;">';
                htm += '<td><input type="text" value="' + start + '" class="layui-input inputdate_' + ++inputdateno + ' v-null" readonly>';
                htm += '<span class="input-group-addon">至</span>';
                htm += '<input type="text" value="' + end + '" class="layui-input inputdate_' + ++inputdateno + ' v-null" readonly></td>';

                htm += '<td><button class="layui-btn layui-btn-sm" onclick="DelTimeLimit(this)"><t>删除时段</t></button></td>';
                htm += '</tr > ';
                return htm;
            },
            getRules: function (id) {
                var str = [];
                $(id).find("tr").each(function (index, item) {
                    var vl = $(this).find("input");
                    var array = { start: $.trim(vl.eq(0).val()), end: $.trim(vl.eq(1).val()) };
                    str[index] = array;
                    pager.ruleData[pager.ruleData.length] = array;
                });
                return JSON.stringify(str);
            },
            setRules: function (id, data) {
                var data = JSON.parse(data);

                for (var i = 0; i < data.length; i++) {
                    data[i].no = i;
                }
                $(id).html($(id + "_Tmp").tmpl(data));
            },
            bindweek: function (data) {
                data = JSON.parse(data);

                if ($.inArray(1, data) != -1) {
                    $("#Monday").prop("checked", true);
                }
                if ($.inArray(2, data) != -1) {
                    $("#Tuesday").prop("checked", true);
                }
                if ($.inArray(3, data) != -1) {
                    $("#Wednesday").prop("checked", true);
                }
                if ($.inArray(4, data) != -1) {
                    $("#Thursday").prop("checked", true);
                }
                if ($.inArray(5, data) != -1) {
                    $("#Friday").prop("checked", true);
                }
                if ($.inArray(6, data) != -1) {
                    $("#Saturday").prop("checked", true);
                }
                if ($.inArray(0, data) != -1) {
                    $("#Sunday").prop("checked", true);
                }
                layui.form.render();
            },
            ruleData: []
        };
        var DelTimeLimit = function (e) {
            $(e).parent().parent().remove();
        }
    </script>
    <script>
        function judege(chkval, data) {
            for (let k in data) {
                if (data[k].start <= chkval.start && data[k].end >= chkval.start) {
                    return false
                }
                if (data[k].start > chkval.start && data[k].start <= chkval.end) {
                    return false
                }
            }
            return true;
        }
        function judegeall() {

            var data = [];
            if ($("#AccessAuth_DateType").val() == 1) {
                data = pager.getRules("#Day_Time_Limit");
            }
            else if ($("#AccessAuth_DateType").val() == 2) {
                data = pager.getRules("#Week_Time_Limit");
            }
            else if ($("#AccessAuth_DateType").val() == 3) {
                data = pager.getRules("#Date_Time_Limit");
            }
            data = JSON.parse(data);
            var chkval;
            for (let i in data) {
                if (data[i].start > data[i].end) return false;

                for (let k in data) {

                    if (i == k) { continue; }
                    chkval = data[i];

                    if (data[k].start <= chkval.start && data[k].end >= chkval.start) {
                        return false
                    }
                    if (data[k].start > chkval.start && data[k].start <= chkval.end) {
                        return false
                    }
                }
            }
            return true;
        }
    </script>
</body>
</html>
