﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-top: 15px; }
        .ParkDiscountSet_Type { display: none; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">车牌号</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-null" id="CouponRecord_IssueCarNo" name="CouponRecord_IssueCarNo" maxlength="32" value="" disabled />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">优惠方式</div>
                <div class="layui-col-xs7">
                    <div class="btnCombox" id="CouponRecord_CouponCode">
                        <ul>
                            <li data-value="101" class="select">优惠金额</li>
                            <li data-value="102">优惠时长</li>
                            <li data-value="103">优惠比例</li>
                            <li data-value="104">免费到指定时间</li>
                        </ul>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label ">优惠设置</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select data-placeholder="优惠设置" class="layui-input v-null" id="CouponRecord_ParkDiscountSetNo" name="CouponRecord_ParkDiscountSetNo" lay-search>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label">&nbsp;</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplParkDiscountSetNo">
        {{if ParkDiscountSet_Type == 101 }}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--优惠${ParkDiscountSet_Amount}元</option>
        {{else ParkDiscountSet_Type == 102 }}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--免费${ParkDiscountSet_Duration}分钟</option>
        {{else ParkDiscountSet_Type == 103 }}
    <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--优惠比例[${ParkDiscountSet_Ratio}折]</option>
        {{else ParkDiscountSet_Type == 104}}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--免费到指定时间:${ParkDiscountSet_AppointHour}</option>
        {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v1.0.01" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>

    <script>
        var index = parent.layer.getFrameIndex(window.name);
        myVerify.init();

        var layform = null;
        var laytable = null;
        var laydate = null;
        var comtable = null;
        var dt = new Date().Format("yyyy-MM-dd HH:mm:ss");

        layui.use(['table', 'form', 'laydate'], function () {
            layform = layui.form;
            laydate = layui.laydate;
            var table = layui.table;
            pager.init();
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramParkOrderCarNo = decodeURIComponent($.getUrlParam("ParkOrder_CarNo"));
        var paramParkOrderNo = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        $("#CouponRecord_IssueCarNo").val(paramParkOrderCarNo);
        var pager = {
            checkStaffData: [],
            ParkDiscountSet: null,
            passways: null,
            alllink: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;

            },
            bindSelect: function () {
                pager.bindParkDiscountSet();
            },
            //数据绑定
            bindData: function (index) {

            },
            bindEvent: function () {

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    if ($("#CouponRecord_ParkDiscountSetNo").val() == "") { layer.msg("请选择优惠设置"); return; }

                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.CouponRecord_ParkDiscountSetNo = $("#CouponRecord_ParkDiscountSetNo").val();
                        data.CouponRecord_CouponCode = config.CouponRecord_CouponCode;
                        data.CouponRecord_StartTime = $("#CouponRecord_StartTime").val();
                        data.CouponRecord_EndTime = $("#CouponRecord_EndTime").val();
                        data.CouponRecord_IssueCarNo = paramParkOrderCarNo;
                        data.CouponRecord_ParkOrderNo = paramParkOrderNo;
                        return data;
                    });
                    $("#Save").attr("disabled", true);
                    LAYER_OPEN_TYPE_0("确定该车牌优惠吗?", res => {
                        LAYER_LOADING("处理中...");
                        $.post("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            $("#Save").removeAttr("disabled")
                            if (json.success) {
                                $("#CouponRecord_StartTime").val("");
                                $("#CouponRecord_EndTime").val("");
                                $("#CouponRecord_IssueCarNo").val("");
                                layer.msg("车牌优惠成功", { time: 1000 }, function () {
                                    parent.pager.bindData();
                                })
                            } else {
                                layer.msg(json.msg);
                            }
                        }, "json");
                    }, res => {
                        $("#Save").removeAttr("disabled")
                    })
                });

                $("#Cancel").click(function () {
                    parent.layer.close(index); 
                })

                $(".btnCombox ul li").click(function () {
                    if ($(this).hasClass("select")) return;
                    var idName = $(this).parent().parent().attr("id");
                    $(this).siblings().removeClass("select");
                    $(this).addClass("select");
                    config[idName] = $(this).attr("data-value");

                    onEventCombox(idName);
                });

                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == 'ParkOrder_CarNo') {
                        $("#ParkOrder_CarNo").val(carno.join(""));
                    }
                }, "web").bindkeyup();
            },
            bindParkDiscountSet: function () {
                var list = [];
                $("#CouponRecord_ParkDiscountSetNo").html('<option value="">请选择优惠设置</option>')
                if (pager.ParkDiscountSet == null) {
                    $.post("/CarCoupon/GetAllParkDiscountSet", {}, function (json) {
                        if (json.Success) {
                            pager.ParkDiscountSet = json.Data;
                            $.each(pager.ParkDiscountSet, function (x, y) {
                                if (y.ParkDiscountSet_Type == config.CouponRecord_CouponCode)
                                    list.push(y);
                            })
                            $("#CouponRecord_ParkDiscountSetNo").append($("#tmplParkDiscountSetNo").tmpl(list))
                            layform.render("select");
                        }
                    }, 'json');
                } else {
                    $.each(pager.ParkDiscountSet, function (x, y) {
                        if (y.ParkDiscountSet_Type == config.CouponRecord_CouponCode)
                            list.push(y);
                    })
                    $("#CouponRecord_ParkDiscountSetNo").append($("#tmplParkDiscountSetNo").tmpl(list))
                    layform.render("select");
                }

            }
        };
    </script>
    <script type="text/javascript">
        //设备参数配置[仅选项按钮]默认值
        var config = {
            CouponRecord_CouponCode: 101,
            SentryHost_Category: 1,
            SentryHost_Net: 1,
        };

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == "CouponRecord_CouponCode") {
                pager.bindParkDiscountSet();
                if (config.CouponRecord_CouponCode == 104) {
                    $(".Time").addClass("layui-hide");
                } else {
                    $(".Time").removeClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
