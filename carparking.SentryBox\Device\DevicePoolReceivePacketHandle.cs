﻿using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Common;
using carparking.Interop.YS;
using carparking.Model;
using carparking.Model.API;
using carparking.Passthrough485;
using carparking.PassTool;
using carparking.SentryBox.BarrierDevice;
using carparking.SentryBox.Command;
using carparking.SentryBox.Util;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using TcpConnPools;
using TcpConnPools.Camera;
using TcpConnPools.Camera.Camera06;
using TcpConnPools.Camera.Camera15;
using TcpConnPools.Camera.CameraYS;
using TcpConnPools.ChannelMachine;
using TcpConnPools.ChannelMachine.Robot;
using TcpConnPools.Controller;
using TcpConnPools.Controller.BarrierY312;
using TcpConnPools.Controller.NonMotorized;
using TcpConnPools.QrCode;
using TcpConnPools.QrCode.QrCode01;
using TcpConnPools.SceneVideo;
using TcpConnPools.SceneVideo.SceneVideo01;
using TcpConnPools.Screen;
using ControlEvent = carparking.BLL.ControlEvent;
using ParkOrder = carparking.Model.ParkOrder;
using Passway = carparking.Model.Passway;

namespace carparking.SentryBox.Device;

/// <summary>
/// 关于TCP设备池接收数据前端处理类
/// </summary>
public class DevicePoolReceivePacketHandle
{
    /// <summary>
    /// 原子操作单例
    /// </summary>
    private static readonly Lazy<DevicePoolReceivePacketHandle> Lazy = new(() => new DevicePoolReceivePacketHandle());

    /// <summary>
    /// 处理方法集合
    /// </summary>
    private readonly Dictionary<string, MethodInfo> _handlers = new();

    /// <summary>
    /// 接收设备生成的订单记录
    /// </summary>
    private readonly ConcurrentDictionary<string, ResultPass> DeviceCreateOrder = new();

    /// <summary>
    /// 接收设备出场信息时间戳
    /// </summary>
    private readonly ConcurrentDictionary<string, DateTime> DeviceOutTimeSpane = new();

    /// <summary>
    /// 上一次的刷卡任务是否完成
    /// </summary>
    public readonly ConcurrentDictionary<string, Task> PassTask = new();

    /// <summary>
    /// 构造函数
    /// </summary>
    private DevicePoolReceivePacketHandle()
    {
        var methods = GetType().GetMethods(BindingFlags.Instance | BindingFlags.NonPublic);
        foreach (var method in methods)
        {
            //查找标记特性的方法
            var attribute = method.GetCustomAttribute<CommandAttribute>();
            if (attribute != null)
            {
                foreach (var command in attribute.Commands)
                {
                    if (!_handlers.ContainsKey(command.ToLower())) _handlers.Add(command.ToLower(), method);
                }
            }
        }
    }

    /// <summary>
    /// 自助通道机压现金等待响应集合
    /// </summary>
    private ConcurrentDictionary<string, int?> RmbMachinePressMoney { get; } = new();

    /// <summary>
    /// 单例
    /// </summary>
    public static DevicePoolReceivePacketHandle Instance => Lazy.Value;

    /// <summary>
    /// 处理数据
    /// </summary>
    /// <param name="device">设备</param>
    /// <param name="packet">数据包</param>
    public void Handle(IDevice device, IPacket packet)
    {
        HandleCamera(device, packet);
        RobotHandle(device, packet);
        BarrierHandle(device, packet);
        NonMotorizedHandle(device, packet);
        QRCodeDeviceHandle(device, packet);
        ScreenTcpHandle(device, packet);
    }

    /// <summary>
    /// 处理相机数据接收回调
    /// </summary>
    /// <param name="camera">相机设备</param>
    /// <param name="packet">上下文接收的数据</param>
    public void HandleCamera(IDevice camera, IPacket packet)
    {
        try
        {
            string jsonstr = null;
            JObject eventModel = null;

            // 处理Camera06的数据包
            if (camera is CameraOf06 && packet is CameraOf06Packet receivePacket06)
            {
                jsonstr = Encoding.UTF8.GetString(receivePacket06.Data);
                eventModel = JObject.Parse(jsonstr);
            }
            // 处理Camera15的数据包
            else if (camera is CameraOf15 && packet is CameraOf15Packet receivePacket15)
            {
                jsonstr = Encoding.UTF8.GetString(receivePacket15.Data);
                eventModel = JObject.Parse(jsonstr);
            }

            if (eventModel != null)
            {
                camera.Model.Type.WriteLog($"[{camera.Model.IPAddress}]相机事件：{eventModel}");

                if (eventModel.TryGetValue("cmd", out var cmdValue) && eventModel.TryGetValue("data", out var dataValue))
                {
                    var cmd = cmdValue.Value<string>();
                    if (cmd == "eventAlarm")
                    {
                        var eventAlarmModel = dataValue.ToObject<EventData>();

                        camera.Model.Type.WriteLog($"[{camera.Model.IPAddress}]处理事件类型：{eventAlarmModel.TypeNo}");

                        switch (eventAlarmModel.TypeNo)
                        {
                            case "004":
                                {
                                    camera.Model.Type.WriteLog($"[{camera.Model.IPAddress}]处理相机倒车事件");
                                    CameraEventAstern((CameraModel)camera.Model, eventAlarmModel);
                                }
                                break;

                            case "005":
                                {
                                    camera.Model.Type.WriteLog($"[{camera.Model.IPAddress}]处理相机跟车事件");
                                    CameraEventFollowing((CameraModel)camera.Model, eventAlarmModel);
                                }
                                break;

                            default:
                                {
                                    camera.Model.Type.WriteLog($"[{camera.Model.IPAddress}]处理相机异常事件，TypeNo：{eventAlarmModel.TypeNo}");
                                    CameraEventAbnormal((CameraModel)camera.Model, eventAlarmModel);
                                }
                                break;
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            camera.Model.Type.WriteLog($"[{camera.Model.IPAddress}]处理相机事件异常", ex);
        }
    }


    /// <summary>
    ///<see cref="DeviceType.T312Screen"/> 数据回调前端处理
    /// </summary>
    /// <param name="device">设备上下文对象</param>
    /// <param name="packet">接收到的数据包</param>
    private void ScreenTcpHandle(IDevice device, IPacket packet)
    {
        try
        {
            if (device is ScreenTcp robotClient && packet is ScreenTcpPacket receivePacket)
            {
                device.Model.Type.WriteLog($"接收设备[{robotClient.Model.IPAddress}]的数据" + JsonConvert.SerializeObject(receivePacket));

                var model = (ScreenModel)robotClient.Model;
                var deviceOwner = AppBasicCache.GetSentryDeviceLinking.Values.FirstOrDefault(m => m.Device_No == model.DeviceNo);
                if (deviceOwner != null)
                {
                    if (_handlers.TryGetValue(Convert.ToString(receivePacket.Command).ToLower(), out var method))
                    {
                        method.Invoke(this, new object[] { deviceOwner, receivePacket, model });
                    }
                    else
                    {
                        device.Model.Type.WriteLog($"未找到设备{model.DeviceNo},{Convert.ToString(receivePacket.Command)}对应的处理方法！");
                    }
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"显示屏{model}相关信息未找到！");
                }
            }
        }
        catch (Exception ex)
        {
            device.Model.Type.WriteLog("显示屏Tcp设备接收数据处理异常", ex);
        }
    }

    /// <summary>
    ///<see cref="DeviceType.RobotOf3288"/> 数据回调前端处理
    /// </summary>
    /// <param name="device">设备上下文对象</param>
    /// <param name="packet">接收到的数据包</param>
    private async void RobotHandle(IDevice device, IPacket packet)
    {
        try
        {
            if (device is not RobotClient robotClient || packet is not RobotReceivePacket receivePacket) return;

            device.Model.Type.WriteLog($"接收设备[{robotClient.Model.IPAddress}]的数据" + JsonConvert.SerializeObject(receivePacket));

            var model = (ChannelMachineModel)robotClient.Model;
            var deviceOwner = AppBasicCache.GetSentryDeviceLinking.Values.FirstOrDefault(m => m.Device_No == model.DeviceNo);
            if (deviceOwner != null)
            {
                var passwayNo = deviceOwner.Device_PasswayNo;
                var mainDevice = DeviceCommonUtil.GetPasswayMainDevice(passwayNo);
                AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayNo, out var passwayModel);
                if (passwayModel != null)
                {
                    if (mainDevice != null && !string.IsNullOrWhiteSpace(mainDevice.Device_IP)
                                           && !string.IsNullOrWhiteSpace(mainDevice.Device_Account) && !string.IsNullOrWhiteSpace(mainDevice.Device_Pwd))
                    {
                        if (_handlers.TryGetValue(Convert.ToString(receivePacket.type).ToLower(), out var method))
                        {
                            method.Invoke(this, new object[] { mainDevice, receivePacket, model, passwayModel });
                            foreach (var v in DeviceOutTimeSpane.Where(v => DateTime.Now.Subtract(v.Value).TotalSeconds > 10))
                            {
                                DeviceOutTimeSpane.TryRemove(v.Key, out _);
                            }
                        }
                        else
                        {
                            device.Model.Type.WriteLog($"未找到设备{model.DeviceNo}对应的处理方法！");
                        }
                    }
                    else
                    {
                        var model0 = new RobotSendPacket
                        {
                            type = RobotCommandType.setDeviceDisplayContent,
                            voiceType = (int)RobotVoiceType.ProhibitCurrent,
                            resultCode = 2,
                            msg = "未关联主设备",
                            data = new
                            {
                                voiceText = $"未关联主设备",
                                displayPageTimeout = 30,
                                messageText = $"未关联主设备",

                            }
                        };
                        await robotClient.SendDataAsync(model0);
                        LogManagementMap.WriteToFileException(null, $"[{model}][{passwayNo}]未关联主设备,禁止取票通行");
                    }
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, "未找到设备关联车道有效信息，请检查车道是否配置正确！");
                }
            }
            else
            {
                LogManagementMap.WriteToFileException(null, $"自助设备{model}相关信息未找到！");
            }
        }
        catch (Exception ex)
        {
            device.Model.Type.WriteLog("机器人设备接收数据处理异常", ex);
        }
    }

    /// <summary>
    /// <see cref="DeviceType.VehicleLaneControl"/> 数据回调前端处理
    /// </summary>
    /// <param name="device">设备上下文对象</param>
    /// <param name="packet">接收到的数据包</param>
    private void BarrierHandle(IDevice device, IPacket packet)
    {
        if (device is BarrierOfY312 barrier && packet is BarrierOfY312Packet barrierOfY312)
        {
            if (_handlers.TryGetValue(Convert.ToString(barrierOfY312.Command).ToLower(), out var method))
            {
                method.Invoke(this, new object[] { barrier, barrierOfY312 });
            }
            else
            {
                var model = (ControllerModel)barrier.Model;
                device.Model.Type.WriteLog($"未找到设备{model.DeviceNo}对应的处理方法！");
            }
        }
    }

    /// <summary>
    /// <see cref="DeviceType.NonMotorizedLaneControl"/> 数据回调前端处理
    /// </summary>
    /// <param name="device">设备上下文对象</param>
    /// <param name="packet">接收到的数据包</param>
    private void NonMotorizedHandle(IDevice device, IPacket packet)
    {
        if (device is NonMotorizedOf moto && packet is NonMotorizedOfPacket ofPacket)
        {
            if (_handlers.TryGetValue(Convert.ToString(ofPacket.Command).ToLower(), out var method))
            {
                method.Invoke(this, new object[] { moto, ofPacket });
            }
            else
            {
                var model = (ControllerModel)moto.Model;
                device.Model.Type.WriteLog($"未找到设备{model.DeviceNo}对应的处理方法！");
            }
        }
    }

    /// <summary>
    /// <see cref="DeviceType.QRCode01"/> 数据回调前端处理
    /// </summary>
    /// <param name="device">设备上下文对象</param>
    /// <param name="packet">接收到的数据包</param>
    private void QRCodeDeviceHandle(IDevice device, IPacket packet)
    {
        if (device is QrCodeOf01 qrCodeOf01 && packet is QrCodeOf01Packet ofPacket)
        {
            if (_handlers.TryGetValue(Convert.ToString(ofPacket.Command).ToLower(), out var method))
            {
                method.Invoke(this, new object[] { qrCodeOf01, ofPacket });
            }
            else
            {
                var model = (ControllerModel)qrCodeOf01.Model;
                device.Model.Type.WriteLog($"未找到设备{model.DeviceNo}对应的处理方法！");
            }
        }
    }

    #region 非机动车车道控制器处理方法

    /// <summary>
    /// 非机动车识别事件上传处理
    /// </summary>
    /// <param name="nonMotorized">非机动车连接对象</param>
    /// <param name="packet">接收到的数据包</param>
    [Command(NonMotorizedOfCommand.eventupload)]
    private void NonMotoEventupload(NonMotorizedOf nonMotorized, NonMotorizedOfPacket packet)
    {
        //var isHandle = false;
        var model = (ControllerModel)nonMotorized.Model;

        ////获取车道策略是否需要判断地感
        var policyPass = AppBasicCache.GetSentryPolicyPasswayDic.FirstOrDefault(m => m.Value.PolicyPassway_PasswayNo == model.PasswayNo);
        if (policyPass.Value == null)
        {
            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"设备[{model.IPAddress}]接收数据：{packet}，未找到车道策略信息！");
            return;
        }

        var reqPush = JObject.Parse(packet.ToString());
        if (PassTask.TryGetValue(model.PasswayNo, out var task))
        {
            if (task != null && !task.IsCompleted)
            {
                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"设备[{model.IPAddress}]接收数据：{JsonConvert.SerializeObject(reqPush)}，本次刷卡无效，已存在刷卡任务！");
                return;
            }
        }

        task = Task.Run(delegate
        {
            try
            {
                var isSendRev = true;

                var ts = DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, 0);
                var timeStamp = Convert.ToInt64(ts.TotalSeconds).ToString();

                var revData = new EventuploadModel
                {
                    msgid = Convert.ToString(reqPush["msgid"]),
                    comtype = "comtype",
                    timestamp = timeStamp
                };

                var sgetCarNo = Convert.ToString(reqPush["cardno"]);
                if (string.IsNullOrEmpty(sgetCarNo))
                {
                    revData.resultCode = "0";
                    revData.advertising = "读取卡号信息为空，请重刷！";
                    revData.ttsvoice = "读取卡号信息为空，请重刷！";
                }

                DeviceServerHandle.LicensePlateQueue.TryGetValue(model.PasswayNo, out var queue);
                if (queue != null)
                {
                    DeviceExt camera = null;
                    var tempImage = string.Empty;
                    //判断是否关联识别相机
                    AppBasicCache.GetAllDeivces.TryGetValue(model.DeviceNo, out var device);
                    if (device != null)
                    {
                        if (!string.IsNullOrEmpty(device.Device_FNo) && device.Device_FNo != "0")
                        {
                            //获取关联识别相机
                            camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(m => m.Device_No == device.Device_FNo);
                            tempImage = CameraImageHelper.ImageSaveHSPathBig(sgetCarNo, camera.Device_SentryHostNo);
                            _ = CameraController.GetSnapshotAsync(camera.Device_IP, tempImage, camera.Device_SentryHostNo, sgetCarNo);
                        }
                    }

                    var carPlate = new CarPlateInfo
                    {
                        TriggerTime = DateTimeHelper.GetNowTime(),
                        Mode = 4,
                        OJson = JsonConvert.SerializeObject(reqPush),
                        PasswayNo = model.PasswayNo,
                        DeviceNo = model.DeviceNo,
                        CarPlate = sgetCarNo,
                        Credibility = 100,
                        IsUnlicensedCar = true,
                        CarmeraIP = model.IPAddress,
                        IsRealLicensePlate = true,
                        BigJPGPath = tempImage,
                    };
                    queue.Enqueue(carPlate);
                    isSendRev = false;
                }
                else
                {
                    revData.resultCode = "0";
                    revData.advertising = "车道设备信息未设置，请联系管理员！";
                    revData.ttsvoice = "车道设备信息未设置，请联系管理员！";
                }

                if (isSendRev)
                {
                    var sRev = JsonConvert.SerializeObject(revData);
                    var tempbytes = Encoding.GetEncoding("GBK").GetBytes(sRev);
                    var sendPacket = new NonMotorizedOfPacket
                    {
                        Command = NonMotorizedOfCommand.eventupload,
                        IsSend = false,
                        Data = tempbytes
                    };
                    _ = nonMotorized.SendCmdAsync(sendPacket, "eventupload事件数据响应");
                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"响应设备[{model.IPAddress}]数据：{JsonConvert.SerializeObject(revData)}");

                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex);
            }
        });
        PassTask.AddOrUpdate(model.PasswayNo, task, (_, _) => task);
    }

    #endregion

    #region 二维码识别设备扫码结果处理方法

    /// <summary>
    /// 二维码设备扫码结果上传处理
    /// </summary>
    /// <param name="qrCodeOf01">二维码识别设备连接对象</param>
    /// <param name="packet">接收到的数据包</param>
    [Command(QrCodeOf01Command.UploadResult)]
    private void QRCodeDeviceResult(QrCodeOf01 qrCodeOf01, QrCodeOf01Packet packet)
    {
        var model = (QrCodeModel)qrCodeOf01.Model;
        var sqr = packet.ToString();
        _ = PasswayConfirmReleaseUtil.PaymentCode(model.PasswayNo, sqr);
    }

    #endregion


    /// <summary>
    /// 执行红包找零步骤2
    /// </summary>
    /// <param name="changeAmount">需要进行微信红包找零的金额</param>
    /// <param name="cashMoney">用户实际缴纳的纸币金额</param>
    /// <param name="orderNo">进出场订单号</param>
    /// <returns>返回指定的元数组【是否找零成功,输出信息,微信找零成功二维码】</returns>
    private Tuple<bool, string, string> RedPacketGiveCharge(decimal changeAmount, decimal cashMoney, string orderNo, string payOrderNo)
    {
        var isSuccess = false; //是否找零成功
        var strMsg = string.Empty; //输出提示信息
        var strRedPacketQR = string.Empty; //找零成功的二维码

        if (AppBasicCache.CurrentSysConfigContent == null || string.IsNullOrWhiteSpace(AppBasicCache.CurrentSysConfigContent.SiteDomain_Weixin))
        {
            return new Tuple<bool, string, string>(false, "微信找零站点不正确！", string.Empty);
        }

        dynamic dyn = new ExpandoObject();
        dyn.parkKey = AppBasicCache.SentryHostInfo.Parking_Key;
        dyn.parkOrderNo = orderNo;
        dyn.payOrderNo = payOrderNo;
        dyn.cashRedpack = (changeAmount * 100).ToString("0"); //需要红包找零的金额
        dyn.payed = (cashMoney * 100).ToString("0"); //纸币支付总金额
        dyn.nonce_str = Guid.NewGuid().ToString("N");

        string data = JsonConvert.SerializeObject(dyn);
        var key = Utils.MD5Encrypt($"#sfmfixedcode#{AppBasicCache.SentryHostInfo.Parking_Key}", Encoding.UTF8).ToLower();
        var sdata = Utils.ToQuery(JsonConvert.DeserializeObject<Dictionary<string, object>>(data), false) + "key=" + key;
        dyn.sign = Utils.MD5Encrypt(sdata, Encoding.UTF8);
        string strRedPacket = JsonConvert.SerializeObject(dyn);
        var url = AppBasicCache.CurrentSysConfigContent.SiteDomain_Weixin.TrimEnd('/') + "/WeixinApi/RedpackQrCode";
        var sResult = HttpPost.PostData(url + "?jsonModel=" + strRedPacket, "", 5000);
        if (!string.IsNullOrEmpty(sResult))
        {
            var objRedPacketGive = (JObject)JsonConvert.DeserializeObject(sResult);
            if (null != objRedPacketGive)
            {
                var strSuccess = Convert.ToString(objRedPacketGive["Success"]);
                if (bool.TryParse(strSuccess, out var b) && b)
                {
                    isSuccess = true;
                    strRedPacketQR = Convert.ToString(objRedPacketGive["Data"]);
                }
            }
            else
            {
                LogManagementMap.WriteToFile("执行微信红包找零步骤，反序列化结果为空", LogLevel.Error);
            }
        }
        else
        {
            strMsg = "执行微信红包找零步骤，请求结果为空";
            LogManagementMap.WriteToFile(strMsg, LogLevel.Error);
        }

        var tuple1 = new Tuple<bool, string, string>(isSuccess, strMsg, strRedPacketQR);
        return tuple1;
    }

    #region 机器人设备处理方法

    /// <summary>
    /// 开始取票，发送打印二维码指令
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(RobotCommandType.unlicensedCarIn)]
    private async void HandleUnlicensedCarIn(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        var lsc = Utils.CreateAuthStr_All(8, false);
        var cardNames = TyziTools.Json.ToString(new List<string> { "无牌车" });

        var tempImage = CameraImageHelper.ImageSaveHSPathBig(lsc, deviceCamera.Device_SentryHostNo);
        _ = LPRTools.GetSnapShootToJpeg(deviceCamera, tempImage);
        var tempModel = new ParkCarInOut
        {
            mode = 2,
            carno = lsc,
            camerano = deviceCamera.Device_No,
            cartype = cardNames,
            parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
            isreal = 0,
            orderno = $"{Utils.CreateNumberWith("N")}-{lsc}",
            img = tempImage,
            time = DateTimeHelper.GetNowTime()
        };
        //通行检测
        var temp = PassHelper.OnCheckCarPass(tempModel);
        if (temp.success && (temp.passres.code == 1 || temp.passres.code == 2))
        {
            var sqr = $"{AppBasicCache.CurrentSysConfigContent.SiteDomain_Weixin}/dwz?parkKey={AppBasicCache.SentryHostInfo.Parking_Key}&pro={tempModel.orderno}";
            var model0 = new RobotSendPacket
            {
                type = RobotCommandType.unlicensedCarInTicketCode,
                MessageIndex = receivePacket.MessageIndex,
                IsRespone = true,
                data = new
                {
                    ticketQrcode = sqr,
                    plateNo = lsc,
                    enterTime = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss)}"
                }
            };
            await ChannelMachineController.SendDataAsync(model.IPAddress, model0);
            DeviceCreateOrder.TryRemove(model.DeviceNo, out var temp1);
            DeviceCreateOrder.TryAdd(model.DeviceNo, temp);
        }
        else
        {
            var model0 = new RobotSendPacket
            {
                type = RobotCommandType.unlicensedCarInTicketCode,
                voiceType = (int)RobotVoiceType.ProhibitCurrent,
                resultCode = 2,
                msg = "车辆禁止通行",
                data = new { }
            };
            await ChannelMachineController.SendDataAsync(model.IPAddress, model0);

            var data485 = Passthrough485Util.InstantDisplay("禁止通行");
            await CameraController.SendDataBy485Async(deviceCamera.Device_IP, (SerialIndexType)(deviceCamera.Device_Com ?? 0), data485, nameof(Passthrough485Util.SetInOutMode));
        }
    }

    /// <summary>
    /// 取票成功，发送无牌车入场指令
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(RobotCommandType.ticketTakeSuccess)]
    private async void HandleTicketTakeSuccess(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        if (receivePacket == null || receivePacket.data == null) return;
        var jo = JObject.Parse(receivePacket.data.ToString());
        if (DeviceCreateOrder.TryGetValue(model.DeviceNo, out var data))
        {
            if (jo != null && jo.Property("ticketQrcode") != null && Convert.ToString(jo["ticketQrcode"])?.Length > 26)
            {
                var sTicketQrcode = Convert.ToString(jo["ticketQrcode"]);
                var orderNo = sTicketQrcode.Substring(sTicketQrcode.LastIndexOf("=") + 1);
                if (data.resorder.resIn.parkorder.ParkOrder_No == orderNo)
                {
                    var passResult = PassHelper.CarInComplete(new ParkGatePass
                    {
                        account = AppBasicCache.CurrentAdmins.Admins_Account,
                        carno = data.passres.carno,
                        time = DateTimeHelper.GetNowTime(),
                        img = data.passres.img,
                        camerano = data.passres.device.Device_No,
                        name = AppBasicCache.CurrentAdmins.Admins_Name,
                        orderdetailno = data.passres.orderdetailno,
                        orderno = data.passres.parkorderno,
                        parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                        code = 200,
                        imgpath = data.passres.localimage,
                        isSupplement = data?.isSupplement ?? false,
                    });
                    if (passResult.success)
                    {
                        var (isDoubleGate, linkCameraNoList) = Command.GateCmd.GetDoubleGateInfo(deviceCamera.Device_PasswayNo, data.passres.cartype.CarType_No);
                        var gateResult = await GateCmd.ExecuteAsync(deviceCamera, GateCmd.ActionEnum.Open, true, false, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);
                        if (data.recog != null)
                        {
                            //更新执行开闸的结果
                            data.recog.CarRecog_OpenStatus = gateResult.Success ? 1 : -1;
                            BLL.CarRecog.GetInstance(data.recog.CarRecog_Time ?? DateTime.Now)
                                .UpdateOpenStatus(data.recog.CarRecog_No, (int)data.recog.CarRecog_OpenStatus);
                        }
                        var ffm = AppBasicCache.GetSentryPolicyPasswayDic.FirstOrDefault(x => x.Value.PolicyPassway_PasswayNo == deviceCamera.Device_PasswayNo);
                        if (ffm.Value != null && ffm.Key != null && ffm.Value.PolicyPassway_ShowOption == 5)
                        {
                            var scontent = $"{data.passres.carno} {ffm.Value.PolicyPassway_Show}";
                            var data485 = Passthrough485Util.InstantDisplay(scontent);
                            await CameraController.SendDataBy485Async(deviceCamera.Device_IP, (SerialIndexType)(deviceCamera.Device_Com ?? 0), data485, nameof(Passthrough485Util.SetInOutMode));
                        }
                        else
                        {
                            var data485 = Passthrough485Util.ShowWelcome();
                            await CameraController.SendDataBy485Async(deviceCamera.Device_IP, (SerialIndexType)(deviceCamera.Device_Com ?? 0), data485, nameof(Passthrough485Util.SetInOutMode));
                        }

                        var tmodel1 = new RobotSendPacket
                        {
                            resultCode = 1,
                            type = receivePacket.type,
                            MessageIndex = receivePacket.MessageIndex,
                            IsRespone = true,
                            data = new { }
                        };
                        await ChannelMachineController.SendDataAsync(model.IPAddress, tmodel1);
                        SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                        {
                            PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                            PostRecordHandle_CarPlate = data.passres.carno,
                            PostRecordHandle_Datas = passResult.data,
                            PostRecordHandle_ToType = 1,
                            PostRecordHandle_Status = 0,
                            PostRecordHandle_ReturnMsg = string.Empty,
                            PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                        });
                        return;
                    }

                    LogManagementMap.WriteToFileException(null, "取票入场通行失败" + TyziTools.Json.ToString(passResult));
                }
            }
        }

        var tmodel = new RobotSendPacket
        {
            resultCode = 0,
            type = receivePacket.type,
            MessageIndex = receivePacket.MessageIndex,
            IsRespone = true,
            data = new { }
        };
        await ChannelMachineController.SendDataAsync(model.IPAddress, tmodel);

        LogManagementMap.WriteToFileException(null, $"取票成功入场失败，接收参数不正确{receivePacket}");
    }

    /// <summary>
    /// 接收支付二维码，发起支付
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(RobotCommandType.licensedWalletQrcode, RobotCommandType.unlicensedWalletQrcode, RobotCommandType.licensedLaneCodeWalletQrcode)]
    private void HandlePayQrcode(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        if (receivePacket == null) return;
        var jo = JObject.Parse(receivePacket.ToString());
        if (null != jo && jo.Property("payQrcode") != null && !string.IsNullOrWhiteSpace(Convert.ToString(jo["payQrcode"])))
        {
            var qr = Convert.ToString(jo["payQrcode"]);
            _ = PasswayConfirmReleaseUtil.PaymentCode(deviceCamera.Device_PasswayNo, qr);
        }
        else
        {
            LogManagementMap.WriteToFileException(null, "接收支付授权码不存在！");
        }
    }

    /// <summary>
    /// 处理无牌车出场请求
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(RobotCommandType.unlicensedReqOut)]
    private async void HandleUnlicensedReqOut(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        if (receivePacket == null) return;
        var tmodel = new RobotSendPacket
        {
            type = RobotCommandType.unlicensedReqTicketOut,
            MessageIndex = receivePacket.MessageIndex,
            IsRespone = true,
            data = new { }
        };
        await ChannelMachineController.SendDataAsync(model.IPAddress, tmodel);
    }

    /// <summary>
    /// 无牌车出场扫描纸票二维码
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的机器人数据包</param>
    /// <param name="model">通道机器人信息</param>
    /// <param name="passway">通道信息</param>
    [Command(RobotCommandType.unlicensedOutSendTicket)]
    private async void HandleUnlicensedOutSendTicket(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        if (receivePacket == null) return;
        var jo = JObject.Parse(receivePacket.ToString());
        if (jo != null && jo.Property("ticketQrcode") != null && Convert.ToString(jo["ticketQrcode"])?.Length > 26)
        {
            var sTicketQrcode = Convert.ToString(jo["ticketQrcode"]);
            var iindex = sTicketQrcode.LastIndexOf("-");
            var sRecord = sTicketQrcode.Substring(iindex + 1);

            if (DeviceOutTimeSpane.TryGetValue(sRecord, out var d1))
            {
                if (DateTimeHelper.GetNowTime().Subtract(d1).TotalSeconds < 10)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{sRecord} 记录在5秒内不处理！");
                    return;
                }

                DeviceOutTimeSpane.TryRemove(sRecord, out var d2);
            }
            else
            {
                DeviceOutTimeSpane.TryAdd(sRecord, DateTimeHelper.GetNowTime());
            }

            var sInImage = CameraImageHelper.ImageSaveHSPathBig(sRecord, deviceCamera.Device_SentryHostNo);
            _ = LPRTools.GetSnapShootToJpeg(deviceCamera, sInImage);
            if (DeviceServerHandle.LicensePlateQueue.TryGetValue(deviceCamera.Device_PasswayNo, out var queue))
            {
                queue.Enqueue(new CarPlateInfo
                {
                    CarPlate = sRecord,
                    TriggerTime = DateTimeHelper.GetNowTime(),
                    BigJPGPath = sInImage,
                    CarmeraIP = deviceCamera.Device_IP,
                    CarTypeMode = "无牌车",
                    DeviceNo = deviceCamera.Device_No,
                    IsRealLicensePlate = false,
                    TriggerMode = 0,
                    PasswayNo = deviceCamera.Device_PasswayNo,
                    CarTypeNameLogo = "",
                    CarPlateColor = 0,
                    MixModel = "无牌车",
                    IsUnlicensedCar = true,
                    Mode = 5
                });
            }
        }
        else
        {
            var tmodel = new RobotSendPacket
            {
                type = receivePacket.type,
                resultCode = 4,
                MessageIndex = receivePacket.MessageIndex,
                IsRespone = true,
                data = new { }
            };
            await ChannelMachineController.SendDataAsync(model.IPAddress, tmodel);
            LogManagementMap.WriteToFileException(null, $"取票成功出场失败，接收参数不正确{receivePacket}");
        }
    }

    /// <summary>
    /// 处理机器人开闸指令的方法
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的机器人数据包</param>
    /// <param name="model">通道机器人信息</param>
    /// <param name="passway">通道信息</param>
    [Command(RobotCommandType.SIPCmdOpenGate)]
    private async void HandleSIPCmdOpenGate(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        var tempImage = CameraImageHelper.ImageSaveHSPathBig("Robot", deviceCamera.Device_SentryHostNo);
        _ = LPRTools.GetSnapShootToJpeg(deviceCamera, tempImage);

        var rlt = PassHelper.OpenGatePass(new SpecialCarPass
        {
            time = DateTimeHelper.GetNowTime(),
            camerano = deviceCamera.Device_No,
            img = tempImage,
            name = AppBasicCache.CurrentAdmins.Admins_Name,
            parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
            account = AppBasicCache.CurrentAdmins.Admins_Account,
            type = 1,
            sRemark = "自助设备-远程开闸"
        });


        #region 控制板

        if (passway.Passway_IsBackCar == 1 && passway.Passway_EnableBoard == 1)
        {
            var scode1 = Utils.CreateAuthStr(8).ToUpper();
            LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{scode1}-倒车功能已启用-0x02");
            BarrierDeviceUtilsl.SendCarOrder(passway.Passway_No, $"{DateTimeHelper.GetNowTime():yyyyMMddHHmmssfff}-{scode1}", 0, passway.Passway_SameInOut, passway.Passway_OutNo);
        }

        #endregion 控制板

        var blong = false;
        var gateResult = await CameraController.ControlIOAsync(deviceCamera.Device_IP, deviceCamera.Device_InIO ?? 0, 1, blong);
        var isSuccess = gateResult.Success;
        blong = gateResult.IsLongOpen;
        if (!isSuccess)
        {
            LogManagementMap.WriteToFileException(null, $"设备[{deviceCamera.Device_IP}]开闸返回失败");
        }
        else
        {
            var data4851 = Passthrough485Util.TalkOpenGate();
            await CameraController.SendDataBy485Async(deviceCamera.Device_IP, (SerialIndexType)(deviceCamera.Device_Com ?? 0), data4851, nameof(Passthrough485Util.TalkOpenGate));
        }


        //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
        if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(deviceCamera.Drive_Name))
        {
            var camera = TcpConnPools.DevicePool.Instance.GetDevice(deviceCamera.Device_IP);
            if (camera is ICamera && camera is CameraOfYS cameraOfYs)
            {
                var leds = new List<LedModel>();
                leds.Add(new LedModel($"开闸成功", 1, 0, 2));
                leds.Add(new LedModel($"请通行", 1, 0, 2));
                await cameraOfYs.SendLedAsync(leds);
            }
        }
        else
        {
            var data485 = Passthrough485Util.InstantDisplay("自助设备开闸成功  请通行");
            await CameraController.SendDataBy485Async(deviceCamera.Device_IP, (SerialIndexType)(deviceCamera.Device_Com ?? 0), data485, nameof(Passthrough485Util.InstantDisplay));
        }
        //机器人开闸相当于人工开闸，添加一条人工开闸记录
        var tmodel = new RobotSendPacket
        {
            type = RobotCommandType.SIPCmdOpenGate,
            MessageIndex = receivePacket.MessageIndex,
            IsRespone = true,
            data = new { },
            resultCode = rlt.success ? 1 : 0
        };
        tmodel.resultCode = rlt.success ? 1 : 0;
        await ChannelMachineController.SendDataAsync(model.IPAddress, tmodel);
        if (rlt.success)
        {
            SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
            {
                PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                PostRecordHandle_CarPlate = string.Empty,
                PostRecordHandle_Datas = rlt.data,
                PostRecordHandle_ToType = 3,
                PostRecordHandle_Status = 0,
                PostRecordHandle_ReturnMsg = string.Empty,
                PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
            });
        }
        else
        {
            LogManagementMap.WriteToFile("自助设备开闸失败" + TyziTools.Json.ToString(rlt));
        }

        //发送红绿灯指令
        if (isSuccess)
        {
            var data4851 = Passthrough485Util.TalkOpenGate();
            await CameraController.SendDataBy485Async(deviceCamera.Device_IP, (SerialIndexType)(deviceCamera.Device_Com ?? 0), data4851, nameof(Passthrough485Util.TalkOpenGate));
        }
    }

    /// <summary>
    /// 接收蓝牙消息
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(RobotCommandType.bluetooth)]
    private void HandleBluetooth(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {

        bool isSuccess = false;
        object data = null;
        string msg = "";
        var actionName = "";

        try
        {
            if (receivePacket == null) return;
            var jo = JObject.Parse(receivePacket.ToString());
            if (null != jo)
            {
                JObject j;
                actionName = Convert.ToString(jo["actionName"]);
                switch (actionName?.ToLower())
                {
                    case "getcarprice":
                        //读取参数
                        actionName = "OrderPrice";
                        var orderno = Convert.ToString(jo["orderNo"]);
                        var freeMinutes = Convert.ToInt32(jo["freeMinutes"]);
                        if (freeMinutes > 0)
                        {
                            actionName = "OrderFreeMinPrice";
                        }

                        //组装中间件请求参数
                        j = new JObject();
                        j["actionName"] = actionName;
                        j["orderNo"] = orderno;
                        j["minutes"] = freeMinutes;
                        //执行中间件业务
                        MidHelper.OrderPrice(j, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(Convert.ToString(jo["actionName"]), isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.getCarPrice);
                        break;
                    case "getoutgateorder":

                        //执行中间件业务
                        MidHelper.GetOutGateOrder(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.getOutGateOrder);

                        break;
                    case "opengate":
                        //执行中间件业务
                        MidHelper.OpenGate(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.openGate);

                        break;

                    case "onlinepayment":

                        //执行中间件业务
                        MidHelper.OnlinePayment(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.onlinePayment);

                        break;

                    case "pulloutcar":
                        //执行中间件业务
                        MidHelper.PullOutCar(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.pullOutCar);

                        break;

                    case "openingate":

                        //执行中间件业务
                        MidHelper.OpenInGate(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.openInGate);

                        break;
                    case "openoutgate":

                        //执行中间件业务
                        MidHelper.OpenOutGate(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.openOutGate);

                        break;
                    case "pullentercar":

                        //执行中间件业务
                        MidHelper.PullEnterCar(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.pullEnterCar);

                        break;
                    case "loadrecord":

                        //执行中间件业务
                        MidHelper.LoadRecord(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        MidHelper.SendBluetoothDatas(actionName, isSuccess, msg, data, deviceCamera.Device_PasswayNo, RobotCommandType.loadRecord);

                        break;
                }

            }
            else
            {
                LogManagementMap.WriteToFileException(null, "接收蓝牙消息不能为空！");
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(null, "接收蓝牙消息处理异常：" + ex.ToString());
        }

    }

    /// <summary>
    /// 【显示屏】接收蓝牙消息
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(ScreenTcpCommand.screenBluetooth)]
    private async void HandleScreenBluetooth(DeviceExt deviceCamera, ScreenTcpPacket receivePacket, ScreenModel model)
    {

        bool isSuccess = false;
        object data = null;
        string msg = "";
        var actionName = "";

        try
        {
            if (receivePacket == null) return;
            if (receivePacket.Data == null) return;

            var packString = receivePacket.Data.ToString();
            if (packString == "screenBluetooth")
            {
                //LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"蓝牙消息[screenBluetooth]已送达");
                return;
            }

            var joData = JObject.Parse(packString);
            if (null != joData)
            {


                var jo = JObject.Parse(Convert.ToString(joData["data"]["content"]));
                if (jo == null) return;

                await Task.Delay(50);//等待50毫秒

                (bool, string) blueRet = (false, "");
                JObject j;
                actionName = Convert.ToString(jo["actionName"]);
                switch (actionName?.ToLower())
                {
                    case "getcarprice":
                        //读取参数
                        actionName = "OrderPrice";
                        var orderno = Convert.ToString(jo["orderNo"]);
                        var freeMinutes = Convert.ToInt32(jo["freeMinutes"]);
                        if (freeMinutes > 0)
                        {
                            actionName = "OrderFreeMinPrice";
                        }

                        //组装中间件请求参数
                        j = new JObject();
                        j["actionName"] = actionName;
                        j["orderNo"] = orderno;
                        j["minutes"] = freeMinutes;
                        //执行中间件业务
                        MidHelper.OrderPrice(j, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, Convert.ToString(jo["actionName"]), isSuccess, msg, data);
                        break;
                    case "getoutgateorder":

                        //执行中间件业务
                        MidHelper.GetOutGateOrder(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;
                    case "opengate":
                        //执行中间件业务
                        MidHelper.OpenGate(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;

                    case "onlinepayment":

                        //执行中间件业务
                        MidHelper.OnlinePayment(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;

                    case "pulloutcar":
                        //执行中间件业务
                        MidHelper.PullOutCar(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;

                    case "openingate":

                        //执行中间件业务
                        MidHelper.OpenInGate(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;
                    case "openoutgate":

                        //执行中间件业务
                        MidHelper.OpenOutGate(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;
                    case "pullentercar":

                        //执行中间件业务
                        MidHelper.PullEnterCar(jo, out isSuccess, out data, out msg, actionName);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;
                    case "loadrecord":

                        //执行中间件业务
                        MidHelper.LoadRecord(jo, out isSuccess, out data, out msg, actionName, true);
                        //发送消息
                        blueRet = await MidHelper.SendScreenBluetoothDatas(deviceCamera.Device_No, actionName, isSuccess, msg, data);
                        break;
                }

                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"设备回复：[{actionName}]{(blueRet.Item1 ? "已接收" : "接收失败")},{blueRet.Item2}");

            }
            else
            {
                LogManagementMap.WriteToFileException(null, "接收蓝牙消息不能为空！");
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(null, "接收蓝牙消息处理异常：" + ex.ToString());
        }

    }

    /// <summary>
    /// 【显示屏】获取蓝牙编号
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(ScreenTcpCommand.getScreenBlueToothNo)]
    private void HandleScreenGetBlueToothNo(DeviceExt deviceCamera, ScreenTcpPacket receivePacket, ScreenModel model)
    {

        try
        {
            if (receivePacket == null) return;

            var blueToothNo = receivePacket.Data?.ToString();
            if (!string.IsNullOrEmpty(blueToothNo))
            {
                AppBasicCache.SetDeviceBlueTooth.TryAdd(model.DeviceNo, blueToothNo);
                var device = AppBasicCache.idToken == "1002" ? AppBasicCache.GetElement(AppBasicCache.GetAllDeivces, model.DeviceNo) : BLL.Device.GetEntity(model.DeviceNo);
                if (device != null)
                {
                    device.Device_BlueToothNo = blueToothNo;
                    BLL.Device.UpdateByModel(TyziTools.Json.ToObject<Model.Device>(TyziTools.Json.ToString(device)));
                }
            }
            else
            {
                LogManagementMap.WriteToFileException(null, "接收显示屏获取蓝牙编号消息不能为空！");
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(null, "接收显示屏获取蓝牙编号消息处理异常：" + ex.ToString());
        }

    }

    /// <summary>
    /// 【显示屏】展示二维码
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(ScreenTcpCommand.showScreenOfflineQrcode)]
    private void HandleScreenShowOfflineQrcode(DeviceExt deviceCamera, ScreenTcpPacket receivePacket, ScreenModel model)
    {

        try
        {
            if (receivePacket == null) return;

        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(null, "接收显示屏展示二维码响应消息处理异常：" + ex.ToString());
        }
    }
    /// <summary>
    /// 【显示屏】关闭二维码
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(ScreenTcpCommand.closeScreenOfflineQrcode)]
    private void HandleScreenCloseOfflineQrcode(DeviceExt deviceCamera, ScreenTcpPacket receivePacket, ScreenModel model)
    {

        try
        {
            if (receivePacket == null) return;

        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(null, "接收显示屏关闭二维码响应消息处理异常：" + ex.ToString());
        }
    }


    /// <summary>
    /// 接收通道机获取蓝牙编号消息
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的数据包</param>
    /// <param name="model">机器人设备实体类</param>
    /// <param name="passway">关联的车道实体类信息</param>
    [Command(RobotCommandType.getBlueToothNo)]
    private void HandleGetBlueToothNo(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {

        try
        {
            if (receivePacket == null) return;
            var jo = JObject.Parse(receivePacket.data?.ToString());
            if (null != jo)
            {
                var blueToothNo = Convert.ToString(jo["blueToothNo"]);
                if (!string.IsNullOrEmpty(blueToothNo))
                {
                    AppBasicCache.SetDeviceBlueTooth.TryAdd(model.DeviceNo, blueToothNo);
                    var device = AppBasicCache.idToken == "1002" ? AppBasicCache.GetElement(AppBasicCache.GetAllDeivces, model.DeviceNo) : BLL.Device.GetEntity(model.DeviceNo);
                    if (device != null)
                    {
                        device.Device_BlueToothNo = blueToothNo;
                        BLL.Device.UpdateByModel(TyziTools.Json.ToObject<Model.Device>(TyziTools.Json.ToString(device)));
                    }
                }
            }
            else
            {
                LogManagementMap.WriteToFileException(null, "接收通道机获取蓝牙编号消息不能为空！");
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(null, "接收通道机获取蓝牙编号消息处理异常：" + ex.ToString());
        }

    }

    /// <summary>
    /// 健康码上报
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的机器人数据包</param>
    /// <param name="model">通道机器人信息</param>
    /// <param name="passway">通道信息</param>
    [Command(RobotCommandType.uploadHealthCodeVerifyResult)]
    private async void HandleUploadHealthCodeVerifyResult(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        try
        {
            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, "自助设备健康码核验结果处理");
            var jo = JObject.Parse(receivePacket.ToString());
            var sparkOrder = Convert.ToString(jo["parkOrder"]);
            ConfirmRelease.Results.TryGetValue(deviceCamera.Device_PasswayNo, out var GetResultPass);
            var device = DeviceCommonUtil.GetPasswayMainDevice(deviceCamera.Device_PasswayNo);
            if (GetResultPass != null && GetResultPass.passres?.parkorderno == sparkOrder)
            {
                var squeryCode = Convert.ToString(jo["queryCode"]);
                var imgUrl = LPRTools.GetSentryHostImg(GetResultPass.passres?.localimage);
                if (squeryCode == "0")
                {
                    var scolorResult = Convert.ToString(jo["colorResult"]);
                    var squeryDetailData = Convert.ToString(jo["queryDetailData"]);
                    squeryDetailData = Regex.Unescape(squeryDetailData);
                    var joIn = JObject.Parse(squeryDetailData);
                    var symcount = Convert.ToString(joIn["ymcount"]);
                    var skydate = Convert.ToString(joIn["kydate"]);
                    var shsjcsj = Convert.ToString(joIn["hsjcsj"]);
                    var sfznumber = Convert.ToString(joIn["sfznumber"]);
                    var sfzname = Convert.ToString(joIn["sfzname"]);
                    var hsjcresult = Convert.ToString(joIn["hsjcresult"]);
                    var kyresult = Convert.ToString(joIn["kyresult"]);
                    var hsjcjg = Convert.ToString(joIn["hsjcjg"]);
                    var kydetail = Convert.ToString(joIn["kydetail"]);

                    var hsjcjgres = Utils.ExtractNumber(hsjcjg);
                    var kydetailres = Utils.ExtractNumber(kydetail);
                    var iColor = 0;
                    switch (scolorResult)
                    {
                        case "G":
                            iColor = 1;
                            break;
                        case "Y":
                            iColor = 2;
                            break;
                        case "R":
                            iColor = 3;
                            break;
                    }

                    var bkyd = DateTime.TryParse(skydate, out var dkydate);
                    var bhsd = DateTime.TryParse(shsjcsj, out var hsdate);
                    int.TryParse(symcount, out var iymcount);
                    if (!int.TryParse(hsjcresult, out var ihsjcresult))
                    {
                        ihsjcresult = 3;
                    }

                    if (!int.TryParse(kyresult, out var ikyresult))
                    {
                        ikyresult = 3;
                    }

                    var dpm = new HealthCode
                    {
                        idcard = sfznumber,
                        name = sfzname,
                        hsres = ihsjcresult,
                        hstime = hsjcjgres, //通过核酸上报时间自己计算
                        hsdate = hsdate,
                        kyres = ikyresult,
                        kytime = kydetailres, //通过抗原上报时间自己计算
                        kydate = dkydate,
                        ymcount = iymcount,
                        color = iColor,
                        data = HttpUtility.UrlEncode(JsonConvert.SerializeObject(jo))
                    };
                    if (!bkyd) dpm.kydate = null;
                    if (!bhsd) dpm.hsdate = null;
                    PassHelper.OnCheckCarPass(dpm, ref GetResultPass, out var dCode);

                    var carno = GetResultPass.passres?.carno;
                    //添加健康码记录同步主机
                    if (dCode != null)
                    {
                        _ = Task.Run(delegate
                        {
                            try
                            {
                                SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                {
                                    PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                    PostRecordHandle_CarPlate = carno,
                                    PostRecordHandle_Datas = JsonConvert.SerializeObject(dCode),
                                    PostRecordHandle_ToType = 11,
                                    PostRecordHandle_Status = 0,
                                    PostRecordHandle_ReturnMsg = string.Empty,
                                    PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                });
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFileException(ex, $"添加健康码记录同步主机异常:{carno}");
                            }
                        });
                    }

                    if (GetResultPass.success)
                    {
                        if (GetResultPass.passres.fycode == 0)
                        {
                            if (GetResultPass.passres?.actcode == 1)
                            {
                                GetResultPass.passres.code = GetResultPass.passres.actcode;

                                #region 遥控开闸入场处理

                                var openGate = true;
                                if (GetResultPass.policy != null && GetResultPass.policy.takerecord == 1 && GetResultPass.policy.autotakerecord != 1)
                                {
                                    var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == deviceCamera.Device_PasswayNo && m.Value.PasswayLink_GateType == 1);
                                    if (areaLink.Count() > 0)
                                    {
                                        openGate = false;
                                    }
                                }

                                #endregion

                                #region 核验通过自动放行处理

                                await DeviceServerHandle.AutoRelease(GetResultPass, deviceCamera, openGate);

                                #endregion
                            }
                            else
                            {
                                #region 语音播报

                                var display = $"{carno}健康码校验通过，请等待管理员确认放行";
                                BroadcastUtil.HealthCodeVerify(deviceCamera.Device_PasswayNo, display, display, deviceCamera);

                                #endregion

                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"入场订单[{sparkOrder}]扫健康码通过，但还需要确认通行！");
                            }
                        }
                        else
                        {
                            var msg = GetResultPass.passres.errmsg;

                            #region 车道缓存清理

                            PasswayConfirmReleaseUtil.RemoveResult(deviceCamera.Device_PasswayNo);

                            #endregion

                            #region 语音播报

                            var display = $"{carno}{msg}";
                            BroadcastUtil.HealthCodeVerify(deviceCamera.Device_PasswayNo, display, display, deviceCamera);

                            #endregion

                            #region 岗亭通知

                            WebSocketUtil.SendWS(GetResultPass);

                            #endregion
                        }
                    }
                    else
                    {
                        #region 车道缓存清理

                        PasswayConfirmReleaseUtil.RemoveResult(deviceCamera.Device_PasswayNo);

                        #endregion

                        #region 语音播报

                        BroadcastUtil.NoEntry(GetResultPass, deviceCamera);

                        #endregion

                        #region 岗亭通知

                        WebSocketUtil.SendWSTip(carno, imgUrl, deviceCamera.Device_PasswayNo, passway.Passway_Name, GetResultPass.errmsg, DateTimeHelper.GetNowTime().ToString("G"));
                        WebSocketUtil.SendWS(GetResultPass);

                        #endregion
                    }
                }
                else
                {
                    #region 车道缓存清理

                    PasswayConfirmReleaseUtil.RemoveResult(deviceCamera.Device_PasswayNo);

                    #endregion

                    #region 语音播报

                    var display = $"{GetResultPass.passres?.carno}健康码扫码获取不成功，请联系管理员！";
                    BroadcastUtil.HealthCodeVerify(deviceCamera.Device_PasswayNo, display, display, deviceCamera);

                    #endregion

                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"健康码信息无效：{JsonConvert.SerializeObject(jo)}");

                    #region 岗亭通知

                    WebSocketUtil.SendWSTip(GetResultPass.passres?.carno, imgUrl, deviceCamera.Device_PasswayNo, passway.Passway_Name, "健康码扫码获取不成功，请联系管理员！", DateTimeHelper.GetNowTime().ToString("G"));

                    #endregion
                }
            }
            else
            {
                #region 车道缓存清理

                PasswayConfirmReleaseUtil.RemoveResult(deviceCamera.Device_PasswayNo);

                #endregion

                #region 语音播报

                var display = "健康码扫码未获取到停车订单，请重新识别再试！";
                BroadcastUtil.HealthCodeVerify(deviceCamera.Device_PasswayNo, display, display, deviceCamera);

                #endregion

                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"健康码信息入场订单号未找到：{JsonConvert.SerializeObject(jo)}");

                #region 岗亭通知

                WebSocketUtil.SendWSTip(sparkOrder, "", deviceCamera.Device_PasswayNo, passway.Passway_Name, "健康码信息入场订单号未找到", DateTimeHelper.GetNowTime().ToString("G"));

                #endregion
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"健康码回调处理异常:{JsonConvert.SerializeObject(receivePacket)}");
        }
    }

    /// <summary>
    /// 无牌车压地感健康码上报
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的机器人数据包</param>
    /// <param name="model">通道机器人信息</param>
    /// <param name="passway">通道信息</param>
    [Command(RobotCommandType.unlicensedCarUploadHealthCode)]
    private void HandleUnlicensedCarUploadHealthCode(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        var passwayno = passway?.Passway_No;
        try
        {
            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, "自助设备健康码无牌车检查");
            var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passway.Passway_No);
            if (areaLink.Count() == 1 && areaLink.FirstOrDefault().Value.PasswayLink_GateType == 1)
            {
                var jo = JObject.Parse(receivePacket.ToString());

                var squeryCode = Convert.ToString(jo["queryCode"]);
                if (squeryCode == "0")
                {
                    var scolorResult = Convert.ToString(jo["colorResult"]);
                    var squeryDetailData = Convert.ToString(jo["queryDetailData"]);
                    squeryDetailData = Regex.Unescape(squeryDetailData);
                    var joIn = JObject.Parse(squeryDetailData);
                    var symcount = Convert.ToString(joIn["ymcount"]);
                    var skydate = Convert.ToString(joIn["kydate"]);
                    var shsjcsj = Convert.ToString(joIn["hsjcsj"]);
                    var sfznumber = Convert.ToString(joIn["sfznumber"]);
                    var sfzname = Convert.ToString(joIn["sfzname"]);
                    var hsjcresult = Convert.ToString(joIn["hsjcresult"]);
                    var kyresult = Convert.ToString(joIn["kyresult"]);
                    var hsjcjg = Convert.ToString(joIn["hsjcjg"]);
                    var kydetail = Convert.ToString(joIn["kydetail"]);

                    var hsjcjgres = Utils.ExtractNumber(hsjcjg);
                    var kydetailres = Utils.ExtractNumber(kydetail);
                    var iColor = 0;
                    switch (scolorResult)
                    {
                        case "G":
                            iColor = 1;
                            break;
                        case "Y":
                            iColor = 2;
                            break;
                        case "R":
                            iColor = 3;
                            break;
                    }

                    var bkyd = DateTime.TryParse(skydate, out var dkydate);
                    var bhsd = DateTime.TryParse(shsjcsj, out var hsdate);
                    int.TryParse(symcount, out var iymcount);
                    if (!int.TryParse(hsjcresult, out var ihsjcresult))
                    {
                        ihsjcresult = 3;
                    }

                    if (!int.TryParse(kyresult, out var ikyresult))
                    {
                        ikyresult = 3;
                    }

                    var data = new ResultPass();
                    data.passres = new ResultPassData
                    {
                        fycode = 1,
                        passway = new Passway
                        {
                            Passway_ParkNo = passway.Passway_ParkNo
                        },
                        parkorderno = "",
                        carno = "无牌车",
                        areano = areaLink.FirstOrDefault().Value.PasswayLink_ParkAreaNo
                    };
                    var dpm = new HealthCode
                    {
                        idcard = sfznumber,
                        name = sfzname,
                        hsres = ihsjcresult,
                        hstime = hsjcjgres, //通过核酸上报时间自己计算
                        hsdate = hsdate,
                        kyres = ikyresult,
                        kytime = kydetailres, //通过抗原上报时间自己计算
                        kydate = dkydate,
                        ymcount = iymcount,
                        color = iColor,
                        data = HttpUtility.UrlEncode(JsonConvert.SerializeObject(jo))
                    };
                    if (!bkyd) dpm.kydate = null;
                    if (!bhsd) dpm.hsdate = null;
                    PassHelper.OnCheckCarPass(dpm, ref data, out var dCode);
                    //添加健康码记录同步主机
                    if (dCode != null)
                    {
                        Task.Run(delegate
                        {
                            try
                            {
                                SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                {
                                    PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                    PostRecordHandle_CarPlate = "无牌车",
                                    PostRecordHandle_Datas = JsonConvert.SerializeObject(dCode),
                                    PostRecordHandle_ToType = 11,
                                    PostRecordHandle_Status = 0,
                                    PostRecordHandle_ReturnMsg = string.Empty,
                                    PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                });
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFileException(ex, "添加健康码记录同步主机异常:无牌车");
                            }
                        });
                    }

                    if (data.passres.fycode == 0)
                    {
                        #region 语音播报

                        BroadcastUtil.HealthCodeNoCarCheckReply(passwayno, 1, true, "核酸检测成功");

                        #endregion

                        #region 岗亭通知

                        WebSocketUtil.SendWSTip("无牌车", "", passwayno, passway.Passway_Name, "核酸检测成功", DateTimeHelper.GetNowTime().ToString("G"));

                        #endregion
                    }
                    else
                    {
                        #region 语音播报

                        BroadcastUtil.HealthCodeNoCarCheckReply(passwayno, 0, false, data.passres.errmsg);

                        #endregion

                        #region 岗亭通知

                        WebSocketUtil.SendWSTip("无牌车", "", passwayno, passway.Passway_Name, data.passres.errmsg, DateTimeHelper.GetNowTime().ToString("G"));

                        #endregion
                    }
                }
                else
                {
                    #region 语音播报

                    BroadcastUtil.HealthCodeNoCarCheckReply(passwayno, 0, false, "健康码扫码获取不成功，请联系管理员！");

                    #endregion

                    #region 岗亭通知

                    WebSocketUtil.SendWSTip("无牌车", "", passwayno, passway.Passway_Name, "当前区域不支持核酸检测", DateTimeHelper.GetNowTime().ToString("G"));

                    #endregion
                }
            }
            else
            {
                #region 语音播报

                BroadcastUtil.HealthCodeNoCarCheckReply(passwayno, 0, false, "当前区域不支持核酸检测");

                #endregion

                #region 岗亭通知

                WebSocketUtil.SendWSTip("无牌车", "", passwayno, passway.Passway_Name, "当前区域不支持核酸检测", DateTimeHelper.GetNowTime().ToString("G"));

                #endregion
            }
        }
        catch (Exception ex)
        {
            #region 语音播报

            BroadcastUtil.HealthCodeNoCarCheckReply(passwayno, 0, false, "核酸检测失败，请联系管理员！");

            #endregion

            #region 岗亭通知

            WebSocketUtil.SendWSTip("无牌车", "", passwayno, passway.Passway_Name, "核酸检测失败", DateTimeHelper.GetNowTime().ToString("G"));

            #endregion


            LogManagementMap.WriteToFileException(ex, $"健康码无牌车检查处理异常:{JsonConvert.SerializeObject(receivePacket)}");
        }
    }

    /// <summary>
    /// 纸币缴费
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的机器人数据包</param>
    /// <param name="model">通道机器人信息</param>
    /// <param name="passway">通道信息</param>
    [Command(RobotCommandType.licensedMultipleSheetsCashPayment, RobotCommandType.unlicensedMultipleSheetsCashPayment, RobotCommandType.licensedCashPayment, RobotCommandType.unlicensedCashPayment)]
    private void HandleMultipleSheetsCashPayment(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {
        Task.Run(async () =>
        {
            try
            {
                if (ConfirmRelease.Results.TryGetValue(deviceCamera.Device_PasswayNo, out var result))
                {
                    var carno = result?.passres?.carno;
                    var orderno = result?.resorder?.resOut?.parkorder?.ParkOrder_No ?? result.passres?.parkorderno;

                    var SSJE = result.payres.payedamount;
                    var YSJE = result.payres.orderamount;
                    var cashrobotamount = result.payres.cashrobotamount;
                    if (receivePacket.data != null && receivePacket.data is JObject jo)
                    {
                        var payMoney = decimal.Parse(jo["payMoney"].ToString());
                        var collectType = 0;
                        if (jo.ContainsKey("collectType")) collectType = int.Parse(Convert.ToString(jo["collectType"]));
                        if (collectType != 0)
                        {
                            RmbMachinePressMoney.TryRemove(orderno, out _);
                            RmbMachinePressMoney.TryAdd(orderno, null);

                            #region 发送设备压现金指令

                            var cashPressure = new RobotSendPacket();
                            cashPressure.resultCode = 1;
                            cashPressure.msg = "控制设备压现金";
                            cashPressure.type = RobotCommandType.rmbmachinePressMoney;
                            await ChannelMachineController.SendDataAsync(model.IPAddress, cashPressure);

                            #endregion

                            var count = 20; //设备押现金每秒触发一次最多触发20次
                            int? vl = null;
                        WaitCallback:
                            if (count > 0 && RmbMachinePressMoney.TryGetValue(orderno, out vl) && vl == null)
                            {
                                count--;
                                await Task.Delay(1000);
                                goto WaitCallback;
                            }

                            if (vl != 1)
                            {
                                payMoney = 0; //本次押现金失败
                            }

                            RmbMachinePressMoney.TryRemove(orderno, out var del);

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"压现金指令发送处理异常{receivePacket}！");
            }
        });
    }

    /// <summary>
    /// 设置压纸币
    /// </summary>
    /// <param name="deviceCamera">设备摄像头信息</param>
    /// <param name="receivePacket">接收到的机器人数据包</param>
    /// <param name="model">通道机器人信息</param>
    /// <param name="passway">通道信息</param>
    [Command(RobotCommandType.rmbmachinePressMoney, RobotCommandType.cashCollectResult)]
    private void HandleRmbmachinePressMoney(DeviceExt deviceCamera, RobotReceivePacket receivePacket, ChannelMachineModel model, PasswayExt passway)
    {

        _ = Task.Run(async () =>
         {
             try
             {
             GotoWait:
                 if (ConfirmRelease.Results.TryGetValue(deviceCamera.Device_PasswayNo, out var result))
                 {
                     var carno = result?.passres?.carno;
                     var orderno = result?.resorder?.resOut?.parkorder?.ParkOrder_No ?? result.passres?.parkorderno;

                     if (!string.IsNullOrEmpty(orderno))
                     {
                         if (!string.IsNullOrEmpty(DataCache.RobotPayCache.Get(orderno)))
                         {
                             Thread.Sleep(100);
                             goto GotoWait;
                         }
                     }

                     var SSJE = result.payres.payedamount;
                     var YSJE = result.payres.orderamount;
                     var cashrobotamount = result.payres.cashrobotamount;
                     if (receivePacket.data != null && receivePacket.data is JObject jo)
                     {
                         var payID = Convert.ToString(jo["payID"]);
                         if (!string.IsNullOrEmpty(payID))
                         {
                             if (payID != orderno)
                             {
                                 LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"当前订单编号[{payID}]与车道弹窗订单[{orderno}]不匹配，跳过处理！！！");
                             }

                             if (RmbMachinePressMoney.TryGetValue(orderno, out var vl))
                             {
                                 RmbMachinePressMoney.TryUpdate(orderno, receivePacket.resultCode, vl);
                             }
                         }
                         else
                         {
                             return;
                         }

                         DataCache.RobotPayCache.Set(orderno, "1");
                         var payMoney = decimal.Parse(jo["payMoney"].ToString());

                         if (payMoney > 0)
                         {
                             var rs = PassHelper.SavePay(new PaymethodParam
                             {
                                 parkNo = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                                 orderNo = orderno,
                                 carNo = carno,
                                 mode = 1,
                                 ysMoney = YSJE,
                                 payMoney = payMoney,
                                 time = DateTimeHelper.GetNowTime(),
                                 deviceNo = deviceCamera.Device_No,
                                 deviceName = deviceCamera.Device_Name,
                                 remark = "",
                                 account = ""
                             });
                             if (rs.success && rs.data != null)
                             {
                                 SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                 {
                                     PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                     PostRecordHandle_CarPlate = carno,
                                     PostRecordHandle_Datas = rs.data,
                                     PostRecordHandle_ToType = 12,
                                     PostRecordHandle_Status = 0,
                                     PostRecordHandle_ReturnMsg = string.Empty,
                                     PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                 });
                             }
                         }

                         var resultCode = 0;
                         var msg = "";
                         var carType = Convert2CarCard.ConvertToCardType(result.passres.carcardtype.CarCardType_Category);
                         string parkDuration = "", enterParkTime = "", outParkTime = result.time.Value.ToString("G");
                         if (result.passres.code == 4)
                         {
                             enterParkTime = result.resorder.resOut.parkorder.ParkOrder_EnterTime.Value.ToString("G");
                             parkDuration = "无入场记录";
                         }
                         else
                         {
                             DateTime dateInTime;
                             if (result.resorder.resOut?.onenter == 0)
                             {
                                 dateInTime = result.resorder.resOut.parkorder.ParkOrder_EnterTime ?? DateTimeHelper.GetNowTime();
                             }
                             else
                             {
                                 dateInTime = (result.resorder.resOut?.parkorder == null ? result.resorder.resIn?.parkorder?.ParkOrder_EnterTime : result.resorder.resOut?.parkorder?.ParkOrder_EnterTime) ?? DateTimeHelper.GetNowTime();
                             }

                             enterParkTime = dateInTime.ToString("G");
                             var ts = TimeSpan.FromMinutes(result.payres.parktimemin);
                             parkDuration = $"{ts.Days}天{ts.Hours}小时{ts.Minutes}分钟";
                         }

                         DataCache.RobotPayCache.Del(orderno);

                         decimal chargeMoney = 0;
                         var cachePayMoney = result.payres.cashrobotamount + payMoney;
                         var isChange = false;
                         decimal changeAmount = 0;
                         if (payMoney < SSJE)
                         {
                             chargeMoney = SSJE - payMoney;
                             msg = $"当前已缴费：{cachePayMoney.ToString("F2")}元，还需缴费{chargeMoney.ToString("F2")}元";
                             resultCode = 2;
                         }
                         else
                         {
                             result.payres.cashrobotamount = cachePayMoney;
                             result.payres.payedamount = 0;
                             if (payMoney > SSJE)
                             {
                                 isChange = true;
                                 changeAmount = payMoney - SSJE;
                                 result.payres.zlamount = changeAmount;
                                 var payOrderNo = "PO" + Utils.CreateNumber + AppBasicCache.SentryHostInfo.Parking_Key;

                                 try
                                 {
                                     //开始找零
                                     var tuple = RedPacketGiveCharge(changeAmount, cachePayMoney, orderno, payOrderNo);
                                     if (tuple.Item1)
                                     {
                                         await PasswayConfirmReleaseUtil.PayPass(result, result.resorder.resOut?.onenter == 0, "自助现金支付成功",
                                             payOrderNo, EnumPayType.owinDeviceCashMoney.ToString(), AppBasicCache.CurrentAdmins, true, tuple.Item3, RobotCommandType.licensedCashPayment);
                                         return;
                                     }
                                 }
                                 catch (Exception ex)
                                 {
                                     LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "纸币缴费红包找零异常：" + ex.ToString());
                                 }

                                 resultCode = 3;
                                 msg = "纸币缴费红包找零失败！";
                             }
                             else
                             {
                                 await PasswayConfirmReleaseUtil.PayPass(result, result.resorder.resOut?.onenter == 0, "自助现金支付成功",
                                     "", EnumPayType.owinDeviceCashMoney.ToString(), AppBasicCache.CurrentAdmins);
                                 return;
                             }
                         }

                         result.payres.cashrobotamount = cachePayMoney;
                         result.payres.payedamount = chargeMoney;

                         if (result.payres.cashrobotamount > 0 && result.payres.payedamount <= 0)
                         {
                             result.unpaidresult = null;
                         }

                         #region 通知岗亭

                         WebSocketUtil.SendWS(result);

                         #endregion

                         #region 语音播报

                         var billQrcode = AppBasicCache.CurrentSysConfigContent.SiteDomain_Weixin + $"/TcisByUrl/Openfp?parkingKey={AppBasicCache.SentryHostInfo.Parking_Key}&orderNo={orderno}";
                         var send = new RobotSendPacket();
                         send.resultCode = resultCode;
                         send.msg = msg;
                         send.type = resultCode == 2 ? RobotCommandType.licensedMultipleSheetsCashPayment : RobotCommandType.licensedCashPayment;
                         send.data = new
                         {
                             cph = carno, //车牌号
                             payID = orderno, //纸币机塞现金时回复
                             carType, //车牌类型
                             parkDuration, //停车时间
                             enterParkTime, //入场时间
                             outParkTime, //出场时间
                             cachePayMoney, //缓存金额,已经收费金额
                             chargeMoney, //当前收费金额
                             SSJE, //实收金额
                             YSJE = chargeMoney, //应付金额
                             isChange, //表示是否需要找零
                             changeAmount //找零金额,只有红包才存在
                         };
                         await ChannelMachineController.SendDataAsync(model.IPAddress, send);

                         #endregion
                     }
                 }
             }
             catch (Exception ex)
             {
                 LogManagementMap.WriteToFileException(ex, $"现金支付处理异常{receivePacket}！");
             }
         });
    }

    #endregion

    #region 智慧道闸处理方法

    /// <summary>
    /// 智慧道闸控制板异常信息上报
    /// </summary>
    /// <param name="barrier"><see cref="BarrierOfY312"/>实体类</param>
    /// <param name="packet"><see cref="BarrierOfY312Packet"/>实体类</param>
    [Command(BarrierOfY312Command.EventAbnormal)]
    private void BarrierEventAbnormal(BarrierOfY312 barrier, BarrierOfY312Packet packet)
    {
        try
        {
            var reqPush = JObject.Parse(packet.ToString());
            var device = DeviceCommonUtil.GetPasswayMainDevice(barrier.Model.PasswayNo);
            if (device != null)
            {
                var sType = Convert.ToString(reqPush["EventAbnormal"]?["typeNo"]);

                var remark = "道闸控制板异常事件记录";
                var fv = reqPush["EventAbnormal"]?.Children().FirstOrDefault(m => m is JProperty p && p.Name == "msgData");
                if (fv != null && fv.HasValues)
                {
                    remark = Convert.ToString(((JProperty)fv).Value);
                }

                //警报异常状态
                var itype = 2; //未定义异常
                switch (sType)
                {
                    case "001":
                        itype = 5;
                        break; //遥控开闸
                    case "002":
                        itype = 6;
                        break; //遥控关闸
                    case "003":
                        itype = 7;
                        break; //长时间不落杆
                    case "004":
                        itype = 9;
                        break; //长时间停车
                    case "006":
                        itype = 8;
                        break; //道闸通信异常
                }

                var cNo = Utils.CreateNumberWith();
                var Url = ControlEvent.CreateDir(cNo);
                var isCache = ControlEvent.GetCacheSendVideo(ref Url, DateTime.Now);

                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"异常事件：编号[{cNo}][{device?.Device_IP}][{itype}][{remark}]");

                var tempImage = CameraImageHelper.ImageSaveHSPathBig(cNo, device?.Device_SentryHostNo, true);
                var imgUrl = LPRTools.GetSentryHostImg(tempImage);

                var isEzvizDevice = false;
                var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(barrier.Model.PasswayNo) && m.Value.Drive_Code == "10103");
                if (ezvizDevice.Value != null)
                {
                    isEzvizDevice = EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "跟车记录");
                    if (!isEzvizDevice)
                    {
                        Url = "";
                    }
                }
                if (!isEzvizDevice)
                {
                    var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(barrier.Model.PasswayNo) && m.Value.Drive_Code == "10123");
                    if (vizCloudDevice.Value != null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, "开始组装viz云视频回放标识URL");

                        // 组装臻云回放标识URL，不调用API
                        var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                        var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                        // 提取设备序列号（如果是新格式，取除最后一个下划线后的所有内容）
                        string deviceSn;
                        if (vizCloudDevice.Value.Device_No.Contains("_"))
                        {
                            var parts = vizCloudDevice.Value.Device_No.Split('_');
                            deviceSn = string.Join("_", parts.Take(parts.Length - 1));
                        }
                        else
                        {
                            deviceSn = vizCloudDevice.Value.Device_No;
                        }

                        // 使用安全的封装方法组装标识URL（不包含敏感信息）
                        var playbackUrl = TcpConnPools.SceneVideo.VziCloud.VziCloudService.BuildPlaybackIdentifierUrl(
                            deviceSn,
                            vizCloudDevice.Value.Device_VideoChannelNo,
                            startTime,
                            endTime,
                            cNo);
                        Url = playbackUrl;
                        isEzvizDevice = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"组装viz云视频回放安全标识URL完成:{playbackUrl}");
                    }
                }

                IDevice deviceScene = null;
                if (!isEzvizDevice)
                {
                    deviceScene = SceneVideoControler.GetDevice(barrier.Model.PasswayNo, DeviceType.SceneVideo01);
                }
                var follModel = new apiFollow
                {
                    no = cNo,
                    bigimg = "",
                    cameraip = device.Device_IP,
                    camerano = device.Device_No,
                    content = reqPush.ToString(),
                    remark = remark,
                    type = itype,
                    time = DateTime.Now,
                    video = Url
                };
                var controlModel = BLL.ControlEvent.Create(follModel, out var errmsg);
                if (controlModel == null)
                {
                    BLL.SystemLogs.AddLog(null, "道闸事件", "道闸异常信息上报记录生成失败，" + errmsg + $",事件信息：{reqPush.ToString()}");
                    return;
                }

                var res = BLL.BaseBLL._Insert(controlModel);
                if (res > 0)
                {
                    var area = BLL.ParkArea.GetEntity(controlModel.ControlEvent_ParkAreaNo);
                    //内场区域的跟车不发送到智慧停车平台
                    if (area != null && area.ParkArea_Type != 1)
                        BLL.PushEvent.CarFollowAdd(AppBasicCache.GetParking?.Parking_Key, controlModel);


                    //FlowBindSave(cNo, controlModel, reqPush.ToString());

                    if (!isEzvizDevice)
                    {
                        if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC)
                        {
                            if (!isCache)
                            {
                                _ = deviceSC.SendRecordVideoAsync(controlModel.ControlEvent_No);
                            }

                            _ = deviceSC.SendCameraPhotoAsync(controlModel.ControlEvent_No);
                        }
                        else
                        {
                            _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "道闸事件抓拍图地址：" + tempImage);
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"道闸事件事件 车道{barrier.Model.PasswayNo}未找到场景相机，或场景相机设备未连接");
                        }
                    }
                    else
                    {
                        _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "道闸事件抓拍图地址：" + tempImage);
                    }
                }
                else
                {
                    BLL.SystemLogs.AddLog(null, "道闸事件", "道闸异常信息上报记录保存失败，" + errmsg + $",事件信息：{reqPush.ToString()}");
                }
            }
            else
            {
                BLL.SystemLogs.AddLog(null, "道闸事件", "道闸异常信息上报记录生成失败，未找到设备信息，事件信息：" + reqPush.ToString());
            }
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(null, "道闸事件", "道闸事件异常，" + ex.Message + $",事件信息：{packet?.ToString()}");
        }
    }

    /// <summary>
    /// 智慧道闸控制板跟车信息上报
    /// </summary>
    /// <param name="barrier"><see cref="BarrierOfY312"/>实体类</param>
    /// <param name="packet"><see cref="BarrierOfY312Packet"/>实体类</param>
    [Command(BarrierOfY312Command.EventFollowing)]
    private void BarrierEventFollowing(BarrierOfY312 barrier, BarrierOfY312Packet packet)
    {
        try
        {
            var reqPush = JObject.Parse(packet.ToString());
            var device = DeviceCommonUtil.GetPasswayMainDevice(barrier.Model.PasswayNo);
            if (device != null)
            {
                var cNo = Utils.CreateNumberWith();
                var Url = ControlEvent.CreateDir(cNo);
                var isCache = ControlEvent.GetCacheSendVideo(ref Url, DateTime.Now);

                var tempImage = CameraImageHelper.ImageSaveHSPathBig(cNo, device?.Device_SentryHostNo, true);
                var imgUrl = LPRTools.GetSentryHostImg(tempImage);

                var isEzvizDevice = false;
                var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Drive_Code == "10103" && m.Value.Device_PasswayNo.Contains(barrier.Model.PasswayNo));
                if (ezvizDevice.Value != null)
                {
                    isEzvizDevice = EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "跟车记录");

                    if (!isEzvizDevice)
                    {
                        Url = "";
                    }
                }
                if (!isEzvizDevice)
                {
                    var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(barrier.Model.PasswayNo) && m.Value.Drive_Code == "10123");
                    if (vizCloudDevice.Value != null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, "开始组装viz云视频回放标识URL");

                        // 组装臻云回放标识URL，不调用API
                        var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                        var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                        // 提取设备序列号（如果是新格式，取除最后一个下划线后的所有内容）
                        string deviceSn;
                        if (vizCloudDevice.Value.Device_No.Contains("_"))
                        {
                            var parts = vizCloudDevice.Value.Device_No.Split('_');
                            deviceSn = string.Join("_", parts.Take(parts.Length - 1));
                        }
                        else
                        {
                            deviceSn = vizCloudDevice.Value.Device_No;
                        }

                        // 使用安全的封装方法组装标识URL（不包含敏感信息）
                        var playbackUrl = TcpConnPools.SceneVideo.VziCloud.VziCloudService.BuildPlaybackIdentifierUrl(
                            deviceSn,
                            vizCloudDevice.Value.Device_VideoChannelNo,
                            startTime,
                            endTime,
                            cNo);
                        Url = playbackUrl;
                        isEzvizDevice = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"组装viz云视频回放安全标识URL完成:{playbackUrl}");
                    }
                }

                IDevice deviceScene = null;
                if (isEzvizDevice)
                {
                    imgUrl = "";
                }
                else
                {
                    deviceScene = SceneVideoControler.GetDevice(barrier.Model.PasswayNo, DeviceType.SceneVideo01);
                    if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC) imgUrl = "";
                }

                var remark = "道闸控制板车辆跟车事件记录";
                var fv = reqPush["EventFollowing"]?.Children().FirstOrDefault(m => m is JProperty p && p.Name == "msgData");
                if (fv != null && fv.HasValues)
                {
                    remark = Convert.ToString(((JProperty)fv).Value);
                }

                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"跟车事件：编号[{cNo}][{device?.Device_IP}][{remark}]");

                var follModel = new apiFollow
                {
                    no = cNo,
                    bigimg = "",
                    cameraip = device.Device_IP,
                    camerano = device.Device_No,
                    content = reqPush.ToString(),
                    remark = remark,
                    type = 1,
                    time = DateTime.Now,
                    video = Url
                };
                var controlModel = BLL.ControlEvent.Create(follModel, out var errmsg);
                if (controlModel == null)
                {
                    BLL.SystemLogs.AddLog(null, "跟车事件", "跟车记录生成失败，" + errmsg + $",事件信息：{reqPush.ToString()}");
                    return;
                }

                var res = BLL.BaseBLL._Insert(controlModel);
                if (res > 0)
                {
                    var area = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, controlModel.ControlEvent_ParkAreaNo);
                    //内场区域的跟车不发送到智慧停车平台
                    if (area != null && area.ParkArea_Type != 1)
                        BLL.PushEvent.CarFollowAdd(AppBasicCache.GetParking?.Parking_Key, controlModel);

                    FlowBindSave(device?.Device_PasswayNo, controlModel, reqPush.ToString());

                    if (!isEzvizDevice)
                    {
                        if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC)
                        {
                            if (!isCache)
                            {
                                _ = deviceSC.SendRecordVideoAsync(controlModel.ControlEvent_No);
                            }

                            _ = deviceSC.SendCameraPhotoAsync(controlModel.ControlEvent_No);
                        }
                    }

                }
            }
            else
            {
                BLL.SystemLogs.AddLog(null, "跟车事件", "跟车记录生成失败，未找到设备信息，事件信息：" + reqPush.ToString());
            }
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(null, "跟车事件", "跟车事件异常，" + ex.Message + $",事件信息：{packet?.ToString()}");
        }
    }

    /// <summary>
    ///  跟车自动追缴
    /// </summary>
    /// <param name="PasswayNo"></param>
    /// <param name="controlModel"></param>
    /// <param name="content"></param>

    public static void FlowBindSave(string PasswayNo, Model.ControlEvent controlModel, string content)
    {
        if (AppBasicCache.GetPolicyPark?.PolicyPark_AutoChase == 1)
        {
            //查找出口弹窗信息
            var confirmRelease = ConfirmRelease.Results.LastOrDefault(m => m.Key == PasswayNo);
            if (confirmRelease.Value != null)
            {
                _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                    {
                        try
                        {
                            //判断车道属于出口
                            if (confirmRelease.Value.passres?.gate == 0 && confirmRelease.Value.resorder?.resOut != null)
                            {
                                var orderNo = confirmRelease.Value.resorder?.resOut?.parkorder?.ParkOrder_No ?? confirmRelease.Value.passres?.parkorderno;
                                if (!string.IsNullOrWhiteSpace(orderNo))
                                {
                                    var parkOrder = BLL.ParkOrder.GetEntity(orderNo);
                                    if (parkOrder != null && parkOrder.ParkOrder_StatusNo == 200)
                                    {
                                        //跟车事件绑定停车订单
                                        var ret = BLL.ControlEvent.bindOrder(ref controlModel, orderNo, out var order, out var detail, out var errmsg2);
                                        if (ret)
                                        {
                                            var po = order.Item1 ?? new ParkOrder();
                                            var payData = carparking.Charge.Calc.GetChargeByCar(order.Item1, controlModel.ControlEvent_Time, null, null, false, "", "", null, order.Item2, true);
                                            controlModel.ControlEvent_Money = payData?.orderamount ?? 0;
                                            var res2 = BLL.ControlEvent.Handle(controlModel, order, out var errmsg3);
                                            if (res2 > 0)
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"跟车事件绑定订单成功：{orderNo}");
                                                //清除弹窗缓存
                                                PasswayConfirmReleaseUtil.RemoveResult(PasswayNo);
                                                //关闭弹窗
                                                WebSocketUtil.SendWSConfirmHandle(1, confirmRelease.Value);
                                                //通知岗亭
                                                WebSocketUtil.SendWSTip(parkOrder.ParkOrder_CarNo, "", PasswayNo, AppBasicCache.GetElement(AppBasicCache.GetAllPassway, PasswayNo)?.Passway_Name,
                                                    $"跟车出场，已关联追缴订单", DateTimeHelper.GetNowTime().ToString("G"));
                                                //计费详情
                                                BLL.CommonBLL.CreateCalcDetail(payData, order.Item1);

                                                var ds = new PushResultParse.ControlEvent()
                                                {
                                                    Item1 = controlModel,
                                                    Item2 = order.Item1,
                                                    Item3 = order.Item2
                                                };

                                                BLL.PushEvent.CarFollowUpdate(
                                                    AppBasicCache.GetParking?.Parking_Key
                                                    , controlModel.ControlEvent_No
                                                    , controlModel.ControlEvent_CarNo
                                                    , controlModel.ControlEvent_ParkOrderNo
                                                    , controlModel.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                                    , controlModel.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                                    , BLL.CarFollowUpdate_Status.BindOrder
                                                    , ""
                                                    , controlModel.ControlEvent_BigImg ?? ""
                                                    , controlModel.ControlEvent_Video ?? ""
                                                    , controlModel.ControlEvent_Remark
                                                    , controlModel.ControlEvent_Money.Value.ToString());
                                            }
                                            else
                                            {
                                                BLL.SystemLogs.AddLog(null, "跟车事件", "跟车事件绑定停车订单失败，" + errmsg3 + $",事件内容：" + content);
                                            }
                                        }
                                        else
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"跟车事件绑定停车订单失败，{errmsg2},事件内容：" + content);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            BLL.SystemLogs.AddLog(null, "跟车自动追缴", "跟车自动追缴异常: " + ex.Message + $",事件内容：" + content);
                        }

                        return Task.CompletedTask;

                    });
            }
        }
    }

    /// <summary>
    /// 智慧道闸道闸控制板倒车
    /// </summary>
    /// <param name="barrier"><see cref="BarrierOfY312"/>实体类</param>
    /// <param name="packet"><see cref="BarrierOfY312Packet"/>实体类</param>
    [Command(BarrierOfY312Command.EventAstern)]
    private void BarrierEventAstern(BarrierOfY312 barrier, BarrierOfY312Packet packet)
    {
        var sPassNo = "";
        try
        {
            var reqPush = JObject.Parse(packet.ToString());
            sPassNo = barrier.Model.PasswayNo;
            if (AppBasicCache.GetSentryPasswayDic.TryGetValue(sPassNo, out var pass))
            {
                if (pass.Passway_IsBackCar == 1)
                {
                    var sInNo = Convert.ToString(reqPush["EventAstern"]?["orderNoIn"]);
                    var sOutNo = Convert.ToString(reqPush["EventAstern"]?["orderNoOut"]);
                    var sCarNo = Convert.ToString(reqPush["EventAstern"]?["carNo"]);
                    var sOrderNo = string.IsNullOrWhiteSpace(sInNo) ? sOutNo : sInNo;
                    var key = string.Empty;

                    // 优先使用车牌号生成缓存键（与存储时保持一致）
                    if (!string.IsNullOrWhiteSpace(sCarNo))
                    {
                        key = BarrierDeviceUtilsl.GenerateBackCarCacheKey(sPassNo, sCarNo);
                        if (!string.IsNullOrWhiteSpace(key))
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录使用车牌号生成缓存键：{key}");
                        }
                    }
                    // 如果没有车牌号，尝试使用订单号后缀
                    if (string.IsNullOrWhiteSpace(key) && !string.IsNullOrWhiteSpace(sOrderNo))
                    {
                        var nos = sOrderNo.Split('-');
                        if (nos.Length > 1 && !string.IsNullOrWhiteSpace(nos.Last()))
                        {
                            key = $"{sPassNo}{nos.Last()}";
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录使用订单号生成缓存键：{key}");
                        }
                    }

                    if (string.IsNullOrWhiteSpace(key))
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"接收倒车记录(缓存主键为空，跳过处理)：{reqPush}");
                        return;
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"接收倒车记录：{reqPush}，缓存键：{key}");

                    ParkOrder innerParkOrder = null;
                    if (AppBasicCache.GetMemoryCache.TryGetValue(key, out var temp1) && temp1 != null && temp1 is ParkOrder temp2)
                    {
                        innerParkOrder = temp2;
                        AppBasicCache.GetMemoryCache.Remove(key);
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录从缓存中找到订单：{temp2.ParkOrder_No}");
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录缓存中未找到数据，缓存键：{key}，temp1值：{JsonConvert.SerializeObject(temp1)}");

                        // 如果缓存中没有找到，尝试从数据库查询
                        if (!string.IsNullOrWhiteSpace(sCarNo))
                        {
                            innerParkOrder = BLL.ParkOrder._GetEntityByWhere(new ParkOrder { }, "*", $"ParkOrder_CarNo='{sCarNo}' and ParkOrder_StatusNo=200 ORDER BY ParkOrder_EnterTime DESC");
                            if (innerParkOrder != null)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录从数据库查询到订单：{innerParkOrder.ParkOrder_No}");
                            }
                        }
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车缓存数据处理结果 - 缓存数据：{JsonConvert.SerializeObject(temp1)}，最终订单：{innerParkOrder?.ParkOrder_No ?? "null"}");

                    //同进同出
                    if (innerParkOrder != null)
                    {
                        var device = DeviceCommonUtil.GetPasswayMainDevice(sPassNo);
                        if (device != null)
                        {
                            var isCancel = false;

                            var policyway = AppBasicCache.GetSentryPolicyPasswayDic.FirstOrDefault(x => x.Value.PolicyPassway_PasswayNo == sPassNo);

                            #region 取消车道弹框
                            if (policyway.Value != null && policyway.Value.PolicyPassway_ClosePasswayWin == 1 && ConfirmRelease.Results.TryGetValue(sPassNo, out var oldData))
                            {
                                var orderno = oldData.resorder?.resOut?.parkorder?.ParkOrder_No ?? oldData.passres?.parkorderno;
                                if (orderno == sOrderNo)
                                {
                                    isCancel = PasswayConfirmReleaseUtil.ConfirmCancelRelease(sPassNo, sOrderNo, "", false);
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录取消车道弹框成功：{key}");
                                }
                            }
                            #endregion

                            if (!isCancel)
                            {
                                var imgUrl = "";
                                //是否抓拍图片
                                var isCap = false;
                                var no = Utils.CreateNumberWith("DC");
                                var tempImage = CameraImageHelper.ImageSaveHSPathBig(no, device?.Device_SentryHostNo, true);
                                if (!string.IsNullOrWhiteSpace(innerParkOrder.ParkOrder_OutImgPath))
                                {
                                    imgUrl = innerParkOrder.ParkOrder_OutImgPath;
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录已有出场图片{imgUrl}");
                                }
                                else if (!string.IsNullOrWhiteSpace(innerParkOrder.ParkOrder_EnterImgPath))
                                {
                                    imgUrl = innerParkOrder.ParkOrder_EnterImgPath;
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录已有入场图片{imgUrl}");
                                }
                                else
                                {
                                    imgUrl = LPRTools.GetSentryHostImg(tempImage);
                                    isCap = true;
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录抓拍图片{imgUrl}");
                                }
                                var paramData = new apiBackCar
                                {
                                    cameraip = device.Device_IP,
                                    camerano = device.Device_No,
                                    count = 1,
                                    orderno = sOrderNo,
                                    imgbig = imgUrl,
                                    time = $"{DateTime.Now:G}",
                                    carno = innerParkOrder.ParkOrder_CarNo,
                                    no = no
                                };
                                ResBody res;

                                var isEzvizDevice = false;
                                var Url = "";

                                var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(barrier.Model.PasswayNo) && m.Value.Drive_Code == "10103");
                                if (ezvizDevice.Value != null)
                                {
                                    isEzvizDevice = EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "倒车记录");
                                    if (isEzvizDevice)
                                    {
                                        paramData.video = Url;
                                    }
                                }

                                if (!isEzvizDevice)
                                {
                                    var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(barrier.Model.PasswayNo) && m.Value.Drive_Code == "10123");
                                    if (vizCloudDevice.Value != null)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, "开始组装viz云视频回放标识URL");

                                        // 组装臻云回放标识URL，不调用API
                                        var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                                        var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                                        // 提取设备序列号（如果是新格式，取除最后一个下划线后的所有内容）
                                        string deviceSn;
                                        if (vizCloudDevice.Value.Device_No.Contains("_"))
                                        {
                                            var parts = vizCloudDevice.Value.Device_No.Split('_');
                                            deviceSn = string.Join("_", parts.Take(parts.Length - 1));
                                        }
                                        else
                                        {
                                            deviceSn = vizCloudDevice.Value.Device_No;
                                        }

                                        // 使用安全的封装方法组装标识URL（不包含敏感信息）
                                        var playbackUrl = TcpConnPools.SceneVideo.VziCloud.VziCloudService.BuildPlaybackIdentifierUrl(
                                            deviceSn,
                                            vizCloudDevice.Value.Device_VideoChannelNo,
                                            startTime,
                                            endTime,
                                            paramData.no);
                                        Url = playbackUrl;
                                        paramData.video = Url;
                                        isEzvizDevice = true;
                                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"组装viz云视频回放安全标识URL完成:{playbackUrl}");
                                    }
                                }

                                var isCache = false;
                                if (policyway.Value != null && policyway.Value.PolicyPassway_BackCarMode == 1)
                                {
                                    res = ParkApi.CloseBackCarOrder(AppBasicCache.SentryHostInfo.SentryHost_ParkNo, paramData);
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"上报倒车事件:{TyziTools.Json.ToString(paramData)}");
                                    res = ParkApi.BackCarEvent(AppBasicCache.SentryHostInfo.SentryHost_ParkNo, paramData, out isCache, isEzvizDevice);
                                }

                                IDevice deviceScene = null;
                                if (!isEzvizDevice)
                                {
                                    deviceScene = SceneVideoControler.GetDevice(barrier.Model.PasswayNo, DeviceType.SceneVideo01);
                                }

                                if (res.success)
                                {
                                    var stempNo = string.Empty;
                                    if (!string.IsNullOrWhiteSpace(res.data))
                                    {
                                        var pairs = JObject.Parse(res.data);
                                        stempNo = pairs.ContainsKey("ControlEvent_No") ? Convert.ToString(pairs["ControlEvent_No"]) : string.Empty;
                                        if (string.IsNullOrEmpty(stempNo)) stempNo = pairs.ContainsKey("BackCar_No") ? Convert.ToString(pairs["BackCar_No"]) : string.Empty;
                                    }

                                    if (!string.IsNullOrWhiteSpace(stempNo))
                                    {
                                        #region 倒车场景相机抓拍

                                        if (!isEzvizDevice)
                                        {
                                            if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC)
                                            {
                                                if (!isCache)
                                                {
                                                    _ = deviceSC.SendRecordVideoAsync(stempNo);
                                                }

                                                _ = deviceSC.SendCameraPhotoAsync(stempNo);
                                            }
                                            else
                                            {
                                                if (isCap)
                                                {
                                                    _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                                                }
                                            }
                                        }
                                        else if (isCap)
                                        {
                                            _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                                        }
                                        #endregion

                                        if (!string.IsNullOrWhiteSpace(sOutNo))
                                        {
                                            WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                $"车辆{innerParkOrder.ParkOrder_CarNo}出场倒车,请注意确认信息！", DateTime.Now.ToString("G"));
                                        }
                                        else
                                        {
                                            WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                                $"车辆{innerParkOrder.ParkOrder_CarNo}入场倒车,请注意确认信息！", DateTime.Now.ToString("G"));
                                        }
                                    }
                                    else
                                    {
                                        WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                            $"{res.errmsg}", DateTime.Now.ToString("G"));
                                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车记录处理返回：{sOrderNo}///{JsonConvert.SerializeObject(res)}");
                                    }
                                }
                                else
                                {
                                    if (!string.IsNullOrWhiteSpace(sOutNo))
                                    {
                                        WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                            $"车辆{innerParkOrder.ParkOrder_CarNo}出场倒车,确认窗口关闭！", DateTime.Now.ToString("G"));
                                    }
                                    else
                                    {
                                        WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                            $"车辆{innerParkOrder.ParkOrder_CarNo}入场倒车，确认窗口关闭！", DateTime.Now.ToString("G"));
                                    }
                                }
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"{barrier.Model.PasswayNo}车道未关联主相机！");
                        }
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"{barrier.Model.PasswayNo}车道未找到缓存的倒车订单！");
                    }
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"{sPassNo}编号的车道未启用倒车功能！");
                }
            }
            else
            {
                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"倒车处理失败，{sPassNo}编号的车道未找到缓存记录！");
            }
        }
        catch (Exception ex)
        {

            BLL.SystemLogs.AddLog(null, "倒车事件", "倒车事件异常，" + ex.Message + $",车道编号：{sPassNo},事件信息：{packet?.ToString()}");
        }
    }

    #endregion

    #region 相机事件处理方法

    /// <summary>
    /// 相机倒车事件处理
    /// </summary>
    /// <param name="cameraModel">相机设备模型</param>
    /// <param name="eventData">事件数据</param>
    private void CameraEventAstern(CameraModel cameraModel, EventData eventData)
    {
        var sPassNo = "";
        try
        {
            sPassNo = cameraModel.PasswayNo;
            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]处理相机倒车事件，车道：{sPassNo}，车牌：{eventData.License}");

            if (AppBasicCache.GetSentryPasswayDic.TryGetValue(sPassNo, out var pass))
            {
                if (pass.Passway_IsBackCar == 1)
                {
                    var sCarNo = eventData.License;
                    var key = string.Empty;

                    if (!string.IsNullOrWhiteSpace(sCarNo))
                    {
                        key = BarrierDevice.BarrierDeviceUtilsl.GenerateBackCarCacheKey(sPassNo, sCarNo);
                    }

                    if (string.IsNullOrWhiteSpace(key))
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录(缓存主键为空，跳过处理)：车牌{sCarNo}");
                        return;
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]接收相机倒车记录：车牌{sCarNo}，缓存键{key}");

                    ParkOrder innerParkOrder = null;
                    if (AppBasicCache.GetMemoryCache.TryGetValue(key, out var temp1) && temp1 != null && temp1 is ParkOrder temp2)
                    {
                        innerParkOrder = temp2;
                        AppBasicCache.GetMemoryCache.Remove(key);
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录从缓存中找到订单：{temp2.ParkOrder_No}");
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录缓存中未找到数据，缓存键：{key}，temp1值：{JsonConvert.SerializeObject(temp1)}");

                        // 如果缓存中没有找到，尝试从数据库查询
                        if (!string.IsNullOrWhiteSpace(sCarNo))
                        {
                            innerParkOrder = BLL.ParkOrder._GetEntityByWhere(new ParkOrder { }, "*", $"ParkOrder_CarNo='{sCarNo}' and ParkOrder_StatusNo=200 ORDER BY ParkOrder_EnterTime DESC");
                            if (innerParkOrder != null)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录从数据库查询到订单：{innerParkOrder.ParkOrder_No}");
                            }
                        }
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车缓存数据处理结果 - 缓存数据：{JsonConvert.SerializeObject(temp1)}，最终订单：{innerParkOrder?.ParkOrder_No ?? "null"}");

                    //同进同出
                    if (innerParkOrder != null)
                    {
                        var device = DeviceCommonUtil.GetPasswayMainDevice(sPassNo);
                        if (device != null)
                        {
                            var isCancel = false;
                            var policyway = AppBasicCache.GetSentryPolicyPasswayDic.FirstOrDefault(x => x.Value.PolicyPassway_PasswayNo == sPassNo);

                            #region 取消车道弹框
                            if (policyway.Value != null && policyway.Value.PolicyPassway_ClosePasswayWin == 1 && ConfirmRelease.Results.TryGetValue(sPassNo, out var oldData))
                            {
                                var orderno = oldData.resorder?.resOut?.parkorder?.ParkOrder_No ?? oldData.passres?.parkorderno;
                                if (orderno == innerParkOrder.ParkOrder_No)
                                {
                                    isCancel = PasswayConfirmReleaseUtil.ConfirmCancelRelease(sPassNo, innerParkOrder.ParkOrder_No, "", false);
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录取消车道弹框成功：{key}");
                                }
                            }
                            #endregion

                            if (!isCancel)
                            {
                                var imgUrl = "";
                                //是否抓拍图片
                                var isCap = false;
                                var no = Utils.CreateNumberWith("DC");
                                var tempImage = CameraImageHelper.ImageSaveHSPathBig(no, device?.Device_SentryHostNo, true);
                                if (!string.IsNullOrWhiteSpace(innerParkOrder.ParkOrder_OutImgPath))
                                {
                                    imgUrl = innerParkOrder.ParkOrder_OutImgPath;
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录已有出场图片{imgUrl}");
                                }
                                else if (!string.IsNullOrWhiteSpace(innerParkOrder.ParkOrder_EnterImgPath))
                                {
                                    imgUrl = innerParkOrder.ParkOrder_EnterImgPath;
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录已有入场图片{imgUrl}");
                                }
                                else
                                {
                                    imgUrl = LPRTools.GetSentryHostImg(tempImage);
                                    isCap = true;
                                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录抓拍图片{imgUrl}");
                                }
                                var paramData = new apiBackCar
                                {
                                    cameraip = device.Device_IP,
                                    camerano = device.Device_No,
                                    count = 1,
                                    orderno = innerParkOrder.ParkOrder_No,
                                    imgbig = imgUrl,
                                    time = $"{DateTime.Now:G}",
                                    carno = innerParkOrder.ParkOrder_CarNo,
                                    no = no
                                };
                                ResBody res;

                                var isEzvizDevice = false;
                                var Url = "";

                                var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(sPassNo) && m.Value.Drive_Code == "10103");
                                if (ezvizDevice.Value != null)
                                {
                                    isEzvizDevice = EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "相机倒车记录");
                                    if (isEzvizDevice)
                                    {
                                        paramData.video = Url;
                                    }
                                }

                                if (!isEzvizDevice)
                                {
                                    var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(sPassNo) && m.Value.Drive_Code == "10123");
                                    if (vizCloudDevice.Value != null)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"[{cameraModel.IPAddress}]开始组装YM01云视频回放标识URL");

                                        // 组装YM01回放标识URL，不调用API
                                        var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                                        var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                                        // 提取设备序列号（如果是新格式，取除最后一个下划线后的所有内容）
                                        string deviceSn;
                                        if (vizCloudDevice.Value.Device_No.Contains("_"))
                                        {
                                            var parts = vizCloudDevice.Value.Device_No.Split('_');
                                            deviceSn = string.Join("_", parts.Take(parts.Length - 1));
                                        }
                                        else
                                        {
                                            deviceSn = vizCloudDevice.Value.Device_No;
                                        }

                                        // 使用安全的封装方法组装标识URL（不包含敏感信息）
                                        var playbackUrl = TcpConnPools.SceneVideo.VziCloud.VziCloudService.BuildPlaybackIdentifierUrl(
                                            deviceSn,
                                            vizCloudDevice.Value.Device_VideoChannelNo,
                                            startTime,
                                            endTime,
                                            paramData.no);
                                        Url = playbackUrl;
                                        paramData.video = Url;
                                        isEzvizDevice = true;
                                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"[{cameraModel.IPAddress}]组装viz云视频回放安全标识URL完成:{playbackUrl}");
                                    }
                                }

                                var isCache = false;
                                if (policyway.Value != null && policyway.Value.PolicyPassway_BackCarMode == 1)
                                {
                                    res = ParkApi.CloseBackCarOrder(AppBasicCache.SentryHostInfo.SentryHost_ParkNo, paramData);
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"[{cameraModel.IPAddress}]上报相机倒车事件:{TyziTools.Json.ToString(paramData)}");
                                    res = ParkApi.BackCarEvent(AppBasicCache.SentryHostInfo.SentryHost_ParkNo, paramData, out isCache, isEzvizDevice);
                                }

                                IDevice deviceScene = null;
                                if (!isEzvizDevice)
                                {
                                    deviceScene = SceneVideoControler.GetDevice(sPassNo, DeviceType.SceneVideo01);
                                }

                                if (res.success)
                                {
                                    var stempNo = string.Empty;
                                    if (!string.IsNullOrWhiteSpace(res.data))
                                    {
                                        var pairs = JObject.Parse(res.data);
                                        stempNo = pairs.ContainsKey("ControlEvent_No") ? Convert.ToString(pairs["ControlEvent_No"]) : string.Empty;
                                        if (string.IsNullOrEmpty(stempNo)) stempNo = pairs.ContainsKey("BackCar_No") ? Convert.ToString(pairs["BackCar_No"]) : string.Empty;
                                    }

                                    if (!string.IsNullOrWhiteSpace(stempNo))
                                    {
                                        #region 相机倒车场景相机抓拍

                                        if (!isEzvizDevice)
                                        {
                                            if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC)
                                            {
                                                if (!isCache)
                                                {
                                                    _ = deviceSC.SendRecordVideoAsync(stempNo);
                                                }

                                                _ = deviceSC.SendCameraPhotoAsync(stempNo);
                                            }
                                            else
                                            {
                                                if (isCap)
                                                {
                                                    _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                                                }
                                            }
                                        }
                                        else if (isCap)
                                        {
                                            _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                                        }
                                        #endregion

                                        WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                            $"车辆{innerParkOrder.ParkOrder_CarNo}相机倒车事件,请注意确认信息！", DateTime.Now.ToString("G"));
                                    }
                                    else
                                    {
                                        WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                            $"{res.errmsg}", DateTime.Now.ToString("G"));
                                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车记录处理返回：{innerParkOrder.ParkOrder_No}///{JsonConvert.SerializeObject(res)}");
                                    }
                                }
                                else
                                {
                                    WebSocketUtil.SendWSTip(innerParkOrder.ParkOrder_CarNo, "", sPassNo, "",
                                        $"车辆{innerParkOrder.ParkOrder_CarNo}相机倒车事件，确认窗口关闭！", DateTime.Now.ToString("G"));
                                }

                                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车事件处理完成，车牌：{sCarNo}");
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车事件处理失败，未找到设备信息，车道：{sPassNo}");
                        }
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车事件处理失败，未找到停车订单，车牌：{sCarNo}");
                    }
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]车道{sPassNo}未启用倒车功能");
                }
            }
            else
            {
                LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机倒车事件处理失败，未找到车道信息：{sPassNo}");
            }
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(null, "相机倒车事件", $"[{cameraModel.IPAddress}]相机倒车事件异常，" + ex.Message + $"，车道：{sPassNo}，车牌：{eventData.License}");
        }
    }

    /// <summary>
    /// 相机跟车事件处理
    /// </summary>
    /// <param name="cameraModel">相机设备模型</param>
    /// <param name="eventData">事件数据</param>
    private void CameraEventFollowing(CameraModel cameraModel, EventData eventData)
    {
        try
        {
            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]处理相机跟车事件，车道：{cameraModel.PasswayNo}，车牌：{eventData.License}");

            var device = DeviceCommonUtil.GetPasswayMainDevice(cameraModel.PasswayNo);
            if (device != null)
            {
                var cNo = Utils.CreateNumberWith();
                var Url = ControlEvent.CreateDir(cNo);
                var isCache = ControlEvent.GetCacheSendVideo(ref Url, DateTime.Now);

                var tempImage = CameraImageHelper.ImageSaveHSPathBig(cNo, device?.Device_SentryHostNo, true);
                var imgUrl = LPRTools.GetSentryHostImg(tempImage);

                var isEzvizDevice = false;
                var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Drive_Code == "10103" && m.Value.Device_PasswayNo.Contains(cameraModel.PasswayNo));
                if (ezvizDevice.Value != null)
                {
                    isEzvizDevice = EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "相机跟车记录");
                    if (!isEzvizDevice)
                    {
                        Url = "";
                    }
                }

                if (!isEzvizDevice)
                {
                    var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(cameraModel.PasswayNo) && m.Value.Drive_Code == "10123");
                    if (vizCloudDevice.Value != null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"[{cameraModel.IPAddress}]开始组装YM01云视频回放标识URL");

                        // 组装YM01回放标识URL，不调用API
                        var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                        var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                        // 提取设备序列号（如果是新格式，取除最后一个下划线后的所有内容）
                        string deviceSn;
                        if (vizCloudDevice.Value.Device_No.Contains("_"))
                        {
                            var parts = vizCloudDevice.Value.Device_No.Split('_');
                            deviceSn = string.Join("_", parts.Take(parts.Length - 1));
                        }
                        else
                        {
                            deviceSn = vizCloudDevice.Value.Device_No;
                        }

                        // 使用安全的封装方法组装标识URL（不包含敏感信息）
                        var playbackUrl = TcpConnPools.SceneVideo.VziCloud.VziCloudService.BuildPlaybackIdentifierUrl(
                            deviceSn,
                            vizCloudDevice.Value.Device_VideoChannelNo,
                            startTime,
                            endTime,
                            cNo);
                        Url = playbackUrl;
                        isEzvizDevice = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"[{cameraModel.IPAddress}]组装viz云视频回放安全标识URL完成:{playbackUrl}");
                    }
                }

                IDevice deviceScene = null;
                if (isEzvizDevice)
                {
                    imgUrl = "";
                }
                else
                {
                    deviceScene = SceneVideoControler.GetDevice(cameraModel.PasswayNo, DeviceType.SceneVideo01);
                    if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC) imgUrl = "";
                }

                var remark = $"相机车辆跟车事件记录 - {eventData.MsgData}";

                var follModel = new apiFollow
                {
                    no = cNo,
                    bigimg = "",
                    cameraip = device.Device_IP,
                    camerano = device.Device_No,
                    content = JsonConvert.SerializeObject(new
                    {
                        EventFollowing = new
                        {
                            Source = "Camera",
                            msgData = eventData.MsgData,
                            carNo = eventData.License,
                            cameraIP = cameraModel.IPAddress
                        }
                    }),
                    remark = remark,
                    type = 1,
                    time = DateTime.Now,
                    video = Url
                };

                var controlModel = BLL.ControlEvent.Create(follModel, out var errmsg);
                if (controlModel == null)
                {
                    BLL.SystemLogs.AddLog(null, "相机跟车事件", $"[{cameraModel.IPAddress}]相机跟车记录生成失败，" + errmsg + $"，事件信息：{JsonConvert.SerializeObject(follModel)}");
                    return;
                }

                var res = BLL.BaseBLL._Insert(controlModel);
                if (res > 0)
                {
                    var area = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, controlModel.ControlEvent_ParkAreaNo);
                    //内场区域的跟车不发送到智慧停车平台
                    if (area != null && area.ParkArea_Type != 1)
                        BLL.PushEvent.CarFollowAdd(AppBasicCache.GetParking?.Parking_Key, controlModel);

                    // 添加相机跟车自动追缴功能
                    FlowBindSave(cameraModel.PasswayNo, controlModel, JsonConvert.SerializeObject(new
                    {
                        EventFollowing = new
                        {
                            Source = "Camera",
                            msgData = eventData.MsgData,
                            carNo = eventData.License,
                            cameraIP = cameraModel.IPAddress
                        }
                    }));

                    if (!isEzvizDevice)
                    {
                        if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC)
                        {
                            if (!isCache)
                            {
                                _ = deviceSC.SendRecordVideoAsync(controlModel.ControlEvent_No);
                            }
                            _ = deviceSC.SendCameraPhotoAsync(controlModel.ControlEvent_No);
                        }
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机跟车事件处理完成，记录编号：{cNo}");
                }
                else
                {
                    BLL.SystemLogs.AddLog(null, "相机跟车事件", $"[{cameraModel.IPAddress}]相机跟车记录保存失败，" + errmsg + $"，事件信息：{JsonConvert.SerializeObject(follModel)}");
                }
            }
            else
            {
                BLL.SystemLogs.AddLog(null, "相机跟车事件", $"[{cameraModel.IPAddress}]相机跟车记录生成失败，未找到设备信息，车道：{cameraModel.PasswayNo}");
            }
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(null, "相机跟车事件", $"[{cameraModel.IPAddress}]相机跟车事件异常，" + ex.Message + $"，车道：{cameraModel.PasswayNo}，车牌：{eventData.License}");
        }
    }

    /// <summary>
    /// 相机异常事件处理
    /// </summary>
    /// <param name="cameraModel">相机设备模型</param>
    /// <param name="eventData">事件数据</param>
    private void CameraEventAbnormal(CameraModel cameraModel, EventData eventData)
    {
        try
        {
            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]处理相机异常事件，车道：{cameraModel.PasswayNo}，类型：{eventData.TypeNo}");

            var device = DeviceCommonUtil.GetPasswayMainDevice(cameraModel.PasswayNo);
            if (device != null)
            {
                var sType = eventData.TypeNo;
                var remark = $"相机异常事件记录 - {eventData.MsgData}";

                //警报异常状态
                var itype = 2; //未定义异常
                switch (sType)
                {
                    case "129":
                        itype = 5;
                        break; //遥控开闸
                    case "130":
                        itype = 6;
                        break; //遥控关闸
                    case "132":
                        itype = 7;
                        break; //长时间不落杆/长时滞留
                    case "134":
                        itype = 8;
                        break; //开闸异常/关闸异常
                    default:
                        itype = 2;
                        break; //其他异常
                }

                var cNo = Utils.CreateNumberWith();
                var Url = ControlEvent.CreateDir(cNo);
                var isCache = ControlEvent.GetCacheSendVideo(ref Url, DateTime.Now);

                var tempImage = CameraImageHelper.ImageSaveHSPathBig(cNo, device?.Device_SentryHostNo, true);
                var imgUrl = LPRTools.GetSentryHostImg(tempImage);

                var isEzvizDevice = false;
                var ezvizDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(cameraModel.PasswayNo) && m.Value.Drive_Code == "10103");
                if (ezvizDevice.Value != null)
                {
                    isEzvizDevice = EzvizApi.GetVideoUrl(ezvizDevice.Value, ref Url, "相机异常记录");
                    if (!isEzvizDevice)
                    {
                        Url = "";
                    }
                }

                if (!isEzvizDevice)
                {
                    var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(cameraModel.PasswayNo) && m.Value.Drive_Code == "10123");
                    if (vizCloudDevice.Value != null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"[{cameraModel.IPAddress}]开始组装YM01云视频回放标识URL");

                        // 组装YM01回放标识URL，不调用API
                        var startTime = DateTimeOffset.Now.AddSeconds(-45).ToUnixTimeSeconds();
                        var endTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                        // 提取设备序列号（如果是新格式，取除最后一个下划线后的所有内容）
                        string deviceSn;
                        if (vizCloudDevice.Value.Device_No.Contains("_"))
                        {
                            var parts = vizCloudDevice.Value.Device_No.Split('_');
                            deviceSn = string.Join("_", parts.Take(parts.Length - 1));
                        }
                        else
                        {
                            deviceSn = vizCloudDevice.Value.Device_No;
                        }

                        // 使用安全的封装方法组装标识URL（不包含敏感信息）
                        var playbackUrl = TcpConnPools.SceneVideo.VziCloud.VziCloudService.BuildPlaybackIdentifierUrl(
                            deviceSn,
                            vizCloudDevice.Value.Device_VideoChannelNo,
                            startTime,
                            endTime,
                            cNo);
                        Url = playbackUrl;
                        isEzvizDevice = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"[{cameraModel.IPAddress}]组装viz云视频回放安全标识URL完成:{playbackUrl}");
                    }
                }

                IDevice deviceScene = null;
                if (!isEzvizDevice)
                {
                    deviceScene = SceneVideoControler.GetDevice(cameraModel.PasswayNo, DeviceType.SceneVideo01);
                }

                var follModel = new apiFollow
                {
                    no = cNo,
                    bigimg = "",
                    cameraip = device.Device_IP,
                    camerano = device.Device_No,
                    content = JsonConvert.SerializeObject(new
                    {
                        EventAbnormal = new
                        {
                            Source = "Camera",
                            msgData = eventData.MsgData,
                            typeNo = sType,
                            cameraIP = cameraModel.IPAddress
                        }
                    }),
                    remark = remark,
                    type = itype,
                    time = DateTime.Now,
                    video = Url
                };

                var controlModel = BLL.ControlEvent.Create(follModel, out var errmsg);
                if (controlModel == null)
                {
                    BLL.SystemLogs.AddLog(null, "相机异常事件", $"[{cameraModel.IPAddress}]相机异常信息上报记录生成失败，" + errmsg + $"，事件信息：{JsonConvert.SerializeObject(follModel)}");
                    return;
                }

                var res = BLL.BaseBLL._Insert(controlModel);
                if (res > 0)
                {
                    var area = BLL.ParkArea.GetEntity(controlModel.ControlEvent_ParkAreaNo);
                    //内场区域的异常不发送到智慧停车平台
                    if (area != null && area.ParkArea_Type != 1)
                        BLL.PushEvent.CarFollowAdd(AppBasicCache.GetParking?.Parking_Key, controlModel);

                    if (!isEzvizDevice)
                    {
                        if (deviceScene != null && deviceScene is SceneVideoOf01 deviceSC)
                        {
                            if (!isCache)
                            {
                                _ = deviceSC.SendRecordVideoAsync(controlModel.ControlEvent_No);
                            }
                            _ = deviceSC.SendCameraPhotoAsync(controlModel.ControlEvent_No);
                        }
                        else
                        {
                            _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"[{cameraModel.IPAddress}]相机异常事件抓拍图地址：" + tempImage);
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"[{cameraModel.IPAddress}]相机异常事件 车道{cameraModel.PasswayNo}未找到场景相机，或场景相机设备未连接");
                        }
                    }
                    else
                    {
                        _ = LPRTools.GetSnapShootToJpeg(device?.Device_No, tempImage);
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"[{cameraModel.IPAddress}]相机异常事件抓拍图地址：" + tempImage);
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"[{cameraModel.IPAddress}]相机异常事件处理完成，记录编号：{cNo}，类型：{itype}");
                }
                else
                {
                    BLL.SystemLogs.AddLog(null, "相机异常事件", $"[{cameraModel.IPAddress}]相机异常信息上报记录保存失败，" + errmsg + $"，事件信息：{JsonConvert.SerializeObject(follModel)}");
                }
            }
            else
            {
                BLL.SystemLogs.AddLog(null, "相机异常事件", $"[{cameraModel.IPAddress}]相机异常信息上报记录生成失败，未找到设备信息，车道：{cameraModel.PasswayNo}");
            }
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(null, "相机异常事件", $"[{cameraModel.IPAddress}]相机异常事件异常，" + ex.Message + $"，车道：{cameraModel.PasswayNo}，类型：{eventData.TypeNo}");
        }
    }



    #endregion
}