using carparking.BLL.Cache;
using carparking.Model.DisplayTemplates;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using TcpConnPools;
using TcpConnPools.Screen.ScreenC08Tcp;
using FontColor = TcpConnPools.Screen.ScreenC08Tcp.FontColor;
using InStyle = TcpConnPools.Screen.ScreenC08Tcp.InStyle;

namespace carparking.SentryBox.Util;

/// <summary>
/// 屏显工具类
/// </summary>
public class DisplayTemplateUtil
{
    /// <summary>
    /// 获取DisplayTemplateUtil的单例实例
    /// </summary>
    private static readonly Lazy<DisplayTemplateUtil> _instance = new(() => new DisplayTemplateUtil(), LazyThreadSafetyMode.ExecutionAndPublication);

    /// <summary>
    /// 获取DisplayTemplateUtil的单例实例
    /// </summary>
    public static DisplayTemplateUtil Instance => _instance.Value;

    /// <summary>
    /// 私有构造函数，防止外部实例化
    /// </summary>
    private DisplayTemplateUtil()
    {
        var methods = GetType().GetMethods(BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.DeclaredOnly);
        foreach (var method in methods)
        {
            var attribute = method.GetCustomAttribute<PresetVariableCalculateAttribute>();
            if (attribute != null)
            {
                CalculateMethods[$"{{{{{attribute.Variable}}}}}"] = method;
            }
        }
    }

    /// <summary>
    /// 发布C08空闲时段信息记录错误日志的最后时间
    /// </summary>
    private DateTime PublishC08IdleTimeLastError = DateTime.MinValue;

    /// <summary>
    /// 初始化后多少秒开发发布显示信息 单位秒
    /// </summary>
    private readonly int PublishDisplayTemplateAfterInitSeconds = 30;

    /// <summary>
    /// 初始化对象时间
    /// </summary>
    private readonly DateTime InitTime = DateTime.Now;

    /// <summary>
    /// 安全访问锁
    /// </summary>
    private readonly SemaphoreSlim PublishC08IdleTimeSemaphore = new(1, 1);

    /// <summary>
    /// 计算方法缓存
    /// </summary>
    private readonly Dictionary<string, MethodInfo> CalculateMethods = [];

    /// <summary>
    /// 定时发布C08屏显空闲时段信息
    /// </summary>
    public async Task PublishC08IdleTimeAsync()
    {
        //如果空闲时段车位数为0，并且初始化时间小于30秒，则不发布空闲时段信息
        if (ParkSpaceUtil.SpaceNumbers.Item1 == 0 && ParkSpaceUtil.SpaceNumbers.Item2 == 0)
        {
            if (DateTime.Now.Subtract(InitTime).TotalSeconds < PublishDisplayTemplateAfterInitSeconds)
            {
                return;
            }
        }

        //尝试获取锁
        var isLock = await PublishC08IdleTimeSemaphore.WaitAsync(500);
        if (!isLock)
        {
            return;
        }

        try
        {
            var tasks = new List<Task>();
            var co8devices = DevicePool.Instance.GetDevicesByTypes(DeviceType.C08ScreenTcp);
            foreach (var p in co8devices)
            {
                var model = (C08TcpModel)p.Model;
                var links = AppBasicCache.GetDisplayTemplateRelatedData(model.DisplayTemplateId);
                if (links != null)
                {
                    //定位显示屏的偏移点
                    var offsetX = links.DisplayTemplate.DisplayTemplate_X;
                    var offsetY = links.DisplayTemplate.DisplayTemplate_Y;
                    var sort = links.ProgramAreas.OrderBy(p => p.ProgramArea_Index).ToList();
                    var areaList = new List<C08TcpAreaModel>();

                    foreach (var l in sort)
                    {
                        var pAction = links.ProgramAreaActions.FirstOrDefault(p => p.ProgramAreaAction_ProgramAreaId == l.ProgramArea_Id);
                        C08TcpAreaModel area = null;

                        if (pAction != null)
                        {
                            var idleContents = pAction.GetIdleContents();
                            if (idleContents != null && idleContents.Any())
                            {
                                var content = ParsePresetVariables(idleContents);
                                var fontColor = GetFinalFontColor(pAction, idleContents, true);

                                area = new C08TcpAreaModel
                                {
                                    StartX = (ushort)Math.Max(l.ProgramArea_X - offsetX, 0),
                                    StartY = (ushort)Math.Max(l.ProgramArea_Y - offsetY, 0),
                                    Width = l.ProgramArea_Width,
                                    Height = l.ProgramArea_Height,
                                    AreaId = l.ProgramArea_Index,
                                    Speed = (byte)pAction.ProgramAreaAction_EffectSpeed,
                                    Effect = (InStyle)pAction.ProgramAreaAction_Effect,
                                    DisplayTime = pAction.ProgramAreaAction_DisplayTime,
                                    FontSize = (FontSize)pAction.ProgramAreaAction_FontSize,
                                    FontColor = fontColor,
                                    DisplayString = content,
                                    IsIdle = true,
                                    IsValidForRefresh = true // 有节目区域动作，标识为有效
                                };
                            }
                            else
                            {
                                // 有节目区域动作但没有空闲内容，创建空闲区域
                                area = CreateIdleAreaModel(l, pAction, offsetX, offsetY);
                            }
                        }
                        else
                        {
                            // 没有节目区域动作，创建默认空闲区域
                            area = CreateDefaultAreaModel(l, offsetX, offsetY);
                        }

                        if (area != null)
                        {
                            areaList.Add(area);
                        }
                    }

                    var aredates = areaList.ToArray();
                    tasks.Add(PublishC08IdleTimeTask((ScreenOfC08Tcp)p, aredates));
                }
            }
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            if (PublishC08IdleTimeLastError == DateTime.MinValue || DateTime.Now.Subtract(PublishC08IdleTimeLastError).TotalMinutes >= 5)
            {
                PublishC08IdleTimeLastError = DateTime.Now;
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[SentryBoxHelper/PublishC08IdleTime]发布C08空闲时段信息异常:{ex.Message}");
            }
        }
        finally
        {
            //需要延迟20000毫秒释放锁
            await Task.Delay(20000);
            if (isLock)
            {
                PublishC08IdleTimeSemaphore.Release();
            }
        }
    }

    /// <summary>
    /// 发布C08空闲时段信息任务
    /// </summary>
    /// <returns>异步任务</returns>
    public Task PublishC08IdleTimeTask(ScreenOfC08Tcp device, params C08TcpAreaModel[] areas)
    {
        if (!device.Model.IsConnected)
        {
            device.Model.Type.WriteLog($"[{device.Model.IPAddress}]设备未连接，不发布空闲时段信息");
            return Task.CompletedTask;
        }

        return device.PublishDisplayContentAsync(areas);
    }

    /// <summary>
    /// 向线程池已经连接的C08设备同步电脑时间
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task SetTimeAllC08Async()
    {
        var devicec08s = DevicePool.Instance.GetDevicesByTypes(DeviceType.C08ScreenTcp);
        foreach (var c in devicec08s)
        {
            if (c is ScreenOfC08Tcp c08)
            {
                await c08.SetTimeAsync();
            }
        }
    }

    /// <summary>
    /// 发布进出场显示模板信息
    /// </summary>
    /// <param name="presetVariableValue">预定义变量值</param>
    /// <returns>异步任务</returns>
    public async Task PublishDisplayTemplateAsync(PresetVariableValue presetVariableValue)
    {
        try
        {
            if (presetVariableValue == null)
            {
                throw new ArgumentNullException(nameof(presetVariableValue));
            }

            // 获取车道编号
            if (string.IsNullOrEmpty(presetVariableValue.PasswayNo))
            {
                throw new ArgumentException("车道编号不能为空", nameof(presetVariableValue.PasswayNo));
            }

            // 获取相关的显示模板链接
            var displayTemplateLinks = await GetDisplayTemplateLinksAsync(presetVariableValue.PasswayNo);
            if (!displayTemplateLinks.Any())
            {
                return;
            }

            var publishTasks = new List<Task>();
            foreach (var link in displayTemplateLinks)
            {
                var co8devices = DevicePool.Instance.GetDevicesByTypes(DeviceType.C08ScreenTcp)
                    .Where(d => ((C08TcpModel)d.Model).DisplayTemplateId == link.DisplayTemplate.DisplayTemplate_Id);

                foreach (var device in co8devices)
                {
                    publishTasks.Add(PublishContentAsync(device, link, presetVariableValue));
                }
            }

            if (publishTasks.Any())
            {
                await Task.WhenAll(publishTasks);
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog,
                $"[DisplayTemplateUtil/PublishDisplayTemplateAsync]发布显示模板信息异常: {ex.Message}\r\n{ex.StackTrace}");
            throw;
        }
    }

    /// <summary>
    /// 获取与指定车道相关的节目区域动作
    /// </summary>
    /// <param name="passwayNo">车道编号</param>
    /// <returns>相关的显示模板链接集合</returns>
    /// <exception cref="ArgumentNullException">车道编号为空时抛出</exception>
    private async Task<IEnumerable<DisplayTemplateLink>> GetDisplayTemplateLinksAsync(string passwayNo)
    {
        return await Task.Run(() =>
        {
            var displayTemplateLinks = AppBasicCache.GetProgramAreaAction.Values
                .Where(action => IsActionValidForPassway(action, passwayNo))
                .Select(action => AppBasicCache.GetDisplayTemplateRelatedData(action.ProgramAreaAction_DisplayTemplateId))
                .Where(link => link != null);

            return displayTemplateLinks;
        });
    }

    /// <summary>
    /// 检查节目区域动作是否对指定车道有效
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <param name="passwayNo">车道编号</param>
    /// <returns>是否有效</returns>
    private bool IsActionValidForPassway(ProgramAreaAction action, string passwayNo)
    {
        if (string.IsNullOrEmpty(action?.ProgramAreaAction_LaneIds))
        {
            return false;
        }

        var laneIds = action.ProgramAreaAction_LaneIds.Split([','], StringSplitOptions.RemoveEmptyEntries);
        return laneIds.Contains(passwayNo);
    }

    /// <summary>
    /// 发布显示内容到指定的节目区域
    /// </summary>
    /// <param name="link">显示模板关联集合</param>
    /// <param name="variableValue">预定义变量值</param>
    /// <returns>异步任务</returns>
    private async Task PublishContentAsync(IDevice device, DisplayTemplateLink link, PresetVariableValue variableValue)
    {
        try
        {
            var model = (C08TcpModel)device.Model;
            var offsetX = link.DisplayTemplate.DisplayTemplate_X;
            var offsetY = link.DisplayTemplate.DisplayTemplate_Y;

            // 按区域索引排序
            var sortedAreas = link.ProgramAreas.OrderBy(p => p.ProgramArea_Index).ToList();
            var areaModels = new List<C08TcpAreaModel>();

            foreach (var area in sortedAreas)
            {
                var areaAction = link.ProgramAreaActions.FirstOrDefault(p => p.ProgramAreaAction_ProgramAreaId == area.ProgramArea_Id && IsActionValidForPassway(p, variableValue.PasswayNo));

                if (areaAction != null)
                {
                    var contents = areaAction.GetNonIdleContents()?.ToList();
                    if (contents != null && contents.Any())
                    {
                        var content = ParsePresetVariables(contents, variableValue);
                        var fontColor = GetFinalFontColor(areaAction, contents, false);

                        var areaModel = new C08TcpAreaModel
                        {
                            StartX = (ushort)Math.Max(0, area.ProgramArea_X - offsetX),
                            StartY = (ushort)Math.Max(0, area.ProgramArea_Y - offsetY),
                            Width = area.ProgramArea_Width,
                            Height = area.ProgramArea_Height,
                            AreaId = area.ProgramArea_Index,
                            Speed = (byte)GetNonIdleEffectSpeed(areaAction),
                            Effect = (InStyle)GetNonIdleInStyle(areaAction),
                            DisplayTime = GetNonIdleDisplayTime(areaAction),
                            FontSize = (FontSize)GetNonIdleFontSize(areaAction),
                            FontColor = fontColor,
                            DisplayString = content,
                            IsIdle = false,
                            IsAutoClose = true,
                            IsValidForRefresh = true // 有节目区域动作，标识为有效
                        };
                        areaModels.Add(areaModel);
                    }
                    else
                    {
                        // 有节目区域动作但没有非空闲内容，创建空闲区域
                        var idleAreaModel = CreateIdleAreaModel(area, areaAction, offsetX, offsetY);
                        areaModels.Add(idleAreaModel);
                    }
                }
                else
                {
                    // 没有节目区域动作，创建默认空闲区域
                    var defaultAreaModel = CreateDefaultAreaModel(area, offsetX, offsetY);
                    areaModels.Add(defaultAreaModel);
                }
            }

            if (areaModels.Any() && device is ScreenOfC08Tcp c08Device)
            {
                await c08Device.PublishDisplayContentAsync([.. areaModels]);
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog,
                $"[DisplayTemplateUtil/PublishContentAsync]发布单个设备显示内容异常: {ex.Message}\r\n{ex.StackTrace}");
            throw;
        }
    }

    /// <summary>
    /// 获取最终的字体颜色，处理车位不足时显示红色的逻辑
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <param name="contents">内容列表</param>
    /// <param name="isIdle">是否为空闲状态</param>
    /// <returns>计算后的字体颜色</returns>
    private TcpConnPools.Screen.ScreenC08Tcp.FontColor GetFinalFontColor(ProgramAreaAction action, List<ProgramActionContent> contents, bool isIdle)
    {
        var fontColor = isIdle ? action.ProgramAreaAction_FontColor : GetNonIdleFontColor(action);

        if (action.ProgramAreaAction_UseRedForNotEnoughSpace == 1)
        {
            var hasTotalSpaces = contents.Any(c => c.Key == "{{AvailableSpaces}}");
            var hasAreaSpaces = contents.Any(c => c.Key == "{{AreaAvailableSpaces}}");

            if (hasTotalSpaces)
            {
                if (ParkSpaceUtil.SpaceNumbers.Item2 <= 0)
                {
                    return FontColor.Red; // Red
                }
            }
            else if (hasAreaSpaces)
            {
                var areaContent = contents.First(c => c.Key == "{{AreaAvailableSpaces}}");
                if (AppBasicCache.GetParkAreas.TryGetValue(areaContent.Value, out var parkArea))
                {
                    if (ParkSpaceUtil.GetAreaSpaceNum(parkArea.ParkArea_No, 0) <= 0)
                    {
                        return FontColor.Red; // Red
                    }
                }
            }
        }
        return fontColor switch
        {
            0xff => FontColor.Red,
            0xff00 => FontColor.Green,
            0xffff => FontColor.Yellow,
            _ => FontColor.Red,
        };
    }

    /// <summary>
    /// 获取非空闲状态字体大小
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <returns>字体大小</returns>
    private int GetNonIdleFontSize(ProgramAreaAction action)
    {
        return action.ProgramAreaAction_NonIdleFontSize != 0 ? action.ProgramAreaAction_NonIdleFontSize : action.ProgramAreaAction_FontSize;
    }

    /// <summary>
    /// 获取非空闲状态字体颜色
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <returns>字体颜色</returns>
    private int GetNonIdleFontColor(ProgramAreaAction action)
    {
        return action.ProgramAreaAction_NonIdleFontColor != 0 ? action.ProgramAreaAction_NonIdleFontColor : action.ProgramAreaAction_FontColor;
    }

    /// <summary>
    /// 获取非空闲状态显示效果
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <returns>显示效果</returns>
    private carparking.Interop.C08.InStyle GetNonIdleInStyle(ProgramAreaAction action)
    {
        return (carparking.Interop.C08.InStyle)(action.ProgramAreaAction_NonIdleEffect != 0 ? action.ProgramAreaAction_NonIdleEffect : action.ProgramAreaAction_Effect);
    }

    /// <summary>
    /// 获取非空闲状态特效速度
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <returns>特效速度</returns>
    private int GetNonIdleEffectSpeed(ProgramAreaAction action)
    {
        return action.ProgramAreaAction_NonIdleEffectSpeed != 0 ? action.ProgramAreaAction_NonIdleEffectSpeed : action.ProgramAreaAction_EffectSpeed;
    }

    /// <summary>
    /// 获取非空闲状态显示时间
    /// </summary>
    /// <param name="action">节目区域动作</param>
    /// <returns>显示时间</returns>
    private int GetNonIdleDisplayTime(ProgramAreaAction action)
    {
        return action.ProgramAreaAction_NonIdleDisplayTime != 0 ? action.ProgramAreaAction_NonIdleDisplayTime : action.ProgramAreaAction_DisplayTime;
    }

    /// <summary>
    /// 解析节目动作内容中的预定义变量
    /// </summary>
    /// <param name="contents">节目动作内容列表</param>
    /// <param name="variableValue">预定义变量值（可选）</param>
    /// <returns>解析后的文本</returns>
    private string ParsePresetVariables(List<ProgramActionContent> contents, PresetVariableValue variableValue = null)
    {
        return string.Join("", contents.Select(c => ParsePresetVariable(c, variableValue)));
    }

    /// <summary>
    /// 解析单个节目动作内容中的预定义变量
    /// </summary>
    /// <param name="content">节目动作内容</param>
    /// <param name="variableValue">预定义变量值（可选）</param>
    /// <returns>解析后的文本</returns>
    private string ParsePresetVariable(ProgramActionContent content, PresetVariableValue variableValue = null)
    {
        var value = (content, variableValue);
        if (CalculateMethods.TryGetValue(content.Key, out var method))
        {
            return method.Invoke(this, [value]) as string;
        }
        return string.Empty;
    }

    /// <summary>
    /// 获取停车场区域的值
    /// </summary>
    /// <param name="areaId">区域编号</param>
    /// <param name="valueSelector">值选择器</param>
    /// <returns>解析后的文本</returns>
    private string GetParkAreaValue(string areaId, Func<Model.ParkArea, string> valueSelector)
    {
        return AppBasicCache.GetParkAreas.TryGetValue(areaId, out var area) ? valueSelector(area) : string.Empty;
    }

    /// <summary>
    /// 计算自定义信息
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.CustomInfo)]
    protected string CalculateCustomInfo((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        return value.content.Value;
    }

    #region 计算预定义变量
    /// <summary>
    /// 计算区域名称
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.AreaName)]
    protected string CalculateAreaName((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.content.Link != 1)
        {
            return string.Empty;
        }
        //获取区域名称
        return GetParkAreaValue(value.content.Value, area => area.ParkArea_Name);
    }

    /// <summary>
    /// 计算区域总车位
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.AreaTotalSpaces)]
    protected string CalculateAreaTotalSpaces((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.content.Link != 1)
        {
            return string.Empty;
        }
        return GetParkAreaValue(value.content.Value, area => area.ParkArea_SpaceNum?.ToString() ?? "0");
    }

    /// <summary>
    /// 计算区域剩余车位
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.AreaAvailableSpaces)]
    protected string CalculateAreaAvailableSpaces((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.content.Link != 1)
        {
            return string.Empty;
        }
        return GetParkAreaValue(value.content.Value, area =>
        {
            var remainingSpaces = ParkSpaceUtil.GetAreaSpaceNum(area.ParkArea_No, 0);
            if (remainingSpaces <= 0)
            {
                return "车位已满";
            }
            return remainingSpaces.ToString();
        });
    }

    /// <summary>
    /// 计算停车车场总车位
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.TotalSpaces)]
    protected string CalculateTotalSpaces((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        return value.content.Prefix + ParkSpaceUtil.SpaceNumbers.Item1.ToString() + value.content.Suffix;
    }

    /// <summary>
    /// 计算停车车场剩余车位
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.AvailableSpaces)]
    protected string CalculateAvailableSpaces((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        var remainingSpaces = ParkSpaceUtil.SpaceNumbers.Item2;
        if (remainingSpaces <= 0)
        {
            return "车位已满";
        }
        return value.content.Prefix + remainingSpaces.ToString() + value.content.Suffix;
    }

    /// <summary>
    /// 计算当前时间
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.CurrentTime)]
    protected string CalculateCurrentTime((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        return $"{value.content.Prefix}{DateTime.Now:yyyy-MM-dd HH:mm}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算入场时间
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.EntryTime)]
    protected string CalculateEntryTime((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.InTime == null)
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.InTime.Value:yyyy-MM-dd HH:mm}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算出场时间
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.ExitTime)]
    protected string CalculateExitTime((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.OutTime == null || value.variableValue.CurrentDirection != 1)
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.OutTime.Value:yyyy-MM-dd HH:mm}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算过期时间
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.ExpireTime)]
    protected string CalculateExpireTime((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.ExpireTime == null)
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.ExpireTime.Value:yyyy-MM-dd}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算停车时长
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>停车时长 如1小时10分钟 11分钟 1天 2天15小时8分钟</returns>
    [PresetVariableCalculate(PresetVariable.ParkingDuration)]
    protected string CalculateParkingDuration((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.InTime == null || value.variableValue.OutTime == null)
        {
            return string.Empty;
        }

        var duration = value.variableValue.OutTime.Value - value.variableValue.InTime.Value;
        var hours = duration.TotalHours;
        var days = (int)hours / 24;
        var remainingHours = (int)hours % 24;
        //判断是否有天数
        if (days > 0)
        {
            return value.content.Prefix + $"{days}天{remainingHours}小时{duration.Minutes}分钟" + value.content.Suffix;
        }
        //判断是否有小时
        if (remainingHours > 0)
        {
            return value.content.Prefix + $"{remainingHours}小时{duration.Minutes}分钟" + value.content.Suffix;
        }
        return value.content.Prefix + $"{duration.Minutes}分钟" + value.content.Suffix;
    }

    /// <summary>
    /// 计算剩余天数
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.RemainingDays)]
    protected string CalculateRemainingDays((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null)
        {
            return string.Empty;
        }
        if (value.variableValue.RemainingDays != null)
        {
            return $"{value.content.Prefix}{value.variableValue.RemainingDays}{value.content.Suffix}";
        }

        if (value.variableValue.ExpireTime != null)
        {
            var remainingDays = (int)(value.variableValue.ExpireTime.Value.Date - DateTime.Now.Date).TotalDays;
            return $"{value.content.Prefix}{remainingDays}{value.content.Suffix}";
        }

        return string.Empty;
    }

    /// <summary>
    /// 计算车牌号码
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.PlateNumber)]
    protected string CalculatePlateNumber((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null)
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.PlateNumber}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算车牌类型
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.PlateType)]
    protected string CalculatePlateType((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.CarCardType == null)
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.CarCardType.CarCardType_Name}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算系统车位号
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.SpaceNumber)]
    protected string CalculateSpaceNo((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || string.IsNullOrWhiteSpace(value.variableValue.SpaceNo))
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.SpaceNo}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算收费金额
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.Fee)]
    protected string CalculateFee((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.Fee == null)
        {
            return string.Empty;
        }

        return $"{value.content.Prefix}{value.variableValue.Fee}{value.content.Suffix}";
    }

    /// <summary>
    /// 计算余额
    /// </summary>
    /// <param name="value">节目动作内容和预定义变量值</param>
    /// <returns>解析后的文本</returns>
    [PresetVariableCalculate(PresetVariable.Balance)]
    protected string CalculateBalance((ProgramActionContent content, PresetVariableValue variableValue) value)
    {
        if (value.variableValue == null || value.variableValue.Balance == null)
        {
            return string.Empty;
        }

        if (value.variableValue.CarCardType != null && value.variableValue.CarCardType.CarCardType_Category == "3657")
        {

            return $"{value.content.Prefix}{value.variableValue.Balance}{value.content.Suffix}";
        }
        return string.Empty;
    }

    /// <summary>
    /// 创建空闲区域模型（有节目区域动作但没有非空闲内容）
    /// </summary>
    /// <param name="area">节目区域</param>
    /// <param name="areaAction">节目区域动作</param>
    /// <param name="offsetX">X偏移量</param>
    /// <param name="offsetY">Y偏移量</param>
    /// <returns>空闲区域模型</returns>
    private C08TcpAreaModel CreateIdleAreaModel(ProgramArea area, ProgramAreaAction areaAction, int offsetX, int offsetY)
    {
        try
        {
            // 获取空闲内容
            var idleContents = areaAction.GetIdleContents();
            var displayString = " "; // 默认空白

            if (idleContents != null && idleContents.Any())
            {
                var content = string.Join("", idleContents.Select(c => c.Value ?? ""));
                displayString = string.IsNullOrEmpty(content) ? " " : content;
            }

            return new C08TcpAreaModel
            {
                StartX = (ushort)Math.Max(0, area.ProgramArea_X - offsetX),
                StartY = (ushort)Math.Max(0, area.ProgramArea_Y - offsetY),
                Width = area.ProgramArea_Width,
                Height = area.ProgramArea_Height,
                AreaId = area.ProgramArea_Index,
                Speed = (byte)(areaAction.ProgramAreaAction_EffectSpeed > 0 ? areaAction.ProgramAreaAction_EffectSpeed : 5),
                Effect = (InStyle)areaAction.ProgramAreaAction_Effect,
                DisplayTime = areaAction.ProgramAreaAction_DisplayTime > 0 ? areaAction.ProgramAreaAction_DisplayTime : 20,
                FontSize = (FontSize)(areaAction.ProgramAreaAction_FontSize > 0 ? areaAction.ProgramAreaAction_FontSize : 16),
                FontColor = areaAction.ProgramAreaAction_FontColor switch
                {
                    0xff => FontColor.Red,
                    0xff00 => FontColor.Green,
                    0xffff => FontColor.Blue,
                    _ => FontColor.Red

                },
                DisplayString = displayString,
                IsIdle = true,
                IsAutoClose = false,
                IsValidForRefresh = true // 有节目区域动作，标识为有效
            };
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog,
                $"[DisplayTemplateUtil/CreateIdleAreaModel]创建空闲区域模型异常: {ex.Message}");

            // 返回基本的默认区域
            return CreateDefaultAreaModel(area, offsetX, offsetY);
        }
    }

    /// <summary>
    /// 创建默认区域模型（没有节目区域动作）
    /// </summary>
    /// <param name="area">节目区域</param>
    /// <param name="offsetX">X偏移量</param>
    /// <param name="offsetY">Y偏移量</param>
    /// <returns>默认区域模型</returns>
    private C08TcpAreaModel CreateDefaultAreaModel(ProgramArea area, int offsetX, int offsetY)
    {
        return new C08TcpAreaModel
        {
            StartX = (ushort)Math.Max(0, area.ProgramArea_X - offsetX),
            StartY = (ushort)Math.Max(0, area.ProgramArea_Y - offsetY),
            Width = area.ProgramArea_Width,
            Height = area.ProgramArea_Height,
            AreaId = area.ProgramArea_Index,
            Speed = 5,
            Effect = InStyle.Immediate,
            DisplayTime = 20,
            FontSize = FontSize.Size16,
            FontColor = FontColor.Red,
            DisplayString = " ", // 默认空白
            IsIdle = true,
            IsAutoClose = false,
            // 标识为无效区域，因为没有设置节目区域动作
            IsValidForRefresh = false
        };
    }

    #endregion
}
