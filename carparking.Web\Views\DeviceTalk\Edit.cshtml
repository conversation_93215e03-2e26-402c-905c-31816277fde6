﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { width: 100%; height: 100%; padding: 0; margin: 0; }
        .layui-row { margin-bottom: 10px; }
        .m-label { padding: 9px 0 0 10px; font-weight: bold; }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .layui-card { box-shadow: none; }

        .layui-form .layui-badge { padding: 3px 5px; margin-top: 9px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-card">
        <div class="layui-card-body layui-form" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">值班中心识别码</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-null v-numen" id="DeviceTalk_No" name="DeviceTalk_No" maxlength="32" autocomplete="off" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">值班中心名称</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-null" id="DeviceTalk_Name" name="DeviceTalk_Name" maxlength="32" autocomplete="off" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row DeviceTalk_Net">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">值班中心IP</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-ip v-null" id="DeviceTalk_TcpIp" name="DeviceTalk_TcpIp" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row DeviceTalk_Net">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">值班中心端口</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-number v-null" id="DeviceTalk_TcpPort" name="DeviceTalk_TcpPort" maxlength="5" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">备注</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input" id="DeviceTalk_Remark" name="DeviceTalk_Remark" maxlength="200" autocomplete="off" />
                </div>
                <div class="layui-col-xs1 red-mark"></div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">管理通道</div>
                <div class="layui-col-xs7">
                    <table lay-filter="table" id="stable">
                    </table>
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 edit-label">&nbsp;</div>
                <div class="layui-col-xs7">
                    <button class="btn btn-primary layui-hide" id="Save"><i class="fa fa-save"></i> <t>保存</t></button>
                    <button class="btn btn-primary layui-bg-red layui-hide" id="Cancel"><i class="fa fa-close"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layform = null;
        var laytable = null;
        layui.use(['form'], function () {
            layform = layui.form;
            laytable = layui.table;
            pager.init();
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("DeviceTalk_No");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            passways: null,
            alllink: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllPasswayAndLink", {}, function (json) {
                    if (json.success) {
                        var passways = json.data.passways;
                        var alllink = json.data.alllink;
                        pager.alllink = alllink;
                        pager.passways = passways;
                        //转换静态表格
                        var cols = [[
                            { type: "checkbox" }
                            , { field: 'Passway_Name', title: '通道名称' }
                            , { field: 'linkName', title: '所属区域' }
                        ]];

                        passways.forEach(function (p, index) {
                            var links = [];
                            alllink.forEach(function (l, index) {
                                if (l.PasswayLink_PasswayNo == p.Passway_No) {
                                    links[links.length] = l;
                                }
                            });

                            var linkName = [];
                            links.forEach(function (l, index) {
                                linkName[linkName.length] = l.ParkArea_Name + "[" + (l.PasswayLink_GateType == 0 ? "出口" : "入口") + "]";
                            });
                            p.linkName = linkName.join("、");
                        });
                        laytable.render({ elem: '#stable', cols: cols, data: passways, limit: 1000 });
                        //$('input[lay-filter="layTableAllChoose"]').parent().html("")

                    } else {
                        layer.msg(json.msg)
                    }
                }, "json");
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {

                    $.post("GetDetail", { DeviceTalk_No: paramNo }, function (json) {
                        if (json.success) {
                            var model = json.data;
                            if (paramAct == "Update") {
                                $("#verifyCheck").fillForm(model, function (data) { });
                                $("#DeviceTalk_No").attr("disabled", true);
                                if (model.DeviceTalk_BindWay && model.DeviceTalk_BindWay != "")
                                    SetCheckedTr(JSON.parse(model.DeviceTalk_BindWay));
                            }
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json")
                } else {
                    $("#DeviceTalk_No").val('DT'+'@carparking.Common.Utils.CreateNumber');
                }
            },
            bindEvent: function () {

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });
                    param.DeviceTalk_BindWay = getLinkData();

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        LAYER_OPEN_TYPE_0("确定新增值班中心设置?", res => {
                            LAYER_LOADING("处理中...");
                            $.post("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                                $("#Save").removeAttr("disabled")
                                if (json.success) {
                                    var passwayNo = json.data;
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    layer.msg("保存成功", { time: 1000 })
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    } else {
                        LAYER_OPEN_TYPE_0("确定修改值班中心设置?", res => {
                            LAYER_LOADING("处理中...");
                            param.DeviceTalk_No = paramNo;
                            $.post("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                                $("#Save").removeAttr("disabled")
                                if (json.success) {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    layer.msg("保存成功", { time: 1000 })
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    }
                });

                $("#Cancel").click(function () {
                    parent.layer.closeAll()
                })
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        };

        //读取当前关联区域列表
        var getLinkData = function () {
            var data = [];
            pager.passways.forEach(function (item, index) {
                var trcheckbox = $("tr[data-index='" + index + "'] input[type='checkbox']");
                if (trcheckbox[0].checked) {
                    data[data.length] = item.Passway_No;
                }
            })
            console.log(data)
            return data;
        }

        //表单赋值，编辑时设置管理的通道
        var SetCheckedTr = function (passways) {
            for (var i = 0; i < passways.length; i++) {
                pager.passways.forEach(function (item, index) {
                    if (item.Passway_No == passways[i]) {
                        var trcheckbox = $("tr[data-index='" + index + "'] input[type='checkbox']");
                        trcheckbox.attr("checked", true);
                    }
                });
            }
            layform.render()
        }
    </script>
    <script type="text/javascript">
        //设备参数配置[仅选项按钮]默认值
        var config = {
            DeviceTalk_Type: 1,
            DeviceTalk_Category: 1,
            DeviceTalk_Net: 1,
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName);
            });
        });
        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == "DeviceTalk_Net") {
                if (config[idName] == "1") {
                    $(".DeviceTalk_Net").removeClass("layui-hide");
                } else {
                    $(".DeviceTalk_Net").removeClass("layui-hide").addClass("layui-hide");
                }
            }
            if (idName == "DeviceTalk_Category") {
                if (config[idName] == "2") {
                    $(".DeviceTalk_Category").removeClass("layui-hide");
                } else {
                    $(".DeviceTalk_Category").removeClass("layui-hide").addClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
