﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .form-group .row label { line-height: 30px; }

        .spanDesc { color: burlywood; }

        .col-xs-3.control-label { text-align: right; }

        .spanDesc { color: coral; margin: 15px 0; }

        #centent { padding-top: 30px; height: 80%; }

        #btm { height: 20%; }
        .desc { display: block; font-size: 12px; text-align: left; width: calc(100% - 1px); margin-left: 1px; margin-top: 5px; }
    </style>
</head>
<body>

    <div class="ibox-content">
        <div class="form-horizontal" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">原密码</div>
                <div class="layui-col-xs8">
                    <input type="password" id="Admins_Pwd_Old" name="Admins_Pwd_Old" class="layui-input no-space" maxlength="20" />
                    <label class="focus valid"></label>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">新密码</div>
                <div class="layui-col-xs8">
                    <input type="password" id="Admins_Pwd_New" name="Admins_Pwd_New" class="layui-input no-space" maxlength="20" />
                    <label class="focus valid"></label>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">确认新密码</div>
                <div class="layui-col-xs8">
                    <input type="password" id="Admins_Pwd_New_Again" name="Admins_Pwd_New_Again" class="layui-input no-space" maxlength="20" />
                    <div class="label-desc" style="color:#5f5b59">强密码规则：6-20位，英文字符、数字和特殊符号三种组合</div>
                    <label class="focus valid"></label>
                </div>

            </div>

            <div class="layui-row">
                <div class="layui-col-xs8 layui-col-xs-offset3">
                    <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> <t>保存</t></button>&nbsp;
                    <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> <t>关闭</t></button>
                </div>
            </div>
            @*<div class="layui-row">
            <div class="layui-col-xs3 edit-label ">&nbsp;</div>
            <div class="layui-col-xs8 layui-col-xs-offset3 spanDesc">
            密码长度六位及六位以上，密码中必须为字母与数字组合
            </div>
            </div>*@
        </div>
    </div>

    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/content.min.js?v=1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jsencrypt.min.js"></script>
    <script>
        myVerify.init();
        var index = parent.layer.getFrameIndex(window.name);
        var pwdType = '@(carparking.BLL.Cache.AppBasicCache.CurrentSysConfigContent?.SysConfig_PwdType ?? 1)';
        //parent.layer.iframeAuto(index); //弹层自适应大小

        //加载checkbox样式 (赋值需使用iCheck('check')方法)
        //$(document).ready(function () { $(".i-checks").iCheck({ checkboxClass: "icheckbox_square-green", radioClass: "iradio_square-green" }) });

        var pager = {
            init: function () {
                this.bindEvent();
            },

            bindEvent: function () {

                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    //验证表单
                    if (!verifyCheck._click()) return;

                    //var pwdStrength = pager.CheckPwdStrength($("#Admins_Pwd_New").val());
                    //if (parseInt(pwdStrength) < 2) {
                    //    $("#Admins_Pwd_New").val('');
                    //    $("#Admins_Pwd_New_Again").val('');
                    //    layer.msg('密码必须为字母与数字组合.', { icon: 2 });
                    //    return;
                    //}

                    //if ($("#Admins_Pwd_New").val() == "") { layer.msg('请输入新密码.', { icon: 7 });return;}
                    //if ($("#Admins_Pwd_New_Again").val() == "") { layer.msg('请再次输入新密码(确认).', { icon: 7 });return;}


                    if ($("#Admins_Pwd_New").val().trim() != $("#Admins_Pwd_New_Again").val().trim()) {
                        layer.msg('新密码与确认新密码不一致.', { icon: 2 });
                        return;
                    }

                    //获取所有表单元素
                    var param = $(".form-horizontal").formToJSON(true, function (data) {
                        data["Admins_Pwd_Old"] = $("#Admins_Pwd_Old").val().trim(); //密码MD5加密
                        data["Admins_Pwd_New"] = $("#Admins_Pwd_New").val().trim();
                        data["Admins_Pwd_New_Again"] = $("#Admins_Pwd_New_Again").val().trim();
                        return data;
                    });
                    if (pwdType!=1 &&!isStrongPassword($("#Admins_Pwd_New").val())) {
                        var frm = layer.confirm('您的密码为弱密码,请立即修改密码', {
                            title: '<font color="#fbba49">密码风险提醒</font>',
                            btn: ['立即修改', '以后再说'],
                            btnAlign: 'c',
                        }, function () {
                            layer.close(frm);
                            $("#Admins_Pwd_New").focus();
                        }, function () {
                            layer.close(frm);
                            layer.msg("处理中", { icon: 16, time: 10000 });

                            var data = { jsonModel: JSON.stringify(param) };

                            var encrypt = new JSEncrypt();
                            encrypt.setPublicKey("@Html.Raw(carparking.Common.RSAConfig.RsaPublicKey)");
                            var encrypted = encrypt.encrypt(JSON.stringify(data));

                            $("#Save").attr("disabled", true);
                            $.ajax({
                                type: 'post',
                                url: '/Index/PasswordUpdate',
                                dataType: 'json',
                                data: { encrypted: encrypted },
                                success: function (json) {
                                    if (json.Success) {
                                        layer.msg("保存成功", { icon: 1, time: 2000 }, function () {
                                            parent.layer.close(index);
                                            window.location.href = "../Login/";
                                        });

                                    } else {
                                        layer.msg(json.Message, { icon: 2 });
                                    }
                                },
                                complete: function () {
                                    $("#Save").attr("disabled", false);
                                },
                                error: function () {
                                    layer.msg("异常错误", { icon: 2 });
                                }
                            });
                        });
                    } else {
                        layer.msg("处理中", { icon: 16, time: 10000 });
                        var data = { jsonModel: JSON.stringify(param) };
                        
                        var encrypt = new JSEncrypt();
                        encrypt.setPublicKey("@Html.Raw(carparking.Common.RSAConfig.RsaPublicKey)");
                        var encrypted = encrypt.encrypt(JSON.stringify(data));

                        $("#Save").attr("disabled", true);
                                $.ajax({
                                    type: 'post',
                                    url: '/Index/PasswordUpdate',
                                    dataType: 'json',
                                    data: { encrypted: encrypted },
                                    success: function (json) {
                                        if (json.Success) {
                                            layer.msg("保存成功", { icon: 1, time: 2000 }, function () {
                                                parent.layer.close(index);
                                                window.location.href = "../Login/";
                                            });

                                        } else {
                                            layer.msg(json.Message, { icon: 2 });
                                        }
                                    },
                                    complete: function () {
                                        $("#Save").attr("disabled", false);
                                    },
                                    error: function () {
                                        layer.msg("异常错误", { icon: 2 });
                                    }
                              });
                    }
                });

                // 选取所有带有 class 'no-space' 的输入框
                $(".no-space").on("keydown", function (event) {
                    if (event.key === " ") {
                        event.preventDefault(); // 阻止空格输入
                    }
                }).on("input", function () {
                    $(this).val($(this).val().replace(/\s/g, "")); // 移除所有空格
                });

            },
            CheckPwdStrength: function (val) {
                var lv = 0;
                //字母+数字
                if (val.match(/^(?!\d+$)(?![a-zA-Z]+$)[a-zA-Z\d]+$/)) { lv = 2; }
                return lv;
            }
        };

        $(function () { pager.init() });

       
    </script>
</body>
</html>
