﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车辆登记</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v1.2.9" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }

        .help-btn { position: absolute; width: 20px; height: 20px; right: 5px; top: 2px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车辆管理</cite></a>
                <a><cite>访客车辆</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            
                            <div class="layui-row">
                                <div class="layui-inline">
                                    <input class="layui-input " name="Reserve_CarNo" id="Reserve_CarNo" autocomplete="off" placeholder="车牌号" />
                                </div>
                                <div class="layui-inline">
                                    <select id="Reserve_Status" name="Reserve_Status" lay-search>
                                        <option value="">状态</option>
                                        <option value="0" selected>已预约</option>
                                        <option value="1">已到达</option>
                                        <option value="4">已出场</option>
                                        <option value="2">已过期</option>
                                        <option value="3">已取消</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Reserve_StartTime0" id="Reserve_StartTime0" autocomplete="off" placeholder="预约入场时间起" value="@Html.Raw(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"))" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Reserve_StartTime1" id="Reserve_StartTime1" autocomplete="off" placeholder="预约入场时间止" value="@Html.Raw(DateTime.Now.ToString("yyyy-MM-dd 23:59:59"))" />
                                </div>
                                
                                <div class="layui-inline">
                                    <div class="operabar-if">更多条件</div>
                                </div>
                                <div class="layui-inline form-group">
                                    <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                </div>
                            </div>

                            <div class="layui-row search-more layui-hide">
                                <div class="layui-inline">
                                    <input class="layui-input " name="Reserve_EndTime0" id="Reserve_EndTime0" autocomplete="off" placeholder="预约出场时间起" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Reserve_EndTime1" id="Reserve_EndTime1" autocomplete="off" placeholder="预约出场时间止" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Reserve_Phone" id="Reserve_Phone" autocomplete="off" placeholder="手机号" maxlength="11" />
                                </div>
                                <div class="layui-inline">
                                    <select id="Reserve_Mode" name="Reserve_Mode" lay-search>
                                        <option value="">创建方式</option>
                                        <option value="1">平台下发</option>
                                        <option value="2">公众号创建</option>
                                        <option value="3">APP创建</option>
                                        <option value="4">小程序创建</option>
                                        <option value="5">线下创建</option>
                                        <option value="6">开放接口</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Add" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Update" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i><t>取消预约</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>

    <script>
        myVerify.init();
        topBar.init();

        var Power = window.parent.global.formPower;
        var comtable = null;
        s_carno_picker.init("Reserve_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "Reserve_CarNo") {
                $("#Reserve_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['table', 'element', 'form'], function () {
            var table = layui.table;

            pager.init();
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Reserve_No', title: '预约单号', hide: true}
                , { field: 'Reserve_CarNo', title: '车牌号' }
                //, { field: 'Reserve_OrderNo', title: '停车订单号' }
                , { field: 'Reserve_CardName', title: '车牌类型', hide: true}
                , { field: 'Reserve_CarTypeName', title: '车牌颜色', hide: true }
                , { field: 'Reserve_StartTime', title: '预约入场' }
                , { field: 'Reserve_EndTime', title: '预约出场' }
                , { field: 'Reserve_EnterTime', title: '入场时间', hide: true }
                , { field: 'Reserve_OutTime', title: '出场时间', hide: true }
                , { field: 'Reserve_Name', title: '姓名', hide: true}
                , { field: 'Reserve_Phone', title: '手机号', hide: true }
                , { field: 'Reserve_Remark', title: '备注' }
                , { field: 'Reserve_UseSpace', title: '占用车位', hide: true }
                , { field: 'Reserve_PersonNumber', title: '人数', hide: true}
                , { field: 'Reserve_AddTime', title: '创建时间' }
                , { field: 'Reserve_CancelTime', title: '取消预约时间', hide: true }
                , { field: 'Reserve_CancelRemark', title: '取消预约备注', hide: true }
                , {
                    field: 'Reserve_Status', title: '状态', templet: function (d) {
                        if (d.Reserve_Status == 0) return tempBar(2, "已预约");
                        else if (d.Reserve_Status == 1) return tempBar(1, "已到达");
                        else if (d.Reserve_Status == 2) return tempBar(0, "已过期");
                        else if (d.Reserve_Status == 3) return tempBar(4, "已取消");
                        else if (d.Reserve_Status == 4) return tempBar(3, "已出场");
                        else return "";
                    }
                }
                , {
                    field: 'Reserve_Mode', title: '创建方式', templet: function (d) {
                        if (d.Reserve_Mode == 1) return tempBar(1, "平台下发");
                        else if (d.Reserve_Mode == 5) return tempBar(2, "线下创建");
                        else if (d.Reserve_Mode == 2) return tempBar(3, "公众号创建");
                        else if (d.Reserve_Mode == 3) return tempBar(4, "APP创建");
                        else if (d.Reserve_Mode == 4) return tempBar(5, "小程序创建");
                        else if (d.Reserve_Mode == 6) return tempBar(6, "开放接口");
                        else return tempBar(3, "其他");
                    }
                }
                , { field: 'Reserve_Admin', title: '操作员' }
                , { field: 'Reserve_AdminName', title: '操作员名称', hide: true }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Reserve/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageindex = $(".layui-laypage-curr").text();
                pager.pageIndex = pageindex;
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增访客车辆",
                            content: '/Reserve/Edit?Act=Add',
                            area: getIframeArea(['600px', '550px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 2000 }); return; }
                        if (data[0].Reserve_Mode != 5) { layer.msg("仅支持编辑线下创建的访客车辆", { icon: 0, time: 2000 }); return; }
                        if (data[0].Reserve_Status == 1) { layer.msg("已到达不能修改", { icon: 0, time: 2000 }); return; }
                        if (data[0].Reserve_Status == 2) { layer.msg("已过期不能修改", { icon: 0, time: 2000 }); return; }
                        if (data[0].Reserve_Status == 3) { layer.msg("已取消不能修改", { icon: 0, time: 2000 }); return; }
                        if (data[0].Reserve_Status == 4) { layer.msg("已出场不能修改", { icon: 0, time: 2000 }); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑访客车辆",
                            content: '/Reserve/Edit?Act=Update&No=' + data[0].Reserve_No,
                            area: getIframeArea(['600px', '550px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var NoList = [];
                        $.each(data, function (k, v) {
                            NoList.push(v.Reserve_No);
                        })
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定取消访客车辆?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/Reserve/DeleteReserve", { Reserve_No: JSON.stringify(NoList) }, function (json) {
                                    if (json.success)
                                        layer.msg("取消成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

              //排序
            table.on('sort(com-table-base)', function(obj){
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox();
        });

        var pager = {
            sortField:null,
            orderField:null,
            carCarNoList: [],
            carNoList: [],
            carParam: {},
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.ajaxSettings.async = false;
               
                _DATE.bind(layui.laydate, ["Reserve_StartTime0", "Reserve_StartTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["Reserve_EndTime0", "Reserve_EndTime1"], { type: 'datetime', range: true });

                $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
                    var key = $(this).attr("data-key");
                    var data = getHelpContent(key);
                    if (data) {
                        layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
                    }
                }, function () {
                    layer.closeAll();
                });

                $.ajaxSettings.async = true;
                layui.form.render();
            },
            bindData: function (index) {
                layer.closeAll();

                pager.GetData(index);
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            },
            GetData: function (index) {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/Reserve/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) ,field: field,order: order} //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
        }


        function getStatus(ChargeRules_BeginTime, ChargeRules_EndTime) {
            if (ChargeRules_BeginTime && ChargeRules_EndTime) {

                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd hh:mm:ss"));
                ChargeRules_EndTime = new Date(new Date(ChargeRules_EndTime).Format("yyyy-MM-dd hh:mm:ss"));

                if (ChargeRules_EndTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return null;
            return data;
        }

        //提示信息数据
        var HelpData = [
            {
                key: "Car_EndDay",
                Description: ["剩余天数：车辆已过期，并且该车辆的结束时间过期超出*天 或者 车辆即将过期，并且该车辆的结束时间在未来*天即将过期"],
            }
        ];
    </script>




</body>
</html>
