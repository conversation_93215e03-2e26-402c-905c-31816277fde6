<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<!--- ServerGarbageCollection ： 服务器垃圾收集 ：不会让内存无限增长 -->
		<ServerGarbageCollection>false</ServerGarbageCollection>
		<!--- ServerGarbageCollection ： 并发垃圾收集 ：不会让内存无限增长 -->
		<ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
		<!--指定工作线程池的最小线程数-->
		<ThreadPoolMinThreads>1000</ThreadPoolMinThreads>
		<!--指定工作线程池的最大线程数-->
		<ThreadPoolMaxThreads>1000</ThreadPoolMaxThreads>
		<Version>10.0.17.1</Version>
		<TargetFramework>netcoreapp3.1</TargetFramework>
		<SatelliteResourceLanguages>zh-Hans</SatelliteResourceLanguages>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
		<RazorCompileOnBuild>false</RazorCompileOnBuild>
		<MvcRazorCompileOnPublish>true</MvcRazorCompileOnPublish>
		<AssemblyName>carparking_web</AssemblyName>
		<AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<LangVersion>latest</LangVersion>
		<StartupObject>carparking.Web.Program</StartupObject>
		<Platforms>AnyCPU;x86;x64;arm64</Platforms>
		<RuntimeIdentifiers>win7-x64;win7-x86;linux-x64;linux-arm64;</RuntimeIdentifiers>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>AnyCPU</PlatformTarget>
		<OutputPath>..\\Debug\\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>x86</PlatformTarget>
		<OutputPath>..\\Debug\\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>x64</PlatformTarget>
		<OutputPath>..\\Debug\\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|arm64'">
		<WarningLevel>5</WarningLevel>
		<RuntimeIdentifier>linux-arm64</RuntimeIdentifier>
		<PlatformTarget>arm64</PlatformTarget>
		<OutputPath>..\\Debug\\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>AnyCPU</PlatformTarget>
		<OutputPath>..\publish\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>x86</PlatformTarget>
		<OutputPath>..\publish\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>x64</PlatformTarget>
		<OutputPath>..\publish\Web</OutputPath>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|arm64'">
		<WarningLevel>5</WarningLevel>
		<PlatformTarget>arm64</PlatformTarget>
		<RuntimeIdentifier>linux-arm64</RuntimeIdentifier>
		<OutputPath>..\publish\Web</OutputPath>
	</PropertyGroup>
	<Target Name="resolveInteropOutputPath" BeforeTargets="ResolveComReferences" Condition="'@(COMReference)'!='' or '@(COMFileReference)'!=''">
		<PropertyGroup Condition=" '$(InteropOutputPath)' == '' ">
			<InteropOutputPath>$(MSBuildProjectDirectory)\$(IntermediateOutputPath)</InteropOutputPath>
		</PropertyGroup>
	</Target>
	<ItemGroup>
		<Compile Remove="Views\OutParkRecord\**" />
		<Compile Remove="Views\ReserveCar\**" />
		<Compile Remove="wwwroot\DOCM\**" />
		<Compile Remove="wwwroot\Exec\**" />
		<Compile Remove="wwwroot\Static\vcharts\**" />
		<Content Remove="Views\OutParkRecord\**" />
		<Content Remove="Views\ReserveCar\**" />
		<Content Remove="wwwroot\DOCM\**" />
		<Content Remove="wwwroot\Exec\**" />
		<Content Remove="wwwroot\Static\vcharts\**" />
		<EmbeddedResource Remove="Views\OutParkRecord\**" />
		<EmbeddedResource Remove="Views\ReserveCar\**" />
		<EmbeddedResource Remove="wwwroot\DOCM\**" />
		<EmbeddedResource Remove="wwwroot\Exec\**" />
		<EmbeddedResource Remove="wwwroot\Static\vcharts\**" />
		<None Remove="Views\OutParkRecord\**" />
		<None Remove="Views\ReserveCar\**" />
		<None Remove="wwwroot\DOCM\**" />
		<None Remove="wwwroot\Exec\**" />
		<None Remove="wwwroot\Static\vcharts\**" />
		<Compile Remove="Logs\**" />
		<EmbeddedResource Remove="Logs\**" />
		<Content Remove="Logs\**" />
		<None Remove="Logs\**" />
	</ItemGroup>
	<ItemGroup>
		<Compile Remove="Filters\Dongle.cs" />
	</ItemGroup>
	<ItemGroup>
		<Content Remove="Views\Car\Edit.cshtml" />
		<Content Remove="Views\Debug\Index.cshtml" />
		<Content Remove="Views\LrtTest\2x2-demo.cshtml" />
		<Content Remove="Views\LrtTest\demo.cshtml" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Mvc" Version="6.4.1" />
		<PackageReference Include="DotNetty.Transport" Version="0.7.5" />
		<PackageReference Include="Mapster" Version="7.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.16" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="3.1.16" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.ViewCompilation" Version="2.1.1" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="3.1.16" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.5" />
		<PackageReference Include="MySql.Data" Version="9.1.0" />
		<PackageReference Include="SqlSugarCore" Version="*********" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\carparking.CloudLink\carparking.CloudLink.csproj" />
		<ProjectReference Include="..\carparking.SDK\carparking.Library\carparking.Library.csproj" />
		<ProjectReference Include="..\carparking.SDK\carparking.PassTool\carparking.PassTool.csproj" />
		<ProjectReference Include="..\carparking.SentryBox\carparking.SentryBox.csproj" />
	</ItemGroup>
	<ItemGroup>
		<None Include="Views\ApiDemo\Debug.cshtml" />
		<None Include="Views\ApiDemo\Index.cshtml" />
		<None Include="Views\Debug\Index.cshtml" />
		<None Include="Views\LrtTest\2x2-demo.cshtml" />
		<None Include="Views\LrtTest\demo.cshtml" />
		<None Include="wwwroot\Static\plugins\echarts\echarts-code.js" />
		<None Include="wwwroot\Static\plugins\echarts\echarts.common.min.js" />
		<None Include="wwwroot\Static\plugins\echarts\echarts.min.js" />
		<None Include="wwwroot\Static\plugins\echarts\echarts.simple.min.js" />
		<None Include="wwwroot\Static\plugins\notification\snackbar\snackbar.min.js" />
		<None Include="wwwroot\Static\plugins\Nstep\bootstrap.min.js" />
		<None Include="wwwroot\Static\plugins\Nstep\jquery-3.4.1.slim.min.js" />
		<None Include="wwwroot\Static\plugins\Nstep\popper.min.js" />
		<None Include="wwwroot\Static\plugins\Nstep\tourguide.min.js" />
	</ItemGroup>
	<ItemGroup>
	  <Reference Include="cpi">
	    <HintPath>..\lib\cpi\cpi.dll</HintPath>
	  </Reference>
	</ItemGroup>
	<Target Name="PreBuild1" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
		<Exec Command="xcopy &quot;$(ProjectDir)..\lib\cpi\x86&quot; &quot;$(ProjectDir)..\Debug\Web&quot; /E/H/C/I/D/Y" />
	</Target>
	<Target Name="PreBuild2" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
		<PropertyGroup>
			<IsLinux Condition="'$(OS)' == 'Unix'">true</IsLinux>
		</PropertyGroup>
		<Exec Command="xcopy &quot;$(ProjectDir)..\lib\cpi\x64&quot; &quot;$(ProjectDir)..\Debug\Web&quot; /E/H/C/I/D/Y" Condition="'$(IsLinux)' != 'true'" />
	</Target>
	<Target Name="PreBuild3" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
		<Exec Command="xcopy &quot;$(ProjectDir)..\lib\cpi\x64&quot; &quot;$(ProjectDir)..\Debug\Web&quot; /E/H/C/I/D/Y" />
	</Target>
	<Target Name="PreBuild4" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
		<Exec Command="xcopy &quot;$(ProjectDir)..\lib\cpi\x86&quot; &quot;$(ProjectDir)..\publish\Web&quot; /E/H/C/I/D/Y" />
	</Target>
	<Target Name="PreBuild5" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
		<Exec Command="xcopy &quot;$(ProjectDir)..\lib\cpi\x64&quot; &quot;$(ProjectDir)..\publish\Web&quot; /E/H/C/I/D/Y" />
	</Target>
	<Target Name="PreBuild6" AfterTargets="PostBuildEvent" Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
		<Exec Command="xcopy &quot;$(ProjectDir)..\lib\cpi\x64&quot; &quot;$(ProjectDir)..\publish\Web&quot; /E/H/C/I/D/Y" />
	</Target>
	
	<ItemGroup>
		<Content Update="Views\ExcepAccess\Detail.cshtml">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\ExcepAccess\Index.cshtml">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Index\Index.cshtml">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Index\Index.cshtml">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Monitoring\ParkOrderDetail.cshtml">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\DataBackup\DownFile.cshtml">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\Owner\BathEditStopSpace.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\RptCarMonth\Index.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\RptCarStore\Index.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="Views\RptMonth\RptView.cshtml">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="wwwroot\Data\car_template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\Data\order_template.xlsx">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\DB\libeay32.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\DB\mysqldump.exe">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\DB\ssleay32.dll">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\install.bat">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\Static\img\login-bg.png">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\Static\flveee\decoder-pro.js">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="wwwroot\Static\flveee\decoder-pro.wasm">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="wwwroot\Static\operationscenter\css\largefont.css">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="wwwroot\Static\operationscenter\player_new.js">
		  <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
		  <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Update="wwwroot\Static\plugins\layui\font\iconfont.ttf">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\Static\plugins\layui\layui.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\Static\plugins\layui\lay\modules\upload.js">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\uninstall.bat">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	
</Project>
