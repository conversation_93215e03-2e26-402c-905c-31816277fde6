﻿using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Charge.Models;
using carparking.ChargeModels;
using carparking.Common;
using carparking.Config;
using carparking.DAL;
using carparking.Model;
using carparking.Model.API;
using carparking.Model.Sys;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carparking.PassTool
{
    /// <summary>
    /// 车道出入通行辅助类
    /// </summary>
    public class PassHelper
    {
        /// <summary>
        /// 检测车辆通行权限
        /// </summary>
        /// <param name="camreano">相机编码</param>
        /// <param name="carno">车牌号</param>
        /// <param name="cartype">车牌颜色</param>
        /// <param name="time">时间</param>
        /// <returns></returns>
        public static Model.ResultPass OnCheckCarPass(Model.ParkCarInOut CarObj, Model.OnCheckCache onCheckCache = null)
        {
            //System.Diagnostics.Stopwatch swCheckPass = new System.Diagnostics.Stopwatch();
            //swCheckPass.Start();
            Model.Device camera = null;
            if (onCheckCache?.devices?.Count > 0)
                camera = onCheckCache.devices.Find(x => x.Device_No == CarObj.camerano);
            else
                camera = BLL.Device.GetEntity(CarObj.camerano);

            var carRecog = PassHelperBiz.AddCarRecog(CarObj, camera, null, null, out var smalllocimg, onCheckCache, false);

            if (!string.IsNullOrEmpty(CarObj.carno))
                CarObj.carno = CarObj.carno.Replace("\n", "").Replace("\r", "");

            Model.ResultPass result = new ResultPass();
            result.recog = carRecog;
            result.time = CarObj.time;
            result.isVideoRecord = CarObj.isVideoRecord;
            result.isSupplement = CarObj.isSupplement;
            //if (AppSettingConfig.SentryMode != VersionEnum.CloudServer) result.isSupplement = true;

            //出口识别查找无入场记录自动放行
            bool isAutoNoenterRecord = false;

            #region 基础条件判断

            if (!Utils.IsZhNumEn(CarObj.carno))
            {
                result.success = false; result.errcode = ResultPassCode.ERROR_CARNO;
                result.errmsg = $"车牌号仅支持(中文、字母、数字)组成";
                PassHelperBiz.UpdateRecog(ref result, carRecog);
                return result;
            }
            CarObj.carno = CarObj.oldcarno = CarObj.carno.ToUpper();

            Model.Car car = null;
            //刷卡通行
            if (CarObj.mode == 4)
            {
                car = BLL.Car.GetEntityByCardNo(CarObj.carno);
                if (car == null)
                {
                    result.success = false;
                    result.errcode = ResultPassCode.ERROR_CARD_NOEXIST;
                    result.errmsg = $"卡号[{CarObj.carno}]未登记,禁止通行";
                    PassHelperBiz.UpdateRecog(ref result, carRecog);
                    return result;
                }
                CarObj.carno = car.Car_CarNo;
            }

            Model.Parking parking = null;
            if (onCheckCache?.parking != null)
                parking = onCheckCache.parking;
            else
                parking = BLL.Parking.GetEntity(CarObj.parkno);
            if (parking == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_PARK_NOTERXIST;
                result.errmsg = $"车场不存在,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog);
                return result;
            }

            if (camera == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_DEVICE_NOTERXIST;
                result.errmsg = $"相机不存在,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog);
                return result;
            }


            //swCheckPass.Stop();
            //result.watchMessage += $"基础条件判断：{swCheckPass.ElapsedMilliseconds} 毫秒，";


            //swCheckPass.Start();
            Model.Parking park = null;
            Model.Device dev = null;
            Model.Passway passway = null;
            Model.PolicyPassway policyPassway = null;
            List<Model.PasswayLink> passwayLinks = null;
            List<Model.ParkArea> parkAreas = null;

            if (onCheckCache?.parkareas == null || onCheckCache?.parkareas?.Count == 0)
            {
                BLL.Pass.OnCheckCarPass(CarObj.parkno, CarObj.camerano, camera.Device_PasswayNo
                    , out park
                    , out dev
                    , out passway
                    , out policyPassway
                    , out passwayLinks
                    , out parkAreas);
            }
            else
            {
                park = onCheckCache.parking;
                dev = camera;
                passway = onCheckCache.passwayes.Find(x => x.Passway_No == camera.Device_PasswayNo);
                policyPassway = onCheckCache.policypassways.Find(x => x.PolicyPassway_PasswayNo == camera.Device_PasswayNo);
                passwayLinks = onCheckCache.passwaylinks.FindAll(x => x.PasswayLink_PasswayNo == camera.Device_PasswayNo);
                parkAreas = onCheckCache.parkareas.FindAll(x => x.ParkArea_ParkNo == CarObj.parkno);

                if (parkAreas == null || parkAreas.Count == 0 || park == null || dev == null || passway == null || policyPassway == null
                    || passwayLinks == null || passwayLinks.Count == 0 || parkAreas == null || parkAreas.Count == 0)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{CarObj.carno}基础数据缓存失效:{parkAreas == null},{parkAreas?.Count},{park == null}," +
                        $"{dev == null},{passway == null},{policyPassway == null},{passwayLinks == null},{passwayLinks?.Count},{parkAreas == null},{parkAreas?.Count}");
                    BLL.Pass.OnCheckCarPass(CarObj.parkno, CarObj.camerano, camera.Device_PasswayNo
                   , out park
                   , out dev
                   , out passway
                   , out policyPassway
                   , out passwayLinks
                   , out parkAreas);
                }
            }

            if (passway == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_PASSWAY_NOTEXIST;
                result.errmsg = $"车道不存在,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera);
                return result;
            }
            if (policyPassway == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_PASSWAY_NOTEXIST;
                result.errmsg = $"车道策略不存在,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera);
                return result;
            }

            int gate = BLL.Passway.GetPasswayGateType(passway.Passway_No, onCheckCache?.passwaylinks, onCheckCache?.parkareas);
            int? matchMode = (CarObj.mode == 6 ? 1 : policyPassway.PolicyPassway_CarMatch ?? 1);//手动输入仅支持完全匹配，1-完全匹配，2-允许任意1位字母错误，3-只允许首字母错误

            Model.Owner owner = null;
            Model.CarCardType carCardType = null;
            Model.CarType carType = null;
            Model.Reserve reserve = null;
            Model.BusinessCar businessCar = null;

            Model.ParkOrder curOrder = PassHelperBiz.MatchParkOrder(CarObj, 1);
            if (curOrder != null) { matchMode = 1; }

            if (CarObj.changeCarCardType)
            {
                carCardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, CarObj.carcardtype);
            }

            //查询访客车辆
            PassHelperBiz.MacthReserve(curOrder, gate == 0 ? 201 : 200, CarObj.carno, matchMode, out reserve, ref car, ref carCardType, ref carType, onCheckCache?.cartypes, onCheckCache?.carcardtypes);
            //访客车辆的订单号
            if (reserve != null) { if (car != null) { if (CarObj.carno != car.Car_CarNo) { if (gate == 0 || gate == 3) { var order = PassHelperBiz.MatchParkOrder(new ParkCarInOut() { carno = car.Car_CarNo }, 1); if (order != null) { curOrder = order; matchMode = 1; CarObj.carno = car.Car_CarNo; CarObj.reserveno = reserve.Reserve_No; } else { car = null; carCardType = null; carType = null; reserve = null; } } else { CarObj.carno = car?.Car_CarNo ?? CarObj.carno; CarObj.reserveno = reserve.Reserve_No; } } else { CarObj.reserveno = reserve.Reserve_No; } } }
            //查询商家车
            else { PassHelperBiz.MacthBusinessCar(CarObj.time.Value, CarObj.carno, matchMode, out businessCar, ref car, ref carCardType, ref carType, onCheckCache?.cartypes, onCheckCache?.carcardtypes); }
            if (businessCar != null) { if (car != null && CarObj.carno != car.Car_CarNo) { if (gate == 0 || gate == 3) { var order = PassHelperBiz.MatchParkOrder(new ParkCarInOut() { carno = car.Car_CarNo }, 1); if (order != null) { curOrder = order; matchMode = 1; CarObj.carno = car.Car_CarNo; } else { car = null; carCardType = null; carType = null; businessCar = null; } } else { CarObj.carno = car?.Car_CarNo ?? CarObj.carno; } } }
            //查询月租车
            if (car == null && carCardType?.CarCardType_Type != 5) { car = PassHelperBiz.MatchCar(CarObj.carno, matchMode); if (car != null && CarObj.carno != car.Car_CarNo) { if (gate == 0 || gate == 3) { var order = PassHelperBiz.MatchParkOrder(new ParkCarInOut() { carno = car.Car_CarNo }, 1); if (order != null) { curOrder = order; matchMode = 1; CarObj.carno = car?.Car_CarNo; } else { car = null; carCardType = null; carType = null; } } else { CarObj.carno = car?.Car_CarNo ?? CarObj.carno; } } }
            if (owner == null) owner = !string.IsNullOrEmpty(car?.Car_OwnerNo) ? BLL.Owner.GetEntity(car?.Car_OwnerNo) : null;
            if (car != null && owner != null)
            {
                car.Car_BeginTime = owner.Owner_StartTime;
                car.Car_EndTime = owner.Owner_EndTime;
            }

            //教练车入场
            if (gate == 1 && AppBasicCache.GetPolicyPark.PolicyPark_CoachCar == 1 && car == null && CarObj.carno.Contains("学"))
            {
                if (!string.IsNullOrEmpty(AppBasicCache.GetPolicyPark?.PolicyPark_CoachCarType) && !CarObj.changeCarCardType) carCardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, AppBasicCache.GetPolicyPark?.PolicyPark_CoachCarType);
                if (!string.IsNullOrEmpty(AppBasicCache.GetPolicyPark?.PolicyPark_CoachCarColor) && !CarObj.changeCarType) carType = AppBasicCache.GetElement(AppBasicCache.GetCarTypes, AppBasicCache.GetPolicyPark?.PolicyPark_CoachCarColor);
            }

            if (curOrder == null && matchMode != 1)
            {
                curOrder = PassHelperBiz.MatchParkOrder(CarObj, matchMode);
            }
            if (reserve != null) { if (car?.Car_CarNo != reserve.Reserve_CarNo || (curOrder != null && curOrder.ParkOrder_CarNo != reserve.Reserve_CarNo)) { CarObj.reserveno = ""; } }


            //车辆在场内车道识别进出，车牌类型都以进外场时创建订单的车牌类型为准
            if (curOrder != null && AppBasicCache.GetPolicyPark?.PolicyPark_EnterCarCardType == 1)
            {
                if (carCardType == null && gate != 1) carCardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, curOrder.ParkOrder_CarCardType);
            }

            //出口判断
            if (gate == 0 || gate == 3)
            {
                if (!CarObj.changeCarType && carType == null)
                {
                    if (curOrder != null)
                    {
                        //停车订单是否修改过车牌颜色，如果修改过则查询订单，以订单的车牌颜色为准
                        var manualRecord = AppBasicCache.GetElement(AppBasicCache.GetAllManualRecord, curOrder.ParkOrder_CarNo);
                        if (manualRecord != null && manualRecord.ManualRecord_ParkOrderNo == curOrder.ParkOrder_No)
                        {
                            carType = AppBasicCache.GetElement(AppBasicCache.GetCarTypes, curOrder.ParkOrder_CarType);
                        }
                    }
                }

                if (curOrder != null)
                {
                    if (carCardType == null)
                    {
                        carCardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, curOrder.ParkOrder_CarCardType);
                    }

                    if (carType == null)
                    {
                        carCardType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, curOrder.ParkOrder_CarType);
                    }
                }
            }

            if (carCardType == null || CarObj.changeCarCardType) carCardType = PassHelperBiz.GetCarCardType(CarObj, car, null, policyPassway, onCheckCache?.carcardtypes);
            if (carCardType == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_CARD_NOTERXIST;
                result.errmsg = $"未匹配到对应车牌类型,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);
                return result;
            }

            if (carType == null || CarObj.changeCarType) carType = PassHelperBiz.GetCarType(CarObj.cartype, car, policyPassway, CarObj.carno, onCheckCache?.cartypes, CarObj.changeCarType);
            if (carType == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_CARTYPE_NOTERXIST;
                result.errmsg = $"未匹配到对应车牌颜色,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);
                return result;
            }

            //放行策略
            Model.PolicyPass policyPass = BLL.PolicyPass.GetEntityByWayAndCardType(passway.Passway_No, carCardType.CarCardType_No, onCheckCache?.policypasses);
            if (policyPass == null)
            {
                result.success = false;
                result.errcode = ResultPassCode.ERROR_POLICY_NOTERXIST;
                result.errmsg = $"未找到[{carCardType.CarCardType_Name}]类型放行策略,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);
                return result;
            }
            //swCheckPass.Stop();
            //result.watchMessage += $"基础条件判断：{swCheckPass.ElapsedMilliseconds} 毫秒，";

            var isInParkForConfirm = false;//是否车辆已在场内
            if (curOrder != null && curOrder.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
            {
                isInParkForConfirm = true;
            }

            #endregion

            #region 车道策略 & 车场全局策略
            //swCheckPass.Start();
            Model.PolicyPark policyPark = null;
            policyPark = onCheckCache?.policyparkes?.Find(x => x.PolicyPark_ParkNo == CarObj.parkno);
            if (policyPark == null) policyPark = BLL.PolicyPark.GetEntity(CarObj.parkno);

            result.policy = new Model.ResultPolicy();
            result.policy.wayscan = policyPassway.PolicyPassway_Scan;
            result.policy.waywaittimeout = policyPassway.PolicyPassway_WaitTimeOut;
            result.policy.waytimeouthandle = policyPassway.PolicyPassway_TimeOutHandle;
            result.policy.payedconfirm = policyPark?.PolicyPark_PayedConfirm;
            result.policy.nopwdpayedconfirm = policyPark?.PolicyPark_NoPwdPayedConfirm;
            result.policy.maxdiscount = policyPark?.PolicyPark_MaxDiscount;
            result.policy.allowcancel = policyPark?.PolicyPark_CollectAllowCancel;
            result.policy.broadparktime = policyPassway.PolicyPassway_Broadparktime;
            result.policy.broadpushinfo = policyPassway.PolicyPassway_Broadpushinfo;
            result.policy.broadpushtext = policyPassway.PolicyPassway_Broadpushtext;
            result.policy.broadspacenum = policyPassway.PolicyPassway_Broadspacenum;
            result.policy.broadspace = policyPassway.PolicyPassway_Broadspace;
            result.policy.broadtype = policyPassway.PolicyPassway_ShowOption;
            result.policy.broadvalue = policyPassway.PolicyPassway_Show;
            result.policy.floatlen = policyPark.PolicyPark_Floatlen;
            result.policy.takerecord = policyPassway.PolicyPassway_TakeRecord != 1 ? (policyPark?.PolicyPark_AutoTakerecord ?? 0) : policyPassway.PolicyPassway_TakeRecord;
            result.policy.autotakerecord = policyPark?.PolicyPark_AutoTakerecord ?? 0;
            result.policy.tempchinese = policyPark?.PolicyPark_TempChineseBroadcast ?? 0;
            result.policy.isinoutsound = policyPassway.PolicyPassway_IsInoutSound ?? 1;
            result.policy.iS017UQR = policyPassway.PolicyPassway_S017UQR ?? 0;

            //无牌车（修改车牌颜色，若未设置指定车牌颜色，则搜索默认带“无”字的车牌颜色）
            bool isNoPalteCar = false;
            if (!string.IsNullOrEmpty(CarObj.orderno) && (CarObj.mode == 2 || CarObj.mode == 5))
            {
                if (!string.IsNullOrWhiteSpace(CarObj.cartype) && CarObj.mode == 2)
                {
                    var list = Newtonsoft.Json.JsonConvert.DeserializeObject<List<string>>(CarObj.cartype);
                    if (list != null && list.Count > 0)
                    {
                        var cartypeno = list[0];
                        carType = onCheckCache?.cartypes?.FirstOrDefault(x => x.CarType_No == cartypeno);
                        if (carType == null)
                        {
                            carType = BLL.CarType.GetEntity("*", $"CarType_No='{cartypeno}'");
                        }
                    }
                }


                if ((gate == 1 || gate == 2) && policyPark != null && !string.IsNullOrEmpty(policyPark.PolicyPark_DefaultNoneCarType))
                {
                    var parkCarType = onCheckCache?.cartypes?.Find(x => x.CarType_No == policyPark.PolicyPark_DefaultNoneCarType);
                    if (parkCarType == null) carType = BLL.CarType.GetEntity(policyPark.PolicyPark_DefaultNoneCarType);
                    if (parkCarType != null)
                    {
                        carType = parkCarType;
                    }
                }
                else
                {
                    if (carType == null)
                    {
                        carType = onCheckCache?.cartypes?.Find(x => x.CarType_Name == "蓝牌车");
                        if (carType == null) carType = BLL.CarType.GetEntity("*", $"CarType_Name='蓝牌车'");
                    }
                }

                if (carType == null)
                {
                    if (car != null)
                        car.Car_VehicleTypeNo = carType?.CarType_No ?? car.Car_VehicleTypeNo;
                    else
                    {
                        if (carType == null)
                        {
                            carType = onCheckCache?.cartypes?.First();
                            if (carType == null) carType = BLL.CarType.GetEntity("*", $"1=1 limit 1");
                        }
                    }
                }

                isNoPalteCar = true;
            }
            //swCheckPass.Stop();
            //result.watchMessage += $"策略查询：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            #endregion

            #region 放行结果
            result.success = true;
            result.passres = new Model.ResultPassData();
            result.passres.type = gate == 0 ? 201 : 200;
            result.passres.fycode = 0;
            result.passres.carno = CarObj.carno;
            result.passres.passway = passway;
            result.passres.car = car;
            result.passres.cartype = carType;
            result.passres.carcardtype = carCardType;
            result.passres.delay = camera.Device_Delay;
            result.passres.localimage = CarObj.img;
            result.passres.localimagesmall = CarObj.imgsmall;
            result.passres.img = carRecog.CarRecog_Img;
            result.passres.expday = -1000;//不播报固定车辆有效期时,默认值-1000
            result.passres.dReminderRecharge = -1000;//不播报
            result.passres.device = camera;
            result.passres.owner = owner;
            result.passres.gate = gate;
            result.passres.useDeviceTime = CarObj.useDeviceTime;
            result.passres.isInParkForConfirm = isInParkForConfirm;
            #endregion

            #region 黑名单检测
            //swCheckPass.Start();
            var isblack = PassHelperBiz.IsBlack(CarObj.carno, CarObj.time.Value, matchMode, out var blackCar);
            //swCheckPass.Stop();
            //result.watchMessage += $"黑名单检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            if (isblack)
            {
                //判断黑名单权限
                // 0 - 全部禁止
                // 1 - 出口禁止
                // 2 - 入口禁止
                if (blackCar.BlackList_AccessControl == 0 || ((gate == 1 || gate == 2) && blackCar.BlackList_AccessControl == 2) || ((gate == 0 || gate == 3) && blackCar.BlackList_AccessControl == 1))
                {
                    carRecog.CarRecog_IsOpen = 10;
                    result.success = false;
                    result.errcode = ResultPassCode.ERROR_BLACKLIST;
                    result.errmsg = $"黑名单,禁止通行：{blackCar.BlackList_Remark}";
                    PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                    return result;
                }
            }
            #endregion

            #region 无效车牌
            if (!string.IsNullOrEmpty(policyPassway.PolicyPassway_CarInvalid) && !string.IsNullOrEmpty(CarObj.carno))
            {
                policyPassway.PolicyPassway_CarInvalid = policyPassway.PolicyPassway_CarInvalid.Replace("，", ",");
                var charList = policyPassway.PolicyPassway_CarInvalid.Split(',').ToList();
                foreach (var c in charList)
                {
                    if (c.Contains(CarObj.carno) || c.Contains(CarObj.carno.Substring(1)) || CarObj.carno.Contains(c))
                    {
                        result.passres.passcode = result.passres.code = 0;
                        result.passres.errcode = ResultPassCode.ERROR_CARNO_INVALID;
                        result.passres.errmsg = "无效车牌,禁止通行";
                        PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);
                        return result;
                    }
                }
            }
            #endregion

            #region 白牌车/军警车检测

            Model.PolicyArea policyArea = new Model.PolicyArea();
            Model.ResultInOutList resultData = new ResultInOutList();

            bool isWhiteCar = false, isMilitaryPoliceCar = false;
            if (policyPark.PolicyPark_WhiteCar == 1 && carType?.CarType_Name.Trim() == "白牌车")
            {
                resultData = PassHelperBiz.WhiteCarIsPass(gate, 1, passway, CarObj, parkAreas, passwayLinks, policyPark, policyPassway, policyPass, carCardType, carType, car, out policyArea, onCheckCache?.policyareaes, curOrder);
                isWhiteCar = true;
            }

            if (policyPark.PolicyPark_MilitaryPoliceCar == 1 && GoHelper.IsMilitaryOrPolicePlate(CarObj.carno))
            {
                resultData = PassHelperBiz.WhiteCarIsPass(gate, 1, passway, CarObj, parkAreas, passwayLinks, policyPark, policyPassway, policyPass, carCardType, carType, car, out policyArea, onCheckCache?.policyareaes, curOrder);
                isMilitaryPoliceCar = true;
            }

            #endregion

            #region 常规放行处理
            if (!isWhiteCar && !isMilitaryPoliceCar)
            {
                if (policyPass.PolicyPass_Pass == 0)
                {
                    result.passres.passcode = result.passres.code = 0;
                    result.passres.errcode = ResultPassCode.ERROR_PASSWAY_STOP;
                    result.passres.errmsg = $"车道[{passway.Passway_Name}]禁止[{carCardType.CarCardType_Name}]通行";
                    PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                    return result;
                }
                else
                {
                    result.passres.passcode = result.passres.code = policyPass.PolicyPass_Pass;
                    if (result.passres.code == 2)
                    {
                        result.passres.errmsg = $"{carCardType.CarCardType_Name} 确认放行";
                    }
                }
            }
            #endregion

            #region 开启车道授权
            if (!isWhiteCar && !isMilitaryPoliceCar && policyPassway.PolicyPassway_IsAuthorize == 1)
            {
                //swCheckPass.Start();
                //读取授权时间列表
                var accesseList = PassHelperBiz.GetAccessAuth(passway.Passway_No, carCardType.CarCardType_No, carType.CarType_No, carCardType.CarCardType_Category, onCheckCache?.cartypes, onCheckCache?.accessauthes);
                if (accesseList != null && accesseList.Count > 0)
                {
                    var accData = PassHelperBiz.IsTimeAccess(accesseList, CarObj);
                    if (accData.code == 0)
                    {
                        result.passres.passcode = result.passres.code = 0;
                        result.passres.errcode = ResultPassCode.ERROR_AUTH_TIME;
                        result.passres.errmsg = "不在授权时间范围内,禁止通行";
                        PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                        return result;
                    }
                    else
                    {
                        if (result.passres.code < accData.code)
                        {
                            result.passres.passcode = result.passres.code = accData.code;
                            result.passres.errmsg = "通行控制授权时间范围内确认放行";
                        }
                    }
                }
                //swCheckPass.Stop();
                //result.watchMessage += $"车道授权检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            }

            string endNumVoice = string.Empty;//尾号限行语音提示
            bool isEndNumAuth = false;//是否触发尾号限行
            int policyPassNumPass = 0;//尾号限行开闸方式
            if (policyPassway.PolicyPassway_IsNumAuthorize == 1)
            {
                //swCheckPass.Start();
                //读取限行规则列表
                List<Model.EndNumAuth> accesseList = null;

                accesseList = onCheckCache?.endnumauthes;
                if (accesseList == null || accesseList.Count == 0) accesseList = BLL.EndNumAuth.GetAllEntity("*", "EndNumAuth_Status=1");

                if (accesseList != null && accesseList.Any(m => m.EndNumAuth_Status == 1))
                {
                    var accData = PassHelperBiz.IsNumTimeAccess(accesseList.Where(m => m.EndNumAuth_Status == 1), CarObj);
                    if (accData.code == 1)
                    {
                        isEndNumAuth = true;
                        endNumVoice = accData.msg;//尾号限行语音
                        if (policyPass.PolicyPass_NumPass == 0)//如果禁止通行，则直接返回
                        {
                            result.passres.code = 0;
                            result.passres.errcode = ResultPassCode.ERROR_AUTH_CARNUM;
                            result.passres.errmsg = $"车牌尾号【{accData.data}】,禁止通行";
                            if (!string.IsNullOrEmpty(endNumVoice))
                            {
                                result.policy.broadtype = 5;
                                result.policy.broadvalue = endNumVoice;
                            }
                            PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                            return result;
                        }
                        policyPass.PolicyPass_NumPass = policyPass.PolicyPass_NumPass ?? 1;
                        policyPassNumPass = policyPass.PolicyPass_NumPass.Value;
                        result.passres.code = result.passres.code < policyPass.PolicyPass_NumPass ? policyPass.PolicyPass_NumPass : result.passres.code;
                        result.passres.errmsg = $"车牌尾号【{accData.data}】";
                    }
                }
                //swCheckPass.Stop();
                //result.watchMessage += $"限行规则检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            }
            #endregion

            #region 判断固定车是否过期
            if (!isWhiteCar && !isMilitaryPoliceCar && car != null && carCardType.CarCardType_Category != Model.EnumCarType.Visitor.ToString())
            {
                //swCheckPass.Start();
                var isExpired = (car.Car_BeginTime == null || car.Car_EndTime == null || car?.Car_BeginTime > DateTimeHelper.GetNowTime());

                Model.PolicyCarCard policyCarCard = null;
                policyCarCard = onCheckCache?.policycarcardes?.Find(x => x.PolicyCarCard_CarCardTypeNo == carCardType.CarCardType_No);
                if (policyCarCard == null) policyCarCard = BLL.PolicyCarCard.GetEntityByCarCard(carCardType.CarCardType_No);

                //月租车
                if (Model.CarCardTypeEnumList.carMonth.Contains(Utils.StrToInt(car.Car_Category)) || Model.CarCardTypeEnumList.carFree.Contains(Utils.StrToInt(car.Car_Category)))
                {
                    //月租车宽限天数
                    var cardPolicyDays = double.Parse((policyCarCard?.PolicyCarCard_AllowExpireDay ?? 0).ToString());
                    isExpired = isExpired || (car?.Car_EndTime.Value.AddDays(cardPolicyDays) < DateTimeHelper.GetNowTime());
                }
                else
                {
                    isExpired = isExpired || (car.Car_EndTime < DateTimeHelper.GetNowTime());
                }

                //储值车
                if (carCardType.CarCardType_Category == Model.EnumCarType.Prepaid.ToString())
                {
                    isExpired = false;
                }

                if (isExpired)
                {
                    if (policyPass.PolicyPass_IsExpire == 0)
                    {
                        result.passres.code = 0;
                        result.passres.errcode = ResultPassCode.ERROR_EXPIRED;
                        result.passres.errmsg = $"{carCardType.CarCardType_Name}已过期,禁止通行";
                        PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                        return result;
                    }
                    else
                    {
                        result.passres.code = result.passres.code < policyPass.PolicyPass_IsExpire ? policyPass.PolicyPass_IsExpire : result.passres.code;
                        result.passres.errmsg = $"{carCardType.CarCardType_Name}已过期";
                        if (result.passres.code == 2) result.passres.reminderinfo = $"{car?.Car_CarNo} {carCardType.CarCardType_Name}已过期";

                        int days = 0;
                        var currentTime = car.Car_BeginTime > DateTimeHelper.GetNowTime() ? car.Car_BeginTime : DateTimeHelper.GetNowTime();
                        var d = (car.Car_EndTime - currentTime).Value.TotalDays;
                        if (d < 0)
                        {
                            d = -d;
                            days = -int.Parse(Math.Ceiling(d).ToString());
                        }
                        else
                        {
                            days = int.Parse(Math.Ceiling(d).ToString());
                        }
                        result.passres.expday = days;
                    }
                }
                else
                {
                    if (Model.CarCardTypeEnumList.carMonth.Contains(Utils.StrToInt(car.Car_Category)) || Model.CarCardTypeEnumList.carFree.Contains(Utils.StrToInt(car.Car_Category)))
                    {
                        //提醒车主月租车即将到期
                        var currentTime = car.Car_BeginTime > DateTimeHelper.GetNowTime() ? car.Car_BeginTime : DateTimeHelper.GetNowTime();
                        int days = int.Parse(Math.Ceiling((car.Car_EndTime - currentTime).Value.TotalDays).ToString());
                        if (policyCarCard != null && policyCarCard.PolicyCarCard_RemindDay > 0 && days > 0 && days < policyCarCard.PolicyCarCard_RemindDay)
                            result.passres.expday = days;

                    }
                    result.passres.dReminderRecharge = policyCarCard?.PolicyCarCard_BalanceRemind;
                }
                //swCheckPass.Stop();
                //result.watchMessage += $"固定车过期检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            }
            #endregion

            #region 出入场
            //swCheckPass.Start();
            string localImgUrlEncode = System.Web.HttpUtility.UrlEncode(result.passres.localimage);
            if (!isWhiteCar && !isMilitaryPoliceCar)
            {
                //判断车辆是否能够从当前车道出入，返回订单、明细实体
                resultData = PassHelperBiz.IsPass(result.passres.code, passway, CarObj, parkAreas, passwayLinks, policyPark, policyPassway, policyPass, carCardType, carType, car, out policyArea, onCheckCache?.policyareaes, onCheckCache?.carcardtypes, onCheckCache?.policycarcardes, curOrder: curOrder, rOrderNo: CarObj.rOrderno);
            }
            //swCheckPass.Stop();
            //result.watchMessage += $"出入场检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";

            result.resorder = resultData;
            result.passres.areano = policyArea?.PolicyArea_ParkAreaNo;
            result.passres.passcode = result.passres.code;

            if (resultData.resIn != null)
            {
                if (isNoPalteCar && resultData.resIn.parkorder != null)
                {
                    resultData.resIn.parkorder.ParkOrder_IsUnlicensedCar = 1;
                }

                result.passres.errcode = resultData.resIn.errcode;
                result.passres.passcode = result.passres.code = resultData.resIn.code;
                result.passres.errmsg = resultData.resIn.errmsg ?? result.passres.errmsg;
                if (result.passres.code == 0)
                {
                    PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);
                    return result;
                }
            }

            List<Model.ParkOrder> ThenParkOrder = new List<Model.ParkOrder>();
            List<Model.OrderDetail> ThenOrderDetail = new List<Model.OrderDetail>();
            #endregion

            #region 检查是否启用防疫（仅外场入场时判断，出场时policyArea=null）,扫码入场不判断防疫
            if (!isWhiteCar && !isMilitaryPoliceCar && gate == 1 && policyArea != null && CarObj.mode != 2)
            {
                //swCheckPass.Start();
                bool idSuccess = false;
                if (policyArea.PolicyArea_EPEnable == 1)
                {
                    result.passres.fycode = 1;
                    idSuccess = PassHelperBiz.EPIDCardResult(ref result, policyArea, parking, passway, owner, onCheckCache?.devices, onCheckCache?.policyareaes);
                    if (!idSuccess || result.passres.fycode != 0)
                    {
                        result.passres.errmsg = "扫健康码通行";
                    }
                }

                var carprefix = CarObj.carno.Length > 2 ? CarObj.carno.Substring(0, 2) : CarObj.carno;
                #region 直辖市特殊处理
                if (CarObj.carno.Contains("沪")) carprefix = "沪";
                else if (CarObj.carno.Contains("渝")) carprefix = "渝";
                else if (CarObj.carno.Contains("津")) carprefix = "津";
                else if (CarObj.carno.Contains("京")) carprefix = "京";
                #endregion
                if (policyArea.PolicyArea_EPAddress != null && !string.IsNullOrEmpty(carprefix) && policyArea.PolicyArea_EPAddress.Contains(carprefix))
                {
                    if (!idSuccess)
                    {
                        result.passres.fycode = 1;
                        result.passres.errmsg = "扫健康码通行";
                    }

                    //存在入场订单，则备注为重点地区车辆
                    if (resultData.resIn != null && resultData.resIn.parkorder != null)
                        resultData.resIn.parkorder.ParkOrder_IsEpCar = 1;

                    result.passres.errmsg = "重点地区车辆";
                    if (policyArea.PolicyArea_EPPassMode != 3)
                    {
                        if (policyArea.PolicyArea_EPPassMode == 0 || policyArea.PolicyArea_EPPassMode > result.passres.code)
                            result.passres.passcode = result.passres.code = policyArea.PolicyArea_EPPassMode;
                    }
                }
                //swCheckPass.Stop();
                //result.watchMessage += $"防疫检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            }
            #endregion

            #region 出场结果处理
            if (resultData.resOut != null)
            {
                if (resultData.resOut.onenter == 0)
                    result.passres.passcode = result.passres.code = resultData.resOut.code;
                else
                    result.passres.passcode = result.passres.code = (resultData.resOut.code == 0 ? 0 : (result.passres.code > resultData.resOut.code ? result.passres.code : resultData.resOut.code));

                result.passres.errcode = resultData.resOut.errcode;
                result.passres.errmsg = resultData.resOut.errmsg ?? result.passres.errmsg;
                if (result.passres.code == 0)
                {

                    if (resultData.resOut != null && resultData.resOut.onenter == 0 && !isWhiteCar && !isMilitaryPoliceCar)
                    {
                        //出场记录查找
                        if (policyPark.PolicyPark_OutRecordFindEnble == 1 && policyPark.PolicyPark_OutRecordFindMin > 0)
                        {
                            var incar = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo='{result.passres.carno}' and InCar_Status=201");
                            if (incar != null)
                            {
                                var order = BLL.ParkOrder.GetEntity(incar.InCar_ParkOrderNo);
                                if (order != null && order.ParkOrder_IsNoInRecord != 1)
                                {
                                    if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out && order.ParkOrder_OutTime != null
                                        //&& order.ParkOrder_OutTime.Value.AddHours(24) >= result.time.Value
                                        && order.ParkOrder_OutTime.Value >= result.time.Value.AddMinutes(-policyPark.PolicyPark_OutRecordFindMin.Value))
                                    {
                                        //出场时间在24小时以内,无需缴费，直接放行
                                        result.passres.code = 1;
                                        result.passres.errmsg = "无入场记录查找到最近记录，自动放行";
                                        result.payres = new ChargeModels.PayResult()
                                        {
                                            payed = 0,
                                            payedamount = 0,
                                            orderamount = 0
                                        };
                                        result.resorder.resOut.code = 1;
                                        result.resorder.resOut.errcode = null;
                                        result.resorder.resOut.errmsg = null;
                                        isAutoNoenterRecord = true;
                                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{result.passres.carno}]无入场记录查找到最近记录[{order.ParkOrder_No}]，自动放行");
                                    }
                                }
                            }
                        }
                    }

                    if (!isAutoNoenterRecord)
                    {
                        //无入场记录查找预入场记录
                        var matchRet = PassHelperBiz.MatchPendingPreIncar(policyPark, passway, ref result, false);
                        if (!matchRet)
                        {
                            PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);
                            return result;
                        }
                    }
                }

                if (!isAutoNoenterRecord)
                {
                    if (resultData.resOut.parkorder != null)
                    {
                        result.passres.parkorderno = resultData.resOut.parkorder.ParkOrder_No;
                        if (gate == 0) resultData.resOut.parkorder.ParkOrder_OutType = 1;
                        resultData.resOut.parkorder.ParkOrder_OutTime = result.time;
                        resultData.resOut.parkorder.ParkOrder_OutImg = localImgUrlEncode;
                        resultData.resOut.parkorder.ParkOrder_OutImgPath = carRecog.CarRecog_Img;
                        resultData.resOut.parkorder.ParkOrder_OutPasswayName = passway.Passway_Name;
                        resultData.resOut.parkorder.ParkOrder_OutPasswayNo = passway.Passway_No;
                        resultData.resOut.parkorder.ParkOrder_TotalAmount = result.payres?.payedamount ?? 0;
                        resultData.resOut.parkorder.ParkOrder_TotalPayed = result.payres?.orderamount ?? 0;
                        result.passres.carno = resultData.resOut.parkorder.ParkOrder_CarNo;
                    }

                    if (resultData.resOut.orderDetail != null)
                    {
                        result.passres.orderdetailno = resultData.resOut.orderDetail.OrderDetail_No;
                        resultData.resOut.orderDetail.OrderDetail_OutType = 1;
                        resultData.resOut.orderDetail.OrderDetail_OutImg = localImgUrlEncode;
                        resultData.resOut.orderDetail.OrderDetail_OutImgPath = carRecog.CarRecog_Img;
                        if (gate == 3)
                        {
                            resultData.resOut.parkorder.ParkOrder_EnterImgPath = resultData.resOut.orderDetail.OrderDetail_EnterImgPath;
                            resultData.resOut.parkorder.ParkOrder_EnterImg = resultData.resOut.orderDetail.OrderDetail_EnterImg;
                        }
                        result.passres.carno = resultData.resOut.orderDetail.OrderDetail_CarNo;
                    }

                    if (resultData.resOut?.closeOrder != null) ThenParkOrder.Add(resultData.resOut.closeOrder);
                    if (resultData.resOut?.parkorder != null) ThenParkOrder.Add(resultData.resOut.parkorder);
                    if (resultData.resOut?.orderDetail != null) ThenOrderDetail.Add(resultData.resOut.orderDetail);
                }
            }
            #endregion

            if (!isAutoNoenterRecord)
            {
                #region 入场订单处理
                if (resultData.resIn != null && result.passres.code != 0)
                {
                    if (resultData.resIn.parkorder != null)//场内区域变动，parkorder不传过来则null
                    {
                        if (gate != 3)
                        {
                            resultData.resIn.parkorder.ParkOrder_EnterImg = localImgUrlEncode;
                            resultData.resIn.parkorder.ParkOrder_EnterImgPath = carRecog.CarRecog_Img;
                        }
                        result.passres.parkorderno = resultData.resIn.parkorder.ParkOrder_No;

                        result.passres.carno = resultData.resIn.parkorder.ParkOrder_CarNo;
                    }

                    if (resultData.resIn.orderDetail != null)
                    {
                        result.passres.orderdetailno = resultData.resIn.orderDetail.OrderDetail_No;
                        if (string.IsNullOrEmpty(result.passres.parkorderno))
                            result.passres.parkorderno = resultData.resIn.orderDetail.OrderDetail_ParkOrderNo;
                        //本地入场图片存储，URL转码
                        resultData.resIn.orderDetail.OrderDetail_EnterImg = localImgUrlEncode;
                        resultData.resIn.orderDetail.OrderDetail_EnterImgPath = carRecog.CarRecog_Img;

                        result.passres.carno = resultData.resIn.orderDetail.OrderDetail_CarNo;
                    }

                    if (resultData.resIn?.closeOrder != null) ThenParkOrder.Add(resultData.resIn.closeOrder);
                    if (resultData.resIn?.closeDetail != null) ThenOrderDetail.Add(resultData.resIn.closeDetail);
                    if (resultData.resIn?.parkorder != null) ThenParkOrder.Add(resultData.resIn.parkorder);
                    if (resultData.resIn?.orderDetail != null) ThenOrderDetail.Add(resultData.resIn.orderDetail);
                }
                #endregion
            }

            #region 订单处理
            //swCheckPass.Start();
            if (resultData.resIn != null && (ThenOrderDetail.Count == 0 || resultData.resIn.orderDetail == null))
            {
                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{CarObj?.carno}][{resultData.resIn?.parkorder?.ParkOrder_No}]通行检测，车辆入场，未产生订单明细");
            }
            int resOrder = 0;
            //符合入场回调的条件：非确认放行 或者 车辆不在场内  或者 车辆在场内并且非外场入口的识别
            if (result?.passres?.passcode != 2 || !isInParkForConfirm || (isInParkForConfirm && gate != 1))
            {
                if (ThenOrderDetail.Count == 0 && ThenParkOrder.Count == 0)
                {
                    string parkorderno = (resultData.resIn?.parkorder?.ParkOrder_No) ?? (resultData.resOut?.parkorder?.ParkOrder_No ?? "");
                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{CarObj?.carno}][{parkorderno}]通行检测，未找到订单明细（无入场记录）", LogLevel.Info, null);
                }
                else
                {
                    resOrder = BLL.ParkOrder.CarInComplete(ThenParkOrder, ThenOrderDetail);
                }
            }
            else
            {
                resOrder = 1;
            }
            //通行检测，订单处理失败，禁止通行
            if (resOrder < 1 && (ThenParkOrder.Count > 0 || ThenOrderDetail.Count > 0))
            {
                string parkorderno = (resultData.resIn?.parkorder?.ParkOrder_No) ?? (resultData.resOut?.parkorder?.ParkOrder_No ?? "");
                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{CarObj?.carno}][{parkorderno}]通行检测，订单处理失败，禁止通行");
                result.passres.passcode = result.passres.code = 0;
                result.passres.errcode = ResultPassCode.ERROR_INFO_ERROR;
                result.passres.errmsg = $"订单处理失败,禁止通行";
                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                return result;
            }
            //关闭旧的场内订单(不存在则跳过)
            if (!isInParkForConfirm && resultData.resIn != null && (resultData.resIn.closeOrder != null || resultData.resIn.closeDetail != null))
            {
                _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                {
                    var data = new Model.ResBodyDataIn(new List<Model.ParkOrder> { resultData.resIn.closeOrder }, new List<Model.OrderDetail> { resultData.resIn.closeDetail });
                    BLL.ParkOrderApi.CarIn(parking.Parking_No, data);
                    return Task.CompletedTask;
                });
            }
            //swCheckPass.Stop();
            //result.watchMessage += $"订单处理检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";

            #endregion

            if (!isAutoNoenterRecord)
            {
                #region 是否需要缴费通行
                if (resultData.resOut != null && resultData.resOut.onenter == 0 && !isWhiteCar && !isMilitaryPoliceCar)
                {
                    //swCheckPass.Start();
                    PassHelperBiz.FeeNoRecord(ref result, resultData, policyPark, policyPass, passway, out isAutoNoenterRecord);
                    //swCheckPass.Stop();
                    //result.watchMessage += $"无入场处理：{swCheckPass.ElapsedMilliseconds} 毫秒，";
                }
                else if (passway.Passway_IsCharge == 1 && !isWhiteCar && !isMilitaryPoliceCar)
                {
                    //swCheckPass.Start();
                    if (result.passres.code != 0 && resultData.resOut != null && resultData.resOut.parkorder != null)
                    {
                        CalcBasicData baiseData = null;
                        if (onCheckCache != null)
                        {
                            baiseData = new CalcBasicData();
                            baiseData.ppList = onCheckCache.policyparkes;
                            baiseData.ctList = onCheckCache.cartypes;
                            baiseData.cctList = onCheckCache.carcardtypes;
                            baiseData.sysconfig = onCheckCache.sysconfig;
                            baiseData.areaList = onCheckCache.parkareas;
                            baiseData.cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && CarObj.isSupplement ? true : false;
                        }
                        result.payres = Charge.Calc.GetChargeByCar(resultData.resOut.parkorder?.Copy(), CarObj.time, car?.Copy(), null, CarObj.useCoupon, (CarObj.changeCarType ? carType.CarType_No : ""), (CarObj.changeCarCardType ? carCardType.CarCardType_No : ""), null, null, false, baiseData);
                        if ((result.payres.payed == 0 || result.payres.payed == 1) && result.payres.payedamount <= 0)
                        {
                            //if (result.policy.payedconfirm == 1)
                            //{
                            //    result.passres.code = 1;
                            //    result.passres.errmsg += "0元自动放行";
                            //}
                            //else
                            //{
                            //if (result.passres.code < 2)
                            //{
                            //    result.passres.code = 2;
                            //    result.passres.errmsg += "0元弹框确认";
                            //}
                            //}
                            if (result.passres.code == 2) result.passres.errmsg += "请稍候";
                        }
                        else if (result.payres.payedamount > 0)
                        {
                            //如果产生费用且放行方式是自动放行，则变更为弹窗放行
                            result.passres.code = result.passres.code == 1 ? 2 : result.passres.code;

                            if (string.IsNullOrEmpty(result.passres.errmsg)) result.passres.errmsg = carCardType?.CarCardType_Name ?? "";
                            result.passres.errmsg += $"|收费{result.payres.payedamount}元";
                        }

                        result.payres.changeCarType = CarObj.changeCarType;
                        result.payres.changeCarTypeNo = carType.CarType_No;
                    }
                    else
                    {
                        string msg = "";
                        if (result.passres.code == 0) msg += "禁止通行，";
                        if (resultData.resOut == null) msg += "没有出场放行结果，";
                        if (resultData.resOut != null && resultData.resOut.parkorder == null) msg += "未找到停车订单信息，";
                        ChargeModels.PayResult payResult = new ChargeModels.PayResult();
                        payResult.payed = 0;
                        payResult.payedmsg = string.IsNullOrEmpty(msg) ? "" : msg.Substring(0, msg.Length - 1);
                        payResult.payedamount = 0;
                        result.payres = payResult;
                    }
                    //swCheckPass.Stop();
                    //result.watchMessage += $"计费消耗：{swCheckPass.ElapsedMilliseconds} 毫秒，";
                }
                else if (passway.Passway_IsCharge != 1)
                {
                    ChargeModels.PayResult payResult = new ChargeModels.PayResult();
                    payResult.payed = 0;
                    payResult.payedmsg = "";
                    payResult.payedamount = 0;
                    result.payres = payResult;
                }

                //查询是否已预缴现金
                if (resultData?.resOut?.parkorder != null && result.payres?.payedamount > 0)
                {
                    //swCheckPass.Start();
                    var pays = GetPaymethodByOrderNo(resultData.resOut.parkorder.ParkOrder_No);
                    decimal? advamount = pays?.Sum(x => x.Paymethod_Money);
                    result.payres.cashrobotamount = advamount ?? 0;
                    decimal payedamount = result.payres.payedamount - result.payres.cashrobotamount;
                    result.payres.payedamount = payedamount >= 0 ? payedamount : 0;
                    result.payres.zlamount = payedamount < 0 ? -payedamount : 0;
                    //swCheckPass.Stop();
                    //result.watchMessage += $"预缴现金检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
                }
                #endregion
            }


            //记录当前状态
            result.passres.actcode = result.passres.passcode = result.passres.code;
            decimal? payAmount = 0;

            if (!isAutoNoenterRecord)
            {
                #region 是否需要追缴
                if (!isWhiteCar && !isMilitaryPoliceCar && policyPassway.PolicyPassway_IsPayClearing == 1)
                {
                    //swCheckPass.Start();
                    Model.Owner newOwner = owner == null ? null : owner.Copy();
                    if (newOwner != null && carCardType.CarCardType_Category == Model.EnumCarType.Prepaid.ToString())
                    {
                        newOwner.Owner_Balance = newOwner.Owner_Balance - (result.payres?.chuzhiamount ?? 0);
                        if (newOwner.Owner_Balance < 0) newOwner.Owner_Balance = 0;
                    }
                    var payResult = PassHelperBiz.GetPayByUnpaidOrder(CarObj.carno, passway.Passway_No, newOwner, car, result);
                    result.unpaidresult = payResult;
                    if (resultData.resIn != null && resultData.resIn.parkorder != null)
                    {
                        payResult?.ForEach(x =>
                        {
                            if (result.calcdetail == null) result.calcdetail = BLL.CommonBLL.GetCalcDetail(x.payres, resultData.resIn.parkorder, true);
                            else { result.calcdetail.AddRange(BLL.CommonBLL.GetCalcDetail(x.payres, resultData.resIn.parkorder, true)); }
                        });
                    }

                    if (payResult != null && payResult.Count > 0)
                    {
                        if (payResult.Find(x => x.payres?.payed == 2) != null)
                        {
                            //计费失败
                            result.passres.passcode = result.passres.actcode = result.passres.code = 0;
                            result.passres.errmsg = "追缴订单计费失败";
                        }
                        else
                        {
                            payAmount = payResult?.Sum(x => x.payres?.payedamount);

                            if (payAmount > 0)
                            {
                                //若为自动放行，改确定放行
                                if (result.passres.code == 1)
                                {
                                    result.passres.passcode = result.passres.code = 2;
                                    result.passres.errmsg = $"追缴金额：{payAmount}";
                                }
                                else if (result.passres.code == 2)
                                {
                                    result.passres.errmsg += $" 追缴金额：{payAmount}";
                                }

                                DataCache.AppendUnpaidOrderCache.Set(result.passres.parkorderno, 1);
                            }
                        }
                    }
                    //swCheckPass.Stop();
                    //result.watchMessage += $"追缴检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
                }
                #endregion

                #region 储值车余额判断(智联云盒不进行判断)
                if (AppSettingConfig.SentryMode != VersionEnum.CloudServer && !isWhiteCar && !isMilitaryPoliceCar && car != null && carCardType.CarCardType_Category == Model.EnumCarType.Prepaid.ToString())
                {
                    //swCheckPass.Start();
                    //入口，设置余额不足禁止通行.则判断储值车余额是否小于等于0，则禁止通行
                    if (gate == 1)
                    {
                        if (car != null)
                        {
                            decimal balance = 0;
                            if (owner != null)//车位余额
                            {
                                if (owner.Owner_Balance == null || owner.Owner_Balance <= 0)
                                    balance = 0;
                                else
                                    balance = owner.Owner_Balance.Value;
                            }

                            if (balance <= 0)
                            {
                                if (policyPass?.PolicyPass_LackPass == 0)
                                {
                                    result.passres.code = 0;
                                    result.passres.errcode = ResultPassCode.ERROR_BALANCELESS;
                                    result.passres.errmsg = "储值车余额不足,禁止通行";
                                    PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                                    return result;
                                }
                                else
                                {
                                    if (result.passres.code < policyPass?.PolicyPass_LackPass)
                                    {
                                        result.passres.code = policyPass?.PolicyPass_LackPass;
                                        result.passres.errmsg = "储值车余额不足";
                                    }
                                }
                            }
                        }


                    }
                    //出口，余额小于本次停车费用
                    else if (gate == 0)
                    {
                        if (result.payres?.payedamount > 0 || result.unpaidresult?.Sum(x => x.payres?.payedamount ?? 0) > 0) //计费结果还需要支付,则表示余额小于本次停车费用
                        {
                            if (policyPass?.PolicyPass_LackPass == 0)
                            {
                                result.passres.code = 0;
                                result.passres.errcode = ResultPassCode.ERROR_BALANCELESS;
                                result.passres.errmsg = "储值车余额不足,禁止通行";
                                PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType, true, passwayLinks);
                                return result;
                            }
                            else
                            {
                                if (result.passres.code < policyPass?.PolicyPass_LackPass)
                                {
                                    result.passres.code = policyPass?.PolicyPass_LackPass;
                                    result.passres.errmsg = "储值车余额不足";
                                }
                            }
                        }
                    }
                    //swCheckPass.Stop();
                    //result.watchMessage += $"储值车余额检测：{swCheckPass.ElapsedMilliseconds} 毫秒，";
                }
                #endregion

                #region 新增识别未支付记录
                if (AppSettingConfig.SentryMode == VersionEnum.WindowsStandard)
                {
                    if (result.payres?.payed == 1)
                    {
                        var unpaidRecordno = Utils.CreateNumber_SnowFlake;
                        var orderno = result.resorder.resOut?.parkorder?.ParkOrder_No ?? result.resorder.resOut?.noRecordOrder?.ParkOrder_No ?? result.passres.parkorderno ?? "";
                        PassHelperBiz.InsertRecogNoPassRecord(unpaidRecordno, carRecog.CarRecog_CarNo, carRecog.CarRecog_No, carRecog.CarRecog_Time, passway.Passway_No, passway.Passway_Name,
                           orderno, result.payres.payedamount + payAmount,
                            carCardType.CarCardType_No, carType.CarType_No, CarObj.mode);
                        result.UnpaidRecord_No = unpaidRecordno;
                    }
                }
                #endregion
            }

            #region 入口弹窗判断
            if (gate == 1 && result.passres.code == 1)
            {
                var confimKey = ConfirmRelease.Results.Where(kv => kv.Value.passres?.carno == result.passres.carno).FirstOrDefault();
                if (!string.IsNullOrEmpty(confimKey.Key) && confimKey.Value != null)
                {
                    if (confimKey.Value.passres.code == 2 && confimKey.Value.passres.errcode == ResultPassCode.ERROR_ENTER_REPEATCONFIRM)
                    {
                        result.passres.passcode = result.passres.actcode = result.passres.code = 2;
                        result.passres.errmsg = confimKey.Value.passres.errmsg;
                    }
                }
            }
            #endregion

            #region 语音播报
            if (result.passres.type == Model.EnumParkOrderStatus.In && !string.IsNullOrEmpty(policyPassway.PolicyPassway_Show))
                result.policy.broadvalue = policyPassway.PolicyPassway_Show.Split('/')[0];
            else if (result.passres.type == Model.EnumParkOrderStatus.Out && !string.IsNullOrEmpty(policyPassway.PolicyPassway_Show) && policyPassway.PolicyPassway_Show.Contains("/"))
                result.policy.broadvalue = policyPassway.PolicyPassway_Show.Split('/')[1];
            else
                result.policy.broadvalue = "您好";

            if (isEndNumAuth && !string.IsNullOrEmpty(endNumVoice))
            {
                result.policy.broadtype = 5;
                result.policy.broadvalue = endNumVoice; //尾号限行语音
            }

            #endregion

            #region 更新预计出场金额
            if (resultData.resOut != null && resultData.resOut.parkorder != null)
            {
                //swCheckPass.Start();
                resultData.resOut.parkorder.ParkOrder_TotalAmount = result.payres?.payedamount ?? 0;
                resultData.resOut.parkorder.ParkOrder_TotalPayed = result.payres?.orderamount ?? 0;
                DataCache.ParkOrder.Set(resultData.resOut.parkorder.ParkOrder_No, resultData.resOut.parkorder);
                //Action act = () =>
                //{
                //    try
                //    {
                //        BLL.DataBase._UpdateByModelByID<Model.ParkOrder>(resultData.resOut.parkorder);
                //    }
                //    catch (Exception e)
                //    {
                //        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"[{CarObj?.carno}][{resultData?.resOut?.parkorder?.ParkOrder_No}]更新预计出场金额:{e.ToString()}", LogLevel.Error, null);
                //    }
                //};
                //if (CustomThreadPool.SyncTaskPool != null)_ = CustomThreadPool.SyncTaskPool.QueueTask(null, act); else _ = Task.Run(() => { act(); });
                //swCheckPass.Stop();
                //result.watchMessage += $"更新预计出场金额：{swCheckPass.ElapsedMilliseconds} 毫秒，";
            }
            #endregion

            PassHelperBiz.UpdateRecog(ref result, carRecog, passway, camera, carCardType, carType);

            result.errmsg = result.errmsg ?? result?.passres.errmsg;
            return result;


        }

        /// <summary>
        /// 核验健康码，并返回通行检测结果
        /// 无牌车校验时:passres/passres.areano区域编号/passway/fycode=1
        /// </summary>
        /// <returns></returns>
        public static void OnCheckCarPass(Model.HealthCode param, ref Model.ResultPass result, out Model.HealthCodeResult data, List<Model.PolicyArea> paList = null)
        {
            data = null;
            if (result.passres == null ||
                result.passres.fycode == 0 ||
                result.passres.fycode == 2) return;

            if (param.hstime == 7) param.hstime = 7 * 24;

            if (param.hstime == null && param.hsdate != null)
            {
                var h = (DateTimeHelper.GetNowTime() - param.hsdate.Value).TotalHours;
                if (h <= 24) param.hstime = 24;
                else if (h <= 48) param.hstime = 48;
                else if (h <= 72) param.hstime = 72;
                else if (h <= 168) param.hstime = 168;
                else param.hstime = Convert.ToInt32(Math.Ceiling(h));
            }

            if (param.kytime == null && param.kydate != null)
            {
                var h = (DateTimeHelper.GetNowTime() - param.kydate.Value).TotalHours;
                if (h <= 24) param.kytime = 24;
                else if (h <= 48) param.kytime = 48;
                else if (h <= 72) param.kytime = 72;
                else if (h <= 168) param.hstime = 168;
                else param.kytime = Convert.ToInt32(Math.Ceiling(h));
            }

            string orderNo = string.Empty;
            if (!string.IsNullOrEmpty(result.passres?.parkorderno)) orderNo = result.passres?.parkorderno;
            else if (result.resorder?.resIn?.parkorder != null) orderNo = result.resorder?.resIn?.parkorder.ParkOrder_No;
            else if (result.resorder?.resOut?.parkorder != null) orderNo = result.resorder?.resOut?.parkorder.ParkOrder_No;

            data = new Model.HealthCodeResult
            {
                HealthCodeResult_No = Utils.CreateNumber,
                HealthCodeResult_ParkNo = result.passres?.passway?.Passway_ParkNo,
                HealthCodeResult_ParkOrderNo = orderNo,
                HealthCodeResult_CarNo = result.passres?.carno,
                HealthCodeResult_Name = param.name,
                HealthCodeResult_IDCard = param.idcard,
                HealthCodeResult_Color = param.color,
                HealthCodeResult_HsDate = param.hsdate,
                HealthCodeResult_HsTime = param.hstime,
                HealthCodeResult_HsRes = param.hsres,
                HealthCodeResult_YmCount = param.ymcount,
                HealthCodeResult_KyDate = param.kydate,
                HealthCodeResult_KyTime = param.kytime,
                HealthCodeResult_KyRes = param.kyres,
                HealthCodeResult_Data = param.data,
                HealthCodeResult_AddTime = DateTimeHelper.GetNowTime(),
                HealthCodeResult_Pass = result.passres?.code
            };

            BLL.BaseBLL._Insert(data);

            result.passres.fycode = 0;

            Model.PolicyArea policyArea = null;
            if (paList?.Count > 0)
            {
                var ano = result.passres.areano;
                policyArea = paList.Find(x => x.PolicyArea_ParkAreaNo == ano);
            }
            else
                policyArea = BLL.PolicyArea.GetEntity(result.passres.areano);
            if (policyArea == null)
            {
                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, "防疫设置检测,读取设置失败", LogLevel.Error);
                result.passres.fycode = 2;
                result.passres.code = 0;
                result.passres.errmsg = "读取区域防疫设置失败";
                return;
            }

            switch (policyArea.PolicyArea_EPHeSuan)
            {
                case 1:
                    if (param.color != 1)
                    {
                        result.passres.fycode = 2;
                        result.passres.code = 0;
                        result.passres.errmsg = "健康码不是绿码";

                        result.passres.errcode = ResultPassCode.ERROR_HEALTHCODE_FALL;
                        data.HealthCodeResult_Pass = result.passres?.code;
                        BLL.BaseBLL._Insert(data);
                        return;
                    }
                    break;
                case 24:
                case 48:
                case 72:
                case 168:
                    if (param.color != 1 || param.hstime == null || param.hstime > policyArea.PolicyArea_EPHeSuan)
                    {
                        result.passres.fycode = 2;
                        result.passres.code = 0;
                        string msg = $"{policyArea.PolicyArea_EPHeSuan}小时内";
                        if (policyArea.PolicyArea_EPHeSuan == 168) msg = "7天内";
                        result.passres.errmsg = $"健康码不是绿码或核酸检测时间不是{msg}";

                        result.passres.errcode = ResultPassCode.ERROR_HEALTHCODE_FALL;
                        data.HealthCodeResult_Pass = result.passres?.code;
                        BLL.BaseBLL._Insert(data);
                        return;
                    }
                    break;
            }

            switch (policyArea.PolicyArea_EPKangYuan)
            {
                case 1:
                    if (param.kyres != 0)
                    {
                        result.passres.fycode = 2;
                        result.passres.code = 0;
                        result.passres.errmsg = "抗原检测不是阴性";

                        result.passres.errcode = ResultPassCode.ERROR_HEALTHCODE_FALL;
                        data.HealthCodeResult_Pass = result.passres?.code;
                        BLL.BaseBLL._Insert(data);
                        return;
                    }
                    break;
                case 24:
                case 48:
                case 72:
                case 168:
                    if (param.kyres != 0 || param.kytime == null || param.kytime > policyArea.PolicyArea_EPKangYuan)
                    {
                        result.passres.fycode = 2;
                        result.passres.code = 0;
                        string msg = $"{policyArea.PolicyArea_EPKangYuan}小时内";
                        if (policyArea.PolicyArea_EPKangYuan == 168) msg = "7天内";
                        result.passres.errmsg = $"抗原检测不是阴性或抗原检测时间不是{msg}";

                        result.passres.errcode = ResultPassCode.ERROR_HEALTHCODE_FALL;
                        data.HealthCodeResult_Pass = result.passres?.code;
                        BLL.BaseBLL._Insert(data);
                        return;
                    }
                    break;
            }

            switch (policyArea.PolicyArea_EPYmCount)
            {
                case 1:
                case 2:
                case 3:
                    if (param.ymcount < policyArea.PolicyArea_EPYmCount)
                    {
                        result.passres.fycode = 2;
                        result.passres.code = 0;
                        result.passres.errmsg = $"疫苗针数小于{policyArea.PolicyArea_EPYmCount}";

                        result.passres.errcode = ResultPassCode.ERROR_HEALTHCODE_FALL;
                        data.HealthCodeResult_Pass = result.passres?.code;
                        BLL.BaseBLL._Insert(data);
                        return;
                    }
                    break;
            }

        }

        /// <summary>
        /// 修改车牌 || 车牌颜色 || 车牌类型
        /// </summary>
        public static ResBody UpdateCarIn(Model.ParkGateIn param, out List<Model.ParkOrder> orderList, out List<Model.OrderDetail> detailList)
        {
            orderList = new List<Model.ParkOrder>();
            detailList = new List<Model.OrderDetail>();

            if (param == null) { return new ResBody(false, "参数错误", param.parkno); }
            if (string.IsNullOrEmpty(param.orderno)) { return new ResBody(false, "订单编码错误", param.parkno); }

            PassTool.PassHelperBiz.UpdateCarIn(param, out orderList, out detailList, out var incar);
            if (orderList.Count == 0 || detailList.Count == 0) { return new ResBody(false, "订单不存在", param.parkno); }



            var res = BLL.ParkOrder.CarInComplete(orderList, detailList, null, incar);
            if (res > 0)
            {
                //通知服务器
                BLL.ParkApi.UpdateCarIn(param);

                return new ResBody(true, "修改成功", param.parkno);
            }
            else
            {
                return new ResBody(false, "修改失败", param.parkno);
            }
        }

        /// <summary>
        /// 开闸后入场成功回调(车辆进入停车场或在场内变更停车区域时,开闸成功回调)
        /// </summary>
        public static ResBody CarInComplete(Model.ParkGatePass param, bool isOnCheckCarPass = true)
        {
            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, "车辆入场回调：" + param?.carno);
            if (string.IsNullOrEmpty(param.parkno))
            {
                return new ResBody() { success = false, errmsg = "停车场编码parkno为空" };
            }
            if (string.IsNullOrEmpty(param.carno))
            {
                return new ResBody() { success = false, errmsg = "车牌号carno为空" };
            }
            if (string.IsNullOrEmpty(param.orderno))
            {
                return new ResBody() { success = false, errmsg = "停车订单号orderno为空" };
            }
            if (param.code != Model.EnumParkOrderStatus.In)
            {
                return new ResBody() { success = false, errmsg = "出入场类型code错误" };
            }

            bool isInParkForConfirm = param?.respass?.passres?.passcode == 2 ? (param?.respass?.passres?.isInParkForConfirm ?? false) : false;
            Model.ParkOrder order = null;
            if (!isInParkForConfirm || param.respass == null)
            {
                order = BLL.ParkOrder.GetEntity(param.orderno);
                if (order == null)
                {
                    return new ResBody() { success = false, errmsg = "订单不存在" };
                }
            }
            else
            {
                order = param?.respass?.passres.gate != 1 ? param?.respass?.resorder?.resOut?.parkorder : param?.respass?.resorder?.resIn?.parkorder;
                if (order == null)
                {
                    return new ResBody() { success = false, errmsg = "订单不存在" };
                }

                //order.ParkOrder_ParkAreaNo = param?.respass.passres.areano;
                //order.ParkOrder_ParkAreaName = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, order.ParkOrder_ParkAreaNo)?.ParkArea_Name;
            }

            DataCache.ParkOrder.Del(param.orderno);

            List<Model.ParkOrder> parkOrders = new List<Model.ParkOrder>();
            List<Model.OrderDetail> orderDetails = new List<Model.OrderDetail>();

            Model.Device camera = BLL.Device.GetEntity(param.camerano);
            if (camera == null)
            {
                return new ResBody() { success = false, errmsg = "相机信息不存在" };
            }
            Model.Passway passway = BLL.Passway.GetEntity(camera.Device_PasswayNo);
            if (passway == null)
            {
                return new ResBody() { success = false, errmsg = "车道信息不存在" };
            }
            var areaway = BLL.PasswayLink.GetAllEntityByPasswayNo(passway.Passway_No); //BLL.PasswayLink.GetAreaPassway("*", $"PasswayLink_PasswayNo='{passway.Passway_No}'");
            if (areaway == null || areaway.Count == 0)
            {
                return new ResBody() { success = false, errmsg = "车道未关联区域" };
            }
            BLL.PasswayLink.GetParkAreaByPassway(areaway, out string inAreaNo, out string outAreaNo);

            string imgSrc = string.Empty;
            if (!string.IsNullOrEmpty(param.imgpath))
            {
                param.imgpath = System.Web.HttpUtility.UrlEncode(param.imgpath);
            }
            if (!string.IsNullOrEmpty(param.img))
            {
                imgSrc = PassHelperBiz.GetImageHttpUrl(param.parkno, param.img, camera?.Device_SentryHostNo);
                param.img = imgSrc;
            }
            else
            {
                imgSrc = PassHelperBiz.GetImageHttpUrl(param.parkno, param.imgpath, camera?.Device_SentryHostNo);
                param.img = imgSrc;
            }

            #region 停车订单 预入场变更为入场

            string policyNoneCarType = "", carTypeName = "";//无牌车指定车牌颜色
            if (param.innonecarno)
            {
                Model.PolicyPark policyPark = BLL.PolicyPark.GetEntity(order.ParkOrder_ParkNo);
                if (policyPark != null && !string.IsNullOrEmpty(policyPark.PolicyPark_DefaultNoneCarType))
                {
                    Model.CarType ctModel = BLL.CarType.GetEntity(policyPark.PolicyPark_DefaultNoneCarType);
                    policyNoneCarType = ctModel?.CarType_No ?? "";
                    carTypeName = ctModel?.CarType_Name ?? "";
                }
            }

            //查询当前车辆预入场明细
            List<Model.OrderDetail> detailList = isInParkForConfirm ? new List<Model.OrderDetail> { param.respass.resorder.resIn.orderDetail } : BLL.OrderDetail.GetAllEntity(order.ParkOrder_No);
            var curDetail = new Model.OrderDetail();
            if (detailList != null && detailList.Count > 0)
            {
                #region 预入场明细 改为入场
                detailList = detailList.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Est);
                if (detailList == null || detailList.Count == 0)
                {
                    return new ResBody() { success = false, errmsg = "订单明细不存在" };
                }
                curDetail = detailList?.First();
                detailList.ForEach(x =>
                {
                    x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                    x.OrderDetail_OutType = 0;
                    x.OrderDetail_EnterTime = param.time;
                    x.OrderDetail_EnterAdminAccount = param.account;
                    x.OrderDetail_EnterAdminName = param.name;
                    if (param.innonecarno && !string.IsNullOrEmpty(policyNoneCarType))
                    {
                        x.OrderDetail_CarType = policyNoneCarType;
                        x.OrderDetail_CarTypeName = carTypeName;
                    }
                    if (string.IsNullOrEmpty(x.OrderDetail_EnterImg))
                    {
                        x.OrderDetail_EnterImg = imgSrc;
                    }
                    if (string.IsNullOrEmpty(x.OrderDetail_EnterImgPath))
                    {
                        x.OrderDetail_EnterImgPath = imgSrc;
                    }
                });
                if (detailList != null) orderDetails.AddRange(detailList);
                #endregion
            }

            if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Est)
            {
                order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                order.ParkOrder_EnterTime = param.time;
                order.ParkOrder_EnterAdminAccount = param.account;
                order.ParkOrder_EnterAdminName = param.name;
                if (param.innonecarno && !string.IsNullOrEmpty(policyNoneCarType))
                {
                    order.ParkOrder_CarType = policyNoneCarType;
                    order.ParkOrder_CarTypeName = carTypeName;
                }
            }
            order.ParkOrder_OutType = 0;
            order.ParkOrder_ParkAreaNo = curDetail.OrderDetail_ParkAreaNo;
            order.ParkOrder_ParkAreaName = curDetail.OrderDetail_ParkAreaName;

            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer) if (param?.isSupplement ?? false) order.ParkOrder_UserNo = order.ParkOrder_StatusNo > 200 ? (order.ParkOrder_UserNo == "1" ? "3" : "2") : "1"; else order.ParkOrder_UserNo = "";

            if (string.IsNullOrEmpty(order.ParkOrder_EnterImgPath))
            {
                order.ParkOrder_EnterImgPath = param.img;
            }
            if (string.IsNullOrEmpty(order.ParkOrder_EnterImg))
            {
                order.ParkOrder_EnterImg = param.imgpath;
            }
            parkOrders.Add(order);
            #endregion

            #region 查询场内是否已存在入场订单(车辆在车场内变换停车区域)
            string areaNo = string.Empty;

            var incarModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", "InCar_CarNo=@InCar_CarNo and InCar_Status=@InCar_Status", new { InCar_CarNo = param.carno, InCar_Status = Model.EnumParkOrderStatus.In });
            Model.ParkOrder inOrder = incarModel == null ? null : BLL.ParkOrder.GetEntity(incarModel.InCar_ParkOrderNo);//BLL.ParkOrder.GetParkOrder("*", param.parkno, param.carno, 0, Model.EnumParkOrderStatus.In);
            if (inOrder != null && inOrder.ParkOrder_IsSettle == 0)
            {
                if (inOrder.ParkOrder_No != param.orderno)
                {
                    //外场区域变换
                    inOrder.ParkOrder_StatusNo = isInParkForConfirm ? Model.EnumParkOrderStatus.Close : Model.EnumParkOrderStatus.Out;
                    if (!isInParkForConfirm)
                    {
                        inOrder.ParkOrder_OutImgPath = imgSrc;
                        inOrder.ParkOrder_OutImg = param.imgpath;
                        inOrder.ParkOrder_OutPasswayNo = passway.Passway_No;
                        inOrder.ParkOrder_OutPasswayName = passway.Passway_Name;
                        inOrder.ParkOrder_OutTime = param.time;
                        inOrder.ParkOrder_OutAdminAccount = param.account;
                        inOrder.ParkOrder_OutAdminName = param.name;
                    }
                    else
                    {
                        inOrder.ParkOrder_Remark += $"重复入场关闭订单,由{order?.ParkOrder_No}关闭";
                    }
                    inOrder.ParkOrder_OutType = 2;

                    parkOrders.Add(inOrder);

                    List<Model.OrderDetail> inDetail = BLL.OrderDetail.GetOrderDetailList("*", param.parkno, inOrder.ParkOrder_No, Model.EnumOrderSettleStatus.False);
                    if (inDetail != null && inDetail.Count > 0)
                    {
                        foreach (var x in inDetail)
                        {
                            if (x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In)
                            {
                                x.OrderDetail_StatusNo = isInParkForConfirm ? Model.EnumParkOrderStatus.Close : Model.EnumParkOrderStatus.Out;
                                areaNo = x.OrderDetail_ParkAreaNo;
                            }
                            if (!isInParkForConfirm)
                            {
                                x.OrderDetail_OutImgPath = imgSrc;
                                x.OrderDetail_OutImg = param.imgpath;
                                x.OrderDetail_OutTime = param.time;
                                x.OrderDetail_OutPasswayNo = passway.Passway_No;
                                x.OrderDetail_OutPasswayName = passway.Passway_Name;
                                x.OrderDetail_OutAdminAccount = param.account;
                                x.OrderDetail_OutAdminName = param.name;
                            }
                            x.OrderDetail_OutType = 2;
                        }
                        orderDetails.AddRange(inDetail);
                    }
                }
                else
                {
                    //内场区域变换
                    inOrder = order;
                    List<Model.OrderDetail> inDetail = BLL.OrderDetail.GetAllEntity(order.ParkOrder_No);
                    if (inDetail != null && inDetail.Count > 0)
                    {
                        inDetail = inDetail.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);

                        areaNo = inDetail.LastOrDefault()?.OrderDetail_ParkAreaNo;
                        inDetail.ForEach(x =>
                        {
                            x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                            x.OrderDetail_OutImgPath = imgSrc;
                            x.OrderDetail_OutImg = param.imgpath;
                            x.OrderDetail_OutTime = param.time;
                            x.OrderDetail_OutPasswayNo = passway.Passway_No;
                            x.OrderDetail_OutPasswayName = passway.Passway_Name;
                            x.OrderDetail_OutAdminAccount = param.account;
                            x.OrderDetail_OutAdminName = param.name;
                            x.OrderDetail_OutType = 2;
                        });
                        orderDetails.AddRange(inDetail);
                    }
                }

            }
            else
            {
                string a = "";
            }
            #endregion

            #region 入场时修改数据
            if (param.onindata != null)
            {
                if (!string.IsNullOrEmpty(param.onindata.carno))
                {
                    parkOrders.ForEach(d => { d.ParkOrder_CarNo = param.onindata.carno; });
                    orderDetails.ForEach(d => { d.OrderDetail_CarNo = param.onindata.carno; });
                }

                if (!string.IsNullOrEmpty(param.onindata.cartypeno) && !string.IsNullOrEmpty(param.onindata.cartypename))
                {
                    parkOrders.ForEach(d => { if (d.ParkOrder_No != incarModel?.InCar_ParkOrderNo) { d.ParkOrder_CarType = param.onindata.cartypeno; d.ParkOrder_CarTypeName = param.onindata.cartypename; } });
                    orderDetails.ForEach(d => { if (d.OrderDetail_ParkOrderNo != incarModel?.InCar_ParkOrderNo) { d.OrderDetail_CarType = param.onindata.cartypeno; d.OrderDetail_CarTypeName = param.onindata.cartypename; } });
                }

                if (!string.IsNullOrEmpty(param.onindata.carcardtypeno) && !string.IsNullOrEmpty(param.onindata.carcardtypename))
                {
                    parkOrders.ForEach(d => { if (d.ParkOrder_No != incarModel?.InCar_ParkOrderNo) { d.ParkOrder_CarCardType = param.onindata.carcardtypeno; d.ParkOrder_CarCardTypeName = param.onindata.carcardtypename; } });
                    orderDetails.ForEach(d => { if (d.OrderDetail_ParkOrderNo != incarModel?.InCar_ParkOrderNo) { d.OrderDetail_CarCardType = param.onindata.carcardtypeno; d.OrderDetail_CarCardTypeName = param.onindata.carcardtypename; } });
                }

                if (!string.IsNullOrEmpty(param.onindata?.remark))
                {
                    parkOrders.ForEach(d => { if (d.ParkOrder_No != incarModel?.InCar_ParkOrderNo) { d.ParkOrder_EnterRemark = param.onindata?.remark; } });
                    orderDetails.ForEach(d => { if (d.OrderDetail_ParkOrderNo != incarModel?.InCar_ParkOrderNo) { d.orderdetail_EnterRemark = param.onindata?.remark; } });
                }
            }
            #endregion

            if (inOrder != null)
            {
                //是否经过通行检测
                if (isOnCheckCarPass)
                {
                    //一位多车处理
                    BLL.ParkOrder.OutParkForMultiCar(inOrder, inAreaNo, outAreaNo, (pLst, dLst) =>
                     {
                         var outList = BLL.ParkOrder.ChangeOrderLst(inOrder, pLst);
                         parkOrders = BLL.ParkOrder.MergeList(parkOrders, outList);
                         orderDetails = BLL.ParkOrder.ChangeDetailLst(orderDetails, dLst);
                     });
                }
                else
                {
                    Model.Car car = BLL.Car.GetEntityByCarNo(inOrder.ParkOrder_CarNo);
                    Model.Owner owner = BLL.Owner.GetEntity(car?.Car_OwnerNo);
                    Model.CarCardType card = BLL.CarCardType.GetEntity(owner?.Owner_CardTypeNo);
                    BLL.ParkOrder.ModifyAnyToChangeOrder(car, owner, card, ref inOrder, ref detailList);
                    parkOrders = BLL.ParkOrder.MergeList(parkOrders, new List<Model.ParkOrder>() { inOrder });
                }
            }

            var pushdata = new Model.ResBodyDataIn(parkOrders, orderDetails);

            if (pushdata.Item1?.Find(x => !string.IsNullOrEmpty(x.ParkOrder_ReserveNo) && x.ParkOrder_StatusNo == 200) != null)
            {
                var reserve = BLL.Reserve._GetEntityByNo(new Model.Reserve(), pushdata.Item1.Find(x => !string.IsNullOrEmpty(x.ParkOrder_ReserveNo) && x.ParkOrder_StatusNo == 200).ParkOrder_ReserveNo);
                if (reserve != null)
                {
                    reserve.Reserve_OrderNo = order.ParkOrder_No;
                    reserve.Reserve_Status = 1;
                    reserve.Reserve_EnterTime = param.time;
                    pushdata.Item3 = new List<Model.Reserve> { reserve };
                }
            }

            #region 车辆入场，绑定时段全免优惠券
            List<Model.CouponRecord> couponList = null;
            List<Model.CouponPlan> couponPlanList = null;
            var timeCouponValidity = AppBasicCache.CurrentSysConfigContent.SysConfig_TimeCouponValidity;
            if (timeCouponValidity != null && order?.ParkOrder_EnterTime != null)
            {
                couponPlanList = BLL.BaseBLL._GetAllEntity(new Model.CouponPlan(), "CouponPlan_ID,CouponPlan_No,CouponPlan_Value,CouponPlan_StartTime,CouponPlan_EndTime,CouponPlan_VaildTime,CouponPlan_MerchantId,CouponPlan_MerchantName,CouponPlan_UseCount,CouponPlan_MaxCount", $"CouponPlan_IssueCarNo='{order?.ParkOrder_CarNo ?? param.respass?.passres?.carno}' " +
                     $"AND CouponPlan_VaildTime>'{order.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' AND CouponPlan_Status=1 AND (CouponPlan_MaxCount = 0 OR CouponPlan_UseCount < CouponPlan_MaxCount) limit 10");
                if (couponPlanList != null && couponPlanList.Count > 0)
                {
                    couponList = new List<Model.CouponRecord>();
                    couponPlanList.ForEach(x =>
                    {
                        //组装优惠券
                        Model.CouponRecord model = new Model.CouponRecord();
                        model.CouponRecord_IssueCarNo = order?.ParkOrder_CarNo;
                        model.CouponRecord_No = Utils.CreateNumber_SnowFlake;
                        model.CouponRecord_ParkDiscountSetNo = x.CouponPlan_No;
                        model.CouponRecord_ParkNo = order?.ParkOrder_ParkNo;
                        model.CouponRecord_Status = 0;
                        model.CouponRecord_Value = Utils.StrToDecimal(x.CouponPlan_Value.ToString(), 0);
                        model.CouponRecord_StartTime = x.CouponPlan_StartTime;
                        model.CouponRecord_EndTime = x.CouponPlan_VaildTime;
                        model.CouponRecord_ParkNo = order?.ParkOrder_ParkNo;
                        model.CouponRecord_ParkOrderNo = order?.ParkOrder_No;
                        model.CouponRecord_VaildTime = x.CouponPlan_EndTime;
                        model.CouponRecord_AddTime = DateTimeHelper.GetNowTime();
                        model.CouponRecord_OnLine = 2;
                        model.CouponRecord_MerchantId = x.CouponPlan_MerchantId;
                        model.CouponRecord_MerchantName = x.CouponPlan_MerchantName;
                        model.CouponRecord_ParkOrderNo = pushdata.Item1?.Find(y => y.ParkOrder_StatusNo == 200)?.ParkOrder_No;
                        model.CouponRecord_CouponCode = ((int)Common.EnumCouponType.HourFree).ToString();

                        DateTime calculatedEnd = DateTime.MinValue;
                        if (model.CouponRecord_StartTime != null)
                        {
                            // 优化时间判断逻辑
                            var now = order.ParkOrder_EnterTime.Value;
                            var startTime = x.CouponPlan_StartTime.Value;
                            var endTime = x.CouponPlan_VaildTime.Value;
                            var deciValue = model.CouponRecord_Value;


                            // ✅ 优先判断 endTime 是否过期
                            bool endTimeExpired = endTime < now;

                            if (!endTimeExpired)
                            {
                                if (startTime < now)
                                    startTime = now;

                                // ✅ 如果有 couponValue，计算优惠券结束时间
                                if (deciValue > 0)
                                {
                                    calculatedEnd = startTime.AddMinutes((double)deciValue);
                                    // 限制 endTime 不超过原 endTime
                                    endTime = endTime < calculatedEnd ? endTime : calculatedEnd;
                                }

                                model.CouponRecord_StartTime = startTime;
                                model.CouponRecord_EndTime = endTime;
                            }
                            else
                            {
                                //已过期
                                model = null;
                            }
                        }
                        else
                        {
                            model = null;
                        }

                        if (model != null)
                        {
                            couponList.Add(model);
                            x.CouponPlan_UseCount += 1;
                            if (x.CouponPlan_Value > 0 && calculatedEnd != DateTime.MinValue && x.CouponPlan_VaildTime > calculatedEnd)
                            {
                                x.CouponPlan_VaildTime = calculatedEnd;
                            }
                        }
                    });
                }
            }

            #endregion

            var res = BLL.ParkOrder.CarInComplete(pushdata.Item1, pushdata.Item2, pushdata.Item3, coupons: couponList, couponPlanList: couponPlanList, overdueBills: pushdata.Item4);
            if (res > 0)
            {
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && (param.respass?.isSupplement ?? ("1,2,3,4".IndexOf(string.IsNullOrEmpty(order.ParkOrder_UserNo) ? "0" : order.ParkOrder_UserNo) != -1))) _ = Task.Run(() => BLL.PushEvent.CloudEnterCar(null, order, param?.account, param?.camerano, MiddlewareEventPriority.Delay_500_1));
                //删除历史记录中预入场订单
                //BLL.OrderDetail.DeleteByEst(param.parkno, param.carno);
                return new ResBody() { success = true, parkno = param.parkno, errmsg = "更新订单成功", data = TyziTools.Json.ToString(pushdata) };
            }
            else
            {
                return new ResBody() { success = false, parkno = param.parkno, errmsg = "更新订单失败" };
            }
        }

        /// <summary>
        /// 车辆离开停车场时，开闸成功回调
        /// </summary>
        /// <param name="param"></param>
        /// <param name="payResult"></param>
        /// <param name="payColl"></param>
        /// <param name="updateStoreCar">是否更新储值车余额：True-更新,false-不更新（微信扫车道码时支付后已下发中间件更新余额，这里调用又会更新一次，造成扣费两次）</param>
        /// <param name="usePayColl">是否需要处理传入的支付订单（传入的支付订单+从数据库查询的订单做统计等处理）：True-需要,false-不需要</param>
        /// <param name="isClose">是否关闭订单</param>
        /// <param name="handWin">是否手动点击弹窗放行</param>
        /// <returns></returns>
        public static ResBody CarOutComplete(Model.ParkGatePass param, ChargeModels.PayResult payResult = null, Model.PayColl payColl = null, bool updateStoreCar = true,
            bool usePayColl = true, bool isClose = true, Model.Owner owner = null, string payOrder_PayTypeCode = "79001", bool updateCoupon = true, bool handWin = false)
        {
            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, "车辆出场回调：" + param?.carno + $",isClose:{isClose}");
            if (string.IsNullOrEmpty(param.parkno)) { return new ResBody() { success = false, errmsg = "停车场编码parkno为空" }; }
            if (string.IsNullOrEmpty(param.carno)) { return new ResBody() { success = false, errmsg = "车牌号carno为空" }; }
            if (param.code != Model.EnumParkOrderStatus.Out) { return new ResBody() { success = false, errmsg = "出入场类型code错误" }; }

            Model.Device camera = BLL.Device.GetEntity(param.camerano) ?? new Model.Device();
            Model.Passway passway = BLL.Passway.GetEntity(camera.Device_PasswayNo) ?? new Model.Passway();
            var areaway = BLL.PasswayLink.GetAllEntityByPasswayNo(passway.Passway_No); //BLL.PasswayLink.GetAreaPassway("*", $"PasswayLink_PasswayNo='{passway.Passway_No}'");
            if (areaway == null || areaway.Count == 0) { return new ResBody() { success = false, errmsg = "车道未关联区域" }; }
            BLL.PasswayLink.GetParkAreaByPassway(areaway, out string inAreaNo, out string outAreaNo);

            string imgSrc = string.Empty;
            if (!string.IsNullOrEmpty(param.imgpath)) { param.imgpath = System.Web.HttpUtility.UrlEncode(param.imgpath); }
            if (!string.IsNullOrEmpty(param.img)) { imgSrc = PassHelperBiz.GetImageHttpUrl(param.parkno, param.img, camera?.Device_SentryHostNo); }
            else { imgSrc = PassHelperBiz.GetImageHttpUrl(param.parkno, param.imgpath, camera?.Device_SentryHostNo); }

            //查询停车订单
            var orderObj = BLL.ParkOrder.GetOrderDetailByCarNo(param.carno);
            if (orderObj.Item1 == null)
            {
                return new ResBody() { success = false, errmsg = "停车订单不存在:" + param.orderno + ",carno:" + param.carno, errcode = 201 };
            }
            DataCache.ParkOrder.Del(orderObj.Item1.ParkOrder_No);

            if (orderObj.Item1.ParkOrder_StatusNo == EnumParkOrderStatus.Out)
            {
                return new ResBody() { success = false, errmsg = "订单状态已出场" };
            }

            param.ParkOrderId = Convert.ToInt64(orderObj.Item1.ParkOrder_ID);

            if (orderObj.Item2 == null || orderObj.Item2.Count == 0)
            {
                return new ResBody() { success = false, errmsg = "停车订单明细不存在" };
            }

            #region【每月最高限额】
            List<Model.CarFees> carfees = null;
            List<Model.CarFeesOrder> carFeesOrders = null;
            var result = CommonBLL.CreateCarFees(new List<Model.ParkOrder>() { orderObj.Item1 }, payColl, new List<PayResult>() { payResult });
            carfees = result.Item1;
            carFeesOrders = result.Item2;
            #endregion

            var payorder = payColl?.payOrderList?.FirstOrDefault();
            string payTypeCode = payColl?.payOrderList?.FirstOrDefault()?.PayOrder_PayTypeCode;
            if (string.IsNullOrWhiteSpace(payTypeCode))
            {
                payTypeCode = payOrder_PayTypeCode;
            }

            string parkKey = payColl?.payOrderList?.FirstOrDefault()?.PayOrder_ParkKey;
            DateTime? payedTime = payColl?.payOrderList?.FirstOrDefault()?.PayOrder_PayedTime;
            var PayOrder_PayScene = payColl?.payOrderList?.FirstOrDefault()?.PayOrder_PayScene;
            var PayOrder_PassWayNo = payColl?.payOrderList?.FirstOrDefault()?.PayOrder_PassWayNo;
            if (!usePayColl) payColl = null;

            //查询车辆信息
            Model.Car car = BLL.Car.GetEntityByCarNo(orderObj.Item1.ParkOrder_CarNo);
            owner = owner ?? BLL.Owner.GetEntity(car?.Car_OwnerNo);

            if (isClose)
            {
                orderObj.Item1.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
                orderObj.Item1.ParkOrder_OutType = 2;
                orderObj.Item1.ParkOrder_IsSettle = Model.EnumOrderSettleStatus.True;
            }
            else
            {
                orderObj.Item1.ParkOrder_OutType = 0;
                orderObj.Item1.ParkOrder_ParkAreaNo = param.ParkAreaNo;
                orderObj.Item1.ParkOrder_ParkAreaName = param.ParkAreaName;
            }
            orderObj.Item1.ParkOrder_OutAdminAccount = param.account;
            orderObj.Item1.ParkOrder_OutAdminName = param.name;
            orderObj.Item1.ParkOrder_OutImgPath = imgSrc;
            orderObj.Item1.ParkOrder_OutImg = param.imgpath;
            orderObj.Item1.ParkOrder_OutPasswayNo = passway.Passway_No;
            orderObj.Item1.ParkOrder_OutPasswayName = passway.Passway_Name;
            orderObj.Item1.ParkOrder_OutTime = param.time;
            orderObj.Item1.ParkOrder_PayScene = orderObj.Item1.ParkOrder_PayScene ?? (int)Model.EnumPayScene.Exit;
            orderObj.Item1.ParkOrder_Remark = "";
            orderObj.Item1.ParkOrder_IsFree = param?.onoutdata?.isfee ?? 0;

            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer) if (param?.isSupplement ?? false) orderObj.Item1.ParkOrder_UserNo = orderObj.Item1.ParkOrder_StatusNo > 200 ? (orderObj.Item1.ParkOrder_UserNo == "1" ? "3" : "2") : "1"; else orderObj.Item1.ParkOrder_UserNo = "";
            #region 出场时修改车牌号或车牌颜色
            if (param.onoutdata != null)
            {
                //orderObj.Item1.ParkOrder_OutType = param.onoutdata.isfee == 1 ? (int)Model.EnumOutType.Free : (int)Model.EnumOutType.Payed;
                orderObj.Item1.ParkOrder_FreeReason = param.onoutdata.feereason;
                if (!string.IsNullOrEmpty(param.onoutdata.carno))
                {
                    orderObj.Item1.ParkOrder_CarNo = param.onoutdata.carno;
                    orderObj.Item2.ForEach(data => { data.OrderDetail_CarNo = param.onoutdata.carno; });
                }

                if (!string.IsNullOrEmpty(param.onoutdata.cartypeno) && !string.IsNullOrEmpty(param.onoutdata.cartypename))
                {
                    orderObj.Item1.ParkOrder_CarType = param.onoutdata.cartypeno;
                    orderObj.Item1.ParkOrder_CarTypeName = param.onoutdata.cartypename;
                    orderObj.Item2.ForEach(data =>
                    {
                        data.OrderDetail_CarType = param.onoutdata.cartypeno;
                        data.OrderDetail_CarTypeName = param.onoutdata.cartypename;
                    });
                }

                if (!string.IsNullOrEmpty(param.onoutdata.carcardtypeno) && !string.IsNullOrEmpty(param.onoutdata.carcardtypename))
                {
                    orderObj.Item1.ParkOrder_CarCardType = param.onoutdata.carcardtypeno;
                    orderObj.Item1.ParkOrder_CarCardTypeName = param.onoutdata.carcardtypename;
                    orderObj.Item2.ForEach(data =>
                    {
                        data.OrderDetail_CarCardType = param.onoutdata.carcardtypeno;
                        data.OrderDetail_CarCardTypeName = param.onoutdata.carcardtypename;
                    });
                }
            }
            #endregion

            #region 优惠券
            bool isUseCouponSet = false;
            List<Model.CouponRecord> couponList = null;
            if (updateCoupon && payResult != null && payResult.list != null && payResult.uselist != null && payResult.uselist.Count > 0)
            {
                List<string> couponnoLst = payResult.uselist.Select(x => x.CouponRecord_No).ToList();
                //选中的优惠设置（生成优惠券编码）
                if (param.onoutdata != null && param.onoutdata.couponList != null)
                {
                    var couponsetList = param.onoutdata.couponList.Where(x => x.CouponRecord_Set == 1).ToList();
                    if (couponsetList.Count > 0)
                    {
                        isUseCouponSet = true;
                        couponsetList.ForEach(item =>
                        {
                            if (couponnoLst.Contains(item.CouponRecord_No)) couponnoLst.Remove(item.CouponRecord_No);
                            string fixNo = "";
                            if (!string.IsNullOrEmpty(item.CouponRecord_IssueCarNo))
                            {
                                if (item.CouponRecord_IssueCarNo.Length > 1) fixNo = item.CouponRecord_IssueCarNo.Substring(1, item.CouponRecord_IssueCarNo.Length - 1);
                                else { fixNo = item.CouponRecord_IssueCarNo; }
                                fixNo = "-" + fixNo;
                            }
                            var couponUse = payResult.uselist.Where(y => y.CouponRecord_No == item.CouponRecord_No).FirstOrDefault();
                            item.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                            item.CouponRecord_Status = 1;
                            item.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                            item.CouponRecord_No = "ET" + Utils.CreateNumber_SnowFlake; //+ fixNo;
                            item.CouponRecord_ParkNo = orderObj.Item1.ParkOrder_ParkNo;
                            item.CouponRecord_DiscountMin = couponUse.CouponRecord_DiscountMin;
                            couponList = couponList ?? new List<Model.CouponRecord>();
                            couponList.Add(TyziTools.Json.ToModel<Model.CouponRecord>(TyziTools.Json.ToString(item)));
                        });
                    }
                }
                //选中的优惠券
                if (couponnoLst.Count > 0)
                {
                    string couponNoArray = string.Join(",", couponnoLst);
                    List<Model.CouponRecord> couponUseList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_ID,CouponRecord_No,CouponRecord_OnLine,CouponRecord_PreUse,CouponRecord_Other,CouponRecord_UseCount,CouponRecord_CouponCode,CouponRecord_ParkDiscountSetNo,CouponRecord_DiscountMin", $"CouponRecord_ParkOrderNo='{orderObj.Item1.ParkOrder_No}'");
                    if (couponUseList != null && couponUseList.Count > 0)
                    {
                        List<string> idList = payResult.uselist.Select(x => x.CouponRecord_No).ToList();
                        couponUseList = couponUseList.Where(x => idList.Contains(x.CouponRecord_No.ToString())).ToList();
                        couponUseList.ForEach(x =>
                        {
                            var couponUse = payResult.uselist.Where(y => y.CouponRecord_No == x.CouponRecord_No).FirstOrDefault();
                            x.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();

                            if (x.CouponRecord_CouponCode == "105")
                            {
                                x.CouponRecord_DiscountMin = x.CouponRecord_DiscountMin ?? 0;
                                x.CouponRecord_Paid = x.CouponRecord_Paid ?? 0;
                                x.CouponRecord_DiscountMin += (couponUse == null ? 0 : couponUse.CouponRecord_DiscountMin);
                                x.CouponRecord_Paid += (couponUse == null ? 0 : couponUse.DiscountMoney);
                            }
                            else
                            {
                                x.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                                x.CouponRecord_DiscountMin = x.CouponRecord_DiscountMin ?? 0;
                                x.CouponRecord_DiscountMin += (couponUse == null ? 0 : couponUse.CouponRecord_DiscountMin);
                            }

                            x.CouponRecord_Status = 1;
                        });

                        couponList = couponList ?? new List<Model.CouponRecord>();
                        couponList.AddRange(couponUseList);

                        //平台下发优惠券，弹窗后，使用扫码支付，此处推送优惠券的使用到平台
                        if (!usePayColl)
                        {
                            //if (payorder != null) //20220709-lrt 在中间件处理上报订单和优惠券
                            //    BLL.PushEvent.PayOrder(payorder.PayOrder_ParkKey, payorder, couponUseList);
                            //else
                            //    BLL.PayOrder.PushPayOrderToPlatform(parkKey, orderObj.Item1, payResult, couponUseList, payedTime, payTypeCode);
                        }

                    }
                }
            }
            #endregion

            #region 合计停车金额
            var payOrders = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{orderObj.Item1.ParkOrder_No}' AND PayOrder_Status=1") ?? new List<Model.PayOrder>();

            decimal alreadyPayedMoeny = 0;//已实收金额
            if (param.onoutdata != null && param.onoutdata.isfee == 1)
            {
                alreadyPayedMoeny = payOrders.Sum(x => x.PayOrder_PayedMoney) ?? 0;
            }

            var edNos = payOrders.Select(x => x.PayOrder_No).ToList();
            //支付订单
            if (usePayColl && payColl != null)
            {
                payColl.payOrderList?.ForEach(payOrder =>
                {
                    if (!string.IsNullOrEmpty(param.onoutdata?.cartypeno)) payOrder.PayOrder_CarTypeNo = param.onoutdata.cartypeno;
                    if (!string.IsNullOrEmpty(param.onoutdata?.carcardtypeno)) payOrder.PayOrder_CarCardTypeNo = param.onoutdata.carcardtypeno;

                    payOrder.PayOrder_PayedTime = DateTimeHelper.GetNowTime();
                    payOrder.PayOrder_Status = 1;//已支付
                                                 //由于选中优惠设置，重新生成了优惠券编号，这里要重新覆盖
                    if (isUseCouponSet && couponList != null && couponList.Count > 0)
                        payOrder.PayOrder_CouponRecordNo = string.Join(",", couponList.Select(x => x.CouponRecord_No));

                    if (!edNos.Contains(payOrder.PayOrder_No))
                        payOrders.Add(payOrder);
                });
                payColl.payPartList?.ForEach(payPart =>
                {
                    if (!string.IsNullOrEmpty(param.onoutdata?.cartypeno)) payPart.PayPart_CarTypeNo = param.onoutdata.cartypeno;
                    if (!string.IsNullOrEmpty(param.onoutdata?.cartypename)) payPart.PayPart_CarTypeName = param.onoutdata.cartypename;

                    if (!string.IsNullOrEmpty(param.onoutdata?.carcardtypeno)) payPart.PayPart_CarCardTypeNo = param.onoutdata.carcardtypeno;
                    if (!string.IsNullOrEmpty(param.onoutdata?.carcardtypename)) payPart.PayPart_CarCardTypeName = param.onoutdata.carcardtypename;

                    payPart.PayPart_EditTime = DateTimeHelper.GetNowTime();
                    payPart.PayPart_Status = 1;//已支付
                });
            }

            orderObj.Item1.ParkOrder_TotalAmount = payOrders?.Sum(x => x.PayOrder_Money) ?? 0;
            orderObj.Item1.ParkOrder_TotalPayed = payOrders?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
            //是否免费放行
            if (param.onoutdata != null && param.onoutdata.isfee == 1)
            {
                orderObj.Item1.ParkOrder_TotalAmount = payOrders.Sum(x => x.PayOrder_Money) ?? 0;
                orderObj.Item1.ParkOrder_TotalPayed = alreadyPayedMoeny;
            }
            #endregion

            List<Model.HoursTotal> hoursTotals = null;

            if (payResult != null && payResult.list != null)
            {
                //查询手动改价金额,分摊减去停车明细的周期累计金额
                payResult.list = BLL.CommonBLL.ApportionedAmountByPayPart(payResult.list, (!usePayColl ? null : payColl), null, orderObj.Item1.ParkOrder_No);
            }

            string areaNo = string.Empty;
            foreach (var item in orderObj.Item2)
            {
                if (item.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In)
                {
                    item.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                    item.OrderDetail_OutAdminAccount = param.account;
                    item.OrderDetail_OutAdminName = param.name;
                    item.OrderDetail_OutImgPath = imgSrc;
                    item.OrderDetail_OutImg = param.imgpath;
                    item.OrderDetail_OutPasswayNo = passway.Passway_No;
                    item.OrderDetail_OutPasswayName = passway.Passway_Name;
                    item.OrderDetail_OutTime = param.time;
                    item.OrderDetail_OutType = 2;
                    item.OrderDetail_Remark = "";

                    areaNo = item.OrderDetail_ParkAreaNo;
                }
                //更新明细结算状态、周期生效时间、金额、计费开始时间
                if (payResult != null && payResult.list != null)
                {
                    payResult.list = payResult.list.OrderBy(x => x.starttime).ToList();
                    var pdItem = payResult.list.Where(x => x.orderdetailno == item.OrderDetail_No).LastOrDefault();
                    if (pdItem != null && !string.IsNullOrEmpty(pdItem.orderdetailno))
                    {
                        item.OrderDetail_NextCycleTime = pdItem.nextcycletime;//下一次周期生效时间
                        item.Orderdetail_CycleMoney = pdItem.NextCyclePaidFees;//下一次停车，周期已累积支付总金额
                        item.Orderdetail_CycleFreeMin = pdItem.nextcyclefreemin;

                        if (!string.IsNullOrEmpty(pdItem.nexthourscontent) && pdItem.nexthourstime != null)
                        {
                            //detail.orderdetail_HoursBeginTime = pdItem.nexthourstime;
                            //detail.orderdetail_HoursContent = pdItem.nexthourscontent;
                            hoursTotals = hoursTotals ?? new List<Model.HoursTotal>();
                            hoursTotals.Add(new Model.HoursTotal()
                            {
                                HoursTotal_No = item.OrderDetail_No,
                                HoursTotal_CarNo = item.OrderDetail_CarNo,
                                HoursTotal_ParkOrderNo = item.OrderDetail_ParkOrderNo,
                                HoursTotal_CarType = item.OrderDetail_CarType,
                                HoursTotal_CarCardType = item.OrderDetail_CarCardType,
                                HoursTotal_ParkAreaNo = item.OrderDetail_ParkAreaNo,
                                HoursTotal_EnterTime = item.OrderDetail_EnterTime,
                                HoursTotal_PayTime = DateTime.Now,
                                HoursTotal_BeginTime = pdItem.nexthourstime,
                                HoursTotal_Content = pdItem.nexthourscontent,
                            });
                        }
                        item.Orderdetail_UseFreeMin = Utils.ObjectToInt(item.Orderdetail_UseFreeMin, 0) + Utils.ObjectToInt(pdItem.currentfreemin, 0);

                        if (AppBasicCache.GetPolicyPark != null && AppBasicCache.GetPolicyPark.PolicyPark_OverTimePay == 1)
                        {
                            if (payOrders.Sum(x => x.PayOrder_Money) > 0)
                            {
                                item.OrderDetail_IsSettle = 1;//结算状态
                            }
                        }
                        else
                        {
                            item.OrderDetail_IsSettle = 1;//结算状态
                        }

                        if (pdItem.calcbegintime != null)
                        {
                            item.OrderDetail_CurrCalcTime = pdItem.calcbegintime;//当前计费开始时间
                        }
                        List<Model.PayPart> paypartlist = BLL.BaseBLL._GetAllEntity(new Model.PayPart(), "*", $" PayPart_OrderDetailNo='{item.OrderDetail_No}' and PayPart_Status=1 ");
                        item.OrderDetail_TotalAmount = paypartlist?.Select(a => a.PayPart_OrderMoney).Sum()
                            + payColl?.payPartList?.Where(a => a.PayPart_OrderDetailNo == item.OrderDetail_No).Select(a => a.PayPart_OrderMoney).Sum(); //出场总金额
                    }
                }

            }

            if (!isClose)
            {
                var ftemp = orderObj.Item2?.Where(m => m.OrderDetail_EnterPasswayNo == passway.Passway_No && m.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Est);
                if (ftemp != null)
                {
                    foreach (var detail1 in ftemp)
                    {
                        detail1.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                        detail1.OrderDetail_IsSettle = 0;
                    }
                }
            }

            List<Model.ParkOrder> poModelLst = null;
            List<Model.OrderDetail> orderDetailList = new List<Model.OrderDetail>();
            orderDetailList.AddRange(orderObj.Item2);

            if (orderObj.Item1.ParkOrder_IsUnlicensedCar == 1)
            {
                if (param.respass?.resorder?.resIn != null && param.respass.resorder.resIn.orderDetail != null)
                {
                    var detail = param.respass.resorder.resIn.orderDetail;
                    detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                    orderDetailList.Add(detail);
                }
            }

            //if( orderObj.Item1.ParkOrder_IsUnlicensedCar==1 && param.respass?.passres?.)

            //一位多车处理
            BLL.ParkOrder.OutParkForMultiCar(orderObj.Item1, inAreaNo, outAreaNo, (pLst, dLst) =>
            {
                poModelLst = BLL.ParkOrder.ChangeOrderLst(orderObj.Item1, pLst);
                orderDetailList = BLL.ParkOrder.ChangeDetailLst(orderDetailList, dLst);
            });

            List<Model.Ledger> leadgerList = null;

            //更新储值车余额
            if (updateStoreCar && car != null && payResult != null && car.Car_Category == Model.EnumCarType.Prepaid.ToString())
            {
                if (payResult.payed != 2 && payResult.chuzhiamount > 0)
                {
                    if (owner != null)
                    {
                        Model.Ledger ledger = null;
                        var remainingMoney = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - payResult.chuzhiamount;
                        //if (remainingMoney > 0)
                        //{
                        ledger = new Model.Ledger()
                        {
                            Ledger_CarNo = car.Car_CarNo,
                            Ledger_Space = owner.Owner_Space,
                            Ledger_Type = 2,
                            Ledger_CardType = Common.CarTypeHelper.GetCarTypeIndex(car.Car_Category),
                            Ledger_Code = 2,
                            Ledger_Money = payResult.chuzhiamount,
                            Ledger_BeforeMoeny = owner.Owner_Balance,
                            Ledger_Time = DateTime.Now,
                            Ledger_ParkOrderNo = orderObj.Item1.ParkOrder_No,
                        };
                        //}
                        owner.Owner_Balance = remainingMoney;
                        if (owner.Owner_Balance < 0) owner.Owner_Balance = 0;
                        if (ledger != null)
                        {
                            ledger.Ledger_AfterMoeny = owner.Owner_Balance;
                            leadgerList = new List<Ledger> { ledger };
                        }
                    }
                }
            }

            List<Model.Car> carList = new List<Model.Car>() { car };
            List<Model.Owner> ownerList = new List<Model.Owner>() { owner };

            //充电滞留数据更新为已支付
            List<Model.DetentionPenalty> penaltyList = null;
            if (payResult != null && payResult.penaltylist != null && payResult.penaltyamount > 0)
            {
                penaltyList = TyziTools.Json.ToObject<List<Model.DetentionPenalty>>(TyziTools.Json.ToString(payResult.penaltylist));
                penaltyList.ForEach(item => { item.DetentionPenalty_PayStatus = 1; item.DetentionPenalty_ParkOrderNo = orderObj.Item1.ParkOrder_No; });
            }

            var pushdata = new ResBodyDataOut(poModelLst, orderDetailList, payColl?.payOrderList, couponList, carList, ownerList, payColl?.payPartList, penaltyList);

            if (poModelLst != null)
            {
                foreach (var item in poModelLst)
                {
                    if (item != null && item.ParkOrder_StatusNo > 200 && item.ParkOrder_StatusNo != 203 && orderObj.Item1?.ParkOrder_No == item.ParkOrder_No)
                    {
                        if (!string.IsNullOrEmpty(item.ParkOrder_ReserveNo))
                        {
                            Model.Reserve reserve = BLL.Reserve._GetEntityByNo(new Model.Reserve(), item.ParkOrder_ReserveNo);
                            if (reserve != null)
                            {
                                reserve.Reserve_Status = 4;
                                reserve.Reserve_OutTime = param.time;
                                pushdata.Item8 = new List<Model.Reserve> { reserve };
                            }
                        }
                        break;
                    }
                }
            }

            #region 是否存在追缴订单
            if (param.respass != null && param.respass.unpaidresult != null && param.respass.unpaidresult.Count > 0)
            {
                pushdata.Item9 = new List<Model.Unpaid>();
                foreach (var unp in param.respass.unpaidresult)
                {
                    var order = unp.unpaidorder;

                    if (string.IsNullOrEmpty(unp.payres?.carBillNo))
                    {
                        //更新储值车余额
                        if (updateStoreCar && car != null && unp.payres != null && car.Car_Category == Model.EnumCarType.Prepaid.ToString())
                        {
                            if (unp.payres.payed != 2 && unp.payres.chuzhiamount > 0)
                            {
                                if (owner != null)
                                {
                                    Model.Ledger ledger = null;
                                    var remainingMoney = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - unp.payres.chuzhiamount;
                                    //if (remainingMoney > 0)
                                    //{
                                    ledger = new Model.Ledger()
                                    {
                                        Ledger_CarNo = car.Car_CarNo,
                                        Ledger_Space = owner.Owner_Space,
                                        Ledger_Type = 2,
                                        Ledger_CardType = Common.CarTypeHelper.GetCarTypeIndex(car.Car_Category),
                                        Ledger_Code = 2,
                                        Ledger_Money = unp.payres.chuzhiamount,
                                        Ledger_BeforeMoeny = owner.Owner_Balance,
                                        Ledger_Time = DateTime.Now,
                                        Ledger_ParkOrderNo = order.ParkOrder_No,
                                    };
                                    //}
                                    owner.Owner_Balance = remainingMoney;
                                    if (owner.Owner_Balance < 0) owner.Owner_Balance = 0;
                                    if (ledger != null)
                                    {
                                        ledger.Ledger_AfterMoeny = owner.Owner_Balance;
                                        leadgerList = leadgerList ?? new List<Ledger>();
                                        leadgerList.Add(ledger);
                                    }
                                }
                            }
                        }


                        order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;

                        var pys = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{unp.unpaidorder.ParkOrder_No}' AND PayOrder_Status=1") ?? new List<Model.PayOrder>();


                        unp.unpaiddetails?.ForEach(detail =>
                        {
                            if (detail.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Follow)
                            {
                                detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                                detail.OrderDetail_TotalAmount = unp.payres.payedamount;
                            }

                        });

                        if (unp.unpaidorder != null) poModelLst.Add(order);
                        if (unp.unpaiddetails != null)
                            orderDetailList.AddRange(unp.unpaiddetails);
                        PayColl payi = null;
                        if (unp.payres?.orderamount > 0)
                        {
                            string PayOrderNo = $"PO{Utils.CreateNumber}{parkKey}";
                            payi = BLL.CommonBLL.AddPayOrder(
                               outTime: order.ParkOrder_OutTime
                               , payResult: unp.payres
                               , ParkOrderNo: order.ParkOrder_No
                               , lgAdmin: new Model.Admins { Admins_Account = param.account, Admins_Name = param.name }
                               , sOrderNo: PayOrderNo
                               , PayOrder_Status: 1
                               , sFree: (param.onoutdata?.isfee == 1 ? param.onoutdata?.feereason : null)
                               , PayOrder_PayTypeCode: payTypeCode);

                            pys.AddRange(payi.payOrderList);
                        }


                        order.ParkOrder_TotalAmount = pys?.Sum(x => x.PayOrder_Money) ?? 0;
                        order.ParkOrder_TotalPayed = pys?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                        order.ParkOrder_Remark += $"由[{orderObj.Item1?.ParkOrder_No}]出场追缴";

                        pushdata.Item1?.Add(order);
                        if (unp.unpaiddetails != null && unp.unpaiddetails.Count > 0)
                            pushdata.Item2?.AddRange(unp.unpaiddetails);

                        if (payi != null)
                        {
                            if (payi.payOrderList?.Count > 0)
                            {
                                pushdata.Item3 = pushdata.Item3 ?? new List<Model.PayOrder>();
                                pushdata.Item3.AddRange(payi.payOrderList);
                            }
                            if (payi.payPartList?.Count > 0)
                            {
                                pushdata.Item7 = pushdata.Item7 ?? new List<Model.PayPart>();
                                pushdata.Item7.AddRange(payi.payPartList);
                            }
                        }

                        pushdata.Item9.Add(new Model.Unpaid
                        {
                            account = param.account,
                            name = param.name,
                            orderNo = order.ParkOrder_No,
                            time = order.ParkOrder_OutTime ?? DateTimeHelper.GetNowTime()
                        });
                    }
                    else
                    {
                        if (handWin)
                        {
                            var billRet = PayOverBills(unp, order);

                            if (pushdata.Item15 == null) pushdata.Item15 = billRet.Item1;
                            else pushdata.Item15.AddRange(billRet.Item1);

                            if (billRet.Item2.Count > 0) if (pushdata.Item3 == null) { pushdata.Item3 = billRet.Item2; } else { pushdata.Item3.AddRange(billRet.Item2); }
                            if (billRet.Item3.Count > 0) if (pushdata.Item7 == null) { pushdata.Item7 = billRet.Item3; } else { pushdata.Item7.AddRange(billRet.Item3); }
                        }
                    }
                }
            }
            #endregion

            PassHelperBiz.settlePaymethod(param.orderno, out var paymethods);
            pushdata.Item10 = paymethods;
            pushdata.ledgerList = leadgerList;
            pushdata.Item12 = hoursTotals;
            pushdata.Item13 = carfees;
            pushdata.Item14 = carFeesOrders;

            if (payResult == null || payResult.payed == 0)
            {
                //更新未支付记录
                var unpaidRecord = BLL.UnpaidRecord.GetInstance(param.time ?? DateTime.Now);
                var unpaidRecordModel = unpaidRecord.GetAllEntity("*", $"UnpaidRecord_ParkOrderNo ='{param.orderno}'");
                if (unpaidRecordModel != null)
                {
                    //支付成功后关闭未支付记录
                    unpaidRecordModel.ForEach(m =>
                    {
                        m.UnpaidRecord_Status = "1";
                        var ret = unpaidRecord.UpdateByModel(m);
                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"关闭识别未支付记录[{m.UnpaidRecord_No}]结果:{ret}");
                    });
                }
            }

            bool res = BLL.OrderDetail.UpdateByList(pushdata);
            if (res)
            {
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && (param.respass?.isSupplement ?? ("1,2,3,4".IndexOf(string.IsNullOrEmpty(orderObj.Item1.ParkOrder_UserNo) ? "0" : orderObj.Item1.ParkOrder_UserNo) != -1)))
                {
                    _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                    {
                        payColl?.payOrderList?.ForEach(x =>
                        {
                            BLL.PushEvent.CloudPayOrder(parkKey, x);
                        });
                        BLL.PushEvent.CloudOutCar(parkKey, orderObj.Item1, param.account, param.camerano);
                        return Task.CompletedTask;
                    });
                }


                if (orderObj.Item1?.ParkOrder_TotalAmount > 0)
                {

                    if (AppBasicCache.PrintSetting != null && AppBasicCache.PrintSetting.enable == 1 && AppBasicCache.PrintSetting.mode == 1)
                    {
                        _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                        {
                            try
                            {
                                Receipt receipt = new Receipt();
                                receipt.Title = AppBasicCache.PrintSetting.title;
                                receipt.SubTitle = AppBasicCache.PrintSetting.subtitle;
                                receipt.CarNo = param.carno;
                                receipt.CarCardType = orderObj.Item1?.ParkOrder_CarCardTypeName;
                                receipt.InTime = orderObj.Item1?.ParkOrder_EnterTime != null ? orderObj.Item1?.ParkOrder_EnterTime.Value.ToString("yyyy年MM月dd日 HH:mm:ss") : "";
                                receipt.OutTime = orderObj.Item1?.ParkOrder_OutTime.Value.ToString("yyyy年MM月dd日 HH:mm:ss");
                                receipt.TotalAmount = orderObj.Item1?.ParkOrder_TotalAmount.Value.ToString("0.00") + "元";
                                receipt.PayedAmount = orderObj.Item1?.ParkOrder_TotalPayed.Value.ToString("0.00") + "元";
                                receipt.Operator = param.name;
                                receipt.End = AppBasicCache.PrintSetting.endcontent;
                                ReceiptPrinter receiptPrinter = new ReceiptPrinter(receipt);
                                receiptPrinter.PrintReceipt();
                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{param.carno}自动打印收费小票!");
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"自动打印收费小票异常：{ex.Message},{ex.StackTrace}");
                            }

                            return Task.CompletedTask;
                        });
                    }
                }


                return new ResBody() { success = true, errmsg = "更新订单成功", data = TyziTools.Json.ToString(pushdata, true) };
            }
            else
            {
                return new ResBody() { success = false, errmsg = "更新订单失败" };
            }
        }


        /// <summary>
        /// [无通行结果]车辆离开停车场时，开闸成功回调
        /// </summary>
        /// <param name="param"></param>
        /// <param name="payResult"></param>
        /// <param name="payColl"></param>
        /// <param name="updateStoreCar">是否更新储值车余额：True-更新,false-不更新（微信扫车道码时支付后已下发中间件更新余额，这里调用又会更新一次，造成扣费两次）</param>
        /// <param name="usePayColl">是否需要处理传入的支付订单（传入的支付订单+从数据库查询的订单做统计等处理）：True-需要,false-不需要</param>
        /// <param name="isClose">是否关闭订单</param>
        /// <returns></returns>
        public static ResBody CarOutCompleteForNoPassResult(string orderNo, Model.ParkOrder order = null)
        {
            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, "车辆出场回调[无通行结果]：" + orderNo);

            if (order == null) order = BLL.ParkOrder.GetEntity(orderNo);
            if (order == null) { return new ResBody() { success = false, errmsg = "停车订单不存在:" + orderNo }; }
            if (order.ParkOrder_StatusNo == null || order.ParkOrder_StatusNo > 200) { return new ResBody() { success = false, errmsg = "停车订单状态错误:" + order.ParkOrder_StatusNo }; }


            DataCache.ParkOrder.Del(order.ParkOrder_No);

            var details = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo='{order.ParkOrder_No}'");
            if (details == null || details.Count == 0) { return new ResBody() { success = false, errmsg = "停车订单明细不存在" }; }

            details = details.OrderBy(x => x.OrderDetail_EnterTime).ToList();

            order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
            order.ParkOrder_OutType = 2;
            order.ParkOrder_IsSettle = Model.EnumOrderSettleStatus.True;

            Model.Passway passway = BLL.Passway.GetEntity(order.ParkOrder_OutPasswayNo) ?? new Model.Passway();
            var areaway = BLL.PasswayLink.GetAllEntityByPasswayNo(passway.Passway_No); //BLL.PasswayLink.GetAreaPassway("*", $"PasswayLink_PasswayNo='{passway.Passway_No}'");
            if (areaway == null || areaway.Count == 0) { return new ResBody() { success = false, errmsg = "车道未关联区域" }; }
            BLL.PasswayLink.GetParkAreaByPassway(areaway, out string inAreaNo, out string outAreaNo);

            #region 合计停车金额
            var payOrders = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{order.ParkOrder_No}' AND PayOrder_Status=1") ?? new List<Model.PayOrder>();
            var edNos = payOrders.Select(x => x.PayOrder_No).ToList();

            order.ParkOrder_TotalAmount = payOrders?.Sum(x => x.PayOrder_Money) ?? 0;
            order.ParkOrder_TotalPayed = payOrders?.Sum(x => x.PayOrder_PayedMoney) ?? 0;

            #endregion

            string areaNo = string.Empty;
            foreach (var item in details)
            {
                if (item.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In)
                {
                    item.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                    item.OrderDetail_OutTime = order.ParkOrder_OutTime;
                    item.OrderDetail_OutType = 2;
                    item.OrderDetail_Remark = "";

                    areaNo = item.OrderDetail_ParkAreaNo;
                }
            }

            List<Model.ParkOrder> poModelLst = null;
            List<Model.OrderDetail> orderDetailList = new List<Model.OrderDetail>();
            orderDetailList.AddRange(details);

            //一位多车处理
            BLL.ParkOrder.OutParkForMultiCar(order, inAreaNo, outAreaNo, (pLst, dLst) =>
            {
                poModelLst = BLL.ParkOrder.ChangeOrderLst(order, pLst);
                orderDetailList = BLL.ParkOrder.ChangeDetailLst(orderDetailList, dLst);
            });

            var pushdata = new ResBodyDataOut(poModelLst, orderDetailList, null, null, null, null, null, null);

            if (poModelLst != null)
            {
                foreach (var item in poModelLst)
                {
                    if (item != null && item.ParkOrder_StatusNo > 200 && item.ParkOrder_StatusNo != 203 && order.ParkOrder_No == item.ParkOrder_No)
                    {
                        if (!string.IsNullOrEmpty(item.ParkOrder_ReserveNo))
                        {
                            Model.Reserve reserve = BLL.Reserve._GetEntityByNo(new Model.Reserve(), item.ParkOrder_ReserveNo);
                            if (reserve != null)
                            {
                                reserve.Reserve_Status = 4;
                                reserve.Reserve_OutTime = order.ParkOrder_OutTime;
                                pushdata.Item8 = new List<Model.Reserve> { reserve };
                            }
                        }
                        break;
                    }
                }
            }

            PassHelperBiz.settlePaymethod(order.ParkOrder_No, out var paymethods);
            pushdata.Item10 = paymethods;

            bool res = BLL.OrderDetail.UpdateByList(pushdata);
            if (res)
            {
                return new ResBody() { success = true, errmsg = "更新订单成功", data = TyziTools.Json.ToString(pushdata, true) };
            }
            else
            {
                return new ResBody() { success = false, errmsg = "更新订单失败" };
            }
        }

        /// <summary>
        /// 无入场记录放行;无入场记录不计周期，不计优惠;
        /// </summary>
        /// <param name="pass">出场判断返回的结果</param>
        /// <param name="Admin_Account">操作员账号</param>
        /// <param name="Admin_Name">操作员名称</param>
        /// <returns></returns>
        public static ResBody CarOutNoRecord(Model.ResultPass pass, string Admin_Account, string Admin_Name, Model.ParkOrder order, bool isPayed = false, string sPayType = ""
            , bool updateStoreCar = true, bool generateUnpayOrder = true, Model.PayColl payColls = null, bool updateCoupon = true, bool handWin = false)
        {
            int outState = 1;
            Model.ResultPassData passres = pass.passres;
            ChargeModels.PayResult payres = pass.payres;
            var cashrobotamount = payres?.cashrobotamount ?? 0;
            var zlamount = payres?.zlamount ?? 0;
            //补缴金额
            decimal unpaidMoeny = pass.unpaidresult?.Sum(m => m.payres?.payedamount) ?? 0;

            if (pass.resorder.resOut != null && pass.passres.code == 0) { return new ResBody() { success = false, errmsg = "禁止通行" }; }

            List<Model.PasswayLink> links = BLL.PasswayLink.GetAllEntity(passres.passway.Passway_ParkNo) ?? new List<Model.PasswayLink>();
            Model.PasswayLink link = links.Find(x => x.PasswayLink_PasswayNo == passres.passway.Passway_No && x.PasswayLink_GateType == 0);
            if (link == null) { return new ResBody() { success = false, errmsg = "区域不存在" }; }
            Model.ParkArea area = BLL.ParkArea.GetEntity(link.PasswayLink_ParkAreaNo);
            if (area == null) { return new ResBody() { success = false, errmsg = "区域不存在" }; }

            var gate = BLL.Passway.GetPasswayGateType(passres.passway.Passway_No);

            if (order == null && gate != 3)
            {
                order = pass.resorder?.resOut?.parkorder ?? pass.resorder?.resOut?.noRecordOrder;

                if (!(pass?.passres?.lastFeeNoRecord ?? false))
                {
                    if (!(pass?.policy?.autotakerecord == 1 || pass?.policy?.takerecord == 1)) outState = 0;
                }
                if (order == null) order = BLL.ParkOrder.CreateParkOrder(area.ParkArea_ParkNo, area.ParkArea_No, area.ParkArea_Name, passres.carno, passres.carcardtype?.CarCardType_No, passres.carcardtype?.CarCardType_Name, passres.cartype?.CarType_No, passres.cartype?.CarType_Name, DateTimeHelper.GetNowTime().AddMinutes(-Utils.ObjectToInt(AppBasicCache.GetPolicyPark?.PolicyPark_NoRecordRangTime, 5)), passres.passway.Passway_No, passres.passway.Passway_Name, 0, 1, passres?.owner?.Owner_No, passres?.owner?.Owner_Name, isNoEnter: true);
            }
            else
            {
                DataCache.ParkOrder.Del(order?.ParkOrder_No);
            }

            string imgSrc = string.Empty;
            if (!string.IsNullOrEmpty(passres.img)) { imgSrc = PassHelperBiz.GetImageHttpUrl(passres.passway.Passway_ParkNo, passres.img, passres?.device?.Device_SentryHostNo); }
            if (!string.IsNullOrEmpty(passres.localimage)) { passres.localimage = System.Web.HttpUtility.UrlEncode(passres.localimage); }

            Model.ParkOrder preOrder = null;
            Model.ParkOrder po = null;
            Model.OrderDetail od = null;
            if (gate == 3)//内出
            {
                //查询停车订单
                var orderObj = BLL.ParkOrder.GetNoRecordOrderByCarNo(passres.carno, DateTimeHelper.GetNowTime().AddMinutes(-((AppBasicCache.GetPolicyPark.PolicyPark_NoRecordRangTime ?? 10) + 360)).ToString("yyyy-MM-dd HH:mm:ss"), gate);
                if (orderObj.Item1 != null && orderObj.Item1.Count > 0)
                {
                    po = orderObj.Item1.Last();
                    po.ParkOrder_StatusNo = 200;
                    po.ParkOrder_IsNoInRecord = 0;
                    po.ParkOrder_EnterImgPath = imgSrc;
                    po.ParkOrder_EnterImg = passres.localimage;
                    po.ParkOrder_UserNo = "4";

                    if (orderObj.Item1.Count > 1)
                    {
                        preOrder = orderObj.Item1.Find(x => x.ParkOrder_No != po.ParkOrder_No);
                        if (preOrder != null) { preOrder.ParkOrder_StatusNo = 202; po.ParkOrder_UserNo = preOrder.ParkOrder_UserNo; }
                    }
                }

                if (orderObj.Item2 != null && orderObj.Item2.Count > 0)
                {
                    orderObj.Item2.Last().OrderDetail_StatusNo = 200;
                    od = orderObj.Item2.Last();
                }

                order = po;
                if (order == null)
                {

                    Model.PasswayLink link2 = links.Find(x => x.PasswayLink_PasswayNo == passres.passway.Passway_No && x.PasswayLink_GateType == 1);
                    if (link2 == null) { return new ResBody() { success = false, errmsg = "区域不存在" }; }
                    Model.ParkArea area2 = BLL.ParkArea.GetEntity(link2.PasswayLink_ParkAreaNo);
                    if (area2 == null) { return new ResBody() { success = false, errmsg = "区域不存在" }; }

                    order = BLL.ParkOrder.CreateParkOrder(area2.ParkArea_ParkNo, area2.ParkArea_No, area2.ParkArea_Name, passres.carno, passres.carcardtype?.CarCardType_No, passres.carcardtype?.CarCardType_Name, passres.cartype?.CarType_No, passres.cartype?.CarType_Name, DateTimeHelper.GetNowTime().AddMinutes(-5), passres.passway.Passway_No, passres.passway.Passway_Name, 0, 1, passres?.owner?.Owner_No, passres?.owner?.Owner_Name, isNoEnter: true);
                    order.ParkOrder_StatusNo = 200;
                    order.ParkOrder_IsNoInRecord = 0;
                    order.ParkOrder_EnterImgPath = imgSrc;
                    order.ParkOrder_EnterImg = passres.localimage;
                    od = BLL.OrderDetail.CreateOrderDetail(order.ParkOrder_No, order.ParkOrder_ParkNo, order.ParkOrder_ParkAreaNo, order.ParkOrder_ParkAreaName, order.ParkOrder_CarNo, order.ParkOrder_CarCardType, order.ParkOrder_CarCardTypeName, order.ParkOrder_CarType, order.ParkOrder_CarTypeName, order.ParkOrder_EnterTime.Value, order.ParkOrder_EnterPasswayNo, order.ParkOrder_EnterPasswayName);
                    od.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                }
            }

            #region 优惠券
            bool isUseCouponSet = false;
            List<Model.CouponRecord> couponList = null;
            if (updateCoupon && order != null && payres != null && payres.uselist != null && payres.uselist.Count > 0)
            {
                List<string> couponnoLst = payres.uselist.Select(x => x.CouponRecord_No).ToList();
                //选中的优惠设置（生成优惠券编码）
                var couponsetList = payres.recordlist.Where(x => x.CouponRecord_Set == 1 && (payres.uselist?.Any(y => y.CouponRecord_No == x.CouponRecord_No) ?? false)).ToList();
                if (couponsetList.Count > 0)
                {
                    isUseCouponSet = true;
                    couponsetList.ForEach(item =>
                    {
                        if (couponnoLst.Contains(item.CouponRecord_No)) couponnoLst.Remove(item.CouponRecord_No);
                        string fixNo = "";
                        if (!string.IsNullOrEmpty(item.CouponRecord_IssueCarNo))
                        {
                            if (item.CouponRecord_IssueCarNo.Length > 1) fixNo = item.CouponRecord_IssueCarNo.Substring(1, item.CouponRecord_IssueCarNo.Length - 1);
                            else { fixNo = item.CouponRecord_IssueCarNo; }
                            fixNo = "-" + fixNo;
                        }
                        var couponUse = payres.uselist.Where(y => y.CouponRecord_No == item.CouponRecord_No).FirstOrDefault();
                        item.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                        item.CouponRecord_Status = 1;
                        item.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                        item.CouponRecord_No = "ET" + Utils.CreateNumber_SnowFlake; //+ fixNo;
                        item.CouponRecord_ParkNo = order?.ParkOrder_ParkNo;
                        item.CouponRecord_DiscountMin = couponUse.CouponRecord_DiscountMin;
                        couponList = couponList ?? new List<Model.CouponRecord>();
                        couponList.Add(TyziTools.Json.ToModel<Model.CouponRecord>(TyziTools.Json.ToString(item)));
                    });
                }

                //选中的优惠券
                if (couponnoLst.Count > 0)
                {
                    string couponNoArray = string.Join(",", couponnoLst);
                    List<Model.CouponRecord> couponUseList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_ID,CouponRecord_No,CouponRecord_OnLine,CouponRecord_PreUse,CouponRecord_Other", $"CouponRecord_ParkOrderNo='{order?.ParkOrder_No}'");
                    if (couponUseList != null && couponUseList.Count > 0)
                    {
                        List<string> idList = payres.uselist.Select(x => x.CouponRecord_No).ToList();
                        couponUseList = couponUseList.Where(x => idList.Contains(x.CouponRecord_No.ToString())).ToList();
                        couponUseList.ForEach(x =>
                        {
                            var couponUse = payres.uselist.Where(y => y.CouponRecord_No == x.CouponRecord_No).FirstOrDefault();
                            x.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                            x.CouponRecord_Status = 1;
                            x.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                        });

                        couponList = couponList ?? new List<Model.CouponRecord>();
                        couponList.AddRange(couponUseList);
                        isUseCouponSet = true;
                    }
                }

                //选中的时段全免
                var couponsetList2 = payres.recordlist.Where(x => x.CouponRecord_CouponCode == "105"
                && (payres.uselist?.Any(y => y.CouponRecord_No == x.CouponRecord_No) ?? false) && !(couponList?.Any(j => j.CouponRecord_No == x.CouponRecord_No) ?? false)).ToList();
                if (couponsetList2.Count > 0)
                {
                    isUseCouponSet = true;
                    couponsetList2.ForEach(item =>
                    {
                        if (couponnoLst.Contains(item.CouponRecord_No)) couponnoLst.Remove(item.CouponRecord_No);
                        string fixNo = "";
                        if (!string.IsNullOrEmpty(item.CouponRecord_IssueCarNo))
                        {
                            if (item.CouponRecord_IssueCarNo.Length > 1) fixNo = item.CouponRecord_IssueCarNo.Substring(1, item.CouponRecord_IssueCarNo.Length - 1);
                            else { fixNo = item.CouponRecord_IssueCarNo; }
                            fixNo = "-" + fixNo;
                        }
                        var couponUse = payres.uselist.Where(y => y.CouponRecord_No == item.CouponRecord_No).FirstOrDefault();
                        item.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                        item.CouponRecord_Status = 1;
                        item.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                        item.CouponRecord_ParkNo = order?.ParkOrder_ParkNo;
                        item.CouponRecord_DiscountMin = couponUse.CouponRecord_DiscountMin;
                        if (order != null) item.CouponRecord_ParkOrderNo = order?.ParkOrder_No;
                        couponList = couponList ?? new List<Model.CouponRecord>();
                        couponList.Add(TyziTools.Json.ToModel<Model.CouponRecord>(TyziTools.Json.ToString(item)));
                    });
                }
            }
            #endregion

            #region 创建异常订单
            string remark = "无入场记录出场";
            Model.PassRecord anOrder = new Model.PassRecord()
            {
                PassRecord_No = Utils.CreateNumberWith("N"),
                PassRecord_ParkOrderNo = order?.ParkOrder_No,
                PassRecord_ParkNo = passres.passway.Passway_ParkNo,
                PassRecord_CarNo = passres.carno,
                PassRecord_CarCardType = passres.carcardtype?.CarCardType_No,
                PassRecord_CarCardTypeName = passres.carcardtype?.CarCardType_Name,
                PassRecord_CarType = passres.cartype?.CarType_No,
                PassRecord_CarTypeName = passres.cartype?.CarType_Name,
                PassRecord_Type = 0,
                PassRecord_PasswayNo = passres.passway.Passway_No,
                PassRecord_PasswayName = passres.passway.Passway_Name,
                PassRecord_DeviceNo = passres.device?.Device_No,
                PassRecord_DeviceName = passres.device?.Device_Name,
                PassRecord_Img = passres.localimage,
                PassRecord_ImgPath = imgSrc,
                PassRecord_PassTime = order?.ParkOrder_OutTime ?? DateTimeHelper.GetNowTime(),
                PassRecord_Name = Admin_Name,
                PassRecord_Remark = remark,
                PassRecord_Account = Admin_Account,
                PassRecord_Money = 0,
                PassRecord_AddTime = DateTimeHelper.GetNowTime(),
            };
            if (gate != 3)
            {
                var totalAmount = (payres?.orderamount ?? 0) + unpaidMoeny;
                var totalPayed = (payres?.payedamount ?? 0);

                order.ParkOrder_PayScene = (int)Model.EnumPayScene.Exit;
                order.ParkOrder_OutImgPath = imgSrc;
                order.ParkOrder_OutImg = passres.localimage;
                order.ParkOrder_Remark = remark;
                order.ParkOrder_OutType = 2;
                order.ParkOrder_EnterPasswayName = "*无入场记录";
                order.ParkOrder_StatusNo = 201;
                order.ParkOrder_EnterAdminName = "*无入场记录";
                order.ParkOrder_OutAdminAccount = Admin_Account;
                order.ParkOrder_OutAdminName = Admin_Name;
                order.ParkOrder_EnterTime = pass?.resorder?.resOut?.noRecordOrder?.ParkOrder_EnterTime ?? order.ParkOrder_EnterTime;
                order.ParkOrder_OutTime = anOrder.PassRecord_PassTime;
                order.ParkOrder_OutPasswayName = anOrder.PassRecord_PasswayName;
                order.ParkOrder_OutPasswayNo = anOrder.PassRecord_PasswayNo;
                order.ParkOrder_TotalAmount = totalAmount;
                order.ParkOrder_TotalPayed = totalPayed;
                order.ParkOrder_TotalPayed += (cashrobotamount - zlamount);
            }


            #endregion

            List<Model.Ledger> leadgerList = null;
            #region 创建支付订单
            Model.Owner owner = null;
            Model.PayColl payColl = new Model.PayColl();
            if (payres != null) payres.chuzhiamount = payres?.chuzhiamount ?? 0;
            if (!isPayed)
            {
                if (order.ParkOrder_IsFree == 1)
                {
                    order.ParkOrder_TotalAmount = 0;
                    order.ParkOrder_TotalPayed = 0;

                    if (payres != null)
                    {
                        payres.chuzhiamount = 0;
                        payres.payedamount = 0;
                        payres.orderamount = 0;
                    }
                    unpaidMoeny = 0;
                    zlamount = 0;
                    cashrobotamount = 0;
                }

                Model.PayOrder payOrder = order != null ? BLL.PayOrder.CreatePayOrder(order, passres.carcardtype, Admin_Name) : null;
                if (payOrder != null)
                {
                    payOrder.PayOrder_Money = (pass.payres?.orderamount ?? 0) + unpaidMoeny;
                    payOrder.PayOrder_PayedMoney = (pass.payres?.payedamount ?? 0) + unpaidMoeny;
                    payOrder.PayOrder_DiscountMoney = payOrder.PayOrder_Money - payOrder.PayOrder_PayedMoney - (cashrobotamount - zlamount) - (payres?.chuzhiamount ?? 0);
                    payOrder.PayOrder_SelfMoney = cashrobotamount;
                    payOrder.PayOrder_OutReduceMoney = zlamount;
                    payOrder.PayOrder_StoredMoney = payres?.chuzhiamount ?? 0;
                    payOrder.PayOrder_Account = Admin_Account;

                    if (!string.IsNullOrEmpty(sPayType)) payOrder.PayOrder_PayTypeCode = sPayType;
                    if (isUseCouponSet && couponList != null && couponList.Count > 0)
                        payOrder.PayOrder_CouponRecordNo = string.Join(",", couponList.Select(x => x.CouponRecord_No));
                    payColl.payOrderList = new List<Model.PayOrder>() { payOrder };

                    var payorder2 = payOrder.Copy();

                    List<Model.PayPart> payPartList = BLL.CommonBLL.CreatePayPartList(anOrder.PassRecord_PassTime, payorder2, 3);
                    payPartList.ForEach(x => { x.PayPart_Desc = "*无入场记录"; });
                    payColl.payPartList = payPartList;

                    #region 更新储值车余额

                    if (updateStoreCar && payOrder.PayOrder_StoredMoney > 0)
                    {

                        Model.CarExt car = BLL.Car.GetCarExtEntityByCarNo(order.ParkOrder_CarNo);
                        if (car != null && car.Car_Category == Model.EnumCarType.Prepaid.ToString())
                        {
                            owner = pass.passres?.owner;
                            Model.Ledger ledger = null;
                            Model.CarCardType cct = BLL.CarCardType.GetEntity(car.Car_TypeNo);

                            var remainingMoney = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - payOrder.PayOrder_StoredMoney;
                            //if (remainingMoney > 0)
                            //{
                            ledger = new Model.Ledger()
                            {
                                Ledger_CarNo = car.Car_CarNo,
                                Ledger_Space = owner.Owner_Space,
                                Ledger_Type = 2,
                                Ledger_CardType = Common.CarTypeHelper.GetCarTypeIndex(car.Car_Category),
                                Ledger_Code = 2,
                                Ledger_Money = payOrder.PayOrder_StoredMoney,
                                Ledger_BeforeMoeny = owner.Owner_Balance,
                                Ledger_Time = DateTime.Now,
                                Ledger_ParkOrderNo = order.ParkOrder_No,
                            };
                            //}

                            car.Owner_Balance = Utils.ObjectToDecimal(car.Owner_Balance, 0) - payOrder.PayOrder_StoredMoney;
                            if (car.Owner_Balance < 0) car.Owner_Balance = 0;
                            owner = owner == null ? new Model.Owner() : owner;
                            owner.Owner_No = car.Owner_No;
                            owner.Owner_Balance = car.Owner_Balance;
                            if (ledger != null)
                            {
                                ledger.Ledger_AfterMoeny = owner.Owner_Balance;
                                leadgerList = new List<Model.Ledger> { ledger };
                            }
                        }
                    }
                    #endregion
                }
            }
            else
            {
                if (order != null)
                {
                    var oldParkorder = BLL.ParkOrder.GetEntity(order.ParkOrder_No);//这里是线上支付，由中间件去更新
                    order.ParkOrder_TotalPayed = oldParkorder?.ParkOrder_TotalPayed ?? order.ParkOrder_TotalPayed;
                    order.ParkOrder_TotalAmount = oldParkorder?.ParkOrder_TotalAmount ?? order.ParkOrder_TotalAmount;

                    if (payres?.chuzhiamount > 0 && !string.IsNullOrEmpty(oldParkorder?.ParkOrder_OwnerNo))
                    {
                        owner = BLL.Owner.GetEntity("*", $"Owner_No='{oldParkorder.ParkOrder_OwnerNo}'");
                    }

                    if (payColls != null && AppSettingConfig.SentryMode != VersionEnum.CloudServer)
                    {
                        payColls?.payOrderList?.ForEach(x => { x.PayOrder_Status = 1; payColl.payOrderList = payColl.payOrderList ?? new List<Model.PayOrder>(); payColl.payOrderList.Add(x); });
                        payColls?.payPartList?.ForEach(x => { x.PayPart_Status = 1; payColl.payPartList = payColl.payPartList ?? new List<Model.PayPart>(); payColl.payPartList.Add(x); });

                        order.ParkOrder_TotalAmount = payColl.payOrderList.Sum(x => x.PayOrder_Money);
                        order.ParkOrder_TotalPayed = payColl.payOrderList.Sum(x => x.PayOrder_PayedMoney);
                    }

                }
            }

            #endregion

            #region【每月最高限额】
            List<Model.CarFees> carfees = null;
            List<Model.CarFeesOrder> carFeesOrders = null;
            var result = CommonBLL.CreateCarFees(new List<Model.ParkOrder>() { order }, payColl);
            carfees = result.Item1;
            carFeesOrders = result.Item2;
            #endregion

            if (order != null)
            {
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                {
                    if (pass?.isSupplement ?? false) order.ParkOrder_UserNo = "4"; else order.ParkOrder_UserNo = "";

                    if (payColls != null)
                    {
                        if (payColls.payOrderList != null) if (payColl.payOrderList == null) payColl.payOrderList = payColls.payOrderList; else payColl.payOrderList.AddRange(payColls.payOrderList);
                        if (payColls.payPartList != null) if (payColl.payPartList == null) payColl.payPartList = payColls.payPartList; else payColl.payPartList.AddRange(payColls.payPartList);
                    }
                }
            }

            Model.ResBodyDataNoRecord pushdata = new Model.ResBodyDataNoRecord(anOrder, order, payColl, outState, null, preOrder, od, owner, couponList);

            #region 是否存在追缴订单
            if (pass.unpaidresult != null && pass.unpaidresult.Count > 0)
            {
                Model.Parking parking = BLL.Parking.GetEntity(area.ParkArea_ParkNo);
                pushdata.Item6 = new List<Model.Unpaid>();
                pushdata.Item8 = new List<Model.ParkOrder>();
                pushdata.Item9 = new List<Model.OrderDetail>();
                foreach (var unp in pass.unpaidresult)
                {
                    if (string.IsNullOrEmpty(unp.payres?.carBillNo))
                    {

                        var orderModel = unp.unpaidorder;
                        if (orderModel != null)
                        {
                            orderModel.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
                            orderModel.ParkOrder_Remark += $"由无入场记录[{order?.ParkOrder_No}]出场追缴";
                        }

                        unp.unpaiddetails?.ForEach(detail =>
                        {
                            if (detail.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Follow)
                            {
                                detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                                detail.OrderDetail_TotalAmount = unp.payres.payedamount;
                            }

                        });

                        if (updateStoreCar)
                        {
                            if (owner == null) owner = pass.passres?.owner;
                            if (owner != null && owner.Owner_CardType == 2)
                            {
                                owner.Owner_Balance = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - (unp.payres?.chuzhiamount ?? 0);
                            }
                        }

                        if (unp.unpaidorder != null) pushdata.Item8.Add(orderModel);
                        if (unp.unpaiddetails != null)
                            pushdata.Item9.AddRange(unp.unpaiddetails);

                        pushdata.Item6.Add(new Model.Unpaid
                        {
                            account = Admin_Account,
                            name = Admin_Name,
                            orderNo = orderModel.ParkOrder_No,
                            time = orderModel.ParkOrder_OutTime ?? DateTimeHelper.GetNowTime()
                        });
                    }
                    else
                    {

                        if (handWin)
                        {
                            var billRet = PayOverBills(unp, order);

                            if (pushdata.Item16 == null) pushdata.Item16 = billRet.Item1;
                            else pushdata.Item16.AddRange(billRet.Item1);

                            if (billRet.Item2.Count > 0)
                            {
                                if (pushdata.Item3 == null)
                                {
                                    pushdata.Item3 = new PayColl() { payOrderList = billRet.Item2 };
                                }
                                else
                                {
                                    if (pushdata.Item3.payOrderList == null)
                                    {
                                        pushdata.Item3.payOrderList = billRet.Item2;
                                    }
                                    else
                                    {
                                        pushdata.Item3.payOrderList.AddRange(billRet.Item2);
                                    }
                                }

                                if (billRet.Item3.Count > 0)
                                {
                                    if (pushdata.Item3.payPartList == null)
                                    {
                                        pushdata.Item3.payPartList = billRet.Item3;
                                    }
                                    else
                                    {
                                        pushdata.Item3.payPartList.AddRange(billRet.Item3);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            PassHelperBiz.settlePaymethod(order?.ParkOrder_No, out var paymethods);
            pushdata.Item7 = paymethods;
            pushdata.ledgerList = leadgerList;
            pushdata.Item14 = carfees;
            pushdata.Item15 = carFeesOrders;

            var res = BLL.PassRecord.CarOutNoRecord(pushdata);
            if (res > 0)
            {
                if (pass.recog != null && pass.recog.CarRecog_OpenStatus != null)
                {
                    BLL.CarRecog.GetInstance(pass.recog.CarRecog_Time ?? DateTime.Now).AddOrUpdate(pass.recog);
                }

                //if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && CarObj.isSupplement) curOrder.ParkOrder_UserNo = "2";
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && pass.isSupplement)
                {
                    _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                    {
                        payColl?.payOrderList?.ForEach(x =>
                        {
                            BLL.PushEvent.CloudPayOrder(null, x);
                        });
                        BLL.PushEvent.CloudOutCar(null, order, Admin_Account, passres?.device?.Device_No);
                        return Task.CompletedTask;
                    });
                }


                var configuration = AppBasicCache.PrintSetting;
                if (configuration != null)
                {
                    if (configuration.mode == 1)
                    {
                        _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                        {
                            try
                            {
                                Receipt receipt = new Receipt();
                                receipt.Title = configuration.title;
                                receipt.SubTitle = configuration.subtitle;
                                receipt.CarNo = pass.passres.carno;
                                receipt.CarCardType = "";
                                receipt.InTime = (pass?.resorder?.resOut?.noRecordOrder?.ParkOrder_EnterTime ?? order.ParkOrder_EnterTime).Value.ToString("yyyy年MM月dd日 HH:mm:ss");
                                receipt.OutTime = (pass?.resorder?.resOut?.noRecordOrder?.ParkOrder_OutTime ?? order.ParkOrder_OutTime).Value.ToString("yyyy年MM月dd日 HH:mm:ss");
                                receipt.TotalAmount = (pass?.resorder?.resOut?.noRecordOrder?.ParkOrder_TotalAmount ?? order.ParkOrder_TotalAmount).Value.ToString("0.00") + "元";
                                receipt.PayedAmount = (pass?.resorder?.resOut?.noRecordOrder?.ParkOrder_TotalPayed ?? order.ParkOrder_TotalPayed).Value.ToString("0.00") + "元";
                                receipt.Operator = Admin_Name;
                                receipt.End = configuration.endcontent;

                                BLL.ReceiptPrinter receiptPrinter = new BLL.ReceiptPrinter(receipt);
                                receiptPrinter.PrintReceipt();
                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{pass?.passres?.carno}自动打印收费小票!");
                            }
                            catch (Exception ex)
                            {
                                BLL.UserLogs.AddLog(new AdminSession() { Admins_Account = Admin_Account, Admins_Name = Admin_Name }, LogEnum.Sentry, SecondOption.PrintReceiptSetting, $"{pass?.passres?.carno}自动打印小票异常：{ex.Message}", SecondIndex.Monitoring);
                            }
                            return Task.CompletedTask;
                        });
                    }
                }


                return new ResBody() { success = true, errmsg = "更新订单成功", data = TyziTools.Json.ToString(pushdata, true) };
            }
            else
            {
                return new ResBody() { success = false, errmsg = "出场失败" };
            }
        }

        /// <summary>
        /// 无入场记录创建提前5分钟的订单
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        public static Model.ParkOrder AddNoRecordParkOrder(string parkno, ref Model.ResultPass result, string adminAccount = null, string adminName = null, bool isUpdateCoudle = false)
        {
            Model.ParkOrder order = new Model.ParkOrder();
            Model.Parking parking = BLL.Parking.GetEntity(parkno);
            #region 创建提前五分钟的订单记录
            Model.ResultPassData passres = result.passres;
            ChargeModels.PayResult payres = result.payres;

            Model.PolicyPark policyPark = BLL.PolicyPark.GetEntity(parkno);

            var currentTime = Utils.StrToDateTime(DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"));
            var enterTime = currentTime.AddMinutes(-(policyPark?.PolicyPark_NoRecordRangTime ?? 5));
            decimal totalmoney = 0;
            if (result.resorder?.resOut != null)
            {
                totalmoney = result.resorder.resOut.minmoney.HasValue ? (decimal)result.resorder.resOut.minmoney.Value : (decimal)0;
                if (result.resorder?.resOut?.onmachorder == 1)
                {
                    enterTime = result.resorder?.resOut?.noRecordOrder.ParkOrder_EnterTime.Value ?? enterTime;
                    totalmoney = result.payres?.orderamount ?? 0;
                }
            }

            List<Model.PasswayLink> links = BLL.PasswayLink.GetAllEntity(passres.passway.Passway_ParkNo) ?? new List<Model.PasswayLink>();
            Model.PasswayLink link = links.Find(x => x.PasswayLink_PasswayNo == passres.passway.Passway_No && x.PasswayLink_GateType == 0);
            Model.ParkArea area = BLL.ParkArea.GetEntity(link.PasswayLink_ParkAreaNo);

            string imgSrc = string.Empty;
            if (!string.IsNullOrEmpty(passres.img)) { imgSrc = PassTool.PassHelperBiz.GetImageHttpUrl(passres.passway.Passway_ParkNo, passres.img, passres.device?.Device_SentryHostNo); }
            if (!string.IsNullOrEmpty(passres.localimage)) { passres.localimage = System.Web.HttpUtility.UrlEncode(passres.localimage); }

            //#region 创建提前五分钟的订单记录
            order = BLL.ParkOrder.CreateParkOrder(area.ParkArea_ParkNo, area.ParkArea_No, area.ParkArea_Name, passres.carno, passres.carcardtype?.CarCardType_No, passres.carcardtype?.CarCardType_Name, passres.cartype?.CarType_No, passres.cartype?.CarType_Name, enterTime, passres.passway.Passway_No, passres.passway.Passway_Name, 0, 1, passres?.owner?.Owner_No, passres?.owner?.Owner_Name, isNoEnter: true);
            if (order != null)
            {
                order.ParkOrder_EnterPasswayName = "*无入场记录";
                order.ParkOrder_TotalAmount = totalmoney;
                order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                order.ParkOrder_OutPasswayNo = passres.passway.Passway_No;
                order.ParkOrder_OutPasswayName = passres.passway.Passway_Name;
                order.ParkOrder_OutType = 1;
                order.ParkOrder_OutTime = currentTime;
                order.ParkOrder_OutImg = passres.localimage;
                order.ParkOrder_OutImgPath = imgSrc;
                order.ParkOrder_OutAdminAccount = adminAccount;
                order.ParkOrder_OutAdminName = adminName;
                order.ParkOrder_FreeReason = "";
                order.ParkOrder_IsSettle = 0;

                //Model.InCar incar = new InCar();
                //incar.InCar_CarNo = order.ParkOrder_CarNo;
                //incar.InCar_ParkOrderNo = order.ParkOrder_No;
                //incar.InCar_ParkAreaNo = order.ParkOrder_ParkAreaNo;
                //incar.InCar_CarCardTypeNo = order.ParkOrder_CarCardType;
                //incar.InCar_EnterTime = order.ParkOrder_EnterTime;
                //incar.InCar_Status = order.ParkOrder_StatusNo;
                var r = BLL.ParkOrder.CarInComplete(new List<Model.ParkOrder> { order }, null, null, null);
                if (r < 0) { LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{order.ParkOrder_CarNo}]无入场记录创建提前5分钟的订单执行SQL错误：{order.ParkOrder_No},{order.ParkOrder_ParkAreaNo},{order.ParkOrder_CarCardType},{order.ParkOrder_EnterTime}", LogLevel.Error); }

                result.resorder.resOut.parkorder = order;
                if (isUpdateCoudle)
                {
                    BLL.PushEvent.EnterCar(parking.Parking_Key, order, MiddlewareEventPriority.Delay_500_1);
                }
            }

            #endregion
            return order;
        }

        /// <summary>
        /// 特殊车辆放行
        /// </summary>
        /// <returns></returns>
        public static ResBody SpecialCarPass(Model.SpecialCarPass model)
        {
            Model.SpecialCar specialCar = BLL.SpecialCar.GetEntity(model.specialcarno);
            if (specialCar == null) { return new ResBody() { success = false, errmsg = "特殊车辆类型不存在", parkno = model.parkno }; }

            //查询车道名称
            Model.Device device = BLL.Device.GetEntity(model.camerano);
            if (device == null) { return new ResBody() { success = false, errmsg = "相机信息不存在", parkno = model.parkno }; }

            Model.Passway passway = BLL.Passway.GetEntity(device.Device_PasswayNo);
            if (device == null) { return new ResBody() { success = false, errmsg = "相机关联车道不存在", parkno = model.parkno }; }

            string imgSrc = string.Empty;
            if (!string.IsNullOrEmpty(model.img))
            {
                imgSrc = PassHelperBiz.GetImageHttpUrl(model.parkno, model.img, device.Device_SentryHostNo);
                model.img = System.Web.HttpUtility.UrlEncode(model.img);
            }

            string freeReason = $"特殊车辆放行";
            Model.PassRecord record = new Model.PassRecord()
            {
                PassRecord_No = Utils.CreateNumberWith("ER"),
                PassRecord_CarNo = model.carno,
                PassRecord_ParkNo = model.parkno,
                PassRecord_Type = 2,
                PassRecord_PassTime = model.time,
                PassRecord_PasswayNo = passway.Passway_No,
                PassRecord_PasswayName = passway.Passway_Name,
                PassRecord_ImgPath = imgSrc,
                PassRecord_Img = model.img,
                PassRecord_SpecialNo = specialCar.SpecialCar_No,
                PassRecord_SpecialName = specialCar.SpecialCar_Name,
                PassRecord_DeviceNo = device.Device_DriveNo,
                PassRecord_DeviceName = device.Device_Name,
                PassRecord_Account = model.account,
                PassRecord_AddTime = DateTimeHelper.GetNowTime(),
                PassRecord_Name = model.name,
                PassRecord_Remark = freeReason
            };

            var res = BLL.BaseBLL._Insert(record);
            if (res > 0)
            {
                return new ResBody() { success = true, errmsg = "", parkno = model.parkno, data = Common.TyziTools.Json.ToString(record, true) };
            }
            else
            {
                return new ResBody() { success = false, errmsg = "特殊车辆放行记录写入失败", parkno = model.parkno };
            }
        }

        /// <summary>
        /// 人工开闸放行
        /// </summary>
        /// <returns></returns>
        public static ResBody OpenGatePass(Model.SpecialCarPass model)
        {
            //查询车道名称
            Model.Device device = BLL.Device.GetEntity(model.camerano);
            if (device == null) { return new ResBody() { success = false, errmsg = "相机信息不存在", parkno = model.parkno }; }

            Model.Passway passway = BLL.Passway.GetEntity(device.Device_PasswayNo);
            if (passway == null) { return new ResBody() { success = false, errmsg = "相机关联车道不存在", parkno = model.parkno }; }

            Model.PolicyPassway policy = BLL.PolicyPassway.GetEntityByPasswayNo(passway.Passway_No);
            if (policy == null) { return new ResBody() { success = false, errmsg = "车道未设置", parkno = model.parkno }; }

            Model.CarCardType carCard = null;

            if (carCard == null && !string.IsNullOrEmpty(policy.PolicyPassway_DefaultCarCardType))
                carCard = BLL.CarCardType.GetEntity(policy.PolicyPassway_DefaultCarCardType);

            carCard = carCard ?? new Model.CarCardType();

            string imgSrc = string.Empty;
            if (!string.IsNullOrEmpty(model.img))
            {
                if (!imgSrc.StartsWith("http://") && !imgSrc.StartsWith("https://")) imgSrc = PassHelperBiz.GetImageHttpUrl(model.parkno, model.img, device.Device_SentryHostNo);
                model.img = System.Web.HttpUtility.UrlEncode(model.img);
            }

            string freeReason = $"人工开闸放行";
            if (!string.IsNullOrEmpty(model.sRemark))
            {
                freeReason = model.sRemark;
            }

            Model.PassRecord record = new Model.PassRecord()
            {
                PassRecord_No = Utils.CreateNumberWith("ER"),
                PassRecord_CarNo = model.carno,
                PassRecord_ParkNo = model.parkno,
                PassRecord_Type = model.type,
                PassRecord_PassTime = model.time,
                PassRecord_PasswayNo = passway.Passway_No,
                PassRecord_PasswayName = passway.Passway_Name,
                PassRecord_ImgPath = imgSrc,
                PassRecord_Img = model.img,
                PassRecord_SpecialNo = "",
                PassRecord_SpecialName = "",
                PassRecord_DeviceNo = device.Device_DriveNo,
                PassRecord_DeviceName = device.Device_Name,
                PassRecord_Account = model.account,
                PassRecord_AddTime = DateTimeHelper.GetNowTime(),
                PassRecord_Name = model.name,
                PassRecord_Remark = freeReason,
                PassRecord_CarCardType = carCard.CarCardType_No,
                PassRecord_CarCardTypeName = carCard.CarCardType_Name,
                PassRecord_Money = 0
            };

            var res = BLL.BaseBLL._Insert(record);
            if (res > 0)
            {
                return new ResBody() { success = true, errmsg = imgSrc, parkno = model.parkno, data = Common.TyziTools.Json.ToString(record, true) };
            }
            else
            {
                return new ResBody() { success = false, errmsg = "特殊车辆放行记录写入失败", parkno = model.parkno, data = imgSrc };
            }
        }

        /// <summary>
        /// 当前班次数据分析
        /// </summary>
        /// <returns></returns>
        public static ResBody GetShiftData(Model.ShiftCondition model)
        {
            if (model.start == null) { return new ResBody(false, "上班时间为空", model.parkno); }
            if (model.end == null) { return new ResBody(false, "下班时间为空", model.parkno); }
            if (model.time == null) { return new ResBody(false, "交班时间为空", model.parkno); }
            if (string.IsNullOrEmpty(model.parkno)) { return new ResBody(false, "车场编码为空", model.parkno); }

            //去除时间格式中的毫秒数
            model.start = Utils.StrToDateTime(model.start.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            model.end = Utils.StrToDateTime(model.end.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            model.time = Utils.StrToDateTime(model.time.Value.ToString("yyyy-MM-dd HH:mm:ss"));

            Model.WorkShift data = BLL.WorkShift.CreateWorkShift(ref model);

            data.WorkShift_No = Utils.CreateNumberWith("AB");
            data.WorkShift_ParkNo = model.parkno;
            data.WorkShift_OffAccount = model.account;
            data.WorkShift_OffName = model.name;
            data.WorkShift_OnAccount = model.nextaccount;
            data.WorkShift_OnName = model.nextname;
            data.WorkShift_OnTime = model.start;
            data.WorkShift_OffTime = model.end;
            data.WorkShift_AddTime = model.time;

            return new ResBody(true, "Ok", model.parkno, data);
        }

        /// <summary>
        /// 模糊匹配场内车辆
        /// </summary>
        /// <param name="CarObj">必须包含parkno,carno参数</param>
        /// <returns>返回场内订单明细</returns>
        public static List<Model.OrderDetail> GetCarNoFuzzyMatch(Model.ParkCarInOut CarObj)
        {
            if (string.IsNullOrEmpty(CarObj.parkno) || string.IsNullOrEmpty(CarObj.carno)) return null;

            List<Model.OrderDetail> orders = PassHelperBiz.GetListByMatchCarNo(CarObj.parkno, CarObj.carno);

            return orders;
        }

        /// <summary>
        /// 模糊匹配场内车辆(允许车牌号错误一位的模糊查询)
        /// </summary>
        /// <param name="CarObj">必须包含parkno,carno参数</param>
        /// <returns>返回场内订单明细</returns>
        public static List<Model.OrderDetail> GetListByMatchOneNotSame(Model.ParkCarInOut CarObj)
        {
            if (string.IsNullOrEmpty(CarObj.parkno) || string.IsNullOrEmpty(CarObj.carno)) return null;

            List<Model.OrderDetail> orders = PassHelperBiz.GetListByMatchOneNotSame(CarObj.parkno, CarObj.carno);

            return orders;
        }

        /// <summary>
        /// 模糊匹配场内车辆 条件查询
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="spassName"></param>
        /// <returns></returns>
        public static List<Model.OrderDetail> GetCarNoFuzzyMatch(string parkno, string startTime, string endTime, string spassName)
        {
            if (string.IsNullOrEmpty(parkno)) return null;


            List<Model.OrderDetail> orders = PassHelperBiz.GetListByMatchCarNo(parkno, startTime, endTime, spassName);

            return orders;
        }

        /// <summary>
        /// 车道策略模糊匹配车牌号码
        /// </summary>
        /// <param name="CarObj">必须包含parkno,carno,camerano参数</param>
        /// <returns>返回符合策略的车辆订单</returns>
        public static List<Model.ParkOrder> MatchCarNo(Model.ParkCarInOut CarObj)
        {
            if (string.IsNullOrEmpty(CarObj.parkno) || string.IsNullOrEmpty(CarObj.carno)) return null;

            Model.Device camera = BLL.Device.GetEntity(CarObj.camerano);
            if (camera == null) return null;

            Model.Passway passway = BLL.Passway.GetEntity(camera.Device_PasswayNo);
            if (passway == null) return null;

            var order = PassHelperBiz.MatchParkOrder(passway.Passway_No, CarObj.carno);
            if (order != null)
                return order;

            return null;
        }

        /// <summary>
        /// 保存预缴金额纪录
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static ResBody SavePay(Model.PaymethodParam data)
        {
            if (string.IsNullOrEmpty(data.parkNo)) return new ResBody(false, "车场编号为空", null);
            if (string.IsNullOrEmpty(data.orderNo)) return new ResBody(false, "停车订单编号为空", null);
            if (string.IsNullOrEmpty(data.carNo)) return new ResBody(false, "车牌号为空", null);
            if (data.mode != 1 && data.mode != 2) return new ResBody(false, "缴费方式错误", null);
            if (data.payMoney == null || data.payMoney <= 0) return new ResBody(false, "缴费金额错误", null);
            if (data.ysMoney == null || data.ysMoney <= 0) return new ResBody(false, "应收金额错误", null);
            if (data.time == null) return new ResBody(false, "缴费时间错误", null);

            //查询当前订单已预缴纪录
            var pays = GetPaymethodByOrderNo(data.orderNo);
            decimal? payedMony = pays?.Sum(x => x.Paymethod_Money) ?? 0;
            if (payedMony >= data.ysMoney) return new ResBody(false, "缴费金额已达到应收金额", null);

            //计算本次应收
            decimal? ysMoney = data.ysMoney - payedMony;//订单应收 - 已预缴金额
            if (ysMoney > data.payMoney) ysMoney = data.payMoney;//若本次应收大于本次缴费金额,则记录应收为本次缴费金额

            //计算找零
            decimal? zlMoney = 0;
            if (data.payMoney > ysMoney) zlMoney = data.payMoney - ysMoney;

            Model.Paymethod model = new Paymethod
            {
                Paymethod_No = Utils.CreateNumber,
                Paymethod_ParkNo = data.parkNo,
                Paymethod_OrderNo = data.orderNo,
                Paymethod_CarNo = data.carNo,
                Paymethod_Mode = data.mode,
                Paymethod_Money = data.payMoney,
                Paymethod_YsMoney = ysMoney,
                Paymethod_ZlMoney = zlMoney,
                Paymethod_DeviceNo = data.deviceNo,
                Paymethod_DeviceName = data.deviceName,
                Paymethod_Time = data.time,
                Paymethod_Status = 0,
                Paymethod_IsSend = 0,
                Paymethod_Remark = data.remark,
                Paymethod_Account = data.account
            };

            var res = BLL.BaseBLL._Add(model);
            if (res > 0)
            {
                return new ResBody(true, "保存成功", null, model);
            }
            else
            {
                return new ResBody(false, "保存失败", null);
            }
        }

        /// <summary>
        /// 获取停车订单已预缴金额
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        public static List<Model.Paymethod> GetPaymethodByOrderNo(string orderNo)
        {
            List<Model.Paymethod> pays = BLL.BaseBLL._GetAllEntity(new Model.Paymethod(), "*", $"Paymethod_OrderNo='{orderNo}' AND Paymethod_Status=0");
            return pays;
        }

        /// <summary>
        /// 账单更新
        /// </summary>
        /// <param name="unp">追缴实体</param>
        /// <param name="order">停车订单</param>
        /// <param name="payTypeCode">支付方式</param>
        /// <param name="Car_OwnerSpace">车位号</param>
        /// <param name="PayOrder_PayScene">支付场所</param>
        /// <param name="PayOrder_PassWayNo">车道号</param>
        /// <returns></returns>
        public static (List<Model.OverdueBill>, List<Model.PayOrder>, List<Model.PayPart>) PayOverBills(Model.UnPaidResult unp, Model.ParkOrder order)
        {
            List<Model.PayOrder> poList = null;
            List<Model.PayPart> ppList = null;

            var obills = BLL.BaseBLL._GetAllEntity(new Model.OverdueBill(), "*", $"OverdueBill_No in ('{unp.payres.carBillNo.Replace(",", "','")}')");
            if (obills?.Count > 0)
            {
                poList = new List<Model.PayOrder>();
                ppList = new List<Model.PayPart>();

                obills.ForEach(x =>
                {
                    var cct = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, order?.ParkOrder_CarCardType);

                    Model.PayOrder payModel = new Model.PayOrder();
                    payModel.PayOrder_No = "PO" + x.OverdueBill_No;
                    payModel.PayOrder_ParkOrderNo = order.ParkOrder_No;
                    payModel.PayOrder_Money = unp.payres.payedamount;
                    payModel.PayOrder_Time = DateTimeHelper.GetNowTime();
                    payModel.PayOrder_CarNo = order.ParkOrder_CarNo;
                    payModel.PayOrder_Status = 1;
                    payModel.PayOrder_PayedMoney = unp.payres.payedamount;
                    payModel.PayOrder_PayedTime = DateTime.Now;
                    payModel.PayOrder_PayTypeCode = "79001";
                    payModel.PayOrder_PayType = 2;
                    payModel.PayOrder_AdminID = 0;
                    payModel.PayOrder_UserNo = "";
                    payModel.PayOrder_ParkKey = AppBasicCache.GetParking.Parking_Key;
                    payModel.PayOrder_ParkNo = AppBasicCache.GetParking.Parking_No;
                    payModel.PayOrder_OrderTypeNo = cct == null ? Convert.ToString((int)Common.EnumOrderType.Temp) : CarTypeHelper.GetOrderType(cct.CarCardType_Category, true);
                    payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                    payModel.PayOrder_TempTimeCount = 0;
                    payModel.PayOrder_TimeCountDesc = order?.ParkOrder_IsNoInRecord != 1 ? Utils.DateDiffStr(payModel.PayOrder_TempTimeCount.Value * 60) : "*无入场记录";
                    payModel.PayOrder_CarCardTypeNo = order?.ParkOrder_CarCardType;
                    payModel.PayOrder_CarTypeNo = order?.ParkOrder_CarType;
                    payModel.PayOrder_ParkAreaNo = order?.ParkOrder_ParkAreaNo;
                    payModel.PayOrder_PayScene = 0; //支付场所
                    payModel.PayOrder_PassWayNo = order.ParkOrder_OutPasswayNo;
                    payModel.PayOrder_Desc = "第三方账单追缴";
                    payModel = BLL.PayOrder.CreatePayOrder(true, payModel, AppBasicCache.GetParking.Parking_Key, appendUnpaidOrder: true);
                    var payPart = BLL.CommonBLL.CreatePayPartListByRatio(null, unp.payres.payedamount, null, payModel, order, appendUnpaidOrder: true);

                    poList.Add(payModel);
                    ppList.AddRange(payPart);

                    x.OverdueBill_Status = 1;
                    x.OverdueBill_ParkOrderNo = order?.ParkOrder_No;
                    x.OverdueBill_PayOrderNo = payModel.PayOrder_No;
                    x.OverdueBill_Remark = x.OverdueBill_Remark ?? "";
                    x.OverdueBill_Remark += " 由线下完成追缴";
                });

                //if (pushdata.Item15 == null) pushdata.Item15 = obills;
                //else pushdata.Item15.AddRange(obills);

                //if (poList.Count > 0) if (pushdata.Item3 == null) { pushdata.Item3 = poList; } else { pushdata.Item3.AddRange(poList); }
                //if (ppList.Count > 0) if (pushdata.Item7 == null) { pushdata.Item7 = ppList; } else { pushdata.Item7.AddRange(ppList); }

            }

            return (obills, poList, ppList);
        }
    }
}
