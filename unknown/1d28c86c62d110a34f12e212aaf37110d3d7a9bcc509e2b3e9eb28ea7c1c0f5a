﻿using carparking.Charge;
using carparking.BLL;
using carparking.Common;
using carparking.Config;
using carparking.Model;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using carparking.BLL.Cache;
using NPOI.Util;
using carparking.Model.API;
using carparking.Charge.Models;
using carparking.DAL;

namespace carparking.PassTool
{
    public class PassHelperBiz
    {
        #region 入出权限

        /// <summary>
        /// 查询车牌是否在黑名单内
        /// </summary>
        /// <param name="carno">车牌号</param>
        /// <param name="model">黑名单详细</param>
        /// <returns>true:是黑名单，false：不是或不在黑名单时间内</returns>
        public static bool IsBlack(string carno, DateTime time, int? macth, out Model.BlackList model)
        {
            if (!AppBasicCache.ReadWriteCache || AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache != 1)
            {
                model = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"BlackList_CarNo='{carno}' AND (Blacklist_Status=1 OR Blacklist_Status IS NULL)");
                //if (model == null)
                //{
                //    switch (macth)
                //    {
                //        case null:
                //        case 1:
                //            break;
                //        case 2:
                //            model = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"BlackList_CarNo like '_{carno.Substring(1)}' AND (Blacklist_Status=1 OR Blacklist_Status IS NULL)");
                //            break;
                //        case 3:
                //            var rexs = carno.ToCharArray();
                //            List<string> str = new List<string>();
                //            for (int i = 0; i < rexs.Length; i++)
                //            {
                //                str.Add($" BlackList_CarNo like '{carno.Substring(0, i)}_{carno.Substring(i + 1)}' ");
                //            }
                //            model = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"({string.Join(" OR ", str)}) AND (Blacklist_Status=1 OR Blacklist_Status IS NULL)");
                //            break;
                //        default:
                //            break;
                //    }
                //}
            }
            else
            {
                model = AppBasicCache.GetBlackList.Values.Where(m => m.BlackList_CarNo == carno && (m.Blacklist_Status == 1 || m.Blacklist_Status == null)).FirstOrDefault();
                //if (model == null)
                //{
                //    string sqlwhere = string.Empty;
                //    switch (macth)
                //    {
                //        case null:
                //        case 1:
                //            break;
                //        case 2:
                //            //仅允许首字符错误
                //            var newCarno = carno.Substring(1);
                //            model = BLL.AppBasicCache.GetBlackList.Values.Where(x => x.BlackList_CarNo.Substring(1) == newCarno && (x.Blacklist_Status == 1 || x.Blacklist_Status == null)).FirstOrDefault();
                //            break;
                //        case 3:
                //            //允许任意一位字符错误
                //            model = BLL.AppBasicCache.GetBlackList.Values.Where(x => Utils.CalculateLevenshteinDistance(carno, x.BlackList_CarNo) <= 1 && (x.Blacklist_Status == 1 || x.Blacklist_Status == null)).FirstOrDefault();
                //            break;
                //        default:
                //            break;
                //    }
                //}
            }

            if (model != null && model.BlackList_BeginTime <= time && model.BlackList_EndTime >= time)
                return true;
            else
            {
                model = null;
                return false;
            }
        }

        /// <summary>
        /// 获取出入权限
        /// </summary>
        /// <param name="Passway_No">通道编码</param>
        /// <param name="CarCardType_No">车牌类型编码</param>
        /// <param name="cartypeno">相机识别的车牌颜色</param>
        public static List<Model.AccessAuth> GetAccessAuth(string Passway_No, string CarCardType_No, string CarType_No, string CarCardType_Category, List<Model.CarType> ctList = null, List<Model.AccessAuth> accessData = null)
        {
            List<Model.AccessAuth> accessList = new List<Model.AccessAuth>();
            if (string.IsNullOrEmpty(Passway_No)) return accessList;

            string selectWhere = string.Empty;
            if (!string.IsNullOrEmpty(CarCardType_No))
            {
                List<Model.AccessAuth> accessList1 = null;
                accessList1 = accessData?.FindAll(x => x.AccessAuth_CarCardTypeNo == CarCardType_No);
                if (accessList1 == null || accessList1.Count == 0)
                {
                    selectWhere = $"AccessAuth_CarCardTypeNo='{CarCardType_No}'";//AND AccessAuth_PasswayNo like '%{Passway_No}%'
                    accessList1 = BLL.AccessAuth.GetAllEntity("*", selectWhere);
                }

                accessList1?.ForEach(item =>
                {
                    var noList = TyziTools.Json.ToObject<List<string>>(item.AccessAuth_PasswayNo);
                    if (noList != null && noList.Contains(Passway_No)) accessList.Add(item);
                });
            }

            if (!string.IsNullOrEmpty(CarType_No))
            {
                if (string.IsNullOrEmpty(CarCardType_Category)
                    || Common.CarTypeHelper.GetCarTypeIndex(CarCardType_Category) == (int)CarTypeEnum.Temp
                    || Common.CarTypeHelper.GetCarTypeIndex(CarCardType_Category) == 0)
                {
                    //通过相机识别的车牌颜色获取数据对应的车牌颜色
                    Model.CarType carType = null;
                    carType = ctList?.Find(x => x.CarType_No == CarType_No);
                    if (carType == null) carType = BLL.CarType.GetEntity("*", $"CarType_No = '{CarType_No}'");

                    if (carType != null)
                    {
                        List<Model.AccessAuth> accessList2 = null;
                        accessList2 = accessData?.FindAll(x => x.AccessAuth_CarTypeNo == carType.CarType_No);
                        if (accessList2 == null || accessList2.Count == 0)
                        {
                            selectWhere = $"AccessAuth_CarTypeNo='{carType.CarType_No}'";//AND AccessAuth_PasswayNo like '%{Passway_No}%'
                            accessList2 = BLL.AccessAuth.GetAllEntity("*", selectWhere);
                        }

                        accessList2?.ForEach(item =>
                        {
                            var noList = TyziTools.Json.ToObject<List<string>>(item.AccessAuth_PasswayNo);
                            if (noList != null && noList.Contains(Passway_No)) accessList.Add(item);
                        });
                    }
                }
            }

            return accessList;
        }

        /// <summary>
        /// 判断时间授权
        /// </summary>
        public static Model.ParkCarInOutResult IsTimeAccess(List<Model.AccessAuth> accesseList, Model.ParkCarInOut CarObj)
        {
            Model.ParkCarInOutResult data = new Model.ParkCarInOutResult();
            if (accesseList == null || accesseList.Count == 0) return data;

            bool allowAcc = false;  //允许通行判断结果
            bool disableAcc = false;//禁止通行判断结果
            bool isacc0 = false;//是否计算了禁止通行
            bool isacc1 = false; //是否计算了允许通行
            bool iscan = false;//是否弹框确认放行

            foreach (var acc in accesseList)
            {
                //设置的是禁止通行
                if (acc.AccessAuth_IsAccess == 0)
                {
                    isacc0 = true;
                    if (disableAcc) break;//存在任意一条时间范围禁止通行,则跳出循环

                    if (acc.AccessAuth_DateType == 3)
                    {
                        DateTime timeSpan = CarObj.time.Value;//入场时间
                        List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(acc.AccessAuth_DayContent);
                        Model.RangeTime time = times?.Find(x => x.GetDateTime(x.start) <= timeSpan && x.GetDateTime(x.end) >= timeSpan);
                        disableAcc = time != null; //不在禁止通行时间内
                    }
                    else if (acc.AccessAuth_DateType == 1)
                    {
                        string currentTime = CarObj.time.Value.ToString("HH:mm");
                        TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                        List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(acc.AccessAuth_DayContent);
                        Model.RangeTime time = times?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                        disableAcc = time != null; //不在禁止通行时间内
                    }
                    else if (acc.AccessAuth_DateType == 2)
                    {
                        string currentTime = CarObj.time.Value.ToString("HH:mm");
                        TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                        int weekDay = (int)CarObj.time.Value.DayOfWeek;
                        List<Model.RangeWeek> times = TyziTools.Json.ToObject<List<Model.RangeWeek>>(acc.AccessAuth_WeekContent);
                        var rangeWeeks = times?.FindAll(x => x.week.Contains(weekDay.ToString()));
                        if (rangeWeeks != null && rangeWeeks.Count > 0)
                        {
                            foreach (var item in rangeWeeks)
                            {
                                if (disableAcc) break;
                                List<Model.RangeTime> timeRange = TyziTools.Json.ToObject<List<Model.RangeTime>>(item.times);
                                var time = timeRange?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                                disableAcc = time != null; //不在禁止通行时间内
                            }
                        }
                    }
                }
                //设置的是允许通行
                else if (acc.AccessAuth_IsAccess == 1)
                {
                    isacc1 = true;
                    if (allowAcc) break;//存在任意一条时间范围允许通行,则跳出循环

                    if (acc.AccessAuth_DateType == 3)
                    {
                        DateTime timeSpan = CarObj.time.Value;//入场时间
                        List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(acc.AccessAuth_DayContent);
                        Model.RangeTime time = times?.Find(x => x.GetDateTime(x.start) <= timeSpan && x.GetDateTime(x.end) >= timeSpan);
                        allowAcc = time != null; //在允许通行时间内
                    }
                    else if (acc.AccessAuth_DateType == 1)
                    {
                        string currentTime = CarObj.time.Value.ToString("HH:mm");
                        TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                        List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(acc.AccessAuth_DayContent);
                        Model.RangeTime time = times?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                        allowAcc = time != null; //在允许通行时间内
                    }
                    else if (acc.AccessAuth_DateType == 2)
                    {
                        string currentTime = CarObj.time.Value.ToString("HH:mm");
                        TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                        int weekDay = (int)CarObj.time.Value.DayOfWeek;
                        List<Model.RangeWeek> times = TyziTools.Json.ToObject<List<Model.RangeWeek>>(acc.AccessAuth_WeekContent);
                        var rangeWeeks = times?.FindAll(x => x.week.Contains(weekDay.ToString()));
                        if (rangeWeeks != null && rangeWeeks.Count > 0)
                        {
                            foreach (var item in rangeWeeks)
                            {
                                if (allowAcc) break;
                                List<Model.RangeTime> timeRange = TyziTools.Json.ToObject<List<Model.RangeTime>>(item.times);
                                var time = timeRange?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                                allowAcc = time != null; //在允许通行时间内
                            }
                        }
                    }
                }
                //设置的是确认放行
                else if (acc.AccessAuth_IsAccess == 4)
                {
                    if (iscan) break;//存在任意一条时间范围弹框确认,则跳出循环

                    if (acc.AccessAuth_DateType == 3)
                    {
                        DateTime timeSpan = CarObj.time.Value;//入场时间
                        List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(acc.AccessAuth_DayContent);
                        Model.RangeTime time = times?.Find(x => x.GetDateTime(x.start) <= timeSpan && x.GetDateTime(x.end) >= timeSpan);
                        iscan = time != null; //在确认放行时间内
                    }
                    else if (acc.AccessAuth_DateType == 1)
                    {
                        string currentTime = CarObj.time.Value.ToString("HH:mm");
                        TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                        List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(acc.AccessAuth_DayContent);
                        Model.RangeTime time = times?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                        iscan = time != null; //在确认放行时间内
                    }
                    else if (acc.AccessAuth_DateType == 2)
                    {
                        string currentTime = CarObj.time.Value.ToString("HH:mm");
                        TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                        int weekDay = (int)CarObj.time.Value.DayOfWeek;
                        List<Model.RangeWeek> times = TyziTools.Json.ToObject<List<Model.RangeWeek>>(acc.AccessAuth_WeekContent);
                        var rangeWeeks = times?.FindAll(x => x.week.Contains(weekDay.ToString()));
                        if (rangeWeeks != null && rangeWeeks.Count > 0)
                        {
                            foreach (var item in rangeWeeks)
                            {
                                if (allowAcc) break;
                                List<Model.RangeTime> timeRange = TyziTools.Json.ToObject<List<Model.RangeTime>>(item.times);
                                var time = timeRange?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                                iscan = time != null; //在确认放行时间内
                            }
                        }
                    }
                }
            }

            if (iscan) data.code = 2;

            if ((isacc0 && !isacc1 && disableAcc) || (isacc0 && isacc1 && (disableAcc || !allowAcc)) || (!isacc0 && isacc1 && !allowAcc))
            {
                data.code = 0;
                data.msg = "不在通行时间内，禁止通行";
            }

            return data;
        }

        /// <summary>
        /// 判断是否触发尾号限行(1-触发限行规则，0-未触发)
        /// </summary>
        public static Model.Result IsNumTimeAccess(IEnumerable<Model.EndNumAuth> accesseList, Model.ParkCarInOut CarObj)
        {
            Model.Result data = new Model.Result();
            if (accesseList == null || !accesseList.Any()) return data;
            if (string.IsNullOrEmpty(CarObj.carno)) return data;

            bool disableAcc = false;//禁止通行判断结果
            bool isacc0 = false;//是否计算了禁止通行

            foreach (var acc in accesseList)
            {
                if (string.IsNullOrEmpty(acc.EndNumAuth_Num)) continue;
                var endNum = Utils.GetCarEndNum(CarObj.carno);
                if (!acc.EndNumAuth_Num.Split(',').Contains(endNum)) continue;

                isacc0 = true;
                if (disableAcc) break;//存在任意一条时间范围禁止通行,则跳出循环

                if (acc.EndNumAuth_IsVoice == 1) data.msg = acc.EndNumAuth_VoiceText; else data.msg = string.Empty;
                data.data = endNum;

                if (acc.EndNumAuth_DateType == 3)
                {
                    DateTime timeSpan = CarObj.time.Value;//入场时间

                    var dayContent = Utils.UrlDecode(acc.EndNumAuth_DayContent);
                    List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(dayContent);
                    Model.RangeTime time = times?.Find(x => x.GetDateTime(x.start) <= timeSpan && x.GetDateTime(x.end) >= timeSpan);
                    disableAcc = time != null; //不在禁止通行时间内
                }
                else if (acc.EndNumAuth_DateType == 1)
                {
                    string currentTime = CarObj.time.Value.ToString("HH:mm");
                    TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                    var dayContent = Utils.UrlDecode(acc.EndNumAuth_DayContent);
                    List<Model.RangeTime> times = TyziTools.Json.ToObject<List<Model.RangeTime>>(dayContent);
                    Model.RangeTime time = times?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                    disableAcc = time != null; //不在禁止通行时间内
                }
                else if (acc.EndNumAuth_DateType == 2)
                {
                    string currentTime = CarObj.time.Value.ToString("HH:mm");
                    TimeSpan timeSpan = new TimeSpan(int.Parse(currentTime.Split(':')[0]), int.Parse(currentTime.Split(':')[1]), 0);
                    int weekDay = (int)CarObj.time.Value.DayOfWeek;
                    //对acc.EndNumAuth_WeekContent进行url解码
                    string weekContent = Utils.UrlDecode(acc.EndNumAuth_WeekContent);
                    List<Model.RangeWeek> times = TyziTools.Json.ToObject<List<Model.RangeWeek>>(weekContent);
                    var rangeWeeks = times?.FindAll(x => x.week.Contains(weekDay.ToString()));
                    if (rangeWeeks != null && rangeWeeks.Count > 0)
                    {
                        foreach (var item in rangeWeeks)
                        {
                            if (disableAcc) break;
                            List<Model.RangeTime> timeRange = TyziTools.Json.ToObject<List<Model.RangeTime>>(item.times);
                            var time = timeRange?.Find(x => x.GetTimeSpan(x.start) <= timeSpan && x.GetTimeSpan(x.end) >= timeSpan);
                            disableAcc = time != null; //不在禁止通行时间内
                        }
                    }
                }
            }

            if (isacc0 && disableAcc)
            {
                data.code = 1;
            }

            return data;
        }

        /// <summary>
        /// 获取车牌类型
        /// </summary>
        public static Model.CarCardType GetCarCardType(Model.ParkCarInOut CarObj, Model.Car car, List<Model.ParkOrder> parkOrders, Model.PolicyPassway policy, List<Model.CarCardType> cctList = null)
        {
            Model.CarCardType carCardType = null;

            if (car != null && !(CarObj?.changeCarCardType ?? false))
            {
                carCardType = cctList?.Find(x => x.CarCardType_No == car.Car_TypeNo);
                if (carCardType == null) carCardType = BLL.CarCardType.GetEntity(car.Car_TypeNo);
            }

            if (carCardType == null && (CarObj?.changeCarCardType ?? false))
            {
                carCardType = cctList?.Find(x => x.CarCardType_No == CarObj.carcardtype);
                if (carCardType == null) carCardType = BLL.CarCardType.GetEntity(CarObj.carcardtype);
            }

            if (carCardType == null && !string.IsNullOrEmpty(policy?.PolicyPassway_DefaultCarCardType))
            {
                carCardType = cctList?.Find(x => x.CarCardType_No == policy?.PolicyPassway_DefaultCarCardType);
                if (carCardType == null) carCardType = BLL.CarCardType.GetEntity(policy?.PolicyPassway_DefaultCarCardType);
            }

            if (carCardType == null) LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[{CarObj?.carno}][{CarObj?.recogresult}]通行检测未找到车道默认计费类型数据:{policy?.PolicyPassway_No}，{policy?.PolicyPassway_DefaultCarCardType}", LogLevel.Error, null);

            return carCardType;
        }

        /// <summary>
        /// 获取车牌颜色
        /// </summary>
        public static Model.CarType GetCarType(string carNoJson, Model.Car car, Model.PolicyPassway policy, string carno, List<Model.CarType> ctList = null, bool isChangeCarType = false)
        {
            List<string> cartypeNoList = TyziTools.Json.ToObject<List<string>>(carNoJson);
            Model.CarType carType = null;
            if (car != null && !isChangeCarType)
            {
                carType = ctList?.Find(x => x.CarType_No == car.Car_VehicleTypeNo);
                if (carType == null) carType = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
            }

            if (carType == null && cartypeNoList != null && cartypeNoList.Count > 0)
            {
                if (!string.IsNullOrEmpty(carno) && Regex.IsMatch(carno.Substring(0, 1).ToString(), "[\u4e00-\u9fa5]") && carno.Substring(1, 1).ToString() == "0")
                {
                    carType = ctList?.Find(x => x.CarType_IsDefault == 1 && x.CarType_Name == "0牌车");
                    if (carType == null) carType = BLL.CarType.GetEntity("*", "CarType_IsDefault=1 and CarType_Name='0牌车'");
                }

                if (carType == null)
                {
                    carType = ctList?.Find(x => cartypeNoList.Contains(x.CarType_No) || cartypeNoList.Contains(x.CarType_Name));
                    if (carType == null) carType = BLL.CarType.GetAllEntity(" * ", $"CarType_No in ('{string.Join("','", cartypeNoList)}') OR CarType_Name in ('{string.Join("','", cartypeNoList)}')").FirstOrDefault();
                }
            }

            if (carType == null && !string.IsNullOrEmpty(policy?.PolicyPassway_DefaultCarType))
            {
                carType = ctList?.Find(x => x.CarType_No == policy?.PolicyPassway_DefaultCarType);
                if (carType == null) carType = BLL.CarType.GetEntity(policy?.PolicyPassway_DefaultCarType);
            }

            if (carType == null) LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[{carno}][{policy?.PolicyPassway_DefaultCarType}]【{carNoJson}】通行检测未找到车牌颜色数据", LogLevel.Error, null);

            return carType;
        }

        /// <summary>
        /// 获取车牌类型
        /// </summary>
        public static Model.CarCardType GetCarCardType(Model.Car car, Model.PolicyPassway policy)
        {
            Model.CarCardType carCardType = null;
            if (car == null && !string.IsNullOrEmpty(policy?.PolicyPassway_DefaultCarCardType))
                carCardType = BLL.CarCardType.GetEntity(policy?.PolicyPassway_DefaultCarCardType);
            else
                carCardType = BLL.CarCardType.GetEntity(car.Car_TypeNo);
            return carCardType;
        }

        /// <summary>
        /// 模糊匹配固定车信息
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="macth"></param>
        /// <returns></returns>
        public static Model.Car MatchCar(string carno, int? macth)
        {
            if (string.IsNullOrEmpty(carno)) return null;

            if (AppBasicCache.ReadWriteCache && AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache == 1)
            {
                //优先完全匹配，匹配到车辆信息则跳过模糊匹配
                AppBasicCache.GetCar.TryGetValue(carno, out var car); //var car = BLL.Car.GetEntityByCarNo(carno);
                if (car != null) return car.Copy();

                string sqlwhere = string.Empty;
                switch (macth)
                {
                    case null:
                    case 1:
                        break;
                    case 2:
                        //仅允许首字符错误
                        var newCarno = carno.Substring(1);
                        var item = AppBasicCache.GetCar.Keys.Where(x => x.Substring(1) == newCarno);
                        if (item != null && item.Count() > 0)
                        {
                            if (AppBasicCache.GetCar.TryGetValue(item.FirstOrDefault(), out var m))
                            {
                                car = m?.Copy();
                            }
                        }
                        break;
                    case 3:
                        //允许任意一位字符错误
                        var k = AppBasicCache.GetCar.Keys.Where(x => Utils.CalculateLevenshteinDistance(carno, x) <= 1);
                        if (k != null && k.Count() > 0)
                        {
                            if (AppBasicCache.GetCar.TryGetValue(k.FirstOrDefault(), out var n))
                            {
                                car = n?.Copy();
                            }
                        }
                        break;
                    default:
                        return null;
                }
                return car;
            }
            else
            {

                //优先完全匹配，匹配到车辆信息则跳过模糊匹配
                var car = BLL.Car.GetEntityByCarNo(carno);
                if (car != null) return car?.Copy();

                string sqlwhere = string.Empty;
                switch (macth)
                {
                    case null:
                    case 1:
                        //sqlwhere = $" Car_CarNo='{carno}' ";
                        return car;
                    case 2:
                        sqlwhere = $" Car_CarNo like '_{carno.Substring(1)}' ";
                        break;
                    case 3:
                        var rexs = carno.ToCharArray();
                        List<string> str = new List<string>();
                        for (int i = 0; i < rexs.Length; i++)
                        {
                            //if (!char.IsLetter(rexs[i]) || rexs[i] > 128) continue;

                            str.Add($" Car_CarNo like '{carno.Substring(0, i)}_{carno.Substring(i + 1)}' ");
                        }

                        sqlwhere = string.Join(" OR ", str);
                        break;
                    default:
                        return null;
                }

                var model = BLL.Car.GetEntity("*", sqlwhere);

                return model;
            }
        }

        /// <summary>
        /// 匹配访客车辆
        /// </summary>
        /// <param name="code">200-入场，201-出场</param>
        /// <param name="carno">车牌号</param>
        /// <param name="reserve">预约记录</param>
        /// <param name="car">车辆信息</param>
        /// <param name="carCardType">车牌类型</param>
        /// <param name="carType">车牌颜色</param>
        public static void MacthReserve(Model.ParkOrder curOrder, int code, string carno, int? macth, out Model.Reserve reserve, ref Model.Car car, ref Model.CarCardType carCardType, ref Model.CarType carType, List<Model.CarType> ctList = null, List<Model.CarCardType> cctList = null)
        {
            reserve = null;
            Model.Reserve data = null;

            if (!AppBasicCache.ReadWriteCache || AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache != 1)
            {
                StringBuilder sqlwhere2 = new StringBuilder();
                if (code == 200)
                {
                    //sqlwhere.Append($" AND ((Reserve_Status=0 AND '{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss")}' BETWEEN Reserve_StartTime AND Reserve_EndTime) ");
                    //sqlwhere.Append($" OR Reserve_Status=1) ");

                    string sql = "Reserve_Status IN(0,1)";
                    if (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes == 1) { sql = "Reserve_Status IN(0,1,4)"; }

                    sqlwhere2.Append($"  Reserve_CarNo='{carno}' AND {sql} AND '{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss")}' BETWEEN Reserve_StartTime AND Reserve_EndTime");
                }
                else if (code == 201)
                {
                    var sql = $"  Reserve_CarNo='{carno}' AND Reserve_Status=1 ";
                    if (curOrder != null && curOrder.ParkOrder_EnterTime != null)
                    {
                        sql += $" AND Reserve_EndTime > '{curOrder.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}'";
                    }
                    sqlwhere2.Append($"  Reserve_CarNo='{carno}' AND Reserve_Status=1 ");
                }
                data = BLL.BaseBLL._GetEntityByWhere(new Model.Reserve(), "*", sqlwhere2.ToString());

                if (data == null && macth != null && macth > 1)
                {
                    StringBuilder sqlwhere = new StringBuilder();
                    switch (macth)
                    {
                        case 2:
                            sqlwhere.Append($" Reserve_CarNo like'_{carno.Substring(1)}' ");
                            break;
                        case 3:
                            var rexs = carno.ToCharArray();
                            List<string> str = new List<string>();
                            for (int i = 0; i < rexs.Length; i++)
                            {
                                //if (!char.IsLetter(rexs[i]) || rexs[i] > 128) continue;

                                str.Add($" Reserve_CarNo like '{carno.Substring(0, i)}_{carno.Substring(i + 1)}' ");
                            }

                            sqlwhere.Append(" (" + string.Join(" OR ", str) + ") ");
                            break;
                        case null:
                        case 1:
                        default:
                            sqlwhere.Append($" Reserve_CarNo='{carno}' ");
                            break;
                    }

                    if (code == 200)
                    {
                        string sql = "Reserve_Status IN(0,1)";
                        if (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes == 1) { sql = "Reserve_Status IN(0,1,4)"; }

                        sqlwhere.Append($" AND {sql} AND '{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss")}' BETWEEN Reserve_StartTime AND Reserve_EndTime");
                    }
                    else if (code == 201)
                    {
                        sqlwhere.Append($" AND Reserve_Status=1 ");
                    }
                    data = BLL.BaseBLL._GetEntityByWhere(new Model.Reserve(), "*", sqlwhere.ToString());
                }
            }
            else
            {
                //&& (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes == 1 && (x.Reserve_Status == 1 && x.Reserve_Status == 4))
                var nowTime = DateTime.Now;
                if (code == 200)
                {
                    if (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes != 1) data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo == carno && (x.Reserve_Status == 0 || x.Reserve_Status == 1) && nowTime >= x.Reserve_StartTime && nowTime <= x.Reserve_EndTime).FirstOrDefault();
                    else data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo == carno && (x.Reserve_Status == 0 || x.Reserve_Status == 1 || x.Reserve_Status == 4) && nowTime >= x.Reserve_StartTime && nowTime <= x.Reserve_EndTime).FirstOrDefault();
                }
                else if (code == 201)
                {
                    data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo == carno && x.Reserve_Status == 1).FirstOrDefault();
                    if (data != null && curOrder != null && curOrder.ParkOrder_EnterTime != null)
                    {
                        //如果车辆进场时，访客登记就已经失效了，直接返回空记录
                        if (curOrder.ParkOrder_EnterTime >= data.Reserve_EndTime)
                        {
                            data = null;
                            return;
                        }
                    }
                }
                else
                    data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo == carno).FirstOrDefault();

                if (data == null && macth != null && macth > 1)
                {
                    switch (macth)
                    {
                        case 2:
                            //仅允许首字符错误
                            var newCarno = carno.Substring(1);
                            if (code == 200)
                            {
                                if (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes != 1) data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo.Substring(1) == newCarno && (x.Reserve_Status == 0 || x.Reserve_Status == 1) && nowTime >= x.Reserve_StartTime && nowTime <= x.Reserve_EndTime).FirstOrDefault();
                                else data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo.Substring(1) == newCarno && (x.Reserve_Status == 0 || x.Reserve_Status == 1 || x.Reserve_Status == 4) && nowTime >= x.Reserve_StartTime && nowTime <= x.Reserve_EndTime).FirstOrDefault();
                            }
                            else if (code == 201)
                                data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo.Substring(1) == newCarno && x.Reserve_Status == 1).FirstOrDefault();
                            else
                                data = AppBasicCache.GetReserve.Values.Where(x => x.Reserve_CarNo.Substring(1) == newCarno).FirstOrDefault();
                            break;
                        case 3:
                            //允许任意一位字符错误
                            if (code == 200)
                            {
                                if (AppBasicCache.GetPolicyPark?.PolicyPark_VisitorTimes != 1) data = AppBasicCache.GetReserve.Values.Where(x => Utils.CalculateLevenshteinDistance(carno, x.Reserve_CarNo) <= 1 && (x.Reserve_Status == 0 || x.Reserve_Status == 1) && nowTime >= x.Reserve_StartTime && nowTime <= x.Reserve_EndTime).FirstOrDefault();
                                else data = AppBasicCache.GetReserve.Values.Where(x => Utils.CalculateLevenshteinDistance(carno, x.Reserve_CarNo) <= 1 && (x.Reserve_Status == 0 || x.Reserve_Status == 1 || x.Reserve_Status == 4) && nowTime >= x.Reserve_StartTime && nowTime <= x.Reserve_EndTime).FirstOrDefault();
                            }
                            else if (code == 201)
                                data = AppBasicCache.GetReserve.Values.Where(x => Utils.CalculateLevenshteinDistance(carno, x.Reserve_CarNo) <= 1 && x.Reserve_Status == 1).FirstOrDefault();
                            else
                                data = AppBasicCache.GetReserve.Values.Where(x => Utils.CalculateLevenshteinDistance(carno, x.Reserve_CarNo) <= 1).FirstOrDefault();
                            break;
                        default:
                            break;
                    }
                }
            }

            if (data != null)
            {
                reserve = data;
                car = new Model.Car()
                {
                    Car_CarNo = reserve.Reserve_CarNo,
                    Car_VehicleTypeNo = reserve.Reserve_CarTypeNo,
                    Car_OrderSpace = reserve.Reserve_OrderNo,
                    Car_TypeNo = reserve.Reserve_CardNo,
                    Car_Status = 1,
                    Car_ParkingNo = reserve.Reserve_ParkNo,
                    Car_BeginTime = reserve.Reserve_StartTime,
                    Car_EndTime = reserve.Reserve_EndTime,
                    Car_OnLine = 2,
                    Car_IsMoreCar = 0,
                    Car_Category = Model.EnumCarType.Visitor.ToString()
                };

                carCardType = cctList?.Find(x => x.CarCardType_No == data.Reserve_CardNo);
                if (carCardType == null) carCardType = BLL.CarCardType.GetEntity(data.Reserve_CardNo);

                carType = ctList?.Find(x => x.CarType_No == data.Reserve_CarTypeNo);
                if (carType == null) carType = BLL.CarType.GetEntity(data.Reserve_CarTypeNo);
            }
        }


        /// <summary>
        /// 匹配商家车辆
        /// </summary>
        /// <param name="code">200-入场，201-出场</param>
        /// <param name="carno">车牌号</param>
        /// <param name="businessCar">商家车辆</param>
        /// <param name="car">车辆信息</param>
        /// <param name="carCardType">车牌类型</param>
        /// <param name="carType">车牌颜色</param>
        public static void MacthBusinessCar(DateTime time, string carno, int? macth, out Model.BusinessCar businessCar, ref Model.Car car, ref Model.CarCardType carCardType, ref Model.CarType carType, List<Model.CarType> ctList = null, List<Model.CarCardType> cctList = null)
        {
            businessCar = null;
            Model.BusinessCar data = null;
            if (!AppBasicCache.ReadWriteCache || AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache != 1)
            {
                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" BusinessCar_CarNo='{carno}' ");
                sqlwhere.Append($" AND BusinessCar_Status=0 ");
                sqlwhere.Append($" AND BusinessCar_EndTime> '{time.ToString("yyyy-MM-dd HH:mm:ss")}' ");
                sqlwhere.Append($" ORDER BY BusinessCar_ID DESC ");
                data = BLL.BaseBLL._GetEntityByWhere(new Model.BusinessCar(), "*", sqlwhere.ToString());

                if (data == null && macth != null && macth > 1)
                {
                    StringBuilder sqlwhere2 = new StringBuilder();
                    switch (macth)
                    {
                        case 2:
                            sqlwhere2.Append($" BusinessCar_CarNo like'_{carno.Substring(1)}' ");
                            break;
                        case 3:
                            var rexs = carno.ToCharArray();
                            List<string> str = new List<string>();
                            for (int i = 0; i < rexs.Length; i++)
                            {
                                //if (!char.IsLetter(rexs[i]) || rexs[i] > 128) continue;

                                str.Add($" BusinessCar_CarNo like '{carno.Substring(0, i)}_{carno.Substring(i + 1)}' ");
                            }

                            sqlwhere2.Append(" (" + string.Join(" OR ", str) + ") ");
                            break;
                        default:
                            break;
                    }
                    sqlwhere2.Append($" AND BusinessCar_Status=0 ");
                    sqlwhere2.Append($" AND BusinessCar_EndTime> '{time.ToString("yyyy-MM-dd HH:mm:ss")}' ");
                    sqlwhere2.Append($" ORDER BY BusinessCar_ID DESC ");
                    data = BLL.BaseBLL._GetEntityByWhere(new Model.BusinessCar(), "*", sqlwhere2.ToString());
                }
            }
            else
            {
                data = AppBasicCache.GetBusinessCar.Values.Where(x => x.BusinessCar_CarNo == carno && x.BusinessCar_Status == 0 && x.BusinessCar_EndTime > time).LastOrDefault();
                if (data == null)
                {
                    switch (macth)
                    {
                        case null:
                        case 1:
                            break;
                        case 2:
                            //仅允许首字符错误
                            var newCarno = carno.Substring(1);
                            data = AppBasicCache.GetBusinessCar.Values.Where(x => x.BusinessCar_CarNo.Substring(1) == newCarno && x.BusinessCar_Status == 0 && x.BusinessCar_EndTime > time).LastOrDefault();
                            break;
                        case 3:
                            //允许任意一位字符错误
                            data = AppBasicCache.GetBusinessCar.Values.Where(x => Utils.CalculateLevenshteinDistance(carno, x.BusinessCar_CarNo) <= 1 && x.BusinessCar_Status == 0 && x.BusinessCar_EndTime > time).LastOrDefault();
                            break;
                        default:
                            break;
                    }
                }
            }

            if (data != null)
            {
                businessCar = data;
                car = new Model.Car()
                {
                    Car_CarNo = businessCar.BusinessCar_CarNo,
                    Car_VehicleTypeNo = businessCar.BusinessCar_CarTypeNo,
                    Car_OrderSpace = businessCar.BusinessCar_OrderNo,
                    Car_TypeNo = businessCar.BusinessCar_CardNo,
                    Car_Status = 1,
                    Car_ParkingNo = businessCar.BusinessCar_ParkNo,
                    Car_BeginTime = businessCar.BusinessCar_StartTime,
                    Car_EndTime = businessCar.BusinessCar_EndTime,
                    Car_OnLine = 2,
                    Car_IsMoreCar = 0,
                    Car_Category = Model.EnumCarType.Free.ToString()
                };

                carCardType = cctList?.Find(x => x.CarCardType_No == data.BusinessCar_CardNo);
                if (carCardType == null) carCardType = BLL.CarCardType.GetEntity(data.BusinessCar_CardNo);

                carType = ctList?.Find(x => x.CarType_No == data.BusinessCar_CarTypeNo);
                if (carType == null) carType = BLL.CarType.GetEntity(data.BusinessCar_CarTypeNo);
            }
        }

        /// <summary>
        /// 获取占用车位的访客车辆数
        /// </summary>
        /// <returns></returns>
        public static List<Model.Reserve> GetReserveUseSapce(DateTime time)
        {
            StringBuilder sqlwhere = new StringBuilder();
            sqlwhere.Append($" Reserve_Status=0 ");
            sqlwhere.Append($" AND '{time.ToString("yyyy-MM-dd HH:mm:ss")}' BETWEEN Reserve_StartTime AND Reserve_EndTime ");

            var data = BLL.Reserve.GetAllEntity("Reserve_CarNo", sqlwhere.ToString());
            return data;
        }

        /// <summary>
        /// 更新识别结果
        /// </summary>
        /// <param name="result"></param>
        /// <param name="carRecog"></param>
        /// <param name="carType"></param>
        /// <param name="camera"></param>
        /// <param name="passway"></param>
        /// <returns></returns>
        public static int UpdateRecog(ref Model.ResultPass result, Model.CarRecog carRecog, Model.Passway passway = null, Model.Device camera = null, Model.CarCardType card = null, Model.CarType carType = null, bool writePreIncar = false, List<Model.PasswayLink> passwayLinks = null)
        {
            try
            {
                //更新识别记录
                carRecog.CarRecog_CarType = card?.CarCardType_Name;
                carRecog.CarRecog_CameraName = camera?.Device_Name;
                carRecog.CarRecog_PasswayNo = passway?.Passway_No;
                carRecog.CarRecog_PasswayName = passway?.Passway_Name;
                if (carRecog.CarRecog_IsOpen != 10)
                {
                    carRecog.CarRecog_IsOpen = result.passres?.code ?? 0;
                }
                carRecog.CarRecog_Remark = result.errmsg ?? result.passres?.errmsg;

                result.recog = carRecog;
                _ = CustomThreadPool.SyncTaskPool.QueueTask(null, () =>
                {
                    try
                    {
                        var res = BLL.CarRecog.GetInstance(carRecog.CarRecog_Time ?? DateTimeHelper.GetNowTime()).AddOrUpdate(carRecog);
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{carRecog?.CarRecog_CarNo}]通行识别结果保存异常：{e.ToString()}");
                    }

                    if (AppBasicCache.GetPolicyPark?.PolicyPark_NoEnterRecordSearch == 1 && writePreIncar)
                    {
                        try
                        {
                            var parkareaLink = passwayLinks.FindAll(x => x.PasswayLink_PasswayNo == passway?.Passway_No);
                            if (parkareaLink.Count == 1)
                            {
                                Model.InCar incar = new InCar();
                                incar.InCar_CarNo = carRecog.CarRecog_CarNo;
                                incar.InCar_CarCardTypeNo = card?.CarCardType_No;
                                incar.InCar_ParkAreaNo = parkareaLink.FirstOrDefault().PasswayLink_ParkAreaNo;
                                incar.InCar_EnterTime = carRecog.CarRecog_Time;
                                incar.InCar_ParkOrderNo = "";
                                incar.InCar_Status = 199;
                                var res = BLL.BaseBLL._Insert(incar);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{carRecog?.CarRecog_CarNo}]通行识别结果保存预入场记录异常：{ex.ToString()}");
                        }
                    }
                    return Task.CompletedTask;
                });
                return 1;
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"通行识别结果保存异常：[{carRecog?.CarRecog_CarNo}]," + ex.ToString());
                return -1;
            }
        }

        /// <summary>
        /// 白名单车辆使用登记的身份证号自动获取健康码信息
        /// </summary>
        /// <param name="result">返回通行结果</param>
        /// <param name="policyArea">区域设置</param>
        /// <param name="parking">车场信息，必须包含字段Parking_Mode</param>
        /// <param name="passway">车道信息，必须包含字段Device_No</param>
        /// <param name="owner">车主信息，必须包含字段Owner_IDCard</param>
        /// <returns></returns>
        public static bool EPIDCardResult(ref Model.ResultPass result, Model.PolicyArea policyArea, Model.Parking parking, Model.Passway passway, Model.Owner owner,
            List<Model.DeviceExt> deviceList = null, List<Model.PolicyArea> paList = null)
        {
            bool idSuccess = false;
            try
            {
                int? code = result.passres?.code;
                if (policyArea?.PolicyArea_EPIDCard == 1)
                {
                    if (parking?.Parking_Mode == null ||
                        string.IsNullOrEmpty(passway?.Passway_No) ||
                        string.IsNullOrEmpty(owner?.Owner_IDCard))
                        return false;

                    Model.Device dSelf = null;

                    var d = deviceList?.Find(x => x.Device_PasswayNo == passway.Passway_No && x.Device_Category == Model.DriveCategory.Support);
                    if (d != null) dSelf = TyziTools.Json.ToModel<Model.Device>(TyziTools.Json.ToString(d));
                    else dSelf = BLL.Device.GetEntity("*", $"Device_PasswayNo='{passway.Passway_No}' AND Device_Category={Model.DriveCategory.Support}");

                    var ds = BLL.OtherApi.HealthCodeResult(parking, dSelf, owner);
                    if (ds.status == 1)
                    {
                        string symcount = ds.res?.ymcount;
                        string skydate = ds.res?.kydate;
                        string shsjcsj = ds.res?.hsjcsj;
                        string sfznumber = ds.res?.sfznumber;
                        string sfzname = ds.res?.sfzname;
                        string hsjcresult = ds.res?.hsjcresult;
                        string kyresult = ds.res?.kyresult;
                        string hsjcjg = ds.res?.hsjcjg;
                        string kydetail = ds.res?.kydetail;

                        int? hsjcjgres = Common.Utils.ExtractNumber(hsjcjg);
                        int? kydetailres = Common.Utils.ExtractNumber(kydetail);
                        int iColor = ds.res?.color ?? 0;
                        bool bkyd = DateTime.TryParse(skydate, out DateTime dkydate);
                        bool bhsd = DateTime.TryParse(shsjcsj, out DateTime hsdate);
                        int.TryParse(symcount, out int iymcount);
                        if (!int.TryParse(hsjcresult, out int ihsjcresult)) { ihsjcresult = 3; }
                        if (!int.TryParse(kyresult, out int ikyresult)) { ikyresult = 3; }

                        var dpm = new Model.HealthCode
                        {
                            idcard = sfznumber,
                            name = sfzname,
                            hsres = ihsjcresult,
                            hstime = hsjcjgres,//通过核酸上报时间自己计算
                            hsdate = hsdate,
                            kyres = ikyresult,
                            kytime = kydetailres,//通过抗原上报时间自己计算
                            kydate = dkydate,
                            ymcount = iymcount,
                            color = iColor,
                            data = System.Web.HttpUtility.UrlEncode(ds.data)
                        };
                        if (!bkyd) dpm.kydate = null;
                        if (!bhsd) dpm.hsdate = null;
                        PassTool.PassHelper.OnCheckCarPass(dpm, ref result, out var dCode, paList);

                        result.hcres = dCode;
                        result.passres.errmsg = "白名单自动获取健康码";
                        if (result.passres.fycode != 0)
                        {
                            result.passres.code = code;//此处还原通行结果
                            result.passres.fycode = 1;//获取白名单健康码失败或不符合条件，提醒车主扫码
                        }
                        idSuccess = true;
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebSocket, $"身份证号获取健康码信息：{TyziTools.Json.ToString(ds, true)}", LogLevel.Warn);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.WebSocket, "身份证号获取健康码信息", LogLevel.Warn, ex);
            }
            return idSuccess;
        }
        #endregion

        #region 订单匹配

        /// <summary>
        /// 模糊匹配车牌订单
        /// </summary>
        /// <param name="ParkArea_No">区域编码</param>
        /// <param name="carno">车牌号</param>
        /// <param name="macth">模糊匹配规则</param>
        /// <param name="ParkOrder_StatusNo">订单状态枚举</param>
        /// <returns></returns>
        public static List<Model.ParkOrder> MatchParkOrder(List<Model.ParkOrder> allOrder, Model.ParkCarInOut CarObj, int? macth)
        {
            List<Model.ParkOrder> orderList = null;
            string carno = CarObj != null ? CarObj.carno : "";
            if (string.IsNullOrEmpty(CarObj.carno)) return orderList;

            string selectWhere = string.Empty;
            allOrder = allOrder?.FindAll(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In);
            allOrder = allOrder?.OrderByDescending(x => x.ParkOrder_EnterTime).ToList();

            //优先完全匹配，如果完全匹配到订单则跳过模糊匹配
            orderList = allOrder.FindAll(x => x.ParkOrder_CarNo == carno);
            if (orderList != null && orderList.Count > 0) return orderList;

            if (macth == null || macth == 1)
            {
                return orderList;
            }
            else if (macth == 2)
            {//允许首字符错误
                string rex = $"[\\d\\D]{carno.Substring(1, carno.Length - 1)}";
                orderList = allOrder.FindAll(x => Regex.IsMatch(x.ParkOrder_CarNo, rex));
            }
            else if (macth == 3)
            {//允许任一字母错误
                List<string> rexCarno = new List<string>();
                var rexs = carno.ToCharArray();
                for (int i = 0; i < rexs.Length; i++)
                {
                    //if (!char.IsLetter(rexs[i]) || rexs[i] > 128) continue;

                    rexCarno.Add($"{carno.Substring(0, i)}[\\d\\D]{carno.Substring(i + 1, carno.Length - i - 1)}");
                }

                if (rexCarno.Count == 0)
                    orderList = allOrder.FindAll(x => x.ParkOrder_CarNo == carno);
                else
                    orderList = allOrder.FindAll(x => Regex.IsMatch(x.ParkOrder_CarNo, string.Join("|", rexCarno)));
            }

            return orderList;
        }

        /// <summary>
        /// 模糊匹配场内车牌订单
        /// </summary>
        /// <param name="ParkArea_No">区域编码</param>
        /// <param name="carno">车牌号</param>
        /// <param name="macth">模糊匹配规则：1-完全匹配，2-允许任意1位字母错误，3-只允许首字母错误</param>
        /// <param name="ParkOrder_StatusNo">订单状态枚举</param>
        /// <returns></returns>
        public static Model.ParkOrder MatchParkOrder(Model.ParkCarInOut CarObj, int? macth)
        {
            string carno = CarObj != null ? (string.IsNullOrEmpty(CarObj.oldcarno) ? CarObj.carno : CarObj.oldcarno) : "";
            if (string.IsNullOrEmpty(CarObj.carno)) return null;

            if (!string.IsNullOrEmpty(CarObj.rOrderno))
            {
                Model.ParkOrder order = BLL.ParkOrder.GetEntity(CarObj.rOrderno);
                if (order != null) return order;
            }

            string selectWhere = string.Empty;

            string incarWhere = string.Empty;
            if (!string.IsNullOrEmpty(CarObj.oldcarno) && CarObj.oldcarno != CarObj.carno)
            {
                incarWhere = $"InCar_CarNo in ('{CarObj.carno}','{CarObj.oldcarno}') and InCar_Status=200";
            }
            else
            {
                incarWhere = $"InCar_CarNo='{CarObj.carno}' and InCar_Status=200";
            }

            //优先完全匹配，如果完全匹配到订单则跳过模糊匹配
            Model.InCar incar = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", incarWhere);
            if (incar != null)
            {
                Model.ParkOrder order = BLL.ParkOrder.GetEntity(incar.InCar_ParkOrderNo);
                if (order != null)
                {
                    if (!string.IsNullOrEmpty(CarObj.oldcarno) && CarObj.oldcarno != CarObj.carno && order.ParkOrder_CarNo == CarObj.oldcarno) { CarObj.carno = CarObj.oldcarno; }
                    return order;
                }
            }

            //完全匹配
            if (macth == null || macth == 1)
            {
                //selectWhere = $" InCar_CarNo='{CarObj.carno}' AND InCar_Status=200 ";
            }
            else
            {
                //允许首字符错误
                if (macth == 2)
                {
                    string rex = $"[\\d\\D]{carno.Substring(1, carno.Length - 1)}";
                    selectWhere = $" InCar_CarNo like '_{carno.Substring(1, carno.Length - 1)}' AND InCar_Status=200 ";
                }
                //允许任一字母错误
                else if (macth == 3)
                {
                    List<string> rexCarno = new List<string>();
                    var rexs = carno.ToCharArray();
                    for (int i = 0; i < rexs.Length; i++)
                    {
                        rexCarno.Add($" InCar_CarNo like '{carno.Substring(0, i)}_{carno.Substring(i + 1, carno.Length - i - 1)}'");
                    }

                    selectWhere = $" ({string.Join(" OR ", rexCarno)}) AND InCar_Status=200 ";
                }

                List<Model.InCar> incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", selectWhere);
                //if (macth == 2) incarList = incarList.Where(x => x.InCar_CarNo.Length == carno.Length).ToList();
                incarList = incarList?.OrderByDescending(x => x.InCar_ParkOrderNo).ToList();
                if (incarList != null && incarList.Count > 0)
                {
                    Model.ParkOrder order = BLL.ParkOrder.GetEntity(incarList.First().InCar_ParkOrderNo);
                    return order;
                }
            }

            return null;
        }

        /// <summary>
        /// 模糊匹配车牌订单明细
        /// </summary>
        /// <param name="ParkArea_No">区域编码</param>
        /// <param name="carno">车牌号</param>
        /// <param name="macth">模糊匹配规则</param>
        /// <param name="ParkOrder_StatusNo">订单状态枚举</param>
        /// <returns></returns>
        public static List<Model.OrderDetail> MatchOrderDetail(List<Model.OrderDetail> allOrder, Model.ParkCarInOut CarObj, int? macth)
        {
            List<Model.OrderDetail> orderList = null;
            string carno = CarObj != null ? CarObj.carno : "";
            string selectWhere = string.Empty;
            allOrder = allOrder.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);
            allOrder = allOrder?.OrderByDescending(x => x.OrderDetail_EnterTime).ToList();
            if (!string.IsNullOrEmpty(CarObj.carno))
            {
                if (macth == null || macth == 1)
                {
                    orderList = allOrder.FindAll(x => x.OrderDetail_CarNo == carno);
                }
                else if (macth == 2)
                {//允许首字符错误
                    string rex = $"[\\d\\D]{carno.Substring(1, carno.Length - 1)}";
                    orderList = allOrder.FindAll(x => Regex.IsMatch(x.OrderDetail_CarNo, rex));
                }
                else if (macth == 3)
                {//允许任一字母错误
                    List<string> rexCarno = new List<string>();
                    var rexs = carno.ToCharArray();
                    for (int i = 0; i < rexs.Length; i++)
                    {
                        //if (!char.IsLetter(rexs[i]) || rexs[i] > 128) continue;

                        rexCarno.Add($"{carno.Substring(0, i)}[\\d\\D]{carno.Substring(i + 1, carno.Length - i - 1)}");
                    }

                    if (rexCarno.Count == 0)
                        orderList = allOrder.FindAll(x => x.OrderDetail_CarNo == carno);
                    else
                        orderList = allOrder.FindAll(x => Regex.IsMatch(x.OrderDetail_CarNo, string.Join("|", rexCarno)));
                }
            }

            return orderList;
        }

        /// <summary>
        /// 匹配车牌的停车订单
        /// </summary>
        /// <param name="parkno">车场编码</param>
        /// <param name="passwayno">通道编码(用于获取通道策略中车牌的匹配规则)</param>
        /// <param name="carno">车牌号</param>
        /// <returns></returns>
        public static List<Model.ParkOrder> MatchParkOrder(string passwayno, string carno)
        {
            Model.PolicyPassway policy = BLL.PolicyPassway.GetEntityByPasswayNo(passwayno);

            List<Model.ParkOrder> allOrder = BLL.ParkOrder.GetParkOrderList("*", policy.PolicyPassway_ParkNo, carno, Model.EnumOrderSettleStatus.False);

            return MatchParkOrder(allOrder, new Model.ParkCarInOut() { carno = carno }, policy.PolicyPassway_CarMatch);
        }

        /// <summary>
        /// 匹配车牌的停车明细
        /// </summary>
        /// <param name="parkno">车场编码</param>
        /// <param name="passwayno">通道编码(用于获取通道策略中车牌的匹配规则)</param>
        /// <param name="carno">车牌号</param>
        /// <returns></returns>
        public static List<Model.OrderDetail> MatchOrderDetail(string passwayno, string carno)
        {
            Model.PolicyPassway policy = BLL.PolicyPassway.GetEntityByPasswayNo(passwayno);

            List<Model.OrderDetail> allOrder = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", $"OrderDetail_ParkNo='{policy.PolicyPassway_ParkNo}' AND OrderDetail_StatusNo={Model.EnumParkOrderStatus.In}");

            return MatchOrderDetail(allOrder, new Model.ParkCarInOut() { carno = carno }, policy.PolicyPassway_CarMatch);
        }

        /// <summary>
        /// 模糊匹配场内车辆
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static List<Model.OrderDetail> GetListByMatchCarNo(string parkno, string carno)
        {
            List<Model.OrderDetail> models = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", $"OrderDetail_ParkNo='{parkno}' AND OrderDetail_StatusNo='{Model.EnumParkOrderStatus.In}' AND OrderDetail_CarNo like '%{carno}%'");

            return models;
        }

        /// <summary>
        /// 模糊匹配场内车辆按入场时间
        /// </summary>
        /// <param name="parkno">车场编码</param>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static List<Model.OrderDetail> GetListByMatchCarNo(string parkno, string startTime, string endTime, string spassName)
        {
            string sw = $"OrderDetail_ParkNo='{parkno}' AND OrderDetail_StatusNo='{Model.EnumParkOrderStatus.In}' ";
            if (!string.IsNullOrEmpty(spassName))
            {
                sw += $" AND OrderDetail_EnterPasswayName like '%{spassName}%' ";
            }

            if (!string.IsNullOrEmpty(startTime))
            {
                sw += $" AND OrderDetail_EnterTime >= '{startTime}' ";
            }

            if (!string.IsNullOrEmpty(endTime))
            {
                sw += $" AND OrderDetail_EnterTime <= '{endTime}' ";
            }

            sw += "  order by OrderDetail_ID DESC limit 20 ";

            List<Model.OrderDetail> models = BLL.OrderDetail._GetAllEntity(new Model.OrderDetail(), "*", sw);

            return models;
        }

        /// <summary>
        /// 模糊匹配场内车辆(允许车牌号错误一位的模糊查询)
        /// </summary>
        public static List<Model.OrderDetail> GetListByMatchOneNotSame(string parkno, string carno)
        {
            List<Model.OrderDetail> dataAll = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_StatusNo='{Model.EnumParkOrderStatus.In}'");
            var data = MatchOrderDetail(dataAll, new Model.ParkCarInOut() { carno = carno }, 3);
            return data;
        }
        #endregion

        #region 图片存储

        /// <summary>
        /// 获取图片本地访问地址
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="path">图片本地路径</param>
        /// <returns></returns>
        public static string GetImageHttpUrl(string parkno, string path, string sentryNo = "", bool getSentryImg = false)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                    return path;

                if (path.Contains("http://") || path.Contains("https://"))
                    return path;

                return ImageTools.GetImgBSUrlByPath(path, BLL.ImageTools.LocalFilePath);

                //    path = HttpUtility.UrlDecode(path);
                //    int fileexitscount = 10;
                //    while (!File.Exists(path) && fileexitscount > 0)
                //    {
                //        fileexitscount--;
                //        Task.Delay(100).Wait();
                //    }

                //    if (!File.Exists(path))
                //    {
                //        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"获取出入场图片地址，未能找到文件：{path}");
                //        return "";
                //    }



                //    fileexitscount = 10;
                //    string base64 = string.Empty;
                //    byte[] buffer;
                //readAgin:
                //    try
                //    {
                //        using (FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read))
                //        {
                //            buffer = new byte[fs.Length];
                //            fs.Read(buffer, 0, buffer.Length);
                //        }
                //        base64 = Convert.ToBase64String(buffer);
                //    }
                //    catch (IOException e)
                //    {
                //        if (fileexitscount <= 0)
                //        {
                //            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"获取出入场图片地址异常，文件本地路径：{path}，{e.ToString()}");
                //            return "";
                //        }
                //        fileexitscount--;
                //        Task.Delay(100).Wait();
                //        goto readAgin;
                //    }

                //    return ImageSaveByBase64(parkno, base64, null, sentryNo, System.IO.Path.GetFileName(path), getSentryImg, path);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"获取出入场图片地址异常，文件本地路径：{path}，{ex.ToString()}");
                return "";
            }
        }

        /// <summary>
        /// 上报出入场图片
        /// </summary>
        /// <param name="parkno"></param>
        /// <param name="base64String"></param>
        /// <param name="rDir">存储目录：默认yyyyMM/dd</param>
        /// <returns></returns>
        public static string ImageSaveByBase64(string parkno, string base64String, string rDir = null, string sentryNo = "", string filename = "", bool getSentryImg = false, string filePath = null)
        {
            //if (!string.IsNullOrEmpty(base64String))
            //{
            //    try
            //    {
            //        string photoExtension = null;
            //        if (base64String.Contains("base64,")) { photoExtension = base64String.Substring(0, base64String.IndexOf(";")).Substring(base64String.IndexOf("/") + 1); }

            //        filename = string.IsNullOrEmpty(filename) ? $"{Utils.CreateNumber}.{photoExtension ?? "jpg"}" : filename;
            //        string path = string.Empty;

            //        //string imgPath = string.IsNullOrEmpty(rDir) ? BLL.SysConfig.GetImgPath(parkno) : rDir;
            //        string ymdPath = $"{DateTimeHelper.GetNowTime().ToString("yyyyMM")}/{DateTimeHelper.GetNowTime().ToString("dd")}";
            //        path = $"{(getSentryImg ? Config.AppSettingConfig.SiteDomain_Web.Trim('/') : Config.AppSettingConfig.SiteDomain_Web.Trim('/'))}/CameraCaptures/{(string.IsNullOrEmpty(sentryNo) ? "" : sentryNo + @"/")}{ymdPath}/{filename}";

            //        Action cmd = () =>
            //        {
            //            try
            //            {
            //                var sentry = AppBasicCache.GetSentryHost(sentryNo);
            //                if (sentry != null && sentry.SentryHost_Type == 1 || AppBasicCache.CurrentSysConfigContent != null && AppBasicCache.CurrentSysConfigContent.SysConfig_EnableImgPath == 1 && AppBasicCache.CurrentSysConfigContent.SysConfig_ImagePath != AppBasicCache.CurrentSysConfigContent.SysConfig_SentryImagePath)
            //                {

            //                    var data = new Model.API.UploadImageData() { category = 10000, base64 = HttpUtility.UrlEncode(base64String), filename = filename, rdir = rDir, sentryno = sentryNo };
            //                    string posturl = $"{Config.AppSettingConfig.SiteDomain_Web.Trim('/')}/api/ParkAPI/UploadImage";
            //                    var res = BLL.PushResult.Send(parkno, posturl, data, 5000);
            //                    if (res != null && res.success)
            //                    {
            //                        JObject obj = res.ParseData<JObject>();
            //                        if (obj != null && obj.ContainsKey("src"))
            //                        {
            //                            string src = HttpUtility.UrlDecode(obj["src"].ToString());
            //                            path = src;
            //                        }
            //                    }
            //                    else
            //                    {
            //                        if (!string.IsNullOrEmpty(filePath)) data.base64 = filePath;
            //                        //插入一条记录，重传识别图片
            //                        BLL.PostRecordHandle.Add(new Model.PostRecordHandle
            //                        {
            //                            PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
            //                            PostRecordHandle_CarPlate = filename,
            //                            PostRecordHandle_Datas = JsonConvert.SerializeObject(data),
            //                            PostRecordHandle_ToType = 18,
            //                            PostRecordHandle_Status = 0,
            //                            PostRecordHandle_ReturnMsg = string.Empty,
            //                            PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
            //                        });

            //                        BLL.SystemLogs.AddLog(null, "上传图片失败", $"原因：{(res != null ? res.errmsg : "")}");
            //                    }
            //                }
            //            }
            //            catch (Exception ex)
            //            {
            //                BLL.SystemLogs.AddLog(null, "上传图片异常", $"原因：{ex.ToString()}");
            //            }
            //        };
            //        if (CustomThreadPool.SyncTaskPool == null) Task.Run(() => { cmd(); }); else_ = CustomThreadPool.SyncTaskPool.QueueTask(null, cmd);

            //        return path;
            //    }
            //    catch (Exception ex)
            //    {
            //        BLL.SystemLogs.AddLog(null, "上传图片失败", $"原因：{ex.ToString()}");
            //        return "";
            //    }
            //}

            return "";
        }

        #endregion

        #region 订单处理

        /// <summary>
        /// 相机识别记录
        /// </summary>
        /// <param name="CarObj"></param>
        /// <param name="camrea"></param>
        /// <param name="passway"></param>
        public static Model.CarRecog AddCarRecog(Model.ParkCarInOut CarObj, Model.Device camrea, Model.Passway passway, Model.CarCardType carCardType, out string imgUrl, Model.OnCheckCache onCheckCache = null, bool isWriteDB = true)
        {
            string smalllocimg = null;
            string platecolor = null;
            string smallimg = "";
            string imgPath = "";

            if (!string.IsNullOrEmpty(CarObj.recogcontent))
            {
                JObject obj = JObject.Parse(CarObj.recogcontent);
                if (obj.ContainsKey("CarPlateColor"))
                    platecolor = obj["CarPlateColor"].ToString();
                if (obj.ContainsKey("SmallJPGPath"))
                    smalllocimg = obj["SmallJPGPath"].ToString();
                if (obj.ContainsKey("BigJPGPath"))
                    imgPath = obj["BigJPGPath"].ToString();
            }
            else
            {
                imgPath = CarObj.img;
            }
            imgUrl = BLL.ImageTools.GetImgWebUrlByPath(imgPath, BLL.ImageTools.LocalFilePath);
            smallimg = BLL.ImageTools.GetImgWebUrlByPath(smalllocimg, BLL.ImageTools.LocalFilePath);

            var model = new Model.CarRecog()
            {
                CarRecog_No = Utils.CreateNumber_SnowFlake,
                CarRecog_CarNo = CarObj.carno,
                CarRecog_CameraNo = CarObj.camerano,
                CarRecog_ParkNo = CarObj.parkno,
                CarRecog_Img = imgUrl,
                CarRecog_Mode = CarObj.mode,
                CarRecog_CarType = carCardType?.CarCardType_Name,
                CarRecog_Time = CarObj.time,
                CarRecog_CameraName = camrea?.Device_Name,
                CarRecog_PasswayNo = passway?.Passway_No,
                CarRecog_PasswayName = passway?.Passway_Name,
                CarRecog_CarLogo = CarObj.carlogo,
                CarRecog_CarYear = CarObj.caryear,
                CarRecog_Credibility = CarObj.credibility,
                CarRecog_IsRealPlate = CarObj.isreal,
                CarRecog_LicensePoint = CarObj.licensepoint,
                CarRecog_Recogresult = "",// CarObj.recogresult,
                CarRecog_RecogContent = "",// CarObj.recogcontent,
                CarRecog_PlateColor = platecolor,
                CarRecog_SmallImg = smallimg,
                CarRecog_IsOpen = 0,
                CarRecog_Remark = ""
            };

            if (isWriteDB)
            {
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                {
                    try
                    {
                        var res = BLL.CarRecog.GetInstance(model.CarRecog_Time ?? DateTimeHelper.GetNowTime()).AddOrUpdate(model);
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[{CarObj?.carno}]保存识别记录异常：{e.ToString()}");
                    }
                    return Task.CompletedTask;
                });
            }
            return model;
        }

        /// <summary>
        /// 修改车牌 || 车牌颜色 || 车牌类型
        /// </summary>
        public static void UpdateCarIn(Model.ParkGateIn param, out List<Model.ParkOrder> orderList, out List<Model.OrderDetail> detailList, out Model.InCar incar)
        {
            orderList = new List<Model.ParkOrder>();
            detailList = new List<Model.OrderDetail>();
            incar = null;
            if (param == null) return;

            Model.ParkOrder parkOrder = BLL.ParkOrder.GetEntity(param.orderno);
            if (parkOrder == null) { return; }
            List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity(param.orderno);
            if (details == null || details.Count == 0) { return; }

            if (!string.IsNullOrEmpty(param.carno))
            {
                Model.Car car = BLL.Car.GetEntityByCarNo(param.carno);
                Model.CarCardType card = null;
                Model.CarType carType = null;
                if (car != null)
                {
                    card = BLL.CarCardType.GetEntity(car.Car_TypeNo);
                    param.carcardtypeno = card?.CarCardType_No;
                    param.carcardtypename = card?.CarCardType_Name;

                    carType = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                    param.cartypeno = carType?.CarType_No;
                    param.cartypename = carType?.CarType_Name;
                }
                else
                {
                    var policy = BLL.PolicyPassway.GetEntityByPasswayNo(parkOrder.ParkOrder_EnterPasswayNo);
                    if (policy != null)
                    {
                        carType = BLL.CarType.GetEntity(policy.PolicyPassway_DefaultCarType);
                        card = BLL.CarCardType.GetEntity(policy.PolicyPassway_DefaultCarCardType);

                        if (string.IsNullOrEmpty(param.cartypeno))
                        {
                            param.cartypeno = carType?.CarType_No;
                            param.cartypename = carType?.CarType_Name;
                        }
                        if (string.IsNullOrEmpty(param.carcardtypeno))
                        {
                            param.carcardtypeno = card?.CarCardType_No;
                            param.carcardtypename = card?.CarCardType_Name;
                        }
                    }
                }

                #region 人工修改车牌导致重复入场,则将前一条入场记录作自动关闭处理
                var order = BLL.ParkOrder.GetDetailByOrderNo(param.orderno);
                if (order.Item1 != null && param.carno != order.Item1.ParkOrder_CarNo)
                {
                    incar = new InCar();
                    incar.InCar_CarNo = order.Item1.ParkOrder_CarNo;
                    incar.InCar_Status = Model.EnumParkOrderStatus.Close;
                    incar.InCar_EnterTime = order.Item1.ParkOrder_EnterTime;
                    incar.InCar_CarCardTypeNo = order.Item1.ParkOrder_CarCardType;
                    incar.InCar_ParkAreaNo = order.Item1.ParkOrder_ParkAreaNo;
                }
                if (order.Item1 != null && (string.IsNullOrEmpty(param.orderno) || param.orderno != order.Item1.ParkOrder_No))
                {
                    order.Item1.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                    order.Item1.ParkOrder_Remark = "修改车牌后重复入场关闭订单";
                    if (order.Item2 != null)
                    {
                        order.Item2.ForEach(x =>
                        {
                            x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                            x.OrderDetail_Remark = "修改车牌后重复入场关闭订单";
                        });
                        detailList.AddRange(order.Item2);
                    }
                    orderList.Add(order.Item1);
                }
                #endregion

                parkOrder.ParkOrder_CarNo = param.carno;
                parkOrder.ParkOrder_IsModify = param.iModify;
                details.ForEach(x => { x.OrderDetail_CarNo = param.carno; x.Orderdetail_IsModify = param.iModify; });
            }

            if (!string.IsNullOrEmpty(param.cartypeno) && !string.IsNullOrEmpty(param.cartypename))
            {
                parkOrder.ParkOrder_CarType = param.cartypeno;
                parkOrder.ParkOrder_CarTypeName = param.cartypename;
                parkOrder.ParkOrder_IsModify = param.iModify;
                details.ForEach(x =>
                {
                    x.OrderDetail_CarType = param.cartypeno;
                    x.OrderDetail_CarTypeName = param.cartypename;
                    x.Orderdetail_IsModify = param.iModify;
                });
            }

            if (!string.IsNullOrEmpty(param.carcardtypeno) && !string.IsNullOrEmpty(param.carcardtypename))
            {
                parkOrder.ParkOrder_CarCardType = param.carcardtypeno;
                parkOrder.ParkOrder_CarCardTypeName = param.carcardtypename;
                parkOrder.ParkOrder_IsModify = param.iModify;
                details.ForEach(x =>
                {
                    x.OrderDetail_CarCardType = param.carcardtypeno;
                    x.OrderDetail_CarCardTypeName = param.carcardtypename;
                    x.Orderdetail_IsModify = param.iModify;
                });
            }

            orderList.Add(parkOrder);
            detailList.AddRange(details);
        }

        /// <summary>
        /// 获取访客车辆记录
        /// </summary>
        /// <param name="status">0-已预约，1-已入场，2-已过期，3-已取消</param>
        /// <param name="orderNo">订单号</param>
        /// <param name="carNo">车牌号</param>
        /// <param name="data">返回访客车辆</param>
        /// <returns></returns>
        public static bool GetReserve(int status, string orderNo, string carNo, out Model.Reserve data)
        {
            StringBuilder sql = new StringBuilder();
            sql.Append($" Reserve_Status={status} ");
            sql.Append($" AND Reserve_OrderNo='{orderNo}' ");
            sql.Append($" AND Reserve_CarNo='{carNo}' ");
            data = BLL.BaseBLL._GetEntityByWhere(new Model.Reserve(), "*", sql.ToString());

            if (data == null) return false;

            return true;
        }
        #endregion

        #region 计费处理
        /// <summary>
        /// 无入场记录计算费用
        /// </summary>
        /// <param name="result"></param>
        /// <param name="resultData"></param>
        /// <param name="policyPark"></param>
        /// <param name="car"></param>
        public static void FeeNoRecord(ref Model.ResultPass result, Model.ResultInOutList resultData, Model.PolicyPark policyPark, Model.PolicyPass policyPass, Model.Passway passway, out bool isAutoNoenterRecord)
        {
            isAutoNoenterRecord = false;
            //出场记录查找
            if (policyPark.PolicyPark_OutRecordFindEnble == 1 && policyPark.PolicyPark_OutRecordFindMin > 0)
            {
                var incar = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo='{result.passres.carno}' and InCar_Status=201");
                if (incar != null)
                {
                    var order = BLL.ParkOrder.GetEntity(incar.InCar_ParkOrderNo);
                    if (order != null && order.ParkOrder_IsNoInRecord != 1)
                    {
                        if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out && order.ParkOrder_OutTime != null
                            //&& order.ParkOrder_OutTime.Value.AddHours(24) >= result.time.Value
                            && order.ParkOrder_OutTime.Value >= result.time.Value.AddMinutes(-policyPark.PolicyPark_OutRecordFindMin.Value))
                        {
                            //出场时间在24小时以内,无需缴费，直接放行
                            result.passres.code = 1;
                            result.passres.errmsg = "无入场记录查找到最近记录，自动放行";
                            result.payres = new ChargeModels.PayResult()
                            {
                                payed = 0,
                                payedamount = 0,
                                orderamount = 0
                            };
                            isAutoNoenterRecord = true;
                            return;
                        }
                    }
                }
            }

            //无入场记录查找预入场记录
            var matchRet = MatchPendingPreIncar(policyPark, passway, ref result, true, true);
            if (matchRet) return;

            bool isGetFee = false;
            if (policyPark.PolicyPark_FeeNoRecord == 1)
            {
                DateTime start = result.time.Value.AddHours(-(policyPark.PolicyPark_FeeNoRecordTime ?? 0));
                StringBuilder selectWhere = new StringBuilder();
                selectWhere.Append($" ParkOrder_StatusNo='{Model.EnumParkOrderStatus.Out}'");
                selectWhere.Append($" AND ParkOrder_CarNo='{result.passres.carno}'");
                selectWhere.Append($" AND ParkOrder_OutTime between '{start.ToString("yyyy-MM-dd HH:mm:ss")}' and '{result.time.Value.ToString("yyyy-MM-dd HH:mm:ss")}'");
                var ggOrder = BLL.ParkOrder.GetAllEntity("*", selectWhere.ToString())?.OrderByDescending(x => x.ParkOrder_OutTime).FirstOrDefault();
                if (ggOrder != null)
                {
                    isGetFee = true;
                    var order = BLL.ParkOrder.CreateParkOrder(ggOrder.ParkOrder_ParkNo, ggOrder.ParkOrder_ParkAreaNo, ggOrder.ParkOrder_ParkAreaName, ggOrder.ParkOrder_CarNo, result.passres.carcardtype.CarCardType_No, result.passres.carcardtype.CarCardType_Name, result.passres.cartype.CarType_No, result.passres.cartype.CarType_Name, ggOrder.ParkOrder_OutTime.Value, ggOrder.ParkOrder_EnterPasswayNo, ggOrder.ParkOrder_EnterPasswayName);
                    order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                    order.ParkOrder_EnterImgPath = ggOrder.ParkOrder_EnterImgPath;
                    order.ParkOrder_EnterTime = ggOrder.ParkOrder_OutTime;

                    var detail = BLL.OrderDetail.CreateOrderDetail(order.ParkOrder_No, ggOrder.ParkOrder_ParkNo, ggOrder.ParkOrder_ParkAreaNo, ggOrder.ParkOrder_ParkAreaName, ggOrder.ParkOrder_CarNo, result.passres.carcardtype.CarCardType_No, result.passres.carcardtype.CarCardType_Name, result.passres.cartype.CarType_No, result.passres.cartype.CarType_Name, ggOrder.ParkOrder_OutTime.Value, ggOrder.ParkOrder_EnterPasswayNo, ggOrder.ParkOrder_EnterPasswayName);
                    detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                    detail.OrderDetail_EnterTime = ggOrder.ParkOrder_OutTime;

                    var details = new List<Model.OrderDetail>() { detail };

                    if (result.passres.car != null && result.passres?.owner != null && result.passres.carcardtype?.CarCardType_IsMoreCar == 1)
                    {
                        BLL.ParkOrder.ModifyAnyToChangeOrder(result.passres.car, result.passres.owner, result.passres.carcardtype, ref order, ref details);
                    }

                    result.resorder.resOut.onmachorder = 1;
                    result.resorder.resOut.noRecordOrder = order;
                    result.resorder.resOut.noRecordDetail = detail;

                    CalcBasicData baiseData = new CalcBasicData()
                    {
                        cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && result.isSupplement ? true : false
                    };

                    if (passway.Passway_IsCharge == 1)
                    {
                        var timeCoupon = BLL.CommonBLL.GetCouponAllTime(order);
                        if (timeCoupon.Item1 != null)
                        {
                            var cResult = BLL.CouponRecord._Insert(timeCoupon.Item1, timeCoupon.Item2);
                            if (cResult > 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{order.ParkOrder_CarNo}优惠券[{timeCoupon.Item1?.FirstOrDefault()?.CouponRecord_No}]自动发放成功");
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{order.ParkOrder_CarNo}优惠券自动发放失败");
                            }
                        }

                        //result.payres = Charge.Calc.GetChargeByCar(order, result.time, result.passres?.car, null, true, "", "", null, details, baiseData: baiseData);
                        result.payres = Calc.GetChargeByMin(order, result.time, 0, null, out var orderAllCouponList, true, "", "", detailList: details, baiseData: baiseData);
                    }
                    else
                    {
                        result.payres = new ChargeModels.PayResult()
                        {
                            payed = 0,
                            payedamount = 0,
                            orderamount = 0,
                            payedmsg = "无入场记录,车道未启用计费"
                        };
                    }
                    result.passres.lastFeeNoRecord = true;
                }
            }

            if (!isGetFee && resultData.resOut.code == 4)
            {
                if (passway.Passway_IsCharge == 1)
                {
                    result.passres.errmsg = "无入场记录";
                    result.payres = new ChargeModels.PayResult()
                    {
                        orderNo = result?.resorder?.resOut?.noRecordOrder?.ParkOrder_No,
                        payed = 1,
                        orderamount = Utils.StrToDecimal($"{resultData.resOut.minmoney}"),
                        payedamount = Utils.StrToDecimal($"{resultData.resOut.minmoney}")
                    };

                    result.payres.list = new List<ChargeModels.PayDetail>() {
                        new ChargeModels.PayDetail()
                        {
                            parkorderno = result?.resorder?.resOut?.noRecordOrder?.ParkOrder_No,
                            starttime = result?.resorder?.resOut?.noRecordOrder?.ParkOrder_EnterTime ?? result.recog?.CarRecog_Time.Value.AddMinutes(-(policyPark?.PolicyPark_NoRecordRangTime ?? 5)),
                            endtime = result?.resorder?.resOut?.noRecordOrder?.ParkOrder_OutTime ?? result.recog?.CarRecog_Time,
                            payed = 1,
                            payedamount = result.payres.payedamount,
                            areaname = BLL.PasswayLink.GetCurrentParkArea(result.passres?.passway?.Passway_No)?.ParkArea_Name ?? ""
                        }
                    };

                    #region 【每月最高限额】处理超额逻辑
                    {
                        if (result?.payres != null && !string.IsNullOrEmpty(result?.passres?.carcardtype?.CarCardType_No) && result.payres.payed != 2 && (result.payres.payedamount + result.payres.chuzhiamount + (result.payres.cashrobotamount - result.payres.zlamount)) > 0)
                        {
                            var cct1 = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, result.passres.carcardtype.CarCardType_No);
                            if (cct1 != null)
                            {
                                var policycct = BLL.PolicyCarCard.GetEntityByCarCard(cct1.CarCardType_No);

                                // 启用了每月最高收费限额
                                if (policycct != null && policycct.PolicyCarCard_MonthMaxMoney > 0)
                                {
                                    var parkOrder = result?.resorder?.resOut?.noRecordOrder ?? BLL.ParkOrder.CreateParkOrder(null, null,
                            null, result.passres.carno, result.passres.carcardtype.CarCardType_No, result.passres.carcardtype.CarCardType_Name,
                            result.passres.cartype.CarType_No, result.passres.cartype.CarType_Name, result.time.Value, null,
                            null);
                                    var payResult = result.payres;

                                    DateTime enterTime = parkOrder.ParkOrder_EnterTime.Value;
                                    DateTime exitTime = result.time.Value;

                                    // 获取 payResult.list 涉及的所有月份
                                    var monthKeys = payResult.list.Select(x => Utils.ObjectToInt(x.starttime.Value.ToString("yyyyMM"), 0)).Distinct().ToList();
                                    // 批量查询多个月份的 CarFees 记录
                                    List<Model.CarFees> carFeesRecords = null;
                                    carFeesRecords = BLL.BaseBLL._GetAllEntity(new Model.CarFees(),
                                        "CarFees_Money, CarFees_Month",
                                        $"CarFees_CarNo=@CarFees_CarNo AND CarFees_Month IN ({string.Join(",", monthKeys)})",
                                        new { CarFees_CarNo = parkOrder.ParkOrder_CarNo }
                                    );


                                    // 转换为字典，方便后续查找
                                    var carFeesDict = carFeesRecords.ToDictionary(
                                        x => x.CarFees_Month.Value,
                                        x => x.CarFees_Money.Value
                                    );

                                    // 获取所有停车明细按月分组
                                    var monthDetails = payResult.list
                                        .GroupBy(x => Utils.ObjectToInt(x.starttime.Value.ToString("yyyyMM"), 0))
                                        .ToDictionary(g => g.Key, g => g.OrderBy(y => y.starttime).ToList());
                                    decimal totalExcessAmount = 0;
                                    decimal monthMaxLimit = policycct.PolicyCarCard_MonthMaxMoney.Value;

                                    foreach (var month in monthDetails.Keys)
                                    {
                                        var monthList = monthDetails[month];

                                        // 历史已缴费金额
                                        decimal monthTotalPaid = carFeesDict.ContainsKey(month) ? carFeesDict[month] : 0;
                                        decimal nowTotalPaid = monthList.Sum(x => x.payedamount ?? 0);

                                        monthList.First().CalcResult += $"<br/><br/>{month.ToString().Substring(0, 4)} 年 {month.ToString().Substring(4, 2)} 月，历史累计费用{monthTotalPaid}元，月限额{monthMaxLimit}元。";

                                        decimal originalMonthTotal = monthTotalPaid;

                                        decimal excessAmount = monthTotalPaid + nowTotalPaid - monthMaxLimit;
                                        decimal runningPaidSum = 0; // 累加已处理过的 payedamount

                                        if (excessAmount > 0)
                                        {
                                            decimal totalReduced = 0;
                                            decimal runningTotalPaid = originalMonthTotal;

                                            for (int i = 0; i < monthList.Count; i++)
                                            {
                                                var item = monthList[i];
                                                decimal oriPay = item.payedamount ?? 0;
                                                if (oriPay <= 0) continue;

                                                // 展示累计值 = 历史 + 当前原始金额 + 前面所有已计费金额
                                                decimal displayRunningTotal = originalMonthTotal + oriPay + runningPaidSum;

                                                decimal overLimit = displayRunningTotal - monthMaxLimit;

                                                if (overLimit >= oriPay)
                                                {
                                                    item.carfeesamount += oriPay;
                                                    item.payedamount = 0;
                                                    item.NextCyclePaidFees = 0;
                                                    item.CalcResult += $"<br/><br/>本月累计费用{displayRunningTotal}元，超出限额，减免{oriPay}元，实际计费金额为：0元。";
                                                    totalReduced += oriPay;
                                                }
                                                else if (overLimit > 0)
                                                {
                                                    decimal realPay = oriPay - overLimit;
                                                    item.payedamount = realPay;
                                                    item.carfeesamount += overLimit;
                                                    item.NextCyclePaidFees -= overLimit;
                                                    if (item.NextCyclePaidFees < 0) item.NextCyclePaidFees = 0;

                                                    item.CalcResult += $"<br/><br/>本月累计费用{displayRunningTotal}元，超出限额，减免{overLimit}元，实际计费金额为：{realPay}元。";
                                                    totalReduced += overLimit;
                                                }
                                                else
                                                {
                                                    item.CalcResult += $"<br/><br/>本月累计费用{displayRunningTotal}元，未超出限额，实际计费金额为：{oriPay}元。";
                                                }

                                                // 更新累计实际支付金额（用于下一次计算）
                                                runningPaidSum += item.payedamount ?? 0;
                                            }


                                            totalExcessAmount += totalReduced;
                                        }
                                    }


                                    // 更新 payResult 的最终支付金额

                                    payResult.orderamount -= totalExcessAmount;

                                    if (carFeesDict.Count > 0)
                                    {
                                        var last10Months = carFeesDict
                                                        .OrderByDescending(x => x.Key)          // 按月份倒序
                                                        .Take(5)                               // 取最近5个月
                                                        .OrderBy(x => x.Key)                    // 再按月份升序排回去
                                                        .ToList();

                                        var descriptions = last10Months
                                            .Select(x => $"[{x.Key.ToString().Substring(0, 4)}年{x.Key.ToString().Substring(4, 2)}月历史累计{x.Value}元]")
                                            .ToList();

                                        var finalDescription = string.Join(" ", descriptions); // 用逗号连接
                                        payResult.payedmsg = finalDescription;
                                    }

                                    decimal remainingAmount = totalExcessAmount;
                                    if (payResult.payedamount >= remainingAmount)
                                    {
                                        payResult.payedamount -= remainingAmount;
                                        payResult.carfeesamount = totalExcessAmount;
                                    }
                                    else
                                    {
                                        payResult.carfeesamount = payResult.payedamount;
                                        remainingAmount -= payResult.payedamount;
                                        payResult.payedamount = 0;

                                        if (payResult.chuzhiamount >= remainingAmount)
                                        {
                                            payResult.chuzhiamount -= remainingAmount;
                                            payResult.carfeesamount += remainingAmount;
                                        }
                                        else
                                        {
                                            payResult.carfeesamount += payResult.chuzhiamount;
                                            payResult.chuzhiamount = 0;
                                        }
                                    }

                                    if (payResult.payedamount == 0 && payResult.chuzhiamount == 0)
                                    {
                                        payResult.payed = 0;
                                    }
                                }
                            }

                        }
                    }
                    #endregion

                    var cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && (result?.isSupplement ?? false) ? true : false;
                    if (!cloudMqttOffline) result.payres = Calc.BusiCarMode(result?.payres, result?.passres?.car, result.passres?.owner, result?.passres?.carcardtype);
                }
                else
                {
                    result.payres = new ChargeModels.PayResult()
                    {
                        payed = 0,
                        payedamount = 0,
                        orderamount = 0,
                        payedmsg = "无入场记录,车道未启用计费"
                    };
                }
            }

            if (result.payres != null)
            {
                if ((result.payres.payed == 0 || result.payres.payed == 1) && result.payres.payedamount <= 0)
                {
                    result.passres.code = policyPass.PolicyPass_Pass;
                }
                else if (result.payres.payedamount > 0)
                {
                    if (!isGetFee)
                    {
                        //如果产生费用且放行方式是自动放行，则变更为弹窗放行
                        result.passres.code = result.passres.code == 1 ? 2 : result.passres.code;
                        result.passres.errmsg = $"收费{result.payres.payedamount}元";
                    }
                    else
                    {
                        result.passres.errmsg = $"无入场匹配记录|收费{result.payres.payedamount}元";
                        result.passres.code = (result.passres.code == 1 || result.passres.code == 4) ? 2 : result.passres.code;
                    }
                }
            }
            else
            {
                result.payres = new ChargeModels.PayResult();
                result.payres.payed = 0;
            }
        }

        /// <summary>
        /// 无入场记录查找预入场记录
        /// </summary>
        /// <param name="policyPark"></param>
        /// <param name="result"></param>
        /// <param name="passway"></param>
        /// <returns>true - 匹配到记录</returns>
        public static bool MatchPendingPreIncar(Model.PolicyPark policyPark, Model.Passway passway, ref Model.ResultPass result, bool createParkOrder = true, bool writeDB = false)
        {
            if (result.resorder != null && result.resorder.resOut != null && result.resorder.resOut.onenter == 0 && policyPark.PolicyPark_NoEnterRecordSearch == 1)
            {
                var incar = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "*", $"InCar_CarNo='{result.passres.carno}' and InCar_Status=199");
                if (incar != null)
                {
                    if (!createParkOrder) return true;

                    var parkarea = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, incar.InCar_ParkAreaNo);
                    var carrecog = BLL.CarRecog.GetInstance(incar.InCar_EnterTime.Value).GetEntity("CarRecog_PasswayNo", $"CarRecog_CarNo='{result.passres.carno}' and CarRecog_Time='{incar.InCar_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}'");
                    if (carrecog == null)
                    {
                        carrecog = new Model.CarRecog();
                        var pass = BLL.PasswayLink.GetAllEntity("*", $"PasswayLink_ParkAreaNo='{parkarea?.ParkArea_No}'");
                        foreach (var p in pass)
                        {
                            int gate = BLL.Passway.GetPasswayGateType(p.PasswayLink_PasswayNo);
                            if (gate == 1)
                            {
                                carrecog.CarRecog_PasswayNo = p.PasswayLink_PasswayNo;
                                break;
                            }
                        }
                    }

                    if (passway != null)
                    {
                        var oldOrder = BLL.ParkOrder.GetEntity(incar.InCar_ParkOrderNo);

                        var order = BLL.ParkOrder.CreateParkOrder(AppBasicCache.GetParking?.Parking_No, parkarea.ParkArea_No, parkarea.ParkArea_Name, result.passres.carno, result.passres.carcardtype.CarCardType_No, result.passres.carcardtype.CarCardType_Name, result.passres.cartype.CarType_No, result.passres.cartype.CarType_Name, incar.InCar_EnterTime.Value, passway.Passway_No, passway.Passway_Name);
                        order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                        order.ParkOrder_EnterTime = incar.InCar_EnterTime;
                        order.ParkOrder_OutTime = result.time;

                        if (oldOrder != null)
                        {
                            order.ParkOrder_EnterTime = oldOrder.ParkOrder_EnterTime;
                            order.ParkOrder_EnterAdminAccount = oldOrder.ParkOrder_EnterAdminAccount;
                            order.ParkOrder_EnterAdminName = oldOrder.ParkOrder_EnterAdminName;
                            order.ParkOrder_EnterImg = oldOrder.ParkOrder_EnterImg;
                            order.ParkOrder_EnterImgPath = oldOrder.ParkOrder_EnterImgPath;
                            order.ParkOrder_EnterPasswayName = oldOrder.ParkOrder_EnterPasswayName;
                            order.ParkOrder_EnterPasswayNo = oldOrder.ParkOrder_EnterPasswayNo;
                            order.ParkOrder_EnterRemark = oldOrder.ParkOrder_EnterRemark;
                        }

                        var detail = BLL.OrderDetail.CreateOrderDetail(order.ParkOrder_No, order.ParkOrder_ParkNo, order.ParkOrder_ParkAreaNo, order.ParkOrder_ParkAreaName, order.ParkOrder_CarNo, result.passres.carcardtype.CarCardType_No, result.passres.carcardtype.CarCardType_Name, result.passres.cartype.CarType_No,
                            result.passres.cartype.CarType_Name, order.ParkOrder_EnterTime.Value, order.ParkOrder_EnterPasswayNo, order.ParkOrder_EnterPasswayName);
                        detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                        detail.OrderDetail_EnterAdminAccount = order.ParkOrder_EnterAdminAccount;
                        detail.OrderDetail_EnterAdminName = order.ParkOrder_EnterAdminName;
                        detail.OrderDetail_EnterImg = order.ParkOrder_EnterImg;
                        detail.OrderDetail_EnterImgPath = order.ParkOrder_EnterImgPath;
                        detail.OrderDetail_EnterPasswayName = order.ParkOrder_EnterPasswayName;
                        detail.OrderDetail_EnterPasswayNo = order.ParkOrder_EnterPasswayNo;
                        detail.orderdetail_EnterRemark = order.ParkOrder_EnterRemark;

                        var details = new List<Model.OrderDetail>() { detail };

                        if (result.passres.car != null && result.passres?.owner != null && result.passres.carcardtype?.CarCardType_IsMoreCar == 1)
                        {
                            BLL.ParkOrder.ModifyAnyToChangeOrder(result.passres.car, result.passres.owner, result.passres.carcardtype, ref order, ref details);
                        }

                        CalcBasicData baiseData = new CalcBasicData()
                        {
                            cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && result.isSupplement ? true : false
                        };

                        // result.payres = Charge.Calc.GetChargeByCar(order, result.time, result.passres.car, null, false, "", "", null, details);
                        result.payres = Calc.GetChargeByMin(order, result.time, 0, null, out var orderAllCouponList, true, "", "", detailList: details, baiseData: baiseData);
                        result.passres.lastFeeNoRecord = true;

                        if (writeDB)
                        {
                            var closeOrder = BLL.ParkOrder.GetEntity(incar.InCar_ParkOrderNo);
                            closeOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                            closeOrder.ParkOrder_Remark = "无入场记录出场，关闭预入场记录";
                            result.resorder.resOut.closeOrder = closeOrder;

                            order.ParkOrder_Remark = "无入场匹配预入场记录";
                            var r = BLL.OrderDetail.UpdateByList(new List<Model.ParkOrder> { closeOrder, order }, details);
                            if (!r)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{order?.ParkOrder_CarNo}查找预入场记录写入订单失败!!!");
                            }
                        }

                        if (result.payres != null)
                        {
                            if (writeDB)
                            {
                                result.resorder.resOut.onmachorder = 0;
                                result.resorder.resOut.onenter = 1;
                                result.passres.noEnterRecordSearch = false;
                                result.resorder.resOut.parkorder = order;
                                result.resorder.resOut.orderDetail = details.FirstOrDefault();
                            }

                            if ((result.payres.payed == 0 || result.payres.payed == 1) && result.payres.payedamount <= 0)
                            {
                                if (!writeDB)
                                {
                                    result.resorder.resOut.onmachorder = 1;
                                    result.passres.noEnterRecordSearch = true;
                                }
                                result.resorder.resOut.noRecordOrder = order;
                                result.resorder.resOut.noRecordDetail = details.FirstOrDefault();
                                result.passres.code = 1;
                                result.passres.errmsg = $"无入场匹配预入场记录";
                                return true;
                            }
                            else if (result.payres.payedamount > 0)
                            {
                                if (!writeDB)
                                {
                                    result.resorder.resOut.onmachorder = 1;
                                    result.passres.noEnterRecordSearch = true;
                                }
                                result.resorder.resOut.noRecordOrder = order;
                                result.resorder.resOut.noRecordDetail = details.FirstOrDefault();
                                result.passres.code = 2;
                                result.passres.errmsg = $"无入场匹配预入场记录|收费{result.payres.payedamount}元";
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 无入场记录计算费用
        /// </summary>
        /// <param name="parkno">车场编号</param>
        /// <param name="carno">车牌号</param>
        /// <param name="time">出场时间</param>
        /// <param name="passwayno">车道编号</param>
        /// <returns></returns>
        public static ChargeModels.PayResult FeeNoRecord2(string parkno, string carno, DateTime time, out List<Model.CouponRecordIntExt> orderAllCouponList,
            string passwayno = null, Model.Car car = null, Model.ResultPass result = null, decimal? freeMinutes = 0, bool isOutGateOrder = false, bool isFllowCar = false,
            Model.ParkOrder po = null, List<Model.OrderDetail> detilList = null, string allTimeFree = null, bool isCouponCharge = false)
        {
            orderAllCouponList = null;
            ChargeModels.PayResult payres = new ChargeModels.PayResult { payed = 2, payedmsg = "计费失败" };
            bool isGetFee = false;

            if (!isCouponCharge)
            {
                Model.PolicyPark policyPark = BLL.PolicyPark.GetEntity(parkno);
                if (policyPark.PolicyPark_FeeNoRecord == 1)
                {
                    if (po == null)
                    {
                        DateTime start = time.AddHours(-(policyPark.PolicyPark_FeeNoRecordTime ?? 0));
                        StringBuilder selectWhere = new StringBuilder();
                        selectWhere.Append($" ParkOrder_StatusNo='{Model.EnumParkOrderStatus.Out}'");
                        selectWhere.Append($" AND ParkOrder_CarNo='{carno}'");
                        selectWhere.Append($" AND ParkOrder_OutTime between '{start.ToString("yyyy-MM-dd HH:mm:ss")}' and '{time.ToString("yyyy-MM-dd HH:mm:ss")}'");
                        var ggOrder = BLL.ParkOrder.GetAllEntity("*", selectWhere.ToString())?.OrderByDescending(x => x.ParkOrder_OutTime).FirstOrDefault();
                        if (ggOrder != null)
                        {
                            //Model.CarCardType cct = null; Model.CarType ct = null;
                            //if (result == null && !string.IsNullOrEmpty(passwayno))
                            //{
                            //    Model.PolicyPassway policyPassway = BLL.PolicyPassway.GetEntity("*", $"PolicyPassway_PasswayNo='{passwayno}'");
                            //    cct = BLL.CarCardType.GetEntity(policyPassway?.PolicyPassway_DefaultCarCardType ?? "");
                            //    ct = BLL.CarType.GetEntity(policyPassway?.PolicyPassway_DefaultCarType ?? "");
                            //}

                            isGetFee = true;
                            //创建无入场停车订单
                            var order = BLL.ParkOrder.CreateParkOrder(ggOrder.ParkOrder_ParkNo, ggOrder.ParkOrder_ParkAreaNo, ggOrder.ParkOrder_ParkAreaName, ggOrder.ParkOrder_CarNo,
                                result?.passres.carcardtype.CarCardType_No ?? ggOrder.ParkOrder_CarCardType,
                                result?.passres.carcardtype.CarCardType_Name ?? ggOrder.ParkOrder_CarCardTypeName,
                                result?.passres.cartype.CarType_No ?? ggOrder.ParkOrder_CarType,
                                result?.passres.cartype.CarType_Name ?? ggOrder.ParkOrder_CarTypeName,
                                ggOrder.ParkOrder_OutTime.Value, ggOrder.ParkOrder_EnterPasswayNo, ggOrder.ParkOrder_EnterPasswayName);
                            order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                            order.ParkOrder_EnterImgPath = ggOrder.ParkOrder_EnterImgPath;
                            //创建无入场订单明细
                            var detail = BLL.OrderDetail.CreateOrderDetail(order.ParkOrder_No, ggOrder.ParkOrder_ParkNo, ggOrder.ParkOrder_ParkAreaNo, ggOrder.ParkOrder_ParkAreaName, ggOrder.ParkOrder_CarNo,
                                result?.passres.carcardtype.CarCardType_No ?? ggOrder.ParkOrder_CarCardType,
                                result?.passres.carcardtype.CarCardType_Name ?? ggOrder.ParkOrder_CarCardTypeName,
                                result?.passres.cartype.CarType_No ?? ggOrder.ParkOrder_CarType,
                                result?.passres.cartype.CarType_Name ?? ggOrder.ParkOrder_CarTypeName,
                                ggOrder.ParkOrder_OutTime.Value, ggOrder.ParkOrder_EnterPasswayNo, ggOrder.ParkOrder_EnterPasswayName);
                            detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;

                            var details = new List<Model.OrderDetail>() { detail };

                            if (result?.passres?.car != null && result.passres?.owner != null && result.passres.carcardtype?.CarCardType_IsMoreCar == 1)
                            {
                                BLL.ParkOrder.ModifyAnyToChangeOrder(result.passres.car, result.passres.owner, result.passres.carcardtype, ref order, ref details);
                            }
                            //计费
                            //payres = Charge.Calc.GetChargeByCar(order, time, null, null, false, "", "", null, details);
                            CalcBasicData baiseData = new CalcBasicData()
                            {
                                cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && (result?.isSupplement ?? false) ? true : false
                            };
                            payres = Calc.GetChargeByMin(order, time, freeMinutes, car, out orderAllCouponList, true, "", "", isOutGateOrder, isFllowCar, detailList: details, baiseData: baiseData, allTimeFree: allTimeFree);
                            return payres;
                        }
                    }
                    else
                    {
                        CalcBasicData baiseData = new CalcBasicData()
                        {
                            cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && (result?.isSupplement ?? false) ? true : false
                        };

                        payres = Calc.GetChargeByMin(po, time, 0, null, out orderAllCouponList, true, "", "", detailList: detilList, baiseData: baiseData, allTimeFree: allTimeFree);
                        //payres = Calc.GetChargeByMin(po, time, freeMinutes, car, out orderAllCouponList, true, "", "", isOutGateOrder, isFllowCar, detailList: detilList, baiseData: baiseData);
                        return payres;
                    }
                }
            }

            if (!isGetFee)
            {
                if (string.IsNullOrEmpty(passwayno))
                {
                    payres.payed = 2;
                    payres.payedmsg = "无入场记录,请在出口缴费";
                    return payres;
                }
                else
                {
                    var policyPassway = BLL.PolicyPassway.GetEntityByPasswayNo(passwayno);
                    if (policyPassway == null)
                    {
                        payres.payedmsg = "未找到默认车牌类型";
                        return payres;
                    }
                    Model.CarCardType carCardType = null;

                    if (car == null) { car = PassHelperBiz.MatchCar(carno, 1); }
                    if (carCardType == null) carCardType = PassHelperBiz.GetCarCardType(null, car, null, policyPassway);

                    //放行策略
                    Model.PolicyPass policyPass = BLL.PolicyPass.GetEntityByWayAndCardType(passwayno, carCardType?.CarCardType_No);
                    if (policyPass == null)
                    {
                        payres.payedmsg = "未找到无入场记录开闸方式";
                        return payres;
                    }


                    switch (policyPass.PolicyPass_NoFundEnter)
                    {
                        case 0:
                            payres.payed = 2;
                            payres.payedmsg = "无入场记录,禁止通行";
                            break;
                        case 1:
                            payres.payed = 1;
                            payres.payedamount = 0;
                            payres.orderamount = 0;
                            payres.payedmsg = "";
                            break;
                        case 2:
                            payres.payed = 2;
                            payres.payedmsg = "无入场记录,请到出口由管理员确认放行";
                            break;
                        case 4:
                            if (isCouponCharge)
                            {
                                CalcBasicData baiseData = new CalcBasicData()
                                {
                                    cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && (result?.isSupplement ?? false) ? true : false
                                };

                                if (detilList == null)
                                {
                                    //创建无入场订单明细
                                    var detail = BLL.OrderDetail.CreateOrderDetail(po.ParkOrder_No, po.ParkOrder_ParkNo, po.ParkOrder_ParkAreaNo, po.ParkOrder_ParkAreaName,
                                        po.ParkOrder_CarNo,
                                        po.ParkOrder_CarCardType,
                                        po.ParkOrder_CarCardTypeName,
                                        po.ParkOrder_CarType,
                                        po.ParkOrder_CarTypeName,
                                        po.ParkOrder_EnterTime.Value, po.ParkOrder_EnterPasswayNo, po.ParkOrder_EnterPasswayName);
                                    detilList = new List<Model.OrderDetail>() { detail };
                                }

                                payres = Calc.GetChargeByMin(po, time, 0, null, out orderAllCouponList, true, "", "", detailList: detilList, baiseData: baiseData);
                            }
                            else
                            {
                                payres.payed = 1;
                                payres.payedamount = (decimal)(policyPass.PolicyPass_MinAmount ?? 0);
                                payres.orderamount = payres.payedamount;
                                payres.payedmsg = "无入场记录,最低收费";
                            }
                            break;
                    }
                    return payres;
                }
            }

            return payres;
        }


        /// <summary>
        /// 无入场记录计算费用
        /// </summary>
        /// <param name="parkno">车场编号</param>
        /// <param name="carno">车牌号</param>
        /// <param name="time">出场时间</param>
        /// <param name="passwayno">车道编号</param>
        /// <returns></returns>
        public static ChargeModels.PayResult FeeNoRecord3(Model.ParkOrder parkOrder, DateTime time, out List<Model.CouponRecordIntExt> orderAllCouponList, string passwayno = null, Model.Car car = null, Model.ResultPass result = null, decimal? freeMinutes = 0, bool isOutGateOrder = false, bool isFllowCar = false)
        {
            orderAllCouponList = null;
            ChargeModels.PayResult payres = new ChargeModels.PayResult { payed = 2, payedmsg = "计费失败" };

            Model.PolicyPark policyPark = BLL.PolicyPark.GetEntity(parkOrder.ParkOrder_ParkNo);
            if (policyPark.PolicyPark_FeeNoRecord == 1)
            {

                DateTime start = time.AddHours(-(policyPark.PolicyPark_FeeNoRecordTime ?? 0));
                StringBuilder selectWhere = new StringBuilder();
                selectWhere.Append($" ParkOrder_StatusNo='{Model.EnumParkOrderStatus.Out}'");
                selectWhere.Append($" AND ParkOrder_CarNo='{parkOrder.ParkOrder_CarNo}'");
                selectWhere.Append($" AND ParkOrder_OutTime between '{start.ToString("yyyy-MM-dd HH:mm:ss")}' and '{time.ToString("yyyy-MM-dd HH:mm:ss")}'");
                var ggOrder = BLL.ParkOrder.GetAllEntity("*", selectWhere.ToString())?.OrderByDescending(x => x.ParkOrder_OutTime).FirstOrDefault();
                if (ggOrder != null)
                {
                    //Model.CarCardType cct = null; Model.CarType ct = null;
                    //if (result == null && !string.IsNullOrEmpty(passwayno))
                    //{
                    //    Model.PolicyPassway policyPassway = BLL.PolicyPassway.GetEntity("*", $"PolicyPassway_PasswayNo='{passwayno}'");
                    //    cct = BLL.CarCardType.GetEntity(policyPassway?.PolicyPassway_DefaultCarCardType ?? "");
                    //    ct = BLL.CarType.GetEntity(policyPassway?.PolicyPassway_DefaultCarType ?? "");
                    //}

                    //创建无入场停车订单
                    var order = BLL.ParkOrder.CreateParkOrder(ggOrder.ParkOrder_ParkNo, ggOrder.ParkOrder_ParkAreaNo, ggOrder.ParkOrder_ParkAreaName, ggOrder.ParkOrder_CarNo,
                        result?.passres.carcardtype.CarCardType_No ?? ggOrder.ParkOrder_CarCardType,
                        result?.passres.carcardtype.CarCardType_Name ?? ggOrder.ParkOrder_CarCardTypeName,
                        result?.passres.cartype.CarType_No ?? ggOrder.ParkOrder_CarType,
                        result?.passres.cartype.CarType_Name ?? ggOrder.ParkOrder_CarTypeName,
                        ggOrder.ParkOrder_OutTime.Value, ggOrder.ParkOrder_EnterPasswayNo, ggOrder.ParkOrder_EnterPasswayName);
                    order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                    order.ParkOrder_EnterImgPath = ggOrder.ParkOrder_EnterImgPath;
                    //创建无入场订单明细
                    var detail = BLL.OrderDetail.CreateOrderDetail(order.ParkOrder_No, ggOrder.ParkOrder_ParkNo, ggOrder.ParkOrder_ParkAreaNo, ggOrder.ParkOrder_ParkAreaName, ggOrder.ParkOrder_CarNo,
                        result?.passres.carcardtype.CarCardType_No ?? ggOrder.ParkOrder_CarCardType,
                        result?.passres.carcardtype.CarCardType_Name ?? ggOrder.ParkOrder_CarCardTypeName,
                        result?.passres.cartype.CarType_No ?? ggOrder.ParkOrder_CarType,
                        result?.passres.cartype.CarType_Name ?? ggOrder.ParkOrder_CarTypeName,
                        ggOrder.ParkOrder_OutTime.Value, ggOrder.ParkOrder_EnterPasswayNo, ggOrder.ParkOrder_EnterPasswayName);
                    detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;

                    var details = new List<Model.OrderDetail>() { detail };

                    if (result?.passres?.car != null && result.passres?.owner != null && result.passres.carcardtype?.CarCardType_IsMoreCar == 1)
                    {
                        BLL.ParkOrder.ModifyAnyToChangeOrder(result.passres.car, result.passres.owner, result.passres.carcardtype, ref order, ref details);
                    }
                    //计费
                    //payres = Charge.Calc.GetChargeByCar(order, time, null, null, false, "", "", null, details);
                    CalcBasicData baiseData = new CalcBasicData()
                    {
                        cloudMqttOffline = AppSettingConfig.SentryMode == VersionEnum.CloudServer && (result?.isSupplement ?? false) ? true : false
                    };
                    payres = Calc.GetChargeByMin(order, time, freeMinutes, car, out orderAllCouponList, true, "", "", isOutGateOrder, isFllowCar, detailList: details, baiseData: baiseData);
                    return payres;
                }
            }
            else
            {
                payres = Calc.GetChargeByMin(parkOrder, time, freeMinutes, car, out orderAllCouponList, true, "", "", isOutGateOrder, isFllowCar);
            }
            return payres;
        }

        /// <summary>
        /// 获取欠费出场订单的费用
        /// </summary>
        /// <param name="carno">车牌号</param>
        /// <param name="unPaidOrder">返回未缴费的订单信息</param>
        /// <param name="passwayno">车道编号 - 无入场记录计费用</param>
        /// <returns></returns>
        public static List<Model.UnPaidResult> GetPayByUnpaidOrder(string carno, string passwayno, Model.Owner owner, Model.Car car, Model.ResultPass result = null)
        {
            List<Model.ParkOrder> orders = null;
            List<Model.UnPaidResult> data = new List<Model.UnPaidResult>();

            List<Model.ControlEvent> ceList = BLL.ControlEvent._GetAllEntity(new Model.ControlEvent(), "ControlEvent_ParkOrderNo", $"ControlEvent_CarNo='{carno}' and ControlEvent_Status=2 and ControlEvent_Type=1 and ControlEvent_Gate=0 and ControlEvent_ParkOrderNo is not NULL ");
            if (ceList != null && ceList.Count > 0)
            {
                orders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in ('{string.Join("','", ceList.Select(x => x.ControlEvent_ParkOrderNo))}')"); //ParkOrder_CarNo='{carno}'
                orders = orders.Where(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow || x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close).ToList();
            }
            if (orders != null && orders.Count > 0)
            {

                foreach (var order in orders)
                {
                    var resItem = new Model.UnPaidResult();
                    resItem.unpaidorder = order;

                    DateTime outTime = order.ParkOrder_OutTime == null ? DateTimeHelper.GetNowTime() : order.ParkOrder_OutTime.Value;
                    if (order.ParkOrder_IsNoInRecord == 0)
                    {
                        var details = BLL.OrderDetail.GetAllEntity(order.ParkOrder_No);

                        resItem.unpaiddetails = details;
                        resItem.payres = Charge.Calc.GetChargeByCar(order, outTime, car, null, false, "", "", owner, details, true);
                    }
                    else
                    {
                        List<Model.CouponRecordIntExt> orderCouponList = null;



                        resItem.payres = resItem.payres = FeeNoRecord3(order, outTime, out orderCouponList, (!string.IsNullOrEmpty(order.ParkOrder_OutPasswayNo) ? order.ParkOrder_OutPasswayNo : passwayno), car, result);
                    }

                    data.Add(resItem);
                }
            }

            if (result != null && result.passres != null && result.passres.gate == 0 && AppBasicCache.GetPolicyPark?.PolicyPark_EnbleOverdueBill == 1)
            {
                var obills = BLL.BaseBLL._GetAllEntity(new Model.OverdueBill(), "OverdueBill_No,OverdueBill_Tag,OverdueBill_Money",
                    $"OverdueBill_CarNo='{carno}' and OverdueBill_Status=0 and OverdueBill_HandleTime>'{DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd HH:mm:ss")}'");

                decimal? money = obills?.Sum(x => x.OverdueBill_Money);

                if (money > 0)
                {
                    data.Add(new UnPaidResult()
                    {
                        payres = new ChargeModels.PayResult()
                        {
                            payed = 1,
                            carBillNo = string.Join(",", obills.Select(x => x.OverdueBill_No)),
                            orderamount = money.Value,
                            payedamount = money.Value,
                            payedmsg = "第三方账单追缴",
                        }
                    });
                }
            }

            if (data.Count == 0) return null;

            return data;
        }

        /// <summary>
        /// 预缴金额 结算处理
        /// </summary>
        /// <param name="parkOrderNo">停车订单号</param>
        public static void settlePaymethod(string parkOrderNo, out List<Model.Paymethod> paymethods)
        {
            if (string.IsNullOrEmpty(parkOrderNo)) { paymethods = null; return; }

            paymethods = BLL.BaseBLL._GetAllEntity(new Model.Paymethod(), "*", $"Paymethod_OrderNo='{parkOrderNo}'");
            paymethods?.RemoveAll(x => x.Paymethod_Status != 0);
            if (paymethods?.Count > 0)
            {
                paymethods?.ForEach(x =>
                {
                    x.Paymethod_Status = 1;
                });
            }

        }

        /// <summary>
        /// 收费0元开闸记录处理
        /// </summary>
        public static Model.AbnorOrder AbnorOrder(Model.ParkOrder order, ChargeModels.PayResult payResult)
        {
            Model.AbnorOrder model = null;
            if (payResult.orderamount != 0 && payResult.payedamount < payResult.orderamount)
            {
                int abnorOrderType = Model.EnumOpenType.NoEnterRecord;
                int abnorOrderOpenReason = Model.EnumOpenReason.TempAutoRelease;
                if (payResult.couponamount > 0)
                {
                    abnorOrderType = Model.EnumOpenType.ChargeChange;
                    abnorOrderOpenReason = Model.EnumOpenReason.OfflineCoupon;
                }

                model = new Model.AbnorOrder
                {
                    AbnorOrder_No = Utils.CreateNumberWith(),
                    AbnorOrder_ParkNo = order.ParkOrder_ParkNo,
                    AbnorOrder_OrderNo = order.ParkOrder_No,
                    AbnorOrder_CarNo = order.ParkOrder_CarNo,
                    AbnorOrder_CardNo = order.ParkOrder_CarCardType,
                    AbnorOrder_CardCategory = 0,
                    AbnorOrder_CardName = order.ParkOrder_CarCardTypeName,
                    AbnorOrder_CarTypeNo = order.ParkOrder_CarType,
                    AbnorOrder_CarTypeName = order.ParkOrder_CarTypeName,
                    AbnorOrder_Time = order.ParkOrder_OutTime,
                    AbnorOrder_Gate = 0,
                    AbnorOrder_EnterTime = order.ParkOrder_EnterTime,
                    AbnorOrder_EntGateName = order.ParkOrder_EnterPasswayName,
                    AbnorOrder_EntOperatorName = order.ParkOrder_EnterAdminAccount,
                    AbnorOrder_EntImgPath = order.ParkOrder_EnterImgPath,
                    AbnorOrder_OutTime = order.ParkOrder_OutTime,
                    AbnorOrder_PasswayNo = order.ParkOrder_OutPasswayNo,
                    AbnorOrder_OutGateName = order.ParkOrder_EnterPasswayName,
                    AbnorOrder_OutOperatorName = order.ParkOrder_OutAdminAccount,
                    AbnorOrder_OutImgPath = order.ParkOrder_OutImgPath,
                    AbnorOrder_Money = payResult.payedamount,
                    AbnorOrder_TotalAmount = payResult.orderamount,
                    AbnorOrder_Remark = payResult.payedmsg,
                    AbnorOrder_Type = abnorOrderType,
                    AbnorOrder_OpenReason = abnorOrderOpenReason
                };
            }

            return model;
        }
        #endregion

        #region 入场条件判断
        /// <summary>
        /// 判断车位是否已满
        /// </summary>
        public static Model.ResultInOut IsFullSpace(
            Model.ParkCarInOut CarObj
            , Model.ParkArea oArea
            , Model.CarCardType carCardType
            , Model.PolicyPass policyPass
            , Model.PolicyArea policyArea
            , Model.PolicyPark policyPark
            , int? code = 2,
            List<Model.CarCardType> cctList = null)
        {
            var resIn = new Model.ResultInOut() { code = code };
            string Parking_No = oArea.ParkArea_ParkNo;

            if (policyArea == null || carCardType == null) return resIn;

            #region 车位已满
            //统计临时车车位，当前车辆不是临时车，则无需计算车位已满
            if (policyArea.PolicyArea_SpaceMode == 1 &&
                carCardType.CarCardType_Type != 1 &&
                carCardType.CarCardType_Type != 5 &&
                carCardType.CarCardType_Type != 6) { return resIn; }

            //统计固定车车位，当前车辆不是固定车，则无需计算车位已满
            if (policyArea.PolicyArea_SpaceMode == 2 &&
                (carCardType.CarCardType_Type == 1 ||
                carCardType.CarCardType_Type == 5 ||
                carCardType.CarCardType_Type == 6)) { return resIn; }


            bool isCheckSpace = false;//是否需要跑车位满位判断逻辑
            if (policyPass.PolicyPass_SpaceFull != 1)
            {
                isCheckSpace = true;
            }

            if (isCheckSpace)
            {
                List<string> cardnos = null;
                if (policyArea.PolicyArea_SpaceMode != 0)
                {
                    List<Model.CarCardType> cards = null;
                    cards = cctList?.FindAll(x => x.CarCardType_ParkNo == Parking_No);
                    if (cards == null || cards.Count == 0) cards = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Type,CarCardType_Category", $"CarCardType_ParkNo='{Parking_No}'");

                    if (policyArea.PolicyArea_SpaceMode == 1)
                        cardnos = cards?.FindAll(x => x.CarCardType_Type == 1 || x.CarCardType_Type == 5 || x.CarCardType_Type == 6)?.Select(x => x.CarCardType_No)?.ToList();
                    else if (policyArea.PolicyArea_SpaceMode == 2)
                        cardnos = cards?.FindAll(x => x.CarCardType_Type != 1 && x.CarCardType_Type != 5 && x.CarCardType_Type != 6)?.Select(x => x.CarCardType_No)?.ToList();
                }

                int totalSpace = policyArea.PolicyArea_SpaceMode == 0 ? oArea.ParkArea_SpaceNum.Value : policyArea.PolicyArea_SpaceNumber.Value;
                List<string> freeCarNoList = null;

                policyPark = policyPark ?? BLL.PolicyPark.GetEntity(Parking_No);
                DateTime? disTime = null;
                //排除自动释放车位的订单
                if (policyPark.PolicyPark_OccupyDay > 0)
                    disTime = Utils.StrToDateTime(DateTimeHelper.GetNowTime().AddDays(-policyPark.PolicyPark_OccupyDay.Value).ToString("yyyy-MM-dd 00:00:00"));

                int inCount = 0;//场内车辆数
                List<Model.SpaceModel> inParkOrders = MonitorHelperBiz.getSpaceList(disTime, oArea.ParkArea_No, cardnos);
                if (inParkOrders != null && inParkOrders.Count > 0)
                {
                    inCount = inParkOrders.Sum(x => x.Num).Value;
                    //车场内登记的免费车
                    if (policyArea != null && policyArea.PolicyArea_FreeCarUseSpace == 1)
                    {
                        List<Model.Car> freeCar = BLL.Car.GetAllEntity("Car_CarNo", $"Car_Category={Model.EnumCarType.Free} AND Car_ParkingNo='{Parking_No}'");
                        freeCarNoList = freeCar?.Select(x => x.Car_CarNo)?.ToList() ?? new List<string>();
                        if (freeCarNoList.Count > 0)
                        {
                            inParkOrders = inParkOrders.Where(x => !freeCarNoList.Contains(x.InCar_CarNo)).ToList();
                            inCount = inParkOrders.Sum(x => x.Num).Value;
                        }
                    }
                }

                //访客车是否占用车位数
                if (policyArea != null && policyArea.PolicyArea_ReserveUseSpace == 0)
                {
                    List<Model.Reserve> reserves = PassHelperBiz.GetReserveUseSapce(CarObj.time.Value);
                    var useSpaceReserve = reserves?.FindAll(x => x.Reserve_CarNo != CarObj.carno);
                    totalSpace = totalSpace - useSpaceReserve?.Count ?? 0;
                }

                //修改余位
                if (!string.IsNullOrEmpty(oArea.ParkArea_No))
                {
                    var area = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, oArea.ParkArea_No);
                    if (area != null && area.ParkArea_RemainSpace != null && area.ParkArea_RemainSpace != 0)
                    {
                        inCount += (-area.ParkArea_RemainSpace.Value);
                        if (inCount > totalSpace) inCount = totalSpace;
                    }
                }

                resIn.RemainSpace = totalSpace - inCount;
                if (inCount >= totalSpace)
                {
                    resIn.errmsg = "车位已满";
                    if (policyPass.PolicyPass_SpaceFull == 0)
                    {
                        resIn.code = 0;
                        resIn.errcode = Model.ResultPassCode.ERROR_SPACE_FULL;
                        resIn.errmsg = "车位已满,禁止通行";
                    }
                    else
                    {
                        resIn.code = resIn.code < policyPass.PolicyPass_SpaceFull ? policyPass.PolicyPass_SpaceFull : resIn.code;
                        if (resIn.code == 3)
                        {
                            resIn.errcode = Model.ResultPassCode.ERROR_SPACE_FULL_LINE;
                            resIn.errmsg = "车位已满,排队等候";
                        }
                    }
                }
                #endregion
            }
            return resIn;
        }
        #endregion

        /// <summary>
        /// 判断车辆是否能够从当前通道出入
        /// </summary>
        /// <param name="code">通用策略中得出的通行状态</param>
        /// <param name="passway">通道数据</param>
        /// <param name="CarObj">相机识别参数</param>
        /// <param name="parkAreas">通道关联的所有区域</param>
        /// <param name="passwayLinks">通道关联的所有区域出入口类型</param>
        /// <param name="policyPassway">通道策略</param>
        /// <param name="policyPass">放行策略</param>
        /// <param name="carCardType">车牌类型</param>
        /// <param name="carType">车牌颜色</param>
        /// <param name="parkOrders">场内停车所有订单</param>
        /// <param name="orderDetails">场内停车所有订单明细</param>
        /// <param name="rOrderNo">当前弹窗操作的订单编号</param>
        /// <returns></returns>
        public static Model.ResultInOutList IsPass(int? code, Model.Passway passway, Model.ParkCarInOut CarObj, List<Model.ParkArea> parkAreas, List<Model.PasswayLink> passwayLinks,
            Model.PolicyPark policyPark, Model.PolicyPassway policyPassway, Model.PolicyPass policyPass, Model.CarCardType carCardType, Model.CarType carType, Model.Car car,
            out Model.PolicyArea poArea, List<Model.PolicyArea> paList = null, List<Model.CarCardType> cctList = null, List<Model.PolicyCarCard> policycards = null, Model.ParkOrder curOrder = null, string rOrderNo = null)
        {
            poArea = null;
            Model.ResultInOutList resultInOut = new Model.ResultInOutList();

            //获取通道关联的出入口类型
            var wayOut = passwayLinks.FindAll(x => x.PasswayLink_GateType == 0).FirstOrDefault();   //出口
            var wayIn = passwayLinks.FindAll(x => x.PasswayLink_GateType == 1).FirstOrDefault();    //入口

            var gate = BLL.Passway.GetPasswayGateType(passwayLinks, parkAreas);


            Model.OrderDetail detail = null;
            List<Model.OrderDetail> detailList = null;

            //int? matchMode = (CarObj.mode == 6 ? 1 : policyPassway.PolicyPassway_CarMatch);//手动输入仅支持完全匹配
            //if (gate == 1 || gate == 2) curOrder = PassHelperBiz.MatchParkOrder(CarObj, matchMode);
            if (curOrder != null)
            {
                if (car == null) CarObj.carno = curOrder.ParkOrder_CarNo;
                detailList = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo='{curOrder.ParkOrder_No}'");
                detail = detailList?.OrderByDescending(x => x.OrderDetail_EnterTime).FirstOrDefault();
            }

            int currentIsCharge = detail?.orderdetail_IsCharge ?? 0;

            #region 入场
            if (wayIn != null)
            {
                var resIn = new Model.ResultInOut() { code = code };
                Model.ParkArea oArea = parkAreas.Find(x => x.ParkArea_No == wayIn.PasswayLink_ParkAreaNo) ?? AppBasicCache.GetElement(AppBasicCache.GetParkAreas, wayIn.PasswayLink_ParkAreaNo);
                if (oArea == null)
                {
                    resIn.code = 0;
                    resIn.errcode = Model.ResultPassCode.ERROR_AREA_NOTEXIST;
                    resIn.errmsg = "通道关联的区域不存在,禁止通行";
                    resultInOut.resIn = resIn;
                    return resultInOut;
                }
                string Parking_No = oArea.ParkArea_ParkNo;

                Model.PolicyArea policyArea = null;
                if (paList?.Count > 0)
                    policyArea = paList.Find(x => x.PolicyArea_ParkAreaNo == oArea.ParkArea_No) ?? AppBasicCache.GetElement(AppBasicCache.GetPolicyareaes, oArea.ParkArea_No);
                else
                    policyArea = BLL.PolicyArea.GetEntity(oArea.ParkArea_No);
                poArea = policyArea;

                #region 车位已满
                resIn = IsFullSpace(CarObj, oArea, carCardType, policyPass, policyArea, policyPark, resIn.code, cctList);
                if (resIn.code == 0) { resultInOut.resIn = resIn; return resultInOut; };
                #endregion

                #region 判断重复入场 || 写入停车订单

                #region 多位多车判断
                int parkOrderIsLift = 0;
                Model.OutMultiSpace outdata = null;
                if (car != null && carCardType != null && carCardType.CarCardType_IsMoreCar == 1 && "2,5,6".IndexOf(carCardType.CarCardType_Type.Value.ToString()) == -1)
                {
                    if (policyArea == null)
                    {
                        resIn.code = 0;
                        resIn.errcode = Model.ResultPassCode.ERROR_OWNERSPACE_DISABLED;
                        resIn.errmsg = "区域未设置一位多车功能方案";
                        resultInOut.resIn = resIn;
                        return resultInOut;
                    }

                    var owner = BLL.Owner.GetEntity(car.Car_OwnerNo);

                    var result = BLL.Pass.IsSpaceCar(car, owner, carCardType, true, oArea.ParkArea_No, wayOut?.PasswayLink_ParkAreaNo, out outdata, out var errmsg, policycards);

                    //属于多车多位类型的车辆，在入场区域未设置车位则禁止入场
                    //if (policyArea.PolicyArea_MoreCar == 3 && (!outdata.spacecar || (outdata.spacecar && outdata.spacenum == 0)))
                    //{
                    //    resIn.errcode = Model.ResultPassCode.ERROR_CAR_NOSPACE;
                    //    resIn.errmsg = "无车位,禁止通行";
                    //    resIn.code = 0;
                    //    resultInOut.resIn = resIn;
                    //    return resultInOut;
                    //}

                    //是多位多车类型且已生效
                    //if (outdata.spacecar && !outdata.expired)
                    //{
                    parkOrderIsLift = 1;
                    outdata.details?.RemoveAll(x => x.OrderDetail_CarNo == car.Car_CarNo || x.orderdetail_IsCharge == 0 || x.orderdetail_IsCharge == null);

                    //车主在入场区域无车位或车位已停满，当前车辆需要计费
                    if (result && (outdata.spacenum == 0 || outdata.details?.Count >= outdata.spacenum))
                    {
                        parkOrderIsLift = 0;
                        if (policyArea.PolicyArea_MoreCar == 3)
                        {
                            resIn.errcode = Model.ResultPassCode.ERROR_OWNERSPACE_FULL;
                            if (outdata.spacenum == 1)
                            {
                                resIn.errmsg = $"车位被{outdata.details[0].OrderDetail_CarNo}占用,禁止通行";
                            }
                            else
                            {
                                resIn.errmsg = "车位已满,禁止通行";
                            }

                            resIn.spaceDetail = outdata.details;
                            resIn.code = 0;
                            resultInOut.resIn = resIn;
                            return resultInOut;
                        }
                        else
                        {
                            if (outdata.spacenum != 0 && outdata.details?.Count >= outdata.spacenum)
                            {

                                if (policyArea.PolicyArea_MoreCarOpen == 0)
                                {
                                    resIn.errmsg = $"车位被{outdata.details[0].OrderDetail_CarNo}占用,禁止通行";
                                    resIn.spaceDetail = outdata.details;
                                    resIn.code = 0;
                                    resultInOut.resIn = resIn;
                                    return resultInOut;
                                }
                                else if (policyArea.PolicyArea_MoreCarOpen == 2)
                                {
                                    resIn.code = 2;
                                    resIn.errmsg = $"车位被{outdata.details[0].OrderDetail_CarNo}\n占用,请稍候";
                                }
                                else
                                {
                                    resIn.errmsg = $"车位被{outdata.details[0].OrderDetail_CarNo}\n占用,请通行";
                                }

                                resIn.spaceused = 1;
                            }
                            else if (outdata.spacenum == 0)
                            {
                                if (policyArea.PolicyArea_MoreCarOpen == 0)
                                {
                                    resIn.errmsg = "车位已满,禁止通行";
                                    resIn.spaceDetail = outdata.details;
                                    resIn.code = 0;
                                    resultInOut.resIn = resIn;
                                    return resultInOut;
                                }
                                else if (policyArea.PolicyArea_MoreCarOpen == 2)
                                {
                                    resIn.code = 2;
                                    resIn.errmsg = $"车主无车位,等待放行";
                                }
                            }
                        }
                    }
                    //}
                    //else
                    //{
                    //    parkOrderIsLift = 0;
                    //}
                }
                #endregion

                #region 外场区域

                var isrepeat = 0;
                if (oArea.ParkArea_Type == 0 || wayOut == null)
                {
                    #region 入场判断是否禁止通行，如果允许则关闭之前的车牌订单
                    if ((detail != null && detail.OrderDetail_ParkAreaNo == oArea.ParkArea_No) ||
                        (curOrder != null && curOrder?.ParkOrder_IsNoInRecord == 1 && curOrder?.ParkOrder_ParkAreaNo == oArea.ParkArea_No) ||
                        (curOrder != null && gate == 1))
                    {

                        if (curOrder.ParkOrder_No != rOrderNo)
                        {
                            int? policypassrepeatenter = policyPass.PolicyPass_RepeatEnter;
                            //if (CarObj.mode == 1 || CarObj.mode == 3 || CarObj.mode == 6) policypassrepeatenter = policyPass.PolicyPass_RepeatEnter;
                            //else if (CarObj.mode == 2 || CarObj.mode == 4) policypassrepeatenter = policyPass.PolicyPass_RepeatEnter2;

                            if (policypassrepeatenter == 0)
                            {
                                resIn.code = 0;
                                resIn.errcode = Model.ResultPassCode.ERROR_ENTER_REPEAT;
                                resIn.errmsg = "重复入场,禁止通行";
                                resultInOut.resIn = resIn;
                                return resultInOut;
                            }

                            if (resIn.code < policypassrepeatenter)
                            {
                                resIn.errcode = ResultPassCode.ERROR_ENTER_REPEATCONFIRM;
                                resIn.code = policypassrepeatenter;
                                resIn.errmsg = "重复入场";
                            }

                            //不是出口车道，重复入场则关闭订单；如果还是出口车道，则只关闭订单明细
                            if (gate == 1)
                            {
                                isrepeat = 1;
                                //if (curOrder != null)
                                //{
                                //    curOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                                //    curOrder.ParkOrder_Remark = "重复入场关闭订单";
                                //    resIn.closeOrder = curOrder;
                                //}
                            }
                        }

                        //if (detail != null)
                        //{
                        //    detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                        //    detail.OrderDetail_Remark = "重复入场关闭订单";
                        //    resIn.closeDetail = detail;
                        //}

                    }
                    #endregion

                    //创建入场订单
                    Model.ParkOrder parkOrder = null;
                    if (curOrder == null || isrepeat == 1 || curOrder.ParkOrder_IsNoInRecord == 1)
                    {
                        if (curOrder != null && curOrder.ParkOrder_No != rOrderNo && detail == null)
                            resIn.closeOrder = curOrder;

                        string parkOrderNo = "";
                        if (curOrder != null && curOrder.ParkOrder_No == rOrderNo)
                        {
                            parkOrderNo = rOrderNo;
                        }
                        else
                        {
                            parkOrderNo = (CarObj.mode != 2 && CarObj.mode != 4 && CarObj.mode != 5 && CarObj.mode != 7) ? string.Empty : CarObj.orderno;//扫码入场自带订单编码
                        }

                        parkOrder = BLL.ParkOrder.CreateParkOrder(Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, CarObj.carno, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name, 0, gate == 3 ? 1 : 0, car?.Car_OwnerNo, car?.Car_OwnerName, parkOrderNo, isSupplement: CarObj.isSupplement);
                        parkOrder.ParkOrder_CarLogo = CarObj.carlogo;
                        parkOrder.ParkOrder_ReserveNo = CarObj.reserveno;
                        parkOrder.ParkOrder_EnterImg = CarObj.img;
                        parkOrder.ParkOrder_EnterImgPath = BLL.ImageTools.GetImgWebUrlByPath(CarObj.img, BLL.ImageTools.LocalFilePath);

                        //一位多车是否收费
                        parkOrder.ParkOrder_IsLift = parkOrderIsLift;
                        if (curOrder != null)
                        {
                            if (curOrder.ParkOrder_No == rOrderNo)
                            {
                                curOrder = parkOrder;
                            }
                            else
                            {
                                #region  重复入场则关闭场内的订单
                                if (gate == 1)
                                {
                                    isrepeat = 1;
                                    if (curOrder != null)
                                    {
                                        List<Model.PayOrder> payorderList = BLL.PayOrder.GetAllEntity("PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{curOrder.ParkOrder_No}' and PayOrder_Status=1");
                                        curOrder.ParkOrder_TotalAmount = payorderList?.Sum(x => x.PayOrder_Money) ?? 0;
                                        curOrder.ParkOrder_TotalPayed = payorderList?.Sum(x => x.PayOrder_PayedMoney) ?? 0;
                                        curOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                                        curOrder.ParkOrder_Remark = "重复入场关闭订单,由" + parkOrder.ParkOrder_No + "关闭";
                                        resIn.closeOrder = curOrder;
                                    }
                                }

                                if (detail != null)
                                {
                                    detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                                    detail.OrderDetail_Remark = "重复入场关闭订单,由" + parkOrder.ParkOrder_No + "关闭";
                                    resIn.closeDetail = detail;
                                }
                                #endregion
                            }
                        }
                    }
                    else
                    {
                        parkOrder = curOrder?.Copy();
                        if (car != null) parkOrder.ParkOrder_CarNo = car.Car_CarNo;

                        if (carCardType != null && parkOrder != null && parkOrder.ParkOrder_CarCardType != carCardType.CarCardType_No)
                        {
                            parkOrder.ParkOrder_CarCardType = carCardType.CarCardType_No;
                            parkOrder.ParkOrder_CarCardTypeName = carCardType.CarCardType_Name;
                        }

                        if (parkOrderIsLift == 0 && parkOrder.ParkOrder_IsLift == null)
                            parkOrder.ParkOrder_IsLift = parkOrderIsLift;
                        else if (parkOrderIsLift == 1)
                        {
                            if (parkOrder.ParkOrder_IsLift == 0 || parkOrder.ParkOrder_IsLift == null)
                            {
                                parkOrder.ParkOrder_IsLift = 2;
                            }
                        }
                    }

                    string OrderDetail_No = "";
                    if (parkOrder.ParkOrder_No == rOrderNo && detail != null)
                    {
                        OrderDetail_No = detail.OrderDetail_No;
                    }

                    //创建入场明细
                    Model.OrderDetail orderDetail = BLL.OrderDetail.CreateOrderDetail(parkOrder.ParkOrder_No, Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name,
                        parkOrder.ParkOrder_CarNo, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name,
                        CarObj.time.Value, passway.Passway_No, passway.Passway_Name, CarObj.AdminAccount, CarObj.AdminName, detailNo: OrderDetail_No);
                    orderDetail.orderDetail_CarLogo = CarObj.carlogo;
                    orderDetail.orderdetail_IsCharge = parkOrderIsLift;
                    if (outdata != null && outdata.spacenum > 0 && outdata.details?.Count >= outdata.spacenum)
                    {
                        string carno = "";
                        if (outdata.details.Count > 3) carno = string.Join(",", outdata.details.Take(3).Select(x => x.OrderDetail_CarNo)); else carno = string.Join(",", outdata.details.Select(x => x.OrderDetail_CarNo)); ;
                        orderDetail.OrderDetail_Remark = $"车位被[{carno}]占用";
                    }

                    if (gate == 3 && detail != null)
                    {
                        orderDetail.OrderDetail_EnterImg = detail.OrderDetail_EnterImg;
                        orderDetail.OrderDetail_EnterImgPath = detail.OrderDetail_EnterImgPath;
                    }
                    else if (gate == 0 && detailList != null && detailList.Count > 0)
                    {
                        orderDetail.OrderDetail_EnterImg = detailList?.OrderBy(x => x.OrderDetail_EnterTime).FirstOrDefault().OrderDetail_EnterImg;
                        orderDetail.OrderDetail_EnterImgPath = detailList?.OrderBy(x => x.OrderDetail_EnterTime).FirstOrDefault().OrderDetail_EnterImgPath;
                    }

                    resIn.parkorder = parkOrder;
                    resIn.orderDetail = orderDetail;
                }
                #endregion

                #region 内场区域
                else
                {
                    if (curOrder == null)
                    {
                        //resIn.code = 0;
                        //resIn.errmsg = "未找到停车订单,禁止通行";
                        //resultInOut.resIn = resIn;
                        //return resultInOut;

                        string parkOrderNo = (CarObj.mode != 2 && CarObj.mode != 4) ? string.Empty : CarObj.orderno;//扫码入场自带订单编码
                                                                                                                    //创建一个停车订单
                        curOrder = BLL.ParkOrder.CreateParkOrder(Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, CarObj.carno, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name, 0, 0, car?.Car_OwnerNo, car?.Car_OwnerName, parkOrderNo, isSupplement: CarObj.isSupplement);
                        curOrder.ParkOrder_CarLogo = CarObj.carlogo;
                        curOrder.ParkOrder_ReserveNo = CarObj.reserveno;

                        //一位多车是否收费
                        curOrder.ParkOrder_IsLift = parkOrderIsLift;

                        resIn.parkorder = curOrder;
                    }
                    else
                    {
                        if (parkOrderIsLift == 1)
                        {
                            if (curOrder.ParkOrder_IsLift == null || curOrder.ParkOrder_IsLift == 0) curOrder.ParkOrder_IsLift = parkOrderIsLift;
                        }
                    }

                    if (detail != null && detail.OrderDetail_ParkAreaNo == oArea.ParkArea_No && (gate == 1 || gate == 2))
                    {
                        //重复入场,禁止通行
                        int? policypassrepeatenter = null;
                        if (CarObj.mode == 1 || CarObj.mode == 3 || CarObj.mode == 6)
                            policypassrepeatenter = policyPass.PolicyPass_RepeatEnter;
                        else if (CarObj.mode == 2 || CarObj.mode == 4)
                            policypassrepeatenter = policyPass.PolicyPass_RepeatEnter2;

                        if (policypassrepeatenter == 0)
                        {
                            resIn.code = 0;
                            resIn.errcode = Model.ResultPassCode.ERROR_ENTER_REPEAT;
                            resIn.errmsg = "重复入场,禁止通行";
                            resultInOut.resIn = resIn;
                            return resultInOut;
                        }

                        if (resIn.code < policypassrepeatenter)
                        {
                            resIn.code = policypassrepeatenter;
                            resIn.errmsg = "重复入场";
                        }

                        isrepeat = 1;
                        detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                        detail.OrderDetail_Remark = "重复入场关闭订单";
                        //BLL.ParkOrder.CarInComplete(null, detail);                        
                        resIn.closeDetail = detail;
                    }

                    //创建入场明细
                    Model.OrderDetail orderDetail = BLL.OrderDetail.CreateOrderDetail(curOrder.ParkOrder_No, Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, CarObj.carno, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name);
                    orderDetail.orderDetail_CarLogo = CarObj.carlogo;
                    orderDetail.orderdetail_IsCharge = parkOrderIsLift;
                    if (outdata != null && outdata.spacenum > 0 && outdata.details?.Count >= outdata.spacenum)
                    {
                        string carno = "";
                        if (outdata.details.Count > 3) carno = string.Join(",", outdata.details.Take(3).Select(x => x.OrderDetail_CarNo)); else carno = string.Join(",", outdata.details.Select(x => x.OrderDetail_CarNo)); ;
                        orderDetail.OrderDetail_Remark = $"车位被[{carno}]占用";
                    }
                    //resIn.parkorder = orderRepeat;
                    resIn.orderDetail = orderDetail;
                    resIn.isrepeat = isrepeat;
                }
                #endregion

                #endregion

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && resIn.parkorder != null) { if (CarObj.isSupplement) { resIn.parkorder.ParkOrder_UserNo = "1"; } }
                resultInOut.resIn = resIn;
            }
            #endregion

            #region 出场
            if (wayOut != null)
            {
                Model.ResultInOut resOut = new Model.ResultInOut() { code = code, onenter = 1 };

                #region 未找到入场记录
                //查询场内停车明细
                if ((curOrder == null || curOrder.ParkOrder_OutType == 1) && detail == null)
                {
                    resOut.onenter = 0;
                    resOut.errmsg = "无入场记录";
                    if (policyPass.PolicyPass_NoFundEnter == 0)
                    {
                        resOut.code = 0;
                        resOut.errcode = Model.ResultPassCode.ERROR_OUT_NORECORD;
                        resOut.errmsg = $"[{carCardType.CarCardType_Name}]未找到入场记录,禁止通行";
                        resultInOut.resOut = resOut;
                        return resultInOut;
                    }
                    resOut.code = policyPass.PolicyPass_NoFundEnter;
                    resOut.minmoney = policyPass.PolicyPass_NoFundEnter == 4 ? policyPass.PolicyPass_MinAmount : 0;

                    if (curOrder != null)
                    {
                        curOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                        curOrder.ParkOrder_Remark = "出口无入场记录重复识别关闭订单";
                        resOut.closeOrder = curOrder;
                    }
                }
                else
                {
                    if (curOrder.ParkOrder_Lock == 1)
                    {
                        resOut.code = 0;
                        resOut.errcode = Model.ResultPassCode.ERROR_CAR_LOCK;
                        resOut.errmsg = $"已锁车,禁止通行";
                        resultInOut.resOut = resOut;
                        return resultInOut;
                    }

                    resOut.parkorder = curOrder;
                    resOut.orderDetail = detail;
                }
                #endregion

                #region 月租车被删除后按过期处理（停车定订单上车牌类型为月租车且未查询到固定车信息）
                //var card = BLL.CarCardType.GetEntity(curOrder?.ParkOrder_CarCardType);
                var fre = (car == null && curOrder != null && carCardType != null && carCardType.CarCardType_Type == 3);
                if (fre)
                {
                    var policyCarCard = BLL.PolicyCarCard.GetEntityByCarCard(carCardType.CarCardType_No);

                    if (policyCarCard.PolicyCarCard_ExpireHandle.Equals("0"))
                    {
                        if (policyPass.PolicyPass_IsExpire == 0)
                        {
                            resOut.code = 0;
                            resOut.errcode = Model.ResultPassCode.ERROR_CAR_EXPIRED;
                            resOut.errmsg = "月租车已过期,禁止通行";
                            resultInOut.resOut = resOut;
                            return resultInOut;
                        }
                        resOut.code = resOut.code < policyPass.PolicyPass_IsExpire ? policyPass.PolicyPass_IsExpire : resOut.code;
                    }
                    else
                    {
                        resOut.code = 2;//固定车已删除，且设置过期按临时车计费。则返回弹框提示
                    }
                }
                #endregion

                resultInOut.resOut = resOut;
            }
            #endregion

            return resultInOut;
        }



        /// <summary>
        /// 判断车辆是否能够从当前通道出入
        /// </summary>
        /// <param name="code">通用策略中得出的通行状态</param>
        /// <param name="passway">通道数据</param>
        /// <param name="CarObj">相机识别参数</param>
        /// <param name="parkAreas">通道关联的所有区域</param>
        /// <param name="passwayLinks">通道关联的所有区域出入口类型</param>
        /// <param name="policyPassway">通道策略</param>
        /// <param name="policyPass">放行策略</param>
        /// <param name="carCardType">车牌类型</param>
        /// <param name="carType">车牌颜色</param>
        /// <param name="car"></param>
        /// <param name="poArea">返回区域策略</param>
        /// <param name="curOrder">场内订单</param>
        /// <param name="detail">场内订单明细</param>
        /// <returns></returns>
        public static Model.ResultInOutList WhiteCarIsPass(int gate, int? code
            , Model.Passway passway
            , Model.ParkCarInOut CarObj
            , List<Model.ParkArea> parkAreas
            , List<Model.PasswayLink> passwayLinks
            , Model.PolicyPark policyPark
            , Model.PolicyPassway policyPassway
            , Model.PolicyPass policyPass
            , Model.CarCardType carCardType
            , Model.CarType carType
            , Model.Car car
            , out Model.PolicyArea poArea
            , List<Model.PolicyArea> paList = null
            , Model.ParkOrder curOrder = null)
        {
            poArea = null;
            Model.ResultInOutList resultInOut = new Model.ResultInOutList();

            //获取通道关联的出入口类型
            var wayOut = passwayLinks.FindAll(x => x.PasswayLink_GateType == 0).FirstOrDefault();   //出口
            var wayIn = passwayLinks.FindAll(x => x.PasswayLink_GateType == 1).FirstOrDefault();    //入口
                                                                                                    //获取通道关联的出入口类型
                                                                                                    //获取车辆场内停车订单
                                                                                                    //Model.ParkOrder curOrder = null;
            Model.OrderDetail detail = null;
            List<Model.OrderDetail> detailList = null;

            //int? matchMode = (CarObj.mode == 6 ? 1 : policyPassway.PolicyPassway_CarMatch);//手动输入仅支持完全匹配
            //if (gate == 1 || gate == 2) curOrder = PassHelperBiz.MatchParkOrder(CarObj, matchMode);
            if (curOrder != null)
            {
                detailList = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo='{curOrder.ParkOrder_No}'");
                detail = detailList?.OrderByDescending(x => x.OrderDetail_EnterTime).FirstOrDefault();
            }

            #region 入场
            if (wayIn != null)
            {
                var resIn = new Model.ResultInOut() { code = code };
                Model.ParkArea oArea = parkAreas.Find(x => x.ParkArea_No == wayIn.PasswayLink_ParkAreaNo);
                if (oArea == null)
                {
                    resIn.code = 0;
                    resIn.errcode = Model.ResultPassCode.ERROR_AREA_NOTEXIST;
                    resIn.errmsg = "通道关联的区域不存在,禁止通行";
                    resultInOut.resIn = resIn;
                    return resultInOut;
                }
                string Parking_No = oArea.ParkArea_ParkNo;
                var policyArea = paList?.Find(x => x.PolicyArea_No == oArea.ParkArea_No) ?? BLL.PolicyArea.GetEntity(oArea.ParkArea_No);
                poArea = policyArea;


                #region 外场区域
                var isrepeat = 0;
                if (oArea.ParkArea_Type == 0 || wayOut == null)
                {
                    var gate2 = BLL.Passway.GetPasswayGateType(passwayLinks, parkAreas);

                    #region 入场判断是否禁止通行，如果允许则关闭之前的车牌订单
                    if ((detail != null && detail.OrderDetail_ParkAreaNo == oArea.ParkArea_No) ||
                        (curOrder != null && curOrder?.ParkOrder_IsNoInRecord == 1 && curOrder?.ParkOrder_ParkAreaNo == oArea.ParkArea_No) ||
                        (curOrder != null && gate2 == 1))
                    {

                        resIn.code = 1;
                        resIn.errmsg = "重复入场";


                        //不是出口车道，重复入场则关闭订单；如果还是出口车道，则只关闭订单明细
                        if (wayOut == null)
                        {
                            isrepeat = 1;
                        }
                    }
                    #endregion

                    //创建入场订单
                    Model.ParkOrder parkOrder = null;
                    if (curOrder == null || isrepeat == 1 || curOrder.ParkOrder_IsNoInRecord == 1)
                    {
                        if (curOrder != null && detail == null)
                            resIn.closeOrder = curOrder;

                        string parkOrderNo = (CarObj.mode != 2 && CarObj.mode != 4 && CarObj.mode != 5 && CarObj.mode != 7) ? string.Empty : CarObj.orderno;//扫码入场自带订单编码

                        parkOrder = BLL.ParkOrder.CreateParkOrder(Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, CarObj.carno, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name, 0, 0, car?.Car_OwnerNo, car?.Car_OwnerName, parkOrderNo, isSupplement: CarObj.isSupplement);
                        parkOrder.ParkOrder_CarLogo = CarObj.carlogo;
                        parkOrder.ParkOrder_ReserveNo = CarObj.reserveno;


                        #region  重复入场则关闭场内的订单
                        if (wayOut == null)
                        {
                            isrepeat = 1;
                            if (curOrder != null)
                            {
                                curOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                                curOrder.ParkOrder_Remark = "重复入场关闭订单,由" + parkOrder.ParkOrder_No + "关闭";
                                resIn.closeOrder = curOrder;
                            }
                        }

                        if (detail != null)
                        {
                            detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                            detail.OrderDetail_Remark = "重复入场关闭订单,由" + parkOrder.ParkOrder_No + "关闭";
                            resIn.closeDetail = detail;
                        }
                        #endregion
                    }
                    else
                    {
                        parkOrder = curOrder;


                    }


                    //创建入场明细
                    Model.OrderDetail orderDetail = BLL.OrderDetail.CreateOrderDetail(parkOrder.ParkOrder_No, Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, parkOrder.ParkOrder_CarNo, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name);
                    orderDetail.orderDetail_CarLogo = CarObj.carlogo;


                    resIn.parkorder = parkOrder;
                    resIn.orderDetail = orderDetail;
                }
                #endregion

                #region 内场区域
                else
                {
                    if (curOrder == null)
                    {
                        //resIn.code = 0;
                        //resIn.errmsg = "未找到停车订单,禁止通行";
                        //resultInOut.resIn = resIn;
                        //return resultInOut;

                        string parkOrderNo = (CarObj.mode != 2 && CarObj.mode != 4) ? string.Empty : CarObj.orderno;//扫码入场自带订单编码
                                                                                                                    //创建一个停车订单
                        curOrder = BLL.ParkOrder.CreateParkOrder(Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, CarObj.carno, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name, 0, 0, car?.Car_OwnerNo, car?.Car_OwnerName, parkOrderNo, isSupplement: CarObj.isSupplement);
                        curOrder.ParkOrder_CarLogo = CarObj.carlogo;
                        curOrder.ParkOrder_ReserveNo = CarObj.reserveno;



                        resIn.parkorder = curOrder;
                    }

                    if (detail != null && detail.OrderDetail_ParkAreaNo == oArea.ParkArea_No)
                    {

                        resIn.code = 1;
                        resIn.errmsg = "重复入场";


                        isrepeat = 1;
                        detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;
                        //BLL.ParkOrder.CarInComplete(null, detail);                        
                        resIn.closeDetail = detail;
                    }

                    //创建入场明细
                    Model.OrderDetail orderDetail = BLL.OrderDetail.CreateOrderDetail(curOrder.ParkOrder_No, Parking_No, oArea.ParkArea_No, oArea.ParkArea_Name, CarObj.carno, carCardType.CarCardType_No, carCardType.CarCardType_Name, carType.CarType_No, carType.CarType_Name, CarObj.time.Value, passway.Passway_No, passway.Passway_Name);
                    orderDetail.orderDetail_CarLogo = CarObj.carlogo;

                    //resIn.parkorder = orderRepeat;
                    resIn.orderDetail = orderDetail;
                    resIn.isrepeat = isrepeat;
                }
                #endregion

                resultInOut.resIn = resIn;
            }
            #endregion
            #region 出场
            if (wayOut != null)
            {
                Model.ResultInOut resOut = new Model.ResultInOut() { code = code, onenter = 1 };

                #region 未找到入场记录
                //查询场内停车明细
                if ((curOrder == null || curOrder.ParkOrder_OutType == 1) && detail == null)
                {
                    resOut.onenter = 0;
                    resOut.errmsg = "无入场记录";

                    resOut.code = 1;

                    if (curOrder != null)
                    {
                        curOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Close;
                        curOrder.ParkOrder_Remark = "出口无入场记录重复识别关闭订单";
                        resOut.closeOrder = curOrder;
                    }
                }
                else
                {

                    resOut.parkorder = curOrder;
                    resOut.orderDetail = detail;
                }
                #endregion


                resultInOut.resOut = resOut;
            }
            #endregion

            return resultInOut;
        }

        /// <summary>
        /// 插入识别未支付记录
        /// </summary>
        /// <param name="carRecog"></param>
        public static void InsertRecogNoPassRecord(string UnpaidRecord_No, string carno, string CarRecog_No, DateTime? CarRecog_Time, string passwayNo, string passwayName, string parkorderNo, decimal? money,
            string carcardtypeNo, string cartypeNo, int? recogMode)
        {
            if (AppBasicCache.GetPolicyPark?.PolicyPark_UnpaidRecord != 1 || AppSettingConfig.SentryMode == VersionEnum.CloudServer || AppSettingConfig.SentryMode == VersionEnum.EPSServer) return;

            _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
            {
                try
                {
                    var nopassrecord = new Model.UnpaidRecord()
                    {
                        UnpaidRecord_No = UnpaidRecord_No,
                        UnpaidRecord_CarNo = carno,
                        UnpaidRecord_RecogTime = CarRecog_Time,
                        UnpaidRecord_PasswayNo = passwayNo,
                        UnpaidRecord_ParkOrderNo = parkorderNo,
                        UnpaidRecord_RecogNo = CarRecog_No,
                        UnpaidRecord_PayedAmount = money,
                        UnpaidRecord_CarCardTypeNo = carcardtypeNo,
                        UnpaidRecord_CarTypeNo = cartypeNo,
                        UnpaidRecord_Mode = recogMode
                    };
                    var ret = BLL.UnpaidRecord.GetInstance(CarRecog_Time.Value).Add(nopassrecord);
                    if (ret < 1)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[{carno}]保存识别未支付记录失败");
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"[{carno}]保存识别未支付记录异常：{ex.ToString()}");
                }
                return Task.CompletedTask;

            });
        }

    }
}
