﻿using Microsoft.Extensions.Caching.Memory;
using System;
using carparking.BLL.Cache;

namespace carparking.SentryBox.BarrierDevice
{
    /// <summary>
    /// 道闸控制板
    /// </summary>
    public class BarrierDeviceUtilsl
    {
        /// <summary>
        /// 生成倒车缓存键值
        /// </summary>
        /// <param name="passwayNo">车道编号</param>
        /// <param name="carNo">车牌号</param>
        /// <returns>缓存键值</returns>
        public static string GenerateBackCarCacheKey(string passwayNo, string carNo)
        {
            if (string.IsNullOrWhiteSpace(passwayNo) || string.IsNullOrWhiteSpace(carNo) || carNo.Length <= 1)
            {
                return string.Empty;
            }
            return $"{passwayNo}{carNo.Substring(1)}";
        }
        /// <summary>
        /// 发送来车事件
        /// </summary>
        /// <param name="data"></param>
        public static void SendCarOrder(Model.ResultPass data, bool saveCache = true)
        {
            string passwayno = string.Empty;
            string orderno = string.Empty;
            try
            {
                //if (!data.isSupplement) return;

                if (data.passres?.passway?.Passway_EnableBoard == 1 && data.passres?.passway?.Passway_IsBackCar == 1
                    && data.passres.code > 0)
                {
                    passwayno = data.passres?.passway?.Passway_No;
                    int? sameInOut = data.passres?.passway?.Passway_SameInOut;
                    string outNo = data.passres?.passway?.Passway_OutNo;
                    int iInOut = 0;
                    Model.ParkOrder parkOrder = data.resorder?.resIn?.parkorder;
                    if (parkOrder == null)
                    {
                        iInOut = 1;
                        parkOrder = data.resorder?.resOut?.parkorder;
                    }
                    if (parkOrder != null)
                    {
                        orderno = parkOrder.ParkOrder_No;
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{orderno}-倒车功能已启用-0x04");

                        if (saveCache)
                        {
                            //单个缓存项的配置
                            var cacheEntityOps = new MemoryCacheEntryOptions()
                            {
                                //相对过期时间
                                SlidingExpiration = TimeSpan.FromMinutes(15),
                                //优先级，当缓存压缩时会优先清除优先级低的缓存项
                                Priority = CacheItemPriority.High,
                                Size = 1
                            };
                            //注册缓存项被清除时的回调
                            cacheEntityOps.RegisterPostEvictionCallback((key, value, reason, state) =>
                            {
                                if (value is Model.ParkOrder order)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"倒车缓存被清理 - 键：{key}，订单：{order.ParkOrder_No}，车牌：{order.ParkOrder_CarNo}，原因：{reason}");
                                }
                            });
                            //缓存当前车道订单
                            var cacheKey = GenerateBackCarCacheKey(passwayno, parkOrder.ParkOrder_CarNo);
                            if (!string.IsNullOrWhiteSpace(cacheKey))
                            {
                                AppBasicCache.GetMemoryCache.Set(cacheKey, parkOrder, cacheEntityOps);
                                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{orderno}-倒车缓存已存储，缓存键：{cacheKey}，车牌：{parkOrder.ParkOrder_CarNo}");
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{orderno}-倒车缓存键生成失败，车道：{passwayno}，车牌：{parkOrder.ParkOrder_CarNo}");
                            }
                        }
                        else
                        {
                            var cacheKey = GenerateBackCarCacheKey(passwayno, parkOrder.ParkOrder_CarNo);
                            if (!string.IsNullOrWhiteSpace(cacheKey))
                            {
                                AppBasicCache.GetMemoryCache.Remove(cacheKey);
                                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{orderno}-倒车缓存已移除，缓存键：{cacheKey}，车牌：{parkOrder.ParkOrder_CarNo}");
                            }
                        }
                    }
                    else
                    {
                        //无入场记录发送
                        orderno = $"{DateTime.Now:yyyyMMddHHmmssfff}-{data.passres.carno.Substring(1)}";
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{orderno}-倒车功能已启用-0x05");
                    }
                    SendCarOrder(passwayno, orderno, iInOut, sameInOut, outNo);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"发送数据车道[{passwayno}][{orderno}]异常", LogLevel.Error, ex);
            }
        }

        /// <summary>
        /// 发送来车订单信息
        /// </summary>
        /// <param name="sPassWayNo">车道编号</param>
        /// <param name="orderNo">进出场记录订单</param>
        /// <param name="iInout">进出场标识1=出场0=入场</param>
        /// <param name="iSameInOut">是否同进同出</param>
        /// <param name="sSamePassNo">同进同出车道编号</param>
        /// <param name="iOpenStatus">是否已经开闸 1已经开闸 0未开闸</param>
        public static void SendCarOrder(string sPassWayNo, string orderNo, int iInout, int? iSameInOut, string sSamePassNo, int iOpenStatus = 0)
        {

            try
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"车道控制板订单发送前---PassWayNo[{sPassWayNo}]，orderNo[{orderNo}]");
                _ = TcpConnPools.Controller.ControllerHelper.SendCarOrderByY312Async(sPassWayNo, orderNo, iInout, iSameInOut ?? 0, sSamePassNo, iOpenStatus);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"发送数据车道[{sPassWayNo}]异常", LogLevel.Error, ex);
            }
        }
    }
}
