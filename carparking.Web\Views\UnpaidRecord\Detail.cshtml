﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>识别详情</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        h3, span { color: black; font-weight: normal !important; }
        .ibox-content { padding-top: 0px !important; }
        body { margin-top: 0; }
        .fishBone { border: 1px solid #f5f5f5; }
        .form-group { margin-bottom: 10px; }
        .gray-bg { background-color: #fdf8f8; margin-top: 5px; }
        h3 { padding-top: 5px; }

        .reog { display: inline-block; }
        .carimg { width: 400px; height: 300px; }
        .carimg > a > img { width: 100%; vertical-align: middle; max-height: 300px; }
        .head > td { color: #676a6c !important; padding: 5px; padding-left: 15px; }
        .content { top: 100px; position: absolute; height: 240px; padding-left: 10px; }
        .content > div { margin-top: 5px; }
        .control-label > span { font-size: 14px; font-weight: 600; }
        h3 { font-weight: 700 !important; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>识别信息</h3>
                </div>
            </div>
            <div class="form-group">
                <div class="row col-sm-12" style="display:flex; padding-left: 30px;">
                    <div class="reog carimg">
                        <a id="linkEnterImgPath" href="javascript:;" target="_blank">
                            <img src="../../Static/img/nophoto.jpg" onerror="src='../../Static/img/nophoto.jpg'" />
                        </a>
                    </div>
                    <div class="reog">
                        <div class="carno" style="position: absolute;padding: 5px; ">
                            <div>
                                <span id="ParkOrder_CarNo" style="font-size: 1.5rem; font-weight: 700; color: #433e3e;"></span>
                            </div>
                            <div style="">
                                <span class="layui-badge layui-bg-red" id="ParkOrder_CarCardTypeName"></span>
                                <span class="layui-badge layui-bg-red" id="ParkOrder_CarType"></span>
                            </div>
                        </div>
                        <div class="content">
                            <div><label class="control-label">车道名称： <span id="CarRecog_PasswayName"></span></label></div>
                            <div><label class="control-label">停车订单： <span id="ParkOrder_OrderNo"></span></label></div>
                            <div><label class="control-label">入场时间： <span id="ParkOrder_EnterTime"></span></label></div>
                            <div><label class="control-label">订单状态： <span id="ParkOrderStatus_Name"></span></label></div>
                            <div><label class="control-label">识别方式： <span id="CarRecog_Mode"></span></label></div>
                            <div><label class="control-label">识别时间： <span id="CarRecog_Time"></span></label></div>
                            <div><label class="control-label">识别结果： <span id="CarRecog_IsOpen"></span></label></div>
                            <div><label class="control-label">识别描述： <span id="CarRecog_Remark"></span></label></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/content.min.js?v=1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/js/fishBone.js?v1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.SuperSlide.2.1.1.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script type="text/x-jquery-tmpl" id="tmplmode">
        {{if CarRecog_Mode==1 }}
        <span class="layui-badge layui-bg-blue">相机识别</span>
        {{else CarRecog_Mode==2 }}
        <span class="layui-badge layui-bg-green">扫码</span>
        {{else CarRecog_Mode==3 }}
        <span class="layui-badge layui-bg-blue">ETC</span>
        {{else CarRecog_Mode==4 }}
        <span class="layui-badge layui-bg-green">刷卡</span>
        {{else CarRecog_Mode==6 }}
        <span class="layui-badge layui-bg-red">输入车牌</span>
        {{/if}}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplopen">
        {{if CarRecog_IsOpen==0 }}
        <span class="layui-badge layui-bg-red">禁止通行</span>
        {{else CarRecog_IsOpen==1 }}
        <span class="layui-badge layui-bg-green">允许通行</span>
        {{else CarRecog_IsOpen==2 }}
        <span class="layui-badge layui-bg-blue">弹框确认</span>
        {{else CarRecog_IsOpen==3 }}
        <span class="layui-badge layui-bg-orange">排队等候</span>
        {{else CarRecog_IsOpen==4 }}
        <span class="layui-badge layui-bg-cyan">最低缴费</span>
         {{else CarRecog_IsOpen==10 }}
        <span class="layui-badge layui-bg-cyan">黑名单车辆</span>
         {{else CarRecog_IsOpen==11 }}
        <span class="layui-badge layui-bg-black">忽略处理</span>
        {{/if}}
    </script>


    <script>
        var data3 = null;
        var UnpaidRecord_ID = decodeURIComponent($.getUrlParam("UnpaidRecord_ID"));
        var UnpaidRecord_RecogTime = decodeURIComponent($.getUrlParam("UnpaidRecord_RecogTime"));
        var pager = {
            init: function () {
                this.bindData();
            },
            bindEvent: function () {

            },

            //数据绑定
            bindData: function () {
                layer.msg('加载中...', { icon: 16, time: 0 });

                if (UnpaidRecord_ID != null) {
                    $.ajax({
                        type: 'post',
                        url: '/UnpaidRecord/GetResult',
                        dataType: 'json',
                        data: { UnpaidRecord_ID: UnpaidRecord_ID, UnpaidRecord_RecogTime: UnpaidRecord_RecogTime },
                        success: function (json) {
                            if (json.success) {
                                if (json.data != null && json.data.unpaidrecord != null) {
                                    $("#ParkOrder_CarCardTypeName").text(json.data.unpaidrecord.UnpaidRecord_CarCardTypeNo);
                                    $("#ParkOrder_CarType").text(json.data.unpaidrecord.UnpaidRecord_CarTypeNo);
                                }
                                //识别信息
                                if (json.data != null && json.data.carreog != null) {
                                    if (json.data.carreog.CarRecog_Img != null && json.data.carreog.CarRecog_Img != "") {
                                        $("#linkEnterImgPath").attr("href", PathCheck(decodeURIComponent(json.data.carreog.CarRecog_Img)))[0].children[0].src = PathCheck(decodeURIComponent(json.data.carreog.CarRecog_Img));
                                    }
                                    var modeHtml = $('#tmplmode').tmpl(json.data.carreog);
                                    var openHtml = $('#tmplopen').tmpl(json.data.carreog);
                                    $("#CarRecog_PasswayName").text(json.data.carreog.CarRecog_PasswayName);
                                    $("#CarRecog_Mode").html(modeHtml);
                                    $("#CarRecog_Time").text(json.data.carreog.CarRecog_Time);
                                    $("#CarRecog_CarLogo").text(json.data.carreog.CarRecog_CarLogo);
                                    $("#CarRecog_CarYear").text(json.data.carreog.CarRecog_CarYear);
                                    $("#CarRecog_PlateColor").text(json.data.carreog.CarRecog_PlateColor);
                                    $("#CarRecog_Credibility").text(json.data.carreog.CarRecog_Credibility);
                                    $("#CarRecog_IsRealPlate").text(json.data.carreog.CarRecog_IsRealPlate);
                                    $("#CarRecog_LicensePoint").text(json.data.carreog.CarRecog_LicensePoint);
                                    $("#CarRecog_IsOpen").html(openHtml);
                                    $("#CarRecog_Remark").text(json.data.carreog.CarRecog_Remark);
                                    $("#ParkOrder_CarNo").text(json.data.carreog.CarRecog_CarNo);
                                }
                                //订单信息
                                if (json.data != null && json.data.model != null) {
                                    $("#ParkOrder_OrderNo").text(json.data.model.ParkOrder_No);
                                    $("#ParkOrder_EnterTime").text(json.data.model.ParkOrder_EnterTime);
                                    $("#ParkOrder_OutTime").text(json.data.model.ParkOrder_OutTime);
                                    $("#ParkOrder_FreeReason").text(json.data.model.ParkOrder_FreeReason);
                                    if (json.data.model.ParkOrder_StatusNo) {
                                        if (json.data.model.ParkOrder_StatusNo == 199) $("#ParkOrderStatus_Name").text("预入场");
                                        else if (json.data.model.ParkOrder_StatusNo == 200 && json.data.model.ParkOrder_OutType == 1) $("#ParkOrderStatus_Name").text("预出场");
                                        else if (json.data.model.ParkOrder_StatusNo == 200) $("#ParkOrderStatus_Name").text("已入场");
                                        else if (json.data.model.ParkOrder_StatusNo == 201) $("#ParkOrderStatus_Name").text("已出场");
                                        else if (json.data.model.ParkOrder_StatusNo == 202) $("#ParkOrderStatus_Name").text("自动关闭");
                                        else if (json.data.model.ParkOrder_StatusNo == 203) $("#ParkOrderStatus_Name").text("场内关闭");
                                        else if (json.data.model.ParkOrder_StatusNo == 204) $("#ParkOrderStatus_Name").text("欠费出场");
                                    }

                                    //$("#ParkOrder_TotalAmount").html(json.data.model.ParkOrder_TotalAmount !== null ? json.data.model.ParkOrder_TotalAmount + " 元" : "");
                                    //$("#ParkOrderStatus_Name").html(json.data.model.ParkOrderStatus_Name);
                                    var WXUser_Nickname = json.data.model.WXUser_Nickname != null ? json.data.model.WXUser_Nickname : json.data.model.WXUser_Nickname2;
                                    $("#WXUser_Nickname").html(WXUser_Nickname);
                                }

                                pager.bindEvent();

                                layer.closeAll();
                            } else {
                                layer.msg('加载失败：' + json.msg, { icon: 5 });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('参数无效', { icon: 0 });
                }
            }
        };

        $(function () { pager.init() });

    </script>
</body>
</html>
