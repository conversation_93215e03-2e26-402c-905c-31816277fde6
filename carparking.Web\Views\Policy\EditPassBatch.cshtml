﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title></title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <style>
        html, body { padding: 0; }
        .layui-tab-content { padding: 0; }
        .layui-select-title input { color: #0094ff; }
        .layui-card-body .layui-row { margin-top: 15px; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }
        select[multiple] + .layui-form-select dd { padding: 0; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }
        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }
        .multiSelect a { padding: 2px 5px; background: #5FB878; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }
        .multiSelect a span { float: left; }
        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }
        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }
        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }
        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }
        .multiSelect a i:hover { background-color: #545556; }
        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }
        .multiOption:hover { color: #5FB878 }
    </style>
</head>
<body>

    <div class="layui-card layui-form">
        <div class="layui-card-header">
            <blockquote class="layui-elem-quote">请谨慎操作，当前批量设置将会覆盖之前的单个设置。</blockquote>
            <div class="layui-row">
                <div class="layui-col-xs12">
                    <select class="layui-select" id="PolicyPass_PasswayNo" name="PolicyPass_PasswayNo" multiple lay-search lay-tools>
                        <option value="">请选择车道</option>
                    </select>
                </div>
            </div>
            <div class="layui-row" style="margin-top:10px;">
                <div class="layui-col-xs12">
                    <select id="PolicyPass_CarCardTypeNo" name="PolicyPass_CarCardTypeNo" lay-filter="multiple" multiple lay-search lay-tools>
                        <option value="">请选择车牌类型</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-xs4">开闸方式</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_Pass" name="PolicyPass_Pass">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                    </select>
                </div>
            </div>
            <div class="layui-row ingate">
                <div class="layui-col-xs4">重复识别车牌入场</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_RepeatEnter" name="PolicyPass_RepeatEnter">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                    </select>
                </div>
            </div>
            <div class="layui-row ingate" style="display:none;">
                <div class="layui-col-xs4">无牌车重复扫码入场</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_RepeatEnter2" name="PolicyPass_RepeatEnter2">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                    </select>
                </div>
            </div>
            <div class="layui-row ingate">
                <div class="layui-col-xs4">车场满位入场</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_SpaceFull" name="PolicyPass_SpaceFull">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                        <option value="3">排队等候</option>
                    </select>
                </div>
            </div>
            <div class="layui-row outgate">
                <div class="layui-col-xs4">无入场记录</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_NoFundEnter" name="PolicyPass_NoFundEnter">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="4">最低收费</option>
                        <option value="0">禁止通行</option>
                    </select>
                    <input type="text" class="layui-input layui-hide v-null v-floatLimit v-min v-max" min="0" max="9999" maxlength="7"
                           id="PolicyPass_MinAmount" name="PolicyPass_MinAmount" placeholder="最低收费标准金额" value="50" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">过期开闸方式</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_IsExpire" name="PolicyPass_IsExpire">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">尾号限行开闸方式</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_NumPass" name="PolicyPass_NumPass">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">余额不足开闸方式</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPass_LackPass" name="PolicyPass_LackPass">
                        <option value="1">自动放行</option>
                        <option value="2">弹框确认</option>
                        <option value="0">禁止通行</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row">
            <div class="layui-col-xs8 layui-col-xs-offset4">
                <button id="Save" class="layui-btn layui-btn-sm"><i class="fa fa-check"></i> 保存</button>
                <button id="Cancel" class="layui-btn layui-btn-sm layui-bg-orange"><i class="fa fa-close"></i> 取消</button>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();

        layui.use(['element', 'form'], function () {

            pager.init();
        })


        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//免费车类型

        var pager = {
            data: null,
            passways: [],
            multi: [],
            gateBase: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllPasswayAndCarCardType", {}, function (json) {
                    if (json.success) {
                        pager.data = json.data;
                        var passways = json.data.passways;
                        var carCardTypes = json.data.carCardTypes;
                        passways.forEach(function (item, index) {
                            var gate = [];
                            pager.data.links.forEach(function (d, i) {
                                if (d.PasswayLink_PasswayNo == item.Passway_No) {
                                    gate[gate.length] = d;
                                }
                            });
                            var gateType = 1;
                            if (gate.length == 1) { gateType = gate[0].PasswayLink_GateType; }

                            var option = '<option value="' + item.Passway_No + '" data-gate="' + gateType + '">' + item.Passway_Name + '</option>';
                            $("#PolicyPass_PasswayNo").append(option);
                        });

                        carCardTypes.forEach(function (item, index) {
                            var option = '<option value="' + item.CarCardType_No + '">' + item.CarCardType_Name + '</option>';
                            $("#PolicyPass_CarCardTypeNo").append(option);
                        });
                    }
                }, "json");

                layui.form.render();
                //showTable();
            },
            bindData: function () {

            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        //showTable();
                        pager.passways = data.value;
                    } else if (data.elem.id == "PolicyPass_NoFundEnter") {
                        showNoFundInput();
                    } else if (data.elem.id == "PolicyPass_CarCardTypeNo") {
                        pager.multi = data.value;
                    }
                });

                $("#Cancel").click(function () { parent.layer.closeAll(); });

                $("#Save").click(function () {
                    var passwayno = $("#PolicyPass_PasswayNo").val();
                    if (!passwayno) { layer.msg("请选择车道"); return; }
                    if (pager.multi == null || pager.multi.length == 0) { layer.msg("请选择车牌类型"); return; }

                    var param = {};
                    $(".layui-card-body").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide"))
                                param[$(this).attr('id')] = $(this).val();
                        }
                    });

                    console.log(pager.passways)
                    console.log(pager.multi)
                    console.log(param)

                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "当前批量设置将会覆盖之前的单个设置，确定保存?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("BatchSave", {
                                PasswayNoList: JSON.stringify(pager.passways),
                                CarCardTypeNoList: JSON.stringify(pager.multi),
                                data: JSON.stringify(param)
                            }, function (json) {
                                if (json.success)
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        parent.policy.pass.onload();
                                        parent.layer.closeAll();
                                    });
                                else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        }
    </script>
    <script id="utils">
        var showTable = function () {
            var gateType = $("#PolicyPass_PasswayNo").find("option:selected").attr("data-gate");
            if (gateType == 0) {
                $(".ingate").removeClass("layui-hide").addClass("layui-hide");
                $(".outgate").removeClass("layui-hide");
            } else {
                $(".outgate").removeClass("layui-hide").addClass("layui-hide");
                $(".ingate").removeClass("layui-hide");
            }
        }

        var showNoFundInput = function () {
            var v = $("#PolicyPass_NoFundEnter").val();
            if (v == 4)
                $("#PolicyPass_MinAmount").removeClass("layui-hide");
            else
                $("#PolicyPass_MinAmount").removeClass("layui-hide").addClass("layui-hide");
        }
    </script>
</body>
</html>
