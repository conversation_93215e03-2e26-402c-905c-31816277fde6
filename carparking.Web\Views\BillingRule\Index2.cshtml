﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>计费规则</title>
    <link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .fa { margin: 10px 4px; float: left; font-size: 16px; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }

        .card { height: 375px; border: 1px solid #e9eefd !important; padding: 6px; }
        .card-header { /*background: linear-gradient(to right,#e9eefd,#e9eefd,#e9eefd) !important;*/ background-color: #e9eefd !important; padding: 10px !important; font-weight: bold; }
        .edit-label { text-align: left; }
        .right { display: inline-block; text-align: right !important; float: right; margin-top: 0px; }
        .card-content { text-align: left; align-content: center; padding-top: 7px; max-height: 57px; overflow: auto; }
        .card-desc { text-align: justify; align-content: center; margin-top: 7px; height: 75px; overflow-y: auto; word-break: break-all; }
        .btn { padding: 3px 15px; }
        .cards { margin-top: 15px; }
        .layui-btn-sm { height: 38px; line-height: 38px; }
        .layui-none { line-height: 26px; padding: 30px 15px; text-align: center; color: #999; }

        .layui-icon2 { padding: 0 2px; vertical-align: middle\9; vertical-align: bottom; }
        .layui-icon2 { font-family: layui-icon !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

        .boxbtn { border: 0; border-radius: 3px; }
        .btn-danger { background-color: #d9534f; color: #fff; border: 0px solid #fefcfc; }
        .btn-success { background-color: rgb(92, 184, 92); color: #fff /*rgba(0,0,0,0)*/; border: 0px solid #fefcfc; }
        .layui-icon2 { font-size: 18px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>计费规则</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ChargeRules_Name" id="ChargeRules_Name" autocomplete="off" placeholder="规则名称" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="ChargeRules_CarCardTypeNo" name="ChargeRules_CarCardTypeNo" data-key="BillRuleTemp_CarCardType">
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="ChargeRules_CarTypeNo" name="ChargeRules_CarTypeNo" data-key="BillRuleTemp_CarType">
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="ChargeRules_ParkAreaNo" name="ChargeRules_ParkAreaNo" data-key="BillRuleTemp_ParkArea">
                                    <option value="">停车区域</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <button class="layui-btn layui-btn-sm" lay-event="Add" id="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                        <button class="layui-btn layui-btn-sm" lay-event="Copy" id="Copy"><i class="fa fa-recycle"></i><t>复制</t></button>
                        <div class="layui-none layui-hide">无数据</div>
                        <div class="layui-row layui-col-space15 cards">
                        </div>
                        <script type="text/x-jquery-tmpl" id="tmplcards">
                            <div class=" layui-col-md4" style="max-height:389px;">
                                <div class="layui-card card" style="max-height:369px;">
                                    <div class="layui-card-header card-header">
                                        ${ChargeRules_Name}
                                        <div class="right">
                                            <button class="edit btn-success boxbtn" lay-event="Update" id="Update" data-key="${ChargeRules_No}" title="编辑"><i class="layui-icon2 layui-icon-edit"></i></button>
                                            &nbsp;<button class="delete btn-danger boxbtn" lay-event="Delete" id="Delete" data-key="${ChargeRules_No}" title="删除"><i class="layui-icon2 layui-icon-delete"></i></button>
                                            &nbsp;<button class="test btn-primary boxbtn" lay-event="Test" id="Test" data-key="${ChargeRules_No}" title="测试"><i class="layui-icon2 layui-icon-tree"></i></button>
                                        </div>
                                    </div>
                                    <div class="layui-card-body">
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">车牌类型</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-content"> ${ChargeRules_CarCardTypeName}</div>
                                            </div>
                                        </div>
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">车牌颜色</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-content">${ChargeRules_CarTypeName}</div>
                                            </div>
                                        </div>
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">停车区域</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-content">${ChargeRules_ParkAreaName}</div>
                                            </div>
                                            <div class="layui-col-xs4 edit-label right"></div>
                                        </div>
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">规则有效</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-content">
                                                    ${linkTime(ChargeRules_BeginTime,ChargeRules_EndTime)}
                                                    {{if getStatus(ChargeRules_BeginTime,ChargeRules_EndTime)==1}}
                                                    <span class="layui-badge layui-bg-green">未生效</span>
                                                    {{else getStatus(ChargeRules_BeginTime,ChargeRules_EndTime)==2}}
                                                    <span class="layui-badge layui-bg-blue">已生效</span>
                                                    {{else getStatus(ChargeRules_BeginTime,ChargeRules_EndTime)==3}}
                                                    <span class="layui-badge layui-bg-cyan">已过期</span>
                                                    {{/if}}
                                                </div>
                                            </div>
                                            <div class="layui-col-xs4 edit-label right"></div>
                                        </div>
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">跨段拆分</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-content">
                                                    {{if ChargeRules_Across==1}}启用{{else}}禁用{{/if}}
                                                </div>
                                            </div>
                                            <div class="layui-col-xs4 edit-label right"></div>
                                        </div>
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">超时收费</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-content">
                                                    {{if ChargeRules_OverTime==1}}   <span class="layui-badge layui-bg-green">是</span>{{else}}   <span class="layui-badge layui-bg-cyan">否</span>{{/if}}
                                                </div>
                                            </div>
                                            <div class="layui-col-xs4 edit-label right"></div>
                                        </div>
                                        <div class="layui-row">
                                            <div class="layui-col-xs3 edit-label ">规则描述</div>
                                            <div class="layui-col-xs9 edit-ipt-ban">
                                                <div class="card-desc">${ChargeRules_Remark}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplarea">
        <option value="${ParkArea_No}" data-type="${ParkArea_Type}">${ParkArea_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.ChargeRules_Status ==1){}}
        <span class="layui-badge layui-bg-blue">启用</span>
        {{# }else{}}
        <span class="layui-badge layui-bg-red">禁用</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            layuiForm = layui.form;

            pager.init();
        });

        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd 00:00:00"));
        function linkTime(ChargeRules_BeginTime, ChargeRules_EndTime) {
            if (ChargeRules_BeginTime && ChargeRules_EndTime) {
                var time = new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd") + " - " + new Date(ChargeRules_EndTime).Format("yyyy-MM-dd");
                return time;
            } else
                return "";
        }
        function getStatus(ChargeRules_BeginTime, ChargeRules_EndTime) {

            if (ChargeRules_BeginTime && ChargeRules_EndTime) {

                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd 00:00:00"));
                ChargeRules_EndTime = new Date(new Date(ChargeRules_EndTime).Format("yyyy-MM-dd 00:00:00"));

                if (ChargeRules_EndTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }
    </script>
    <script>



        var pager = {
            ChargeRules_No: null,
            parkAreas: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            category: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindData();
                pager.bindEvent();
                pager.bindPower();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.carCardTypes = json.data.carCardTypes;
                        pager.carTypes = json.data.carTypes;
                        pager.parkAreas = json.data.parkAreas;

                        $("#ChargeRules_CarCardTypeNo").html('<option value="">车牌类型</option>');
                        $("#ChargeRules_CarTypeNo").html('<option value="">车牌颜色</option>');
                        $("#ChargeRules_ParkAreaNo").html(' <option value="">停车区域</option>');

                        $("#ChargeRules_CarCardTypeNo").append($("#tmplcarcardtype").tmpl(pager.carCardTypes));
                        $("#ChargeRules_CarTypeNo").append($("#tmplcartype").tmpl(pager.carTypes));
                        $("#ChargeRules_ParkAreaNo").append($("#tmplarea").tmpl(pager.parkAreas));

                        layuiForm.render("select")
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                //comtable.reload({
                //    url: '/BillingRule/GetChargeRulesList'
                //    , where: { conditionParam: JSON.stringify(conditionParam) }
                //    , page: { curr: index }
                //});

                //tmplcards
                $("#Search").attr("disabled", true);
                layer.msg("处理中", { icon: 16, time: 0 });
                $.ajax({
                    type: 'post',
                    url: '/BillingRule/GetChargeRulesList?r=' + Math.random(),
                    dataType: 'json',
                    data: { conditionParam: JSON.stringify(conditionParam) },
                    success: function (json) {
                        if (json.code == 0) {
                            layer.msg("查询成功", { icon: 1, time: 1000 });
                            if (json.data == null || json.data.length == 0) {
                                $(".layui-none").removeClass("layui-hide");
                                $(".cards").html("");
                            } else {
                                $(".layui-none").addClass("layui-hide");
                                $(".cards").html($("#tmplcards").tmpl(json.data));
                            }

                            pager.bindItemEvent();
                            pager.bindPower();
                        } else {
                            layer.msg("查询失败：" + json.msg, { icon: 0 });
                        }
                    },
                    complete: function () {
                        $("#Search").attr("disabled", false);
                    },
                    error: function () {
                        layer.msg("查询失败", { icon: 2 });
                    }
                });


            },
            bindEvent: function () {
                layuiForm.on("select", function (data) {
                    pager.bindData(1);
                });
                $("#Search").click(function () { pager.bindData(1); });

                $("#Add").click(function () {
                    layer.open({
                        type: 2, id: 1,
                        title: "新增计费规则",
                        content: 'TempRule?Act=Add',
                        area: getIframeArea(['65%', '99%']),
                        maxmin: true
                    });
                });

                $("#Copy").click(function () {
                    layer.open({
                        type: 2, id: 1,
                        title: "复制计费规则",
                        content: 'CopyRule?Act=Add',
                        area: getIframeArea(['65%', '80%']),
                        maxmin: true
                    });
                });

                if (Power.BillingRule.Add != "true") {
                    document.getElementById("Add").style.display = "none";
                }
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });
            },
            bindItemEvent: function () {
                $(".edit").unbind("click").click(function () {
                    var ChargeRules_No = $(this).attr("data-key");
                    var action_name = "TempRule";
                    if (ChargeRules_No.indexOf("YC") == 0) { action_name = "YinChuanRule"; }
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑计费规则",
                        content: action_name + '?Act=Update&ChargeRules_No=' + ChargeRules_No,
                        area: getIframeArea(['65%', '99%']),
                        maxmin: true
                    });
                })

                $(".test").unbind("click").click(function () {
                    var ChargeRules_No = $(this).attr("data-key");
                    layer.open({
                        type: 2, id: 1,
                        title: "测试计费规则",
                        content: 'Debug?Act=Debug&ChargeRules_No=' + ChargeRules_No,
                        area: getIframeArea(['65%', '85%']),
                        maxmin: true
                    });
                })

                $(".delete").unbind("click").click(function () {
                    var ChargeRules_No = $(this).attr("data-key");
                    if (!ChargeRules_No) { layer.msg("参数不能为空", { icon: 5, time: 1500 }); return; }
                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定删除?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $(this).attr("disabled", true);
                            $.post("DelChargeRules", { ChargeRules_No: ChargeRules_No }, function (json) {
                                $(this).removeAttr("disabled");
                                if (json.success) {
                                    layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                        pager.bindData();
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0 });
                                }
                            }, "json");
                        },
                        btn2: function () { }
                    })
                })
            }
        }

        var CraeteCols = function () {

            var category = $("#MonthRule_CarCardTypeNo").find("option:selected").attr("data-category") || "3";
            var cols = [];
            cols[cols.length] = { type: 'radio' };
            cols[cols.length] = { field: 'MonthRule_No', title: '规则编码', width: 160 };
            cols[cols.length] = { field: 'MonthRule_CarCardTypeName', title: '车牌类型' };
            cols[cols.length] = { field: 'MonthRule_CarTypeName', title: '车牌颜色' };
            if (category == '3')
                cols[cols.length] = { field: 'MonthRule_Cycle', title: '周期数', toolbar: "#tmplCycle" };
            else if (category == '2')
                cols[cols.length] = { field: 'MonthRule_EMoney', title: '充值金额(元)' };
            cols[cols.length] = { field: 'MonthRule_Money', title: '收费金额(元)' };

            return cols;
        }
    </script>
</body>
</html>
