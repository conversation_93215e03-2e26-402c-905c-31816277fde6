﻿<!DOCTYPE html>
<html>
<head>
    <script>
        var top = window.location.href.indexOf("top=false");
        if (window.top !== window.self && top < 0) {
            window.top.location = window.location;
        }

        var model = '@Html.Raw(ViewBag.model)';
        localStorage.setItem('sysconfig', model);
    </script>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>登录-T30停车管理系统</title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <style>
        @@media screen and (max-width: 400px) {
            main { width: 80% !important; margin-left: 10%; }
            main form { right: auto !important; width: 100%; }
            .title { margin: 2rem !important; font-size: 1.5rem !important; }
            .footer { display: none !important; }
        }
        html, body { margin: 0; padding: 0; height: 100%; width: 100%; min-width: 1024px !important; overflow: hidden; font-size: 13px; }
        body { font-family: 'Microsoft YaHei'; background-image: url(../Static/img/login-bg.png); background-size: 100% 100%; }
        .footer { position: absolute; line-height: 50px; bottom: 0; width: 100%; text-align: center; color: #fff; background-color: rgba(0,0,0,0.1); }

        .title { margin: 60px 0 0 160px; font-size: 3rem; color: #fff; font-family: sans-serif,'Microsoft YaHei'; }
        .title .logo { float: left; }
        .title .logo img { height: 60px; }
        .title text { margin: 0 15px; line-height: 60px; }

        ::-webkit-input-placeholder { color: #ddd; }
        ::-moz-placeholder { color: #ddd; }
        :-ms-input-placeholder { color: #ddd; }

        input:-webkit-autofill,
        textarea:-webkit-autofill,
        select:-webkit-autofill { -webkit-text-fill-color: #fff !important; -webkit-box-shadow: 0 0 0px 1000px transparent inset !important; background-color: transparent; background-image: none; transition: background-color 50000s ease-in-out 0s; }
        input { background-color: transparent; }
    </style>

    <link href="~/Static/js/ui.roboto/style.css" rel="stylesheet" />
    <style>
        .form { position: absolute !important; left: calc(50% - 180px); top: calc(50% - 180px); width: 360px; height: 360px; }
        .table { width: 400px; height: 350px; margin: 80px auto; }
        .table form { width: 100%; }
        .table .name { width: 280px; margin: 20px auto 30px auto; display: block; height: 30px; border-radius: 20px; border: none; background: rgba(0,0,0,0.2); text-indent: 0.5em; }
        .table .btn { width: 100px; height: 30px; background: rgba(0,0,0,0.1); border-radius: 8px; border: none; color: white; margin: 0 auto; display: block; }
        .styled-button .styled-button__text { color: #fff; }
    </style>
</head>
<body id="loginBody">
    <div class="title">
        <span class="logo"><img src="~/Static/img/icon/icon_logo.svg" /></span>
        <text id="PlatformName">T30停车管理系统</text>
    </div>
    <main>
        <form class="form">
            <div class="form__cover"></div>
            <div class="form__content">
                <h1>账号登录</h1>
                <div class="styled-input">
                    <input type="text" class="styled-input__input" name="nickname" autocomplete="off" id="Admins_Account" placeholder="请输入账号">
                </div>
                <div class="styled-input">
                    <input type="password" class="styled-input__input" autocomplete="off" id="Admins_Pwd" placeholder="请输入密码">
                </div>
                <button type="button" class="styled-button" id="btnLogin">
                    <span class="styled-button__text">登录</span>
                </button>
            </div>
        </form>
    </main>
    <div class="footer">
        @*<span style="float:left;margin-left:20px;"><a href="http://www.beian.miit.gov.cn" target="_blank" style="color:#0094ff;">粤ICP备********号-1</a></span>*@
        <span style="float:right;margin-right:20px;" data-lan="lg_foot_tip">为了更好的体验效果 推荐使用谷歌浏览器</span>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js" asp-append-version="true"></script>
    <script src="~/Static/js/ui.roboto/index.js" asp-append-version="true"></script>
    <script>
        $(function () {
            $("#Admins_Account").focus();
            loginFrm.bindEvent();
        });

        var user_code = $.getUrlParam("user_code");
        if (user_code) console.log("user_code：" + user_code);

        var loginFrm = {
            bindEvent: function () {
                $('#btnLogin').click(function () {
                    ToLogin();
                });

                //回车登录
                $("#Admins_Account").keydown(function (event) {
                    if (event.keyCode == 13) {
                        ToLogin();
                    }
                });

                $("#Admins_Pwd").keydown(function (event) {
                    if (event.keyCode == 13) {
                        ToLogin();
                    }
                });

                function ToLogin() {
                    var account = $("#Admins_Account").val().trim();
                    var pwd = $("#Admins_Pwd").val().trim();
                    if (account.length < 5) {
                        layer.tips("账号不得少于5位", '#Admins_Account', { tips: [3] });
                        return false;
                    }
                    if (pwd.length < 6) {
                        layer.tips("密码不得少于6位", '#Admins_Pwd', { tips: [3] });
                        return false;
                    }

                    $('#btnLogin').button('loading');
                    $.post("/Login/ToLogin", { Admins_Account: account, Admins_Pwd: pwd },
                        function (data) {
                            if (data.Success) {
                                localData.set('logintype', 'platform');
                                localStorage.removeItem("pm_defualutCommunity");
                                //localStorage.setItem("layuiAdmin", JSON.stringify(theme));
                                document.location.href = "../index";
                                localData.set("Admins_Account", account);
                                var pwdStrength = CheckPwdStrength(pwd);
                                localData.set('pwdStrength', pwdStrength);
                            } else {
                                setTimeout(function () {
                                    $('#btnLogin').button('reset');
                                }, 500);
                                layer.tips(data.Message, '#btnLogin', { tips: [3] });
                            }
                        }, "json");
                    return false; //防止submit按钮自动刷新一次页面
                }

            }
        }

        function CheckPwdStrength(val) {
            var lv = 0;
            //字母+数字
            if (val.match(/^(?!\d+$)(?![a-zA-Z]+$)[a-zA-Z\d]+$/)) { lv = 2; }
            return lv;
        };
        //var theme = { "theme": { "color": { "main": "#000fff", "logo": "#AAAA00", "selected": "#AA3130", "alias": "fashion-red", "index": 7 } } };
    </script>
</body>
</html>
