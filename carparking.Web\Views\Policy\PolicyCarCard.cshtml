﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>功能策略设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <script src="~/Static/plugins/carnopicker/carnopicker.js?1" asp-append-version="true"></script>
    <style>
        html, body { background-color: #fff !important; }
        .layui-tab-title { padding-left: 2rem; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }
        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }
        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }
        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_park1.svg'); }
        .layui-tab-title li.layui-this.type5::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-content { padding: 2rem; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }
        .layui-disabled { background-color: #eee; opacity: 1; }
        .layui-form-select dl { box-shadow: 0 0 6px; }

        input[value='自动放行'] { color: #1ab394 !important; }
        input[value='禁止通行'] { color: red !important; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }
        select[multiple] + .layui-form-select dd { padding: 0; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }
        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }
        .multiSelect a { padding: 2px 5px; background: #0094ff; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }
        .multiSelect a span { float: left; }
        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }
        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }
        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }
        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }
        .multiSelect a i:hover { background-color: #545556; }
        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }
        .multiOption:hover { color: #5FB878 }
        .simplehide { display: none; }
        .moresetting { display: none; }
        .headmoresetting { cursor: pointer; color: #1e9fff; }
        .headmoresetting:hover { font-weight: 600; }
        .otherdesc { display: none; }
        .descicon { cursor: pointer; font-size: 1.1rem; }
        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }
        .help-btn { position: absolute; width: 20px; margin-left: 7px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .dot { display: inline-block; width: 7px; height: 7px; background-color: red;  border-radius: 50%;  }
        .bottomButton { position: fixed; bottom: 0; left: 0; width: 100%; background: #fff; padding: 10px; z-index: 9999; }
        .layui-table { margin-bottom: 50px; }
        tr td:first-child { min-width: 100px; }
        tr td:nth-child(2) { min-width: 200px; }
    </style>
</head>
<body>
    <div class="layui-form" style="background-color:#fff !important;">

        <!--车牌计费设置-->
        <div class="layui-row">
            <div class="layui-inline">
                <select class="layui-select" lay-search id="PolicyCarCard_CarCardTypeNo" name="PolicyCarCard_CarCardTypeNo">
                    <option value="">车牌类型</option>
                </select>
            </div>
        </div>
        <table class="layui-table">
            <thead>
                <tr>
                    <th>功能类目</th>
                    <th>功能方案</th>
                    <th></th>
                    <th>注释</th>
                </tr>
            </thead>
            <tbody data-key="card" id="cardpanl">
                <tr class="carmonth freemonth">
                    <td>有效期到期天数提醒</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyCarCard_RemindDay" name="PolicyCarCard_RemindDay" maxlength="5" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        设置月租车和免费车有效期低于设定天数时，入出口设备显示播报车主剩余天数；<br />
                        <t style="color:red;">注意：系统默认"15"天，天数填"0"表示关闭提醒，填99999表示全部提醒。</t>
                    </td>
                </tr>

                <tr class="carmonth">
                    <td>过期宽限天数</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyCarCard_AllowExpireDay" name="PolicyCarCard_AllowExpireDay" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        设置月租车过期后可宽限天数，在宽限天数内过期月租车将按正常月租车进出；适用于月租车未能及时延期时，预留延期缓冲时间。<br />
                        <t style="color:red;">注意：超过宽限期仍未延期则按"过期处理方式"处理。设置"0"天表示到期立即按"过期处理方式"进行处理。</t>
                    </td>
                </tr>
                <tr class="carmonth">
                    <td>自动注销超期未延期的过期车牌</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyCarCard_AutoLogoutDay" name="PolicyCarCard_AutoLogoutDay" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        系统将在月租车过期超过设定天数后，自动注销符合条件的车辆。<br />
                        <t style="color:red;"><i class="dot"></i> 设置为 0 表示不启用自动注销功能；<br /><i class="dot"></i> 若车辆仍在场内，将不会被注销登记信息；<br /><i class="dot"></i> 注销操作将在每天凌晨 2:00 至 4:00 之间自动执行；<br /><i class="dot"></i> ⚠️注意：注销为不可逆操作，请在设置前谨慎确认。</t>
                    </td>
                </tr>
                @* <tr class="carmonth">
                <td>车主自助延期截止日</td>
                <td>
                <input type="text" class="layui-input v-date" id="PolicyCarCard_DelayMaxDate" name="PolicyCarCard_DelayMaxDate" />
                </td>
                <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                <td>车主自助延期最大截止日期，输入的格式要求：yyyy-MM-dd。用于年底充值优惠活动等</td>

                </tr> *@
                <tr class="layui-hide other">
                    <td>默认计费规则</td>
                    <td>
                        <select class="layui-select" id="PolicyCarCard_CalculateRule" name="PolicyCarCard_CalculateRule">
                            <option value="">请选择</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>如果该车牌类型没有在《计费规则》里定义有效的计费规则，那么车辆出场时使用当前设置的默认规则计费；如果能在《计费规则》里找到相应的计费规则，那么始终以定义计费规则为准，当前默认设置无效。</td>
                </tr>
                <tr class="storeExpire">
                    <td><expire>过期处理方式</expire></td>
                    <td>
                        <select class="layui-select" id="PolicyCarCard_ExpireHandle" name="PolicyCarCard_ExpireHandle">
                            <option value="0">忽略</option>
                            <!--加载车牌/卡类型数据-->
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        设置已登记的车牌类型过期后（或储值金额为零时）按指定的计费规则进行计费。<br />
                        <t style="color:red;">注意：①选择"忽略"时，将不计费并以设定的过期开闸方式进行处理（商家车即使选择"忽略"，也会按临时车收费）； ②因储值车无有效期概念，故该设置对储值车无效。</t>
                    </td>
                </tr>
                <tr class="stored">
                    <td>储值车余额不足语音提醒</td>
                    <td>
                        <input type="text" class="layui-input v-number v-null" id="PolicyCarCard_BalanceRemind" name="PolicyCarCard_BalanceRemind" maxlength="10" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        设置储值车余额低于设定金额时，入出口设备显示播报车牌的剩余金额；<br />
                        <t style="color:red;">注意：系统默认填"0"表示关闭提醒。例如：系统填"X"时，储值余额低于X元，入场显示播报"车牌号码+欢迎光临+余额X元+余额少请充值"；出场时播报显示"车牌号+本次收费金额+余额X元"。</t>
                    </td>
                </tr>
                <tr>
                    <td colspan="4">
                        <div class="layui-row headmoresetting"><t class="content">更多设置</t>&nbsp;<i class="layui-icon layui-icon-down"></i></div>
                    </td>
                </tr>
                <tr class="moresetting">
                    <td>每月最高收费限额</td>
                    <td>
                        <input type="text" class="layui-input v-floatLimit v-null" id="PolicyCarCard_MonthMaxMoney" name="PolicyCarCard_MonthMaxMoney" value="0" min="0" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        车辆当月累计收费已达最高限额时，无需再缴费 <br />
                        <t style="color:red;">
                            填写“0”表示该配置项不生效
                            <br/>
                            注意： <br />
                            1、追缴费用将单独计算，不计入每月限额。 <br />
                            2、该功能仅适用于旗舰版计费，不支持标准版计费。 <br />
                            3、车辆当月累计金额，自启用该配置项时开始统计缴费信息。<br/>
                            4、系统计费逻辑为：先抵扣优惠券，再判断是否超出限额，因此当车主已使用优惠券又触发限额时，可能出现实收金额未减免、应收金额反而增加的情况。
                        </t>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="layui-row savebtn">
            <button class="layui-btn layui-btn-sm saveAll" data-id="card">保存全部</button>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}" data-type="${CarCardType_Type}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplrule">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">以 ${CarCardType_Name} 计费规则计费</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1.9" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        if ('@carparking.Config.PubVar.iParkingType' == "1") {
            $("#PolicyPark_MaxDiscount").attr("disabled", true);
        }

        myVerify.init();
        var cp = new CarnoPicker("#PolicyPark_CarPrefix", { ischar: false });
        cp.init();
        layui.use(['element', 'form', 'laydate'], function () {
            pager.init();
        })


        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//访客车类型

        var pager = {
            parkareas: null,
            passways: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            drives: null,       //设备型号列表
            links: null,        //通道关联区域列表
            Province: [],
            City: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PolicyCarCard_DelayMaxDate"], { type: 'date' });

                pager.passways = parent.pager.passways;
                pager.carCardTypes = parent.pager.carCardTypes;
                pager.carTypes = parent.pager.carTypes;
                pager.drives = parent.pager.drives;
                pager.links = parent.pager.links;

                $("#PolicyCarCard_ExpireHandle").append($("#tmplrule").tmpl(pager.carCardTypes));

                var data = [], tempcar = [];
                pager.carCardTypes.forEach(function (item, index) {
                    //data[data.length] = item;
                    if (item.CarCardType_Type != 5) { data[data.length] = item; }//过滤访客车
                    if (item.CarCardType_Type == 1) { tempcar[tempcar.length] = item; }
                });
                $("#PolicyCarCard_CarCardTypeNo").html($("#tmplcarcardtype").tmpl(data));
                $("#PolicyPassway_DefaultCarCardType").append($("#tmplcarcardtype").tmpl(tempcar));

                layui.form.render();

                $("td").hover(function () {
                    //判断td里有headdesc样式
                    if ($(this).find("span.headdesc").length > 0) {
                        var $td = $(this).find("span.headdesc").siblings(".otherdesc");
                        var $div = $('<div>').append($td.contents().clone());
                        layer.tips($div.html(), this, {
                            tips: [1, '#090a0c'],
                            time: 0,
                            area: '50wh'  // 设置宽度为300px
                        });
                    }
                }, function () {
                    layer.closeAll('tips');
                });
            },
            bindData: function () {
                policy.card.onload();
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    //console.log(data.elem); //得到select原始DOM对象
                    //console.log(data.elem.id); //得到select原始DOM对象
                    //console.log(data.value); //得到被选中的值
                    //console.log(data.othis); //得到美化后的DOM对象

                    var val = data.value;
                    //开闸方式切换通道
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        policy.pass.onload();
                    }
                    //开闸方式切换车牌类型
                    else if (data.elem.id == "PolicyPass_CarCardTypeNo") {
                        policy.pass.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyPassway_PasswayNo") {
                        policy.passway.onload();
                    }
                    //车牌策略切换类型
                    else if (data.elem.id == "PolicyCarCard_CarCardTypeNo") {
                        policy.card.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyArea_ParkAreaNo") {
                        policy.area.onload();
                    }
                    //自定义语音设置
                    else if (data.elem.id == "PolicyPassway_ShowOption") {
                        policy.voicediy(data.elem.id, val);
                    }
                    //开闸方式-未找到入场记录最低收费标准
                    else if (data.elem.id == "PolicyPass_NoFundEnter") {
                        policy.minpayed(data.elem.id, val);
                    }
                    //发布内容
                    else if (data.elem.id == "PolicyPassway_Broadpushinfo") {
                        policy.pushinfo(data.elem.id, val);
                    }
                    //防疫设置
                    else if (data.elem.id == "PolicyArea_EPEnable") {
                        policy.fymodal(data.elem.id, val);
                    }
                    //车主车位已满后其余车辆
                    else if (data.elem.id == "PolicyArea_MoreCar") {
                        policy.opengate(data.elem.id, val);
                    }
                })

                $("button.save").click(function () {

                    if (!myVerify.check()) return;

                    var param = {};
                    $(this).parent().siblings().find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            var v = $(this).val();
                            if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                if (v == null || v == '') v = [];
                                v = JSON.stringify(v);
                            }

                            param[$(this).attr('id')] = v;
                        }
                    });

                    var datakey = $(this).parent().parent().parent().attr("data-key");
                    pager.onSave(datakey, param);
                });

                $("button.saveAll").click(function () {
                    if (!myVerify.check()) return;

                    var param = {};
                    var datakey = $(this).attr("data-id");
                    $("tbody[data-key='" + datakey + "']").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide")) {
                                var v = $(this).val();
                                if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                    if (v == null || v == '') v = [];
                                    v = JSON.stringify(v);
                                }

                                param[$(this).attr('id')] = v;
                            }
                        }
                    });
                    pager.onSave(datakey, param);
                });

                $("#BatchSetPassway").unbind("click").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/BatchSetPassWay",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $("#EditBatch").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/EditPassBatch",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $(".headmoresetting").unbind("click").click(function () {
                    var table = $(this).parent().parent().parent().find(".moresetting");
                    if ($(table).last().is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                        $(this).find("t").text("隐藏更多设置");
                        $(".savebtn").addClass("bottomButton");
                        var versionType1 = localStorage.getItem("versionType");
                        if (versionType1 == "simple") {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide").addClass("layui-hide").addClass("versionHide");
                        } else {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide");
                            if ($("#PolicyArea_EPEnable").val() == "1")
                                $(".fymodal").removeClass("layui-hide");
                            else
                                $(".fymodal").removeClass("layui-hide").addClass("layui-hide");

                        }
                    } else {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                        $(this).find("t").text("更多设置");
                        $(".savebtn").removeClass("bottomButton");
                    }
                    $(table).toggle("fast");
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                        $("button.saveAll").removeClass("layui-hide");
                    }

                    if (pagePower['EditBatch'] == 'true') {
                        $("#BatchSetPassway").removeClass("layui-hide");
                    }
                });
            },
            onSave: function (datakey, param) {
                //开闸方式保存
                if (datakey == 'pass') {
                    var PolicyPass_PasswayNo = $("#PolicyPass_PasswayNo").val();
                    var PolicyPass_CarCardTypeNo = $("#PolicyPass_CarCardTypeNo").val();
                    var obj = { PolicyPass_PasswayNo: PolicyPass_PasswayNo, PolicyPass_CarCardTypeNo: PolicyPass_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyPass", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车道设置保存
                else if (datakey == 'passway') {
                    var PolicyPassway_PasswayNo = $("#PolicyPassway_PasswayNo").val();
                    var obj = { PolicyPassway_PasswayNo: PolicyPassway_PasswayNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    // 处理PolicyPassway_DoubleGateColorCodes的值，过滤掉空值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    if (selectedColors && selectedColors.length > 0) {
                        selectedColors = selectedColors.filter(function(color) {
                            return color !== "" && color !== null && color !== undefined;
                        });
                    } else {
                        selectedColors = [];
                    }
                    obj.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));
                    console.log(obj)
                    $.post("SavePolicyPassway", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车牌类型策略保存
                else if (datakey == 'card') {
                    var PolicyCarCard_CarCardTypeNo = $("#PolicyCarCard_CarCardTypeNo").val();
                    var obj = { PolicyCarCard_CarCardTypeNo: PolicyCarCard_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyCarCard", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车场策略保存
                else if (datakey == 'park') {
                    var obj = {};
                    //obj[key] = val;
                    Object.assign(obj, param);
                    $.post("SavePolicyPark", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //区域策略保存
                else if (datakey == 'area') {
                    var obj = {};
                    //obj[key] = val;
                    var obj = { PolicyArea_ParkAreaNo: $("#PolicyArea_ParkAreaNo").val() };
                    Object.assign(obj, param);
                    console.log(obj)
                    $.post("SavePolicyArea", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.open({
                                icon: 0,
                                content: json.msg,
                                area: ["180px", "auto"],
                                btn: ["我知道了"],
                                closeBtn: false,
                            });
                            //layer.msg(json.msg);
                        }
                    }, "json");
                }
            }
        }

        var policy = {
            card: {
                onload: function () {
                    var PolicyCarCard_CarCardTypeNo = $("#PolicyCarCard_CarCardTypeNo").val();

                    var category = $("#PolicyCarCard_CarCardTypeNo").find('option:selected').attr('data-category');
                    var type = $("#PolicyCarCard_CarCardTypeNo").find('option:selected').attr('data-type');

                    $("tr.storeExpire").find("expire").html("过期处理方式");
                    //console.log(category)
                    if (type == 3) {//免费+月租类型显示【是否为白名单】
                        $("tr.carmonth").removeClass("layui-hide");
                        $("tr.other").removeClass("layui-hide").addClass("layui-hide");
                        $("tr.stored").removeClass("layui-hide").addClass("layui-hide");
                        $("tr.chuzhi").removeClass("layui-hide");
                    } else {

                        $("tr.carmonth").removeClass("layui-hide").addClass("layui-hide");
                        if (type == 4) $("tr.freemonth").removeClass("layui-hide");
                        $("tr.other").removeClass("layui-hide").addClass("layui-hide");
                        if (type == 2) {
                            $("tr.stored").removeClass("layui-hide");
                            $("tr.chuzhi").addClass("layui-hide");
                            $("tr.storeExpire").find("expire").html("储值金额为零时");
                        } else {
                            $("tr.chuzhi").removeClass("layui-hide");
                            $("tr.stored").removeClass("layui-hide").addClass("layui-hide");
                            $("tr.other").removeClass("layui-hide");

                            //获取当前类型的计费规则
                            var rules = [];
                            if (pager.carCardTypes && pager.carCardTypes.length > 0) {
                                pager.carCardTypes.forEach(function (item, index) {
                                    if (item.CarCardType_Category == category) {
                                        rules[rules.length] = item;
                                    }
                                });
                            }
                            $("#PolicyCarCard_CalculateRule").html($("#tmplrule").tmpl(rules))
                        }
                    }

                    $.post("GetPolicyCarCard", { PolicyCarCard_CarCardTypeNo: PolicyCarCard_CarCardTypeNo }, function (json) {
                        if (json.success) {
                            $("#cardpanl").fillForm(json.data, function (data) { });
                            layui.form.render()
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            },
            voicediy: function (id, val) {
                if (val == 5)
                    $("#PolicyPassway_Show").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Show").removeClass("layui-hide").addClass("layui-hide");
            },
            pushinfo: function (id, val) {
                if (val == 1)
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide").addClass("layui-hide");
            },
            minpayed: function (id, val) {
                if (val == 4)
                    $("#PolicyPass_MinAmount").removeClass("layui-hide");
                else
                    $("#PolicyPass_MinAmount").removeClass("layui-hide").addClass("layui-hide");
            },
            fymodal: function (id, val) {
                if (!$(".fymodal").hasClass("versionHide")) {
                    if (val == 1)
                        $(".fymodal").removeClass("layui-hide");
                    else
                        $(".fymodal").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            opengate: function (id, val) {
                if (!$(".opengate").hasClass("versionHide")) {
                    if (val != 3)
                        $(".opengate").removeClass("layui-hide");
                    else
                        $(".opengate").removeClass("layui-hide").addClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
