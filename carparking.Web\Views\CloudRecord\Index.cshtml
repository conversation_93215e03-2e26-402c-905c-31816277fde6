﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>云车场记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; }

        .layui-form-select .layui-input { width: 182px; }

        .content { margin: 7px 19px 0px 19px !important; cursor: pointer; }

        .content:hover { color: #1E9FFF; font-weight: 600; font-size: 20px; }

        span.ss { font-size: 13px; text-align: justify; word-break: break-all; color: #61a8d1; background-color: #f5eeee; float: left; padding: 3px 5px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>云车场记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <select class="layui-input" id="PushEvent_ReqName" name="PushEvent_ReqName" lay-search>
                                    <option value="">事件名称</option>
                                    @{
                                        foreach (var item in ViewBag.PushEventCommand)
                                        {
                                            <option value="@item.Key">@item.Value</option>
                                        }
                                    }
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-input" id="PushEvent_Success" name="PushEvent_Success" lay-search>
                                    <option value="">上传结果</option>
                                    <option value="0">未上传</option>
                                    <option value="1">已成功</option>
                                    <option value="2">已失败</option>
                                    <option value="3">已停止</option>
                                    <option value="5">已延迟</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="PushEvent_JsonData" id="PushEvent_JsonData" autocomplete="off" placeholder="事件消息模糊查询" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="PushEvent_Time" id="PushEvent_Time" autocomplete="off" placeholder="记录日期" value="@DateTime.Now.ToString("yyyy-MM-dd")" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" id="Upload" lay-event="Upload"><i class="layui-icon layui-icon-upload-drag"></i>重传</button>
                            </div>
                        </script>

                    </div>
                    <div class="layui-col-xs9"><span class="ss"><b>温馨提示：</b>当前界面记录着由B30服务器向云车场上报数据的实时状态，若上传结果未成功，表示服务器数据与云车场未能及时同步，可以手动点击【重传】,重新向云车场同步数据.</span></div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?230804" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.use(['table', 'form', 'laydate'], function () {
            pager.init();

            var table = layui.table;

            _DATE.bind(layui.laydate, ["PushEvent_Time"], { type: 'date' });

            var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                if (data.PushEvent_Time != null)
                    data.PushEvent_Time = new Date(data.PushEvent_Time).Format("yyyy-MM-dd 00:00:00");
                return data;
            });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'PushEvent_No', title: '事件编号', hide: true }
                , {
                    field: 'PushEvent_ReqName', title: '事件名称', templet: function (d) {
                        var os = @Html.Raw(carparking.Common.TyziTools.Json.ToString(ViewBag.PushEventCommand));
                        return os[d.PushEvent_ReqName];
                    }
                }
                , { field: 'PushEvent_Time', title: '事件时间' }
                , {
                    field: 'PushEvent_No', title: '事件消息', width: 100, templet: function (d) {
                        return '<i class="fa fa-search-plus content" title="点击查看" lay-event="showContent"></i>';//formatJson(d.PushResult_ReqPush);
                    }
                }
                , {
                    field: 'PushEvent_Success', title: '上传结果', templet: function (d) {
                        if (d.PushEvent_Success == 0) return tempBar(3, "未上传");
                        else if (d.PushEvent_Success == 1) return tempBar(1, "已成功");
                        else if (d.PushEvent_Success == 2) return tempBar(0, "已失败");
                        else if (d.PushEvent_Success == 3) return tempBar(4, "已停止");
                        else if (d.PushEvent_Success == 5) return tempBar(2, "已延迟");
                        else return "";
                    }
                }
                , { field: 'PushEvent_Errcode', title: '失败代码', hide: true }
                , { field: 'PushEvent_Errmsg', title: '失败描述' }
                , { field: 'PushEvent_Level', title: '优先等级' }
                , { field: 'PushEvent_Remark', title: '备注' }
                , { field: 'PushEvent_RepeatCount', title: '重发次数', hide: true }
                , { field: 'PushEvent_Three_Success', title: '转发状态', hide: true }
                , { field: 'PushEvent_Three_LastTime', title: '转发时间', hide: true }
                , { field: 'PushEvent_Three_Count', title: '转发次数', hide: true }
                , { field: 'PushEvent_Three_Message', title: '转发描述', hide: true }
            ]]

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CloudRecord/GetApiRecordList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data);
                }
            });



            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Upload':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        var nos = [];
                        data.forEach((item, index) => { nos[nos.length] = item.PushEvent_No; });
                        LAYER_OPEN_TYPE_0("确定重传选中的事件?", res => {
                            layer.msg("正在处理...", { icon: 16, time: 0 });
                            $.post("ReUpload", { jsonModel: JSON.stringify(nos), PushEvent_Time: data[0].PushEvent_Time }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                        pager.bindData(pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            }, "json");
                        }, res => { })
                        break;
                };
            });

            table.on("tool(com-table-base)", function (obj) {
                var data = obj.data;
                switch (obj.event) {
                    case 'showContent':
                        var d = [];
                        try {
                            d = JSON.parse(data.PushEvent_JsonData);
                        } catch {
                            d = data;
                        }

                        layer.open({
                            type: 1,
                            title: '事件消息',
                            area: ['700px', '520px'], //宽高
                            btn: ['关闭'],
                            content:
                                ' <div class="layui-content">' +
                                '<div class="layui-row"><pre>' +
                                JSON.stringify(d, null, 2) +
                                '</pre></div>' +
                                '</div>',
                            success: function () {
                            }
                        });
                        break;
                }
            });
            tb_row_checkbox();

        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                    if (data.PushEvent_Time != null)
                        data.PushEvent_Time = new Date(data.PushEvent_Time).Format("yyyy-MM-dd 00:00:00");
                    return data;
                });
                comtable.reload({
                    url: '/CloudRecord/GetApiRecordList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }

    </script>
</body>
</html>
