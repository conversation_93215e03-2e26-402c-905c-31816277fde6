﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>无感支付日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px; float: left; font: normal normal normal 14px/1 FontAwesome; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a>
                    <cite>系统管理</cite>
                </a>
                <a>
                    <cite>无感支付日志</cite>
                </a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                           
                            <div class="layui-inline">
                                <input class="layui-input " name="NoSensePayLogs_CarNo" id="NoSensePayLogs_CarNo" autocomplete="off" placeholder="车牌号码" />
                            </div>
                            <div class="layui-inline">
                                <select lay-search id="NoSensePayLogs_PayType" name="NoSensePayLogs_PayType">
                                    <option value="">无感支付方式</option>
                                    <option value="1">工行无感支付</option>
                                    <option value="2">微信无感支付</option>
                                    <option value="3">支付宝无感支付</option>
                                    <option value="4">招行无感支付</option>
                                    <option value="5">农行无感支付</option>
                                    <option value="6">银联无感支付</option>
                                    <option value="7">中行无感支付</option>
                                    <option value="8">建行无感支付</option>
                                    <option value="9">ETC(黔通)无感支付</option>
                                    <option value="10">第三方无感支付</option>
                                    <option value="11">ETC(信联盒子)无感支付</option>
                                    <option value="12">ETC(信联云端)无感支付</option>
                                    <option value="13">城市服务无感扣费</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select lay-search id="NoSensePayLogs_Status" name="NoSensePayLogs_Status">
                                    <option value="">无感支付状态</option>
                                    <option value="100">支付成功并关单</option>
                                    <option value="101">支付不成功</option>
                                    <option value="102">支付未响应或超时</option>
                                    <option value="103">支付成功但未关单</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="StartTime" id="StartTime" autocomplete="off" placeholder="记录开始时间" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="EndTime" id="EndTime" autocomplete="off" placeholder="记录结束时间" value='@DateTime.Now.ToString("yyyy-MM-dd 23:59:59")' />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search">
                                    <i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t>
                                </button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="NoSensePayLogs_PayOrderNo" id="NoSensePayLogs_PayOrderNo" autocomplete="off" placeholder="支付订单" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="NoSensePayLogs_LaneName" id="NoSensePayLogs_LaneName" autocomplete="off" placeholder="出口车道" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="NoSensePayLogs_BoothName" id="NoSensePayLogs_BoothName" autocomplete="off" placeholder="出口岗亭" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="NoSensePayLogs_Remark" id="NoSensePayLogs_Remark" autocomplete="off" placeholder="备注信息" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        topBar.init();

        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {

            pager.init();
            var table = layui.table;
            $("#StartTime").val(new Date().Format("yyyy-MM-dd 00:00:00"));
            $("#EndTime").val(new Date().Format("yyyy-MM-dd 23:59:59"));
            _DATE.bind(layui.laydate, ["StartTime", "EndTime"], { type: 'datetime', range: true });
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            pager.conditionParam = conditionParam;

            var cols = [[
                { field: 'NoSensePayLogs_ID', width: 80, title: '编号' }
                , {
                    field: 'NoSensePayLogs_PayType', title: '无感支付类型', width: 150, templet: function (d) {
                        //0未定义 1工行无感支付 2微信无感支付 3支付宝无感支付 4招行无感支付 5农行无感支付 6银联无感支付 7中行无感支付 8建行无感支付 9ETC(黔通)无感支付 10第三方无感支付 11ETC(信联盒子)无感支付 12ETC(信联云端)无感支付 13城市服务无感扣费
                        if (d.NoSensePayLogs_PayType === 1) return tempBar1(1, "工行无感支付");
                        else if (d.NoSensePayLogs_PayType === 2) return tempBar1(2, "微信无感支付");
                        else if (d.NoSensePayLogs_PayType === 3) return tempBar1(4, "支付宝无感支付");
                        else if (d.NoSensePayLogs_PayType === 4) return tempBar1(3, "招行无感支付");
                        else if (d.NoSensePayLogs_PayType === 5) return tempBar1(5, "农行无感支付");
                        else if (d.NoSensePayLogs_PayType === 6) return tempBar1(6, "银联无感支付");
                        else if (d.NoSensePayLogs_PayType === 7) return tempBar1(7, "中行无感支付");
                        else if (d.NoSensePayLogs_PayType === 8) return tempBar1(8, "建行无感支付");
                        else if (d.NoSensePayLogs_PayType === 9) return tempBar1(9, "ETC(黔通)无感支付");
                        else if (d.NoSensePayLogs_PayType === 10) return tempBar1(10, "第三方无感支付");
                        else if (d.NoSensePayLogs_PayType === 11) return tempBar1(11, "ETC(信联盒子)无感支付");
                        else if (d.NoSensePayLogs_PayType === 12) return tempBar1(12, "ETC(信联云端)无感支付");
                        else if (d.NoSensePayLogs_PayType === 13) return tempBar1(13, "城市服务无感扣费");
                        else return tempBar1(14, "未定义");
                    }
                }
                , { field: 'NoSensePayLogs_PayOrderNo', width: 150, title: '支付订单' }
                , { field: 'NoSensePayLogs_LaneName', width: 160, title: '出口名称' }
                , { field: 'NoSensePayLogs_BoothName', width: 160, title: '出口岗亭' }
                , { field: 'NoSensePayLogs_Response', width: 300, title: '响应内容' }
                , { field: 'NoSensePayLogs_AddTime', width: 180, title: '支付时间' }
                , {
                    field: 'NoSensePayLogs_Status', width: 180, title: '无感支付的状态', templet: function (d) {
                        //0未定义 100支付成功并关单 101支付不成功 102支付未响应或超时 103支付成功但未关单
                        if (d.NoSensePayLogs_Status === 100) return tempBar1(2, "支付成功并关单");
                        else if (d.NoSensePayLogs_Status === 101) return tempBar1(6, "支付不成功");
                        else if (d.NoSensePayLogs_Status === 102) return tempBar1(1, "支付未响应或超时");
                        else if (d.NoSensePayLogs_Status === 103) return tempBar1(3, "支付成功但未关单");
                        else return tempBar1(5, "未定义");
                    }
                }
                , { field: 'NoSensePayLogs_Remark', title: '备注信息' }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/NoSensePayLogs/QueryLogs'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    pager.dataCount = d.count;
                    tb_page_set(d);
                }
            });
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            conditionParam: null,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index) {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                comtable.reload({
                    url: '/NoSensePayLogs/QueryLogs'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
        $(function () { pager.bindSelect(); });
    </script>
</body>
</html>