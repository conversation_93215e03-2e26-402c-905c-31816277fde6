﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        span.ss { font-size: 12px; text-align: justify; word-break: break-all; color: #888; background-color: lemonchiffon; width: calc(100% - 10px); float: left; padding: 3px 5px; }
        .layui-input[readonly] { background-color: initial !important; }

        .model_input { border: none !important; outline: none; width: 50px; padding: 0px 0px; height: 95%; text-align: center; color: #0e0e0e; }
        .model_text { height: 38px; border: 1px solid #e6e6e6; padding: 2px; border-radius: 2px; resize: both; box-sizing: border-box; max-width: 95%; }
        .layui-tab { margin-top: 0px; height: 99%; }
        .layui-tab-item ,.layui-show ,.layui-tab-content { height: 99% !important; }
        html, body { height: 95%; }
    </style>
</head>
<body>
    <div class="layui-tab layui-tab-card">
        <ul class="layui-tab-title">
            <li class="layui-this">音量</li>
            <li>补光灯</li>
            <li>广告</li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show"><iframe id="ifSound" src="ScreenSoundEdit" frameborder="0" width="100%" height="100%"></iframe></div>
            <div class="layui-tab-item"><iframe  id="ifLed" src="" frameborder="0" width="100%" height="100%"></iframe></div>
            <div class="layui-tab-item"><iframe  id="ifAdvert" src="" frameborder="0" width="100%" height="100%"></iframe></div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var pager = {
            SelDeviceNoes: null,
            SelDeviceNames: null,
        }

        pager.SelDeviceNoes = parent.pager.SelDeviceNoes;  //decodeURIComponent($.getUrlParam("Device_No"));
        pager.SelDeviceNames = parent.pager.SelDeviceNames;// decodeURIComponent($.getUrlParam("Device_Name"));
        var index = parent.layer.getFrameIndex(window.name);
        console.log(pager.SelDeviceNames)

        $(function(){

            $("#ifSound").attr("src", "ScreenSoundEdit?t=" + Math.random())
            $("#ifLed").attr("src", "ScreenLedEdit?t=" + Math.random())
            $("#ifAdvert").attr("src", "ScreenAdvertEdit?t=" + Math.random())

        });


    </script>

</body>
</html>
