﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>值班中心设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场配置</cite></a>
                <a><cite>值班中心</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="DeviceTalk_No" id="DeviceTalk_No" autocomplete="off" placeholder="值班中心识别码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="DeviceTalk_Name" id="DeviceTalk_Name" autocomplete="off" placeholder="值班中心名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.DeviceTalk.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.DeviceTalk.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.DeviceTalk.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'DeviceTalk_No', title: '值班中心识别码' }
                , { field: 'DeviceTalk_Name', title: '值班中心名称' }
                 , { field: 'DeviceTalk_TcpIp', title: '值班中心IP' }
                , { field: 'DeviceTalk_Remark', title: '备注' }
                , {
                    field: 'DeviceTalk_Status', title: '值班中心状态', templet: function (d) {
                        if (d.DeviceTalk_Status == 1) { return tempBar(1, '繁忙'); } else if (d.DeviceTalk_Status == 2) { return tempBar(3, '离线') } else { return tempBar(2, '空闲') };
                    }
                }
                , { field: 'DeviceTalk_AddTime', title: '注册时间' }
                , { field: 'Admins_Name', title: '操作员' }
            ]];

            cols = tb_page_cols(cols);
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/DeviceTalk/GetDeviceTalkList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增值班中心设置",
                            content: 'Edit?Act=Add',
                            area: getIframeArea(['600px', '650px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑值班中心设置",
                            content: 'Edit?Act=Update&DeviceTalk_No=' + data[0].DeviceTalk_No,
                            area: getIframeArea(['600px', '650px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { DeviceTalk_No: data[0].DeviceTalk_No }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();

            layui.form.render("select");
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/DeviceTalk/GetDeviceTalkList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
