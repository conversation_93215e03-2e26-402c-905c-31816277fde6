﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车辆登记</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?t=@DateTime.Now.Ticks" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v1.0" rel="stylesheet" />
    <style>

        :root { --line-border-fill: #01aaed; --line-border-empty: linear-gradient(to right, rgb(161 238 239 / 80%), rgba(0, 0, 0, 0.75)); }
        .circle::before { content: ""; position: absolute; top: 50%; left: 0; width: 100%; height: 2px; background: var(--line-border-empty); z-index: -1; }
        .circle:last-child::before { width: 50%; left: 10%; }
        .container { display: flex; flex-direction: column; align-items: center; justify-content: center; }
        .progress { display: flex; position: relative; justify-content: space-between; width: 350px; }
        .progress::before { content: ''; background-color: var(--line-border-empty); position: absolute; top: 50%; left: 0; transform: translateY(-50%); height: 4px; width: 100%; z-index: -1; }
        .progress-bar { position: absolute; background-color: var(--line-border-fill); width: 0; height: 4px; top: 52%; left: 0; z-index: -1; transform: translateY(-50%); transition: 0.3s ease; }
        .circle { background: red; width: 2rem; aspect-ratio: 1; display: flex; justify-content: center; align-items: center; border-radius: 50%; border: 3px solid #e7ebed; transition: 0.4s ease-in; background: linear-gradient(45deg,rgb(238 242 249 / 100%),rgb(50 136 137),rgb(178 189 195 / 100%)); color: #22ebc8; }
        .circle.active { border: 3px solid var(--line-border-fill); }
        .circle.active span { color: #01aaed; }
        .circle span { position: absolute; top: 100%; color: #a8c1b8; white-space: nowrap; }


        .layui-input.timerange { width: 300px; }
        .layui-layer-btn-l { margin-left: 8.33%; }
        .layui-btn-warm { background-color: #f0ad4e; }

        .tablediv { }

        .list_con_old, .list_con { display: inline-block; /* width: 100%; */ line-height: 2rem; border: 1px solid #ffffff; margin-left: 5px; margin-top: 2px; background-color: #4b9fff; border-radius: 5px; padding-left: 1px; position: relative; padding: 0 0.5rem; color: #fff; font-weight: 800; }
        .content, .list_wrap { width: 99%; height: 100%; overflow-x: hidden; margin-left: 1rem; }
        .list_con_old { margin: 0 !important; border: 0 !important; background-color: #cffafb; margin-bottom: 2px !important; margin-top: 2px !important; }

        .td-3 { overflow-wrap: break-word; padding: 10px; }
        .td-3 label { padding: 5px 0px; }
        .td-3 label.hide { display: none; }
        .form-horizontal .form-group { margin-right: 0; margin-left: 0; }
        .text-navy { color: #1ab394; }
        .font-bold { font-weight: 600; }
        .i-checks { padding-left: 0; }
        label { margin: 0; font-weight: normal; }

        .checkbox-inline, .checkbox-inline + .checkbox-inline, .radio-inline, .radio-inline + .radio-inline { margin: 0 15px 0 0; }
        .layui-form-checkbox, .layui-form-checkbox *, .layui-form-switch { display: none !important; }
        .rightDiv { width: 50%; height: 300px; border-left: 1px solid #e1d4d4; position: relative; overflow-y: auto; border-radius: 5px; overflow-x: hidden; float: right; position: absolute; right: 0; }
        .leftDiv { left: 0; width: 100%; overflow-y: auto; overflow-x: hidden; position: absolute; /* height: 100%; */ float: none !important; margin-top: 1rem; }
        /*.layui-btn { background-color: #238c8c; border-radius: 3px; color: #ad1c1c; margin: 10px; }*/

        .floatBottom { position: absolute; bottom: 1rem; top: 25%; background-color: #fff; left: 0; right: 0; background-color: rgba(0,0,0,0); box-shadow: 0 0 0.5rem #000; width: 90%; margin-left: 1rem; margin-top: 1rem }
        .floatBottom .box { }
        .way { height: 100%; width: 25rem; background-color: rgba(255,255,255,.8); float: right; overflow: auto; }
        .box div.close { z-index: 9999; opacity: 1; position: absolute; line-height: 1.5rem; height: 1.5rem; width: 2rem; text-align: center; top: -1.65rem; right: -.03rem; cursor: pointer; background-color: #5868e0; border: 1px solid #f5f5f5; border-bottom: 0; box-shadow: 0 0 0.3rem #5868e0; color: #fff; border-top-left-radius: 5px; border-top-right-radius: 5px; }
        .box div.close:hover { background-color: #202b83; }

        .input-group { margin-top: .5rem; }
        .input-group .input-div { float: left; }
        .leftDiv .header { line-height: 80px; text-align: center; min-height: 80px; width: 95%; margin: 1rem; font-size: 2rem; font-weight: 600; color: #1e9fff; padding: .1rem; border-radius: 5px; background: linear-gradient(45deg,rgb(238 242 249 / 30%),rgb(161 238 239 / 80%),rgb(178 189 195 / 30%)); box-shadow: 0px 10px 5px 0px rgba(0, 0, 0, 0.75); }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight" style="width: 100%;">
        <div class="layui-content layui-form tablediv" id="verifyCheck">


            <div class="layui-card tablediv">
                <div class="tablediv">
                    <div class="leftDiv">
                        <div style=" width: 100%;">

                            <div class="header"></div>

                            <div class="content">
                                <div class="container">
                                    <div class="progress">
                                        <div class="progress-bar"></div>
                                        <div class="circle active">1<span>检测车辆车牌类型</span></div>
                                        <div class="circle">2<span>检测白名单</span></div>
                                        <div class="circle">3<span>检测车辆有效期</span></div>
                                        <div class="circle">4<span>检测储值余额</span></div>
                                    </div>
                                </div>
                                <div class="list_wrap" id="list_wrap">

                                    <div class="layui-inline item sum">
                                        <div class="input-group">
                                            <div class="input-div">
                                                <button class="layui-btn layui-btn-md" id="UpSum" style="float: left;"><i class="fa fa"></i> <t>一键处理数据</t></button>
                                            </div>

                                            <span class="input-group-btn" style="vertical-align: top;">
                                                <button type="button" class="btn btn-primary modify" id="SearchCar" style="margin:0px !important;padding: 11px 12px !important;background-color: #657de5;border-color: #999;"><i class="fa pwd-edit fa-rotate-left">查看车辆数据</i></button>
                                            </span>

                                            <span class="input-group-btn" style="vertical-align: top;">
                                                <button type="button" class="btn btn-primary modify" id="SearchCarCardType" style="margin:0px !important; padding: 11px 12px !important;background-color: #657de5;border-color: #999;"><i class="fa pwd-edit fa-rotate-left">查看车牌类型数据</i></button>
                                            </span>
                                        </div>
                                    </div>

                                    <div class="layui-inline item upownercarcardtype layui-hide">
                                        <div class="input-group">
                                            <div class="input-div">
                                                <button class="layui-btn layui-btn-md" id="UpOwnerCarCardType" style="float: left;"><i class="fa fa"></i> <t>执行车位车牌类型数据处理</t></button>
                                            </div>
                                            @*<span class="input-group-btn" style="vertical-align: top;">
                                            <button type="button" class="btn btn-primary modify" id="ScCarOwner" style="padding: 11px 12px !important;background-color: #657de5;border-color: #999;"><i class="fa pwd-edit fa-rotate-left">查看数据</i></button>
                                            </span>*@
                                        </div>
                                    </div>

                                    <div class="layui-inline item upownerblackwhite layui-hide">
                                        <div class="input-group">
                                            <div class="input-div">
                                                <button class="layui-btn layui-btn-md" id="UpOwnerBlackWhite" style="float: left;"><i class="fa fa"></i> <t>执行车位下发白名单数据处理</t></button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-inline item upownervalidtime layui-hide">
                                        <div class="input-group">
                                            <div class="input-div">
                                                <button class="layui-btn layui-btn-md" id="UpOwnerValidTime" style="float: left;"><i class="fa fa"></i> <t>执行车位有效期数据处理</t></button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="layui-inline item upownerbalance layui-hide">
                                        <div class="input-group">
                                            <div class="input-div">
                                                <button class="layui-btn layui-btn-md" id="UpOwnerBalance" style="float: left;"><i class="fa fa"></i> <t>执行车位账号余额数据处理</t></button>
                                            </div>
                                        </div>
                                    </div>

                                    @*<div class="layui-inline item upcarcardtype layui-hide">
                                    <div class="input-group">
                                    <div class="input-div">
                                    <button class="layui-btn layui-btn-md" id="UpCarCardType" style="float: left;"><i class="fa fa"></i> <t>执行车牌类型数据处理</t></button>
                                    </div>
                                    </div>
                                    </div>*@




                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div class="floatBottom layui-row layui-form layui-hide">
        <div class="box">
            <div class="close layui-icon layui-icon-close"></div>
            <div class="ops">

                @*<table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                <script type="text/html" id="toolbar_btns_car">
                <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>注销</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="Vaild"><i class="fa fa-calendar"></i><t>修改有效期</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="BathVaild"><i class="fa fa-crosshairs"></i><t>批量修改有效期</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>白名单启用</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="Disable"><i class="fa fa-ban"></i><t>白名单禁用</t></button>
                <button class="layui-btn layui-btn-sm" lay-event="Charge"><i class="fa fa-money"></i><t>储值车退费</t></button>
                </div>
                </script>*@

            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/tools.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var arrtemp = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var arrmonth = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664', '3656'];//月租车类型
        var arrfree = ['3656'];//免费车类型
        var arrprepaid = ['3657'];//储值车类型
        var arrvisitor = ['3658'];//免费车类型

        var comtable = null;
        var table = null;
        layui.use(['table', 'jquery', 'form'], function () {
            table = layui.table;
            $(".close").click(function () { $(".floatBottom").removeClass("layui-hide").addClass("layui-hide") });

            Tools.contorls.init();
        });
    </script>

    <script>
        const allCircles = document.querySelectorAll('.circle')
        const progressBar = document.querySelector('.progress-bar')
        // 获取 点的长度
        const circleLength = allCircles.length;

        // 根据当前索引，更新进度条属性 以及按钮 属性
        function updateProgressBar(setIndex) {

            allCircles.forEach((circle, circleIndex) => {
                if (circleIndex <= setIndex && !circle.classList.contains('active')) {
                    circle.classList.add('active');
                } else if (circleIndex > setIndex && circle.classList.contains('active')) {
                    circle.classList.remove('active');
                }
            })

            let width = setIndex * 100 / (circleLength - 1)
            if (width > 100) {
                width = 100
            } else if (width < 0) {
                width = 0
            }
            // 进度条宽度
            progressBar.style.width = width + '%';
        }



        var clickCount = 0;
        var timer = null;
        $(".header").click(function (event) {
            if (!event.ctrlKey && !event.metaKey) return;

            clickCount++;
            if (clickCount == 5) {
                timer = setInterval(function () {
                    clickCount = 0;
                    clearInterval(timer);
                }, 5000);

                layer.open({
                    title: "每月最高限额"
                    , content: "您已触发每月最高限额统计，确定重新统计每月累计收费限额吗？重新统计后不可恢复，请谨慎操作！！！"
                    , btn: ["确定", "取消"]
                    , btnAlign: 'center'
                    , yes: function (index, layero) {
                        layer.msg("正在处理,请等待..", { icon: 16, time: 0 });
                        $.getJSON("/Tools/CalcMonthPay", {}, function (json) {
                            if (json.success) {
                                layer.closeAll();
                                layer.open({
                                    type: 2,
                                    title: false,
                                    closeBtn: 0,
                                    skin: "diy-class-layer",
                                    area: ["220px", "20px"],
                                    content: "/ZProgress/Index"
                                });
                            }
                            else {
                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageIndex); } });
                            }
                        });
                    },
                    //关闭
                    btn2: function (index, layero) {
                        layer.close(index);
                    }
                });
            }
        });


    </script>

    <script type="text/html" id="TmplCategory">
        {{# if(d.CarCardType_Category==1){ }}
        <span class="layui-badge layui-bg-orange">临停类型</span>
        {{# }else if(d.CarCardType_Category==2){ }}
        <span class="layui-badge layui-bg-green">储值类型</span>
        {{# }else if(d.CarCardType_Category==3){ }}
        <span class="layui-badge layui-bg-blue">月租类型</span>
        {{# }else if(d.CarCardType_Category==4){ }}
        <span class="layui-badge layui-bg-gray">免费类型</span>
        {{# }else if(d.CarCardType_Category==5){ }}
        <span class="layui-badge layui-bg-black">访客类型</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplIsMoreCar">
        {{# if(d.CarCardType_IsMoreCar==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplIsDefault">
        {{# if(d.CarCardType_IsDefault==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>
</body>
</html>
