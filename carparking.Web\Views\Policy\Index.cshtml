<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>功能策略设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <script src="~/Static/plugins/carnopicker/carnopicker.js?1" asp-append-version="true"></script>
    <style>
        html, body { background-color: #fff !important; }
        .layui-tab-title { padding-left: 2rem; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }
        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }
        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }
        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_park1.svg'); }
        .layui-tab-title li.layui-this.type5::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-content { padding: 2rem; padding-bottom: 0; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }
        .layui-disabled { background-color: #eee; opacity: 1; }
        .layui-form-select dl { box-shadow: 0 0 6px; }

        input[value='自动放行'] { color: #1ab394 !important; }
        input[value='禁止通行'] { color: red !important; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }
        select[multiple] + .layui-form-select dd { padding: 0; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }
        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }
        .multiSelect a { padding: 2px 5px; background: #0094ff; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }
        .multiSelect a span { float: left; }
        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }
        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }
        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }
        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }
        .multiSelect a i:hover { background-color: #545556; }
        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }
        .multiOption:hover { color: #5FB878 }
        .simplehide { display: none; }
        .moresetting { display: none; }
        .headmoresetting { cursor: pointer; color: #1e9fff; }
        .headmoresetting:hover { font-weight: 600; }
        .otherdesc { display: none; }
        .descicon { cursor: pointer; font-size: 1.1rem; }
        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }
        .help-btn { position: absolute; width: 20px; margin-left: 7px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }

        iframe { border: none; }
        .layui-tab-item { height: calc(100vh - 95px); }
        tr td:first-child { min-width: 100px; }
        tr td:nth-child(2) { min-width: 200px; }
    </style>
</head>
<body>
    <div class="layui-tab">
        <ul class="layui-tab-title">
            <li class="type1 layui-this">开闸方式</li>
            <li class="type3 simple">车牌计费设置</li>
            <li class="type2 simple">车道设置</li>
            <li class="type5">区域设置</li>
            <li class="type4">停车场设置</li>
        </ul>
        <div class="layui-tab-content layui-form">
            <!--开闸方式-->
            <div class="layui-tab-item layui-show">
                <div class="layui-row">
                    <div class="layui-inline">
                        <select class="layui-select" lay-search id="PolicyPass_PasswayNo" name="PolicyPass_PasswayNo">
                            <option value="">出入通道</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <select class="layui-select" lay-search id="PolicyPass_CarCardTypeNo" name="PolicyPass_CarCardTypeNo">
                            <option value="">车牌类型</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn " id="EditBatch">批量设置</button>
                    </div>
                </div>
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>功能类目</th>
                            <th>功能方案</th>
                            <th></th>
                            <th>注释</th>
                        </tr>
                    </thead>
                    <tbody data-key="pass" id="passpanl">
                        <tr>
                            <td>开闸方式</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_Pass" name="PolicyPass_Pass">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">根据不同的入出口车道类型，设置指定车牌类型的通行方式；<i class="help-btn">?</i><br /></span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                </span>
                                <t style="color:red;"> 注意：出口设置为自动放行时，产生费用时，系统将会弹框收费。</t>
                            </td>
                        </tr>
                        <tr class="outgate">
                            <td>无入场记录</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_NoFundEnter" name="PolicyPass_NoFundEnter">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="4">最低收费</option>
                                    <option value="0">禁止通行</option>
                                </select>
                                <input type="text" class="layui-input layui-hide v-null v-floatLimit v-min v-max" min="0" max="9999" maxlength="7" id="PolicyPass_MinAmount" name="PolicyPass_MinAmount" placeholder="最低收费标准金额" />
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">根据不同的出口车道类型，设置车辆没有入场记录时的通行方式；<i class="help-btn">?</i><br /></span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行；<br />
                                    【最低收费】：识别后系统按预设最低金额弹框收费，该收费支持电子支付；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                </span>
                                <t style="color:red;">注意：该设置项仅对出口生效。</t>
                            </td>
                        </tr>
                        <tr class="ingate">
                            <td>重复识别车牌入场</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_RepeatEnter" name="PolicyPass_RepeatEnter">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">设置场内已有记录的车辆再次入场时的开闸方式；<i class="help-btn">?</i><br /></span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行并覆盖原记录；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行并覆盖原记录；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                </span>
                                <t style="color:red;">注意：该设置项仅对入口生效。</t>
                            </td>
                        </tr>
                        <tr class="ingate" style="display:none;">
                            <td>无牌车重复扫码入场</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_RepeatEnter2" name="PolicyPass_RepeatEnter2">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>仅对入口产生作用。"禁止通行"时，实现"一进一出"功能，其他方式，则直接覆盖场内记录。</td>
                        </tr>
                        <tr class="ingate">
                            <td>车场满位入场</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_SpaceFull" name="PolicyPass_SpaceFull">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                    <option value="3">排队等候</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">设置车场满位后不同车牌类型的开闸方式；<i class="help-btn">?</i><br /></span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                    【排队等候】：识别后系统弹框等待释放车位后自动开闸放行；<br />
                                </span>
                                <t style="color:red;">注意：该设置项仅对入口生效。</t>
                            </td>
                        </tr>

                        <tr class="passExpire">
                            <td>过期开闸方式</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_IsExpire" name="PolicyPass_IsExpire">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">根据不同的入出口车道类型，设置指定车牌类型过期后的通行方式；<i class="help-btn">?</i><br /></span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                </span>
                                <t style="color:red;">注意：商家车不受此控制,商家车过期按临时车类型处理。</t>
                            </td>
                        </tr>
                        <tr class="simple">
                            <td>尾号限行开闸方式</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_NumPass" name="PolicyPass_NumPass">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">根据不同的入出口车道类型，设置指定车牌类型的尾号通行方式；<i class="help-btn">?</i><br /></span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                </span>
                                <t style="color:red;">注意：需配合"车场设置"->"车道设置"启用车道尾号限行控制功能项使用。</t>
                            </td>
                        </tr>
                        <tr class="layui-hide passStored">
                            <td>余额不足开闸方式</td>
                            <td>
                                <select class="layui-select" id="PolicyPass_LackPass" name="PolicyPass_LackPass">
                                    <option value="1">自动放行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="0">禁止通行</option>
                                </select>
                            </td>
                            <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                            <td>
                                <span class="headdesc">根据不同的入出口车道类型，设置储值车余额不足时的通行方式；<i class="help-btn">?</i><br /> </span>
                                <span class="otherdesc">
                                    【自动放行】：识别后自动开闸放行；<br />
                                    【弹框确认】：识别后系统弹框由人工确认开闸放行；<br />
                                    【禁止通行】：识别后系统不予开闸放行；<br />
                                </span>
                                <t style="color:red;">注意：当入口识别储值车余额为0或出口识别储值车余额小于停车费用时该设置项生效。若出口设置自动放行，但余额不足以抵扣所产生的停车费用时，系统依旧会弹出收费框等待确认放行。</t>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="layui-row">
                    <button class="layui-btn layui-btn-sm saveAll" data-id="pass">保存全部</button>
                </div>
            </div>

            <!--车牌计费设置-->
            <div class="layui-tab-item">
                <iframe data-original-src="/Policy/PolicyCarCard" width="100%" height="100%"></iframe>
            </div>

            <!--车道设置-->
            <div class="layui-tab-item">
                <iframe data-original-src="/Policy/PolicyPassway" width="100%" height="100%"></iframe>
            </div>

            <!--区域设置-->
            <div class="layui-tab-item">
                <iframe data-original-src="/Policy/PolicyArea" width="100%" height="100%"></iframe>
            </div>

            <!--停车场设置-->
            <div class="layui-tab-item">
                <iframe data-original-src="/Policy/PolicyPark" width="100%" height="100%"></iframe>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}" data-type="${CarCardType_Type}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplrule">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">以 ${CarCardType_Name} 计费规则计费</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1.9" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        var loadingIndex;
        $(function () {

            if ('@carparking.Config.PubVar.iParkingType' == "1") {
                $("#PolicyPark_MaxDiscount").attr("disabled", true);
            }

            myVerify.init();
            var cp = new CarnoPicker("#PolicyPark_CarPrefix", { ischar: false });
            cp.init();
            layui.use(['element', 'form', 'laydate'], function () {
                var element = layui.element;

                loadingIndex = layer.load(1, {
                    shade: false
                });
                pager.init();

                // 监听标签页切换事件
                element.on('tab', function (data) {
                    // 显示加载提示
                    loadingIndex = layer.load(1, {
                        shade: false
                    });

                    // 使用setTimeout模拟切换过程，给用户更好的体验
                    setTimeout(function () {
                        layer.close(loadingIndex);
                    }, 200);
                });

                // 延迟加载iframe内容
                initIframes();
            })
        });

        // 添加iframe初始化函数
        function initIframes() {
            // 先清空所有iframe的src
            $('iframe').attr('src', '');

            // 等待父页面资源加载完成后再加载iframe
            setTimeout(function () {
                $('iframe').each(function () {
                    var originalSrc = $(this).data('original-src') || $(this).attr('src');
                    if (originalSrc) {
                        // 添加加载事件监听
                        $(this).on('load', function () {
                            layer.close(loadingIndex);
                        });
                        $(this).attr('src', originalSrc + '?t=' + new Date().getTime());
                    }
                });
            }, 500); // 延迟500ms加载
        }

        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//访客车类型

        var pager = {
            parkareas: null,
            passways: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            drives: null,       //设备型号列表
            links: null,        //通道关联区域列表
            Province: [],
            City: [],
            init: function () {
                try {
                    $.ajaxSettings.async = false;
                    this.bindPower();
                    this.bindSelect();
                    this.bindData();
                    this.bindEvent();
                    $.ajaxSettings.async = true;
                } finally {
                    layer.close(loadingIndex); // Close loading indicator when done
                }
            },
            bindSelect: function () {
                $.post("GetAllPasswayAndCarCardType2", {}, function (json) {
                    if (json.success) {
                        pager.passways = json.data.passways;
                        pager.links = json.data.links;
                        pager.carCardTypes = json.data.carCardTypes;
                        // pager.carTypes = json.data.carTypes;
                        // pager.drives = json.data.drives;

                        $("#PolicyPass_PasswayNo").html($("#tmplpassway").tmpl(json.data.passways));
                        $("#PolicyPass_CarCardTypeNo").html($("#tmplcarcardtype").tmpl(json.data.carCardTypes));

                    } else {
                        console.log(json.msg);
                    }
                }, "json");


                layui.form.render();

                $("td").hover(function () {
                    //判断td里有headdesc样式
                    if ($(this).find("span.headdesc").length > 0) {
                        var $td = $(this).find("span.headdesc").siblings(".otherdesc");
                        var $div = $('<div>').append($td.contents().clone());
                        layer.tips($div.html(), this, {
                            tips: [1, '#090a0c'],
                            time: 0,
                            area: '50wh'  // 设置宽度为300px
                        });
                    }
                }, function () {
                    layer.closeAll('tips');
                });
            },
            bindData: function () {
                policy.pass.onload();
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {

                    var val = data.value;
                    //开闸方式切换通道
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        policy.pass.onload();
                    }
                    //开闸方式切换车牌类型
                    else if (data.elem.id == "PolicyPass_CarCardTypeNo") {
                        policy.pass.onload();
                    } //开闸方式-未找到入场记录最低收费标准
                    else if (data.elem.id == "PolicyPass_NoFundEnter") {
                        policy.minpayed(data.elem.id, val);
                    }
                })

                $("button.save").click(function () {

                    if (!myVerify.check()) return;

                    var param = {};
                    $(this).parent().siblings().find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            var v = $(this).val();
                            if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                if (v == null || v == '') v = [];
                                v = JSON.stringify(v);
                            }

                            param[$(this).attr('id')] = v;
                        }
                    });

                    var datakey = $(this).parent().parent().parent().attr("data-key");
                    pager.onSave(datakey, param);
                });

                $("button.saveAll").click(function () {
                    if (!myVerify.check()) return;

                    var param = {};
                    var datakey = $(this).attr("data-id");
                    $("tbody[data-key='" + datakey + "']").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide")) {
                                var v = $(this).val();
                                if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                    if (v == null || v == '') v = [];
                                    v = JSON.stringify(v);
                                }

                                param[$(this).attr('id')] = v;
                            }
                        }
                    });
                    pager.onSave(datakey, param);
                });

                $("#BatchSetPassway").unbind("click").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/BatchSetPassWay",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $("#EditBatch").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/EditPassBatch",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $(".headmoresetting").unbind("click").click(function () {
                    var table = $(this).parent().parent().parent().find(".moresetting");
                    if ($(table).is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                        $(this).find("t").text("隐藏更多设置");
                        var versionType1 = localStorage.getItem("versionType");
                        if (versionType1 == "simple") {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide").addClass("layui-hide").addClass("versionHide");
                        } else {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide");
                            if ($("#PolicyArea_EPEnable").val() == "1")
                                $(".fymodal").removeClass("layui-hide");
                            else
                                $(".fymodal").removeClass("layui-hide").addClass("layui-hide");

                        }
                    } else {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                        $(this).find("t").text("更多设置");
                    }
                    $(table).toggle("fast");
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                        $("button.saveAll").removeClass("layui-hide");
                    }

                    if (pagePower['EditBatch'] == 'true') {
                        $("#BatchSetPassway").removeClass("layui-hide");
                    }
                });
            },
            onSave: function (datakey, param) {
                //开闸方式保存
                if (datakey == 'pass') {
                    var PolicyPass_PasswayNo = $("#PolicyPass_PasswayNo").val();
                    var PolicyPass_CarCardTypeNo = $("#PolicyPass_CarCardTypeNo").val();
                    var obj = { PolicyPass_PasswayNo: PolicyPass_PasswayNo, PolicyPass_CarCardTypeNo: PolicyPass_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyPass", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车道设置保存
                else if (datakey == 'passway') {
                    var PolicyPassway_PasswayNo = $("#PolicyPassway_PasswayNo").val();
                    var obj = { PolicyPassway_PasswayNo: PolicyPassway_PasswayNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    // 处理PolicyPassway_DoubleGateColorCodes的值，过滤掉空值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    if (selectedColors && selectedColors.length > 0) {
                        selectedColors = selectedColors.filter(function(color) {
                            return color !== "" && color !== null && color !== undefined;
                        });
                    } else {
                        selectedColors = [];
                    }
                    obj.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));
                    console.log(obj)
                    $.post("SavePolicyPassway", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            }
        }

        var linkObj = {
            //获取车道出入口类型
            getGateType: function (passwayno) {
                var gate = 0;//默认出口
                // if (pager.links == null || pager.links.length == 0)
                //     return gate;
                pager.passways.forEach(function (item, index) {
                    if (item.Passway_No == passwayno) {
                        gate = item.PasswayLink_GateType;
                        return gate;
                    }
                });


                return gate;
            }
        }

        var policy = {
            pass: {
                onload: function () {
                    var PolicyPass_PasswayNo = $("#PolicyPass_PasswayNo").val();
                    var PolicyPass_CarCardTypeNo = $("#PolicyPass_CarCardTypeNo").val();
                    var category = $("#PolicyPass_CarCardTypeNo").find('option:selected').attr('data-category');
                    var type = $("#PolicyPass_CarCardTypeNo").find('option:selected').attr('data-type');

                    var gate = linkObj.getGateType(PolicyPass_PasswayNo);
                    if (gate == 0) {
                        $(".outgate").removeClass("layui-hide");
                        $(".ingate").removeClass("layui-hide").addClass("layui-hide");
                    } else {
                        $(".ingate").removeClass("layui-hide");
                        $(".outgate").removeClass("layui-hide").addClass("layui-hide");
                    }

                    //console.log(category)
                    if (type == 4 || type == 3) {//免费+月租类型显示【是否为白名单】
                        $("tr.whitelist").removeClass("layui-hide");
                        if (type == 3)//月租类型显示【有效期过期】
                            $("tr.month").removeClass("layui-hide");
                        else
                            $("tr.month").removeClass("layui-hide").addClass("layui-hide");
                    } else {
                        $("tr.whitelist").removeClass("layui-hide").addClass("layui-hide");
                        $("tr.month").removeClass("layui-hide").addClass("layui-hide");
                    }

                    if (type == 2) {
                        $("tr.passStored,tr.passExpire").removeClass("layui-hide");
                        $("tr.passExpire").addClass("layui-hide");
                    } else {
                        $("tr.passStored").removeClass("layui-hide").addClass("layui-hide");
                        $("tr.passExpire").removeClass("layui-hide");
                    }

                    $.post("GetPolicyPass", { PolicyPass_PasswayNo: PolicyPass_PasswayNo, PolicyPass_CarCardTypeNo: PolicyPass_CarCardTypeNo }, function (json) {
                        if (json.success) {
                            $("#passpanl").fillForm(json.data, function (data) { });
                            layui.form.render();
                            if (json.data != null)
                                policy.minpayed("", json.data.PolicyPass_NoFundEnter);
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            },
            voicediy: function (id, val) {
                if (val == 5)
                    $("#PolicyPassway_Show").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Show").removeClass("layui-hide").addClass("layui-hide");
            },
            pushinfo: function (id, val) {
                if (val == 1)
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide").addClass("layui-hide");
            },
            minpayed: function (id, val) {
                if (val == 4)
                    $("#PolicyPass_MinAmount").removeClass("layui-hide");
                else
                    $("#PolicyPass_MinAmount").removeClass("layui-hide").addClass("layui-hide");
            },
            fymodal: function (id, val) {
                if (!$(".fymodal").hasClass("versionHide")) {
                    if (val == 1)
                        $(".fymodal").removeClass("layui-hide");
                    else
                        $(".fymodal").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            opengate: function (id, val) {
                if (!$(".opengate").hasClass("versionHide")) {
                    if (val != 3)
                        $(".opengate").removeClass("layui-hide");
                    else
                        $(".opengate").removeClass("layui-hide").addClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
