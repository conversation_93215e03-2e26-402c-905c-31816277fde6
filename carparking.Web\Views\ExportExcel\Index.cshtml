﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>导出Excel</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/iCheck/icheck.min.js" asp-append-version="true"></script>
    <style>
        .fa { margin: 6px 4px; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-input.timerange { width: 300px; }
        .layui-layer-btn-l { margin-left: 8.33%; }
        .layui-btn-warm { background-color: #f0ad4e; }

        div { float: left; }
        .tablediv { border: 1px solid #edecec; }

        .list_con_old, .list_con { display: inline-block; /* width: 100%; */ line-height: 2rem; border: 1px solid #ffffff; margin-left: 5px; margin-top: 2px; background-color: #4b9fff; border-radius: 5px; padding-left: 1px; position: relative; padding: 0 0.5rem; color: #fff; font-weight: 800; }
        .content, .list_wrap { width: 99%; height: 100%; overflow-x: hidden; }
        .list_con_old { margin: 0 !important; border: 0 !important; background-color: #cffafb; margin-bottom: 2px !important; margin-top: 2px !important; }

        .td-3 { overflow-wrap: break-word; padding: 10px; }
        .td-3 label { padding: 5px 0px; }
        .td-3 label.hide { display: none; }
        .form-horizontal .form-group { margin-right: 0; margin-left: 0; }
        .text-navy { color: #1ab394; }
        .font-bold { font-weight: 600; }
        .i-checks { padding-left: 0; }
        label { margin: 0; font-weight: normal; }

        .checkbox-inline, .checkbox-inline + .checkbox-inline, .radio-inline, .radio-inline + .radio-inline { margin: 0 15px 0 0; }
        .layui-form-checkbox, .layui-form-checkbox *, .layui-form-switch { display: none !important; }
        .rightDiv { width: 50%; height: 300px; border-left: 1px solid #e1d4d4; position: relative; overflow-y: auto; border-radius: 5px; overflow-x: hidden; float: right; position: absolute; right: 0; }
        .leftDiv { left: 0; width: 50%; overflow-y: auto; overflow-x: hidden; position: absolute; /* height: 100%; */ float: none !important; }
    </style>
</head>
<body>
    <div class="layui-content layui-form tablediv" id="verifyCheck">
        <div class="layui-card tablediv">
            <div class="tablediv">
                <div class="leftDiv">
                    <div style=" width: 100%;min-height: 220px;">
                        <table id="tblFiles" style="width: 100%" class="draggable">
                            <thead>
                                <tr>
                                    <td class="exhead">
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr id="1" class="dragtr">
                                    <td class="td-3">
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="layui-card-footer" style="width: 100%;margin: 1rem;text-align: center;position: relative;bottom: 0px;">
                        <button class="layui-btn layui-btn-md" id="AllCheck"><i class="fa fa-check"></i> <t>全选</t></button>
                        <button class="layui-btn layui-btn-md layui-btn-warm" id="CancelCheck"><i class="fa fa-times"></i> <t>全不选</t></button>
                    </div>
                </div>
                <div class="rightDiv" id="divSelectedFiles">
                    <div style=" width: 100%;min-height: 220px;">
                        <div class="content">
                            <div class="list_wrap" id="list_wrap"></div>
                        </div>
                    </div>

                    <div class="layui-card-footer" style="width: 100%;text-align: center;position: relative;bottom: 0px;">
                        <button class="layui-btn layui-btn-md" id="Save"><i class="fa fa-check"></i> <t>确定</t></button>
                        <button class="layui-btn layui-btn-md layui-btn-warm" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                    </div>

                </div>

            </div>

        </div>
    </div>
    <script src="~/Static/js/Sortable.min.js" asp-append-version="true"></script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        var filedColl = parent.pager.dataField; //[{ name: "名字1", value: "f1", chk: true }, { name: "名字2", value: "f2", chk: false }];
        parent.pager.dataField = null;
        $(function () {
            if (!filedColl || filedColl == null || filedColl.length == 0) { layer.msg("无导出字段，请重试"); return; }

            $("#Cancel").click(function () { parent.layer.close(index); });
            filedColl.forEach((item) => {
                $(".td-3").append('<label class="checkbox-inline i-checks"><input type="checkbox" class="filed" data-text="' + item.name + '" value="' + item.value + '"><t>' + item.name + '</t></label>');
                if (item.chk) {
                    $("input[value=" + item.value + "]").iCheck('check');
                    $(".list_wrap").append('<div class="list_con" data-value="' + item.value + '"><div class="title">' + item.name + '</div></div>');
                }
            })
            var d = document.querySelector(".list_wrap");
            new Sortable(d, {
                handle: '.list_con', // handle's class
                animation: 150,
                // direction: 'horizontal',
            });

            //加载表中checkbox的i-checks样式
            $(".i-checks").iCheck({ checkboxClass: "icheckbox_square-green", radioClass: "iradio_square-green" });

            $(".filed").on('ifUnchecked', function () {
                $('div[data-value=' + $(this).val() + ']').remove();
            });
            $(".filed").on('ifChecked', function () {
                $(".list_wrap").append('<div class="list_con" data-value="' + $(this).val() + '"><div class="title">' + $(this).attr("data-text") + '</div></div>');
            });

            //全选|全不选
            $("#AllCheck").click(function () {
                $(".list_wrap").html("");
                $("tr").each(function () {
                    $(this).find("label").each(function () {
                        $(this).find("input").iCheck('uncheck');
                        $(this).find("input").iCheck('check');
                    })
                });

                var d = document.querySelector(".list_wrap");
                new Sortable(d, {
                    handle: '.list_con', // handle's class
                    animation: 150,
                    // direction: 'horizontal',
                });
            })

            $("#CancelCheck").click(function () {
                $(".list_wrap").html("");
                $("table").find("tr").each(function () {
                    $(this).find("label").find("input").iCheck('uncheck');
                });
            })

            $("#Save").click(function () {
                var filedList = [];
                $(".list_con").each(function (item) {
                    var v = $(this).attr("data-value");
                    var t = $(this).text()
                    if (v && t && v != "" && t != "")
                        filedList.push({ text: t, filed: v });
                })
                if (filedList.length == 0) { layer.msg("请勾选需要导出的字段"); return; }
                parent.pager.dataField = filedList;
                parent.layer.close(index);
            })
        })
    </script>
</body>
</html>
