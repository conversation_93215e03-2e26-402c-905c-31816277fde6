﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>出入权限管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
         html, body { height:100%;width:100%;overflow:auto; }

        .layui-tab { margin: 0;  background: #fff; height:100%;position:relative; }

        .layui-tab-title { padding-left: 2rem; padding-top: 15px; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .5rem; left: .8rem; top: .75rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_type.svg'); }    

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_type1.svg'); }

        .layui-tab-content { padding: 0; position: absolute; bottom: 0; top: 60px; left: 0; right: 0; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab-item.full { width: 100%; height: 100%; }
        .layui-tab-item.full iframe{width:100%;height:100%;border:0;}

        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right:5px;}


        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>
    <div class="layui-tab">
        <ul class="layui-tab-title">
            <li class="type1 layui-this">按车牌类型授权</li>
            <li class="type2">按车牌颜色授权</li>
        </ul>
        <div class="layui-card">
            <div class="layui-card-header">
                <div class="test-table-reload-btn layui-form" id="searchForm">
                    <div class="layui-inline">
                        <input class="layui-input " name="AccessAuth_No" id="AccessAuth_No" autocomplete="off" placeholder="出入权限编码" />
                    </div>
                    <div class="layui-inline panel1">
                        <select id="AccessAuth_CarCardTypeNo" name="AccessAuth_CarCardTypeNo" class="layui-select" placeholder="车牌类型" lay-search>
                            <option value="">车牌类型</option>
                        </select>
                    </div>
                    <div class="layui-inline layui-hide panel2">
                        <select id="AccessAuth_CarTypeNo" name="AccessAuth_CarTypeNo" class="layui-select" placeholder="车牌颜色" lay-search>
                            <option value="">车牌颜色</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                    </div>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                <script type="text/html" id="toolbar_btns">
                    <div class="layui-btn-container">
                        {{# if(Power.AccessAuth.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                        {{# if(Power.AccessAuth.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                        {{# if(Power.AccessAuth.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                    </div>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="TmplIsMoreCar">
        {{# if(d.AccessAuth_IsMoreCar==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>
    <script type="text/html" id="tmplTypeName">
        {{# if(d.AccessAuth_Type==1){ }}
        <span>{{d.CarCardType_Name}}</span>
        {{# }else if(d.AccessAuth_Type==2){ }}
        <span>{{d.CarType_Name}}</span>
        {{# } }}
    </script>
    <script type="text/html" id="tmplIsAccess">
        {{# if(d.AccessAuth_IsAccess==1){ }}
        <span class="layui-badge layui-bg-green">允许通行</span>
        {{# }else if(d.AccessAuth_IsAccess==2){}}
        <span class="layui-badge layui-bg-orange">禁止单号通行</span>
        {{# }else if(d.AccessAuth_IsAccess==3){}}
        <span class="layui-badge layui-bg-orange">禁止双号通行</span>
        {{# }else if(d.AccessAuth_IsAccess==4){}}
        <span class="layui-badge layui-bg-blue">弹框确认</span>
        {{# }else if(d.AccessAuth_IsAccess==0){}}
        <span class="layui-badge layui-bg-red">禁止通行</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        $(function () {

            $(".layui-tab-title").find("li").click(function () {
                var type = $(this).index() + 1;
                if (type == 1) {
                    $(".panel1").removeClass("layui-hide");
                    $(".panel2").removeClass("layui-hide").addClass("layui-hide");
                } else {
                    $(".panel2").removeClass("layui-hide");
                    $(".panel1").removeClass("layui-hide").addClass("layui-hide");
                }
                pager.bindData(pager.pageIndex, type);
            });
        })
    </script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            var type = $(".layui-this").index() + 1;
            conditionParam.AccessAuth_Type = type;

            var cols = [[
                { type: 'radio' }
                , { field: 'AccessAuth_No', title: '出入权限编码' , hide: true}
                , { field: 'TypeName', title: '授权对象', toolbar: "#tmplTypeName" }
                , { field: 'Passway_Name', title: '授权通道' }
                , { field: 'AccessAuth_IsAccess', title: '授权权限', toolbar: "#tmplIsAccess" }
                , {
                    field: 'AccessAuth_DateType', title: '授权日期', templet: function (d) {
                        if (d.AccessAuth_DateType == 1) {
                            return "每天";
                        } else if (d.AccessAuth_DateType == 2) {
                            return getWeekNames(d.AccessAuth_WeekContent);
                        } else if (d.AccessAuth_DateType == 3) {
                            return "指定日期";
                        }
                    }
                }
                , {
                    field: 'TimeContent', title: '授权时间', templet: function (d) {
                        if (d.AccessAuth_DateType == 1 || d.AccessAuth_DateType == 3) {
                            return getTimeContent(d.AccessAuth_DayContent, 0);
                        } else if (d.AccessAuth_DateType == 2) {
                            return getTimeContent(d.AccessAuth_WeekContent, 1);
                        }
                    }
                }
                , { field: 'Admins_Name', title: '添加人' }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/AccessAuth/GetAccessAuthList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                var type = $(".layui-this").index() + 1;

                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增出入权限",
                            content: 'Edit?Act=Add&Type=' + type,
                            area: getIframeArea(['830px', '90%']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑出入权限",
                            content: 'Edit?Act=Update&AccessAuth_No=' + data[0].AccessAuth_No + '&Type=' + type,
                            area: getIframeArea(['830px', '90%']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data[0].AccessAuth_IsDefault == 1) { layer.msg("系统默认数据不可删除"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { AccessAuth_No: data[0].AccessAuth_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

        var getWeekNames = function (weekContent) {
            try {
                var d = JSON.parse(weekContent);
                var item = d[0];
                var weeks = JSON.parse(item.week);
                for (var i = 0; i < weeks.length; i++) {
                    var w = weeks[i];
                    if (w == 1) weeks[i] = "周一";
                    else if (w == 2) weeks[i] = "周二";
                    else if (w == 3) weeks[i] = "周三";
                    else if (w == 4) weeks[i] = "周四";
                    else if (w == 5) weeks[i] = "周五";
                    else if (w == 6) weeks[i] = "周六";
                    else if (w == 0) weeks[i] = "周日";
                }
                return weeks.join(',');
            } catch (e) {
                console.log("解析失败：" + weekContent);
                return "";
            }
        }

        var getTimeContent = function (content, type) {
            try {
                var d;
                if (type == 0) d = JSON.parse(content);
                else {
                    var js = JSON.parse(content);
                    d = JSON.parse(js[0].times);
                }

                var rs = [];
                d.forEach((item, index) => {
                    rs[rs.length] = item.start + '~' + item.end;
                });

                return rs.join(',');
            } catch (e) {
                console.log("解析失败：" + content);
                return "";
            }
        }
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        var option = '';
                        json.data.forEach(function (d, i) {
                            option += '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                        });
                        $("#AccessAuth_CarCardTypeNo").append(option);
                        layuiForm.render("select");
                    }
                }, 'json');

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        var option = '';
                        json.data.forEach(function (d, i) {
                            option += '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                        });
                        $("#AccessAuth_CarTypeNo").append(option);
                        layuiForm.render("select");
                    }
                }, 'json');
            },
            bindData: function (index, type) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                if (type == null || type == "")
                    type = $(".layui-this").index() + 1;
                conditionParam.AccessAuth_Type = type;
                comtable.reload({
                    url: '/AccessAuth/GetAccessAuthList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
