﻿using Aliyun.OSS;
using carparking.BillingBlackBox;
using carparking.BillingBlackBox.Models;
using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Charge.Models;
using carparking.ChargeModels;
using carparking.Common;
using carparking.Config;
using carparking.DAL;
using carparking.Model;
using carparking.Model.Sys;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using System;
using System.Collections.Generic;
using System.Linq;

namespace carparking.Charge
{
    public class Calc
    {
        #region 计费策略业务处理

        /// <summary>
        /// 计费业务处理
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="car">车辆信息</param>
        /// <param name="crList">优惠券集合，为null时会重新到数据库查询优惠券集合，不为null则直接计费使用</param>
        /// <param name="useOrderCoupon">是否使用关联当前车辆的其它优惠券</param>
        /// <param name="carTypeNo">修改车牌颜色</param>
        /// <param name="carCarTypeNo">修改车牌类型</param>
        /// <param name="owner">车主信息</param>
        /// <param name="allDetailList">停车明细信息</param>
        /// <param name="isFllowCar">是否跟车追缴计费：true-已关闭的明细都需要计费</param>
        /// <param name="basicData">缓存信息</param>
        /// <returns></returns>
        private static ChargeModels.PayResult Main(ref Model.ParkOrder parkOrder, DateTime? outTime, ref Model.Car car, ref Model.Owner owner, Model.CarCardType cct, ref bool UseChuzhiCharage, List<Model.CouponRecordIntExt> crList = null,
            bool UseOrderCoupon = true, string carTypeNo = "", string carCarTypeNo = "", List<Model.OrderDetail> allDetailList = null,
            bool isFllowCar = false, CalcBasicData basicData = null, bool checkPayoder = true)
        {
            ChargeModels.PayResult payResult = new ChargeModels.PayResult();
            payResult.payed = 2;
            payResult.payedmsg = "计费失败";
            if (parkOrder == null) { payResult.payedmsg = "计费失败，停车订单不能为空"; return payResult; }
            if (outTime == null) { payResult.payedmsg = "计费失败，出场时间不能为空"; return payResult; }
            payResult.calctime = outTime.Value;
            payResult.parktimemin = 0; /*(outTime - parkOrder.ParkOrder_EnterTime).Value.TotalMinutes;*/

            //访客车处理
            if (car != null && car.Car_Category == "3658")
            {
                if (parkOrder.ParkOrder_EnterTime > car.Car_EndTime) car = null;
            }

            List<Model.CarCardType> cctList = null;
            UseChuzhiCharage = false;
            DateTime? PayOrder_PayedTime = null;
            Model.PolicyPark policy = null;//读取车场配置
            Model.CarCardType changeCarCardType = null;
            Model.CarType changeCarType = null;
            var parkorderCct = parkOrder.ParkOrder_CarCardType;
            var parkorderCt = parkOrder.ParkOrder_CarType;

            if (!string.IsNullOrEmpty(carCarTypeNo))
            {
                if (carCarTypeNo != parkorderCct)
                {
                    changeCarCardType = basicData?.cctList?.Find(x => x.CarCardType_No == carCarTypeNo);
                    if (changeCarCardType == null) changeCarCardType = BLL.CarCardType.GetEntity(carCarTypeNo);
                    if (changeCarCardType == null) carCarTypeNo = "";
                }
                else
                {
                    parkorderCct = "";
                }
            }
            if (!string.IsNullOrEmpty(carTypeNo))
            {
                if (carTypeNo != parkorderCt)
                {
                    changeCarType = basicData?.ctList?.Find(x => x.CarType_No == carTypeNo);
                    if (changeCarType == null) changeCarType = BLL.CarType.GetEntity(carTypeNo);
                    if (changeCarType == null) carTypeNo = "";
                }
                else
                {
                    carTypeNo = "";
                }
            }

            Model.CarCardType parkOrderCardType = basicData?.cctList?.Find(x => x.CarCardType_No == parkorderCct);
            if (parkOrderCardType == null)
            {
                cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_Type,CarCardType_IsMoreCar", "") ?? new List<Model.CarCardType>();
                parkOrderCardType = cctList?.Find(x => x.CarCardType_No == parkorderCct);
            }
            else
            {
                cctList = basicData.cctList;
            }

            if (parkOrderCardType == null) { payResult.payed = 2; payResult.payedmsg = "计费失败，未找到车牌颜色信息."; return payResult; }

            policy = basicData?.ppList?.FirstOrDefault();
            if (policy == null) policy = BLL.PolicyPark.GetEntity(parkOrder.ParkOrder_ParkNo);

            if (allDetailList == null) allDetailList = BLL.OrderDetail.GetAllEntity(parkOrder.ParkOrder_No);
            if (allDetailList == null || allDetailList.Count == 0 || parkOrder.ParkOrder_IsNoInRecord == 1)
            {
                if (policy != null && policy.PolicyPark_FeeNoRecord == 1)
                {
                    payResult.payed = 0; payResult.payedmsg = "无最近出场记录"; return payResult;
                    //payResult.payed = 2; payResult.payedmsg = "计费失败，无入场记录."; return payResult;
                }

                //无入场记录处理
                if (string.IsNullOrEmpty(parkOrder.ParkOrder_OutPasswayNo)) { payResult.payed = 0; payResult.payedmsg = "无需缴费"; return payResult; }
                Model.PolicyPass ppModel = BLL.PolicyPass.GetEntityByWayAndCardType(parkOrder.ParkOrder_OutPasswayNo, parkOrder.ParkOrder_CarCardType);
                if (ppModel == null || ppModel.PolicyPass_NoFundEnter != 4) { payResult.payed = 0; payResult.payedmsg = "无需缴费"; return payResult; }
                if (ppModel.PolicyPass_MinAmount == null || ppModel.PolicyPass_MinAmount <= 0) { payResult.payed = 0; payResult.payedmsg = "无需缴费"; return payResult; }

                payResult.payed = 1;
                payResult.payedmsg = "无入场缴费";
                payResult.parktimemin = (policy?.PolicyPark_NoRecordRangTime ?? 5);
                payResult.orderamount = payResult.payedamount = Utils.ObjectToDecimal(ppModel.PolicyPass_MinAmount, 0);
                cct = parkOrderCardType;

                if (car == null) car = BLL.Car.GetEntity(parkOrder.ParkOrder_CarNo);
                if (car != null && owner == null) owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
                //if (!(basicData?.cloudMqttOffline ?? false)) payResult = BusiCarMode(payResult, car, owner, parkOrderCardType);

                //根据车场策略限制优惠券张数
                if (crList != null && payResult != null)
                {
                    int? PolicyPark_MaxDiscount = 0;
                    crList = GetPolicyCoupon(parkOrder.ParkOrder_ParkNo, crList, out PolicyPark_MaxDiscount, basicData);
                    payResult.couponcount = Utils.ObjectToInt(PolicyPark_MaxDiscount, 0);
                }
                //优惠券处理
                payResult = CouponResult(payResult, parkOrder, outTime, allDetailList, ref crList, UseOrderCoupon, PayOrder_PayedTime, car);
                return payResult;
            }
            else
            {
                //嵌套区域有进场没出场记录按当前记录的上级区域计费
                if (policy?.PolicyPark_NestedRecords == 1)
                {
                    List<Model.ParkArea> areaList = basicData?.areaList;
                    if (areaList == null) areaList = BLL.ParkArea.GetAllEntity("ParkArea_ID,ParkArea_FID,ParkArea_FNo,ParkArea_No,ParkArea_Level,ParkArea_Name", "");

                    allDetailList = allDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                    bool isTimeNull = false;
                    for (int i = 0; i < allDetailList.Count; i++)
                    {
                        if (allDetailList[i].OrderDetail_OutTime == null)//&& i < allDetailList.Count - 1
                        {
                            if (i == allDetailList.Count - 1)
                            {
                                var area = areaList?.Find(x => x.ParkArea_No == allDetailList[i].OrderDetail_ParkAreaNo && x.ParkArea_Level != 0);
                                if (area != null)
                                {
                                    isTimeNull = true;
                                }
                            }
                            else
                            {
                                isTimeNull = true;
                            }
                        }
                    }

                    if (isTimeNull)
                    {
                        for (int i = 0; i < allDetailList.Count; i++)
                        {
                            if (allDetailList[i].OrderDetail_OutTime == null)//&& i < allDetailList.Count - 1
                            {
                                //allDetailList[i].OrderDetail_OutTime = allDetailList[i + 1]?.OrderDetail_EnterTime;
                                allDetailList[i] = GetPreviousRecord(allDetailList[i], areaList, allDetailList);
                            }
                        }
                    }
                }
            }

            allDetailList.ForEach(x => { x.OrderDetail_Lock = 0; });//这里将“锁车状态”字段用来标记“过期缴费”使用

            if (checkPayoder)
            {
                //判断是否已缴过并且出场时间比缴费时间大于等于一分钟，读取车场配置(缴费后是否超出车场配置的时间内，未超出则无需缴费)
                var payOrders = BLL.PayOrder.GetAllEntity("PayOrder_ID,PayOrder_PayedTime", $"PayOrder_ParkOrderNo='{parkOrder.ParkOrder_No}' and PayOrder_Status=1");
                if (payOrders != null && payOrders.Count > 0)
                {
                    PayOrder_PayedTime = payOrders.OrderByDescending(x => x.PayOrder_PayedTime).FirstOrDefault().PayOrder_PayedTime;
                    if (PayOrder_PayedTime != null)
                    {
                        if (GetChargeRulesType() != 0)//标准版不在此处计算超时分钟
                        {
                            var totalMin = (outTime - PayOrder_PayedTime).Value.TotalMinutes;
                            if (totalMin >= 1)
                            {
                                if (policy != null)
                                {
                                    if (totalMin <= policy.PolicyPark_MaxStayTime)
                                    {
                                        payResult.payed = 0;
                                        payResult.payedmsg = "无需缴费";
                                        payResult.calctime = outTime.Value;
                                        return payResult;
                                    }
                                }
                            }
                            else
                            {
                                payResult.payed = 0;
                                payResult.payedmsg = "无需缴费";
                                payResult.calctime = outTime.Value;
                                return payResult;
                            }
                        }
                    }
                }
            }

            if (car == null)
            {
                car = BLL.Car.GetEntityByCarNo(parkOrder.ParkOrder_CarNo);
                if (car != null)
                {
                    if (owner == null || owner != null && car.Car_OwnerNo != owner.Owner_No)
                    {
                        owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
                    }

                    if (owner != null)
                    {
                        var cartypeno = owner.Owner_CardTypeNo;
                        var ctype = cctList.Find(x => x.CarCardType_No == cartypeno);
                        if (ctype != null && ctype.CarCardType_IsMoreCar == 1)
                        {
                            car.Car_TypeNo = ctype.CarCardType_No;
                            car.Car_Category = ctype.CarCardType_Category;
                            owner.Owner_CardType = ctype.CarCardType_Type;
                            car.Car_IsMoreCar = ctype.CarCardType_IsMoreCar;
                        }
                    }
                }
            }

            List<Model.OrderDetail> orderDetailList = null;
            if (!isFllowCar)
                orderDetailList = allDetailList?.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Out || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Follow);
            else
                orderDetailList = allDetailList?.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Out || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Follow || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Close || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.InClose);

            if (orderDetailList != null)
            {
                orderDetailList = orderDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                for (var i = 0; i < orderDetailList.Count; i++)
                {
                    //检测停车明细出场时间是否合理
                    if (i + 1 < orderDetailList.Count)
                    {
                        if (orderDetailList[i].OrderDetail_OutTime == null) { orderDetailList[i].OrderDetail_OutTime = orderDetailList[i + 1].OrderDetail_EnterTime; continue; }
                        if (orderDetailList[i].OrderDetail_OutTime > orderDetailList[i + 1].OrderDetail_EnterTime) { orderDetailList[i].OrderDetail_OutTime = orderDetailList[i + 1].OrderDetail_EnterTime; continue; }
                    }
                }
            }

            if (orderDetailList != null && orderDetailList.Count > 0)
            {
                orderDetailList = orderDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                for (var i = 0; i < orderDetailList.Count; i++)
                {
                    if (!string.IsNullOrEmpty(carTypeNo))
                    {
                        parkOrder.ParkOrder_CarType = carTypeNo; orderDetailList[i].OrderDetail_CarType = carTypeNo;
                    }
                    else
                    {
                        if (car != null && parkOrderCardType.CarCardType_Type != (int)CarTypeEnum.DeviceVisitor)
                        {
                            parkOrder.ParkOrder_CarType = car.Car_VehicleTypeNo; orderDetailList[i].OrderDetail_CarType = car.Car_VehicleTypeNo;
                        }
                    }

                    if (changeCarCardType != null)
                    {
                        parkOrder.ParkOrder_CarCardType = changeCarCardType.CarCardType_No; orderDetailList[i].OrderDetail_CarCardType = changeCarCardType.CarCardType_No;
                    }
                    else
                    {
                        if (car != null && parkOrderCardType.CarCardType_Type != (int)CarTypeEnum.DeviceVisitor)
                        {
                            parkOrder.ParkOrder_CarCardType = car.Car_TypeNo; orderDetailList[i].OrderDetail_CarCardType = car.Car_TypeNo;
                        }
                    }

                }
            }

            List<Model.OrderDetail> orderDetailList1 = new List<Model.OrderDetail>();//有效期前
            List<Model.OrderDetail> orderDetailList2 = new List<Model.OrderDetail>();//有效期内
            List<Model.OrderDetail> orderDetailList3 = new List<Model.OrderDetail>();//过期停车
            int? payed = 0;
            Model.CarCardType policyCct = null;
            cct = null;
            Model.CarType ct = null;
            ChargeModels.PayResult result1 = null, result2 = null, result3 = null;

            Model.Car businesCar = null;
            //读取商家车信息
            BLL.BusinessCar.MacthBusinessCar(parkOrder.ParkOrder_CarNo, parkOrder.ParkOrder_EnterTime.Value, outTime.Value, out var bCar, ref businesCar, ref cct, ref ct, basicData?.cctList, basicData?.ctList);
            //商家车/访客车 处理
            bool SpecialCar = CheckSpecialCar(ref businesCar, parkOrder, ref orderDetailList, ref parkOrderCardType, outTime, ref cct, ref ct, out var isCalcFree);
            if (!isCalcFree)
            {
                payResult.payed = 0;
                payResult.payedmsg = "无需缴费";
                payResult.calctime = outTime.Value;
                return payResult;
            }
            else
            {
                if (businesCar != null && car != null)
                {
                    cct = null;
                    ct = null;
                }

                if (businesCar != null && car == null) car = businesCar;
            }

            //修改了车牌类型
            if (changeCarCardType != null && parkorderCct != changeCarCardType.CarCardType_No) { cct = changeCarCardType; }
            //修改了车牌颜色
            if (changeCarType != null && parkorderCt != changeCarType.CarType_No) { ct = changeCarType; }

            if (car == null)
            {
                //车辆信息为空,如果是临停车，直接计费
                if (CarTypeHelper.GetCarTypeIndex(parkOrderCardType.CarCardType_Category) == (int)Common.CarTypeEnum.Temp
                    || (changeCarCardType != null && CarTypeHelper.GetCarTypeIndex(changeCarCardType.CarCardType_Category) == (int)Common.CarTypeEnum.Temp))
                {
                    ChargeModels.PayResult ret = GetAllCharge(parkOrder, outTime, car, crList, UseOrderCoupon, orderDetailList, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, cctList, null, basicData);//临时车直接计费
                    ret.parktimemin = Math.Floor(ret.parktimemin);
                    ret.calctime = outTime.Value;
                    return ret;
                }
                //车辆信息为空,非临停车，按过期处理
                List<Models.OrderDetailResult> rsList2 = RetOrderDetail2(parkOrder, orderDetailList, outTime, ref policyCct, ref orderDetailList1, ref payed);
                if (rsList2.Count > 0)
                {
                    var subList = rsList2.FindAll(x => x.OrderDetail_Status == 2);
                    if (subList != null && subList.Count > 0)
                    {
                        payResult.payed = 2;
                        payResult.payedamount = 0;
                        payResult.payedmsg = subList[0].OrderDetail_Msg;
                        payResult.parktimemin = Math.Floor(payResult.parktimemin);
                        payResult.calctime = outTime.Value;
                        return payResult;
                    }

                    var subList2 = rsList2.FindAll(x => x.OrderDetail_Status == 1);
                    if (subList2 != null && subList2.Count > 0)
                    {
                        var sumDetailCount = orderDetailList1.Count + orderDetailList2.Count + orderDetailList3.Count;
                        if (sumDetailCount == 0)
                        {
                            payResult.payed = 0;
                            payResult.payedamount = 0;
                            payResult.payedmsg = "车辆过期允许通行，无需缴费";
                            payResult.parktimemin = Math.Floor(payResult.parktimemin);
                            payResult.calctime = outTime.Value;
                            return payResult;
                        }
                    }
                }

                result1 = GetAllCharge(parkOrder, outTime, car, null, false, orderDetailList1, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, cctList, null, basicData);

                if (basicData?.ppList?.Count > 0)
                {
                    policy = basicData.ppList.FirstOrDefault();
                }
                else
                {
                    policy = BLL.PolicyPark.GetEntity(null);
                }

                //车场配置限制最大使用金额
                if (result1.payed == 1 && (policy?.PolicyPark_MaxUseAmount == 0 || result1.payedamount <= policy?.PolicyPark_MaxUseAmount))
                {
                    //优惠集合为空，读取数据库
                    if (crList == null)
                    {
                        if (UseOrderCoupon) crList = GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime);
                    }
                    else
                    {
                        if (UseOrderCoupon) crList.AddRange(GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime));
                    }

                    if (crList != null) payResult.recordlist = TyziTools.Json.ToObject<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(crList));
                    result1.recordlist = crList == null ? null : TyziTools.Json.ToObject<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(crList));

                    //根据车场策略限制优惠券张数
                    if (crList != null && result1 != null)
                    {
                        int? PolicyPark_MaxDiscount = 0;
                        crList = GetPolicyCoupon(parkOrder.ParkOrder_ParkNo, crList, out PolicyPark_MaxDiscount, basicData);
                        result1.couponcount = Utils.ObjectToInt(PolicyPark_MaxDiscount, 0);
                    }
                    //优惠券处理
                    result1 = CouponResult(result1, parkOrder, outTime, orderDetailList1, ref crList, UseOrderCoupon, PayOrder_PayedTime, car);
                    result1 = GetPayResult(new List<ChargeModels.PayResult>() { result1, result2, result3 });
                }

                result1.calctime = outTime.Value;
                return result1;
            }

            if (cct == null) cct = BLL.CarCardType.GetEntity(car.Car_TypeNo);
            if (cct == null) { payResult.calctime = outTime.Value; payResult.payed = 2; payResult.payedmsg = "计费失败，未找到车牌颜色信息"; return payResult; }

            //商家车处理
            if (cct != null && car != null && cct.CarCardType_Category == Model.EnumCarType.Free.ToString())
            {
                //商家车过期处理
                if (car.Car_EndTime < outTime)
                {
                    Model.Car monthCar = BLL.Car.GetEntityByCarNo(car.Car_CarNo);
                    if (monthCar != null)
                    {
                        var monthOwner = BLL.Owner.GetEntity(monthCar.Car_OwnerNo);

                        if (monthOwner != null)
                        {
                            var ctype = cctList.Find(x => x.CarCardType_No == monthOwner.Owner_CardTypeNo);
                            if (ctype != null && ctype.CarCardType_IsMoreCar == 1)
                            {
                                monthCar.Car_TypeNo = ctype.CarCardType_No;
                                monthCar.Car_Category = ctype.CarCardType_Category;
                                monthCar.Car_IsMoreCar = ctype.CarCardType_IsMoreCar;
                                if (owner != null)
                                {
                                    monthCar.Car_BeginTime = owner.Owner_StartTime;
                                    monthCar.Car_EndTime = owner.Owner_EndTime;
                                }
                                monthOwner.Owner_CardType = ctype.CarCardType_Type;
                                cct = ctype;
                            }
                            car = monthCar;
                            owner = monthOwner;

                            if (parkOrder != null)
                            {
                                parkOrder.ParkOrder_CarCardType = owner.Owner_CardTypeNo;
                                parkOrder.ParkOrder_CarType = car.Car_VehicleTypeNo;

                                if (orderDetailList != null && orderDetailList.Count > 0)
                                {
                                    foreach (var x in orderDetailList)
                                    {
                                        x.OrderDetail_CarCardType = owner.Owner_CardTypeNo;
                                        x.OrderDetail_CarType = car.Car_VehicleTypeNo;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //储值车处理（车位账户余额大于0，则直接按照订单进出场计费，抵扣储值车余额；如果余额为0，则按照车辆过期处理）
            if (cct != null && car != null && cct.CarCardType_Category == Model.EnumCarType.Prepaid.ToString())
            {
                if (car != null && owner == null && !string.IsNullOrEmpty(car.Car_OwnerNo)) { owner = BLL.Owner.GetEntity(car.Car_OwnerNo); }
                if (owner != null)
                {
                    if (owner.Owner_Balance > 0)
                    {
                        //if (basicData?.checkChuzhiCharage ?? true) UseChuzhiCharage = true;
                        ChargeModels.PayResult ret = GetAllCharge(parkOrder, outTime, car, crList, UseOrderCoupon, orderDetailList, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, cctList, cct, basicData);
                        ret.parktimemin = Math.Floor(ret.parktimemin);
                        ret.calctime = outTime.Value;
                        return ret;
                    }
                    else
                    {
                        car.Car_BeginTime = owner.Owner_StartTime = parkOrder.ParkOrder_EnterTime.Value.AddDays(-1);
                        car.Car_EndTime = owner.Owner_EndTime = parkOrder.ParkOrder_EnterTime.Value.AddMinutes(-1);
                    }
                }
            }

            //判断时间段是否完整，若不完整，按照入场的车牌类型计费
            if (car.Car_BeginTime == null || car.Car_EndTime == null || SpecialCar)
            {
                ChargeModels.PayResult ret = GetAllCharge(parkOrder, outTime, car, crList, UseOrderCoupon, orderDetailList, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, cctList, cct, basicData);
                ret.parktimemin = Math.Floor(ret.parktimemin);
                ret.calctime = outTime.Value;
                return ret;
            }

            //固定车过期处理并增加宽限天数
            CheckCarSpace(ref cct, ref car, ref owner, outTime, ref parkOrder, ref orderDetailList);

            List<Models.OrderDetailResult> rsList = RetOrderDetail(parkOrder, orderDetailList, car, outTime, cct, parkOrderCardType, ref policyCct, ref orderDetailList1, ref orderDetailList2, ref orderDetailList3, ref payed, cctList.Find(x => x.CarCardType_Category == "3651"));
            if (rsList.Count > 0)
            {
                var subList = rsList.FindAll(x => x.OrderDetail_Status == 2);
                if (subList != null && subList.Count > 0)
                {
                    payResult.payed = 2;
                    payResult.payedamount = 0;
                    payResult.payedmsg = subList[0].OrderDetail_Msg;
                    payResult.parktimemin = Math.Floor(payResult.parktimemin);
                    payResult.calctime = outTime.Value;
                    return payResult;
                }

                var subList2 = rsList.FindAll(x => x.OrderDetail_Status == 1);
                if (subList2 != null && subList2.Count > 0)
                {
                    var sumDetailCount = orderDetailList1.Count + orderDetailList2.Count + orderDetailList3.Count;
                    if (sumDetailCount == 0)
                    {
                        payResult.payed = 0;
                        payResult.payedamount = 0;
                        payResult.payedmsg = "车辆过期允许通行，无需缴费";
                        payResult.parktimemin = Math.Floor(payResult.parktimemin);
                        payResult.calctime = outTime.Value;
                        return payResult;
                    }
                }
            }

            if (orderDetailList1 != null && orderDetailList1.Count > 0)
            {
                //1.登记时间段前的停车时长
                result1 = GetAllCharge(parkOrder, car.Car_BeginTime > outTime ? outTime : car.Car_BeginTime, car, null, false, orderDetailList1, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, null, null, basicData);
            }

            if (orderDetailList2 != null && orderDetailList2.Count > 0)
            {
                //2.登记时间段内的停车时长
                result2 = GetAllCharge(parkOrder, car.Car_EndTime > outTime ? outTime : car.Car_EndTime, car, null, false, orderDetailList2, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, null, null, basicData);
            }

            if (orderDetailList3 != null && orderDetailList3.Count > 0)
            {
                if (policyCct == null) { payResult.calctime = outTime.Value; payResult.payed = 2; payResult.payedmsg = "计费失败，未找到车场策略。"; return payResult; }
                //3.过期停车时长
                result3 = GetAllCharge(parkOrder, outTime, car, null, false, orderDetailList3, UseChuzhiCharage, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, null, null, basicData);
            }

            //结果累计
            ChargeModels.PayResult result4 = GetPayResult(new List<ChargeModels.PayResult>() { result1, result2, result3 });

            if (basicData?.ppList?.Count > 0)
            {
                policy = basicData.ppList.FirstOrDefault();
            }
            else
            {
                policy = BLL.PolicyPark.GetEntity(null);
            }

            //车场配置限制最大使用金额
            if (result4.payed == 1 && (policy?.PolicyPark_MaxUseAmount == 0 || result4.payedamount <= policy?.PolicyPark_MaxUseAmount))
            {
                //优惠集合为空，读取数据库
                if (crList == null)
                {
                    if (UseOrderCoupon) crList = GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime);
                }
                else
                {
                    if (UseOrderCoupon) crList.AddRange(GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime));
                }
                if (crList != null) payResult.recordlist = TyziTools.Json.ToObject<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(crList));

                //根据车场策略限制优惠券张数
                if (crList != null && payResult != null)
                {
                    int? PolicyPark_MaxDiscount = 0;
                    crList = GetPolicyCoupon(parkOrder.ParkOrder_ParkNo, crList, out PolicyPark_MaxDiscount);
                    payResult.couponcount = Utils.ObjectToInt(PolicyPark_MaxDiscount, 0);
                }

                //优惠券处理
                if (result1 != null) result1 = CouponResult(result1, parkOrder, car.Car_BeginTime > outTime ? outTime : car.Car_BeginTime, orderDetailList1, ref crList, UseOrderCoupon, PayOrder_PayedTime, car);
                if (result2 != null) result2 = CouponResult(result2, parkOrder, car.Car_EndTime > outTime ? outTime : car.Car_EndTime, orderDetailList2, ref crList, UseOrderCoupon, PayOrder_PayedTime, car);
                if (result3 != null) result3 = CouponResult(result3, parkOrder, outTime, orderDetailList3, ref crList, UseOrderCoupon, PayOrder_PayedTime, car);
                //结果累计
                result4 = GetPayResult(new List<ChargeModels.PayResult>() { result1, result2, result3 });
                result4.recordlist = payResult.recordlist;
            }
            else
            {
                result4.recordlist = null;
                result4.uselist = null;
            }

            result4.calctime = outTime.Value;
            return result4;
        }

        /// <summary>
        /// 内场区域有进场无出场的记录，如果有上层区域的停车记录，就按照上层区域的规则来计费，以此类推向上查找
        /// </summary>
        /// <returns></returns>
        public static Model.OrderDetail GetPreviousRecord(Model.OrderDetail detail, List<Model.ParkArea> areaList, List<Model.OrderDetail> allDetailList, int MaxCount = 0)
        {
            if (MaxCount > 20) return detail;

            var area = areaList?.Find(x => x.ParkArea_No == detail.OrderDetail_ParkAreaNo && x.ParkArea_Level != 0);
            if (area != null)
            {
                var topArea = areaList?.Find(x => x.ParkArea_No == area.ParkArea_FNo);
                if (topArea != null)
                {
                    var topDetail = allDetailList.Find(x => x.OrderDetail_ParkAreaNo == topArea.ParkArea_No);
                    detail.OrderDetail_ParkAreaNo = topArea.ParkArea_No;
                    detail.OrderDetail_ParkAreaName = topArea.ParkArea_Name;
                    if (topDetail != null)
                    {
                        detail.OrderDetail_CarCardType = topDetail.OrderDetail_CarCardType;
                        detail.OrderDetail_CarCardTypeName = topDetail.OrderDetail_CarCardTypeName;
                        detail.OrderDetail_CarType = topDetail.OrderDetail_CarType;
                        detail.OrderDetail_CarTypeName = topDetail.OrderDetail_CarTypeName;
                        return detail;
                    }
                    else
                    {
                        if (topArea.ParkArea_Level == 0)
                        {
                            return detail;
                        }
                        else
                        {
                            return GetPreviousRecord(detail, areaList, allDetailList, MaxCount + 1);
                        }
                    }
                }
            }

            return detail;
        }

        /// <summary>
        /// 车辆特殊处理（商家车/访客车）
        /// </summary>
        /// <param name="car">车辆信息</param>
        /// <param name="parkOrder">订单信息</param>
        /// <param name="parkOrderCardType">订单车牌类型信息</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="cct">车牌类型信息</param>
        /// <param name="ct">车牌颜色信息</param>
        /// <returns>True-按临时车处理，False-按原有类型</returns>
        private static bool CheckSpecialCar(ref Model.Car car, Model.ParkOrder parkOrder, ref List<Model.OrderDetail> orderDetailList, ref Model.CarCardType parkOrderCardType,
            DateTime? outTime, ref Model.CarCardType cct, ref Model.CarType ct, out bool isCalcFree)
        {
            bool SpecialCar = false;
            isCalcFree = true;//是否需要计费

            if (parkOrder?.ParkOrder_IsNoInRecord == 1) return SpecialCar;

            //商家车特殊处理
            //1、商家车辆在有效期内进出免费；
            //2、商家车辆在有效期前进出场或在有效期后进出场（不与有效期范围产生时段交集的情况进出场），按停车订单的类型收费（车道默认类型）；
            //3、商家车有效期与停车时间交集的情况，只收商家车有效期外的停车时间段费用（按商家车过期策略指定的计费规则收费）。
            if (car != null && cct != null && cct.CarCardType_Type == (int)CarTypeEnum.MerchantCar)
            {
                if (car.Car_BeginTime != null && car.Car_EndTime != null)
                {
                    DateTime validStart = car.Car_BeginTime.Value;
                    DateTime validEnd = car.Car_EndTime.Value;

                    var newDetailList = new List<Model.OrderDetail>();

                    foreach (var detail in orderDetailList)
                    {
                        DateTime detailEnter = detail.OrderDetail_EnterTime.Value;
                        DateTime detailOut = (detail.OrderDetail_OutTime ?? outTime).Value;

                        // 完全在有效期内（免费）
                        if (detailEnter >= validStart && detailOut <= validEnd)
                        {
                            var newDetail = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                            newDetail.OrderDetail_EnterTime = detailEnter;
                            newDetail.OrderDetail_OutTime = detailOut;
                            newDetail.OrderDetail_CarCardType = cct.CarCardType_No;
                            newDetail.OrderDetail_CarCardTypeName = cct.CarCardType_Name;
                            newDetail.orderdetail_IsCharge = 1;
                            newDetailList.Add(newDetail);
                            continue;
                        }

                        // 完全不与有效期交集（原样保留）
                        if (detailOut <= validStart || detailEnter >= validEnd)
                        {
                            newDetailList.Add(detail);
                            continue;
                        }

                        // 拆分情况处理
                        // ① 有效期前段（收费）
                        if (detailEnter < validStart)
                        {
                            var beforeDetail = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                            beforeDetail.OrderDetail_EnterTime = detailEnter;
                            beforeDetail.OrderDetail_OutTime = validStart;
                            newDetailList.Add(beforeDetail);
                        }

                        // ② 有效期中段（免费）
                        var freeStart = (detailEnter < validStart) ? validStart : detailEnter;
                        var freeEnd = (detailOut > validEnd) ? validEnd : detailOut;
                        if (freeEnd > freeStart)
                        {
                            var freeDetail = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                            freeDetail.OrderDetail_EnterTime = freeStart;
                            freeDetail.OrderDetail_OutTime = freeEnd;
                            freeDetail.OrderDetail_CarCardType = cct.CarCardType_No;
                            freeDetail.OrderDetail_CarCardTypeName = cct.CarCardType_Name;
                            freeDetail.orderdetail_IsCharge = 1;
                            newDetailList.Add(freeDetail);
                        }

                        // ③ 有效期后段（收费）
                        if (detailOut > validEnd)
                        {
                            var afterDetail = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                            afterDetail.OrderDetail_EnterTime = validEnd;
                            afterDetail.OrderDetail_OutTime = detailOut;
                            newDetailList.Add(afterDetail);
                        }
                    }

                    orderDetailList = newDetailList;

                    // 设置整体订单卡类为商家车类型
                    parkOrderCardType = cct;
                    parkOrder.ParkOrder_CarCardType = cct.CarCardType_No;
                    parkOrder.ParkOrder_CarCardTypeName = cct.CarCardType_Name;
                }
            }

            //访客车处理
            //1、访客车在预约时间内入场按访客车正常收费（添加计费规则）；
            //2、访客车在预约时间外入场按临时车收费。
            else if (CarTypeHelper.GetCarTypeIndex(parkOrderCardType.CarCardType_Category) == (int)CarTypeEnum.DeviceVisitor)
            {
                BLL.Reserve.MacthReserve((int)ParkOrderStatusEnum.Out, parkOrder.ParkOrder_CarNo, out var bCar, ref car, ref cct, ref ct);
                parkOrderCardType = cct ?? parkOrderCardType;
                SpecialCar = true;
            }

            return SpecialCar;
        }

        /// <summary>
        /// 固定车车分段处理 并 增加宽限天数
        /// </summary>
        /// <param name="car"></param>
        /// <param name="owner"></param>
        /// <param name="outTime"></param>
        /// <param name="parkOrder"></param>
        /// <param name="orderDetailList"></param>
        private static void CheckCarSpace(ref Model.CarCardType cct, ref Model.Car car, ref Model.Owner owner, DateTime? outTime, ref Model.ParkOrder parkOrder, ref List<Model.OrderDetail> orderDetailList)
        {
            if (car != null && owner == null && !string.IsNullOrEmpty(car.Car_OwnerNo)) { owner = BLL.Owner.GetEntity(car.Car_OwnerNo); }

            if (cct != null && owner != null && cct.CarCardType_Type != 2)
            {
                BLL.Car.OwnerAddEnDay(ref owner, null);
                car.Car_BeginTime = Utils.ObjectToDateTime(owner.Owner_StartTime.Value.ToString("yyyy-MM-dd 00:00:00"));
                car.Car_EndTime = Utils.ObjectToDateTime(owner.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
            }

            //对过期的停车时段 与 未过期的时段 要拆分处理
            if (orderDetailList != null && owner != null && owner.Owner_EndTime != null && owner.Owner_StartTime != null)
            {
                List<Model.OrderDetail> newDetailList = new List<Model.OrderDetail>();
                for (int i = 0; i < orderDetailList.Count; i++)
                {
                    if (i == (orderDetailList.Count - 1) && orderDetailList[i].OrderDetail_OutTime == null) { orderDetailList[i].OrderDetail_OutTime = outTime; }

                    CarTimeChange(owner, orderDetailList[i], ref newDetailList, ref parkOrder);
                }
                orderDetailList = newDetailList;
            }

            return;
        }

        /// <summary>
        /// 根据固定车有效期，停车明细的拆分与状态修改
        /// </summary>
        /// <param name="owner"></param>
        /// <param name="detail"></param>
        /// <param name="newDetailList"></param>
        /// <param name="parkOrder"></param>
        private static void CarTimeChange(Model.Owner owner, Model.OrderDetail detail, ref List<Model.OrderDetail> newDetailList, ref Model.ParkOrder parkOrder)
        {
            //收费状态：0-收费，1-免费，2-智能升降
            if (detail.orderdetail_IsCharge == 1)
            {
                if (detail.OrderDetail_EnterTime < owner.Owner_StartTime)//入场时间小于有效开始时间
                {
                    if (detail.OrderDetail_OutTime > owner.Owner_EndTime.Value.AddSeconds(1))//出场时间大于有效结束时间
                    {
                        //未生效时间段
                        Model.OrderDetail newDetail2 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                        newDetail2.OrderDetail_OutTime = owner.Owner_StartTime.Value;
                        newDetail2.orderdetail_IsCharge = 0;
                        newDetailList.Add(newDetail2);

                        //过期时间
                        Model.OrderDetail newDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                        newDetail.OrderDetail_EnterTime = owner.Owner_EndTime.Value.AddSeconds(1);
                        newDetail.orderdetail_IsCharge = 0;
                        newDetailList.Add(newDetail);

                        //有效期内
                        detail.OrderDetail_EnterTime = owner.Owner_StartTime.Value.AddSeconds(1);
                        detail.OrderDetail_OutTime = owner.Owner_EndTime.Value;
                        detail.orderdetail_IsCharge = parkOrder.ParkOrder_IsLift != 0 ? 1 : 0;
                        newDetailList.Add(detail);
                    }
                    else//入场时间小于有效开始时间，出场时间小于有效结束时间
                    {
                        if (detail.OrderDetail_OutTime > owner.Owner_StartTime)//出场时间大于有效开始时间
                        {
                            //有效期内
                            Model.OrderDetail newDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                            newDetail.OrderDetail_EnterTime = owner.Owner_StartTime.Value.AddSeconds(1);
                            newDetail.orderdetail_IsCharge = parkOrder.ParkOrder_IsLift != 0 ? 1 : 0;
                            newDetailList.Add(newDetail);

                            //过期
                            detail.OrderDetail_OutTime = owner.Owner_StartTime;
                            detail.orderdetail_IsCharge = 0;
                            newDetailList.Add(detail);
                        }
                        else//出场时间小于有效开始时间
                        {
                            detail.orderdetail_IsCharge = 0;
                            newDetailList.Add(detail);
                        }
                    }
                }
                else//入场时间大于有效开始时间
                {
                    if (detail.OrderDetail_EnterTime > owner.Owner_EndTime.Value.AddSeconds(1))//入场时间大于有效开始时间,入场时间大于有效结束时间
                    {
                        detail.orderdetail_IsCharge = 0;
                        newDetailList.Add(detail);
                    }
                    else//入场时间大于有效开始时间,入场时间小于有效结束时间
                    {
                        if (detail.OrderDetail_OutTime > owner.Owner_EndTime.Value.AddSeconds(1))//出场时间大于有效结束时间
                        {
                            //有效期内
                            Model.OrderDetail newDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(detail));
                            newDetail.OrderDetail_OutTime = owner.Owner_EndTime;
                            newDetail.orderdetail_IsCharge = parkOrder.ParkOrder_IsLift != 0 ? 1 : 0;
                            newDetailList.Add(newDetail);

                            //过期
                            detail.OrderDetail_EnterTime = owner.Owner_EndTime.Value.AddSeconds(1);
                            detail.orderdetail_IsCharge = 0;
                            newDetailList.Add(detail);
                        }
                        else//入场时间大于有效开始时间，出场时间小于有效结束时间
                        {
                            newDetailList.Add(detail);
                        }
                    }
                }
            }
            else
            {
                newDetailList.Add(detail);
            }
        }
        #endregion

        #region 数据筛选处理
        /// <summary>
        /// 获取临停计费（不包括储值费用抵扣）
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="Car_No">车牌号码</param>
        /// <param name="CarTypeNo">车牌颜色编号</param>
        /// <param name="CarCardTypeNo">车牌类型编号</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="crList">优惠券集合，为null时会重新到数据库查询优惠券集合，不为null则直接计费使用</param>
        /// <param name="UseOrderCoupon">是否使用关联当前车辆的其它优惠券</param>
        private static ChargeModels.PayResult GetCharge(Model.ParkOrder parkOrder, DateTime? outTime, Model.Car car = null, List<Model.CouponRecordIntExt> crList = null,
            bool UseOrderCoupon = true, List<Model.OrderDetail> orderDetailList = null, string carTypeNo = "", string carCarTypeNo = "", DateTime? PayOrder_PayedTime = null,
            Model.Owner owner = null, List<Model.CarCardType> cctList = null, CalcBasicData basiceData = null)
        {
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            ChargeModels.PayResult payResult = new ChargeModels.PayResult();
            List<ChargeModels.PayDetail> payDetailList = new List<ChargeModels.PayDetail>();
            List<ChargeModels.PayDetail> isChargeDetailList = new List<ChargeModels.PayDetail>();

            if (parkOrder != null)
            {
                List<Model.OrderDetail> orderDetailListbakup = null;
                //不需要支付
                //ChargeModels.PayDetail ret = CreatePayDetail(0, "无需缴费", 0, order.OrderDetail_ParkAreaNo, order.OrderDetail_ParkOrderNo, order.OrderDetail_No, order.OrderDetail_ID,
                //nextCycleTime, NextCyclePaidFees, detailOutTime, order.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, order.OrderDetail_ParkAreaName);
                //ret.iscarexpire = param.readParam.IsCarExpire;
                //ret.isovertime = param.readParam.IsOverTime;
                //payDetailList.Add(ret);

                if (orderDetailList == null) { orderDetailList = BLL.OrderDetail.GetAllEntity(parkOrder.ParkOrder_No)?.FindAll(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In || x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Out).OrderBy(x => x.OrderDetail_EnterTime).ToList(); orderDetailList.ForEach(x => { x.OrderDetail_Lock = 0; }); }
                if (orderDetailList != null)
                {
                    orderDetailListbakup = orderDetailList.Copy();

                    orderDetailList.Where(x => x.orderdetail_IsCharge == 1).ToList().ForEach(order =>
                    {
                        if (order.orderdetail_IsCharge == 1)//一位多车免费
                        {
                            //不需要支付
                            ChargeModels.PayDetail ret = CreatePayDetail(0, "无需缴费", 0, order.OrderDetail_ParkAreaNo, order.OrderDetail_ParkOrderNo, order.OrderDetail_No, order.OrderDetail_ID,
                                 null, null, order.OrderDetail_OutTime ?? outTime, order.OrderDetail_EnterTime, null, null, null, order.OrderDetail_ParkAreaName);
                            isChargeDetailList.Add(ret);
                        }
                    });

                    orderDetailList = orderDetailList.Where(x => x.orderdetail_IsCharge != 1).OrderBy(x => x.OrderDetail_EnterTime).ToList();
                }
                if (orderDetailList != null && orderDetailList.Count > 0)
                {
                    //    bool charginPile = false;
                    //    if (crList != null) { 
                    //    Model.CouponRecord uCouponModel = crList.Find(c => c.CouponRecord_Other == 1);
                    //    if (uCouponModel != null)//如果为充电桩优惠，则把去掉不使用首计时charginPile
                    //    {
                    //        charginPile = true;
                    //    }
                    //}

                    payResult = GetPriCharge(parkOrder, outTime, orderDetailList, car, carTypeNo, carCarTypeNo, PayOrder_PayedTime, cctList, false, checkPayMonthMax: basiceData?.checkPayMonthMax ?? true);
                    if (payResult == null)
                    {
                        payResult = new ChargeModels.PayResult();
                        payResult.payed = 0;
                        payResult.payedmsg = "无需缴费";
                        return payResult;
                    }
                }
                else
                {
                    payResult = new ChargeModels.PayResult();
                    payResult.payed = 0;
                    payResult.payedmsg = "无需缴费";
                    orderDetailListbakup?.ForEach(x =>
                    {
                        ChargeModels.PayDetail ret = CreatePayDetail(0, "无需缴费", 0, x.OrderDetail_ParkAreaNo, x.OrderDetail_ParkOrderNo, x.OrderDetail_No, x.OrderDetail_ID,
                        null, null, x.OrderDetail_OutTime ?? outTime, x.OrderDetail_EnterTime, null, null, null, x.OrderDetail_ParkAreaName);
                        ret.CalcResult = x.orderdetail_IsCharge == 1 ? "" : (x.orderdetail_IsCharge == 2 ? "智能升降" : "");
                        payDetailList.Add(ret);
                    });
                    payResult.list = payDetailList;
                    return payResult;
                }

                Model.PolicyPark policy = basiceData?.ppList?.Find(x => x.PolicyPark_ParkNo == parkOrder.ParkOrder_ParkNo);
                if (policy == null) policy = BLL.PolicyPark.GetEntity(parkOrder.ParkOrder_ParkNo);

                if (policy?.PolicyPark_MaxUseAmount == 0 || (payResult.payed == 1 && payResult.payedamount <= policy?.PolicyPark_MaxUseAmount))
                {
                    //优惠集合为空，读取数据库
                    if (crList == null)
                    {
                        if (UseOrderCoupon) crList = GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime);
                    }
                    else
                    {
                        if (UseOrderCoupon) { crList.AddRange(GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime)); crList.Distinct(); }
                    }

                    payResult.recordlist = crList == null ? new List<ChargeModels.CouponRecordIntExt>() : TyziTools.Json.ToModel<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(crList));

                    //根据车场策略限制优惠券张数
                    if (UseOrderCoupon && crList != null && payResult != null)
                    {
                        int? PolicyPark_MaxDiscount = 0;
                        crList = GetPolicyCoupon(parkOrder.ParkOrder_ParkNo, crList, out PolicyPark_MaxDiscount);
                        payResult.couponcount = Utils.ObjectToInt(PolicyPark_MaxDiscount, 0);
                    }
                }
                else
                {
                    payResult.recordlist = null;
                }

            }

            //优惠券使用
            if (payResult != null && crList != null)
            {
                bool charginPile = false;
                if (crList != null)
                {
                    Model.CouponRecord uCouponModel = crList.Find(c => c.CouponRecord_Other == 1);
                    if (uCouponModel != null)//如果为充电桩优惠，则把去掉不使用首计时charginPile
                    {
                        charginPile = true;
                    }
                }

                payResult.uselist = GetCouponCharge(parkOrder, crList, outTime, orderDetailList, payResult, PayOrder_PayedTime, cctList, charginPile, car, basiceData);
            }
            if (payResult.payed == 1)
            {
                if (payResult.payedamount == 0) { payResult.payed = 0; }
            }
            if (payResult.payed == 0)
            {
                if (payResult.payedamount > 0) { payResult.payed = 1; }
            }

            if (payResult.list != null) payResult.list.AddRange(isChargeDetailList);
            //sw.Stop();
            //BLL.SystemLogs.AddLog(null, "获取停车费用", "获取停车费用耗时 " + sw.ElapsedMilliseconds.ToString() + " 毫秒");
            return payResult;
        }

        /// <summary>
        /// 获取临停计费（不包括储值费用抵扣）
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="Car_No">车牌号码</param>
        /// <param name="CarTypeNo">车牌颜色编号</param>
        /// <param name="CarCardTypeNo">车牌类型编号</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="crList">优惠券集合，为null时会重新到数据库查询优惠券集合，不为null则直接计费使用</param>
        private static ChargeModels.PayResult GetCharge(Model.ParkOrder parkOrder, DateTime? outTime, List<Model.OrderDetail> orderDetailList, List<Model.CouponRecordIntExt> crList = null, string carTypeNo = "", string carCarTypeNo = "", DateTime? PayOrder_PayedTime = null, List<Model.CarCardType> cctList = null)
        {
            ChargeModels.PayResult payResult = null;
            List<ChargeModels.PayDetail> payDetailList = new List<ChargeModels.PayDetail>();
            if (parkOrder != null)
            {
                if (orderDetailList != null) orderDetailList = orderDetailList.Where(x => x.OrderDetail_IsSettle == 0 && x.orderdetail_IsCharge != 1).ToList();
                if (orderDetailList == null || orderDetailList.Count == 0)
                {
                    payResult = new ChargeModels.PayResult();
                    payResult.payed = 0;
                    payResult.payedmsg = "无需缴费";
                    return payResult;
                }
                payResult = GetPriCharge(parkOrder, outTime, orderDetailList, null, carTypeNo, carCarTypeNo, PayOrder_PayedTime, cctList);
                if (payResult == null)
                {
                    payResult = new ChargeModels.PayResult();
                    payResult.payed = 0;
                    payResult.payedmsg = "无需缴费";
                    return payResult;
                }

                //优惠集合为空，读取数据库
                if (crList == null)
                {
                    crList = GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime);
                }
                //根据车场策略限制优惠券张数
                if (crList != null)
                {
                    int? PolicyPark_MaxDiscount = 0;
                    crList = GetPolicyCoupon(parkOrder.ParkOrder_ParkNo, crList, out PolicyPark_MaxDiscount);
                    payResult.couponcount = Utils.ObjectToInt(PolicyPark_MaxDiscount, 0);
                }
            }
            payResult.recordlist = crList == null ? new List<ChargeModels.CouponRecordIntExt>() : TyziTools.Json.ToModel<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(crList)); ;
            //优惠券使用
            if (payResult != null && crList != null)
            {
                payResult.uselist = GetCouponCharge(parkOrder, crList, outTime, orderDetailList, payResult, PayOrder_PayedTime);
            }
            if (payResult.payed == 1)
            {
                if (payResult.payedamount == 0) { payResult.payed = 0; }
            }
            if (payResult.payed == 0)
            {
                if (payResult.payedamount > 0) { payResult.payed = 1; }
            }
            return payResult;
        }

        ///// <summary>
        ///// 获取临停计费（不包括储值费用抵扣）
        ///// </summary>
        ///// <param name="Park_No">车场编号</param>
        ///// <param name="Car_No">车牌号码</param>
        ///// <param name="CarTypeNo">车牌颜色编号</param>
        ///// <param name="CarCardTypeNo">车牌类型编号</param>
        ///// <param name="outTime">出场时间</param>
        ///// <param name="crList">优惠券集合，为null时会重新到数据库查询优惠券集合，不为null则直接计费使用</param>
        //private static ChargeModels.PayResult GetCharge(string Park_No, string Car_No, DateTime? outTime, List<Model.CouponRecordIntExt> crList = null, string carTypeNo = "", string carCarTypeNo = "", List<Model.CarCardType> cctList = null)
        //{
        //    List<ChargeModels.PayDetail> payDetailList = new List<ChargeModels.PayDetail>();
        //    List<Model.OrderDetail> orderDetailList = new List<Model.OrderDetail>();
        //    ChargeModels.PayResult payResult = null;
        //    //获取当前未结算的停车订单
        //    Model.ParkOrder parkOrder = BLL.ParkOrder.GetParkOrder("*", Park_No, Car_No, 0, 200);
        //    if (parkOrder != null)
        //    {
        //        payResult = GetCharge(parkOrder, outTime, null, crList, carTypeNo, carCarTypeNo, null, cctList);
        //    }
        //    if (payResult.payed == 1)
        //    {
        //        if (payResult.payedamount == 0) { payResult.payed = 0; }
        //    }
        //    if (payResult.payed == 0)
        //    {
        //        if (payResult.payedamount > 0) { payResult.payed = 1; }
        //    }
        //    return payResult;
        //}

        /// <summary>
        /// 获取临停计费（包括储值费用抵扣）
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="Car_No">车牌号码</param>
        /// <param name="CarTypeNo">车牌颜色编号</param>
        /// <param name="CarCardTypeNo">车牌类型编号</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="car">车辆信息</param>
        /// <param name="crList">优惠券集合，为null时会重新到数据库查询优惠券集合，不为null则直接计费使用</param>
        /// <param name="UseOrderCoupon">是否使用关联当前车辆的其它优惠券</param>
        private static ChargeModels.PayResult GetAllCharge(Model.ParkOrder parkOrder, DateTime? outTime, Model.Car car, List<Model.CouponRecordIntExt> crList = null,
            bool UseOrderCoupon = true, List<Model.OrderDetail> orderDetailList = null, bool UseChuzhiCharage = true, string carTypeNo = "", string carCarTypeNo = "",
            DateTime? PayOrder_PayedTime = null, Model.Owner owner = null, List<Model.CarCardType> cctList = null, Model.CarCardType cct = null, CalcBasicData basiceData = null)
        {
            ChargeModels.PayResult payResult = GetCharge(parkOrder, outTime, car, crList, UseOrderCoupon, orderDetailList, carTypeNo, carCarTypeNo, PayOrder_PayedTime, owner, cctList, basiceData);

            if (AppSettingConfig.SentryMode != "2")
            {
                if (UseChuzhiCharage && !(basiceData?.cloudMqttOffline ?? false))
                {
                    //储值车费用抵扣
                    payResult = BusiCarMode(payResult, car, owner, cct);
                }
                if (payResult.payed == 1)
                {
                    if (payResult.payedamount == 0) { payResult.payed = 0; }
                }
            }
            if (payResult.payed == 0)
            {
                if (payResult.payedamount > 0) { payResult.payed = 1; }
            }
            return payResult;
        }

        /// <summary>
        /// 储值车费用抵扣
        /// </summary>
        /// <param name="payResult"></param>
        /// <param name="car"></param>
        /// <returns></returns>
        public static ChargeModels.PayResult BusiCarMode(ChargeModels.PayResult payResult, Model.Car car, Model.Owner owner, Model.CarCardType cct)
        {
            if (payResult.payedamount > 0 && car != null && payResult != null && payResult.payed == 1)
            {
                if (cct == null) cct = BLL.CarCardType.GetEntity(car.Car_TypeNo);
                if (cct == null || cct.CarCardType_Category != Model.EnumCarType.Prepaid.ToString()) return payResult;
                InterceptNumber(payResult);

                //if (cct.CarCardType_IsMoreCar != 1)
                //{
                //    car.Car_Balance = car.Car_Balance ?? 0;
                //    car.Car_Balance = Utils.StrToDecimal(ToResultString(car.Car_Balance.Value, 2));
                //    //储值车余额
                //    if (car.Car_Balance > 0)
                //    {
                //        if (payResult.payedamount >= car.Car_Balance)
                //        {
                //            payResult.chuzhiamount = car.Car_Balance.Value;
                //            payResult.payedamount = payResult.payedamount - car.Car_Balance.Value;
                //            if (payResult.payedamount == 0) payResult.payed = 0;
                //            if (payResult.payed == 0 && payResult.payedamount > 0) { payResult.payed = 1; }
                //        }
                //        else
                //        {
                //            payResult.chuzhiamount = payResult.payedamount;
                //            payResult.payedamount = 0;
                //            payResult.payed = 0;
                //        }
                //        payResult.payedmsg = "储值费用抵扣";
                //    }
                //}
                //else
                //{
                if (owner != null && owner.Owner_Balance > 0)
                {
                    if (payResult.payedamount >= owner.Owner_Balance)
                    {
                        payResult.chuzhiremainingamount = 0;
                        payResult.chuzhiamount = owner.Owner_Balance.Value;
                        payResult.payedamount = payResult.payedamount - owner.Owner_Balance.Value;
                        if (payResult.payedamount == 0) payResult.payed = 0;
                        if (payResult.payed == 0 && payResult.payedamount > 0) { payResult.payed = 1; }
                    }
                    else
                    {
                        payResult.chuzhiremainingamount = owner.Owner_Balance.Value - payResult.payedamount;
                        payResult.chuzhiamount = payResult.payedamount;
                        payResult.payedamount = 0;
                        payResult.payed = 0;
                    }
                    payResult.payedmsg = "车位储值费用抵扣";
                }
                //}
            }

            return payResult;
        }

        #endregion

        #region 优惠券处理

        /// <summary>
        /// 获取优惠券集合（通过停车订单号）
        /// </summary>
        /// <param name="ParkNo"></param>
        /// <param name="ParkOrder_No"></param>
        /// <returns></returns>
        public static List<Model.CouponRecordIntExt> GetCouponByOrderNo(string ParkOrder_No, DateTime? outTime)
        {
            List<Model.CouponRecordIntExt> crExtList = null;
            List<Model.CouponRecord> crList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_ID,CouponRecord_No,CouponRecord_CouponCode,CouponRecord_Value,CouponRecord_StartTime,CouponRecord_EndTime,CouponRecord_VaildTime,CouponRecord_OnLine,CouponRecord_ParkNo,CouponRecord_Other", $"CouponRecord_ParkOrderNo='{ParkOrder_No}' and CouponRecord_Status=0");
            if (crList != null)
            {
                crList = crList.FindAll(x => x.CouponRecord_VaildTime >= outTime);
                crExtList = new List<Model.CouponRecordIntExt>();
                crList?.ForEach(x =>
                {
                    var model = TyziTools.Json.ToModel<Model.CouponRecordIntExt>(TyziTools.Json.ToString(x));
                    model.CouponRecord_Name = "优惠券";

                    switch (Utils.StrToInt(x.CouponRecord_CouponCode, 0))
                    {
                        case (int)Common.EnumCouponType.Money:
                            model.CouponRecord_Name = $"减免{x.CouponRecord_Value.ToString().Replace(".00", "")}元";
                            break;
                        case (int)Common.EnumCouponType.Discount:
                            model.CouponRecord_Name = $"{x.CouponRecord_Value.ToString().Replace(".00", "")}折";
                            break;
                        case (int)Common.EnumCouponType.Time:
                            model.CouponRecord_Name = $"减免{x.CouponRecord_Value.ToString().Replace(".00", "")}分钟";
                            break;
                        case (int)Common.EnumCouponType.AppointHour:
                            model.CouponRecord_Name = $"免费到{x.CouponRecord_EndTime}";
                            break;
                        case (int)Common.EnumCouponType.HourFree:
                            model.CouponRecord_Name = $"时段全免:{x.CouponRecord_Value.ToString().Replace(".00", "")}分钟";
                            break;
                    }

                    if (x.CouponRecord_CouponCode == "105")
                    {
                        if (outTime >= model.CouponRecord_StartTime && outTime <= model.CouponRecord_VaildTime)
                        {
                            crExtList.Add(model);
                        }
                    }
                    else
                    {
                        crExtList.Add(model);
                    }

                });
            }

            return crExtList;
        }

        /// <summary>
        /// 获取优惠券集合（通过策略）
        /// </summary>
        /// <param name="ParkNo"></param>
        /// <param name="ParkOrder_No"></param>
        /// <returns></returns>
        public static List<Model.CouponRecordIntExt> GetPolicyCoupon(string ParkNo, List<Model.CouponRecordIntExt> crList, out int? PolicyPark_MaxDiscount, CalcBasicData basicData = null)
        {
            PolicyPark_MaxDiscount = 0;

            Model.PolicyPark policy = null;//读取车场配置
            if (basicData?.ppList?.Count > 0)
            {
                policy = basicData.ppList.Find(x => x.PolicyPark_ParkNo == ParkNo);
            }
            else
            {
                policy = BLL.PolicyPark.GetEntity(ParkNo);
            }
            if (policy != null && policy.PolicyPark_MaxDiscount != null)
            {
                PolicyPark_MaxDiscount = policy.PolicyPark_MaxDiscount;
                if (policy.PolicyPark_MaxDiscount == 0 || policy.PolicyPark_MaxDiscount < 0) { return null; }
                if (crList != null && policy.PolicyPark_MaxDiscount < crList.Count)
                    crList = crList.OrderByDescending(x => x.CouponRecord_ID).ToList().Take(PolicyPark_MaxDiscount.Value).Reverse().ToList();
            }

            return crList;
        }

        /// <summary>
        /// 获取临停计费(不使用优惠券)
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="Car_No">车牌号码</param>
        /// <param name="CarTypeNo">车牌颜色编号</param>
        /// <param name="CarCardTypeNo">车牌类型编号</param>
        /// <param name="outTime">出场时间</param>
        private static ChargeModels.PayResult GetPriCharge(Model.ParkOrder parkOrder, DateTime? outTime, List<Model.OrderDetail> orderDetailList, Model.Car car = null, string carTypeNo = "", string carCarTypeNo = "", DateTime? PayOrder_PayedTime = null, List<Model.CarCardType> cctList = null, bool chargingPile = false, bool havaMinCoupon = false, bool checkPayMonthMax = true)
        {
            ChargeModels.PayResult payResult = null;
            List<ChargeModels.PayDetail> payDetailList = new List<ChargeModels.PayDetail>();
            RuleRunData ruleRunData = new RuleRunData();
            if (parkOrder != null)
            {
                if (orderDetailList != null && orderDetailList.Count > 0)
                {
                    orderDetailList = orderDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                    //过滤空出场时间的异常停车明细
                    List<Model.OrderDetail> newDetailList = new List<Model.OrderDetail>();
                    for (var i = 0; i < orderDetailList.Count; i++)
                    {
                        if (orderDetailList[i].OrderDetail_OutTime == null || orderDetailList[i].OrderDetail_OutTime == DateTime.Parse("1900-01-01 00:00:00")) { orderDetailList[i].OrderDetail_OutTime = outTime; }
                        newDetailList.Add(orderDetailList[i]);
                    }
                    orderDetailList = newDetailList;

                    if (GetChargeRulesType() == 0)//标准版本计费
                    {
                        payDetailList = GetChargeBySimple(ruleRunData, parkOrder, orderDetailList, parkOrder.ParkOrder_CarNo, parkOrder.ParkOrder_CarType, parkOrder.ParkOrder_CarCardType, outTime, car, PayOrder_PayedTime, null, cctList, chargingPile, havaMinCoupon);
                    }
                    else
                    {
                        payDetailList = GetChargeByParkOrder(ruleRunData, parkOrder, orderDetailList, parkOrder.ParkOrder_CarNo, parkOrder.ParkOrder_CarType, parkOrder.ParkOrder_CarCardType, outTime, car, PayOrder_PayedTime, null, cctList, chargingPile, havaMinCoupon, checkPayMonthMax);
                    }
                }
            }

            if (payDetailList != null && payDetailList.Count > 0)
            {
                //停车明细收费结果统计
                payResult = ProcessDetail(payDetailList);
            }

            //计费规则周期处理（实际计费金额 必须要小于或者等于（停车时长/周期时长*周期封顶金额），如果大于就直接取（停车时长/周期时长*周期封顶金额））
            if (payResult != null)
            {
                foreach (var item in ruleRunData.dicRuleParam)
                {
                    if (item.Value?.Cycle_HoursMaxAmount != 0 && ruleRunData.dicParkMin.ContainsKey(item.Key) && ruleRunData.dicRuleCharge.ContainsKey(item.Key) && ruleRunData.dicRuleCycle.ContainsKey(item.Key))
                    {
                        decimal a = Convert.ToDecimal(ruleRunData.dicParkMin[item.Key]) / (item.Value.Cycle_Unit.Value * 60);
                        int countCycle = (int)Math.Ceiling(a);
                        decimal? maxSumMoney = countCycle * item.Value.Cycle_UnitMaxCharge;

                        if (ruleRunData.dicRuleCharge[item.Key] > maxSumMoney)
                        {
                            var cycleNoChargeMoney = ruleRunData.dicRuleCharge[item.Key] - maxSumMoney;
                            payResult.payedamount -= cycleNoChargeMoney.Value;
                            payResult.orderamount = payResult.payedamount;

                            CycleParam cp = ruleRunData.dicRuleCycle[item.Key];

                            var payItem = payResult.list.Where(x => x.areano == cp.Cycle_AreaNo).Last();
                            payItem.NextCyclePaidFees -= cycleNoChargeMoney;
                            if (payItem.NextCyclePaidFees < 0) payItem.NextCyclePaidFees = 0;
                            if (payItem.CalcResult?.Length < 1000) payItem.CalcResult += $"触发按停车时长封顶金额，计费结果：{payItem.payedamount}元，周期金额已累计{payItem.NextCyclePaidFees}元。";

                            var disPayList = payResult.list.Where(x => x.areano == cp.Cycle_AreaNo);
                            if (disPayList != null && disPayList.Count() > 0)
                            {
                                decimal totalReduced = CommonBLL.DistributeReduction(
                                                        disPayList,
                                                        cycleNoChargeMoney.Value,
                                                        t => t.payedamount ?? 0,
                                                        (t, reduce) =>
                                                        {
                                                            t.payedamount -= reduce;
                                                        },
                                                        ReductionTailStrategy.MultiItem
                                                    );
                            }
                            //payItem.payedamount -= cycleNoChargeMoney;
                            //if (payItem.payedamount < 0) payItem.payedamount = 0;
                        }
                    }
                }
            }

            //过夜费处理
            if (payResult != null && payResult.nightamount > 0)
            {
                payResult.orderamount += payResult.nightamount;
                payResult.payedamount += payResult.nightamount;
            }

            return payResult ?? new ChargeModels.PayResult() { payed = 0, payedmsg = "无需缴费" };
        }

        /// <summary>
        /// 获取周期最大金额
        /// </summary>
        /// <param name="currentChargeMoney">计费金额</param>
        /// <param name="startCalcTime">开始时间</param>
        /// <param name="endCalcTime">结束时间</param>
        /// <param name="cycleTime">周期结束时间</param>
        /// <param name="cycleUnit">周期小时数</param>
        /// <param name="cycleMaxMoney">周期封顶金额</param>
        /// <returns>返回：周期封顶后，设置下个周期累计金额时需要减去当前周期溢出的金额</returns>
        private static decimal? GetCycleTopCharge(int parkMin, decimal? currentChargeMoney, int? cycleUnit, decimal? cycleMaxMoney)
        {
            decimal? cycleNoChargeMoney = 0;//周期溢出的金额

            double a = parkMin / (cycleUnit.Value * 60);
            int countCycle = (int)Math.Ceiling(a);

            decimal? maxSumMoney = countCycle * cycleMaxMoney;
            if (currentChargeMoney > maxSumMoney)
            {
                cycleNoChargeMoney = currentChargeMoney - maxSumMoney;
                currentChargeMoney = maxSumMoney;
            }
            return cycleNoChargeMoney;
        }

        /// <summary>
        /// 使用优惠券优惠计费
        /// </summary>
        private static List<ChargeModels.CouponRecordUse> GetCouponCharge(Model.ParkOrder parkOrder, List<Model.CouponRecordIntExt> crList, DateTime? outTime,
            List<Model.OrderDetail> orderDetailList, ChargeModels.PayResult payResult, DateTime? PayOrder_PayedTime = null, List<Model.CarCardType> cctList = null,
            bool charginPile = false, Model.Car car = null, CalcBasicData basiceData = null)
        {
            List<Model.CouponRecordUse> CouponRecordUseList = new List<Model.CouponRecordUse>();
            //优惠券集合
            if (crList != null && crList.Count > 0 && orderDetailList != null && orderDetailList.Count > 0 && payResult.payed == 1 && payResult.payedamount > 0)
            {
                orderDetailList = orderDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                List<Model.CouponRecordIntExt> appointList = crList.Where(x => x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.AppointHour)).ToList();
                List<Model.CouponRecordIntExt> timeList = crList.Where(x => x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Time) || x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.HourFree)).ToList();
                List<Model.CouponRecordIntExt> discountList = crList.Where(x => x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Discount)).ToList();
                List<Model.CouponRecordIntExt> moneyList = crList.Where(x => x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Money)).ToList();

                //1.优惠免费时间
                Model.CouponRecordUse appointResult = ProcessAppointHourCoupon(parkOrder, orderDetailList, appointList, outTime, payResult, PayOrder_PayedTime, cctList, checkPayMonthMax: basiceData?.checkPayMonthMax ?? true);
                if (appointResult != null)
                {
                    CouponRecordUseList.Add(appointResult);
                }

                if (((appointResult != null && appointResult.orderDetailList != null && appointResult.orderDetailList.Count > 0 && timeList != null && timeList.Count > 0)
                    || (appointResult == null && timeList != null && timeList.Count > 0)))
                {

                    var orderList = appointResult == null ? orderDetailList : appointResult.orderDetailList;

                    List<Model.OrderDetail> couponOrderList = new List<Model.OrderDetail>();
                    orderList.ForEach(item =>
                    {
                        var order = payResult.list.Where(x => x.orderdetailno == item.OrderDetail_No).FirstOrDefault();
                        if (order != null && order.payed == 1 && order.payedamount > 0)
                        {
                            couponOrderList.Add(item);
                        }
                    });
                    //2.优惠时长
                    List<Model.CouponRecordUse> TimeCouponUseList = ProcessTimeCoupon(parkOrder, couponOrderList, timeList, outTime, payResult, PayOrder_PayedTime, cctList, charginPile, checkPayMonthMax: basiceData?.checkPayMonthMax ?? true);
                    if (TimeCouponUseList != null && TimeCouponUseList.Count > 0)
                    {
                        CouponRecordUseList.AddRange(TimeCouponUseList);
                    }
                }

                //减去优惠时间的金额
                var sumMoney = CouponRecordUseList.Sum(x => x.DiscountMoney);
                payResult.couponamount = Utils.ObjectToDecimal(sumMoney, 0);
                payResult.payedamount = payResult.orderamount - Utils.ObjectToDecimal(sumMoney, 0);
                if (payResult.payedamount < 0) payResult.payedamount = 0;

                if (payResult.payedamount > 0)
                {
                    int? couponSort = 0;
                    //同时有折扣金额优惠，则读取系统配置的顺序使用
                    if (discountList.Count > 0 && moneyList.Count > 0)
                    {
                        Model.SysConfig sysConfig = null;
                        sysConfig = basiceData?.sysconfig?.Find(x => x.SysConfig_ParkNo == parkOrder.ParkOrder_ParkNo);
                        if (sysConfig == null) sysConfig = BLL.SysConfig._GetEntityByWhere(new Model.SysConfig(), "SysConfig_Content", $"SysConfig_ParkNo='{parkOrder.ParkOrder_ParkNo}'");

                        if (sysConfig != null)
                        {
                            Model.SysConfigContent sysConfig1 = TyziTools.Json.ToObject<Model.SysConfigContent>(BLL.SysConfig.GetUrlDecode(sysConfig.SysConfig_Content));
                            if (sysConfig1 != null)
                                couponSort = sysConfig1.SysConfig_CouponSort ?? 0;
                        }
                    }

                    //3.优惠折扣   //4.优惠金额
                    OrderCouponSort(couponSort, CouponRecordUseList, payResult, discountList, moneyList);
                }
            }

            return TyziTools.Json.ToModel<List<ChargeModels.CouponRecordUse>>(TyziTools.Json.ToString(CouponRecordUseList));
        }

        private static void OrderCouponSort(int? couponSort, List<Model.CouponRecordUse> CouponRecordUseList, ChargeModels.PayResult payResult, List<Model.CouponRecordIntExt> discountList, List<Model.CouponRecordIntExt> moneyList)
        {
            if (couponSort == null || couponSort != 1)
            {
                //3.优惠折扣
                if (payResult.payedamount > 0 && discountList.Count > 0)
                {
                    List<Model.CouponRecordUse> DiscountCouponUseList = ProcessDiscountCoupon(discountList, payResult);
                    if (DiscountCouponUseList != null && DiscountCouponUseList.Count > 0)
                        CouponRecordUseList.AddRange(DiscountCouponUseList);
                }
                //4.优惠金额
                if (payResult.payedamount > 0 && moneyList.Count > 0)
                {
                    List<Model.CouponRecordUse> MoneyCouponUseList = ProcessMoneyCoupon(moneyList, payResult);
                    if (MoneyCouponUseList != null && MoneyCouponUseList.Count > 0)
                        CouponRecordUseList.AddRange(MoneyCouponUseList);
                }
            }
            else
            {
                //4.优惠金额
                if (payResult.payedamount > 0 && moneyList.Count > 0)
                {
                    List<Model.CouponRecordUse> MoneyCouponUseList = ProcessMoneyCoupon(moneyList, payResult);
                    if (MoneyCouponUseList != null && MoneyCouponUseList.Count > 0)
                        CouponRecordUseList.AddRange(MoneyCouponUseList);
                }
                //3.优惠折扣
                if (payResult.payedamount > 0 && discountList.Count > 0)
                {
                    List<Model.CouponRecordUse> DiscountCouponUseList = ProcessDiscountCoupon(discountList, payResult);
                    if (DiscountCouponUseList != null && DiscountCouponUseList.Count > 0)
                        CouponRecordUseList.AddRange(DiscountCouponUseList);
                }
            }
        }

        /// <summary>
        /// 处理优惠金额的优惠券
        /// </summary>
        /// <param name="crList"></param>
        /// <param name="payResult"></param>
        private static List<Model.CouponRecordUse> ProcessMoneyCoupon(List<Model.CouponRecordIntExt> crList, ChargeModels.PayResult payResult)
        {
            if (crList.Count == 0) { return null; }
            //优惠使用记录集合
            List<Model.CouponRecordUse> CouponRecordUseList = new List<Model.CouponRecordUse>();
            crList.ForEach(x =>
            {
                if (payResult.payedamount > 0)
                {
                    decimal Money = 0;
                    if (x.CouponRecord_Value <= payResult.payedamount)
                    {
                        Money = x.CouponRecord_Value.Value;
                    }
                    else
                    {
                        Money = payResult.payedamount;
                    }

                    decimal? useAmount = 0;
                    if (payResult != null && payResult.list != null)
                    {
                        for (var i = 0; i < payResult.list.Count; i++)
                        {
                            var useOrderAmount = Money - useAmount;
                            if (useOrderAmount > 0)
                            {
                                if (payResult.list[i].payedamount >= useOrderAmount)
                                {
                                    payResult.list[i].payedamount = payResult.list[i].payedamount - useOrderAmount;
                                    payResult.list[i].couponamount = Utils.ObjectToDecimal(payResult.list[i].couponamount, 0) + useOrderAmount;
                                    payResult.list[i].NextCyclePaidFees = Utils.ObjectToDecimal(payResult.list[i].NextCyclePaidFees, 0) - useOrderAmount;
                                    if (payResult.list[i].NextCyclePaidFees < 0) payResult.list[i].NextCyclePaidFees = 0;

                                    useAmount += useOrderAmount;
                                    break;
                                }
                                else
                                {
                                    useAmount += payResult.list[i].payedamount;
                                    payResult.list[i].couponamount = Utils.ObjectToDecimal(payResult.list[i].couponamount, 0) + payResult.list[i].payedamount;
                                    payResult.list[i].NextCyclePaidFees = Utils.ObjectToDecimal(payResult.list[i].NextCyclePaidFees, 0) - payResult.list[i].payedamount;
                                    if (payResult.list[i].NextCyclePaidFees < 0) payResult.list[i].NextCyclePaidFees = 0;
                                    payResult.list[i].payedamount = 0;
                                }
                            }
                        }
                    }

                    payResult.couponamount += Money;
                    payResult.payedamount = payResult.payedamount - Money;

                    CouponRecordUseList.Add(new Model.CouponRecordUse()
                    {
                        CouponRecord_No = x.CouponRecord_No,
                        CouponRecord_CouponCode = x.CouponRecord_CouponCode,
                        DiscountMoney = Money,
                        CouponRecord_OnLine = x.CouponRecord_OnLine
                    });
                }
            });

            return CouponRecordUseList;
        }
        /// <summary>
        /// 处理优惠折扣的优惠券
        /// </summary>
        /// <param name="crList"></param>
        /// <param name="payResult"></param>
        private static List<Model.CouponRecordUse> ProcessDiscountCoupon(List<Model.CouponRecordIntExt> crList, ChargeModels.PayResult payResult)
        {
            if (crList.Count == 0) { return null; }
            //优惠使用记录集合
            List<Model.CouponRecordUse> CouponRecordUseList = new List<Model.CouponRecordUse>();
            decimal payedamount = payResult.payedamount;
            crList.ForEach(x =>
            {
                decimal sumCouponAMount = 0;
                if (payResult != null && payResult.list != null)
                {
                    for (var i = 0; i < payResult.list.Count; i++)
                    {
                        var discountMoney = Convert.ToDecimal(ToResultString(payResult.list[i].payedamount.Value * ((x.CouponRecord_Value ?? 0) / 10), 2));
                        var couponmoney = payResult.list[i].payedamount - discountMoney;
                        sumCouponAMount += couponmoney.Value;

                        payResult.list[i].payedamount = discountMoney;
                        payResult.list[i].couponamount = Utils.ObjectToDecimal(payResult.list[i].couponamount, 0) + couponmoney;
                        payResult.list[i].NextCyclePaidFees = Utils.ObjectToDecimal(payResult.list[i].NextCyclePaidFees, 0) - couponmoney;
                        if (payResult.list[i].NextCyclePaidFees < 0) payResult.list[i].NextCyclePaidFees = 0;

                        payResult.couponamount = Utils.ObjectToDecimal(payResult.couponamount, 0) + Utils.ObjectToDecimal(couponmoney, 0);

                        payResult.payedamount = payedamount - couponmoney.Value;
                        if (payResult.payedamount < 0) payResult.payedamount = 0;
                        payedamount = payResult.payedamount;
                    }
                }

                CouponRecordUseList.Add(new Model.CouponRecordUse()
                {
                    CouponRecord_No = x.CouponRecord_No,
                    CouponRecord_CouponCode = x.CouponRecord_CouponCode,
                    DiscountMoney = sumCouponAMount,
                    CouponRecord_OnLine = x.CouponRecord_OnLine,
                });
            });

            return CouponRecordUseList;
        }
        /// <summary>
        /// 处理免费时间的优惠券
        /// </summary>
        /// <param name="parkOrder"></param>
        /// <param name="orderDetailList"></param>
        /// <param name="crList"></param>
        /// <param name="outTime"></param>
        /// <returns></returns>
        private static Model.CouponRecordUse ProcessAppointHourCoupon(Model.ParkOrder parkOrder, List<Model.OrderDetail> orderDetailList, List<Model.CouponRecordIntExt> crList, DateTime? outTime, ChargeModels.PayResult payResult, DateTime? PayOrder_PayedTime = null, List<Model.CarCardType> cctList = null,
           bool checkPayMonthMax = true)
        {
            if (crList.Count == 0) { return null; }
            crList = crList.OrderBy(cr => cr.CouponRecord_EndTime).ToList();
            //优惠券
            Model.CouponRecord couponRecord = crList.Last();
            //优惠使用记录
            Model.CouponRecordUse couponUse = null;
            //停车明细
            List<Model.OrderDetail> orderDetailList_New = TyziTools.Json.ToObject<List<Model.OrderDetail>>(TyziTools.Json.ToString(orderDetailList));

            if (couponRecord.CouponRecord_EndTime.Value.CompareTo(orderDetailList_New[0].OrderDetail_EnterTime.Value) > 0)//优惠券的免费时间必须大于停车进场时间，否则不可用
            {
                //优惠时间至第几段
                int index = GetOrderIndex(orderDetailList_New, couponRecord.CouponRecord_EndTime, 0);

                //优惠时间小于出场时间
                if (couponRecord.CouponRecord_EndTime.Value.CompareTo(orderDetailList_New[index].OrderDetail_OutTime.Value) < 0)
                {
                    var enterTime = orderDetailList_New[index].OrderDetail_EnterTime;
                    orderDetailList_New[index].OrderDetail_EnterTime = couponRecord.CouponRecord_EndTime;

                    //若有多段停车明细，则移除优惠时间之前的停车明细
                    if (index != 0)
                    {
                        for (var m = 0; m < index - 1; m++)
                        {
                            orderDetailList_New.RemoveAt(m);
                        }
                    }

                    //计算优惠前后的差额
                    var payResult_coupon = GetPriCharge(parkOrder, outTime, orderDetailList_New, null, null, null, PayOrder_PayedTime, cctList, false, true, checkPayMonthMax: checkPayMonthMax);
                    ProcessPayDetail(ref payResult, payResult_coupon);
                    var couponMoney = payResult.payedamount - payResult_coupon.payedamount;
                    //优惠记录
                    couponUse = new Model.CouponRecordUse();
                    couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                    couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                    couponUse.DiscountMoney = couponMoney;
                    couponUse.OrderDetail_Index = index;
                    couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                    couponUse.OrderDetail_Index = index;
                    couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                    couponUse.CouponRecord_DiscountMin = couponRecord.CouponRecord_EndTime.Value.Subtract(enterTime.Value).TotalMinutes;
                }
                //优惠时间等于出场时间
                else if (couponRecord.CouponRecord_EndTime.Value.CompareTo(orderDetailList_New[index].OrderDetail_OutTime.Value) == 0)
                {
                    //若有多段停车明细，则移除优惠时间之前的停车明细
                    if (index != 0)
                    {
                        for (var m = 0; m <= index; m++)
                        {
                            orderDetailList_New.RemoveAt(m);
                        }
                    }

                    if (orderDetailList_New.Count == 0)
                    {
                        //停车时间在优惠时间内，全额优惠
                        couponUse = new Model.CouponRecordUse();
                        couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                        couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                        couponUse.orderDetailList = orderDetailList_New;
                        couponUse.DiscountMoney = payResult.payedamount;
                        couponUse.OrderDetail_Index = index;
                        couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                        couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                        couponUse.CouponRecord_DiscountMin = couponRecord.CouponRecord_EndTime.Value.Subtract(orderDetailList_New[index].OrderDetail_EnterTime.Value).TotalMinutes;
                        ProcessPayDetail(ref payResult, null);
                    }
                    else
                    {
                        //计算优惠前后的差额
                        var payResult_coupon = GetPriCharge(parkOrder, outTime, orderDetailList_New, null, null, null, PayOrder_PayedTime, cctList, false, true, checkPayMonthMax: checkPayMonthMax);
                        ProcessPayDetail(ref payResult, payResult_coupon);
                        var couponMoney = payResult.payedamount - payResult_coupon.payedamount;
                        //优惠记录
                        couponUse = new Model.CouponRecordUse();
                        couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                        couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                        couponUse.orderDetailList = orderDetailList_New;
                        couponUse.DiscountMoney = couponMoney;
                        couponUse.OrderDetail_Index = index;
                        couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                        couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                        couponUse.CouponRecord_DiscountMin = couponRecord.CouponRecord_EndTime.Value.Subtract(orderDetailList_New[index].OrderDetail_EnterTime.Value).TotalMinutes;
                    }
                }
                //优惠时间大于出场时间
                else
                {
                    //全额优惠
                    //优惠记录
                    couponUse = new Model.CouponRecordUse();
                    couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                    couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                    couponUse.orderDetailList = orderDetailList_New;
                    couponUse.DiscountMoney = payResult.payedamount;
                    couponUse.OrderDetail_Index = index;
                    couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                    couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                    couponUse.CouponRecord_DiscountMin = orderDetailList_New[index].OrderDetail_OutTime.Value.Subtract(orderDetailList_New[index].OrderDetail_EnterTime.Value).TotalMinutes;
                    ProcessPayDetail(ref payResult, null);
                }
            }

            return couponUse;

        }

        private static void ProcessPayDetail(ref ChargeModels.PayResult payResult, ChargeModels.PayResult payResult_coupon)
        {
            if (payResult.list != null)
            {
                payResult.list.ForEach(detail =>
                {
                    ChargeModels.PayDetail couponDetail = new ChargeModels.PayDetail();
                    if (payResult_coupon != null && payResult_coupon.list != null)
                    {
                        couponDetail = payResult_coupon.list.FindAll(x => x.orderdetailid == detail.orderdetailid).FirstOrDefault();
                        if (couponDetail == null) couponDetail = new ChargeModels.PayDetail();

                        couponDetail.payedamount = Utils.ObjectToDecimal(couponDetail.payedamount, 0);
                        detail.couponamount = Utils.ObjectToDecimal(detail.couponamount, 0);

                        var diffMount = detail.payedamount - couponDetail.payedamount; //优惠金额
                        if (diffMount > 0)//优惠金额 和 支付金额 相等  //去掉 diffMount != detail.payedamount这个条件，优惠全免BUG
                        {
                            detail.payedamount = detail.payedamount - diffMount;
                            detail.couponamount = Utils.ObjectToDecimal(detail.couponamount, 0) + diffMount;
                            detail.NextCyclePaidFees = Utils.ObjectToDecimal(detail.NextCyclePaidFees, 0) - diffMount;
                            if (detail.NextCyclePaidFees < 0) detail.NextCyclePaidFees = 0;
                        }
                    }
                    else
                    {
                        detail.couponamount = Utils.ObjectToDecimal(detail.couponamount, 0) + detail.payedamount;

                        var diffMount = detail.payedamount;
                        detail.NextCyclePaidFees = Utils.ObjectToDecimal(detail.NextCyclePaidFees, 0) - diffMount;
                        if (detail.NextCyclePaidFees < 0) detail.NextCyclePaidFees = 0;

                        detail.payedamount = 0;
                    }
                });
            }
        }

        /// <summary>
        /// 处理免费时长的优惠券
        /// </summary>
        /// <param name="parkOrder"></param>
        /// <param name="orderDetailList"></param>
        /// <param name="crList"></param>
        /// <param name="outTime"></param>
        /// <returns></returns>
        private static List<Model.CouponRecordUse> ProcessTimeCoupon(Model.ParkOrder parkOrder, List<Model.OrderDetail> orderDetailList, List<Model.CouponRecordIntExt> crList, DateTime? outTime, ChargeModels.PayResult payResult, DateTime? PayOrder_PayedTime = null, List<Model.CarCardType> cctList = null, bool charginPile = false,
            bool checkPayMonthMax = true)
        {
            if (crList == null || crList.Count == 0) { return null; }
            crList = crList.OrderByDescending(cr => cr.CouponRecord_Value).ToList();
            //优惠使用记录集合
            List<Model.CouponRecordUse> CouponRecordUseList = new List<Model.CouponRecordUse>();

            //停车明细
            List<Model.OrderDetail> orderDetailList_New = new List<Model.OrderDetail>();
            if (orderDetailList != null) { orderDetailList_New = TyziTools.Json.ToObject<List<Model.OrderDetail>>(TyziTools.Json.ToString(orderDetailList)); orderDetailList_New = orderDetailList_New.OrderBy(x => x.OrderDetail_EnterTime).ToList(); }

            var prePayresult = payResult.Copy();
            foreach (var couponRecord in crList)
            {
                if (orderDetailList_New.Count == 0) { break; }

                if (couponRecord.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.HourFree))
                {

                    DateTime enterTime = parkOrder.ParkOrder_EnterTime.Value;
                    DateTime outTimeValue = outTime.Value;

                    TimeSpan parkingDuration = outTimeValue - enterTime;
                    int parkingMinutes = (int)Math.Ceiling(parkingDuration.TotalMinutes);

                    // 优惠券最大可抵用分钟数（decimal → int）
                    int originalCouponMinutes = (int)Math.Floor(couponRecord.CouponRecord_Value.Value);

                    // 计算实际可用分钟的初始值，先按停车时间和优惠券面值取较小者
                    int usableMinutes = Math.Min(parkingMinutes, originalCouponMinutes);

                    // 如果优惠券有指定有效时间段，且开始时间晚于入场时间，判断时间交叉
                    bool hasPresetTimeRange =
                        couponRecord.CouponRecord_StartTime.HasValue &&
                        couponRecord.CouponRecord_EndTime.HasValue;

                    if (hasPresetTimeRange && couponRecord.CouponRecord_StartTime.Value > enterTime)
                    {
                        DateTime couponStart = couponRecord.CouponRecord_StartTime.Value;
                        DateTime couponEnd = couponRecord.CouponRecord_EndTime.Value;

                        // 如果优惠券开始时间晚于出场时间，券不可用
                        if (couponStart >= outTimeValue)
                        {
                            usableMinutes = 0;
                        }
                        else
                        {
                            // 计算优惠券有效时间段与停车时间的重叠区间
                            DateTime overlapStart = couponStart > enterTime ? couponStart : enterTime;
                            DateTime overlapEnd = couponEnd < outTimeValue ? couponEnd : outTimeValue;

                            if (overlapEnd > overlapStart)
                            {
                                int overlapMinutes = (int)Math.Ceiling((overlapEnd - overlapStart).TotalMinutes);

                                // 取 重叠时间和优惠券最大可用分钟数 中的最小值
                                usableMinutes = Math.Min(overlapMinutes, originalCouponMinutes);
                            }
                            else
                            {
                                // 没有交集，券不可用
                                usableMinutes = 0;
                            }
                        }
                    }

                    // 防止usableMinutes超过停车时长（保险措施）
                    usableMinutes = Math.Min(usableMinutes, parkingMinutes);

                    // 保存实际可使用分钟
                    couponRecord.CouponRecord_Value = usableMinutes;
                }

                //优惠使用记录
                Model.CouponRecordUse couponUse = null;
                double outSeconds = 0;
                var couponTotalSeconds = (double)couponRecord.CouponRecord_Value.Value * 60;


                //优惠时间至第几段
                int index = GetOrderIndexByTime(orderDetailList_New, couponTotalSeconds, 0, 0, out outSeconds);

                //优惠时长小于出场时间
                if (couponTotalSeconds < outSeconds)
                {
                    orderDetailList_New[index].OrderDetail_EnterTime = orderDetailList_New[index].OrderDetail_OutTime.Value.AddSeconds(couponTotalSeconds - outSeconds);
                    //parkOrder.ParkOrder_EnterTime = parkOrder.ParkOrder_EnterTime.Value.AddSeconds(couponTotalSeconds);
                    //若有多段停车明细，则移除优惠时间之前的停车明细
                    if (index != 0)
                    {
                        for (var m = 0; m < index; m++)
                        {
                            orderDetailList_New.RemoveAt(m);
                        }
                    }

                    //计算优惠前后的差额
                    var payResult_coupon = GetPriCharge(parkOrder, outTime, orderDetailList_New, null, null, null, PayOrder_PayedTime, cctList, charginPile, true, checkPayMonthMax: checkPayMonthMax);
                    if (payResult_coupon.payedamount < prePayresult.payedamount)
                    {
                        ProcessPayDetail(ref payResult, payResult_coupon);
                        var couponMoney = prePayresult.payedamount - payResult_coupon.payedamount;
                        //优惠记录
                        couponUse = new Model.CouponRecordUse();
                        couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                        couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                        couponUse.orderDetailList = orderDetailList_New;
                        couponUse.DiscountMoney = couponMoney;
                        couponUse.CouponRecord_DiscountMin = couponTotalSeconds / 60;
                        couponUse.CouponRecord_Value = 0;
                        couponUse.OrderDetail_Index = index;
                        couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                        couponUse.CouponRecord_Other = couponRecord.CouponRecord_Other;
                        couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                        prePayresult = payResult_coupon;
                    }
                    else
                    {
                        //优惠记录
                        couponUse = new Model.CouponRecordUse();
                        couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                        couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                        couponUse.orderDetailList = orderDetailList_New;
                        couponUse.DiscountMoney = 0;
                        couponUse.CouponRecord_DiscountMin = couponTotalSeconds / 60;
                        couponUse.CouponRecord_Value = 0;
                        couponUse.OrderDetail_Index = index;
                        couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                        couponUse.CouponRecord_Other = couponRecord.CouponRecord_Other;
                        couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                    }

                }
                //优惠时长等于出场时间
                else if (couponTotalSeconds == outSeconds)
                {
                    //若有多段停车明细，则移除优惠时间之前的停车明细
                    if (index != 0)
                    {
                        for (var m = 0; m <= index; m++)
                        {
                            orderDetailList_New.RemoveAt(m);
                        }
                    }
                    else
                    {
                        orderDetailList_New.RemoveAt(0);
                    }

                    if (orderDetailList_New.Count == 0)
                    {
                        //停车时间在优惠时间内，全额优惠
                        couponUse = new Model.CouponRecordUse();
                        couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                        couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                        couponUse.orderDetailList = orderDetailList_New;
                        couponUse.DiscountMoney = payResult.payedamount;
                        couponUse.OrderDetail_Index = index;
                        couponUse.CouponRecord_Value = 0;
                        couponUse.CouponRecord_DiscountMin = couponTotalSeconds / 60;
                        couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                        couponUse.CouponRecord_Other = couponRecord.CouponRecord_Other;
                        couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                        ProcessPayDetail(ref payResult, null);
                    }
                    else
                    {
                        //计算优惠前后的差额
                        var payResult_coupon = GetPriCharge(parkOrder, outTime, orderDetailList_New, null, null, null, PayOrder_PayedTime, cctList, false, true, checkPayMonthMax: checkPayMonthMax);
                        ProcessPayDetail(ref payResult, payResult_coupon);
                        var couponMoney = payResult.payedamount - payResult_coupon.payedamount;
                        //优惠记录
                        couponUse = new Model.CouponRecordUse();
                        couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                        couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                        couponUse.orderDetailList = orderDetailList_New;
                        couponUse.DiscountMoney = couponMoney;
                        couponUse.OrderDetail_Index = index;
                        couponUse.CouponRecord_Value = 0;
                        couponUse.CouponRecord_DiscountMin = couponTotalSeconds / 60;
                        couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                        couponUse.CouponRecord_Other = couponRecord.CouponRecord_Other;
                        couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;
                    }

                    CouponRecordUseList.Add(couponUse);
                    return CouponRecordUseList;
                }
                //优惠时长大于出场时间
                else
                {
                    //全额优惠
                    //优惠记录
                    couponUse = new Model.CouponRecordUse();
                    couponUse.CouponRecord_No = couponRecord.CouponRecord_No;
                    couponUse.OrderDetail_ID = orderDetailList[index].OrderDetail_ID;
                    couponUse.orderDetailList = orderDetailList_New;
                    couponUse.DiscountMoney = payResult.payedamount - CouponRecordUseList.Sum(x => x.DiscountMoney);
                    if (couponUse.DiscountMoney < 0) couponUse.DiscountMoney = 0;
                    couponUse.OrderDetail_Index = index;
                    couponUse.CouponRecord_Value = couponTotalSeconds - outSeconds;
                    couponUse.CouponRecord_DiscountMin = outSeconds / 60;
                    couponUse.CouponRecord_CouponCode = couponRecord.CouponRecord_CouponCode;
                    couponUse.CouponRecord_Other = couponRecord.CouponRecord_Other;
                    couponUse.CouponRecord_OnLine = couponRecord.CouponRecord_OnLine;

                    ProcessPayDetail(ref payResult, null);
                    CouponRecordUseList.Add(couponUse);
                    return CouponRecordUseList;
                }

                CouponRecordUseList.Add(couponUse);
            }

            return CouponRecordUseList;

        }
        /// <summary>
        /// 比较停车明细的出场时间与优惠券的免费时间
        /// </summary>
        /// <param name="orderDetailList_New"></param>
        /// <param name="CouponRecord_EndTime"></param>
        /// <param name="index"></param>
        /// <returns>优惠免费时间至哪个停车明细段</returns>
        private static int GetOrderIndex(List<Model.OrderDetail> orderDetailList_New, DateTime? CouponRecord_EndTime, int index)
        {
            var x = orderDetailList_New[index];

            if (CouponRecord_EndTime.Value.CompareTo(x.OrderDetail_OutTime.Value) <= 0)
            {
                return index;
            }
            else
            {
                var index_New = index + 1;
                if (orderDetailList_New.Count > index_New)
                {
                    index = GetOrderIndex(orderDetailList_New, CouponRecord_EndTime, index_New);
                }
                else
                {
                    return index;
                }
            }

            return index;
        }
        /// <summary>
        /// 比较停车明细的出场时间与优惠券的优惠时长
        /// </summary>
        /// <param name="orderDetailList_New"></param>
        /// <param name="CouponRecord_EndTime"></param>
        /// <param name="index"></param>
        /// <returns>优惠免费时间至哪个停车明细段</returns>
        private static int GetOrderIndexByTime(List<Model.OrderDetail> orderDetailList_New, double CouponRecord_Value, int index, double totalSeconds, out double outSeconds)
        {
            var x = orderDetailList_New[index];

            totalSeconds += (x.OrderDetail_OutTime - x.OrderDetail_EnterTime).Value.TotalSeconds;
            outSeconds = totalSeconds;

            if (CouponRecord_Value <= totalSeconds)
            {
                return index;
            }
            else
            {
                var index_New = index + 1;
                if (orderDetailList_New.Count > index_New)
                {
                    index = GetOrderIndexByTime(orderDetailList_New, CouponRecord_Value, index_New, totalSeconds, out outSeconds);
                }
                else
                {
                    return index;
                }
            }

            return index;
        }
        #endregion

        #region 通过策略等获取计费规则、停车订单超时逻辑处理
        private static List<ChargeModels.PayDetail> GetChargeByParkOrder(RuleRunData ruleRunData, Model.ParkOrder parkOrder, List<Model.OrderDetail> orderDetailList, string Car_No, string CarTypeNo, string CarCardTypeNo, DateTime? outTime, Model.Car car = null, DateTime? PayOrder_PayedTime = null, Model.Owner owner = null, List<Model.CarCardType> cctList = null, bool charginPile = false
            , bool havaMinCoupon = false, bool checkPayMonthMax = true)
        {
            if (cctList == null) cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_Type,CarCardType_IsMoreCar", "") ?? new List<Model.CarCardType>();

            List<ChargeModels.PayDetail> payDetailList = new List<ChargeModels.PayDetail>();
            orderDetailList = orderDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
            var orderCount = orderDetailList.Count;

            var parkorderType = cctList.Find(x => x.CarCardType_No == parkOrder.ParkOrder_CarCardType);

            //超时计费：true--属于超时收费，false--不属于超时
            var isOverTime = false;
            //Model.PayOrder payDeatail = null;//上一次的支付订单

            var settleList = orderDetailList.FindAll(x => x.OrderDetail_IsSettle == 1 && x.OrderDetail_StatusNo != Model.EnumParkOrderStatus.InClose).OrderBy(x => x.OrderDetail_EnterTime).ToList();
            if (settleList.Count > 0)
            {
                isOverTime = true;
                //if (PayOrder_PayedTime == null) PayOrder_PayedTime = orderDetailList.FindAll(x => x.OrderDetail_IsSettle == 1 && x.OrderDetail_StatusNo != Model.EnumParkOrderStatus.InClose).OrderBy(x => x.OrderDetail_EnterTime).ToList().LastOrDefault()?.OrderDetail_OutTime;
            }
            //if (isOverTime)//属于超时收费，则需要读取关联的上一次支付的订单历史，获取支付时间
            //{
            //    payDeatail = BLL.PayOrder._GetEntityByWhere(new Model.PayOrder(), "PayOrder_ID,PayOrder_Time", $"PayOrder_ParkOrderNo='{settleList[0].OrderDetail_ParkOrderNo}' order by PayOrder_Time desc limit 1");
            //    //if (payDeatail == null) isOverTime = false;
            //}

            //非超时计费，过滤已结算的明细订单
            if (!isOverTime)
                orderDetailList = orderDetailList.Where(x => x.OrderDetail_IsSettle == 0).ToList();

            var areaNoGroup = orderDetailList.Select(x => x.OrderDetail_ParkAreaNo).Distinct().ToList();


            BillingBlackBox.Models.SectionParam param = new BillingBlackBox.Models.SectionParam();


            //超时计费,不累加已使用的免费分钟
            if (!isOverTime)
            {
                orderDetailList.ForEach(item =>
                {
                    param.writeParam.SetMin(ref param, item.OrderDetail_ParkAreaNo, item.OrderDetail_No, Utils.ObjectToInt(item.Orderdetail_UseFreeMin, 0));
                });
            }

            param.readParam.ParkOrder_TotalPayed = parkOrder.ParkOrder_TotalPayed ?? 0; //parkOrder.ParkOrder_TotalPayed ?? 0;
            param.readParam.ParkOrder_StartTime = parkOrder.ParkOrder_EnterTime;
            param.readParam.ParkOrder_EndTime = outTime;

            var nowTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd");
            //foreach (var areano in areaNoGroup)
            //{
            //获取该区域的停车记录详情订单
            //List<Model.OrderDetail> areaOrderDetailList = orderDetailList.Where(x => x.OrderDetail_ParkAreaNo == areano).ToList();


            #region 【每月最高限额】处理跨月拆分逻辑
            var policyCarCard = BLL.PolicyCarCard.GetEntityByCarCard(parkOrder.ParkOrder_CarCardType);
            bool enbleMonthMoney = checkPayMonthMax ? (policyCarCard != null && policyCarCard.PolicyCarCard_MonthMaxMoney > 0) : false;

            if (enbleMonthMoney || AppBasicCache.GetPolicyPark?.PolicyPark_MergeyHours == 1)
            {
                List<Model.OrderDetail> newDetailList = new List<Model.OrderDetail>();

                if (AppBasicCache.GetPolicyPark?.PolicyPark_MergeyHours == 1)
                {
                    if (orderDetailList?.Count > 0)
                    {
                        var dicAreaDetails = new Dictionary<string, List<Model.OrderDetail>>();

                        foreach (var currDetail in orderDetailList)
                        {
                            if (currDetail.OrderDetail_EnterTime == null || currDetail.OrderDetail_OutTime == null)
                                continue;

                            // 超时计费逻辑判断
                            if (isOverTime && currDetail.OrderDetail_IsSettle == 1)
                            {
                                if (PayOrder_PayedTime != null)
                                {
                                    if (currDetail.OrderDetail_OutTime > PayOrder_PayedTime &&
                                        currDetail.OrderDetail_OutTime >= currDetail.OrderDetail_EnterTime)
                                    {
                                        if (PayOrder_PayedTime > currDetail.OrderDetail_EnterTime)
                                            currDetail.OrderDetail_EnterTime = PayOrder_PayedTime;
                                    }
                                    else
                                    {
                                        continue; // 不参与计费
                                    }
                                }
                            }

                            var areaNo = currDetail.OrderDetail_ParkAreaNo ?? "UNKNOWN";
                            areaNo = areaNo + currDetail.OrderDetail_CarCardType ?? "";
                            if (!dicAreaDetails.ContainsKey(areaNo))
                                dicAreaDetails[areaNo] = new List<Model.OrderDetail>();

                            // 判断是否启用每月最高限额
                            if (checkPayMonthMax && enbleMonthMoney)
                            {
                                var enter = currDetail.OrderDetail_EnterTime.Value;
                                var exit = currDetail.OrderDetail_OutTime.Value;

                                if (enter.Year == exit.Year && enter.Month == exit.Month)
                                {
                                    dicAreaDetails[areaNo].Add(currDetail);
                                }
                                else
                                {
                                    while (enter.Year != exit.Year || enter.Month != exit.Month)
                                    {
                                        var endOfMonth = new DateTime(enter.Year, enter.Month,
                                            DateTime.DaysInMonth(enter.Year, enter.Month), 23, 59, 59);

                                        var newDetail = currDetail.Copy();
                                        newDetail.OrderDetail_EnterTime = enter;
                                        newDetail.OrderDetail_OutTime = endOfMonth;
                                        dicAreaDetails[areaNo].Add(newDetail);

                                        enter = endOfMonth.AddSeconds(1); // 下月起点
                                    }

                                    var finalDetail = currDetail.Copy();
                                    finalDetail.OrderDetail_EnterTime = enter;
                                    finalDetail.OrderDetail_OutTime = exit;
                                    dicAreaDetails[areaNo].Add(finalDetail);
                                }
                            }
                            else
                            {
                                dicAreaDetails[areaNo].Add(currDetail);
                            }
                        }

                        //区域内的订单合并
                        foreach (var kvp in dicAreaDetails)
                        {
                            var details = kvp.Value;

                            if (enbleMonthMoney)
                            {
                                // 按月分组
                                var grouped = details.GroupBy(d => new { d.OrderDetail_EnterTime.Value.Year, d.OrderDetail_EnterTime.Value.Month });

                                foreach (var group in grouped)
                                {
                                    var merged = group.OrderBy(d => d.OrderDetail_EnterTime).ToList();
                                    var first = merged.First().Copy();
                                    first.OrderDetail_EnterTime = merged.Min(d => d.OrderDetail_EnterTime);
                                    // 计算总时长（单位：秒）
                                    var totalSeconds = merged.Sum(d =>
                                        (d.OrderDetail_OutTime.Value - d.OrderDetail_EnterTime.Value).TotalSeconds
                                    );
                                    // 根据总时长生成新的出场时间
                                    first.OrderDetail_OutTime = first.OrderDetail_EnterTime.Value.AddSeconds(totalSeconds);
                                    newDetailList.Add(first);
                                }
                            }
                            else
                            {
                                // 不区分月份，整合同区域所有明细
                                var merged = details.OrderBy(d => d.OrderDetail_EnterTime).ToList();
                                var first = merged.First().Copy();
                                first.OrderDetail_EnterTime = merged.Min(d => d.OrderDetail_EnterTime);
                                // 计算总时长（单位：秒）
                                var totalSeconds = merged.Sum(d =>
                                    (d.OrderDetail_OutTime.Value - d.OrderDetail_EnterTime.Value).TotalSeconds
                                );
                                // 根据总时长生成新的出场时间
                                first.OrderDetail_OutTime = first.OrderDetail_EnterTime.Value.AddSeconds(totalSeconds);
                                newDetailList.Add(first);
                            }
                        }

                        orderDetailList = newDetailList;
                    }
                }
                else
                {
                    if (checkPayMonthMax && orderDetailList?.Count > 0 && enbleMonthMoney)
                    {

                        for (var i = 0; i < orderDetailList.Count; i++)
                        {
                            var detail_enterTime = orderDetailList[i].OrderDetail_EnterTime.Value;
                            var detail_outTime = orderDetailList[i].OrderDetail_OutTime.Value;

                            if (detail_enterTime.Year == detail_outTime.Year && detail_enterTime.Month == detail_outTime.Month)
                            {
                                newDetailList.Add(orderDetailList[i]);
                                continue;
                            }

                            // 跨月处理
                            while (detail_enterTime.Year != detail_outTime.Year || detail_enterTime.Month != detail_outTime.Month)
                            {
                                var endOfMonth = new DateTime(detail_enterTime.Year, detail_enterTime.Month,
                                                              DateTime.DaysInMonth(detail_enterTime.Year, detail_enterTime.Month),
                                                              23, 59, 59);

                                var newDetail = orderDetailList[i].Copy();
                                newDetail.OrderDetail_EnterTime = detail_enterTime;
                                newDetail.OrderDetail_OutTime = endOfMonth;
                                newDetailList.Add(newDetail);

                                detail_enterTime = endOfMonth.AddSeconds(1); // 防止时间重叠
                            }

                            var newDetailFinal = orderDetailList[i].Copy();
                            newDetailFinal.OrderDetail_EnterTime = detail_enterTime;
                            newDetailFinal.OrderDetail_OutTime = detail_outTime;
                            newDetailList.Add(newDetailFinal);
                        }

                        newDetailList = newDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                        orderDetailList = newDetailList;
                    }
                }
            }
            #endregion

            if (orderDetailList != null && orderDetailList.Count > 0)
            {
                //是否使用首计时（True-已使用首计时，False-未使用首计时）
                bool isUseFirstTime = false;
                //已计算的明细编号
                List<string> calcDetails = null;

                //循环结算停车订单
                for (var i = 0; i < orderDetailList.Count; i++)
                {
                    var currDetail = orderDetailList[i];

                    if (calcDetails != null && calcDetails.Find(x => x == currDetail.OrderDetail_No) != null) continue;

                    DateTime? nextCycleTime = null; //下一次停车，周期生效时间
                    decimal? NextCyclePaidFees = null;//下一次停车，周期已累积支付总金额
                    DateTime? NextHoursTime = null;
                    string NextHoursContent = null;
                    int? NextCycleFreeMin = null;
                    CycleParam orderDetailCycle = null;

                    param.OrderDetail_No = currDetail.OrderDetail_No;
                    param.Area_No = currDetail.OrderDetail_ParkAreaNo;
                    param.Area_Name = currDetail.OrderDetail_ParkAreaName;
                    param.readParam.IsCarExpire = currDetail.OrderDetail_Lock == 0 ? false : true;
                    param.readParam.IsOverTime = isOverTime;

                    var detailOutTime = outTime;
                    if (currDetail.OrderDetail_StatusNo != EnumParkOrderStatus.In || i != orderDetailList.Count - 1)
                    {
                        detailOutTime = currDetail.OrderDetail_OutTime == null ? outTime : currDetail.OrderDetail_OutTime;
                    }
                    else if (currDetail.OrderDetail_OutTime != null && currDetail.OrderDetail_OutTime > currDetail.OrderDetail_EnterTime && currDetail.OrderDetail_OutTime <= outTime)
                    {
                        detailOutTime = currDetail.OrderDetail_OutTime;
                    }
                    currDetail.OrderDetail_OutTime = detailOutTime;

                    var carcardType = cctList.Find(x => x.CarCardType_No == currDetail.OrderDetail_CarCardType);
                    if (carcardType != null && carcardType.CarCardType_IsMoreCar == 1 && carcardType.CarCardType_Type != 2 && parkorderType?.CarCardType_Type != 2)
                    {
                        string carSpaceMsg = "";
                        if (car != null)
                        {
                            if (parkOrder.ParkOrder_IsLift == 0 || parkOrder.ParkOrder_IsLift == 2)
                            {
                                carSpaceMsg = "多位多车，";
                            }
                            else
                            {
                                carSpaceMsg = "";
                            }
                        }

                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"{carSpaceMsg}{(currDetail.orderdetail_IsCharge == 2 ? "智能升降，" : currDetail.orderdetail_IsCharge == 1 ? "免费，" : "")}");
                    }

                    //超时计费,修改明细订单的进场时间，计算超时的部分费用
                    if (isOverTime && currDetail.OrderDetail_IsSettle == 1)
                    {
                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"超时计费（{PayOrder_PayedTime?.ToString("yyyy-MM-dd HH:mm:ss")}已支付一次停车费用），");
                        if (PayOrder_PayedTime != null)
                        {
                            if (currDetail.OrderDetail_OutTime > PayOrder_PayedTime && currDetail.OrderDetail_OutTime >= currDetail.OrderDetail_EnterTime)
                            {
                                if (PayOrder_PayedTime > currDetail.OrderDetail_EnterTime) currDetail.OrderDetail_EnterTime = PayOrder_PayedTime;
                            }
                            else
                            {
                                continue;//不符合条件的停车明细，直接跳过，不参与计费
                            }
                        }
                    }

                    //查找计费规则
                    Model.ChargeRules crModel = GetRules(currDetail, isOverTime, nowTime);
                    if (crModel == null)
                    {
                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"未设计费规则，不收取费用。");
                        //不需要支付
                        ChargeModels.PayDetail ret = CreatePayDetail(0, "未设计费规则，不收取费用", 0, currDetail.OrderDetail_ParkAreaNo, currDetail.OrderDetail_ParkOrderNo, currDetail.OrderDetail_No, currDetail.OrderDetail_ID,
                            nextCycleTime, NextCyclePaidFees, detailOutTime, currDetail.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, currDetail.OrderDetail_ParkAreaName);
                        payDetailList.Add(ret);
                        continue;
                    }

                    TimeSpan ts = currDetail.OrderDetail_EnterTime.Value.Subtract(currDetail.OrderDetail_OutTime.Value);
                    int sumMin = (int)Math.Abs(Math.Floor(ts.TotalMinutes));
                    if (ruleRunData.dicParkMin.ContainsKey(crModel.ChargeRules_No))
                    {
                        ruleRunData.dicParkMin[crModel.ChargeRules_No] += sumMin;
                    }
                    else
                    {
                        ruleRunData.dicParkMin.Add(crModel.ChargeRules_No, sumMin);
                    }

                    string calcJson = System.Web.HttpUtility.UrlDecode(crModel.ChargeRules_JsonData);
                    if (string.IsNullOrWhiteSpace(calcJson))
                    {
                        //不需要支付
                        ChargeModels.PayDetail ret = CreatePayDetail(2, "计费参数错误", 0, currDetail.OrderDetail_ParkAreaNo, currDetail.OrderDetail_ParkOrderNo, currDetail.OrderDetail_No, currDetail.OrderDetail_ID,
                            nextCycleTime, NextCyclePaidFees, detailOutTime, currDetail.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, currDetail.OrderDetail_ParkAreaName);
                        payDetailList.Add(ret);
                        break;
                    }

                    if (currDetail.orderdetail_IsCharge == 1)//一位多车免费
                    {
                        //不需要支付
                        ChargeModels.PayDetail ret = CreatePayDetail(0, "多车位多车免费", 0, currDetail.OrderDetail_ParkAreaNo, currDetail.OrderDetail_ParkOrderNo, currDetail.OrderDetail_No, currDetail.OrderDetail_ID,
                             nextCycleTime, NextCyclePaidFees, detailOutTime, currDetail.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, currDetail.OrderDetail_ParkAreaName);
                        payDetailList.Add(ret);
                        continue;
                    }

                    PayDetail orderPayMoney = null;
                    if (crModel.ChargeRules_No.Contains("YC"))
                    {
                        ChargeModels.PayDetail payDetail = CreatePayDetail(2, "计费失败", 0, null, null, null, null, DateTimeHelper.GetNowTime(), 0, outTime, currDetail.OrderDetail_EnterTime, null, null, null);
                        var result = CalcYinChuanLogic.AIDivideCalc(currDetail.OrderDetail_EnterTime.Value, detailOutTime.Value, calcJson);
                        if (!string.IsNullOrEmpty(result))
                        {
                            BillingBlackBox.Models.ChargeResult chargeResult = TyziTools.Json.ToModel<BillingBlackBox.Models.ChargeResult>(result);
                            if (chargeResult != null)
                            {
                                if (chargeResult.Code == 0)//计费失败
                                {
                                    payDetail.payedmsg = chargeResult.Msg;
                                }
                                else if (chargeResult.Code == 2)//无需缴费
                                {
                                    payDetail.payed = 0;
                                    payDetail.starttime = chargeResult.StartTime;
                                    payDetail.endtime = chargeResult.EndTime;
                                    payDetail.payedmsg = chargeResult.Msg;
                                    payDetail.nextcycletime = chargeResult.NextCycleTime;
                                    payDetail.NextCyclePaidFees = chargeResult.NextCyclePaidFees;
                                    payDetail.nextcyclefreemin = chargeResult.NextCycleFreeMin;
                                    payDetail.nexthourscontent = chargeResult.HoursMoenyList == null ? null : TyziTools.Json.ToString(chargeResult.HoursMoenyList);
                                    payDetail.currentfreemin = chargeResult.CurrentFreeMin;
                                }
                                else//计费成功
                                {
                                    payDetail.payed = 1;
                                    payDetail.starttime = chargeResult.StartTime;
                                    payDetail.endtime = chargeResult.EndTime;
                                    payDetail.payedmsg = chargeResult.Msg;
                                    payDetail.nextcycletime = chargeResult.NextCycleTime;
                                    payDetail.NextCyclePaidFees = Utils.ObjectToDecimal(chargeResult.NextCyclePaidFees, 0);
                                    payDetail.calcbegintime = chargeResult.StartTime;
                                    payDetail.payedamount = Utils.ObjectToDecimal(chargeResult.FeePayable, 0);
                                    payDetail.nextcyclefreemin = chargeResult.NextCycleFreeMin;
                                    payDetail.nexthourscontent = chargeResult.HoursMoenyList == null ? null : TyziTools.Json.ToString(chargeResult.HoursMoenyList);
                                    payDetail.currentfreemin = chargeResult.CurrentFreeMin;
                                }
                            }
                        }
                        orderPayMoney = payDetail;
                    }
                    else
                    {
                        carparking.BillingBlackBox.Models.ChargeRulesLogic crlModel = JsonConvert.DeserializeObject<carparking.BillingBlackBox.Models.ChargeRulesLogic>(calcJson);

                        //根据时段设置的开始结束时间,对停车的时间进行拆分重组 
                        if (crlModel.Logic_IsDiffTime == 1 && crlModel.Logic_IsDiffHoliday != 1 && crlModel.Logic_MergeSectionTime == 1
                            && crlModel.Logic_Sections != null && crlModel.Logic_Sections.Count > 0)
                        {
                            var sameAreaDetailList = orderDetailList.Where(x => x.OrderDetail_ParkAreaNo == currDetail.OrderDetail_ParkAreaNo && x.OrderDetail_EnterTime >= currDetail.OrderDetail_EnterTime).OrderBy(x => x.OrderDetail_EnterTime).ToList();
                            if (sameAreaDetailList.Count > 1)
                            {
                                if (calcDetails == null) calcDetails = new List<string>();
                                int detailIndex = 0;
                                foreach (var detailItem in sameAreaDetailList)
                                {
                                    List<BillingBlackBox.Models.ChargeRulesSection> currLogicSection = crlModel.Logic_Sections;

                                    #region 区域获取计费规则时段
                                    DateTime? startTime = detailItem.OrderDetail_OutTime.Value.AddMinutes(-1);//时段开始时间
                                    DateTime? endTime = detailItem.OrderDetail_OutTime;//时段结束时间

                                    DateTime? sectionStartTime = null;//最后一个时段的开始时间（增加日期，时分未做修改）
                                    DateTime? sectionEndTime = null;//最后一个时段的结束时间

                                    DateTime? ruleSectionStartTime = null;//计费规则时段开始时间（未做时间修改）
                                    DateTime? ruleSectionEndTime = null;//计费规则时段结束时间
                                    BillingBlackBox.CalcLogic.CheckHoursTime(currLogicSection, ref startTime, ref endTime, ref sectionStartTime, ref sectionEndTime, ref ruleSectionStartTime, ref ruleSectionEndTime);
                                    #endregion

                                    if (sectionEndTime != endTime && detailIndex < (sameAreaDetailList.Count - 1))//停车结束时间点 和 计费规则时间段不一致，说明需要合并跨区域的停车时长
                                    {
                                        #region 区域获取计费规则时段
                                        DateTime? startTime2 = sameAreaDetailList[detailIndex + 1].OrderDetail_EnterTime;//时段开始时间
                                        DateTime? endTime2 = sameAreaDetailList[detailIndex + 1].OrderDetail_OutTime;//时段结束时间

                                        DateTime? sectionStartTime2 = null;//第一个时段的开始时间（增加日期，时分未做修改）
                                        DateTime? sectionEndTime2 = null;//第一个时段的结束时间

                                        DateTime? ruleSectionStartTime2 = null;//计费规则时段开始时间（未做时间修改）
                                        DateTime? ruleSectionEndTime2 = null;//计费规则时段结束时间

                                        BillingBlackBox.CalcLogic.CheckHoursTime(currLogicSection, ref startTime2, ref endTime2, ref sectionStartTime2, ref sectionEndTime2, ref ruleSectionStartTime2, ref ruleSectionEndTime2, false);
                                        #endregion

                                        //判断停车时间点不是从规则时段开始时间开始的 并且 属于同一个规则时段，说明和跨区域的上一停车时间段存在可合并计费的时间
                                        if (ruleSectionStartTime2 != null && sectionStartTime2 != null && ruleSectionStartTime != null && ruleSectionStartTime2 != null &&
                                            sectionStartTime2.Value.ToString("HH:mm") != ruleSectionStartTime2.Value.ToString("HH:mm")
                                            && ruleSectionStartTime.Value.ToString("HH:mm") == ruleSectionStartTime2.Value.ToString("HH:mm"))
                                        {
                                            var nextEnterTime = sameAreaDetailList[detailIndex + 1].OrderDetail_EnterTime.Value;
                                            var margeTime = sectionEndTime2;
                                            if (margeTime > sameAreaDetailList[detailIndex + 1].OrderDetail_OutTime) margeTime = sameAreaDetailList[detailIndex + 1].OrderDetail_OutTime.Value;
                                            sameAreaDetailList[detailIndex + 1].OrderDetail_EnterTime = margeTime;
                                            var mins = (int)Math.Floor((margeTime - nextEnterTime).Value.TotalMinutes);
                                            if (mins > 0)
                                            {
                                                //LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"合并{mins}分钟");
                                                BillingBlackBox.CalcLogic.setCalcContent(ref param, $"跨区域合并同一时段{mins}分钟停车时长【{currDetail.OrderDetail_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}延长至{currDetail.OrderDetail_OutTime.Value.AddMinutes(mins).ToString("yyyy-MM-dd HH:mm:ss")}】。");
                                                detailOutTime = currDetail.OrderDetail_OutTime = currDetail.OrderDetail_OutTime.Value.AddMinutes(mins);

                                                if (sameAreaDetailList[detailIndex + 1].OrderDetail_EnterTime >= sameAreaDetailList[detailIndex + 1].OrderDetail_OutTime)
                                                    calcDetails.Add(sameAreaDetailList[detailIndex + 1].OrderDetail_No);
                                            }
                                        }
                                    }

                                    detailIndex++;
                                }
                            }
                        }

                        //周期参数赋值
                        if (crlModel.Cycle_IsCharge == 1)
                        {
                            if (ruleRunData.dicRuleCycle.ContainsKey(crModel.ChargeRules_No) && crlModel.Cycle_ReadStart != 1)
                            {
                                orderDetailCycle = ruleRunData.dicRuleCycle[crModel.ChargeRules_No];
                                nextCycleTime = orderDetailCycle?.Cycle_EndTime;
                                NextCyclePaidFees = orderDetailCycle?.Cycle_SumMoney;
                                NextCycleFreeMin = orderDetailCycle?.Cycle_SumFreeMin;
                            }

                            if (!ruleRunData.dicRuleParam.ContainsKey(crModel.ChargeRules_No))
                            {
                                ruleRunData.dicRuleParam.Add(crModel.ChargeRules_No, new BillingBlackBox.Models.ChargeRulesCycle()
                                {
                                    Cycle_Unit = crlModel.Cycle_Unit,
                                    Cycle_UnitMaxCharge = crlModel.Cycle_UnitMaxCharge,
                                    Cycle_HoursMaxAmount = crlModel.Cycle_HoursMaxAmount
                                });
                            }

                            //BillingBlackBox.CalcLogic.setCalcContent(ref param, $"获取周期时间：{nextCycleTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}，");
                        }

                        NextHoursContent = param.writeParam.Hours_StrSumTimeMoney;

                        if (crlModel.Cycle_IsCharge == 1 && !ruleRunData.dicRuleCycle.ContainsKey(crModel.ChargeRules_No) && crlModel.Cycle_ReadStart != 1)
                        {
                            var areaList = TyziTools.Json.ToObject<List<string>>(crModel.ChargeRules_ParkAreaNo);

                            int cycleHours = 2;
                            if (crlModel.Cycle_StartTime == "1")
                            {
                                cycleHours += crlModel.Cycle_Unit.Value;
                            }
                            else
                            {
                                cycleHours += 24;
                            }

                            //获取上一次的停车区域停车明细订单
                            // 1. 先用精确匹配条件过滤
                            var baseQuery = $@"
                                            OrderDetail_ParkNo = '{currDetail.OrderDetail_ParkNo}' AND
                                            OrderDetail_CarNo = '{Car_No}' AND 
                                            OrderDetail_IsSettle = 1 AND 
                                            OrderDetail_NextCycleTime is not null";
                            // 2. 再加范围条件
                            baseQuery += $@" AND orderdetail_EnterTime < '{currDetail.OrderDetail_EnterTime.Value:yyyy-MM-dd HH:mm:ss}'";
                            if (!isOverTime)
                            {
                                baseQuery += $@" AND orderdetail_OutTime >= '{currDetail.OrderDetail_EnterTime.Value.AddHours(-cycleHours):yyyy-MM-dd 00:00:00}'";
                            }

                            // 3. IN条件放在where后面
                            baseQuery += $" AND orderdetail_ParkAreaNo IN ('{string.Join("','", areaList)}')";

                            Model.OrderDetail orderDetail = BLL.OrderDetail._GetEntityByWhere(
                                new Model.OrderDetail(),
                                "OrderDetail_ID,Orderdetail_CycleMoney,OrderDetail_NextCycleTime,orderdetail_CycleFreeMin",
                                baseQuery + " order by orderdetail_ID DESC LIMIT 1"
                            );

                            orderDetailCycle = new CycleParam();
                            orderDetailCycle.Cycle_AreaNo = currDetail.OrderDetail_ParkAreaNo;
                            orderDetailCycle.Cycle_EndTime = orderDetail?.OrderDetail_NextCycleTime;
                            orderDetailCycle.Cycle_SumMoney = orderDetail?.Orderdetail_CycleMoney;
                            orderDetailCycle.Cycle_SumFreeMin = orderDetail?.Orderdetail_CycleFreeMin;
                            ruleRunData.dicRuleCycle.Add(crModel.ChargeRules_No, orderDetailCycle);

                            nextCycleTime = orderDetailCycle?.Cycle_EndTime;
                            NextCyclePaidFees = orderDetailCycle?.Cycle_SumMoney;
                            NextCycleFreeMin = orderDetailCycle?.Cycle_SumFreeMin;

                            if (!param.readParam.FisrtCycle.ContainsKey(crModel.ChargeRules_No) && nextCycleTime != null)
                            {
                                param.readParam.FisrtCycle.Add(crModel.ChargeRules_No, orderDetailCycle);
                            }
                            //BillingBlackBox.CalcLogic.setCalcContent(ref param, $"获取周期时间：{nextCycleTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}，");
                        }

                        if (!ruleRunData.dicHours.ContainsKey(crModel.ChargeRules_No))
                        {
                            //查询时段累计表数据
                            var checkData = false;
                            {
                                var areaList = TyziTools.Json.ToObject<List<string>>(crModel.ChargeRules_ParkAreaNo);
                                //获取上一次的停车区域停车明细订单(时段累积金额)
                                // 1. 先用精确匹配条件过滤
                                var baseQuery = $@"
                                            HoursTotal_CarNo = '{Car_No}' AND 
                                            HoursTotal_CarType = '{parkOrder.ParkOrder_CarType}' AND
                                            HoursTotal_CarCardType = '{parkOrder.ParkOrder_CarCardType}'";
                                // 2. 再加范围条件
                                baseQuery += $@" AND HoursTotal_EnterTime < '{currDetail.OrderDetail_EnterTime.Value:yyyy-MM-dd HH:mm:ss}'";
                                if (!isOverTime)
                                {
                                    baseQuery += $@" AND HoursTotal_PayTime >= '{currDetail.OrderDetail_EnterTime.Value.AddHours(-24):yyyy-MM-dd 00:00:00}'";
                                }

                                // 3. IN条件放在where后面
                                baseQuery += $" AND HoursTotal_ParkAreaNo IN ('{string.Join("','", areaList)}')";

                                Model.HoursTotal hoursTotal = BLL.HoursTotal.GetEntity("", baseQuery + " ORDER BY HoursTotal_PayTime DESC  LIMIT 1");

                                //swCheckPass.Stop();
                                //LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"停车明细时段累计查询：{swCheckPass.ElapsedMilliseconds} 毫秒，");
                                if (hoursTotal != null)
                                {
                                    checkData = true;
                                    var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(hoursTotal.HoursTotal_Content);
                                    if (currentItem != null && currentItem.Count > 0)
                                    {
                                        if (param.writeParam.Hours_SumTimeMoney != null && param.writeParam.Hours_SumTimeMoney.Count > 0)
                                        {
                                            currentItem.ForEach(x =>
                                            {
                                                var searchItem = param.writeParam.Hours_SumTimeMoney.Where(m => m.SectionStartTime == x.SectionStartTime && m.SectionEndTime == x.SectionEndTime).ToList();
                                                if (searchItem.Count == 0)
                                                {
                                                    param.writeParam.Hours_SumTimeMoney.Add(x);
                                                }
                                            });
                                            param.writeParam.Hours_StrSumTimeMoney = NextHoursContent = TyziTools.Json.ToString(param.writeParam.Hours_SumTimeMoney);
                                        }
                                        else
                                        {
                                            param.writeParam.Hours_StrSumTimeMoney = NextHoursContent = hoursTotal.HoursTotal_Content;
                                        }
                                    }
                                }
                            }

                            //未查询到时段累计表数据，则查询历史的停车明细表数据（版本升级24小时内）
                            if (!checkData && (AppBasicCache.GetVersion?.Version_Time != null && AppBasicCache.GetVersion.Version_Time > DateTime.Now.AddHours(-24)) && !(AppBasicCache.CheckHoursTotalVersion ?? false))
                            {
                                var areaList = TyziTools.Json.ToObject<List<string>>(crModel.ChargeRules_ParkAreaNo);
                                //System.Diagnostics.Stopwatch swCheckPass = new System.Diagnostics.Stopwatch();
                                //swCheckPass.Start();
                                //获取上一次的停车区域停车明细订单(时段累积金额)
                                // 1. 先用精确匹配条件过滤
                                var baseQuery = $@"
                                            OrderDetail_ParkNo = '{currDetail.OrderDetail_ParkNo}' AND
                                            OrderDetail_CarNo = '{Car_No}' AND 
                                            orderdetail_CarType = '{parkOrder.ParkOrder_CarType}' AND
                                            orderdetail_CarCardType = '{parkOrder.ParkOrder_CarCardType}' AND
                                            OrderDetail_IsSettle = 1 AND
                                            orderdetail_HoursBeginTime IS NOT NULL AND
                                            orderdetail_HoursContent IS NOT NULL ";
                                // 2. 再加范围条件
                                baseQuery += $@" AND orderdetail_EnterTime < '{currDetail.OrderDetail_EnterTime.Value:yyyy-MM-dd HH:mm:ss}'";
                                if (!isOverTime)
                                {
                                    baseQuery += $@" AND orderdetail_OutTime >= '{currDetail.OrderDetail_EnterTime.Value.AddHours(-24):yyyy-MM-dd 00:00:00}'";
                                }

                                // 3. IN条件放在where后面
                                baseQuery += $" AND orderdetail_ParkAreaNo IN ('{string.Join("','", areaList)}')";

                                List<Model.OrderDetail> orderDetailForHoursTimeList =
                                    BLL.OrderDetail.GetAllEntity("OrderDetail_ID,orderdetail_HoursBeginTime,orderdetail_HoursContent", baseQuery + "  LIMIT 1000");

                                //swCheckPass.Stop();
                                //LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"停车明细时段累计查询：{swCheckPass.ElapsedMilliseconds} 毫秒，");
                                if (orderDetailForHoursTimeList.Count > 0)
                                {
                                    var orderDetailForHoursTime = orderDetailForHoursTimeList.OrderByDescending(x => x.orderdetail_HoursBeginTime).ThenByDescending(x => x.OrderDetail_ID).FirstOrDefault();
                                    if (orderDetailForHoursTime != null && !string.IsNullOrEmpty(orderDetailForHoursTime.orderdetail_HoursContent))
                                    {
                                        var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(orderDetailForHoursTime.orderdetail_HoursContent);
                                        if (currentItem != null && currentItem.Count > 0)
                                        {
                                            if (param.writeParam.Hours_SumTimeMoney != null && param.writeParam.Hours_SumTimeMoney.Count > 0)
                                            {
                                                currentItem.ForEach(x =>
                                                {
                                                    var searchItem = param.writeParam.Hours_SumTimeMoney.Where(m => m.SectionStartTime == x.SectionStartTime && m.SectionEndTime == x.SectionEndTime).ToList();
                                                    if (searchItem.Count == 0)
                                                    {
                                                        param.writeParam.Hours_SumTimeMoney.Add(x);
                                                    }
                                                });
                                                param.writeParam.Hours_StrSumTimeMoney = NextHoursContent = TyziTools.Json.ToString(param.writeParam.Hours_SumTimeMoney);
                                            }
                                            else
                                            {
                                                param.writeParam.Hours_StrSumTimeMoney = NextHoursContent = orderDetailForHoursTime?.orderdetail_HoursContent;
                                            }
                                        }
                                    }
                                }
                            }

                            ruleRunData.dicHours.Add(crModel.ChargeRules_No, NextHoursContent);
                        }

                        //初始化
                        param.Section_CurrentUseFreeTimeMin = 0;
                        param.Section_HavaMinCoupon = havaMinCoupon;

                        //跨区域仅使用一次首计时【0-禁用,1-启用】
                        if (crlModel.Logic_IsOnlyFirstTime != 1) isUseFirstTime = false;//是否使用首计时（True-已使用首计时，False-未使用首计时）

                        //使用过优惠券的就去掉首计时
                        if (charginPile) { isUseFirstTime = true; }

                        if (isOverTime) NextCycleFreeMin = 0;//超时计费，周期累计的免费分钟为0

                        if (currDetail.OrderDetail_EnterTime >= detailOutTime) { continue; }


                        //LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"计费时间：{order.OrderDetail_EnterTime} --  {detailOutTime}");

                        //开始计费
                        orderPayMoney = BenginChargeRule(ref param, crlModel, currDetail.OrderDetail_EnterTime, detailOutTime, ref isUseFirstTime, ref NextCyclePaidFees, ref nextCycleTime, NextHoursTime, NextHoursContent, NextCycleFreeMin);//得到计费金额、下次周期生效时间

                    }

                    if (orderPayMoney != null)
                    {
                        orderPayMoney.starttime = currDetail.OrderDetail_EnterTime;
                        orderPayMoney.endtime = detailOutTime;
                        orderPayMoney.areano = currDetail.OrderDetail_ParkAreaNo;
                        orderPayMoney.areaname = currDetail.OrderDetail_ParkAreaName;
                        orderPayMoney.parkorderno = currDetail.OrderDetail_ParkOrderNo;
                        orderPayMoney.orderdetailno = currDetail.OrderDetail_No;
                        orderPayMoney.orderdetailid = currDetail.OrderDetail_ID;
                        orderPayMoney.iscarexpire = param.readParam.IsCarExpire;
                        orderPayMoney.isovertime = param.readParam.IsOverTime;
                        orderPayMoney.parktimemin = (detailOutTime - currDetail.OrderDetail_EnterTime).Value.TotalMinutes;

                        Model.CarCardType cct = cctList.Where(x => x.CarCardType_No == currDetail.OrderDetail_CarCardType).FirstOrDefault();
                        if (cct != null)
                        {
                            orderPayMoney.payedmsg = $"按 {cct.CarCardType_Name} 收费";
                        }
                        //支付
                        orderPayMoney.currentfreemin = param.Section_CurrentUseFreeTimeMin;
                        orderPayMoney.payedamount = orderPayMoney.payedamount ?? 0;
                        orderPayMoney.nexthourstime = detailOutTime;
                        orderPayMoney.CalcResult = param.CalcResult != null ? string.Join("", param.CalcResult.Select(x => x.Remark)) : "";
                        payDetailList.Add(orderPayMoney);
                        param.CalcResult.Clear();

                        if (ruleRunData.dicRuleCycle.ContainsKey(crModel.ChargeRules_No)) { ruleRunData.dicRuleCycle.Remove(crModel.ChargeRules_No); }
                        CycleParam cycleParam = new CycleParam();
                        cycleParam.Cycle_AreaNo = currDetail.OrderDetail_ParkAreaNo;
                        cycleParam.Cycle_IsCharge = param.readParam.Cycle.Cycle_IsCharge;
                        cycleParam.Cycle_SumFreeMin = orderPayMoney.nextcyclefreemin;
                        cycleParam.Cycle_SumMoney = orderPayMoney.NextCyclePaidFees;
                        cycleParam.Cycle_EndTime = orderPayMoney.nextcycletime;
                        ruleRunData.dicRuleCycle.Add(crModel.ChargeRules_No, cycleParam);

                        if (orderPayMoney.payed == 1)
                        {
                            if (ruleRunData.dicRuleCharge.ContainsKey(crModel.ChargeRules_No))
                                ruleRunData.dicRuleCharge[crModel.ChargeRules_No] += orderPayMoney.payedamount ?? 0;
                            else
                                ruleRunData.dicRuleCharge.Add(crModel.ChargeRules_No, orderPayMoney.payedamount ?? 0);
                        }

                        //处理时段累计金额
                        if (!string.IsNullOrEmpty(orderPayMoney.nexthourscontent))
                        {
                            param.writeParam.Hours_SumTimeMoney = TyziTools.Json.ToObject<List<CalcHoursResult>>(orderPayMoney.nexthourscontent);
                            param.writeParam.Hours_StrSumTimeMoney = orderPayMoney.nexthourscontent;
                            payDetailList.ForEach(xDetail =>
                            {
                                //处理非当前停车明细的又同时段的时段计费累计：以当前停车明细的时段累计为准
                                if (!string.IsNullOrEmpty(xDetail.nexthourscontent) && xDetail.orderdetailno != param.OrderDetail_No)
                                {
                                    var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(xDetail.nexthourscontent);
                                    if (currentItem != null && currentItem.Count > 0)
                                    {
                                        param.writeParam.Hours_SumTimeMoney.ForEach(x =>
                                        {
                                            var searchItem = currentItem.Where(m => m.SectionStartTime == x.SectionStartTime && m.SectionEndTime == x.SectionEndTime).ToList();
                                            if (searchItem.Count == 0)
                                            {
                                                currentItem.Add(x);
                                            }
                                            else
                                            {
                                                currentItem.Remove(searchItem.First());
                                                currentItem.Add(x);
                                            }
                                        });
                                        xDetail.nexthourscontent = TyziTools.Json.ToString(currentItem);
                                        if (ruleRunData.dicHours.ContainsKey(crModel.ChargeRules_No)) { ruleRunData.dicHours.Remove(crModel.ChargeRules_No); }
                                        ruleRunData.dicHours.Add(crModel.ChargeRules_No, xDetail.nexthourscontent);
                                    }
                                }
                            });

                            if (ruleRunData.dicHours.ContainsKey(crModel.ChargeRules_No)) { ruleRunData.dicHours.Remove(crModel.ChargeRules_No); }
                            ruleRunData.dicHours.Add(crModel.ChargeRules_No, orderPayMoney.nexthourscontent);
                        }
                    }
                    else
                    {
                        var item = payDetailList.Where(x => x.orderdetailno == currDetail.OrderDetail_No).FirstOrDefault();
                        if (item != null)
                        {
                            payDetailList.Remove(item);
                            if (item.nextcycletime < nextCycleTime) item.nextcycletime = nextCycleTime;
                            if (item.NextCyclePaidFees < NextCyclePaidFees) item.NextCyclePaidFees = NextCyclePaidFees;
                            if (item.nextcyclefreemin < Utils.ObjectToInt(orderPayMoney.nextcyclefreemin, 0)) item.nextcyclefreemin = Utils.ObjectToInt(orderPayMoney.nextcyclefreemin, 0);

                            item.parktimemin += (detailOutTime - currDetail.OrderDetail_EnterTime).Value.TotalMinutes;
                            payDetailList.Add(item);
                        }
                        else
                        {

                            //不需要支付
                            ChargeModels.PayDetail ret = CreatePayDetail(0, "无需缴费", 0, currDetail.OrderDetail_ParkAreaNo, currDetail.OrderDetail_ParkOrderNo, currDetail.OrderDetail_No, currDetail.OrderDetail_ID,
                              nextCycleTime, NextCyclePaidFees, detailOutTime, currDetail.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, currDetail.OrderDetail_ParkAreaName);
                            ret.iscarexpire = param.readParam.IsCarExpire;
                            ret.isovertime = param.readParam.IsOverTime;
                            payDetailList.Add(ret);
                        }
                    }
                }
            }
            //}


            payDetailList?.ForEach(xDetail =>
            {
                if (!string.IsNullOrEmpty(xDetail.nexthourscontent))
                {
                    var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(xDetail.nexthourscontent);
                    if (currentItem != null && currentItem.Count > 0)
                    {
                        var lastItem = currentItem.LastOrDefault();
                        xDetail.nexthourscontent = TyziTools.Json.ToString(new List<CalcHoursResult>() { lastItem });
                    }
                }
            });

            return payDetailList;
        }

        /// <summary>
        /// 创建明细计费结果
        /// </summary>
        /// <param name="payed">计费结果：0-无需计费，1-计费成功,2-计费失败</param>
        /// <param name="payedmsg">计费描述</param>
        /// <param name="payedamount">计费金额</param>
        /// <param name="OrderDetail_ParkAreaNo">区域编号</param>
        /// <param name="OrderDetail_ParkOrderNo">停车订单编号</param>
        /// <param name="OrderDetail_No">停车明细编号</param>
        /// <param name="OrderDetail_ID">停车明细ID</param>
        /// <param name="nextCycleTime">周期生效时间</param>
        /// <param name="NextCyclePaidFees">周期已累积支付总金额</param>
        /// <param name="detailOutTime">出场时间</param>
        /// <param name="OrderDetail_EnterTime">进场时间</param>
        /// <param name="NextHoursTime">时段累计生效时间</param>
        /// <param name="NextHoursContent">时段累计金额</param>
        /// <param name="NextCycleFreeMin">周期累计免费分钟</param>
        /// <returns>明细计费结果</returns>
        public static ChargeModels.PayDetail CreatePayDetail(int payed, string payedmsg, decimal payedamount, string OrderDetail_ParkAreaNo = null, string OrderDetail_ParkOrderNo = null,
            string OrderDetail_No = null, long? OrderDetail_ID = null, DateTime? nextCycleTime = null, decimal? NextCyclePaidFees = null,
            DateTime? detailOutTime = null, DateTime? OrderDetail_EnterTime = null, DateTime? NextHoursTime = null, string NextHoursContent = null, int? NextCycleFreeMin = null, string OrderDetail_ParkAreaName = null)
        {
            return new ChargeModels.PayDetail()
            {
                starttime = OrderDetail_EnterTime,
                endtime = detailOutTime,
                areano = OrderDetail_ParkAreaNo,
                parkorderno = OrderDetail_ParkOrderNo,
                orderdetailno = OrderDetail_No,
                orderdetailid = OrderDetail_ID,
                payed = payed,
                payedmsg = payedmsg,
                nextcycletime = nextCycleTime,
                NextCyclePaidFees = NextCyclePaidFees,
                payedamount = payedamount,
                parktimemin = (detailOutTime - OrderDetail_EnterTime).Value.TotalMinutes,
                nexthourstime = NextHoursTime,
                nexthourscontent = NextHoursContent,
                nextcyclefreemin = NextCycleFreeMin,
                calcbegintime = OrderDetail_EnterTime,
                areaname = OrderDetail_ParkAreaName
            };
        }

        /// <summary>
        /// 获取计费规则
        /// </summary>
        /// <param name="order"></param>
        /// <param name="isOverTime"></param>
        /// <param name="nowTime"></param>
        /// <returns></returns>
        public static Model.ChargeRules GetRules(Model.OrderDetail order, bool isOverTime, string nowTime)
        {
            Model.ChargeRules crModel = null;

            if (!AppBasicCache.ReadWriteCache || AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache != 1)
            {
                var enterTime = order.OrderDetail_EnterTime.Value.ToString("yyyy-MM-dd");
                List<Model.ChargeRelation> relationList = BLL.ChargeRelation._GetAllEntity(new Model.ChargeRelation(), "*", $"ChargeRelation_ParkNo='{order.OrderDetail_ParkNo}' " +
                    (isOverTime ? "and (ChargeRelation_OverTime=1 or ChargeRelation_OverTime=2)" : "and (ChargeRelation_OverTime=0 or ChargeRelation_OverTime=2)") +
                    $" and ChargeRelation_CarTypeNo='{order.OrderDetail_CarType}' and ChargeRelation_CarCardTypeNo='{order.OrderDetail_CarCardType}' and ChargeRelation_ParkAreaNo='{order.OrderDetail_ParkAreaNo}' " +
                    $" and (" +
                    $"   (ChargeRelation_BeginTime>='{enterTime}' and ChargeRelation_BeginTime<='{nowTime}') " +
                    $"or (ChargeRelation_BeginTime<='{enterTime}' and ChargeRelation_EndTime >='{nowTime}')" +
                    $"or (ChargeRelation_BeginTime <='{enterTime}' and ChargeRelation_EndTime >='{enterTime}')" +
                    $")");
                if (relationList != null && relationList.Count > 0)
                {
                    crModel = BLL.ChargeRules._GetEntityByWhere(new Model.ChargeRules(), "*", $"ChargeRules_No='{relationList.Last().ChargeRelation_ChargeRulesNo}'");
                }
            }
            else
            {
                var relationList = new List<Model.ChargeRelation>();
                AppBasicCache.GetAllChargeRelation.Values.ToList().ForEach(x => { relationList.AddRange(x); });
                var rList = relationList.Where(x => (x.ChargeRelation_OverTime == (isOverTime ? 1 : 0) || x.ChargeRelation_OverTime == 2)
                 && x.ChargeRelation_CarTypeNo == order.OrderDetail_CarType && x.ChargeRelation_CarCardTypeNo == order.OrderDetail_CarCardType && x.ChargeRelation_ParkAreaNo == order.OrderDetail_ParkAreaNo
                 && (
                    (x.ChargeRelation_BeginTime >= order.OrderDetail_EnterTime.Value && x.ChargeRelation_BeginTime <= Utils.ObjectToDateTime(nowTime))
                    || (x.ChargeRelation_BeginTime <= order.OrderDetail_EnterTime.Value && x.ChargeRelation_EndTime >= Utils.ObjectToDateTime(nowTime))
                    || (x.ChargeRelation_BeginTime <= order.OrderDetail_EnterTime.Value && x.ChargeRelation_EndTime >= order.OrderDetail_EnterTime.Value)
                   )
                 ).ToList();
                if (rList != null && rList.Count > 0)
                {
                    crModel = AppBasicCache.GetElement(AppBasicCache.GetAllChargeRules, rList.Last().ChargeRelation_ChargeRulesNo);
                }
            }

            if (crModel == null)
            {
                //获取计费规则(通过车场设置的车牌计费设置，找到关联的默认计费规则)
                crModel = GetPolicyRules(order, isOverTime, nowTime);
            }

            return crModel;
        }
        /// <summary>
        /// 获取计费规则(通过车场设置的车牌计费设置，找到关联的默认计费规则)
        /// </summary>
        /// <param name="order"></param>
        /// <param name="isOverTime"></param>
        /// <param name="nowTime"></param>
        /// <returns></returns>
        private static Model.ChargeRules GetPolicyRules(Model.OrderDetail order, bool isOverTime, string nowTime)
        {
            Model.ChargeRules crModel = null;
            //车场设置（通过车牌类型找到关联的默认计费规则）
            Model.PolicyCarCard policy = BLL.PolicyCarCard.GetEntityByCarCard(order.OrderDetail_CarCardType);
            if (policy == null) { return null; }

            //月租车没有默认计费规则
            var cctModel = BLL.CarCardType.GetEntity(order.OrderDetail_CarCardType);
            if (cctModel != null && (cctModel.CarCardType_Type == 3 || cctModel.CarCardType_Type == 2)) { return null; }

            if (!string.IsNullOrEmpty(policy.PolicyCarCard_CalculateRule) && policy.PolicyCarCard_CalculateRule != "0")
            {
                if (!AppBasicCache.ReadWriteCache || AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache != 1)
                {
                    var enterTime = order.OrderDetail_EnterTime.Value.ToString("yyyy-MM-dd");
                    List<Model.ChargeRelation> relationList = BLL.ChargeRelation._GetAllEntity(new Model.ChargeRelation(), "*", $"ChargeRelation_ParkNo='{order.OrderDetail_ParkNo}' " +
                        (isOverTime ? "and (ChargeRelation_OverTime=1 or ChargeRelation_OverTime=2)" : "and (ChargeRelation_OverTime=0 or ChargeRelation_OverTime=2)") +
                        $" and ChargeRelation_CarTypeNo='{order.OrderDetail_CarType}' and ChargeRelation_CarCardTypeNo='{policy.PolicyCarCard_CalculateRule}' and ChargeRelation_ParkAreaNo='{order.OrderDetail_ParkAreaNo}' " +
                        $" and (" +
                        $"   (ChargeRelation_BeginTime>='{enterTime}' and ChargeRelation_BeginTime<='{nowTime}') " +
                        $"or (ChargeRelation_BeginTime<='{enterTime}' and ChargeRelation_EndTime >='{nowTime}')" +
                        $"or (ChargeRelation_BeginTime <='{enterTime}' and ChargeRelation_EndTime >='{enterTime}')" +
                        $")");
                    if (relationList != null && relationList.Count > 0)
                    {
                        crModel = BLL.ChargeRules._GetEntityByWhere(new Model.ChargeRules(), "*", $"ChargeRules_No='{relationList.Last().ChargeRelation_ChargeRulesNo}'");
                    }
                }
                else
                {
                    var relationList = new List<Model.ChargeRelation>();
                    AppBasicCache.GetAllChargeRelation.Values.ToList().ForEach(x => { relationList.AddRange(x); });
                    var rList = relationList.Where(x => (x.ChargeRelation_OverTime == (isOverTime ? 1 : 0) || x.ChargeRelation_OverTime == 2)
                     && x.ChargeRelation_CarTypeNo == order.OrderDetail_CarType && x.ChargeRelation_CarCardTypeNo == policy.PolicyCarCard_CalculateRule && x.ChargeRelation_ParkAreaNo == order.OrderDetail_ParkAreaNo
                     && (
                        (x.ChargeRelation_BeginTime >= order.OrderDetail_EnterTime.Value && x.ChargeRelation_BeginTime <= Utils.ObjectToDateTime(nowTime))
                        || (x.ChargeRelation_BeginTime <= order.OrderDetail_EnterTime.Value && x.ChargeRelation_EndTime >= Utils.ObjectToDateTime(nowTime))
                        || (x.ChargeRelation_BeginTime <= order.OrderDetail_EnterTime.Value && x.ChargeRelation_EndTime >= order.OrderDetail_EnterTime.Value)
                       )
                     ).ToList();
                    if (rList != null && rList.Count > 0)
                    {
                        crModel = AppBasicCache.GetElement(AppBasicCache.GetAllChargeRules, rList.Last().ChargeRelation_ChargeRulesNo);
                    }
                }
            }

            return crModel;
        }

        /// <summary>
        /// 通过计费规则，开始计费
        /// </summary>
        /// <param name="enterTime">入场时间</param>
        /// <param name="outTime">出场时间</param>
        /// <returns></returns>
        public static ChargeModels.PayDetail BenginChargeRule(ref BillingBlackBox.Models.SectionParam param, BillingBlackBox.Models.ChargeRulesLogic crlModel, DateTime? enterTime, DateTime? outTime, ref bool isUseFirstTime, ref decimal? cycleMoney, ref DateTime? cycleTime,
            DateTime? NextHoursTime = null, string NextHoursContent = null, int? NextCycleFreeMin = null, int? UserFreeTime = 0)
        {
            ChargeModels.PayDetail payDetail = CreatePayDetail(2, "计费失败", 0, null, null, null, null, DateTimeHelper.GetNowTime(), 0, outTime, enterTime, null, null, null);
            payDetail.preNextcycletime = cycleTime;
            payDetail.preNextCyclePaidFees = cycleMoney;
            payDetail.preNextcyclefreemin = NextCycleFreeMin;

            BillingBlackBox.Helper.ChargeHelper.SetParamDefaultValue(ref crlModel);

            #region  获取工作日节假日
            List<string> holidays = new List<string>();
            List<string> workdays = new List<string>();

            List<Model.DateSet> dsList = GetWorkHoliday(enterTime, outTime);
            dsList.ForEach(x =>
            {
                if (x.DateSet_Type == 1) { workdays.Add(x.DateSet_Date.Value.ToString("yyyy-MM-dd")); }
                else { holidays.Add(x.DateSet_Date.Value.ToString("yyyy-MM-dd")); }
            });

            for (var day = new DateTime(enterTime.Value.Year, enterTime.Value.Month, enterTime.Value.Day); day <= new DateTime(outTime.Value.Year, outTime.Value.Month, outTime.Value.Day); day = day.AddDays(1))
            {
                if (dsList.FindAll(x => x.DateSet_Date == day).Count == 0)
                {
                    if ((int)day.DayOfWeek != 6 && (int)day.DayOfWeek != 0)
                    {
                        workdays.Add(day.ToString("yyyy-MM-dd"));
                    }
                    else
                    {
                        holidays.Add(day.ToString("yyyy-MM-dd"));
                    }
                }
            }

            #endregion

            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();

            //时段累计金额
            List<BillingBlackBox.Models.CalcHoursResult> hrList = string.IsNullOrEmpty(NextHoursContent) ? null : TyziTools.Json.ToModel<List<BillingBlackBox.Models.CalcHoursResult>>(NextHoursContent);
            if (hrList != null)
            {
                if (hrList.Count > 0)
                {
                    BillingBlackBox.Models.CalcHoursResult calcHours = hrList.Last();
                    if (calcHours.SectionStartTime < enterTime.Value)
                    {
                        hrList = hrList.FindAll(x => x.SectionEndTime >= enterTime.Value);
                    }
                    else
                    {
                        hrList = null;//历史时段累计开始时间大于入场时间，则清空时段累计金额重新计费
                    }
                }
                else
                {
                    hrList = null;
                }
            }
            else
            {
                hrList = null;
            }

            //计费算法入口
            string result = CalcLogic.AIDivideCalc(ref param, enterTime.Value, outTime.Value, crlModel, ref isUseFirstTime, ref cycleMoney, ref cycleTime, holidays, workdays, NextHoursTime, hrList, NextCycleFreeMin);

            //sw.Stop();
            //BLL.SystemLogs.AddLog(null, "计费时长", "计费算法运算耗时 " + sw.ElapsedMilliseconds.ToString() + " 毫秒," + TyziTools.Json.ToString(result));
            if (!string.IsNullOrEmpty(result))
            {
                BillingBlackBox.Models.ChargeResult chargeResult = TyziTools.Json.ToModel<BillingBlackBox.Models.ChargeResult>(result);
                if (chargeResult != null)
                {
                    if (chargeResult.Code == 0)//计费失败
                    {
                        return payDetail;
                    }
                    else if (chargeResult.Code == 2)//无需缴费
                    {
                        payDetail.payed = 0;
                        payDetail.starttime = chargeResult.StartTime;
                        payDetail.endtime = chargeResult.EndTime;
                        payDetail.payedmsg = chargeResult.Msg;
                        payDetail.nextcycletime = chargeResult.NextCycleTime;
                        payDetail.NextCyclePaidFees = chargeResult.NextCyclePaidFees;
                        payDetail.nextcyclefreemin = chargeResult.NextCycleFreeMin;
                        payDetail.nexthourscontent = chargeResult.HoursMoenyList == null ? null : TyziTools.Json.ToString(chargeResult.HoursMoenyList);
                        payDetail.currentfreemin = chargeResult.CurrentFreeMin;
                        return payDetail;
                    }
                    else//计费成功
                    {
                        payDetail.payed = 1;
                        payDetail.starttime = chargeResult.StartTime;
                        payDetail.endtime = chargeResult.EndTime;
                        payDetail.payedmsg = chargeResult.Msg;
                        payDetail.nextcycletime = chargeResult.NextCycleTime;
                        payDetail.NextCyclePaidFees = Utils.ObjectToDecimal(chargeResult.NextCyclePaidFees, 0);
                        payDetail.calcbegintime = chargeResult.StartTime;
                        payDetail.payedamount = Utils.ObjectToDecimal(chargeResult.FeePayable, 0);
                        payDetail.nightamount = Utils.ObjectToDecimal(chargeResult.SumNightFee, 0);
                        payDetail.nextcyclefreemin = chargeResult.NextCycleFreeMin;
                        payDetail.nexthourscontent = chargeResult.HoursMoenyList == null ? null : TyziTools.Json.ToString(chargeResult.HoursMoenyList);
                        payDetail.currentfreemin = chargeResult.CurrentFreeMin;

                        //payDetail.total2 = param.writeParam.ParkOrder_TotalPayed2.Value + payDetail.payedamount.Value;
                        return payDetail;
                    }
                }
            }


            return payDetail;
        }
        /// <summary>
        /// 停车明细结果处理
        /// </summary>
        /// <param name="payDetailList"></param>
        /// <returns></returns>
        private static ChargeModels.PayResult ProcessDetail(List<ChargeModels.PayDetail> payDetailList)
        {
            if (payDetailList != null) payDetailList = payDetailList.OrderBy(x => x.starttime).ToList();

            ChargeModels.PayResult result = new ChargeModels.PayResult();
            result.payed = 1;
            result.payedmsg = "计费成功";
            result.payedamount = 0;
            result.nightamount = 0;
            result.couponamount = 0;
            result.orderamount = 0;
            result.parktimemin = 0;
            result.list = payDetailList;
            if (payDetailList != null && payDetailList.Count > 0)
            {
                foreach (var item in payDetailList)
                {
                    if (item.payed == 2)
                    {
                        result.payed = 2;
                        result.payedmsg = item.payedmsg;
                        break;
                    }
                    result.parktimemin += Utils.ObjectToDouble(item.parktimemin, 0);
                    result.orderamount += Utils.ObjectToDecimal(item.payedamount, 0);
                    result.nightamount += Utils.ObjectToDecimal(item.nightamount, 0);
                }
                result.payedamount = result.orderamount;
                if (result.payed != 2 && result.orderamount == 0)
                {
                    result.payed = 0;
                    result.payedmsg = "无需缴费";
                }
            }
            else
            {
                result.payed = 0;
                result.payedmsg = "无需缴费";
            }
            return result;
        }

        private static List<Model.DateSet> GetWorkHoliday(DateTime? startTime, DateTime? endTime)
        {
            List<Model.DateSet> dsList = BLL.DateSet.GetAllEntity("DateSet_Date,DateSet_Type", $"DateSet_Date>='{startTime.Value.ToString("yyyy-MM-dd 00:00:00")}' and DateSet_Date<='{endTime.Value.ToString("yyyy-MM-dd 00:00:00")}'");
            return dsList;
        }
        #endregion

        #region 具体优惠时间、优惠金额、优惠打折计费调用
        /// <summary>
        ///  优惠分钟
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="freeMinutes">免费分钟</param>
        /// <param name="UseOrderCoupon">是否使用关联当前车辆的其它优惠券</param>
        /// <returns></returns>
        public static ChargeModels.PayResult GetChargeByMin(Model.ParkOrder parkOrder, DateTime? outTime, decimal? freeMinutes, Model.Car car,
            out List<Model.CouponRecordIntExt> orderAllCouponList, bool UseOrderCoupon = true, string carTypeNo = "",
            string carCarTypeNo = "", bool isOutGateOrder = false, bool isFllowCar = false, List<Model.OrderDetail> detailList = null,
            CalcBasicData baiseData = null, string allTimeFree = null)
        {
            List<Model.CouponRecordIntExt> crList = null;
            List<Model.CouponRecordIntExt> orderCouponList = null;
            orderAllCouponList = null;
            if (freeMinutes > 0)
            {
                crList = new List<Model.CouponRecordIntExt>();
                Model.CouponRecordIntExt crExt = new Model.CouponRecordIntExt();
                crExt.CouponRecord_ID = 2147483647; //赋一个最大值，不会保存数据库，下面做优惠券集合排序时排在第一位
                crExt.CouponRecord_CouponCode = Convert.ToString((int)Common.EnumCouponType.Time);
                crExt.CouponRecord_Value = freeMinutes;
                crExt.CouponRecord_ParkNo = parkOrder.ParkOrder_ParkNo;
                crList.Add(crExt);
            }

            if (!string.IsNullOrEmpty(allTimeFree))
            {
                string carno = "";//车牌号
                string startTime = ""; //优惠券开始时间
                string endTime = "";//优惠券结束时间
                int availableLimit = 1;//可用次数限制
                decimal couponValue = 0;//优惠券值
                var couponTimeList = TyziTools.Json.ToObject<List<JObject>>(allTimeFree);
                if (couponTimeList != null && couponTimeList.Count > 0)
                {
                    int index = 1;
                    crList = crList ?? new List<Model.CouponRecordIntExt>();
                    foreach (var couponTime in couponTimeList)
                    {
                        startTime = couponTime["startTime"].ToString();
                        endTime = couponTime["endTime"].ToString();
                        availableLimit = Utils.ObjectToInt(couponTime["availableLimit"].ToString(), 1);
                        carno = couponTime["carNo"].ToString();
                        couponValue = Utils.ObjectToDecimal(couponTime["couponValue"].ToString(), 0);
                        Model.CouponRecordIntExt crExt = new Model.CouponRecordIntExt();
                        crExt.CouponRecord_ID = 2147483647 - index; //赋一个最大值，不会保存数据库，下面做优惠券集合排序时排在第一位
                        crExt.CouponRecord_CouponCode = Convert.ToString((int)Common.EnumCouponType.HourFree);
                        crExt.CouponRecord_StartTime = Utils.ObjectToDateTime(startTime);
                        crExt.CouponRecord_EndTime = Utils.ObjectToDateTime(endTime);
                        crExt.CouponRecord_ParkNo = parkOrder.ParkOrder_ParkNo;
                        crExt.CouponRecord_Value = couponValue;
                        crList.Add(crExt);
                        index++;
                    }
                }
            }

            if (UseOrderCoupon)
            {
                orderAllCouponList = GetCouponByOrderNo(parkOrder.ParkOrder_No, outTime);
                if (orderAllCouponList != null)
                {
                    orderCouponList = isOutGateOrder ? orderAllCouponList : orderAllCouponList.FindAll(x => x.CouponRecord_OnLine != 1 || x.CouponRecord_Other == 1);//如果不是出口缴费，属于线下生成的优惠券 或者 属于第三方下发的优惠券 才让用。
                                                                                                                                                                     //根据车场策略限制优惠券张数
                    if (orderCouponList != null)
                    {
                        int? PolicyPark_MaxDiscount = 0;
                        orderCouponList = GetPolicyCoupon(parkOrder.ParkOrder_ParkNo, orderCouponList, out PolicyPark_MaxDiscount);
                    }
                    if (crList != null && orderCouponList != null) crList.AddRange(orderCouponList); else crList = orderCouponList;
                }
            }

            ChargeModels.PayResult result = GetChargeByCar(parkOrder, outTime, car, crList?.Copy(), false, carTypeNo, carCarTypeNo, null, detailList, isFllowCar, baiseData: baiseData);
            if (UseOrderCoupon && orderAllCouponList != null && orderAllCouponList.Count > 0 && result.payed == 1)
            {
                orderAllCouponList.ForEach(x =>
                {
                    x.CouponRecord_PreUse = 0;
                    x.CouponRecord_Paid = 0;
                    if (orderCouponList != null && orderCouponList.Count > 0)
                    {
                        var isExitModel = orderCouponList.FindAll(y => y.CouponRecord_No == x.CouponRecord_No);
                        if (isExitModel != null && isExitModel.Count > 0)
                        {
                            x.CouponRecord_PreUse = 1;
                            if (result.uselist != null)
                            {
                                var couponUse = result.uselist.Where(y => y.CouponRecord_No == x.CouponRecord_No).FirstOrDefault();
                                if (couponUse != null)
                                {
                                    x.CouponRecord_Paid = couponUse == null ? 0 : couponUse.DiscountMoney;
                                }
                            }
                        }
                    }
                });
            }

            InterceptNumber(result);

            return result;
        }
        /// <summary>
        /// 优惠金额
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="money">优惠金额</param>
        /// <param name="UseOrderCoupon">是否使用关联当前车辆的其它优惠券</param>
        public static ChargeModels.PayResult GetChargeByMoney(Model.ParkOrder parkOrder, DateTime? outTime, decimal? money, Model.Car car = null, bool UseOrderCoupon = true, string carTypeNo = "", string carCarTypeNo = "")
        {
            List<Model.CouponRecordIntExt> crList = null;
            if (money > 0)
            {
                crList = new List<Model.CouponRecordIntExt>();
                Model.CouponRecordIntExt crExt = new Model.CouponRecordIntExt();
                crExt.CouponRecord_ID = 2147483647; //赋一个最大值，不会保存数据库，下面做优惠券集合排序时排在第一位
                crExt.CouponRecord_CouponCode = Convert.ToString((int)Common.EnumCouponType.Money);
                crExt.CouponRecord_Value = money;
                crList.Add(crExt);
            }
            ChargeModels.PayResult result = GetChargeByCar(parkOrder, outTime, car, crList, UseOrderCoupon, carTypeNo, carCarTypeNo);
            return result;
        }
        /// <summary>
        /// 优惠折扣
        /// </summary>
        /// <param name="parkOrder">停车订单</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="discount">折扣数</param>
        /// <param name="UseOrderCoupon">是否使用关联当前车辆的其它优惠券</param>
        public static ChargeModels.PayResult GetChargeByDiscount(Model.ParkOrder parkOrder, DateTime? outTime, decimal? discount, Model.Car car = null, bool UseOrderCoupon = true, string carTypeNo = "", string carCarTypeNo = "")
        {
            List<Model.CouponRecordIntExt> crList = null;
            if (discount > 0)
            {
                crList = new List<Model.CouponRecordIntExt>();
                Model.CouponRecordIntExt crExt = new Model.CouponRecordIntExt();
                crExt.CouponRecord_ID = 2147483647; //赋一个最大值，不会保存数据库，下面做优惠券集合排序时排在第一位
                crExt.CouponRecord_CouponCode = Convert.ToString((int)Common.EnumCouponType.Discount);
                crExt.CouponRecord_Value = discount;
                crList.Add(crExt);
            }
            ChargeModels.PayResult result = GetChargeByCar(parkOrder, outTime, car, crList, UseOrderCoupon, carTypeNo, carCarTypeNo);
            return result;
        }

        #endregion

        #region 车辆有效时段过期拆分处理计费

        /// <summary>
        /// 优惠计费
        /// </summary>
        /// <param name="parkOrder"></param>
        /// <param name="outTime"></param>
        /// <param name="payResult"></param>
        /// <param name="orderDetailList"></param>
        /// <param name="crList"></param>
        /// <param name="UseOrderCoupon"></param>
        /// <returns></returns>
        private static ChargeModels.PayResult UserCoupon(Model.ParkOrder parkOrder, DateTime? outTime, ChargeModels.PayResult payResult, List<Model.OrderDetail> orderDetailList, List<Model.CouponRecordIntExt> crList = null, bool UseOrderCoupon = true, DateTime? PayOrder_PayedTime = null, Model.Car car = null)
        {
            payResult.recordlist = crList == null ? new List<ChargeModels.CouponRecordIntExt>() : TyziTools.Json.ToModel<List<ChargeModels.CouponRecordIntExt>>(TyziTools.Json.ToString(crList)); ;
            //优惠券使用
            if (payResult != null && crList != null)
            {
                payResult.uselist = GetCouponCharge(parkOrder, crList, outTime, orderDetailList, payResult, PayOrder_PayedTime, null, false, car);
            }
            return payResult;
        }
        /// <summary>
        /// 调整优惠券的值（用于拆分停车明细优惠）
        /// </summary>
        /// <param name="result"></param>
        /// <param name="parkOrder"></param>
        /// <param name="outTime"></param>
        /// <param name="orderDetailList"></param>
        /// <param name="crList"></param>
        /// <param name="UseOrderCoupon"></param>
        /// <returns></returns>
        private static ChargeModels.PayResult CouponResult(ChargeModels.PayResult result, Model.ParkOrder parkOrder, DateTime? outTime, List<Model.OrderDetail> orderDetailList, ref List<Model.CouponRecordIntExt> crList, bool UseOrderCoupon = true, DateTime? PayOrder_PayedTime = null, Model.Car car = null)
        {
            if (result.payed == 1)
            {
                if (orderDetailList != null) orderDetailList = orderDetailList.Where(x => x.orderdetail_IsCharge != 1).ToList();
                //优惠券使用
                result = UserCoupon(parkOrder, outTime, result, orderDetailList, crList, UseOrderCoupon, PayOrder_PayedTime, car);

                if (crList != null)
                {
                    //减免金额
                    var crList1 = crList.FindAll(x => x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Money));
                    if (crList1 != null && crList1.Count > 0)
                    {
                        crList1.ForEach(x =>
                        {
                            if (result.uselist != null)
                            {
                                var useModel = result.uselist.Find(y => y.CouponRecord_No == x.CouponRecord_No);
                                if (useModel != null && useModel.DiscountMoney != null)
                                {
                                    if (x.CouponRecord_Value > useModel.DiscountMoney)
                                    {
                                        x.CouponRecord_Value = x.CouponRecord_Value - useModel.DiscountMoney;
                                    }
                                    else
                                    {
                                        x.CouponRecord_Value = 0;
                                    }
                                }
                            }
                        });
                    }

                    //优惠时间
                    var crList2 = crList.FindAll(x => x.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Time));
                    if (crList2 != null && crList2.Count > 0)
                    {
                        crList2.ForEach(x =>
                        {
                            if (result.uselist != null)
                            {
                                var useModel = result.uselist.Find(y => y.CouponRecord_No == x.CouponRecord_No);
                                if (useModel != null && useModel.CouponRecord_Value != null)
                                {
                                    if (useModel.CouponRecord_Value != null && useModel.CouponRecord_Value > 0)
                                    {
                                        x.CouponRecord_Value = Utils.ObjectToDecimal(useModel.CouponRecord_Value.Value / 60);
                                    }
                                    else
                                    {
                                        x.CouponRecord_Value = 0;
                                    }
                                }
                            }
                        });
                    }
                }

            }
            return result;
        }
        /// <summary>
        /// 拆分停车明细
        /// </summary>
        /// <param name="parkOrder"></param>
        /// <param name="car"></param>
        /// <param name="outTime"></param>
        /// <param name="orderDetailList1"></param>
        /// <param name="orderDetailList2"></param>
        /// <param name="orderDetailList3"></param>
        private static List<Models.OrderDetailResult> RetOrderDetail(Model.ParkOrder parkOrder, List<Model.OrderDetail> orderDetailList, Model.Car car, DateTime? outTime, Model.CarCardType cct, Model.CarCardType parkOrderCardType, ref Model.CarCardType policyCct, ref List<Model.OrderDetail> orderDetailList1, ref List<Model.OrderDetail> orderDetailList2, ref List<Model.OrderDetail> orderDetailList3, ref int? payed, Model.CarCardType tempAcct = null)
        {
            orderDetailList1 = new List<Model.OrderDetail>();//非登记时间内
            orderDetailList2 = new List<Model.OrderDetail>();//登记时间段
            orderDetailList3 = new List<Model.OrderDetail>();//过期
            List<Models.OrderDetailResult> rsList = new List<Models.OrderDetailResult>();
            foreach (var x in orderDetailList)
            {
                if (x.OrderDetail_OutTime == null) x.OrderDetail_OutTime = outTime;

                if (CarTypeHelper.GetCarTypeIndex(cct.CarCardType_Category) != (int)CarTypeEnum.DeviceVisitor)//访客车不修改
                {
                    SplitDetail(x, car, cct, ref rsList, ref policyCct, ref orderDetailList1, ref orderDetailList2, ref orderDetailList3, ref payed, ref tempAcct);
                }
                else
                {
                    orderDetailList1.Add(x);
                }
            }
            return rsList;
        }

        /// <summary>
        /// 拆分计费时段
        /// </summary>
        /// <param name="x"></param>
        /// <param name="car"></param>
        /// <param name="cct"></param>
        /// <param name="rsList"></param>
        /// <param name="policyCct"></param>
        /// <param name="orderDetailList1"></param>
        /// <param name="orderDetailList2"></param>
        /// <param name="orderDetailList3"></param>
        /// <param name="payed"></param>
        /// <param name="tempAcct"></param>
        private static void SplitDetail(Model.OrderDetail x, Model.Car car, Model.CarCardType cct, ref List<Models.OrderDetailResult> rsList, ref Model.CarCardType policyCct, ref List<Model.OrderDetail> orderDetailList1, ref List<Model.OrderDetail> orderDetailList2, ref List<Model.OrderDetail> orderDetailList3, ref int? payed, ref Model.CarCardType tempAcct)
        {
            if (x.OrderDetail_EnterTime > car.Car_EndTime || x.OrderDetail_EnterTime < car.Car_BeginTime)
            {
                //车场设置（通过车牌类型找到关联的过期处理方式：按指定类型临时车缴费出场）
                if (policyCct == null)
                {

                    var rsultMsg = GetPolicy(car, ref policyCct, ref payed, tempAcct);
                    if (!string.IsNullOrEmpty(rsultMsg))
                    {
                        rsList.Add(new Models.OrderDetailResult { OrderDetail_No = x.OrderDetail_No, OrderDetail_Status = payed, OrderDetail_Msg = rsultMsg });
                        return;
                    }
                }

                if (x.OrderDetail_EnterTime > car.Car_EndTime)
                {
                    if (policyCct != null)
                    {
                        x.OrderDetail_CarCardType = policyCct.CarCardType_No;
                        x.OrderDetail_Lock = cct.CarCardType_Type != 2 ? 1 : 0;
                        orderDetailList3.Add(x);
                    }
                    return;
                }
            }

            if (x.OrderDetail_EnterTime < car.Car_BeginTime)
            {
                if (x.OrderDetail_OutTime <= car.Car_BeginTime)//非登记时间段内(一段,按过期处理)
                {
                    if (policyCct == null) { orderDetailList2.Add(x); return; }//策划（忽略），按正常计费
                    Model.OrderDetail detail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//登记时间段前
                    detail.OrderDetail_CarCardType = policyCct.CarCardType_No;
                    detail.OrderDetail_Lock = cct.CarCardType_Type != 2 ? 1 : 0;
                    orderDetailList3.Add(detail);
                    return;
                }
                else
                {
                    if (policyCct != null)
                    {
                        Model.OrderDetail detail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//登记时间段前（按过期处理）
                        detail.OrderDetail_OutTime = car.Car_BeginTime;
                        detail.OrderDetail_StatusNo = EnumParkOrderStatus.Out;
                        detail.OrderDetail_CarCardType = policyCct.CarCardType_No;
                        detail.OrderDetail_Lock = cct.CarCardType_Type != 2 ? 1 : 0;
                        orderDetailList3.Add(detail);
                    }

                    if (x.OrderDetail_OutTime <= car.Car_EndTime || policyCct == null)//出场时间小于或等于月租结束时间（分两段停车计费）
                    {
                        Model.OrderDetail detail2 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//时间段内
                        if (policyCct != null) detail2.OrderDetail_EnterTime = car.Car_BeginTime;
                        detail2.OrderDetail_CarCardType = cct.CarCardType_No;
                        orderDetailList2.Add(detail2);
                    }
                    else//出场时间大于登记结束时间（分三段停车计费）
                    {
                        if (policyCct == null)
                        {
                            var rsultMsg = GetPolicy(car, ref policyCct, ref payed, tempAcct);
                            if (!string.IsNullOrEmpty(rsultMsg))
                            {
                                rsList.Add(new Models.OrderDetailResult { OrderDetail_No = x.OrderDetail_No, OrderDetail_Status = payed, OrderDetail_Msg = rsultMsg });
                                return;
                            }
                        }

                        Model.OrderDetail detail2 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//时间段内
                        detail2.OrderDetail_EnterTime = car.Car_BeginTime;
                        detail2.OrderDetail_OutTime = car.Car_EndTime;
                        detail2.OrderDetail_StatusNo = EnumParkOrderStatus.Out;
                        detail2.OrderDetail_CarCardType = cct.CarCardType_No;
                        orderDetailList2.Add(detail2);

                        if (policyCct != null)
                        {
                            Model.OrderDetail detail3 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//过期
                            detail3.OrderDetail_EnterTime = car.Car_EndTime;
                            detail3.OrderDetail_CarCardType = policyCct.CarCardType_No;
                            detail3.OrderDetail_Lock = cct.CarCardType_Type != 2 ? 1 : 0;
                            orderDetailList3.Add(detail3);
                        }
                    }
                }
            }
            else if (x.OrderDetail_EnterTime == car.Car_BeginTime)
            {
                if (x.OrderDetail_OutTime <= car.Car_EndTime) { x.OrderDetail_CarCardType = cct.CarCardType_No; orderDetailList2.Add(x); }//时间段内
                else//出场时间大于登记结束时间（分两段停车计费）
                {
                    if (policyCct == null)
                    {
                        var rsultMsg = GetPolicy(car, ref policyCct, ref payed, tempAcct);
                        if (!string.IsNullOrEmpty(rsultMsg))
                        {
                            rsList.Add(new Models.OrderDetailResult { OrderDetail_No = x.OrderDetail_No, OrderDetail_Status = payed, OrderDetail_Msg = rsultMsg });
                            return;
                        }
                    }

                    Model.OrderDetail detail2 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//时间段内
                    detail2.OrderDetail_OutTime = car.Car_EndTime;
                    detail2.OrderDetail_StatusNo = EnumParkOrderStatus.Out;
                    detail2.OrderDetail_CarCardType = cct.CarCardType_No;
                    orderDetailList2.Add(detail2);

                    if (policyCct != null)
                    {
                        Model.OrderDetail detail3 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//过期
                        detail3.OrderDetail_EnterTime = car.Car_EndTime;
                        detail3.OrderDetail_CarCardType = policyCct.CarCardType_No;
                        detail3.OrderDetail_Lock = cct.CarCardType_Type != 2 ? 1 : 0;
                        orderDetailList3.Add(detail3);
                    }
                }
            }
            else//进场时间大于登记开始时间
            {
                if (x.OrderDetail_EnterTime < car.Car_EndTime)
                {
                    if (x.OrderDetail_OutTime <= car.Car_EndTime)
                    {
                        x.OrderDetail_CarCardType = cct.CarCardType_No;
                        orderDetailList2.Add(x);//时间段内
                    }
                    else
                    {
                        if (policyCct == null)
                        {
                            var rsultMsg = GetPolicy(car, ref policyCct, ref payed, tempAcct);
                            if (!string.IsNullOrEmpty(rsultMsg))
                            {
                                rsList.Add(new Models.OrderDetailResult { OrderDetail_No = x.OrderDetail_No, OrderDetail_Status = payed, OrderDetail_Msg = rsultMsg });
                                return;
                            }
                        }

                        Model.OrderDetail detail2 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//时间段内
                        detail2.OrderDetail_OutTime = car.Car_EndTime;
                        detail2.OrderDetail_StatusNo = EnumParkOrderStatus.Out;
                        detail2.OrderDetail_CarCardType = cct.CarCardType_No;
                        orderDetailList2.Add(detail2);

                        if (policyCct != null)
                        {
                            Model.OrderDetail detail3 = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(x));//过期
                            detail3.OrderDetail_EnterTime = car.Car_EndTime;
                            detail3.OrderDetail_CarCardType = policyCct.CarCardType_No;
                            detail3.OrderDetail_Lock = cct.CarCardType_Type != 2 ? 1 : 0;
                            orderDetailList3.Add(detail3);
                        }
                    }
                }
                else
                {
                    orderDetailList2.Add(x);
                }
            }
        }

        /// <summary>
        /// 累计多个计费结果
        /// </summary>
        /// <param name="resultList"></param>
        /// <returns></returns>
        private static ChargeModels.PayResult GetPayResult(List<ChargeModels.PayResult> resultList)
        {

            ChargeModels.PayResult sumResult = new ChargeModels.PayResult();
            sumResult.chuzhiamount = 0;
            sumResult.couponamount = 0;
            sumResult.couponcount = 0;
            sumResult.orderamount = 0;
            sumResult.parktimemin = 0;
            sumResult.payed = 0;
            sumResult.payedamount = 0;

            foreach (var x in resultList)
            {
                if (x == null) continue;
                if (x.payed == 2) { sumResult.payed = 2; sumResult.payedmsg = x.payedmsg; return sumResult; }

                x.chuzhiamount = Utils.ObjectToDecimal(x.chuzhiamount, 0);
                x.couponamount = Utils.ObjectToDecimal(x.couponamount, 0);
                x.orderamount = Utils.ObjectToDecimal(x.orderamount, 0);
                x.payedamount = Utils.ObjectToDecimal(x.payedamount, 0);

                if (sumResult.payed == 0) sumResult.payed = Utils.ObjectToInt(x.payed, 0);

                sumResult.chuzhiamount += x.chuzhiamount;
                sumResult.couponamount += x.couponamount;
                sumResult.couponcount += Utils.ObjectToInt(x.couponcount, 0);
                sumResult.payedamount += x.payedamount;
                sumResult.parktimemin += Utils.ObjectToDouble(x.parktimemin, 0);
                sumResult.orderamount += x.orderamount;


                //计费金额累计
                if (x.list != null && sumResult.list != null)
                {
                    //foreach (var m in sumResult.list)
                    //{
                    //ChargeModels.PayDetail detail = x.list.Find(n => n.orderdetailno == m.orderdetailno);
                    //if (detail != null)
                    //{
                    //    if (m.calcbegintime > detail.calcbegintime) m.calcbegintime = detail.calcbegintime;
                    //    if (m.NextCyclePaidFees < detail.NextCyclePaidFees) m.NextCyclePaidFees = detail.NextCyclePaidFees;
                    //    if (m.nextcyclefreemin < detail.nextcyclefreemin) m.nextcyclefreemin = detail.nextcyclefreemin;
                    //    if (m.nextcycletime < detail.nextcycletime) m.nextcycletime = detail.nextcycletime;

                    //    m.payedamount = Utils.ObjectToDecimal(m.payedamount, 0);

                    //    m.parktimemin += x.parktimemin;
                    //    m.payedamount += x.payedamount;
                    //    m.payedmsg = x.payedmsg;
                    //    if (detail.payed == 2)
                    //    {
                    //        sumResult.payed = 2;
                    //        sumResult.payedmsg = detail.payedmsg;
                    //        return sumResult;
                    //    }
                    //    else
                    //    {
                    //        if (sumResult.payed == 0) sumResult.payed = Utils.ObjectToInt(detail.payed, 0);
                    //    }
                    //}
                    //};
                    sumResult.list.AddRange(x.list.Copy());
                }
                else
                {
                    if (sumResult.list == null) sumResult.list = x.list?.Copy();
                    if (x.payed == 2) sumResult.payed = 2;
                }

                //优惠券累计
                if (x.uselist != null && sumResult.uselist != null)
                {
                    //foreach (var m in sumResult.uselist)
                    //{
                    //    m.DiscountMoney = Utils.ObjectToDecimal(m.DiscountMoney, 0);

                    //    Model.CouponRecordUse detail = x.uselist.Find(n => n.CouponRecord_No == m.CouponRecord_No);
                    //    if (detail != null)
                    //    {
                    //        detail.DiscountMoney = Utils.ObjectToDecimal(detail.DiscountMoney, 0);

                    //        m.DiscountMoney += Utils.ObjectToDecimal(detail.DiscountMoney, 0);
                    //    }
                    //};
                    sumResult.uselist.AddRange(x.uselist.Copy());
                }
                else
                {
                    if (sumResult.uselist == null) sumResult.uselist = x.uselist;
                }


                //优惠券列表
                if (x.recordlist != null && sumResult.recordlist != null)
                {
                    foreach (var m in x.recordlist)
                    {
                        ChargeModels.CouponRecordIntExt detail = sumResult.recordlist.Find(n => n.CouponRecord_No == m.CouponRecord_No);
                        if (detail == null)
                        {
                            sumResult.recordlist.Add(m);
                        }
                    };
                }
                else
                {
                    if (sumResult.recordlist == null) sumResult.recordlist = x.recordlist?.Copy();
                }
            }

            if (sumResult.payed == 1)
            {
                if (sumResult.payedamount == 0) { sumResult.payed = 0; }
            }

            if (sumResult.payed == 0)
            {
                if (sumResult.payedamount > 0) { sumResult.payed = 1; }
            }

            sumResult.parktimemin = Math.Floor(sumResult.parktimemin);

            return sumResult;
        }
        /// <summary>
        /// 获取车场设置（通过车牌类型找到关联的过期处理方式：按指定类型临时车缴费出场）
        /// </summary>
        /// <param name="car"></param>
        /// <param name="policyCct"></param>
        /// <returns></returns>
        private static string GetPolicy(Model.Car car, ref Model.CarCardType policyCct, ref int? payed, Model.CarCardType tempAcct = null)
        {
            payed = 0;
            //车场设置（通过车牌类型找到关联的过期处理方式：按指定类型临时车缴费出场）
            Model.PolicyCarCard policy = BLL.PolicyCarCard.GetEntityByCarCard(car.Car_TypeNo);
            if (policy == null)
            {
                payed = 2;
                return "计费失败，未找到车场策略";
            }
            if (string.IsNullOrEmpty(policy.PolicyCarCard_ExpireHandle) || policy.PolicyCarCard_ExpireHandle == "0")
            {
                if (car.Car_Category == "3656" && tempAcct != null)
                {
                    policyCct = tempAcct;
                    return "";
                }
                else
                {
                    payed = 1;
                    return "车辆过期，无需缴费";
                }
            }
            //策略设置过期的使用的车牌颜色
            policyCct = BLL.CarCardType.GetEntity(policy.PolicyCarCard_ExpireHandle);
            if (policyCct == null)
            {
                payed = 2;
                return "计费失败，未找到车牌类型.";
            }

            return "";
        }

        /// <summary>
        /// 获取车场设置（通过车牌类型找到关联的过期处理方式：按指定类型临时车缴费出场）
        /// </summary>
        /// <param name="Car_TypeNo"></param>
        /// <param name="policyCct"></param>
        /// <returns></returns>
        private static string GetPolicy(string Car_TypeNo, ref Model.CarCardType policyCct, ref int? payed)
        {
            payed = 0;
            //车场设置（通过车牌类型找到关联的过期处理方式：按指定类型临时车缴费出场）
            Model.PolicyCarCard policy = BLL.PolicyCarCard.GetEntityByCarCard(Car_TypeNo);
            if (policy == null)
            {
                payed = 2;
                return "计费失败，未找到车场策略";
            }
            if (string.IsNullOrEmpty(policy.PolicyCarCard_ExpireHandle) || policy.PolicyCarCard_ExpireHandle == "0")
            {
                payed = 1;
                return "车辆过期，无需缴费";
            }
            //策略设置过期的使用的车牌颜色
            policyCct = BLL.CarCardType.GetEntity(policy.PolicyCarCard_ExpireHandle);
            if (policyCct == null)
            {
                payed = 2;
                return "计费失败，未找到车牌类型.";
            }

            return "";
        }

        /// <summary>
        /// 车辆信息为空，明细按过期车辆处理
        /// </summary>
        private static List<Models.OrderDetailResult> RetOrderDetail2(Model.ParkOrder parkOrder, List<Model.OrderDetail> orderDetailList, DateTime? outTime, ref Model.CarCardType policyCct, ref List<Model.OrderDetail> orderDetailList1, ref int? payed)
        {
            orderDetailList1 = new List<Model.OrderDetail>();
            List<Models.OrderDetailResult> rsList = new List<Models.OrderDetailResult>();
            foreach (var x in orderDetailList)
            {
                if (x.OrderDetail_OutTime == null) x.OrderDetail_OutTime = outTime;
                if (policyCct == null)
                {
                    var rsultMsg = GetPolicy(parkOrder.ParkOrder_CarCardType, ref policyCct, ref payed);
                    if (!string.IsNullOrEmpty(rsultMsg))
                    {
                        rsList.Add(new Models.OrderDetailResult { OrderDetail_No = x.OrderDetail_No, OrderDetail_Status = payed, OrderDetail_Msg = rsultMsg });
                        continue;
                    }
                }

                if (policyCct != null)
                {
                    x.OrderDetail_CarCardType = policyCct.CarCardType_No;
                    x.OrderDetail_Lock = 1;//将该字段用来标记“过期缴费”
                    orderDetailList1.Add(x);
                }
            }
            return rsList;
        }

        #endregion

        #region 计费结果数字保留两位小数

        private static void InterceptNumber(ChargeModels.PayResult pResult)
        {
            if (pResult != null && pResult.orderamount > 0)
            {
                decimal oldAmount = pResult.orderamount;
                pResult.orderamount = Convert.ToDecimal(ToResultString(pResult.orderamount, 2));
                decimal diff = oldAmount - pResult.orderamount;

                decimal oldAmount2 = pResult.payedamount;
                pResult.payedamount = Convert.ToDecimal(ToResultString(pResult.payedamount, 2));
                decimal diff2 = oldAmount2 - pResult.payedamount;

                if (diff > 0 || diff2 > 0)
                {
                    if (pResult.couponamount > 0)
                    {
                        pResult.list.ForEach(x =>
                        {
                            if (x.couponamount > 0) x.couponamount = Convert.ToDecimal(ToResultString(x.couponamount ?? 0, 2));
                            if (x.CalcResult?.Length > 1000) x.CalcResult = x.CalcResult.Substring(0, 1000);
                        });

                        var sumCouponAmount = pResult.list.Sum(x => x.couponamount ?? 0);
                        decimal diffCoupon = pResult.couponamount - sumCouponAmount;
                        if (diffCoupon > 0)
                        {
                            pResult.couponamount = sumCouponAmount;
                            pResult.payedamount = pResult.orderamount - pResult.couponamount - pResult.chuzhiamount;
                        }
                    }
                    else
                    {
                        pResult.payedamount = pResult.orderamount - pResult.couponamount - pResult.chuzhiamount;
                    }
                }
            }
        }
        /// <summary>
        /// decimal保留指定位数小数
        /// </summary>
        /// <param name="num">原始数量</param>
        /// <param name="scale">保留小数位数</param>
        /// <returns>截取指定小数位数后的数量字符串</returns>
        public static string ToResultString(decimal num, int scale)
        {
            string numToString = num.ToString();

            int index = numToString.IndexOf(".");
            int length = numToString.Length;

            if (index != -1)
            {
                return string.Format("{0}.{1}",
                    numToString.Substring(0, index),
                    numToString.Substring(index + 1, Math.Min(length - index - 1, scale)));
            }
            else
            {
                return num.ToString();
            }
        }

        #endregion

        #region 充电滞留处罚金额
        /// <summary>
        /// 充电滞留处罚金额
        /// </summary>
        /// <param name="carNo"></param>
        /// <param name="inTime"></param>
        /// <param name="outTime"></param>
        /// <param name="penaltyList"></param>
        /// <param name="money"></param>
        private static void GetDetentionPenalty(string carNo, DateTime? inTime, DateTime? outTime, out List<ChargeModels.DetentionPenalty> penaltyList, out decimal money)
        {
            penaltyList = null;
            money = 0;

            List<Model.DetentionPenalty> dpList = BLL.BaseBLL._GetAllEntity(new Model.DetentionPenalty(), "*", $"DetentionPenalty_CarNo='{carNo}' and DetentionPenalty_InTime>='{inTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' and DetentionPenalty_OutTime<='{outTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' and DetentionPenalty_PayStatus=0");
            if (dpList != null && dpList.Count > 0)
            {
                penaltyList = TyziTools.Json.ToObject<List<ChargeModels.DetentionPenalty>>(TyziTools.Json.ToString(dpList));
                money = dpList.Sum(x => Utils.ObjectToDecimal(x.DetentionPenalty_Money, 0));
            }
        }

        #endregion

        #region 计费入口

        /// <summary>
        /// 获取停车费用
        /// </summary>
        /// <param name="parkOrder">-停车订单</param>
        /// <param name="outTime">-车辆出场时间(或场内缴费时间)</param>
        /// <param name="car">-车辆信息</param>
        /// <param name="crList">-计费时已查询到可用的优惠券集合</param>
        /// <param name="useOrderCoupon">-是否到数据库查询优惠券信息合并【crList】一起使用，True-是(需要查询其它优惠)，False-否(不查询其它优惠)</param>
        /// <param name="carTypeNo">-计费时对车牌颜色做了修改，则传修改后的车牌颜色</param>
        /// <param name="carCarTypeNo">-计费时对车牌类型做了修改，则传修改后的车牌类型</param>
        /// <param name="owner">-车主信息</param>
        /// <param name="detailList">-停车明细集合信息</param>
        /// <returns>-PayResult-计费结果实体</returns>
        public static ChargeModels.PayResult GetChargeByCar(Model.ParkOrder parkOrder, DateTime? outTime, Model.Car car, List<Model.CouponRecordIntExt> crList = null, bool UseOrderCoupon = true,
            string carTypeNo = "", string carCarTypeNo = "", Model.Owner owner = null, List<Model.OrderDetail> detailList = null, bool isFllowCar = false, CalcBasicData baiseData = null
            , bool checkPayoder = true, List<Model.CarFees> fees = null)
        {
            ChargeModels.PayResult payResult = new ChargeModels.PayResult();
            payResult.payed = 2;
            payResult.payedmsg = "计费失败";
            payResult.payedamount = 0;

            try
            {
                Model.Owner owner1 = owner?.Copy();
                Model.Car car1 = car?.Copy();
                Model.CarCardType cct = null;
                bool UseChuzhiCharage = false;
                payResult = Calc.Main(ref parkOrder, outTime, ref car1, ref owner1, cct, ref UseChuzhiCharage, crList, UseOrderCoupon, carTypeNo, carCarTypeNo, detailList, isFllowCar, baiseData, checkPayoder);

                #region 【每月最高限额】处理超额逻辑
                if (baiseData?.checkPayMonthMax ?? true)
                {
                    if (!string.IsNullOrEmpty(parkOrder?.ParkOrder_CarCardType) && parkOrder?.ParkOrder_StatusNo != 204 && payResult?.payed != 2 && (payResult.payedamount + payResult.chuzhiamount + (payResult.cashrobotamount - payResult.zlamount)) > 0)
                    {
                        var cct1 = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, parkOrder?.ParkOrder_CarCardType);
                        if (cct1 != null)
                        {
                            var policycct = BLL.PolicyCarCard.GetEntityByCarCard(cct1.CarCardType_No);

                            // 启用了每月最高收费限额
                            if (policycct != null && policycct.PolicyCarCard_MonthMaxMoney > 0)
                            {


                                DateTime enterTime = parkOrder.ParkOrder_EnterTime.Value;
                                DateTime exitTime = outTime.Value;

                                // 获取 payResult.list 涉及的所有月份
                                var monthKeys = payResult.list
                                    .Select(x => Utils.ObjectToInt(x.starttime.Value.ToString("yyyyMM"), 0))
                                    .Distinct()
                                    .ToList();

                                // 批量查询多个月份的 CarFees 记录
                                List<Model.CarFees> carFeesRecords = null;

                                if (fees == null)
                                {
                                    carFeesRecords = BLL.BaseBLL._GetAllEntity(new Model.CarFees(),
                                        "CarFees_Money, CarFees_Month",
                                        $"CarFees_CarNo=@CarFees_CarNo AND CarFees_Month IN ({string.Join(",", monthKeys)})",
                                        new { CarFees_CarNo = parkOrder.ParkOrder_CarNo }
                                    );
                                }
                                else
                                {
                                    carFeesRecords = fees.Where(x => x.CarFees_CarNo == parkOrder.ParkOrder_CarNo && monthKeys.Contains(x.CarFees_Month.Value)).ToList();
                                }

                                // 转换为字典，方便后续查找
                                var carFeesDict = carFeesRecords.ToDictionary(
                                    x => x.CarFees_Month.Value,
                                    x => x.CarFees_Money.Value
                                );

                                // 获取所有停车明细按月分组
                                var monthDetails = payResult.list
                                    .GroupBy(x => Utils.ObjectToInt(x.starttime.Value.ToString("yyyyMM"), 0))
                                    .ToDictionary(g => g.Key, g => g.OrderBy(y => y.starttime).ToList());
                                decimal totalExcessAmount = 0;
                                decimal monthMaxLimit = policycct.PolicyCarCard_MonthMaxMoney.Value;

                                foreach (var month in monthDetails.Keys)
                                {
                                    var monthList = monthDetails[month];

                                    // 历史已缴费金额
                                    decimal monthTotalPaid = carFeesDict.ContainsKey(month) ? carFeesDict[month] : 0;
                                    decimal nowTotalPaid = monthList.Sum(x => x.payedamount ?? 0);

                                    monthList.First().CalcResult += $"<br/><br/>{month.ToString().Substring(0, 4)} 年 {month.ToString().Substring(4, 2)} 月，历史累计费用{monthTotalPaid}元，月限额{monthMaxLimit}元。";

                                    decimal originalMonthTotal = monthTotalPaid;

                                    decimal excessAmount = monthTotalPaid + nowTotalPaid - monthMaxLimit;
                                    decimal runningPaidSum = 0; // 累加已处理过的 payedamount

                                    if (excessAmount > 0)
                                    {
                                        decimal totalReduced = 0;
                                        decimal runningTotalPaid = originalMonthTotal;

                                        for (int i = 0; i < monthList.Count; i++)
                                        {
                                            var item = monthList[i];
                                            decimal oriPay = item.payedamount ?? 0;
                                            if (oriPay <= 0) continue;

                                            // 展示累计值 = 历史 + 当前原始金额 + 前面所有已计费金额
                                            decimal displayRunningTotal = originalMonthTotal + oriPay + runningPaidSum;

                                            decimal overLimit = displayRunningTotal - monthMaxLimit;

                                            if (overLimit >= oriPay)
                                            {
                                                item.carfeesamount += oriPay;
                                                item.payedamount = 0;
                                                item.NextCyclePaidFees = 0;
                                                item.CalcResult += $"<br/><br/>本月累计费用{displayRunningTotal}元，超出限额，减免{oriPay}元，实际计费金额为：0元。";
                                                totalReduced += oriPay;
                                            }
                                            else if (overLimit > 0)
                                            {
                                                decimal realPay = oriPay - overLimit;
                                                item.payedamount = realPay;
                                                item.carfeesamount += overLimit;
                                                item.NextCyclePaidFees -= overLimit;
                                                if (item.NextCyclePaidFees < 0) item.NextCyclePaidFees = 0;

                                                item.CalcResult += $"<br/><br/>本月累计费用{displayRunningTotal}元，超出限额，减免{overLimit}元，实际计费金额为：{realPay}元。";
                                                totalReduced += overLimit;
                                            }
                                            else
                                            {
                                                item.CalcResult += $"<br/><br/>本月累计费用{displayRunningTotal}元，未超出限额，实际计费金额为：{oriPay}元。";
                                            }

                                            // 更新累计实际支付金额（用于下一次计算）
                                            runningPaidSum += item.payedamount ?? 0;
                                        }


                                        totalExcessAmount += totalReduced;
                                    }
                                }


                                // 更新 payResult 的最终支付金额

                                payResult.orderamount -= totalExcessAmount;

                                if (carFeesDict.Count > 0)
                                {
                                    var last10Months = carFeesDict
                                                    .OrderByDescending(x => x.Key)          // 按月份倒序
                                                    .Take(5)                               // 取最近5个月
                                                    .OrderBy(x => x.Key)                    // 再按月份升序排回去
                                                    .ToList();

                                    var descriptions = last10Months
                                        .Select(x => $"[{x.Key.ToString().Substring(0, 4)}年{x.Key.ToString().Substring(4, 2)}月历史累计{x.Value}元]")
                                        .ToList();

                                    var finalDescription = string.Join(" ", descriptions); // 用逗号连接
                                    payResult.payedmsg = finalDescription;
                                }

                                decimal remainingAmount = totalExcessAmount;
                                if (payResult.payedamount >= remainingAmount)
                                {
                                    payResult.payedamount -= remainingAmount;
                                    payResult.carfeesamount = totalExcessAmount;
                                }
                                else
                                {
                                    payResult.carfeesamount = payResult.payedamount;
                                    remainingAmount -= payResult.payedamount;
                                    payResult.payedamount = 0;

                                    if (payResult.chuzhiamount >= remainingAmount)
                                    {
                                        payResult.chuzhiamount -= remainingAmount;
                                        payResult.carfeesamount += remainingAmount;
                                    }
                                    else
                                    {
                                        payResult.carfeesamount += payResult.chuzhiamount;
                                        payResult.chuzhiamount = 0;
                                    }
                                }

                                if (payResult.payedamount == 0 && payResult.chuzhiamount == 0)
                                {
                                    payResult.payed = 0;
                                }

                                #region ***同区域合并 vs 同区域按月拆分计费比较，保障不多收
                                if (payResult.payedamount > 0 && AppBasicCache.GetPolicyPark?.PolicyPark_MergeyHours == 1)
                                {
                                    var plist = payResult.list;
                                    var areaGroups = plist.GroupBy(x => new { x.areano, x.iscarexpire }).ToList();
                                    if (areaGroups.Any(g => g.Count() > 1))// 存在某一组有多个记录
                                    {
                                        baiseData = baiseData ?? new CalcBasicData();
                                        baiseData.checkPayMonthMax = false;//禁用每月限额干扰
                                        // 重新计算一次，按原 detailList 不做月拆分处理
                                        var payResultMerged = Calc.Main(
                                            ref parkOrder, outTime, ref car1, ref owner1, cct, ref UseChuzhiCharage, crList,
                                            UseOrderCoupon, carTypeNo, carCarTypeNo,
                                            detailList, isFllowCar, baiseData, checkPayoder
                                        );

                                        if (payResultMerged.payed == 1 && payResultMerged.payedamount > 0)
                                        {
                                            // 遍历每个区域，对比合并 vs 拆分结果
                                            foreach (var areaNo in areaGroups.Select(g => g.Key))
                                            {
                                                var originalItems = payResult.list.Where(x => x.areano == areaNo.areano && x.iscarexpire == areaNo.iscarexpire).ToList();
                                                var mergedItems = payResultMerged.list.Where(x => x.areano == areaNo.areano && x.iscarexpire == areaNo.iscarexpire).ToList();

                                                decimal originalAmount = originalItems.Sum(x => x.payedamount ?? 0);
                                                decimal mergedAmount = mergedItems.Sum(x => x.payedamount ?? 0);
                                                // 计算该月的超额部分
                                                decimal excessAmount = originalAmount - mergedAmount;

                                                // 如果拆分计费大于合并计费，则要对拆分计费扣减差额
                                                if (mergedAmount > 0 && excessAmount > 0)
                                                {
                                                    decimal totalReduced = 0;
                                                    decimal remainingMerged = mergedAmount;

                                                    foreach (var item in originalItems)
                                                    {
                                                        decimal oriPay = item.payedamount ?? 0;

                                                        if (remainingMerged <= 0)
                                                        {
                                                            // 全部超额，全部扣完
                                                            totalReduced += oriPay;
                                                            item.payedamount = 0;
                                                            item.CalcResult += $"检测到拆分金额超出合并计费，超额部分已全额减免{oriPay}元，实际应付：0元。";
                                                            continue;
                                                        }

                                                        if (oriPay <= remainingMerged)
                                                        {
                                                            // 保留原金额，不需要减
                                                            remainingMerged -= oriPay;
                                                        }
                                                        else
                                                        {
                                                            // 部分超额，扣除多余部分
                                                            decimal reduce = oriPay - remainingMerged;
                                                            item.payedamount = remainingMerged;
                                                            item.carfeesamount -= reduce;
                                                            if (item.carfeesamount < 0) item.carfeesamount = 0;
                                                            item.CalcResult += $"检测到拆分金额超出合并计费，减免超额{reduce}元，实际应付：{item.payedamount}元。";
                                                            totalReduced += reduce;
                                                            remainingMerged = 0;
                                                        }
                                                    }

                                                    // 更新 payResult 的最终支付金额
                                                    payResult.orderamount -= totalReduced;
                                                    if (payResult.orderamount < 0) payResult.orderamount = 0;

                                                    payResult.carfeesamount -= totalReduced;
                                                    if (payResult.carfeesamount < 0) payResult.carfeesamount = 0;

                                                    if (payResult.payedamount >= totalReduced)
                                                    {
                                                        payResult.payedamount -= totalReduced;
                                                    }
                                                    else
                                                    {
                                                        decimal reAmount = totalReduced - payResult.payedamount;
                                                        payResult.payedamount = 0;

                                                        if (payResult.chuzhiamount >= reAmount)
                                                        {
                                                            payResult.chuzhiamount -= reAmount;
                                                        }
                                                        else
                                                        {
                                                            payResult.chuzhiamount = 0;
                                                        }
                                                    }

                                                    if (payResult.payedamount == 0 && payResult.chuzhiamount == 0)
                                                    {
                                                        payResult.payed = 0;
                                                    }
                                                }


                                            }
                                        }
                                    }
                                }
                                #endregion
                            }
                        }

                    }
                }
                #endregion

                //储值车费用抵扣
                if (payResult.payed == 1 && !UseChuzhiCharage && !(baiseData?.cloudMqttOffline ?? false))
                {
                    payResult = BusiCarMode(payResult, car1, owner1, cct);
                }

                if (parkOrder != null && payResult != null && payResult.payed != 2 && parkOrder.ParkOrder_EnterTime != null && outTime != null)
                {
                    List<ChargeModels.DetentionPenalty> penaltyList = null;
                    decimal money = 0;
                    GetDetentionPenalty(parkOrder.ParkOrder_CarNo, parkOrder.ParkOrder_EnterTime, outTime, out penaltyList, out money);
                    payResult.penaltylist = penaltyList;
                    payResult.penaltyamount = money;
                    payResult.orderamount += money;
                    payResult.payedamount += money;
                    payResult.inTime = parkOrder.ParkOrder_EnterTime.Value;
                    payResult.orderNo = parkOrder.ParkOrder_No;
                }

                InterceptNumber(payResult);
                return payResult;
            }
            catch (Exception ex)
            {
                payResult.payedmsg = "计费失败，" + ex.Message;
                payResult.list = new List<PayDetail>() { new PayDetail() { payed = 2, CalcResult = "计费异常:" + ex.Message } };
            }
            return payResult;
        }

        /// <summary>
        /// 根据优惠设置，生成优惠券(优惠设置)
        /// </summary>
        public static List<Model.CouponRecordIntExt> GetOutCarCoupon(string parkNo, string parkOrderNo, string carNo, int? adminId)
        {
            List<Model.CouponRecordIntExt> couponList = new List<Model.CouponRecordIntExt>();
            //if (string.IsNullOrEmpty(parkNo) || string.IsNullOrEmpty(parkOrderNo)) return couponList;

            List<Model.ParkDiscountSet> list = BLL.ParkDiscountSet.GetAllEntity("*", $"ParkDiscountSet_ParkNo='{parkNo}' and ParkDiscountSet_Scene!=0");
            if (list != null)
            {
                foreach (var dissetModel in list)
                {
                    if (dissetModel.ParkDiscountSet_Type == Convert.ToString((int)Common.EnumCouponType.AppointHour))
                    {
                        if (dissetModel.ParkDiscountSet_AppointHour.Value.CompareTo(DateTimeHelper.GetNowTime()) < 0) { continue; }
                    }

                    //string fixNo = "";
                    //if (!string.IsNullOrEmpty(carNo))
                    //{
                    //    if (carNo.Length > 1) fixNo = carNo.Substring(1, carNo.Length - 1);
                    //    else { fixNo = carNo; }
                    //    fixNo = "-" + fixNo;
                    //}

                    Model.CouponRecordIntExt model = new Model.CouponRecordIntExt();
                    model.CouponRecord_ParkOrderNo = parkOrderNo;
                    model.CouponRecord_IssueCarNo = carNo;
                    model.CouponRecord_No = dissetModel.ParkDiscountSet_No;
                    model.CouponRecord_ParkNo = parkNo;
                    model.CouponRecord_Status = 0;
                    model.CouponRecord_CouponCode = dissetModel.ParkDiscountSet_Type;

                    if (model.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Money))
                    {
                        model.CouponRecord_Value = dissetModel.ParkDiscountSet_Amount;
                        model.CouponRecord_Name = $"减免{model.CouponRecord_Value}元[{dissetModel.ParkDiscountSet_Name}]";
                    }
                    else if (model.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Time))
                    {
                        model.CouponRecord_Value = dissetModel.ParkDiscountSet_Duration;
                        model.CouponRecord_Name = $"减免{model.CouponRecord_Value}分钟[{dissetModel.ParkDiscountSet_Name}]";
                    }
                    else if (model.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.Discount))
                    {
                        model.CouponRecord_Value = dissetModel.ParkDiscountSet_Ratio;
                        model.CouponRecord_Name = $"{model.CouponRecord_Value}折[{dissetModel.ParkDiscountSet_Name}]";
                    }
                    else if (model.CouponRecord_CouponCode == Convert.ToString((int)Common.EnumCouponType.AppointHour))
                    {
                        model.CouponRecord_StartTime = DateTimeHelper.GetNowTime();
                        model.CouponRecord_EndTime = dissetModel.ParkDiscountSet_AppointHour;
                        model.CouponRecord_Value = 0;
                        model.CouponRecord_Name = $"免费到{model.CouponRecord_EndTime}[{dissetModel.ParkDiscountSet_Name}]";
                    }

                    model.CouponRecord_AddTime = DateTimeHelper.GetNowTime();
                    model.CouponRecord_AddID = adminId;
                    model.CouponRecord_VaildTime = DateTimeHelper.GetNowTime().AddYears(100);
                    model.CouponRecord_OnLine = 0;
                    model.CouponRecord_Set = 1;
                    couponList.Add(model);
                }
            }
            return couponList;
        }

        /// <summary>
        /// 规则类型：0-标准版，1-旗舰版，2-银川收费
        /// </summary>
        /// <returns></returns>
        private static int GetChargeRulesType()
        {
            //旗舰版和标准版收费切换
            var ruleTypes = AppBasicCache.ReadWriteCache && AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache == 1 ? AppBasicCache.GetAllChargeRules.Values.FirstOrDefault() : BLL.ChargeRules._GetEntityByWhere(new Model.ChargeRules(), "*", $" 1=1 LIMIT 1");
            if (ruleTypes != null)
            {
                if (ruleTypes.ChargeRules_Type == null) return 1;
                return ruleTypes.ChargeRules_Type.Value;
            }
            return 1;
        }

        #endregion

        #region 标准版计费

        /// <summary>
        /// 多个区域合并
        /// </summary>
        /// <param name="OrderDetailList"></param>
        /// <returns></returns>
        public static List<Model.OrderDetail> GetMergeOrderDetailByParkArea(List<Model.OrderDetail> OrderDetailList)
        {
            if (OrderDetailList.Count == 1) return OrderDetailList;
            var areaNoGroup = OrderDetailList.Select(x => x.OrderDetail_ParkAreaNo).Distinct().ToList();
            var nowTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd");

            List<Model.ChargeRules> tempRules = new List<Model.ChargeRules>();
            List<string> haveRuleOrderDetails = new List<string>();
            foreach (var tempOrder in OrderDetailList)
            {
                Model.ChargeRules tempRule = GetRulesBySimple(tempOrder, false, nowTime);
                if (tempRule != null) { tempRules.Add(tempRule); haveRuleOrderDetails.Add(tempOrder.OrderDetail_No); }
            }
            if (tempRules.Count == 0) { return OrderDetailList; }
            var ruleNoGroup = tempRules.Select(x => x.ChargeRules_No).Distinct().ToList();

            List<Model.OrderDetail> ordersList = new List<Model.OrderDetail>();
            if (ruleNoGroup.Count > 1)//存在多个收费规则
            {
                foreach (var areano in areaNoGroup)
                {
                    //获取该区域的停车记录详情订单
                    List<Model.OrderDetail> areaOrderDetailList = OrderDetailList.Where(x => x.OrderDetail_ParkAreaNo == areano && haveRuleOrderDetails.Contains(x.OrderDetail_No)).OrderBy(x => x.OrderDetail_EnterTime.Value).ToList();
                    if (areaOrderDetailList.Count > 0)
                    {
                        Model.OrderDetail tempDetail = null;
                        for (int i = 0; i < areaOrderDetailList.Count; i++)
                        {
                            if (tempDetail == null)
                            {
                                tempDetail = areaOrderDetailList[i];
                                continue;
                            }

                            if (tempDetail.OrderDetail_ParkAreaNo == areaOrderDetailList[i].OrderDetail_ParkAreaNo)
                            {
                                //出场时间把其他时间段的停车时长加上
                                tempDetail.OrderDetail_OutTime = tempDetail.OrderDetail_OutTime.Value.AddSeconds((areaOrderDetailList[i].OrderDetail_OutTime.Value - areaOrderDetailList[i].OrderDetail_EnterTime.Value).TotalSeconds);
                            }
                        }
                        ordersList.Add(tempDetail);
                    }
                }
            }
            else
            {
                //所有都是一个收费规则
                List<Model.OrderDetail> areaOrderDetailList = OrderDetailList.OrderBy(x => x.OrderDetail_EnterTime.Value).ToList();
                if (areaOrderDetailList.Count > 0)
                {
                    Model.OrderDetail tempDetail = null;
                    for (int i = 0; i < areaOrderDetailList.Count; i++)
                    {
                        //过滤不计费的区域
                        if (!haveRuleOrderDetails.Contains(areaOrderDetailList[i].OrderDetail_No)) continue;

                        if (tempDetail == null)
                        {
                            tempDetail = areaOrderDetailList[i];
                            continue;
                        }

                        //出场时间把其他时间段的停车时长加上
                        tempDetail.OrderDetail_OutTime = tempDetail.OrderDetail_OutTime.Value.AddSeconds((areaOrderDetailList[i].OrderDetail_OutTime.Value - areaOrderDetailList[i].OrderDetail_EnterTime.Value).TotalSeconds);

                    }
                    ordersList.Add(tempDetail);
                }
            }
            return ordersList;
        }

        private static List<ChargeModels.PayDetail> GetChargeBySimple(RuleRunData ruleRunData, Model.ParkOrder parkOrder, List<Model.OrderDetail> orderDetailList, string Car_No, string CarTypeNo, string CarCardTypeNo, DateTime? outTime, Model.Car car = null, DateTime? PayOrder_PayedTime = null, Model.Owner owner = null, List<Model.CarCardType> cctList = null, bool charginPile = false, bool havaMinCoupon = false)
        {
            if (cctList == null) cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_Type,CarCardType_IsMoreCar", "") ?? new List<Model.CarCardType>();

            var nowTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd");
            List<ChargeModels.PayDetail> payDetailList = new List<ChargeModels.PayDetail>();
            orderDetailList = orderDetailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
            var orderCount = orderDetailList.Count;

            var parkorderType = cctList.Find(x => x.CarCardType_No == parkOrder.ParkOrder_CarCardType);

            //超时计费：true--属于超时收费，false--不属于超时
            var isOverTime = false;
            //Model.PayOrder payDeatail = null;//上一次的支付订单

            var settleList = orderDetailList.FindAll(x => x.OrderDetail_IsSettle == 1).OrderBy(x => x.OrderDetail_EnterTime).ToList();
            if (settleList.Count > 0)
            {
                isOverTime = true;
                //if (PayOrder_PayedTime == null) PayOrder_PayedTime = orderDetailList.FindAll(x => x.OrderDetail_IsSettle == 1).OrderBy(x => x.OrderDetail_EnterTime).ToList().LastOrDefault()?.OrderDetail_OutTime;
            }
            //if (isOverTime)//属于超时收费，则需要读取关联的上一次支付的订单历史，获取支付时间
            //{
            //    payDeatail = BLL.PayOrder._GetEntityByWhere(new Model.PayOrder(), "PayOrder_ID,PayOrder_Time", $"PayOrder_ParkOrderNo='{settleList[0].OrderDetail_ParkOrderNo}' order by PayOrder_Time desc limit 1");
            //    //if (payDeatail == null) isOverTime = false;
            //}


            BillingBlackBox.Models.SectionParam param = new BillingBlackBox.Models.SectionParam();

            //超时计费
            if (isOverTime && PayOrder_PayedTime.HasValue)
            {
                //判断超时停车时长是否大于0：出场时间-支付时间
                var totalMin = (outTime - PayOrder_PayedTime).Value.TotalMinutes;
                if (totalMin <= 0) return null;//无需缴费

                //判断停车时长是否大于支付滞留时间
                bool isPay = false;
                foreach (var tempOrder in orderDetailList)
                {
                    Model.ChargeRules crModel = GetRulesBySimple(tempOrder, isOverTime, nowTime);
                    if (crModel != null)
                    {
                        string calcJson = System.Web.HttpUtility.UrlDecode(crModel.ChargeRules_JsonData);
                        carparking.BillingBlackBox.Models.ChargeRulesLogic crlModel = JsonConvert.DeserializeObject<carparking.BillingBlackBox.Models.ChargeRulesLogic>(calcJson);

                        //停车时间超过场内滞留时间，需要计费
                        if (totalMin > crlModel.Logic_PayLeaveMin)
                        {
                            isPay = true;
                            break;
                        }
                        else
                        {
                            ChargeModels.PayDetail ret = CreatePayDetail(0, "支付滞留时间内无需缴费", 0, tempOrder.OrderDetail_ParkAreaNo, tempOrder.OrderDetail_ParkOrderNo, tempOrder.OrderDetail_No, tempOrder.OrderDetail_ID,
                        null, null, tempOrder.OrderDetail_OutTime ?? outTime, tempOrder.OrderDetail_EnterTime > PayOrder_PayedTime ? tempOrder.OrderDetail_EnterTime : PayOrder_PayedTime, null, null, null, tempOrder.OrderDetail_ParkAreaName);
                            ret.CalcResult = tempOrder.orderdetail_IsCharge == 1 ? "" : (tempOrder.orderdetail_IsCharge == 2 ? "智能升降" : "");
                            payDetailList.Add(ret);
                        }
                    }
                }

                if (!isPay) return payDetailList;//无需缴费
            }

            //非超时计费，过滤已结算的明细订单
            if (!isOverTime)
                orderDetailList = orderDetailList.Where(x => x.OrderDetail_IsSettle == 0).ToList();

            var areaNoGroup = orderDetailList.Select(x => x.OrderDetail_ParkAreaNo).Distinct().ToList();


            //超时计费,不累加已使用的免费分钟
            if (!isOverTime)
            {
                orderDetailList.ForEach(item =>
                {
                    param.writeParam.SetMin(ref param, item.OrderDetail_ParkAreaNo, item.OrderDetail_No, Utils.ObjectToInt(item.Orderdetail_UseFreeMin, 0));
                });
            }

            param.readParam.ParkOrder_TotalPayed = parkOrder.ParkOrder_TotalPayed ?? 0; //parkOrder.ParkOrder_TotalPayed ?? 0;
            param.readParam.ParkOrder_StartTime = parkOrder.ParkOrder_EnterTime;
            param.readParam.ParkOrder_EndTime = parkOrder.ParkOrder_OutTime;

            //foreach (var areano in areaNoGroup)
            //{
            //获取该区域的停车记录详情订单
            //List<Model.OrderDetail> areaOrderDetailList = orderDetailList.Where(x => x.OrderDetail_ParkAreaNo == areano).ToList();
            if (orderDetailList != null && orderDetailList.Count > 0)
            {
                //是否使用首计时（True-已使用首计时，False-未使用首计时）
                bool isUseFirstTime = false;
                var tempOrderDetailList = GetMergeOrderDetailByParkArea(orderDetailList);
                //循环结算停车订单
                for (var i = 0; i < tempOrderDetailList.Count; i++)
                {
                    var temporder = tempOrderDetailList[i];

                    DateTime? nextCycleTime = null; //下一次停车，周期生效时间
                    decimal? NextCyclePaidFees = null;//下一次停车，周期已累积支付总金额
                    DateTime? NextHoursTime = null;
                    string NextHoursContent = null;
                    int? NextCycleFreeMin = null;
                    CycleParam orderDetailCycle = null;

                    param.OrderDetail_No = temporder.OrderDetail_No;
                    param.Area_No = temporder.OrderDetail_ParkAreaNo;
                    param.Area_Name = temporder.OrderDetail_ParkAreaName;
                    param.readParam.IsCarExpire = temporder.OrderDetail_Lock == 0 ? false : true;
                    param.readParam.IsOverTime = isOverTime;

                    var detailOutTime = temporder.OrderDetail_OutTime;
                    if (temporder.OrderDetail_StatusNo != EnumParkOrderStatus.In || i != orderDetailList.Count - 1)
                    {
                        detailOutTime = temporder.OrderDetail_OutTime == null ? outTime : temporder.OrderDetail_OutTime;
                    }
                    temporder.OrderDetail_OutTime = detailOutTime;

                    var carcardType = cctList.Find(x => x.CarCardType_No == temporder.OrderDetail_CarCardType);
                    if (carcardType != null && carcardType.CarCardType_IsMoreCar == 1 && carcardType.CarCardType_Type != 2 && parkorderType?.CarCardType_Type != 2)
                    {
                        string carSpaceMsg = "";
                        if (car != null)
                        {
                            if (parkOrder.ParkOrder_IsLift == 0 || parkOrder.ParkOrder_IsLift == 2)
                            {
                                carSpaceMsg = "多位多车，";
                            }
                            else
                            {
                                carSpaceMsg = "";
                            }
                        }

                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"{carSpaceMsg}{(temporder.orderdetail_IsCharge == 2 ? "智能升降，" : temporder.orderdetail_IsCharge == 1 ? "免费，" : "")}");
                    }

                    //超时计费,修改明细订单的进场时间，计算超时的部分费用
                    if (isOverTime && temporder.OrderDetail_IsSettle == 1)
                    {
                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"超时计费（{PayOrder_PayedTime?.ToString("yyyy-MM-dd HH:mm:ss")}已支付一次停车费用），");
                        if (PayOrder_PayedTime != null)
                        {
                            if (temporder.OrderDetail_OutTime > PayOrder_PayedTime && temporder.OrderDetail_OutTime >= temporder.OrderDetail_EnterTime)
                            {
                                if (PayOrder_PayedTime > temporder.OrderDetail_EnterTime) temporder.OrderDetail_EnterTime = PayOrder_PayedTime;
                            }
                            else
                            {
                                continue;//不符合条件的停车明细，直接跳过，不参与计费
                            }
                        }
                    }

                    //查找计费规则
                    Model.ChargeRules crModel = GetRulesBySimple(temporder, isOverTime, nowTime);
                    if (crModel == null)
                    {
                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"未设计费规则，不收取费用。");
                        //不需要支付
                        ChargeModels.PayDetail ret = CreatePayDetail(0, "未设计费规则，不收取费用", 0, temporder.OrderDetail_ParkAreaNo, temporder.OrderDetail_ParkOrderNo, temporder.OrderDetail_No, temporder.OrderDetail_ID,
                            nextCycleTime, NextCyclePaidFees, detailOutTime, temporder.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, temporder.OrderDetail_ParkAreaName);
                        payDetailList.Add(ret);
                        continue;
                    }

                    TimeSpan ts = temporder.OrderDetail_EnterTime.Value.Subtract(temporder.OrderDetail_OutTime.Value);
                    int sumMin = (int)Math.Abs(Math.Floor(ts.TotalMinutes));
                    if (ruleRunData.dicParkMin.ContainsKey(crModel.ChargeRules_No))
                    {
                        ruleRunData.dicParkMin[crModel.ChargeRules_No] += sumMin;
                    }
                    else
                    {
                        ruleRunData.dicParkMin.Add(crModel.ChargeRules_No, sumMin);
                    }

                    string calcJson = System.Web.HttpUtility.UrlDecode(crModel.ChargeRules_JsonData);
                    if (string.IsNullOrWhiteSpace(calcJson))
                    {
                        //不需要支付
                        ChargeModels.PayDetail ret = CreatePayDetail(2, "计费参数错误", 0, temporder.OrderDetail_ParkAreaNo, temporder.OrderDetail_ParkOrderNo, temporder.OrderDetail_No, temporder.OrderDetail_ID,
                            nextCycleTime, NextCyclePaidFees, detailOutTime, temporder.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, temporder.OrderDetail_ParkAreaName);
                        payDetailList.Add(ret);
                        break;
                    }

                    if (temporder.orderdetail_IsCharge == 1)//一位多车免费
                    {
                        //不需要支付
                        ChargeModels.PayDetail ret = CreatePayDetail(0, "多车位多车免费", 0, temporder.OrderDetail_ParkAreaNo, temporder.OrderDetail_ParkOrderNo, temporder.OrderDetail_No, temporder.OrderDetail_ID,
                             nextCycleTime, NextCyclePaidFees, detailOutTime, temporder.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, temporder.OrderDetail_ParkAreaName);
                        payDetailList.Add(ret);
                        continue;
                    }

                    carparking.BillingBlackBox.Models.ChargeRulesLogic crlModel = JsonConvert.DeserializeObject<carparking.BillingBlackBox.Models.ChargeRulesLogic>(calcJson);

                    //根据时段设置的开始结束时间,对停车的时间进行拆分重组 
                    if (crlModel.Logic_IsDiffTime == 1 && crlModel.Logic_IsDiffHoliday != 1 && crlModel.Logic_MergeSectionTime == 1
                        && crlModel.Logic_Sections != null && crlModel.Logic_Sections.Count > 0)
                    {
                        var sameAreaDetailList = orderDetailList.Where(x => x.OrderDetail_ParkAreaNo == temporder.OrderDetail_ParkAreaNo).ToList();
                        var areaIndex = sameAreaDetailList.IndexOf(temporder);
                        if (areaIndex + 1 < sameAreaDetailList.Count)
                        {
                            List<BillingBlackBox.Models.ChargeRulesSection> currLogicSection = crlModel.Logic_Sections;

                            #region 区域获取计费规则时段
                            DateTime? startTime = temporder.OrderDetail_OutTime.Value.AddMinutes(-1);//时段开始时间
                            DateTime? endTime = temporder.OrderDetail_OutTime;//时段结束时间

                            DateTime? sectionStartTime = null;//最后一个时段的开始时间（增加日期，时分未做修改）
                            DateTime? sectionEndTime = null;//最后一个时段的结束时间

                            DateTime? ruleSectionStartTime = null;//计费规则时段开始时间（未做时间修改）
                            DateTime? ruleSectionEndTime = null;//计费规则时段结束时间
                            BillingBlackBox.CalcLogic.CheckHoursTime(currLogicSection, ref startTime, ref endTime, ref sectionStartTime, ref sectionEndTime, ref ruleSectionStartTime, ref ruleSectionEndTime);
                            #endregion

                            if (sectionEndTime != endTime)//停车结束时间点 和 计费规则时间段不一致，说明需要合并跨区域的停车时长
                            {
                                #region 区域获取计费规则时段
                                DateTime? startTime2 = sameAreaDetailList[areaIndex + 1].OrderDetail_EnterTime;//时段开始时间
                                DateTime? endTime2 = sameAreaDetailList[areaIndex + 1].OrderDetail_OutTime;//时段结束时间

                                DateTime? sectionStartTime2 = null;//第一个时段的开始时间（增加日期，时分未做修改）
                                DateTime? sectionEndTime2 = null;//第一个时段的结束时间

                                DateTime? ruleSectionStartTime2 = null;//计费规则时段开始时间（未做时间修改）
                                DateTime? ruleSectionEndTime2 = null;//计费规则时段结束时间

                                BillingBlackBox.CalcLogic.CheckHoursTime(currLogicSection, ref startTime2, ref endTime2, ref sectionStartTime2, ref sectionEndTime2, ref ruleSectionStartTime2, ref ruleSectionEndTime2, false);
                                #endregion

                                //判断停车时间点不是从规则时段开始时间开始的 并且 属于同一个规则时段，说明和跨区域的上一停车时间段存在可合并计费的时间
                                if (sectionStartTime2.Value.ToString("HH:mm") != ruleSectionStartTime2.Value.ToString("HH:mm")
                                    && ruleSectionStartTime.Value.ToString("HH:mm") == ruleSectionStartTime2.Value.ToString("HH:mm"))
                                {
                                    var nextEnterTime = sameAreaDetailList[areaIndex + 1].OrderDetail_EnterTime.Value;
                                    var margeTime = sectionEndTime2;
                                    if (margeTime > sameAreaDetailList[areaIndex + 1].OrderDetail_OutTime) margeTime = sameAreaDetailList[areaIndex + 1].OrderDetail_OutTime.Value;
                                    sameAreaDetailList[areaIndex + 1].OrderDetail_EnterTime = margeTime;

                                    var mins = (int)Math.Floor((margeTime - nextEnterTime).Value.TotalMinutes);
                                    if (mins > 0)
                                    {
                                        BillingBlackBox.CalcLogic.setCalcContent(ref param, $"跨区域合并同一时段{mins}分钟停车时长【{temporder.OrderDetail_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}延长至{temporder.OrderDetail_OutTime.Value.AddMinutes(mins).ToString("yyyy-MM-dd HH:mm:ss")}】。");
                                        detailOutTime = temporder.OrderDetail_OutTime = temporder.OrderDetail_OutTime.Value.AddMinutes(mins);
                                    }
                                }
                            }
                        }
                    }

                    //周期参数赋值
                    if (crlModel.Cycle_IsCharge == 1)
                    {
                        if (ruleRunData.dicRuleCycle.ContainsKey(crModel.ChargeRules_No) && crlModel.Cycle_ReadStart != 1)
                        {
                            orderDetailCycle = ruleRunData.dicRuleCycle[crModel.ChargeRules_No];
                            nextCycleTime = orderDetailCycle?.Cycle_EndTime;
                            NextCyclePaidFees = orderDetailCycle?.Cycle_SumMoney;
                            NextCycleFreeMin = orderDetailCycle?.Cycle_SumFreeMin;
                        }

                        if (!ruleRunData.dicRuleParam.ContainsKey(crModel.ChargeRules_No))
                        {
                            ruleRunData.dicRuleParam.Add(crModel.ChargeRules_No, new BillingBlackBox.Models.ChargeRulesCycle()
                            {
                                Cycle_Unit = crlModel.Cycle_Unit,
                                Cycle_UnitMaxCharge = crlModel.Cycle_UnitMaxCharge,
                                Cycle_HoursMaxAmount = crlModel.Cycle_HoursMaxAmount
                            });
                        }

                        //BillingBlackBox.CalcLogic.setCalcContent(ref param, $"获取周期时间：{nextCycleTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}，");
                    }

                    NextHoursContent = param.writeParam.Hours_StrSumTimeMoney;

                    if (crlModel.Cycle_IsCharge == 1 && !ruleRunData.dicRuleCycle.ContainsKey(crModel.ChargeRules_No) && crlModel.Cycle_ReadStart != 1)
                    {
                        var areaList = TyziTools.Json.ToObject<List<string>>(crModel.ChargeRules_ParkAreaNo);
                        //获取上一次的停车区域停车明细订单
                        Model.OrderDetail orderDetail = BLL.OrderDetail._GetEntityByWhere(new Model.OrderDetail(), "OrderDetail_ID,Orderdetail_CycleMoney,OrderDetail_NextCycleTime,orderdetail_CycleFreeMin",
                            $"OrderDetail_ParkNo='{temporder.OrderDetail_ParkNo}' and OrderDetail_CarNo='{Car_No}' " +  //and orderdetail_CarType='{order.OrderDetail_CarType}' and orderdetail_CarCardType='{order.OrderDetail_CarCardType}'
                            $" and OrderDetail_IsSettle=1 " +
                            $" and orderdetail_ParkAreaNo in ('{string.Join("','", areaList)}') " +
                            $" and orderdetail_EnterTime<'{temporder.OrderDetail_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' " +
                            $" and OrderDetail_NextCycleTime is not null " +
                            $" order by orderdetail_ID desc limit 1");

                        orderDetailCycle = new CycleParam();
                        orderDetailCycle.Cycle_AreaNo = temporder.OrderDetail_ParkAreaNo;
                        orderDetailCycle.Cycle_EndTime = orderDetail?.OrderDetail_NextCycleTime;
                        orderDetailCycle.Cycle_SumMoney = orderDetail?.Orderdetail_CycleMoney;
                        orderDetailCycle.Cycle_SumFreeMin = orderDetail?.Orderdetail_CycleFreeMin;
                        ruleRunData.dicRuleCycle.Add(crModel.ChargeRules_No, orderDetailCycle);

                        nextCycleTime = orderDetailCycle?.Cycle_EndTime;
                        NextCyclePaidFees = orderDetailCycle?.Cycle_SumMoney;
                        NextCycleFreeMin = orderDetailCycle?.Cycle_SumFreeMin;

                        if (!param.readParam.FisrtCycle.ContainsKey(crModel.ChargeRules_No) && nextCycleTime != null)
                        {
                            param.readParam.FisrtCycle.Add(crModel.ChargeRules_No, orderDetailCycle);
                        }
                        //BillingBlackBox.CalcLogic.setCalcContent(ref param, $"获取周期时间：{nextCycleTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "无"}，");
                    }

                    if (!ruleRunData.dicHours.ContainsKey(crModel.ChargeRules_No))
                    {
                        var areaList = TyziTools.Json.ToObject<List<string>>(crModel.ChargeRules_ParkAreaNo);
                        //获取上一次的停车区域停车明细订单(时段累积金额)
                        Model.OrderDetail orderDetailForHoursTime = BLL.OrderDetail._GetEntityByWhere(new Model.OrderDetail(), "OrderDetail_ID,orderdetail_HoursBeginTime,orderdetail_HoursContent",
                            $"OrderDetail_ParkNo='{temporder.OrderDetail_ParkNo}' " +
                            $" and OrderDetail_CarNo='{Car_No}' " +
                            $" and orderdetail_CarType='{temporder.OrderDetail_CarType}'" +
                            $" and orderdetail_CarCardType='{temporder.OrderDetail_CarCardType}' " +
                            $" and OrderDetail_IsSettle=1 " +
                            $" and orderdetail_ParkAreaNo in ('{string.Join("','", areaList)}') " +
                            $" and orderdetail_EnterTime<'{temporder.OrderDetail_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' " +
                            $" and orderdetail_HoursBeginTime is not null " +
                            $" order by orderdetail_HoursBeginTime desc ,orderdetail_ID desc limit 1");
                        if (orderDetailForHoursTime != null && !string.IsNullOrEmpty(orderDetailForHoursTime.orderdetail_HoursContent))
                        {
                            var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(orderDetailForHoursTime.orderdetail_HoursContent);
                            if (currentItem != null && currentItem.Count > 0)
                            {
                                if (param.writeParam.Hours_SumTimeMoney != null && param.writeParam.Hours_SumTimeMoney.Count > 0)
                                {
                                    currentItem.ForEach(x =>
                                    {
                                        var searchItem = param.writeParam.Hours_SumTimeMoney.Where(m => m.SectionStartTime == x.SectionStartTime && m.SectionEndTime == x.SectionEndTime).ToList();
                                        if (searchItem.Count == 0)
                                        {
                                            param.writeParam.Hours_SumTimeMoney.Add(x);
                                        }
                                    });
                                    param.writeParam.Hours_StrSumTimeMoney = NextHoursContent = TyziTools.Json.ToString(param.writeParam.Hours_SumTimeMoney);
                                }
                                else
                                {
                                    param.writeParam.Hours_StrSumTimeMoney = NextHoursContent = orderDetailForHoursTime?.orderdetail_HoursContent;
                                }
                            }
                        }
                        ruleRunData.dicHours.Add(crModel.ChargeRules_No, NextHoursContent);
                    }

                    //初始化
                    param.Section_CurrentUseFreeTimeMin = 0;
                    param.Section_HavaMinCoupon = havaMinCoupon;

                    //跨区域仅使用一次首计时【0-禁用,1-启用】
                    if (crlModel.Logic_IsOnlyFirstTime != 1) isUseFirstTime = false;//是否使用首计时（True-已使用首计时，False-未使用首计时）

                    //使用过优惠券的就去掉首计时
                    if (charginPile) { isUseFirstTime = true; }

                    if (isOverTime) NextCycleFreeMin = 0;//超时计费，周期累计的免费分钟为0

                    //开始计费
                    PayDetail orderPayMoney = BenginChargeRuleBySimple(ref param, crlModel, temporder.OrderDetail_EnterTime, detailOutTime, ref isUseFirstTime, ref NextCyclePaidFees, ref nextCycleTime, NextHoursTime, NextHoursContent, NextCycleFreeMin);//得到计费金额、下次周期生效时间

                    if (orderPayMoney != null)
                    {
                        orderPayMoney.starttime = temporder.OrderDetail_EnterTime;
                        orderPayMoney.endtime = detailOutTime;
                        orderPayMoney.areano = temporder.OrderDetail_ParkAreaNo;
                        orderPayMoney.areaname = temporder.OrderDetail_ParkAreaName;
                        orderPayMoney.parkorderno = temporder.OrderDetail_ParkOrderNo;
                        orderPayMoney.orderdetailno = temporder.OrderDetail_No;
                        orderPayMoney.orderdetailid = temporder.OrderDetail_ID;
                        orderPayMoney.iscarexpire = param.readParam.IsCarExpire;
                        orderPayMoney.isovertime = param.readParam.IsOverTime;
                        orderPayMoney.parktimemin = (detailOutTime - temporder.OrderDetail_EnterTime).Value.TotalMinutes;

                        Model.CarCardType cct = cctList.Where(x => x.CarCardType_No == temporder.OrderDetail_CarCardType).FirstOrDefault();
                        if (cct != null)
                        {
                            orderPayMoney.payedmsg = $"按 {cct.CarCardType_Name} 收费";
                        }
                        //支付
                        orderPayMoney.currentfreemin = param.Section_CurrentUseFreeTimeMin;
                        orderPayMoney.payedamount = orderPayMoney.payedamount ?? 0;
                        orderPayMoney.nexthourstime = detailOutTime;
                        orderPayMoney.CalcResult = param.CalcResult != null ? string.Join("", param.CalcResult.Select(x => x.Remark)) : "";
                        payDetailList.Add(orderPayMoney);
                        param.CalcResult.Clear();

                        if (ruleRunData.dicRuleCycle.ContainsKey(crModel.ChargeRules_No)) { ruleRunData.dicRuleCycle.Remove(crModel.ChargeRules_No); }
                        CycleParam cycleParam = new CycleParam();
                        cycleParam.Cycle_AreaNo = temporder.OrderDetail_ParkAreaNo;
                        cycleParam.Cycle_IsCharge = param.readParam.Cycle.Cycle_IsCharge;
                        cycleParam.Cycle_SumFreeMin = orderPayMoney.nextcyclefreemin;
                        cycleParam.Cycle_SumMoney = orderPayMoney.NextCyclePaidFees;
                        cycleParam.Cycle_EndTime = orderPayMoney.nextcycletime;
                        ruleRunData.dicRuleCycle.Add(crModel.ChargeRules_No, cycleParam);

                        if (orderPayMoney.payed == 1)
                        {
                            if (ruleRunData.dicRuleCharge.ContainsKey(crModel.ChargeRules_No))
                                ruleRunData.dicRuleCharge[crModel.ChargeRules_No] += orderPayMoney.payedamount ?? 0;
                            else
                                ruleRunData.dicRuleCharge.Add(crModel.ChargeRules_No, orderPayMoney.payedamount ?? 0);
                        }

                        //处理时段累计金额
                        if (!string.IsNullOrEmpty(orderPayMoney.nexthourscontent))
                        {
                            param.writeParam.Hours_SumTimeMoney = TyziTools.Json.ToObject<List<CalcHoursResult>>(orderPayMoney.nexthourscontent);
                            param.writeParam.Hours_StrSumTimeMoney = orderPayMoney.nexthourscontent;
                            payDetailList.ForEach(xDetail =>
                            {
                                //处理非当前停车明细的又同时段的时段计费累计：以当前停车明细的时段累计为准
                                if (!string.IsNullOrEmpty(xDetail.nexthourscontent) && xDetail.orderdetailno != param.OrderDetail_No)
                                {
                                    var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(xDetail.nexthourscontent);
                                    if (currentItem != null && currentItem.Count > 0)
                                    {
                                        param.writeParam.Hours_SumTimeMoney.ForEach(x =>
                                        {
                                            var searchItem = currentItem.Where(m => m.SectionStartTime == x.SectionStartTime && m.SectionEndTime == x.SectionEndTime).ToList();
                                            if (searchItem.Count == 0)
                                            {
                                                currentItem.Add(x);
                                            }
                                            else
                                            {
                                                currentItem.Remove(searchItem.First());
                                                currentItem.Add(x);
                                            }
                                        });
                                        xDetail.nexthourscontent = TyziTools.Json.ToString(currentItem);
                                        if (ruleRunData.dicHours.ContainsKey(crModel.ChargeRules_No)) { ruleRunData.dicHours.Remove(crModel.ChargeRules_No); }
                                        ruleRunData.dicHours.Add(crModel.ChargeRules_No, xDetail.nexthourscontent);
                                    }
                                }
                            });

                            if (ruleRunData.dicHours.ContainsKey(crModel.ChargeRules_No)) { ruleRunData.dicHours.Remove(crModel.ChargeRules_No); }
                            ruleRunData.dicHours.Add(crModel.ChargeRules_No, orderPayMoney.nexthourscontent);
                        }
                    }
                    else
                    {
                        var item = payDetailList.Where(x => x.orderdetailno == temporder.OrderDetail_No).FirstOrDefault();
                        if (item != null)
                        {
                            payDetailList.Remove(item);
                            if (item.nextcycletime < nextCycleTime) item.nextcycletime = nextCycleTime;
                            if (item.NextCyclePaidFees < NextCyclePaidFees) item.NextCyclePaidFees = NextCyclePaidFees;
                            if (item.nextcyclefreemin < Utils.ObjectToInt(orderPayMoney.nextcyclefreemin, 0)) item.nextcyclefreemin = Utils.ObjectToInt(orderPayMoney.nextcyclefreemin, 0);

                            item.parktimemin += (detailOutTime - temporder.OrderDetail_EnterTime).Value.TotalMinutes;
                            payDetailList.Add(item);
                        }
                        else
                        {

                            //不需要支付
                            ChargeModels.PayDetail ret = CreatePayDetail(0, "无需缴费", 0, temporder.OrderDetail_ParkAreaNo, temporder.OrderDetail_ParkOrderNo, temporder.OrderDetail_No, temporder.OrderDetail_ID,
                              nextCycleTime, NextCyclePaidFees, detailOutTime, temporder.OrderDetail_EnterTime, NextHoursTime, NextHoursContent, NextCycleFreeMin, temporder.OrderDetail_ParkAreaName);
                            ret.iscarexpire = param.readParam.IsCarExpire;
                            ret.isovertime = param.readParam.IsOverTime;
                            payDetailList.Add(ret);
                        }
                    }
                }
            }
            //}

            payDetailList?.ForEach(xDetail =>
            {
                if (!string.IsNullOrEmpty(xDetail.nexthourscontent))
                {
                    var currentItem = TyziTools.Json.ToObject<List<CalcHoursResult>>(xDetail.nexthourscontent);
                    if (currentItem != null && currentItem.Count > 0)
                    {
                        var lastItem = currentItem.LastOrDefault();
                        xDetail.nexthourscontent = TyziTools.Json.ToString(new List<CalcHoursResult>() { lastItem });
                    }
                }
            });

            return payDetailList;
        }

        /// <summary>
        /// 获取计费规则
        /// </summary>
        /// <param name="order"></param>
        /// <param name="isOverTime"></param>
        /// <param name="nowTime"></param>
        /// <returns></returns>
        private static Model.ChargeRules GetRulesBySimple(Model.OrderDetail order, bool isOverTime, string nowTime)
        {
            Model.ChargeRules crModel = null;
            if (isOverTime && GetChargeRulesType() == 0)
            {
                isOverTime = false;
            }

            if (!AppBasicCache.ReadWriteCache || AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache != 1)
            {
                var enterTime = order.OrderDetail_EnterTime.Value.ToString("yyyy-MM-dd");
                List<Model.ChargeRelation> relationList = BLL.ChargeRelation._GetAllEntity(new Model.ChargeRelation(), "*", $"ChargeRelation_ParkNo='{order.OrderDetail_ParkNo}' " +
                    (isOverTime ? "and (ChargeRelation_OverTime=1 or ChargeRelation_OverTime=2)" : "and (ChargeRelation_OverTime=0 or ChargeRelation_OverTime=2)") +
                    $" and ChargeRelation_CarTypeNo='{order.OrderDetail_CarType}' and ChargeRelation_CarCardTypeNo='{order.OrderDetail_CarCardType}' and ChargeRelation_ParkAreaNo='{order.OrderDetail_ParkAreaNo}' " +
                    $" and (" +
                    $"   (ChargeRelation_BeginTime>='{enterTime}' and ChargeRelation_BeginTime<='{nowTime}') " +
                    $"or (ChargeRelation_BeginTime<='{enterTime}' and ChargeRelation_EndTime >='{nowTime}')" +
                    $"or (ChargeRelation_BeginTime <='{enterTime}' and ChargeRelation_EndTime >='{enterTime}')" +
                    $")");
                if (relationList != null && relationList.Count > 0)
                {
                    crModel = BLL.ChargeRules._GetEntityByWhere(new Model.ChargeRules(), "*", $"ChargeRules_No='{relationList.Last().ChargeRelation_ChargeRulesNo}'");
                }
            }
            else
            {
                var relationList = new List<Model.ChargeRelation>();
                AppBasicCache.GetAllChargeRelation.Values.ToList().ForEach(x => { relationList.AddRange(x); });
                var rList = relationList.Where(x => (x.ChargeRelation_OverTime == (isOverTime ? 1 : 0) || x.ChargeRelation_OverTime == 2)
                 && x.ChargeRelation_CarTypeNo == order.OrderDetail_CarType && x.ChargeRelation_CarCardTypeNo == order.OrderDetail_CarCardType && x.ChargeRelation_ParkAreaNo == order.OrderDetail_ParkAreaNo
                 && (
                    (x.ChargeRelation_BeginTime >= order.OrderDetail_EnterTime.Value && x.ChargeRelation_BeginTime <= Utils.ObjectToDateTime(nowTime))
                    || (x.ChargeRelation_BeginTime <= order.OrderDetail_EnterTime.Value && x.ChargeRelation_EndTime >= Utils.ObjectToDateTime(nowTime))
                    || (x.ChargeRelation_BeginTime <= order.OrderDetail_EnterTime.Value && x.ChargeRelation_EndTime >= order.OrderDetail_EnterTime.Value)
                   )
                 ).ToList();
                if (rList != null && rList.Count > 0)
                {
                    crModel = AppBasicCache.GetElement(AppBasicCache.GetAllChargeRules, rList.Last().ChargeRelation_ChargeRulesNo);
                }
            }

            if (crModel == null)
            {
                //获取计费规则(通过车场设置的车牌计费设置，找到关联的默认计费规则)
                crModel = GetPolicyRules(order, isOverTime, nowTime);
            }

            return crModel;
        }

        /// <summary>
        /// 通过计费规则，开始计费
        /// </summary>
        /// <param name="enterTime">入场时间</param>
        /// <param name="outTime">出场时间</param>
        /// <returns></returns>
        public static ChargeModels.PayDetail BenginChargeRuleBySimple(ref BillingBlackBox.Models.SectionParam param, BillingBlackBox.Models.ChargeRulesLogic crlModel, DateTime? enterTime, DateTime? outTime, ref bool isUseFirstTime, ref decimal? cycleMoney, ref DateTime? cycleTime,
            DateTime? NextHoursTime = null, string NextHoursContent = null, int? NextCycleFreeMin = null, int? UserFreeTime = 0)
        {
            ChargeModels.PayDetail payDetail = CreatePayDetail(2, "计费失败", 0, null, null, null, null, DateTimeHelper.GetNowTime(), 0, outTime, enterTime, null, null, null);
            payDetail.preNextcycletime = cycleTime;
            payDetail.preNextCyclePaidFees = cycleMoney;
            payDetail.preNextcyclefreemin = NextCycleFreeMin;

            BillingBlackBox.Helper.ChargeHelper.SetParamDefaultValue(ref crlModel);

            //时段累计金额
            List<BillingBlackBox.Models.CalcHoursResult> hrList = string.IsNullOrEmpty(NextHoursContent) ? null : TyziTools.Json.ToModel<List<BillingBlackBox.Models.CalcHoursResult>>(NextHoursContent);
            if (hrList != null)
            {
                BillingBlackBox.Models.CalcHoursResult calcHours = hrList.Last();
                if (calcHours.SectionStartTime < enterTime.Value)
                {
                    hrList = hrList.FindAll(x => x.SectionEndTime >= enterTime.Value);
                }
                else
                {
                    hrList = null;//历史时段累计开始时间大于入场时间，则清空时段累计金额重新计费
                }
            }

            //计费算法入口
            string result = CalcSimpleLogic.LogicCalc(ref param, enterTime.Value, outTime.Value, crlModel, ref isUseFirstTime, ref cycleMoney, ref cycleTime, NextHoursTime, hrList, NextCycleFreeMin);

            if (!string.IsNullOrEmpty(result))
            {
                BillingBlackBox.Models.ChargeResult chargeResult = TyziTools.Json.ToModel<BillingBlackBox.Models.ChargeResult>(result);
                if (chargeResult != null)
                {
                    if (chargeResult.Code == 0)//计费失败
                    {
                        return payDetail;
                    }
                    else if (chargeResult.Code == 2)//无需缴费
                    {
                        payDetail.payed = 0;
                        payDetail.starttime = chargeResult.StartTime;
                        payDetail.endtime = chargeResult.EndTime;
                        payDetail.payedmsg = chargeResult.Msg;
                        payDetail.nextcycletime = chargeResult.NextCycleTime;
                        payDetail.NextCyclePaidFees = chargeResult.NextCyclePaidFees;
                        payDetail.nextcyclefreemin = chargeResult.NextCycleFreeMin;
                        payDetail.nexthourscontent = chargeResult.HoursMoenyList == null ? null : TyziTools.Json.ToString(chargeResult.HoursMoenyList);
                        payDetail.currentfreemin = chargeResult.CurrentFreeMin;
                        return payDetail;
                    }
                    else//计费成功
                    {
                        payDetail.payed = 1;
                        payDetail.starttime = chargeResult.StartTime;
                        payDetail.endtime = chargeResult.EndTime;
                        payDetail.payedmsg = chargeResult.Msg;
                        payDetail.nextcycletime = chargeResult.NextCycleTime;
                        payDetail.NextCyclePaidFees = Utils.ObjectToDecimal(chargeResult.NextCyclePaidFees, 0);
                        payDetail.calcbegintime = chargeResult.StartTime;
                        payDetail.payedamount = Utils.ObjectToDecimal(chargeResult.FeePayable, 0);
                        payDetail.nextcyclefreemin = chargeResult.NextCycleFreeMin;
                        payDetail.nexthourscontent = chargeResult.HoursMoenyList == null ? null : TyziTools.Json.ToString(chargeResult.HoursMoenyList);
                        payDetail.currentfreemin = chargeResult.CurrentFreeMin;

                        //payDetail.total2 = param.writeParam.ParkOrder_TotalPayed2.Value + payDetail.payedamount.Value;
                        return payDetail;
                    }
                }
            }
            return payDetail;
        }

        #endregion
    }
}
