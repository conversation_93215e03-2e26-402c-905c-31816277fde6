﻿
using System;
using System.Collections.Generic;
using System.Text;
namespace carparking.SentryBox
{
    /// <summary>
    /// 入口识别结果[用于ws通知客户端]
    /// </summary>
    public class EnterLPR
    {
        public EnterLPR() { }
        public EnterLPR(Model.ResultPass rs)
        {
            this.carno = rs.passres.carno;
            this.cartype = rs.resorder?.resIn?.parkorder?.ParkOrder_CarTypeName ?? rs.passres.cartype?.CarType_Name;
            this.carcardtype = rs.resorder?.resIn?.parkorder?.ParkOrder_CarCardTypeName ?? rs.passres.carcardtype?.CarCardType_Name;
            this.carcardtypeno = rs.resorder?.resIn?.parkorder?.ParkOrder_CarCardType ?? rs.passres.carcardtype?.CarCardType_No;
            this.passwayno = rs.passres.passway?.Passway_No;
            this.passcode = rs.passres.code.ToString();
            this.passmsg = rs.errmsg;
            this.orderno = rs.passres.parkorderno;
            this.entertime = rs.time.Value.ToString("yyyy-MM-dd HH:mm:ss");
            this.enterimg = Util.LPRTools.GetSentryHostImg(rs.passres.localimage);
            this.enterramrk = rs.resorder?.resIn?.parkorder?.ParkOrder_EnterRemark ?? rs.resorder?.resOut?.parkorder?.ParkOrder_EnterRemark;
            if (this.passcode == "3")
            {
                this.passcode = "2";
                this.enterramrk = "车场满位,排队等候";
            }
            if (this.passcode != "0" && rs?.passres?.fycode == 1)
            {
                this.passcode = "2";
                this.enterramrk = rs.passres?.errmsg;
            }
            if (string.IsNullOrWhiteSpace(this.enterramrk))
            {
                this.enterramrk = rs.passres?.owner?.Owner_Remark ?? rs.passres?.car?.Car_Remark ?? "";
            }
            this.data = rs;
            this.name = rs.passres?.owner?.Owner_Name ?? rs.passres?.car?.Car_OwnerName;
            if (rs.passres?.owner?.Owner_CardType == 2)
                this.balance = rs.passres?.owner?.Owner_Balance?.ToString() ?? rs.passres?.car?.Car_Balance.ToString();
        }

        /// <summary>
        /// 识别车牌
        /// </summary>
        public string carno { get; set; }

        /// <summary>
        /// 车牌颜色
        /// </summary>
        public string cartype { get; set; }

        /// <summary>
        /// 车牌类型名称
        /// </summary>
        public string carcardtype { get; set; }
        /// <summary>
        /// 车牌类型编号
        /// </summary>
        public string carcardtypeno { get; set; }

        /// <summary>
        /// 通行车道编码
        /// </summary>
        public string passwayno { get; set; }

        /// <summary>
        /// 通行结果：0[禁止通行],1[自动放行],2[确认放行],3[排队等候],4[最低收费缴费通行]
        /// </summary>
        public string passcode { get; set; }

        /// <summary>
        /// 通行信息
        /// </summary>
        public string passmsg { get; set; }

        /// <summary>
        /// 停车订单号
        /// </summary>
        public string orderno { get; set; }

        /// <summary>
        /// 识别图片
        /// </summary>
        public string enterimg { get; set; }

        /// <summary>
        /// 入场时间
        /// </summary>
        public string entertime { get; set; }

        /// <summary>
        /// 入场备注
        /// </summary>
        public string enterramrk { get; set; }

        /// <summary>
        /// 通行检测结果
        /// </summary>
        public Model.ResultPass data { get; set; }

        /// <summary>
        /// 车主姓名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 储值车余额
        /// </summary>
        public string balance { get; set; } = "";
        /// <summary>
        /// 储值车抵扣金额
        /// </summary>
        public string deduction { get; set; } = "";
    }
}
