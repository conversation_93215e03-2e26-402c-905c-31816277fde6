﻿using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Charge;
using carparking.ChargeModels;
using carparking.Common;
using carparking.Config;
using carparking.DirectCloudMQTT;
using carparking.Interop.YS;
using carparking.Library;
using carparking.Model;
using carparking.Model.API;
using carparking.Passthrough485;
using carparking.PassTool;
using carparking.SentryBox.BarrierDevice;
using carparking.SentryBox.Command;
using carparking.SentryBox.Device;
using carparking.SentryBox.Util;
using DotNetty.Buffers;
using DotNetty.Transport.Channels;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using TcpConnPools;
using TcpConnPools.Camera;
using TcpConnPools.Camera.CameraYS;
using TcpConnPools.ChannelMachine.Robot;
using TcpConnPools.Controller;
using TcpConnPools.Controller.BarrierY312;
using TcpConnPools.Controller.NonMotorized;
using Admins = carparking.Model.Admins;
using BusinessCar = carparking.Model.BusinessCar;
using Car = carparking.Model.Car;
using CarCardType = carparking.BLL.CarCardType;
using CarRecog = carparking.Model.CarRecog;
using CarType = carparking.BLL.CarType;
using ChargeRelation = carparking.Model.ChargeRelation;
using ChargeRules = carparking.Model.ChargeRules;
using CityParking = carparking.BLL.CityParking;
using CouponRecord = carparking.Model.CouponRecord;
using OrderDetail = carparking.BLL.OrderDetail;
using Owner = carparking.Model.Owner;
using ParkArea = carparking.BLL.ParkArea;
using Parking = carparking.BLL.Parking;
using ParkOrder = carparking.Model.ParkOrder;
using PassRecord = carparking.Model.PassRecord;
using Passway = carparking.BLL.Passway;
using PasswayLink = carparking.BLL.PasswayLink;
using PolicyPark = carparking.BLL.PolicyPark;
using PolicyPassway = carparking.BLL.PolicyPassway;
using PushResult = carparking.BLL.PushResult;
using Reserve = carparking.Model.Reserve;
using SentryHost = carparking.BLL.SentryHost;
using SysConfig = carparking.BLL.SysConfig;

namespace carparking.SentryBox;

#region 账号 密码变更/删除

/// <summary>
/// 账号 密码变更/删除
/// </summary>
public class AdminsHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "admins";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context);
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return Task.FromResult(RunAction(reqPush, context));
    }

    private ResPush RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            if (reqPush.act == "Delete")
            {
                var data = PushResult.ParsePushData(reqPush);
                int id = data[0].Admins_ID;
                string pwd = data[0].Admins_Pwd;
                if (Sessions.BSDelAdminSession(data[0].Admins_Account, pwd, true))
                {
                    WebSocketHandler.SendScramble(data[0].Admins_Account);
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"账号信息变更处理异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return new ResPush() { guid = reqPush.guid, code = 1 };
    }
}

#endregion

#region 策略处理

/// <summary>
/// 车场策略信息更新
/// </summary>
public class PolicyParkHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "policypark";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            AppBasicCache.GetPolicyPark = AppCache.GetPolicyPark = null;
            DataCache.LoadDeviceVoice.Clear();

            AppBasicCache.GetPolicyPark = AppCache.GetPolicyPark = PolicyPark.GetEntity(AppBasicCache.SentryHostInfo.SentryHost_ParkNo);
            if (AppCache.GetPolicyPark != null)
            {
                AppBasicCache.GetPolicyPark.PolicyPark_BusinessCache = 1;
                AppCache.GetPolicyPark.PolicyPark_BusinessCache = 1;

                #region 相机识别间隔

                CameraGlobal.SameLanePlateInterval = AppCache.GetPolicyPark.PolicyPark_RememberTime0 ?? 5;
                CameraGlobal.SamePlateInterval = AppCache.GetPolicyPark.PolicyPark_RememberTime ?? 5;

                #endregion

                AppBasicCache.FloatLen = AppCache.GetPolicyPark.PolicyPark_Floatlen;
                CameraUtil.LoadAllMainDeviceVoice();
                LogManagementMap.WriteToFile(LoggerEnum.CacheLog, $"更新岗亭收费小数位：{AppBasicCache.FloatLen}");
            }

            AppBasicCache.GetBasicCache = null;

            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新车场策略信息异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

#endregion

#region 岗亭缓存信息更新

/// <summary>
/// 车场信息
/// </summary>
public class ParkingHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "parking";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            //1.更新岗亭信息
            AppBasicCache.SentryHostInfo = AppCache.LoadSentryHostInfo();
            var park = Parking.GetEntity(null);

            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
            {
                if ((park.Parking_EnableNet == 1 && AppCache.GetParking.Parking_EnableNet != 1) || (park.Parking_EnableNet == 1 && park.Parking_Mode != AppCache.GetParking.Parking_Mode))
                {
                    AppCache.GetParking = park;
                    MqttClient.Instance.UpdateConfigAsync(CloudSettings.MqttConfig);
                    LogManagementMap.WriteToFile(LoggerEnum.CacheLog, "启用云车场服务MQ通信");
                }

                if (park?.Parking_EnableNet != 1 && AppCache.GetParking.Parking_EnableNet == 1)
                {
                    MqttClient.Instance.StopAsync();
                    LogManagementMap.WriteToFile(LoggerEnum.CacheLog, "停止云车场服务MQ通信");
                }
            }
            AppCache.GetParking = park;

            if (AppSettingConfig.SentryMode == VersionEnum.EPSServer && AppSettingConfig.InstallType == InstallTypeEnum.LinuxB30BOX && !string.IsNullOrEmpty(park.Parking_AuthKey))
            {
                if ((SentryBoxHelper.CheckAuthService?.IsCancellationRequested ?? true))
                {
                    SentryBoxHelper.CheckAuthService = new CancellationTokenSource();
                    Task CheckAuthTask = new Task(delegate { SentryBoxHelper.CheckAuthServices(); }, SentryBoxHelper.CheckAuthService.Token, TaskCreationOptions.LongRunning);
                    CheckAuthTask.Start();
                }
            }

            if (!AppCache.IsWindows) CommHelper.SendServiceHeart("1002");

            AppBasicCache.GetBasicCache = null;
            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新车场信息异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// Mq在线状态
/// </summary>
public class MqStatusHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "mqstatus";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = MqttClient.Instance == null ? 0 : MqttClient.Instance.IsOnline ? 1 : 0,
                data = new { IsEmergency = MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0, ConnMode = MqttClient.Instance == null ? 0 : CameraGlobal.ConnMode ?? 0 }
            };
            SendTCPDatas(context, resPush);

            return Task.FromResult(resPush);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"获取Mq在线状态异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 基础数据缓存信息更新
/// </summary>
public class BasicHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "initdata,policy,carcardtype,cartype,policypass,policyarea,policycarcard,accessauth,endnumauth,editcarcardtype";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        Model.API.ResPush resPush = new Model.API.ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };

        try
        {
            AppBasicCache.LoadBasicData();

            if (reqPush != null && reqPush.tname == "initdata")
            {
                AppBasicCache.SentryHostInfo = AppCache.LoadSentryHostInfo();
                if (ConfigurationMap.GetModel != null && AppBasicCache.SentryHostInfo != null && ConfigurationMap.GetModel.SentryHostNo != AppBasicCache.SentryHostInfo.SentryHost_No)
                {
                    ConfigurationMap.GetModel.SentryHostNo = AppBasicCache.SentryHostInfo.SentryHost_No;
                    ConfigurationMap.Save();
                }
            }

            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新基础数据缓存信息更新发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return Task.FromResult(resPush);
    }
}

/// <summary>
/// 系统参数更新
/// </summary>
public class PushsysconfigHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "pushsysconfig";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var sysConfig = SysConfig.GetEntity(AppBasicCache.SentryHostInfo.SentryHost_ParkNo);
            if (sysConfig != null)
            {
                AppBasicCache.CurrentSysConfig = sysConfig;
                AppBasicCache.CurrentSysConfigContent = TyziTools.Json.ToObject<SysConfigContent>(SysConfig.GetUrlDecode(sysConfig.SysConfig_Content));
                SentryBoxHelper.SetPayParms(AppBasicCache.CurrentSysConfigContent);
                CameraGlobal.CameraImageQuality = AppBasicCache.CurrentSysConfigContent.SysConfig_CameraImageQuality;

                if (AppBasicCache.CurrentSysConfigContent?.SysConfig_EnableImgPath == 1 && !string.IsNullOrEmpty(AppBasicCache.CurrentSysConfigContent?.SysConfig_ImagePath))
                {
                    CameraGlobal.strImgpath = ImageTools.LocalFilePath = HttpUtility.UrlDecode(AppBasicCache.CurrentSysConfigContent.SysConfig_ImagePath);
                }
                else
                {
                    if (AppCache.IsWindows)
                    {
                        var path1 = AppDomain.CurrentDomain.BaseDirectory;
                        CameraGlobal.strImgpath = ImageTools.LocalFilePath = path1.Substring(0, path1.LastIndexOf("Web")) + "CameraCaptures";
                    }
                    else
                    {
                        CameraGlobal.strImgpath = ImageTools.LocalFilePath = "/mnt/sda1/b30/CameraCaptures";
                    }
                }

                LogManagementMap.SetLogOpen = AppBasicCache.CurrentSysConfigContent?.SysConfig_LogOpen ?? 0;
                LogManagementMap.SetOverDays = AppBasicCache.CurrentSysConfigContent?.SysConfig_LogSaveDay ?? 0;
            }
            else
            {
                AppBasicCache.CurrentSysConfigContent = null;
            }

            AppBasicCache.GetBasicCache = null;
            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新系统参数异常：{TyziTools.Json.ToString(reqPush)}");
        }

        return Task.FromResult(new ResPush() { guid = reqPush.guid });
    }
}

/// <summary>
/// 系统参数更新
/// </summary>
public class SconfigHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "sysconfig";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        Model.API.ResPush resPush = new Model.API.ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };

        try
        {
            var sysConfig = SysConfig.GetEntity(AppBasicCache.SentryHostInfo.SentryHost_ParkNo);
            if (sysConfig != null)
            {
                AppBasicCache.CurrentSysConfigContent = AppBasicCache.CurrentSysConfigContent = TyziTools.Json.ToObject<SysConfigContent>(SysConfig.GetUrlDecode(sysConfig.SysConfig_Content));
                SentryBoxHelper.SetPayParms(AppBasicCache.CurrentSysConfigContent);
                CameraGlobal.CameraImageQuality = AppBasicCache.CurrentSysConfigContent.SysConfig_CameraImageQuality;

                //if (AppCache.IsWindows)
                //{
                if (AppBasicCache.CurrentSysConfigContent?.SysConfig_EnableImgPath == 1 && !string.IsNullOrEmpty(AppBasicCache.CurrentSysConfigContent?.SysConfig_ImagePath))
                {
                    CameraGlobal.strImgpath = ImageTools.LocalFilePath = HttpUtility.UrlDecode(AppBasicCache.CurrentSysConfigContent.SysConfig_ImagePath);
                }
                else
                {
                    if (AppCache.IsWindows)
                    {

                        var path1 = AppDomain.CurrentDomain.BaseDirectory;
                        if (path1?.Contains("Web") ?? false) CameraGlobal.strImgpath = ImageTools.LocalFilePath = path1.Substring(0, path1.LastIndexOf("Web")) + "CameraCaptures";
                    }
                    else
                    {
                        CameraGlobal.strImgpath = ImageTools.LocalFilePath = "/mnt/sda1/b30/CameraCaptures";
                    }
                }
                //}

                LogManagementMap.SetLogOpen = AppBasicCache.CurrentSysConfigContent?.SysConfig_LogOpen ?? 0;
                LogManagementMap.SetOverDays = AppBasicCache.CurrentSysConfigContent?.SysConfig_LogSaveDay ?? 20;
            }
            else
            {
                AppBasicCache.CurrentSysConfig = null;
                AppBasicCache.CurrentSysConfigContent = null;
            }

            AppBasicCache.GetBasicCache = null;
            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新系统参数异常：{TyziTools.Json.ToString(reqPush)}");
        }

        return Task.FromResult(resPush);
    }
}

/// <summary>
/// 城市服务信息更新
/// </summary>
public class CityParkingHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "cityparking";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }



    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        var create = Convert.ToDateTime(reqPush.time);
        var tempModels = CityParking.GetAllEntity("*", "1=1");
        //更新数据
        tempModels.ForEach(m =>
        {
            if (AppBasicCache.GetCityParkings.TryGetValue(m.CityServer_No, out var model))
            {
                model = m;
            }
            else
            {
                AppBasicCache.GetCityParkings.TryAdd(m.CityServer_No, m);
            }
        });

        //删除数据
        AppBasicCache.GetCityParkings.AsParallel().ForAll(m =>
        {
            if (!tempModels.Exists(x => x.CityServer_No == m.Key))
            {
                AppBasicCache.GetCityParkings.TryRemove(m.Key, out var v);
            }
        });

        //更新支付参数
        SentryBoxHelper.SetPayParms(AppBasicCache.CurrentSysConfigContent);

        WebSocketUtil.SendWSAllLink("updatedata", InstructionName);

        return Task.FromResult(new ResPush() { guid = reqPush.guid });
    }
}

/// <summary>
/// 更新岗亭时同时更新车道信息以及相关设备
/// </summary>
public class UpdatesentryhostHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "updatesentryhost";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }


    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            //1.更新岗亭信息
            var host = AppCache.LoadSentryHostInfo();
            if (host == null)
            {
                var sentryConfigPath = "";
                var path = AppDomain.CurrentDomain.BaseDirectory;
                if (AppCache.IsWindows)
                {
                    path = path.Substring(0, path.LastIndexOf("\\"));
                    path = path.Substring(0, path.LastIndexOf("\\"));
                    sentryConfigPath = path + "\\Config\\ManualSentryBox.config";
                }
                else
                {
                    sentryConfigPath = "/mnt/sda1/b30/Config/ManualSentryBox.config";
                }

                File.Delete(sentryConfigPath);
                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, "岗亭信息被删，重启站点！");
                if (AppCache.IsWindows)
                    Process.GetCurrentProcess().Kill();
                else
                {
                    Process.GetCurrentProcess().Close();
                }

                return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
            }

            var list = SentryHost.GetAllEntity();
            list.ForEach(m => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSentryDic, m.SentryHost_No, m); });


            AppBasicCache.GetSentryDic.AddOrUpdate(AppBasicCache.SentryHostInfo.SentryHost_No, TyziTools.Json.ToObject<Model.SentryHost>(TyziTools.Json.ToString(host)), (key, oldValue) =>
            {
                oldValue = TyziTools.Json.ToObject<Model.SentryHost>(TyziTools.Json.ToString(host));
                return oldValue;
            });

            AppCache.Load(true);

            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新岗亭信息异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 更新岗亭时同时更新车道信息以及相关设备
/// </summary>
public class MQUpdatesentryhostHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "mqupdatesentryhost";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }


    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var data = reqPush.data;
            if (!string.IsNullOrEmpty(data))
            {
                var sqls = TyziTools.Json.ToObject<List<string>>(data);
                if (sqls != null && sqls.Count > 0)
                    BaseBLL._ExecuteTrans(sqls[0], false);
            }

            var list = SentryHost.GetAllEntity();
            list.ForEach(m => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSentryDic, m.SentryHost_No, m); });
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新岗亭信息异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 更新岗亭时同时更新车道信息以及相关设备
/// </summary>
public class InitsentryhostHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "initsentryhost";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        var resPush = new ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };

        try
        {
            var host = AppCache.LoadSentryHostInfo();
            if (host == null)
            {
                var sentryConfigPath = "";
                var path = AppDomain.CurrentDomain.BaseDirectory;
                if (AppCache.IsWindows)
                {
                    path = path.Substring(0, path.LastIndexOf("\\"));
                    path = path.Substring(0, path.LastIndexOf("\\"));
                    sentryConfigPath = path + "\\Config\\ManualSentryBox.config";
                }
                else
                {
                    sentryConfigPath = "/mnt/sda1/b30/Config/ManualSentryBox.config";
                }

                File.Delete(sentryConfigPath);
                return Task.FromResult(resPush);
            }

            AppBasicCache.SentryHostInfo = host;
            AppCache.Load(true);

            var oloConfig = AppBasicCache.CurrentSysConfig?.Copy();
            AppBasicCache.CurrentSysConfig = SysConfig.GetEntity();
            if (AppBasicCache.CurrentSysConfig != null && !string.IsNullOrEmpty(AppBasicCache.CurrentSysConfig.SysConfig_Content))
            {
                AppBasicCache.CurrentSysConfigContent = TyziTools.Json.ToObject<SysConfigContent>(SysConfig.GetUrlDecode(AppBasicCache.CurrentSysConfig.SysConfig_Content));
                CameraGlobal.CameraImageQuality = AppBasicCache.CurrentSysConfigContent.SysConfig_CameraImageQuality;
            }

            RSocket.onlineChannel.AsParallel().ForAll(x =>
            {
                try
                {
                    RSocket.Close(x.Value.Channel);
                }
                catch { }
            });

            RSocket.onlineChannel?.Clear();

            RSocket.hosts = AppBasicCache.GetSentryDatas();
            RSocket.RefreshHost();

            try
            {
                if (oloConfig == null || oloConfig.SysConfig_Content != AppBasicCache.CurrentSysConfig.SysConfig_Content)
                {
                    SentryBoxHelper.SetPayParms(AppBasicCache.CurrentSysConfigContent);
                }
            }
            catch (Exception ex)
            {
                resPush.code = 0;
                resPush.msg = "异常错误：" + ex.Message;
                LogManagementMap.WriteToFileException(ex, $"加载支付参数异常[initsentryhost]：{TyziTools.Json.ToString(reqPush)}");
            }

            var list = SentryHost.GetAllEntity();
            list.ForEach(m => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSentryDic, m.SentryHost_No, m); });

            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            resPush.code = 0;
            resPush.msg = "异常错误：" + ex.Message;
            LogManagementMap.WriteToFileException(ex, $"更新岗亭信息异常：{TyziTools.Json.ToString(reqPush)}");
        }

        return Task.FromResult(resPush);
    }
}

/// <summary>
///
/// </summary>
public class WebtoSentryHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "bathdelparkorder,bathcarowner,updateorderlist,delallpasswayact,getsync";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        var resPush = new ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };
        try
        {
            dynamic item = TyziTools.Json.ToObject<Object>(reqPush.data);
            item = item[0];
            switch (reqPush?.tname)
            {
                case "BathDelParkOrder":
                    (DateTime?, DateTime?) obj = (item.Item1, item.Item2);
                    var res = BLL.ParkOrder.CloseList(obj.Item1, obj.Item2);
                    if (res > 0)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"批量删除停车订单成功{obj.Item1.Value.ToString("yyyy-MM-dd HH:mm:ss")},{obj.Item2.Value.ToString("yyyy-MM-dd HH:mm:ss")}");
                        return Task.FromResult(resPush);
                    }
                    else
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"批量删除停车订单失败{obj.Item1.Value.ToString("yyyy-MM-dd HH:mm:ss")},{obj.Item2.Value.ToString("yyyy-MM-dd HH:mm:ss")}");
                    break;
                case "BathCarOwner":
                    (string, string) obj2 = (item.Item1, item.Item2);
                    var bathCarOwnerRes = BLL.Owner.BathAuthArea(obj2.Item1, obj2.Item2, out List<Model.Owner> owneres);
                    if (bathCarOwnerRes)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"批量区域授权成功{item.Item1},{item.Item2}");
                        return Task.FromResult(resPush);
                    }
                    else
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"批量区域授权失败{item.Item1},{item.Item2}");
                    break;

                case "UpdateOrderList":
                    var models = new List<Model.OrderDetail>();
                    if (item.Item1 != null) models = (List<Model.OrderDetail>)item.Item1;
                    var dataRes = BLL.OrderDetail.UpdateByList(models);
                    if (dataRes)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"批量修改停车订单成功{string.Join(";", models.Select(x => $"{x.OrderDetail_CarNo}=>{x.OrderDetail_No}"))}");
                        return Task.FromResult(resPush);
                    }
                    else
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"批量修改停车订单失败{string.Join(";", models.Select(x => $"{x.OrderDetail_CarNo}=>{x.OrderDetail_No}"))}");
                    break;
                case "DelAllPassway":
                    List<string> delAllPasswayParam = (List<string>)item.Item1;
                    var result = BLL.Passway.DeleteAll(delAllPasswayParam);
                    if (result >= 0)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"删除车道成功:{string.Join(",", delAllPasswayParam)}");
                        return Task.FromResult(resPush);
                    }
                    else
                    { LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"删除车道失败:{string.Join(",", delAllPasswayParam)}"); }
                    break;
                case "GetSync":
                    string GetSyncObj = (string)item.Item1;
                    if (!string.IsNullOrWhiteSpace(GetSyncObj))
                    {
                        var r = DataCache.SyncAction.Get(GetSyncObj);
                        if (!string.IsNullOrWhiteSpace(r)) { resPush.data = r; return Task.FromResult(resPush); }
                    }
                    break;
                default:
                    resPush.code = 0;
                    resPush.msg = "指令错误";
                    return Task.FromResult(resPush);
            }
        }
        catch (Exception ex)
        {
            resPush.code = 0;
            resPush.msg = "异常错误：" + ex.Message;
            LogManagementMap.WriteToFileException(ex, $"同步信息异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(resPush);
    }
}

/// <summary>
/// 更新车场设置
/// </summary>
public class ChangeSysConfigHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "changesysconfig";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }


    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            DataCache.LoadDeviceVoice.Clear();

            AppBasicCache.CurrentSysConfig = SysConfig.GetEntity();
            if (AppBasicCache.CurrentSysConfig != null && !string.IsNullOrEmpty(AppBasicCache.CurrentSysConfig.SysConfig_Content))
            {
                AppBasicCache.CurrentSysConfigContent = TyziTools.Json.ToObject<SysConfigContent>(SysConfig.GetUrlDecode(AppBasicCache.CurrentSysConfig.SysConfig_Content));
                CameraGlobal.CameraImageQuality = AppBasicCache.CurrentSysConfigContent.SysConfig_CameraImageQuality;
            }

            AppBasicCache.GetParking = null;
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新岗亭信息异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 车道缓存信息更新
/// </summary>
public class PasswayHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "passway,addpassway,updatepassway,delpassway,delallpassway";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            AppCache.Load(true);

            if (reqPush.act?.ToLower() == "edit")
            {
                var editPasswayAll = JsonConvert.DeserializeObject<List<PushResultParse.EditPasswayAll>>(reqPush.data);
                if (editPasswayAll != null && editPasswayAll.Count > 0)
                {
                    var passwayList = editPasswayAll[0].Item1;

                    passwayList?.ForEach(passway =>
                    {
                        CameraController.UpdateCameraModel(passway.Passway_No, openGateAndVoice: passway.Passway_OpenGateAndVoice);
                        CameraGlobal.SameLaneIntervalDic.AddOrUpdate(passway.Passway_No, passway.Passway_IdInterval ?? 5, (_, _) => passway.Passway_IdInterval ?? 5);
                        if (passway.Passway_No != null && AppBasicCache.GetSentryPolicyPasswayDic.TryGetValue(passway.Passway_No, out var pp))
                        {
                            var p1 = Passway.GetEntityExt("*", $"Passway_No='{passway.Passway_No}'");
                            if (p1 != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSentryPasswayDic, p1.Passway_No, p1);
                        }

                        //道闸控制板
                        var devices = BLL.Device.GetAllEntityExt("*", $"Device_PasswayNo='{passway.Passway_No}' and Device_Category=6 and Device_SentryHostNo='{AppBasicCache.SentryHostInfo.SentryHost_No}'");
                        if (devices?.Count > 0)
                        {
                            if (passway.Passway_EnableBoard == 1)
                            {
                                devices.ForEach(m =>
                                {
                                    AppBasicCache.GetSentryDeviceLinking.TryRemove(m.Device_No, out _);
                                    DevicePool.Instance.RemoveDevice(m.Device_IP);
                                    //设备连接处理
                                    DeviceCommonUtil.TryLinkDevice(m);
                                });
                            }
                            else
                            {
                                devices.ForEach(m =>
                                {
                                    if (AppBasicCache.GetSentryDeviceLinking.TryGetValue(m.Device_No, out var device))
                                    {
                                        AppBasicCache.GetSentryDeviceLinking.TryRemove(m.Device_No, out _);
                                        DevicePool.Instance.RemoveDevice(m.Device_IP);
                                    }
                                });
                            }
                        }

                        var model = PolicyPassway.GetMainEntity($" and Device_PasswayNo like '%{passway.Passway_No}%' ");
                        if (model != null) CameraUtil.LoadDeviceVoice(model);
                    });
                }
            }
            //else if (reqPush.act?.ToLower() == "add")
            //{
            //    var data = JsonConvert.DeserializeObject<dynamic>(reqPush.data);
            //    if (data.Item1 != null)
            //    {
            //        var passway = (Model.Passway)data.Item1;
            //        Model.PolicyPasswayMain model = BLL.PolicyPassway.GetMainEntity($" and Device_PasswayNo='{passway.Passway_No}' ");
            //        if (model != null)
            //            CameraUtil.LoadDeviceVoice(model);
            //    }
            //}

            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新车道相关缓存发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 车道策略
/// </summary>
public class PolicypasswayHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "policypassway";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            DataCache.LoadDeviceVoice.Clear();

            var list = new List<Model.PolicyPassway>();
            if (!string.IsNullOrEmpty(reqPush.data))
            {
                list = JsonConvert.DeserializeObject<List<Model.PolicyPassway>>(reqPush.data);
                list.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllPolicyPassway, item.PolicyPassway_No, item); });
            }
            else
            {
                var passwayes = Passway.GetAllEntity("*", $"Passway_SentryHostNo='{AppBasicCache.SentryHostInfo?.SentryHost_No}'");
                list = PolicyPassway.GetAllEntity("*", "");
                AppBasicCache.ClearElement(AppBasicCache.GetAllPolicyPassway);
                list.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllPolicyPassway, item.PolicyPassway_No, item); });

                var noes = passwayes.Select(x => x.Passway_No).ToList();
                list = list.Where(x => noes.Contains(x.PolicyPassway_PasswayNo)).ToList();
            }

            var tip = string.Empty;
            foreach (var pp in list)
            {
                if (AppBasicCache.GetSentryPasswayDic.TryGetValue(pp.PolicyPassway_PasswayNo, out var old))
                {
                    if (AppBasicCache.GetSentryPolicyPasswayDic.TryGetValue(pp.PolicyPassway_PasswayNo, out var oldModel))
                    {
                        //if (pp.PolicyPassway_Updatetime != oldModel.PolicyPassway_Updatetime)
                        //{
                        tip = $"更新车道策略缓存[{pp.PolicyPassway_PasswayNo}][{pp.PolicyPassway_DefaultCarType}][{pp.PolicyPassway_DefaultCarCardType}]";
                        if (AppBasicCache.GetSentryPolicyPasswayDic.TryUpdate(pp.PolicyPassway_PasswayNo, pp, oldModel))
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CacheLog, tip);
                            if (pp.PolicyPassway_Show != oldModel.PolicyPassway_Show || pp.PolicyPassway_ShowOption != oldModel.PolicyPassway_ShowOption
                                                                                     || pp.PolicyPassway_Broadparktime != oldModel.PolicyPassway_Broadparktime)
                            {
                                var model = PolicyPassway.GetMainEntity($" and Device_PasswayNo='{pp.PolicyPassway_PasswayNo}' ");
                                if (model != null)
                                    CameraUtil.LoadDeviceVoice(model);
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFileException(null, $"{tip}失败");
                        }
                        //}

                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSentryPolicyPasswayDic, pp.PolicyPassway_No, pp);
                    }
                    else
                    {
                        tip = $"加载车道策略缓存[{pp.PolicyPassway_PasswayNo}][{pp.PolicyPassway_DefaultCarType}][{pp.PolicyPassway_DefaultCarCardType}]";
                        if (AppBasicCache.GetSentryPolicyPasswayDic.TryAdd(pp.PolicyPassway_PasswayNo, pp))
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CacheLog, tip);
                            var model = PolicyPassway.GetMainEntity($" and Device_PasswayNo='{pp.PolicyPassway_PasswayNo}' ");
                            if (model != null)
                                CameraUtil.LoadDeviceVoice(model);
                        }
                        else
                        {
                            LogManagementMap.WriteToFileException(null, $"{tip}失败");
                        }
                    }
                }
            }


            AppBasicCache.GetBasicCache = null;

            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新车道策略缓存发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 设备缓存信息更新
/// </summary>
public class DeviceHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "device";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };
            SendTCPDatas(context, resPush);


            DataCache.LoadDeviceVoice.Clear();

            //设备
            var devices = BLL.Device.GetAllEntityExt("*", "");
            var passways = Passway.GetAllEntity("*", "");
            AppBasicCache.ClearElement(AppBasicCache.GetAllDeivces);
            devices.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllDeivces, item.Device_No, item); });
            AppBasicCache.ClearElement(AppBasicCache.GetAllPassway);
            passways.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllPassway, item.Passway_No, item); });

            if (DogModel.DogExist)
            {
                if (reqPush.act?.ToLower() == "edit")
                {
                    var deviceList = JsonConvert.DeserializeObject<List<Model.Device>>(reqPush.data);
                    if (deviceList != null && deviceList.Count > 0)
                    {
                        deviceList.ForEach((item) =>
                        {
                            // 车道控制板设备需要重新连接
                            if (item.Device_Category == 6)
                            {
                                AppBasicCache.GetSentryDeviceLinking.TryRemove(item.Device_No, out _);
                                DevicePool.Instance.RemoveDevice(item.Device_IP);
                            }
                        });
                    }
                }

                var tempDeviceExts = devices.Where(x => x.Device_SentryHostNo == AppBasicCache.SentryHostInfo.SentryHost_No).ToList();
                tempDeviceExts.ForEach(m =>
                {
                    //设备连接处理
                    DeviceCommonUtil.TryLinkDevice(m);
                });
                //关闭已不在当前岗亭设备的连接
                if (tempDeviceExts.Count < AppBasicCache.GetSentryDeviceLinking.Count)
                {
                    var tempDeviceNos = tempDeviceExts.Select(x => x.Device_No).ToList();
                    var deviceNos = AppBasicCache.GetSentryDeviceLinking.Keys.ToList();
                    var exceptNos = deviceNos.Except(tempDeviceNos).ToList();
                    foreach (var no in exceptNos)
                    {
                        if (AppBasicCache.GetSentryDeviceLinking.TryGetValue(no, out var device))
                        {
                            AppBasicCache.GetSentryDeviceLinking.TryRemove(no, out _);
                            DevicePool.Instance.RemoveDevice(device?.Device_IP);
                        }
                    }
                }
            }

            AppBasicCache.GetBasicCache = null;
            WebSocketUtil.SendWSAllLink("updatedata", InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"更新设备缓存发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

#endregion

#region 岗亭业务响应处理

/// <summary>
/// 获取设备状态
/// </summary>
public class GetDeviceOnlineStatusHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "deviceonlinestatus";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        var resPush = new ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };

        try
        {
            var deviceStatus = new List<object>();
            foreach (var dv in AppBasicCache.GetSentryDeviceLinking.Values)
            {
                var statu = 0;
                try
                {
                    var device = DevicePool.Instance.GetDevice(dv.Device_IP);
                    if (device != null)
                    {
                        statu = device.Model.IsConnected ? 1 : 0;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, $"[{dv?.Device_IP}]获取设备状态异常");
                    statu = 0;
                }

                deviceStatus.Add(new
                {
                    deviceno = dv.Device_No,
                    online = statu
                });
            }
            resPush.data = TyziTools.Json.ToString(deviceStatus);
        }
        catch (Exception ex)
        {
            SendTCPDatas(context, reqPush, TyziTools.Json.ToString(new
            {
                code = 1,
                msg = "获取设备状态异常！"
            }));
            LogManagementMap.WriteToFileException(ex, $"获取设备状态异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return Task.FromResult(resPush);
    }
}

/// <summary>
/// 获取指定车道的地感和道闸状态
/// </summary>
public class GetPassWayStatusHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "passwaystatus";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        var resPush = new ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };
        try
        {
            var isOnline = -1;
            var gateStatus = -1;
            var gsStatus = -1;
            var gateLongOpen = -1;
            var passwayNo = Convert.ToString(reqPush.data);
            var device = (ICamera)DevicePool.Instance.GetAllDevices().FirstOrDefault(m => m.Model.PasswayNo == passwayNo && m is ICamera camera && camera.Model is CameraModel model && model.IO == 1);
            if (device == null)
            {
                isOnline = 0;
            }
            else
            {
                isOnline = device.Model.IsConnected ? 1 : 0;
                gsStatus = await device.ReadGpioInStatusAsync(); //地感状态
                gateStatus = await device.ReadIoOutStatusAsync(((CameraModel)device.Model).OutIO); //道闸状态
                gateLongOpen = ((CameraModel)device.Model).GateLongOpen ? 1 : 0;
            }

            string data = JsonConvert.SerializeObject(new
            {
                code = 0,
                msg = "获取指定车道地感道闸状态成功！",
                IsOnline = isOnline,
                GateStatus = gateStatus,
                GSStatus = gsStatus,
                GateLongOpen = gateLongOpen
            });
            resPush.data = data;

            if (context != null)
            {
                SendTCPDatas(context, reqPush, data);
            }
        }
        catch (Exception ex)
        {
            string data = TyziTools.Json.ToString(new
            {
                code = 1,
                msg = "获取指定车道地感道闸状态异常！"
            });
            resPush.data = data;
            SendTCPDatas(context, reqPush, data);
            LogManagementMap.WriteToFileException(ex, $"获取指定车道地感道闸状态异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return resPush;
    }
}

/// <summary>
/// 支付订单下发
/// </summary>
public class PaySuccessHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "paysuccess";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var datas = TyziTools.Json.ToObject<List<PayReq>>(reqPush.data);
            foreach (var d in datas)
            {
                var orderNo = d.Item1.ParkOrder_No;
                var carNo = d.Item1.ParkOrder_CarNo;

                DataCache.OrderStatus.Set(orderNo, 2);

                //判断是否找到弹窗的订单处理，否则直接更新数据
                var isSearchOrder = false;
                bool isSearchCarData = false;

                if (Utils.PairsLockOrder.ContainsKey(orderNo))
                {
                    Utils.PairsLockOrder.TryRemove(orderNo, out _);//出场移除锁定状态
                    LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"出场{orderNo}移除锁单.");
                }

                foreach (var no in ConfirmRelease.Results.Keys)
                {
                    isSearchCarData = false;
                    //场内缴费的记录推送下来只要停车订单在出口车道就默认缴费放行
                    ConfirmRelease.Results.TryGetValue(no, out var data);
                    if (data == null)
                    {
                        if (!string.IsNullOrEmpty(d.Item13))
                        {
                            //根据车牌号查弹窗信息
                            data = ConfirmRelease.Results.Values.Where(x => x.passres?.carno == carNo && x.passres?.passway?.Passway_No == d.Item13).FirstOrDefault();
                            if (data != null) { isSearchCarData = true; }
                        }
                    }

                    if (data != null)
                    {
                        //当前停车订单的是否属于追缴订单
                        var isUnpaid = false;
                        if (data.unpaidresult != null)
                        {
                            if (data.unpaidresult.Find(x => x.unpaidorder?.ParkOrder_No == orderNo) != null)
                            {
                                isUnpaid = true;
                            }
                        }

                        //if (d.Item12 == true)
                        //{
                        //    isUnpaid = false;
                        //    data.unpaidresult = null;
                        //}

                        //弹窗(出入场)的订单号
                        var rsOrderNo = data.resorder?.resOut?.parkorder?.ParkOrder_No ?? data.resorder?.resOut?.noRecordOrder?.ParkOrder_No ?? data.resorder.resIn?.parkorder?.ParkOrder_No ?? data.resorder.resIn?.noRecordOrder?.ParkOrder_No ?? data.passres?.parkorderno;
                        var openGate = true;

                        //判断订单号是否一致，一致则默认自动放行处理，不一致则属于追缴订单直接更新数据
                        if (rsOrderNo == orderNo || isUnpaid || isSearchCarData)
                        {
                            isSearchOrder = true; //找到订单处理


                            if (d.Item1.ParkOrder_PayScene == 2)
                            {
                                LocalMemoryCache.Set($"ConfirmPay:{orderNo}", DateTimeHelper.GetNowTime(), 30); //保存30秒钟的缓存,用于防止无牌车手动出场+扫车道码离场 产生三条识别记录
                            }

                            var mainDevice = DeviceCommonUtil.GetPasswayMainDevice(no);
                            var passwayno = mainDevice.Device_PasswayNo;
                            var carno = data.passres?.carno;
                            if (mainDevice == null)
                            {
                                LogManagementMap.WriteToFileException(null, $"未配置主相机,无法开闸：[{passwayno}][{JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}]");
                            }

                            var code = data.passres.code = data.passres.actcode;

                            //判断订单号是否一致，一致则默认自动放行处理
                            if (d.Item7 != 1 && (rsOrderNo == orderNo || isSearchCarData))
                            {
                                if (!string.IsNullOrEmpty(rsOrderNo))
                                {
                                    if (data.recog?.CarRecog_Time != null)
                                    {
                                        BLL.UnpaidRecord.UpdateStatus(data.recog?.CarRecog_Time, rsOrderNo, carNo, data.resorder?.resOut?.onenter == 0);
                                    }
                                }

                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，订单号与弹窗一致自动放行处理：{rsOrderNo},{data?.payres?.payedamount}");
                                var result = AppBasicCache.GetElement(AppBasicCache.GetOutParkOrder, rsOrderNo);
                                if (result != null && result.Item2 != null) AppBasicCache.GetOutParkOrder.TryRemove(rsOrderNo, out var temp);

                                //缴费完成的默认自动放行处理
                                data.passres.code = 1;
                                var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(pl => pl.Value.PasswayLink_PasswayNo == passwayno);
                                if (areaLink.Count() > 0)
                                {
                                    if (d.Item1.ParkOrder_PayScene != 2 && mainDevice != null)
                                    {
                                        #region 控制板发送来车事件

                                        if (result == null || (result != null && result.Item2 == null)) BarrierDeviceUtilsl.SendCarOrder(data);

                                        #endregion

                                        if (result == null || (result != null && result.Item2 == null))
                                        {
                                            if (d.Item11)
                                            {
                                                //是否开双闸
                                                var(isDoubleGate, linkCameraNoList)  = Command.GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);

                                                //开闸
                                                var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate,linkCameraNoList:linkCameraNoList);
                                                if (!gateResult.Success) //开闸失败
                                                {
                                                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"缴费完成开闸失败[{AppBasicCache.GetAdmin(passwayno)?.Admins_Account}][{carNo}]，{gateResult.Msg}");
                                                }

                                                if (data.recog != null)
                                                {
                                                    //更新执行开闸的结果
                                                    data.recog.CarRecog_OpenStatus = gateResult.Success ? 1 : -1;
                                                    BLL.CarRecog.GetInstance(data.recog.CarRecog_Time ?? DateTime.Now)
                                                        .UpdateOpenStatus(data.recog.CarRecog_No, (int)data.recog.CarRecog_OpenStatus);
                                                }
                                            }
                                        }
                                    }

                                    //只有一个区域而且是出场，则认定为出场； 或 两个区域而且为出口类型 则为内场出口缴费未离场
                                    if (areaLink.Count() > 0)
                                    {
                                        //更新停车订单、停车明细、结算状态、支付订单状态、优惠券状态
                                        ResBody rs = null;
                                        if (data.passres.gate == 0 || data.passres.gate == 3) //areaLink.FirstOrDefault().Value.PasswayLink_GateType == 0
                                        {
                                            if (data.resorder.resOut.onenter == 1)
                                            {
                                                var area = ParkArea.GetEntity(data.passres?.areano);

                                                var onoutdata = new Model.ParkGateOut();
                                                onoutdata.money = 0;
                                                onoutdata.feereason = string.Empty;
                                                onoutdata.isfee = 0;
                                                if (data.passres.cartype != null)
                                                {
                                                    onoutdata.cartypeno = data.passres.cartype.CarType_No;
                                                    onoutdata.cartypename = data.passres.cartype.CarType_Name;
                                                }
                                                if (data.passres.carcardtype != null)
                                                {
                                                    onoutdata.carcardtypeno = data.passres.carcardtype.CarCardType_No;
                                                    onoutdata.carcardtypename = data.passres.carcardtype.CarCardType_Name;
                                                }

                                                rs = PassHelper.CarOutComplete(new ParkGatePass
                                                {
                                                    ParkAreaNo = area?.ParkArea_No,
                                                    ParkAreaName = area?.ParkArea_Name,
                                                    account = AppBasicCache.GetAdmin(passwayno).Admins_Account,
                                                    carno = carno,
                                                    time = data.time,
                                                    img = data.passres.img,
                                                    camerano = data.passres.device.Device_No,
                                                    name = AppBasicCache.GetAdmin(passwayno).Admins_Name,
                                                    orderdetailno = data.passres.orderdetailno,
                                                    orderno = data.passres.parkorderno,
                                                    parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                                                    code = 201,
                                                    isSupplement = data?.isSupplement ?? false,
                                                    onoutdata = onoutdata
                                                }, data.payres, d.Item2, false, false, data.passres.gate == 0, d.Item5, updateCoupon: d.Item14);
                                            }
                                            else
                                            {
                                                rs = PassHelper.CarOutNoRecord(data, AppBasicCache.GetAdmin(passwayno).Admins_Account, AppBasicCache.GetAdmin(passwayno).Admins_Name, data.resorder.resOut.parkorder, true, payColls: d.Item2, generateUnpayOrder: false, updateCoupon: d.Item14);
                                                var newData = TyziTools.Json.ToObject<(PassRecord, ParkOrder, PayColl, int, Car)>(rs.data);
                                                newData.Item3 = d.Item2;
                                                rs.data = TyziTools.Json.ToString(newData);
                                            }

                                            if (rs.success)
                                            {
                                                #region 清理车道缓存

                                                PasswayConfirmReleaseUtil.RemoveResult(no);

                                                #endregion

                                                #region 语音播报

                                                if (result == null || (result != null && result.Item2 == null)) BroadcastUtil.AutoReleaseOut(data, mainDevice, data.recog?.CarRecog_Mode == 5, d.Item2);

                                                #endregion

                                                #region 记录上传处理

                                                var postRecordHandleToType = data.resorder.resOut.onenter == 0 ? 4 : 2;
                                                //if (AppCache.IsWindows)
                                                SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                                {
                                                    PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                                    PostRecordHandle_CarPlate = carno,
                                                    PostRecordHandle_Datas = rs.data,
                                                    PostRecordHandle_ToType = postRecordHandleToType,
                                                    PostRecordHandle_Status = 0,
                                                    PostRecordHandle_ReturnMsg = string.Empty,
                                                    PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                                });

                                                #endregion

                                                _ = CustomThreadPool.ScheduledTaskPool?.QueueTask(null, () =>
                                                {
                                                    ParkSpaceUtil.UpdateDate();
                                                    return Task.CompletedTask;
                                                });
                                            }
                                            else
                                            {
                                                LogManagementMap.WriteToFileException(null, $"[{carno}][{passwayno}] 通行不成功！输出：{TyziTools.Json.ToString(rs)}");
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    LogManagementMap.WriteToFileException(null, $"[{carno}]通行区域信息不存在！");
                                }

                                DataCache.OrderStatus.Set(orderNo, 3);
                                if (rsOrderNo != orderNo) DataCache.OrderStatus.Set(rsOrderNo, 3);
                                WebSocketUtil.SendWSConfirmHandle(2, data);
                            }
                            //不一致则属于追缴订单直接更新数据
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，订单号与弹窗不一致只更新数据：{orderNo}");
                                CommonBLL.PaySuccess(d.Item1, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9, createLedger: true);
                                DataCache.OrderStatus.Set(orderNo, 3);

                                bool checkUnPay = false;
                                if (isUnpaid || d.Item7 == 1)
                                {
                                    int gate = BLL.Passway.GetPasswayGateType(no);
                                    //入口追缴成功,检查健康码
                                    if (gate == 1 || gate == 2)
                                    {
                                        data.passres.errmsg = "补缴完成";
                                        if (data.passres.fycode == 1)
                                        {
                                            data.passres.errmsg = "补缴完成,扫健康码通行";
                                            DeviceServerHandle.HealthCodeScanPass(data, mainDevice);
                                            return new ResPush() { guid = reqPush.guid, code = 1 };
                                        }

                                        checkUnPay = true;
                                    }
                                    //出口追缴完成，判断计费金额，是否需要处理开闸等后续业务
                                    else if (data?.passres?.actcode == 1 && data?.payres?.payed == 0 && data.unpaidresult?.Sum(x => x.payres?.payedamount ?? 0) > 0)
                                    {
                                        checkUnPay = true;
                                    }
                                    else//其它情况（出口还有费用要缴费），刷新弹窗，不处理开闸等后续业务
                                    {
                                        SentryBox.CommHelper.CheckConfirmResultForCarNo(null, rsOrderNo, CloseNoInPark: false);
                                        if (!string.IsNullOrEmpty(rsOrderNo))
                                        {
                                            CalcCache.Del("OrderPrice:" + rsOrderNo);
                                            CalcCache.Del($"OrderPrice1:" + rsOrderNo);
                                            CalcCache.Del($"OrderPrice2:" + rsOrderNo);
                                            CalcCache.Del($"OrderPrice3:" + rsOrderNo);
                                            CalcCache.Del($"OrderPrice4:" + rsOrderNo);
                                            CalcCache.Del("OrderPriceAutoPay:" + rsOrderNo);
                                        }
                                    }
                                }

                                //属于追缴支付，处理业务
                                if (checkUnPay)
                                {
                                    bool haveUnPay = false;//是否还有需要补缴的金额
                                    //入口/出口追缴完成，关窗
                                    if (data?.passres?.actcode == 1 && data.unpaidresult?.Sum(x => x.payres?.payedamount ?? 0) > 0)
                                    {
                                        if (data.unpaidresult.Find(x => x.unpaidorder?.ParkOrder_No != orderNo) == null || d.Item7 == 1)
                                        {
                                            code = 1;
                                        }
                                        else
                                        {
                                            haveUnPay = true;
                                            //还有需要补缴的金额，刷新弹窗
                                            SentryBox.CommHelper.CheckConfirmResultForCarNo(null, rsOrderNo, CloseNoInPark: false);
                                            if (!string.IsNullOrEmpty(rsOrderNo))
                                            {
                                                CalcCache.Del("OrderPrice:" + rsOrderNo);
                                                CalcCache.Del($"OrderPrice1:" + rsOrderNo);
                                                CalcCache.Del($"OrderPrice2:" + rsOrderNo);
                                                CalcCache.Del($"OrderPrice3:" + rsOrderNo);
                                                CalcCache.Del($"OrderPrice4:" + rsOrderNo);
                                                CalcCache.Del("OrderPriceAutoPay:" + rsOrderNo);
                                            }

                                        }

                                    }

                                    if (!haveUnPay)
                                    {
                                        #region 遥控开闸处理
                                        if (code > 0 && data.policy != null && data.policy.takerecord == 1 && data.policy.autotakerecord != 1)
                                        {
                                            if (AppBasicCache.GetSentryPasswayLinkDic.Where(pl => pl.Value.PasswayLink_PasswayNo == passwayno).Count() > 0)
                                            {
                                                if (data.passres.code != 1) openGate = false;
                                                //code = 1;
                                                //data.passres.code = 1;
                                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{carNo} 支付订单下发补缴完成，启用了优先保存记录不开闸！");
                                            }
                                        }

                                        if (!d.Item11) openGate = false;
                                        #endregion

                                        switch (code)
                                        {
                                            //禁止通行
                                            case 0:
                                                {
                                                    #region 语音播报

                                                    BroadcastUtil.NoEntry(data, mainDevice);

                                                    #endregion

                                                    #region 通知岗亭

                                                    WebSocketUtil.SendWS(data);

                                                    #endregion
                                                }
                                                break;
                                            case 1:
                                                await DeviceServerHandle.AutoRelease(data, mainDevice, openGate);
                                                break;
                                            case 2:
                                                data.passres.errmsg = "补缴完成,等待管理员放行";
                                                WebSocketUtil.SendWS(data);

                                                //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                                                if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                                                {
                                                    var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                                                    if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                                                    {
                                                        var leds = new List<LedModel>();
                                                        leds.Add(new LedModel($"补缴完成", 1, 0, 1));
                                                        leds.Add(new LedModel($"请等待管理员确认放行", 1, 1, 1));
                                                        await cameraOfYs.SendLedAsync(leds);
                                                    }
                                                }
                                                else
                                                {

                                                    var data485 = Passthrough485Util.InstantDisplay("补缴完成  请等待管理员确认放行");
                                                    await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, nameof(Passthrough485Util.InstantDisplay));
                                                }

                                                var tmodel1 = new RobotSendPacket
                                                {
                                                    type = RobotCommandType.setDeviceDisplayContent,
                                                    voiceType = (int)RobotVoiceType.ConfirmCurrent,
                                                    data = new
                                                    {
                                                        voiceText = "补缴完成，请等待管理员确认放行",
                                                        displayPageTimeout = 30,
                                                        messageText = "补缴完成，请等待管理员确认放行"
                                                    }
                                                };
                                                DevicePoolsUtil.Device3288Sends(tmodel1, passwayno);
                                                break;
                                            case 3:
                                                DeviceServerHandle.WaitInLine(new CarPlateInfo { CarPlate = carno }, data, passwayno, mainDevice);
                                                break;
                                            case 4:
                                                DeviceServerHandle.MinimumCharge(new CarPlateInfo { CarPlate = carno }, data, passwayno, mainDevice);
                                                break;
                                        }

                                        return new ResPush() { guid = reqPush.guid, code = 1 };
                                    }
                                }
                            }
                        }
                        else if (rsOrderNo != orderNo && !isUnpaid && !isSearchCarData && data.passres?.carno == carNo)
                        {
                            if (data?.passres?.actcode == 1 && data.payres?.payed == 0)
                            {
                                var mainDevice = DeviceCommonUtil.GetPasswayMainDevice(no);
                                if (mainDevice != null)
                                {
                                    #region 遥控开闸处理
                                    var code = data.passres.code = data.passres.actcode;
                                    if (code > 0 && data.policy != null && data.policy.takerecord == 1 && data.policy.autotakerecord != 1)
                                    {
                                        var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(pl => pl.Value.PasswayLink_PasswayNo == mainDevice.Device_PasswayNo);
                                        if (areaLink.Count() > 0)
                                        {
                                            if (data.passres.code != 1) openGate = false;
                                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{carNo} 支付订单下发补缴完成，启用了优先保存记录不开闸！");
                                        }
                                    }

                                    if (!d.Item11) openGate = false;
                                    #endregion

                                    await DeviceServerHandle.AutoRelease(data, mainDevice, openGate);
                                    PasswayConfirmReleaseUtil.RemoveResultByCarNo(carNo);
                                    return new ResPush() { guid = reqPush.guid, code = 1 };
                                }
                            }
                        }
                    }
                }

                //未找到弹窗的订单,直接更新数据
                if (!isSearchOrder)
                {
                    if (d.Item5 != null)
                    {
                        var owner = BLL.Owner.GetEntity("*", $"Owner_No='{d.Item5.Owner_No}'");
                        if (owner != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, owner.Owner_No, owner); }
                    }

                    if (d.Item4 != null)
                    {
                        var car = BLL.Car.GetEntity("*", $"Car_No='{d.Item4.Car_No}'");
                        if (car != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, car.Car_CarNo, car); }
                    }

                    if (AppBasicCache.IsSendTcp && !string.IsNullOrEmpty(d.Item10) && !AppBasicCache.IpList.Contains(d.Item10)) //B30 中间件和岗亭不在同一个IP
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调][{carNo}]缴费，未检测到弹窗，IP[{d.Item10}],订单号：{orderNo}");
                        _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                        {
                            if (d.Item1 != null)
                            {
                                var po = d.Item1;
                                var isUpdateIncar = true;
                                var inModel = BaseBLL._GetEntityByWhere(new InCar(), "InCar_ParkOrderNo,InCar_Status,InCar_EnterTime", $"InCar_CarNo='{po.ParkOrder_CarNo}'");
                                if (inModel != null)
                                {
                                    if (po.ParkOrder_No == inModel.InCar_ParkOrderNo)
                                    {
                                        isUpdateIncar = false;
                                        if (inModel.InCar_Status > EnumParkOrderStatus.In) po.ParkOrder_StatusNo = inModel.InCar_Status;
                                    }
                                    else if (po.ParkOrder_EnterTime <= inModel.InCar_EnterTime) //入场时间小于当前入场记录时间，不更新Incar状态
                                    {
                                        isUpdateIncar = false;
                                        if (po.ParkOrder_StatusNo < EnumParkOrderStatus.Out) po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                                    }

                                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，只更新缴费数据，当前订单状态：{orderNo}，{po?.ParkOrder_StatusNo}");
                                    CommonBLL.PaySuccess(po, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9, isUpdateIncar, createLedger: true);
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，只更新缴费数据，未找到Incar信息");
                                    CommonBLL.PaySuccess(d.Item1, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9, createLedger: true);
                                }
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，只更新缴费数据，无订单");
                                CommonBLL.PaySuccess(d.Item1, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9, createLedger: true);
                            }

                            DataCache.OrderStatus.Set(orderNo, 3);
                            return Task.CompletedTask;
                        });
                    }
                    else //检测是否存在开闸操作的订单缓存，执行车辆出场回调
                    {
                        if (string.IsNullOrEmpty(d.Item10))
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，未检测到弹窗,中间件IP空");
                            _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                            {
                                if (d.Item1 != null)
                                {
                                    var po = d.Item1;
                                    var isUpdateIncar = true;
                                    var inModel = BaseBLL._GetEntityByWhere(new InCar(), "InCar_ParkOrderNo,InCar_Status,InCar_EnterTime", $"InCar_CarNo='{po.ParkOrder_CarNo}'");
                                    if (inModel != null)
                                    {
                                        if (po.ParkOrder_No == inModel.InCar_ParkOrderNo)
                                        {
                                            isUpdateIncar = false;
                                            if (inModel.InCar_Status > EnumParkOrderStatus.In) po.ParkOrder_StatusNo = inModel.InCar_Status;
                                        }
                                        else if (po.ParkOrder_EnterTime <= inModel.InCar_EnterTime) //入场时间小于当前入场记录时间，不更新Incar状态
                                        {
                                            isUpdateIncar = false;
                                            if (po.ParkOrder_StatusNo < EnumParkOrderStatus.Out) po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                                        }

                                        if (po.ParkOrder_StatusNo == EnumParkOrderStatus.Follow)
                                        {
                                            po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                                        }

                                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，只更新缴费数据，当前订单状态：{orderNo}，{po?.ParkOrder_StatusNo}");
                                        CommonBLL.PaySuccess(po, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9, isUpdateIncar);
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，只更新缴费数据，未找到Incar信息");

                                        if (d.Item1?.ParkOrder_StatusNo == EnumParkOrderStatus.Follow)
                                        {
                                            d.Item1.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                                        }

                                        CommonBLL.PaySuccess(d.Item1, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9);
                                    }
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，只更新缴费数据，无订单");
                                    CommonBLL.PaySuccess(d.Item1, d.Item2, d.Item3, d.Item4, d.Item5, d.Item6, d.Item9);
                                }

                                DataCache.OrderStatus.Set(orderNo, 3);
                                return Task.CompletedTask;
                            });
                        }

                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}缴费，未检测到弹窗，IP[{d.Item10}]，订单：{orderNo}");
                        var haveCache = AppBasicCache.GetOutParkOrder.TryGetValue(orderNo, out var result);
                        if (haveCache) //存在开闸操作的订单缓存
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}，存在开闸操作的订单缓存");
                            //检测车辆还在不在场内
                            var inCar = BaseBLL._GetEntityByWhere(new InCar(), "InCar_ID", $"InCar_CarNo='{carNo}' and InCar_ParkOrderNo='{orderNo}' and InCar_Status=200");
                            if (inCar != null)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}，检测车辆还在场内");
                                //车辆在场内，则执行车辆出场回调
                                if (result != null && result.Item2 != null) //判断是否是出场缴费
                                {
                                    if (result.Item2?.passres?.gate == 0)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调][{carNo}][{orderNo}]，执行车辆出场回调");
                                        //更新停车订单、停车明细、结算状态、支付订单状态、优惠券状态
                                        ResBody rs = null;
                                        var passwayno = result.Item2.passres?.passway?.Passway_No;

                                        if (result.Item2.resorder.resOut.onenter == 1)
                                        {
                                            var area = ParkArea.GetEntity(result.Item2.passres?.areano);

                                            var isClose = result.Item2.passres.gate == 0;

                                            rs = PassHelper.CarOutComplete(new ParkGatePass
                                            {
                                                ParkAreaNo = area?.ParkArea_No,
                                                ParkAreaName = area?.ParkArea_Name,
                                                account = AppBasicCache.GetAdmin(passwayno).Admins_Account,
                                                carno = carNo,
                                                time = result.Item2.time,
                                                img = result.Item2.passres.img,
                                                camerano = result.Item2.passres.device.Device_No,
                                                name = AppBasicCache.GetAdmin(passwayno).Admins_Name,
                                                orderdetailno = result.Item2.passres.orderdetailno,
                                                orderno = result.Item2.passres.parkorderno,
                                                parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                                                code = 201,
                                                isSupplement = result.Item2?.isSupplement ?? false,
                                                onoutdata = new ParkGateOut
                                                {
                                                    feereason = string.Empty,
                                                    isfee = 0,
                                                    money = 0
                                                }
                                            }, result.Item2.payres, d.Item2, false, false, isClose, d.Item5);
                                        }
                                        else
                                        {
                                            rs = PassHelper.CarOutNoRecord(result.Item2, AppBasicCache.GetAdmin(passwayno).Admins_Account, AppBasicCache.GetAdmin(passwayno).Admins_Name, result.Item2.resorder.resOut.parkorder, true, generateUnpayOrder: false);
                                            var newData = TyziTools.Json.ToObject<(PassRecord, ParkOrder, PayColl, int, Car)>(rs.data);
                                            newData.Item3 = d.Item2;
                                            rs.data = TyziTools.Json.ToString(newData);
                                        }

                                        if (rs?.success ?? false)
                                        {
                                            #region 记录上传处理

                                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调][{carNo}][{orderNo}]，出场回调结果上传");
                                            var postRecordHandleToType = result.Item2.resorder.resOut.onenter == 0 ? 4 : 2;
                                            //if (AppCache.IsWindows)
                                            SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                            {
                                                PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                                PostRecordHandle_CarPlate = carNo,
                                                PostRecordHandle_Datas = rs.data,
                                                PostRecordHandle_ToType = postRecordHandleToType,
                                                PostRecordHandle_Status = 0,
                                                PostRecordHandle_ReturnMsg = string.Empty,
                                                PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                            });

                                            #endregion

                                            _ = CustomThreadPool.ScheduledTaskPool?.QueueTask(null, () =>
                                            {
                                                ParkSpaceUtil.UpdateDate();
                                                return Task.CompletedTask;
                                            });
                                        }
                                        else
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调][{carNo}][{passwayno}] .通行不成功！输出：{TyziTools.Json.ToString(rs)}");
                                        }
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}，出入口类型：{result.Item2?.passres?.gate}");
                                    }
                                }
                                else
                                {
                                    var order = BLL.ParkOrder.GetEntity(orderNo);
                                    var gate = Passway.GetPasswayGateType(order?.ParkOrder_OutPasswayNo);
                                    LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}，状态：{order?.ParkOrder_StatusNo},出口车道：{order?.ParkOrder_OutPasswayNo},出入口类型：{gate}");
                                    if (order != null && order.ParkOrder_StatusNo == 200 && !string.IsNullOrEmpty(order.ParkOrder_OutPasswayNo))
                                    {
                                        if (gate == 0)
                                        {
                                            var rs = PassHelper.CarOutCompleteForNoPassResult(orderNo);
                                            if (rs?.success ?? false)
                                            {
                                                #region 记录上传处理

                                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调][{carNo}][{orderNo}]，出场回调结果上传.");
                                                //if (AppCache.IsWindows)
                                                SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                                {
                                                    PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                                    PostRecordHandle_CarPlate = carNo,
                                                    PostRecordHandle_Datas = rs.data,
                                                    PostRecordHandle_ToType = order.ParkOrder_IsNoInRecord == 1 ? 4 : 2,
                                                    PostRecordHandle_Status = 0,
                                                    PostRecordHandle_ReturnMsg = string.Empty,
                                                    PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                                });

                                                #endregion
                                            }
                                            else
                                            {
                                                LogManagementMap.WriteToFileException(null, $"[支付回调][{carNo}][{order?.ParkOrder_OutPasswayNo}] .通行不成功！输出：{TyziTools.Json.ToString(rs)}");
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调]{carNo}，检测车辆已不在场内，不做更新");
                            }

                            AppBasicCache.GetOutParkOrder.TryRemove(orderNo, out var temp);
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"[支付回调][{carNo}][{orderNo}]，支付结果下发后但未检测到岗亭缴费弹窗，不处理停车订单状态");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"缴费信息处理异常：{TyziTools.Json.ToString(reqPush)}");
        }

        return new ResPush() { guid = reqPush.guid, code = 1 };
    }

}

/// <summary>
/// 获取岗亭当前指定车道的缴费订单
/// </summary>
public class PasswayOrderHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "PasswayOrder";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        ResPush resPush = null;
        var estModel = new EstOutPayInfoResult
        {
            code = 0
        };
        try
        {
            var sRemark = string.Empty;
            var temp = TyziTools.Json.ToObject<PasswayOrder>(reqPush.data);
            resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };
            if (ConfirmRelease.Results.TryGetValue(temp.Passway_No, out var data))
            {
                var carno = data.passres?.carno;
                if (data.success)
                {
                    //是否允许通行，0 - 禁止通行 ,1 - 自动放行，2 - 弹窗确认放行,3 - 排队等候,4 - 最低收费缴费通行(出口时未找到记录)
                    switch (data.passres.code)
                    {
                        case 0:
                            {
                                sRemark = "禁止通行";
                                break;
                            }
                        case 1:
                            {
                                sRemark = "自动放行";
                                break;
                            }
                        case 2:
                            {
                                if (data.resorder.resOut.onenter == 1 && data.payres != null)
                                {
                                    estModel.code = 1;
                                    estModel.OrderNo = data.passres.parkorderno;
                                    estModel.chargeMoney = data.payres.payedamount;
                                    var sCouponKey = new List<string>();
                                    if (data.payres.uselist != null && data.payres.uselist.Count > 0)
                                    {
                                        sCouponKey = data.payres.uselist.Select(x => x.CouponRecord_No).ToList();
                                    }

                                    estModel.CouponKey = sCouponKey;
                                    estModel.payResult = data.payres;
                                }
                                else
                                {
                                    sRemark = $"[{carno}]无入场记录需人工确认";
                                }

                                break;
                            }
                        case 3:
                            {
                                sRemark = "排队等候";
                            }
                            break;
                        case 4:
                            {
                                estModel.code = 1;
                                estModel.OrderNo = carno;
                                estModel.chargeMoney = data.payres.payedamount - data.payres.cashrobotamount;
                                var sCouponKey = new List<string>();
                                if (data.payres.uselist != null && data.payres.uselist.Count > 0)
                                {
                                    sCouponKey = data.payres.uselist.Select(x => x.CouponRecord_No).ToList();
                                }

                                estModel.CouponKey = sCouponKey;
                                estModel.payResult = data.payres;
                                sRemark = "最低收费";
                                break;
                            }
                    }
                }
                else
                {
                    sRemark = $"[{carno}]检测通行权限失败:[{data.errmsg}]";
                }
            }
            else
            {
                estModel.code = 2;
                sRemark = "未检测到车辆出场！";
            }

            estModel.msg = sRemark;
            resPush.data = estModel;
            if (context != null)
            {
                var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                byteBuffer1.WriteBytes(bytes1);
                context.Channel.WriteAndFlushAsync(byteBuffer1);
            }
        }
        catch (Exception ex)
        {
            if (context != null && resPush != null)
            {
                estModel.code = 0;
                estModel.msg = "获取车道车辆失败";
                resPush.data = estModel;
                var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                byteBuffer1.WriteBytes(bytes1);
                context.Channel.WriteAndFlushAsync(byteBuffer1);
            }

            LogManagementMap.WriteToFileException(ex, $"获取车道实时订单异常：{TyziTools.Json.ToString(reqPush)}");
        }
        return Task.FromResult(resPush);
    }
}

public class CarInHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "carin,carout,ParkOrder,payandeditorder,updateorder,parkarea";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        if (reqPush?.tname.ToLower() == "parkarea")
        {
            try
            {
                AppBasicCache.LoadBasicData();
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"更新基础数据缓存信息更新发生异常：[{TyziTools.Json.ToString(reqPush)}]");
            }
        }

        _ = CustomThreadPool.ScheduledTaskPool?.QueueTask(null, () =>
        {
            ParkSpaceUtil.UpdateDate();
            return Task.CompletedTask;
        });

        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 显示屏参数设置
/// </summary>
public class DisplayScreenHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "displayscreen1,displayscreen2,displayscreen3,displayscreen4";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var result = TyziTools.Json.ToObject<List<DisplayScreen>>(reqPush.data);
            if (result == null || result.Count == 0)
            {
                return new ResPush() { guid = reqPush.guid, code = 1 };
            }

            var data = result[0];
            var noList = data.DisplayScreen_DeviceNo.Split(',');
            //多个设备
            foreach (var no in noList)
            {
                var d = AppBasicCache.GetSentryDeviceLinking.TryGetValue(no, out var device);
                if (d)
                {
                    try
                    {
                        //广告
                        if (reqPush?.tname.ToLower() == "displayscreen3" || reqPush?.tname.ToLower() == "displayscreen4")
                        {
                            var bytes = new List<byte[]>();
                            if (reqPush?.tname.ToLower() == "displayscreen4") //设置显示类型
                            {
                                var byte1 = Passthrough485Util.SetAdFactoryParameter(data.DisplayScreen_DisplayType.Value, data.DisplayScreen_DisplayPattern.Value);
                                bytes.Add(byte1);
                            }
                            else //设置广告内容
                            {
                                if (data.DisplayScreen_DisplayType == 0)
                                {
                                    bytes.AddRange(Passthrough485Util.SetAdMainContent(data.DisplayScreen_AdvertText1));
                                }
                                else
                                {
                                    bytes.AddRange(Passthrough485Util.SetAdMainContent(data.DisplayScreen_AdvertText1));
                                    var ret2 = Passthrough485Util.SetAdBottomLayer(data.DisplayScreen_AdvertText2);
                                    bytes.Add(ret2);
                                }
                            }

                            foreach (var item in bytes)
                            {
                                await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), item, nameof(Passthrough485Util.SetLedSoundLevel));
                            }
                        }
                        else
                        {
                            byte[] bytes1;
                            //音量
                            if (reqPush?.tname.ToLower() == "displayscreen1")
                            {
                                bytes1 = Passthrough485Util.SetLedSoundLevel(LedSoundType.Sound, (byte)Convert.ToInt32(data.DisplayScreen_VolumeOpenTime), (byte)Convert.ToInt32(data.DisplayScreen_VolumeCloseTime), innersoundLevel: (SoundLevelType)data.DisplayScreen_VolumeWithin, outsoundLevelType: (SoundLevelType)data.DisplayScreen_VolumeOther);
                                await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), bytes1, nameof(Passthrough485Util.SetLedSoundLevel));
                            }

                            //LED灯
                            if (reqPush?.tname.ToLower() == "displayscreen2")
                            {
                                bytes1 = Passthrough485Util.SetLedSoundLevel(LedSoundType.Led, (byte)Convert.ToInt32(data.DisplayScreen_LEDCloseTime), (byte)Convert.ToInt32(data.DisplayScreen_LEDOpenTime), (LEDLevelType)(160 + data.DisplayScreen_LEDLightBright));
                                await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), bytes1, nameof(Passthrough485Util.SetLedSoundLevel));
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, $"[{device.Device_Name}]显示屏参数设置,未找到设备信息：[{TyziTools.Json.ToString(reqPush)}],{e}");
                    }
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, $"显示屏参数设置,未找到设备信息：[{TyziTools.Json.ToString(reqPush)}]");
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"显示屏参数设置发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return new ResPush() { guid = reqPush.guid, code = 1 };
    }
}

/// <summary>
/// 余位屏参数设置
/// </summary>
public class ResidualHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "residual";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var result = TyziTools.Json.ToObject<List<Residual>>(reqPush.data);
            if (result == null || result.Count == 0)
            {
                return new ResPush() { guid = reqPush.guid, code = 1 };
            }

            var data = result[0];

            var noList = data.Residual_DeviceNo.Split(',');
            var sTypeName = string.Empty;
            var bytes = new List<byte[]>();
            //多个设备
            foreach (var no in noList)
            {
                var d = AppBasicCache.GetSentryDeviceLinking.TryGetValue(no, out var device);
                if (d)
                {
                    try
                    {
                        var port = (SerialIndexType)((device.Device_Com ?? 0) == 0 ? 1 : 0);
                        //单行余位屏基础参数
                        await CameraController.SendDataBy485Async(device.Device_IP, port, Passthrough485Util.SetDisplayFactoryParameter((byte)data.Residual_RdMachineNumber, 0,
                            (DisplayMoveMode)data.Residual_RdMobileMode,
                            (DisplayMoveLevel)data.Residual_RdMovingSpeed,
                            (byte)data.Residual_RdRestTime,
                            (DisplayColor)data.Residual_RdShowYa,
                            (byte)data.Residual_RdDisplayTime,
                            (byte)data.Residual_RdModuleQuantity,
                            (DisplayInterfaceType)data.Residual_RdInterfaceType,
                            (DisplayReverse)data.Residual_RdInverseEnable,
                            (DisplayDefaultShow)data.Residual_RdDefaultDisplay,
                            0), nameof(Passthrough485Util.SetDisplayFactoryParameter));

                        if (data.Residual_RdDefaultDisplay == 2) //空车位
                        {
                            await Task.Delay(800);
                            //车位数前缀参数
                            await CameraController.SendDataBy485Async(device.Device_IP, port, Passthrough485Util.SetDisplayCarNumberPrefix((byte)data.Residual_RdMachineNumber, 0,
                                (DisplayMoveMode)data.Residual_RdMobileMode,
                                (DisplayMoveLevel)data.Residual_RdMovingSpeed,
                                (byte)data.Residual_RdRestTime,
                                (DisplayColor)data.Residual_RdShowYa,
                                (byte)data.Residual_RdDisplayTime,
                                data.Residual_RdPrefix), nameof(Passthrough485Util.SetDisplayCarNumberPrefix));
                        }
                        else //广告
                        {
                            await Task.Delay(800);
                            //广告参数
                            await CameraController.SendDataBy485Async(device.Device_IP, port, Passthrough485Util.SetDisplayAd((byte)data.Residual_RdMachineNumber, 0,
                                (DisplayMoveMode)data.Residual_DlMobileMode,
                                (DisplayMoveLevel)data.Residual_DlMovingSpeed,
                                (byte)data.Residual_DlRestTime,
                                (DisplayColor)data.Residual_DlShowYa,
                                (byte)data.Residual_DlDisplayTime,
                                (byte)data.Residual_DlModuleQuantity,
                                data.Residual_RdPrefix), nameof(Passthrough485Util.SetDisplayAd));
                        }

                        if (data.Residual_DoubleLineDisplay == 1)
                        {
                            //双行余位屏基础参数
                            await Task.Delay(800);
                            await CameraController.SendDataBy485Async(device.Device_IP, port, Passthrough485Util.SetDisplayFactoryParameter((byte)data.Residual_RdMachineNumber, (DisplayUpDownMode)1,
                                (DisplayMoveMode)data.Residual_DlMobileMode,
                                (DisplayMoveLevel)data.Residual_DlMovingSpeed,
                                (byte)data.Residual_DlRestTime,
                                (DisplayColor)data.Residual_DlShowYa,
                                (byte)data.Residual_DlDisplayTime,
                                (byte)data.Residual_DlModuleQuantity,
                                (DisplayInterfaceType)data.Residual_DlInterfaceType,
                                (DisplayReverse)data.Residual_DlInverseEnable,
                                (DisplayDefaultShow)data.Residual_DlDefaultDisplay,
                                0), nameof(Passthrough485Util.SetDisplayFactoryParameter));

                            if (data.Residual_DlDefaultDisplay == 2) //空车位
                            {
                                await Task.Delay(800);
                                //车位数前缀参数
                                await CameraController.SendDataBy485Async(device.Device_IP, port, Passthrough485Util.SetDisplayCarNumberPrefix((byte)data.Residual_RdMachineNumber, (DisplayUpDownMode)1,
                                    (DisplayMoveMode)data.Residual_RdMobileMode,
                                    (DisplayMoveLevel)data.Residual_RdMovingSpeed,
                                    (byte)data.Residual_RdRestTime,
                                    (DisplayColor)data.Residual_RdShowYa,
                                    (byte)data.Residual_RdDisplayTime,
                                    data.Residual_DlAdvert), nameof(Passthrough485Util.SetDisplayCarNumberPrefix));
                            }
                            else //广告
                            {
                                await Task.Delay(800);
                                //广告参数
                                await CameraController.SendDataBy485Async(device.Device_IP, port, Passthrough485Util.SetDisplayAd((byte)data.Residual_RdMachineNumber, (DisplayUpDownMode)1,
                                    (DisplayMoveMode)data.Residual_DlMobileMode,
                                    (DisplayMoveLevel)data.Residual_DlMovingSpeed,
                                    (byte)data.Residual_DlRestTime,
                                    (DisplayColor)data.Residual_DlShowYa,
                                    (byte)data.Residual_DlDisplayTime,
                                    (byte)data.Residual_DlModuleQuantity,
                                    data.Residual_DlAdvert), nameof(Passthrough485Util.SetDisplayAd));
                            }
                        }


                        await Task.Delay(800);
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFileException(e, $"[{device.Device_Name}]余位屏参数设置发生异常：[{TyziTools.Json.ToString(reqPush)}],{e}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"余位屏参数设置发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return new ResPush() { guid = reqPush.guid, code = 1 };
    }

}

/// <summary>
/// 智慧道闸配置
/// </summary>
public class LanHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "lanectrl";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var result = TyziTools.Json.ToObject<List<LaneCtrl>>(reqPush.data);
            if (result == null || result.Count == 0)
            {
                return new ResPush() { guid = reqPush.guid, code = 1 };
            }

            var data = result[0];

            var noList = data.LaneCtrl_DeviceNo.Split(',');
            var sTypeName = string.Empty;
            //多个设备
            foreach (var no in noList)
            {
                var d = AppBasicCache.GetSentryDeviceLinking.TryGetValue(no, out var device);
                if (d)
                {
                    try
                    {
                        var port = (SerialIndexType)(device.Device_Com ?? 0);

                        var fv = DevicePool.Instance.GetDevice(device.Device_IP);
                        if (fv != null && fv is BarrierOfY312 barrier)
                        {
                            var msgId = DateTime.Now.ToString("yyyyMMddHHmmssfff") + new Random().Next(0, 1000).ToString("0000");
                            var datas = new
                            {
                                cmd = "synparas",
                                msgId,
                                deviceNo = no,
                                //ipaddr = data.LaneCtrl_IPAddr,
                                //NetMask = data.,
                                //NetGw = DeviceData.DeviceGateway,
                                //NetDns = DeviceData.DeviceDns,
                                BrakeDrvno = data.LaneCtrl_BrakeDrvno,
                                //BrakeType = $"{DeviceData.BrakeType + 1}",
                                ChannelType = $"{Utils.ObjectToInt(data.LaneCtrl_ChannelType, 1)}",
                                controlOpen = data.LaneCtrl_ControlOpen,
                                delayTime = data.LaneCtrl_delayTime,
                                //deviceTypeNo = DeviceData.DeviceType,
                                LongTimeStopDelay = data.LaneCtrl_LongTimeStopDelay,
                                NocarBackDelay = data.LaneCtrl_NoCarBackDelay,
                                NoCarCloseDelay = data.LaneCtrl_NoCarCloseDelay,
                                ShortCarBackDelay = data.LaneCtrl_ShortCarBackDelay,
                                SysternEnable = "1",
                                timeStamp = Utils.GetDateTime(),
                                EnT120Genser1 = data.LaneCtrl_EnT120Genser1,
                                EnT120Genser3 = data.LaneCtrl_EnT120Genser3,
                                EnIoStataUpdata = data.LaneCtrl_EnIoStataUpData,
                                EnRobBrakeJudge = data.LaneCtrl_EnRobBrakeJudge
                            };
                            var sJson = JsonConvert.SerializeObject(datas);
                            var tempbytes = Encoding.GetEncoding("GBK").GetBytes(sJson);
                            var packet = new BarrierOfY312Packet
                            {
                                Data = tempbytes,
                                IsSend = true,
                                Command = BarrierOfY312Command.Synparas
                            };
                            var sresult = await barrier.SendCmdAsync(packet, "设置参数", true, true, 1000);
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"向车道控制板发送配置信息：deviceNo[{no}]，{sJson}，{sresult}");
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.CommonTcpLog, $"发送数据失败，设备[{no}]设备未连接未找到");
                        }
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFileException(e, $"[{device.Device_Name}]智慧道闸配置发生异常：[{TyziTools.Json.ToString(reqPush)}],{e}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"智慧道闸配置发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return new ResPush() { guid = reqPush.guid, code = 1 };
    }
}

/// <summary>
/// 车辆车主信息处理
/// </summary>
public class CarBlackWhiteHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "owner,addcarowner,delcarowner,onspacepaycharge,addcar,updatecarowner,carimport,reserve,businesscar,blacklist,onspacepaychargelist,car,mthcarfailpush,mthcarfailpush2";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context);
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return Task.FromResult(RunAction(reqPush, context));
    }

    private ResPush RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };
            SendTCPDatas(context, resPush);

            var actList = new List<string> { "add", "edit" };


            switch (reqPush.tname.ToLower())
            {
                case "owner":
                    var datas = TyziTools.Json.ToObject<List<Owner>>(reqPush.data);
                    if (actList.Contains(reqPush.act.ToLower()))
                    {
                        datas?.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, item.Owner_No, item); });
                        HandleCommon.SyncCarWhite(datas, AppBasicCache.SentryHostInfo?.SentryHost_No);
                    }
                    else
                    {
                        datas?.ForEach(item => { AppBasicCache.DeleteElement(AppBasicCache.GetOwner, item.Owner_No, item); });
                        HandleCommon.SyncDelCarBlcakWhite(datas, AppBasicCache.SentryHostInfo?.SentryHost_No);
                    }

                    break;
                case "car":
                    var cardatas = TyziTools.Json.ToObject<List<Car>>(reqPush.data);
                    if (reqPush.act.ToLower() == "delete")
                    {
                        cardatas?.ForEach(item =>
                        {
                            AppBasicCache.DeleteElement(AppBasicCache.GetCar, item.Car_CarNo, item);
                            CommHelper.CheckConfirmResultForCarNo(item.Car_CarNo, CloseNoInPark: false);
                            HandleCommon.SyncDelCarBlcakWhite(new List<Owner> { AppBasicCache.GetElement(AppBasicCache.GetOwner, item.Car_OwnerNo) }, AppBasicCache.SentryHostInfo?.SentryHost_No, new List<Car> { item });
                        });
                    }

                    break;
                case "mthcarfailpush":
                    var carfaildatas = TyziTools.Json.ToObject<List<(Model.Car, bool)>>(reqPush.data);
                    if (reqPush.act.ToLower() == "delete")
                    {
                        carfaildatas?.ForEach(item =>
                        {
                            AppBasicCache.DeleteElement(AppBasicCache.GetCar, item.Item1.Car_CarNo, item.Item1);
                            CommHelper.CheckConfirmResultForCarNo(item.Item1.Car_CarNo, CloseNoInPark: false);

                            HandleCommon.SyncDelCarBlcakWhite(new List<Owner> { AppBasicCache.GetElement(AppBasicCache.GetOwner, item.Item1.Car_OwnerNo) }, AppBasicCache.SentryHostInfo?.SentryHost_No, new List<Car> { item.Item1 });
                        });
                    }

                    break;
                case "mthcarfailpush2":
                    var carfaildatas2 = TyziTools.Json.ToObject<List<(Model.Car, Model.Owner, List<Model.StopSpace>)>>(reqPush.data);
                    if (reqPush.act.ToLower() == "delete")
                    {
                        carfaildatas2?.ForEach(item =>
                        {
                            AppBasicCache.DeleteElement(AppBasicCache.GetCar, item.Item1.Car_CarNo, item.Item1);
                            if (item.Item2 != null) AppBasicCache.DeleteElement(AppBasicCache.GetOwner, item.Item2.Owner_No, item.Item2);

                            CommHelper.CheckConfirmResultForCarNo(item.Item1.Car_CarNo, CloseNoInPark: false);

                            HandleCommon.SyncDelCarBlcakWhite(new List<Owner> { AppBasicCache.GetElement(AppBasicCache.GetOwner, item.Item1.Car_OwnerNo) }, AppBasicCache.SentryHostInfo?.SentryHost_No, new List<Car> { item.Item1 });
                        });
                    }

                    break;
                case "addcarowner":
                    var carownerData = TyziTools.Json.ToObject<List<PushResultParse.CarOwner>>(reqPush.data);
                    if (carownerData != null)
                    {
                        carownerData.ForEach(x =>
                        {
                            AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Item1.Owner_No, x.Item1);
                            x.Item2?.ForEach(m =>
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, m.Car_CarNo, m);

                                CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false);
                            });
                        });
                        HandleCommon.SyncCarWhite(new List<Owner> { carownerData?.FirstOrDefault()?.Item1 }, AppBasicCache.SentryHostInfo?.SentryHost_No, carownerData?.FirstOrDefault()?.Item2);
                        if (carownerData?.First()?.Item4 != null && carownerData?.First()?.Item4.Count > 0)
                        {
                            HandleCommon.SyncDelCarBlcakWhite(new List<Owner> { carownerData?.FirstOrDefault()?.Item1 }, AppBasicCache.SentryHostInfo?.SentryHost_No, carownerData?.FirstOrDefault()?.Item4);
                        }

                    }

                    break;
                case "updatecarowner":
                    var updata = TyziTools.Json.ToObject<List<PushResultParse.CarOwner>>(reqPush.data);
                    if (updata != null)
                    {
                        updata.ForEach(x =>
                        {
                            AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Item1.Owner_No, x.Item1);
                            x.Item2?.ForEach(m =>
                            {
                                if (m != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, m.Car_CarNo, m); CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false); }
                            });
                            x.Item4?.ForEach(m =>
                            {
                                if (m != null) { AppBasicCache.DeleteElement(AppBasicCache.GetCar, m.Car_CarNo, m); CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false); }
                            });
                            HandleCommon.SyncCarWhite(new List<Owner> { x.Item1 }, AppBasicCache.SentryHostInfo?.SentryHost_No, x.Item2);
                            if (x.Item4 != null && x.Item4.Count > 0)
                            {
                                HandleCommon.SyncDelCarBlcakWhite(new List<Owner> { x.Item1 }, AppBasicCache.SentryHostInfo?.SentryHost_No, x.Item4);
                            }
                        });
                    }

                    break;
                case "onspacepaycharge":
                    var d1 = TyziTools.Json.ToObject<List<PushResultParse.CarOwnerPayDate>>(reqPush.data);
                    d1?.ForEach(x =>
                    {
                        if (x.Item1 != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Item1.Owner_No, x.Item1);
                        x.Item3?.ForEach(m =>
                        {
                            if (m != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, m.Car_CarNo, m); CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false); }
                        });
                        HandleCommon.SyncCarWhite(new List<Owner> { x?.Item1 }, AppBasicCache.SentryHostInfo?.SentryHost_No, x?.Item3);
                    });
                    break;
                case "delcarowner":
                    var d = TyziTools.Json.ToObject<List<PushResultParse.DelCarOwner>>(reqPush.data);
                    if (d != null)
                    {
                        d.ForEach(x =>
                        {
                            x.Item1?.ForEach(m =>
                            {
                                if (m != null) AppBasicCache.DeleteElement(AppBasicCache.GetOwner, m.Owner_No, m);
                            });
                            x.Item3?.ForEach(m =>
                            {
                                if (m != null) { AppBasicCache.DeleteElement(AppBasicCache.GetCar, m.Car_CarNo, m); CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false); }
                            });
                        });
                    }

                    if (d?.First()?.Item3 != null && d?.First()?.Item3.Count > 0)
                    {
                        HandleCommon.SyncDelCarBlcakWhite(d?.First()?.Item1, AppBasicCache.SentryHostInfo?.SentryHost_No, d?.First()?.Item3);
                    }

                    break;
                case "addcar":
                    var d2 = TyziTools.Json.ToObject<List<PushResultParse.AddCar>>(reqPush.data);
                    d2?.ForEach(x =>
                    {
                        if (x.Item2 != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Item2.Owner_No, x.Item2);
                        if (x.Item1 != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Item1.Car_CarNo, x.Item1); CommHelper.CheckConfirmResultForCarNo(x.Item1.Car_CarNo, CloseNoInPark: false); }
                    });

                    if (d2?.First()?.Item2 != null && d2?.First()?.Item1 != null)
                    {
                        HandleCommon.SyncCarWhite(new List<Owner> { d2?.First()?.Item2 }, AppBasicCache.SentryHostInfo?.SentryHost_No, new List<Car> { d2?.First()?.Item1 });
                    }

                    break;
                case "carimport":
                    var d3 = TyziTools.Json.ToObject<List<PushResultParse.CarPush>>(reqPush.data);
                    if (d3 != null)
                    {
                        d3.ForEach(x =>
                        {
                            x.Item2?.ForEach(m =>
                            {
                                if (m != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, m.Owner_No, m);


                            });
                            x.Item1?.ForEach(m =>
                            {
                                if (m != null)
                                {
                                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, m.Car_CarNo, m);
                                    CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false);
                                }
                            });

                            try
                            {
                                HandleCommon.SyncCarWhite(x.Item2, AppBasicCache.SentryHostInfo?.SentryHost_No, x.Item1);
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "车辆导入，下发白名单异常：" + ex.ToString());
                            }

                        });
                    }

                    break;
                case "reserve":
                    if ("mq_del,mq_add".Contains(reqPush.act))
                    {
                        var noList = reqPush.data?.Split(',') ?? new string[] { };
                        if (reqPush.act == "mq_del")
                        {
                            foreach (var item in noList)
                            {
                                AppBasicCache.DeleteElement(AppBasicCache.GetReserve, item, new Reserve { Reserve_No = item });
                            }
                        }
                        else
                        {
                            var mList = BLL.Reserve.GetAllEntity("*", $"Reserve_No in ('{string.Join("','", noList)}')");
                            foreach (var item in mList)
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetReserve, item.Reserve_No, item);
                            }
                        }
                    }
                    else
                    {
                        var d4 = TyziTools.Json.ToObject<List<Reserve>>(reqPush.data);
                        if (d4 != null)
                        {
                            d4.ForEach(item =>
                            {
                                if (item != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetReserve, item.Reserve_No, item);
                            });
                        }
                    }

                    break;
                case "businesscar":
                    if ("mq_del,mq_add".Contains(reqPush.act))
                    {
                        var noList = reqPush.data?.Split(',') ?? new string[] { };
                        if (reqPush.act == "mq_del")
                        {
                            foreach (var item in noList)
                            {
                                AppBasicCache.DeleteElement(AppBasicCache.GetBusinessCar, item, new BusinessCar { BusinessCar_No = item });
                            }
                        }
                        else
                        {
                            var mList = BLL.BusinessCar.GetAllEntity("*", $"BusinessCar_No in ('{string.Join("','", noList)}')");
                            foreach (var item in mList)
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBusinessCar, item.BusinessCar_No, item);
                            }
                        }
                    }
                    else
                    {
                        var d5 = TyziTools.Json.ToObject<List<BusinessCar>>(reqPush.data);
                        if (d5 != null)
                        {
                            d5.ForEach(item => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBusinessCar, item.BusinessCar_No, item); });
                        }
                    }

                    break;
                case "blacklist":
                    var d6 = TyziTools.Json.ToObject<List<BlackList>>(reqPush.data);
                    if (d6 != null && d6.Any())
                    {
                        d6.ForEach(item =>
                        {
                            var old = AppBasicCache.GetBlackList.Values.Where(x => x.BlackList_CarNo == item.BlackList_CarNo)?.FirstOrDefault();
                            if (old != null) { AppBasicCache.DeleteElement(AppBasicCache.GetBlackList, old.BlackList_No, old); }
                            AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBlackList, item.BlackList_No, item);
                        });
                        HandleCommon.SyncBlcak(d6.Select(m => m.BlackList_No).ToList());
                    }

                    break;
                case "onspacepaychargelist":
                    var d7 = TyziTools.Json.ToObject<List<PushResultParse.DelCarOwner>>(reqPush.data);
                    if (d7 != null)
                    {
                        d7.ForEach(x =>
                        {
                            x.Item1?.ForEach(m =>
                            {
                                if (m != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, m.Owner_No, m);
                            });
                            x.Item3?.ForEach(m =>
                            {
                                if (m != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, m.Car_CarNo, m); CommHelper.CheckConfirmResultForCarNo(m.Car_CarNo, CloseNoInPark: false); }
                            });
                            if (x.Item1 != null && x.Item3 != null) HandleCommon.SyncCarWhite(x.Item1, AppBasicCache.SentryHostInfo?.SentryHost_No, x.Item3);
                        });
                    }

                    break;
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"黑白名单处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return new ResPush() { guid = reqPush.guid, code = 1 };
    }
}

/// <summary>
/// 白名单信息处理
/// </summary>
public class SyncWhiteRecordHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "syncwhiterecord";


    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            if (reqPush.data == null) return new ResPush() { guid = reqPush.guid, code = 1 };

            var datas = TyziTools.Json.ToObject<List<WhiteRecord>>(reqPush.data);
            if (datas != null)
            {
                var devices = BLL.Device.GetAllEntity("Device_No,Device_Name,Device_IP,Device_SentryHostNo,Device_PasswayNo", $"Device_No in ('{string.Join("','", datas.Select(x => x.WhiteRecord_DeviceNo))}')");
                var cars = BLL.Car.GetAllEntity("*", $"Car_CarNo in ('{string.Join("','", datas.Select(x => x.WhiteRecord_CarNo))}')");
                var noes = cars.Select(x => x.Car_OwnerNo).ToList().Distinct().ToList();
                var owners = BLL.Owner.GetAllEntity("Owner_No,Owner_EnableOffline,Owner_CardType", $"Owner_No in ('{string.Join("','", noes)}')");
                var AllstopSpaceList = BaseBLL._GetAllEntity(new StopSpace(), "*", $"StopSpace_OwnerNo in ('{string.Join("','", cars.Select(x => x.Car_OwnerNo))}')");

                BWhiteListModel listModel;
                for (var i = 0; i < datas.Count; i++)
                {
                    if (datas[i] == null) continue;

                    try
                    {
                        var device = devices.Find(x => x.Device_No == datas[i].WhiteRecord_DeviceNo);
                        var car = cars.Find(x => x.Car_CarNo == datas[i].WhiteRecord_CarNo);
                        if (device == null)
                        {
                            var remark = "";
                            if (device == null) remark = "未找到设备信息";
                            //if (car == null && (datas[i].WhiteRecord_Status == null || datas[i].WhiteRecord_Status == 0 || datas[i].WhiteRecord_Status == 1 || datas[i].WhiteRecord_Status == 2)) remark += $"{(string.IsNullOrEmpty(remark) ? "" : ",")}未找到车辆信息";

                            if (!string.IsNullOrEmpty(remark))
                            {
                                remark += "，无法同步处理";
                                ParkApi.UpdateWhiteStatus(datas[i].WhiteRecord_DeviceNo, datas[i].WhiteRecord_CarNo, "0", remark);
                                continue;
                            }
                        }

                        if (device.Device_SentryHostNo != AppBasicCache.GetSentryHost()?.SentryHost_No && (datas[i].WhiteRecord_Status == null || datas[i].WhiteRecord_Status == 0 || datas[i].WhiteRecord_Status == 1 || datas[i].WhiteRecord_Status == 2))
                        {
                            ParkApi.UpdateWhiteStatus(datas[i].WhiteRecord_DeviceNo, datas[i].WhiteRecord_CarNo, "0", "该设备未与当前岗亭关联，无法同步处理");
                            continue;
                        }

                        var black = AppBasicCache.GetBlackList.Values.Where(x => x.BlackList_CarNo == datas[i].WhiteRecord_CarNo).FirstOrDefault();
                        bool isBlack = car != null ? false : (black?.Blacklist_Status == 1 ? true : false);

                        if (car == null && !isBlack) { datas[i].WhiteRecord_Status = 4; } else { datas[i].WhiteRecord_Status = 2; }
                        var owner = owners.Find(x => x.Owner_No == car?.Car_OwnerNo);
                        switch (datas[i].WhiteRecord_Status)
                        {
                            case null:
                            case 0: //未处理
                            case 1: //添加成功
                            case 2: //添加失败

                                if (!isBlack)
                                {
                                    listModel = new BWhiteListModel
                                    {
                                        CarPlate = car?.Car_CarNo ?? datas[i].WhiteRecord_CarNo,
                                        CarColor = car.Car_Colour,
                                        CarId = Utils.ObjectToInt(car?.Car_ID, 0),
                                        CreateTime = car.Car_AddTime ?? DateTime.Now,
                                        StartTime = Convert.ToDateTime(car?.Car_BeginTime),
                                        EndTime = Convert.ToDateTime(car?.Car_EndTime),
                                        CarType = AppBasicCache.GetElement(AppBasicCache.GetCarTypes, car?.Car_VehicleTypeNo)?.CarType_Name ?? "",
                                        SRemark = car?.Car_Remark,
                                        UserId = car?.Car_OwnerNo?.GetHashCode() ?? 0,
                                        UserName = car?.Car_OwnerName,
                                        IsWhite = true
                                    };
                                    if (owner?.Owner_EnableOffline == 1 && car.Car_Status == 1)
                                    {
                                        listModel.IsEnable = true;
                                    }
                                    else
                                    {
                                        listModel.IsEnable = false;
                                    }
                                }
                                else
                                {
                                    listModel = new BWhiteListModel
                                    {
                                        CarPlate = black.BlackList_CarNo,
                                        CarId = Utils.ObjectToInt(black.BlackList_ID, 0),
                                        CreateTime = black.BlackList_AddTime ?? DateTimeHelper.GetNowTime(),
                                        StartTime = Convert.ToDateTime(black.BlackList_BeginTime),
                                        EndTime = Convert.ToDateTime(black.BlackList_EndTime),
                                        SRemark = black.BlackList_Remark,
                                        UserId = Convert.ToInt32(black.BlackList_ID),
                                        IsWhite = false,
                                        IsEnable = true
                                    };

                                    if (CameraController.SendWhitelistAsync(device.Device_IP, listModel).ConfigureAwait(false).GetAwaiter().GetResult())
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.SyncList, $"向主识别设备[{device.Device_IP}]下载黑名单[{black.BlackList_CarNo}]成功！");
                                        //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "1");
                                        BLL.CommonBLL.UpdateWhiteStatus("1", listModel.CarPlate, device.Device_No, "操作成功");
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFileException(null, $"向主识别设备[{device.Device_IP}]下载黑名单[{black.BlackList_CarNo}]失败！");
                                        //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "2");
                                        BLL.CommonBLL.UpdateWhiteStatus("2", listModel.CarPlate, device.Device_No, "相机未能同步该黑名单,请检查相机在线状态");
                                    }
                                    Task.Delay(50).Wait();
                                    continue;
                                }

                                try
                                {
                                    bool isPrepaid = false;
                                    if (owner?.Owner_CardType == 2) { isPrepaid = true; listModel.EndTime = DateTime.Now.AddYears(50); }


                                    bool isAllAuthArea = false;
                                    var SendPasswayNoList = new List<string>();
                                    var SendAreaNoList = new List<string>();
                                    if (!isPrepaid)
                                    {
                                        //查询车辆车主的可停车区域
                                        var stopSpaceList = AllstopSpaceList.Where(x => x.StopSpace_OwnerNo == car.Car_OwnerNo);
                                        //判断设备是否属于授权区域

                                        foreach (var stopSpace in stopSpaceList)
                                        {
                                            if (stopSpace != null && stopSpace.StopSpace_Number > 0)
                                            {
                                                if (stopSpace.StopSpace_Type == 1)
                                                {
                                                    if (!string.IsNullOrWhiteSpace(stopSpace.StopSpace_AreaNo))
                                                    {
                                                        var sare1 = JsonConvert.DeserializeObject<List<string>>(stopSpace.StopSpace_AreaNo);
                                                        SendAreaNoList.AddRange(sare1);
                                                        var fares = AppBasicCache.GetAllPasswayLink.Values.Where(pare => stopSpace.StopSpace_AreaNo.Contains(pare.PasswayLink_ParkAreaNo)).ToList();
                                                        if (fares.Count() > 0 && sare1.Count > 0)
                                                        {
                                                            var noes1 = fares.Select(x => x.PasswayLink_PasswayNo).ToList();
                                                            SendPasswayNoList.AddRange(noes1);
                                                            SendPasswayNoList = SendPasswayNoList.Distinct().ToList();
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    isAllAuthArea = true;
                                                    SendPasswayNoList = AppBasicCache.GetAllPassway.Values.Select(x => x.Passway_No).ToList();
                                                    break;
                                                }
                                            }
                                        }

                                        if (!isAllAuthArea)
                                        {
                                            //既是外场出口，又是内场入口,并且没有内场区域授权的车道，不发白名单
                                            for (var y = 0; y < SendPasswayNoList.Count; y++)
                                            {
                                                var passway = AppBasicCache.GetElement(AppBasicCache.GetAllPassway, SendPasswayNoList[y]);
                                                if (passway != null)
                                                {
                                                    int gate = BLL.Passway.GetPasswayGateType(SendPasswayNoList[y]);
                                                    if (gate == 2)
                                                    {
                                                        //车道绑定的区域
                                                        var areas = AppBasicCache.GetAllPasswayLink.Values.Where(pare => pare.PasswayLink_PasswayNo == SendPasswayNoList[y]).Select(x => x.PasswayLink_ParkAreaNo).ToList();
                                                        if (areas?.Count > 1)
                                                        {
                                                            var area1 = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, areas[0]);
                                                            var area2 = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, areas[1]);
                                                            if (area1 != null && area2 != null)
                                                            {
                                                                if (area2.ParkArea_Level > area1.ParkArea_Level)
                                                                {
                                                                    if (!SendAreaNoList.Contains(area2.ParkArea_No)) { SendPasswayNoList.Remove(SendPasswayNoList[y]); y--; }
                                                                }
                                                                else if (area2.ParkArea_Level < area1.ParkArea_Level)
                                                                {
                                                                    if (!SendAreaNoList.Contains(area1.ParkArea_No)) { SendPasswayNoList.Remove(SendPasswayNoList[y]); y--; }
                                                                }
                                                                else
                                                                {
                                                                    if (!SendAreaNoList.Contains(area2.ParkArea_No)) { SendPasswayNoList.Remove(SendPasswayNoList[y]); y--; }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (isPrepaid || SendPasswayNoList.Contains(device.Device_PasswayNo))
                                    {
                                        if (listModel.IsEnable)
                                        {
                                            if (CameraController.SendWhitelistAsync(device.Device_IP, listModel).ConfigureAwait(false).GetAwaiter().GetResult())
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.SyncList, $"向主识别设备[{device.Device_IP}]下载白名单[{car.Car_CarNo}]成功！");
                                                //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "1");
                                                BLL.CommonBLL.UpdateWhiteStatus("1", listModel.CarPlate, device.Device_No, "操作成功");
                                            }
                                            else
                                            {
                                                LogManagementMap.WriteToFileException(null, $"向主识别设备[{device.Device_IP}]下载白名单[{car.Car_CarNo}]失败！");
                                                //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "2");
                                                BLL.CommonBLL.UpdateWhiteStatus("2", listModel.CarPlate, device.Device_No, "相机未能同步该白名单,请检查相机在线状态");
                                            }
                                        }
                                        else
                                        {
                                            //不在停车区域，则删除白名单
                                            var iret1 = CameraController.RemoveWhitelistAsync(device.Device_IP, listModel.CarPlate).GetAwaiter().GetResult();
                                            if (iret1)
                                            {
                                                ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "3");
                                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"向主识别设备[{device.Device_IP}]删除白名单[{car.Car_CarNo}]成功.！");
                                            }
                                            else
                                            {
                                                ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "4");
                                                LogManagementMap.WriteToFileException(null, $"向主识别设备[{device.Device_IP}]删除白名单[{car.Car_CarNo}]失败.！");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        //不在停车区域，则删除白名单
                                        var iret1 = CameraController.RemoveWhitelistAsync(device.Device_IP, listModel.CarPlate)
                                            .ConfigureAwait(false)
                                            .GetAwaiter()
                                            .GetResult();
                                        LogManagementMap.WriteToFile($"车牌号码：[{car.Car_CarNo}]不能停区域，向设备[{device.Device_Name}]删除名单：{iret1}！");

                                        if (iret1)
                                        {
                                            //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "3");
                                            BLL.CommonBLL.UpdateWhiteStatus("3", listModel.CarPlate, device.Device_No, "操作成功");
                                        }
                                        else
                                        {
                                            //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "4");
                                            BLL.CommonBLL.UpdateWhiteStatus("4", listModel.CarPlate, device.Device_No, "相机未能注销该白名单,请检查相机在线状态");
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    //ParkApi.UpdateWhiteStatus(device.Device_No, listModel.CarPlate, "2", "下发相机异常，请检查相机在线状态");
                                    BLL.CommonBLL.UpdateWhiteStatus("2", listModel.CarPlate, device.Device_No, "下发相机异常，请检查相机在线状态");
                                    LogManagementMap.WriteToFileException(ex, $"名单信息处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
                                }

                                break;
                            case 3: //删除成功
                            case 4: //删除失败
                            case 5: //清空成功
                            case 6: //清空失败
                                try
                                {
                                    var iret = await CameraController.RemoveWhitelistAsync(device.Device_IP, datas[i].WhiteRecord_CarNo);
                                    if (iret)
                                    {
                                        //ParkApi.UpdateWhiteStatus(device.Device_No, datas[i].WhiteRecord_CarNo, "3");
                                        BLL.CommonBLL.UpdateWhiteStatus("3", datas[i].WhiteRecord_CarNo, device.Device_No, "操作成功");
                                        LogManagementMap.WriteToFile(LoggerEnum.SyncList, $"向主识别设备[{device.Device_IP}]注销白名单[{datas[i].WhiteRecord_CarNo}]成功！");
                                    }
                                    else
                                    {
                                        //ParkApi.UpdateWhiteStatus(device.Device_No, datas[i].WhiteRecord_CarNo, "4");
                                        BLL.CommonBLL.UpdateWhiteStatus("4", datas[i].WhiteRecord_CarNo, device.Device_No, "相机未能注销该白名单,请检查相机在线状态");
                                        LogManagementMap.WriteToFileException(null, $"向主识别设备[{device.Device_IP}]注销白名单[{datas[i].WhiteRecord_CarNo}]失败！");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    //ParkApi.UpdateWhiteStatus(device.Device_No, datas[i].WhiteRecord_CarNo, "4", "下发相机异常，请检查相机在线状态");
                                    BLL.CommonBLL.UpdateWhiteStatus("3", datas[i].WhiteRecord_CarNo, device.Device_No, "下发相机异常，请检查相机在线状态");
                                    LogManagementMap.WriteToFileException(ex, $"白名单信息处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
                                }

                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFileException(ex, $"名单信息处理发生异常.：[{TyziTools.Json.ToString(reqPush)}]");
                        //parkApi.UpdateWhiteStatus(datas[i]?.WhiteRecord_DeviceNo, datas[i]?.WhiteRecord_CarNo, Utils.ObjectToInt(datas[i]?.WhiteRecord_Status, 0).ToString(), "下发相机异常");
                        BLL.CommonBLL.UpdateWhiteStatus("0", datas[i].WhiteRecord_CarNo, datas[i]?.WhiteRecord_DeviceNo, "下发相机异常");
                    }

                    Task.Delay(50).Wait();
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"白名单信息处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return new ResPush() { guid = reqPush.guid, code = 1 };
    }
}

/// <summary>
/// 计费规则缓存
/// </summary>
public class ChargeRulesHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "addchargerules";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var datas = TyziTools.Json.ToObject<List<(ChargeRules, List<ChargeRelation>)>>(reqPush.data);
            if (datas != null)
            {
                datas.ForEach(item =>
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllChargeRules, item.Item1.ChargeRules_No, item.Item1);
                    AppBasicCache.DeleteElement(AppBasicCache.GetAllChargeRelation, item.Item1.ChargeRules_No, item.Item2);
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllChargeRelation, item.Item1.ChargeRules_No, item.Item2);
                });
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"计费规则缓存{InstructionName}处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}


/// <summary>
/// 取消预约
/// </summary>
public class CancelReserveParkSpaceHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "cancelreserveparkspace";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var data = TyziTools.Json.ToObject<Model.Reserve>(reqPush.data);
            if (data != null)
            {
                var r = BLL.Reserve._AddOrUpdateModel(data);
                if (r == -1)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "取消预约车更新数据失败:" + data.Reserve_CarNo);
                }
                AppBasicCache.DeleteElement(AppBasicCache.GetReserve, data.Reserve_No, data);
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"计费规则缓存{InstructionName}处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}


/// <summary>
/// 计费规则缓存
/// </summary>
public class ChargeRulesDelHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "delchargerules";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }
    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var datas = TyziTools.Json.ToObject<List<ChargeRules>>(reqPush.data);
            if (datas != null)
            {
                datas.ForEach(data =>
                {
                    AppBasicCache.DeleteElement(AppBasicCache.GetAllChargeRules, data.ChargeRules_No, data);
                    AppBasicCache.DeleteElement(AppBasicCache.GetAllChargeRelation, data.ChargeRules_No, new List<ChargeRelation> { new() { ChargeRelation_ChargeRulesNo = data.ChargeRules_No } });
                });
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"计费规则缓存{InstructionName}处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 白名单处理
/// </summary>
public class OwnerHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "ownerblack";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            if (reqPush.act.ToLower() == "addowner")
            {
                int success = 0; int fail = 0;
                HandleCommon.SyncOwnerWhite(reqPush.data?.Split(',').ToList(), AppBasicCache.SentryHostInfo?.SentryHost_No, ref success, ref fail);
            }
            else if (reqPush.act.ToLower() == "addcar")
            {
                int success = 0; int fail = 0;
                HandleCommon.SyncCarWhite(reqPush.data?.Split(',').ToList(), AppBasicCache.SentryHostInfo?.SentryHost_No, ref success, ref fail);
            }
            else if (reqPush.act.ToLower() == "del")
            {
                HandleCommon.SyncDelCarBlcakWhite(reqPush.data?.Split(',').ToList(), AppBasicCache.SentryHostInfo?.SentryHost_No);
            }
            else if (reqPush.act.ToLower() == "clear")
            {
                HandleCommon.SyncClearCarBlcakWhite(AppBasicCache.SentryHostInfo?.SentryHost_No);
            }
            else if (reqPush.act.ToLower() == "addblack")
            {
                HandleCommon.SyncBlcak(reqPush.data?.Split(',').ToList());
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"黑白名单处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }
        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 月租车延期
/// </summary>
public class MthcarchargeHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "mthcarcharge";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"月租车延期：[{reqPush.data}]");
            var datas = TyziTools.Json.ToObject<List<(Car, Owner, PayColl)>>(reqPush.data);
            if (datas != null && datas.Count > 0)
            {
                foreach (var d in datas)
                {
                    if (d.Item1 != null) { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, d.Item1.Car_CarNo, d.Item1); CommHelper.CheckConfirmResultForCarNo(d.Item1.Car_CarNo, CloseNoInPark: false); }
                    if (d.Item2 != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, d.Item2.Owner_No, d.Item2);
                    HandleCommon.SyncCarWhite(new List<Owner> { d.Item2 }, AppBasicCache.SentryHostInfo?.SentryHost_No, new List<Car>() { d.Item1 });
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"月租车延期缓存处理发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 优惠设置更新
/// </summary>
public class ParkDiscountSetHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "parkdiscountset,powergroup";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            WebSocketUtil.SendWSAllLink("updatedata", reqPush?.tname.ToLower() ?? InstructionName);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"优惠设置更新发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }
}

/// <summary>
/// 删除停车订单
/// </summary>
public class DelParkOrderHandle : HandleBase
{
    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "delparkorder";

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        try
        {
            var list = JsonConvert.DeserializeObject<List<ParkOrder>>(reqPush.data);
            if (list != null && list.Count > 0)
            {
                var res = BLL.ParkOrder.CloseList(list);
                if (res >= 0)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"删除订单成功:[{string.Join(",", list.Select(x => x.ParkOrder_No))}][{string.Join(",", list.Select(x => x.ParkOrder_CarNo))}]");
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebTcp, "删除订单执行SQL失败:" + reqPush.data);
                }
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"删除停车订单发生异常：[{TyziTools.Json.ToString(reqPush)}]");
        }

        return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
    }

}

/// <summary>
///
/// </summary>
public class DeviceOptionsHandle : HandleBase
{
    protected static MemoryCacheEntryOptions cacheEntityOps;
    protected static MemoryCacheEntryOptions cacheEntityOpsMoto;

    public DeviceOptionsHandle()
    {
        if (GetMemoryCache == null)
        {
            //缓存的配置
            var cacheOps = new MemoryCacheOptions
            {
                //##注意netcore中的缓存是没有单位的，缓存项和缓存的相对关系
                SizeLimit = 1024,
                //缓存满了时，压缩20%（即删除20份优先级低的缓存项）
                CompactionPercentage = 0.2,
                //30秒钟查找一次过期项
                ExpirationScanFrequency = TimeSpan.FromSeconds(30)
            };
            GetMemoryCache = new MemoryCache(cacheOps);

            //单个缓存项的配置
            cacheEntityOps = new MemoryCacheEntryOptions
            {
                //相对过期时间
                SlidingExpiration = TimeSpan.FromSeconds(30),
                //优先级，当缓存压缩时会优先清除优先级低的缓存项
                Priority = CacheItemPriority.High,
                Size = 1
            };
            //注册缓存项被清除时的回调，可以注册多个回调
            cacheEntityOps.RegisterPostEvictionCallback((key, value, reason, state) =>
            {
                //LogManagementMap.WriteToFile($"回调函数输出[键:{key},值:{value}]");
            });

            //-----------------非机动车道缓存设置----------------------------------//
            //单个缓存项的配置
            cacheEntityOpsMoto = new MemoryCacheEntryOptions
            {
                //相对过期时间
                SlidingExpiration = TimeSpan.FromSeconds(60),
                //优先级，当缓存压缩时会优先清除优先级低的缓存项
                Priority = CacheItemPriority.High,
                Size = 1
            };
            //注册缓存项被清除时的回调，可以注册多个回调
            cacheEntityOpsMoto.RegisterPostEvictionCallback((key, value, reason, state) =>
            {
                //LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"超时未支付自动关单[键:{key},值:{value}");
                if (value is Tuple<ResultPass, ParkOrder> t)
                {
                    if (t.Item2 != null && t.Item1 != null)
                    {
                        try
                        {
                            if (t.Item2.ParkOrder_StatusNo == 200)
                            {
                                //关闭订单记录
                                if (t.Item2.ParkOrder_No == t.Item1?.resorder?.resOut?.parkorder?.ParkOrder_No)
                                {
                                    t.Item2.ParkOrder_StatusNo = 202;
                                    t.Item2.ParkOrder_Remark = "扫码出场创建订单记录超时未支付自动关单";
                                    var rlt = BaseBLL._UpdateByModelByNo(t.Item2);

                                    (PassRecord, ParkOrder, PayColl, int, Car) sdata = (null, t.Item2, null, 2, null);
                                    //if (AppCache.IsWindows)
                                    SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                                    {
                                        PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                        PostRecordHandle_CarPlate = string.Empty,
                                        PostRecordHandle_Datas = TyziTools.Json.ToString(sdata, true),
                                        PostRecordHandle_ToType = 4,
                                        PostRecordHandle_Status = 0,
                                        PostRecordHandle_ReturnMsg = string.Empty,
                                        PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                                    });
                                }
                            }
                        }

                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, "无入场记录关单");
                        }
                    }
                }
            });

            //-----------------非机动车道----------------------------------//
        }
    }

    /// <summary>
    /// 定义一个数据缓存对象
    /// </summary>
    protected static MemoryCache GetMemoryCache { set; get; }

    /// <summary>
    /// 指令名称
    /// </summary>
    public override string InstructionName { get; set; } = "DeviceOptions".ToLower();

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        RunAction(reqPush, context).Wait();
    }

    /// <summary>
    /// 执行处理方法
    /// </summary>
    /// <param name="reqPush">上下文接收到的数据</param>
    /// <param name="context">TCP服务上下文操作通道对象</param>
    public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        return await RunAction(reqPush, context);
    }

    private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
    {
        LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"接收消息DeviceOptions<-{reqPush?.tname}");
        var resPush = new ResPush
        {
            act = reqPush.act,
            tname = reqPush.tname,
            guid = reqPush.guid,
            time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
            code = 1
        };
        var isSuccess = false;
        var value = string.Empty;
        var sRemark = string.Empty;
        var carno = "";
        try
        {
            var ff = JsonConvert.DeserializeObject<DeviceOptionsModel>(reqPush.data);
            if (ff != null)
            {
                carno = ff.sCarplate;
                if (AppBasicCache.GetSentryPasswayDic.TryGetValue(ff.PasswayNo, out var passwayModel))
                {
                    var device = passwayModel.Passway_Type == 1 ? DeviceCommonUtil.GetPasswayMainDevice(ff.PasswayNo) : DeviceCommonUtil.GetPasswayMotoDevice(ff.PasswayNo);
                    if (device != null)
                    {
                        switch (ff.SendOptions)
                        {
                            //发送485指令
                            case SendOptionsType.Send485:
                                {
                                    var bytes = Encoding.UTF8.GetBytes(ff.BaseData);
                                    isSuccess = await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), bytes, ff.DataType);
                                    break;
                                }
                            //平台人工开闸
                            case SendOptionsType.OpenGate:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"平台人工开闸：{ff?.operatorName},{passwayModel?.Passway_Name}");
                                var tfOpenCloseGate1 = await OpenCloseGate(passwayModel, device, 1, ff.DataType, sRemark, ff.operatorName);
                                isSuccess = tfOpenCloseGate1.Item1;
                                sRemark = tfOpenCloseGate1.Item2;
                                break;
                            //平台人工关闸
                            case SendOptionsType.CloseGate:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"平台人工关闸：{ff?.operatorName},{passwayModel?.Passway_Name}");
                                var tfOpenCloseGate2 = await OpenCloseGate(passwayModel, device, 0, ff.DataType, sRemark, ff.operatorName);
                                isSuccess = tfOpenCloseGate2.Item1;
                                sRemark = tfOpenCloseGate2.Item2;
                                break;
                            //道闸常开/取消操作
                            case SendOptionsType.GateLongOpen:
                                var tfOpenCloseGate3 = await OpenCloseGate(passwayModel, device, 2, ff.DataType, sRemark, ff.operatorName);
                                isSuccess = tfOpenCloseGate3.Item1;
                                sRemark = tfOpenCloseGate3.Item2;
                                break;
                            //抓拍上传图片
                            case SendOptionsType.GetImage:
                                var tfst = await SnapShootToImage(device, value, sRemark);
                                isSuccess = tfst.Item1;
                                value = tfst.Item2;
                                sRemark = tfst.Item3;
                                break;
                            //车辆预入场开闸
                            case SendOptionsType.OpenInGate:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"车辆预入场开闸：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                var tfOpenInGate = await OpenInGate(ff, passwayModel, device, sRemark);
                                isSuccess = tfOpenInGate.Item1;
                                sRemark = tfOpenInGate.Item2;
                                break;
                            //车辆确认完成入场
                            case SendOptionsType.EnterCar:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"车辆确认完成入场：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                isSuccess = EnterCar(ff, passwayModel, device, ref sRemark);
                                break;
                            //车辆预出场开闸
                            case SendOptionsType.OpenOutGate:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"车辆预出场开闸：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                var tfOpenOutGate = await OpenOutGate(ff, passwayModel, device, sRemark);
                                isSuccess = tfOpenOutGate.Item1;
                                sRemark = tfOpenOutGate.Item2;
                                break;
                            //车辆确认完成出场
                            case SendOptionsType.OutCar:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"车辆确认完成出场：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                isSuccess = OutCar(ff, passwayModel, device, ref sRemark);
                                break;
                            //获取车道状态
                            case SendOptionsType.GateStatus:
                                var tfGateStatus = await GateStatus(ff, passwayModel, device, sRemark, value);
                                isSuccess = tfGateStatus.Item1;
                                sRemark = tfGateStatus.Item3;
                                value = tfGateStatus.Item2;
                                break;
                            //出场获取指定的订单
                            case SendOptionsType.GetOutGateOrder:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"出场获取指定的订单：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                var tfGetOutGateOrder = await GetOutGateOrder(ff, passwayModel, device, value, sRemark);
                                isSuccess = tfGetOutGateOrder.Item1;
                                value = tfGetOutGateOrder.Item2;
                                sRemark = tfGetOutGateOrder.Item3;
                                break;
                            //云托管手动入场
                            case SendOptionsType.PullManualEnterCar:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"云托管手动入场：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                var tfPullManualEnterCar = await PullManualEnterCar(ff, passwayModel, device, sRemark);
                                isSuccess = tfPullManualEnterCar.Item1;
                                sRemark = tfPullManualEnterCar.Item2;
                                break;
                            //平台扫码入场信息获取
                            case SendOptionsType.GetInGateOrder:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"平台扫码入场信息获取：{passwayModel?.Passway_Name}");
                                var tfGetInGateOrder = GetInGateOrder(passwayModel);
                                isSuccess = tfGetInGateOrder.Item1;
                                value = tfGetInGateOrder.Item2;
                                sRemark = tfGetInGateOrder.Item3;
                                break;
                            //平台推送确认入场
                            case SendOptionsType.PushInGateOrdel:
                                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"平台推送确认入场：{ff?.sCarplate},{passwayModel?.Passway_Name}");
                                var tfPushInGateOrdel = await PushInGateOrdel(ff, passwayModel);
                                isSuccess = tfPushInGateOrdel.Item1;
                                sRemark = tfPushInGateOrdel.Item2;
                                break;
                        }
                    }
                    else
                    {
                        sRemark = $"未找到车道编号[{ff.PasswayNo}]绑定的{(passwayModel.Passway_Type == 1 ? "主识别相机" : "车道控制器")}！";
                    }
                }
                else
                {
                    sRemark = $"未找到车道编号[{ff.PasswayNo}]信息！";
                }
            }
            else
            {
                sRemark = "接收数据反序列化失败！";
            }

            resPush.msg = sRemark;
            resPush.data = JsonConvert.SerializeObject(new
            {
                isSuccess,
                value,
                sRemark
            });
        }
        catch (Exception ex)
        {
            resPush.msg = "DeviceOptions接收操作异常";
            resPush.data = new { isSuccess = false, sRemark = resPush.msg };
            LogManagementMap.WriteToFileException(ex, $"DeviceOptions接收操作异常：{TyziTools.Json.ToString(reqPush)}");
        }

        if (context != null)
        {
            var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
            var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
            byteBuffer1.WriteBytes(bytes1);
            await context.Channel.WriteAndFlushAsync(byteBuffer1);
        }

        LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"返回消息DeviceOptions->【{carno}】{resPush.tname}");

        return resPush;
    }

    /// <summary>
    /// 人工开关闸
    /// </summary>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="mainDevice">设备信息</param>
    /// <param name="status">0关闸 1开闸</param>
    /// <param name="sRemark">上下文输出信息</param>
    /// <returns>是否开关闸成功</returns>
    private async Task<(bool, string)> OpenCloseGate(PasswayExt passwayModel, DeviceExt mainDevice, int status, string OpName, string sRemark, string operatorName)
    {
        var isSuccess = false;
        var evtname = OpName ?? "";
        try
        {
            string imgUrl = "", tempImage = "";
            if (mainDevice.Device_Category != 9)
            {
                tempImage = CameraImageHelper.ImageSaveHSPathBig("Online", mainDevice?.Device_SentryHostNo);
                await LPRTools.GetSnapShootToJpeg(mainDevice, tempImage);
                imgUrl = LPRTools.GetSentryHostImg(tempImage);
            }
            else
            {
                //查找车道对应的非机动车控制器
                var controller = DevicePool.Instance.GetAllDevices()
                    .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));
                if (controller != null && controller is NonMotorizedOf Non)
                {
                    //从缓存读取设备的最新信息
                    var NonDevice = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                    if (NonDevice != null)
                    {
                        //判断是否关联识别相机并播报
                        if (!string.IsNullOrWhiteSpace(NonDevice.Device_FNo) && NonDevice.Device_FNo != "0")
                        {
                            //获取关联识别相机
                            var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == NonDevice.Device_FNo);
                            if (camera != null)
                            {
                                tempImage = CameraImageHelper.ImageSaveHSPathBig("Online", mainDevice?.Device_SentryHostNo);
                                await LPRTools.GetSnapShootToJpeg(camera, tempImage);
                                imgUrl = LPRTools.GetSentryHostImg(tempImage);
                            }
                        }
                    }
                }
            }

            var deviceNo = mainDevice.Device_No;
            var time = DateTimeHelper.GetNowTime();
            if (passwayModel.Passway_IsBackCar == 1 && passwayModel.Passway_EnableBoard == 1 && status == 1)
            {
                var scode1 = Utils.CreateAuthStr(8).ToUpper();
                var sOrderno = $"{DateTimeHelper.GetNowTime():yyyyMMddHHmmssfff}-{scode1}";
                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{sOrderno}-倒车功能已启用-0x12");
                BarrierDeviceUtilsl.SendCarOrder(passwayModel.Passway_No, sOrderno, 0, passwayModel.Passway_SameInOut, passwayModel.Passway_OutNo);
            }

            var isLongOpen = status == 2; //是否道闸常开
            var act = GateCmd.ActionEnum.Open;
            if (status == 0)
            {
                act = GateCmd.ActionEnum.Close;
            }

            var gateResult = await GateCmd.ExecuteAsync(mainDevice, act, blong: isLongOpen);
            if (status == 2)
            {
                if (gateResult.Code == 1)
                {
                    evtname += "-道闸常开";
                }
                else
                {
                    evtname += "-取消常开";
                }
            }

            //if (passwayModel.Passway_EnableBoard == 1)
            //{
            //    var operation =
            //        status == 2 && gateResult.Code == 1
            //            ? BarrierOfY312Operation.Openlong
            //            : status == 0
            //                ? BarrierOfY312Operation.Close
            //                : status == 2 && gateResult.Code != 1
            //                    ? BarrierOfY312Operation.CancelOpenlong
            //                    : BarrierOfY312Operation.Open;
            //    _ = ControllerHelper.SendOpenGateByY312Async(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo, operation);
            //}

            sRemark = gateResult.Msg;
            if (gateResult.Success && gateResult.RealOpen && !gateResult.Msg.Contains("请取消道闸常开"))
            {
                #region 语音播报

                if (status == 1)
                {
                    BroadcastUtil.OpenGate(mainDevice, "人工开闸成功，请通行");
                }

                #endregion

                isSuccess = true;
                if (!evtname.Contains("招行"))
                {
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                    {
                        var rlt = PassHelper.OpenGatePass(new SpecialCarPass
                        {
                            time = time,
                            camerano = deviceNo,
                            img = tempImage,
                            name = string.IsNullOrEmpty(operatorName) ? "平台操作员" : operatorName,
                            parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                            account = operatorName,
                            type = status == 0 ? 3 : status == 2 ? 4 : 1,
                            sRemark = gateResult.Msg
                        });
                        if (rlt.success)
                        {
                            #region 通知岗亭

                            if (status == 2)
                            {
                                if (gateResult.Code == 1)
                                {
                                    evtname += "-道闸常开";
                                    WebSocketUtil.SendWSAllLink("gate", TyziTools.Json.ToString(new { action = "open", passwayno = passwayModel.Passway_No }));
                                    WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                                        $"后台管理员【{operatorName}】操作道闸常开", time.ToString("yyyy-MM-dd HH:mm:ss"));
                                }
                                else
                                {
                                    evtname += "-取消常开";
                                    WebSocketUtil.SendWSAllLink("gate", TyziTools.Json.ToString(new { action = "close", passwayno = passwayModel.Passway_No }));
                                    WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                                        $"后台管理员【{operatorName}】取消道闸常开", time.ToString("yyyy-MM-dd HH:mm:ss"));
                                }
                            }
                            else
                            {
                                WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                                    gateResult.Msg, time.ToString("yyyy-MM-dd HH:mm:ss"));
                            }

                            #endregion

                            #region 记录上传

                            //if (AppCache.IsWindows)
                            SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                            {
                                PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                PostRecordHandle_CarPlate = string.Empty,
                                PostRecordHandle_Datas = rlt.data,
                                PostRecordHandle_ToType = 3,
                                PostRecordHandle_Status = 0,
                                PostRecordHandle_ReturnMsg = string.Empty,
                                PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                            });

                            #endregion
                        }
                        else
                        {
                            #region 通知岗亭

                            WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                                $"{evtname}成功,记录保存失败!", time.ToString("yyyy-MM-dd HH:mm:ss"));

                            #endregion
                        }

                        return Task.CompletedTask;
                    });
                }
                else
                {
                    #region 通知岗亭

                    WebSocketUtil.SendWSTip("", imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name,
                        gateResult.Msg, time.ToString("yyyy-MM-dd HH:mm:ss"));

                    #endregion
                }
            }
            else
            {
                LogManagementMap.WriteToFileException(null, gateResult.Msg);
            }
        }
        catch (Exception ex)
        {
            sRemark = $"{evtname}失败:" + ex.Message;
            LogManagementMap.WriteToFileException(ex, sRemark);
        }

        return (isSuccess, sRemark);
    }

    /// <summary>
    /// 无牌车扫码入场
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="mainDevice">设备信息</param>
    /// <param name="sRemark">上下文输出信息</param>
    /// <returns>是否入场成功</returns>
    private static async Task<(bool, string)> OpenInGate(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt mainDevice, string sRemark)
    {
        try
        {
            if (!AppBasicCache.GetSentryPolicyPasswayDic.TryGetValue(ff.PasswayNo, out var policy))
            {
                sRemark = $"[{ff.sCarplate}]=>车道关联策略信息不存在！";
                return (false, sRemark);
            }

            if (policy.PolicyPassway_Scan == 0)
            {
                sRemark = $"[{ff.sCarplate}]=>车道禁止扫码！";
                return (false, sRemark);
            }

            var Gndsenver1 = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense1, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
            var Gndsenver2 = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense2, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
            var Gndsenver3 = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense3, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
            var Brakestatus = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.BarrierStatus, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
            var iCounter = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.Counter, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
            var iHaveCar = -1;


            //判断车道是否有弹出确认窗口
            if (PasswayConfirmReleaseUtil.HavePasswayConfirm(passwayModel.Passway_No))
            {
                iHaveCar = 1;
            }
            else
            {
                iHaveCar = 0;
            }

            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"车道检测是否弹出窗口 {ff.PasswayNo}/{iHaveCar}");

            //需要检查地感
            if (policy.PolicyPassway_Scan == 1 || policy.PolicyPassway_Scan == 4) //有车允许扫码 、识别无牌车+有车可扫码
            {
                //是否启用车道控制板
                if (passwayModel.Passway_EnableBoard == 1)
                {
                    //车道属于同进同出，则入车道要判断3号地感
                    var temp1 = passwayModel.Passway_SameInOut == 1
                        ? passwayModel.Passway_SameInOutType == 0 ? Gndsenver3 == 1 : Gndsenver1 == 1
                        : Gndsenver1 == 1;

                    //道闸不能处于开状态 入口地感要被触发
                    if (Brakestatus == 1 || !temp1 || iHaveCar == 1 || iCounter != 0)
                    {
                        if (Brakestatus == 1)
                        {
                            sRemark = "检测车道道闸已打开，禁止扫码！";
                        }
                        else if (!temp1)
                        {
                            if (string.IsNullOrEmpty(ff.ImgUrl)) sRemark = "未检测到地感，禁止扫码！";
                        }
                        else if (iHaveCar == 1)
                        {
                            sRemark = "当前车道已经弹出通行确认框，禁止扫码！";
                        }
                        else if (iCounter != 0)
                        {
                            sRemark = "当前车道车辆计数器不为0！";
                        }

                        if (mainDevice.Device_Category == 9)
                        {
                            BroadcastUtil.SendMotoOrder(mainDevice.Device_PasswayNo, sRemark, sRemark); //非机动车道控制器
                        }
                        else
                        {
                            //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                            if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                            {
                                var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                                if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                                {
                                    var leds = new List<LedModel>();
                                    leds.Add(new LedModel($"入场开闸", 0, 0, 2));
                                    leds.Add(new LedModel(sRemark, 1, 1, 2));
                                    await cameraOfYs.SendLedAsync(leds);
                                }
                            }
                            else
                            {
                                var data485 = Passthrough485Util.InstantDisplay(sRemark);
                                await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                            }
                        }

                        return (false, sRemark);
                    }
                }

                //判断如果是摩托车车道则读取摩托车车道状态
                if (passwayModel.Passway_Type is (2 or 3))
                {
                    var ns = await ControllerHelper.GetNonMotorizedStatus(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
                    Gndsenver1 = ns.GroundSense1;
                    Gndsenver2 = ns.GroundSense2;
                    Gndsenver3 = ns.GroundSense3;
                    Brakestatus = ns.BreakStatus;
                    iCounter = 0;
                    if (Brakestatus is (1 or 2) || iHaveCar == 1 || (Gndsenver1 == 0 && Gndsenver3 == 0))
                    {
                        if (Brakestatus is (1 or 2))
                        {
                            sRemark = "检测车道道闸已打开，禁止扫码！";
                        }
                        else if (iHaveCar == 1)
                        {
                            sRemark = "当前车道已经弹出通行确认框，禁止扫码！";
                        }
                        else if (Gndsenver1 == 0 && Gndsenver3 == 0)
                        {
                            sRemark = "未检测到地感，禁止扫码！";
                        }

                        _ = ControllerHelper.SendOpenGateByMotoAsync(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo, NonMotorizedOperation.Other, sRemark, sRemark, true, null);

                        #region 非机动车车道控制器关联相机播报
                        try
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"开始处理非机动车车道控制器关联相机播报，车道号：{passwayModel.Passway_No}，播报内容：{sRemark}");

                            //查找车道对应的非机动车控制器
                            var controller = DevicePool.Instance.GetAllDevices()
                                .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));

                            if (controller != null && controller is NonMotorizedOf Non)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"找到非机动车控制器，设备号：{Non.Model.DeviceNo}，车道号：{passwayModel.Passway_No}");

                                //从缓存读取设备的最新信息
                                var device = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                                if (device != null)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"获取到设备缓存信息，设备号：{device.Device_No}，关联相机号：{device.Device_FNo}，相机使用状态：{device.Device_CameraUsage}");

                                    //判断是否关联识别相机并播报
                                    if (!string.IsNullOrWhiteSpace(device.Device_FNo) && device.Device_FNo != "0" && device.Device_CameraUsage == 1)
                                    {
                                        //获取关联识别相机
                                        var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == device.Device_FNo);
                                        if (camera != null)
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"非机动车控制器关联相机播报：车道[{passwayModel.Passway_No}] -> 相机[{camera.Device_IP}:{camera.Device_Com}]，播报内容：{sRemark}");

                                            var voice = Passthrough485Util.InstantDisplay(sRemark);
                                            _ = CameraController.SendDataBy485Async(camera.Device_IP, (SerialIndexType)(camera.Device_Com ?? 0), voice, nameof(Passthrough485Util.InstantDisplay));
                                        }
                                        else
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"未找到关联识别相机，相机设备号：{device.Device_FNo}，车道号：{passwayModel.Passway_No}");
                                        }
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"设备未配置关联相机或相机未启用，设备号：{device.Device_No}，关联相机号：{device.Device_FNo}，相机使用状态：{device.Device_CameraUsage}");
                                    }
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"未找到非机动车控制器设备缓存信息，设备号：{Non.Model.DeviceNo}，车道号：{passwayModel.Passway_No}");
                                }
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"未找到对应的非机动车控制器，车道号：{passwayModel.Passway_No}，出场车道号：{passwayModel.Passway_OutNo}");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, $"非机动车车道控制器关联相机播报失败，车道号：{passwayModel?.Passway_No}，播报内容：{sRemark}");
                        }
                        #endregion

                        return (false, sRemark);
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(ff.ImgUrl))
                    {
                        var byteStatus = await CameraController.ReadGPIOInStatusAsync(mainDevice.Device_IP);
                        if (byteStatus == 0)
                        {
                            sRemark = "未检测到地感，禁止扫码！";
                            //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                            if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                            {
                                var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                                if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                                {
                                    var leds = new List<LedModel>();
                                    leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                                    leds.Add(new LedModel("未检测到地感", 1, 1, 1));
                                    await cameraOfYs.SendLedAsync(leds);
                                }
                            }
                            else
                            {
                                var data485 = Passthrough485Util.InstantDisplay(sRemark);
                                await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));

                            }

                            return (false, sRemark);
                        }
                    }
                }
            }

            if (policy.PolicyPassway_Scan == 3 || policy.PolicyPassway_Scan == 4) //识别无牌车可扫码
            {
                if (CameraController.Platedic.TryGetValue(ff.PasswayNo, out var fout))
                {
                    var ispass = true;
                    if (!(fout.Item1.Contains("_无_") || fout.Item1 == "无车牌" || fout.Item1 == "未知" || fout.Item1 == "无牌车"))
                    {
                        ispass = false;
                        sRemark = "未检测到无牌车识别记录禁止扫码！";
                    }
                    else if (DateTimeHelper.GetNowTime().Subtract(fout.Item2).TotalMinutes > 5)
                    {
                        ispass = false;
                        sRemark = "检测无牌车识别记录已超时，请重新识别再扫码！";
                    }

                    if (!ispass)
                    {
                        //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                        if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                        {
                            var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                            if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                            {
                                var leds = new List<LedModel>();
                                leds.Add(new LedModel($"禁止通行", 0, 0, 1));
                                leds.Add(new LedModel("未检测到地感", 1, 1, 1));
                                await cameraOfYs.SendLedAsync(leds);
                            }
                        }
                        else
                        {
                            var data485 = Passthrough485Util.InstantDisplay(sRemark);
                            await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                        }
                        return (false, sRemark);
                    }
                }
                else
                {
                    sRemark = "当前扫码未检测到无牌车识别记录！";
                    //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                    if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                    {
                        var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                        if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                        {
                            var leds = new List<LedModel>();
                            leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                            leds.Add(new LedModel(sRemark, 1, 1, 1));
                            await cameraOfYs.SendLedAsync(leds);
                        }
                    }
                    else
                    {
                        var data485 = Passthrough485Util.InstantDisplay(sRemark);
                        await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                    }
                    return (false, sRemark);
                }
            }


            //判断车道同进同出
            if (passwayModel.Passway_SameInOut == 1 && passwayModel.Passway_Type == 1)
            {
                if (passwayModel.Passway_EnableBoard == 1)
                {
                    var isHaveCar = false;


                    //获取同进同出的车道信息
                    if (!AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayModel.Passway_OutNo, out PasswayExt sameModel))
                    {
                        return (true, string.Empty);
                    }
                    //排序
                    var passwayList = new List<PasswayExt> { passwayModel, sameModel }.OrderBy(x => x.Passway_ID).ToList();
                    //两个车道以Id排序后，连接成一个key
                    var cacheSamekey = string.Join("_", passwayList.Select(x => x.Passway_No));

                    if (AppBasicCache.GetSameInOutLs.TryGetValue(cacheSamekey, out var ilanestatus))
                    {
                        //判断同进同出上一辆车的方向，只有方向不同才需要判断礼让通行
                        if (ilanestatus != passwayModel.Passway_SameInOutType)
                        {
                            //【入口车道 判断1号地感】
                            if (passwayModel.Passway_SameInOutType == 0 && Gndsenver1 == 1)
                            {
                                isHaveCar = true;
                            }
                            //【出口车道 判断3号地感】
                            else if (passwayModel.Passway_SameInOutType == 1 && Gndsenver3 == 1)
                            {
                                isHaveCar = true;
                            }

                            //判断有车，同时上一辆车通行方向不一场，提示礼让通行
                            if (isHaveCar)
                            {
                                sRemark = "同车道车辆地感检测有车，请稍候扫码！";
                                //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                                if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                                {
                                    var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                                    if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                                    {
                                        var leds = new List<LedModel>();
                                        leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                                        leds.Add(new LedModel(sRemark, 1, 1, 1));
                                        await cameraOfYs.SendLedAsync(leds);
                                    }
                                }
                                else
                                {
                                    var data485 = Passthrough485Util.InstantDisplay(sRemark);
                                    await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                                }
                                return (false, sRemark);
                            }
                        }
                    }

                    if (!isHaveCar)
                    {
                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSameInOutLs, cacheSamekey, passwayModel.Passway_SameInOutType);
                    }

                    LogManagementMap.WriteToFile($"无牌车在{passwayModel.Passway_Name}同进同出,输出地感1：{Gndsenver1} 地感3：{Gndsenver3} 判断是否有车：{isHaveCar} 上次车辆通行方向：{ilanestatus} 本次车辆通行方向：{passwayModel.Passway_SameInOutType}");
                }
                else
                {
                    if (AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayModel.Passway_OutNo, out var samePasswayModel))
                    {
                        //判断车道是否有弹出确认窗口
                        if (PasswayConfirmReleaseUtil.HavePasswayConfirm(samePasswayModel.Passway_No))
                        {

                            sRemark = "同车道车辆检测有车，请稍候扫码！";
                            //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                            if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(mainDevice.Drive_Name))
                            {
                                var camera = TcpConnPools.DevicePool.Instance.GetDevice(mainDevice.Device_IP);
                                if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                                {
                                    var leds = new List<LedModel>();
                                    leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                                    leds.Add(new LedModel(sRemark, 1, 1, 1));
                                    await cameraOfYs.SendLedAsync(leds);
                                }
                            }
                            else
                            {
                                var data485 = Passthrough485Util.InstantDisplay(sRemark);
                                await CameraController.SendDataBy485Async(mainDevice.Device_IP, (SerialIndexType)(mainDevice.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                            }
                            return (false, sRemark);
                        }
                    }
                }
            }

            //抓拍图片
            var tempImage = CameraImageHelper.ImageSaveHSPathBig(ff.sCarplate, ff.SentryHostNo);
            if (mainDevice.Device_Category != 9)
            {
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                {
                    var swSnapImg = new Stopwatch();
                    swSnapImg.Start();
                    await LPRTools.GetSnapShootToJpeg(mainDevice, tempImage);
                    swSnapImg.Stop();
                    LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]入场抓拍图片消耗时长：{swSnapImg.ElapsedMilliseconds}");
                });
            }
            else
            {
                //查找车道对应的非机动车控制器
                var controller = DevicePool.Instance.GetAllDevices()
                    .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));
                if (controller != null && controller is NonMotorizedOf Non)
                {
                    //从缓存读取设备的最新信息
                    var NonDevice = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                    if (NonDevice != null)
                    {
                        //判断是否关联识别相机并播报
                        if (!string.IsNullOrWhiteSpace(NonDevice.Device_FNo) && NonDevice.Device_FNo != "0")
                        {
                            //获取关联识别相机
                            var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == NonDevice.Device_FNo);
                            if (camera != null)
                            {
                                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                                {
                                    var swSnapImg = new Stopwatch();
                                    swSnapImg.Start();
                                    await LPRTools.GetSnapShootToJpeg(camera, tempImage);
                                    swSnapImg.Stop();
                                    LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]入场抓拍图片消耗时长：{swSnapImg.ElapsedMilliseconds}");
                                });
                            }
                        }
                    }
                }
            }

            var imgUrl = LPRTools.GetSentryHostImg(tempImage);

            //获取无牌车类型 纯数字加字母组合判断为无牌车 车牌号码里面有无字的判断为无牌车
            var cardNames = string.Empty;
            if (string.IsNullOrWhiteSpace(ff.sCarplate) || ff.sCarplate.Contains("无") || (!ff.sCarplate.Contains("无") && !Passthrough485Util.IsContainsPfx(ff.sCarplate)))
            {
                var fCarType = AppBasicCache.GetCarTypes.FirstOrDefault(cartype => cartype.Value.CarType_Name == "无牌车");
                if (fCarType.Value != null)
                {
                    cardNames = TyziTools.Json.ToString(new List<string> { fCarType.Value.CarType_No });
                }
                else
                {
                    var policyPark = PolicyPark.GetEntity(AppBasicCache.SentryHostInfo.SentryHost_ParkNo);
                    if (policyPark != null && !string.IsNullOrEmpty(policyPark.PolicyPark_DefaultNoneCarType))
                    {
                        var ctModel = CarType.GetEntity(policyPark.PolicyPark_DefaultNoneCarType);
                        if (ctModel != null)
                        {
                            cardNames = TyziTools.Json.ToString(new List<string> { ctModel.CarType_No });
                        }
                    }
                }
            }


            var swCheck = new Stopwatch();
            swCheck.Start();
            var passTime = DateTimeHelper.GetNowTime();
            var data = PassHelper.OnCheckCarPass(new ParkCarInOut
            {
                camerano = mainDevice.Device_No,
                carno = ff.sCarplate,
                cartype = cardNames,
                time = passTime,
                parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                img = tempImage,
                mode = 2,
                orderno = ff.sOrderNo,
                isreal = 0,
                credibility = 100,
                licensepoint = string.Empty,
                carlogo = string.Empty,
                caryear = string.Empty
            }, AppBasicCache.GetBasicCache);
            swCheck.Stop();
            //添加识别记录同步主机
            if (data.recog != null)
            {
                //if (AppCache.IsWindows)
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                {

                    try
                    {
                        #region 上报识别记录

                        if (data.recog != null)
                        {
                            SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                            {
                                PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                                PostRecordHandle_CarPlate = data.passres.carno,
                                PostRecordHandle_Datas = JsonConvert.SerializeObject(data.recog),
                                PostRecordHandle_ToType = 10,
                                PostRecordHandle_Status = 0,
                                PostRecordHandle_ReturnMsg = string.Empty,
                                PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                            });
                        }

                        #endregion
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFileException(ex, $"添加识别记录同步到主机异常:{data.passres.carno}");
                    }

                    return Task.CompletedTask;

                });
            }

            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]入场检测消耗时长：{swCheck.ElapsedMilliseconds}");
            if (data.success)
            {
                if (data.passres.code == 1 || data.passres.code == 2)
                {
                    GateCmd.Result gateResult = null;
                    if (data.passres.code == 1)
                    {
                        //获取双开信息
                        var (isDoubleGate, linkCameraNoList) = Command.GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);

                        var swOpenGate = new Stopwatch();
                        swOpenGate.Start();
                        gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);
                        swOpenGate.Stop();
                        LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]入场开闸消耗时长：{swOpenGate.ElapsedMilliseconds}");

                        if (data.recog != null)
                        {
                            //更新执行开闸的结果
                            data.recog.CarRecog_OpenStatus = gateResult.Success ? 1 : -1;
                            BLL.CarRecog.GetInstance(data.recog.CarRecog_Time ?? DateTime.Now)
                                .UpdateOpenStatus(data.recog.CarRecog_No, (int)data.recog.CarRecog_OpenStatus);
                        }
                    }
                    else
                    {
                        gateResult = new GateCmd.Result();
                        gateResult.Success = true;
                    }

                    if (gateResult.Success)
                    {
                        if (passwayModel.Passway_IsBackCar == 1 && passwayModel.Passway_EnableBoard == 1)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{ff.sOrderNo}-倒车功能已启用-0x12");
                            _ = ControllerHelper.SendCarOrderByY312Async(passwayModel.Passway_No, ff.sOrderNo, 0, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo, 1);
                        }

                        if (data.passres.code == 2) PasswayConfirmReleaseUtil.AddOrUpdateResult(data);

                        _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                        {
                            #region 语音播报

                            BroadcastUtil.OpenInGate(data, mainDevice);

                            #endregion

                            #region 通知岗亭

                            //WebSocketUtil.SendWS(data);

                            #endregion

                            return Task.CompletedTask;
                        });

                        GetMemoryCache.Set($"EstCarIn=>{ff.sOrderNo}", new Tuple<string, ResultPass>(tempImage, data), cacheEntityOps);
                        if (string.IsNullOrEmpty(sRemark) && data.passres.code == 2)
                        {
                            sRemark = "请等待管理员确认放行";
                        }

                        return (true, sRemark);
                        //System.Diagnostics.Stopwatch swOpenGate = new System.Diagnostics.Stopwatch();
                        //swOpenGate.Start();
                        //var result = Command.GateCmd.ExcuteExt(mainDevice, Command.GateCmd.ActionEnum.Open);
                        //swOpenGate.Stop();
                        //LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]入场开闸消耗时长：{swOpenGate.ElapsedMilliseconds}");
                        //if (result.Success)
                        //{

                        //    Task.Run(() =>
                        //    {
                        //        #region 语音播报
                        //        BroadcastUtil.OpenInGate(data, mainDevice);
                        //        #endregion

                        //        #region 通知岗亭
                        //        //WebSocketUtil.SendWS(data);
                        //        #endregion
                        //    });


                        //    GetMemoryCache.Set($"EstCarIn=>{ff.sOrderNo}", new Tuple<string, ResultPass>(tempImage, data), cacheEntityOps);
                        //    sRemark = $"[{ff.sCarplate}]=>预入场开闸成功";
                        //    return true;
                        //}
                        //else
                        //{
                        //    sRemark = $"[{ff.sCarplate}]=>开闸不成功！";
                        //    return false;
                        //}
                    }

                    var msg = data.errmsg;
                    if (data.passres.code == 3)
                    {
                        msg = "车场满位";
                        data.passres.errmsg = "车场满位,禁止扫码入场";
                    }

                    if (string.IsNullOrWhiteSpace(msg))
                    {
                        msg = string.IsNullOrWhiteSpace(data.passres.errmsg) ? gateResult.Success ? "" : "开闸失败" : data.passres.errmsg;
                    }

                    #region 语音播报

                    BroadcastUtil.NoEntry(data, mainDevice);

                    #endregion

                    #region 岗亭通知

                    if (!data.success)
                    {
                        WebSocketUtil.SendWSTip(ff.sCarplate, imgUrl, ff.PasswayNo, mainDevice.Passway_Name, data.errmsg, passTime.ToString("G"));
                    }
                    else
                    {
                        #region 通知岗亭

                        data.passres.code = 0;
                        WebSocketUtil.SendWS(data);

                        #endregion
                    }

                    #endregion

                    sRemark = $"[{ff.sCarplate}]=>禁止通行：{msg}";
                    return (false, sRemark);
                }

                return (false, data.passres.errmsg);
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"无牌车扫码入场异常：{TyziTools.Json.ToString(ff)}");
        }

        return (false, sRemark);
    }

    /// <summary>
    /// 车辆确认入场操作
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="device">设备信息</param>
    /// <param name="sRemark">上下文输出信息</param>
    /// <returns>是否入场成功</returns>
    private static bool EnterCar(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt device, ref string sRemark)
    {
        try
        {
            var rlt = GetMemoryCache.Get<Tuple<string, ResultPass>>($"EstCarIn=>{ff.sOrderNo}");
            if (rlt == null)
            {
                sRemark = $"[{ff.sCarplate}]没有找到预入场记录！";
                return false;
            }

            Model.ParkOrder order = null;
            if (string.IsNullOrEmpty(ff.opTime) || string.IsNullOrEmpty(ff.sCarplate))
            {
                order = BLL.ParkOrder.GetEntity(ff.sOrderNo);
                if (order != null)
                {
                    if (string.IsNullOrEmpty(ff.sCarplate)) ff.sCarplate = order.ParkOrder_CarNo;
                    if (string.IsNullOrEmpty(ff.opTime)) ff.opTime = order.ParkOrder_EnterTime == null ? DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss") : order.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }
            }

            var imgUrl = rlt?.Item2.passres.img;
            //    int count = 5;
            //checkIMG:
            //    if (device.Device_Category != 9 && string.IsNullOrWhiteSpace(imgUrl) && !string.IsNullOrWhiteSpace(rlt?.Item1) && !File.Exists(rlt?.Item1))
            //    {
            //        count--;
            //        Thread.Sleep(20);
            //        goto checkIMG;
            //    }
            if (string.IsNullOrWhiteSpace(imgUrl))
            {
                imgUrl = PassHelperBiz.GetImageHttpUrl(AppBasicCache.SentryHostInfo.SentryHost_ParkNo, rlt.Item1, AppBasicCache.SentryHostInfo.SentryHost_No);
                rlt.Item2.passres.img = imgUrl;
                if (rlt.Item2.recog != null)
                {
                    rlt.Item2.recog.CarRecog_Img = imgUrl;
                    try
                    {
                        BaseBLL._Insert(rlt.Item2.recog);
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFileException(ex, $"[{ff.sCarplate}]更新车牌识别记录异常");
                    }
                }
            }

            if (rlt.Item2.recog != null)
            {
                //if (AppCache.IsWindows)
                try
                {
                    SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                    {
                        PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                        PostRecordHandle_CarPlate = ff.sCarplate,
                        PostRecordHandle_Datas = JsonConvert.SerializeObject(rlt.Item2.recog),
                        PostRecordHandle_ToType = 10,
                        PostRecordHandle_Status = 0,
                        PostRecordHandle_ReturnMsg = string.Empty,
                        PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                    });
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, $"[{ff.sCarplate}]保存上传车牌识别记录异常");
                }
            }

            //入场更改计费卡类信息
            var parkGateIn = new ParkGateIn();
            //是否修改车牌类型
            if (!string.IsNullOrWhiteSpace(ff.opCarCardType))
            {
                var templ = AppBasicCache.GetCarcardTypes.FirstOrDefault(m => m.Value.CarCardType_No == ff.opCarCardType);
                if (templ.Value != null)
                {
                    parkGateIn.carcardtypename = templ.Value.CarCardType_Name;
                    parkGateIn.carcardtypeno = templ.Value.CarCardType_No;
                }
            }

            //是否添加备注
            if (!string.IsNullOrWhiteSpace(ff.opRemarks))
            {
                parkGateIn.remark = ff.opRemarks;
            }

            //是否修改车牌颜色
            if (!string.IsNullOrWhiteSpace(ff.opCarType))
            {
                var templ = AppBasicCache.GetCarTypes.FirstOrDefault(m => m.Value.CarType_No == ff.opCarType);
                if (templ.Value != null)
                {
                    parkGateIn.cartypename = templ.Value.CarType_Name;
                    parkGateIn.cartypeno = templ.Value.CarType_No;
                }
            }

            var result = PassHelper.CarInComplete(new ParkGatePass
            {
                account = ff.operatorName,
                parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                carno = ff.sCarplate,
                orderno = ff.sOrderNo,
                code = 200,
                imgpath = rlt.Item1,
                name = ff.operatorName,
                camerano = device.Device_No,
                onenter = 0,
                time = Convert.ToDateTime(ff.opTime),
                img = imgUrl,
                innonecarno = ff.InNoneCarNo,
                onindata = parkGateIn,
            }, false);
            if (result.success)
            {
                #region 通知岗亭

                if (rlt?.Item2?.resorder?.resIn?.parkorder != null)
                    rlt.Item2.resorder.resIn.parkorder.ParkOrder_EnterRemark = "扫码入场";

                rlt.Item2.passres.img = rlt.Item1.Replace(CameraGlobal.strImgpath, $@"..\{AppBasicCache.SentryHostInfo.SentryHost_No}");

                if (IsValidJsonArray(parkGateIn.remark) && rlt?.Item2?.passres?.code == 2 && AppBasicCache.GetPolicyPark?.PolicyPark_ScanEnter == 1)
                {
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                    {
                        try
                        {
                            #region 开闸

                            //获取双开信息
                            var (isDoubleGate, linkCameraNoList) = Command.GateCmd.GetDoubleGateInfo(passwayModel.Passway_No, rlt.Item2.passres.cartype.CarType_No);
                            var gateResult = await GateCmd.ExecuteAsync(device, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);
                            if (gateResult != null && rlt.Item2.recog != null)
                            {
                                //更新执行开闸的结果
                                rlt.Item2.recog.CarRecog_OpenStatus = gateResult.Success ? 1 : -1;
                                BLL.CarRecog.GetInstance(rlt.Item2.recog.CarRecog_Time ?? DateTime.Now)
                                    .UpdateOpenStatus(rlt.Item2.recog.CarRecog_No, (int)rlt.Item2.recog.CarRecog_OpenStatus);
                            }
                            #endregion

                            #region 语音播报

                            if (rlt?.Item2.passres.gate == 1 || rlt?.Item2.passres.gate == 2)// if (areaLink.Count() > 1)
                            {
                                BroadcastUtil.AutoReleaseEnter(rlt.Item2.passres.areano, rlt.Item2, device);
                            }
                            else
                            {
                                BroadcastUtil.AutoReleaseOut(rlt.Item2, device);
                            }
                            #endregion

                            #region 清理车道缓存

                            PasswayConfirmReleaseUtil.RemoveResult(passwayModel.Passway_No);

                            #endregion
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "扫码登记入场开闸处理异常：" + ex.ToString());
                        }

                        return;
                    });
                    WebSocketUtil.SendWSTip(ff.sCarplate, rlt.Item2.passres.img, passwayModel.Passway_No, passwayModel.Passway_Name, "扫码登记入场成功", ff.opTime);

                }
                else
                {
                    WebSocketUtil.SendWS(rlt?.Item2);

                    #region 清理车道缓存

                    if (rlt?.Item2?.passres?.code != 2 && rlt?.Item2?.passres?.code != 3)
                    {
                        var confirmOrders = ConfirmRelease.Results.Values.FirstOrDefault(x => x.passres?.parkorderno == ff.sOrderNo || x.resorder?.resOut?.parkorder?.ParkOrder_No == ff.sOrderNo);
                        if (confirmOrders != null)
                        {
                            PasswayConfirmReleaseUtil.RemoveResult(confirmOrders.passres?.passway?.Passway_No);
                        }
                    }

                    #endregion
                }
                //WebSocketUtil.SendWSTip(ff.sCarplate, rlt.Item2, passwayModel.Passway_No, passwayModel.Passway_Name, "扫码入场成功", ff.opTime);

                #endregion

                #region 上传记录

                //同步记录
                //if (AppCache.IsWindows)
                if (!ff.InNoneCarNo)
                {
                    SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                    {
                        PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                        PostRecordHandle_CarPlate = ff.sCarplate,
                        PostRecordHandle_Datas = result.data,
                        PostRecordHandle_ToType = 1,
                        PostRecordHandle_Status = 0,
                        PostRecordHandle_ReturnMsg = string.Empty,
                        PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                    });
                }

                #endregion

                #region 上传图片到平台
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                {
                    try
                    {
                        if (order == null) order = BLL.ParkOrder.GetEntity(ff.sOrderNo);
                        if (order != null && !string.IsNullOrEmpty(order.ParkOrder_EnterImgPath))
                        {
                            await Task.Delay(3000);
                            var img = BLL.PushEvent.UploadImgToAliyun(order.ParkOrder_EnterImgPath, AppBasicCache.GetParking?.Parking_Key, localImgPath: order.ParkOrder_EnterImg, orderNo: ff.sOrderNo);

                            PlateColorConvert.ToENCode(order?.ParkOrder_CarTypeName ?? "", out var plateColorCode);
                            var res2 = BLL.PushEvent.EnterCarImg(img, AppBasicCache.GetParking?.Parking_Key,
                                ff.sOrderNo, ff.sCarplate, order?.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss") ?? "", order?.ParkOrder_CarType ?? "", order?.ParkOrder_EnterPasswayName ?? "", order?.ParkOrder_EnterAdminName ?? "",
                                "", plateColorCode.ToString(), MiddlewareEventPriority.Delay_500_2, iState: 0);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"上传图片到平台异常：" + ex.ToString());
                    }
                });
                #endregion

                sRemark = $"[{ff.sCarplate}]确认入场成功！";
                return true;
            }

            {
                var po = BLL.ParkOrder.GetEntity(ff.sOrderNo);
                if (po != null)
                {
                    var detailList = OrderDetail.GetAllEntity(ff.sOrderNo);
                    if (detailList != null)
                    {
                        detailList.ForEach(x => { x.OrderDetail_StatusNo = EnumParkOrderStatus.Close; });
                    }

                    po.ParkOrder_StatusNo = EnumParkOrderStatus.Close;
                    var fresult = OrderDetail.UpdateByList(new List<ParkOrder> { po }, detailList);
                    if (fresult)
                    {
                        if (AppBasicCache.IsSendTcp)
                        {
                            var res = MiddlewareApi.UpdateOrderDetail(po, detailList);
                        }
                        else
                        {
                            HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (po, detailList), "updateorder");
                        }
                    }
                }

                sRemark = $"[{ff.sCarplate}]记录保存失败:" + result.errmsg;
                return false;
            }
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"无牌车扫码确认入场异常：{TyziTools.Json.ToString(ff)}");
            return false;
        }


    }

    private static bool IsValidJsonArray(string json)
    {
        try
        {
            if (string.IsNullOrEmpty(json)) return false;
            var list = TyziTools.Json.ToObject<List<string>>(json);
            return list != null && list.Count > 0;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 出场开闸操作
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="mainDevice">设备信息</param>
    /// <param name="sRemark">上下文输出信息</param>
    /// <returns>是否执行成功</returns>
    private static async Task<(bool, string)> OpenOutGate(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt mainDevice, string sRemark)
    {
        try
        {
            var isConfirm = false; //是否岗亭有弹框
            var tempImage = "";
            var SendVoice = false;
            var cartypeNo = string.Empty;
            if (DataCache.OpenGateInit.Get($"OpenGate:{ff.PasswayNo}{ff.sOrderNo}") == 0 && !string.IsNullOrEmpty(ff.sOrderNo))
            {
                DataCache.OpenGateInit.Set($"OpenGate:{ff.PasswayNo}{ff.sOrderNo}", 1);
                SendVoice = true;
            }
            else
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{ff.sCarplate}不播报语音！");
            }

            Model.ResultPass passResule = null;

            var cache = LocalMemoryCache.Get($"ConfirmPay:{ff.sOrderNo}");
            if (cache == null)
            {
                if (ConfirmRelease.Results.TryGetValue(ff.PasswayNo, out passResule))
                {
                    cartypeNo = passResule.passres?.cartype?.CarType_No;
                    var orderno = passResule.resorder?.resOut?.parkorder?.ParkOrder_No ?? passResule.passres?.parkorderno;
                    if (orderno == ff.sOrderNo)
                    {
                        BLL.ParkOrder.AddOpenOutGateCache(ff.sOrderNo, passResule);
                        isConfirm = true;
                    }
                    else
                    {
                        BLL.ParkOrder.AddOpenOutGateCache(ff.sOrderNo, null);
                    }
                }
                else
                {
                    BLL.ParkOrder.AddOpenOutGateCache(ff.sOrderNo, null);
                }

                if (isConfirm)
                {
                    tempImage = passResule.passres.img;
                }
                else
                {
                    if (mainDevice.Device_Category != 9)
                    {
                        tempImage = CameraImageHelper.ImageSaveHSPathBig(ff.sCarplate, ff.SentryHostNo);
                        _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                        {
                            var swSnapImg = new Stopwatch();
                            swSnapImg.Start();
                            await LPRTools.GetSnapShootToJpeg(mainDevice, tempImage);
                            swSnapImg.Stop();
                            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]出场抓拍图片消耗时长：{swSnapImg.ElapsedMilliseconds}");
                        });
                    }
                    else
                    {
                        //查找车道对应的非机动车控制器
                        var controller = DevicePool.Instance.GetAllDevices()
                            .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));
                        if (controller != null && controller is NonMotorizedOf Non)
                        {
                            //从缓存读取设备的最新信息
                            var NonDevice = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                            if (NonDevice != null)
                            {
                                //判断是否关联识别相机并播报
                                if (!string.IsNullOrWhiteSpace(NonDevice.Device_FNo) && NonDevice.Device_FNo != "0")
                                {
                                    //获取关联识别相机
                                    var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == NonDevice.Device_FNo);
                                    if (camera != null)
                                    {
                                        tempImage = CameraImageHelper.ImageSaveHSPathBig(ff.sCarplate, ff.SentryHostNo);
                                        var swSnapImg = new Stopwatch();
                                        swSnapImg.Start();
                                        await LPRTools.GetSnapShootToJpeg(camera, tempImage);
                                        swSnapImg.Stop();
                                        LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]出场抓拍图片消耗时长：{swSnapImg.ElapsedMilliseconds}");
                                    }
                                }
                            }
                        }
                    }

                }
            }
            else
            {
                if (ConfirmRelease.Results.TryGetValue(ff.PasswayNo, out passResule))
                {
                    cartypeNo = passResule.passres?.cartype?.CarType_No;
                    var orderno = passResule.resorder?.resOut?.parkorder?.ParkOrder_No ?? passResule.passres?.parkorderno;
                    if (orderno == ff.sOrderNo)
                    {
                        BLL.ParkOrder.AddOpenOutGateCache(ff.sOrderNo, passResule);
                    }
                    else
                    {
                        BLL.ParkOrder.AddOpenOutGateCache(ff.sOrderNo, null);
                    }
                }
                else
                {
                    BLL.ParkOrder.AddOpenOutGateCache(ff.sOrderNo, null);
                }
            }

            var swOpenGate = new Stopwatch();
            swOpenGate.Start();

            //获取订单信息
            var order = BLL.ParkOrder.GetEntity(ff.sOrderNo);
            if (order == null && passResule?.resorder?.resOut?.noRecordOrder != null && passResule.resorder.resOut.noRecordOrder.ParkOrder_No == ff.sOrderNo)
            {
                order = passResule?.resorder?.resOut?.noRecordOrder;
            }
            cartypeNo = order?.ParkOrder_CarType;

            //获取双开信息
            var (isDoubleGate, linkCameraNoList) = Command.GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, cartypeNo);
            var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate, linkCameraNoList: linkCameraNoList);
            swOpenGate.Stop();
            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{mainDevice.Device_IP}][{ff.sCarplate}]出场开闸消耗时长：{swOpenGate.ElapsedMilliseconds}");
            if (gateResult.Success)
            {
                if (!string.IsNullOrWhiteSpace(ff.sCarplate))
                {
                    //获取该车牌的最后识别记录
                    var carRecog = BLL.CarRecog.GetInstance(order.ParkOrder_OutTime ?? DateTimeHelper.GetNowTime()).GetLastRecord(ff.sCarplate);
                    if (carRecog != null)
                    {
                        carRecog.CarRecog_OpenStatus = 1;
                        BLL.CarRecog.GetInstance(carRecog.CarRecog_Time ?? DateTimeHelper.GetNowTime()).AddOrUpdate(carRecog);
                    }
                }

                //车道控制板订单发送
                if (passwayModel.Passway_IsBackCar == 1 && passwayModel.Passway_EnableBoard == 1)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{ff.sOrderNo}-倒车功能已启用-0x12");
                    BarrierDeviceUtilsl.SendCarOrder(passwayModel.Passway_No, ff.sOrderNo, 1, passwayModel.Passway_SameInOut, passwayModel.Passway_OutNo, 1);
                }

                if (SendVoice)
                {
                    #region 语音播报
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                    {
                        BroadcastUtil.OpenOutGate(mainDevice, ff.sCarplate, ff.sOrderNo);
                        return Task.CompletedTask;
                    });
                    #endregion
                }

                GetMemoryCache.Set($"EstCarOut=>{ff.sOrderNo}", new Tuple<string, string, bool, bool>(ff.sCarplate, tempImage, isConfirm, gateResult.Success), cacheEntityOps);
            }

            sRemark = gateResult.Msg;
            return (gateResult.Success, gateResult.Msg);
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFileException(ex, $"出场开闸异常：{TyziTools.Json.ToString(ff)}");
            return (false, sRemark);
        }
    }

    /// <summary>
    /// 出场确认操作
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="device">设备信息</param>
    /// <param name="sRemark">上下文输出信息</param>
    /// <returns>是否执行成功</returns>
    private static bool OutCar(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt device, ref string sRemark)
    {
        try
        {
            var status = DataCache.OrderStatus.Get(ff.sOrderNo);
            if (status == 0 || status != 3)
            {
                var askCount = 0;
                while (true)
                {
                    if (askCount > 20) break;
                    if (status == 3) break;
                    Thread.Sleep(100);
                    status = DataCache.OrderStatus.Get(ff.sOrderNo);
                    askCount++;
                }
            }

            var rlt = GetMemoryCache.Get<Tuple<string, string, bool, bool>>($"EstCarOut=>{ff.sOrderNo}");
            var time = Convert.ToDateTime(ff.opTime);
            var img = string.Empty;
            CarRecog carRecog = null;

            var swOpenGate = new Stopwatch();
            swOpenGate.Start();

            var cache = LocalMemoryCache.Get($"ConfirmPay:{ff.sOrderNo}");
            if (cache != null)
            {
                sRemark = $"[{ff.sCarplate}]出场确认处理成功!";
                LocalMemoryCache.Del(ff.sOrderNo);
                return true;
            }


            var carCards = CarCardType.GetEntity("*", $" CarCardType_ParkNo ='{AppBasicCache.SentryHostInfo.SentryHost_ParkNo}' and CarCardType_Category='{ff.opCarCardType}' ");
            if (rlt != null && !rlt.Item3 && string.IsNullOrWhiteSpace(img))
            {

                img = PassHelperBiz.GetImageHttpUrl(AppBasicCache.SentryHostInfo.SentryHost_ParkNo, rlt.Item2, AppBasicCache.SentryHostInfo?.SentryHost_No);
                try
                {
                    carRecog = new CarRecog
                    {
                        CarRecog_No = Utils.CreateNumber,
                        CarRecog_CarNo = ff.sCarplate,
                        CarRecog_CameraNo = device.Device_No,
                        CarRecog_ParkNo = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                        CarRecog_Img = img,
                        CarRecog_SmallImg = "",
                        CarRecog_Mode = 2,
                        CarRecog_Time = time,
                        CarRecog_CameraName = device.Device_Name,
                        CarRecog_PasswayNo = passwayModel.Passway_No,
                        CarRecog_PasswayName = passwayModel.Passway_Name,
                        CarRecog_CarLogo = "",
                        CarRecog_PlateColor = "",
                        CarRecog_CarYear = "",
                        CarRecog_Credibility = 100,
                        CarRecog_IsRealPlate = 0,
                        CarRecog_LicensePoint = "",
                        CarRecog_Recogresult = "",
                        CarRecog_IsOpen = 1,
                        CarRecog_Remark = "",
                        CarRecog_CarType = carCards?.CarCardType_Name,
                        CarRecog_OpenStatus = rlt.Item4 ? 1 : null

                    };
                    BLL.CarRecog.GetInstance(carRecog.CarRecog_Time ?? DateTimeHelper.GetNowTime()).AddOrUpdate(carRecog);

                    if (AppBasicCache.IsSendTcp)
                    {
                        SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                        {
                            PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                            PostRecordHandle_CarPlate = ff.sCarplate,
                            PostRecordHandle_Datas = JsonConvert.SerializeObject(carRecog),
                            PostRecordHandle_ToType = 10,
                            PostRecordHandle_Status = 0,
                            PostRecordHandle_ReturnMsg = string.Empty,
                            PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                        });
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, $"[{ff.sCarplate}]保存上传车牌识别记录异常");
                }
            }

            var lt = PassHelper.CarOutComplete(new ParkGatePass
            {
                account = AppBasicCache.GetAdmin(passwayModel.Passway_No).Admins_Account,
                parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                carno = ff.sCarplate,
                orderno = ff.sOrderNo,
                code = 201,
                imgpath = rlt?.Item2,
                name = ff.operatorName,
                camerano = device.Device_No,
                onenter = 0,
                time = time,
                img = img
            });
            swOpenGate.Stop();

            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{device?.Device_IP}][{ff.sCarplate}]出场检测消耗时长：{swOpenGate.ElapsedMilliseconds}");
            if (lt.success)
            {
                #region 通知岗亭

                if (rlt == null || !rlt.Item3)
                {
                    try
                    {
                        WebSocketUtil.SendWSTip(ff.sCarplate, img, passwayModel.Passway_No, passwayModel.Parking_Name, rlt == null ? "出场成功" : "扫码出场成功", time.ToString("G"));

                        var data = JsonConvert.DeserializeObject<Model.ResBodyDataOut>(lt.data);
                        if (data != null)
                        {
                            var parkorder = data.Item1.FirstOrDefault();
                            if (parkorder != null)
                            {
                                var carType = BLL.CarType.GetEntity(parkorder.ParkOrder_CarType);
                                WebSocketUtil.SendWS(new ResultPass
                                {
                                    success = true,
                                    time = DateTimeHelper.GetNowTime(),
                                    errcode = 0,
                                    errmsg = "扫码出场成功",
                                    isVideoRecord = false,
                                    recog = carRecog,
                                    passres = new ResultPassData
                                    {
                                        type = Model.EnumParkOrderStatus.Out, // 出场类型
                                        code = 1, // 自动放行
                                        carno = ff.sCarplate, // 车牌号
                                        errmsg = "扫码出场成功",
                                        cartype = carType, // 车辆类型
                                        carcardtype = carCards, // 车牌类型
                                        passway = passwayModel, // 通道信息
                                        localimage = rlt?.Item2, // 本地图片路径
                                        img = img, // 图片URL
                                        owner = null, // 车主信息
                                    },
                                    payres = new PayResult
                                    {
                                        payedamount = data.Item3?.Sum(x => x.PayOrder_PayedMoney ?? 0) ?? 0, // 支付金额
                                        parktimemin = (parkorder.ParkOrder_OutTime ?? DateTime.Now).Subtract(parkorder.ParkOrder_EnterTime.Value).TotalMinutes, // 停车时长(分钟)
                                        chuzhiremainingamount = 0, // 储值卡余额
                                        chuzhiamount = 0, // 储值卡扣款金额
                                    },
                                    resorder = new ResultInOutList
                                    {
                                        resOut = new ResultInOut
                                        {
                                            parkorder = parkorder, // 出场订单
                                        }
                                    }
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFileException(ex, $"[{ff.sCarplate}]通知岗亭异常");
                    }
                }

                #endregion

                #region 上传记录

                SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                {
                    PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                    PostRecordHandle_CarPlate = ff.sCarplate,
                    PostRecordHandle_Datas = lt.data,
                    PostRecordHandle_ToType = 2,
                    PostRecordHandle_Status = 0,
                    PostRecordHandle_ReturnMsg = string.Empty,
                    PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                });

                #endregion

                _ = CustomThreadPool.ScheduledTaskPool?.QueueTask(null, () =>
                {
                    ParkSpaceUtil.UpdateDate();
                    return Task.CompletedTask;
                });

                sRemark = $"[{ff.sCarplate}]出场确认处理成功!";
                LocalMemoryCache.Del(ff.sOrderNo);
                return true;
            }
            else
            {
                if (lt.errcode == 201)
                {
                    Model.InCar inModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo='{ff.sCarplate}'");
                    if (inModel != null)
                    {
                        if (inModel.InCar_ParkOrderNo == ff.sOrderNo && inModel.InCar_Status > EnumParkOrderStatus.In)
                        {
                            sRemark = $"[{ff.sCarplate}]出场确认处理成功.";
                            LocalMemoryCache.Del(ff.sOrderNo);
                            return true;
                        }
                    }
                }
            }

            sRemark = $"[{ff.sCarplate}]出场确认处理不成功:" + lt.errmsg;
            LocalMemoryCache.Del(ff.sOrderNo);
            return false;
        }
        catch (Exception ex)
        {
            LocalMemoryCache.Del(ff.sOrderNo);
            LogManagementMap.WriteToFileException(ex, $"出场确认异常：{TyziTools.Json.ToString(ff)}");
            return false;
        }
    }

    /// <summary>
    /// 获取车道状态
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="device">设备信息</param>
    /// <param name="sRemark">上下文输出信息</param>
    /// <param name="value">上下文信息</param>
    /// <returns>是否执行成功</returns>
    private static async Task<(bool, string, string)> GateStatus(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt device, string sRemark, string value)
    {
        int gateState;
        int groundState;
        try
        {
            if (passwayModel.Passway_EnableBoard == 1)
            {
                gateState = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.BarrierStatus, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
                groundState = gateState = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense1, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
                sRemark = "从车道控制板获取车道状态成功!";
            }
            else if (passwayModel.Passway_Type is (2 or 3))
            {
                var ns = await ControllerHelper.GetNonMotorizedStatus(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
                groundState = ns.GroundSense1;
                gateState = ns.BreakStatus;
            }
            else
            {
                gateState = await CameraController.ReadIOOutStatusAsync(device.Device_IP, device.Device_InIO ?? 0);
                groundState = await CameraController.ReadGPIOInStatusAsync(device.Device_IP);
                sRemark = "从识别相机获取车道状态成功!";
            }

            value = JsonConvert.SerializeObject(new Tuple<int, int>(groundState, gateState));
            return (true, value, sRemark);
        }
        catch (Exception ex)
        {
            sRemark = ex.Message;
            LogManagementMap.WriteToFileException(ex, $"获取车道状态异常：{TyziTools.Json.ToString(ff)}");
            return (false, value, sRemark);
        }
    }

    /// <summary>
    /// 获取车道订单信息
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="device">设备信息</param>
    /// <param name="value">上下文输出信息</param>
    /// <param name="sRemark">上下文信息</param>
    /// <returns>是否执行成功</returns>
    private static async Task<(bool, string, string)> GetOutGateOrder(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt device, string value, string sRemark)
    {
        try
        {
            //有牌车
            //订单号为空则是扫车道码获取价格
            if (string.IsNullOrEmpty(ff.sOrderNo))
            {
                var bl = await GetConfirmCarOut(ff, value, sRemark); //获取当前车道弹出窗口的缴费信息
                if (bl.Item1 || (bl.Item3?.Contains("无入场记录") ?? false))
                {
                    return bl;
                }

                if (passwayModel.Passway_Type == 3)
                {
                    var userno = ff.userNo;
                    return await CreateNoCarOrder(passwayModel, device, value, sRemark, userno);
                }

                sRemark = "当前车道不存在缴费信息！";
                return (true, value, sRemark);
            }

            //无入场记录扫码获取订单方式 0：优先有无牌车、1：优先有牌车。默认优先有无牌车
            if (AppCache.GetPolicyPark.PolicyPark_NoRecordGetMoney == 1)
            {
                var bl = await GetConfirmCarOut(ff, value, sRemark); //获取当前车道弹出窗口的缴费信息
                if (bl.Item1 || (bl.Item3?.Contains("无入场记录") ?? false))
                {
                    return bl;
                }

                var t1 = await GetOrderCarByNo(ff, passwayModel, device, value, sRemark); //获取指定订单信息
                if (t1.Item1)
                {
                    return t1;
                }

                if (passwayModel.Passway_Type == 3)
                {
                    return await CreateNoCarOrder(passwayModel, device, value, sRemark);
                }

                return t1;
            }

            var blt = await GetOrderCarByNo(ff, passwayModel, device, value, sRemark); //获取指定订单信息
            if (blt.Item1)
            {
                return blt;
            }

            var t2 = await GetConfirmCarOut(ff, value, sRemark); //获取当前车道弹出窗口的缴费信息
            if (t2.Item1 || (t2.Item3?.Contains("无入场记录") ?? false))
            {
                return t2;
            }

            if (passwayModel.Passway_Type == 3)
            {
                return await CreateNoCarOrder(passwayModel, device, value, sRemark);
            }

            return t2;
        }
        catch (Exception ex)
        {
            sRemark = ex.Message;
            LogManagementMap.WriteToFileException(ex, $"获取车道订单异常：{TyziTools.Json.ToString(ff)}");
            return (false, value, sRemark);
        }
    }

    /// <summary>
    /// 检测当前弹窗信息
    /// </summary>
    /// <param name="passResule"></param>
    /// <param name="ff"></param>
    /// <param name="passwayModel"></param>
    /// <param name="device"></param>
    /// <param name="value"></param>
    /// <param name="sRemark"></param>
    private static void CheckPassWayOrder(ResultPass passResule, DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt device, ref string value, ref string sRemark)
    {
        sRemark = "获取车道缴费信息成功！";
        var estModel = new EstOutPayInfoResult
        {
            code = 0
        };
        if (passResule.success)
        {
            //是否允许通行，0 - 禁止通行 ,1 - 自动放行，2 - 弹窗确认放行,3 - 排队等候,4 - 最低收费缴费通行(出口时未找到记录)
            switch (passResule.passres.code)
            {
                case 0:
                    {
                        sRemark = "禁止通行";
                        break;
                    }
                case 1:
                    {
                        sRemark = "自动放行";
                        break;
                    }
                case 2:
                    {
                        if (passResule.payres != null)
                        {
                            estModel.code = 1;
                            if (passResule.resorder.resOut?.onenter == 0)
                            {
                                estModel.OrderNo = passResule.resorder?.resOut?.parkorder?.ParkOrder_No ?? passResule.passres?.parkorderno;
                            }
                            else
                            {
                                estModel.OrderNo = passResule.resorder?.resOut?.parkorder?.ParkOrder_No ?? passResule.passres?.parkorderno;
                            }

                            estModel.chargeMoney = passResule.payres.payedamount;
                            var sCouponKey = new List<string>();
                            if (passResule.payres.uselist != null && passResule.payres.uselist.Count > 0)
                            {
                                sCouponKey = passResule.payres.uselist.Select(x => x.CouponRecord_No).ToList();
                            }

                            estModel.CouponKey = sCouponKey;
                            estModel.payResult = passResule.payres;
                        }
                        else
                        {
                            sRemark = $"[{passResule.passres.carno}]人工确认放行";
                        }

                        break;
                    }
                case 3:
                    {
                        sRemark = "排队等候";
                    }
                    break;
                case 4:
                    {
                        estModel.code = 1;
                        estModel.OrderNo = passResule.resorder?.resOut?.parkorder?.ParkOrder_No ?? passResule.passres?.parkorderno;
                        estModel.chargeMoney = passResule.payres.payedamount - passResule.payres.cashrobotamount;
                        var sCouponKey = new List<string>();
                        if (passResule.payres.uselist != null && passResule.payres.uselist.Count > 0)
                        {
                            sCouponKey = passResule.payres.uselist.Select(x => x.CouponRecord_No).ToList();
                        }

                        estModel.CouponKey = sCouponKey;
                        estModel.payResult = passResule.payres;
                        sRemark = "最低收费";
                        break;
                    }
            }
        }
        else
        {
            sRemark = $"[{passResule.passres.carno}]检测通行权限失败:[{passResule.errmsg}]";
        }

        estModel.msg = sRemark;
        value = JsonConvert.SerializeObject(estModel);
    }

    /// <summary>
    /// 创建电动车无牌车订单
    /// </summary>
    /// <param name="passwayModel"></param>
    /// <param name="device"></param>
    /// <param name="value"></param>
    /// <param name="sRemark"></param>
    /// <returns></returns>
    private static async Task<(bool, string, string)> CreateNoCarOrder(PasswayExt passwayModel, DeviceExt device, string value, string sRemark, string userno = "")
    {
        await Task.Delay(10);
        var s1 = Utils.CreateAuthStr_All(8, false).ToUpper();
        var data = PassHelper.OnCheckCarPass(new ParkCarInOut
        {
            carno = s1,
            cartype = string.Empty,
            time = DateTimeHelper.GetNowTime(),
            parkno = passwayModel.Passway_ParkNo,
            camerano = device.Device_No,
            img = string.Empty,
            mode = 2
        }, AppBasicCache.GetBasicCache);
        if (data.success)
        {
            //是否允许通行，1-自动放行，2-弹窗确认放行，0-禁止通行,3-排队等候,4-最低收费缴费通行(出口时未找到记录)
            if (data.passres != null)
            {
                if (data.passres.code == 1 || data.passres.code == 4 || (data.payres?.payedamount > 0 && data.passres.code == 2))
                {
                    var passres = data.passres;
                    var payres = data.payres;

                    var links = PasswayLink.GetAllEntity(passres.passway.Passway_ParkNo) ?? new List<Model.PasswayLink>();
                    var link = links.Find(x => x.PasswayLink_PasswayNo == passres.passway.Passway_No && x.PasswayLink_GateType == 0);
                    var area = ParkArea.GetEntity(link.PasswayLink_ParkAreaNo);

                    #region 摩托车道扫码缴费出场相机抓拍
                    var tempImage = string.Empty;
                    // 只在最低收费缴费通行(code == 4)时才进行抓拍
                    if (passres.code == 4)
                    {
                        // 查找车道对应的非机动车控制器
                        var controller = DevicePool.Instance.GetAllDevices()
                            .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));
                        if (controller != null && controller is NonMotorizedOf Non)
                        {
                            // 从缓存读取设备的最新信息
                            var NonDevice = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                            if (NonDevice != null)
                            {
                                // 判断是否关联识别相机并进行抓拍
                                if (!string.IsNullOrWhiteSpace(NonDevice.Device_FNo) && NonDevice.Device_FNo != "0")
                                {
                                    // 获取关联识别相机
                                    var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == NonDevice.Device_FNo);
                                    if (camera != null)
                                    {
                                        tempImage = CameraImageHelper.ImageSaveHSPathBig(s1, AppBasicCache.SentryHostInfo?.SentryHost_No);
                                        _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                                        {
                                            var swSnapImg = new Stopwatch();
                                            swSnapImg.Start();
                                            await LPRTools.GetSnapShootToJpeg(camera, tempImage);
                                            swSnapImg.Stop();
                                            LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"[{camera.Device_IP}][{s1}]摩托车道最低收费缴费出场抓拍图片消耗时长：{swSnapImg.ElapsedMilliseconds}");
                                        });
                                    }
                                }
                            }
                        }
                    }
                    // 只有在最低收费缴费通行时才抓拍，其他情况不进行抓拍
                    #endregion

                    var imgSrc = string.Empty;
                    if (!string.IsNullOrEmpty(passres.img))
                    {
                        imgSrc = PassHelperBiz.GetImageHttpUrl(passres.passway.Passway_ParkNo, passres.img, AppBasicCache.SentryHostInfo?.SentryHost_No);
                    }
                    else if (!string.IsNullOrEmpty(tempImage))
                    {
                        // 如果没有原始图片但有抓拍图片，使用抓拍图片
                        imgSrc = PassHelperBiz.GetImageHttpUrl(passres.passway.Passway_ParkNo, tempImage, AppBasicCache.SentryHostInfo?.SentryHost_No);
                        passres.img = tempImage;
                        passres.localimage = tempImage;
                    }

                    if (!string.IsNullOrEmpty(passres.localimage))
                    {
                        passres.localimage = HttpUtility.UrlEncode(passres.localimage);
                    }

                    //#region 创建提前五分钟的订单记录
                    var enterTime = data.time.Value.AddMinutes(-5);
                    decimal totalmoney = 0;
                    if (data.resorder?.resOut != null)
                    {
                        totalmoney = data.resorder.resOut.minmoney.HasValue ? (decimal)data.resorder.resOut.minmoney.Value : 0;
                        if (data.resorder?.resOut?.onmachorder == 1)
                        {
                            enterTime = data.resorder?.resOut?.noRecordOrder.ParkOrder_EnterTime.Value ?? enterTime;
                            totalmoney = data.payres?.orderamount ?? 0;
                        }
                    }

                    var order = BLL.ParkOrder.CreateParkOrder(area.ParkArea_ParkNo, area.ParkArea_No, area.ParkArea_Name, passres.carno, passres.carcardtype?.CarCardType_No, passres.carcardtype?.CarCardType_Name, passres.cartype?.CarType_No, passres.cartype?.CarType_Name, enterTime, passres.passway.Passway_No, passres.passway.Passway_Name, 0, 1, data.passres?.owner?.Owner_No, data.passres?.owner?.Owner_Name, $"{Utils.CreateNumberWith()}-{s1}");
                    if (order != null)
                    {
                        order.ParkOrder_TotalAmount = totalmoney;
                        order.ParkOrder_StatusNo = EnumParkOrderStatus.In;
                        order.ParkOrder_OutPasswayNo = passres.passway.Passway_No;
                        order.ParkOrder_OutPasswayName = passres.passway.Passway_Name;
                        order.ParkOrder_OutType = 1;
                        order.ParkOrder_OutTime = data.time;
                        // 确保使用抓拍的图片信息
                        order.ParkOrder_OutImg = !string.IsNullOrEmpty(tempImage) ? HttpUtility.UrlEncode(tempImage) : passres.localimage;
                        order.ParkOrder_OutImgPath = imgSrc;
                        order.ParkOrder_OutAdminAccount = AppBasicCache.GetAdmin(passres.passway.Passway_No)?.Admins_Account;
                        order.ParkOrder_OutAdminName = AppBasicCache.GetAdmin(passres.passway.Passway_No)?.Admins_Name;
                        order.ParkOrder_FreeReason = "";
                        order.ParkOrder_IsSettle = 0;
                        order.ParkOrder_IsNoInRecord = 0;
                        order.ParkOrder_IsUnlicensedCar = 1;
                        order.ParkOrder_IsAutoOutCar = 1;
                        order.ParkOrder_EnterRemark = "电动车车道扫码创建记录";

                        //int res = BLL.BaseBLL._Add(order);
                        //if (res <= 0) { LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"电动车车道扫码创建记录失败:{TyziTools.Json.ToString(order)}"); }

                        data.resorder.resOut.parkorder = order;
                        data.passres.parkorderno = order.ParkOrder_No;

                        var rempdata = new ResBodyDataIn(new List<ParkOrder> { order }, null);
                        //BLL.ParkOrderApi.CarIn(passwayModel.Passway_ParkNo, rempdata);

                        var dataf1 = new EstPassInfor
                        {
                            OrderNo = order.ParkOrder_No,
                            chargeMoney = data.passres.code == 4 ? (data.resorder.resOut.minmoney.HasValue ? (decimal)data.resorder.resOut.minmoney.Value : 0) : data.passres.code == 2 ? (data.payres?.payedamount ?? 0) : 0,
                            CouponKey = default,
                            payResult = data.payres,
                            carInData = order
                        };

                        GetMemoryCache.Set($"MotoCarOut=>{order.ParkOrder_No}", Tuple.Create(data, order), cacheEntityOpsMoto);

                        #region 关闭预出场订单
                        try
                        {
                            if (!string.IsNullOrEmpty(userno))
                            {
                                //获取旧订单号
                                var oldOrderNo = DataCache.NonMotoPasswayOrderCache.Get(userno);
                                if (!string.IsNullOrEmpty(oldOrderNo))//找到旧订单号，查询Order表关闭
                                {
                                    var oldOrder = BLL.ParkOrder.GetEntity(oldOrderNo);
                                    if (oldOrder != null && oldOrder.ParkOrder_StatusNo == 200 && oldOrder.ParkOrder_OutType == 1)
                                    {
                                        oldOrder.ParkOrder_StatusNo = 202;
                                        oldOrder.ParkOrder_Remark = "无入场记录重复创建关闭订单";
                                        var r = BLL.OrderDetail.UpdateByList(new List<Model.ParkOrder>() { oldOrder });
                                        if (!r)
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, oldOrder.ParkOrder_CarNo + "无入场记录重复创建关闭订单异常：" + TyziTools.Json.ToString(oldOrder));
                                        }
                                    }
                                }

                                //缓存当前最新的订单号
                                DataCache.NonMotoPasswayOrderCache.Set(userno, order.ParkOrder_No);
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, order.ParkOrder_CarNo + "无入场记录重复创建关闭订单异常：" + ex.ToString());
                        }

                        #endregion
                        value = JsonConvert.SerializeObject(dataf1);
                        return (true, value, sRemark);
                    }

                    sRemark = "创建订单失败了";
                    return (false, value, sRemark);
                }

                sRemark = "车道禁止通行，" + data.errmsg;
                return (false, value, sRemark);
            }

            sRemark = "扫码获取通行信息失败！";
            return (false, value, sRemark);
        }

        sRemark = "扫码通行检测返回失败！" + data.errmsg;
        return (false, value, sRemark);
    }

    /// <summary>
    /// [有牌车]获取当前车道弹出窗口的缴费信息
    /// </summary>
    /// <param name="ff"></param>
    /// <param name="value"></param>
    /// <param name="sRemark"></param>
    /// <returns></returns>
    private static async Task<(bool, string, string)> GetConfirmCarOut(DeviceOptionsModel ff, string value, string sRemark)
    {
        if (ConfirmRelease.Results.TryGetValue(ff.PasswayNo, out var passInfo))
        {
            sRemark = "获取车道缴费信息成功！";
            await Task.Delay(10);

            #region 是否存在追缴金额
            string unpaidOrderNo = "";
            decimal unpaidMoeny = 0;
            decimal unpaidOrderMoeny = 0;
            decimal unpaidCZAmount = 0;
            PayResult payres = null;
            if (passInfo != null && passInfo.unpaidresult != null)
            {
                passInfo.unpaidresult.ForEach(x =>
                {
                    if (x.payres != null && x.payres.payedamount > 0)
                    {
                        unpaidOrderMoeny += x.payres.orderamount;
                        unpaidMoeny += x.payres.payedamount;
                        unpaidOrderNo = x.unpaidorder?.ParkOrder_No;
                        unpaidCZAmount += x.payres.chuzhiamount;
                        payres = x.payres;
                    }
                });
            }
            #endregion

            var orderno = passInfo?.passres?.parkorderno ?? passInfo?.resorder?.resOut?.parkorder?.ParkOrder_No ?? passInfo?.resorder?.resOut?.noRecordOrder?.ParkOrder_No;

            //出口有牌车扫码，判断是否存在0元弹窗的情况，有弹窗则无需缴费，管理员确定放行
            if (string.IsNullOrEmpty(ff.sOrderNo) || (!string.IsNullOrEmpty(ff.sOrderNo) && ff.sOrderNo != orderno))
            {
                if (passInfo.passres?.gate == 0 && passInfo.resorder?.resOut != null && passInfo.resorder.resOut.onenter == 0)
                {
                    if (passInfo.payres == null || passInfo.payres.payed == 0 || passInfo.payres.payedamount == 0)
                    {
                        if (unpaidMoeny == 0) return (false, value, $"{passInfo.passres.carno} 无入场记录，确定放行");
                    }
                }
            }

            {

                if (ff.ActionType == 1)
                {
                    var orderprice = new Model.OrderPrice()
                    {
                        OrderPrice_No = Utils.CreateNumber_SnowFlake,
                        OrderPrice_CarNo = passInfo.passres?.carno,
                        OrderPrice_ParkOrderNo = orderno,
                        OrderPrice_CalcTime = passInfo.time,
                        OrderPrice_OutPasswayNo = passInfo.passres?.passway?.Passway_No,
                    };
                    orderprice.OrderPrice_PayedMoney = passInfo.payres?.payedamount;
                    orderprice.OrderPrice_CouponMoney = passInfo.payres?.couponamount;
                    orderprice.OrderPrice_UseMinute = passInfo.payres?.uselist?.Sum(x => x.CouponRecord_DiscountMin) ?? 0;

                    var orderresult = new Model.OrderResult()
                    {
                        OrderResult_No = orderprice.OrderPrice_No,
                        OrderResult_PayData = HttpUtility.UrlEncode(TyziTools.Json.ToString(passInfo))
                    };
                    BLL.BaseBLL._Insert(orderprice, orderresult);
                }

                var data = new EstPassInfor
                {
                    OrderNo = orderno,
                    chargeMoney = passInfo?.payres?.payedamount ?? 0,
                    CouponKey = passInfo?.payres?.uselist?.Select(x => x.CouponRecord_No).ToList() ?? default,
                    payResult = passInfo?.payres
                    //carInData = passInfo?.resorder?.resOut?.parkorder
                };
                value = JsonConvert.SerializeObject(data);
                return (true, value, sRemark);
            }
        }

        sRemark = "当前车道不存在缴费信息！";
        return (false, value, sRemark);
    }

    /// <summary>
    /// 获取指定订单信息
    /// </summary>
    /// <param name="ff"></param>
    /// <param name="passwayModel"></param>
    /// <param name="device"></param>
    /// <param name="value"></param>
    /// <param name="sRemark"></param>
    /// <returns></returns>
    private static async Task<(bool, string, string)> GetOrderCarByNo(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt device, string value, string sRemark)
    {
        var policy = AppBasicCache.GetSentryPolicyPasswayDic.FirstOrDefault(x => x.Value.PolicyPassway_PasswayNo == ff.PasswayNo);
        if (policy.Value == null)
        {
            sRemark = $"[{ff.sCarplate}]=>车道关联策略信息不存在！";
            return (false, value, sRemark);
        }

        if (policy.Value.PolicyPassway_Scan == 0)
        {
            sRemark = $"[{ff.sCarplate}]=>车道禁止扫码！";
            return (false, value, sRemark);
        }

        var Gndsenver1 = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense1, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
        var Gndsenver2 = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense2, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
        var Gndsenver3 = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.GroundSense3, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
        var Brakestatus = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.BarrierStatus, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
        var iCounter = ControllerHelper.GetStatusByY312(passwayModel.Passway_No, BarrierOfY312StatusIndex.Counter, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
        var iHaveCar = -1;

        //判断车道是否有弹出确认窗口
        if (PasswayConfirmReleaseUtil.HavePasswayConfirm(ff.PasswayNo))
        {
            iHaveCar = 1;
        }
        else
        {
            iHaveCar = 0;
        }

        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"车道检测是否弹出窗口 {ff.PasswayNo}/{iHaveCar}");

        //需要检查地感
        if (policy.Value.PolicyPassway_Scan == 1)
        {
            //是否启用车道控制板
            if (passwayModel.Passway_EnableBoard == 1)
            {
                //车道属于同进同出，则入车道要判断3号地感
                var temp1 = passwayModel.Passway_SameInOut == 1
                    ? passwayModel.Passway_SameInOutType == 0 ? Gndsenver3 == 1 : Gndsenver1 == 1
                    : Gndsenver1 == 1;

                //道闸不能处于开状态 入口地感要被触发
                if (Brakestatus == 1 || !temp1 || iHaveCar == 1 || iCounter != 0)
                {
                    if (Brakestatus == 1)
                    {
                        sRemark = "检测车道道闸已打开，禁止扫码！";
                    }
                    else if (!temp1)
                    {
                        sRemark = "未检测到地感，禁止扫码！";
                    }
                    else if (iHaveCar == 1)
                    {
                        sRemark = "当前车道已经弹出通行确认框，禁止扫码！";
                    }
                    else if (iCounter != 0)
                    {
                        sRemark = "当前车道车辆计数器不为0！";
                    }
                    //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                    if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(device.Drive_Name))
                    {
                        var camera = TcpConnPools.DevicePool.Instance.GetDevice(device.Device_IP);
                        if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                        {
                            var leds = new List<LedModel>();
                            leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                            leds.Add(new LedModel(sRemark, 1, 1, 1));
                            await cameraOfYs.SendLedAsync(leds);
                        }
                    }
                    else
                    {
                        var data485 = Passthrough485Util.InstantDisplay(sRemark);
                        await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                    }
                    return (false, value, sRemark);
                }
            }

            //判断如果是摩托车车道则读取摩托车车道状态
            if (passwayModel.Passway_Type is (2 or 3))
            {
                var ns = await ControllerHelper.GetNonMotorizedStatus(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo);
                Gndsenver1 = ns.GroundSense1;
                Gndsenver2 = ns.GroundSense2;
                Gndsenver3 = ns.GroundSense3;
                Brakestatus = ns.BreakStatus;
                iCounter = 0;
                if (Brakestatus is (1 or 2) || iHaveCar == 1 || (Gndsenver1 == 0 && Gndsenver3 == 0))
                {
                    if (Brakestatus is (1 or 2))
                    {
                        sRemark = "检测车道道闸已打开，禁止扫码！";
                    }
                    else if (iHaveCar == 1)
                    {
                        sRemark = "当前车道已经弹出通行确认框，禁止扫码！";
                    }
                    else if (Gndsenver1 == 0 && Gndsenver3 == 0)
                    {
                        sRemark = "未检测到地感，禁止扫码！";
                    }

                    _ = ControllerHelper.SendOpenGateByMotoAsync(passwayModel.Passway_No, passwayModel.Passway_SameInOut ?? 0, passwayModel.Passway_OutNo, NonMotorizedOperation.Other, sRemark, sRemark, true, null);

                    #region 非机动车车道控制器关联相机播报
                    try
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"开始处理非机动车车道控制器关联相机播报，车道号：{passwayModel.Passway_No}，播报内容：{sRemark}");

                        //查找车道对应的非机动车控制器
                        var controller = DevicePool.Instance.GetAllDevices()
                            .FirstOrDefault(d => d.Model.Type == DeviceType.NonMotorizedLaneControl && d.Model is ControllerModel c && (c.PasswayNo == passwayModel.Passway_No || ((passwayModel.Passway_SameInOut ?? 0) == 1 && c.PasswayNo == passwayModel.Passway_OutNo)));

                        if (controller != null && controller is NonMotorizedOf Non)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"找到非机动车控制器，设备号：{Non.Model.DeviceNo}，车道号：{passwayModel.Passway_No}");

                            //从缓存读取设备的最新信息
                            var NonDevice = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == Non.Model.DeviceNo);
                            if (NonDevice != null)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"获取到设备缓存信息，设备号：{NonDevice.Device_No}，关联相机号：{NonDevice.Device_FNo}，相机使用状态：{NonDevice.Device_CameraUsage}");

                                //判断是否关联识别相机并播报
                                if (!string.IsNullOrWhiteSpace(NonDevice.Device_FNo) && NonDevice.Device_FNo != "0" && NonDevice.Device_CameraUsage == 1)
                                {
                                    //获取关联识别相机
                                    var camera = AppBasicCache.GetAllDeivces.Values.FirstOrDefault(x => x.Device_No == NonDevice.Device_FNo);
                                    if (camera != null)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"非机动车控制器关联相机播报：车道[{passwayModel.Passway_No}] -> 相机[{camera.Device_IP}:{camera.Device_Com}]，播报内容：{sRemark}");

                                        var voice = Passthrough485Util.InstantDisplay(sRemark);
                                        _ = CameraController.SendDataBy485Async(camera.Device_IP, (SerialIndexType)(camera.Device_Com ?? 0), voice, nameof(Passthrough485Util.InstantDisplay));
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"未找到关联识别相机，相机设备号：{NonDevice.Device_FNo}，车道号：{passwayModel.Passway_No}");
                                    }
                                }
                                else
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"设备未配置关联相机或相机未启用，设备号：{NonDevice.Device_No}，关联相机号：{NonDevice.Device_FNo}，相机使用状态：{NonDevice.Device_CameraUsage}");
                                }
                            }
                            else
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"未找到非机动车控制器设备缓存信息，设备号：{Non.Model.DeviceNo}，车道号：{passwayModel.Passway_No}");
                            }
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"未找到对应的非机动车控制器，车道号：{passwayModel.Passway_No}，出场车道号：{passwayModel.Passway_OutNo}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFileException(ex, $"非机动车车道控制器关联相机播报失败，车道号：{passwayModel?.Passway_No}，播报内容：{sRemark}");
                    }
                    #endregion

                    return (false, value, sRemark);
                }
            }
            else
            {
                var byteStatus = await CameraController.ReadGPIOInStatusAsync(device.Device_IP);
                if (byteStatus == 0)
                {
                    sRemark = "未检测到地感，禁止扫码！";
                    //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                    if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(device.Drive_Name))
                    {
                        var camera = TcpConnPools.DevicePool.Instance.GetDevice(device.Device_IP);
                        if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                        {
                            var leds = new List<LedModel>();
                            leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                            leds.Add(new LedModel("未检测到地感", 1, 1, 1));
                            await cameraOfYs.SendLedAsync(leds);
                        }
                    }
                    else
                    {
                        var data485 = Passthrough485Util.InstantDisplay(sRemark);
                        await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                    }
                    return (false, value, sRemark);
                }
            }
        }

        //判断车道同进同出
        if (passwayModel.Passway_SameInOut == 1 && passwayModel.Passway_Type == 1)
        {
            if (passwayModel.Passway_EnableBoard == 1)
            {
                var isHaveCar = false;

                //获取同进同出的车道信息
                if (!AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayModel.Passway_OutNo, out PasswayExt sameModel))
                {
                    return (true, string.Empty, string.Empty);
                }

                //排序
                var passwayList = new List<PasswayExt> { passwayModel, sameModel }.OrderBy(x => x.Passway_ID).ToList();
                //两个车道以Id排序后，连接成一个key
                var cacheSamekey = string.Join("_", passwayList.Select(x => x.Passway_No));

                if (AppBasicCache.GetSameInOutLs.TryGetValue(cacheSamekey, out var ilanestatus))
                {
                    //判断同进同出上一辆车的方向，只有方向不同才需要判断礼让通行
                    if (ilanestatus != passwayModel.Passway_SameInOutType)
                    {
                        //【入口车道 判断1号地感】
                        if (passwayModel.Passway_SameInOutType == 0 && Gndsenver1 == 1)
                        {
                            isHaveCar = true;
                        }
                        //【出口车道 判断3号地感】
                        else if (passwayModel.Passway_SameInOutType == 1 && Gndsenver3 == 1)
                        {
                            isHaveCar = true;
                        }

                        //判断有车，同时上一辆车通行方向不一场，提示礼让通行
                        if (isHaveCar)
                        {
                            sRemark = "同车道车辆地感检测有车，请稍候扫码！";
                            //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                            if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(device.Drive_Name))
                            {
                                var camera = TcpConnPools.DevicePool.Instance.GetDevice(device.Device_IP);
                                if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                                {
                                    var leds = new List<LedModel>();
                                    leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                                    leds.Add(new LedModel(sRemark, 1, 1, 1));
                                    await cameraOfYs.SendLedAsync(leds);
                                }
                            }
                            else
                            {
                                var data485 = Passthrough485Util.InstantDisplay(sRemark);
                                await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                            }
                            return (false, value, sRemark);
                        }
                    }
                }

                if (!isHaveCar)
                {
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSameInOutLs, cacheSamekey, passwayModel.Passway_SameInOutType);
                }

                LogManagementMap.WriteToFile($"无牌车在{passwayModel.Passway_Name}同进同出,输出地感1：{Gndsenver1} 地感3：{Gndsenver3} 判断是否有车：{isHaveCar} 上次车辆通行方向：{ilanestatus} 本次车辆通行方向：{passwayModel.Passway_SameInOutType}");
            }
            else
            {
                if (AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayModel.Passway_OutNo, out var samePasswayModel))
                {
                    //判断车道是否有弹出确认窗口
                    if (PasswayConfirmReleaseUtil.HavePasswayConfirm(samePasswayModel.Passway_No))
                    {
                        sRemark = "同车道车辆检测有车，请稍候扫码！";
                        //判断启用宇视显示屏卡，并且相机类型是宇视相机，则使用宇视显示屏卡
                        if (AppBasicCache.CurrentSysConfigContent.SysConfig_ScreenDrive == "2" && CameraDeviceType.driveNameListYS.Contains(device.Drive_Name))
                        {
                            var camera = TcpConnPools.DevicePool.Instance.GetDevice(device.Device_IP);
                            if (camera is ICamera && camera is CameraOfYS cameraOfYs)
                            {
                                var leds = new List<LedModel>();
                                leds.Add(new LedModel($"禁止扫码", 0, 0, 1));
                                leds.Add(new LedModel(sRemark, 1, 1, 1));
                                await cameraOfYs.SendLedAsync(leds);
                            }
                        }
                        else
                        {
                            var data485 = Passthrough485Util.InstantDisplay(sRemark);
                            await CameraController.SendDataBy485Async(device.Device_IP, (SerialIndexType)(device.Device_Com ?? 0), data485, "Mid-" + nameof(Passthrough485Util.InstantDisplay));
                            return (false, value, sRemark);
                        }
                    }
                }
            }
        }

        value = ff.sOrderNo;
        sRemark = "获取信息成功";
        return (true, value, sRemark);
    }

    /// <summary>
    /// 云托管手动入场
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="mainDevice">设备信息</param>
    /// <param name="sRemark">上下文信息</param>
    /// <returns>是否执行成功</returns>
    private static async Task<(bool, string)> PullManualEnterCar(DeviceOptionsModel ff, PasswayExt passwayModel, DeviceExt mainDevice, string sRemark)
    {
        try
        {
            //抓拍图片
            var tempImage = CameraImageHelper.ImageSaveHSPathBig(ff.sCarplate, ff.SentryHostNo);
            var imgUrl = "";
            if (passwayModel.Passway_Type == 1)
            {
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () => { await LPRTools.GetSnapShootToJpeg(mainDevice, tempImage); });

                imgUrl = LPRTools.GetSentryHostImg(tempImage);
            }

            //获取云托管类型
            var cardNames = string.Empty;

            var data = PassHelper.OnCheckCarPass(new ParkCarInOut
            {
                camerano = mainDevice.Device_No,
                carno = ff.sCarplate,
                cartype = cardNames,
                time = DateTimeHelper.GetNowTime(),
                parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                img = tempImage,
                mode = 6,
                isreal = 0,
                credibility = 100,
                licensepoint = string.Empty,
                carlogo = string.Empty,
                caryear = string.Empty
            }, AppBasicCache.GetBasicCache);
            if (data.success && (data.passres.code == 1 || data.passres.code == 2))
            {
                #region 控制板发送来车事件

                if (passwayModel.Passway_IsBackCar == 1 && passwayModel.Passway_EnableBoard == 1)
                {
                    var sOrderno = data.passres.parkorderno;
                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"{sOrderno}-倒车功能已启用-0x12");
                    BarrierDeviceUtilsl.SendCarOrder(passwayModel.Passway_No, sOrderno, 0, passwayModel.Passway_SameInOut, passwayModel.Passway_OutNo);
                }

                #endregion

                //是否开双闸
                var (isDoubleGate, linkCameraNoList) = Command.GateCmd.GetDoubleGateInfo(mainDevice.Device_PasswayNo, data.passres.cartype.CarType_No);
                var gateResult = await GateCmd.ExecuteAsync(mainDevice, GateCmd.ActionEnum.Open, isDoubleGate: isDoubleGate,linkCameraNoList:linkCameraNoList);
                if (data.recog != null)
                {
                    //更新执行开闸的结果
                    data.recog.CarRecog_OpenStatus = gateResult.Success ? 1 : -1;
                    BLL.CarRecog.GetInstance(data.recog.CarRecog_Time ?? DateTime.Now)
                        .UpdateOpenStatus(data.recog.CarRecog_No, (int)data.recog.CarRecog_OpenStatus);
                }

                if (gateResult.Success)
                {
                    #region 语音播报

                    var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passwayModel.Passway_No);
                    if (areaLink.Count() > 1)
                    {
                        BroadcastUtil.AutoReleaseOut(data, mainDevice);
                    }
                    else
                    {
                        BroadcastUtil.AutoReleaseEnter(areaLink.FirstOrDefault().Value.PasswayLink_ParkAreaNo, data, mainDevice);
                    }

                    #endregion

                    var rb = PassHelper.CarInComplete(new ParkGatePass
                    {
                        account = AppBasicCache.GetAdmin(passwayModel?.Passway_No).Admins_Account,
                        parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                        carno = data.passres.carno,
                        orderno = data.passres.parkorderno,
                        code = 200,
                        imgpath = tempImage,
                        name = ff.operatorName,
                        camerano = mainDevice.Device_No,
                        onenter = 0,
                        time = data.time,
                        img = data.passres?.img,
                        isSupplement = data?.isSupplement ?? false,
                        respass = data,
                    });
                    if (rb.success)
                    {
                        #region 通知岗亭

                        WebSocketUtil.SendWSTip(ff.sCarplate, imgUrl, passwayModel.Passway_No, passwayModel.Passway_Name, "云托管手动入场成功", data.time?.ToString("G"));

                        #endregion

                        #region 上传记录

                        //同步记录
                        //if (AppCache.IsWindows)
                        SentryBoxHelper.SyncToPostRecordTransmit(new Model.PostRecordHandle
                        {
                            PostRecordHandle_AddTime = DateTimeHelper.GetNowTime(),
                            PostRecordHandle_CarPlate = data.passres.carno,
                            PostRecordHandle_Datas = rb.data,
                            PostRecordHandle_ToType = 1,
                            PostRecordHandle_Status = 0,
                            PostRecordHandle_ReturnMsg = string.Empty,
                            PostRecordHandle_LastTime = DateTimeHelper.GetNowTime()
                        });

                        #endregion

                        sRemark = $"[{ff.sCarplate}]云托管手动入场成功！";
                        return (true, sRemark);
                    }

                    var po = BLL.ParkOrder.GetEntity(data.passres?.parkorderno);
                    if (po != null)
                    {
                        var detailList = OrderDetail.GetAllEntity(data.passres?.parkorderno);
                        if (detailList != null)
                        {
                            detailList.ForEach(x => { x.OrderDetail_StatusNo = EnumParkOrderStatus.Close; });
                        }

                        po.ParkOrder_StatusNo = EnumParkOrderStatus.Close;
                        var fresult = OrderDetail.UpdateByList(new List<ParkOrder> { po }, detailList);
                        if (fresult)
                        {
                            var res = MiddlewareApi.UpdateOrderDetail(po, detailList);
                        }
                    }

                    sRemark = $"[{ff.sCarplate}]云托管手动入场保存失败:" + rb.errmsg;
                    return (false, sRemark);
                }

                sRemark = $"云托管手动入场车牌[{ff.sCarplate}]失败：{data.errmsg}";
                return (false, sRemark);
            }

            if (string.IsNullOrEmpty(data.errmsg))
            {
                data.errmsg = data.passres?.errmsg;
            }

            sRemark = $"云托管手动入场车牌[{ff.sCarplate}]失败：{data.errmsg}";
            return (false, sRemark);
        }
        catch (Exception ex)
        {
            sRemark = ex.Message;
            LogManagementMap.WriteToFileException(ex, $"云托管手动入场异常：{TyziTools.Json.ToString(ff)}");
            return (false, sRemark);
        }
    }

    /// <summary>
    /// 抓拍图片
    /// </summary>
    /// <param name="device"></param>
    /// <param name="value">图片BASE64</param>
    /// <param name="sRemark"></param>
    /// <returns></returns>
    private static async Task<(bool, string, string)> SnapShootToImage(DeviceExt device, string value, string sRemark)
    {
        var isSuccess = false;
        try
        {
            var tempImage = CameraImageHelper.ImageSaveHSPathBig("Manual", device?.Device_SentryHostNo);
            await LPRTools.GetSnapShootToJpeg(device, tempImage);
            tempImage = ImageTools.ImageHttpPath(device.Device_ParkNo, tempImage, device?.Device_SentryHostNo);
            value = tempImage;
            isSuccess = true;
        }
        catch (Exception ex)
        {
            sRemark = "抓拍失败:" + ex.Message;
            LogManagementMap.WriteToFileException(ex);
        }

        return (isSuccess, value, sRemark);
    }

    /// <summary>
    /// 平台扫码入场信息获取
    /// </summary>
    /// <param name="passwayModel">车道信息</param>
    /// <returns>是否成功，返回信息，返回值</returns>
    private static (bool, string, string) GetInGateOrder(PasswayExt passwayModel)
    {
        string sRemark;
        try
        {
            //获取当前是否有弹出入场确认窗口
            if (ConfirmRelease.Results.TryGetValue(passwayModel.Passway_No, out var resultPass))
            {
                sRemark = "当前车道有弹出入场确认窗口";
                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                var carNo = resultPass.passres.carno;
                var plate = 1;
                var parkOrderNo = resultPass.passres.parkorderno;
                var value = JsonConvert.SerializeObject(new
                {
                    carNo,
                    plate,
                    parkOrderNo
                });
                return (true, value, sRemark);
            }
            else
            {
                sRemark = "当前车道没有弹出入场确认窗口";
                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                var carNo = string.Empty;
                var plate = 0;
                var parkOrderNo = string.Empty;
                var value = JsonConvert.SerializeObject(new
                {
                    carNo,
                    plate,
                    parkOrderNo
                });
                return (true, value, sRemark);
            }
        }
        catch (Exception ex)
        {
            sRemark = "获取出入场信息失败:" + ex.Message;
            LogManagementMap.WriteToFileException(ex);
        }

        return (false, string.Empty, sRemark);
    }

    /// <summary>
    /// 平台推送确认入场
    /// </summary>
    /// <param name="ff">操作信息</param>
    /// <param name="passwayModel">车道信息</param>
    /// <param name="sRemark">上下文信息</param>
    /// <returns>是否成功，返回信息</returns>
    private static async Task<(bool, string)> PushInGateOrdel(DeviceOptionsModel ff, PasswayExt passwayModel)
    {
        string sRemark;
        try
        {
            //获取当前车道是否有弹出入场确认窗口
            if (ConfirmRelease.Results.TryGetValue(passwayModel.Passway_No, out var resultPass))
            {
                //判断当前车道弹出的订单是否匹配
                if (resultPass.passres.parkorderno == ff.sOrderNo)
                {
                    sRemark = "当前车道弹出的订单匹配成功";
                    LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                    //执行入场操作
                    var result = await PasswayConfirmReleaseUtil.ConfirmEnter(resultPass, remark: ff.opRemarks, isOpenGate: true, handEnter: true, Scan: false,
                        phone: ff.BaseData);
                    if (result.Item1)
                    {
                        sRemark = $"平台推送确认入场成功--{sRemark}";
                        LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                        return (true, sRemark);
                    }
                    else
                    {
                        sRemark = $"平台推送确认入场失败--{sRemark}";
                        LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                        return (false, sRemark);
                    }
                }
                else
                {
                    sRemark = "当前车道弹出的订单不匹配,无法确认入场!";
                    LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                    return (false, sRemark);
                }
            }
            else
            {
                sRemark = "当前车道没有弹出入场确认窗口,无法确认入场!";
                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, sRemark); // 记录过程日志
                return (false, sRemark);
            }
        }
        catch (Exception ex)
        {
            sRemark = "平台推送确认入场异常:" + ex.Message;
            LogManagementMap.WriteToFileException(ex);
        }
        return (false, sRemark);
    }

    #endregion

    #region 云托管

    /// <summary>
    /// 云托管 语音播报
    /// </summary>
    public class VoicePlaybackHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "VoicePlayback".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };

            try
            {
                var sRemark = string.Empty;
                var isSuccess = false;
                var passwayno = Convert.ToString(reqPush.data);
                if (ConfirmRelease.Results.TryGetValue(passwayno, out var data))
                {
                    var mainDevice = DeviceCommonUtil.GetPasswayMainDevice(passwayno);
                    if (data.passres.code == 4)
                    {
                        BroadcastUtil.MinimumCharge(data, mainDevice, false, true);
                    }
                    else if (data.passres.code == 3)
                    {
                        BroadcastUtil.WaitInLine(data, mainDevice);
                    }
                    else
                    {
                        BroadcastUtil.ConfirmRelease(data, mainDevice);
                    }

                    isSuccess = true;
                    sRemark = "语音播报成功！";
                }
                else
                {
                    sRemark = "岗亭未查询到缴费窗口信息！";
                }

                resPush.msg = sRemark;
                resPush.data = new { isSuccess };
            }
            catch (Exception ex)
            {
                resPush.msg = "语音播报异常";
                resPush.data = new { isSuccess = false };
                LogManagementMap.WriteToFileException(ex, $"语音播报异常：{TyziTools.Json.ToString(reqPush)}");
            }
            finally
            {
                var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                byteBuffer1.WriteBytes(bytes1);
                context.Channel.WriteAndFlushAsync(byteBuffer1);

                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"云端语音播报返回：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(resPush);
        }
    }

    /// <summary>
    /// 云托管 修改金额
    /// </summary>
    public class UpdateParkOrderMoneyHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "UpdateParkOrderMoney".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };
            try
            {
                var sRemark = string.Empty;
                var isSuccess = false;

                var keys = JObject.Parse(Convert.ToString(reqPush.data));
                var spassno = Convert.ToString(keys["spassno"]);
                var sorderNo = Convert.ToString(keys["sorderNo"]);
                var smoney = Convert.ToString(keys["smoney"]);

                if (ConfirmRelease.Results.TryGetValue(spassno, out var data))
                {
                    if (data.resorder?.resOut?.parkorder?.ParkOrder_No == sorderNo)
                    {
                        #region 更新车道弹框缓存

                        var d1 = Utils.StrToDecimal(smoney);
                        if (d1 > data.payres.orderamount)
                        {
                            sRemark = "修改金额不能大于应收金额！";
                        }
                        else
                        {
                            data.payres.payedamount = d1;
                            isSuccess = PasswayConfirmReleaseUtil.AddOrUpdateResult(data);

                            #endregion

                            #region 语音播报

                            var mainDevice = DeviceCommonUtil.GetPasswayMainDevice(spassno);
                            if (data.passres.code == 4)
                            {
                                BroadcastUtil.MinimumCharge(data, mainDevice, false, true);
                            }
                            else if (data.passres.code == 3)
                            {
                                BroadcastUtil.WaitInLine(data, mainDevice);
                            }
                            else
                            {
                                BroadcastUtil.ConfirmRelease(data, mainDevice);
                            }

                            #endregion

                            #region 通知岗亭

                            WebSocketUtil.SendWSConfirmHandle(3, data);

                            #endregion

                            sRemark = "更新缴费金额成功！";
                        }
                    }
                    else
                    {
                        sRemark = "岗亭缴费窗口订单信息不匹配！";
                    }
                }
                else
                {
                    sRemark = "岗亭未查询到缴费窗口信息！";
                }

                resPush.msg = sRemark;
                resPush.data = new { isSuccess, sRemark };
            }
            catch (Exception ex)
            {
                resPush.msg = "更新缴费金额异常";
                resPush.data = new { isSuccess = false, sRemark = resPush.msg };
                LogManagementMap.WriteToFileException(ex, $"云端修改金额异常：{TyziTools.Json.ToString(reqPush)}");
            }
            finally
            {
                if (context != null)
                {
                    var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                    var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                    byteBuffer1.WriteBytes(bytes1);
                    context.Channel.WriteAndFlushAsync(byteBuffer1);
                }

                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"云端修改金额返回：{TyziTools.Json.ToString(reqPush)}");
            }
            return Task.FromResult(resPush);
        }
    }

    /// <summary>
    /// 云托管 打印小票
    /// </summary>
    public class PullOrderHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullOrder".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };
            try
            {
                var sRemark = string.Empty;
                var isSuccess = false;
                var keys = JObject.Parse(Convert.ToString(reqPush.data));
                var passwayno = Convert.ToString(keys["spassno"]);
                var orderno = Convert.ToString(keys["sorderNo"]);
                if (ConfirmRelease.Results.TryGetValue(passwayno, out var data) &&
                    (data?.resorder?.resOut?.parkorder?.ParkOrder_No == orderno || data?.resorder?.resIn?.parkorder?.ParkOrder_No == orderno))
                {
                    var parkDuration = "";
                    var enterTime = DateTimeHelper.GetNowTime();
                    var outTime = DateTimeHelper.GetNowTime();
                    if (data.passres.code == 4)
                    {
                        parkDuration = "无入场记录";
                        if (data.resorder?.resOut?.parkorder != null)
                        {
                            enterTime = data.resorder.resOut.parkorder.ParkOrder_EnterTime.Value;
                        }
                    }
                    else
                    {
                        var ts = TimeSpan.FromMinutes(data.payres.parktimemin);
                        parkDuration = $"{ts.Days}天{ts.Hours}小时{ts.Minutes}分钟";
                        enterTime = data.resorder.resOut.parkorder.ParkOrder_EnterTime.Value;
                    }

                    BroadcastUtil.PrintTicket(data, "", parkDuration, enterTime.ToString("G"), outTime.ToString("G"));
                    isSuccess = true;
                    sRemark = "语音播报成功！";
                }
                else
                {
                    sRemark = "岗亭未查询到缴费窗口信息！";
                }


                resPush.msg = sRemark;
                resPush.data = new { isSuccess };
            }
            catch (Exception ex)
            {
                resPush.msg = "打印小票异常";
                resPush.data = new { isSuccess = false };
                LogManagementMap.WriteToFileException(ex, $"云端打印小票失败：{TyziTools.Json.ToString(reqPush)}");
            }
            finally
            {
                if (context != null)
                {
                    var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                    var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                    byteBuffer1.WriteBytes(bytes1);
                    context.Channel.WriteAndFlushAsync(byteBuffer1);
                }
                LogManagementMap.WriteToFile(LoggerEnum.WebTcp, $"云端打印小票返回：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(resPush);
        }
    }

    /// <summary>
    /// 云托管 车牌矫正
    /// </summary>
    public class PullMateCarNoHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullMateCarNo".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }
        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            var resPush = new ResPush
            {
                act = reqPush.act,
                tname = reqPush.tname,
                guid = reqPush.guid,
                time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                code = 1
            };
            try
            {
                var sRemark = string.Empty;
                var isSuccess = false;
                var keys = JObject.Parse(Convert.ToString(reqPush.data));
                var passwayNo = Convert.ToString(keys["spassno"]);
                if (AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayNo, out var pass))
                {
                    //spassno, sorderNo,scarNo,snewCarNo, sctrlNo,scarTypeNo
                    var sorderNo = Convert.ToString(keys["sorderNo"]);
                    var scarNo = Convert.ToString(keys["scarNo"]);
                    var snewCarNo = Convert.ToString(keys["snewCarNo"]);
                    var scarTypeNo = Convert.ToString(keys["scarTypeNo"]);

                    var device = DeviceCommonUtil.GetPasswayMainDevice(passwayNo);
                    if (device != null)
                    {
                        var tempImage = CameraImageHelper.ImageSaveHSPathBig(snewCarNo, device.Device_SentryHostNo);

                        if (DeviceServerHandle.LicensePlateQueue.TryGetValue(pass.Passway_No, out var queue))
                        {
                            queue.Enqueue(new CarPlateInfo
                            {
                                CarPlate = snewCarNo,
                                TriggerTime = DateTimeHelper.GetNowTime(),
                                BigJPGPath = tempImage,
                                CarmeraIP = device.Device_IP,
                                CarTypeMode = "车辆出场",
                                DeviceNo = device.Device_No,
                                IsRealLicensePlate = false,
                                TriggerMode = 0,
                                PasswayNo = pass.Passway_No,
                                MixModel = "",
                                Mode = 6
                                //车牌颜色  0未知,1蓝色,2黄色,3白色,4黑色,5绿色
                            });

                            if (!string.IsNullOrWhiteSpace(tempImage))
                            {
                                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () => { await LPRTools.GetSnapShootToJpeg(device, tempImage); });
                                sRemark = "已经发送矫正出场命令";
                                isSuccess = true;
                            }
                        }
                    }
                    else
                    {
                        sRemark = "车道未绑定主相机";
                    }
                }
                else
                {
                    sRemark = "岗亭未查询到车道信息！";
                }

                resPush.msg = sRemark;
                resPush.data = new { isSuccess };
            }
            catch (Exception ex)
            {
                resPush.msg = "车牌纠正异常";
                resPush.data = new { isSuccess = false };
                LogManagementMap.WriteToFileException(ex, $"接收系统信息处理失败：{TyziTools.Json.ToString(reqPush)}");
            }
            finally
            {
                if (context != null)
                {
                    var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                    var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                    byteBuffer1.WriteBytes(bytes1);
                    context.Channel.WriteAndFlushAsync(byteBuffer1);
                }
            }

            return Task.FromResult(resPush);
        }
    }

    #endregion

    /// <summary>
    /// 其他岗亭弹出收费窗口
    /// </summary>
    public class OutConfirmNoticeHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OutConfirmNotice";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            try
            {
                if (reqPush.data != null)
                {
                    var outConfirmNotices = JsonConvert.DeserializeObject<OutConfirmNotice>(reqPush.data);
                    if (outConfirmNotices != null && outConfirmNotices.SentryHost_No != AppBasicCache.SentryHostInfo.SentryHost_No)
                    {
                        var orderno = outConfirmNotices.OrderNo;
                        foreach (var rs in ConfirmRelease.Results)
                        {
                            var rsOrderNo = rs.Value.resorder?.resOut?.parkorder?.ParkOrder_No ?? rs.Value.passres?.parkorderno;
                            if (rsOrderNo == orderno)
                            {
                                PasswayConfirmReleaseUtil.ConfirmCancelRelease(rs.Key, rsOrderNo, "", false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"接收其他岗亭弹出收费窗口信息处理失败：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
        }
    }

    #region Web后台-抓拍图片、车道白名单处理

    /// <summary>
    /// 抓拍图片
    /// </summary>
    public class GetCameraPhotoHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GetCameraPhoto";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            ResPush resPush = null;
            try
            {
                var getCameraPhotoParam = TyziTools.Json.ToObject<List<Model.Device>>(reqPush.data);

                resPush = new ResPush
                {
                    act = reqPush.act,
                    tname = reqPush.tname,
                    guid = reqPush.guid,
                    time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    code = 1
                };

                var tempImage = CameraImageHelper.ImageSaveHSPathBig("Manual", getCameraPhotoParam?.First().Device_SentryHostNo);
                var t = await LPRTools.GetSnapShootToJpeg(getCameraPhotoParam?.First().Device_No, tempImage);
                if (t.Item1)
                {
                    try
                    {
                        //var imgPath = Path.GetFileNameWithoutExtension(tempImage);
                        if (AppSettingConfig.SentryMode == VersionEnum.CloudServer)
                        {
                            resPush.data = ImageTools.GetImgBSUrlByPath(tempImage, BLL.ImageTools.LocalFilePath);
                            //BLL.AppBasicCache.CurrentSysConfigContent.SysConfig_EnableImgPath
                            //var ms = TyziTools.ImageHelper.GetLocalImage(RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? tempImage.Replace("/", "\\") : tempImage);
                            //var d = new UploadImageData { category = 0, base64 = HttpUtility.UrlEncode(Convert.ToBase64String(ms.ToArray())), filename = string.IsNullOrEmpty(imgPath) ? Guid.NewGuid().ToString() : imgPath, sentryno = getCameraPhotoParam?.First().Device_SentryHostNo };
                            //var posturl = $"{AppSettingConfig.SiteDomain_Web.Trim('/')}/api/ParkAPI/UploadImage";
                            //var imgRes = PushResult.Send(getCameraPhotoParam?.First().Device_ParkNo, posturl, d, 5000);
                            //if (imgRes != null && imgRes.success)
                            //{
                            //	var o = imgRes.ParseData<JObject>();
                            //	if (o != null && o.ContainsKey("src"))
                            //	{
                            //		var src = HttpUtility.UrlDecode(o["src"].ToString());
                            //		resPush.data = src;
                            //	}
                            //}
                            //else
                            //{
                            //	resPush.code = 0;
                            //	resPush.msg = $"抓拍图片失败{(imgRes != null ? imgRes.errmsg : "")}";
                            //	LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"抓拍图片失败{getCameraPhotoParam?.First().Device_IP}，{(imgRes != null ? imgRes.errmsg : "")}");
                            //}
                        }
                        else
                        {
                            resPush.data = ImageTools.GetImgBSUrlByPath(tempImage, BLL.ImageTools.LocalFilePath);

                            //Model.SentryHost sentry = string.IsNullOrEmpty(getCameraPhotoParam?.First().Device_SentryHostNo) ? null : BLL.SentryHost.GetEntity(getCameraPhotoParam?.First().Device_SentryHostNo);
                            //if (sentry?.SentryHost_Type == 1)
                            //{
                            //	//BLL.AppBasicCache.CurrentSysConfigContent.SysConfig_EnableImgPath
                            //	var ms = TyziTools.ImageHelper.GetLocalImage(RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? tempImage.Replace("/", "\\") : tempImage);

                            //	var filename = string.IsNullOrEmpty(imgPath) ? Guid.NewGuid().ToString() : imgPath;
                            //	//string imgPath = AppBasicCache.strImgpath;
                            //	string base64String = HttpUtility.UrlDecode(Convert.ToBase64String(ms.ToArray()));
                            //	var aliyunPath = DataCache.Aliyun.Get(filename);
                            //	if (!string.IsNullOrEmpty(aliyunPath) && AppBasicCache.GetParking != null && AppBasicCache.IsWritePushEventMsg)
                            //	{

                            //		//上传阿里云
                            //		_ = CustomThreadPool.SyncTaskPool.QueueTask(null, async () =>
                            //		 {
                            //			 await Task.Delay(1);
                            //			 try
                            //			 {
                            //				 var bt = Convert.FromBase64String(base64String);
                            //				 if (bt != null)
                            //				 {
                            //					 if (AppBasicCache.CurrentSysConfigContent == null)
                            //					 {
                            //						 LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "上传图片失败：阿里云存储配置错误。", LogLevel.Error);
                            //						 return;
                            //					 }

                            //					 if (AppBasicCache.CurrentSysConfigContent.SysConfig_AliyunEndpoint == null || AppBasicCache.CurrentSysConfigContent.SysConfig_AliyunAccessKeyId == null || AppBasicCache.CurrentSysConfigContent.SysConfig_AliyunAccessKeySecret == null)
                            //					 {
                            //						 LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "上传图片失败：阿里云存储配置错误。", LogLevel.Error);
                            //						 return;
                            //					 }
                            //					 else
                            //					 {
                            //						 LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "开始重传阿里云图片：" + filename, LogLevel.Info);
                            //					 }

                            //					 var instance = AliFileHelper.GetIntance(AppBasicCache.CurrentSysConfigContent, false);
                            //					 var uri = instance.UploadByByte(filename, bt, DateTimeHelper.GetNowTime().AddYears(100), false);
                            //					 if (!string.IsNullOrEmpty(uri))
                            //					 {
                            //						 LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "重传阿里云图片成功：" + uri, LogLevel.Info);
                            //						 DataCache.Aliyun.Del(filename);
                            //					 }
                            //					 else
                            //					 {
                            //						 LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "重传阿里云图片失败：" + filename, LogLevel.Info);
                            //					 }
                            //				 }
                            //			 }
                            //			 catch (Exception e)
                            //			 {
                            //				 LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, $"上传图片失败：{e.Message}。", LogLevel.Error);
                            //			 }
                            //		 });
                            //	}

                            //	try
                            //	{
                            //		string imgsrc = LPRTools.OnSaveByDate(base64String, filename, tempImage, !string.IsNullOrEmpty(tempImage), sentry.SentryHost_No);
                            //		resPush.data = imgsrc;
                            //	}
                            //	catch (Exception ex)
                            //	{
                            //		resPush.code = 0;
                            //		LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"Web后台-抓拍图片,图片格式错误，请检查是否为base64格式并且使用URL编码，{ex.Message}");
                            //	}
                            //}
                            //else
                            //{
                            //	//string photoExtension = null;
                            //	//var ms = TyziTools.ImageHelper.GetLocalImage(RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? tempImage.Replace("/", "\\") : tempImage);
                            //	//string base64String = HttpUtility.UrlDecode(Convert.ToBase64String(ms.ToArray()));
                            //	//var filename = string.IsNullOrEmpty(imgPath) ? Guid.NewGuid().ToString() : imgPath;

                            //	//if (base64String.Contains("base64,")) //isWindows && base64string.Contains("base64,")
                            //	//{
                            //	//    photoExtension = base64String.Substring(0, base64String.IndexOf(";")).Substring(base64String.IndexOf("/") + 1);
                            //	//    base64String = base64String.Substring(base64String.IndexOf("base64,") + 7);
                            //	//}

                            //	//if (string.IsNullOrEmpty(filename))
                            //	//{
                            //	//    filename = $"{Utils.CreateNumber}.{photoExtension ?? "jpg"}";
                            //	//}
                            //	//else
                            //	//{
                            //	//    if (string.IsNullOrEmpty(Path.GetExtension(filename)))
                            //	//    {
                            //	//        filename = filename + "." + (photoExtension ?? "jpg");
                            //	//    }
                            //	//}

                            //	//string imgsrc = LPRTools.OnSaveByDate(base64String, filename, imgPath, !string.IsNullOrEmpty(imgPath), sentry?.SentryHost_No);
                            //	resPush.data = ImageTools.GetImgBSUrlByPath(tempImage, BLL.ImageTools.LocalFilePath);
                            //}

                        }
                    }
                    catch (Exception ex)
                    {
                        resPush.code = 0;
                        resPush.msg = $"抓拍图片失败,{ex.Message}";
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"抓拍图片失败[GetCameraPhoto]:{getCameraPhotoParam?.First().Device_IP}，{ex}");
                    }
                }
                else
                {
                    resPush.code = 0;
                    resPush.msg = "抓拍图片失败.";
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{getCameraPhotoParam?.First().Device_IP}抓拍图片失败.");
                }

                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"GetCameraPhoto:{getCameraPhotoParam?.First().Device_IP},{tempImage}");
                if (context != null) SendTCPDatas(context, resPush);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"抓拍图片处理异常：{TyziTools.Json.ToString(reqPush)},{ex.Message}");
                if (resPush == null) resPush = new ResPush();

                resPush.code = 0;
                resPush.msg = "";
                resPush.data = ex.Message;
                if (context != null) SendTCPDatas(context, resPush);
            }

            return resPush;
        }
    }

    /// <summary>
    /// 清空白名单
    /// </summary>
    public class ClearWhiteHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "ClearWhite";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            ResPush resPush = null;
            try
            {
                resPush = new ResPush
                {
                    act = reqPush.act,
                    tname = reqPush.tname,
                    guid = reqPush.guid,
                    time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    code = 1
                };

                var datas = TyziTools.Json.ToObject<List<DeviceExt>>(reqPush.data);
                if (datas == null || datas.Count == 0)
                {
                    resPush.code = 0;
                    resPush.msg = "参数异常";
                    SendTCPDatas(context, resPush);
                    return resPush;
                }

                var SyncWhiteDevice = datas[0];
                var isSuccess = false;
                if (SyncWhiteDevice.Device_Category == 9)
                {
                    AppBasicCache.GetAllPassway.TryGetValue(SyncWhiteDevice.Device_PasswayNo, out var passway);
                    var rlt = await ControllerHelper.DeleteWhiteListByMotoAsync(SyncWhiteDevice.Device_PasswayNo, passway?.Passway_SameInOut ?? 0, passway?.Passway_OutNo ?? "", "");
                    isSuccess = rlt.isSuccess;
                }
                else
                {
                    isSuccess = await CameraController.RemoveWhitelistAsync(SyncWhiteDevice.Device_IP, "");
                }
                if (isSuccess)
                {
                    ParkApi.UpdateWhiteStatus(SyncWhiteDevice.Device_No, "", "5");
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"清空设备白名单成功{SyncWhiteDevice.Device_IP}");
                }
                else
                {
                    ParkApi.UpdateWhiteStatus(SyncWhiteDevice.Device_No, "", "6");
                    resPush.code = 0;
                    resPush.msg = "清空设备白名单失败";
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"清空设备白名单失败{SyncWhiteDevice.Device_IP}");
                }

                SendTCPDatas(context, resPush);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"清空设备白名单异常：{TyziTools.Json.ToString(reqPush)},{ex.Message}");
                if (resPush == null) resPush = new ResPush();

                resPush.code = 0;
                resPush.msg = "";
                resPush.data = ex.Message;
                SendTCPDatas(context, resPush);
            }
            return resPush;
        }
    }

    /// <summary>
    /// 同步白名单
    /// </summary>
    public class SyncWhiteHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "SyncWhite";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            ResPush resPush = null;
            var result2 = "success";
            try
            {
                resPush = new ResPush
                {
                    act = reqPush.act,
                    tname = reqPush.tname,
                    guid = reqPush.guid,
                    time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    code = 1
                };

                var datas = TyziTools.Json.ToObject<List<(Model.Device, bool)>>(reqPush.data);
                if (datas == null || datas.Count == 0)
                {
                    resPush.code = 0;
                    resPush.msg = "参数异常";
                    SendTCPDatas(context, resPush);
                    return Task.FromResult(resPush);
                }

                var item = datas[0];

                DeviceExt SyncWhiteDevice = null;
                var isAllLoad = true;

                if (item.Item1 != null) SyncWhiteDevice = TyziTools.Json.ToObject<DeviceExt>(TyziTools.Json.ToString(item.Item1));
                isAllLoad = item.Item2;

                var cacheCount = DataCache.SyncAction.Get(SyncWhiteDevice.Device_No);
                if (!string.IsNullOrEmpty(cacheCount))
                {
                    result2 = $"正在执行同步名单:{cacheCount}";
                    resPush.code = 1;
                    SendTCPDatas(context, resPush);
                    return Task.FromResult(resPush);
                }

                var lsCars = BLL.Car.GetCarExtAllEntity("*", "1=1");
                if (lsCars.Count > 0)
                {
                    DataCache.SyncAction.Set(SyncWhiteDevice.Device_No, $"{lsCars.Count},0,0,0");
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                    {
                        var runCount = 0;
                        var success = 0;
                        var fail = 0;
                        var lsDeviceIds = new List<string>();

                        foreach (var car in lsCars)
                        {
                            runCount++;
                            try
                            {
                                HandleCommon.SyncCarWhite(new List<string> { car.Car_CarNo }, "", ref success, ref fail, new List<Model.Device>() { SyncWhiteDevice });
                            }
                            catch (Exception e)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"同步白名单【{car.Car_CarNo}】到设备【{SyncWhiteDevice.Device_Name}】【{SyncWhiteDevice.Device_IP}】异常：{e}");
                            }
                            finally
                            {
                                DataCache.SyncAction.Set(SyncWhiteDevice.Device_No, $"{lsCars.Count},{runCount},{success},{fail}");
                                await Task.Delay(200);
                                if (runCount == lsCars.Count)
                                {
                                    //await Task.Delay(2000);
                                    //DataCache.SyncAction.Del(SyncWhiteDevice.Device_No);
                                }
                            }

                            Thread.Sleep(50);
                        }
                    });
                }

                SendTCPDatas(context, resPush);
                return Task.FromResult(resPush);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"同步白名单处理异常：{TyziTools.Json.ToString(reqPush)},{ex.Message}");
                if (resPush == null) resPush = new ResPush();

                resPush.code = 0;
                resPush.msg = "";
                resPush.data = ex.Message;
                SendTCPDatas(context, resPush);
                return Task.FromResult(resPush);
            }
        }
    }

    #endregion

    /// <summary>
    /// 接收到MQ下发的开闸信号
    /// </summary>
    public class MQOpenGateHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "mqoepngate";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private async Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            ResPush resPush = null;
            try
            {
                resPush = new ResPush
                {
                    act = reqPush.act,
                    tname = reqPush.tname,
                    guid = reqPush.guid,
                    time = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    code = 1
                };

                var data = TyziTools.Json.ToObject<JObject>(reqPush.data);
                if (data == null)
                {
                    resPush.code = 0;
                    resPush.msg = "参数异常";
                    SendTCPDatas(context, resPush);
                    return resPush;
                }

                var deviceno = data["deviceno"].ToString();
                var carno = data["carno"].ToString();

                var deviceModel = AppBasicCache.GetElement(AppBasicCache.GetAllDeivces, deviceno);
                if (deviceModel == null)
                {
                    resPush.code = 0;
                    resPush.msg = "未找到设备";
                    SendTCPDatas(context, resPush);
                    return resPush;
                }

                if (ConfirmRelease.Results.TryGetValue(deviceModel.Device_PasswayNo, out var d)) //有弹窗缓存
                {
                    if (d.passres?.carno == carno)
                    {
                        var gate = Passway.GetPasswayGateType(deviceModel.Device_PasswayNo);
                        if (gate == 1 || gate == 2) //入口
                        {
                            var bl = await PasswayConfirmReleaseUtil.ConfirmEnter(d, "",
                                new Admins { Admins_Account = "admin", Admins_Name = "admin" }, saveCache: false);
                        }
                        else
                        {
                            var bl = await PasswayConfirmReleaseUtil.ConfirmPaymentCompletedRelease(d, deviceModel, d.payres?.payedamount ?? 0, null,
                                new Admins { Admins_Account = "admin", Admins_Name = "admin" }, saveCache: false, openGate: false);
                            PasswayConfirmReleaseUtil.RemoveResult(deviceModel.Device_PasswayNo);
                        }
                    }
                }

                SendTCPDatas(context, resPush);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"接收到MQ下发的开闸信号异常：{TyziTools.Json.ToString(reqPush)},{ex.Message}");
                if (resPush == null) resPush = new ResPush();

                resPush.code = 0;
                resPush.msg = "";
                resPush.data = ex.Message;
                SendTCPDatas(context, resPush);
            }

            return resPush;
        }
    }

    /// <summary>
    /// 计费规则缓存
    /// </summary>
    public class MQChargeRulesHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "mqchargerules";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            try
            {
                if (string.IsNullOrEmpty(reqPush.data)) return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
                var noes = reqPush.data.Split(',').ToList();

                if (reqPush.act.ToLower() == "del")
                {
                    noes.ForEach(item =>
                    {
                        AppBasicCache.DeleteElement(AppBasicCache.GetAllChargeRules, item, new ChargeRules { ChargeRules_No = item });
                        AppBasicCache.DeleteElement(AppBasicCache.GetAllChargeRelation, item, new List<ChargeRelation>());
                    });
                }
                else if (reqPush.act.ToLower() == "update")
                {
                    var rules = BaseBLL._GetAllEntity(new ChargeRules(), "*", $"ChargeRules_No in ('{string.Join("','", noes)}')");
                    var relations = BaseBLL._GetAllEntity(new ChargeRelation(), "*", $"ChargeRelation_ChargeRulesNo  in ('{string.Join("','", noes)}')");

                    rules.ForEach(item =>
                    {
                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllChargeRules, item.ChargeRules_No, item);
                        AppBasicCache.DeleteElement(AppBasicCache.GetAllChargeRelation, item.ChargeRules_No, new List<ChargeRelation>());
                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetAllChargeRelation, item.ChargeRules_No, relations.Where(x => x.ChargeRelation_ChargeRulesNo == item.ChargeRules_No).ToList());
                    });
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"接收到MQ下发的计费规则缓存异常：{TyziTools.Json.ToString(reqPush)},{ex.Message}");
            }
            return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
        }
    }

    /// <summary>
    /// 优惠券下发
    /// </summary>
    public class AddCarCouponHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "addcarcoupon";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }


        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            try
            {
                var datas = TyziTools.Json.ToObject<List<(List<CouponRecord>, List<ParkOrder>)>>(reqPush.data);
                foreach (var d in datas)
                {
                    if (d.Item1 != null && d.Item2 != null)
                    {
                        foreach (var order in d.Item2)
                        {
                            var orderNo = order.ParkOrder_No;
                            CommHelper.CheckConfirmResultForCarNo("", orderNo, CloseNoInPark: false);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"优惠券下发信息处理异常：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
        }
    }

    /// <summary>
    /// 优惠券
    /// </summary>
    public class CouponRecordHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "couponrecord";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }


        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            try
            {
                var act = reqPush.act?.ToLower() ?? "";
                switch (act)
                {
                    case "delete":
                    case "add":
                    case "edit":
                        var datas = TyziTools.Json.ToObject<List<CouponRecord>>(reqPush.data);
                        foreach (var d in datas)
                        {
                            if (d != null && !string.IsNullOrEmpty(d.CouponRecord_ParkOrderNo))
                            {
                                var orderNo = d.CouponRecord_ParkOrderNo;
                                foreach (var no in ConfirmRelease.Results.Keys)
                                {
                                    //场内缴费的记录推送下来只要停车订单在出口车道就默认缴费放行
                                    ConfirmRelease.Results.TryGetValue(no, out var data);

                                    if (data != null)
                                    {
                                        var parkOrder = data.resorder?.resOut?.parkorder;
                                        //弹窗(出入场)的订单号
                                        var rsOrderNo = data.resorder?.resOut?.parkorder?.ParkOrder_No ?? data.passres?.parkorderno;

                                        //判断订单号是否一致，一致则发送刷新计费命令
                                        if (rsOrderNo == orderNo && parkOrder != null)
                                        {
                                            //WebSocketUtil.SendWSAllLink("updatedata", reqPush?.tname.ToLower() ?? InstructionName);

                                            if (data.passres != null)
                                            {
                                                //获取计费结果
                                                data.payres = null;
                                                var passway = AppBasicCache.GetElement(AppBasicCache.GetAllPassway, no);

                                                if (passway?.Passway_IsCharge == 1)
                                                {
                                                    if (parkOrder.ParkOrder_IsNoInRecord == 1)
                                                    {
                                                        data.payres = PassHelperBiz.FeeNoRecord2(parkOrder.ParkOrder_ParkNo, parkOrder.ParkOrder_CarNo, DateTimeHelper.GetNowTime(), out var CouponNos, no, result: data);
                                                    }
                                                    else
                                                    {
                                                        data.payres = Calc.GetChargeByCar(parkOrder, DateTimeHelper.GetNowTime(), data.passres.car, null, true, "", "", data.passres.owner);
                                                    }
                                                }
                                                else
                                                {
                                                    var payResult = new PayResult();
                                                    payResult.payed = 0;
                                                    payResult.payedmsg = "";
                                                    payResult.payedamount = 0;
                                                    data.payres = payResult;
                                                }
                                            }

                                            WebSocketUtil.SendWS(data);
                                        }
                                    }
                                }
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"优惠券信息处理异常：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
        }
    }


    /// <summary>
    /// 测试Frp
    /// </summary>
    public class TestFrpHandle : HandleBase
    {
        /// <summary>
        /// FRP 过期时间
        /// </summary>
        private DateTime _frpcOverTime;

        /// <summary>
        /// FRP 过期时间任务
        /// </summary>
        private Task _frpcOverTimeTask;

        /// <summary>
        /// frpc进程
        /// </summary>
        private static Process processFrpc { set; get; }

        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "testfrp";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            var isSuccess = false;
            var duration = 10; //分钟

            try
            {
                var data = TyziTools.Json.ToObject<List<JObject>>(reqPush.data);
                if (data == null)
                {
                    return;
                }

                var ServerAddress = data[0]["serveraddr"].ToString();
                var ServerPort = data[0]["serverport"].ToString();
                var ServerToken = data[0]["servertoken"].ToString();
                var parkkey = AppBasicCache.GetParking?.Parking_Key;

                processFrpc?.CancelErrorRead();
                processFrpc?.CancelOutputRead();
                processFrpc?.Kill();
                processFrpc?.Dispose();
                processFrpc = null;

                _frpcOverTime = DateTime.Now;

                #region 生成Frp配置文件

                const string commonTmpl = "[common]\r\nserver_addr={0}\r\nserver_port={1}\r\nauthentication_method={2}\r\ntoken={3}\r\n";
                const string hostTmpl = "[{0}]\r\ntype={1}\r\nlocal_ip={2}\r\nlocal_port={3}\r\nsubdomain={4}\r\n";

                var iniBuilder = new StringBuilder();
                iniBuilder.AppendFormat(commonTmpl, ServerAddress, ServerPort, "token", ServerToken);

                var mqttConfig = MqttClient.Instance.GetConfig;
                iniBuilder.AppendFormat(hostTmpl, parkkey, "http", "127.0.0.1", AppSettingConfig.SiteDomain_WebPort, parkkey);

                var sentryHosts = SentryHost.GetAllEntity("SentryHost_No,SentryHost_IP,SentryHost_BSPort,SentryHost_WSPort,SentryHost_Category", "");
                foreach (var sentry in sentryHosts)
                {
                    if (sentry.SentryHost_Category == 2)
                    {
                        var bsHost = $"{sentry.SentryHost_No}{mqttConfig.DeviceSN}";
                        iniBuilder.AppendFormat(hostTmpl, bsHost, "http", sentry.SentryHost_IP, sentry.SentryHost_BSPort?.ToString(), bsHost);

                        var wsHost = $"ws{bsHost}";
                        iniBuilder.AppendFormat(hostTmpl, wsHost, "http", sentry.SentryHost_IP, sentry.SentryHost_WSPort?.ToString(), wsHost);
                    }

                    var cameras15 = CameraDeviceType.driveNameList15;
                    var devices = BLL.Device.GetAllEntityExt("*", $"Device_SentryHostNo='{sentry.SentryHost_No}'");
                    foreach (var dv in devices)
                    {
                        if (!cameras15.Contains(dv.Drive_Name)) continue;
                        var dvHost = $"{dv.Device_No}";
                        iniBuilder.AppendFormat(hostTmpl, dvHost, "http", dv.Device_IP, "80", dvHost);

                        var dvFlvHost = $"{dv.Device_No}flv";
                        iniBuilder.AppendFormat(hostTmpl, dvFlvHost, "http", dv.Device_IP, "9080", dvFlvHost);
                    }
                }

                var ini = iniBuilder.ToString();

                #endregion

                //Frp文件目录
                var basePath = "";
                //Frp Exe文件路径
                var exePath = "";
                //Frp配置文件路径
                var iniPath = "";

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "Sdk", "Frpc");
                    iniPath = Path.Combine(basePath, "frpc.ini");
                    exePath = Path.Combine(basePath, "frpc.exe");
                    File.WriteAllText(iniPath, ini);
                }
                else
                {
                    basePath = "/mnt/app2/b30/Sdk/Frpc";
                    iniPath = Path.Combine(basePath, "frpc.ini");
                    exePath = Path.Combine(basePath, "frpc");

                    var newBasePath = "/mnt/sda1/b30/Config/Frpc";
                    var newExePath = Path.Combine(newBasePath, "frpc");
                    var newIniPath = Path.Combine(newBasePath, "frpc.ini");

                    LocalFile.CreateDirectory(newBasePath);

                    if (!File.Exists(newExePath))
                    {
                        File.Copy(exePath, newExePath, true);
                    }

                    if (!File.Exists(newIniPath))
                    {
                        File.Copy(iniPath, newIniPath, true);
                    }

                    File.WriteAllText(newIniPath, ini);

                    #region frpc增加执行权限

                    //创建一个ProcessStartInfo对象 使用系统shell 指定命令和参数 设置标准输出
                    var psi = new ProcessStartInfo("chmod", $"+x {newExePath}") { RedirectStandardOutput = false };
                    //启动
                    Process.Start(psi);

                    #endregion

                    iniPath = newIniPath;
                    exePath = newExePath;
                }


                MqttLog.WriteLog(iniPath);
                MqttLog.WriteLog(exePath);

                processFrpc = new Process();
                processFrpc.StartInfo.UseShellExecute = false; //是否使用操作系统shell启动
                processFrpc.StartInfo.CreateNoWindow = true; //是否在新窗口中启动该进程的值 (不显示程序窗口)
                processFrpc.StartInfo.RedirectStandardOutput = true; // 由调用程序获取输出信息
                processFrpc.StartInfo.RedirectStandardError = true; //重定向标准错误输出
                processFrpc.StartInfo.FileName = exePath;
                processFrpc.StartInfo.Arguments = $"-c {iniPath}";
                processFrpc.StartInfo.Verb = "runas";
                processFrpc.OutputDataReceived += (_, e) =>
                {
                    try
                    {
                        //if (string.IsNullOrWhiteSpace(e.Data)) return;
                        MqttLog.WriteLog($"开启frpc服务输出控制台日志：{e.Data}");
                        if (e.Data.Contains("start proxy success"))
                        {
                            isSuccess = true;
                        }
                        else if (e.Data.Contains("router config conflict"))
                        {
                            isSuccess = false;
                        }
                    }
                    catch
                    {
                        //异常不需要处理
                    }
                };
                processFrpc.ErrorDataReceived += (_, e) =>
                {
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(e.Data))
                        {
                            MqttLog.WriteLog($"开启frpc服务输出控制台日志：{e.Data}");
                        }
                    }
                    catch
                    {
                        //异常不需要处理
                    }
                };
                processFrpc.Start();
                MqttLog.WriteLog($"开启frpc服务进程ID：{processFrpc.Id}");
                processFrpc.BeginOutputReadLine();
                processFrpc.BeginErrorReadLine();
                var timeout = 8000;
                var startTime = DateTime.Now;
                while (!isSuccess && (DateTime.Now - startTime).TotalMilliseconds < timeout)
                {
                    Thread.Sleep(100);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"测试Frp异常：{TyziTools.Json.ToString(reqPush)},{ex.Message}");
            }
            finally
            {
                if (!isSuccess)
                {
                    processFrpc?.CancelErrorRead();
                    processFrpc?.CancelOutputRead();
                    processFrpc?.Kill();
                    processFrpc?.Dispose();
                    processFrpc = null;
                }

                //启动frpc服务成功后，向云端发送frpc服务启动成功消息
                if ((processFrpc != null && _frpcOverTimeTask == null) || _frpcOverTimeTask.IsCompleted)
                {
                    _frpcOverTimeTask = Task.Run(() =>
                    {
                        try
                        {
                            while (DateTime.Now <= _frpcOverTime.AddMinutes(duration))
                            {
                                Thread.Sleep(500);
                            }

                            processFrpc?.CancelErrorRead();
                            processFrpc?.CancelOutputRead();
                            processFrpc?.Kill();
                            processFrpc?.Dispose();
                            processFrpc = null;
                        }
                        catch
                        {
                            //超时关闭任务异常不做处理，只是为了不影响主任务
                        }
                    });
                }
            }
        }
    }

    /// <summary>
    /// 同步进出口模式及小数点位数
    /// </summary>
    public class SyncInOutModeAndDecimalPointHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "SyncInOut".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            ResPush resPush = null;
            try
            {
                resPush = new ResPush
                {
                    act = reqPush.act,
                    tname = reqPush.tname,
                    guid = reqPush.guid,
                    time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    code = 1,
                    msg = "同步成功"
                };

                var temp = TyziTools.Json.ToObject<List<string>>(reqPush.data);
                if (temp == null)
                {
                    resPush.code = 0;
                    resPush.msg = "未找到车道信息";
                }
                else
                {
                    var passways = AppBasicCache.GetAllPassway.Values.Where(m => temp.Contains(m.Passway_No) && AppBasicCache.SentryHostInfo.SentryHost_No == m.Passway_SentryHostNo).ToList();
                    if (passways.Count > 0)
                    {
                        foreach (var p in passways)
                        {
                            var m = PolicyPassway.GetMainEntity($" and dv.Device_PasswayNo ='{p.Passway_No}' ");
                            if (m != null) CameraUtil.LoadDeviceVoice(m, true);
                        }
                    }
                    else
                    {
                        resPush.code = 0;
                        resPush.msg = "未找到车道信息";
                    }
                }

                var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                byteBuffer1.WriteBytes(bytes1);
                context?.Channel.WriteAndFlushAsync(byteBuffer1);
            }
            catch (Exception ex)
            {
                if (resPush != null)
                {
                    resPush.code = 0;
                    resPush.msg = "同步失败";
                    var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                    var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                    byteBuffer1.WriteBytes(bytes1);
                    context?.Channel.WriteAndFlushAsync(byteBuffer1);
                }

                LogManagementMap.WriteToFileException(ex, $"同步进出口模式及小数点位数处理失败：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(new ResPush() { guid = reqPush.guid, code = 1 });
        }
    }

    /// <summary>
    /// 同步车牌特殊处理规则
    /// </summary>
    public class SpecialPlateHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "SpecialPlate".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context);
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <returns>返回处理结果</returns>
        private ResPush RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            AppBasicCache.GetSpecialPlateDic.Clear();
            //重新查询所有特殊处理规则
            var specialPlate = BaseBLL._GetAllEntity(new Model.SpecialPlate(), "*");
            specialPlate.ForEach(item =>
            {
                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetSpecialPlateDic, item.SpecialPlate_No, item);
            });
            return null;
        }
    }

    /// <summary>
    /// 同步屏显模板信息
    /// </summary>
    public class DisplayTemplateHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "DisplayTemplate".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            try
            {
                //重新查询所有屏显模板信息
                var displayTemplates = BaseBLL._GetAllEntity(new Model.DisplayTemplates.DisplayTemplate(), "*", "DisplayTemplate_Status=1");
                displayTemplates.ForEach(item =>
                {
                    AppBasicCache.GetDisplayTemplateDic.AddOrUpdate(item.DisplayTemplate_No, item, (key, oldItem) => item);
                });

                //遍历缓存，删除不存在的屏显模板
                foreach (var item in AppBasicCache.GetDisplayTemplateDic.Values)
                {
                    //不存在则删除
                    if (!displayTemplates.Any(x => x.DisplayTemplate_No == item.DisplayTemplate_No))
                    {
                        AppBasicCache.GetDisplayTemplateDic.TryRemove(item.DisplayTemplate_No, out _);
                    }
                }

                //重新查询所有屏显节目区域
                var displayAreas = BaseBLL._GetAllEntity(new Model.DisplayTemplates.ProgramArea(), "*", "ProgramArea_Status=1");
                displayAreas.ForEach(item =>
                {
                    AppBasicCache.GetProgramAreaDic.AddOrUpdate(item.ProgramArea_No, item, (key, oldItem) => item);
                });

                //遍历缓存，删除不存在的屏显节目区域
                foreach (var item in AppBasicCache.GetProgramAreaDic.Values)
                {
                    //不存在则删除
                    if (!displayAreas.Any(x => x.ProgramArea_No == item.ProgramArea_No))
                    {
                        AppBasicCache.GetProgramAreaDic.TryRemove(item.ProgramArea_No, out _);
                    }
                }

                //重新查询所有屏显节目动作
                var displayActions = BaseBLL._GetAllEntity(new Model.DisplayTemplates.ProgramAreaAction(), "*", "ProgramAreaAction_Status=1");
                displayActions.ForEach(item =>
                {
                    AppBasicCache.GetProgramAreaAction.AddOrUpdate(item.ProgramAreaAction_No, item, (key, oldItem) => item);
                });

                //遍历缓存，删除不存在的屏显节目动作
                foreach (var item in AppBasicCache.GetProgramAreaAction.Values)
                {
                    //不存在则删除
                    if (!displayActions.Any(x => x.ProgramAreaAction_No == item.ProgramAreaAction_No))
                    {
                        AppBasicCache.GetProgramAreaAction.TryRemove(item.ProgramAreaAction_No, out _);
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"同步屏显模板信息失败");
            }
        }
    }

    /// <summary>
    /// 测试车辆进出场
    /// </summary>
    public class InOutTestHandle : HandleBase
    {
        /// <summary>
        /// 指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "InOutCar".ToLower();

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override void Execute(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            RunAction(reqPush, context).Wait();
        }

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="reqPush">上下文接收到的数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        public override async Task<ResPush> ExecuteRet(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            return await RunAction(reqPush, context);
        }

        private Task<ResPush> RunAction(ReqPush reqPush, IChannelHandlerContext context = null)
        {
            ResPush resPush = null;
            try
            {
                resPush = new ResPush
                {
                    act = reqPush.act,
                    tname = reqPush.tname,
                    guid = reqPush.guid,
                    time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    code = 1,
                    msg = "操作成功"
                };

                var jo = TyziTools.Json.ToObject<JObject>(reqPush.data);
                if (jo == null)
                {
                    resPush.code = 0;
                    resPush.msg = "参数错误";
                    SendTCPDatas(context, resPush);
                    return Task.FromResult(resPush);
                }

                var carno = jo["carno"].ToString();
                var passwayno = jo["passwayno"].ToString();
                var time = jo["time"].ToString();

                Model.Device device = AppBasicCache.GetAllDeivces.Values.Where(x => x.Device_PasswayNo == passwayno && x.Device_Category == 1 && x.Device_IO == 1).ToList().FirstOrDefault();
                if (device == null)
                {
                    resPush.code = 0;
                    resPush.msg = "未设置车道主相机";
                    SendTCPDatas(context, resPush);
                    return Task.FromResult(resPush);
                }

                var tempImage = CameraImageHelper.ImageSaveHSPathBig(carno, device.Device_SentryHostNo);
                //await CameraController.GetSnapshotAsync(device.Device_IP, tempImage, device.Device_SentryHostNo, carno);
                var base64 = string.Empty;
                var errmsg = string.Empty;
                _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                 {
                     var ffaa = await LPRTools.GetSnapShootToJpeg(passwayno, tempImage, base64, errmsg);
                 });

                Thread.Sleep(100);

                var m = new CarPlateInfo
                {
                    CarPlate = carno,
                    TriggerTime = string.IsNullOrEmpty(time) ? DateTimeHelper.GetNowTime() : DateTime.Parse(time),
                    BigJPGPath = tempImage,
                    CarmeraIP = device?.Device_IP,
                    CarTypeMode = "蓝牌车",
                    DeviceNo = device?.Device_No,
                    IsRealLicensePlate = true,
                    TriggerMode = 0,
                    PasswayNo = passwayno,
                    CarTypeNameLogo = "",
                    CarPlateColor = 1,
                    MixModel = "蓝牌车",
                    IsUnlicensedCar = false,
                    Mode = 1,
                    UseDeviceTime = true,
                    IsSupplement = AppSettingConfig.SentryMode == VersionEnum.CloudServer && MqttClient.Instance.IsOnline && (AppBasicCache.CurrentSysConfigContent?.SysConfig_CloudBoxLprEmergency ?? false ? false : true) ? false : true,
                    CarType = 1
                };

                if (DeviceServerHandle.LicensePlateQueue.TryGetValue(passwayno, out var temp))
                {
                    temp.Enqueue(m);
                }
                else
                {
                    var queue = new ConcurrentQueue<CarPlateInfo>();
                    queue.Enqueue(m);
                    DeviceServerHandle.LicensePlateQueue.TryAdd(passwayno, queue);
                }

                resPush.data = "1";

                if (context != null)
                {
                    var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                    var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                    byteBuffer1.WriteBytes(bytes1);
                    context?.Channel.WriteAndFlushAsync(byteBuffer1);
                }
            }
            catch (Exception ex)
            {
                if (resPush != null)
                {
                    resPush.code = 0;
                    resPush.msg = "同步失败";
                    resPush.data = "0";
                    if (context != null)
                    {
                        var bytes1 = Encoding.UTF8.GetBytes(TyziTools.Json.ToString(resPush));
                        var byteBuffer1 = Unpooled.Buffer(bytes1.Length);
                        byteBuffer1.WriteBytes(bytes1);
                        context?.Channel.WriteAndFlushAsync(byteBuffer1);
                    }
                }

                LogManagementMap.WriteToFileException(ex, $"测试车辆进出场失败：{TyziTools.Json.ToString(reqPush)}");
            }

            return Task.FromResult(resPush);
        }
    }
}