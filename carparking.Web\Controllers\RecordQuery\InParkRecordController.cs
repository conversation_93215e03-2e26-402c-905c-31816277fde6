﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using carparking.BLL;
using carparking.Common;
using carparking.Charge;
using System.Data;
using carparking.Model;
using System.IO;
using carparking.BLL.Cache;
using carparking.Config;
using carparking.Model.API;
using carparking.SentryBox;
using FastDeepCloner;

namespace carparking.Web.Controllers
{
    public class InParkRecordController : BaseController
    {
        private readonly Microsoft.Extensions.Hosting.IHostEnvironment _hostingEnvironment;
        public InParkRecordController(Microsoft.Extensions.Hosting.IHostEnvironment hostingEnvironment)
        {
            _hostingEnvironment = hostingEnvironment;
        }


        public ActionResult Index()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.View.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            ViewBag.ParkKey = AppBasicCache.GetParking?.Parking_Key ?? "";

            return View();
        }

        public IActionResult Edit()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Update.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult Payment()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Payment.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult BathDelete()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.DelBind.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult PaymentDetail()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Payment.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        /// <summary>
        /// 查询停车记录
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public IActionResult GetParkOrderList(int pageIndex, int pageSize, string conditionParam, string field, string order)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Search.ToString(), false, true, lgAdmins))
                { return Ok(oModel); }

                if (pageSize > 1000)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);

                StringBuilder sqlwhere = new StringBuilder();


                if (model.ParkOrder_StatusNo != null)
                {
                    if (model.ParkOrder_StatusNo != 0)
                        sqlwhere.Append($" and ParkOrder_StatusNo = '{model.ParkOrder_StatusNo}' ");
                    else
                        sqlwhere.Append($" and ParkOrder_StatusNo='{Model.EnumParkOrderStatus.In}' and ParkOrder_OutType=1 ");
                }

                if (model.ParkOrder_Lock != null)
                    sqlwhere.Append($" and ParkOrder_Lock = @ParkOrder_Lock ");
                if (model.ParkOrder_OutType != null)
                    sqlwhere.Append($" and ParkOrder_OutType = @ParkOrder_OutType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarCardType))
                    sqlwhere.Append($" and ParkOrder_CarCardType = @ParkOrder_CarCardType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarType))
                    sqlwhere.Append($" and ParkOrder_CarType = @ParkOrder_CarType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                    sqlwhere.Append($" and ParkOrder_EnterPasswayNo = @ParkOrder_EnterPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                    sqlwhere.Append($" and ParkOrder_OutPasswayNo = @ParkOrder_OutPasswayNo ");

                if (!string.IsNullOrEmpty(model.ParkOrder_EnterAdminAccount))
                    sqlwhere.Append($" and ParkOrder_EnterAdminAccount = @ParkOrder_EnterAdminAccount ");

                if (!string.IsNullOrEmpty(model.ParkOrder_OutAdminAccount))
                    sqlwhere.Append($" and ParkOrder_OutAdminAccount = @ParkOrder_OutAdminAccount ");
                if (!string.IsNullOrEmpty(model.ParkOrder_ParkAreaNo))
                    sqlwhere.Append($" and ParkOrder_ParkAreaNo = @ParkOrder_ParkAreaNo ");
                if (model.ParkOrder_IsFree != null)
                    sqlwhere.Append($" and ParkOrder_IsFree = @ParkOrder_IsFree ");
                if (model.ParkOrder_IsNoInRecord != null)
                    sqlwhere.Append($" and ParkOrder_IsNoInRecord = @ParkOrder_IsNoInRecord ");
                if (model.ParkOrder_IsEpCar != null)
                    sqlwhere.Append($" and ParkOrder_IsEpCar = @ParkOrder_IsEpCar ");

                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 AND  ParkOrder_EnterTime < @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime < @ParkOrder_EnterTime1 ");

                if (model.ParkOrder_OutTime0 != null && model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 AND ParkOrder_OutTime < @ParkOrder_OutTime1 ");
                else if (model.ParkOrder_OutTime0 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 ");
                else if (model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime < @ParkOrder_OutTime1 ");


                if (!string.IsNullOrEmpty(model.ParkOrder_No))
                    sqlwhere.Append($" and ParkOrder_No like @ParkOrder_No ");

                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");
                else if (!string.IsNullOrEmpty(model.ParkOrder_CarNo1))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo1 ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterAdminName))
                    sqlwhere.Append($" and ParkOrder_EnterAdminName like @ParkOrder_EnterAdminName ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarLogo))
                    sqlwhere.Append($" and ParkOrder_CarLogo like @ParkOrder_CarLogo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_FreeReason))
                    sqlwhere.Append($" and ParkOrder_FreeReason like @ParkOrder_FreeReason ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutAdminName))
                    sqlwhere.Append($" and ParkOrder_OutAdminName like @ParkOrder_OutAdminName ");

                bool isSearchOwner = false;
                var ownerNoes = new List<string>();
                if (!string.IsNullOrEmpty(model.ParkOrder_OwnerName))
                {
                    var owners = BLL.Owner.GetAllEntity("Owner_No", $"Owner_Name like @ParkOrder_OwnerName ", new { ParkOrder_OwnerName = "%" + model.ParkOrder_OwnerName.Trim() + "%" });
                    if (owners != null && owners.Count > 0)
                    {
                        isSearchOwner = true;
                        ownerNoes = owners.Select(x => x.Owner_No).ToList();
                    }
                    else
                    {
                        sqlwhere.Append($" and ParkOrder_OwnerName like @ParkOrder_OwnerName ");
                    }
                }
                if (!string.IsNullOrEmpty(model.ParkOrder_OwnerSpace))
                {
                    isSearchOwner = true;
                    var owners = BLL.Owner.GetAllEntity("Owner_No", $"Owner_Space like @ParkOrder_OwnerSpace ", new { ParkOrder_OwnerSpace = "%" + model.ParkOrder_OwnerSpace.Trim() + "%" });
                    if (owners != null && owners.Count > 0)
                    {
                        ownerNoes.AddRange(owners.Select(x => x.Owner_No).ToList());
                    }
                }
                if (ownerNoes.Count > 0)
                {
                    ownerNoes = ownerNoes.Distinct().ToList();
                    sqlwhere.Append($" and ParkOrder_OwnerNo in @ParkOrder_OwnerNoList ");
                }
                else
                {
                    if (isSearchOwner)
                    {
                        oModel.code = 0;
                        oModel.data = new List<Model.ParkOrder>();
                        oModel.count = 0;
                        return Ok(oModel);
                    }
                }

                object parameters = new
                {
                    ParkOrder_Lock = model.ParkOrder_Lock,
                    ParkOrder_OutType = model.ParkOrder_OutType,
                    ParkOrder_CarCardType = model.ParkOrder_CarCardType,
                    ParkOrder_CarType = model.ParkOrder_CarType,
                    ParkOrder_EnterPasswayNo = model.ParkOrder_EnterPasswayNo,
                    ParkOrder_OutPasswayNo = model.ParkOrder_OutPasswayNo,
                    ParkOrder_EnterAdminAccount = model.ParkOrder_EnterAdminAccount != null ? model.ParkOrder_EnterAdminAccount.Trim() : null,
                    ParkOrder_OutAdminAccount = model.ParkOrder_OutAdminAccount != null ? model.ParkOrder_OutAdminAccount.Trim() : null,
                    ParkOrder_ParkAreaNo = model.ParkOrder_ParkAreaNo,
                    ParkOrder_IsFree = model.ParkOrder_IsFree,
                    ParkOrder_IsNoInRecord = model.ParkOrder_IsNoInRecord,
                    ParkOrder_IsEpCar = model.ParkOrder_IsEpCar,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0 != null ? model.ParkOrder_EnterTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1 != null ? model.ParkOrder_EnterTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_OutTime0 = model.ParkOrder_OutTime0 != null ? model.ParkOrder_OutTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_OutTime1 = model.ParkOrder_OutTime1 != null ? model.ParkOrder_OutTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_No = model.ParkOrder_No != null ? "%" + model.ParkOrder_No.Trim() + "%" : null,
                    ParkOrder_CarNo = model.ParkOrder_CarNo != null ? model.ParkOrder_CarNo.Trim() : null,
                    ParkOrder_CarNo1 = model.ParkOrder_CarNo1 != null ? "%" + model.ParkOrder_CarNo1.Trim() + "%" : null,
                    ParkOrder_EnterAdminName = model.ParkOrder_EnterAdminName != null ? "%" + model.ParkOrder_EnterAdminName.Trim() + "%" : null,
                    ParkOrder_CarLogo = model.ParkOrder_CarLogo != null ? "%" + model.ParkOrder_CarLogo.Trim() + "%" : null,
                    ParkOrder_FreeReason = model.ParkOrder_FreeReason != null ? "%" + model.ParkOrder_FreeReason.Trim() + "%" : null,
                    ParkOrder_OutAdminName = model.ParkOrder_OutAdminName != null ? "%" + model.ParkOrder_OutAdminName.Trim() + "%" : null,
                    ParkOrder_OwnerName = model.ParkOrder_OwnerName != null ? "%" + model.ParkOrder_OwnerName.Trim() + "%" : null,
                    ParkOrder_OwnerSpace = model.ParkOrder_OwnerSpace != null ? "%" + model.ParkOrder_OwnerSpace.Trim() + "%" : null,
                    ParkOrder_OwnerNoList = ownerNoes,
                };


                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && model.ParkOrder_IsSupplement != null)
                    sqlwhere.Append($" and ParkOrder_UserNo in('1','2','3','4') ");

                field = string.IsNullOrEmpty(field) ? "ParkOrder_EnterTime" : field;
                order = string.IsNullOrEmpty(order) ? "0" : (order == "asc" ? "1" : "0");

                DateTime? dataTime = model.ParkOrder_EnterTime0 ?? model.ParkOrder_EnterTime1 ?? model.ParkOrder_OutTime0 ?? model.ParkOrder_OutTime1 ?? DateTime.Now;

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder.GetList(Model.EnumParkOrderStatus.In, "*", sqlwhere.ToString(), pageIndex, pageSize, field, Utils.ObjectToInt(order, 0), out pageCount, out totalRecord, model.DataType, dataTime, 80, parameters);

                var time = DateTime.Parse("1900-01-01 00:00:00");
                lst.ForEach(x => { if (x.ParkOrder_OutTime == time) { x.ParkOrder_OutTime = null; } });

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"失败:{ex.Message}", SecondIndex.InParkRecord);
                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return Ok(oModel);
        }

        public ActionResult Detail()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Detail.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        public IActionResult GetParkOrderByNo(string ParkOrder_No, int dataType = 0, string dataTime = "")
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Detail.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrder model = null;
                List<Model.PayOrder> payOrders = null;
                List<Model.CouponRecord> couponList = null;
                List<Model.OrderDetail> detailList = null;
                List<Model.CalcDetail> calcList = null;

                if (dataType == 1)
                {
                    if (string.IsNullOrEmpty(dataTime)) dataTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                    model = BLL.ParkOrder.GetEntity(ParkOrder_No, dataType, Utils.ObjectToDateTime(dataTime, DateTime.Now));
                    payOrders = BLL.PayOrder.GetAllEntity("*", $"PayOrder_ParkOrderNo=@ParkOrder_No", dataType, Utils.ObjectToDateTime(dataTime, DateTime.Now), parameters: new { ParkOrder_No = ParkOrder_No });
                    if (model != null && model.ParkOrder_StatusNo != EnumParkOrderStatus.Follow) couponList = BLL.CouponRecord.GetAllEntity("*", $"CouponRecord_ParkOrderNo=@ParkOrder_No and CouponRecord_Status=1", dataType, Utils.ObjectToDateTime(dataTime, DateTime.Now), parameters: new { ParkOrder_No = ParkOrder_No });
                    detailList = BLL.OrderDetail.GetAllEntity(ParkOrder_No, dataType, Utils.ObjectToDateTime(dataTime, DateTime.Now));
                    calcList = BLL.CalcDetail.GetAllEntity(ParkOrder_No, dataType, Utils.ObjectToDateTime(dataTime, DateTime.Now));
                }
                else
                {
                    model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                    payOrders = BLL.PayOrder.GetAllEntity("*", $"PayOrder_ParkOrderNo=@ParkOrder_No", parameters: new { ParkOrder_No = ParkOrder_No });
                    if (model != null && model.ParkOrder_StatusNo != EnumParkOrderStatus.Follow) couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $"CouponRecord_ParkOrderNo=@ParkOrder_No and CouponRecord_Status=1", parameters: new { ParkOrder_No = ParkOrder_No });
                    detailList = BLL.OrderDetail.GetAllEntity(ParkOrder_No);
                    calcList = BLL.BaseBLL._GetAllEntity(new Model.CalcDetail(), "*", $"CalcDetail_ParkOrderNo=@ParkOrder_No", parameters: new { ParkOrder_No = ParkOrder_No });
                }

                if (model == null) { return ResOk(false, "未找到停车订单"); }


                var time = DateTime.Parse("1900-01-01 00:00:00");
                if (model != null && model.ParkOrder_OutTime == time) { model.ParkOrder_OutTime = null; }

                if (detailList != null) detailList = detailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                detailList.ForEach(x =>
                {
                    if (x != null && x.OrderDetail_OutTime == time) { x.OrderDetail_OutTime = null; }
                });

                List<Model.DetentionPenalty> penaltyList = BLL.BaseBLL._GetAllEntity(new Model.DetentionPenalty(), "*", $"DetentionPenalty_ParkOrderNo=@ParkOrder_No", parameters: new { ParkOrder_No = ParkOrder_No });

                return ResOk(true, "", new { model = model, payOrders = payOrders, parking = parking, detail = detailList, coupon = couponList, calclist = calcList, penaltylist = penaltyList });
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询停车订单详情", "查询停车订单详情发生异常:" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 修改车牌
        /// </summary>
        public IActionResult UpdateCarNo(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Update.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrder obj = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (obj == null) { return ResOk(false, "数据错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_No)) { return ResOk(false, "订单号错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_CarNo)) { return ResOk(false, "车牌号错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_CarCardType)) { return ResOk(false, "车牌类型错误"); }
                if (obj.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间错误"); }
                if (obj.ParkOrder_EnterTime > DateTimeHelper.GetNowTime()) { return ResOk(false, "入场时间不能大于当前时间"); }

                string ParkOrder_No = obj.ParkOrder_No;
                string NewCarNo = obj.ParkOrder_CarNo;
                if (string.IsNullOrEmpty(NewCarNo) || NewCarNo.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(NewCarNo))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                NewCarNo = NewCarNo.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");//字母O替换数字0
                NewCarNo = NewCarNo.ToUpper();

                string NewCarCardType = obj.ParkOrder_CarCardType;
                DateTime? NewEnterTime = obj.ParkOrder_EnterTime;

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                if (model == null) { BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"订单不存在:停车订单编号：{ParkOrder_No},车牌号：{NewCarNo},车牌类型：{NewCarCardType},详情：{jsonModel}", SecondIndex.InParkRecord); return ResOk(false, "停车订单不存在"); }

                if (model.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Est)
                {
                    var confirmOrder = ConfirmRelease.Results.Where(kv => kv.Value.passres?.parkorderno == ParkOrder_No).FirstOrDefault();
                    if (confirmOrder.Value != null)
                    {
                        return ResOk(false, $"很抱歉，请先处理{model.ParkOrder_CarNo}在岗亭{confirmOrder.Value?.passres?.passway?.Passway_Name}弹框");
                    }
                }

                var res = BLL.ParkOrder.UpdateOrder(model, NewCarNo, NewCarCardType, NewEnterTime, obj.ParkOrder_StatusNo, obj.ParkOrder_Remark, true, CheckSpaceNumByEnterTime: false, lgAdmins: lgAdmins);
                if (res.code == 1)
                {
                    var confirmOrder = ConfirmRelease.Results.Where(kv => kv.Value.passres?.parkorderno == ParkOrder_No).FirstOrDefault();
                    if (confirmOrder.Value != null)
                    {
                        if (confirmOrder.Value.passres != null)
                        {
                            confirmOrder.Value.passres.carno = NewCarNo;
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(NewCarNo, ParkOrder_No, CloseNoInPark: false, mode: 6);
                        }
                    }

                    DataCache.ParkOrder.Set(model.ParkOrder_No, model);
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"失败:停车订单编号：{ParkOrder_No},车牌号：{NewCarNo},入场时间：{NewEnterTime}，车牌类型：{model.ParkOrder_CarCardTypeName},订单备注：{obj.ParkOrder_Remark},订单状态：{Common.EnumHelper.GetParkOrderStatusName(obj.ParkOrder_StatusNo)}", SecondIndex.InParkRecord);
                    return ResOk(false, res.msg);
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, "修改车牌发生异常:" + ex.ToString(), SecondIndex.InParkRecord);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取车辆信息
        /// </summary>
        public IActionResult GetCarInfo(string carno)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Search.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");
                if (!string.IsNullOrEmpty(carno) && carno.Length >= 4)
                {
                    Model.CarExt car = BLL.Car.GetCarExtEntityByCarNo(carno);
                    return ResOk(true, "获取成功", car);
                }
                else
                {
                    return ResOk(false, "车牌号为空");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, "修改订单获取车辆信息发生异常:" + ex.ToString(), SecondIndex.InParkRecord);
                return ResOk(false, ex.Message);
            }
        }

        #region 导入、导出
        public IActionResult Import()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Import.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        /// <summary>
        /// 导入停车记录
        /// </summary>
        public IActionResult ToImport()
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Import.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var importRet = DataCache.SyncAction.Get("orderimport1");
                if (!string.IsNullOrEmpty(importRet))
                {
                    var retArray = importRet.Split(",");
                    if (retArray.Count() > 0)
                    {
                        var sumCount = Utils.ObjectToInt(retArray[0], 0);
                        var runCount = Utils.ObjectToInt(retArray[1], 0);
                        var successCount = Utils.ObjectToInt(retArray[2], 0);
                        var failCount = Utils.ObjectToInt(retArray[3], 0);

                        if (sumCount > 0)
                        {
                            if (runCount < sumCount)
                            {
                                return ResOk(false, $"上一次的导入任务尚未完成，请稍候。（当前执行状态：共{sumCount}条，已处理{runCount}条）");
                            }
                        }
                    }
                }

                //解析导入的Excel文件
                var postForm = Request.Form;

                IFormFileCollection formFiles = Request.Form.Files;
                if (!Utils.IsOnlyExcelFiles(formFiles))
                {
                    return ResOk(false, "只允许上传Excel文件");
                }

                List<Model.CarType> carTypes = BLL.CarType.GetAllEntity("CarType_No,CarType_Name", $"CarType_ParkNo='{parking.Parking_No}'");
                if (carTypes == null || carTypes.Count == 0) { return ResOk(false, "未设置车牌颜色"); }
                List<Model.CarCardType> cards = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_IsMoreCar", $"CarCardType_ParkNo='{parking.Parking_No}'");
                if (cards == null || cards.Count == 0) { return ResOk(false, "未设置车牌类型"); }
                List<Model.ParkArea> areas = BLL.ParkArea.GetAllEntity("*", $"ParkArea_ParkNo='{parking.Parking_No}'");
                if (areas == null || areas.Count == 0) { return ResOk(false, "未设置停车区域"); }
                List<Model.Passway> passways = BLL.Passway.GetAllEntity("*", $"Passway_ParkNo='{parking.Parking_No}'");
                if (passways == null || passways.Count == 0) { return ResOk(false, "未设置车道"); }

                long size = formFiles.Sum(f => f.Length);
                var rb = new ResultBase();
                ExcelToDataTable(formFiles, areas, cards, carTypes, passways, out var orders, out var details, ref rb);
                //文件不合符导入条件
                if (rb.Code == "1") { return ResOk(false, rb.Message, null, "1"); }

                //查询场内车辆记录
                List<Model.InCar> incars = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "*", $"Incar_Status='{Model.EnumParkOrderStatus.In}'");
                List<string> inParkCarNoList = incars?.Select(x => x.InCar_CarNo).ToList();
                if (inParkCarNoList != null && inParkCarNoList.Count > 0)
                {
                    orders.RemoveAll(x => inParkCarNoList.Contains(x.ParkOrder_CarNo));
                    details.RemoveAll(x => inParkCarNoList.Contains(x.OrderDetail_CarNo));
                }

                if (orders.Count == 0) return ResOk(false, "没有需要导入场内的车辆");

                orders.ForEach(o =>
                {
                    BLL.ParkOrder.EpParkOrder(ref o, null);
                });

                List<Model.Car> carList = new List<Model.Car>();
                List<Model.Owner> ownerList = new List<Model.Owner>();
                List<Model.StopSpace> stopsapce = null;


                var carnoList = orders.Select(x => x.ParkOrder_CarNo).ToList().Distinct();
                carList = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerNo", $"");
                var carList2 = carList.Where(x => carnoList.Contains(x.Car_CarNo)).ToList();
                var nolist = carList2.Select(x => x.Car_OwnerNo).ToList().Distinct();
                ownerList = BLL.Owner.GetAllEntity("Owner_No,Owner_CardTypeNo", $"");
                ownerList = ownerList.Where(x => nolist.Contains(x.Owner_No)).ToList();
                carList = carList.Where(x => nolist.Contains(x.Car_OwnerNo)).ToList();
                stopsapce = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "StopSpace_OwnerNo,StopSpace_Type,StopSpace_Number,StopSpace_AreaNo", $"StopSpace_OwnerNo in ('{string.Join("','", nolist)}')");

                BLL.ParkOrder.ImportRecordToChangeOrder(carList, ownerList, stopsapce, cards, ref orders, ref details);

                var res = BLL.ParkOrder.CarInComplete(orders, details);
                if (res > 0)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            var count = 0;
                            var datas = BLL.ParkOrder.sendPush(orders, details, 50);

                            foreach (var item in datas)
                            {
                                PushSync(Model.API.PushAction.Edit, item, new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.EnterCar, Desc: $"场内记录导入{string.Join(",", item.Item1?.Select(x => x.ParkOrder_CarNo))}");
                                Task.Delay(10).Wait();
                                count++;
                                DataCache.SyncAction.Set("orderimport1", $"{orders.Count},{count * 50},{count * 50},0");
                            }

                            orders?.ForEach(order =>
                            {
                                BLL.PushEvent.EnterCar(parking.Parking_Key, order);
                            });
                        }
                        catch (Exception ex)
                        {
                            DataCache.SyncAction.Del("orderimport1");
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"导入停车记录异常:{ex.ToString()}", SecondIndex.InParkRecord);
                        }

                        return Task.CompletedTask;

                    });

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"导入停车记录成功:{orders.Count}", SecondIndex.InParkRecord);

                    return ResOk(true, "导入成功");
                }

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"导入停车记录失败:{orders.Count}", SecondIndex.InParkRecord);

                return ResOk(false, "导入失败");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"导入停车记录异常:{ex.ToString()}", SecondIndex.InParkRecord);
                return ResOk(false, $"导入失败：{ex.Message}");
            }
        }

        private DataTable ExcelToDataTable(IFormFileCollection hfc
            , List<Model.ParkArea> areas
            , List<Model.CarCardType> cards
            , List<Model.CarType> carTypes
            , List<Model.Passway> passways
            , out List<Model.ParkOrder> orders
            , out List<Model.OrderDetail> details
            , ref ResultBase rb)
        {
            orders = new List<Model.ParkOrder>();
            details = new List<Model.OrderDetail>();

            string webRootPath = _hostingEnvironment.ContentRootPath;
            string contentRootPath = _hostingEnvironment.ContentRootPath;

            if (!AppBasicCache.IsWindows) { Common.LocalFile.CreateDirectory("/mnt/sda1/b30/import"); }
            string imgPath = (AppBasicCache.IsWindows ? "\\wwwroot\\Data\\" : "/mnt/sda1/b30/import/") + DateTimeHelper.GetNowTime().ToString("yyyyMMddhhmmssf") + hfc[0].FileName;
            string physicalPath = AppBasicCache.IsWindows ? webRootPath + imgPath : imgPath;

            using (var stream = new FileStream(physicalPath, FileMode.Create))
            {
                hfc[0].CopyTo(stream);
            }
            //读取excel到Table
            NPOIExcelHelper npoi = new NPOIExcelHelper(physicalPath);
            DataTable dtExcel = npoi.ImportToDataTableHasDate("", true, 0);
            if (dtExcel == null) { rb = new ResultBase() { Code = "1", Message = "读取Excel数据失败", Success = false }; return null; }

            //限制每次导入最大2000条
            if (dtExcel.Rows.Count > 10000) { rb = new ResultBase() { Code = "1", Message = "每次最多导入10000条", Success = false }; return null; }

            int curIndex = 0;
            StringBuilder errMsg = new StringBuilder();    //错误记录
            List<string> orderNoList = Utils.GetRandomLst(dtExcel.Rows.Count * 2);
            try
            {

                List<string> carnoList = new List<string>();
                foreach (DataRow dr in dtExcel.Rows)
                {
                    curIndex = dtExcel.Rows.IndexOf(dr) + 1; //当前数据行

                    Model.ParkOrder order = new Model.ParkOrder()
                    {
                        ParkOrder_ParkNo = parking.Parking_No,
                        ParkOrder_StatusNo = Model.EnumParkOrderStatus.In,
                        ParkOrder_OutType = 0,
                        ParkOrder_TotalAmount = 0,
                        ParkOrder_Lock = 0,
                        ParkOrder_UserNo = "",
                        ParkOrder_QrCodeType = 1,
                        ParkOrder_Remark = "",
                        ParkOrder_IsSettle = 0,
                        ParkOrder_IsLift = 0
                    };
                    Model.OrderDetail detail = new Model.OrderDetail()
                    {
                        OrderDetail_ParkNo = parking.Parking_No,
                        OrderDetail_StatusNo = Model.EnumParkOrderStatus.In,
                        OrderDetail_OutType = 0,
                        OrderDetail_TotalAmount = 0,
                        OrderDetail_Lock = 0,
                        OrderDetail_UserNo = "",
                        OrderDetail_QrCodeType = 1,
                        OrderDetail_Remark = "",
                        OrderDetail_IsSettle = 0
                    };

                    #region 单行数据值转换
                    for (int i = 0; i < dr.ItemArray.Length; i++)
                    {
                        Model.CarCardType card = null;
                        Model.CarType cartp = null;
                        Model.ParkArea area = null;
                        Model.Passway passway = null;
                        DateTime curDateTime = DateTimeHelper.GetNowTime();
                        string value = dr.ItemArray[i].ToString().Trim();
                        switch (i)
                        {
                            case 0://车牌号
                                if (string.IsNullOrEmpty(value) || !Utils.ValidationCarNo(value)) { errMsg.Append($"第{curIndex}行：车牌号[{value}]格式错误.\r\n"); break; }
                                if (carnoList.Contains(value)) { errMsg.Append($"第{curIndex}行：车牌号[{value}]存在重复.\r\n"); break; }
                                order.ParkOrder_CarNo = value;
                                detail.OrderDetail_CarNo = value;
                                carnoList.Add(value);
                                break;
                            case 1://车牌类型
                                if (string.IsNullOrEmpty(value)) { errMsg.Append($"第{curIndex}行：车牌类型[{value}]错误.\r\n"); break; }
                                card = cards.Find(x => x.CarCardType_Name == value);
                                if (card == null) { errMsg.Append($"第{curIndex}行：车牌类型[{value}]未添加.\r\n"); break; }

                                order.ParkOrder_CarCardType = card.CarCardType_No;
                                order.ParkOrder_CarCardTypeName = card.CarCardType_Name;
                                detail.OrderDetail_CarCardType = card.CarCardType_No;
                                detail.OrderDetail_CarCardTypeName = card.CarCardType_Name;
                                break;
                            case 2://车牌颜色
                                if (string.IsNullOrEmpty(value)) { errMsg.Append($"第{curIndex}行：车牌颜色[{value}]错误.\r\n"); break; }
                                cartp = carTypes.Find(x => x.CarType_Name == value);
                                if (cartp == null) { errMsg.Append($"第{curIndex}行：车牌颜色[{value}]未添加.\r\n"); break; }

                                order.ParkOrder_CarType = cartp.CarType_No;
                                order.ParkOrder_CarTypeName = cartp.CarType_Name;
                                detail.OrderDetail_CarType = cartp.CarType_No;
                                detail.OrderDetail_CarTypeName = cartp.CarType_Name;
                                break;
                            case 3://停车区域
                                if (string.IsNullOrEmpty(value)) { errMsg.Append($"第{curIndex}行：停车区域[{value}]错误.\r\n"); break; }
                                area = areas.Find(x => x.ParkArea_Name == value);
                                if (area == null) { errMsg.Append($"第{curIndex}行：停车区域名称[{value}]未添加.\r\n"); break; }

                                order.ParkOrder_ParkAreaNo = area.ParkArea_No;
                                order.ParkOrder_ParkAreaName = area.ParkArea_Name;
                                detail.OrderDetail_ParkAreaNo = area.ParkArea_No;
                                detail.OrderDetail_ParkAreaName = area.ParkArea_Name;
                                break;
                            case 4://入场时间
                                if (string.IsNullOrEmpty(value)) { errMsg.Append($"第{curIndex}行：入场时间[{value}]错误.\r\n"); break; }
                                if (!DateTime.TryParse(value, out curDateTime)) { errMsg.Append($"第{curIndex}行：入场时间[{value}]错误.\r\n"); break; }
                                if (curDateTime > DateTimeHelper.GetNowTime()) { errMsg.Append($"第{curIndex}行：入场时间[{value}]大于当前时间.\r\n"); break; }

                                order.ParkOrder_EnterTime = curDateTime;
                                detail.OrderDetail_EnterTime = curDateTime;
                                break;
                            case 5://入场车道
                                if (!string.IsNullOrEmpty(value))
                                    passway = passways.Find(x => x.Passway_Name == value);

                                order.ParkOrder_EnterPasswayNo = passway?.Passway_No ?? "";
                                order.ParkOrder_EnterPasswayName = passway?.Passway_Name ?? "导入";
                                detail.OrderDetail_EnterPasswayNo = passway?.Passway_No ?? "";
                                detail.OrderDetail_EnterPasswayName = passway?.Passway_Name ?? "导入";
                                break;
                            case 6://入场操作员
                                order.ParkOrder_EnterAdminName = value;
                                detail.OrderDetail_EnterAdminName = value;
                                break;
                            case 7://入场备注
                                order.ParkOrder_EnterRemark = value;
                                detail.orderdetail_EnterRemark = value;
                                break;
                            default:
                                break;
                        }
                    }
                    #endregion

                    if (!string.IsNullOrEmpty(errMsg.ToString()))
                    {
                        rb = new ResultBase() { Code = "1", Message = errMsg.ToString(), Success = false };
                        return null;
                    }

                    #region 设置订单编号
                    string ParkOrder_No = BLL.OrderDetail.NewOrderNo(order.ParkOrder_CarNo, orderNoList.First());
                    orderNoList.RemoveAt(0);
                    string OrderDetail_No = BLL.OrderDetail.NewOrderNo(detail.OrderDetail_CarNo, orderNoList.First());
                    orderNoList.RemoveAt(0);

                    order.ParkOrder_No = ParkOrder_No;
                    detail.OrderDetail_No = OrderDetail_No;
                    detail.OrderDetail_ParkOrderNo = ParkOrder_No;
                    #endregion

                    orders.Add(order);
                    details.Add(detail);
                }

                Utils.DelFile(physicalPath); //删除Excel
                return dtExcel;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"导入停车记录异常:{ex.ToString()}", SecondIndex.InParkRecord);
                rb = new ResultBase()
                {
                    Code = "1",
                    Message = $"第{curIndex}行,存在数据格式错误！{ex.Message}",
                    Success = false
                };
                Utils.DelFile(physicalPath);
                return null;
            }
        }

        /// <summary>
        /// 查询需要导出的数据
        /// </summary>
        /// <param name="conditionParam"></param>
        /// <returns></returns>
        private List<Model.ParkOrder> QueryList(string conditionParam, string field, string order)
        {
            Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);

            StringBuilder sqlwhere = new StringBuilder();
            sqlwhere.Append(" 1=1 ");

            if (model.ParkOrder_StatusNo != null)
            {
                if (model.ParkOrder_StatusNo != 0)
                    sqlwhere.Append($" and ParkOrder_StatusNo=@ParkOrder_StatusNo ");
                else
                    sqlwhere.Append($" and ParkOrder_StatusNo='{Model.EnumParkOrderStatus.In}' and ParkOrder_OutType=1 ");
            }

            if (model.ParkOrder_Lock != null)
                sqlwhere.Append($" and ParkOrder_Lock = @ParkOrder_Lock ");
            if (model.ParkOrder_OutType != null)
                sqlwhere.Append($" and ParkOrder_OutType = @ParkOrder_OutType ");
            if (!string.IsNullOrEmpty(model.ParkOrder_CarCardType))
                sqlwhere.Append($" and ParkOrder_CarCardType = @ParkOrder_CarCardType ");
            if (!string.IsNullOrEmpty(model.ParkOrder_CarType))
                sqlwhere.Append($" and ParkOrder_CarType = @ParkOrder_CarType ");
            if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                sqlwhere.Append($" and ParkOrder_EnterPasswayNo = @ParkOrder_EnterPasswayNo ");
            if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                sqlwhere.Append($" and ParkOrder_OutPasswayNo = @ParkOrder_OutPasswayNo ");

            if (!string.IsNullOrEmpty(model.ParkOrder_EnterAdminAccount))
                sqlwhere.Append($" and ParkOrder_EnterAdminAccount = @ParkOrder_EnterAdminAccount ");
            if (!string.IsNullOrEmpty(model.ParkOrder_OutAdminAccount))
                sqlwhere.Append($" and ParkOrder_OutAdminAccount = @ParkOrder_OutAdminAccount ");
            if (!string.IsNullOrEmpty(model.ParkOrder_ParkAreaNo))
                sqlwhere.Append($" and ParkOrder_ParkAreaNo = @ParkOrder_ParkAreaNo ");
            if (model.ParkOrder_IsFree != null)
                sqlwhere.Append($" and ParkOrder_IsFree = @ParkOrder_IsFree ");
            if (model.ParkOrder_IsNoInRecord != null)
                sqlwhere.Append($" and ParkOrder_IsNoInRecord = @ParkOrder_IsNoInRecord ");
            if (model.ParkOrder_IsEpCar != null)
                sqlwhere.Append($" and ParkOrder_IsEpCar = @ParkOrder_IsEpCar ");

            if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 AND ParkOrder_EnterTime< @ParkOrder_EnterTime1 ");
            else if (model.ParkOrder_EnterTime0 != null)
                sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
            else if (model.ParkOrder_EnterTime1 != null)
                sqlwhere.Append($" and ParkOrder_EnterTime < @ParkOrder_EnterTime1 ");

            if (model.ParkOrder_OutTime0 != null && model.ParkOrder_OutTime1 != null)
                sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 AND ParkOrder_OutTime< @ParkOrder_OutTime1 ");
            else if (model.ParkOrder_OutTime0 != null)
                sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 ");
            else if (model.ParkOrder_OutTime1 != null)
                sqlwhere.Append($" and ParkOrder_OutTime < @ParkOrder_OutTime1 ");

            if (!string.IsNullOrEmpty(model.ParkOrder_No))
                sqlwhere.Append($" and ParkOrder_No like @ParkOrder_No ");
            if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");
            else if (!string.IsNullOrEmpty(model.ParkOrder_CarNo1))
                sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo1 ");
            if (!string.IsNullOrEmpty(model.ParkOrder_EnterAdminName))
                sqlwhere.Append($" and ParkOrder_EnterAdminName like @ParkOrder_EnterAdminName ");
            if (!string.IsNullOrEmpty(model.ParkOrder_OutAdminName))
                sqlwhere.Append($" and ParkOrder_OutAdminName like @ParkOrder_OutAdminName ");
            if (!string.IsNullOrEmpty(model.ParkOrder_CarLogo))
                sqlwhere.Append($" and ParkOrder_CarLogo like @ParkOrder_CarLogo ");
            if (!string.IsNullOrEmpty(model.ParkOrder_FreeReason))
                sqlwhere.Append($" and ParkOrder_FreeReason like @ParkOrder_FreeReason ");

            bool isSearchOwner = false;
            var ownerNoes = new List<string>();
            if (!string.IsNullOrEmpty(model.ParkOrder_OwnerName))
            {
                var owners = BLL.Owner.GetAllEntity("Owner_No", $"Owner_Name like @ParkOrder_OwnerName");
                if (owners != null && owners.Count > 0)
                {
                    isSearchOwner = true;
                    ownerNoes = owners.Select(x => x.Owner_No).ToList();
                }
                else
                {
                    sqlwhere.Append($" and ParkOrder_OwnerName like @ParkOrder_OwnerName ");
                }
            }
            if (!string.IsNullOrEmpty(model.ParkOrder_OwnerSpace))
            {
                isSearchOwner = true;
                var owners = BLL.Owner.GetAllEntity("Owner_No", $"Owner_Space like @ParkOrder_OwnerSpace");
                if (owners != null && owners.Count > 0)
                {
                    ownerNoes.AddRange(owners.Select(x => x.Owner_No).ToList());
                }
            }
            if (ownerNoes.Count > 0)
            {
                ownerNoes = ownerNoes.Distinct().ToList();
                sqlwhere.Append($" and ParkOrder_OwnerNo in @ParkOrder_OwnerNoList ");
            }
            else
            {
                if (isSearchOwner) return new List<Model.ParkOrder>();
            }

            field = string.IsNullOrEmpty(field) ? "ParkOrder_ID" : field;
            order = string.IsNullOrEmpty(order) ? "asc" : order;

            string whereSql = sqlwhere.ToString() + $" order by {field} {order}";

            DateTime? dataTime = model.ParkOrder_EnterTime0 ?? model.ParkOrder_EnterTime1 ?? model.ParkOrder_OutTime0 ?? model.ParkOrder_OutTime1 ?? DateTime.Now;

            var parameters = new
            {
                ParkOrder_StatusNo = model.ParkOrder_StatusNo,
                ParkOrder_Lock = model.ParkOrder_Lock,
                ParkOrder_OutType = model.ParkOrder_OutType,
                ParkOrder_CarCardType = model.ParkOrder_CarCardType,
                ParkOrder_CarType = model.ParkOrder_CarType,
                ParkOrder_EnterPasswayNo = model.ParkOrder_EnterPasswayNo,
                ParkOrder_OutPasswayNo = model.ParkOrder_OutPasswayNo,
                ParkOrder_EnterAdminAccount = model.ParkOrder_EnterAdminAccount,
                ParkOrder_OutAdminAccount = model.ParkOrder_OutAdminAccount,
                ParkOrder_ParkAreaNo = model.ParkOrder_ParkAreaNo,
                ParkOrder_IsFree = model.ParkOrder_IsFree,
                ParkOrder_IsNoInRecord = model.ParkOrder_IsNoInRecord,
                ParkOrder_IsEpCar = model.ParkOrder_IsEpCar,
                ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0 != null ? model.ParkOrder_EnterTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1 != null ? model.ParkOrder_EnterTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                ParkOrder_OutTime0 = model.ParkOrder_OutTime0 != null ? model.ParkOrder_OutTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                ParkOrder_OutTime1 = model.ParkOrder_OutTime1 != null ? model.ParkOrder_OutTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                ParkOrder_No = model.ParkOrder_No != null ? "%" + model.ParkOrder_No.Trim() + "%" : null,
                ParkOrder_CarNo = model.ParkOrder_CarNo != null ? model.ParkOrder_CarNo.Trim() : null,
                ParkOrder_CarNo1 = model.ParkOrder_CarNo1 != null ? "%" + model.ParkOrder_CarNo1.Trim() + "%" : null,
                ParkOrder_EnterAdminName = model.ParkOrder_EnterAdminName != null ? "%" + model.ParkOrder_EnterAdminName.Trim() + "%" : null,
                ParkOrder_OutAdminName = model.ParkOrder_OutAdminName != null ? "%" + model.ParkOrder_OutAdminName.Trim() + "%" : null,
                ParkOrder_CarLogo = model.ParkOrder_CarLogo != null ? "%" + model.ParkOrder_CarLogo.Trim() + "%" : null,
                ParkOrder_FreeReason = model.ParkOrder_FreeReason != null ? "%" + model.ParkOrder_FreeReason.Trim() + "%" : null,
                ParkOrder_OwnerName = model.ParkOrder_OwnerName != null ? "%" + model.ParkOrder_OwnerName.Trim() + "%" : null,
                ParkOrder_OwnerSpace = model.ParkOrder_OwnerSpace != null ? "%" + model.ParkOrder_OwnerSpace.Trim() + "%" : null,
                ParkOrder_OwnerNoList = ownerNoes
            };

            List<Model.ParkOrder> lst = BLL.ParkOrder.GetAllEntity("*", whereSql, model.DataType, dataTime, 80, parameters);

            return lst;
        }

        /// <summary>
        /// 导出停车记录
        /// </summary>
        public FileContentResult Export(string conditionParam, string field, string order, string chkfield)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Export.ToString(), false, true, lgAdmins))
                    return null;

                var data = QueryList(conditionParam, field, order);

                if (data == null || data.Count == 0)
                {
                    HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true", new Microsoft.AspNetCore.Http.CookieOptions { Expires = DateTimeOffset.Now.AddSeconds(10), IsEssential = true });
                    return File(new byte[0], "application/vnd.ms-excel", $"停车记录_{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd")}.xlsx");
                }

                DataTable newTable = new DataTable();
                DataTable dataTable = Utils.ListToDataTableString(data);
                Dictionary<string, string> dic = new Dictionary<string, string>();
                var filedArray = TyziTools.Json.ToObject<List<JObject>>(chkfield);
                var filedList = new List<string>();
                foreach (var item in filedArray)
                {
                    if (item != null)
                    {
                        var filedName = Convert.ToString(item["filed"]);
                        if (!dic.ContainsKey(filedName))
                        {
                            dic.Add(filedName, Convert.ToString(item["text"]));
                            newTable.Columns.Add(new DataColumn(filedName, typeof(string)));
                        }
                    }
                }
                //if (dic.ContainsKey("Owner_AddName")) adminList = BLL.Admins.GetAllEntity("Admins_ID,Admins_Name", "1=1");

                foreach (DataRow item in dataTable.Rows)
                {
                    if (dic.ContainsKey("ParkOrder_Lock")) item["ParkOrder_Lock"] = item["ParkOrder_Lock"].ToString() == "1" ? "已锁车" : "未锁车";
                    if (dic.ContainsKey("ParkOrder_StatusNo")) item["ParkOrder_StatusNo"] = Common.EnumHelper.GetParkOrderStatusName(item["ParkOrder_StatusNo"].ToString());
                    if (dic.ContainsKey("ParkOrder_IsEpCar")) item["ParkOrder_IsEpCar"] = item["ParkOrder_IsEpCar"].ToString() == "1" ? "是" : "否";
                    if (dic.ContainsKey("ParkOrder_IsNoInRecord")) item["ParkOrder_IsNoInRecord"] = item["ParkOrder_IsNoInRecord"].ToString() == "1" ? "是" : "否";
                    if (dic.ContainsKey("ParkOrder_PayScene")) item["ParkOrder_PayScene"] = item["ParkOrder_PayScene"].ToString() == "1" ? "场内缴费" : "出口缴费";
                    if (dic.ContainsKey("ParkOrder_CarLogo")) item["ParkOrder_CarLogo"] = (item["ParkOrder_CarLogo"].ToString() == "0" || item["ParkOrder_CarLogo"].ToString() == "") ? "" : item["ParkOrder_CarLogo"].ToString();
                    if (dic.ContainsKey("ParkOrder_IsFree")) item["ParkOrder_IsFree"] = item["ParkOrder_IsFree"].ToString() == "1" ? "是" : "否";
                    if (dic.ContainsKey("ParkOrder_IsLift")) item["ParkOrder_IsLift"] = (item["ParkOrder_IsLift"].ToString() == "1" || item["ParkOrder_IsLift"].ToString() == "2") ? "是" : "否";

                    DataRow dr = newTable.NewRow();
                    foreach (var d in dic)
                    {
                        dr[d.Key] = item[d.Key];
                    }
                    newTable.Rows.Add(dr);
                }

                #region 填充EXCEL数据

                foreach (DataColumn dc in newTable.Clone().Columns)
                {
                    if (!dic.ContainsKey(dc.ColumnName)) //删除不显示列
                    {
                        newTable.Columns.Remove(dc.ColumnName);
                        newTable.AcceptChanges();
                    }

                    {
                        newTable.Columns[dc.ColumnName].ColumnName = dic[dc.ColumnName];
                    }
                }

                byte[] bt = null;

                if (newTable.Rows.Count > 5000)
                    bt = NPOIExcelHelper.ExportToExcel2(newTable);
                else
                    bt = NPOIExcelHelper.ToExport(newTable, null);// (index, sheet) => { sheet.SetColumnWidth(index, columnWidths[index] * 256); }

                HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true",
                   new Microsoft.AspNetCore.Http.CookieOptions
                   {
                       Expires = DateTimeOffset.Now.AddSeconds(10),
                       IsEssential = true
                   });

                #endregion 

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Export, $"导出停车记录:{DateTime.Now.ToString("yyyy-MM-dd")}", SecondIndex.InParkRecord);

                return File(bt, "application/vnd.ms-excel", $"停车记录_{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd")}.xlsx");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Export, $"导出停车记录异常:{ex.ToString()}", SecondIndex.InParkRecord);
                return null;
            }
        }
        #endregion

        #region 新增、删除
        public IActionResult Add()
        {
            if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Add.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult AddParkOrder(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Add.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrder query = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (string.IsNullOrEmpty(query?.ParkOrder_CarNo)) { return ResOk(false, "车牌号不能为空"); }
                if (query?.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间不能为空"); }

                var exist = BLL.ParkOrder.GetParkOrder("*", parking.Parking_No, query.ParkOrder_CarNo);
                if (exist != null) { return ResOk(false, "车辆已在场内，不允许重复添加"); }

                var area = BLL.ParkArea.GetEntity(query?.ParkOrder_ParkAreaNo);
                if (area == null) { return ResOk(false, "区域不存在"); }

                var passway = BLL.Passway.GetEntity(query?.ParkOrder_EnterPasswayNo);
                if (passway == null) { return ResOk(false, "车道不存在"); }

                var card = BLL.CarCardType.GetEntity(query?.ParkOrder_CarCardType);
                if (card == null) { return ResOk(false, "车牌类型不存在"); }

                var carType = BLL.CarType.GetEntity(query?.ParkOrder_CarType);
                if (carType == null) { return ResOk(false, "车牌颜色不存在"); }

                var car = BLL.Car.GetEntityByCarNo(query.ParkOrder_CarNo);
                var owner = car != null ? BLL.Owner.GetEntity(car.Car_OwnerNo) : null;

                query.ParkOrder_ParkAreaName = area.ParkArea_Name;
                query.ParkOrder_EnterPasswayName = passway.Passway_Name;
                query.ParkOrder_CarCardTypeName = card.CarCardType_Name;
                query.ParkOrder_CarTypeName = carType.CarType_Name;

                Model.ParkOrder model = BLL.ParkOrder.CreateParkOrder(parking.Parking_No, query.ParkOrder_ParkAreaNo, query.ParkOrder_ParkAreaName, query.ParkOrder_CarNo, query.ParkOrder_CarCardType, query.ParkOrder_CarCardTypeName, query.ParkOrder_CarType, query.ParkOrder_CarTypeName, query.ParkOrder_EnterTime.Value, query.ParkOrder_EnterPasswayNo, query.ParkOrder_EnterPasswayName, 0, 0, car?.Car_OwnerNo, car?.Car_OwnerName);
                model.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                model.ParkOrder_EnterAdminAccount = lgAdmins?.Admins_Account;
                model.ParkOrder_EnterAdminName = lgAdmins?.Admins_Name;
                model.ParkOrder_EnterRemark = query.ParkOrder_Remark;
                model.ParkOrder_Remark = query.ParkOrder_Remark;

                Model.OrderDetail detail = BLL.OrderDetail.CreateOrderDetail(model.ParkOrder_No, parking.Parking_No, query.ParkOrder_ParkAreaNo, query.ParkOrder_ParkAreaName, query.ParkOrder_CarNo, query.ParkOrder_CarCardType, query.ParkOrder_CarCardTypeName, query.ParkOrder_CarType, query.ParkOrder_CarTypeName, query.ParkOrder_EnterTime.Value, query.ParkOrder_EnterPasswayNo, query.ParkOrder_EnterPasswayName);
                detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                detail.OrderDetail_EnterAdminAccount = lgAdmins?.Admins_Account;
                detail.OrderDetail_EnterAdminName = lgAdmins?.Admins_Name;
                detail.orderdetail_EnterRemark = query.ParkOrder_Remark;
                detail.OrderDetail_Remark = query.ParkOrder_Remark;

                BLL.ParkOrder.EpParkOrder(ref model, null);

                var detailList = new List<Model.OrderDetail>() { detail };

                BLL.ParkOrder.ModifyAnyToChangeOrder(car, owner, card, ref model, ref detailList);
                detail = detailList.FirstOrDefault();

                var res = BLL.ParkOrder.CarInComplete(model, detail);
                if (res > 0)
                {
                    Push(Model.API.PushAction.Add, (new List<Model.ParkOrder> { model }, new List<Model.OrderDetail> { detail }), new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.InParkRecord, Desc: $"新增{model.ParkOrder_CarNo}");

                    BLL.PushEvent.EnterCar(parking.Parking_Key, model);

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"[{model.ParkOrder_CarNo}]:{LogHelper.GetEntityCotent(model)}", SecondIndex.InParkRecord);
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"新增停车记录失败:{LogHelper.GetEntityCotent(model)}", SecondIndex.InParkRecord);
                    return ResOk(false, "保存失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"新增停车记录异常:{jsonModel}", SecondIndex.InParkRecord);
                return ResOk(false, $"保存失败：{ex.Message}");
            }
        }


        public async Task<IActionResult> Delete(string ParkOrder_NoArray)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Delete.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                List<string> ParkOrder_NoList = TyziTools.Json.ToObject<List<string>>(ParkOrder_NoArray);
                if (ParkOrder_NoList == null) return ResOk(false, "请选择");

                List<Model.ParkOrder> parkOrders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_ParkNo='{parking.Parking_No}' AND ParkOrder_No in @ParkOrder_NoList", parameters: new { ParkOrder_NoList = ParkOrder_NoList });
                //parkOrders?.RemoveAll(x => x.ParkOrder_StatusNo != Model.EnumParkOrderStatus.In);//过滤不是在车场内的记录
                if (parkOrders?.Count == 0) { return ResOk(false, "没有需要删除的记录"); }

                var noLists = parkOrders.Select(x => x.ParkOrder_No).ToList();

                var confirmOrders = ConfirmRelease.Results.Values.Where(x => noLists.Contains(x.passres?.parkorderno) || noLists.Contains(x.resorder?.resOut?.parkorder?.ParkOrder_No)).ToList();
                if (confirmOrders.Count > 0)
                {
                    var carnoes = confirmOrders.Select(x => x.passres.carno).ToList();
                    return ResOk(false, $"删除失败，请先处理岗亭弹框的车辆：{string.Join(",", carnoes)}");
                }


                List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkNo='{parking.Parking_No}' AND OrderDetail_ParkOrderNo in ('{string.Join("','", parkOrders.Select(x => x.ParkOrder_No))}')");

                var res = BLL.ParkOrder.CloseList(parkOrders, new { ParkOrder_NoList = ParkOrder_NoList });
                if (res > 0)
                {
                    _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                    {
                        parkOrders?.ForEach(async model =>
                        {
                            if (model.ParkOrder_StatusNo == 204)
                            {
                                var cvModel = BLL.ControlEvent.GetEntityByOrderNo(model.ParkOrder_No);
                                if (cvModel != null)
                                {
                                    cvModel.ControlEvent_Status = 1;
                                    var r = BLL.ControlEvent._UpdateByModelByNo(cvModel);
                                    if (r < 0)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{cvModel.ControlEvent_CarNo}更改跟车事件状态失败！");
                                    }

                                    BLL.PushEvent.CarFollowUpdate(
                                    parking.Parking_Key
                                    , cvModel.ControlEvent_No
                                    , cvModel.ControlEvent_CarNo ?? ""
                                    , cvModel.ControlEvent_ParkOrderNo ?? ""
                                    , (cvModel.ControlEvent_Time == null ? "" : cvModel.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss"))
                                    , cvModel.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                    , BLL.CarFollowUpdate_Status.Ignore
                                    , lgAdmins?.Admins_Name
                                    , cvModel.ControlEvent_BigImg ?? ""
                                    , cvModel.ControlEvent_Video ?? ""
                                    , cvModel.ControlEvent_Remark
                                    , "0");
                                }
                            }

                            if (model.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In || model.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow)
                            {
                                model.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                                BLL.PushEvent.CloseCar(parking.Parking_Key, model, "场内关闭");
                            }

                            //刷新弹窗
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ParkOrder_CarNo, null, CloseNoInPark: false);
                            if (!string.IsNullOrEmpty(model.ParkOrder_No))
                            {
                                CalcCache.Del("OrderPrice:" + model.ParkOrder_No);
                                CalcCache.Del($"OrderPrice1:" + model.ParkOrder_No);
                                CalcCache.Del($"OrderPrice2:" + model.ParkOrder_No);
                                CalcCache.Del($"OrderPrice3:" + model.ParkOrder_No);
                                CalcCache.Del($"OrderPrice4:" + model.ParkOrder_No);
                                CalcCache.Del("OrderPriceAutoPay:" + model.ParkOrder_No);
                            }

                            await Task.Delay(10);
                        });

                        ParkSpaceUtil.UpdateDate();
                        return Task.CompletedTask;
                    });

                    Push(Model.API.PushAction.Delete, parkOrders, new List<Model.SentryHost>() { }, "delparkorder", null);
                    Push(Model.API.PushAction.Delete, details);

                    BLL.PushEvent.SendParkSpace(parking.Parking_No);

                    for (int i = 0; i < parkOrders.Count; i++)
                    {
                        var order = parkOrders[i];

                        var parts = new List<string>
                        {
                            $"停车订单编号：{order.ParkOrder_No ?? "未知"}",
                            $"车牌：{order.ParkOrder_CarNo ?? "未知"}",
                            $"车型：{order.ParkOrder_CarCardTypeName ?? "未知"}",
                            $"状态：{EnumHelper.GetParkOrderStatusName(order.ParkOrder_StatusNo)}"
                        };

                        if (order.ParkOrder_EnterTime != null)
                            parts.Add($"入场时间：{order.ParkOrder_EnterTime:yyyy-MM-dd HH:mm:ss}");

                        if (order.ParkOrder_OutTime != null)
                            parts.Add($"出场时间：{order.ParkOrder_OutTime:yyyy-MM-dd HH:mm:ss}");

                        string message = string.Join("，", parts);

                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, message, SecondIndex.InParkRecord);

                        await Task.Delay(1);
                    }
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, $"停车订单{parkOrders.Count}条", SecondIndex.InParkRecord);

                    return ResOk(true, "删除成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, $"删除停车订单编号列表失败：{ParkOrder_NoArray}", SecondIndex.InParkRecord);
                    return ResOk(false, "删除失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, $"删除停车记录异常：{ex.ToString()}", SecondIndex.InParkRecord);
                return ResOk(false, "删除失败");
            }
        }


        public IActionResult SendCloudPark(string ParkOrder_NoArray)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Send.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                if (parking.Parking_EnableNet == 0) { return ResOk(false, "停车场未启用云平台"); }
                if (string.IsNullOrEmpty(parking.Parking_Key)) { return ResOk(false, "车场授权码为空"); }

                List<string> ParkOrder_NoList = TyziTools.Json.ToObject<List<string>>(ParkOrder_NoArray);
                if (ParkOrder_NoList == null) return ResOk(false, "请选择停车订单");

                List<Model.ParkOrder> parkOrders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_ParkNo='{parking.Parking_No}' AND ParkOrder_No in @ParkOrder_NoList", parameters: new { ParkOrder_NoList = ParkOrder_NoList });
                if (parkOrders == null || parkOrders.Count == 0) { return ResOk(false, "请选择停车订单"); }

                foreach (var item in parkOrders)
                {
                    if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                    {
                        BLL.PushEvent.EnterCar(parking.Parking_Key, item);
                    }
                    else if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out)
                    {
                        BLL.PushEvent.OutCar(parking.Parking_Key, item);
                    }
                    else if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close
                        || item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.InClose
                        || item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow)
                    {
                        BLL.PushEvent.CloseCar(parking.Parking_Key, item);
                    }
                }

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Send, $"上传停车记录到云平台：{string.Join(",", parkOrders.Select(x => x.ParkOrder_CarNo))}", SecondIndex.InParkRecord);

                return ResOk(true, "操作成功");
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "上传停车记录到云平台", $"上传停车记录到云平台异常：{ex.ToString()}");
                return ResOk(false, ex.Message);
            }
        }
        #endregion

        #region 平台支付
        /// <summary>
        /// 获取订单价格
        /// </summary>
        public IActionResult GetOrderPrice(string ParkOrder_No, string CouponIDes)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Payment.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                DateTime? now = DateTimeHelper.GetNowTime();

                Model.ParkOrder po = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                if (po == null) { ResOk(false, "未找到停车订单"); }
                if (po.ParkOrder_IsSettle == 1) { ResOk(false, "停车订单已结算"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Est) { return ResOk(false, "停车订单未入场"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out) { return ResOk(false, "停车订单已出场"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close) { return ResOk(false, "停车订单已关闭"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow) { now = po.ParkOrder_OutTime; }

                bool isSelectCoupon = false;

                CouponIDes = Utils.ClearRiskSQL(CouponIDes);
                List<string> idList = new List<string>();
                if (!string.IsNullOrEmpty(CouponIDes))
                {
                    string[] idArray = CouponIDes.Split(',');
                    for (int i = 0; i < idArray.Length; i++)
                    {
                        var value = idArray[i];
                        if (!string.IsNullOrEmpty(value)) idList.Add(value);
                    }
                    CouponIDes = string.Join(",", idList);
                }

                int? policyCount = 0;
                List<Model.CouponRecordIntExt> couponList = null;
                if (po.ParkOrder_StatusNo != EnumParkOrderStatus.Follow)
                {

                    Calc.GetPolicyCoupon(po.ParkOrder_ParkNo, couponList, out policyCount);

                    if (string.IsNullOrEmpty(CouponIDes))
                    {
                        isSelectCoupon = false;
                        couponList = Calc.GetCouponByOrderNo(po.ParkOrder_No, DateTimeHelper.GetNowTime());
                    }
                    else
                    {
                        isSelectCoupon = true;
                        couponList = BLL.CouponRecord.GetAllExtEntity(ParkOrder_No, now, $"and CouponRecord_ID in @CouponIDes ", new { CouponIDes = idList });
                    }

                    if (policyCount == 0 || policyCount < 0) { couponList = null; }
                }

                //获取计费结果
                ChargeModels.PayResult result = null;
                if (po.ParkOrder_IsNoInRecord == 1)
                {
                    return ResOk(false, "不支持无入场记录支付");
                    result = PassTool.PassHelperBiz.FeeNoRecord2(parking.Parking_No, carno: po.ParkOrder_CarNo, time: now.Value, out couponList, po.ParkOrder_OutPasswayNo);
                }
                else
                {
                    result = Calc.GetChargeByCar(po, now, null, po.ParkOrder_StatusNo == EnumParkOrderStatus.Follow ? null : (isSelectCoupon ? couponList : null), false, "", "", null, null, po.ParkOrder_StatusNo == EnumParkOrderStatus.Follow);
                }
                if (result.payed == 2) { return ResOk(false, result.payedmsg); }

                if (result != null)
                {
                    if (result.list != null && result.list.Count > 0)
                        CalcCache.Set("OrderPrice:" + po.ParkOrder_No, result);
                    else
                        CalcCache.Del("OrderPrice:" + po.ParkOrder_No);
                }

                result.payedamount = Utils.ObjectToDecimal(Utils.ObjectToDouble(result.payedamount, 0), 0);
                result.couponamount = Utils.ObjectToDecimal(Utils.ObjectToDouble(result.couponamount, 0), 0);
                result.orderamount = Utils.ObjectToDecimal(Utils.ObjectToDouble(result.orderamount, 0), 0);

                po.ParkOrder_TotalAmount = result.orderamount;
                var coupon = new
                {
                    uselist = result.uselist == null ? "" : TyziTools.Json.ToString(result.uselist),
                    count = result.uselist == null ? 0 : result.uselist.Count,
                    money = result.couponamount,
                };

                var calcdetail = BLL.CommonBLL.GetCalcDetail(result, po);

                return ResOk(true, "", new { order = po, policy = policyCount, coupon = coupon, parktimemin = Utils.ObjectToFloat(result.parktimemin, 0), couponinfolist = couponList, payedamount = result.payedamount, chuzhiamount = result.chuzhiamount, penaltyamount = result.penaltyamount, calcdetail = calcdetail });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"获取计费价格发生异常:{ex.ToString()}", SecondIndex.InParkRecord);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 平台支付
        /// </summary>
        public IActionResult SendNoticeForPayed(string ParkOrder_No, string receAmount, string remark, string CouponRecord_ID, string paidAmount, string parktimemin, string uselist, string chuzhiamount)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.Payment.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrder po = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                if (po == null) { return ResOk(false, "停车订单不能为空"); }
                if (po.ParkOrder_IsSettle == 1) { return ResOk(false, "停车订单已结算"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Est) { return ResOk(false, "停车订单未入场"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out) { return ResOk(false, "停车订单已出场"); }
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close) { return ResOk(false, "停车订单已关闭"); }
                string paytypNo = Model.EnumPayType.OffLineCash.ToString();//支付类型
                List<Model.OrderDetail> orderDetailList = BLL.OrderDetail.GetAllEntity(po.ParkOrder_No);

                CouponRecord_ID = Utils.ClearRiskSQL(CouponRecord_ID);

                string[] idArray = TyziTools.Json.ToObject<string[]>(CouponRecord_ID);

                if (!string.IsNullOrEmpty(CouponRecord_ID) && idArray == null) { return ResOk(false, "优惠券异常"); }

                //string smqPayCode = string.Empty;
                //if (!string.IsNullOrEmpty(payCode))
                //{
                //    var taskPay = carparking.Command.Pay.PaymentCode(po.ParkOrder_CarNo, Utils.DateDiffStr(Utils.ObjectToInt(parktimemin, 0) * 60), Utils.StrToDecimal(paidAmount, 0), payCode);
                //    var payresult = taskPay.Result;
                //    if (!payresult.Item1)
                //    {
                //        return ResOk(false, payresult.Item4);
                //    }
                //    smqPayCode = Convert.ToString(payresult.Item3);
                //}

                List<string> idList = new List<string>();
                if (!string.IsNullOrEmpty(CouponRecord_ID) && idArray != null)
                {
                    for (int i = 0; i < idArray.Length; i++)
                    {
                        var value = idArray[i];
                        if (!string.IsNullOrEmpty(value)) idList.Add(value);
                    }
                    CouponRecord_ID = string.Join(",", idList);
                }
                List<Model.CouponRecord> couponList = null;
                if (!string.IsNullOrEmpty(CouponRecord_ID))
                {
                    couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $"CouponRecord_ID in({CouponRecord_ID})");
                }

                //查询车辆信息、车牌类型、车牌颜色
                Model.CarCardType cct = null;
                Model.Owner owner = null;
                Model.Car car = BLL.Car.GetEntityByCarNo(po.ParkOrder_CarNo);
                if (car != null)
                {
                    cct = BLL.CarCardType.GetEntity(car.Car_TypeNo);
                    owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
                }

                List<Model.CouponRecordUse> uList = uselist == null ? new List<Model.CouponRecordUse>() : TyziTools.Json.ToModel<List<Model.CouponRecordUse>>(uselist);

                List<string> noList = Utils.GetRandomLst(2);

                var PayOrder_Money = Utils.StrToDecimal(receAmount, 0);
                var PayOrder_PayedMoney = Utils.StrToDecimal(paidAmount, 0);
                var chuzhiMoney = Utils.StrToDecimal(chuzhiamount, 0);
                PayOrder_Money = PayOrder_Money - chuzhiMoney;

                ChargeModels.PayResult payResult = CalcCache.Get("OrderPrice:" + po.ParkOrder_No);
                ChargeModels.PayResult notModifiedResult = payResult != null ? DeepCloner.Clone(payResult) : null;

                //支付订单
                Model.PayColl paycOLL = null;
                List<Model.PayOrder> payOrderList = new List<Model.PayOrder>();
                List<Model.PayPart> payPartList = new List<Model.PayPart>();

                if (couponList != null)
                {
                    Model.CouponRecord uCouponModel = couponList.Find(c => c.CouponRecord_Other == 1);
                    if (uCouponModel != null && PayOrder_PayedMoney == 0)
                    {
                        paytypNo = Model.EnumPayType.chargingPilePayment.ToString();//支付类型
                    }
                }


                if (payResult == null)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"后台支付[SendNoticeForPayed]:{ParkOrder_No}");
                    Model.PayOrder payModel = new Model.PayOrder();
                    payModel.PayOrder_ParkOrderNo = ParkOrder_No;
                    payModel.PayOrder_Time = DateTimeHelper.GetNowTime();
                    payModel.PayOrder_CarNo = po.ParkOrder_CarNo;
                    payModel.PayOrder_Status = 0;
                    payModel.PayOrder_PayedTime = null;
                    payModel.PayOrder_PayTypeCode = paytypNo;
                    payModel.PayOrder_PayType = 0;
                    payModel.PayOrder_CouponRecordNo = couponList == null ? "" : string.Join(",", couponList.Select(x => x.CouponRecord_No)); ;
                    payModel.PayOrder_UserNo = "";
                    payModel.PayOrder_ParkKey = parking.Parking_Key;
                    payModel.PayOrder_ParkNo = parking.Parking_No;
                    payModel.PayOrder_OrderTypeNo = cct == null ? Convert.ToString((int)Common.EnumOrderType.Temp) : CarTypeHelper.GetOrderType(cct.CarCardType_Category, true);
                    payModel.PayOrder_EnterTime = po.ParkOrder_EnterTime;
                    payModel.PayOrder_TempTimeCount = Utils.ObjectToInt(parktimemin, 0);
                    payModel.PayOrder_TimeCountDesc = Utils.DateDiffStr(payModel.PayOrder_TempTimeCount.Value * 60);
                    payModel.PayOrder_CarCardTypeNo = po.ParkOrder_CarCardType;
                    payModel.PayOrder_CarTypeNo = po.ParkOrder_CarType;
                    payModel.PayOrder_DiscountMoney = uList.Sum(x => x.DiscountMoney);
                    payModel.PayOrder_ParkAreaNo = po.ParkOrder_ParkAreaNo;
                    payModel.PayOrder_PayedTime = DateTimeHelper.GetNowTime();
                    payModel.PayOrder_Status = 1;//已支付
                    payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                    payModel.PayOrder_OperatorName = lgAdmins?.Admins_Name;
                    payModel.PayOrder_Account = lgAdmins?.Admins_Account;
                    payModel.PayOrder_AdminID = lgAdmins?.Admins_ID;
                    payModel.PayOrder_Desc = remark;

                    if (chuzhiMoney > 0)
                    {
                        payModel.PayOrder_No = "PO" + noList[0] + parking.Parking_Key;
                        payModel.PayOrder_Money = chuzhiMoney;
                        payModel.PayOrder_PayedMoney = chuzhiMoney;
                        payModel.PayOrder_DiscountMoney = null;
                        payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmins, null, null, null, null, null, null, null, null, null, null, null, null, null, remark);
                        payOrderList.Add(payModel);
                        payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(null, payModel));
                    }

                    //手动改价后，支付总金额不等于 实收金额+优惠金额
                    payModel.PayOrder_No = "PO" + noList[1] + parking.Parking_Key;
                    payModel.PayOrder_Money = PayOrder_Money;
                    payModel.PayOrder_PayedMoney = PayOrder_PayedMoney;
                    payModel.PayOrder_DiscountMoney = uList.Sum(x => x.DiscountMoney);
                    payModel = BLL.PayOrder.CreatePayOrder(true, payModel, parking.Parking_Key, null, lgAdmins, null, null, null, null, null, null, null, null, null, null, null, null, null, remark);
                    //手动改价后，生成手动改价明细
                    payPartList.AddRange(BLL.CommonBLL.CreatePayPartList(DateTimeHelper.GetNowTime(), payModel, 0, payResult, po, lgAdmins, null, null, orderDetailList));
                    //生成手动改价明细后，重新调整优惠金额
                    payModel.PayOrder_DiscountMoney = payModel.PayOrder_Money - payModel.PayOrder_PayedMoney;
                    payOrderList.Add(payModel);
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Payment, $"后台支付[{ParkOrder_No}]:{remark}", SecondIndex.InParkRecord);
                    payResult.payedamount = PayOrder_PayedMoney;
                    paycOLL = BLL.CommonBLL.AddPayOrder(DateTimeHelper.GetNowTime(), payResult, po.ParkOrder_No, lgAdmins, "", paytypNo, po.ParkOrder_ParkAreaNo, 2, DateTimeHelper.GetNowTime(), 1, (PayOrder_PayedMoney + chuzhiMoney > 0 ? "" : remark), false);
                    if (paycOLL != null)
                    {
                        if (paycOLL.payOrderList != null && paycOLL.payOrderList.Count > 0) paycOLL.payOrderList[0].PayOrder_Desc = remark;
                        if (paycOLL.payPartList != null) payPartList.AddRange(paycOLL.payPartList);
                        payOrderList = paycOLL.payOrderList;
                        if (payOrderList.Count > 1 && payOrderList[1].PayOrder_Money == payResult.chuzhiamount)
                        {
                            payOrderList[1].PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                        }
                    }
                }

                //周期处理
                List<Model.HoursTotal> hoursTotals = null;
                if (payResult != null && payResult.list != null && orderDetailList != null && orderDetailList.Count > 0)
                {
                    payResult.list = BLL.CommonBLL.ApportionedAmountByPayPart(payResult.list, payPartList, po.ParkOrder_No);
                    orderDetailList.ForEach(item =>
                    {
                        var pdItem = payResult.list.Where(x => x.orderdetailno == item.OrderDetail_No).FirstOrDefault();
                        if (pdItem != null && !string.IsNullOrEmpty(pdItem.orderdetailno))
                        {
                            item.OrderDetail_NextCycleTime = pdItem.nextcycletime;//下一次周期生效时间
                            item.Orderdetail_CycleMoney = pdItem.NextCyclePaidFees;//下一次停车，周期已累积支付总金额
                            item.Orderdetail_CycleFreeMin = pdItem.nextcyclefreemin;//下一次停车，周期已累积免费分钟
                            //item.Orderdetail_UseFreeMin = pdItem.;//下一次停车，周期已累积免费分钟
                            if (!string.IsNullOrEmpty(pdItem.nexthourscontent) && pdItem.nexthourstime != null)
                            {
                                hoursTotals = hoursTotals ?? new List<Model.HoursTotal>();
                                hoursTotals.Add(new Model.HoursTotal()
                                {
                                    HoursTotal_No = item.OrderDetail_No,
                                    HoursTotal_CarNo = item.OrderDetail_CarNo,
                                    HoursTotal_ParkOrderNo = item.OrderDetail_ParkOrderNo,
                                    HoursTotal_CarType = item.OrderDetail_CarType,
                                    HoursTotal_CarCardType = item.OrderDetail_CarCardType,
                                    HoursTotal_ParkAreaNo = item.OrderDetail_ParkAreaNo,
                                    HoursTotal_EnterTime = item.OrderDetail_EnterTime,
                                    HoursTotal_PayTime = DateTime.Now,
                                    HoursTotal_BeginTime = pdItem.nexthourstime,
                                    HoursTotal_Content = pdItem.nexthourscontent,
                                });
                            }
                            item.Orderdetail_UseFreeMin = Utils.ObjectToInt(item.Orderdetail_UseFreeMin, 0) + Utils.ObjectToInt(pdItem.currentfreemin, 0);
                            item.OrderDetail_IsSettle = 1;//结算状态
                            if (pdItem.calcbegintime != null)
                            {
                                item.OrderDetail_CurrCalcTime = pdItem.calcbegintime;//当前计费开始时间
                            }
                            item.OrderDetail_TotalAmount = pdItem.payedamount;//出场总金额
                        }
                    });
                }

                //优惠券处理
                List<Model.CouponRecord> couponPayList = null;
                if (!string.IsNullOrEmpty(CouponRecord_ID) && couponList != null && couponList.Count > 0)
                {
                    couponPayList = new List<Model.CouponRecord>();
                    couponList.ForEach(x =>
                    {
                        Model.CouponRecordUse uModel = uList.Find(y => y.CouponRecord_No == x.CouponRecord_No);
                        Model.CouponRecord crModel = new Model.CouponRecord();
                        crModel.CouponRecord_No = x.CouponRecord_No;
                        x.CouponRecord_Status = crModel.CouponRecord_Status = 1;
                        x.CouponRecord_UsedTime = crModel.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                        x.CouponRecord_Paid = crModel.CouponRecord_Paid = uModel != null ? uModel.DiscountMoney : 0;
                        couponPayList.Add(crModel);
                    });
                }

                //车辆储值金额处理
                if (chuzhiMoney > 0)
                {
                    if (owner != null)
                    {
                        owner.Owner_Balance = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - chuzhiMoney;
                        if (owner.Owner_Balance < 0) owner.Owner_Balance = 0;
                    }
                }

                List<Model.PayOrder> payorderList = BLL.PayOrder.GetAllEntity("PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{po.ParkOrder_No}' and PayOrder_Status=1");
                if (po.ParkOrder_TotalAmount == null) po.ParkOrder_TotalAmount = payOrderList.Sum(x => x.PayOrder_Money);
                else po.ParkOrder_TotalAmount = payOrderList.Sum(x => x.PayOrder_Money) + (payorderList?.Sum(x => x.PayOrder_Money) ?? 0);

                if (po.ParkOrder_TotalPayed == null) po.ParkOrder_TotalPayed = payOrderList.Sum(x => x.PayOrder_PayedMoney);
                else po.ParkOrder_TotalPayed = payOrderList.Sum(x => x.PayOrder_PayedMoney) + (payorderList?.Sum(x => x.PayOrder_PayedMoney) ?? 0);

                Model.PayColl payColl = new Model.PayColl() { payOrderList = payOrderList, payPartList = payPartList };

                bool isFollow = false;
                if (po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow)
                {
                    isFollow = true;
                    po.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
                }

                po.ParkOrder_PayScene = (int)Model.EnumPayScene.Centre;

                //充电滞留数据更新为已支付

                if (payResult != null && payResult.penaltylist != null && payResult.penaltyamount > 0)
                {
                    List<Model.DetentionPenalty> penaltyList = TyziTools.Json.ToObject<List<Model.DetentionPenalty>>(TyziTools.Json.ToString(payResult.penaltylist));
                    penaltyList.ForEach(item => { item.DetentionPenalty_PayStatus = 1; item.DetentionPenalty_ParkOrderNo = po.ParkOrder_No; });
                    payColl.penaltyList = penaltyList;
                }

                Model.ControlEvent cev = null;
                if (isFollow)
                {
                    cev = BLL.ControlEvent.GetEntityByOrderNo(po.ParkOrder_No);
                    if (cev != null) cev.ControlEvent_Status = 4;
                }

                //更新数据
                var result = BLL.CommonBLL.PaySuccess(po, payColl, couponPayList, car, owner, orderDetailList, cev, createLedger: true, hoursTotals: hoursTotals, payResults: new List<ChargeModels.PayResult>() { notModifiedResult });
                if (result)
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Payment, $"订单号:{po.ParkOrder_No} 车牌:{po.ParkOrder_CarNo} 支付成功", SecondIndex.InParkRecord);
                    try
                    {
                        if (isFollow || po?.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close)
                        {
                            if (cev != null)
                            {
                                //上报已追缴事件到云平台
                                BLL.PushEvent.CarFollowUpdate(
                                    parking.Parking_Key
                                    , cev.ControlEvent_No
                                    , cev.ControlEvent_CarNo
                                    , cev.ControlEvent_ParkOrderNo
                                    , cev.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                    , cev.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                    , "2"
                                    , cev.ControlEvent_AdminName ?? ""
                                    , cev.ControlEvent_BigImg ?? ""
                                    , cev.ControlEvent_Video ?? ""
                                    , cev.ControlEvent_Remark
                                    , cev.ControlEvent_Money?.ToString() ?? "0",
                                    1
                                );
                            }


                        }
                    }
                    catch (Exception e)
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Payment, $"订单号:{po.ParkOrder_No} 车牌:{po.ParkOrder_CarNo} 支付成功后，上报已追缴事件发生异常:{e.ToString()}", SecondIndex.InParkRecord);
                    }

                    try
                    {
                        PayReq pr = new PayReq(po, payColl, couponList, car, owner, orderDetailList, 0, 0, null, "", true, isPlamtPay: true);
                        Push(Model.API.PushAction.Edit, pr, new List<Model.SentryHost>(), "paysuccess", dataType: DataTypeEnum.InParkRecord, Desc: $"后台支付{po.ParkOrder_CarNo}");

                        payOrderList.ForEach(pay =>
                        {
                            BLL.PushEvent.PayOrder(parking.Parking_Key, po, pay, couponList);
                        });
                    }
                    catch (Exception e)
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Payment, $"订单号:{po.ParkOrder_No} 车牌:{po.ParkOrder_CarNo} 支付成功后，分发上报数据发生异常:{e.ToString()}", SecondIndex.InParkRecord);
                    }

                    return ResOk(true, "支付成功", po.ParkOrder_No);
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Payment, $"订单号:{po.ParkOrder_No} 车牌:{po.ParkOrder_CarNo} 支付失败", SecondIndex.InParkRecord);
                    return ResOk(false, "支付失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Payment, $"订单号[{ParkOrder_No}]清缴费用发生异常:{ex.ToString()}", SecondIndex.InParkRecord);
                return ResOk(false, ex.Message);
            }
        }

        #endregion


        /// <summary>
        /// 批量删除停车订单
        /// </summary>
        public IActionResult GetBathDelete(string jsonModel)
        {
            string type = "0";
            try
            {
                if (!Powermanage.PowerCheck("InParkRecord", PowerEnum.DelBind.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrder obj = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (obj == null)
                {
                    return ResOk(false, "参数错误");
                }
                if (obj.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间始不能为空"); }
                if (obj.ParkOrder_OutTime == null) { return ResOk(false, "入场时间止不能为空"); }
                if (obj.ParkOrder_EnterTime > obj.ParkOrder_OutTime) { return ResOk(false, "入场时间始不能大于入场时间止"); }

                type = obj.ParkOrder_CarType ?? "0";//0-第一次确定删除只查询数据条目，1-第二次确定则真正删除
                if (type == "1")
                {
                    List<Model.ParkOrder> poList = BLL.ParkOrder.GetAllEntity("ParkOrder_No,ParkOrder_CarNo,ParkOrder_Remark,ParkOrder_StatusNo,ParkOrder_CarCardTypeName,ParkOrder_EnterTime,ParkOrder_OutTime", $"ParkOrder_StatusNo=200 and ParkOrder_EnterTime>='{obj.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' and ParkOrder_EnterTime<='{obj.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}'");
                    var res = BLL.ParkOrder.CloseList(obj.ParkOrder_EnterTime, obj.ParkOrder_OutTime);
                    //_ =  CustomThreadPool.WebTaskPool?.QueueTask(null, () => { LinuxApi.SendServiceReboot(ServicesEnum.Sentry); });
                    if (res > 0)
                    {
                        if (AppBasicCache.IsSendTcp) Push(Model.API.PushAction.Act, (obj.ParkOrder_EnterTime, obj.ParkOrder_OutTime), new List<Model.SentryHost>(), "BathDelParkOrder", dataType: DataTypeEnum.InParkRecord, Desc: $"批量删除{obj.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}-{obj.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}");

                        DataCache.ParkOrder.Clear();
                        
                        _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, async () =>
                        {
                            try
                            {
                                foreach (var model in poList)
                                {

                                    var order = model;

                                    var parts = new List<string>
                                    {
                                        $"停车订单编号：{order.ParkOrder_No ?? "未知"}",
                                        $"车牌：{order.ParkOrder_CarNo ?? "未知"}",
                                        $"车型：{order.ParkOrder_CarCardTypeName ?? "未知"}",
                                        $"状态：{EnumHelper.GetParkOrderStatusName(order.ParkOrder_StatusNo)}"
                                    };

                                    if (order.ParkOrder_EnterTime != null)
                                        parts.Add($"入场时间：{order.ParkOrder_EnterTime:yyyy-MM-dd HH:mm:ss}");

                                    if (order.ParkOrder_OutTime != null)
                                        parts.Add($"出场时间：{order.ParkOrder_OutTime:yyyy-MM-dd HH:mm:ss}");

                                    string message = string.Join("，", parts);

                                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, message, SecondIndex.InParkRecord);

                                    if (model.ParkOrder_StatusNo == 204)
                                    {
                                        var cvModel = BLL.ControlEvent.GetEntityByOrderNo(model.ParkOrder_No);
                                        if (cvModel != null)
                                        {
                                            cvModel.ControlEvent_Status = 1;
                                            var r = BLL.ControlEvent._UpdateByModelByNo(cvModel);
                                            if (r < 0)
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{cvModel.ControlEvent_CarNo}更改跟车事件状态失败！");
                                            }

                                            BLL.PushEvent.CarFollowUpdate(
                                            parking.Parking_Key
                                            , cvModel.ControlEvent_No
                                            , cvModel.ControlEvent_CarNo ?? ""
                                            , cvModel.ControlEvent_ParkOrderNo ?? ""
                                            , (cvModel.ControlEvent_Time == null ? "" : cvModel.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss"))
                                            , cvModel.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                            , BLL.CarFollowUpdate_Status.Ignore
                                            , lgAdmins?.Admins_Name
                                            , cvModel.ControlEvent_BigImg ?? ""
                                            , cvModel.ControlEvent_Video ?? ""
                                            , cvModel.ControlEvent_Remark
                                            , "0");
                                        }
                                    }

                                    if (model.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In || model.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow)
                                    {
                                        model.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                                        BLL.PushEvent.CloseCar(parking.Parking_Key, model, "场内关闭");
                                    }

                                    //刷新弹窗
                                    SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ParkOrder_CarNo, null, CloseNoInPark: false);
                                    if (!string.IsNullOrEmpty(model.ParkOrder_No))
                                    {
                                        CalcCache.Del("OrderPrice:" + model.ParkOrder_No);
                                        CalcCache.Del($"OrderPrice1:" + model.ParkOrder_No);
                                        CalcCache.Del($"OrderPrice2:" + model.ParkOrder_No);
                                        CalcCache.Del($"OrderPrice3:" + model.ParkOrder_No);
                                        CalcCache.Del($"OrderPrice4:" + model.ParkOrder_No);
                                        CalcCache.Del("OrderPriceAutoPay:" + model.ParkOrder_No);
                                    }
                                    await Task.Delay(10);
                                }
                                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.BatchDelete, $"{obj.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")} ～ {obj.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}，停车订单{poList.Count}条", SecondIndex.InParkRecord);
                                BLL.PushEvent.SendParkSpace(parking.Parking_No);
                            }
                            catch (Exception e)
                            {
                                BLL.SystemLogs.AddLog(lgAdmins, "批量删除停车订单", $"批量删除停车订单上报云平台异常：{e.ToString()}");
                            }
                        });

                        return ResOk(true, "删除成功");
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.BatchDelete, $"批量删除停车订单失败:{obj.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")} ～ {obj.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}", SecondIndex.InParkRecord);
                        //_ =  CustomThreadPool.SyncTaskPool?.QueueTask(null, () => { LinuxApi.SendServiceReboot(ServicesEnum.Sentry); });
                        return ResOk(false, "批量删除失败，请缩短时间范围后重试");
                    }
                }
                else
                {
                    List<Model.ParkOrder> poList = BLL.ParkOrder.GetAllEntity("ParkOrder_No", $"ParkOrder_EnterTime>='{obj.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}' and ParkOrder_EnterTime<='{obj.ParkOrder_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss")}'");
                    return ResOk(true, "查询成功", poList.Count);
                }
            }
            catch (Exception ex)
            {
                //if (type == "1") { _ =  CustomThreadPool.SyncTaskPool?.QueueTask(null, () => { LinuxApi.SendServiceReboot(ServicesEnum.Sentry); }); }
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.BatchDelete, $"批量删除停车订单发生异常:{ex.ToString()}", SecondIndex.InParkRecord);
                return ResOk(false, ex.Message);
            }
        }
    }
}