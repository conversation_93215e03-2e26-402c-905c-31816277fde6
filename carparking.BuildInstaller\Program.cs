﻿using carparking.BuildInstaller.Flows;
using carparking.Installer.Compressed;
using Newtonsoft.Json;
using Renci.SshNet;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.Loader;
using System.Text;
using System.Text.RegularExpressions;
using static carparking.BuildInstaller.PVInvoke;

namespace carparking.BuildInstaller
{
    public class Program
    {

        /// <summary>
        /// 系统版本号只能输入数字
        /// </summary>
        static readonly string appVersion = "10.360";

        /// <summary>
        /// 日期
        /// </summary>
        static readonly string appDate = $"{DateTime.Now:yyMMddHHmmss}";

        /// <summary>
        /// 非标单号
        /// </summary>
        static readonly string appFB = "";

        /// <summary>
        /// 打包加密狗协议
        /// </summary>
        static readonly int iXieYi = 0;

        /// <summary>
        /// 打包加密狗类型
        /// </summary>
        static readonly int iType = 0;

        /// <summary>
        /// 加密狗子客户代码
        /// </summary>
        static readonly int iSonType = 0;

        /// <summary>
        /// 发布linux-arm64的安装包时，需要上传到服务器的路径
        /// </summary>
        static string appLinuxArm64Path = "/app/b30_bll_package";

        /// <summary>
        /// 发布linux-arm64的安装包时，主机地址
        /// </summary>
        static readonly string appLinuxArm64Host = "*************";

        /// <summary>
        /// 发布linux-arm64的安装包时，主机端口
        /// </summary>
        /// <remarks>默认端口22</remarks>
        static readonly int appLinuxArm64Port = 22;

        /// <summary>
        /// 发布linux-arm64的安装包时，主机用户名
        /// </summary>
        static readonly string appLinuxArm64UserName = "root";

        /// <summary>
        /// 发布linux-arm64的安装包时，主机密码
        /// </summary>
        static readonly string appLinuxArm64Password = "admin123";

        /// <summary>
        /// 构建安装包程序入口
        /// </summary>
        /// <param name="args">入参</param>
        static void Main(string[] args)
        {
            string originalPubVarContent = null;
            string pubVarPath = string.Empty;

            try
            {
                // 注册控制台关闭事件处理程序
                PVInvoke.SetConsoleCtrlHandler(new HandlerRoutine(ConsoleCtrlHandler), true);
                SettingUtil.LoadSetting();
                //设置控制台样式
                Console.Title = "停车场安装包构建工具";
                //设置控制台输出编码
                Console.OutputEncoding = Encoding.UTF8;
                Console.ForegroundColor = ConsoleColor.Blue;
                Console.WriteLine("停车场安装包构建工具");
                Console.WriteLine("==========================================================================");

                //请选择构建的安装包类型
                //1.构建停车场T30的X64安装包
                //2.构建停车场T30的X86安装包
                //3.构建停车场B30的X64安装包
                //4.构建停车场B30的X86安装包
                Console.WriteLine("请选择构建的安装包类型：");
                //Console.ForegroundColor = ConsoleColor.Red;
                //Console.WriteLine("T30");
                //Console.ForegroundColor = ConsoleColor.Blue;
                //Console.WriteLine("      1.构建停车场T30的X64安装包");
                //Console.WriteLine("      2.构建停车场T30的X86安装包");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("B30 PC");
                Console.ForegroundColor = ConsoleColor.Blue;
                Console.WriteLine("      3.构建 B30安装包 (PC x64)                 Windows操作系统B30软件");
                Console.WriteLine("      4.构建 B30安装包 (PC x86)                 Windows操作系统B30软件");
                Console.WriteLine(" ");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("B30盒子");
                Console.ForegroundColor = ConsoleColor.Blue;
                Console.WriteLine("      5.构建 B30盒子-B30模式-BOX-B30 (B30)   B30本地数据中心,连接智慧停车平台 [F735680V00T180]");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(" ");
                Console.WriteLine("智管云盒");
                Console.ForegroundColor = ConsoleColor.Blue;
                Console.WriteLine("      6.构建 智管云盒-云边模式-BOX-ZG02 (ZG.C)  直连智联云平台 [F730000U00T180]");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(" ");
                Console.WriteLine("停车盒子");
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine("      8.构建 停车盒子-国际版-BOX-B30-IV (B30.IV) 国外语言包，不连平台 [F735680V01T180]");
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine(" ");
                Console.WriteLine("智联云盒");
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine("      9.构建 智联云盒-BOX-ZL01 (ZL.G)           支持我司相机、第三方相机, MQTT连第三方平台 [F735680V03T180]");
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine(" ");
                Console.ForegroundColor = ConsoleColor.White;
                Console.WriteLine("      10.退出程序");
                Console.ForegroundColor = ConsoleColor.Blue;
                Console.WriteLine("==========================================================================");

                var isStepCity = true;//是否部署WEB城市服务

                Console.ForegroundColor = ConsoleColor.Magenta;
                Console.WriteLine(" ");
                Console.WriteLine("请选择打包B30系统的版本：");
            tryagain:
                //获取用户输入的安装包类型
                var input = Console.ReadLine();
                if (!int.TryParse(input, out var type) && type < 1 && type > 10)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("输入的打包的版本不正确，请重新输入！");
                    Console.ForegroundColor = ConsoleColor.Green;
                    goto tryagain;
                }

                if (type != 3 && type != 4)
                {
                    //Console.ForegroundColor = ConsoleColor.Magenta;
                    //Console.WriteLine(" ");
                    //Console.WriteLine($"请选择服务器打包节点：1 - 打包旧MYSQL密码节点(V3.5以下)，2 - 打包新MYSQL密码节点(v3.6以上)");
                //tryagain3:
                    //Console.ForegroundColor = ConsoleColor.Green;
                    //var input3 = Console.ReadLine();
                    //if (input3 != "1" && input3 != "2")
                    //{
                    //    Console.ForegroundColor = ConsoleColor.Red;
                    //    Console.WriteLine("选择服务器打包目录路径不正确，请重新输入！");
                    //    Console.ForegroundColor = ConsoleColor.Green;
                    //    goto tryagain3;
                    //}
                    //appLinuxArm64Path = input3 == "2" ? "/lrt/b30_bll_package" : appLinuxArm64Path;

                    appLinuxArm64Path = "/lrt/b30_bll_package";

                    Console.ForegroundColor = ConsoleColor.Magenta;
                    Console.WriteLine(" ");
                    Console.WriteLine($"是否部署城市服务：1 - 是，2 - 否");
                tryagain4:
                    Console.ForegroundColor = ConsoleColor.Green;
                    var input4 = Console.ReadLine();
                    if (input4 != "1" && input4 != "2")
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine("选择部署城市服务不正确，请重新输入！");
                        Console.ForegroundColor = ConsoleColor.Green;
                        goto tryagain4;
                    }
                    isStepCity = input4 == "1" ? true : false;
                }

                var bType = 0;//0-linuxArm64,1-linux64
                if (type >= 5 && type <= 9)
                {
                    Console.ForegroundColor = ConsoleColor.Magenta;
                    Console.WriteLine(" ");
                    Console.WriteLine("请输入系统安装类型（0-Linux Arm64，1-Linux64）：");
                tryagin2:
                    var input2 = Console.ReadLine();
                    if (!int.TryParse(input2, out bType) && bType < 0 && bType > 1)
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine("输入的系统安装类型不正确，请重新输入！");
                        Console.ForegroundColor = ConsoleColor.Green;
                        goto tryagin2;
                    }
                }

                Console.ForegroundColor = ConsoleColor.Magenta;
                Console.WriteLine(" ");
                Console.WriteLine("是否构建专用版本 (会修改 bAuthorization)？ 1 - 是，2 - 否");
            tryagain5:
                Console.ForegroundColor = ConsoleColor.Green;
                var input5 = Console.ReadLine();
                if (input5 != "1" && input5 != "2")
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("选择不正确，请重新输入！");
                    Console.ForegroundColor = ConsoleColor.Green;
                    goto tryagain5;
                }
                bool isDedicatedBuild = input5 == "1";
                string versionSuffix = isDedicatedBuild ? "-专用" : "-通用";

                //判断是否退出
                if (type == 10)
                {
                    return;
                }

                Console.ForegroundColor = ConsoleColor.DarkGreen;
                var stopwatch = new Stopwatch();
                stopwatch.Start();

                //正在检测电脑环境信息
                Console.WriteLine("正在检测电脑环境信息...");
                var vsInstallDir = string.Empty;
                try
                {
                    //获取vs安装目录
                    vsInstallDir = Environment.GetEnvironmentVariable("VSAPPIDDIR");
                }
                catch { }

                if (!string.IsNullOrWhiteSpace(vsInstallDir))
                {
                    SettingUtil.GetInstance.VsPath = vsInstallDir;
                    SettingUtil.SaveSetting(SettingUtil.GetInstance);
                }
                else
                {
                    vsInstallDir = SettingUtil.GetInstance.VsPath;
                }

                if (string.IsNullOrWhiteSpace(vsInstallDir))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("未找到VSINSTALLDIR环境变量！");
                    Console.ForegroundColor = ConsoleColor.Green;
                    return;
                }

                //截取到Common7
                vsInstallDir = vsInstallDir[..vsInstallDir.LastIndexOf("Common7", StringComparison.Ordinal)];

                Console.WriteLine($"vs的安装目录：{vsInstallDir}");

                //判断vs的开发者命令提示符是否存在
                var vsDeveloperCommandPrompt = Path.Combine(vsInstallDir, "Common7", "Tools", "VsDevCmd.bat");
                if (!File.Exists(vsDeveloperCommandPrompt))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("未找到VsDevCmd.bat文件！");
                    Console.ForegroundColor = ConsoleColor.Green;
                    return;
                }

                Console.WriteLine($"vs的开发者命令提示符路径：{vsDeveloperCommandPrompt}");

                //获取当前解决方案的目录 SolutionDir
                var solutionDir = Environment.CurrentDirectory;
                //截取到整个解决方案的目录,当前项目名称carparking.BuildInstaller
                solutionDir = solutionDir[..solutionDir.LastIndexOf("carparking.BuildInstaller", StringComparison.Ordinal)];
                Console.WriteLine($"当前解决方案的目录：{solutionDir}");

                //获取 Advanced Installer 的安装目录
                //var advancedInstallerInstallDir = RegistryExtensions.QueryAdvancedInstallerInstallPath();
                //if (string.IsNullOrWhiteSpace(advancedInstallerInstallDir) && type != 5 && type != 6)
                //{
                //    Console.ForegroundColor = ConsoleColor.Red;
                //    Console.WriteLine("未找到Advanced Installer的安装目录！");
                //    Console.ForegroundColor = ConsoleColor.Green;
                //    return;
                //}

                //获取 Advanced Installer 的执行文件
                //var advancedInstallerExe = string.Empty;
                //if (type != 5 && type != 6)
                //{
                //    advancedInstallerExe = Path.Combine(advancedInstallerInstallDir, "bin", "x86", "advinst.exe");
                //    if (!File.Exists(advancedInstallerExe))
                //    {
                //        Console.ForegroundColor = ConsoleColor.Red;
                //        Console.WriteLine("未找到Advanced Installer的执行文件！");
                //        Console.ForegroundColor = ConsoleColor.Green;
                //        return;
                //    }
                //}

                //Console.WriteLine($"Advanced Installer的安装目录：{advancedInstallerInstallDir}");
                //Console.WriteLine($"Advanced Installer的执行文件：{advancedInstallerExe}");

                //获取发布目录
                var publicPath = Path.Combine(solutionDir, "publish");
                //获取输出安装包目录
                var installDir = Path.Combine(solutionDir, "Installer");
                //获取输出安装包目录
                //var outSetupPath = Path.Combine(solutionDir, "carparking.T30InstallPacket", "Setup Files");
                ////获取构建安装包的aip脚本路径
                //var aipPath = Path.Combine(solutionDir, "carparking.T30InstallPacket", "T30Install.aip");
                ////发布脚本默认发布的文件名称
                //var publishFileName = Path.Combine(outSetupPath, "智慧停车收费管理系统安装包.exe");
                //默认输出发布程序bat脚本路径
                var outBulidBatPath = Path.Combine(solutionDir, "carparking.BuildInstaller", "Bulid.bat");
                //默认输出打包程序bat脚本路径
                var outSetupBatPath = Path.Combine(solutionDir, "carparking.BuildInstaller", "Setup.bat");

                //发布前删除所有项目下的obj目录
                //var objDirs = Directory.GetDirectories(solutionDir, "obj", SearchOption.AllDirectories);
                //foreach (var objDir in objDirs)
                //{
                //    Directory.Delete(objDir, true);
                //    Console.WriteLine($"删除目录：{objDir}");
                //}

                //发布前判断carparking.Installer\Resources\ParkingMainApp.lz4文件是否存在，不存在创建一个空文件
                var lz4File1 = Path.Combine(solutionDir, "carparking.Installer", "Resources", "ParkingMainApp.lz4");
                if (!File.Exists(lz4File1))
                {
                    File.Create(lz4File1).Close();
                }

                //实例化发布流程IFlow
                IFlow flow = type switch
                {
                    //1 => new T30X64Flow(solutionDir, 1, "X64"),
                    //2 => new T30X86Flow(solutionDir, 2, "X86"),
                    3 => new B30X64Flow(solutionDir, 3, "X64"),
                    4 => new B30X86Flow(solutionDir, 4, "X86"),
                    5 => bType == 0 ? new B30LinuxArm64Flow(solutionDir, 5, "64.B30", bType) : new B30Linux64Flow(solutionDir, 5, "64.B30", bType),//64.ZG.B
                    6 => bType == 0 ? new B30LinuxArm64Flow(solutionDir, 6, "64.ZG.C", bType) : new B30Linux64Flow(solutionDir, 6, "64.ZG.C", bType),
                    //7 => new B30LinuxArm64Flow(solutionDir, 5, "64.B30"),
                    8 => bType == 0 ? new B30LinuxArm64Flow(solutionDir, 5, "64.B30.IV", bType) : new B30Linux64Flow(solutionDir, 5, "64.B30.IV", bType),
                    9 => bType == 0 ? new B30LinuxArm64Flow(solutionDir, 5, "64.ZL.G", bType) : new B30Linux64Flow(solutionDir, 5, "64.ZL.G", bType),
                    _ => throw new ArgumentOutOfRangeException(nameof(type), type, null)
                };

                pubVarPath = Path.Combine(solutionDir, "carparking.Config", "PubVar.cs");

                if (isDedicatedBuild)
                {
                    Console.WriteLine("为专用版本修改配置文件...");
                    originalPubVarContent = File.ReadAllText(pubVarPath);
                    var modifiedContent = Regex.Replace(originalPubVarContent, @"public static bool bAuthorization\s*=\s*false;", "public static bool bAuthorization = true;");
                    File.WriteAllText(pubVarPath, modifiedContent);
                    Console.WriteLine("配置文件修改成功。");
                }

                //更改解决方案路径下carparking.Config目录下的加密狗类型
                var dogStringContent = File.ReadAllText(pubVarPath);
                // 替换 iXieYi = 0;//加密狗类型 正则表达式匹配
                dogStringContent = Regex.Replace(dogStringContent, @"iXieYi\s*=\s*\d+;//加密狗类型", $"iXieYi = {iXieYi};//加密狗类型");
                // 替换 iType = 0;//加密狗协议 正则表达式匹配
                dogStringContent = Regex.Replace(dogStringContent, @"iType\s*=\s*\d+;//加密狗协议", $"iType = {iType};//加密狗协议");
                // 替换 iSonType = 0;//子客户代码 正则表达式匹配
                dogStringContent = Regex.Replace(dogStringContent, @"iSonType\s*=\s*\d+;//子客户代码", $"iSonType = {iSonType};//子客户代码");
                File.WriteAllText(pubVarPath, dogStringContent);

                //构建的是window平台的程序，判断并设置安装包的模式
                if (type is 1 or 2 or 3 or 4)
                {
                    //更改安装程序的安装包模式 0-T30安装包 1-B30安装包
                    var iPacketMode = ((type is 3 or 4) ? 1 : 0);
                    var installCsProjCs = Path.Combine(solutionDir, "carparking.Installer", "AppCache.cs");
                    var installCsProjCsContent = File.ReadAllText(installCsProjCs);
                    // 替换 PacketMode = 1;
                    installCsProjCsContent = Regex.Replace(installCsProjCsContent, @"PacketMode\s*=\s*\d+;", $"PacketMode = {iPacketMode};");
                    File.WriteAllText(installCsProjCs, installCsProjCsContent);

                    //拷贝图片资源
                    string resourceDir;
                    if (iPacketMode is 0)
                    {
                        resourceDir = Path.Combine(solutionDir, "carparking.BuildInstaller", "Resources", "T30");
                    }
                    else
                    {
                        resourceDir = Path.Combine(solutionDir, "carparking.BuildInstaller", "Resources", "B30");
                    }

                    var allFiles = Directory.GetFiles(resourceDir, "*.*", SearchOption.AllDirectories);
                    //覆盖到carparking.Installer 的Resources目录下
                    foreach (var file in allFiles)
                    {
                        var fileName = Path.GetFileName(file);
                        var targetDir = Path.Combine(solutionDir, "carparking.Installer", "Resources", fileName);
                        File.Copy(file, targetDir, true);
                        Console.WriteLine($"拷贝资源文件：{file} 到 carparking.Installer 的Resources目录下");
                    }
                }

                //构建Bat执行文件脚本
                var bat = new StringBuilder();
                bat.AppendLine($"@echo off");
                bat.AppendLine("chcp 65001");
                //删除之前的安装包目录
                //bat.AppendLine($"if exist \"{outSetupPath}\" rd /s /q \"{outSetupPath}\" \r\n");
                //删除之前的发布目录
                bat.AppendLine($"if exist \"{publicPath}\" rd /s /q \"{publicPath}\" \r\n");
                //call vs的开发者命令提示符
                bat.AppendLine($"call \"{vsDeveloperCommandPrompt}\"");
                //切换到解决方案目录
                bat.AppendLine($"cd /d \"{solutionDir}\"");
                //为每个项目生成发布文件构建bat脚本
                bat.AppendLine(flow.BulidCmdString(new FlowContext { IsPublishCityService = isStepCity }));
                //进入到发布路径
                bat.AppendLine($"cd /d \"{publicPath}\"");
                //删除后缀.pdb的文件
                bat.AppendLine($"del /s /q *.pdb");
                //删除发布目录下的tools目录下所有后缀为.xml的文件
                bat.AppendLine($"if exist \"{publicPath}\\tools\\*.xml\" del /s /q tools\\*.xml");
                //删除发布目录下的ManageCenter目录下所有后缀为.xml的文件
                bat.AppendLine($"if exist \"{publicPath}\\ManageCenter\\*.xml\" del /s /q ManageCenter\\*.xml");
                //如果存在ManualSentryBox则，删除发布目录下的ManualSentryBox目录下所有后缀为.xml的文件
                bat.AppendLine($"if exist \"{publicPath}\\ManualSentryBox\\DSkin.xml\" del /s /q ManualSentryBox\\DSkin.xml");
                //如果存在ManualSentryBox则，删除后缀为deps.json的文件
                bat.AppendLine($"if exist \"{publicPath}\\ManualSentryBox\\*.deps.json\" del /s /q ManualSentryBox\\*.deps.json");
                //如果存在DSkin.xml,则删除
                bat.AppendLine($"if exist \"{publicPath}\\DSkin.xml\" del /s /q DSkin.xml");
                //如果存在 卸载程序.exe.config, 则删除
                bat.AppendLine($"if exist \"{publicPath}\\卸载程序.exe.config\" del /s /q 卸载程序.exe.config");

                BatExtensions.ExecuteBatAsync(bat.ToString(), outBulidBatPath).GetAwaiter().GetResult();

                //检测执行发布脚本是否成功
                if (!flow.IsBulid())
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"{flow.Name}检测发布软件不成功！");
                    Console.ForegroundColor = ConsoleColor.Green;
                    return;
                }

                //发布完成后修改程序版本号
                //1.读取发布目录的Config目录下的appsettings.json文件
                //2.修改配置节AppSettings中的ApiVersion和ApiVersion_FB值
                //3.保存文件
                var appsettingsJsonPath = Path.Combine(publicPath, "Config", "appsettings.json");
                var appsettingsJson = File.ReadAllText(appsettingsJsonPath);
                var appsettings = JsonConvert.DeserializeObject<dynamic>(appsettingsJson);
                appsettings.AppSettings.ApiVersion = appVersion;
                appsettings.AppSettings.ApiVersion_FB = $"{appDate}{(string.IsNullOrWhiteSpace(appFB) ? "" : $".{appFB}")}.{flow.Name}";
                //设置SentryMode，当type==5的时候是1 type==6的时候是 其他是0
                appsettings.AppSettings.SentryMode = (type == 5 || type == 7 || type == 8 || type == 9) ? 1 : type == 6 ? 2 : 0;
                appsettings.AppSettings.InstallType = type;

                switch (flow.InstallMode)
                {
                    case 1:
                        {
                            //清空工具包里面的多余文件，只保留后缀是.exe的文件
                            var toolsPath = Path.Combine(publicPath, "tools");
                            var toolsFiles = Directory.GetFiles(toolsPath);
                            foreach (var toolsFile in toolsFiles)
                            {
                                if (!toolsFile.EndsWith(".exe"))
                                {
                                    File.Delete(toolsFile);
                                }
                            }

                            File.WriteAllText(appsettingsJsonPath, JsonConvert.SerializeObject(appsettings, Formatting.Indented));

                            Lz4Util.DecompressThreadCount = 8;
                            var lz4File = Path.Combine(solutionDir, "carparking.Installer", "Resources", "ParkingMainApp.lz4");
                            var setupFile = Path.Combine(installDir, "车牌识别软件安装向导.exe");

                            Dictionary<string, string> dics;
                            var setupStream = File.OpenRead(setupFile);
                            using (setupStream)
                            {
                                //从指定程序集中读取资源文件目录
                                var context = new AssemblyLoadContext("SetupContext", true);
                                var setupAssembly = context.LoadFromStream(setupStream);
                                var setupResource = setupAssembly.GetManifestResourceNames();
                                //释放程序集
                                context.Unload();
                                dics = GetEmbeddedResourceInfo(setupResource, installDir);
                            }

                            Lz4Util.CompressDirectoryByLz4StreamAsync(publicPath, lz4File, K4os.Compression.LZ4.LZ4Level.L12_MAX, progress => Console.WriteLine($"{progress}"), dics).ConfigureAwait(false).GetAwaiter().GetResult();

                            //删除installFile下的所有文件
                            Directory.Delete(installDir, true);

                            //压缩完成后重新发布软件
                            bat.Clear();
                            bat.AppendLine($"@echo off");
                            bat.AppendLine("chcp 65001");
                            //call vs的开发者命令提示符
                            bat.AppendLine($"call \"{vsDeveloperCommandPrompt}\"");
                            //切换到解决方案目录
                            bat.AppendLine($"cd /d \"{solutionDir}\"");
                            bat.AppendLine(TextExtensions.GetEchoXingConent("开始构建安装包,会比较耗时..."));
                            bat.AppendLine(@$"msbuild {solutionDir}\carparking.Installer\carparking.Installer.csproj -t:rebuild -p:Configuration=Release /p:Platform=AnyCPU /p:WarningLevel=0 /verbosity:minimal /p:OutputPath={solutionDir}\Installer");
                            BatExtensions.ExecuteBatAsync(bat.ToString(), outSetupBatPath).Wait();
                            if (File.Exists(setupFile))
                            {
                                Console.ForegroundColor = ConsoleColor.Green;
                                Console.WriteLine($"\r\n{flow.Name}打包成功！");
                                Console.ForegroundColor = ConsoleColor.Green;
                                //重命名
                                var newPublishFileName = Path.Combine(installDir, $"{flow.SteupName}_v{appVersion}_{flow.Name}_{appFB}_{appDate}{versionSuffix}.exe");
                                File.Move(setupFile, newPublishFileName);

                                //除了newPublishFileName不删除，删除其他临时文件
                                var files = Directory.GetFiles(installDir);
                                foreach (var file in files)
                                {
                                    if (File.Exists(file))
                                    {
                                        if (!file.Equals(newPublishFileName, StringComparison.OrdinalIgnoreCase))
                                        {
                                            File.Delete(file);
                                        }
                                    }
                                }

                                Console.WriteLine($"重命名成功!");
                                //打开文件夹
                                Process.Start("explorer.exe", installDir);
                                Console.WriteLine($"打开文件夹成功!");
                            }
                            else
                            {
                                Console.ForegroundColor = ConsoleColor.Red;
                                Console.WriteLine($"{flow.Name}打包失败！");
                                Console.ForegroundColor = ConsoleColor.Green;
                            }

                            break;
                        }

                    case 2:
                        {
                            appsettings.ConnectionStrings.ReadDBConnectionString = $@"Database='carparking';Data Source='127.0.0.1';User Id='root';port=3306; Password='park2025@b30';charset='utf8mb4';pooling=true;Allow User Variables=True;SslMode=None;";
                            appsettings.ConnectionStrings.WriteDBConnectionString = $@"Database='carparking';Data Source='127.0.0.1';User Id='root';port=3306; Password='park2025@b30';charset='utf8mb4';pooling=true;Allow User Variables=True;SslMode=None;";
                            File.WriteAllText(appsettingsJsonPath, JsonConvert.SerializeObject(appsettings, Formatting.Indented));

                            //创建发布目录
                            if (!Directory.Exists(installDir))
                            {
                                Directory.CreateDirectory(installDir);
                            }

                            //压缩文件publish.zip
                            var zipPath = Path.Combine(installDir, "b30.zip");
                            var oldprogress = -1;
                            //把发布目录下的所有文件目录移动到b30目录下
                            string b30Path = Path.Combine(solutionDir, "TempFiles");
                            if (Directory.Exists(b30Path))
                            {
                                Directory.Delete(b30Path, true);
                            }
                            //文件夹移动不能同名目录
                            DirectoryInfo di = new DirectoryInfo(publicPath);
                            di.MoveTo(b30Path);
                            //移动回来
                            di = new DirectoryInfo(b30Path);
                            if (!Directory.Exists(publicPath))
                            {
                                Directory.CreateDirectory(publicPath);
                            }
                            di.MoveTo(Path.Combine(publicPath, "b30"));

                            ZipExtensions.CompressToZip(publicPath, zipPath, progress =>
                            {
                                if (progress != oldprogress)
                                {
                                    Console.WriteLine($"压缩进度：{progress}%");
                                    oldprogress = progress;
                                }
                            });
                            if (!File.Exists(zipPath))
                            {
                                Console.ForegroundColor = ConsoleColor.Red;
                                Console.WriteLine($"{flow.Name}压缩文件失败！");
                                Console.ForegroundColor = ConsoleColor.Green;
                                return;
                            }

                            //连接ssh上传文件到/app/b30_bll_package
                            using var client = new SshClient(appLinuxArm64Host, appLinuxArm64Port, appLinuxArm64UserName, appLinuxArm64Password);
                            client.ConnectWithRetry(3, 1000);
                            if (!client.IsConnected)
                            {
                                Console.ForegroundColor = ConsoleColor.Red;
                                Console.WriteLine($"连接{appLinuxArm64Host}失败！");
                                Console.ForegroundColor = ConsoleColor.Green;
                                return;
                            }

                            Console.WriteLine($"连接{appLinuxArm64Host}成功，正在上传文件...");

                            //执行linux命令删除/app/b30_bll_package/b30.zip
                            client.ExecuteCommandWithOutput($"cd {appLinuxArm64Path} && rm -rf b30.zip");

                            //上传文件
                            client.UploadFile(zipPath, appLinuxArm64Path);
                            //执行linux命令检测文件是否上传成功
                            client.ExecuteCommandWithOutput($"cd {appLinuxArm64Path} && ls b30.zip");

                            //读取本地build_partions-app2.sh动态设置IMAGE_PACK_DT的值

                            //F730000U00T180---------- - 智管云盒--B30模式（BOX - ZG02的ZG.B30）---原有EPS
                            //F735680V00T180---------- - 标准B30盒子（BOX - B30）
                            //F735680V01T180---------- - 停车盒子 - 国际版（BOX - B30 - IV）
                            //F730000U00T180---------- - 智管云盒云边模式（BOX - ZG02的ZG.C）
                            //F735680V03T180---------- - 智联云盒（BOX - ZL01的ZL.G）

                            //版本固定编码【不同版本之间，在盒子不能互升】
                            var typeNumber = type switch
                            {
                                5 => "F735680V00T180",
                                6 => "F730000U00T180",
                                //7 => "F735680V00T180",
                                8 => "F735680V01T180",
                                9 => "F735680V03T180",
                                _ => ""
                            };

                            var buildPartionsApp2ShPath = Path.Combine(solutionDir, "carparking.BuildInstaller", "RunSH", "build_partions-app2_1.sh");
                            var buildPartionsApp2Sh = File.ReadAllText(buildPartionsApp2ShPath);

                            // 替换IMAGE_PACK_DT=任意内容+换行正则表达式匹配
                            buildPartionsApp2Sh = Regex.Replace(buildPartionsApp2Sh, @"IMAGE_PACK_DT=.+[\n]", $"IMAGE_PACK_DT=\"{appVersion}_{typeNumber}_{appDate}\"\n", RegexOptions.Multiline);
                            //由于 Windows 和 Unix 系统之间的换行符不同导致的。Windows 使用回车符和换行符 (\r\n) 来表示换行，而 Unix 使用换行符 (\n) 来表示换行。
                            //所以在Windows下使用\r\n换行，在linux下使用\n换行
                            buildPartionsApp2Sh = buildPartionsApp2Sh.Replace("\r\n", "\n");
                            //覆盖保存文件
                            File.WriteAllText(buildPartionsApp2ShPath, buildPartionsApp2Sh);

                            //上传build_partions-app2.sh到/app/B30_bll_package 名称为 build_partions-app2_1.sh,先删除原来的文件
                            client.ExecuteCommandWithOutput($"cd {appLinuxArm64Path} && rm -rf build_partions-app2_1.sh");
                            client.UploadFile(buildPartionsApp2ShPath, appLinuxArm64Path);

                            //执行linux命令build_partions-app2.sh
                            client.ExecuteCommandWithOutput($"cd {appLinuxArm64Path} && sh build_partions-app2_1.sh" + " " + typeNumber);

                            //sz out/B30_20230817102035.img
                            client.ExecuteCommandWithOutput($"cd {appLinuxArm64Path} && sz out/B30_{appVersion}_{typeNumber}_{appDate}.img");

                            //sz out/app2_20230817102035.squash
                            client.ExecuteCommandWithOutput($"cd {appLinuxArm64Path} && sz out/app2_{appVersion}_{typeNumber}_{appDate}.squash");
                            oldprogress = -1;
                            //下载文件到本地
                            client.DownloadFile($"{appLinuxArm64Path}/out/B30_{appVersion}_{typeNumber}_{appDate}.img", installDir, progress =>
                            {
                                if (progress != oldprogress)
                                {
                                    Console.WriteLine($"img下载进度：{progress}%");
                                    oldprogress = progress;
                                }
                            });
                            client.DownloadFile($"{appLinuxArm64Path}/out/app2_{appVersion}_{typeNumber}_{appDate}.squash", installDir, progress =>
                            {
                                if (progress != oldprogress)
                                {
                                    Console.WriteLine($"squash下载进度：{progress}%");
                                    oldprogress = progress;
                                }
                            });
                            client.Disconnect();

                            //打开文件夹
                            Process.Start("explorer.exe", installDir);
                            break;
                        }
                }
                stopwatch.Stop();

                //转换成秒
                var seconds = stopwatch.ElapsedMilliseconds / 1000;
                Console.WriteLine($"本次打包总共耗时：{seconds}秒");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异常：{ex.ToString()}");
            }
            finally
            {
                if (originalPubVarContent != null)
                {
                    Console.WriteLine("正在恢复配置文件...");
                    File.WriteAllText(pubVarPath, originalPubVarContent);
                    Console.WriteLine("配置文件恢复成功。");
                }
                Console.ReadLine();
            }


        }

        /// <summary>
        /// 获取嵌入资源文件信息
        /// </summary>
        private static Dictionary<string, string> GetEmbeddedResourceInfo(string[] args, string rootPath)
        {
            var dics = new Dictionary<string, string>();
            foreach (var arg in args)
            {
                if (!arg.StartsWith("costura.")) continue;
                var s1 = arg.Substring(8);
                if (arg.EndsWith(".compressed"))
                {
                    s1 = s1.Replace(".compressed", "");
                }

                var allpath = rootPath + s1;
                if (!File.Exists(allpath)) continue;
                //获取文件标识
                var id = Lz4Util.GetFileHash(allpath);
                dics.Add(s1, id);
            }

            return dics;
        }
    }
}