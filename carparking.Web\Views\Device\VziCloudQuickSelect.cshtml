﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速选择设备</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .layui-table-cell {
            height: auto;
            line-height: 28px;
        }

        .config-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
        }

        .config-title {
            font-weight: bold;
            margin-bottom: 15px;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }

        .status-online {
            background-color: #5FB878;
            color: #fff;
        }

        .status-offline {
            background-color: #FF5722;
            color: #fff;
        }

        .layui-btn {
            height: 38px;
            line-height: 38px;
            padding: 0 18px;
            font-size: 14px;
        }

        .layui-icon {
            vertical-align: middle;
            font-size: 14px;
        }

        .layui-form-inline .layui-input-inline {
            vertical-align: middle;
        }

        .layui-inline {
            margin-right: 10px;
            margin-top: 0px;
            vertical-align: top;
        }

        .layui-form-item .layui-inline {
            margin-bottom: 5px;
            margin-right: 0;
        }

        .config-section .layui-form-item {
            margin-bottom: 0;
        }

        .config-status {
            padding: 10px 15px;
            background-color: #f8f8f8;
            border-radius: 2px;
        }

        .config-status .layui-link {
            color: #1E9FFF;
            cursor: pointer;
        }

        .config-status .layui-link:hover {
            color: #0d8bff;
        }

        /* 添加样式 */
        .layui-form-item {
            margin-bottom: 15px;
        }

        .layui-input-block {
            margin-left: 110px;
            /* 调整输入框左边距，与label对齐 */
        }

        .layui-form-label {
            width: 110px;
            padding: 8px 15px;
        }

        .layui-input-inline {
            margin-right: 0;
        }

        /* 添加折叠面板样式 */
        .layui-collapse {
            margin-bottom: 15px;
        }

        .layui-colla-title {
            background-color: #f8f8f8;
        }

        .layui-colla-content {
            padding: 15px;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <!-- 臻云平台配置部分 -->
                <div class="layui-collapse">
                    <div class="layui-colla-item">
                        <h2 class="layui-colla-title">YM01平台配置</h2>
                        <div class="layui-colla-content">
                            <form class="layui-form" id="configForm" lay-filter="configForm">
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">接口地址</label>
                                        <div class="layui-input-inline" style="width: 350px;">
                                            <input type="text" name="baseUrl" required lay-verify="required"
                                                placeholder="请输入接口地址" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">AccessKey</label>
                                        <div class="layui-input-inline" style="width: 350px;">
                                            <input type="text" name="accessKeyId" required lay-verify="required"
                                                placeholder="请输入AccessKey" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">AccessSecret</label>
                                        <div class="layui-input-inline" style="width: 350px;">
                                            <input type="password" name="accessKeySecret" required lay-verify="required"
                                                placeholder="请输入AccessSecret" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">设备组ID</label>
                                        <div class="layui-input-inline" style="width: 350px;">
                                            <input type="text" name="groupId" required lay-verify="required|number"
                                                placeholder="请输入设备组ID" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label"></label>
                                        <div class="layui-input-inline">
                                            <button class="layui-btn" lay-submit lay-filter="saveConfig">保存参数</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 配置状态显示 -->
                <div class="config-status" id="configStatus" style="display: none; margin-bottom: 20px;">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <i class="layui-icon layui-icon-ok-circle" style="color: #5FB878; margin-right: 5px;"></i>
                            <span>YM01平台已配置</span>
                            <a href="javascript:;" class="layui-link" id="editConfig" style="margin-left: 10px;">
                                <i class="layui-icon layui-icon-edit"></i>编辑配置
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 设备列表表格 -->
                <table class="layui-table" id="deviceTable" lay-filter="deviceTable"></table>
            </div>
        </div>
    </div>

    <script type="text/html" id="tableToolbar">
        <div class="layui-form layui-form-inline" lay-filter="searchForm">
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input type="text" name="name" placeholder="设备名称" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-input-inline">
                    <input type="text" name="sn" placeholder="设备SN" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-sm" lay-event="search">
                    <i class="layui-icon layui-icon-search"></i>获取设备
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="getCheckData">选择设备</button>
            </div>
        </div>
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script>
        layui.use(['form', 'table', 'element'], function () {
            var form = layui.form,
                table = layui.table,
                element = layui.element;

            // 初始化表格
            var deviceTable = table.render({
                elem: '#deviceTable',
                url: '/Device/GetVziCloudDevices',
                toolbar: '#tableToolbar',
                defaultToolbar: ['filter'],
                cols: [[
                    { type: 'radio' },
                    { field: 'sn', title: 'SN', width: 180 },
                    { field: 'name', title: '设备名称', width: 150 },
                    {
                        field: 'state', title: '状态', width: 100, templet: function (d) {
                            return d.state == 0 ?
                                '<span class="status-badge status-online">在线</span>' :
                                '<span class="status-badge status-offline">离线</span>';
                        }
                    },
                    { field: 'group_name', title: '设备组', width: 120 },
                    { field: 'local_ip', title: '内网IP', width: 130 },
                    { field: 'global_ip', title: '公网IP', width: 130 },
                    { field: 'series_name', title: '设备系列', width: 120 },
                    { field: 'soft_version', title: '软件版本', width: 100 },
                    { field: 'username', title: '登录用户名', width: 100 },
                    { field: 'password', title: '登录密码', width: 100 },
                    {
                        field: 'http_port', title: 'HTTP端口', width: 100, templet: function (d) {
                            return d.addition ? d.addition.http_port : '';
                        }
                    },
                    {
                        field: 'rtsp_port', title: 'RTSP端口', width: 100, templet: function (d) {
                            return d.addition ? d.addition.rtsp_port : '';
                        }
                    },
                    {
                        field: 'region', title: '区域', width: 200, templet: function (d) {
                            if (!d.region) return '';
                            return d.region.country_name + ' ' + d.region.region_name + ' ' + d.region.city_name;
                        }
                    },
                    { field: 'remark', title: '备注', width: 150 }
                ]],
                page: true,
                limits: [10, 20, 50, 100],
                limit: 10,
                request: {
                    pageName: 'pageIndex',
                    limitName: 'pageSize'
                },
                parseData: function (res) {
                    if (res.code != 0) {
                        layer.msg(res.m);
                        // 如果是配置相关错误，展开配置面板
                        $('.layui-colla-content').addClass('layui-show');
                    }
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data
                    };
                }
            });

            // 检查配置并显示相应界面
            function checkAndShowConfig() {
                $.ajax({
                    url: '/Device/GetVziCloudConfig',
                    type: 'GET',
                    success: function (res) {
                        if (res.success && res.data) {
                            // 使用form.val正确设置表单值
                            form.val('configForm', {
                                "baseUrl": res.data.baseUrl || 'https://open.vzicloud.com',
                                "accessKeyId": res.data.accessKeyId,
                                "accessKeySecret": res.data.accessKeySecret,
                                "groupId": res.data.groupId
                            });

                            // 修改判断逻辑，只要有accessKeyId和accessKeySecret就认为已配置
                            if (res.data.accessKeyId && res.data.accessKeySecret && res.data.groupId) {
                                $('.layui-colla-content').removeClass('layui-show');
                                $('.layui-colla-title').addClass('layui-colla-close');
                                // 配置完整，收起面板
                                element.render('collapse');  // 重新渲染collapse组件
                            } else {
                                $('.layui-colla-content').addClass('layui-show');
                                $('.layui-colla-title').removeClass('layui-colla-close');
                                // 配置完整，收起面板
                                element.render('collapse');  // 重新渲染collapse组件
                            }
                        } else {
                            // 获取配置失败，展开面板
                            element.render('collapse');  // 重新渲染collapse组件
                            $('.layui-colla-content').addClass('layui-show');
                            $('.layui-colla-title').removeClass('layui-colla-close');
                            layer.msg(res.msg || '获取配置失败');
                        }
                    }
                });
            }

            // 编辑配置按钮点击事件
            $('#editConfig').click(function () {
                $('#configStatus').hide();
                $('#configSection').show();
            });

            // 保存配置
            form.on('submit(saveConfig)', function (data) {
                $.ajax({
                    url: '/Device/SaveVziCloudConfig',
                    type: 'POST',
                    data: {
                        accessKeyId: data.field.accessKeyId,
                        accessKeySecret: data.field.accessKeySecret,
                        groupId: parseInt(data.field.groupId),
                        baseUrl: data.field.baseUrl
                    },
                    success: function (res) {
                        if (res.success) {
                            layer.msg('配置已保存');
                            $('.layui-colla-content').removeClass('layui-show');
                            // 重新加载设备列表
                            table.reload('deviceTable');
                        } else {
                            layer.msg(res.msg || '保存失败');
                        }
                    }
                });
                return false;
            });

            // 初始化时检查配置
            checkAndShowConfig();

            // 工具栏事件
            table.on('toolbar(deviceTable)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                switch (obj.event) {
                    case 'search':
                        // 先检查是否已配置
                        $.ajax({
                            url: '/Device/GetVziCloudConfig',
                            type: 'GET',
                            success: function (res) {
                                if (res.success) {
                                    // 已配置，执行搜索
                                    var formData = form.val('searchForm');
                                    table.reload('deviceTable', {
                                        where: formData,
                                        page: {
                                            curr: 1
                                        }
                                    });
                                } else {
                                    // 未配置，提示并展开配置面板
                                    layer.msg('请先配置YM01平台参数');
                                    $('.layui-colla-content').addClass('layui-show');
                                }
                            }
                        });
                        break;
                    case 'getCheckData':
                        var data = checkStatus.data[0];
                        if (!data) {
                            layer.msg('请选择一个设备');
                            return;
                        }
                        // 调用父窗口的回调函数
                        if (window.parent && window.parent.handleVziCloudDeviceSelection) {
                            window.parent.handleVziCloudDeviceSelection({
                                sn: data.sn,
                                name: data.name,
                                ip: data.local_ip,
                                username: data.username,
                                password: data.password,
                                http_port: data.addition ? data.addition.http_port : '',
                                rtsp_port: data.addition ? data.addition.rtsp_port : ''
                            });
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        }
                        break;
                }
            });

            // 加载保存的配置
            var savedConfig = JSON.parse(localStorage.getItem('vzicloud_config') || '{}');
            form.val('configForm', savedConfig);
        });
    </script>
</body>

</html>
