﻿using carparking.BLL;
using carparking.Charge;
using carparking.Common;
using carparking.Model;
using carparking.Model.API;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Pipelines.Sockets.Unofficial.Arenas;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using carparking.BLL.Cache;
using NPOI.Util;
using Dapper;
using carparking.DAL;
using SqlSugar;

namespace carparking.Web.Controllers
{
    public class OwnerController : BaseController
    {
        private ResultBase rb = new ResultBase();
        private readonly Microsoft.Extensions.Hosting.IHostEnvironment _hostingEnvironment;
        public OwnerController(Microsoft.Extensions.Hosting.IHostEnvironment hostingEnvironment)

        {
            _hostingEnvironment = hostingEnvironment;
        }

        public ActionResult Index()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.View.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public ActionResult Add()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Add.ToString(), false, true, lgAdmins))
                return new EmptyResult();
            ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
            var policy = BLL.BaseBLL._GetEntityByWhere(new Model.PolicyPark(), "*", $"");
            ViewBag.CarPrefix = policy != null ? policy.PolicyPark_CarPrefix : "";
            ViewBag.Create = Powermanage.PowerCheck("Owner", PowerEnum.Create.ToString(), false, false, lgAdmins) ? "1" : "0";
            return View();
        }

        public ActionResult Edit()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Update.ToString(), false, true, lgAdmins))
                return new EmptyResult();
            ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
            var policy = BLL.BaseBLL._GetEntityByWhere(new Model.PolicyPark(), "*", $"");
            ViewBag.CarPrefix = policy != null ? policy.PolicyPark_CarPrefix : "";
            return View();
        }

        public ActionResult Charge()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Charge.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            try
            {
                string Owner_No = Request.Query["Owner_No"];
                Model.Owner model = BLL.Owner.GetEntity(Owner_No, false) ?? new Model.Owner();
                ViewBag.Owner_Space = model?.Owner_Space;
                var parameters = new { Car_OwnerNo = Owner_No };
                var cars = BLL.Car.GetAllEntity("*", "Car_OwnerNo=@Car_OwnerNo", parameters);
                var carList = cars?.Select(x => x.Car_CarNo).ToList() ?? new List<string>();
                ViewBag.Car_CarNo = string.Join(",", carList);
            }
            catch (Exception)
            {
            }

            ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
            return View();
        }

        public IActionResult Payment()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Charge.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult PaymentDetail()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Charge.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public ActionResult Refund()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Refund.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            try
            {
                string Owner_No = Request.Query["Owner_No"];
                Model.Owner model = BLL.Owner.GetEntity(Owner_No, false) ?? new Model.Owner();
                ViewBag.Owner_Space = model?.Owner_Space;
                var parameters = new { Car_OwnerNo = Owner_No };
                var cars = BLL.Car.GetAllEntity("*", "Car_OwnerNo=@Car_OwnerNo", parameters);
                var carList = cars?.Select(x => x.Car_CarNo).ToList() ?? new List<string>();
                ViewBag.Car_CarNo = string.Join(",", carList);
            }
            catch (Exception)
            {
            }

            ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
            return View();
        }

        public ActionResult BathEditStopSpace()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Bind.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public ActionResult Import()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Add.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        #region 车位设置
        public ActionResult EditStopSpace()
        {
            return View();
        }

        public IActionResult GetStopSpace(string StopSpace_No)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Search.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");

            var data = BLL.BaseBLL._GetEntityByNo(new Model.StopSpace(), StopSpace_No);

            return ResOk(true, "", data);
        }
        #endregion

        #region 车牌绑定
        public ActionResult EditCarNo()
        {

            var policy = BLL.BaseBLL._GetEntityByWhere(new Model.PolicyPark(), "*", $"");
            ViewBag.CarPrefix = policy != null ? policy.PolicyPark_CarPrefix : "";
            return View();
        }

        #endregion

        #region 车位续期
        public ActionResult Retweet()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Retweet.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public ActionResult Vaild()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Vaild.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }
        public ActionResult BathVaild()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.BathVaild.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }
        /// <summary>
        /// 车位续期
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public IActionResult OnSpacePayCharge(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Retweet.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.SpaceCharge param = Utils.ClearModelRiskSQL<Model.SpaceCharge>(jsonModel);
                if (string.IsNullOrEmpty(param.Owner_No)) return ResOk(false, "车位编号错误");
                var model = BLL.Owner.GetEntity(param.Owner_No, false);
                if (model == null) ResOk(false, "车位信息不存在");
                var old = model.Copy();
                var parameters = new { Car_ParkingNo = parking.Parking_No, Car_OwnerNo = model.Owner_No };
                var cars = BLL.Car.GetAllEntity("*", "Car_ParkingNo=@Car_ParkingNo AND Car_OwnerNo=@Car_OwnerNo", parameters);
                if ((cars?.Count ?? 0) == 0) return ResOk(false, "车位未绑定车辆");

                Model.CarCardType cct = BLL.CarCardType.GetEntity(model.Owner_CardTypeNo);

                string logmsg = string.Empty;

                #region 生成支付订单
                Model.PayOrder payOrder = new Model.PayOrder()
                {
                    PayOrder_No = Utils.CreateNumberWith("MC") + parking.Parking_No,
                    PayOrder_ParkNo = parking.Parking_No,
                    PayOrder_ParkKey = parking.Parking_No,
                    PayOrder_OrderTypeNo = null,
                    PayOrder_ParkAreaNo = null,
                    PayOrder_ParkOrderNo = null,
                    PayOrder_Money = 0,
                    PayOrder_Time = DateTimeHelper.GetNowTime(),
                    PayOrder_Status = 1,
                    PayOrder_PayedMoney = 0,
                    PayOrder_PayedTime = DateTimeHelper.GetNowTime(),
                    PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString(),
                    PayOrder_PayType = 0,
                    PayOrder_OwnerNo = model.Owner_No,
                    PayOrder_OwnerName = model.Owner_Name,
                    PayOrder_OwnerSpace = model.Owner_Space,
                    PayOrder_CarNos = string.Join(",", cars?.Take(50).Select(x => x.Car_CarNo)),
                    PayOrder_AdminID = lgAdmins?.Admins_ID,
                    PayOrder_Account = lgAdmins?.Admins_Account,
                    PayOrder_OperatorName = lgAdmins?.Admins_Name,
                    PayOrder_Category = cct?.CarCardType_Category,
                    PayOrder_CarCardTypeNo = model.Owner_CardTypeNo,
                    PayOrder_CarNo = cars?.FirstOrDefault()?.Car_CarNo,
                    PayOrder_CarTypeNo = cars?.FirstOrDefault()?.Car_VehicleTypeNo,

                    PayOrder_UserNo = null,
                    PayOrder_CouponRecordNo = null,
                    PayOrder_Desc = null,
                    PayOrder_EnterTime = null,
                    PayOrder_TempTimeCount = null,
                    PayOrder_DayTimeCount = null,
                    PayOrder_MonthEffTime = null,
                    PayOrder_MonthBeginTime = null,
                    PayOrder_MonthEndTime = null,
                    PayOrder_TimeCountDesc = null,
                    PayOrder_PayedSN = null,

                    PayOrder_FpStatus = 0,
                    PayOrder_DiscountMoney = 0,
                    PayOrder_StoredMoney = 0,
                    PayOrder_OutReduceMoney = 0,
                    PayOrder_SelfMoney = 0
                };
                #endregion

                #region 计算延期/充值

                List<Model.Ledger> ledgerList = null;


                //按规则
                if (param.PayOrder_PayType == 1)
                {
                    if (string.IsNullOrEmpty(param.MonthRule_No)) return ResOk(false, "请选择延期/充值规则");
                    if (param.PayOrder_PayedMoney == null || param.PayOrder_PayedMoney < 0) return ResOk(false, "支付金额错误");

                    var mr = BLL.MonthRule._GetEntityByNo(new Model.MonthRule(), param.MonthRule_No);
                    if (mr == null) return ResOk(false, "延期/充值规则不存在");

                    //储值车
                    if (model.Owner_CardType == 2)
                    {
                        payOrder.PayOrder_Money = mr.MonthRule_EMoney;
                        payOrder.PayOrder_PayedMoney = param.PayOrder_PayedMoney;
                        payOrder.PayOrder_DiscountMoney = payOrder.PayOrder_Money - param.PayOrder_PayedMoney;
                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.Wallet.ToString();

                        Model.Ledger ledger = null;
                        var remainingMoney = Utils.ObjectToDecimal(model.Owner_Balance, 0) - payOrder.PayOrder_PayedMoney;
                        //if (remainingMoney > 0)
                        //{
                        ledger = new Model.Ledger()
                        {
                            Ledger_CarNo = cars?.FirstOrDefault()?.Car_CarNo,
                            Ledger_Space = model.Owner_Space,
                            Ledger_Type = 1,
                            Ledger_CardType = 2,
                            Ledger_Code = 1,
                            Ledger_Money = payOrder.PayOrder_Money,
                            Ledger_BeforeMoeny = model.Owner_Balance,
                            Ledger_Time = DateTime.Now,
                            Ledger_Remark = "延期充值"
                        };
                        //}

                        model.Owner_Balance = (model.Owner_Balance ?? 0) + mr.MonthRule_EMoney;
                        if (ledger != null)
                        {
                            ledger.Ledger_AfterMoeny = model.Owner_Balance;
                            ledgerList = new List<Model.Ledger> { ledger };
                        }
                        logmsg = $"充值{mr.MonthRule_EMoney}元,支付{param.PayOrder_PayedMoney}元";
                    }
                    //月租车
                    else if (model.Owner_CardType == 3)
                    {
                        payOrder.PayOrder_Money = mr.MonthRule_Money * model.Owner_SpaceNum;
                        payOrder.PayOrder_PayedMoney = param.PayOrder_PayedMoney;
                        payOrder.PayOrder_DiscountMoney = payOrder.PayOrder_Money - param.PayOrder_PayedMoney;
                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.OwnerCharge.ToString();
                        payOrder.PayOrder_MonthEffTime = model.Owner_EndTime.Copy();

                        DateTime? MonthBeginTime = null;//充值开始时间
                        if (mr.MonthRule_Type == 1) MonthBeginTime = DateTime.Parse(DateTime.Parse(DateTimeHelper.GetNowTime().ToString("yyyy-MM-01 00:00:00")).AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
                        else if (mr.MonthRule_Type == 2) MonthBeginTime = DateTime.Parse(DateTimeHelper.GetNowTime().AddDays(-1).ToString("yyyy-MM-dd 23:59:59"));
                        else if (mr.MonthRule_Type == 3) MonthBeginTime = DateTime.Parse(model.Owner_EndTime.Copy().Value.ToString("yyyy-MM-dd 23:59:59"));

                        payOrder.PayOrder_MonthBeginTime = DateTime.Parse(MonthBeginTime.Value.AddDays(1).ToString("yyyy-MM-dd 00:00:00"));

                        if (mr.MonthRule_Unit == 1)//充值单位 天
                            model.Owner_EndTime = MonthBeginTime.Value.AddDays(mr.MonthRule_Cycle ?? 0);
                        else if (mr.MonthRule_Unit == 2)//充值单位 月
                            model.Owner_EndTime = Utils.AddMonths(MonthBeginTime.Copy().Value, mr.MonthRule_Cycle ?? 0);
                        else if (mr.MonthRule_Unit == 3)//充值单位 年
                            model.Owner_EndTime = MonthBeginTime.Value.AddYears(mr.MonthRule_Cycle ?? 0);

                        //启用赠送
                        if (mr.MonthRule_AddEnable == 1)
                        {
                            if (mr.MonthRule_AddUnit == 1)//赠送单位 天
                                model.Owner_EndTime = model.Owner_EndTime.Value.AddDays(mr.MonthRule_AddCycle ?? 0);
                            else if (mr.MonthRule_AddUnit == 2)//赠送单位 月
                                model.Owner_EndTime = Utils.AddMonths(model.Owner_EndTime.Copy().Value, mr.MonthRule_AddCycle ?? 0);
                            else if (mr.MonthRule_AddUnit == 3)//赠送单位 年
                                model.Owner_EndTime = model.Owner_EndTime.Value.AddYears(mr.MonthRule_AddCycle ?? 0);
                        }

                        payOrder.PayOrder_MonthEndTime = DateTime.Parse(model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                        payOrder.PayOrder_DayTimeCount = Convert.ToInt32((payOrder.PayOrder_MonthEndTime - payOrder.PayOrder_MonthBeginTime).Value.TotalDays);

                        logmsg = $"充值{payOrder.PayOrder_Money}元,支付{param.PayOrder_PayedMoney}元,延期至{model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59")}";
                    }
                }
                //自定义
                else if (param.PayOrder_PayType == 2)
                {
                    //储值车
                    if (model.Owner_CardType == 2)
                    {
                        Model.Ledger ledger = null;
                        var remainingMoney = Utils.ObjectToDecimal(model.Owner_Balance, 0) - param.PayOrder_PayedMoneyYQ;
                        //if (remainingMoney > 0)
                        //{
                        ledger = new Model.Ledger()
                        {
                            Ledger_CarNo = cars?.FirstOrDefault()?.Car_CarNo,
                            Ledger_Space = model.Owner_Space,
                            Ledger_Type = 1,
                            Ledger_CardType = 2,
                            Ledger_Code = 1,
                            Ledger_Money = param.PayOrder_MoneyYQ,
                            Ledger_BeforeMoeny = model.Owner_Balance,
                            Ledger_Time = DateTime.Now,
                            Ledger_Remark = "自定义延期充值"
                        };
                        //}

                        model.Owner_Balance = (model.Owner_Balance ?? 0) + param.PayOrder_MoneyYQ;
                        if (ledger != null)
                        {
                            ledger.Ledger_AfterMoeny = model.Owner_Balance;
                            ledgerList = new List<Model.Ledger> { ledger };
                        }
                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.Wallet.ToString();
                        payOrder.PayOrder_Money = param.PayOrder_MoneyYQ;
                        payOrder.PayOrder_PayedMoney = param.PayOrder_PayedMoneyYQ;
                        payOrder.PayOrder_DiscountMoney = param.PayOrder_MoneyYQ - param.PayOrder_PayedMoneyYQ;

                        logmsg = $"充值{param.PayOrder_MoneyYQ}元,支付{param.PayOrder_PayedMoneyYQ}元";
                    }
                    //月租车
                    else if (model.Owner_CardType == 3)
                    {
                        if (param.Owner_EndTimeYQ <= param.Owner_StartTimeYQ) { return ResOk(false, "截止日期不能小于开始日期"); }

                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.OwnerCharge.ToString();
                        payOrder.PayOrder_Money = param.PayOrder_PayedMoneyYQ;
                        payOrder.PayOrder_PayedMoney = param.PayOrder_PayedMoneyYQ;
                        payOrder.PayOrder_DiscountMoney = 0;
                        payOrder.PayOrder_MonthEffTime = model.Owner_EndTime.Copy();
                        payOrder.PayOrder_MonthBeginTime = param.Owner_StartTimeYQ;
                        payOrder.PayOrder_MonthEndTime = DateTime.Parse(param.Owner_EndTimeYQ.Value.ToString("yyyy-MM-dd 23:59:59"));

                        var result = BLL.Owner.ModifyOwnerStartTime(model, param, cars, out var errorMsg, out bool isReadCarTime, out bool isExpireSpace, out bool isInPark);
                        if (!result)
                        {
                            return ResOk(false, errorMsg);
                        }

                        if (isExpireSpace && isReadCarTime && isInPark)
                            model.Owner_StartTime = param.Owner_StartTimeYQ;
                        model.Owner_EndTime = param.Owner_EndTimeYQ;

                        logmsg = $"充值{param.PayOrder_PayedMoneyYQ}元,支付{param.PayOrder_PayedMoneyYQ}元,延期至{model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59")}";
                    }
                }

                model.Owner_EndTime = DateTime.Parse(model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                if (payOrder.PayOrder_PayedMoney > payOrder.PayOrder_Money) return ResOk(false, "支付金额大于应付金额");
                #endregion

                #region 更新CAR表
                cars?.ForEach(item =>
                {
                    item.Car_BeginTime = model.Owner_StartTime;
                    item.Car_EndTime = model.Owner_EndTime;
                });
                #endregion

                var oRes = BLL.Owner.Insert(model, cars, null, null, lgAdmins, ledgerList);
                if (oRes > 0)
                {

                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            var pRes = BLL.PayOrder.AddPayOrderCar(payOrder);
                            if (!pRes)
                            {
                                BLL.UserLogs.AddLog(lgAdmins, SecondOption.Delay, $"支付订单生成失败：{TyziTools.Json.ToString(payOrder)}", SecondIndex.Owner);
                            }

                            int count = 0;
                            cars?.ForEach(car =>
                            {
                                try
                                {
                                    car.Car_BeginTime = model.Owner_StartTime;
                                    car.Car_EndTime = model.Owner_EndTime;
                                    BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, model, count == 0 ? payOrder : null);
                                    count++;
                                }
                                catch (Exception e)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"【{car?.Car_CarNo}】车位充值延期上报云平台异常", LogLevel.Error, e);
                                }
                            });

                            if (payOrder != null)
                            {
                                var car = cars.FirstOrDefault();
                                var payPart = BLL.CommonBLL.CreatePayPartList(null, payOrder, (model.Owner_CardType == 2 ? 1 : 2));

                                BLL.PayOrder.AddPayOrderList(new List<Model.PayOrder>() { payOrder }, payPart);
                                var sendData = new Model.API.PushResultParse.CarOwnerPayDate() { Item1 = model, Item2 = new Model.PayColl() { payOrderList = new List<Model.PayOrder>() { payOrder }, payPartList = payPart }, Item3 = cars };
                                var r = PushSync(Model.API.PushAction.Edit, sendData, new List<Model.SentryHost>(), "onspacepaycharge", dataType: DataTypeEnum.Owner, Desc: $"续期{sendData?.Item1?.Owner_Space}");
                                if (r == null || r.Where(x => x.success == false).Count() > 0)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车车位延期充值处理成功，但未能同步数据至岗亭：【{model.Owner_Space}】");
                                }
                            }

                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.Delay, $"{BLL.Owner.GetChargeLogs(model, old, cars)},{logmsg}", SecondIndex.Owner);
                        }
                        catch (Exception ex)
                        {
                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.Delay, $"支付订单生成失败：{TyziTools.Json.ToString(payOrder)}。{ex.ToString()}", SecondIndex.Owner);

                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "操作成功");
                }
                else
                {
                    return ResOk(false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, SecondOption.Delay, $"程序异常：{ex.Message}", SecondIndex.Owner);
                return ResOk(false, ex.Message);
            }
        }


        public ActionResult BathPayDate()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.BathPayDate.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }
        /// <summary>
        /// 批量延期
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public IActionResult BathSpacePayCharge(string jsonModel)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.BathPayDate.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            try
            {
                JObject param = Utils.ClearModelRiskSQL<JObject>(jsonModel);

                if (param == null)
                {
                    return ResOk(false, "参数错误");
                }
                if (!Utils.IsParam(param, "noList")) { return ResOk(false, "请选择延期的车辆"); }
                if (!Utils.IsParam(param, "unit")) { return ResOk(false, "请选择延期的单位"); }
                if (!Utils.IsParam(param, "unitNum")) { return ResOk(false, "请选择延期的单位数"); }
                if (!Utils.IsParam(param, "money")) { return ResOk(false, "请填写应收金额"); }
                if (!Utils.IsParam(param, "payedMoney")) { return ResOk(false, "请填写实收金额"); }

                string OwnerNoList = string.Join(",", TyziTools.Json.ToObject<string[]>(param["noList"].ToString()));
                string unit = param["unit"].ToString();//1-天，2-月，3-年
                int unitNum = Utils.ObjectToInt(param["unitNum"].ToString(), 0);
                decimal money = Utils.ObjectToDecimal(param["money"].ToString(), 0);
                decimal payedMoney = Utils.ObjectToDecimal(param["payedMoney"].ToString(), 0);
                string remark = param.ContainsKey("remark") ? param["remark"].ToString() : "";

                if (payedMoney > money)
                {
                    return ResOk(false, "支付金额不能大于充值金额");
                }

                if (unit == "3")
                {
                    if (unitNum > 100) { return ResOk(false, "延期单位不能大于100年"); }
                }

                List<Model.Owner> modelList = BLL.Owner.GetAllEntity("*", $"Owner_No in ('{OwnerNoList.Replace(",", "','")}')");
                if (modelList == null || modelList.Count == 0) { return ResOk(false, "车主信息不存在"); }

                var carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo IN ('{OwnerNoList.Replace(",", "','")}')");

                var ccts = BLL.CarCardType.GetAllEntity("*", $"");

                string logmsg = $"充值{money}元,支付{payedMoney}元"; ;

                var newOwnerList = new List<Model.Owner>();
                var newCarList = new List<Model.Car>();
                var payOrderList = new List<Model.PayOrder>();

                var noList = Utils.GetRandomLst(modelList.Count + 1, "MC");

                List<Model.Ledger> ledgerList = null;

                modelList.ForEach(model =>
                {
                    var cars = carList.FindAll(x => x.Car_OwnerNo == model.Owner_No)?.Take(100)?.ToList();

                    var no = noList.LastOrDefault();
                    //生成支付订单
                    var payOrder = new Model.PayOrder()
                    {
                        PayOrder_No = no + parking.Parking_Key,
                        PayOrder_ParkNo = parking.Parking_No,
                        PayOrder_ParkKey = parking.Parking_Key,
                        PayOrder_OrderTypeNo = null,
                        PayOrder_ParkAreaNo = null,
                        PayOrder_ParkOrderNo = null,
                        PayOrder_Money = 0,
                        PayOrder_Time = DateTimeHelper.GetNowTime(),
                        PayOrder_Status = 1,
                        PayOrder_PayedMoney = 0,
                        PayOrder_PayedTime = DateTimeHelper.GetNowTime(),
                        PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString(),
                        PayOrder_PayType = 0,
                        PayOrder_OwnerNo = model.Owner_No,
                        PayOrder_OwnerName = model.Owner_Name,
                        PayOrder_OwnerSpace = model.Owner_Space,
                        PayOrder_CarNos = string.Join(",", cars?.Take(50).Select(x => x.Car_CarNo)),
                        PayOrder_AdminID = lgAdmins?.Admins_ID,
                        PayOrder_Account = lgAdmins?.Admins_Account,
                        PayOrder_OperatorName = lgAdmins?.Admins_Name,
                        PayOrder_Category = ccts?.Find(x => x.CarCardType_No == model.Owner_CardTypeNo)?.CarCardType_Category,
                        PayOrder_CarCardTypeNo = model.Owner_CardTypeNo,
                        PayOrder_CarNo = cars?.FirstOrDefault()?.Car_CarNo,
                        PayOrder_CarTypeNo = cars?.FirstOrDefault()?.Car_VehicleTypeNo,

                        PayOrder_UserNo = null,
                        PayOrder_CouponRecordNo = null,
                        PayOrder_Desc = null,
                        PayOrder_EnterTime = null,
                        PayOrder_TempTimeCount = null,
                        PayOrder_DayTimeCount = null,
                        PayOrder_MonthEffTime = null,
                        PayOrder_MonthBeginTime = null,
                        PayOrder_MonthEndTime = null,
                        PayOrder_TimeCountDesc = null,
                        PayOrder_PayedSN = null,

                        PayOrder_FpStatus = 0,
                        PayOrder_DiscountMoney = 0,
                        PayOrder_StoredMoney = 0,
                        PayOrder_OutReduceMoney = 0,
                        PayOrder_SelfMoney = 0,
                    };

                    noList.Remove(no);

                    payOrder.PayOrder_Money = money;
                    payOrder.PayOrder_PayedMoney = payedMoney;
                    payOrder.PayOrder_DiscountMoney = money - payedMoney;
                    payOrder.PayOrder_Desc = string.IsNullOrEmpty(remark) ? "批量延期" : remark;

                    //储值车
                    if (model.Owner_CardType == 2)
                    {

                        Model.Ledger ledger = null;
                        var remainingMoney = Utils.ObjectToDecimal(model.Owner_Balance, 0) - money;
                        //if (remainingMoney > 0)
                        //{
                        ledger = new Model.Ledger()
                        {
                            Ledger_CarNo = AppBasicCache.GetCar.Values.FirstOrDefault(x => x.Car_OwnerNo == model.Owner_No)?.Car_CarNo,
                            Ledger_Space = model.Owner_Space,
                            Ledger_Type = 1,
                            Ledger_CardType = 2,
                            Ledger_Code = 1,
                            Ledger_Money = money,
                            Ledger_BeforeMoeny = model.Owner_Balance,
                            Ledger_Time = DateTime.Now,
                            Ledger_Remark = "批量延期"
                        };
                        //}

                        model.Owner_Balance = (model.Owner_Balance ?? 0) + money;
                        if (ledger != null)
                        {
                            ledger.Ledger_AfterMoeny = model.Owner_Balance;
                            if (ledgerList == null) ledgerList = new List<Model.Ledger>();
                            ledgerList.Add(ledger);
                        }
                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.Wallet.ToString();
                    }
                    //月租车/临时车
                    else if (model.Owner_CardType == 1 || model.Owner_CardType == 3 || model.Owner_CardType == 4)
                    {
                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.OwnerCharge.ToString();
                        payOrder.PayOrder_MonthEffTime = model.Owner_EndTime.Copy();
                        payOrder.PayOrder_MonthBeginTime = Utils.ObjectToDateTime(model.Owner_EndTime.Value.ToString("yyyy-MM-dd 00:00:00")).AddDays(1);

                        if (unit == "1")//充值单位 天
                            model.Owner_EndTime = DateTime.Parse(model.Owner_EndTime.Value.AddDays(unitNum).ToString("yyyy-MM-dd 23:59:59"));
                        else if (unit == "2")//充值单位 月
                            model.Owner_EndTime = DateTime.Parse(Utils.AddMonths(model.Owner_EndTime.Value, unitNum).ToString("yyyy-MM-dd 23:59:59"));
                        else if (unit == "3")//充值单位 年
                            model.Owner_EndTime = DateTime.Parse(model.Owner_EndTime.Value.AddYears(unitNum).ToString("yyyy-MM-dd 23:59:59"));

                        payOrder.PayOrder_MonthEndTime = DateTime.Parse(model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                    }
                    else
                    {
                        payOrder.PayOrder_OrderTypeNo = Model.EnumOrderType.OwnerCharge.ToString();
                    }

                    //更新CAR表
                    cars?.ForEach(item =>
                    {
                        item.Car_BeginTime = model.Owner_StartTime;
                        item.Car_EndTime = model.Owner_EndTime;
                    });

                    newOwnerList.Add(model);
                    if (cars != null && cars.Count > 0) newCarList.AddRange(cars);
                    payOrderList.Add(payOrder);
                });

                var payColl = new Model.PayColl();
                payColl.payOrderList = payOrderList;
                var paypartList = new List<Model.PayPart>();
                payOrderList?.ForEach(payOrder =>
                {
                    var payPart = BLL.CommonBLL.CreatePayPartList(null, payOrder, 1, remark: remark);
                    paypartList.AddRange(payPart);
                });
                payColl.payPartList = paypartList;

                var oRes = BLL.Owner.OnSpacePayChargeList(newOwnerList, payColl, newCarList, ledgerList);
                if (oRes > 0)
                {

                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            newOwnerList.ForEach(owner =>
                            {
                                if (owner != null)
                                {
                                    BLL.UserLogs.AddLog(lgAdmins, SecondOption.BatchDelay, $"{BLL.Owner.GetVaildLog(owner, owner.Owner_StartTime.Value, owner.Owner_EndTime.Value)}", SecondIndex.Owner);

                                    int count = 0;
                                    newCarList?.Where(x => x.Car_OwnerNo == owner.Owner_No).ToList()?.ForEach(car =>
                                    {
                                        try
                                        {
                                            car.Car_BeginTime = owner.Owner_StartTime;
                                            car.Car_EndTime = owner.Owner_EndTime;
                                            BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, owner, count == 0 ? payOrderList.Find(x => x.PayOrder_OwnerNo == owner.Owner_No) : null);
                                            payOrderList?.ForEach(x =>
                                            {
                                                string payOrderDesc = "";
                                                if (!string.IsNullOrEmpty(x.PayOrder_CarNo))
                                                {
                                                    var car = AppBasicCache.GetElement(AppBasicCache.GetCar, x.PayOrder_CarNo);
                                                    if (car != null && car.Car_Category == "3657")
                                                    {
                                                        payOrderDesc = AppBasicCache.GetElement(AppBasicCache.GetOwner, car.Car_OwnerNo)?.Owner_Balance?.ToString() ?? "";
                                                        if (!string.IsNullOrEmpty(payOrderDesc)) payOrderDesc = "储值余额:" + payOrderDesc;
                                                    }
                                                }
                                                BLL.PushEvent.PayOrder(parking.Parking_Key, x, null, 0, payOrderDesc);
                                            });

                                            count++;
                                        }
                                        catch (Exception e)
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"【{car?.Car_CarNo}】车位充值延期上报云平台异常", LogLevel.Error, e);
                                        }
                                    });
                                }
                            });

                            var sendData = new List<Model.API.PushResultParse.CarOwnerPayDate>();
                            newOwnerList.ForEach(owner =>
                            {
                                if (owner != null)
                                {
                                    var payes = payOrderList.FindAll(x => x.PayOrder_OwnerNo == owner.Owner_No);
                                    sendData.Add(new Model.API.PushResultParse.CarOwnerPayDate() { Item1 = owner, Item2 = new Model.PayColl() { payOrderList = payes, payPartList = paypartList.FindAll(x => payes.Any(y => y.PayOrder_No == x.PayPart_PayOrderNo)) }, Item3 = newCarList.FindAll(x => x.Car_OwnerNo == owner.Owner_No) });
                                }
                            });
                            var r = PushSync(Model.API.PushAction.Edit, sendData, new List<Model.SentryHost>(), "onspacepaycharge", dataType: DataTypeEnum.Owner, Desc: $"批量延期");
                            if (r == null || r.Where(x => x.success == false).Count() > 0)
                            {
                                //return ResOk(false, "批量延期成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车批量延期处理成功，但未能同步数据至岗亭：【{string.Join(',', carList?.Select(x => x.Car_CarNo))}】");
                            }

                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.BatchDelay, $"{logmsg}", SecondIndex.Owner);
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"固定车批量延期处理异常：【{string.Join(',', carList?.Select(x => x.Car_CarNo))}】" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "操作成功");
                }
                else
                {
                    return ResOk(false, "操作失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, SecondOption.BatchDelay, $"异常：{ex.Message}", SecondIndex.Owner);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 设置车位有效期
        /// </summary>
        /// <param name="jsonModel"></param>
        /// <returns></returns>
        public IActionResult OnUpdateVaild(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Vaild.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                JObject obj = Utils.ClearModelRiskSQL<JObject>(jsonModel);
                if (obj["Owner_No"] == null) return ResOk(false, "请选择车位");
                if (obj["Owner_StartTime"] == null) return ResOk(false, "有效期起不能为空");
                if (obj["Owner_EndTime"] == null) return ResOk(false, "有效期止不能为空");
                var clearfee = Convert.ToInt32(obj["clearfee"]);
                var ss = DateTime.TryParse(obj["Owner_StartTime"].ToString(), out var Owner_StartTime);
                var se = DateTime.TryParse(obj["Owner_EndTime"].ToString(), out var Owner_EndTime);
                if (!ss) return ResOk(false, "有效期起格式错误");
                if (!se) return ResOk(false, "有效期止格式错误");

                Owner_StartTime = Utils.ObjectToDateTime(Owner_StartTime.ToString("yyyy-MM-dd 00:00:00"));
                Owner_EndTime = Utils.ObjectToDateTime(Owner_EndTime.ToString("yyyy-MM-dd 23:59:59"));

                var nos = TyziTools.Json.ToObject<List<string>>(obj["Owner_No"].ToString());
                if (nos == null || nos.Count == 0) return ResOk(false, "请选择车位");


                var models = BLL.Owner.GetAllEntity("*", $"Owner_ParkNo='{parking.Parking_No}' AND Owner_No in ('{string.Join("','", nos)}')");
                if (models == null || models.Count == 0) return ResOk(false, "请选择车位");
                if (models.Select(x => x.Owner_CardType)?.Distinct().Count() > 1) return ResOk(false, "请选择相同车牌类型");

                var cars = BLL.Car.GetAllEntity("*", $"Car_ParkingNo='{parking.Parking_No}' AND Car_OwnerNo IN ('{string.Join("','", nos)}')");

                foreach (var item in models)
                {
                    item.Owner_StartTime = Owner_StartTime;
                    item.Owner_EndTime = Owner_EndTime;
                    item.Owner_EditTime = DateTimeHelper.GetNowTime();
                    item.Owner_EditID = lgAdmins?.Admins_ID;

                    var carItems = cars.FindAll(x => x.Car_OwnerNo == item.Owner_No);
                    carItems.ForEach(x =>
                    {
                        x.Car_BeginTime = item.Owner_StartTime;
                        x.Car_EndTime = item.Owner_EndTime;
                    });
                }

                var res = BLL.Owner.OnSpacePayChargeList(models, null, cars);
                if (res > 0)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            models.ForEach(x =>
                            {
                                BLL.UserLogs.AddLog(lgAdmins, SecondOption.SetValid, BLL.Owner.GetVaildLog(x, x.Owner_StartTime.Value, x.Owner_EndTime.Value), SecondIndex.Owner);
                            });


                            cars.ForEach(car =>
                                {
                                    BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, models.Find(x => x.Owner_No == car.Car_OwnerNo));
                                });

                            #region 已入场车辆不计算过期费用
                            if (clearfee == 1)
                            {
                                var cars1 = BLL.Car.GetAllEntity("Car_CarNo", $"Car_ParkingNo='{parking.Parking_No}' AND Car_OwnerNo IN ('{string.Join("','", nos)}')");
                                if (cars1?.Count > 0)
                                {
                                    var curdt = DateTimeHelper.GetNowTime();
                                    Owner_StartTime = Owner_StartTime > curdt ? curdt : Owner_StartTime;
                                    StringBuilder builder = new StringBuilder();
                                    builder.Append($" OrderDetail_StatusNo={Model.EnumParkOrderStatus.In} ");
                                    builder.Append($" AND OrderDetail_IsSettle={Model.EnumOrderSettleStatus.False} ");
                                    builder.Append($" AND OrderDetail_EnterTime<'{Owner_StartTime.ToString("yyyy-MM-dd HH:mm:ss")}' ");
                                    builder.Append($" AND OrderDetail_CarNo in ('{string.Join("','", cars1.Select(x => x.Car_CarNo))}')");
                                    List<Model.OrderDetail> clears = BLL.OrderDetail.GetAllEntity("*", builder.ToString());
                                    if (clears != null && clears.Count > 0)
                                    {
                                        clears?.ForEach(item =>
                                        {
                                            item.OrderDetail_IsSettle = Model.EnumOrderSettleStatus.True;
                                            item.OrderDetail_Remark = "批量修改有效期时结算";
                                        });

                                        cars1?.ForEach(x =>
                                        {
                                            var items = clears?.FindAll(i => i.OrderDetail_CarNo == x.Car_CarNo).OrderBy(a => a.OrderDetail_EnterTime).ToList();
                                            if (items?.Count > 0)
                                            {
                                                var last = items.LastOrDefault();
                                                last.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Close;

                                                var newDetail = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(last));
                                                newDetail.OrderDetail_No = BLL.OrderDetail.NewOrderNo(last.OrderDetail_CarNo, 1).First();
                                                newDetail.OrderDetail_EnterTime = Owner_StartTime;
                                                newDetail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                                                newDetail.OrderDetail_IsSettle = Model.EnumOrderSettleStatus.False;
                                                newDetail.OrderDetail_OutType = 0;
                                                newDetail.orderdetail_EnterRemark = "批量修改有效期时生成入场";
                                                clears.Add(newDetail);
                                            }
                                        });

                                        if (BLL.OrderDetail.UpdateByList(clears))
                                        {
                                            var r2 = PushSync(Model.API.PushAction.Act, clears, new List<Model.SentryHost>(), "UpdateOrderList", dataType: DataTypeEnum.InParkRecord, Desc: $"修改车辆有效期，更新订单");
                                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.SetValid, $"结算订单：{string.Join(";", clears.Select(x => $"{x.OrderDetail_CarNo}"))}", SecondIndex.Owner);
                                        }
                                        else
                                        {
                                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.SetValid, $"结算订单失败：{string.Join(";", clears.Select(x => $"{x.OrderDetail_CarNo}"))}", SecondIndex.Owner);
                                        }
                                    }
                                }
                            }
                            #endregion

                            var sendData = new Model.API.PushResultParse.DelCarOwner() { Item1 = models, Item2 = null, Item3 = cars };
                            var r = PushSync(Model.API.PushAction.Edit, sendData, new List<Model.SentryHost>(), "onspacepaychargelist", dataType: DataTypeEnum.Owner, Desc: $"修改有效期{string.Join(",", models?.Select(x => x.Owner_Space))}");
                            if (r == null || r.Where(x => x.success == false).Count() > 0)
                            {
                                //return ResOk(false, "设置车位有效期成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车有效期设置成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', cars?.Select(x => x.Car_CarNo))}）");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"固定车有效期处理异常：【{string.Join(',', cars?.Select(x => x.Car_CarNo))}】" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "保存成功");
                }
                else
                {
                    return ResOk(false, "保存失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, $"设置有效期程序异常：{ex.Message}", SecondIndex.Owner);
                return ResOk(false, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 批量设置车位有效期
        /// </summary>
        public void BathValidCar(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.BathVaild.ToString(), adminSession: lgAdmins))
                { MiniResponse.ResponseResult("无权限", false); return; }

                JObject param = Utils.ClearModelRiskSQL<JObject>(jsonModel);

                if (param == null)
                {
                    MiniResponse.ResponseResult("参数错误", false, null);
                    return;
                }
                if (!Utils.IsParam(param, "OwnerNoList")) { MiniResponse.ResponseResult("参数不能为空", false); return; }
                string OwnerNoList = param["OwnerNoList"].ToString();
                string Car_ChangeTime = param.ContainsKey("Car_ChangeTime") ? param["Car_ChangeTime"].ToString() : null;
                string Car_ChangeBeginTime = param.ContainsKey("Car_ChangeBeginTime") ? param["Car_ChangeBeginTime"].ToString() : null;

                if (string.IsNullOrEmpty(Car_ChangeTime)) { MiniResponse.ResponseResult("变更有效期止不能为空", false); return; }
                if (string.IsNullOrEmpty(Car_ChangeBeginTime)) { MiniResponse.ResponseResult("变更有效期始不能为空", false); return; }

                DateTime newBeginTime = Utils.StrToDateTime(Car_ChangeBeginTime);
                DateTime newEndTime = Utils.StrToDateTime(Car_ChangeTime);
                newBeginTime = Utils.ObjectToDateTime(newBeginTime.ToString("yyyy-MM-dd 00:00:00"));
                newEndTime = Utils.ObjectToDateTime(newEndTime.ToString("yyyy-MM-dd 23:59:59"));

                if (newBeginTime > newEndTime)
                {
                    MiniResponse.ResponseResult("开始时间不能大于结束时间", false); return;
                }

                List<Model.Owner> modelList = BLL.Owner.GetAllEntity("*", $"Owner_No in ('{OwnerNoList.Replace(",", "','")}')");
                if (modelList == null) { MiniResponse.ResponseResult("车主信息不存在", false); return; }
                if (modelList.Find(x => x.Owner_CardType == 2) != null) { MiniResponse.ResponseResult("有效期变更不适用于储值车类型，请忽略该类型", false); return; }

                int error = 0;
                List<ResPushHost> clearsPush = null;
                modelList.ForEach(model =>
                {
                    try
                    {
                        var oldModel = JObject.FromObject(model).ToObject<Model.Owner>();

                        //查询属于[多位多车]的车辆信息
                        List<Model.Car> cars = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{model.Owner_No}'");
                        List<Model.CarCardType> cardTypes = BLL.CarCardType.GetAllEntity("*", $"1=1");
                        var cardNos = cardTypes.Select(x => x.CarCardType_No).ToList();
                        var spaceCars = cars.FindAll(x => cardNos.Contains(x.Car_TypeNo)).ToList();

                        model.Owner_StartTime = newBeginTime;
                        model.Owner_EndTime = newEndTime;
                        //仅修改[多位多车]类型车辆的有效期
                        if (spaceCars != null && spaceCars.Count > 0)
                        {
                            spaceCars.ForEach(x =>
                            {
                                x.Car_BeginTime = model.Owner_StartTime;
                                x.Car_EndTime = model.Owner_EndTime;
                                if (x.Car_EndTime != null) x.Car_EndTime = Utils.StrToDateTime(x.Car_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                            });
                        }

                        var res = BLL.Owner.OnSpacePayCharge(model, null, spaceCars);
                        if (res > 0)
                        {
                            spaceCars.ForEach(car =>
                            {
                                try
                                {
                                    car.Car_BeginTime = model.Owner_StartTime;
                                    car.Car_EndTime = model.Owner_EndTime;
                                    BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, model);
                                }
                                catch (Exception e)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"【{car?.Car_CarNo}】批量设置车位有效期上报云平台异常", LogLevel.Error, e);
                                }
                            });
                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.BathSetValid, CreateLogs(oldModel, model, spaceCars), SecondIndex.Owner);

                            var sendData = new Model.API.PushResultParse.CarOwnerPayDate() { Item1 = model, Item2 = null, Item3 = spaceCars };
                            clearsPush = PushSync(Model.API.PushAction.Edit, sendData, new List<Model.SentryHost>(), "onspacepaycharge", dataType: DataTypeEnum.Owner, Desc: $"修改有效期{model?.Owner_Space}");
                        }
                    }
                    catch (Exception ex)
                    {
                        error++;
                        BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "车辆批量延期", "车辆批量延期发生异常:" + ex.ToString());
                    }
                });
                if (error > 0)
                {
                    rb.Success = true;
                    rb.Code = "0000";
                    rb.Message = $"成功{modelList.Count - error}条,失败{error}条";
                }
                else
                {
                    rb.Success = true;
                    rb.Code = "0000";
                    rb.Message = "保存成功";
                }


                if (clearsPush != null && clearsPush.Count > 0)
                {
                    List<Model.API.ResPushHost> failRes = clearsPush.Where(x => x.success == false).ToList();
                    if (failRes.Count > 0)
                    {
                        var errorMsg = "";
                        if (error > 0) { errorMsg = $"成功{modelList.Count - error}条,失败{error}条，"; }
                        rb.Success = false; rb.Message = $"车辆批量延期成功，{errorMsg}但未能同步数据至岗亭（{string.Join(",", failRes.GroupBy(x => x.hostname).Select(m => m.Key))}），请检查服务器与岗亭网络通讯情况";
                    }
                }

                Response.WriteAsync(JsonConvert.SerializeObject(rb));

            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "车辆批量延期", "车辆批量延期发生异常:" + ex.ToString());
                MiniResponse.ResponseResult("异常错误", false);
            }
        }

        private string CreateLogs(Model.Owner oldModel, Model.Owner model, List<Model.Car> carList)
        {
            StringBuilder logStr = new StringBuilder();
            logStr.Append($"车位号：{model.Owner_Space}；");
            if (oldModel.Owner_StartTime != model.Owner_StartTime)
                logStr.Append($"有效期起变更：{oldModel.Owner_StartTime?.ToString("yyyy-MM-dd")} 改为 {model.Owner_StartTime?.ToString("yyyy-MM-dd")}；");
            if (oldModel.Owner_EndTime != model.Owner_EndTime)
                logStr.Append($"有效期止变更：{oldModel.Owner_EndTime?.ToString("yyyy-MM-dd")} 改为 {model.Owner_EndTime?.ToString("yyyy-MM-dd")}；");
            if (carList != null && carList.Count > 0)
                logStr.Append($"影响车辆：{string.Join(",", carList.Select(x => x.Car_CarNo))}");
            return logStr.ToString();
        }
        #endregion

        /// <summary>
        /// 车主列表
        /// </summary>
        public void GetOwnerList(int pageIndex, int pageSize, string conditionParam, string field, string order)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Search.ToString(), adminSession: lgAdmins))
            { Response.WriteAsync(oModel.ParseJson()); return; }

            Response.WriteAsync(SearchOwnerList(pageIndex, pageSize, conditionParam, field, order).ParseJson());
        }

        /// <summary>
        /// 查询车主列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        private Model.PageResult SearchOwnerList(int pageIndex, int pageSize, string conditionParam, string field, string order, bool isExportExcel = false)
        {
            try
            {
                if (pageSize > 1000 && !isExportExcel)
                    return new Model.PageResult(-1, "", 0, null);

                string sqlwhere = "";
                Model.OwnerWhere model = Utils.ClearModelRiskSQL<Model.OwnerWhere>(conditionParam);

                var parameters = new Dapper.DynamicParameters();

                if (!string.IsNullOrEmpty(model.Owner_Space))
                {
                    sqlwhere += string.Format(" and Owner_Space like @Owner_Space ", model.Owner_Space);
                    parameters.Add("Owner_Space", "%" + model.Owner_Space + "%");
                }
                if (!string.IsNullOrEmpty(model.Owner_ParkSpace))
                {
                    sqlwhere += string.Format("and Owner_ParkSpace like @Owner_ParkSpace ", model.Owner_ParkSpace);
                    parameters.Add("Owner_ParkSpace", "%" + model.Owner_ParkSpace + "%");
                }
                if (!string.IsNullOrEmpty(model.Owner_Name))
                {
                    sqlwhere += string.Format(" and Owner_Name like @Owner_Name ", model.Owner_Name);
                    parameters.Add("Owner_Name", "%" + model.Owner_Name + "%");
                }
                if (!string.IsNullOrEmpty(model.Owner_IDCard))
                {
                    sqlwhere += string.Format("and Owner_IDCard = @Owner_IDCard ", AESHelper.AesEncrypt_DB(model.Owner_IDCard));
                    parameters.Add("Owner_IDCard", AESHelper.AesEncrypt_DB(model.Owner_IDCard));
                }
                if (!string.IsNullOrEmpty(model.Owner_Phone))
                {
                    sqlwhere += string.Format("and Owner_Phone = @Owner_Phone ", AESHelper.AesEncrypt_DB(model.Owner_Phone));
                    parameters.Add("Owner_Phone", AESHelper.AesEncrypt_DB(model.Owner_Phone));
                }
                if (!string.IsNullOrWhiteSpace(model.Owner_PhoneLastFour))
                {
                    sqlwhere += string.Format("and Owner_PhoneLastFour = @Owner_PhoneLastFour ", model.Owner_PhoneLastFour);
                    parameters.Add("Owner_PhoneLastFour", model.Owner_PhoneLastFour);
                }
                if (!string.IsNullOrEmpty(model.Owner_Address))
                {
                    sqlwhere += string.Format(" and Owner_Address like @Owner_Address ", model.Owner_Address);
                    parameters.Add("Owner_Address", "%" + model.Owner_Address + "%");
                }
                if (!string.IsNullOrEmpty(model.Owner_Remark))
                {
                    sqlwhere += string.Format(" and Owner_Remark like @Owner_Remark ", model.Owner_Remark);
                    parameters.Add("Owner_Remark", "%" + model.Owner_Remark + "%");
                }
                if (!string.IsNullOrWhiteSpace(model.Owner_CardTypeNo))
                {
                    sqlwhere += string.Format("and Owner_CardTypeNo = @Owner_CardTypeNo ", model.Owner_CardTypeNo);
                    parameters.Add("Owner_CardTypeNo", model.Owner_CardTypeNo);
                }
                if (model.Owner_EnableOffline != null)
                {
                    sqlwhere += string.Format("and Owner_EnableOffline = @Owner_EnableOffline ", model.Owner_EnableOffline);
                    parameters.Add("Owner_EnableOffline", model.Owner_EnableOffline);
                }
                if (model.Owner_IsMoreCar != null)
                {
                    sqlwhere += string.Format("and Owner_CardTypeNo in (select CarCardType_No from CarCardType where CarCardType_IsMoreCar=@Owner_IsMoreCar) ", model.Owner_IsMoreCar);
                    parameters.Add("Owner_IsMoreCar", model.Owner_IsMoreCar);
                }
                if (model.Owner_StartTime0 != null || model.Owner_StartTime1 != null || model.Owner_EndTime0 != null || model.Owner_EndTime1 != null)
                {
                    sqlwhere += $" and Owner_CardType NOT IN (2)";

                    if (model.Owner_StartTime0 != null && model.Owner_StartTime1 != null)
                    {
                        sqlwhere += $" and Owner_StartTime BETWEEN @Owner_StartTime0 AND @Owner_StartTime1 ";
                        parameters.Add("Owner_StartTime0", model.Owner_StartTime0.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        parameters.Add("Owner_StartTime1", model.Owner_StartTime1.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                    else if (model.Owner_StartTime0 != null)
                    {
                        sqlwhere += $" and Owner_StartTime >= @Owner_StartTime0 ";
                        parameters.Add("Owner_StartTime0", model.Owner_StartTime0.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                    else if (model.Owner_StartTime1 != null)
                    {
                        sqlwhere += $" and Owner_StartTime <= @Owner_StartTime1 ";
                        parameters.Add("Owner_StartTime1", model.Owner_StartTime1.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    }

                    if (model.Owner_EndTime0 != null && model.Owner_EndTime1 != null)
                    {
                        sqlwhere += $" and Owner_EndTime BETWEEN @Owner_EndTime0 AND @Owner_EndTime1 ";
                        parameters.Add("Owner_EndTime0", model.Owner_EndTime0.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        parameters.Add("Owner_EndTime1", model.Owner_EndTime1.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                    else if (model.Owner_EndTime0 != null)
                    {
                        sqlwhere += $" and Owner_EndTime >= @Owner_EndTime0 ";
                        parameters.Add("Owner_EndTime0", model.Owner_EndTime0.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                    else if (model.Owner_EndTime1 != null)
                    {
                        sqlwhere += $" and Owner_EndTime <= @Owner_EndTime1 ";
                        parameters.Add("Owner_EndTime1", model.Owner_EndTime1.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                }

                if (model.Owner_AddTime0 != null && model.Owner_AddTime1 != null)
                {
                    sqlwhere += $" and Owner_AddTime BETWEEN @Owner_AddTime0 AND @Owner_AddTime1 ";
                    parameters.Add("Owner_AddTime0", model.Owner_AddTime0.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                    parameters.Add("Owner_AddTime1", model.Owner_AddTime1.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                }
                else if (model.Owner_AddTime0 != null)
                {
                    sqlwhere += $" and Owner_AddTime >= @Owner_AddTime0 ";
                    parameters.Add("Owner_AddTime0", model.Owner_AddTime0.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                }
                else if (model.Owner_AddTime1 != null)
                {
                    sqlwhere += $" and Owner_AddTime <= @Owner_AddTime1 ";
                    parameters.Add("Owner_AddTime1", model.Owner_AddTime1.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                }

                if (model.Owner_BalanceType != null)
                {
                    //条件不为空则只查询储值车类型
                    sqlwhere += $" and Owner_CardType ='2' ";

                    // 转换条件信息添加查询条件
                    switch (model.Owner_BalanceType)
                    {
                        case 0: sqlwhere += $" and Owner_Balance>'{model.Owner_BalanceValue}' "; break;
                        case 1: sqlwhere += $" and Owner_Balance>='{model.Owner_BalanceValue}' "; break;
                        case 2: sqlwhere += $" and Owner_Balance<'{model.Owner_BalanceValue}' "; break;
                        case 3: sqlwhere += $" and Owner_Balance<='{model.Owner_BalanceValue}' "; break;
                        default: break;
                    }
                }

                if (model.SearchType != 0)
                {
                    string curdt = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");
                    string dtto7 = DateTimeHelper.GetNowTime().AddDays(7).ToString("yyyy-MM-dd HH:mm:ss");
                    switch (model.SearchType)
                    {
                        case 1: sqlwhere += $" and (Owner_CardType=2 or '{curdt}' BETWEEN Owner_StartTime AND Owner_EndTime) "; break;
                        case 2: sqlwhere += $" and Owner_CardType<>2 and Owner_EndTime BETWEEN '{curdt}' AND '{dtto7}' "; break;
                        case 3: sqlwhere += $" and Owner_CardType<>2 and Owner_EndTime <'{curdt}' "; break;
                        default: break;
                    }
                }

                #region 车辆信息条件
                if (!string.IsNullOrEmpty(model.Owner_CarCardNo))
                {
                    List<Model.Car> car = BLL.Car.GetAllEntity("Car_OwnerNo", $"Car_CarNo like @Owner_CarCardNo", new { Owner_CarCardNo = "%" + model.Owner_CarCardNo.Trim() + "%" });
                    parameters.Add("Owner_CarCardNo", "%" + model.Owner_CarCardNo + "%");

                    var ownerNoList = car?.Select(x => x.Car_OwnerNo).ToList() ?? new List<string>();
                    sqlwhere += $" and Owner_No in ('{string.Join("','", ownerNoList)}') ";
                }
                if (!string.IsNullOrEmpty(model.Car_Remark))
                {
                    sqlwhere += (string.Format(" and Owner_No in (SELECT Car_OwnerNo  from car where Car_Remark like @Car_Remark) ", model.Car_Remark));
                    parameters.Add("Car_Remark", "%" + model.Car_Remark + "%");
                }
                if (!string.IsNullOrEmpty(model.Car_CardNo))
                {
                    sqlwhere += string.Format(" and Owner_No in (SELECT Car_OwnerNo  from car where Car_CardNo like @Car_CardNo) ", model.Car_CardNo);
                    parameters.Add("Car_CardNo", "%" + model.Car_CardNo + "%");
                }
                #endregion

                field = string.IsNullOrEmpty(field) ? "Owner_ID" : field;
                order = string.IsNullOrEmpty(order) ? "0" : (order == "asc" ? "1" : "0");


                var objpara = parameters.ParameterNames.ToDictionary(name => name, name => (object)parameters.Get<object>(name));

                int pageCount = 0, totalRecord = 0;
                List<Model.OwnerExt> lst = BLL.Owner.GetExtList("*", sqlwhere, pageIndex, pageSize, field, Utils.ObjectToInt(order, 0), out pageCount, out totalRecord, objpara);

                //读取车场配置，判断是否保密手机号码
                var PolicyPark_PhoneSecret = 1;
                var parkinfor = BLL.Parking.GetAllEntity().FirstOrDefault();
                if (parkinfor != null)
                {
                    var policyPark = BLL.PolicyPark.GetEntity(parkinfor.Parking_No);
                    if (policyPark != null)
                    {
                        PolicyPark_PhoneSecret = policyPark.PolicyPark_PhoneSecret ?? 1;
                    }
                }
                //设置车主手机号码是否保密
                lst.ForEach(m => m.Owner_IsSecretPhone = PolicyPark_PhoneSecret);

                #region 获取车主绑定的车牌号码
                List<Model.Admins> adminList = BLL.Admins.GetAllEntity("Admins_ID,Admins_Name", "1=1");
                List<Model.CarCardType> cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_IsMoreCar", "");
                var ownerNos = lst?.Select(x => x.Owner_No).ToList();
                if (ownerNos?.Count > 0)
                {
                    Regex regex = new Regex(@"^(\d+,)*\d+$");
                    var parameters2 = new { Car_OwnerNos = ownerNos };
                    var cars = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerNo,Car_CardNo", "Car_OwnerNo IN @Car_OwnerNos", parameters2);
                    var incars = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_CarNo", $"InCar_Status=200 and InCar_CarNo in ('{string.Join("','", cars.Select(x => x.Car_CarNo))}')");
                    List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo IN ('{string.Join("','", ownerNos)}')");
                    lst.ForEach(item =>
                    {
                        item.Owner_StopAreaName = string.Join(",", spaces?.Where(x => x.StopSpace_OwnerNo == item.Owner_No).Select(x => x.StopSpace_AreaName).ToList());
                        var carno = cars?.FindAll(x => x.Car_OwnerNo == item.Owner_No)?.Select(x => x.Car_CarNo).ToList();
                        if (carno?.Count > 0)
                        {
                            item.Owner_CarCardNo = string.Join(",", carno);
                            if (incars.Find(x => carno.Contains(x.InCar_CarNo)) != null) item.Owner_IsInPark = 1;
                        }
                        item.Owner_CardName = cctList?.Find(x => x.CarCardType_No == item.Owner_CardTypeNo)?.CarCardType_Name ?? "";
                        item.Owner_AddName = adminList?.Find(x => x.Admins_ID == item.Owner_AddID)?.Admins_Name ?? "";
                        item.Owner_IsMoreCar = cctList?.Find(x => x.CarCardType_No == item.Owner_CardTypeNo)?.CarCardType_IsMoreCar ?? 0;
                        var cardnoes = cars?.FindAll(x => x.Car_OwnerNo == item.Owner_No && !string.IsNullOrEmpty(x.Car_CardNo))?.Select(x => x.Car_CardNo).ToList();
                        if (cardnoes?.Count > 0)
                        {
                            cardnoes = cardnoes.Distinct().ToList();
                            item.Owner_ParkNo = string.Join(",", cardnoes);
                            //if (!regex.IsMatch(item.Owner_ParkNo)) item.Owner_ParkNo = "";
                        }
                        else
                        {
                            item.Owner_ParkNo = "";
                        }
                    });
                }
                #endregion

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return oModel;
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询车主列表", "查询车主列表发生异常:" + ex.ToString());

                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return oModel;
        }

        /// <summary>
        /// 获取车主信息
        /// </summary>
        public void GetOwner(string Owner_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Update.ToString(), adminSession: lgAdmins))
                { MiniResponse.ResponseResult("无权限", false); return; }

                Model.Owner model = BLL.Owner.GetEntity(Owner_No, false) ?? new Model.Owner();

                List<Model.CarExt> cars = BLL.Car.GetCarExtAllEntity("*", $"Car_OwnerNo = '{model.Owner_No}'");

                List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{model.Owner_No}'");

                MiniResponse.ResponseResult("读取成功", true, new { model = model, cars = cars, spaces = spaces });
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询车主详情", "获取车主信息发生异常:" + ex.ToString());
                MiniResponse.ResponseResult("异常错误", false);
            }
        }

        /// <summary>
        /// 获取车主信息
        /// </summary>
        public void GetOwnerByNo(string Owner_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                    return;

                Model.Owner model = BLL.Owner.GetEntity(Owner_No, false) ?? new Model.Owner();
                MiniResponse.ResponseResult("读取成功", true, model);
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询车主详情", "获取车主信息发生异常:" + ex.ToString());
                MiniResponse.ResponseResult("异常错误", false);
            }
        }

        /// <summary>
        /// 查询车辆信息
        /// </summary>
        /// <param name="Car_CarNo"></param>
        /// <returns></returns>
        public IActionResult GetCarDetail(string Car_CarNo)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                return ResOk(false, "无权限");

            Model.Car model = BLL.Car.GetEntityByCarNo(Car_CarNo);

            return ResOk(true, "", model);
        }

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="jsonModel"></param>
        public IActionResult AddOwner(string jsonModel, string carJson, string spaceJson)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Add.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                string errmsg = string.Empty;
                List<Model.Car> carList = new List<Model.Car>();
                List<Model.Car> delCarList = new List<Model.Car>();
                List<string> newCarNoList = new List<string>();

                JObject obj = Utils.ClearModelRiskSQL<JObject>(jsonModel);
                Model.Owner model = Utils.ClearModelRiskSQL<Model.Owner>(jsonModel);
                if (obj == null || model == null) { return ResOk(false, "参数错误"); }

                List<Model.CarExt> cars = Utils.ClearListModelRiskSQL<Model.CarExt>(carJson);
                if (cars != null && cars.Count > 0)
                {
                    string blacklistMsg = string.Empty;

                    foreach (var car in cars)
                    {
                        car.Car_CardNo = car.Car_CardNo?.Trim();
                        //判断发卡长度不足10，前置补0
                        if (!string.IsNullOrWhiteSpace(car.Car_CardNo) && car.Car_CardNo.Length < 10)
                        {
                            car.Car_CardNo = car.Car_CardNo.PadLeft(10, '0');
                            if (car.Car_CarNo.Length < 7)
                            {
                                car.Car_CarNo = car.Car_CardNo;
                            }
                        }
                        if (string.IsNullOrEmpty(car.Car_CarNo)) { return ResOk(false, "车牌号不能为空"); }
                        car.Car_CarNo = car.Car_CarNo.Trim().Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");
                        if (car.Car_CarNo.Length < 7) { return ResOk(false, "车牌号不能少于7位"); }
                        if (cars.FindAll(x => x.Car_CarNo == car.Car_CarNo).Count > 1) { return ResOk(false, $"[{car.Car_CarNo}]车牌号不能重复"); }

                        // 检查是否为黑名单
                        var blacklistCar = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"BlackList_CarNo='{car.Car_CarNo}' AND BlackList_Status=1");
                        if (blacklistCar != null)
                        {
                            var now = DateTime.Now;
                            if (blacklistCar.BlackList_BeginTime <= now && now <= blacklistCar.BlackList_EndTime)
                            {
                                blacklistMsg += $"车牌号[{car.Car_CarNo}]在黑名单有效期内，请先删除黑名单。<br/>";
                            }
                            else if (now < blacklistCar.BlackList_BeginTime)
                            {
                                blacklistMsg += $"车牌号[{car.Car_CarNo}]已登记为黑名单（生效时间：{blacklistCar.BlackList_BeginTime:yyyy-MM-dd HH:mm:ss}），请先删除黑名单。<br/>";
                            }
                        }
                    }

                    // 如果有黑名单车牌，返回所有黑名单信息
                    if (!string.IsNullOrEmpty(blacklistMsg))
                    {
                        return ResOk(false, blacklistMsg.TrimEnd("<br/>".ToCharArray()));
                    }
                }

                List<Model.StopSpace> spaces = Utils.ClearListModelRiskSQL<Model.StopSpace>(spaceJson) ?? new List<Model.StopSpace>();

                if (string.IsNullOrEmpty(model.Owner_Space)) { return ResOk(false, $"系统车位号不能为空"); }
                if (Utils.IsSpecialChar(model.Owner_Space)) { return ResOk(false, $"系统车位号不能带有特殊符号"); }
                if (Utils.IsLastCharLetter(model.Owner_Space)) { return ResOk(false, $"系统车位号最后一位不能是字母"); }

                var parameters1 = new { Owner_Space = model.Owner_Space };
                Model.Owner isExist = BLL.Owner.GetEntity("Owner_No", "Owner_Space=@Owner_Space", parameters1);
                if (isExist != null) { return ResOk(false, $"系统车位号[{model.Owner_Space}]已存在，请重新生成"); }

                model.Owner_ParkNo = parking.Parking_No;
                model.Owner_No = Utils.CreateNumberWith();
                model.Owner_AddID = lgAdmins?.Admins_ID;
                model.Owner_AddTime = DateTimeHelper.GetNowTime();
                model.Owner_StartTime = Utils.StrToDateTime(model.Owner_StartTime.Value.ToString("yyyy-MM-dd 00:00:00"));
                model.Owner_EndTime = Utils.StrToDateTime(model.Owner_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                model.Owner_EnableOffline = 1;
                model.Owner_Balance = Utils.ObjectToDecimal(obj["Owner_Money"], 0);

                if (!BLL.Owner.checkParam(model, cars, ref spaces, ref carList, ref delCarList, ref newCarNoList, ref errmsg, out var isEditType, out var cct, out var payOrder, false,
                    Utils.ObjectToDecimal(obj["Owner_Money"], 0), Utils.ObjectToDecimal(obj["Owner_PayedMoney"], 0), lgAdmins)) { return ResOk(false, errmsg); }


                bool orderHandleResult = false;
                List<Model.AddInParkTempCar> addInCar = new List<Model.AddInParkTempCar>();

                //清缴场内费用
                var ret = PayedInParkCarFee(parking.Parking_No, model, cct, cars, cars, spaces, ref addInCar, lgAdmins, out errmsg, out orderHandleResult);
                if (!string.IsNullOrEmpty(ret)) { return Json(TyziTools.Json.ToObject<JObject>(ret)); }

                List<Model.PayPart> payPartList = BLL.CommonBLL.CreatePayPartList(null, payOrder, 3, null, null, lgAdmins);
                Model.PayColl payColl = new PayColl() { payOrderList = new List<Model.PayOrder>() { payOrder }, payPartList = payPartList };

                model.Owner_SpaceNum = spaces?.Sum(x => x.StopSpace_Number) ?? 1;


                List<Model.Ledger> ledgerList = null;
                if (model.Owner_Balance > 0 && model.Owner_CardType == 2)
                {
                    Model.Ledger ledger = new Model.Ledger()
                    {
                        Ledger_CarNo = carList?.FirstOrDefault()?.Car_CarNo,
                        Ledger_Space = model.Owner_Space,
                        Ledger_Type = 4,
                        Ledger_Code = 1,
                        Ledger_Money = model.Owner_Balance,
                        Ledger_BeforeMoeny = 0,
                        Ledger_AfterMoeny = model.Owner_Balance,
                        Ledger_Time = DateTime.Now,
                        Ledger_Remark = "登记"
                    };
                    ledgerList = new List<Model.Ledger>() { ledger };
                }
                int newID = BLL.Owner.Insert(model, carList, null, spaces, payColl, lgAdmins, ledgerList);
                if (newID > 0)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, BLL.Owner.GetUpdateLogs(model, carList, spaces), SecondIndex.Owner);
                        try
                        {
                            try
                            {
                                BLL.Owner.InsertWhiteRecord(carList);

                                if (orderHandleResult && addInCar.Count > 0)
                                {
                                    Task.Delay(100).Wait();
                                    foreach (var item in addInCar)
                                    {
                                        Task.Delay(10).Wait();
                                        var orders = item.orders;
                                        var details = item.details;

                                        orders?.ForEach(x => { DataCache.ParkOrder.Del(x.ParkOrder_No); });

                                        var orderRes = BLL.ParkOrder.CarInComplete(orders, details);
                                        if (orderRes > 0)
                                        {
                                            var outOrder = orders.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out);
                                            var outDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == outOrder.ParkOrder_No);
                                            BLL.ParkOrderApi.CarOut(parking.Parking_No, new ResBodyDataOut(new List<Model.ParkOrder>() { outOrder }, outDetails, null, null, null, null, null, null));

                                            var inOrder = orders.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In);
                                            var inDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == inOrder.ParkOrder_No);

                                            var rempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { inOrder }, inDetails);
                                            BLL.ParkOrderApi.CarIn(parking.Parking_No, rempdata);
                                        }
                                    }
                                }

                                int count = 0;
                                carList?.ForEach(car =>
                                {
                                    BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, model, count == 0 ? payOrder : null, spaces);
                                    count++;
                                });
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"固定车新增处理异常.：【{string.Join(',', carList?.Select(x => x.Car_CarNo))}】" + ex.ToString());
                            }

                            var sendData = new Model.API.PushResultParse.CarOwner() { Item1 = model, Item2 = carList, Item3 = spaces, Item5 = payColl };
                            var r = PushSync(Model.API.PushAction.WebToSentry, sendData, new List<Model.SentryHost>(), "addcarowner", dataType: DataTypeEnum.Owner, Desc: $"新增{model?.Owner_Space}");
                            if (r == null)
                            {
                                Push(Model.API.PushAction.Add, sendData, new List<Model.SentryHost>(), "addcarowner", dataType: DataTypeEnum.Owner, Desc: $"新增{model?.Owner_Space}");
                                //return ResOk(false, "保存成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车更新成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', carList?.Select(x => x.Car_CarNo))}）");
                            }
                            List<Model.API.ResPushHost> failRes = r.Where(x => x.success == false).ToList();
                            if (failRes.Count > 0)
                            {
                                Push(Model.API.PushAction.Add, sendData, new List<Model.SentryHost>(), "addcarowner", dataType: DataTypeEnum.Owner, Desc: $"新增{model?.Owner_Space}");
                                //return ResOk(false, $"保存成功，但未能同步数据至岗亭（{string.Join(",", failRes.GroupBy(x => x.hostname).Select(m => m.Key))}），请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车更新成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', carList?.Select(x => x.Car_CarNo))}）");
                            }


                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"固定车新增处理异常：【{string.Join(',', carList?.Select(x => x.Car_CarNo))}】" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    var msg = "保存成功";
                    var isMoreCar = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, model.Owner_CardTypeNo)?.CarCardType_IsMoreCar ?? 0;
                    var spanceCount = spaces?.Sum(x => x.StopSpace_Number) ?? 0;
                    if (carList?.Count > 1 && isMoreCar != 1 && spanceCount > 0)
                    {
                        msg += "，车牌类型未启用一位多车";
                    }

                    var isReg = BLL.Owner.CheckRegCar(carList.Select(x => x.Car_CarNo).ToList(), ref msg, false, true, true);

                    return ResOk(true, msg, isReg);
                }
                else
                {
                    return ResOk(false, "保存失败");
                }

            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "新增车主", "新增车主发生异常:" + ex.ToString());
                return ResOk(false, "异常错误");
            }
        }

        /// <summary>
        /// 更新
        /// </summary>
        public IActionResult UpdateOwner(string jsonModel, string carJson, string spaceJson)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Update.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                string errmsg = string.Empty;
                List<Model.Car> carList = new List<Model.Car>();
                List<Model.Car> delCarList = new List<Model.Car>();
                List<string> newCarNoList = new List<string>();

                Model.Owner model = Utils.ClearModelRiskSQL<Model.Owner>(jsonModel);
                if (model == null) { return ResOk(false, "参数错误"); }
                Model.Owner old = BLL.Owner.GetEntity(model.Owner_No, false);
                if (old == null) { return ResOk(false, "未找到车主信息"); }

                if (model.Owner_Space != old.Owner_Space)
                {
                    if (!Powermanage.PowerCheck("Owner", PowerEnum.Create.ToString(), false, false)) return ResOk(false, "禁止修改系统车位号");
                }

                model = Utils.Returnobj(old, model);

                List<Model.CarExt> cars = Utils.ClearListModelRiskSQL<Model.CarExt>(carJson);
                if (cars != null && cars.Count > 0)
                {
                    string blacklistMsg = string.Empty;

                    foreach (var car in cars)
                    {
                        car.Car_CardNo = car.Car_CardNo?.Trim();
                        //判断发卡长度不足10，前置补0
                        if (!string.IsNullOrWhiteSpace(car.Car_CardNo) && car.Car_CardNo.Length < 10)
                        {
                            car.Car_CardNo = car.Car_CardNo.PadLeft(10, '0');
                            if (car.Car_CarNo.Length < 7)
                            {
                                car.Car_CarNo = car.Car_CardNo;
                            }
                        }

                        if (string.IsNullOrEmpty(car.Car_CarNo)) { return ResOk(false, "车牌号不能为空"); }
                        car.Car_CarNo = car.Car_CarNo.Trim().Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");
                        if (car.Car_CarNo.Length < 7) { return ResOk(false, "车牌号不能少于7位"); }
                        if (cars.FindAll(x => x.Car_CarNo == car.Car_CarNo).Count > 1) { return ResOk(false, $"[{car.Car_CarNo}]车牌号不能重复"); }

                        // 检查是否为黑名单
                        var blacklistCar = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"BlackList_CarNo='{car.Car_CarNo}' AND BlackList_Status=1");
                        if (blacklistCar != null)
                        {
                            var now = DateTime.Now;
                            if (blacklistCar.BlackList_BeginTime <= now && now <= blacklistCar.BlackList_EndTime)
                            {
                                blacklistMsg += $"车牌号[{car.Car_CarNo}]在黑名单有效期内，请先删除黑名单。<br/>";
                            }
                            else if (now < blacklistCar.BlackList_BeginTime)
                            {
                                blacklistMsg += $"车牌号[{car.Car_CarNo}]已登记为黑名单（生效时间：{blacklistCar.BlackList_BeginTime:yyyy-MM-dd HH:mm:ss}），请先删除黑名单。<br/>";
                            }
                        }
                    }

                    // 如果有黑名单车牌，返回所有黑名单信息
                    if (!string.IsNullOrEmpty(blacklistMsg))
                    {
                        return ResOk(false, blacklistMsg.TrimEnd("<br/>".ToCharArray()));
                    }
                }
                List<Model.StopSpace> spaces = Utils.ClearListModelRiskSQL<Model.StopSpace>(spaceJson);
                List<Model.StopSpace> oldSpaces = BLL.BaseBLL._GetAllEntity(new StopSpace(), "*",
                   $" StopSpace_OwnerNo ='{model.Owner_No}' ");

                model.Owner_EditID = lgAdmins?.Admins_ID;
                model.Owner_EditTime = DateTimeHelper.GetNowTime();
                model.Owner_ParkNo = old.Owner_ParkNo;
                model.Owner_StartTime = old.Owner_StartTime;
                model.Owner_EndTime = old.Owner_EndTime;

                bool orderHandleResult = false;
                List<Model.AddInParkTempCar> addInCar = new List<Model.AddInParkTempCar>();

                if (!BLL.Owner.checkParam(model, cars, ref spaces, ref carList, ref delCarList, ref newCarNoList, ref errmsg, out var isEditType,
                    out var cct, out var payOrder, true, 0, 0, lgAdmins)) { return ResOk(false, errmsg); }

                if (model.Owner_CardType != old.Owner_CardType && model.Owner_CardTypeNo != old.Owner_CardTypeNo) model.Owner_Balance = 0;
                if (cct.CarCardType_Type != 2) model.Owner_Balance = 0;
                if (model.Owner_CardType == 2) model.Owner_SpaceNum = 0;

                if (isEditType || model.Owner_CardTypeNo != old.Owner_CardTypeNo)
                {
                    //清缴场内费用
                    var ret = PayedInParkCarFee(parking.Parking_No, model, cct, cars, cars, spaces, ref addInCar, lgAdmins, out errmsg, out orderHandleResult);
                    if (!string.IsNullOrEmpty(ret)) { return Json(TyziTools.Json.ToObject<JObject>(ret)); }
                }
                else
                {
                    if (newCarNoList.Count > 0)
                    {
                        //清缴场内费用
                        var ret = PayedInParkCarFee(parking.Parking_No, model, cct, cars, cars.Where(x => newCarNoList.Find(m => m == x.Car_CarNo) != null).ToList(), spaces, ref addInCar, lgAdmins, out errmsg, out orderHandleResult);
                        if (!string.IsNullOrEmpty(ret)) { return Json(TyziTools.Json.ToObject<JObject>(ret)); }
                    }
                }

                model.Owner_SpaceNum = spaces?.Sum(x => x.StopSpace_Number) ?? 1;
                int newID = BLL.Owner.Insert(model, carList, delCarList, spaces, lgAdmins);
                if (newID > 0)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {

                        try
                        {
                            List<ResPushHost> clearsPush = new List<ResPushHost>();
                            try
                            {
                                // 先删除旧车辆的白名单，避免非机动车卡号冲突导致新白名单被误删
                                delCarList?.ForEach(car =>
                                {
                                    BLL.PushEvent.MthCarFailSend(parking.Parking_Key, car.Car_CarNo, "", DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"));
                                });

                                // 再下发新车辆的白名单
                                BLL.Owner.InsertWhiteRecord(carList);

                                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, BLL.Owner.GetUpdateLogs(model, carList, spaces, delCarList, oldSpaces, old), SecondIndex.Owner);

                                carList?.ForEach(car =>
                                {
                                    BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, model);
                                });

                                #region 若车辆已在场内,则修改场内订单
                                List<Model.ParkOrder> orders = null;
                                if (delCarList?.Count > 0)
                                {
                                    string sqlCarNo = string.Join("','", delCarList.Select(x => x.Car_CarNo));
                                    orders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_StatusNo={Model.EnumParkOrderStatus.In} AND ParkOrder_CarNo in ('{sqlCarNo}')");

                                    orders?.ForEach(x =>
                                    {
                                        x.ParkOrder_OwnerNo = model.Owner_No;
                                        x.ParkOrder_OwnerName = model.Owner_Name;

                                        DataCache.ParkOrder.Del(x.ParkOrder_No);
                                    });

                                    //占用车位的订单数
                                    var isLiftCount = orders?.FindAll(x => x.ParkOrder_IsLift == 1)?.Count ?? 0;

                                    BLL.ParkOrder.UpdateByLogoutCar(parking.Parking_No, orders, out var datas);

                                    if (isLiftCount > 0 && cct.CarCardType_Type != 2 && cct.CarCardType_IsMoreCar == 1)
                                    {
                                        //获取当前车主的所有在场车辆
                                        var incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "InCar_ParkOrderNo", $"InCar_CarNo in('{string.Join("','", carList.Select(x => x.Car_CarNo))}') and InCar_Status=200");
                                        if (incarList.Count > 0)
                                        {
                                            //修改后的订单集合
                                            var modifyOrders = new List<Model.ParkOrder>();
                                            var modifyDetails = new List<Model.OrderDetail>();

                                            //获取当前车主的所有在场车辆的订单
                                            List<string> incarOrderNos = incarList.Select(x => x.InCar_ParkOrderNo).ToList();
                                            List<string> delOrders = orders.Select(x => x.ParkOrder_No).ToList();
                                            delOrders.AddRange(incarOrderNos);

                                            //获取当前车主的所有在场车辆的订单（不包含注销车辆的订单）
                                            var orders2 = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_StatusNo={Model.EnumParkOrderStatus.In} AND ParkOrder_No in ('{string.Join("','", incarOrderNos)}')");
                                            //获取当前车主的所有在场车辆的订单明细（包含注销车辆的明细）
                                            var detail2 = BLL.OrderDetail.GetAllEntity("*", $"orderdetail_ParkOrderNo in ('{string.Join("','", delOrders)}')");

                                            //占用车位的订单,将占用的车位升降到其它订单
                                            var liftOrders = orders?.FindAll(x => x.ParkOrder_IsLift > 0);
                                            var listDetails = detail2?.FindAll(x => liftOrders.Find(m => m.ParkOrder_No == x.OrderDetail_ParkOrderNo) != null);
                                            var liftOrderNos = liftOrders?.Select(x => x.ParkOrder_No).ToList();
                                            var otherOrders = orders2?.FindAll(x => x.ParkOrder_IsLift == 0);
                                            var otherOrderNos = otherOrders?.Select(x => x.ParkOrder_No).ToList();

                                            if (liftOrderNos?.Count > 0 && otherOrderNos?.Count > 0)
                                            {
                                                //按照进场时间排序
                                                otherOrders = otherOrders.OrderBy(x => x.ParkOrder_EnterTime).ToList();
                                                var nowCarsList = carList.Copy();
                                                nowCarsList.AddRange(delCarList);
                                                for (var i = 0; i < otherOrders.Count; i++)
                                                {
                                                    //占用车位的订单数
                                                    if (isLiftCount <= 0) break;

                                                    //占用车位的订单
                                                    var outOrder = liftOrders[isLiftCount - 1];

                                                    //判断车场设置是否允许智能升降
                                                    var policyParkArea = BLL.PolicyArea.GetEntity(outOrder.ParkOrder_ParkAreaNo);
                                                    if (policyParkArea == null) continue;
                                                    if (policyParkArea.PolicyArea_MoreCar != 1) continue;

                                                    //占用车位的订单(修改成出场)
                                                    outOrder.ParkOrder_OutTime = DateTime.Now.AddSeconds(-1);
                                                    outOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;

                                                    //占用车位的订单的所有明细（修改成出场）
                                                    var outDetail = detail2.FindAll(x => x.OrderDetail_ParkOrderNo == outOrder.ParkOrder_No);
                                                    outDetail.ForEach(x => { x.OrderDetail_OutTime = outOrder.ParkOrder_OutTime; x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out; });

                                                    //未占用车位的第一辆车订单
                                                    var item2 = otherOrders[i];
                                                    //未占用车位的第一辆车订单的所有明细
                                                    var details = detail2.FindAll(x => x.OrderDetail_ParkOrderNo == otherOrders[i].ParkOrder_No);
                                                    details = details.OrderBy(x => x.OrderDetail_EnterTime).ToList();

                                                    //占用车位的所有明细
                                                    var sumDetails = detail2.FindAll(x => liftOrders.Find(m => m.ParkOrder_No == x.OrderDetail_ParkOrderNo) != null);
                                                    //将占用车位的订单的明细添加到未占用车位的第一辆车订单的明细中
                                                    sumDetails.AddRange(outDetail);
                                                    //将占用车位的订单明细添加到未占用车位的订单中
                                                    orders2.Add(outOrder);

                                                    //将注销车辆添加进场记录
                                                    incarList.Add(new InCar()
                                                    {
                                                        InCar_CarNo = outOrder.ParkOrder_CarNo,
                                                        InCar_ParkOrderNo = outOrder.ParkOrder_No,
                                                        InCar_Status = 200,
                                                        InCar_CarCardTypeNo = outOrder.ParkOrder_CarCardType,
                                                        InCar_EnterTime = outOrder.ParkOrder_EnterTime,
                                                        InCar_ParkAreaNo = outOrder.ParkOrder_ParkAreaNo,
                                                    });

                                                    //将在场占用车位的订单明细进行车位升降（改出场）
                                                    var lastDeatils = details.Last();
                                                    lastDeatils.OrderDetail_OutTime = DateTime.Now;
                                                    lastDeatils.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;

                                                    //未占用车位的订单进行车位升降（新增入场）
                                                    var newOrderDetail = TyziTools.Json.ToModel<Model.OrderDetail>(TyziTools.Json.ToString(lastDeatils));
                                                    newOrderDetail.OrderDetail_No = "OC" + Utils.CreateNumber;
                                                    newOrderDetail.OrderDetail_EnterTime = DateTime.Now;
                                                    newOrderDetail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                                                    newOrderDetail.OrderDetail_Remark = $"[{outOrder.ParkOrder_CarNo}]注销，由[{item2.ParkOrder_CarNo}]占用车位";

                                                    //当前车辆新增入场的明细（当作车辆新入场）
                                                    List<Model.OrderDetail> nowDetails = new List<Model.OrderDetail>();
                                                    nowDetails.Add(newOrderDetail);

                                                    //修改订单
                                                    BLL.ParkOrder.ModifyAnyToChangeOrder(carList.Find(x => x.Car_CarNo == otherOrders[i].ParkOrder_CarNo), model, cct, ref item2, ref nowDetails, incarList, orders2, sumDetails, spaces, nowCarsList);

                                                    //如果新增入场的明细未能占到车位，则将改出场的明细改成入场
                                                    var modifyDetail = nowDetails.Find(x => x.OrderDetail_No == newOrderDetail.OrderDetail_No);
                                                    if (modifyDetail.orderdetail_IsCharge == 0)
                                                    {
                                                        lastDeatils.OrderDetail_OutTime = null;
                                                        lastDeatils.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                                                    }
                                                    else
                                                    {
                                                        details.AddRange(nowDetails);
                                                        modifyOrders.Add(item2);
                                                        modifyDetails.AddRange(details);
                                                    }

                                                    isLiftCount--;
                                                }
                                            }

                                            if (modifyOrders.Count > 0)
                                            {
                                                var ret = BLL.ParkOrder.CarInComplete(modifyOrders, modifyDetails);
                                                if (ret > 0)
                                                {
                                                    Push(Model.API.PushAction.Edit, new Model.API.PushResultParse.UpdateParkOrder() { Item1 = modifyOrders, Item2 = modifyDetails }, new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.Owner, Desc: $"更新{model?.Owner_Space},订单重新入场");
                                                }
                                            }
                                        }
                                    }

                                    datas?.ForEach(item =>
                                    {
                                        var r = PushSync(Model.API.PushAction.Edit, item, new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.EnterCar, Desc: $"注销车辆{string.Join(",", item.Item1?.Select(x => x.ParkOrder_CarNo))}");
                                        if (r != null) clearsPush.AddRange(r);
                                    });
                                }
                                #endregion

                                if (orderHandleResult && addInCar.Count > 0)
                                {
                                    Task.Delay(100).Wait();
                                    foreach (var item in addInCar)
                                    {
                                        var orders1 = item.orders;
                                        var details = item.details;
                                        Task.Delay(10).Wait();
                                        var orderRes = BLL.ParkOrder.CarInComplete(orders1, details);
                                        if (orderRes > 0)
                                        {
                                            var outOrder = orders1.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out);
                                            var outDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == outOrder.ParkOrder_No);
                                            BLL.ParkOrderApi.CarOut(parking.Parking_No, new ResBodyDataOut(new List<Model.ParkOrder>() { outOrder }, outDetails, null, null, null, null, null, null));

                                            var inOrder = orders1.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In);
                                            var inDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == inOrder.ParkOrder_No);

                                            var rempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { inOrder }, inDetails);
                                            BLL.ParkOrderApi.CarIn(parking.Parking_No, rempdata);
                                        }
                                    }
                                }

                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"固定车更新处理异常.：【{string.Join(',', carList?.Select(x => x.Car_CarNo))}】" + ex.ToString());
                            }

                            var sendData = new Model.API.PushResultParse.CarOwner() { Item1 = model, Item2 = carList, Item3 = spaces, Item4 = delCarList };
                            var r = PushSync(Model.API.PushAction.WebToSentry, sendData, new List<Model.SentryHost>(), "updatecarowner", dataType: DataTypeEnum.Owner, Desc: $"更新{(delCarList?.Count > 0 ? $",删除{string.Join(",", delCarList.Select(x => x.Car_CarNo))}" : "")}");
                            if (r == null)
                            {
                                //return ResOk(false, "保存成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车更新成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', carList?.Select(x => x.Car_CarNo))}）");
                            }
                            clearsPush.AddRange(r);
                            List<Model.API.ResPushHost> failRes = clearsPush.Where(x => x.success == false).ToList();
                            if (failRes.Count > 0)
                            {
                                Push(Model.API.PushAction.Edit, sendData, new List<Model.SentryHost>(), "updatecarowner", dataType: DataTypeEnum.Owner, Desc: $"更新{(delCarList?.Count > 0 ? $",删除{string.Join(",", delCarList.Select(x => x.Car_CarNo))}" : "")}");
                                //return ResOk(false, $"保存成功，但未能同步数据至岗亭（{string.Join(",", failRes.GroupBy(x => x.hostname).Select(m => m.Key))}），请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车更新成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', carList?.Select(x => x.Car_CarNo))}）");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"车辆登记修改异常：【{BLL.Owner.GetUpdateLogs(model, carList, spaces, delCarList, oldSpaces, old)}】" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    var msg = "保存成功";
                    var isMoreCar = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, model.Owner_CardTypeNo)?.CarCardType_IsMoreCar ?? 0;
                    var spanceCount = spaces?.Sum(x => x.StopSpace_Number) ?? 0;
                    if (carList?.Count > 1 && isMoreCar != 1 && spanceCount > 0)
                    {
                        msg += "，车牌类型未启用一位多车";
                    }

                    var isReg = BLL.Owner.CheckRegCar(carList.Select(x => x.Car_CarNo).ToList(), ref msg, false, true, true);
                    return ResOk(true, msg, isReg);
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"异常，车主[{old.Owner_Name}]：{BLL.Owner.GetUpdateLogs(model, carList, spaces, delCarList, oldSpaces, old)}", SecondIndex.Owner);
                    return ResOk(false, "保存失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "修改车主信息", "更新车主发生异常:" + ex.ToString());
                return ResOk(false, "异常错误");
            }
        }

        /// <summary>
        /// 删除车主
        /// </summary>
        /// <param name="Owner_ID"></param>
        public void DeleteOwner(string ownerNoArray, bool inParkCar)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Delete.ToString(), adminSession: lgAdmins))
                { MiniResponse.ResponseResult("无权限", false); return; }

                if (string.IsNullOrEmpty(ownerNoArray)) { MiniResponse.ResponseResult("请选择", false); return; }

                var parameters = new { Owner_No = ownerNoArray.Split(',').ToList() };
                List<Model.Owner> ownerList = BLL.Owner.GetAllEntity("Owner_No,Owner_Name,Owner_Space,Owner_Balance", $" Owner_No in @Owner_No ", parameters);
                if (ownerList == null || ownerList.Count == 0) { MiniResponse.ResponseResult("车主信息不存在", false); return; }

                List<Model.Car> carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo in @Owner_No ", parameters);

                List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo in @Owner_No", parameters);

                //var ownerNameList = new List<string>();
                //if (carList != null && carList.Count > 0)
                //{
                //    carList.ForEach(x =>
                //    {
                //        if (!string.IsNullOrEmpty(x.Car_OwnerNo))
                //        {
                //            var owner = ownerList.Find(y => y.Owner_No == x.Car_OwnerNo);
                //            if (owner != null) ownerNameList.Add(string.IsNullOrEmpty(owner.Owner_Name) ? owner.Owner_Space : owner.Owner_Name);
                //        }
                //    });
                //    ownerNameList = ownerNameList.Distinct().ToList();


                //    carList?.ForEach(car =>
                //    {
                //        BLL.PushEvent.MthCarFailSend(parking.Parking_Key, car.Car_CarNo, "", DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"));
                //    });

                //    //if (ownerNameList.Count > 0)
                //    //{
                //    //    MiniResponse.ResponseResult($"[{string.Join(",", ownerNameList)}]已绑定车辆,不允许删除", false); return;
                //    //}
                //}

                var parameters2 = new { ParkOrder_CarNo = carList.Select(x => x.Car_CarNo).ToList() };
                List<Model.ParkOrder> orders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_StatusNo={Model.EnumParkOrderStatus.In} AND ParkOrder_CarNo in @ParkOrder_CarNo", parameters: parameters2);
                if (inParkCar)
                {
                    if (orders.Count > 0)
                    {
                        string carstring = "";
                        var carlist = orders.Select(x => x.ParkOrder_CarNo).ToList();
                        if (carlist.Count > 50)
                        {
                            carlist = carlist.Take(50).ToList();
                            carstring = string.Join("、", orders.Select(x => x.ParkOrder_CarNo)) + "..." + orders.Count + "辆车";
                        }
                        else
                        {
                            carstring = string.Join("、", orders.Select(x => x.ParkOrder_CarNo));
                        }


                        MiniResponse.ResponseResult($"车牌【{string.Join("、", orders.Select(x => x.ParkOrder_CarNo))}】已在场内，将变更该车辆的计费类型以及入场时间，从当前时间开始重新计费，确认吗？", true, 1); return;
                    }
                }

                if (BLL.Owner.DeleteList(ownerList, carList, spaces, lgAdmins) >= 0)
                {

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, BLL.Owner.GetDelLogs(lgAdmins, ownerList, spaces), SecondIndex.Owner);

                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, async () =>
                    {
                        #region 删除储值车并记录储值余额日志
                        try
                        {
                            var ownerMap = ownerList.ToDictionary(o => o.Owner_No, o => o);

                            foreach (var car in carList)
                            {
                                if (car.Car_Category == "3657") // 储值车类型
                                {
                                    if (ownerMap.TryGetValue(car.Car_OwnerNo, out var owner))
                                    {
                                        decimal balance = owner?.Owner_Balance ?? 0m;
                                        string logMessage = $"{car.Car_CarNo} 储值车余额: {balance:0.00} 元";

                                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, logMessage, SecondIndex.Owner);

                                        await Task.Delay(1);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            string errMsg = $"记录储值车余额日志时发生异常：{ex.Message}";
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "储值车删除", errMsg);
                        }
                        #endregion

                        #region 修改场内订单
                        try
                        {
                            BLL.ParkOrder.UpdateByLogoutCar(parking.Parking_No, orders, out var datas);
                            datas?.ForEach(item =>
                            {
                                PushSync(Model.API.PushAction.Edit, item, new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.Owner, Desc: $"删除车辆，修改场内订单");
                            });

                            orders?.ForEach(item =>
                            {
                                SentryBox.CommHelper.CheckConfirmResultForCarNo(item?.ParkOrder_CarNo, item?.ParkOrder_No);
                            });

                        }
                        catch (Exception ex)
                        {
                            BLL.SystemLogs.AddLog(lgAdmins, "车辆注销", $"车辆注销，修改场内订单发生异常:{ex.ToString()}");
                        }
                        #endregion

                        #region 删除车辆缓存
                        try
                        {
                            var sendData = new Model.API.PushResultParse.DelCarOwner() { Item1 = ownerList, Item2 = spaces, Item3 = carList };
                            var r = PushSync(Model.API.PushAction.Delete, sendData, new List<Model.SentryHost>(), "delcarowner", dataType: DataTypeEnum.Owner, Desc: $"删除{string.Join(",", ownerList.Select(x => x.Owner_Space))}");
                            if (r == null)
                            {
                                //MiniResponse.ResponseResult("删除成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况", false);
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车删除成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', carList?.Select(x => x.Car_CarNo))}）");
                                return;
                            }
                            List<Model.API.ResPushHost> failRes = r.Where(x => x.success == false).ToList();
                            if (failRes.Count > 0)
                            {
                                //MiniResponse.ResponseResult($"删除成功，但未能同步数据至岗亭（{string.Join(",", failRes.GroupBy(x => x.hostname).Select(m => m.Key))}），请检查服务器与岗亭网络通讯情况", false);
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"固定车删除成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况（{string.Join(',', carList?.Select(x => x.Car_CarNo))}）");
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "固定车注销处理异常：" + ex.ToString());
                        }
                        #endregion

                        #region 车辆注销上报平台
                        try
                        {
                            foreach (var x in carList)
                            {
                                try
                                {
                                    BLL.PushEvent.MthCarFailSend(parking.Parking_Key, x.Car_CarNo, "", DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"));
                                    await Task.Delay(50);
                                }
                                catch (Exception e)
                                {
                                    BLL.SystemLogs.AddLog(lgAdmins, "车辆注销失败", $"车辆注销发生异常:{x.Car_CarNo},{e.ToString()}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "固定车注销处理异常：" + ex.ToString());
                        }
                        #endregion

                    });

                    MiniResponse.ResponseResult("删除成功", true);
                }
                else
                {
                    MiniResponse.ResponseResult("Exception", false);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "删除车主", "删除车主发生异常:" + ex.ToString());
                MiniResponse.ResponseResult("Exception", false);
            }
        }

        /// <summary>
        /// 推送到线上
        /// </summary>
        /// <param name="Owner_ID"></param>
        public async Task PushOnlineOwner(string ownerNoArray)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Pulldown.ToString(), adminSession: lgAdmins))
                { MiniResponse.ResponseResult("无权限", false); return; }

                if (string.IsNullOrEmpty(ownerNoArray)) { MiniResponse.ResponseResult("请选择", false); return; }

                if (!AppBasicCache.IsWritePushEventMsg) { MiniResponse.ResponseResult("未启用云平台服务", false); return; }

                var parameters = new { Owner_NoList = ownerNoArray.Split(',').ToList() };
                List<Model.Owner> ownerList = BLL.Owner.GetAllEntity("Owner_No,Owner_Name,Owner_Space,Owner_SpaceNum,Owner_Name,Owner_Phone,Owner_Address,Owner_ParkSpace", $" Owner_No in @Owner_NoList ", parameters);
                if (ownerList == null || ownerList.Count == 0) { MiniResponse.ResponseResult("车主信息不存在", false); return; }

                var parameters2 = new { Car_OwnerNo = ownerList.Select(x => x.Owner_No).ToList() };
                List<Model.Car> carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo in @Car_OwnerNo", parameters2);

                var parameters3 = new { StopSpace_OwnerNo = ownerList.Select(x => x.Owner_No).ToList() };
                List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo in @StopSpace_OwnerNo", parameters3);

                int count = 0;
                if (carList.Count > 0)
                {
                    foreach (var car in carList)
                    {
                        try
                        {
                            object r = BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, ownerList.Find(x => x.Owner_No == car.Car_OwnerNo), null, spaces);
                            if (Utils.ObjectToInt(r, 0) != 0)
                                count++;
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "推送固定车到线上出现异常：" + ex.ToString());
                        }
                        await Task.Delay(20);
                    }
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Push, $"成功添加任务，推送 {count} 固定车到线上", SecondIndex.Owner);
                }
                else
                {
                    MiniResponse.ResponseResult("选中的车位没有绑定车辆信息", true, count);
                    return;
                }

                MiniResponse.ResponseResult("推送成功", true, count);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "推送固定车到线上出现异常.：" + ex.ToString());
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "推送到线上", "推送到线上发生异常:" + ex.ToString());
                MiniResponse.ResponseResult("异常错误", false);
            }

            return;
        }

        /// <summary>
        /// 车辆列表
        /// </summary>
        public void GetCarExtList(int pageIndex, int pageSize, string conditionParam)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                return;

            Response.WriteAsync(SearchCarExtList(pageIndex, pageSize, conditionParam).ParseJson());
        }

        /// <summary>
        /// 查询车辆列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        private Model.PageResult SearchCarExtList(int pageIndex, int pageSize, string conditionParam, bool isCount = true)
        {
            try
            {
                if (pageSize > 1000)
                    if (isCount)
                        return new Model.PageResult(-1, "", 0, null);


                string sqlwhere = "";
                Model.Car model = Utils.ClearModelRiskSQL<Model.Car>(conditionParam);

                sqlwhere += string.Format(" and Car_OwnerNo = @Car_OwnerNo ", model.Car_OwnerNo);
                if (!string.IsNullOrEmpty(model.Car_CarNo))
                    sqlwhere += string.Format(" and Car_CarNo like @Car_CarNo ", model.Car_CarNo);
                if (!string.IsNullOrEmpty(model.Car_OwnerName))
                    sqlwhere += string.Format("and Car_OwnerName like @Car_OwnerName ", model.Car_OwnerName);
                if (!string.IsNullOrEmpty(model.Car_OrderSpace))
                    sqlwhere += string.Format("and Car_Space like @Car_OrderSpace ", model.Car_OrderSpace);
                if (model.Car_Status != null)
                    sqlwhere += string.Format("and Car_Status = @Car_Status ", model.Car_Status);

                var parameters = new
                {
                    Car_OwnerNo = string.IsNullOrEmpty(model.Car_OwnerNo) ? null : $"%{model.Car_OwnerNo}%",
                    Car_CarNo = string.IsNullOrEmpty(model.Car_CarNo) ? null : $"%{model.Car_CarNo}%",
                    Car_OrderSpace = string.IsNullOrEmpty(model.Car_OrderSpace) ? null : $"%{model.Car_OrderSpace}%",
                    Car_Status = model.Car_Status != null ? model.Car_Status : null,
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.CarExt> lst = BLL.Car.GetExt2List("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return oModel;
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询车辆列表", "查询车辆列表发生异常:" + ex.ToString());

                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return oModel;
        }

        public IActionResult CheckSpace(string Owner_No)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Retweet.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");
            try
            {
                Model.Owner owner = BLL.Owner.GetEntity(Owner_No, false);
                if (owner == null) { return ResOk(false, $"车辆信息异常"); }
                if (owner.Owner_CardType != 2) { return ResOk(false, $"非储值车不能进行充值"); }

                List<Model.Car> cars = BLL.Car.GetAllEntity("Car_CarNo,Car_TypeNo", $"Car_OwnerNo = '{Owner_No}'");
                if (cars == null || cars.Count == 0) { return ResOk(false, "车位未绑定储值车"); }

                List<Model.CarCardType> cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Category,CarCardType_IsMoreCar,CarCardType_Category", $"CarCardType_No in ('{string.Join("','", cars.Select(x => x.Car_TypeNo).ToList())}')");
                if (cctList == null || cctList.Count == 0) { return ResOk(false, "车牌类型数据异常"); }

                //var ct2 = cctList.Find(x => x.CarCardType_Category == Model.EnumCarType.Prepaid.ToString() && x.CarCardType_IsMoreCar == 1);
                //if (ct2 == null)
                //{
                //    return ResOk(false, $"非储值车不能进行充值");
                //}
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "储值车充值", "储值车充值发生异常:" + ex.ToString());
                return ResOk(false, "数据异常");
            }

            return ResOk(true, "success");

        }

        public IActionResult RefundSpace(string Owner_No)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Refund.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");
            try
            {
                Model.Owner owner = BLL.Owner.GetEntity(Owner_No, false);
                if (owner == null) { return ResOk(false, $"车辆信息异常"); }
                if (owner.Owner_CardType != 2) { return ResOk(false, $"非储值车不能进行退费"); }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "储值车充值", "储值车充值发生异常:" + ex.ToString());
                return ResOk(false, "数据异常");
            }

            return ResOk(true, "success");

        }

        /// <summary>
        /// 车位充值
        /// </summary>
        public IActionResult SpaceCharge(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Retweet.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                JObject param = Utils.ClearModelRiskSQL<JObject>(jsonModel);

                if (param == null)
                {
                    return ResOk(false, "参数错误");
                }
                if (!Utils.IsParam(param, "Car_OwnerNo")) { return ResOk(false, "参数不能为空"); }
                string Car_OwnerNo = param["Car_OwnerNo"].ToString();
                string Car_Balance = param.ContainsKey("Car_Balance") ? param["Car_Balance"].ToString() : null;
                string Car_PayMoney = param.ContainsKey("Car_PayMoney") ? param["Car_PayMoney"].ToString() : null;
                string Car_PayType = param.ContainsKey("Car_PayType") ? param["Car_PayType"].ToString() : null;

                Model.Owner owner = BLL.Owner.GetEntity(Car_OwnerNo, false);
                if (owner == null) { return ResOk(false, "车位信息不存在"); }

                var oldOwner = owner.Copy();

                List<Model.Car> cars = BLL.Car.GetAllEntity("Car_CarNo,Car_TypeNo,Car_VehicleTypeNo", $"Car_OwnerNo = '{Car_OwnerNo}'");
                if (cars == null || cars.Count == 0) { return ResOk(false, "车位未绑定储值车"); }

                List<Model.CarCardType> cctList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Category,CarCardType_IsMoreCar,CarCardType_Category", $"CarCardType_No in ('{string.Join("','", cars.Select(x => x.Car_TypeNo).ToList())}')");
                if (cctList == null || cctList.Count == 0) { return ResOk(false, "车牌类型数据异常"); }

                var ct2 = cctList.Find(x => x.CarCardType_Category == Model.EnumCarType.Prepaid.ToString());
                if (ct2 == null)
                {
                    return ResOk(false, $"非储值车不能进行充值");
                }

                #region 支付订单
                Model.PayOrder payorder = new Model.PayOrder();
                payorder.PayOrder_CarCardTypeNo = cctList[0].CarCardType_No;
                payorder.PayOrder_CarTypeNo = cars[0].Car_VehicleTypeNo;
                payorder.PayOrder_CouponRecordNo = null;
                payorder.PayOrder_OrderTypeNo = Convert.ToString((int)Common.EnumOrderType.Wallet);
                payorder.PayOrder_ParkKey = parking.Parking_Key;
                payorder.PayOrder_ParkNo = parking.Parking_No;
                payorder.PayOrder_Category = Model.EnumCarType.Prepaid.ToString();
                payorder.PayOrder_CarNo = "";
                payorder.PayOrder_PayedTime = DateTimeHelper.GetNowTime();
                payorder.PayOrder_PayType = Utils.StrToInt(Car_PayType, 0);
                payorder.PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                payorder.PayOrder_Status = 1;
                payorder.PayOrder_Time = DateTimeHelper.GetNowTime();
                payorder.PayOrder_OwnerSpace = owner.Owner_Space;
                payorder.PayOrder_OwnerName = owner.Owner_Name;
                payorder.PayOrder_OwnerNo = owner.Owner_No;
                #endregion

                payorder.PayOrder_TimeCountDesc = "车位储值车充值";
                payorder.PayOrder_Desc = "车位储值车充值";
                payorder.PayOrder_Money = Utils.ObjectToDecimal(Car_Balance, 0);
                owner.Owner_Balance = Utils.ObjectToDecimal(owner.Owner_Balance, 0) + payorder.PayOrder_Money;
                payorder.PayOrder_PayedMoney = Utils.ObjectToDecimal(Car_PayMoney, 0);

                payorder = BLL.PayOrder.CreatePayOrder(true, payorder, parking.Parking_Key, "PC", lgAdmins);
                payorder.PayOrder_DiscountMoney = payorder.PayOrder_Money - payorder.PayOrder_PayedMoney;

                List<Model.PayPart> payPartList = BLL.CommonBLL.CreatePayPartList(null, payorder, 1);
                Model.PayColl payColl = new Model.PayColl() { payOrderList = new List<Model.PayOrder>() { payorder }, payPartList = payPartList };

                var result = BLL.Car.AddCarOwner(null, owner, null, payColl);
                if (result > 0)
                {

                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Recharge, BLL.Owner.GetChargeLogs(owner, oldOwner, cars), SecondIndex.Owner);

                            int count = 0;
                            cars?.ForEach(car =>
                            {
                                try
                                {
                                    car.Car_BeginTime = owner.Owner_StartTime;
                                    car.Car_EndTime = owner.Owner_EndTime;
                                    BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, owner, count == 0 ? payorder : null);
                                    count++;
                                }
                                catch (Exception e)
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MainDebugLog, $"【{car?.Car_CarNo}】车位充值延期上报云平台异常", LogLevel.Error, e);
                                }
                            });

                            (Model.Car, Model.Owner, Model.PayColl) data = (null, owner, payColl);
                            var r = PushSync(Model.API.PushAction.Add, data, new List<Model.SentryHost>(), "addcar", dataType: DataTypeEnum.Owner, Desc: $"充值{owner.Owner_Space}");
                            if (r == null || r.Where(x => x.success == false).Count() > 0)
                            {
                                //return ResOk(false, $"充值成功，但未能同步数据至岗亭（{string.Join(",", failRes.GroupBy(x => x.hostname).Select(m => m.Key))}），请检查服务器与岗亭网络通讯情况");
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"充值成功，但未能同步数据至岗亭，车位号{owner.Owner_Space}：");
                            }
                            //BLL.PushEvent.MthCarRegister(parking.Parking_Key, null, owner, payorder);
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"车位充值处理异常，车位号{owner.Owner_Space}：" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "success");
                }
                else
                {
                    return ResOk(false, "充值失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "车位储值车充值", "车位储值车充值发生异常:" + ex.ToString());
                return ResOk(false, "异常错误");
            }
        }


        /// <summary>
        /// 批量授权
        /// </summary>
        /// <param name="jsonModel"></param>
        public IActionResult BathAuthSpace(string spaceJson, string ownerNoes)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Bind.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                string errmsg = string.Empty;
                List<Model.Car> carList = new List<Model.Car>();
                List<Model.Car> delCarList = new List<Model.Car>();
                List<string> newCarNoList = new List<string>();

                if (string.IsNullOrEmpty(ownerNoes)) { return ResOk(false, "请选择车主"); }
                List<Model.StopSpace> spaces = Utils.ClearListModelRiskSQL<Model.StopSpace>(spaceJson) ?? new List<Model.StopSpace>();
                if (spaces.Count == 0) { return ResOk(false, "请选择区域"); }

                var res = BLL.Owner.BathAuthArea(spaceJson, ownerNoes, out List<Model.Owner> owneres);
                if (res)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            string log = "";
                            spaces.ForEach(x =>
                            {
                                log += $"【可停区域：{x.StopSpace_AreaName},车位数：{x.StopSpace_Number}】";
                            });

                            if (owneres != null)
                            {
                                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.BatchAuth, $"车位号：{string.Join(",", owneres?.Select(x => x?.Owner_Space ?? "")) ?? "无"}，区域授权：{log}", SecondIndex.Owner);
                                _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                                {
                                    try
                                    {
                                        if (owneres != null && owneres.Count > 0)
                                        {
                                            List<Model.CarOwnerExt> coExtList = BLL.Car.GetCarOwnerAllEntity("*", $"Owner_No in('{string.Join("','", owneres.Select(x => x.Owner_No))}')");
                                            coExtList.ForEach(async coExt =>
                                            {
                                                var jo = TyziTools.Json.ToString(coExt);
                                                BLL.PushEvent.MthCarRegister(parking.Parking_Key, TyziTools.Json.ToObject<Model.Car>(jo), TyziTools.Json.ToObject<Model.Owner>(jo));
                                                await Task.Delay(50);
                                            });
                                        }
                                    }
                                    catch (Exception e)
                                    {
                                        BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "批量区域授权上报车辆", "批量区域授权上报车辆发生异常:" + e.ToString());
                                    }
                                    return Task.CompletedTask;
                                });
                            }

                            var r = PushSync(Model.API.PushAction.Act, (spaceJson, ownerNoes), new List<Model.SentryHost>(), "BathCarOwner", dataType: DataTypeEnum.Owner, Desc: $"车位授权{string.Join(',', (owneres?.Select(x => x.Owner_Space) ?? new List<string> { }))}");
                            if (r == null || r.Where(x => x.success == false).Count() > 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"车位授权处理成功，但未能同步数据至岗亭，车位号{string.Join(',', (owneres?.Select(x => x.Owner_Space) ?? new List<string> { }))}");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"车位授权处理异常，车位号{string.Join(',', (owneres?.Select(x => x.Owner_Space) ?? new List<string> { }))}：" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "授权成功");
                }
                else
                {
                    return ResOk(false, "授权失败");
                }

            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "批量区域授权", "批量授权发生异常:" + ex.ToString());
                return ResOk(false, "异常错误");
            }
        }


        /// <summary>
        /// 白名单启用
        /// </summary>
        /// <param name="Car_CarNoList">车牌号列表</param>
        /// <returns></returns>
        public IActionResult EnableWhiteList(string NoList)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Enable.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                List<string> nos = TyziTools.Json.ToObject<List<string>>(NoList);
                nos.Remove(string.Empty);
                nos = nos.Distinct().ToList();

                var parameters = new { Owner_ParkNo = parking.Parking_No, Owner_No = nos };
                List<Model.Owner> models = BLL.Owner.GetAllEntity("*", $"Owner_ParkNo=@Owner_ParkNo AND Owner_No in @Owner_No", parameters);
                if (models == null || models.Count == 0) { return ResOk(false, "请选择"); }

                models.ForEach(item =>
                {
                    item.Owner_EnableOffline = 1;
                });

                var res = BLL.Owner.UpdateByList(models);
                if (res > 0)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            models.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x); });
                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.WhiteEnable, $"车位号：{string.Join(",", models?.Select(x => x.Owner_Space))}", SecondIndex.Owner);
                            var r = PushSync(Model.API.PushAction.Edit, models, dataType: DataTypeEnum.Owner, Desc: $"启用白名单");
                            if (r == null || r.Where(x => x.success == false).Count() > 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"车位白名单启用处理成功，但未能同步数据至岗亭，车位号{string.Join(',', models?.Select(x => x.Owner_Space))}");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"车位白名单处理异常，车位号{string.Join(',', models?.Select(x => x.Owner_Space))}：" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "设置白名单成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, SecondOption.WhiteEnable, $"设置白名单失败：{NoList}", SecondIndex.Owner);
                    return ResOk(false, "设置白名单失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "设置白名单", $"设置白名单异常：{ex.Message}");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 白名单禁用
        /// </summary>
        /// <param name="Car_CarNoList">车牌号列表</param>
        /// <returns></returns>
        public IActionResult DisableWhiteList(string NoList)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Disable.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                List<string> nos = TyziTools.Json.ToObject<List<string>>(NoList);
                nos.Remove(string.Empty);
                nos = nos.Distinct().ToList();

                var parameters = new { Owner_ParkNo = parking.Parking_No, Owner_No = nos };
                List<Model.Owner> models = BLL.Owner.GetAllEntity("*", $"Owner_ParkNo=@Owner_ParkNo AND Owner_No in @Owner_No", parameters);
                if (models == null || models.Count == 0) { return ResOk(false, "请选择"); }

                models.ForEach(item =>
                {
                    item.Owner_EnableOffline = 0;
                });

                var res = BLL.Owner.UpdateByList(models);
                if (res > 0)
                {
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            models.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x); });
                            BLL.UserLogs.AddLog(lgAdmins, SecondOption.WhiteDisable, $"车位号：{string.Join(",", models?.Select(x => x.Owner_Space))}", SecondIndex.Owner);
                            var r = PushSync(Model.API.PushAction.Edit, models, dataType: DataTypeEnum.Owner, Desc: $"禁用白名单");
                            if (r == null || r.Where(x => x.success == false).Count() > 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"车位白名单禁用成功，但未能同步数据至岗亭，车位号{string.Join(',', models?.Select(x => x.Owner_Space))}");
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"车位白名单禁用处理异常，车位号{string.Join(',', models?.Select(x => x.Owner_Space))}：" + ex.ToString());
                        }

                        return Task.CompletedTask;
                    });

                    return ResOk(true, "设置白名单成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, SecondOption.WhiteDisable, $"设置白名单失败：{NoList}", SecondIndex.Owner);
                    return ResOk(false, "设置白名单失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "设置白名单", $"设置白名单异常：{ex.Message}");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 判断当前车牌是否需要缴费
        /// </summary>
        /// <param name="Car_CarNo"></param>
        /// <returns></returns>
        public IActionResult CheckNeedPayee(string Owner_No)
        {
            try
            {

                if (!Powermanage.PowerCheck("Owner", PowerEnum.Retweet.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var parameters = new { Owner_No = Owner_No };
                var owner = BLL.Owner.GetOwnerExt("*", $"Owner_No=@Owner_No ", parameters);
                if (owner == null) { return ResOk(false, "获取车辆信息失败"); }

                List<ChargeModels.PayResult> payResultList = new List<ChargeModels.PayResult>();
                List<Model.ParkOrder> parkorderList = new List<Model.ParkOrder>();

                Model.PolicyPark policy = BLL.PolicyPark.GetEntity(AppBasicCache.GetParking.Parking_No);
                if (policy?.PolicyPark_PayedCharge == 1)
                {
                    List<Model.Car> carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{Owner_No}'");

                    carList?.ForEach(car =>
                    {
                        var parkOrder = BLL.ParkOrder.GetOrderAndThatDetail(parking.Parking_No, car.Car_CarNo);
                        ChargeModels.PayResult payResult = null;
                        if (parkOrder.Item1 != null)
                        {
                            payResult = Calc.GetChargeByCar(parkOrder.Item1, DateTimeHelper.GetNowTime(), null, null, false, "", "", owner);
                            if (payResult.payed == 1)
                            {
                                CalcCache.Set("OrderPrice:" + parkOrder.Item1.ParkOrder_No, payResult);
                                parkorderList.Add(parkOrder.Item1);
                                payResultList.Add(payResult);
                            }
                        }
                    });

                    //查询是否已在场内
                    if (carList != null && carList.Count > 0)
                    {
                        foreach (var car in carList)
                        {
                            Model.CarType carType = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                            Model.ParkOrder parkOrder = BLL.ParkOrder.GetParkOrder("*", parking.Parking_No, car.Car_CarNo, 0, 200);
                            if (parkOrder != null)
                            {
                                var isPay = Calc.GetChargeByCar(parkOrder, DateTimeHelper.GetNowTime(), null, null, false);
                                if (isPay.payed == 1 && isPay.payedamount > 0)
                                    return ResOk(false, $"{parkOrder.ParkOrder_CarNo}已在场内，请清缴停车费用", parkOrder.ParkOrder_No);
                                else if (isPay.payed == 2)
                                    return ResOk(false, "计费失败，请稍候重试");
                            }
                        }
                    }
                }
                return ResOk(true, $"无需缴费", 1);
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "获取价格异常" + ex.Message, ex.ToString());
                return ResOk(false, "获取价格异常" + ex.Message);
            }
        }

        /// <summary>
        /// 退费
        /// </summary>
        public IActionResult RefundFee(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Refund.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                JObject param = JObject.Parse(jsonModel);
                string Owner_No = param.ContainsKey("Owner_No") ? param["Owner_No"].ToString() : null;
                string Car_PayMoney = param.ContainsKey("Car_PayMoney") ? param["Car_PayMoney"].ToString() : null;
                if (string.IsNullOrEmpty(Owner_No)) { return ResOk(false, "车辆信息异常，请刷新重试"); }
                if (string.IsNullOrEmpty(Car_PayMoney)) { return ResOk(false, "退费金额不能为空"); }
                var money = Utils.ObjectToDecimal(Car_PayMoney, 0);
                if (money <= 0) { return ResOk(false, "退费金额不能小于0"); }

                Model.Owner owner = BLL.Owner.GetEntity(Owner_No, false);
                if (owner == null) { return ResOk(false, "车辆信息异常，请刷新重试"); }
                if (owner.Owner_Balance == null || owner.Owner_Balance < money) { return ResOk(false, "退费金额不能大于账户余额"); }


                List<Model.Ledger> ledgerList = null;
                var remainingMoney = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - money;
                //if (remainingMoney > 0)
                //{
                Model.Ledger ledger = new Model.Ledger()
                {
                    Ledger_CarNo = AppBasicCache.GetCar.Values.FirstOrDefault(x => x.Car_OwnerNo == owner.Owner_No)?.Car_CarNo,
                    Ledger_Space = owner.Owner_Space,
                    Ledger_Type = 3,
                    Ledger_Code = 2,
                    Ledger_Money = money,
                    Ledger_BeforeMoeny = owner.Owner_Balance,
                    Ledger_AfterMoeny = remainingMoney,
                    Ledger_Time = DateTime.Now,
                    Ledger_Remark = ""
                };
                ledgerList = new List<Model.Ledger>() { ledger };
                //}
                owner.Owner_Balance = remainingMoney;

                List<Model.Car> carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{Owner_No}'");

                Model.PayOrder payorder = new Model.PayOrder();
                payorder.PayOrder_CarCardTypeNo = owner.Owner_CardTypeNo;
                payorder.PayOrder_CarTypeNo = carList?.FirstOrDefault()?.Car_VehicleTypeNo;
                payorder.PayOrder_CouponRecordNo = null;
                payorder.PayOrder_OrderTypeNo = Convert.ToString((int)Common.EnumOrderType.Wallet);
                payorder.PayOrder_ParkKey = parking.Parking_Key;
                payorder.PayOrder_ParkNo = parking.Parking_No;
                payorder.PayOrder_Category = carList?.FirstOrDefault()?.Car_Category;
                payorder.PayOrder_CarNo = carList?.FirstOrDefault()?.Car_CarNo;
                payorder.PayOrder_PayedTime = DateTimeHelper.GetNowTime();
                payorder.PayOrder_PayType = 0;
                payorder.PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                payorder.PayOrder_Status = 1;
                payorder.PayOrder_Time = DateTimeHelper.GetNowTime();
                payorder.PayOrder_OwnerSpace = owner.Owner_Space;
                //payorder.PayOrder_MonthEffTime = owner.Owner_EndTime;
                payorder.PayOrder_TimeCountDesc = "储值车退费";
                payorder.PayOrder_Desc = "储值车退费";
                payorder.PayOrder_Money = -money;
                payorder.PayOrder_PayedMoney = -money;
                payorder = BLL.PayOrder.CreatePayOrder(true, payorder, parking.Parking_Key, "CZ", lgAdmins);
                payorder.PayOrder_DiscountMoney = payorder.PayOrder_Money - payorder.PayOrder_PayedMoney;
                List<Model.PayPart> payPartList = BLL.CommonBLL.CreatePayPartList(null, payorder, 1);
                Model.PayColl payColl = new PayColl() { payOrderList = new List<Model.PayOrder>() { payorder }, payPartList = payPartList };

                //carList?.ForEach(item => {
                //    item.Car_Balance = owner.Owner_Balance;
                //});

                var res = BLL.Car.AddCarOwner(null, owner, null, payColl, ledgerList: ledgerList);
                if (res < 0)
                {
                    return ResOk(false, "退费失败");
                }
                BLL.UserLogs.AddLog(lgAdmins, SecondOption.Refund, $"车位号：{owner.Owner_Space},退费金额：{Car_PayMoney}元", SecondIndex.Owner);

                (Model.Car, Model.Owner, Model.PayColl) data = (null, owner, payColl);
                var r = PushSync(Model.API.PushAction.Add, data, new List<Model.SentryHost>(), "addcar", dataType: DataTypeEnum.Owner, Desc: $"退费{owner.Owner_Space}");
                if (r == null) { return ResOk(false, "退费成功，但未能同步数据至岗亭，请检查服务器与岗亭网络通讯情况"); }
                List<Model.API.ResPushHost> failRes = r.Where(x => x.success == false).ToList();
                if (failRes.Count > 0)
                {
                    return ResOk(false, $"退费成功，但未能同步数据至岗亭（{string.Join(",", failRes.GroupBy(x => x.hostname).Select(m => m.Key))}），请检查服务器与岗亭网络通讯情况");
                }
                return ResOk(true, "退费成功");

            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "更新车辆信息", "更新车辆信息发生异常:" + ex.ToString());
                return ResOk(true, "异常错误");
            }
        }

        #region V.New

        /// <summary>
        /// 系统生成车位号
        /// </summary>
        /// <returns></returns>
        public IActionResult CreateOwnerSpace()
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                string NewNo = string.Empty;
                var owner = BLL.Owner.GetLast();
                if (owner == null)
                {
                    NewNo = "1".PadLeft(5, '0');
                    NewNo = $"C{NewNo}";
                    return ResOk(true, "", NewNo);
                }
                else
                {
                    var has = true;
                    string strNo = Regex.Replace(owner.Owner_Space, "[a-z]", "", RegexOptions.IgnoreCase);
                    int LastNo = Utils.StrToInt(strNo, 0);

                    while (has)
                    {
                        LastNo = LastNo + 1;
                        NewNo = LastNo.ToString().PadLeft(5, '0');
                        var item = BLL.Owner.GetEntity("Owner_No", $"Owner_Space='C{NewNo}'");
                        if (item == null)
                            has = false;
                    }

                    NewNo = $"C{NewNo}";
                    return ResOk(true, "", NewNo);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "生成车位号异常" + ex.Message, ex.ToString());
                return ResOk(false, "生成车位号失败，请重新打开窗口.");
            }
        }

        #endregion

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="conditionParam">条件</param>
        /// <returns></returns>
        public FileContentResult ExportExcel(string conditionParam, string field, string order, string chkfield)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Export.ToString(), adminSession: lgAdmins))
                return null;

            object obj = SearchOwnerList(1, 1048575, conditionParam, field, order, true).data;
            List<Model.OwnerExt> list = (List<Model.OwnerExt>)obj;
            if (list == null || list.Count == 0)
            {
                HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true", new Microsoft.AspNetCore.Http.CookieOptions { Expires = DateTimeOffset.Now.AddSeconds(10), IsEssential = true });
                return File(new byte[0], "application/vnd.ms-excel", DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd") + "车辆信息.xlsx");
            }

            var parameters = new { Car_ParkingNo = parking.Parking_No, Car_OwnerNo = list.Select(x => x.Owner_No).ToList() };
            List<Model.Car> cars = BLL.Car.GetAllEntity("*", $"Car_ParkingNo=@Car_ParkingNo AND Car_OwnerNo IN @Car_OwnerNo", parameters);

            List<Model.CarType> carTypes = BLL.CarType.GetAllEntity("CarType_No,CarType_Name", $"CarType_ParkNo='{parking.Parking_No}'");
            List<Model.Admins> adminList = null;

            DataTable newTable = new DataTable();
            DataTable dt = Utils.ListToDataTableString(list);
            Dictionary<string, string> dic = new Dictionary<string, string>();

            var filedArray = TyziTools.Json.ToObject<List<JObject>>(chkfield);
            var filedList = new List<string>();
            foreach (var item in filedArray)
            {
                if (item != null)
                {
                    var filedName = Convert.ToString(item["filed"]);
                    if (!dic.ContainsKey(filedName))
                    {
                        if (filedName == "Owner_OnlyDay") filedName = "Owner_ID";
                        dic.Add(filedName, Convert.ToString(item["text"]));
                        newTable.Columns.Add(new DataColumn(filedName, typeof(string)));
                    }
                }
            }
            //newTable.Columns.Add(new DataColumn("Owner_ParkNo", typeof(string)));
            //dic.Add("Owner_ParkNo", "卡号");

            if (dic.ContainsKey("Owner_AddName")) adminList = BLL.Admins.GetAllEntity("Admins_ID,Admins_Name", "1=1");
            foreach (DataRow item in dt.Rows)
            {

                if (dic.ContainsKey("Owner_AddName"))
                {
                    int? id = Utils.ObjectToInt(item["Owner_AddID"].ToString(), 0);
                    item["Owner_AddName"] = (adminList?.Find(x => x.Admins_ID == id)?.Admins_Name) ?? "";
                }
                if (dic.ContainsKey("Owner_EnableOffline")) item["Owner_EnableOffline"] = item["Owner_EnableOffline"].ToString() == "1" ? "启用" : "禁用";
                if (dic.ContainsKey("Owner_IsMoreCar")) item["Owner_IsMoreCar"] = item["Owner_IsMoreCar"].ToString() == "1" ? "是" : "否";

                if (dic.ContainsKey("Owner_ID"))
                {
                    if (item["Owner_CardType"].ToString() != "2")
                    {
                        var startTime = Utils.StrToDateTime(Utils.StrToDateTime(item["Owner_StartTime"].ToString()).ToString("yyyy-MM-dd 00:00:00"));
                        var endTime = Utils.StrToDateTime(Utils.StrToDateTime(item["Owner_EndTime"].ToString()).ToString("yyyy-MM-dd 23:59:59"));

                        item["Owner_StartTime"] = startTime.ToString("yyyy-MM-dd 00:00:00");
                        item["Owner_EndTime"] = endTime.ToString("yyyy-MM-dd 23:59:59");

                        DateTime nowTime = Utils.StrToDateTime(DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd 00:00:00"));
                        DateTime nowTime1 = startTime > nowTime ? startTime : nowTime;
                        var diffTime = endTime - nowTime1;
                        var days = diffTime.Days;

                        if (days >= 0 && (diffTime.Hours > 0 || diffTime.Minutes > 0 || diffTime.Seconds > 0))
                        {
                            if (diffTime.Hours > 0 || diffTime.Minutes > 0) days++;
                        }
                        else
                        {
                            if (diffTime.Hours != 0 || diffTime.Minutes != 0 || diffTime.Seconds != 0) days--;
                        }

                        item["Owner_ID"] = days.ToString() + " 天";
                        item["Owner_Balance"] = "-";
                    }
                    else
                    {
                        item["Owner_ID"] = "-";
                        item["Owner_StartTime"] = "-";
                        item["Owner_EndTime"] = "-";
                    }
                }
                if (dic.ContainsKey("Owner_CardType")) item["Owner_CardType"] = item["Owner_CardName"].ToString();
                if (dic.ContainsKey("Owner_IsInPark")) item["Owner_IsInPark"] = item["Owner_IsInPark"].ToString() == "1" ? "是" : "否";

                DataRow dr = newTable.NewRow();
                foreach (var d in dic)
                {
                    dr[d.Key] = item[d.Key];
                }
                newTable.Rows.Add(dr);
            }

            foreach (DataColumn dc in newTable.Clone().Columns)
            {
                if (!dic.ContainsKey(dc.ColumnName)) //删除不显示列
                {
                    newTable.Columns.Remove(dc.ColumnName);
                    newTable.AcceptChanges();
                }
                else //修改列名
                {
                    newTable.Columns[dc.ColumnName].ColumnName = dic[dc.ColumnName];
                }
            }

            byte[] bt = null;

            if (newTable.Rows.Count > 5000)
                bt = NPOIExcelHelper.ExportToExcel2(newTable);
            else
                bt = NPOIExcelHelper.ToExport(newTable, null);//(index, sheet) => { sheet.SetColumnWidth(index, columnWidths[index] * 256); }

            HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true",
               new Microsoft.AspNetCore.Http.CookieOptions
               {
                   Expires = DateTimeOffset.Now.AddSeconds(10),
                   IsEssential = true
               });

            BLL.UserLogs.AddLog(lgAdmins, SecondOption.Export, $"导出条数：{newTable.Rows.Count}", SecondIndex.Owner);

            return File(bt, "application/vnd.ms-excel", DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd") + "车辆信息.xlsx");
        }



        #region 导入车辆
        public void Upload(bool replacecar, bool replaceorder, bool createpayorder)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.Import.ToString(), false, true, lgAdmins))
                { MiniResponse.ResponseResult("无权限", false); return; }


                var importRet = DataCache.SyncAction.Get("carimport1");
                if (!string.IsNullOrEmpty(importRet))
                {
                    var retArray = importRet.Split(",");
                    if (retArray.Count() > 0)
                    {
                        var sumCount = Utils.ObjectToInt(retArray[0], 0);
                        var runCount = Utils.ObjectToInt(retArray[1], 0);
                        var successCount = Utils.ObjectToInt(retArray[2], 0);
                        var failCount = Utils.ObjectToInt(retArray[3], 0);

                        if (sumCount > 0)
                        {
                            if (runCount < sumCount)
                            {
                                MiniResponse.ResponseResult($"导入失败，系统任务尚未完成（{runCount}/{sumCount}）。请稍候重试。", false, null, "-1"); return;
                            }
                        }
                    }
                }

                #region 导入
                var errorMsg = string.Empty;

                //解析导入的Excel文件
                var postForm = Request.Form;

                List<Model.CarType> carTypeList = BLL.CarType.GetAllEntity("CarType_No,CarType_Name", $"CarType_ParkNo='{parking.Parking_No}'");
                List<Model.CarCardType> carCardTypeList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_Type,CarCardType_Category,CarCardType_IsMoreCar,CarCardType_WhiteEnable", $"CarCardType_ParkNo='{parking.Parking_No}'");
                if (carTypeList == null || carTypeList.Count == 0) { MiniResponse.ResponseResult("系统未设置车牌颜色，无法导入车辆", false, null, "-1"); return; }
                if (carCardTypeList == null || carCardTypeList.Count == 0) { MiniResponse.ResponseResult("系统未设置车牌类型，无法导入车辆", false, null, "-1"); return; }
                List<Model.ParkArea> areaList = BLL.ParkArea.GetAllEntity("*", $"ParkArea_ParkNo='{parking.Parking_No}'");
                if (areaList == null || areaList.Count == 0) { MiniResponse.ResponseResult("系统未设置车场区域，无法导入车辆", false, null, "-1"); return; }

                IFormFileCollection formFiles = Request.Form.Files;
                if (!Utils.IsOnlyExcelFiles(formFiles))
                {
                    MiniResponse.ResponseResult($"导入失败，只允许上传 Excel 文件 。", false, null, "-1"); return;
                }
                long size = formFiles.Sum(f => f.Length);
                DataTable dtExcel = GetExcel(formFiles, areaList, carTypeList, carCardTypeList, ref rb);

                //文件不合符导入条件
                if (rb.Code == "1") { MiniResponse.ResponseResult(rb.Message, false, null, "1"); return; }

                List<string> spaceList = new List<string>();
                List<string> carNoList = new List<string>();
                foreach (DataRow dr in dtExcel.Rows) //读取手机号码集合
                {
                    var carno = dr[0].ToString().Trim();//车牌号
                    var cardno = dr[8].ToString().Trim();//卡号
                    spaceList.Add(dr[6].ToString().Trim());//车位号
                    carNoList.Add(carno);

                    if (!string.IsNullOrEmpty(cardno) && AppBasicCache.GetCar.Values.Where(x => x.Car_CardNo == cardno && x.Car_CarNo != carno).Count() > 0)
                        errorMsg += $"【{carno} {cardno}】卡号不能重复登记！<br/>";
                }
                spaceList = spaceList.Distinct<string>().ToList();
                carNoList = carNoList.Distinct<string>().ToList();
                //查询车主数据
                List<Model.Owner> oList = new List<Model.Owner>();
                if (spaceList.Count > 0) oList = BLL.Owner.GetAllEntity("Owner_ParkNo,Owner_No,Owner_Name,Owner_Space,Owner_CardTypeNo", $"Owner_ParkNo='{parking.Parking_No}' AND Owner_Space in ('{string.Join("','", spaceList)}')");

                //查询车主车位数量
                List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new StopSpace(), "StopSpace_OwnerNo,StopSpace_Type,StopSpace_Number,StopSpace_AreaNo", $"StopSpace_ParkNo='{parking.Parking_No}'");

                var parameters = new { Car_ParkingNo = parking.Parking_No, Car_CarNo = carNoList };
                List<Model.Car> existCarList = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerSpace", $"Car_ParkingNo=@Car_ParkingNo AND Car_CarNo in @Car_CarNo", parameters);

                //预生成车位编号集合
                List<string> carSpaceNoList = BLL.Owner.GeneralCarSpaceNoList(parking.Parking_No, dtExcel.Rows.Count);

                List<Model.Car> carList = new List<Model.Car>();
                List<Model.Owner> ownerList = new List<Model.Owner>();
                List<Model.StopSpace> stopList = new List<Model.StopSpace>();
                List<Model.StopSpace> old_stopList = new List<Model.StopSpace>();
                List<Model.PayOrder> payorderList = new List<Model.PayOrder>();
                List<Model.PayPart> paypartList = new List<Model.PayPart>();
                List<Model.Ledger> ledgerList = new List<Ledger>();

                List<string> lstNo = new List<string>();
                lstNo = Utils.GetRandomLst(dtExcel.Rows.Count * 4);

                DateTime curTime = DateTimeHelper.GetNowTime();
                rb = new ResultBase();
                var CurrenRowIndex = 0;
                string errorownermsg = string.Empty;    //错误记录
                List<string> allNoList = Utils.GetRandomLst(100 + dtExcel.Rows.Count);

                #region 检测车主车辆信息车牌类型是否一致
                var lstNo2 = Utils.GetRandomLst(dtExcel.Rows.Count * 4);
                List<string> carSpaceNoList2 = BLL.Owner.GeneralCarSpaceNoList(parking.Parking_No, dtExcel.Rows.Count);

                List<Model.Owner> ownerList2 = new List<Model.Owner>();
                List<Model.Car> carList2 = new List<Model.Car>();
                foreach (DataRow dr in dtExcel.Rows)    //遍历Excel数据
                {
                    var Car_CardName = dr[1].ToString().Trim();
                    var Car_CarNo = dr[0].ToString().Trim().Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "").ToUpper();
                    var Car_OwnerSpace = dr[6].ToString().Trim();
                    if (string.IsNullOrEmpty(Car_OwnerSpace))
                    {
                        var oldCar = existCarList?.Find(x => x.Car_CarNo == Car_OwnerSpace);
                        if (oldCar != null && !string.IsNullOrEmpty(oldCar.Car_OwnerSpace)) { Car_OwnerSpace = oldCar.Car_OwnerSpace; }
                        else
                        {
                            Car_OwnerSpace = carSpaceNoList2.First();
                            carSpaceNoList2.RemoveAt(0);
                        }
                    }

                    Model.CarCardType card = carCardTypeList.Find(x => x.CarCardType_Name == Car_CardName);
                    Model.Owner person = oList?.Find(x => x.Owner_Space == Car_OwnerSpace);
                    bool existOwner = false;
                    if (person != null) existOwner = true;
                    person = person ?? ownerList2?.Find(x => x.Owner_Space == Car_OwnerSpace);
                    person = person ?? new Model.Owner()
                    {
                        Owner_No = lstNo2.First(),
                        Owner_Space = Car_OwnerSpace,
                        Owner_EnableOffline = card?.CarCardType_WhiteEnable ?? 0
                    };
                    lstNo2.RemoveAt(0);

                    if (existOwner && card.CarCardType_No != person.Owner_CardTypeNo)
                    {
                        if (!replacecar || replacecar && AppBasicCache.GetCar.Values.Where(x => x.Car_OwnerNo == person.Owner_No && !carNoList.Contains(x.Car_CarNo)).Count() > 0)
                        {
                            var ownerCarCardType = carCardTypeList.Find(x => x.CarCardType_No == person.Owner_CardTypeNo);
                            errorMsg += $"【{Car_CarNo}】车牌类型【{Car_CardName}】与车主信息【{ownerCarCardType?.CarCardType_Name}】不一致，请修改后再试！<br/>";
                        }
                    }

                    person.Owner_CardTypeNo = card.CarCardType_No;
                    ownerList2.Add(person);
                    carList2.Add(new Model.Car() { Car_CarNo = Car_CarNo, Car_TypeNo = card.CarCardType_No, Car_OwnerSpace = Car_OwnerSpace, Car_OwnerNo = person.Owner_No });
                }

                if (!string.IsNullOrEmpty(errorMsg))
                {
                    MiniResponse.ResponseResult(errorMsg, false, null, "-1"); return;
                }
                else
                {
                    List<string> carComplete2 = new List<string>();//记录已处理的车牌，循环时跳过
                    foreach (var car in carList2)
                    {
                        if (carComplete2.Contains(car.Car_CarNo)) continue;

                        var cars = carList2.FindAll(x => x.Car_OwnerNo == car.Car_OwnerNo).ToList();
                        if (cars.Count > 0)
                        {
                            var last = cars.Last();

                            if (cars.Count > 1)
                            {
                                var diff = cars.Find(x => x.Car_TypeNo != car.Car_TypeNo);
                                if (diff != null)
                                {
                                    errorMsg += $"同一个车位【{car.Car_OwnerSpace}】，车牌【{car.Car_CarNo}】【{diff.Car_CarNo}】车牌类型不一致，请修改后再试！<br/>";
                                }
                            }
                            carComplete2.AddRange(cars.Select(x => x.Car_CarNo));
                        }
                    }

                    if (!string.IsNullOrEmpty(errorMsg))
                    {
                        MiniResponse.ResponseResult(errorMsg, false, null, "-1"); return;
                    }
                }
                #endregion

                foreach (DataRow dr in dtExcel.Rows)    //遍历Excel数据
                {
                    CurrenRowIndex++;
                    try
                    {
                        #region 单行数据值转换
                        Model.CarImport carEdit = new Model.CarImport();
                        if (dr == null || dr[0] == null) { LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{CurrenRowIndex}行, 空"); continue; }
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{CurrenRowIndex}行," + dr[0].ToString());

                        carEdit.Car_CarNo = dr[0]?.ToString().Trim().Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "").ToUpper();
                        carEdit.Car_CardName = dr[1]?.ToString().Trim();
                        carEdit.Car_CarTypeName = dr[2]?.ToString().Trim();
                        carEdit.Car_BeginTime = Utils.StrToDateTime(dr[3]?.ToString().Trim());
                        carEdit.Car_EndTime = Utils.StrToDateTime(dr[4]?.ToString().Trim());
                        carEdit.Car_OwnerName = dr[5]?.ToString().Trim();
                        carEdit.Car_OwnerSpace = dr[6]?.ToString().Trim();
                        if (string.IsNullOrEmpty(carEdit.Car_OwnerSpace))
                        {

                            var oldCar1 = existCarList?.Find(x => x.Car_CarNo == carEdit.Car_CarNo);
                            if (oldCar1 != null && !string.IsNullOrEmpty(oldCar1.Car_OwnerSpace)) { carEdit.Car_OwnerSpace = oldCar1.Car_OwnerSpace; }
                            else
                            {
                                carEdit.Car_OwnerSpace = carSpaceNoList.First();
                                carSpaceNoList.RemoveAt(0);
                            }
                        }
                        carEdit.Car_ParkSpace = dr[7]?.ToString().Trim();
                        carEdit.Car_CardNo = dr[8]?.ToString().Trim();
                        //carEdit.Car_SpaceNum = Utils.StrToInt(dr[7].ToString().Trim(), 1);
                        carEdit.Car_OwnerAddress = dr[9]?.ToString().Trim();
                        carEdit.Car_OwnerPhone = dr[10]?.ToString().Trim();
                        carEdit.Car_OwnerSex = dr[11]?.ToString().Trim();
                        carEdit.Car_Balance = Utils.StrToDecimal(dr[12].ToString().Trim(), 0);
                        string remark = dr[13]?.ToString().Trim();

                        #endregion
                        var oldCar = existCarList?.Find(x => x.Car_CarNo == carEdit.Car_CarNo);
                        if (!replacecar && oldCar != null) { continue; }

                        Model.CarCardType card = carCardTypeList.Find(x => x.CarCardType_Name == carEdit.Car_CardName);
                        Model.CarType carType = carTypeList.Find(x => x.CarType_Name == carEdit.Car_CarTypeName);

                        #region 车主信息
                        bool existOwner = false;
                        decimal? Owner_Balance = 0;
                        //查询车位号是否已存在
                        Model.Owner person = oList?.Find(x => x.Owner_Space == carEdit.Car_OwnerSpace);
                        if (person != null)
                        {
                            Owner_Balance = person.Owner_Balance ?? 0;
                            existOwner = true;
                        }

                        person = person ?? ownerList?.Find(x => x.Owner_Space == carEdit.Car_OwnerSpace);

                        person = person ?? new Model.Owner()
                        {
                            Owner_No = existOwner ? person.Owner_No : lstNo.First(),
                            Owner_StartTime = Utils.StrToDateTime(carEdit.Car_BeginTime.Value.ToString("yyyy-MM-dd 00:00:00")),
                            Owner_EndTime = Utils.StrToDateTime(carEdit.Car_EndTime.Value.ToString("yyyy-MM-dd 23:59:59")),
                            Owner_ParkNo = parking.Parking_No,
                            Owner_AddTime = curTime,
                            Owner_Space = existOwner ? person.Owner_Space : carEdit.Car_OwnerSpace,
                            Owner_ParkSpace = carEdit.Car_ParkSpace,
                            Owner_AddID = lgAdmins?.Admins_ID,
                            Owner_EnableOffline = card?.CarCardType_WhiteEnable ?? 0,
                        };
                        person.Owner_Name = carEdit.Car_OwnerName;
                        person.Owner_Phone = carEdit.Car_OwnerPhone;
                        person.Owner_Address = carEdit.Car_OwnerAddress;
                        person.Owner_Sex = Model.OwnerConvert.Sex(carEdit.Car_OwnerSex);
                        person.Owner_EditTime = curTime;
                        person.Owner_EditID = lgAdmins?.Admins_ID;
                        person.Owner_Remark = string.IsNullOrEmpty(remark) ? "后台导入" : remark;
                        person.Owner_Balance = carEdit.Car_Balance ?? 0;
                        lstNo.RemoveAt(0);
                        #endregion

                        #region 车辆信息

                        Model.Car car = oldCar ?? new Model.Car();
                        car.Car_No = oldCar?.Car_No ?? lstNo.First();
                        car.Car_CarNo = carEdit.Car_CarNo;
                        car.Car_OwnerNo = person.Owner_No;
                        car.Car_OwnerName = person.Owner_Name;
                        car.Car_OwnerSpace = existOwner ? person.Owner_Space : carEdit.Car_OwnerSpace;
                        car.Car_ParkingNo = person.Owner_ParkNo;
                        car.Car_BeginTime = Utils.StrToDateTime(carEdit.Car_BeginTime.Value.ToString("yyyy-MM-dd 00:00:00"));
                        car.Car_EndTime = Utils.StrToDateTime(carEdit.Car_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                        car.Car_AddTime = curTime;
                        car.Car_OnLine = 1;
                        car.Car_Status = 1;
                        car.Car_VehicleTypeNo = carType.CarType_No;
                        car.Car_TypeNo = card.CarCardType_No;
                        car.Car_AddID = lgAdmins?.Admins_ID;
                        car.Car_Remark = string.IsNullOrEmpty(carEdit.Car_Remark) ? "后台导入" : carEdit.Car_Remark;
                        car.Car_EnableOffline = person.Owner_EnableOffline;
                        car.Car_IsMoreCar = card.CarCardType_IsMoreCar;
                        car.Car_Category = card.CarCardType_Category;
                        car.Car_CardNo = carEdit.Car_CardNo;

                        lstNo.RemoveAt(0);

                        if (existOwner)
                        {
                            if (card.CarCardType_No != person.Owner_CardTypeNo)
                            {
                                var ownerCarCardType = carCardTypeList.Find(x => x.CarCardType_No == person.Owner_CardTypeNo);
                                MiniResponse.ResponseResult($"车牌【{carEdit.Car_CarNo}】车牌类型【{carEdit.Car_CardName}】与车主信息【{ownerCarCardType?.CarCardType_Name}】不一致，请修改后再试", false, null, "-1"); return;
                            }
                        }

                        if (card.CarCardType_Type == 2) person.Owner_SpaceNum = 0;
                        person.Owner_CardType = card.CarCardType_Type;
                        person.Owner_CardTypeNo = card.CarCardType_No;
                        //person.Owner_EnableOffline = 1;

                        #endregion

                        #region 车主车位数量

                        var df = spaces.FindAll(x => x.StopSpace_OwnerNo == person.Owner_No);
                        var has = stopList.Find(x => x.StopSpace_OwnerNo == person.Owner_No);
                        if ((df == null || df.Count == 0) && has == null)
                        {
                            var sumSpaceNum = 0;
                            if (!string.IsNullOrEmpty(dr[14]?.ToString().Trim()) && card.CarCardType_Type != 2)
                            {
                                string area = dr[14].ToString().Trim(' ').Trim(' ');
                                area = area.Replace("，", ",").Replace("：", ":");
                                List<string> currentCarArea = area.Split(",").ToList();
                                List<string> currAreaList = new List<string>();
                                foreach (var item in currentCarArea)
                                {
                                    var itemArray = item.Split(':');
                                    var nameList = itemArray[0]?.Split("、").ToList() ?? [];
                                    var noList = new List<string>();
                                    var isAll = false;
                                    foreach (var name in nameList)
                                    {
                                        noList.Add(areaList.Find(x => x.ParkArea_Name == name)?.ParkArea_No ?? "");
                                        if (name == "全部区域") { isAll = true; break; }
                                    }
                                    noList.Distinct();


                                    var stopitem = new Model.StopSpace()
                                    {
                                        StopSpace_No = allNoList.Last(),
                                        StopSpace_ParkNo = parking.Parking_No,
                                        StopSpace_OwnerNo = person.Owner_No,
                                        StopSpace_Type = isAll ? 0 : 1,
                                        StopSpace_Number = Utils.ObjectToInt(itemArray[1], 1),
                                        StopSpace_AddTime = DateTimeHelper.GetNowTime(),
                                        StopSpace_AreaNo = isAll ? "[\"0\"]" : TyziTools.Json.ToString(noList),
                                        StopSpace_AreaName = isAll ? "全部区域" : itemArray[0]
                                    };
                                    sumSpaceNum += stopitem.StopSpace_Number.Value;
                                    stopList.Add(stopitem);
                                    allNoList.Remove(allNoList.Last());

                                    if (allNoList.Count == 0)
                                    {
                                        allNoList = Utils.GetRandomLst(100 + dtExcel.Rows.Count);
                                    }
                                }
                            }
                            else
                            {
                                sumSpaceNum = card.CarCardType_Type == 2 ? 0 : 1;
                                var stopitem = new Model.StopSpace()
                                {
                                    StopSpace_No = allNoList.Last(),
                                    StopSpace_ParkNo = parking.Parking_No,
                                    StopSpace_OwnerNo = person.Owner_No,
                                    StopSpace_Type = 0,
                                    StopSpace_Number = sumSpaceNum,
                                    StopSpace_AddTime = DateTimeHelper.GetNowTime(),
                                    StopSpace_AreaNo = "[\"0\"]",
                                    StopSpace_AreaName = "全部区域"
                                };
                                stopList.Add(stopitem);
                                allNoList.Remove(allNoList.Last());
                            }
                            person.Owner_SpaceNum = sumSpaceNum;
                        }
                        else
                        {
                            if (df != null) old_stopList.AddRange(df);
                        }
                        #endregion
                        carList.Add(car);
                        ownerList.Add(person);

                        if (createpayorder && person.Owner_Balance > 0 && person.Owner_CardType == 2)
                        {
                            if (payorderList.Find(x => x.PayOrder_OwnerNo == person.Owner_No) == null)
                            {
                                #region 支付订单
                                Model.PayOrder payorder = new Model.PayOrder();
                                payorder.PayOrder_CarCardTypeNo = person.Owner_CardTypeNo;
                                payorder.PayOrder_CarTypeNo = car.Car_VehicleTypeNo;
                                payorder.PayOrder_CouponRecordNo = null;
                                payorder.PayOrder_OrderTypeNo = Convert.ToString((int)Common.EnumOrderType.Wallet);
                                payorder.PayOrder_ParkKey = parking.Parking_Key;
                                payorder.PayOrder_ParkNo = parking.Parking_No;
                                payorder.PayOrder_Category = Model.EnumCarType.Prepaid.ToString();
                                payorder.PayOrder_CarNo = car.Car_CarNo;
                                payorder.PayOrder_PayedTime = DateTimeHelper.GetNowTime();
                                payorder.PayOrder_PayType = 0;
                                payorder.PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
                                payorder.PayOrder_Status = 1;
                                payorder.PayOrder_Time = DateTimeHelper.GetNowTime();
                                payorder.PayOrder_OwnerSpace = person.Owner_Space;
                                payorder.PayOrder_OwnerName = person.Owner_Name;
                                payorder.PayOrder_OwnerNo = person.Owner_No;
                                payorder.PayOrder_MonthBeginTime = car.Car_BeginTime;
                                payorder.PayOrder_MonthEndTime = car.Car_EndTime;
                                #endregion

                                payorder.PayOrder_TimeCountDesc = "后台导入";
                                payorder.PayOrder_Desc = "后台导入";
                                payorder.PayOrder_Money = Utils.ObjectToDecimal(person.Owner_Balance, 0);
                                payorder.PayOrder_PayedMoney = Utils.ObjectToDecimal(person.Owner_Balance, 0);

                                payorder = BLL.PayOrder.CreatePayOrder(true, payorder, parking.Parking_Key, "PC", lgAdmins);
                                payorder.PayOrder_DiscountMoney = payorder.PayOrder_Money - payorder.PayOrder_PayedMoney;

                                List<Model.PayPart> payPartList = BLL.CommonBLL.CreatePayPartList(null, payorder, 1);

                                payorderList.Add(payorder);
                                paypartList.AddRange(payPartList);


                                var ledger = new Model.Ledger()
                                {
                                    Ledger_CarNo = car.Car_CarNo,
                                    Ledger_Space = person.Owner_Space,
                                    Ledger_Type = 4,
                                    Ledger_CardType = 2,
                                    Ledger_Code = 1,
                                    Ledger_Money = Utils.ObjectToDecimal(person.Owner_Balance, 0),
                                    Ledger_BeforeMoeny = Owner_Balance,
                                    Ledger_AfterMoeny = Utils.ObjectToDecimal(person.Owner_Balance, 0),
                                    Ledger_Time = DateTime.Now,
                                    Ledger_Remark = "后台导入"
                                };
                                ledgerList.Add(ledger);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        BLL.UserLogs.AddLog(lgAdmins, SecondOption.Import, $"{CurrenRowIndex}行,导入人员信息发生错误：{ex.Message}", SecondIndex.Owner);
                    }
                }

                #region 多位多车有效期处理,车主下所有属于车位车辆采用导入最后一条车辆有效期

                List<string> carComplete = new List<string>();//记录已处理的车牌，循环时跳过
                foreach (var car in carList)
                {
                    //if (car.Car_IsMoreCar != 1) continue;
                    if (carComplete.Contains(car.Car_CarNo)) continue;

                    var cars = carList.FindAll(x => x.Car_OwnerNo == car.Car_OwnerNo).ToList();
                    var last = cars.Last();

                    if (cars.Count > 1)
                    {
                        var diff = cars.Find(x => x.Car_TypeNo != car.Car_TypeNo);
                        if (diff != null)
                        {
                            MiniResponse.ResponseResult($"同一个车位下车牌【{car.Car_CarNo}】与车牌【{diff.Car_CarNo}】车牌类型不一致，请修改后再试", false, null, "-1"); return;
                        }
                    }

                    cars.ForEach(d =>
                    {
                        d.Car_BeginTime = last.Car_BeginTime;
                        d.Car_EndTime = last.Car_EndTime;
                        d.Car_OwnerName = last.Car_OwnerName;
                    });
                    var owner = ownerList.Find(x => x.Owner_No == car.Car_OwnerNo);
                    owner.Owner_StartTime = last.Car_BeginTime;
                    owner.Owner_EndTime = last.Car_EndTime;

                    carComplete.AddRange(cars.Select(x => x.Car_CarNo));
                }
                #endregion

                //导入信息判断
                if (!string.IsNullOrEmpty(errorownermsg)) { MiniResponse.ResponseResult(errorownermsg, false); return; }
                if (carList == null || carList.Count == 0) { MiniResponse.ResponseResult("没有需要导入的车辆！", false, null, "1"); return; }

                var blackCarMsg = string.Empty;
                foreach (var car in carList)
                {
                    // 检查是否为黑名单
                    var blacklistCar = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"BlackList_CarNo='{car.Car_CarNo}' AND BlackList_Status=1");
                    if (blacklistCar != null)
                    {
                        var now = DateTime.Now;
                        if (blacklistCar.BlackList_BeginTime <= now && now <= blacklistCar.BlackList_EndTime)
                        {
                            blackCarMsg += $"车牌号[{car.Car_CarNo}]在黑名单有效期内，请先删除黑名单。<br/>";
                        }
                        else if (now < blacklistCar.BlackList_BeginTime)
                        {
                            blackCarMsg += $"车牌号[{car.Car_CarNo}]已登记为黑名单（生效时间：{blacklistCar.BlackList_BeginTime:yyyy-MM-dd HH:mm:ss}），请先删除黑名单。<br/>";
                        }
                    }
                }

                if (!string.IsNullOrEmpty(blackCarMsg))
                {
                    MiniResponse.ResponseResult(blackCarMsg.TrimEnd("<br/>".ToCharArray()), false, null, "1"); return;
                }

                List<Model.ParkOrder> retOrders = null;
                List<Model.OrderDetail> retDetails = null;
                if (replaceorder)
                {
                    var ssList = stopList.Copy();
                    ssList.AddRange(old_stopList);
                    BLL.Owner.ImportCars(lgAdmins, carList, ownerList, ssList, out var errmsg, out retOrders, out retDetails);
                    //BLL.ParkOrder.ImportCarToChangeOrder(carList, ownerList, ssList, carCardTypeList, out var parkorderList, out var orderDetailList);
                }

                var r = BLL.Car.AddCarOwnerList(carList, ownerList, stopList, retOrders, retDetails, payorderList, paypartList, ledgerList);
                if (r)
                {
                    BLL.UserLogs.AddLog(lgAdmins, SecondOption.Import, $"导入车主信息{carList?.Count ?? 0}条，车辆信息{ownerList?.Count}条", SecondIndex.Owner);
                    _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                    {
                        try
                        {
                            sendPush(carList, ownerList, stopList, retOrders, retDetails, AppBasicCache.IsSendTcp ? 20 : 1000);
                        }
                        catch (Exception e)
                        {
                            BLL.SystemLogs.AddLog(lgAdmins, "车辆导入", $"车辆导入分发岗亭异常:{e.ToString()}");
                        }

                        try
                        {
                            //循环添加上报的事件插入数据库
                            foreach (var item in carList)
                            {
                                var ownerModel = ownerList.Find(x => x.Owner_No == item.Car_OwnerNo);
                                BLL.PushEvent.MthCarRegister(parking.Parking_Key, item, ownerModel);
                                Task.Delay(5);
                            }

                            foreach (var payorder in payorderList)
                            {
                                BLL.PushEvent.MthCarChargeSend(
                                  parking?.Parking_Key
                                  , payorder.PayOrder_CarNo
                                  , payorder.PayOrder_Category
                                  , payorder.PayOrder_MonthBeginTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                  , payorder.PayOrder_MonthEndTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                  , payorder.PayOrder_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                                  , payorder.PayOrder_PayedMoney.Value.ToString()
                                  , payorder.PayOrder_OperatorName ?? "后台导入"
                                  , payorder.PayOrder_No);
                                Task.Delay(5);
                            }
                        }
                        catch (Exception e)
                        {
                            BLL.SystemLogs.AddLog(lgAdmins, "车辆导入", $"车辆导入循环添加上报的事件异常:{e.ToString()}");
                        }
                        return Task.CompletedTask;
                    });


                    string msg = string.Empty;
                    var isReg = BLL.Owner.CheckRegCar(carList.Select(x => x.Car_CarNo).ToList(), ref msg, false, true, true);

                    MiniResponse.ResponseResult($"数据导入成功！" + msg, true, null, "0");
                    return;
                }
                else
                {
                    MiniResponse.ResponseResult("数据导入失败！", false, null, "1");
                    return;
                }
                #endregion
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "车辆导入", $"车辆导入异常:{ex.ToString()}");
                MiniResponse.ResponseResult("车辆导入异常", false);
                return;
            }
        }

        private DataTable GetExcel(IFormFileCollection hfc, List<Model.ParkArea> areaList, List<Model.CarType> carTypeList, List<Model.CarCardType> carCardTypeList, ref ResultBase rb)
        {
            string webRootPath = _hostingEnvironment.ContentRootPath;
            string contentRootPath = _hostingEnvironment.ContentRootPath;

            if (!AppBasicCache.IsWindows) { Common.LocalFile.CreateDirectory("/mnt/sda1/b30/import"); }
            string imgPath = (AppBasicCache.IsWindows ? "\\wwwroot\\Data\\" : "/mnt/sda1/b30/import/") + DateTimeHelper.GetNowTime().ToString("yyyyMMddhhmmssf") + hfc[0].FileName;
            string physicalPath = AppBasicCache.IsWindows ? webRootPath + imgPath : imgPath;

            using (var stream = new FileStream(physicalPath, FileMode.Create))
            {
                hfc[0].CopyTo(stream);
            }
            //读取excel到Table
            NPOIExcelHelper npoi = new NPOIExcelHelper(physicalPath);
            DataTable dtExcel = npoi.ImportToDataTableHasDate("", true, 0);
            if (dtExcel == null)
            {
                rb = new ResultBase() { Code = "1", Message = "读取Excel数据失败", Success = false };
                return dtExcel;
            }

            //限制每次导入最大2000条
            if (dtExcel.Rows.Count > 10000)
            {
                rb = new ResultBase() { Code = "1", Message = "每次最多导入10000条", Success = false };
                return dtExcel;
            }

            int CurrenRowIndex = 0;
            string errorownermsg = string.Empty;    //错误记录
            try
            {
                List<string> carnoList = new List<string>();
                List<string> cardList = new List<string>();
                foreach (DataRow dr in dtExcel.Rows)
                {
                    CurrenRowIndex = dtExcel.Rows.IndexOf(dr) + 1; //当前数据行

                    #region 单行数据值转换
                    string carno = dr[0].ToString().Trim();                             //车牌号
                    string carcardtype = dr[1].ToString().Trim();                       //车牌类型
                    string cartype = dr[2].ToString().Trim();                           //车牌颜色
                    string begindate = dr[3].ToString().Trim();                         //开始日期
                    string enddate = dr[4].ToString().Trim();                           //结束效期
                    string name = dr[5].ToString().Trim();                              //姓名
                    string space = dr[6].ToString().Trim();                             //系统车位号
                    string parkspace = dr[7].ToString().Trim();                         //车场车位号
                    string cardno = dr[8].ToString().Trim();                         //车辆卡号
                    //string spacecount = dr[7].ToString().Trim();                        //车位数
                    string address = dr[9].ToString().Trim();                           //地址
                    string phone = dr[10].ToString().Trim();                             //手机号
                    string sex = dr[11].ToString().Trim();                              //性别
                    string money = dr[12].ToString().Trim();                            //余额
                    string remark = dr[13].ToString().Trim();                           //备注
                    string area = dr[14].ToString().Trim();                             //区域


                    if (!string.IsNullOrEmpty(name)) { dr[5] = Utils.RemoveInvalidStr(ref name); }
                    if (!string.IsNullOrEmpty(space)) { dr[6] = Utils.RemoveInvalidStr(ref space); }
                    if (!string.IsNullOrEmpty(parkspace)) { dr[7] = Utils.RemoveInvalidStr(ref parkspace); }
                    if (!string.IsNullOrEmpty(cardno)) { dr[8] = Utils.RemoveInvalidStr(ref cardno); }
                    if (!string.IsNullOrEmpty(remark)) { dr[13] = Utils.RemoveInvalidStr(ref remark); }
                    if (!string.IsNullOrEmpty(area)) { dr[14] = Utils.RemoveInvalidStr(ref area); }
                    if (!string.IsNullOrEmpty(address)) { dr[9] = Utils.RemoveInvalidStr(ref address); }

                    carno = carno.Replace("O", "0").Replace("o", "0").ToUpper();//系统默认车牌号字母O用数字0替代
                    #endregion

                    if (carnoList.Contains(carno)) { errorownermsg += $"第{CurrenRowIndex}行：车牌号[{carno}]重复.<br/>"; }
                    if (!string.IsNullOrEmpty(cardno) && cardList.Contains(cardno)) { errorownermsg += $"第{CurrenRowIndex}行：卡号[{cardno}]重复.<br/>"; }

                    Model.CarCardType carCardType = carCardTypeList.Find(x => x.CarCardType_Name == carcardtype);
                    if (string.IsNullOrEmpty(carno) || !Utils.ValidationCarNo(carno)) errorownermsg += $"第{CurrenRowIndex}行：车牌号[{carno}]格式错误.<br/>";
                    if (string.IsNullOrEmpty(carcardtype) || Encoding.Default.GetBytes(carcardtype).Length > 50) errorownermsg += $"第{CurrenRowIndex}行：车牌类型错误.<br/>";
                    else
                    {
                        if (carCardType == null) errorownermsg += $"第{CurrenRowIndex}行：车牌类型错误.<br/>";
                    }
                    if (string.IsNullOrEmpty(cartype) || Encoding.Default.GetBytes(cartype).Length > 50) errorownermsg += $"第{CurrenRowIndex}行：车牌颜色错误.<br/>";
                    else
                    {
                        Model.CarType ct = carTypeList.Find(x => x.CarType_Name == cartype);
                        if (ct == null) errorownermsg += $"第{CurrenRowIndex}行：车牌颜色错误.<br/>";
                    }

                    if (carCardType == null || carCardType.CarCardType_Type != 2)
                    {
                        if (string.IsNullOrEmpty(begindate) || !Utils.IsDateTime(begindate)) errorownermsg += $"第{CurrenRowIndex}行：开始日期[{begindate}]格式错误.<br/>";
                        else
                        {
                            DateTime StartDate = Convert.ToDateTime(begindate);
                            DateTime EffDate = Convert.ToDateTime(enddate);
                            if (EffDate < StartDate) errorownermsg += $"第{CurrenRowIndex}行：开始日期[{begindate}]必须小于或等于有效期[{enddate}].<br/>";
                        }
                        if (string.IsNullOrEmpty(enddate) || !Utils.IsDateTime(enddate)) errorownermsg += $"第{CurrenRowIndex}行：有效期[{enddate}]格式错误.<br/>";
                    }

                    if (!string.IsNullOrEmpty(money) && Encoding.Default.GetBytes(money).Length > 20) errorownermsg += $"第{CurrenRowIndex}行：余额[{money}]数字过长.<br/>";
                    if (!string.IsNullOrEmpty(money) && !Utils.IsDouble(money)) errorownermsg += $"第{CurrenRowIndex}行：余额[{money}]格式错误.<br/>";
                    if (!string.IsNullOrEmpty(name) && Encoding.Default.GetBytes(name).Length > 50) errorownermsg += $"第{CurrenRowIndex}行：车主姓名不能超过50个字符.<br/>";
                    if (!string.IsNullOrEmpty(space) && Encoding.Default.GetBytes(space).Length > 8) errorownermsg += $"第{CurrenRowIndex}行：系统车位号不能超过8个字符.<br/>";
                    if (!string.IsNullOrEmpty(parkspace) && Encoding.Default.GetBytes(parkspace).Length > 32) errorownermsg += $"第{CurrenRowIndex}行：车场车位号不能超过32个字符.<br/>";
                    if (!string.IsNullOrEmpty(cardno) && Encoding.Default.GetBytes(cardno).Length > 50) errorownermsg += $"第{CurrenRowIndex}行：车辆卡号不能超过50个字符.<br/>";

                    if (!string.IsNullOrEmpty(space))
                    {
                        bool isSpecialChar = !Utils.IsNumEn(space);
                        bool isLastCharLetter = Utils.IsLastCharLetter(space);
                        if (isSpecialChar)
                        {
                            errorownermsg += $"第{CurrenRowIndex}行：【{space}】系统车位号仅支持字母和数字,";
                        }
                        if (isLastCharLetter)
                        {
                            if (!isSpecialChar) errorownermsg += $"第{CurrenRowIndex}行：【{space}】最后一位不能是字母.";
                            else errorownermsg += $"最后一位不能是字母.";
                        }
                        else
                        {
                            if (errorownermsg.EndsWith(","))
                            {
                                errorownermsg = errorownermsg.Substring(0, errorownermsg.Length - 1) + ".";
                            }
                        }

                        if (isSpecialChar || isLastCharLetter) errorownermsg += "<br/>";
                    }


                    if (!string.IsNullOrEmpty(phone) && !Utils.IsPhone(phone)) { errorownermsg += $"第{CurrenRowIndex}行：手机号码[{phone}]格式错误.<br/>"; }
                    if (!string.IsNullOrEmpty(sex) && !",男,女,".Contains("," + sex + ",")) { errorownermsg += $"第{CurrenRowIndex}行：车主性别[{sex}]无效.<br/>"; }

                    if (!string.IsNullOrEmpty(remark) && remark.Length > 200) { errorownermsg += $"第{CurrenRowIndex}行：备注字符过长，请不要超出200个字符.<br/>"; }
                    if (!string.IsNullOrEmpty(area))
                    {
                        area = area.Trim(' ').Trim(' ');
                        area = area.Replace("，", ",").Replace("：", ":");
                        List<string> currentCarArea = area.Split(",").ToList();
                        foreach (var item in currentCarArea)
                        {
                            var itemArray = item.Split(':');
                            if (itemArray.Length != 2)
                            {
                                errorownermsg += $"第{CurrenRowIndex}行：区域名称格式设置错误【{item}】.<br/>";
                            }
                            else
                            {
                                if (string.IsNullOrEmpty(itemArray[0])) { errorownermsg += $"第{CurrenRowIndex}行：区域名称格式设置错误,区域名称不能为空【{item}】.<br/>"; continue; }
                                if (string.IsNullOrEmpty(itemArray[1])) { errorownermsg += $"第{CurrenRowIndex}行：区域名称格式设置错误,车位数不能为空【{item}】.<br/>"; continue; }
                                if (!Utils.IsNumeric(itemArray[1])) { errorownermsg += $"第{CurrenRowIndex}行：区域名称格式设置错误,车位数必须是数字【{item}】.<br/>"; continue; }

                                var nameList = itemArray[0].Split("、").ToList();
                                foreach (var n in nameList)
                                {
                                    if (n != "全部区域" && areaList.Find(x => x.ParkArea_Name == n) == null)
                                    {
                                        errorownermsg += $"第{CurrenRowIndex}行：区域名称不存在【{item}】.<br/>"; continue;
                                    }
                                }
                            }
                        }
                    }

                    carnoList.Add(carno);
                    if (!string.IsNullOrEmpty(cardno)) cardList.Add(cardno);
                }

                if (!string.IsNullOrEmpty(errorownermsg))
                {
                    rb = new ResultBase()
                    {
                        Code = "1",
                        Message = errorownermsg,
                        Success = false
                    };
                }
                Utils.DelFile(physicalPath); //删除Excel
            }
            catch (Exception ex)
            {
                rb = new ResultBase()
                {
                    Code = "1",
                    Message = $"第{CurrenRowIndex}行,存在数据格式错误！{ex.Message}",
                    Success = false
                };
                Utils.DelFile(physicalPath);
            }
            return dtExcel;
        }

        /// <summary>
        /// 数据太多，每次最多发送20条
        /// </summary>
        /// <param name="carList"></param>
        /// <param name="ownerList"></param>
        /// <param name="stopList"></param>
        private void sendPush(List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> stopList, List<Model.ParkOrder> parkOrderList, List<Model.OrderDetail> orderDetailList, int maxCount = 20)
        {
            try
            {
                var count = 0;
                foreach (var item in carList)
                {
                    var owner = ownerList.Find(x => x.Owner_No == item.Car_OwnerNo);

                    var stop = stopList.FindAll(x => x.StopSpace_OwnerNo == item.Car_OwnerNo);

                    var order = parkOrderList?.FindAll(x => x.ParkOrder_CarNo == item.Car_CarNo);

                    List<Model.OrderDetail> detail = null;
                    if (order != null && order.Count > 0)
                    {
                        var noes = order.Select(x => x.ParkOrder_No).ToList();
                        detail = orderDetailList?.FindAll(x => noes.Contains(x.OrderDetail_ParkOrderNo));
                    }

                    (List<Model.Car>, List<Model.Owner>, List<Model.StopSpace>, List<Model.Car>, List<Model.StopSpace>, List<Model.PayColl>, List<Model.ParkOrder>, List<Model.OrderDetail>) obj =
                        (new List<Model.Car>() { item }, new List<Model.Owner>() { owner }, stop, null, null, null, order, detail);

                    //分发岗亭
                    PushSync(Model.API.PushAction.Add, obj, new List<Model.SentryHost>(), "carimport", dataType: DataTypeEnum.Owner, Desc: "车辆导入");
                    Task.Delay(10).Wait();
                    count++;
                    DataCache.SyncAction.Set("carimport1", $"{carList.Count},{count},{count},0");
                }
            }
            catch (Exception ex)
            {
                DataCache.SyncAction.Del("carimport1");
                BLL.SystemLogs.AddLog(lgAdmins, "车辆导入", $"车辆导入异常:{ex.ToString()}");
            }
        }
        #endregion


        #region 清缴费用
        /// <summary>
        /// 清缴场内车辆费用
        /// </summary>
        /// <param name="Parking_No"></param>
        /// <param name="cct"></param>
        /// <param name="AllCars"></param>
        /// <param name="cars"></param>
        /// <param name="addInCar"></param>
        /// <param name="lgAdmins"></param>
        /// <param name="errmsg"></param>
        /// <param name="orderHandleResult"></param>
        /// <returns></returns>
        public static string PayedInParkCarFee(string Parking_No, Model.Owner model, Model.CarCardType cct, List<Model.CarExt> AllCars, List<Model.CarExt> cars, List<Model.StopSpace> spaces, ref List<Model.AddInParkTempCar> addInCar, AdminSession lgAdmins,
            out string errmsg, out bool orderHandleResult)
        {
            errmsg = "";
            orderHandleResult = false;

            Model.PolicyPark policy = BLL.PolicyPark.GetEntity(Parking_No);
            //查询是否已在场内
            if (policy != null && policy.PolicyPark_PayedCharge == 1 && cct != null && cars != null && cars.Count > 0)
            {
                foreach (var car in cars)
                {
                    Model.CarType carType = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                    Model.ParkOrder parkOrder = BLL.ParkOrder.GetParkOrder("*", Parking_No, car.Car_CarNo, 0, 200);
                    if (parkOrder != null)
                    {
                        var isPay = Calc.GetChargeByCar(parkOrder, DateTimeHelper.GetNowTime(), null, null, false);
                        if (isPay.payed == 1 && isPay.payedamount > 0)
                            return TyziTools.Json.ToString(new { success = false, msg = $"{parkOrder.ParkOrder_CarNo}已在场内，请清缴停车费用", data = parkOrder.ParkOrder_No });
                        else if (isPay.payed == 2)
                            return TyziTools.Json.ToString(new { success = false, msg = $"计费失败，请稍候重试", data = parkOrder.ParkOrder_No });
                    }
                }
            }

            BLL.Owner.PayedInParkCarFee(Parking_No, model, cct, AllCars, cars, spaces, ref addInCar, lgAdmins, out errmsg, out orderHandleResult);

            return null;
        }
        #endregion

        /// <summary>
        /// 查询车位绑定的场内车辆，返回最早车辆的入场时间
        /// </summary>
        /// <param name="Owner_No"></param>
        /// <returns></returns>
        public IActionResult GetInCarTime(string Owner_No, bool isHand = false)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.Retweet.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");
            try
            {
                Model.Owner owner = BLL.Owner.GetEntity(Owner_No, false);
                if (owner == null) { return ResOk(false, $"车辆信息异常"); }


                List<Model.Car> cars = BLL.Car.GetAllEntity("Car_CarNo", $"Car_OwnerNo = '{Owner_No}'");
                if (cars == null || cars.Count == 0) { return ResOk(false, "车位未绑定车辆"); }

                if (owner.Owner_CardType == 2) { return ResOk(false, $"当前余额 {owner.Owner_Balance} 元"); }

                var parameters5 = new { InCar_Status = 200, InCar_CarNos = cars.Select(x => x.Car_CarNo).ToList() };
                Model.InCar incarModel = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "*", "InCar_Status=@InCar_Status AND InCar_CarNo IN @InCar_CarNos ORDER BY InCar_EnterTime", parameters5);
                if (incarModel != null)
                {
                    if (owner.Owner_EndTime > DateTime.Now)
                    {
                        return ResOk(false, $"车位未过期，存在场内车辆【{incarModel.InCar_CarNo}】");
                    }
                    else
                    {
                        if (isHand)
                            return ResOk(true, $"车位已过期，将续期开始日期更新为：场内车【{incarModel.InCar_CarNo}】的入场时间【{incarModel.InCar_EnterTime.Value.ToString("yyyy-MM-dd")}】", incarModel.InCar_EnterTime.Value.ToString("yyyy-MM-dd"));
                        else
                            return ResOk(false, $"车位已过期，存在场内车辆【{incarModel.InCar_CarNo}】");
                    }
                }
                else
                {
                    if (owner.Owner_EndTime > DateTime.Now)
                    {
                        return ResOk(false, "车位未过期，无场内车辆");
                    }
                    else
                    {
                        return ResOk(false, "车位已过期，无场内车辆");
                    }
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "储值车充值", "储值车充值发生异常:" + ex.ToString());
                return ResOk(false, "数据异常");
            }
        }


        #region 白名单记录


        public ActionResult WhiteRecord()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.DownLoadCarList.ToString(), false, true, lgAdmins))
                return new EmptyResult();
            return View();
        }

        /// <summary>
        /// 记录列表
        /// </summary>
        public void GetWhiteRecordList(int pageIndex, int pageSize, string conditionParam)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.DownLoadCarList.ToString(), adminSession: lgAdmins))
            { Response.WriteAsync(oModel.ParseJson()); return; }

            Response.WriteAsync(SearchWhiteRecordList(pageIndex, pageSize, conditionParam).ParseJson());
        }

        /// <summary>
        /// 查询记录列表
        /// </summary>
        private Model.PageResult SearchWhiteRecordList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                string sqlwhere = "";
                Model.WhiteRecordExt obj = Utils.ClearModelRiskSQL<Model.WhiteRecordExt>(conditionParam);

                if (!string.IsNullOrEmpty(obj.WhiteRecord_CarNo))
                {
                    if (obj.WhiteRecord_CarNo.Contains(","))
                    {
                        sqlwhere += $" AND WhiteRecord_CarNo in @WhiteRecord_CarNo ";
                    }
                    else
                    {
                        sqlwhere += $" AND WhiteRecord_CarNo like @WhiteRecord_CarNo1 ";
                    }
                }
                if (!string.IsNullOrEmpty(obj.WhiteRecord_DeviceNo))
                    sqlwhere += $" AND WhiteRecord_DeviceNo like @WhiteRecord_DeviceNo ";
                if (!string.IsNullOrEmpty(obj.WhiteRecord_Remark))
                    sqlwhere += $" AND WhiteRecord_Remark like @WhiteRecord_Remark ";

                if (!string.IsNullOrEmpty(obj.WhiteRecord_DeviceName))
                {
                    var parameters2 = new { Device_Name = $"%{obj.WhiteRecord_DeviceName}%" };
                    var devices = BLL.Device.GetAllEntity("Device_No,Device_Name", "Device_SentryHostNo!='' AND Device_Name LIKE @Device_Name", parameters2);
                    if (devices.Count > 0)
                        sqlwhere += $" AND WhiteRecord_DeviceNo in ('{string.Join("','", devices.Select(x => x.Device_No))}') ";
                    else
                        sqlwhere += $" AND WhiteRecord_DeviceStatus='100' ";
                }

                if (obj.WhiteRecord_DeviceStatus != null)
                {
                    if (obj.WhiteRecord_DeviceStatus == 0)
                        sqlwhere += $" AND IFNULL(WhiteRecord_DeviceStatus,0)=0 ";
                    else
                        sqlwhere += $" AND WhiteRecord_DeviceStatus=@WhiteRecord_DeviceStatus ";
                }
                if (obj.WhiteRecord_Status != null)
                {
                    if (obj.WhiteRecord_Status == 0)
                        sqlwhere += $" AND IFNULL(WhiteRecord_Status,0)=0 ";
                    else
                        sqlwhere += $" AND WhiteRecord_Status=@WhiteRecord_Status ";
                }


                var parameters = new
                {
                    WhiteRecord_CarNo = !string.IsNullOrEmpty(obj.WhiteRecord_CarNo) ? (obj.WhiteRecord_CarNo.Contains(",") ? obj.WhiteRecord_CarNo.Split(',').ToList() : null) : null,
                    WhiteRecord_CarNo1 = !string.IsNullOrEmpty(obj.WhiteRecord_CarNo) ? (obj.WhiteRecord_CarNo.Contains(",") ? null : $"%{obj.WhiteRecord_CarNo}%") : null,
                    WhiteRecord_DeviceNo = !string.IsNullOrEmpty(obj.WhiteRecord_DeviceNo) ? $"%{obj.WhiteRecord_DeviceNo}%" : null,
                    WhiteRecord_Remark = !string.IsNullOrEmpty(obj.WhiteRecord_Remark) ? $"%{obj.WhiteRecord_Remark}%" : null,
                    WhiteRecord_Status = obj.WhiteRecord_Status,
                    WhiteRecord_DeviceStatus = obj.WhiteRecord_DeviceStatus,
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.WhiteRecord> lst = BLL.BaseBLL._GetList<Model.WhiteRecord>("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);
                var data = TyziTools.Json.ToObject<List<Model.WhiteRecordExt>>(TyziTools.Json.ToString(lst));
                if (data.Count > 0)
                {
                    var parameters3 = new { Device_Nos = data.Select(x => x.WhiteRecord_DeviceNo).ToList() };
                    var devices = BLL.Device.GetAllEntity("Device_No,Device_Name", "Device_No IN @Device_Nos ", parameters3);
                    data.ForEach(x =>
                    {
                        x.WhiteRecord_DeviceName = devices?.Find(m => m.Device_No == x.WhiteRecord_DeviceNo)?.Device_Name;
                    });
                }

                oModel.code = 0;
                oModel.data = data;
                oModel.count = totalRecord;
                return oModel;
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询报警记录异常", "查询报警记录异常:" + ex.ToString());

                oModel.code = 4;
                oModel.msg = ex.Message;
            }
            return oModel;
        }


        public IActionResult SendWhiteRecord(string WhiteRecord_IDArray)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.DownLoadCarList.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限处理");

                if (string.IsNullOrEmpty(WhiteRecord_IDArray)) return ResOk(false, "请选择");

                var WhiteRecord_IDList = TyziTools.Json.ToObject<List<string>>(WhiteRecord_IDArray);
                if (WhiteRecord_IDList == null) return ResOk(false, "请选择");

                var parameters6 = new { WhiteRecord_IDs = WhiteRecord_IDList };
                var recordList = BLL.BaseBLL._GetAllEntity(new Model.WhiteRecord(), "*", "WhiteRecord_ID IN @WhiteRecord_IDs", parameters6);
                var sentrys = BLL.SentryHost.GetAllEntity();
                var parameters4 = new
                {
                    ParkOrder_ParkNo = parking.Parking_No,
                    ParkOrder_CarNo = recordList.Select(x => x.WhiteRecord_CarNo).ToList(),
                    ParkOrder_Status = new int[] { 0, 200 }
                };
                var parkorderList = BLL.ParkOrder.GetAllEntity("*", "ParkOrder_ParkNo=@ParkOrder_ParkNo AND ParkOrder_CarNo IN @ParkOrder_CarNo AND ParkOrder_StatusNo IN @ParkOrder_Status", parameters: parameters4);
                var devices = BLL.Device.GetAllEntity("Device_No,Device_SentryHostNo", $"Device_No in ('{string.Join("','", recordList.Select(x => x.WhiteRecord_DeviceNo))}') and Device_SentryHostNo!=''");

                sentrys.ForEach(sentry =>
                {
                    var devices_sub = devices.Where(x => x.Device_SentryHostNo == sentry.SentryHost_No).ToList();
                    var devicenoes = devices_sub.Select(x => x.Device_No).ToList();

                    var records = recordList.Where(x => devicenoes.Contains(x.WhiteRecord_DeviceNo)).ToList();
                    double count = records.Count;
                    for (int i = 1; i <= Math.Ceiling(Utils.ObjectToDouble(count / 10, 0)); i++)
                    {
                        var num = 10 > records.Count ? records.Count : 10;
                        var takeList = records.Take(num).ToList();
                        records.RemoveRange(0, num);

                        Push(Model.API.PushAction.Add, takeList, new List<Model.SentryHost>() { sentry }, "syncwhiterecord");
                        Task.Delay(10).Wait();
                    }
                });

                var devicenoes = devices.Select(x => x.Device_No).ToList();
                var recordList2 = recordList.Where(x => !devicenoes.Contains(x.WhiteRecord_DeviceNo)).ToList();
                if (recordList2.Count > 0)
                {
                    recordList2.ForEach(x => { x.WhiteRecord_Remark = "设备未关联岗亭，无法同步处理"; });
                    BLL.BaseBLL._AddOrUpdateModel(recordList2);
                }

                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "同步白名单:" + string.Join(",", recordList.Select(x => x.WhiteRecord_CarNo)), SecondIndex.Owner);
                return ResOk(true, "处理成功");
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, $"同步白名单异常：{ex.ToString()}", SecondIndex.Owner);
                return ResOk(false, "处理失败");
            }
        }


        #endregion

        #region 场内车辆
        /// <summary>
        /// 场内记录
        /// </summary>
        /// <returns></returns>
        public ActionResult OwnerCarInPark()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.SearchPassRecord.ToString(), false))
                return new EmptyResult();

            ViewBag.Owner_No = Request.Query["Owner_No"];

            return View();
        }

        /// <summary>
        /// 打开场内车辆详情
        /// </summary>
        public ActionResult InParkCarDetail(string ParkOrder_No)
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.SearchPassRecord.ToString(), false))
                return new EmptyResult();

            ViewBag.ParkOrder_No = ParkOrder_No;

            return View();
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        public IActionResult GetParkOrderByNo(string ParkOrder_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.SearchPassRecord.ToString(), false))
                    return ResOk(false, "无权限");

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                var time = DateTime.Parse("1900-01-01 00:00:00");
                if (model != null && model.ParkOrder_OutTime == time) { model.ParkOrder_OutTime = null; }

                List<Model.PayOrder> payOrders = BLL.PayOrder.GetAllEntity("*", $"PayOrder_ParkOrderNo='{ParkOrder_No}'");

                List<Model.OrderDetail> detailList = BLL.OrderDetail.GetAllEntity(ParkOrder_No);
                if (detailList != null) detailList = detailList.OrderBy(x => x.OrderDetail_EnterTime).ToList();
                detailList.ForEach(x =>
                {
                    if (x != null && x.OrderDetail_OutTime == time) { x.OrderDetail_OutTime = null; }
                });

                List<Model.CouponRecord> couponList = null;
                if (model != null && model.ParkOrder_StatusNo != EnumParkOrderStatus.Follow) couponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "*", $"CouponRecord_ParkOrderNo='{ParkOrder_No}' and CouponRecord_Status=1");

                List<Model.CalcDetail> calcList = BLL.BaseBLL._GetAllEntity(new Model.CalcDetail(), "*", $"CalcDetail_ParkOrderNo='{ParkOrder_No}'");

                List<Model.DetentionPenalty> penaltyList = BLL.BaseBLL._GetAllEntity(new Model.DetentionPenalty(), "*", $"DetentionPenalty_ParkOrderNo='{ParkOrder_No}'");

                return ResOk(true, "", new { model = model, payOrders = payOrders, parking = parking, detail = detailList, coupon = couponList, calclist = calcList, penaltylist = penaltyList });
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询停车订单详情", "查询停车订单详情发生异常:" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 查询场内车辆列表
        /// </summary>
        public IActionResult GetInParkCarList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.SearchPassRecord.ToString(), false))
                    return Ok(oModel);

                var obj = Utils.ClearModelRiskSQL<JObject>(conditionParam);
                var Owner_No = obj["Owner_No"].ToString();
                var InCar_CarNo = obj.ContainsKey("InCar_CarNo") ? obj["InCar_CarNo"].ToString() : "";

                var owner = BLL.Owner.GetEntity(Owner_No);
                if (owner == null) { return Ok(oModel); }

                int pageCount = 0, totalRecord = 0;

                var sqlCar = $" AND Car_OwnerNo = '{Owner_No}'";
                if (!string.IsNullOrEmpty(InCar_CarNo)) sqlCar += string.Format(" and Car_CarNo like '%{0}%' ", InCar_CarNo);

                List<Model.Car> carLst = BLL.BaseBLL._GetList<Model.Car>("*", sqlCar, pageIndex, pageSize, out pageCount, out totalRecord);
                if (carLst == null || carLst.Count == 0) { oModel.msg = "无数据"; return Ok(oModel); }

                var carnos = string.Join("','", carLst.Select(x => x.Car_CarNo));
                string sqlwhere = " InCar_Status = 200 ";

                if (string.IsNullOrEmpty(InCar_CarNo))
                {
                    sqlwhere += $" AND InCar_CarNo in ('{carnos}') ";
                }
                else
                {
                    sqlwhere += $" AND InCar_CarNo in ('{carnos}') and  InCar_CarNo like '%{InCar_CarNo}%' ";
                }

                List<Model.InCar> inCarLst = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "*", sqlwhere);
                inCarLst = inCarLst.OrderByDescending(x => x.InCar_EnterTime).ToList();

                int index = 1;
                inCarLst.ForEach(x =>
                {
                    var area = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, x.InCar_ParkAreaNo);
                    x.InCar_ParkAreaNo = area.ParkArea_Name;
                    x.InCar_ID = index++;
                });

                carLst.ForEach(x =>
                {
                    var inCar = inCarLst.Find(m => m.InCar_CarNo == x.Car_CarNo);
                    if (inCar == null)
                    {
                        inCar = new Model.InCar()
                        {
                            InCar_CarNo = x.Car_CarNo,
                            InCar_ID = index++,
                        };
                        inCarLst.Add(inCar);
                    }
                });

                oModel.code = 0;
                oModel.data = inCarLst;
                oModel.count = totalRecord;
                oModel.msg = "成功";
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询报警记录异常", "查询报警记录异常:" + ex.ToString());

                oModel.code = 4;
                oModel.msg = ex.Message;
            }
            return Ok(oModel);
        }

        /// <summary>
        /// 删除场内停车订单
        /// </summary>
        public IActionResult DeleteInCar(string InCar_CarNo)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.SearchPassRecord.ToString(), false))
                    return ResOk(false, "无权限处理");

                var inCar = BLL.BaseBLL._GetEntityByWhere(new Model.InCar(), "*", $"InCar_CarNo = '{InCar_CarNo}' and InCar_Status = 200");
                if (inCar == null) { return ResOk(false, "未找到场内停车订单"); }

                var parkorder = BLL.ParkOrder.GetEntity(inCar.InCar_ParkOrderNo);
                if (parkorder == null) { return ResOk(false, "未找到关联停车订单"); }

                var confirmOrders = ConfirmRelease.Results.Values.Where(x => x.passres?.parkorderno == inCar.InCar_ParkOrderNo || x.resorder?.resOut?.parkorder?.ParkOrder_No == inCar.InCar_ParkOrderNo).ToList();
                if (confirmOrders.Count > 0)
                {
                    var carnoes = confirmOrders.Select(x => x.passres.carno).ToList();
                    return ResOk(false, $"关闭失败，请先处理岗亭正在出场的车辆：{string.Join(",", carnoes)}");
                }

                var parkOrders = new List<Model.ParkOrder>() { parkorder };
                var res = BLL.ParkOrder.CloseList2(parkOrders);
                if (res > 0)
                {
                    DataCache.ParkOrder.Del(parkorder.ParkOrder_No);

                    parkOrders.FindAll(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)?.ForEach(item =>
                    {
                        item.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                        BLL.PushEvent.CloseCar(parking.Parking_Key, item, "场内关闭");
                    });
                    BLL.PushEvent.SendParkSpace(parking.Parking_No);

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, $"关闭场内停车订单：{string.Join(",", parkOrders.Select(x => x.ParkOrder_CarNo))}", SecondIndex.InParkRecord);
                    return ResOk(true, "关闭成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, $"关闭停车订单失败：{string.Join(",", parkOrders.Select(x => x.ParkOrder_CarNo))}", SecondIndex.InParkRecord);
                    return ResOk(false, "关闭失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Close, "关闭场内停车订单异常:" + ex.ToString(), SecondIndex.Owner);
                return ResOk(false, ex.Message);
            }
        }
        #endregion

        #region 修改车牌类型
        public IActionResult SetCarCardType()
        {
            if (!Powermanage.PowerCheck("Owner", PowerEnum.SetCarCardType.ToString(), false))
                return ResOk(false, "无权限");

            return View();
        }

        /// <summary>
        /// 修改车牌类型
        /// </summary>
        public IActionResult SaveSetCarCardType(string ownerNoList, string carcardtype, bool chkincar)
        {
            try
            {
                if (!Powermanage.PowerCheck("Owner", PowerEnum.SetCarCardType.ToString(), false))
                    return ResOk(false, "无权限");


                var cct = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, carcardtype);
                if (cct == null) { return ResOk(false, "未找到车牌类型信息"); }

                List<Model.StopSpace> allStopspace = BLL.BaseBLL._GetAllEntity(new StopSpace(), "*", $"StopSpace_OwnerNo in ('{string.Join("','", ownerNoList)}')");
                List<Model.Owner> owners = AppBasicCache.GetOwner.Values.Where(x => ownerNoList.Contains(x.Owner_No)).ToList();
                List<Model.Car> sourceAllCar = AppBasicCache.GetCar.Values.Where(y => ownerNoList.Contains(y.Car_OwnerNo)).ToList();
                List<Model.CarExt> allCar = TyziTools.Json.ToObject<List<Model.CarExt>>(TyziTools.Json.ToString(sourceAllCar));
                string incarTips = "";
                if (chkincar)
                {
                    var incars = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "*", $"InCar_Status = 200 and InCar_CarNo in ('{string.Join("','", allCar.Select(x => x.Car_CarNo))}')");
                    if (incars.Count > 0)
                    {
                        var carsToRemove = new List<Model.CarExt>();

                        foreach (var car in allCar)
                        {
                            if (incars.Find(x => x.InCar_CarNo == car.Car_CarNo) != null)
                            {
                                incarTips += $"{car.Car_CarNo},";
                                carsToRemove.Add(car);

                                if (owners.Remove(owners.Find(x => x.Owner_No == car.Car_OwnerNo)))
                                {
                                    allStopspace.RemoveAll(x => x.StopSpace_OwnerNo == car.Car_OwnerNo);
                                }
                            }
                        }

                        foreach (var car in carsToRemove)
                        {
                            allCar.Remove(car);
                        }
                    }

                    if (incarTips.Length > 0) { incarTips = $"场内车辆修改不生效：{incarTips.TrimEnd(',')}"; }
                }

                List<string> errList = new List<string>();
                string errmsg = "";
                bool orderHandleResult = false;
                List<Model.AddInParkTempCar> addInCar = new List<AddInParkTempCar>();
                List<Model.Owner> newOwners = new List<Model.Owner>();
                List<Model.Car> newCars = new List<Model.Car>();
                List<Model.ParkOrder> orders = new List<Model.ParkOrder>();
                List<Model.OrderDetail> details = new List<Model.OrderDetail>();

                int count = 0;
                foreach (var owner in owners)
                {
                    addInCar = new List<AddInParkTempCar>();
                    owner.Owner_CardTypeNo = cct.CarCardType_No;
                    owner.Owner_CardType = cct.CarCardType_Type;
                    var cars = allCar.Where(y => y.Car_OwnerNo == owner.Owner_No).ToList();
                    cars.ForEach(x => { x.Car_TypeNo = cct.CarCardType_No; x.CarCardType_Name = cct.CarCardType_Name; x.Car_Category = cct.CarCardType_Category; });
                    var spaces = allStopspace.FindAll(z => z.StopSpace_OwnerNo == owner.Owner_No);
                    var ret = BLL.Owner.PayedInParkCarFee(AppBasicCache.GetParking.Parking_No, owner, cct, cars, cars, spaces, ref addInCar, lgAdmins, out errmsg, out orderHandleResult);

                    if (cct.CarCardType_Type == 2) owner.Owner_SpaceNum = 0;
                    if (ret != null)
                    {
                        errList.Add($"[{owner.Owner_Space}]{errmsg}");
                    }
                    else
                    {
                        newOwners.Add(owner);
                        newCars.AddRange(TyziTools.Json.ToObject<List<Model.Car>>(TyziTools.Json.ToString(cars)));
                        if (addInCar.Count > 0)
                        {
                            addInCar.ForEach(x =>
                            {
                                orders.AddRange(x.orders);
                                details.AddRange(x.details);
                            });
                        }
                        count++;
                    }
                }


                var updateRet = BLL.Owner.UpdateByList(newOwners, newCars);
                if (updateRet >= 0)
                {
                    _ = CustomThreadPool.SyncTaskPool.QueueTask(null, async () =>
                    {
                        foreach (var car in newCars)
                        {
                            try
                            {
                                var nOrders = orders.FindAll(x => x.ParkOrder_CarNo == car.Car_CarNo);
                                var nDetails = details.FindAll(x => x.OrderDetail_CarNo == car.Car_CarNo);

                                if (nOrders?.Count > 0)
                                {
                                    var orderRes = BLL.ParkOrder.CarInComplete(nOrders, nDetails);
                                    if (orderRes > 0)
                                    {
                                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, $"修改车牌类型更新订单成功,车牌号：{car.Car_CarNo}，订单号：{string.Join(",", nOrders.Select(x => x.ParkOrder_No))}", SecondIndex.Owner);

                                        try
                                        {
                                            var outOrder = nOrders.FindAll(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out);
                                            var inOrder = nOrders.FindAll(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In);
                                            if (outOrder != null)
                                            {
                                                foreach (var item in outOrder)
                                                {
                                                    BLL.PushEvent.OutCar(parking.Parking_Key, item);
                                                    await Task.Delay(50);
                                                }
                                            }

                                            if (inOrder != null)
                                            {
                                                foreach (var item in inOrder)
                                                {
                                                    BLL.PushEvent.EnterCar(parking.Parking_Key, item);
                                                    await Task.Delay(50);
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, "修改车牌类型推送事件异常:" + ex.Message, SecondIndex.Owner);
                                        }


                                        nOrders?.ForEach(x => { DataCache.ParkOrder.Del(x.ParkOrder_No); });
                                    }
                                    else
                                    {
                                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, $"修改车牌类型更新订单失败,车牌号：{car.Car_CarNo}，订单号：{string.Join(",", nOrders.Select(x => x.ParkOrder_No))}", SecondIndex.Owner);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, $"修改车牌类型更新订单异常，车牌号：{car.Car_CarNo}，{ex.ToString()}", SecondIndex.Owner);
                            }
                        }

                        try
                        {
                            foreach (var car in newCars)
                            {
                                BLL.PushEvent.MthCarRegister(parking.Parking_Key, car, newOwners.Find(x => x.Owner_No == car.Car_OwnerNo), null, allStopspace.FindAll(z => z.StopSpace_OwnerNo == car.Car_OwnerNo));
                            }
                        }
                        catch (Exception ex)
                        {
                            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, $"修改车牌类型上报云平台异常 .：{ex.ToString()}", SecondIndex.Owner);
                        }
                    });

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, $"修改车牌类型[{cct.CarCardType_Name}]成功,车位号：{string.Join(",", newOwners.Select(x => x.Owner_Space))}，车牌号：{string.Join(",", newCars.Select(x => x.Car_CarNo))}", SecondIndex.Owner);

                    return ResOk(true, $"修改成功！" + (incarTips.Length > 0 ? $"<br/><br/>{incarTips}" : "") + (errList.Count > 0 ? $"<br/><br/>{string.Join(",", errList)}" : ""));
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, $"修改车牌类型[{cct.CarCardType_Name}]失败，执行SQL更新失败,车位号：{string.Join(",", newOwners.Select(x => x.Owner_Space))}，车牌号：{string.Join(",", newCars.Select(x => x.Car_CarNo))}", SecondIndex.Owner);
                    return ResOk(true, $"修改失败！");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.SetCarCardType, "修改车牌类型异常:" + ex.Message, SecondIndex.Owner);
                return ResOk(false, ex.Message);
            }
        }
        #endregion
    }
}