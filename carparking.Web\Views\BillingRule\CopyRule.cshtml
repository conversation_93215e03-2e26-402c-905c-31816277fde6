﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>计费规则</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/css/free.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <style>

        html, body { height: 100% !important; width: 100%; overflow: hidden; }
        body { height: auto; }
        .mainbody { padding: 15px; }

        .layui-select-title input { /*color: #0094ff;*/ }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.xsall { width: 70px; float: left; }
        .select-xsall { float: left; margin-right: 5px; }


        .layui-row.primary { padding: 1rem 0; }
        .layui-row.primary label { font-weight: bold; background-color: #18605a; color: #fff; padding: 3px 10px; }

        .layui-select-title input { }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { /*margin-top: 10px;*/ }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.xsall { width: 70px; float: left; }
        .select-xsall { float: left; margin-right: 5px; }

        .layui-col-xs12 { margin-bottom: 5px; padding-bottom: 10px; }
        .layui-row.panel { border-bottom: 1px solid #bbb; }

        .workday_item, .workday_holidayItem { border-top: 1px solid #54c8d8; }
        .workday_item:last-child { border-bottom: 1px solid #bbb; }
        .workday_holidayItem:last-child, .tmplnoworkday_notimepart_numbertime_item:last-child, .tmplnoworkday_notimepart_spacetime_item:last-child, .tmplnoworkday_notimepart_rangtime_item:last-child, .tmplnoworkday_timepart_item:last-child { border-bottom: 1px solid #bbb; }
        .tmplnoworkday_timepart_item:not(:last-child) { border-bottom: 1px solid #54c8d8; }

        .divInLine { display: inline-block; margin-top: 5px; }
        .divInLine input { max-height: 30px; }
        .divInLine2 input { max-height: 38px !important; width: 100%; }
        .divInLine input.normal { max-width: 50px; max-height: 30px !important; text-align: center; padding-left: 3px; }
        .divInLine button { max-height: 32px !important; line-height: 30px; padding: 0px 10px; margin-bottom: 5px; }
        .divInLine label { height: 15px; padding: 0; }
        .workday_delitem, .workday_holidayDelitem, .tmplnoworkday_notimepart_rangtime_item_rule_delitem, .noworkday_timepart_delitem, .tmplworkday_rangtime_item_rule_delitem { background-color: #ff6a00 !important; }
        .workday_header, .workday_holiday_header { margin-bottom: 5px; }
        .workday_header { padding: 3px; }
        .workday_holiday_header { float: left; width: 100%; padding: 3px; }

        .workday_holiday_header span, .workday_header span { font-weight: 600; }
        input[type=time] { min-width: 100px; }
        .topheader { padding: 3px; }

        #ruleContent { }
        .tipMsg { color: #18605a; font-size: 15px; padding-left: 15px; margin-left: 20px; }

        .edit-label { float: left; }
        .edit-ipt-ban { float: left; }
        .label2 { color: #999; font-size: 12px; padding-bottom: 5px; clear: both; }
        .itemcontent { }

        .checkResult { color: red; font-weight: 500; }
        .layui-row { margin-bottom: 15px; }
        .itemtitle { height: 28px; text-align: center; padding-top: 8px; font-weight: 700; color: #635f5f; background: linear-gradient(to right,#fbf9f9,#e9eefd85,#e9eefd85) !important; cursor: pointer; }
        .headitem { margin-bottom: 10px; margin-top: 10px; }
        .layui-btn { }
        .layui-btn-normal { background-color: #1ab394; }
        .iconItem { border: 1px dashed #bbb; height: 20px; border-radius: 20px; right: 10px; float: right; width: 20px; text-align: center; line-height: 20px; cursor: pointer; }
        .iconItem:hover { border: 1px dashed #424040; }
        .iconItem:hover i { color: #19be3b; }
        .iconItem i { color: #424040; font-size: 13px; }

        .iconItem2 { border: 1px dashed #bbb; height: 20px; border-radius: 20px; right: 10px; float: right; width: 20px; text-align: center; line-height: 20px; cursor: pointer; }
        .iconItem2:hover { border: 1px dashed #424040; }
        .iconItem2:hover i { color: #19be3b; }
        .iconItem2 i { color: #424040; font-size: 13px; }

        .btnUnit { border-color: #f2eeee !important; border-top-right-radius: 5px; border-bottom-right-radius: 5px; color: #5db587 }
        .layui-btn-outline { margin-left: 0 !important; }

        .input-group { position: relative; display: table; border-collapse: separate; }
        .input-group-addon, .input-group-btn { width: 1%; white-space: nowrap; vertical-align: middle; }
        .input-group .form-control, .input-group-addon, .input-group-btn { display: table-cell; }

        .help-btn { z-index: 999; position: absolute; width: 20px; margin-left: 7px; margin-top: 6px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }

        .help-btn-tip { z-index: 999; position: absolute; width: 20px; right: -28px; margin-top: -27px !important; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn-tip:after { font-weight: bold; }
        .help-btn-tip:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }

        .help-tmpl { width: 100%; height: auto; min-height: 100px; position: relative; line-height: 24px; }
        .help-tmpl ul { margin: 10px; padding: 0; list-style: none; }
        .help-tmpl p { margin: 10px; padding: 0; text-indent: 0; }
        .help-tmpl .help-footer { margin: 10px; }
        .help-tmpl ul img { box-shadow: 0px 1px 10px #bbb; max-width: 100%; }
        .tipRequest { }
        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }
        .stime { max-width: 80px !important; width: 80px !important; }
        .layui-laydate-content > .layui-laydate-list { padding-bottom: 0px; overflow: hidden; }
        .layui-laydate-content > .layui-laydate-list > li { width: 50% }
        .merge-box .scrollbox .merge-list { padding-bottom: 5px; }
        .laydate-time-list ol li { padding-left: 53px !important; }
        .label-content { max-height: 95px; overflow-y: auto; }

        .tgreen { color: #009688 !important }
        .tblue { color: #1E9FFF !important }
        .tcyan { color: #2F4056 !important }
        .cards { position: absolute; z-index: 999; float: right; top: 0; right: 0; }
        .card { height: auto; border: 1px solid #e9eefd !important; padding: 6px; }
        .card-header { background-color: #e9eefd !important; padding: 10px !important; font-weight: bold; }
    </style>
</head>
<body>

    <div class="ibox-content layui-form">
        <span class="light"></span>
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row">
                <div class="edit-label  layui-col-xs2">复制计费规则</div>
                <div class="edit-ipt-ban layui-col-xs8">
                    <select class="layui-select" lay-search id="ChargeRules_No" name="ChargeRules_No" data-key="ChargeRules_No">
                        <option value="">请选择规则</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="edit-label  layui-col-xs2">规则名称</div>
                <div class="edit-ipt-ban layui-col-xs8">
                    <input type="text" class="layui-input v-null" id="ChargeRules_Name" name="ChargeRules_Name" data-minlen="2" maxlength="50" value="" placeholder="请填写规则名称" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label type">规则有效期</div>
                <div class="layui-col-xs3 edit-ipt-ban type">
                    <input type="text" class="layui-input v-null v-submit" value="" id="ChargeRules_Time" name="" data-key="ChargeRules_Time" placeholder="请选择规则有效期" readonly />
                </div>
                <div class="layui-col-xs1 red-mark type">*</div>
                <div class="layui-col-xs2 edit-label ">车牌类型</div>
                <div class="layui-col-xs3 edit-ipt-ban">
                    <div id="BillRuleTemp_CarCardType" class="v-null" style="min-width:210px;"></div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs2 edit-label ">车牌颜色</div>
                <div class="layui-col-xs3 edit-ipt-ban">
                    <div id="BillRuleTemp_CarType" class="v-null" style="min-width:210px;"></div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-xs2 edit-label ">停车区域</div>
                <div class="layui-col-xs3 edit-ipt-ban">
                    <div id="BillRuleTemp_ParkArea" class="v-null" style="min-width:210px;"></div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="edit-label  layui-col-xs2">规则描述</div>
                <div class="edit-ipt-ban layui-col-xs8">
                    <textarea placeholder="请填写规则描述" class="layui-textarea v-null" maxlength="500" id="ChargeRules_Remark" name="ChargeRules_Remark"></textarea>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row type">
                <div class="layui-col-xs2 edit-label ">规则类型</div>
                <div class="layui-col-xs3 edit-ipt-ban">
                    <select class="layui-select" lay-search id="ChargeRules_OverTime" name="ChargeRules_OverTime" lay-filter="ChargeRules_OverTime" data-key="ChargeRules_OverTime">
                        <option value="0">标准计费规则</option>
                        <option value="1">超时计费规则</option>
                        <option value="2">通用计费规则</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="help-btn-div"><i class="help-btn" data-key="ChargeRules_OverTime">?</i></div>
            </div>

        </div>
        <div class="layui-row bottom">
            <div class="layui-col-xs2 edit-label ">&nbsp;</div>
            <div class="layui-col-xs8 edit-ipt-ban btnAction" style="text-align:center;">
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-normal" id="Save"><i class="layui-icon layui-icon-ok"></i>保存</button>
                </div>&nbsp;&nbsp;
                <div class="layui-inline">
                    <button class="layui-btn layui-btn-danger " id="Cancel" style="background-color: #f0ad4e !important;"><i class="layui-icon layui-icon-close"></i>取消</button>
                </div>
            </div>
        </div>
    </div>

    <div class="cards"></div>

    <div class="tipRequest"></div>

    <script type="text/x-jquery-tmpl" id="tmplcards">
        <div style="height:auto;width:400px;">
            <div class="layui-card card">
                <div class="layui-card-header card-header">
                    ${ChargeRules_Name}
                </div>
                <div class="layui-card-body">
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">车牌类型</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-content"> ${ChargeRules_CarCardTypeName}</div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">车牌颜色</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-content">${ChargeRules_CarTypeName}</div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">停车区域</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-content">${ChargeRules_ParkAreaName}</div>
                        </div>
                        <div class="layui-col-xs4 edit-label right"></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">规则有效</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-content">
                                ${linkTime(ChargeRules_BeginTime,ChargeRules_EndTime)}
                                {{if getStatus(ChargeRules_BeginTime,ChargeRules_EndTime)==1}}
                                <span class="layui-badge layui-bg-green">未生效</span>
                                {{else getStatus(ChargeRules_BeginTime,ChargeRules_EndTime)==2}}
                                <span class="layui-badge layui-bg-blue">已生效</span>
                                {{else getStatus(ChargeRules_BeginTime,ChargeRules_EndTime)==3}}
                                <span class="layui-badge layui-bg-cyan">已过期</span>
                                {{/if}}
                            </div>
                        </div>
                        <div class="layui-col-xs4 edit-label right"></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">跨段拆分</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-content">
                                {{if ChargeRules_Across==1}}启用{{else}}禁用{{/if}}
                            </div>
                        </div>
                        <div class="layui-col-xs4 edit-label right"></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">超时收费</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-content">
                                {{if ChargeRules_OverTime==1}}   <span class="layui-badge layui-bg-green">是</span>{{else}}   <span class="layui-badge layui-bg-cyan">否</span>{{/if}}
                            </div>
                        </div>
                        <div class="layui-col-xs4 edit-label right"></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3 edit-label ">规则描述</div>
                        <div class="layui-col-xs9 edit-ipt-ban">
                            <div class="card-desc">${ChargeRules_Remark}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>


    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplarea">
        <option value="${ParkArea_No}" data-type="${ParkArea_Type}">${ParkArea_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplrule">
        <option value="${ChargeRules_No}" data-type="${ChargeRules_Across}" data-key="${ChargeRules_Type}">${ChargeRules_Name}</option>
    </script>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?2" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v=1.1" asp-append-version="true"></script>
    <script type="text/javascript">
        var paramChargeRulesNo = $.getUrlParam("ChargeRules_No");
        var layuiForm = null;
        var xmSelect = null;
        var laydate = null;

        var selParkArea = null;
        var selCarType = null;
        var selCarCardType = null;
        var index = parent.layer.getFrameIndex(window.name);

        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd 00:00:00"));
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['element', 'form', 'xmSelect', 'laydate'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            laydate = layui.laydate;

            layui.laydate.render({
                elem: '#ChargeRules_Time', range: true, done: function (value, date, endDate) {
                    pager.ChargeRules_BeginTime = new Date(date.year, date.month - 1, date.date).Format("yyyy-MM-dd");
                    pager.ChargeRules_EndTime = new Date(endDate.year, endDate.month - 1, endDate.date).Format("yyyy-MM-dd");
                }
            });
            pager.ChargeRules_BeginTime = now.Format("yyyy-MM-dd");
            pager.ChargeRules_EndTime = new Date(now.setFullYear(now.getFullYear() + 10)).Format("yyyy-MM-dd");
            $("#ChargeRules_Time").val(pager.ChargeRules_BeginTime + " - " + pager.ChargeRules_EndTime);

            _DATE.bind(laydate, ["Cycle_CustomStartTime"], { type: 'time', range: false, format: 'HH:mm' });

            pager.init();
        })

        var pager = {
            ChargeRulesData: null,
            ChargeRules_BeginTime: null,
            ChargeRules_EndTime: null,
            ChargeRules_No: null,
            parkAreas: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                $.post("GetRuleLst", {}, function (json) {
                    $("#ChargeRules_No").html('<option value="">请选择计费规则</option>');
                    if (json.success) {
                        if (json.data && json.data.length > 0) {
                            pager.ChargeRulesData = json.data;
                            $("#ChargeRules_No").append($("#tmplrule").tmpl(json.data));
                        }

                        layuiForm.render("select")

                        $("dd").hover(function (e) {
                            pager.showRule(this, e)
                        }, function () {
                            $(".cards").hide();
                        })


                        layuiForm.on("select", function (data) {
                            var val = data.value;
                            var id = data.elem.id;
                            var ChargeRules_Type = $(data.elem).find("option:selected").attr("data-key");
                            
                            if (id == "ChargeRules_No") {
                                if (ChargeRules_Type == 0) {
                                    $(".type").addClass("layui-hide");
                                     $("#ChargeRules_Remark").removeClass("v-null");
                                } else {
                                    $(".type").removeClass("layui-hide");
                                   
                                }
                            }
                        })
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");


                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.carCardTypes = json.data.carCardTypes;
                        pager.carTypes = json.data.carTypes;
                        pager.parkAreas = json.data.parkAreas;

                        if (pager.carTypes && pager.carTypes.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.carTypes.length; i++) {
                                data[i] = {
                                    "name": pager.carTypes[i].CarType_Name,
                                    "value": pager.carTypes[i].CarType_No
                                };
                            }
                            selCarType = xmSelect.render({
                                el: '#BillRuleTemp_CarType',
                                name: 'BillRuleTemp_CarType',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        if (pager.carCardTypes && pager.carCardTypes.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.carCardTypes.length; i++) {
                                data[i] = {
                                    "name": pager.carCardTypes[i].CarCardType_Name,
                                    "value": pager.carCardTypes[i].CarCardType_No
                                };
                            }
                            selCarCardType = xmSelect.render({
                                el: '#BillRuleTemp_CarCardType',
                                name: 'BillRuleTemp_CarCardType',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        if (pager.parkAreas && pager.parkAreas.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.parkAreas.length; i++) {
                                data[i] = {
                                    "name": pager.parkAreas[i].ParkArea_Name,
                                    "value": pager.parkAreas[i].ParkArea_No
                                };
                            }
                            selParkArea = xmSelect.render({
                                el: '#BillRuleTemp_ParkArea',
                                name: 'BillRuleTemp_ParkArea',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }

                        layuiForm.render("select")
                        $("dd").hover(function (e) {
                            pager.showRule(this, e)
                        }, function () {
                            $(".cards").hide();
                        })
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function () {

            },
            bindEvent: function () {

                //保存规则
                $("#Save").click(function () {
                    if ($(".rulesetting").is(":hidden")) { $(".ruletitle").click() }
                    if ($(".hoursetting").is(":hidden")) { $(".hourtitle").click() }
                    if ($(".basesetting").is(":hidden")) { $(".basetitle").click() }
                    $(".iconItem").each(function () {
                        var content = $(this).parent().parent().find(".content");
                        if ($(content).is(":hidden")) {
                            $(this).click();
                        }
                    })

                    var ChargeRules_CarCardTypeNo = selCarCardType.getValue('value');
                    var ChargeRules_CarTypeNo = selCarType.getValue('value');
                    var ChargeRules_ParkAreaNo = selParkArea.getValue('value');

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.ChargeRules_CarCardTypeNo = ChargeRules_CarCardTypeNo;
                        data.ChargeRules_CarTypeNo = ChargeRules_CarTypeNo;
                        data.ChargeRules_ParkAreaNo = ChargeRules_ParkAreaNo;
                        data.ChargeRules_BeginTime = pager.ChargeRules_BeginTime;
                        data.ChargeRules_EndTime = pager.ChargeRules_EndTime;
                        return data;
                    });

                    var ChargeRules_No = $("#ChargeRules_No").val();
                    if (!ChargeRules_No || ChargeRules_No == "") {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择要复制的计费规则", "#ChargeRules_No", { time: 2000 });
                        return;
                    }

                    var time = $("#ChargeRules_Time").val();
                    if (time == "") {
                        $("#ChargeRules_Time").focus();
                        layer.tips("请选择规则有效期", "#ChargeRules_Time", { time: 2000 });
                        return;
                    }

                    if (!ChargeRules_CarCardTypeNo || ChargeRules_CarCardTypeNo.length == 0) {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择车牌类型", "#BillRuleTemp_CarCardType", { time: 2000 });
                        return;
                    }
                    if (!ChargeRules_CarTypeNo || ChargeRules_CarTypeNo.length == 0) {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择车牌颜色", "#BillRuleTemp_CarType", { time: 2000 });
                        return;
                    }
                    if (!ChargeRules_ParkAreaNo || ChargeRules_ParkAreaNo.length == 0) {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择停车区域", "#BillRuleTemp_ParkArea", { time: 2000 });
                        return;
                    }
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    $("#Save").attr("disabled", true);

                    setTimeout(function () {
                        pager.showActionSpan();
                        $.post("CopyChargeRules", { jsonModel: JSON.stringify(param) }, function (json) {
                            $("#Save").removeAttr("disabled");
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                var retMsg = json.msg.replace(/#/g, "<br/>");

                                layer.msg(retMsg, { icon: 0, btn: ['确定'], time: 0 });
                            }
                        }, "json");
                    }, 200);

                });

                $("#Cancel").click(function () {
                    parent.layer.close(index);
                });

                bindTip();
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                    }
                });
            },
            //显示动画
            showActionSpan: function () {
                $(".mainbody>.light").remove();
                $(".mainbody").prepend(' <span class="light"></span>');
            },

            showRule: function (d, e) {
                var ChargeRules_No = $(d).attr("lay-value");
                if (ChargeRules_No && ChargeRules_No != "" && pager.ChargeRulesData != null) {
                    $.each(pager.ChargeRulesData, function (k, v) {
                        if (v.ChargeRules_No == ChargeRules_No) {
                            if (v.ChargeRules_CarCardTypeNo && v.ChargeRules_CarCardTypeNo != "") {
                                if (v.ChargeRules_CarCardTypeNo.indexOf('[') == -1) { v.ChargeRules_CarCardTypeNo = '["' + v.ChargeRules_CarCardTypeNo + '"]'; }
                            }
                            $(".cards").html($("#tmplcards").tmpl([v])).css("left", e.pageX + 20).css("top", e.pageY - 50);
                            $(".cards").show();
                        }
                    })
                }
            }
        }


        function getStatus(ChargeRules_BeginTime, ChargeRules_EndTime) {

            if (ChargeRules_BeginTime && ChargeRules_EndTime) {

                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd 00:00:00"));
                ChargeRules_EndTime = new Date(new Date(ChargeRules_EndTime).Format("yyyy-MM-dd 00:00:00"));

                if (ChargeRules_EndTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }

        function bindTip() {
            $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
                var key = $(this).attr("data-key");
                var data = getHelpContent(key);
                if (data) {
                    layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
                }
            }, function () {
                layer.closeAll();
            });

            $(".help-btn-tip").unbind("click").click(function () {
                var key = $(this).attr("data-key");
                var data = getHelpContent(key);
                var htm = "<div style='margin:10px;'>" + data.Description[0] + "</div>";
                if (data) {
                    layer.open({
                        type: 1,
                        title: "简要说明",
                        content: htm,
                        area: ["60%", "60%"],
                        shadeClose: true
                    });
                }
            });
        }

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return null;
            return data;
        }

        //提示信息数据
        var HelpData = [{
            key: "ChargeRules_OverTime",
            Description: ["标准计费规则：停车订单从未支付过，用此计费规则。<br/><br/>超时计费规则：停车订单支付过并且超过场内缴费限时出场时间，用此计费规则。<br/><br/>通用计费规则：停车订单无论支付或未支付过，都用此计费规则；"],
        }
        ];
    </script>

</body>
</html>
