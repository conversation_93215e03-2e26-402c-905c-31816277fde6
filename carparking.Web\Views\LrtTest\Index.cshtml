﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>车位引导API调试</title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <style>
        html, body { width: 100%; height: 100%; }
        .layui-card { width: 800px; min-height: 100%; margin: 0 auto 0; padding-right: 30px; background-color: #f9f9f9; }
        .layui-form-label { width: 120px; font-weight: bold; }
        .layui-input-block { margin-left: 150px; }
        .layui-input { color: #300bee; }
        .layui-input[readonly] { background-color:#f5f5f5;}
        .layui-btn[disabled] { background-color: #666 !important; }
    </style>
</head>
<body>
    <div class="layui-card">
        <div class="layui-card-header">API调试</div>
        <div class="layui-card-body layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">url</label>
                <div class="layui-input-block">
                    <input type="text" id="url" class="layui-input" value="http://localhost:7701/api/" readonly />
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">parkno</label>
                <div class="layui-input-block">
                    <input type="text" id="parkno" placeholder="请输入停车场编码" class="layui-input" value="20210602090822331">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">timestamp</label>
                <div class="layui-input-block">
                    <input type="text" id="timestamp" placeholder="请输入时间戳" class="layui-input" value="@Html.Raw(DateTime.Now.ToString("yyyyMMddHHmmssfff"))">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">api</label>
                <div class="layui-input-block">
                    <select name="city" lay-filter="api-action" lay-verify="required" lay-search>
                        <option value=""></option>
                        <optgroup label="停车管理">
                            <option value="ParkAPI/Login" selected>ParkAPI/Login -- 获取登录授权code</option>
                            <option value="ParkAPI/HeartBeat">ParkAPI/HeartBeat -- 心跳包数据</option>
                            <option value="ParkAPI/ResetHeartBeat">ParkAPI/ResetHeartBeat -- 长时间未接收到心跳则离线处理</option>
                            <option value="ParkAPI/CarInOut">ParkAPI/CarInOut -- 上传车辆出入场记录</option>
                        </optgroup>
                    </select>
                </div>
            </div>
            <div class="actionBox">
                <div class="layui-form-item">
                    <label class="layui-form-label">act</label>
                    <div class="layui-input-block">
                        <input type="text" name="act" placeholder="1-入库，0-出库" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">type</label>
                    <div class="layui-input-block">
                        <input type="text" name="type" placeholder="0-设备报警，1-锁车报警，2-乱停报警" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">carno</label>
                    <div class="layui-input-block">
                        <input type="text" name="carno" placeholder="请输入车牌号" class="layui-input" value="湘Z99999">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">spacename</label>
                    <div class="layui-input-block">
                        <input type="text" name="spacename" placeholder="请输入车位号" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">camerano</label>
                    <div class="layui-input-block">
                        <input type="text" name="camerano" placeholder="请输入相机编码" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">time</label>
                    <div class="layui-input-block">
                        <input type="text" name="time" placeholder="请输入出/入库时间" class="layui-input" value="@Html.Raw(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">&nbsp;</label>
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm" id="btnSendApi" onclick="sendApi()"><i class="layui-icon layui-icon-ok-circle"></i><text>提交</text></button>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">未加密参数</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:50px;" id="uparam"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">已加密参数</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:50px;" id="wparam"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">返回结果</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:200px;" id="result"></textarea>
                </div>
            </div>
        </div>
    </div>



    <script type="text/javascript">
        var apiAction = {
            onShow: function (action) {
                try {
                    var act = action.split('/');
                    var fileds = null;
                    fileds = apiAction[act[0]][act[1]];
                    //console.log(fileds)

                    $(".actionBox .layui-form-item").removeClass("layui-hide").addClass("layui-hide");
                    $(".actionBox .layui-form-item").each(function () {
                        var that = $(this);
                        var input = that.find('input');
                        var name = input.attr('name');
                        for (var i = 0; i < fileds.length; i++) {
                            if (name == fileds[i]) {
                                that.removeClass("layui-hide");
                                break;
                            }
                        }
                    });

                    $("textarea").val("");
                } catch (e) {
                    $(".actionBox .layui-form-item").removeClass("layui-hide").addClass("layui-hide");
                    //layer.msg(e)
                }

            },
            ParkAPI: {
                Login: ["carno", "act", "time", "spacename", "camerano"],
                HeartBeat: ["carno", "act", "time", "spacename", "camerano"],
                ResetHeartBeat: ["carno", "act", "time", "spacename", "camerano"],
                CarInOut: ["carno", "act", "time", "spacename", "camerano"],
            }
        }
    </script>
    <script type="text/javascript">
        $(".actionBox .layui-form-item").removeClass("layui-hide").addClass("layui-hide");
        var apiaction = $("select").find("option:selected").val(); //action默认值

        //Demo
        layui.use(['form', 'element', 'laydate'], function () {
            var form = layui.form, laydate = layui.laydate;

            //初始化action参数
            apiAction.onShow(apiaction);

            //切换action
            form.on('select(api-action)', function (data) {
                apiaction = data.value;
                apiAction.onShow(data.value);
            });

            //加载时间控件
            $("input").each(function () {
                var name = $(this).attr("name");
                if (name != null && name.indexOf("Time") > -1) {
                    $(this).attr("id", name);
                    laydate.render({ elem: "#" + name, type: 'datetime', trigger: "click", theme: "#0094ff" });

                    adddateicon($(this));
                }
            });

            $("#url").dblclick(function () { $(this).removeAttr("readonly"); });
            $("#url").blur(function () { $(this).attr("readonly", true); });
        });

        var sendApi = function () {
            submitBtn.disabled();
            //获取所有表单元素
            var param = $(".actionBox").formToJSON(true, function (data) {
                return data;
            });

            var postd = {};
            try {
                var act = apiaction.split('/');
                var fileds = null;
                fileds = apiAction[act[0]][act[1]];
                for (var i = 0; i < fileds.length; i++) {
                    postd[fileds[i]] = param[fileds[i]];
                }
            } catch (e) {
                postd = param;
            }

            var json = {
                version: $("#version").val(),
                key: $("#parkkey").val(),
                timestamp: $("#timestamp").val(),
                data: JSON.stringify(postd)
            };

            var url = $("#url").val();
            var jsonData = JSON.stringify(json);
            $.post("/ApiDemo/sendApi", { posturl: url + apiaction, jsonData: jsonData }, function (json) {
                //layer.msg(json.msg);
                submitBtn.enable();

                $("#uparam").val(jsonData);
                $("#wparam").val(JSON.stringify(json.post));
                $("#result").val(json.result);
            }, "json")
        };

        var submitBtn = {
            enable: function () { $("#btnSendApi").removeAttr("disabled"); },
            disabled: function () { $("#btnSendApi").attr("disabled", true); }
        }

        //给时间控件添加图标
        var adddateicon = function (dateinput) {
            $(dateinput).css({ "padding-right": "40px" });
            var iconhtm = '<icon class="layui-icon layui-icon-date" style="position: absolute;right: 8px;top: 8px;font-size: 1.5rem;color: #0094ff;"></icon>';
            $(dateinput).parent().append(iconhtm);
        }
    </script>
</body>
</html>