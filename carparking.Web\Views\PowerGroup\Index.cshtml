﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>权限组管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?1" rel="stylesheet" />
    <style>
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>权限管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="PowerGroup_Name" id="PowerGroup_Name" autocomplete="off" placeholder="权限组名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="启用状态" class="form-control chosen-select" id="PowerGroup_Enable" name="PowerGroup_Enable" lay-search>
                                    <option value="">启用状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.PowerGroup.Add){}} <button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa  fa-plus"></i><t></t><t>新增</t></button>{{# } }}
                                {{# if(Power.PowerGroup.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.PowerGroup.Enable){}} <button class="layui-btn layui-btn-sm" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>启用</t></button>{{# } }}
                                {{# if(Power.PowerGroup.Disable){}}<button class="layui-btn layui-btn-sm" lay-event="Disable"><i class="fa fa-ban"></i><t>禁用</t></button>{{# } }}
                                {{# if(Power.PowerGroup.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}

                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TmplEnable">
        {{#  if(d.PowerGroup_Enable==1){ }}
        <span class="layui-badge layui-bg-blue ">启用</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-orange ">禁用</span>
        {{#  } }}
    </script>

    <script type="text/x-jquery-tmpl" id="TmplCommunityNo">
        {{(d.PowerGroup_CommunityNo=='0'||d.PowerGroup_CommunityNo==null)?"":d.PowerGroup_CommunityNo}}
    </script>
    <script type="text/x-jquery-tmpl" id="TmplCompanyNo">
        {{(d.PowerGroup_CompanyNo=='0'||d.PowerGroup_CompanyNo==null)?"":d.PowerGroup_CompanyNo}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        //页面初始化所执行的脚本
        $(function () {
            $("#BtnSearch").click(function () { pager.bindData(1); });
        });
    </script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'PowerGroup_No', title: '权限组编码', hide: true }
                , { field: 'PowerGroup_Name', title: '权限组名称' }
                , { field: 'PowerGroup_Enable', title: '状态', toolbar: '#TmplEnable' }
                , { field: 'PowerGroup_AddName', title: '操作员' }
                , { field: 'PowerGroup_AddTime', title: '添加时间' }
                , { field: 'PowerGroup_Remark', title: '备注', hide: true }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/PowerGroup/PowerGroupList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) { 
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        pager.Action.Add();
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        pager.Action.Update(data[0].PowerGroup_ID);
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        $(this).attr("disabled", true);
                        pager.Action.Enable(data[0].PowerGroup_ID, this);
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        $(this).attr("disabled", true);
                        pager.Action.Disable(data[0].PowerGroup_ID, this);

                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        $(this).attr("disabled", true);
                        pager.Action.Delete(data[0].PowerGroup_ID, this);
                        break;
                };
            });

            tb_row_radio(table);
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                this.bindSelect();
            },
            //重新加载数据
            bindData: function (index) {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/PowerGroup/PowerGroupList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
                layer.closeAll();
            },
            bindSelect: function () {
               
            },
            //新增更新启用禁用删除操作
            Action: {
                //新增
                Add: function () {
                    layer.open({
                        type: 2, id: 1,
                        title: "新增",
                        content: '/PowerGroup/Edit?Act=Add',
                        area: getIframeArea(['80%', '100%']),
                        maxmin: true
                    });
                },
                //更新
                Update: function (PowerGroup_ID) {
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑",
                        content: '/PowerGroup/Edit?Act=Update&PowerGroup_ID=' + PowerGroup_ID,
                        area: getIframeArea(['80%', '100%']),
                        maxmin: true
                    });
                },
                //启用
                Enable: function (PowerGroup_ID, obj) {
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $.getJSON("/PowerGroup/EnablePowerGroup", { PowerGroup_ID: PowerGroup_ID }, function (json) {
                        if (json.Success) {
                            layer.msg("启用成功", { icon: 1, time: 1500 }, function () {
                                $(obj).removeAttr("disabled");
                                pager.bindData(pager.pageIndex);
                            });
                        } else
                            layer.msg(json.Message, { icon: 0, time: 1500 }, function () { $(obj).removeAttr("disabled"); });
                    });
                },
                //禁用
                Disable: function (PowerGroup_ID, obj) {
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $.getJSON("/PowerGroup/DisablePowerGroup", { PowerGroup_ID: PowerGroup_ID }, function (json) {
                        if (json.Success) {
                            layer.msg("禁用成功", { icon: 1, time: 1500 }, function () {
                                $(obj).removeAttr("disabled");
                                window.parent.global.updatePower();
                                pager.bindData(pager.pageIndex);
                            });
                        }
                        else
                            layer.msg(json.Message, { icon: 0, time: 1500 }, function () { $(obj).removeAttr("disabled"); });
                    });
                },
                //删除
                Delete: function (PowerGroup_ID, obj) {
                    layer.msg("处理中", { icon: 16, time: 0 });
                    layer.confirm("确定删除?", { title: "消息提示", icon: 3 }, function (e) {
                        $.getJSON("/PowerGroup/DeletePowerGroup", { PowerGroup_ID: PowerGroup_ID }, function (json) {
                            if (json.Success) {
                                layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                    $(obj).removeAttr("disabled");
                                    pager.bindData(pager.pageIndex);
                                });
                            }
                            else
                                layer.msg(json.Message, { icon: 0, time: 1500 }, function () { $(obj).removeAttr("disabled"); });
                        });
                    }, function () {
                        $(obj).removeAttr("disabled");
                    });
                }
            }
        }

        $(function () { pager.init(); });
    </script>
</body>
</html>
