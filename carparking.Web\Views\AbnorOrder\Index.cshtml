﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>人工开闸记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>异常放行记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <select class="layui-input" lay-search name="PassRecord_PasswayNo" id="PassRecord_PasswayNo" placeholder="车道名称">
                                    <option value="">车道名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PassRecord_PassTime1" id="PassRecord_PassTime1" autocomplete="off" placeholder="通行时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")'/>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PassRecord_PassTime2" id="PassRecord_PassTime2" autocomplete="off" placeholder="通行时间止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="AbnorOrder_CardNo" name="AbnorOrder_CardNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PassRecord_Name" id="PassRecord_Name" autocomplete="off" placeholder="操作员" />
                            </div>

                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">

                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        topBar.init();

        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { field: 'PassRecord_No', title: '记录编号', hide: true }
                , { field: 'PassRecord_CarNo', title: '车牌号' }
                , { field: 'PassRecord_CarCardTypeName', title: '车牌类型', templet: function (d) { return (d.PassRecord_CarNo != null && d.PassRecord_CarNo != '') ? d.PassRecord_CarCardTypeName : ""; } }
                , { field: 'PassRecord_PasswayNo', title: '车道编码', hide: true }
                , { field: 'PassRecord_PasswayName', title: '车道名称' }
                , { field: 'PassRecord_DeviceNo', title: '相机编码', hide: true }
                , { field: 'PassRecord_DeviceName', title: '相机名称', hide: true }
                , { field: 'PassRecord_ImgPath', title: '抓拍图', templet: function (d) { return tempImg(d.PassRecord_ImgPath); } }
                , {
                    field: 'PassRecord_Type', title: '类型', templet: function (d) {
                        if (d.PassRecord_Type == 1) return tempBar(1, "人工开闸");
                        else if (d.PassRecord_Type == 2) return tempBar(2, "特殊车辆放行");
                        else if (d.PassRecord_Type == 3) return tempBar(3, "人工关闸");
                        else if (d.PassRecord_Type == 4) return tempBar(0, "道闸常开");
                        else if (d.PassRecord_Type == 0) return tempBar(4, "无入场记录放行");
                        else return "";
                    }
                }
                , { field: 'PassRecord_PassTime', title: '通行时间' }
                , { field: 'PassRecord_Account', title: '操作员账号', hide: true }
                , { field: 'PassRecord_Name', title: '操作员' }
                , { field: 'PassRecord_Remark', title: '备注' }
                , { field: 'PassRecord_AddTime', title: '创建时间', hide: true  }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/AbnorOrder/GetAbnorOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                //, totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {

                };
            });

            tb_row_checkbox()
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PassRecord_PassTime1", "PassRecord_PassTime2"], { type: 'datetime', range: true });
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#AbnorOrder_CardNo").append(option)
                        });
                    }
                }, "json");
                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                            $("#PassRecord_PasswayNo").append(option);
                            layui.form.render('select');
                        })
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/AbnorOrder/GetAbnorOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
    </script>
</body>
</html>
