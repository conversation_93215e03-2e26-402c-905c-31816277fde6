﻿using Aliyun.OSS;
using carparking.BLL.Cache;
using carparking.Charge;
using carparking.CloudLink.Entitys;
using carparking.Common;
using carparking.Config;
using carparking.Model;
using carparking.SentryBox;
using carparking.SentryBox.Device;
using carparking.SentryBox.Util;
using DotNetty.Transport.Channels;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.Util;
using ParkPoroxyStandardLib;
using RestSharp;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using TcpConnPools.Camera;
using static Aliyun.OSS.Model.SelectObjectRequestModel;
using System.Security.Cryptography;
using NPOI.HPSF;
using System.Web;
using NPOI.HSSF.Record.Chart;
using FastDeepCloner;
using static carparking.CloudLink.UploadProcessing.UploadOutCar;
using carparking.Charge.Models;
using carparking.Model.API;

namespace carparking.CloudLink
{
    /// <summary>
    /// 平台设置参数配置下发指令处理类
    /// </summary>
    public class PushOfflineMiddlwareSystemConfigHandle : TcpHandleBase
    {
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PushOfflineMiddlwareSystemConfig";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                CloudTask.GetCloudParkConfig = jo.ToObject<CloudParkConfig>();
                CloudTask.SetCloudParms(CloudTask.GetCloudParkConfig);
                Common.DateTimeHelper.SetLocalDateTime(CloudTask.GetCloudParkConfig?.dateTime);

                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"获取平台参数成功！");
                IsSuccess = true;
                sRemark = "配置信息接收成功！";


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));

                //Common.LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,"平台下发参数更改，1秒钟后重启中间件服务...");
                //Thread.Sleep(1000);
                ////下发平台参数，需要重启中间件，这里只需要停止服务就行，由看门狗程序启动服务
                //Common.WindowsServiceUitl.StopService("carparking_cloudclient");
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 桌面远程操作指令处理类
    /// </summary>
    public class RemoteDesktopHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "StartDesktop";

        public static bool IsLoading = false;

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    if (IsLoading)
                    {
                        SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { actionName = InstructionName, result = 0, msg = "远程桌面正在加载中，请稍后" }));
                        return Task.CompletedTask;
                    }
                    IsLoading = true;
                    try
                    {
                        bool IsSuccess = false;
                        string sRemark = string.Empty;
                        string sl_app_id = Convert.ToString(jo["sl_app_id"]);
                        string sl_app_key = Convert.ToString(jo["sl_app_key"]);
                        string sl_app_domain = "sl-api.oray.com";
                        if (jo.ContainsKey("sl_app_domain"))
                        {
                            var s1 = Convert.ToString(jo["sl_app_domain"]);
                            if (!string.IsNullOrWhiteSpace(s1))
                            {
                                sl_app_domain = s1;
                            }
                        }

                        dynamic dyn = new ExpandoObject();
                        int i = 0;
                    al:
                        if (RemoteDesktopClient.m_desktopSession == Slapi.SLSESSION_INVAILD)
                        {
                            if (RemoteDesktopClient.Login(sl_app_domain, Appid: sl_app_id, AppKey: sl_app_key))
                            {
                                int count = 0;
                                while (count < 10)
                                {
                                    if (!string.IsNullOrWhiteSpace(ClientCache.RemoteDesktopAddress))
                                    {
                                        ClientCache.RemoteDesktopSessionID = RemoteDesktopClient.Create();
                                        break;
                                    }

                                    count++;
                                    Thread.Sleep(300);
                                }

                                if (!string.IsNullOrWhiteSpace(ClientCache.RemoteDesktopSessionID) && !string.IsNullOrWhiteSpace(ClientCache.RemoteDesktopAddress))
                                {
                                    dyn.version = AppCache.GetParking.AppVersion;
                                    dyn.key = AppCache.GetParking.Parking_Key;
                                    dyn.SL_Client_Address = ClientCache.RemoteDesktopAddress;
                                    dyn.SL_Client_SessionID = ClientCache.RemoteDesktopSessionID;

                                    _ = Task.Run(() => UploadRemoteDesktopPara(ClientCache.RemoteDesktopAddress, ClientCache.RemoteDesktopSessionID));

                                    IsSuccess = true;
                                    sRemark = "桌面远程参数获取成功！";
                                }
                                else
                                {
                                    IsSuccess = false;
                                    sRemark = "桌面远程登录失败！";
                                }
                            }
                            else
                            {
                                sRemark = "远程桌面登录失败";
                            }
                        }
                        else
                        {
                            RemoteDesktopClient.Logout();
                            RemoteDesktopClient.Destroy();
                            if (i++ <= 0)
                            {
                                goto al;
                            }
                            else
                            {
                                sRemark = "远程桌面启用失败";
                            }
                        }

                        SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { actionName = InstructionName, result = IsSuccess ? 1 : 0, msg = sRemark, data = dyn }));
                    }
                    finally
                    {
                        IsLoading = false;
                    }
                }
                else
                {
                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { actionName = InstructionName, result = 0, msg = "不支持远程桌面连接" }));
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{InstructionName}异常信息," + ex.ToString());
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }


        /// <summary>
        /// 上传远程桌面参数到云托管
        /// </summary>
        /// <param name="SL_Client_Address">远程桌面地址</param>
        /// <param name="SL_Client_SessionID">远程桌面授权标识</param>
        /// <returns></returns>
        public bool UploadRemoteDesktopPara(string SL_Client_Address, string SL_Client_SessionID)
        {
            dynamic dnc = new ExpandoObject();
            dnc.version = AppCache.GetParking.AppVersion;
            dnc.key = AppCache.GetParking.Parking_Key;
            dnc.SL_Client_Address = SL_Client_Address;
            dnc.SL_Client_SessionID = SL_Client_SessionID;
            var reluststr = Common.HttpPost.PostAuthorizationData(AppCache.GetSysConfigContent.ParkServiceUrl + "/api/ParkingServer/SendRemoteDesktopParam",
                JsonConvert.SerializeObject(dnc),
                AppCache.GetParking.Parking_Key,
                AppCache.GetParking.AppVersion,
                true, AppCache.GetSysConfigContent.SysConfig_AESKey, AppCache.GetSysConfigContent.SysConfig_AESIV);
            relustModel relustmode = JsonConvert.DeserializeObject<relustModel>(reluststr.Data);
            if (relustmode.resultcode == 1) //上传成功
            {
                return true;
            }

            return false;
        }
    }

    /// <summary>
    /// 远程升级操作
    /// </summary>
    public class UpgradeParkingHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "UpgradeParking";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    var tempUpdateModel = Common.TyziTools.Json.ToObject<List<Model.UpdateModel>>(Convert.ToString(jo["upgradedata"]));
                    float.TryParse(AppCache.GetParking.AppVersion, out float fl1);
                    var GetUpdateModel = tempUpdateModel.Where(m => float.TryParse(m.PlugIn_Version, out float fl2) && fl2 > fl1).OrderBy(m => m.PlugIn_Version).ToList();
                    if (GetUpdateModel != null && GetUpdateModel.Count > 0)
                    {
                        sRemark = "升级指令接收处理成功！";
                        IsSuccess = true;

                        Task.Factory.StartNew(delegate
                        {
                            try
                            {
                                string _Directorys = Environment.CurrentDirectory + $"\\..\\Local\\upgrade\\DownUpdates";
                                if (!Directory.Exists(_Directorys))
                                {
                                    Directory.CreateDirectory(_Directorys);
                                }
                                else
                                {
                                    Common.FileUtil.ClearDirectory(_Directorys);
                                }

                                int iIndex = 0;
                                GetUpdateModel.ForEach(m =>
                                {
                                    string sIndexName = $"{_Directorys}\\{m.PlugIn_No}.zip";
                                    Common.FileUtil.DowloadFile(m.PlugIn_Path, sIndexName, d =>
                                    {
                                        if (d > 0.9)
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"安装包总数：{GetUpdateModel.Count}个，正在下载第{iIndex + 1}个安装包：{d:0.00}%");
                                        }
                                    });
                                    iIndex++;
                                    m.SavePath = sIndexName;
                                });


                                if (GetUpdateModel.Exists(m => m.PlugIn_Sign != Common.TyziTools.MyDES.HashFile(m.SavePath)))
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"校正下载的升级文件不成功，升级处理终止！");
                                }
                                else
                                {
                                    string content = Common.StringGzip.GZipCompressString(Common.TyziTools.Json.ToString(GetUpdateModel));
                                    Console.WriteLine(content);

                                    GetUpdateModel.ForEach(temp1 =>
                                    {
                                        Model.UpgradeRecord record = new Model.UpgradeRecord
                                        {
                                            UpgradeRecord_Ext = JsonConvert.SerializeObject(temp1),
                                            UpgradeRecord_Remarks = temp1.PlugIn_Detail,
                                            UpgradeRecord_UPath = temp1.SavePath,
                                            UpgradeRecord_MarkIndex = Convert.ToInt32(temp1.PlugIn_ID),
                                            UpgradeRecord_Mode = Convert.ToInt32(temp1.PlugIn_Category),
                                            UpgradeRecord_Sha1 = temp1.PlugIn_Sign,
                                            UpgradeRecord_Status = 0,
                                            UpgradeRecord_Url = temp1.PlugIn_Path,
                                            UpgradeRecord_Time = DateTimeHelper.GetNowTime(),
                                            UpgradeRecord_Version = $"{temp1.PlugIn_Version:0.0000}"
                                        };
                                        int iexc = BLL.Record.UpgradeRecord.Add(record);
                                        temp1.SaveRecordId = Convert.ToString(iexc);
                                    });

                                    ProcessStartInfo startInfo = new ProcessStartInfo();
                                    startInfo.FileName = $"{Environment.CurrentDirectory}\\..\\升级维护工具.exe";
                                    startInfo.UseShellExecute = true;
                                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"正在启动升级应用程序：{startInfo.FileName}");
                                    // 使用管理员方式打开
                                    startInfo.Verb = "runas";
                                    startInfo.Arguments = content;
                                    Process proc = Process.Start(startInfo);
                                }
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, ex.ToString());
                            }
                        });
                    }
                    else
                    {
                        sRemark = "升级指令接收处理不成功！";
                    }
                }
                else
                {
                    sRemark = "非Windows平台不能执行升级操作！";
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { actionName = InstructionName, result = IsSuccess ? 1 : 0, msg = sRemark }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{InstructionName}异常信息," + ex.ToString());
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 获取车辆收费价格指令处理类
    /// </summary>
    public class OrderPriceHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OrderPrice,OrderFreeMinPrice";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            //多指令先获取指令接收的指令名称
            string sActionName = jo.ContainsKey("actionName") ? Convert.ToString(jo["actionName"]) : InstructionName;

            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                //判断是否带有平台优惠时间获取价格接口
                int iFreeMinutes = 0; //平台优惠免费时间
                string allTimeFree = ""; //非时段全免类型，该值为null

                //string sMedicalModelTradeNo = Convert.ToString(jo["tradeNo"]);//医疗扫码模式下的二维码标识（当前没有用处）

                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单
                if (sActionName == "OrderFreeMinPrice")
                {
                    iFreeMinutes = Convert.ToInt32(jo["minutes"]); //如果平台下发了免费分钟，计算的进场时间向前推
                }

                if (jo.ContainsKey("allTimeFree"))
                {
                    allTimeFree = Convert.ToString(jo["allTimeFree"]);
                }

                //bool isOutGateOrder = !string.IsNullOrEmpty(DataCache.AskPrice.Get("OrderPrice:" + orderNo)); //是否出口车道订单

                if (string.IsNullOrEmpty(orderNo))
                {
                    SendTCPDatas(context, sendsession, IsSuccess, "停车订单编号不能为空", sActionName);
                    return Task.CompletedTask;
                }

                bool shouldPayOrder = true;
                int PayTimeoutSeconds = AppBasicCache.CurrentSysConfigContent.SysConfig_LockOrderMin * 60;
                if (PayTimeoutSeconds > 0)
                {
                    var currentOrder = orderNo;
                    if (Utils.PairsLockOrder.TryGetValue(currentOrder, out var lockStatus))
                    {
                        // 优化条件判断顺序
                        if (lockStatus.Item1 == 1)
                        {
                            shouldPayOrder = false;
                            var elapsedSeconds = DateTime.Now.Subtract(lockStatus.Item2).TotalSeconds;
                            if (elapsedSeconds > PayTimeoutSeconds)
                            {
                                shouldPayOrder = true;//超出设置时间，可进行无感
                            }
                            LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"查费逻辑，{currentOrder}锁单状态：{shouldPayOrder.ToString()}.");
                        }
                    }
                    else
                    {
                        Utils.PairsLockOrder.TryAdd(currentOrder, new Tuple<int, DateTime>(2, DateTime.Now));
                        LogManagementMap.WriteToFile(LoggerEnum.MainWarnLog, $"查费逻辑，{currentOrder}添加锁单.");
                    }
                }

                if (!shouldPayOrder)
                {
                    SendTCPDatas(context, sendsession, IsSuccess, "订单锁单中,请稍后重试...", sActionName);
                    return Task.CompletedTask;
                }

                //查询订单
                ResultPass passResule = null;
                Model.ParkOrder po = BLL.ParkOrder.GetEntity(orderNo);
                if (po == null)
                {
                    passResule = BLL.ConfirmRelease.Results.FirstOrDefault(item => item.Value?.resorder?.resOut?.noRecordOrder?.ParkOrder_No == orderNo).Value;
                    if (passResule != null)
                    {
                        po = passResule?.resorder?.resOut?.parkorder ?? passResule?.resorder?.resOut?.noRecordOrder;
                    }

                    if (po == null)
                    {
                        SendTCPDatas(context, sendsession, false, "未找到停车订单信息", sActionName);
                        return Task.CompletedTask;
                    }
                }

                if (passResule != null && passResule.resorder?.resOut?.noRecordOrder != null)
                {
                    //计费结果重组
                    carPrice price1 = new carPrice();
                    price1.enter_Time = po.ParkOrder_EnterTime != null ? po.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "";
                    price1.orderNo = orderNo;
                    price1.parking_Time = po.ParkOrder_EnterTime != null ? Utils.ConvertDateTimeInt(po.ParkOrder_EnterTime.Value).ToString() : "0";
                    price1.total_value = passResule.payres.payedamount.ToString("0.00"); //支付金额
                    price1.couponamount = passResule.payres.couponamount.ToString("0.00");//优惠金额
                    price1.useMinute = passResule.payres.uselist?.Sum(x => x.CouponRecord_DiscountMin)?.ToString();//优惠分钟

                    var isHaveOnlineCoupon = passResule.payres.uselist?.Find(x => x.CouponRecord_OnLine == 1) != null;
                    if (isHaveOnlineCoupon)
                        price1.couponKey = "1";
                    else
                    {
                        if (iFreeMinutes != 0 || (!string.IsNullOrEmpty(allTimeFree) && allTimeFree == "[]" && allTimeFree == "{}"))
                        {
                            var payres = Calc.GetChargeByMin(po, DateTime.Now, iFreeMinutes, null, out var orderAllCouponList, true, "", "", true,
                                false, detailList: new List<OrderDetail>() { passResule.resorder.resOut.noRecordDetail }, allTimeFree: allTimeFree);
                            if (payres?.payed != 2)
                            {
                                price1.total_value = payres.payedamount.ToString("0.00"); //支付金额
                                price1.couponamount = payres.couponamount.ToString("0.00");//优惠金额
                                price1.useMinute = payres.uselist?.Sum(x => x.CouponRecord_DiscountMin)?.ToString();//优惠分钟
                            }
                        }
                    }

                    SendTCPDatas(context, sendsession, true, "计费成功", sActionName, price1, false);
                    return Task.CompletedTask;

                }

                var outpasswayNo = "";
                bool isOutGateOrder = false; //!string.IsNullOrEmpty(DataCache.AskPrice.Get("OrderPrice:" + orderNo)); //是否出口车道订单
                //根据订单号查弹窗信息
                var ComfirmOrder = BLL.ConfirmRelease.GetComfirmOrderByOrderNo(orderNo);
                if (ComfirmOrder != null) { isOutGateOrder = true; }


                //追缴订单计费：以订单出场时间计算
                bool isFllowCar = false;
                if (po.ParkOrder_StatusNo == EnumParkOrderStatus.Follow) { isFllowCar = true; outpasswayNo = po.ParkOrder_OutPasswayNo; }

                if (isFllowCar)
                {
                    Model.ControlEvent controlEvent = BLL.ControlEvent.GetEntityByOrderNo(orderNo);
                    if (controlEvent != null)
                    {
                        //计费结果重组
                        carPrice price1 = new carPrice();
                        price1.enter_Time = po.ParkOrder_EnterTime != null ? po.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "";
                        price1.orderNo = orderNo;
                        price1.parking_Time = po.ParkOrder_EnterTime != null ? Utils.ConvertDateTimeInt(po.ParkOrder_EnterTime.Value).ToString() : "0";
                        price1.total_value = controlEvent.ControlEvent_Money.Value.ToString("0.00"); //金额

                        if (controlEvent.ControlEvent_Status == 4)
                        {
                            price1.total_value = "0.00";
                            SendTCPDatas(context, sendsession, true, "线下已缴费", sActionName, price1, false);
                            return Task.CompletedTask;
                        }

                        if (controlEvent.ControlEvent_Status != 0 && controlEvent.ControlEvent_Status != 1 && controlEvent.ControlEvent_Money > 0)
                        {
                            SendTCPDatas(context, sendsession, true, "计费成功", sActionName, price1, false);
                            return Task.CompletedTask;
                        }
                    }
                }


                DateTime OutTime = DateTimeHelper.GetNowTime();
                if (jo.ContainsKey("outTime"))
                {
                    OutTime = DateTime.Parse(jo["outTime"].ToString());
                    isFllowCar = true;
                }
                else if (isFllowCar)
                {
                    OutTime = po.ParkOrder_OutTime ?? OutTime;
                }
                else if (isOutGateOrder)
                {
                    OutTime = ComfirmOrder.time != null ? ComfirmOrder.time.Value : DateTimeHelper.GetNowTime();
                }

                Model.Car car = BLL.Car.GetEntityByCarNo(po.ParkOrder_CarNo);

                //获取计费结果
                List<Model.CouponRecordIntExt> orderCouponList = null;

                ChargeModels.PayResult result = null;
                if (po.ParkOrder_IsNoInRecord == 1)
                {
                    result = PassTool.PassHelperBiz.FeeNoRecord2(po.ParkOrder_ParkNo, carno: po.ParkOrder_CarNo, time: OutTime, passwayno: po.ParkOrder_OutPasswayNo,
                        orderAllCouponList: out orderCouponList, car: car, result: null, freeMinutes: iFreeMinutes, isOutGateOrder: isOutGateOrder, isFllowCar: isFllowCar,
                        allTimeFree: allTimeFree);
                }
                else
                {
                    var orderprice = new Model.OrderPrice()
                    {
                        OrderPrice_No = Utils.CreateNumber_SnowFlake,
                        OrderPrice_CarNo = po.ParkOrder_CarNo,
                        OrderPrice_ParkOrderNo = po.ParkOrder_No,
                        OrderPrice_CouponMin = iFreeMinutes,
                        OrderPrice_AllTimeFree = allTimeFree,
                        OrderPrice_CalcTime = OutTime,
                        OrderPrice_OutPasswayNo = ComfirmOrder?.passres?.passway?.Passway_No,
                        OrderPrice_FllowCar = isFllowCar ? 1 : 0,
                    };

                    result = Calc.GetChargeByMin(po, OutTime, iFreeMinutes, car, out orderCouponList, true, "", "", isOutGateOrder, isFllowCar, allTimeFree: allTimeFree);

                    orderprice.OrderPrice_PayedMoney = result.payedamount;
                    orderprice.OrderPrice_CouponMoney = result.couponamount;
                    orderprice.OrderPrice_UseMinute = result.uselist?.Sum(x => x.CouponRecord_DiscountMin) ?? 0;

                    var orderresult = new Model.OrderResult()
                    {
                        OrderResult_No = orderprice.OrderPrice_No,
                        OrderResult_PayData = HttpUtility.UrlEncode(TyziTools.Json.ToString(result))
                    };
                    BLL.BaseBLL._Insert(orderprice, orderresult);
                }

                if (result != null)
                {
                    if (result.payed == 2)
                    {
                        SendTCPDatas(context, sendsession, IsSuccess, result.payedmsg == null ? "计费失败" : result.payedmsg, sActionName);
                        return Task.CompletedTask;
                    }

                    if (result.payed == 0 && result.orderamount == 0)
                    {
                        CalcCache.Del((po?.ParkOrder_IsUnlicensedCar == 1 ? "OrderPrice2:" : "OrderPrice1:") + orderNo);
                        CalcCache.Del("OrderPrice:" + orderNo);
                    }
                    else
                    {
                        if (result.list != null && result.list.Count > 0) //无入场记录不写入缓存，因创建支付订单时无法判断
                        {
                            CalcCache.Set((po?.ParkOrder_IsUnlicensedCar == 1 ? "OrderPrice2:" : "OrderPrice1:") + orderNo, result);
                            CalcCache.Set("OrderPrice:" + orderNo, result);
                        }
                    }
                }

                if (orderCouponList != null && orderCouponList.Count > 0)
                {
                    List<Model.CouponRecord> crList = TyziTools.Json.ToModel<List<Model.CouponRecord>>(TyziTools.Json.ToString(orderCouponList));
                    if (BLL.CouponRecord._AddOrUpdateModel<Model.CouponRecord>(crList) >= 0)
                    {
                        var r = BLL.MiddlewareApi.UpdateCoupon(crList);
                        if (!r.success)
                        {
                            LogManagementMap.WriteToFileException(null, $"{InstructionName}更新优惠券信息分发失败{InstructionName}：{r.errmsg}，{orderNo}");
                        }
                    }
                }

                //计费结果重组
                carPrice price = new carPrice();
                price.enter_Time = po.ParkOrder_EnterTime != null ? po.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "";
                price.orderNo = orderNo;
                price.parking_Time = po.ParkOrder_EnterTime != null ? Utils.ConvertDateTimeInt(po.ParkOrder_EnterTime.Value).ToString() : "0";
                price.total_value = result.payedamount.ToString("0.00"); //支付金额
                price.couponamount = result.couponamount.ToString("0.00");//优惠金额
                price.useMinute = result.uselist?.Sum(x => x.CouponRecord_DiscountMin)?.ToString();//优惠分钟

                //车主扫场内码，如果线下软件使用了平台优惠券，则线上支付不可以再用其它卷
                if (result?.couponamount > 0 || result?.uselist?.Count > 0)
                {
                    if (isOutGateOrder)
                    {
                        var isHaveOnlineCoupon = result?.uselist?.Find(x => x.CouponRecord_OnLine == 1 || x.CouponRecord_CouponCode == "105") != null;
                        if (isHaveOnlineCoupon) price.couponKey = "1";
                    }
                    else
                    {
                        var isHaveOnlineCoupon = result?.uselist?.Find(x => x.CouponRecord_CouponCode == "105") != null;
                        if (isHaveOnlineCoupon) price.couponKey = "1";
                    }
                }

                SendTCPDatas(context, sendsession, true, "计费成功", sActionName, price, false);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 车辆收费价格附加数据
        /// </summary>
        public class carPrice
        {
            /// <summary>
            /// 订单号码
            /// </summary>
            public string orderNo { get; set; }

            /// <summary>
            /// 入场时间
            /// </summary>
            public string enter_Time { get; set; }

            /// <summary>
            /// 停车时间 秒
            /// </summary>
            public string parking_Time { get; set; }

            /// <summary>
            /// 停车总费用分
            /// </summary>
            public string total_value { get; set; }

            /// <summary>
            /// 当前计费使用的优惠券key，多个用逗号隔开
            /// </summary>
            public string couponKey { get; set; }
            /// <summary>
            /// 优惠金额
            /// </summary>
            public string couponamount { get; set; }

            /// <summary>
            /// 优惠券累计抵扣的分钟
            /// </summary>
            public string useMinute { get; set; }
        }
    }

    /// <summary>
    /// 执行开闸操作指令处理类
    /// </summary>
    public class OpenGateHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OpenGate";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                string type = jo.ContainsKey("type") ? Convert.ToString(jo["type"]) : "1"; //1-平台车道人工开闸 !1-表示其他方式开闸

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    #region 发送开闸操作

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "云平台-远程开闸",
                        BaseData = null,
                        SendOptions = Model.API.SendOptionsType.OpenGate
                    };

                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);

                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;

                    if (retSuccess.Contains("true")) //开闸成功
                    {
                        IsSuccess = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "平台人工开闸成功-" + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "平台人工开闸不成功-" + sRemark);
                    }

                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 车辆预入场执行开闸操作指令处理类
    /// </summary>
    /// <summary>
    public class OpenInGateHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OpenInGate";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                string orderNo = Convert.ToString(jo["parkOrderNo"]);
                string carNo = Convert.ToString(jo["carNo"]);
                string imgUrl = Convert.ToString(jo["imgUrl"]);

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    #region 发送预入场开闸操作

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "预入场开闸",
                        BaseData = null,
                        sCarplate = carNo,
                        sOrderNo = orderNo,
                        SendOptions = Model.API.SendOptionsType.OpenInGate,
                        ImgUrl = imgUrl
                    };

                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    if (retSuccess.ToLower().Contains("true")) //预入场开闸成功
                    {
                        GetMemoryCache.Set($"Mid_{Entitys.CacheType.EstCarIn}=>{orderNo}", new Tuple<string, string, string>(model.Passway_SentryHostNo, passNo, orderNo), cacheEntityOps);
                        IsSuccess = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "车辆预入场成功-" + sRemark);
                    }
                    else
                    {
                        IsSuccess = false;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "车辆预入场不成功-" + sRemark);
                    }

                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = "openGate" }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = "openGate" }));
            }
        }
    }

    /// <summary>
    /// 【扫码登记入场】平台扫车道码入场信息获取 GetInGateOrder
    /// </summary>
    public class GetInGateOrderHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GetInGateOrder";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                string orderNo = Convert.ToString(jo["parkOrderNo"]);

                var dataValue = new
                {
                    carNo = string.Empty,
                    plate = 0,
                    parkOrderNo = string.Empty
                };

                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"[GetInGateOrder]接收到指令: ctrlNo={passNo}, parkOrderNo={orderNo}");

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"[GetInGateOrder]失败原因: {sRemark}");
                }
                else
                {
                    #region 发送预入场开闸操作

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "平台扫码入场信息获取",
                        BaseData = null,
                        sCarplate = string.Empty,
                        sOrderNo = orderNo,
                        SendOptions = Model.API.SendOptionsType.GetInGateOrder,
                        ImgUrl = string.Empty
                    };

                    string retSuccess = "";
                    string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    if (IsSuccess && retSuccess.ToLower().Contains("true"))
                    {
                        var jo11 = JObject.Parse(retValue);
                        dataValue = new
                        {
                            carNo = jo11.ContainsKey("carNo") ? Convert.ToString(jo11["carNo"]) : string.Empty,
                            plate = jo11.ContainsKey("plate") ? Convert.ToInt32(jo11["plate"]) : 0,
                            parkOrderNo = jo11.ContainsKey("parkOrderNo") ? Convert.ToString(jo11["parkOrderNo"]) : string.Empty
                        };
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "[GetInGateOrder]获取入场订单成功");
                    }
                    else
                    {
                        IsSuccess = false;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "[GetInGateOrder]获取入场订单失败-" + sRemark);
                    }

                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = "GetInGateOrder",
                    data = dataValue
                }));

            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
            }
        }
    }

    /// <summary>
    /// 【扫码登记入场】推送确认入场指令处理类PushInGateOrdel
    /// </summary>
    public class PushInGateOrdelHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PushInGateOrder";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                var IsSuccess = false;
                var sRemark = string.Empty;
                var passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                var orderNo = Convert.ToString(jo["parkOrderNo"]);//订单号
                var carNo = Convert.ToString(jo["carNo"]);//车牌号
                var phone = Convert.ToString(jo["phone"]);//手机号
                var remark = Convert.ToString(jo["remark"]);//登记备注

                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"[PushInGateOrdel]接收到指令: ctrlNo={passNo}, parkOrderNo={orderNo}, carNo={carNo}, phone={phone}");

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"[PushInGateOrdel]失败原因: {sRemark}");
                }
                else
                {
                    #region 发送确认入场操作
                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "平台推送确认入场",
                        BaseData = phone,
                        opRemarks = remark,
                        sCarplate = carNo,
                        sOrderNo = orderNo,
                        SendOptions = Model.API.SendOptionsType.PushInGateOrdel,
                        ImgUrl = string.Empty
                    };

                    string retSuccess = "";
                    string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;

                    if (IsSuccess && retSuccess.ToLower().Contains("true"))
                    {
                        IsSuccess = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "[PushInGateOrdel]平台推送确认入场成功-" + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "[PushInGateOrdel]平台推送确认入场不成功-" + sRemark);
                    }
                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = "PushInGateOrdel" }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
            }
        }
    }

    /// <summary>
    /// 场内记录下发通知指令处理类
    /// </summary>
    public class PullEnterCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullEnterCar,PullEstEnterCar"; //PullEstEnterCar-动态码，PullEnterCar-车道固定码

        public class Param
        {
            /// <summary>
            /// 车牌号
            /// </summary>
            public string carNo { get; set; }

            /// <summary>
            /// 订单号
            /// </summary>
            public string orderNo { get; set; }

            /// <summary>
            /// 入场时间
            /// </summary>
            public string enterTime { get; set; }

            /// <summary>
            /// 车牌类型数值：3651等等
            /// </summary>
            public string carType { get; set; }

            /// <summary>
            /// 车道名称
            /// </summary>
            public string gateName { get; set; }

            /// <summary>
            /// 操作员名称
            /// </summary>
            public string operatorName { get; set; }

            /// <summary>
            /// 控制机号,T30软件中车道编号
            /// </summary>
            public string ctrlNo { get; set; }

            /// <summary>
            /// 入场类型：默认为空和1（无牌车扫码入场），2-导入入场记录+新增入场记录（不做入场前校验,场内已有的车牌则关闭旧订单）
            /// </summary>
            public string enterCode { get; set; }

            /// <summary>
            /// 推送记录的时候图片
            /// </summary>
            public string img { get; set; }

            /// <summary>
            /// 备注
            /// </summary>
            public string remark { get; set; }
        }


        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            //多指令先获取指令接收的指令名称
            string sActionName = jo.ContainsKey("actionName") ? Convert.ToString(jo["actionName"]) : InstructionName;

            try
            {
                string sRemark = string.Empty;
                bool IsSuccess = false;

                PullEnterCarHandle.Param param = jo.ToObject<PullEnterCarHandle.Param>();
                if (param.enterCode != null && param.enterCode.Equals("2"))
                {
                    IsSuccess = ExecuteEnterCar(param, out sRemark);
                    if (AppSettingConfig.SentryMode == VersionEnum.EPSServer) Task.Delay(50).Wait();
                }
                else
                {
                    var rlt = GetMemoryCache.Get<Tuple<string, string, string>>($"Mid_{Entitys.CacheType.EstCarIn}=>{param.orderNo}");
                    if (rlt == null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "车辆缓存信息未找到，扫码入场记录写入失败" + JsonConvert.SerializeObject(new { param.carNo, param.orderNo, param.enterTime, param.carType, param.gateName, param.operatorName }));
                        sRemark = "车辆缓存信息未找到，保存无牌车扫码入场记录失败";
                    }
                    else
                    {
                        #region 发送开闸入场确认操作

                        Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                        {
                            ParkNo = AppCache.GetParking.Parking_No,
                            PasswayNo = rlt.Item2,
                            SentryHostNo = rlt.Item1,
                            DataType = "车辆确认入场",
                            BaseData = null,
                            sCarplate = param.carNo,
                            sOrderNo = param.orderNo,
                            SendOptions = Model.API.SendOptionsType.EnterCar,
                            opTime = param.enterTime,
                            operatorName = param.operatorName,
                            InNoneCarNo = true,
                            opRemarks = param.remark,
                        };

                        string retSuccess = ""; string retValue = "";
                        var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                        IsSuccess = task.Item1;
                        sRemark = task.Item2;
                        retSuccess = task.Item3;
                        retValue = task.Item4;
                        //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                        if (retSuccess.ToLower().Contains("true"))
                        {
                            IsSuccess = true;
                            LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "入场确认成功-" + sRemark);
                        }
                        else
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "入场确认不成功-" + sRemark);
                        }

                        #endregion
                    }
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = sActionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = sActionName }));
            }
        }

        /// <summary>
        /// 创建入场记录(不判断入场策略)
        /// </summary>
        public bool ExecuteEnterCar(PullEnterCarHandle.Param param, out string sRemark)
        {
            try
            {
                var passway = BLL.Passway.GetEntity(param.ctrlNo);
                var links = BLL.PasswayLink.GetEntity(param.ctrlNo);
                var gate = links.Find(x => x.PasswayLink_GateType == 1);
                var area = BLL.ParkArea.GetEntity(gate.PasswayLink_ParkAreaNo);

                var car = BLL.Car.GetEntityByCarNo(param.carNo);
                var card = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='{param.carType}' AND CarCardType_IsDefault=1");
                var cartype = new Model.CarType();
                //未登记车辆，使用默认参数(临时车A，蓝牌车)
                if (car != null)
                {
                    cartype = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                    if (card == null)
                        card = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='3651' AND CarCardType_IsDefault=1");
                }
                else
                {
                    cartype = BLL.CarType.GetEntity("*", "CarType_Name='蓝牌车'");
                }

                #region 创建订单

                Model.ParkOrder order = BLL.ParkOrder.CreateParkOrder(
                    AppCache.GetParking.Parking_No
                    , area?.ParkArea_No
                    , area?.ParkArea_Name
                    , param.carNo
                    , card.CarCardType_No
                    , card.CarCardType_Name
                    , cartype.CarType_No
                    , cartype.CarType_Name
                    , Utils.StrToDateTime(param.enterTime)
                    , param.ctrlNo
                    , passway.Passway_Name
                    , 0
                    , 0
                    , car?.Car_OwnerNo
                    , car?.Car_OwnerName
                    , param.orderNo);
                order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                order.ParkOrder_EnterRemark = param.remark;

                Model.OrderDetail detail = BLL.OrderDetail.CreateOrderDetail(
                    param.orderNo
                    , AppCache.GetParking.Parking_No
                    , area?.ParkArea_No
                    , area?.ParkArea_Name
                    , param.carNo
                    , card?.CarCardType_No
                    , card?.CarCardType_Name
                    , cartype?.CarType_No
                    , cartype?.CarType_Name
                    , Utils.StrToDateTime(param.enterTime)
                    , passway?.Passway_No
                    , passway?.Passway_Name);
                detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;

                #endregion

                //防疫
                BLL.ParkOrder.EpParkOrder(ref order, null);

                var orderList = new List<ParkOrder> { order };
                var detailList = new List<OrderDetail> { detail };

                //一位多车检测
                bool checkSpace = false;
                List<Model.InCar> incarList = null;
                List<Model.Car> carList = null;
                Model.Owner owner = null;
                List<Model.ParkOrder> ordersList = null;
                List<Model.OrderDetail> detailsList = null;
                if (car != null && card != null && card.CarCardType_IsMoreCar == 1)
                {
                    owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
                    if (owner != null) carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{car.Car_OwnerNo}'");
                    if (carList != null && carList.Count > 1) incarList = BLL.BaseBLL._GetAllEntity(new Model.InCar(), "*", $"InCar_Status=200 and InCar_CarNo in ('{string.Join("','", carList.Select(x => x.Car_CarNo))}')");
                    if (owner != null && carList != null)
                    {
                        if (carList.Count > 1)
                        {
                            if (incarList != null && incarList.Where(x => x.InCar_CarNo != car.Car_CarNo).Count() > 0)
                            {
                                checkSpace = true;
                                ordersList = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}')");
                                detailsList = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkOrderNo in ('{string.Join("','", incarList.Select(x => x.InCar_ParkOrderNo))}')");
                            }
                            else
                            {
                                orderList.ForEach(x => { x.ParkOrder_IsLift = 1; });
                                detailList.ForEach(x => { x.orderdetail_IsCharge = 1; });
                            }
                        }
                        else
                        {
                            orderList.ForEach(x => { x.ParkOrder_IsLift = 1; });
                            detailList.ForEach(x => { x.orderdetail_IsCharge = 1; });
                        }
                    }
                }

                //判断场内是否已存在此车牌
                BLL.ParkOrder.InOutRecordWithOrder(ref orderList, ref detailList, incarList, ordersList, detailsList);

                //一位多车检测
                if (checkSpace)
                {
                    List<Model.ParkOrder> newOrderList = null;
                    List<Model.OrderDetail> newDetailList = null;
                    var newOrder = orderList.Find(x => x.ParkOrder_CarNo == param.carNo && x.ParkOrder_StatusNo == 200); //当前入场订单
                    if (newOrder != null)
                    {
                        //排除当前入场车牌
                        newOrderList = ordersList.Where(x => x.ParkOrder_CarNo != newOrder.ParkOrder_CarNo).ToList();
                        //车主的所有入场订单
                        newOrderList.Add(newOrder);

                        //当前订单的明细
                        newDetailList = detailList.Where(x => x.OrderDetail_ParkOrderNo == newOrder.ParkOrder_No).ToList();
                        if (newDetailList.Count > 0)
                        {
                            //排除当前入场车牌的明细
                            var d1 = detailsList.Where(x => x.OrderDetail_CarNo != newOrder.ParkOrder_CarNo).ToList();
                            if (d1.Count > 0) newDetailList.AddRange(d1);
                        }
                        else
                        {
                            //车主的所有入场明细
                            newDetailList.AddRange(detailsList);
                        }

                        if (incarList.Find(x => x.InCar_CarNo == param.carNo) == null)
                            incarList.Add(new Model.InCar()
                            {
                                InCar_ParkOrderNo = newOrder.ParkOrder_No,
                                InCar_CarNo = newOrder.ParkOrder_CarNo,
                                InCar_Status = newOrder.ParkOrder_StatusNo,
                                InCar_CarCardTypeNo = newOrder.ParkOrder_CarCardType,
                                InCar_ParkAreaNo = newOrder.ParkOrder_ParkAreaNo,
                                InCar_EnterTime = newOrder.ParkOrder_EnterTime,
                            });
                    }

                    newOrderList = newOrderList.OrderBy(x => x.ParkOrder_EnterTime).ToList();

                    //导入订单检查
                    BLL.ParkOrder.ImportRecordToChangeOrder(carList, new List<Model.Owner> { owner }, null, null, ref newOrderList, ref newDetailList, incarList, newOrderList, newDetailList);

                    //更新要导入的数据
                    for (var i = 0; i < orderList.Count; i++)
                    {
                        var oModel = newOrderList.Find(x => x.ParkOrder_No == orderList[i].ParkOrder_No);
                        if (oModel != null)
                        {
                            orderList[i] = oModel;
                            var dModel = detailList.FindAll(x => x.OrderDetail_ParkOrderNo == orderList[i].ParkOrder_No);
                            if (dModel != null)
                            {
                                dModel.ForEach(x => { detailList.Remove(x); });
                                detailList.AddRange(newDetailList.Where(x => x.OrderDetail_ParkOrderNo == orderList[i].ParkOrder_No));
                            }
                        }
                    }

                    newOrderList.ForEach(item =>
                    {
                        var oModel = orderList.Find(x => x.ParkOrder_No == item.ParkOrder_No);
                        if (oModel == null)
                        {
                            orderList.Add(item);
                            var dModel = newDetailList.FindAll(x => x.OrderDetail_ParkOrderNo == item.ParkOrder_No);
                            if (dModel != null)
                            {
                                detailList.AddRange(dModel);
                            }
                        }
                    });
                }

                //入场更新
                var ret = BLL.ParkOrder.CarInComplete(orderList, detailList);
                if (ret > 0)
                {
                    Task.Run(() =>
                    {
                        var pushData = new Model.API.PushResultParse.UpdateParkOrder
                        {
                            Item1 = orderList,
                            Item2 = detailList
                        };
                        HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), pushData, "carin");
                        //var resBody = BLL.MiddlewareApi.UpdateOrderList(pushData);
                        //if (!resBody.success)
                        //{
                        //    LogManagementMap.WriteToFileException(null, $"保存平台下发的入场记录成功, 推送到其他岗亭失败：{resBody.errmsg}");
                        //}
                    });
                    sRemark = "保存平台下发的入场记录成功";
                    return true;
                }
                else
                {
                    sRemark = "保存平台下发的入场记录失败";
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"平台推送入场记录异常");

                sRemark = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 创建入场记录(判断入场策略)
        /// </summary>
        public bool ExecuteEnterCarPolicy(PullEnterCarHandle.Param param, out string sRemark)
        {
            try
            {
                var device = string.IsNullOrEmpty(param.ctrlNo) ? null : BLL.Device.GetEntityByPasswayNo(param.ctrlNo);
                var data = PassTool.PassHelper.OnCheckCarPass(new ParkCarInOut()
                {
                    carno = param.carNo,
                    camerano = device?.Device_No,
                    isreal = 0,
                    isVideoRecord = false,
                    mode = 7,
                    time = Utils.StrToDateTime(param.enterTime),
                    parkno = AppCache.GetParking.Parking_No,
                    cartype = "",
                    orderno = param.orderNo
                });

                if (!data.success)
                {
                    sRemark = data.errmsg;
                    return false;
                }

                if (data.passres.code == 0)
                {
                    sRemark = data.passres.errmsg;
                    return false;
                }

                var card = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='{param.carType}' AND CarCardType_IsDefault=1");
                var resBody = PassTool.PassHelper.CarInComplete(new ParkGatePass()
                {
                    carno = data.passres.carno,
                    camerano = data.passres.device.Device_No,
                    code = 200,
                    onenter = 1,
                    parkno = AppCache.GetParking.Parking_No,
                    time = data.time,
                    orderno = data.passres.parkorderno,
                    orderdetailno = data.passres.orderdetailno,
                    account = "online",
                    name = "平台下发",
                    img = param.img, // data.passres.img,
                    imgpath = data.passres.localimage,
                    isSupplement = data?.isSupplement ?? false,
                    onindata = new ParkGateIn()
                    {
                        carno = param.carNo,
                        carcardtypeno = card?.CarCardType_No,
                        carcardtypename = card?.CarCardType_Name
                    }
                });

                if (resBody.success)
                {
                    Model.ResBodyDataIn resData = resBody.ParseData<Model.ResBodyDataIn>();
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (resData.Item1?.FirstOrDefault(), resData.Item2), "updateorder");
                    //var toRes = BLL.MiddlewareApi.UpdateOrderDetail(resData.Item1?.FirstOrDefault(), resData.Item2);
                    //if (!toRes.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"保存平台下发的入场记录成功,同步到其他岗亭失败：{toRes.errmsg},=>{resBody.data}");
                    //}

                    sRemark = "保存平台下发的入场记录成功";
                    return true;
                }
                else
                {
                    sRemark = resBody.errmsg;
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"平台推送入场记录异常");

                sRemark = ex.Message;
                return false;
            }
        }
    }


    /// <summary>
    /// 无牌车出场执行开闸操作指令处理类
    /// </summary>
    /// <summary>
    public class OpenOutGateHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OpenOutGate";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                string orderNo = Convert.ToString(jo["parkOrderNo"]);
                string carNo = Convert.ToString(jo["carNo"]);

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    Model.ParkOrder parkorder = BLL.ParkOrder.GetEntity(orderNo);

                    #region 发送预出场开闸操作

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "预出场开闸",
                        BaseData = null,
                        sCarplate = carNo,
                        sOrderNo = orderNo,
                        SendOptions = Model.API.SendOptionsType.OpenOutGate
                    };

                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;

                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                    if (retSuccess.ToLower().Contains("true")) //预出场开闸成功
                    {
                        if (parkorder != null)
                        {
                            if (parkorder.ParkOrder_IsUnlicensedCar == 1 && parkorder.ParkOrder_IsAutoOutCar == 1)
                            {
                                parkorder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
                                parkorder.ParkOrder_TotalPayed = parkorder.ParkOrder_TotalAmount;
                                BLL.ParkOrder._UpdateByModelByNo(parkorder);
                                //BLL.MiddlewareApi.UpdateOrder(parkorder);
                                HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), parkorder);
                                _ = Task.Run(() =>
                                 {
                                     try
                                     {
                                         Model.Parking parking = BLL.Parking.GetEntity(parkorder.ParkOrder_ParkNo);
                                         BLL.PushEvent.OutCar(parking?.Parking_Key, parkorder);
                                     }
                                     catch (Exception e)
                                     {
                                         LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"预出场开闸上报平台异常OpenOutGate-{TyziTools.Json.ToString(parkorder)}," + e.ToString());
                                     }
                                 });
                            }
                        }

                        GetMemoryCache.Set($"{Entitys.CacheType.EstCarOut}=>{orderNo}", new Tuple<string, string, string>(model.Passway_SentryHostNo, passNo, orderNo), cacheEntityOps);
                        IsSuccess = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "预出场开闸成功-" + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "预出场开闸不成功-" + sRemark);
                    }

                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = "openGate" }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = "openGate" }));
            }
        }
    }


    /// <summary>
    /// 车辆出场下发通知指令处理类
    /// </summary>
    public class PullOutCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullOutCar";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string carNo = Convert.ToString(jo["carNo"]);
                string orderNo = Convert.ToString(jo["orderNo"]);
                string outTime = Convert.ToString(jo["outTime"]);
                string gateName = Convert.ToString(jo["gateName"]);
                string operatorName = Convert.ToString(jo["operatorName"]);
                string carType = Convert.ToString(jo["carType"]);
                //string totalAmount = Convert.ToString(jo["totalAmount"]);
                //string payType = Convert.ToString(jo["payType"]);

                var history = GetMemoryCache.Get<Tuple<string, string, string>>($"{InstructionName}_{orderNo}");
                if (history != null)
                {
                    //车辆出场信息已经接收到
                    sRemark = $"重复下发车辆订单数据[{orderNo}],跳过处理";
                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                }
                else
                {
                    //添加一个15秒后才过期的缓存，缓存主键是停车订单号
                    GetMemoryCache.Set($"{InstructionName}_{orderNo}", new Tuple<string, string, string>(carNo, orderNo, outTime), cacheEntityOps);
                    var rlt = GetMemoryCache.Get<Tuple<string, string, string>>($"{Entitys.CacheType.EstCarOut}=>{orderNo}");
                    if (rlt == null)
                    {
                        sRemark = $"[{carNo}]预出场信息不存在";

                        Model.ParkOrder po = BLL.ParkOrder.GetEntity(orderNo);
                        if (po != null && po.ParkOrder_StatusNo < (int)ParkOrderStatusEnum.Out)
                        {
                            po.ParkOrder_StatusNo = (int)ParkOrderStatusEnum.Close; //这里执行关闭订单操作
                            po.ParkOrder_Remark = "预出场信息不存在，平台关闭订单";

                            List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity(po.ParkOrder_No);
                            details?.ForEach(x =>
                            {
                                x.OrderDetail_StatusNo = (int)ParkOrderStatusEnum.Close;
                                x.OrderDetail_Remark = "预出场信息不存在，平台关闭订单";
                            });

                            var result = BLL.OrderDetail.UpdateByList(new List<Model.ParkOrder>() { po }, details);
                            if (result)
                            {
                                HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (po, details), "updateorder");
                                //var r = BLL.MiddlewareApi.UpdateOrderDetail(po, details);
                                //if (!r.success)
                                //{
                                //    LogManagementMap.WriteToFileException(null, $"{InstructionName}车辆出场确认，预出场信息不存在关闭场内记录分发失败：{r.errmsg}，{orderNo}");
                                //}

                                SendTCPDatas(context, sendsession, true, "车辆出场确认，预出场信息不存在关闭场内记录成功", InstructionName);
                                return;
                            }
                            else
                            {
                                LogManagementMap.WriteToFileException(null, $"{InstructionName}车辆出场确认，预出场信息不存在关闭场内记录执行SQL错误：订单号--{orderNo}");
                                SendTCPDatas(context, sendsession, false, "车辆出场确认，预出场信息不存在关闭场内记录失败", InstructionName);
                                return;
                            }
                        }

                        SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = $"{sRemark}", actionName = InstructionName }));
                    }
                    else
                    {
                        #region 发送出场确认操作

                        SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = "已收到车辆出场指令", actionName = InstructionName }));

                        Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                        {
                            ParkNo = AppCache.GetParking.Parking_No,
                            PasswayNo = rlt.Item2,
                            SentryHostNo = rlt.Item1,
                            DataType = "车辆出场确认",
                            BaseData = null,
                            sCarplate = carNo,
                            sOrderNo = orderNo,
                            opTime = outTime,
                            operatorName = operatorName,
                            SendOptions = Model.API.SendOptionsType.OutCar,
                            opCarCardType = carType
                        };

                        string retSuccess = ""; string retValue = "";
                        var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                        IsSuccess = task.Item1;
                        sRemark = task.Item2;
                        retSuccess = task.Item3;
                        retValue = task.Item4;
                        //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                        if (retSuccess.ToLower().Contains("true")) //车辆出场确认
                        {
                            IsSuccess = true;
                            sRemark = "车辆出场确认成功-" + sRemark;
                        }
                        else
                        {
                            IsSuccess = false;
                            sRemark = "车辆出场确认不成功-" + sRemark;
                        }

                        SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
                        #endregion
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 执行关闸指令处理类
    /// </summary>
    public class CloseGateHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CloseGate";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //关闸机号


                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    #region 发送关闸操作

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "云平台-远程关闸",
                        BaseData = null,
                        SendOptions = Model.API.SendOptionsType.CloseGate
                    };

                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                    if (retSuccess.ToLower().Contains("true")) //关闸成功
                    {
                        IsSuccess = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "平台人工关闸成功-" + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "平台人工关闸不成功" + sRemark);
                    }

                    #endregion
                }


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 获取车道道闸状态指令处理类
    /// </summary>
    public class GateStatusHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GateStatus";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                var data = new { groundState = 0 /*0无车1有车*/, gateState = 0 /*0关闸1开闸*/ };

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    #region 获取车道状态

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "获取车道状态",
                        SendOptions = Model.API.SendOptionsType.GateStatus
                    };

                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    if (retSuccess.ToLower().Contains("true"))
                    {
                        IsSuccess = true;
                        var fv1 = JsonConvert.DeserializeObject<Tuple<int, int>>(retValue);
                        if (fv1 != null)
                        {
                            data = new
                            {
                                groundState = fv1.Item1 == -1 ? 0 : fv1.Item1, //地感状态
                                gateState = fv1.Item2 //道闸状态
                            };
                        }
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "获取车道状态成功-" + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "获取车道状态不成功-" + sRemark);
                    }

                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName, data = data }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 执行相机抓图操作指令处理类
    /// </summary>
    /// <summary>
    public class GetCameraPhotoHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GetCameraPhoto";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string videoIp = Convert.ToString(jo["videoIp"]); //摄像机IP

                var devices = BLL.Device.GetAllEntityExt("*", $"Device_Category='1' and Device_IP ='{videoIp}'  and  Device_Account!='' and Device_Account is not null and Device_Pwd !='' and Device_Pwd is not null");
                if (devices == null || devices.Count <= 0)
                {
                    sRemark = "未找到车道识别相机设备！";
                }
                else
                {
                    PingReply reply = new Ping().Send(videoIp, 120);
                    if (reply.Status == IPStatus.Success)
                    {
                        ThreadStart starter1 = delegate
                        {
                            Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                            {
                                ParkNo = devices[0].Device_ParkNo,
                                PasswayNo = devices[0].Device_PasswayNo,
                                SentryHostNo = devices[0].Device_SentryHostNo,
                                BaseData = null,
                                DataType = "抓拍",
                                SendOptions = Model.API.SendOptionsType.GetImage
                            };
                            Model.SentryHost sentryHost = BLL.SentryHost.GetEntity(devices[0].Device_SentryHostNo);

                            CamerasCaptureUtil.UploadSnapShootImg(devices[0], sentryHost, optionsModel);
                        };
                        new Thread(starter1) { Priority = ThreadPriority.Highest }.Start();
                        IsSuccess = true;
                        sRemark = "执行图片抓拍操作成功!";
                    }
                    else
                    {
                        sRemark = "相机连接不通!";
                    }
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }


    /// <summary>
    /// 获取车辆订单图片指令处理类
    /// </summary>
    public class GetOrderImgHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GetOrderImg";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["orderNo"]); //车辆订单编码

                if (CloudTask.GetCloudParkConfig.picUpload != 1)
                {
                    sRemark = "上传图片功能未启用！";
                }
                else
                {
                    string sInImagePath = string.Empty;
                    string sOutImagePath = string.Empty;
                    string sCarNo = string.Empty; //车牌号码

                    var pn = BLL.ParkOrder.GetEntity(orderNo);
                    if (pn == null)
                    {
                        sRemark = "没有查询到订单信息";
                    }
                    else
                    {
                        sInImagePath = pn.ParkOrder_EnterImgPath;
                        sOutImagePath = pn.ParkOrder_OutImgPath;
                        sCarNo = pn.ParkOrder_CarNo;
                    }

                    bool isInPic = false;
                    bool isOut = false;

                    var dstInImagePathOld = "";
                    if (!string.IsNullOrEmpty(sInImagePath))
                    {
                        string inpath = Path.Combine(AppBasicCache.strImgpath, DateTime.Now.ToString("yyyyMM"), DateTime.Now.ToString("dd"));
                        if (!Directory.Exists(inpath))
                        {
                            Directory.CreateDirectory(inpath);
                        }

                        dstInImagePathOld = Path.Combine(inpath, $"{orderNo}_I_Copy.jpg");
                        if (Common.FileHelper.SaveLocalFile(sInImagePath, dstInImagePathOld))
                        {
                            isInPic = true;
                            sInImagePath = dstInImagePathOld;
                        }
                    }

                    var sOutImagePathOld = "";
                    if (!string.IsNullOrEmpty(sOutImagePath))
                    {
                        string outpath = Path.Combine(AppBasicCache.strImgpath, DateTime.Now.ToString("yyyyMM"), DateTime.Now.ToString("dd"));
                        if (!Directory.Exists(outpath))
                        {
                            Directory.CreateDirectory(outpath);
                        }
                        sOutImagePathOld = Path.Combine(outpath, $"{orderNo}_O_Copy.jpg");
                        if (Common.FileHelper.SaveLocalFile(sOutImagePath, sOutImagePathOld))
                        {
                            isOut = true;
                            sOutImagePath = sOutImagePathOld;
                        }
                    }

                    if (isInPic || isOut)
                    {
                        Task.Factory.StartNew(() =>
                        {
                            try
                            {
                                OssClient client = new OssClient(CloudTask.GetCloudParkConfig.endpoint, CloudTask.GetCloudParkConfig.accessKeyId, CloudTask.GetCloudParkConfig.AccessKeySecret);
                                if (!client.DoesBucketExist(CloudTask.GetCloudParkConfig.bucketName))
                                {
                                    client.CreateBucket(CloudTask.GetCloudParkConfig.bucketName);
                                }

                                //入场图片上传
                                if (isInPic)
                                {
                                    string inpath = Path.Combine(AppBasicCache.strImgpath, DateTime.Now.ToString("yyyyMM"), DateTime.Now.ToString("dd"));
                                    if (!Directory.Exists(inpath))
                                    {
                                        Directory.CreateDirectory(inpath);
                                    }
                                    string dstInImagePath = Path.Combine(inpath, $"{orderNo}_I.jpg");
                                    if (File.Exists(dstInImagePath))
                                    {
                                        File.Delete(dstInImagePath);
                                    }

                                    ImageUtil.CompressImage(sInImagePath, dstInImagePath, 90, 512);
                                    string imgName = Path.GetFileNameWithoutExtension(dstInImagePath);
                                    PutObjectResult putResult = client.PutObject(CloudTask.GetCloudParkConfig.bucketName, imgName, dstInImagePath);
                                    if (putResult != null && putResult.HttpStatusCode == HttpStatusCode.OK)
                                    {
                                        var req = new GeneratePresignedUriRequest(CloudTask.GetCloudParkConfig.bucketName, imgName, SignHttpMethod.Get)
                                        {
                                            Expiration = DateTimeHelper.GetNowTime().AddYears(10)
                                        };
                                        // 产生带有签名的URI
                                        var uri = client.GeneratePresignedUri(req);
                                        dynamic dnc = new ExpandoObject();
                                        dnc.version = AppCache.GetParking.AppVersion;
                                        dnc.key = AppCache.GetParking.Parking_Key;
                                        dnc.carNo = sCarNo;
                                        dnc.orderNo = orderNo;
                                        dnc.imgUrl = uri.ToString();

                                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"上传入场图片：{JsonConvert.SerializeObject(dnc)}");
                                        string sResult = ParkCloudHelper.CloudParkHttpSend("Car/EnterCarImg", JsonConvert.SerializeObject(dnc));
                                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"上传入场图片应答：{JsonConvert.SerializeObject(dnc)}");
                                        if (!string.IsNullOrWhiteSpace(sResult))
                                        {
                                            JObject @object = JObject.Parse(sResult);
                                            if (@object.Property("resultcode") != null && Convert.ToString(@object["resultcode"]) == "1")
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{orderNo} 入场图片上传成功 ！");
                                            }
                                            else
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{orderNo} 入场上传失败！");
                                            }
                                        }
                                        else
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"上传入场图片失败！");
                                        }
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{orderNo} 入场图片上传到阿里云服务失败 ！");
                                    }


                                    if (File.Exists(sInImagePath))
                                    {
                                        File.Delete(sInImagePath);
                                    }

                                    if (File.Exists(dstInImagePath))
                                    {
                                        File.Delete(dstInImagePath);
                                    }
                                }

                                //出场图片上传
                                if (isOut)
                                {
                                    string inpath = Path.Combine(AppBasicCache.strImgpath, DateTime.Now.ToString("yyyyMM"), DateTime.Now.ToString("dd"));
                                    if (!Directory.Exists(inpath))
                                    {
                                        Directory.CreateDirectory(inpath);
                                    }
                                    string dstOutImagePath = Path.Combine(inpath, $"{orderNo}_O.jpg");
                                    if (File.Exists(dstOutImagePath))
                                    {
                                        File.Delete(dstOutImagePath);
                                    }

                                    ImageUtil.CompressImage(sOutImagePath, dstOutImagePath, 90, 512);
                                    string imgName = Path.GetFileNameWithoutExtension(dstOutImagePath);
                                    PutObjectResult putResult = client.PutObject(CloudTask.GetCloudParkConfig.bucketName, imgName, dstOutImagePath);
                                    if (putResult != null && putResult.HttpStatusCode == HttpStatusCode.OK)
                                    {
                                        var req = new GeneratePresignedUriRequest(CloudTask.GetCloudParkConfig.bucketName, imgName, SignHttpMethod.Get)
                                        {
                                            Expiration = DateTimeHelper.GetNowTime().AddYears(10)
                                        };
                                        // 产生带有签名的URI
                                        var uri = client.GeneratePresignedUri(req);

                                        dynamic dnc = new ExpandoObject();
                                        dnc.version = AppCache.GetParking.AppVersion;
                                        dnc.key = AppCache.GetParking.Parking_Key;
                                        dnc.carNo = sCarNo;
                                        dnc.orderNo = orderNo;
                                        dnc.imgUrl = uri.ToString();

                                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"上传出场图片：{JsonConvert.SerializeObject(dnc)}");
                                        string sResult = ParkCloudHelper.CloudParkHttpSend("Car/OutCarImg", JsonConvert.SerializeObject(dnc));
                                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"上传出场图片应答：{JsonConvert.SerializeObject(dnc)}");
                                        if (!string.IsNullOrWhiteSpace(sResult))
                                        {
                                            JObject @object = JObject.Parse(sResult);
                                            if (@object.Property("resultcode") != null && Convert.ToString(@object["resultcode"]) == "1")
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{orderNo} 出场图片上传成功 ！");
                                            }
                                            else
                                            {
                                                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{orderNo} 出场上传失败！");
                                            }
                                        }
                                        else
                                        {
                                            LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"上传出场图片失败！");
                                        }
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{orderNo} 出场图片上传到阿里云服务失败 ！");
                                    }

                                    if (File.Exists(sOutImagePath))
                                    {
                                        File.Delete(sOutImagePath);
                                    }

                                    if (File.Exists(dstOutImagePath))
                                    {
                                        File.Delete(dstOutImagePath);
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                LogManagementMap.WriteToFileException(ex, "重新上传图片任务执行失败");
                            }
                        });

                        IsSuccess = true;
                        sRemark = "上传图片操作正在执行！";
                    }
                    else
                    {
                        IsSuccess = false;
                        sRemark = $"当前订单进出场图片都不存在！sInImagePath={sInImagePath}/sOutImagePath={sOutImagePath}";
                    }
                }

                //应答数据
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 读取HTTP图片路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns></returns>
        private bool ReadHttpImage(string urlPath, string savePath)
        {
            bool isReadSuccess = false;
            try
            {
                FileInfo fileInfo = new FileInfo(savePath);
                if (!fileInfo.Directory.Exists)
                {
                    fileInfo.Directory.Create();
                }

                WebRequest myrequest = WebRequest.Create(urlPath);
                WebResponse myresponse = myrequest.GetResponse();
                Stream imgstream = myresponse.GetResponseStream();
                System.Drawing.Image img = System.Drawing.Image.FromStream(imgstream);
                img.Save(savePath, System.Drawing.Imaging.ImageFormat.Jpeg);
                if (File.Exists(savePath))
                {
                    isReadSuccess = true;
                }
            }
            catch
            {
                isReadSuccess = false;
            }

            return isReadSuccess;
        }
    }

    /// <summary>
    /// 获取车位信息指令处理类
    /// </summary>
    /// <summary>
    public class ParkSpaceHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "ParkSpace";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                int sRemainCar = 0;
                int total_spaces = 0;
                bool IsSuccess = false;
                string sRemark = string.Empty;

                var spaceData = PassTool.MonitorHelper.GetAllSpaceItems(AppCache.GetParking.Parking_No);
                if (spaceData.Item1 > -1 && spaceData.Item3 != null && spaceData.Item3.Count > 0)
                {
                    total_spaces = spaceData.Item1;
                    sRemainCar = spaceData.Item2;
                }

                IsSuccess = true;
                //应答数据
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = InstructionName,
                    data = new
                    {
                        remai_spaces = sRemainCar, //剩余车位
                        total_spaces = total_spaces, //总车位数
                    }
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }


    /// <summary>
    /// 车辆锁定指令处理类
    /// </summary>
    /// <summary>
    public class LockCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "LockCar";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                //bool IsSuccess = false;
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单

                //查询停车订单
                Model.ParkOrder order = BLL.ParkOrder.GetEntity(orderNo);
                if (order == null)
                {
                    SendTCPDatas(context, sendsession, false, "未找到停车订单信息", InstructionName);
                    return Task.CompletedTask;
                }


                order.ParkOrder_Lock = 1;

                var result = BLL.ParkOrder._UpdateByModelByNo(order);
                if (result >= 0)
                {
                    //var res = BLL.MiddlewareApi.UpdateOrder(order);
                    //if (!res.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}车辆锁定分发失败：{res.errmsg}，{orderNo}");
                    //}
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), order);

                    SendTCPDatas(context, sendsession, true, "车辆锁定成功", InstructionName);
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "车辆锁定失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    ///  解除车辆锁定指令处理类
    /// </summary>
    /// <summary>
    public class UnlockCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "UnlockCar";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                //bool IsSuccess = false;
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单


                //查询停车订单
                Model.ParkOrder order = BLL.ParkOrder.GetEntity(orderNo);
                if (order == null)
                {
                    SendTCPDatas(context, sendsession, false, "未找到停车订单信息", InstructionName);
                    return Task.CompletedTask;
                }

                order.ParkOrder_Lock = 0;

                var result = BLL.ParkOrder._UpdateByModelByNo(order);
                if (result >= 0)
                {
                    //var res = BLL.MiddlewareApi.UpdateOrder(order);
                    //if (!res.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}解除车辆锁定分发失败：{res.errmsg}，{orderNo}");
                    //}
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), order);

                    SendTCPDatas(context, sendsession, true, "解除车辆锁定成功", InstructionName);
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "解除车辆锁定失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 获取预支付订单指令处理类
    /// </summary>
    public class GetOutGateOrderHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GetOutGateOrder";


        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                object data = default;
                string passNo = Convert.ToString(jo["ctrlNo"]);
                string orderNo = Convert.ToString(jo["parkOrderNo"]);
                string userNo = Convert.ToString(jo["userNo"]);

                var model = BLL.BaseBLL._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    #region 获取停车订单信息

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "获取停车订单信息",
                        BaseData = null,
                        sOrderNo = orderNo,
                        userNo = userNo,
                        ActionType = 1,
                        SendOptions = Model.API.SendOptionsType.GetOutGateOrder
                    };

                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    if (retSuccess.ToLower() == "true")
                    {
                        IsSuccess = true;
                        string value = retValue;
                        if (!string.IsNullOrWhiteSpace(value))
                        {
                            if (value == orderNo && !string.IsNullOrWhiteSpace(orderNo))
                            {
                                IsSuccess = true;
                                data = new { parkOrderNo = orderNo };
                                DataCache.AskPrice.Set("OrderPrice:" + orderNo, passNo);
                            }
                            else
                            {
                                var fv1 = JsonConvert.DeserializeObject<Model.EstPassInfor>(value);

                                IsSuccess = true;
                                string sCouponKey = string.Empty;
                                if (fv1.CouponKey != null && fv1.CouponKey.Count > 0)
                                {
                                    sCouponKey = fv1.CouponKey[0];
                                }

                                if (fv1.carInData != null)
                                {
                                    int r = BLL.BaseBLL._AddOrUpdateModel(fv1.carInData);
                                    if (r <= 0)
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"电动车车道扫码创建记录失败:{TyziTools.Json.ToString(fv1.carInData)}");
                                    }

                                    //BLL.MiddlewareApi.UpdateOrder(fv1.carInData);
                                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), fv1.carInData);

                                    data = new { parkOrderNo = fv1.OrderNo, chargeMoney = fv1.chargeMoney, couponKey = "" };

                                    //Task.Run(() =>
                                    //{
                                    //    try
                                    //    {
                                    //        var r = BLL.MiddlewareApi.UpdateOrder(fv1.carInData);
                                    //        if (!r.success)
                                    //        {
                                    //            LogManagementMap.WriteToFileException(null, $"{InstructionName}电动车车道扫码创建记录分发失败：{r.errmsg}，{orderNo}");
                                    //        }
                                    //    }
                                    //    catch (Exception e)
                                    //    {

                                    //        LogManagementMap.WriteToFileException(null, $"{InstructionName}电动车车道扫码创建记录分发失败：{e.ToString()}，{orderNo}");
                                    //    }
                                    //});
                                }
                                else
                                {
                                    data = new
                                    {
                                        parkOrderNo = fv1.OrderNo,
                                        chargeMoney = fv1.chargeMoney,
                                        couponKey = sCouponKey
                                    };
                                }

                                if (fv1.payResult != null && !string.IsNullOrEmpty(fv1.OrderNo))
                                {
                                    DataCache.AskPrice.Set("OrderPrice:" + fv1.OrderNo, passNo);
                                    if (fv1.payResult.list != null && fv1.payResult.list.Count > 0)
                                    {
                                        CalcCache.Set("OrderPrice2:" + fv1.OrderNo, fv1.payResult);
                                        CalcCache.Set("OrderPrice:" + fv1.OrderNo, fv1.payResult);
                                    }
                                    else
                                    {
                                        CalcCache.Del("OrderPrice2:" + fv1.OrderNo);
                                        CalcCache.Del("OrderPrice:" + fv1.OrderNo);
                                    }
                                }
                            }
                        }
                        else
                        {
                            IsSuccess = true;
                        }
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "获取停车订单信息不成功-" + sRemark);
                    }


                    #endregion
                }

                //应答数据
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1 /*IsSuccess ? 1 : 0*/, msg = sRemark, actionName = InstructionName, data = data }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = 0,
                    msg = $"{InstructionName}异常信息：{ex.Message}",
                    actionName = InstructionName
                }));
            }
        }
    }

    /// <summary>
    /// 优惠券下发通知指令处理类
    /// </summary>
    /// <summary>
    public class CouponPullHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CouponPull";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单
                int couponType = Convert.ToInt32(jo["couponType"]); //优惠券类型 101-优惠金额，102-优惠时间，103-优惠打折
                string couponValue = Convert.ToString(jo["couponValue"]); //优惠额度
                DateTime invalidTime = Convert.ToDateTime(jo["invalidTime"]); //过期时间
                decimal.TryParse(couponValue, out var deciValue);
                string couponId = Convert.ToString(jo["couponKey"]); //平台优惠券编号主键
                string merchantId = Convert.ToString(jo["merchantId"]); //发布优惠券的商户ID
                string merchantName = Convert.ToString(jo["merchantName"]); //发布优惠券的商户名称
                int couponStatus = Convert.ToInt32(jo["couponStatus"]); //优惠券的状态 1-未使用 0-已使用或无效 2-注销  SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));

                string allTimeFree = Convert.ToString(jo["allTimeFree"]); //非时段全免类型，该值为null
                string carno = "";//车牌号
                int availableLimit = 1;//可用次数限制
                DateTime startTime = default(DateTime), endTime = default(DateTime);//优惠券方案的开始时间、优惠券方案的结束时间

                if (couponType == (int)Common.EnumCouponType.AppointHour && !string.IsNullOrEmpty(allTimeFree))
                {
                    var couponTime = TyziTools.Json.ToObject<JObject>(allTimeFree);
                    if (couponTime == null)
                    {
                        SendTCPDatas(context, sendsession, false, "优惠券时间下发错误", InstructionName);
                        return Task.CompletedTask;
                    }

                    if (!DateTime.TryParse(couponTime["startTime"]?.ToString(), out startTime) ||
                        !DateTime.TryParse(couponTime["endTime"]?.ToString(), out endTime))
                    {
                        SendTCPDatas(context, sendsession, false, "优惠券时间格式错误", InstructionName);
                        return Task.CompletedTask;
                    }

                    // 安全提取其他字段
                    availableLimit = Utils.ObjectToInt(couponTime["availableLimit"]?.ToString(), 1);
                    carno = couponTime["carNo"]?.ToString();
                }

                if (string.IsNullOrEmpty(couponId)) couponId = "MD" + Utils.CreateNumber;

                //判断优惠类型
                if (couponType != (int)Common.EnumCouponType.Money && couponType != (int)Common.EnumCouponType.Discount && couponType != (int)Common.EnumCouponType.Time
                    && couponType != (int)Common.EnumCouponType.AppointHour)
                {
                    SendTCPDatas(context, sendsession, false, "优惠券类型错误", InstructionName);
                    return Task.CompletedTask;
                }

                List<Model.ParkOrder> orderList = null;
                List<Model.CouponRecord> CancelRecordList = null;
                List<Model.CouponRecord> RegRecordList = null;
                Model.CouponPlan plan = null;

                int? CouponRecord_MerchantId = null;
                CouponRecord_MerchantId = string.IsNullOrEmpty(merchantId) ? CouponRecord_MerchantId : Utils.ObjectToInt(merchantId, 0);

                //查询要注销的优惠券
                if (couponStatus == 2)
                {
                    if (couponType == 104)
                    {
                        CancelRecordList = BLL.CouponRecord.GetAllEntity("*", $"CouponRecord_ParkDiscountSetNo='{couponId}'");
                    }
                    else
                    {
                        var record = BLL.CouponRecord._GetEntityByNo(new Model.CouponRecord(), couponId);
                        if (record != null) CancelRecordList = new List<CouponRecord>() { record };
                    }

                    if (CancelRecordList != null) orderNo = CancelRecordList.FirstOrDefault()?.CouponRecord_ParkOrderNo;

                    CancelRecordList?.ForEach(item =>
                    {
                        item.CouponRecord_Status = 2;
                    });
                }

                //注销优惠券 或 下发订单号绑定优惠券
                if (!string.IsNullOrEmpty(orderNo) || (couponStatus == 2 && couponType == 104))
                {
                    if (couponStatus == 2 && couponType == 104)
                    {
                        List<string> ordernoes = CancelRecordList?.FindAll(x => !string.IsNullOrEmpty(x.CouponRecord_ParkOrderNo)).Select(x => x.CouponRecord_ParkOrderNo).ToList();
                        if (ordernoes.Count > 0)
                        {
                            ordernoes.Distinct();
                            orderList = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_No in @ParkOrder_No", parameters: new { ParkOrder_No = ordernoes });
                        }
                    }
                    else
                    {
                        //查询停车订单
                        var order = BLL.ParkOrder.GetEntity(orderNo);
                        if (order != null)
                        {
                            orderList = new List<ParkOrder>() { order };
                        }
                    }

                    if (couponStatus != 2 && (orderList?.Count ?? 0) == 0)
                    {
                        SendTCPDatas(context, sendsession, false, "未找到停车订单信息", InstructionName);
                        return Task.CompletedTask;
                    }
                }

                //组装优惠券并且订单绑定该优惠券
                orderList?.ForEach(order =>
                {
                    if (couponStatus != 2)
                    {
                        //组装优惠券
                        Model.CouponRecord model = null;
                        if (!string.IsNullOrEmpty(orderList?.FirstOrDefault()?.ParkOrder_No))
                        {
                            model = new CouponRecord();
                            model.CouponRecord_IssueCarNo = order.ParkOrder_CarNo;
                            model.CouponRecord_No = couponId;
                            if (couponType == (int)Common.EnumCouponType.AppointHour) model.CouponRecord_ParkDiscountSetNo = couponId;
                            model.CouponRecord_ParkNo = order.ParkOrder_ParkNo;
                            model.CouponRecord_Status = couponStatus == 1 ? 0 : (couponStatus == 0 ? 1 : 2);
                            model.CouponRecord_Value = Utils.StrToDecimal(couponValue, 0);
                            model.CouponRecord_CouponCode = (couponType == ((int)Common.EnumCouponType.AppointHour) ? ((int)Common.EnumCouponType.HourFree).ToString() : couponType.ToString());
                            model.CouponRecord_ParkNo = order.ParkOrder_ParkNo;
                            model.CouponRecord_ParkOrderNo = order.ParkOrder_No;
                            model.CouponRecord_VaildTime = invalidTime;
                            model.CouponRecord_AddTime = DateTimeHelper.GetNowTime();
                            model.CouponRecord_OnLine = 1;
                            model.CouponRecord_MerchantId = CouponRecord_MerchantId;
                            model.CouponRecord_MerchantName = merchantName;
                            model.CouponRecord_StartTime = order.ParkOrder_EnterTime;
                        }

                        if (model != null)
                        {
                            if (RegRecordList == null) RegRecordList = new List<CouponRecord>();
                            RegRecordList.Add(model);
                        }
                    }

                    if (order != null)
                    {
                        if (couponStatus == 1)
                        {
                            order.ParkOrder_CouponNum = order.ParkOrder_CouponNum ?? 0;
                            order.ParkOrder_CouponNum += 1;
                        }
                        else if (couponStatus != 0)
                        {
                            order.ParkOrder_CouponNum = order.ParkOrder_CouponNum ?? 0;
                            order.ParkOrder_CouponNum -= 1;
                            if (order.ParkOrder_CouponNum < 0) order.ParkOrder_CouponNum = 0;
                        }
                    }

                });

                //存在注销优惠券
                if (CancelRecordList != null) RegRecordList = CancelRecordList;

                //优惠计划
                if (couponType == (int)Common.EnumCouponType.AppointHour)
                {
                    //新增优惠方案
                    if (couponStatus != 2)
                    {
                        if (!string.IsNullOrEmpty(allTimeFree))
                        {
                            var model = RegRecordList?.FirstOrDefault();
                            var checkOne = !string.IsNullOrEmpty(orderList?.FirstOrDefault().ParkOrder_No) && (availableLimit > 1 || availableLimit == 0);
                            var checkTwo = string.IsNullOrEmpty(orderList?.FirstOrDefault().ParkOrder_No);
                            if (checkOne || checkTwo)
                            {
                                plan = new Model.CouponPlan();
                                plan.CouponPlan_No = couponId;
                                plan.CouponPlan_IssueCarNo = carno;
                                plan.CouponPlan_MerchantId = CouponRecord_MerchantId;
                                plan.CouponPlan_MerchantName = merchantName;
                                plan.CouponPlan_StartTime = startTime;
                                plan.CouponPlan_EndTime = endTime;
                                plan.CouponPlan_VaildTime = endTime;
                                plan.CouponPlan_Value = deciValue;
                                plan.CouponPlan_UseCount = checkOne ? 1 : 0;
                                plan.CouponPlan_MaxCount = availableLimit;
                                plan.CouponPlan_AddTime = DateTime.Now;
                                plan.CouponPlan_Status = couponStatus == 2 ? 2 : 1;
                            }

                            if (model != null && model.CouponRecord_StartTime != null)
                            {
                                // 优化时间判断逻辑
                                var now = model.CouponRecord_StartTime.Value;//车辆入场时间
                                var couponEndTime = endTime;
                                DateTime calculatedEnd = DateTime.MinValue;

                                // ✅ 优先判断 endTime 是否过期
                                bool endTimeExpired = couponEndTime < now;

                                if (!endTimeExpired)
                                {
                                    // ⏱ 如果 endTime 未过期，才判断 startTime 是否早于当前时间
                                    if (startTime < now)
                                        startTime = now;

                                    // ✅ 如果有 couponValue，计算优惠券结束时间
                                    if (deciValue > 0)
                                    {
                                        calculatedEnd = startTime.AddMinutes((double)deciValue);
                                        // 限制 endTime 不超过原 endTime
                                        couponEndTime = couponEndTime < calculatedEnd ? couponEndTime : calculatedEnd;
                                    }
                                    model.CouponRecord_StartTime = startTime;
                                    model.CouponRecord_EndTime = couponEndTime;

                                    if (plan != null && plan.CouponPlan_Value > 0 && calculatedEnd != DateTime.MinValue && plan.CouponPlan_VaildTime > calculatedEnd)
                                    {
                                        plan.CouponPlan_VaildTime = calculatedEnd;
                                    }
                                }
                                else
                                {
                                    //已过期
                                    model = null;
                                }
                            }
                        }
                    }
                    //注销优惠方案
                    else
                    {
                        plan = new CouponPlan();
                        plan.CouponPlan_No = couponId;
                        plan.CouponPlan_Status = 2;
                    }
                }

                var result = BLL.BaseBLL._AddOrUpdateModel(RegRecordList, orderList, plan);
                if (result > 0)
                {
                    if (couponType == (int)Common.EnumCouponType.AppointHour)
                    {
                        var end = endTime;
                        if (AppBasicCache.CurrentSysConfigContent.SysConfig_TimeCouponValidity == null ||
                            AppBasicCache.CurrentSysConfigContent.SysConfig_TimeCouponValidity < end
                           )
                        {
                            AppBasicCache.CurrentSysConfigContent.SysConfig_TimeCouponValidity = end;
                            AppCache.GetSysConfigContent = AppBasicCache.CurrentSysConfigContent;
                            AppBasicCache.CurrentSysConfig.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(AppBasicCache.CurrentSysConfigContent));
                            var res = BLL.BaseBLL._Insert(AppBasicCache.CurrentSysConfig);
                            if (res < 0)
                            {
                                BLL.UserLogs.AddLog(null, LogEnum.Middleware, SecondOption.Update, $"更新时段全免优惠有效期 {endTime} 失败", SecondIndex.SystemSetting);
                            }
                        }
                    }
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (RegRecordList, orderList), "addcarcoupon");
                    //var r = BLL.MiddlewareApi.CouponPull(model, order);
                    //if (!r.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}优惠券下发分发失败：{r.errmsg}，{orderNo}-{couponType}-{couponValue}");
                    //}

                    SendTCPDatas(context, sendsession, true, $"优惠券{(couponStatus != 2 ? "下发成功" : "注销成功")}", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}优惠券下发执行SQL错误：{TyziTools.Json.ToString(RegRecordList)}");
                    SendTCPDatas(context, sendsession, false, $"优惠券{(couponStatus != 2 ? "下发失败" : "注销失败")}", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// [充电桩]优惠券下发通知指令处理类
    /// </summary>
    /// <summary>
    public class addChargeDiscountRecordHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "addChargeDiscountRecord";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                var data = jo.ToObject<addChargeDiscountRecordParam>();
                int couponType = 0;
                if (data.discountRecordType == "1") couponType = (int)Common.EnumCouponType.Time;
                if (data.discountRecordType == "2") couponType = (int)Common.EnumCouponType.Money;
                if (data.discountRecordType == "4") couponType = (int)Common.EnumCouponType.Discount;

                //判断优惠类型
                if (couponType != (int)Common.EnumCouponType.Money &&
                    couponType != (int)Common.EnumCouponType.Discount &&
                    couponType != (int)Common.EnumCouponType.Time)
                {
                    SendTCPDatas(context, sendsession, false, "优惠券类型错误", InstructionName);
                    return Task.CompletedTask;
                }

                //查询停车订单
                Model.ParkOrder order = BLL.ParkOrder.GetParkOrderByCarNo(data.carNo);
                if (order == null)
                {
                    SendTCPDatas(context, sendsession, false, "未找到停车订单信息", InstructionName);
                    return Task.CompletedTask;
                }

                //组装优惠券
                Model.CouponRecord model = new CouponRecord();
                model.CouponRecord_IssueCarNo = order.ParkOrder_CarNo;
                model.CouponRecord_No = data.discountRecordNo;
                model.CouponRecord_ParkNo = order.ParkOrder_ParkNo;
                model.CouponRecord_Status = 0;
                model.CouponRecord_Value = Utils.StrToDecimal(data.discountRecordResult, 0);
                model.CouponRecord_CouponCode = couponType.ToString();
                model.CouponRecord_ParkNo = order.ParkOrder_ParkNo;
                model.CouponRecord_ParkOrderNo = order.ParkOrder_No;
                model.CouponRecord_VaildTime = Utils.StrToDateTime(data.chargeOrderEndTime).AddYears(1);
                model.CouponRecord_AddTime = DateTimeHelper.GetNowTime();
                model.CouponRecord_OnLine = 1;
                model.CouponRecord_Other = 1;
                model.CouponRecord_Remark = $"充电时间：{data.chargeOrderStartTime} 至 {data.chargeOrderEndTime}";

                if (model.CouponRecord_Status == 0)
                {
                    order.ParkOrder_CouponNum = order.ParkOrder_CouponNum ?? 0;
                    order.ParkOrder_CouponNum += 1;
                }

                var result = BLL.BaseBLL._AddOrUpdateModel(model, order);
                if (result > 0)
                {
                    //var r = BLL.MiddlewareApi.CouponPull(model, order);
                    //if (!r.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}优惠券下发分发失败：{r.errmsg}，{order.ParkOrder_No}-{couponType}-{data.discountRecordResult}");
                    //}
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (new List<Model.CouponRecord> { model }, new List<Model.ParkOrder> { order }), "addcarcoupon");

                    SendTCPDatas(context, sendsession, true, "优惠券下发成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}优惠券下发执行SQL错误：{TyziTools.Json.ToString(model)}");
                    SendTCPDatas(context, sendsession, false, "优惠券下发失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }

        public class addChargeDiscountRecordParam
        {
            public string version { get; set; }
            public string actionName { get; set; }
            public string key { get; set; }
            public string carNo { get; set; }

            /// <summary>
            /// 充电开始时间
            /// </summary>
            public string chargeOrderStartTime { get; set; }

            /// <summary>
            /// 充电结束时间
            /// </summary>
            public string chargeOrderEndTime { get; set; }

            /// <summary>
            /// 减免编号
            /// </summary>
            public string discountRecordNo { get; set; }

            /// <summary>
            /// 减免类型  1减免时长  2减免金额
            /// </summary>
            public string discountRecordType { get; set; }

            /// <summary>
            /// 优惠结果(分钟/元)
            /// </summary>
            public string discountRecordResult { get; set; }
        }
    }

    /// <summary>
    /// 识别区域下发通知指令处理类
    /// </summary>
    public class OnlineRecognAreaHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OnlineRecognArea";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;
                bool IsSuccess = false;

                string vlvideoIp = Convert.ToString(jo["vlvideoIp"]); //相机IP地址
                string pointA = Convert.ToString(jo["pointA"]); //坐标点AX
                string pointB = Convert.ToString(jo["pointB"]); //坐标点BX
                string pointC = Convert.ToString(jo["pointC"]); //坐标点CX
                string pointD = Convert.ToString(jo["pointD"]); //坐标点DX

                var devices = BLL.Device.GetAllEntityExt("*", $"Device_Category='1' and Device_IP ='{vlvideoIp}'  and  Device_Account!='' and Device_Account is not null and Device_Pwd !='' and Device_Pwd is not null");
                if (devices == null || devices.Count <= 0)
                {
                    sRemark = "未找到车道识别相机设备！";
                }
                else
                {
                    PingReply reply = new Ping().Send(vlvideoIp, 120);
                    if (reply.Status == IPStatus.Success)
                    {
                        IsSuccess = CamerasCaptureUtil.SetCameraPotion(devices[0].Device_IP, devices[0].Device_Account, devices[0].Device_Pwd, pointA, pointB, pointC, pointD);
                        if (IsSuccess)
                        {
                            sRemark = "设置相机识别区域成功！";
                        }
                        else
                        {
                            sRemark = "设置相机识别区域失败！";
                        }
                    }
                    else
                    {
                        sRemark = "相机连接不通!";
                    }
                }


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 删除场内记录推送到线下指令处理类
    /// </summary>
    public class PullDeleteOrderHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullDeleteOrder";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单

                if (string.IsNullOrEmpty(orderNo))
                {
                    SendTCPDatas(context, sendsession, false, "停车订单编号不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                Model.ParkOrder po = BLL.ParkOrder.GetEntity(orderNo);
                if (po == null)
                {
                    SendTCPDatas(context, sendsession, false, "未找到停车订单信息", InstructionName);
                    return Task.CompletedTask;
                }

                po.ParkOrder_StatusNo = (int)ParkOrderStatusEnum.InClose; //不真的删除订单，这里执行关闭订单操作
                po.ParkOrder_Remark = "平台关闭订单";

                List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity(po.ParkOrder_No);
                details?.ForEach(x =>
                {
                    x.OrderDetail_StatusNo = (int)ParkOrderStatusEnum.InClose;
                    x.OrderDetail_Remark = "平台关闭订单";
                });

                var result = BLL.OrderDetail.UpdateByList(new List<Model.ParkOrder>() { po }, details);
                if (result)
                {
                    //var r = BLL.MiddlewareApi.UpdateOrderDetail(po, details);
                    //if (!r.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}关闭场内分发失败：{r.errmsg}，{orderNo}");
                    //}
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (po, details), "updateorder");

                    SendTCPDatas(context, sendsession, true, "关闭场内记录成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}关闭场内记录执行SQL错误：订单号--{orderNo}");
                    SendTCPDatas(context, sendsession, false, "关闭场内记录失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }


    /// <summary>
    /// [充电桩]查询场内车辆指令处理类
    /// </summary>
    public class CarNoStatusHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CarNoStatus";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            //多指令先获取指令接收的指令名称
            string sActionName = jo.ContainsKey("actionName") ? Convert.ToString(jo["actionName"]) : InstructionName;

            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string parkkey = Convert.ToString(jo["key"]); //车场key
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                if (string.IsNullOrEmpty(parkkey))
                {
                    SendTCPDatas(context, sendsession, IsSuccess, "车场key不能为空", sActionName);
                    return Task.CompletedTask;
                }

                if (string.IsNullOrEmpty(carNo))
                {
                    SendTCPDatas(context, sendsession, IsSuccess, "车牌号码不能为空", sActionName);
                    return Task.CompletedTask;
                }

                Model.Parking parking = BLL.Parking.GetEntityByKey(parkkey);
                if (parking == null)
                {
                    SendTCPDatas(context, sendsession, IsSuccess, "车场信息未找到", sActionName);
                    return Task.CompletedTask;
                }

                //查询停车订单
                Model.ParkOrder po = BLL.ParkOrder.GetParkOrderByCarNo(carNo);
                if (po == null)
                {
                    SendTCPDatas(context, sendsession, IsSuccess, "停车订单未找到", sActionName);
                    return Task.CompletedTask;
                }

                //计费结果重组
                CarOrder carObj = new CarOrder();
                carObj.EnterTime = po.ParkOrder_EnterTime != null ? po.ParkOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "";
                carObj.CarNum = carNo;
                carObj.ParkCode = parkkey;

                SendTCPDatas(context, sendsession, true, "查询成功", sActionName, carObj, false);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// 车辆收费价格附加数据
        /// </summary>
        public class CarOrder
        {
            /// <summary>
            /// 车场key
            /// </summary>
            public string ParkCode { get; set; }

            /// <summary>
            /// 车牌号码
            /// </summary>
            public string CarNum { get; set; }

            /// <summary>
            /// 入场时间
            /// </summary>
            public string EnterTime { get; set; }
        }
    }


    /// <summary>
    /// 重启电脑指令处理类
    /// </summary>
    public class RestartClientHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "RestartClient";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;
                bool IsSuccess = false;
                if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    ProcessStartInfo ps = new ProcessStartInfo
                    {
                        FileName = "shutdown.exe",
                        Arguments = "-r -t 10"
                    };
                    Process.Start(ps);
                    IsSuccess = true;
                    sRemark = "已经执行重启电脑操作，10秒后重启电脑！";
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    sRemark = "系统 不支持重启";
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 停止中间件指令处理类
    /// </summary>
    public class StopMiddlewareHandle : TcpHandleBase
    {
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "StopMiddleware,UnActivated";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                //多指令先获取指令接收的指令名称
                string sActionName = jo.ContainsKey("actionName") ? Convert.ToString(jo["actionName"]) : InstructionName;
                string sContent = JsonConvert.SerializeObject(new { actionName = sActionName, msg = "已接收停止中间件命令!", result = 1 });
                SendTCPDatas(context, sendsession, sContent);

                // 检查是否是UnActivated反激活指令
                if (sActionName == "UnActivated")
                {
                    // 获取反激活指令中的车场key
                    string deactivatedParkKey = Convert.ToString(jo["key"]);

                    string currentParkKey = AppCache.GetParking?.Parking_Key ?? "";

                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,
                        $"[UnActivated] 接收到云平台反激活指令，目标车场Key: {deactivatedParkKey}, 当前车场Key: {currentParkKey}");

                    // 只有当反激活的车场key与当前车场key匹配时，才清除缓存
                    if (!string.IsNullOrEmpty(deactivatedParkKey) &&
                        !string.IsNullOrEmpty(currentParkKey) &&
                        deactivatedParkKey.Equals(currentParkKey, StringComparison.OrdinalIgnoreCase))
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,
                            $"[UnActivated] 车场Key匹配，清除注册缓存状态");
                        // 清除云停车场注册成功状态和缓存，确保下次重连时必须重新注册
                        CloudTask.ClearCloudParkRegistrationCache(deactivatedParkKey);
                        // 清除CloudApi中的注册缓存
                        BLL.CloudApi.ClearRegistrationCache(deactivatedParkKey);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,
                            $"[UnActivated] 车场Key不匹配或为空，忽略反激活指令");
                    }
                }

                await CloudTask.OnStop();
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
            }
        }
    }

    /// <summary>
    /// 缴费记录下发通知指令处理类
    /// </summary>
    /// <summary>
    public class OnlinePaymentHandle : TcpHandleBase
    {
        /// <summary>
        /// 缓存支付记录，防止重复插入支付记录
        /// </summary>
        private static ConcurrentDictionary<string, int> OrderValuePairs = new ConcurrentDictionary<string, int>();

        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OnlinePayment,OfflinePayment";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            //多指令先获取指令接收的指令名称
            string sActionName = jo.ContainsKey("actionName") ? Convert.ToString(jo["actionName"]) : InstructionName;
            try
            {
                string sRemark = string.Empty;
                string payOrderNo = Convert.ToString(jo["payOrderNo"]); //缴费交易支付订单号
                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单
                decimal paidAmount = Convert.ToDecimal(jo["paidAmount"]); //平台缴费应支付金额
                decimal receAmount = Convert.ToDecimal(jo["receAmount"]); //平台缴费已支付金额
                string payTime = Convert.ToString(jo["payTime"]); //支付时间
                string paytype = Convert.ToString(jo["payType"]); //支付类型代码，详细看支付类型对应参数
                string couponKey = jo.ContainsKey("couponKey") ? Convert.ToString(jo["couponKey"]) : ""; //平台优惠券标识键值
                string payScene = Convert.ToString(jo["payScene"]); //支付场所 2-车道扫码，1-扫场内支付,3-建行,4-招行
                string couponList = jo.ContainsKey("couponList") ? Convert.ToString(jo["couponList"]) : ""; //平台优惠券集合
                string ctlNo = jo.ContainsKey("ctlNo") ? Convert.ToString(jo["ctlNo"]) : ""; //机号
                string orderType = jo.ContainsKey("orderType") ? Convert.ToString(jo["orderType"]) : ""; //订单类型，5909-追缴订单
                string appendPayOrderNo = jo.ContainsKey("appendPayOrderNo") ? Convert.ToString(jo["appendPayOrderNo"]) : ""; //合并追缴费用，合并了其它订单，订单编号数据字符串

                if (string.IsNullOrEmpty(orderNo))
                {
                    SendTCPDatas(context, sendsession, false, "停车订单编号不能为空", sActionName);
                    return;
                }

                if (!string.IsNullOrEmpty(payOrderNo))
                {
                    var cachePayOrderNo = DataCache.PayOrder.Get("Online:" + payOrderNo);
                    if (!string.IsNullOrEmpty(cachePayOrderNo))
                    {
                        SendTCPDatas(context, sendsession, true, "缴费记录下发成功(重复订单号)", sActionName);
                        return;
                    }
                }

                List<string> appendPayorder = null;
                if (!string.IsNullOrEmpty(appendPayOrderNo))
                {
                    appendPayorder = TyziTools.Json.ToObject<List<string>>(appendPayOrderNo);
                }

                //查询订单
                Model.ParkOrder po = BLL.ParkOrder.GetEntity(orderNo);
                if (po == null)
                {
                    var passResule = BLL.ConfirmRelease.Results.FirstOrDefault(item => item.Value?.resorder?.resOut?.parkorder?.ParkOrder_No == orderNo
                                                                                || item.Value?.resorder?.resOut?.noRecordOrder?.ParkOrder_No == orderNo);
                    if (passResule.Value != null)
                    {
                        po = passResule.Value?.resorder?.resOut?.parkorder ?? passResule.Value?.resorder?.resOut?.noRecordOrder;
                    }

                    if (po == null)
                    {
                        SendTCPDatas(context, sendsession, false, "未找到停车订单信息", sActionName);
                        return;
                    }
                }

                Model.Passway passway = null;
                if (!string.IsNullOrEmpty(ctlNo))
                {
                    passway = BLL.Passway._GetEntityByNo(new Model.Passway { }, ctlNo);
                    if (passway == null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "OnlinePayment-未找到车道信息：" + ctlNo);
                    }
                }

                //招行无牌车出场缴费下发，由中间件直接开闸并出场回调
                if (payScene == "4" && passway != null)
                {
                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = po.ParkOrder_ParkNo,
                        PasswayNo = passway.Passway_No,
                        SentryHostNo = passway.Passway_SentryHostNo,
                        DataType = "云平台-招行无牌车出场开闸",
                        BaseData = null,
                        SendOptions = Model.API.SendOptionsType.OpenGate
                    };
                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                    string retSuccess = ""; string retValue = ""; bool IsSuccess = false;
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    if (retSuccess.ToLower().Contains("true")) //开闸
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"招行支付-车辆出场开闸成功-{po.ParkOrder_CarNo}," + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"招行支付-车辆出场开闸不成功-{po.ParkOrder_CarNo}," + sRemark);
                    }
                }

                //查询停车明细
                List<Model.OrderDetail> orderDetailList = BLL.OrderDetail.GetAllEntity(po.ParkOrder_No);

                int? ps = null;
                if (payScene == "2" || payScene == "4") ps = 2;
                else if (payScene == "1") ps = 1;

                po.ParkOrder_PayScene = ps;
                po.ParkOrder_TotalAmount = po.ParkOrder_TotalAmount ?? 0;

                Model.ControlEvent cev = null;
                //如果是跟车出场，修改跟车出场订单状态
                if (po.ParkOrder_StatusNo == EnumParkOrderStatus.Follow || po.ParkOrder_StatusNo == EnumParkOrderStatus.Close)
                {
                    var cevList = BLL.ControlEvent.GetAllEntityByOrderNo(po.ParkOrder_No);
                    if (cevList != null && cevList.Count > 0)
                    {
                        cev = cevList.Find(x => x.ControlEvent_Gate == 0 && x.ControlEvent_Money > 0);
                        if (cev == null) cev = cevList.Find(x => x.ControlEvent_Money > 0);
                        if (cev == null) cev = cevList.FirstOrDefault();
                    }
                    if (po.ParkOrder_StatusNo != EnumParkOrderStatus.Close) po.ParkOrder_StatusNo = EnumParkOrderStatus.Out;
                    if (cev != null) cev.ControlEvent_Status = 4;
                }

                DateTime datePayTime = Utils.StrToDateTime(payTime);
                DateTime? outTime = po.ParkOrder_OutTime == null ? datePayTime : po.ParkOrder_OutTime;
                //是否更新计费使用的优惠券
                bool checkCoupon = true;

                //查询车辆信息、车牌类型、车牌颜色
                Model.CarCardType cct = null;
                //Model.CarType ct = null;
                Model.Car car = BLL.Car.GetEntityByCarNo(po.ParkOrder_CarNo);
                Model.Owner owner = BLL.Owner.GetEntity(car?.Car_OwnerNo);
                if (car != null)
                {
                    cct = BLL.CarCardType.GetEntity(car.Car_TypeNo);
                    //ct = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                }

                //获取本地计费结果缓存
                //ChargeModels.PayResult payResult = paytype != Convert.ToString(EnumPayType.WeixinNonInductive) ? CalcCache.Get($"OrderPrice{payScene}:" + orderNo) : null;
                //if (paytype != Convert.ToString(EnumPayType.WeixinNonInductive) && payResult == null && (payScene == "3" || payScene == "4"))
                //{
                //    payResult = CalcCache.Get("OrderPrice:" + orderNo);
                //}
                bool appendUnpaidOrder = false;
                if (orderType == "5909" || !string.IsNullOrEmpty(payOrderNo) && payOrderNo.ToLower().StartsWith("ap"))
                {
                    appendUnpaidOrder = true;
                }

                ChargeModels.PayResult payResult = CalcCache.Get($"OrderPrice{payScene}:" + orderNo);
                #region **计费结果为空，则重新计费，用来结算订单状态
                if ((payResult == null || payResult.list == null) && po.ParkOrder_IsNoInRecord == 0)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{InstructionName} 订单[{orderNo}]无计费结果缓存信息");
                    var orderprices = BLL.BaseBLL._GetAllEntity(new Model.OrderPrice(), "*", $"OrderPrice_ParkOrderNo='{orderNo}' and OrderPrice_CalcTime<'{datePayTime.AddSeconds(1).ToString("yyyy-MM-dd HH:mm:ss")}'");
                    if (orderprices.Count > 0)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{InstructionName}订单[{orderNo}]获取计费条件详情");
                        var orderprice = orderprices
                                           .Where(x => string.IsNullOrEmpty(ctlNo) || x.OrderPrice_OutPasswayNo == ctlNo)
                                           .OrderBy(x => x.OrderPrice_CalcTime)
                                           .LastOrDefault();

                        if (orderprice != null)
                        {
                            var orderresult = BLL.BaseBLL._GetEntityByNo(new Model.OrderResult(), orderprice.OrderPrice_No);

                            bool checkDBResult = false;
                            if (orderresult != null)
                            {
                                var orderresult_paydata = TyziTools.Json.ToObject<ChargeModels.PayResult>(HttpUtility.UrlDecode(orderresult.OrderResult_PayData));
                                if (orderresult_paydata != null)
                                {

                                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{InstructionName}订单[{orderNo}]已查询到历史计费结果");
                                    payResult = orderresult_paydata;
                                    checkDBResult = true;
                                }
                            }

                            if (!checkDBResult)
                            {
                                Model.Car car1 = BLL.Car.GetEntityByCarNo(po.ParkOrder_CarNo);
                                //获取计费结果
                                List<Model.CouponRecordIntExt> orderCouponList = null;
                                ChargeModels.PayResult result1 = Calc.GetChargeByMin(po.Copy(), orderprice.OrderPrice_CalcTime, orderprice.OrderPrice_CouponMin, car1,
                                    out orderCouponList, true, "", "", string.IsNullOrEmpty(orderprice.OrderPrice_OutPasswayNo) ? false : true, orderprice.OrderPrice_FllowCar == 1 ? true : false,
                                    allTimeFree: orderprice.OrderPrice_AllTimeFree);
                                if (result1 != null && result1.payed == 1)
                                {
                                    var useMin = result1.uselist?.Sum(x => x.CouponRecord_DiscountMin) ?? 0;//优惠分钟
                                    if (result1.payedamount == orderprice.OrderPrice_PayedMoney && result1.couponamount == orderprice.OrderPrice_CouponMoney
                                        && useMin == (orderprice.OrderPrice_UseMinute ?? 0))
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{InstructionName}订单[{orderNo}]已重新计费并且得到计费结果");
                                        payResult = result1;
                                    }
                                    else
                                    {
                                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{InstructionName}订单[{orderNo}]重新计费但计费结果差异");
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                if (payResult == null && (payScene == "3" || payScene == "4"))
                {
                    payResult = CalcCache.Get("OrderPrice:" + orderNo);
                }
                if (payResult == null && (paytype == EnumPayType.WeixinNonInductive.ToString() || paytype == EnumPayType.OtherPay.ToString()))//无感支付
                {
                    payResult = CalcCache.Get("OrderPriceAutoPay:" + orderNo);
                }

                ChargeModels.PayResult notModifiedResult = payResult != null ? DeepCloner.Clone(payResult) : null;
                string passWayNo = DataCache.AskPrice.Get("OrderPrice:" + orderNo);

                CalcCache.Del($"OrderPrice{payScene}:" + orderNo);
                CalcCache.Del($"OrderPrice:" + orderNo);
                CalcCache.Del($"OrderPriceAutoPay:" + orderNo);
                DataCache.AskPrice.Del("OrderPrice:" + orderNo);

                //修改了车牌颜色
                if (notModifiedResult?.changeCarType ?? false)
                {
                    var cartype = BLL.CarType.GetEntity(notModifiedResult.changeCarTypeNo);
                    if (cartype != null)
                    {
                        po.ParkOrder_CarType = cartype.CarType_No;
                        po.ParkOrder_CarTypeName = cartype.CarType_Name;
                    }
                }

                //支付订单集合
                List<Model.PayOrder> payModelList = new List<Model.PayOrder>();
                List<Model.PayPart> payPartList = new List<PayPart>();

                #region 线上优惠券处理

                List<string> onLineconpouNoList = new List<string>();
                List<Model.CouponRecord> couponPayList = null;
                List<Model.CouponRecord> onLineCouponPayList = null;
                decimal? sumCouponPaid = 0;
                if (!string.IsNullOrEmpty(couponList))
                {
                    List<JObject> joList = TyziTools.Json.ToModel<List<JObject>>(couponList);
                    if (joList != null && joList.Count > 0)
                    {
                        couponPayList = new List<CouponRecord>();
                        onLineCouponPayList = new List<CouponRecord>();

                        var coupons = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_No,CouponRecord_CouponCode,CouponRecord_Status,CouponRecord_UseCount", $"CouponRecord_CouponCode=105 AND CouponRecord_No IN ({string.Join(",", joList.Select(x => $"'{x["couponKey"].ToString()}'"))})");
                        foreach (var item in joList)
                        {
                            var couponno = item["couponKey"].ToString();
                            if (string.IsNullOrEmpty(couponno)) continue;

                            Model.CouponRecord crModel = new CouponRecord();
                            crModel.CouponRecord_No = item["couponKey"].ToString();
                            crModel.CouponRecord_Status = 1;
                            crModel.CouponRecord_Paid = Utils.ObjectToDecimal(item["paidMoney"].ToString(), 0);
                            crModel.CouponRecord_OnLine = 1;
                            crModel.CouponRecord_ParkOrderNo = po.ParkOrder_No;
                            crModel.CouponRecord_IssueCarNo = po.ParkOrder_CarNo;
                            if (crModel.CouponRecord_Paid < 0) crModel.CouponRecord_Paid = 0;
                            crModel.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();

                            var coupon = coupons.Where(x => x.CouponRecord_No == couponno).FirstOrDefault();
                            if (coupon != null)
                            {
                                if (crModel.CouponRecord_UseCount > 0)
                                {
                                    crModel.CouponRecord_UseCount = coupon.CouponRecord_UseCount - 1;
                                    if (crModel.CouponRecord_UseCount <= 0) crModel.CouponRecord_UseCount = -1;
                                }
                                if (coupon.CouponRecord_UseCount == -1)
                                {
                                    crModel.CouponRecord_Status = 1;
                                }
                                crModel.CouponRecord_Paid += Utils.ObjectToDecimal(item["paidMoney"].ToString(), 0);
                                if (crModel.CouponRecord_Paid < 0) crModel.CouponRecord_Paid = 0;
                            }
                            else
                            {
                                crModel.CouponRecord_Paid = Utils.ObjectToDecimal(item["paidMoney"].ToString(), 0);
                                if (crModel.CouponRecord_Paid < 0) crModel.CouponRecord_Paid = 0;
                            }
                            couponPayList.Add(crModel);
                            onLineCouponPayList.Add(crModel);
                            sumCouponPaid += crModel.CouponRecord_Paid;
                            onLineconpouNoList.Add(crModel.CouponRecord_No);
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(couponKey))
                {
                    couponPayList = new List<CouponRecord>();
                    onLineCouponPayList = new List<CouponRecord>();

                    Model.CouponRecord crModel = new CouponRecord();
                    crModel.CouponRecord_No = couponKey;
                    crModel.CouponRecord_Status = 1;
                    crModel.CouponRecord_OnLine = 1;
                    crModel.CouponRecord_Paid = paidAmount - receAmount;
                    crModel.CouponRecord_ParkOrderNo = po.ParkOrder_No;
                    crModel.CouponRecord_IssueCarNo = po.ParkOrder_CarNo;
                    if (crModel.CouponRecord_Paid < 0) crModel.CouponRecord_Paid = 0;
                    crModel.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                    couponPayList.Add(crModel);
                    onLineCouponPayList.Add(crModel);
                    sumCouponPaid += crModel.CouponRecord_Paid;
                    onLineconpouNoList.Add(crModel.CouponRecord_No);
                }

                #endregion

                #region 线上支付订单

                Model.PayOrder payModel = new PayOrder();
                payModel.PayOrder_No = string.IsNullOrEmpty(payOrderNo) ? null : payOrderNo;
                payModel.PayOrder_ParkOrderNo = orderNo;
                payModel.PayOrder_Money = paidAmount;
                payModel.PayOrder_Time = DateTimeHelper.GetNowTime();
                payModel.PayOrder_CarNo = po.ParkOrder_CarNo;
                payModel.PayOrder_Status = 1;
                payModel.PayOrder_PayedMoney = receAmount;
                payModel.PayOrder_PayedTime = Utils.StrToDateTime(payTime);
                payModel.PayOrder_PayTypeCode = paytype;
                payModel.PayOrder_PayType = 2;
                payModel.PayOrder_AdminID = 0;
                if (payScene == "1")//场内支付
                {
                    payModel.PayOrder_Account = "场内支付";
                    payModel.PayOrder_OperatorName = "场内支付";
                }
                payModel.PayOrder_UserNo = "";
                payModel.PayOrder_Desc = po.ParkOrder_IsNoInRecord == 1 ? "*无入场记录" : "";
                payModel.PayOrder_ParkKey = AppCache.GetParking.Parking_Key;
                payModel.PayOrder_ParkNo = AppCache.GetParking.Parking_No;
                payModel.PayOrder_OrderTypeNo = cct == null ? Convert.ToString((int)Common.EnumOrderType.Temp) : CarTypeHelper.GetOrderType(cct.CarCardType_Category, true);
                payModel.PayOrder_Category = cct != null ? cct.CarCardType_Category : "";
                payModel.PayOrder_EnterTime = po.ParkOrder_EnterTime;
                payModel.PayOrder_TempTimeCount = (int)(Math.Floor((outTime - po.ParkOrder_EnterTime).Value.TotalMinutes));
                payModel.PayOrder_TimeCountDesc = po.ParkOrder_IsNoInRecord != 1 ? Utils.DateDiffStr(payModel.PayOrder_TempTimeCount.Value * 60) : "*无入场记录";
                payModel.PayOrder_CarCardTypeNo = po.ParkOrder_CarCardType;
                payModel.PayOrder_CarTypeNo = po.ParkOrder_CarType;
                payModel.PayOrder_ParkAreaNo = po.ParkOrder_ParkAreaNo;
                payModel.PayOrder_CouponRecordNo = string.Join(",", onLineconpouNoList);
                payModel.PayOrder_OwnerSpace = car?.Car_OwnerSpace;
                payModel.PayOrder_PayScene = Utils.ObjectToInt(payScene, 0); //支付场所
                payModel.PayOrder_PassWayNo = string.IsNullOrEmpty(passWayNo) ? ctlNo : passWayNo;

                //属于追缴订单，不要管优惠金额扣减
                if (appendUnpaidOrder)
                {
                    payModel.PayOrder_Desc = "追缴金额";
                }
                else
                {
                    if (DataCache.AppendUnpaidOrderCache.Get(orderNo) == 1 || appendPayorder?.Count > 0)
                    {
                        //属于合并追缴金额一起支付的订单
                        payModel.PayOrder_Desc = $"合并追缴订单{(appendPayorder != null ? (":" + string.Join(",", appendPayorder)) : "")}";
                        appendUnpaidOrder = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{orderNo} 追缴金额合并订单：{appendPayOrderNo}");
                    }
                }
                payModel = BLL.PayOrder.CreatePayOrder(true, payModel, AppCache.GetParking.Parking_Key, appendUnpaidOrder: appendUnpaidOrder);
                if (payResult != null && payResult.carfeesamount > 0)
                {
                    payModel.PayOrder_Desc += $" 限额减免 {payResult.carfeesamount} 元";
                }

                if (appendUnpaidOrder)
                {
                    payModel.PayOrder_DiscountMoney = sumCouponPaid;
                }

                payModelList.Add(payModel);
                payPartList.AddRange(BLL.CommonBLL.CreatePayPartListByRatio(payResult?.list, receAmount, orderDetailList?.Copy(), payModel, po, appendUnpaidOrder: appendUnpaidOrder));

                #endregion

                //扣除应付金额，若还有余额，则是线下改价或线下优惠
                if (payResult != null)
                {
                    //针对线下弹窗0元，电子支付大于0元的情况，不更新优惠券的使用状态（由于车主领了券并且岗亭弹窗计费也更新了，但是还继续对优惠之前的费用进行支付，导致优惠券抵扣不正确）
                    if (paidAmount > 0 && payResult.payedamount == 0) { payResult.uselist = null; checkCoupon = false; }
                    payResult.orderamount = payResult.orderamount - paidAmount;
                    payResult.payedamount = payResult.payedamount - paidAmount;
                    if (payResult.payedamount < 0) payResult.payedamount = 0;
                }

                #region 计费结果=>线下支付订单生成、线下优惠券处理
                PayColl paySpacial = null;
                PayColl paycOLL = null;
                List<Model.CouponRecord> offLineCouponPayList = new List<CouponRecord>(); //重组线下优惠券集合
                List<Model.CouponRecord> SpacialCouponPayList = new List<CouponRecord>(); //特殊优惠券集合
                List<string> offLineconpouNoList = new List<string>();

                bool isChangeMoney = false;
                if (payResult != null)
                {

                    if ((payResult.payed == 1 || payResult.payed == 0) && (payResult.orderamount == 0 || payResult.orderamount - payResult.couponamount == 0))
                    {
                        //计费结果中是否还使用了线下优惠券，移除线上优惠后还有线下优惠，则创建新的支付订单并上报云平台
                        if (payResult.uselist?.Count > 0)
                        {
                            for (var i = 0; i < payResult.uselist.Count; i++)
                            {
                                var items = onLineCouponPayList?.Where(x => x.CouponRecord_No == payResult.uselist[i].CouponRecord_No);
                                if (items != null && items.Count() > 0)
                                {
                                    if (payResult.couponamount > 0)
                                    {
                                        payResult.couponamount -= payResult.uselist[i].DiscountMoney.Value;
                                    }

                                    payResult.uselist.RemoveAt(i);
                                }
                            }

                            //处理平台下发的优惠券
                            var useOnlineCoupons = payResult.uselist?.FindAll(x => x.CouponRecord_OnLine == 1);
                            if (useOnlineCoupons?.Count > 0)
                            {
                                List<Model.CouponRecord> orderCouponList = BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_ID,CouponRecord_No,CouponRecord_OnLine,CouponRecord_PreUse,CouponRecord_Paid", $"CouponRecord_ParkOrderNo='{orderNo}'");
                                List<Model.CouponRecord> onlineCouponList = orderCouponList.FindAll(x => x.CouponRecord_OnLine == 1 && useOnlineCoupons.Find(y => y.CouponRecord_No == x.CouponRecord_No) != null);
                                if (onlineCouponList != null && onlineCouponList.Count > 0)
                                {
                                    decimal onlineDiscountMoney = 0;
                                    bool hasOnlineCoupon = false;
                                    onlineCouponList.ForEach(x =>
                                    {
                                        if (!onLineconpouNoList.Contains(x.CouponRecord_No) && !offLineconpouNoList.Contains(x.CouponRecord_No))
                                        {
                                            x.CouponRecord_Status = 1;
                                            x.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                                            var userCoupon = payResult.uselist?.Find(m => m.CouponRecord_No == x.CouponRecord_No);
                                            if (userCoupon != null)
                                            {
                                                x.CouponRecord_Paid = userCoupon.DiscountMoney;
                                                onlineDiscountMoney += userCoupon.DiscountMoney.Value;
                                                payResult.uselist.Remove(userCoupon);
                                            }
                                            SpacialCouponPayList.Add(x);
                                            hasOnlineCoupon = true;
                                        }
                                    });

                                    if (hasOnlineCoupon)
                                    {
                                        //新建支付订单
                                        paySpacial = BLL.CommonBLL.AddPayOrder(outTime, payResult, orderNo, null, null, paytype, po.ParkOrder_ParkAreaNo, 3, Utils.StrToDateTime(payTime), 1, null, false, null, "", 0, 0, notModifiedResult);
                                        if (paySpacial != null)
                                        {
                                            paySpacial.payOrderList[0].PayOrder_CouponRecordNo = string.Join(",", SpacialCouponPayList.Select(a => a.CouponRecord_No));
                                        }
                                    }

                                    payResult.couponamount -= onlineDiscountMoney;
                                }

                            }

                            //处理线下勾选了优惠设置集合的优惠券
                            var useOffLineCoupons = payResult.uselist?.FindAll(x => x.CouponRecord_OnLine != 1);
                            if (useOffLineCoupons.Count > 0)
                            {
                                //线下优惠券
                                var orderOffLineCouponList = payResult.recordlist?.Where(a => a.CouponRecord_Set == 0 && !string.IsNullOrEmpty(a.CouponRecord_No) && a.CouponRecord_No.StartsWith("CF")).ToList();
                                var orderOffLineCouponUseList = payResult.uselist.Where(x => orderOffLineCouponList.Any(y => y.CouponRecord_No == x.CouponRecord_No)).ToList().Copy();
                                //线下优惠设置
                                List<ChargeModels.CouponRecordIntExt> chargeCouponList = payResult.recordlist?.Where(a => a.CouponRecord_Set == 1).ToList();
                                if (chargeCouponList.Count > 0)//存在优惠设置
                                {
                                    payResult.uselist = payResult.uselist.Where(x => chargeCouponList.Any(y => y.CouponRecord_No == x.CouponRecord_No)).ToList();
                                    if (payResult.uselist.Count > 0)//使用了优惠券设置
                                    {
                                        List<Model.CouponRecordIntExt> crieList = null;
                                        crieList = chargeCouponList == null ? null : TyziTools.Json.ToModel<List<Model.CouponRecordIntExt>>(TyziTools.Json.ToString(chargeCouponList));
                                        List<Model.CouponRecordIntExt> parkdiscountsetlist = crieList;

                                        //新建支付订单
                                        paycOLL = BLL.CommonBLL.AddPayOrder(outTime, payResult, orderNo, null, null, paytype, po.ParkOrder_ParkAreaNo, 2, Utils.StrToDateTime(payTime), 1, null, false, null, "", null, null, notModifiedResult);
                                        if (paycOLL != null)
                                        {
                                            if (paycOLL.payOrderList != null)
                                            {
                                                if (paycOLL.payOrderList.Count > 1 && paycOLL.payOrderList[1].PayOrder_Money == payResult.chuzhiamount)
                                                    paycOLL.payOrderList[1].PayOrder_PayTypeCode = EnumPayType.OffLineCash.ToString();
                                            }
                                        }

                                        //线下优惠券  
                                        if (paycOLL.payOrderList != null && paycOLL.payOrderList.Count > 0 && paycOLL.payOrderList[0].PayOrder_CouponRecordNo != null)
                                        {


                                            //线下优惠券集合遍历
                                            paycOLL.payOrderList[0]?.PayOrder_CouponRecordNo?.Split(",").ToList().ForEach(x =>
                                            {
                                                if (!string.IsNullOrEmpty(x))
                                                {
                                                    Model.CouponRecord item = new CouponRecord();

                                                    item.CouponRecord_Status = 1;
                                                    item.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                                                    item.CouponRecord_Paid = payResult.uselist?.Where(a => a.CouponRecord_No == x).FirstOrDefault()?.DiscountMoney;

                                                    //如果是优惠设置，则新建编码
                                                    Model.CouponRecordIntExt parkdiscountsetitem = parkdiscountsetlist?.Where(a => a.CouponRecord_No == x).FirstOrDefault();
                                                    if (parkdiscountsetitem != null)
                                                    {
                                                        item.CouponRecord_CouponCode = parkdiscountsetitem.CouponRecord_CouponCode;
                                                        item.CouponRecord_ParkOrderNo = po.ParkOrder_No;
                                                        item.CouponRecord_IssueCarNo = po.ParkOrder_CarNo;

                                                        item.CouponRecord_Value = parkdiscountsetitem.CouponRecord_Value;
                                                        item.CouponRecord_StartTime = DateTimeHelper.GetNowTime();
                                                        item.CouponRecord_EndTime = parkdiscountsetitem.CouponRecord_EndTime;

                                                        //string fixNo = "";
                                                        //if (!string.IsNullOrEmpty(item.CouponRecord_IssueCarNo))
                                                        //{
                                                        //    if (item.CouponRecord_IssueCarNo.Length > 1) fixNo = item.CouponRecord_IssueCarNo.Substring(1, item.CouponRecord_IssueCarNo.Length - 1);
                                                        //    else
                                                        //    {
                                                        //        fixNo = item.CouponRecord_IssueCarNo;
                                                        //    }

                                                        //    fixNo = "-" + fixNo;
                                                        //}

                                                        item.CouponRecord_No = "ET" + Utils.CreateNumber_SnowFlake; //新建优惠编码
                                                        item.CouponRecord_ParkNo = po.ParkOrder_ParkNo;
                                                    }
                                                    else
                                                    {
                                                        item.CouponRecord_No = x;
                                                    }

                                                    offLineCouponPayList.Add(item);
                                                }
                                            });
                                            paycOLL.payOrderList[0].PayOrder_CouponRecordNo = string.Join(",", offLineCouponPayList.Select(a => a.CouponRecord_No));
                                        }

                                        if (paycOLL.payOrderList != null) payModelList.AddRange(paycOLL.payOrderList);
                                        if (paycOLL.payPartList != null) payPartList.AddRange(paycOLL.payPartList);

                                        payResult.couponamount -= payResult.uselist.Sum(x => x.DiscountMoney.Value);
                                    }
                                }

                                //线下发的优惠券

                                if (payResult.couponamount > 0 && orderOffLineCouponUseList?.Count > 0)
                                {
                                    payResult.uselist = orderOffLineCouponUseList;
                                    //新建支付订单
                                    var paycOLL2 = BLL.CommonBLL.AddPayOrder(outTime, payResult, orderNo, null, null, paytype, po.ParkOrder_ParkAreaNo, 2, Utils.StrToDateTime(payTime), 1, null, false, null, "", null, null, notModifiedResult);
                                    if (paycOLL2 != null)
                                    {
                                        if (paycOLL2.payOrderList != null)
                                        {
                                            if (paycOLL2.payOrderList.Count > 1 && paycOLL2.payOrderList[1].PayOrder_Money == payResult.chuzhiamount)
                                                paycOLL2.payOrderList[1].PayOrder_PayTypeCode = EnumPayType.OffLineCash.ToString();
                                        }
                                    }

                                    //线下优惠券  
                                    if (paycOLL2.payOrderList != null && paycOLL2.payOrderList.Count > 0 && paycOLL2.payOrderList[0].PayOrder_CouponRecordNo != null)
                                    {
                                        //线下优惠券集合遍历
                                        paycOLL2.payOrderList[0]?.PayOrder_CouponRecordNo?.Split(",").ToList().ForEach(x =>
                                        {
                                            if (!string.IsNullOrEmpty(x))
                                            {
                                                Model.CouponRecord item = new CouponRecord();

                                                item.CouponRecord_Status = 1;
                                                item.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                                                item.CouponRecord_Paid = payResult.uselist?.Where(a => a.CouponRecord_No == x).FirstOrDefault()?.DiscountMoney;
                                                item.CouponRecord_No = x;
                                                offLineCouponPayList.Add(item);
                                            }
                                        });
                                        paycOLL2.payOrderList[0].PayOrder_CouponRecordNo = string.Join(",", offLineCouponPayList.Select(a => a.CouponRecord_No));
                                    }

                                    if (paycOLL2.payOrderList != null) payModelList.AddRange(paycOLL2.payOrderList);
                                    if (paycOLL2.payPartList != null) payPartList.AddRange(paycOLL2.payPartList);

                                }
                            }
                        }
                    }
                    else if (payResult.payed == 1 && payResult.orderamount > 0)
                    {

                        //计费结果中是否还使用了线下优惠券，移除线上优惠后还有线下优惠，则创建新的支付订单并上报云平台
                        if (payResult.uselist != null)
                        {
                            for (var i = 0; i < payResult.uselist.Count; i++)
                            {
                                var items = onLineCouponPayList?.Where(x => x.CouponRecord_No == payResult.uselist[i].CouponRecord_No);
                                if (items != null && items.Count() > 0)
                                {
                                    if (payResult.couponamount > 0)
                                    {
                                        payResult.couponamount -= payResult.uselist[i].DiscountMoney.Value;
                                    }

                                    payResult.uselist.RemoveAt(i);
                                }
                            }
                        }

                        //查询预使用的优惠券（智慧停车平台询价时，自动预使用线下的优惠券并标记出当前优惠价格关联的优惠预使用状态）
                        decimal? preUseCouponMoney = 0;
                        List<Model.CouponRecord> orderCouponList = checkCoupon ? BLL.CouponRecord._GetAllEntity(new Model.CouponRecord(), "CouponRecord_ID,CouponRecord_No,CouponRecord_OnLine,CouponRecord_PreUse,CouponRecord_Paid", $"CouponRecord_ParkOrderNo='{orderNo}'")?.FindAll(x => x.CouponRecord_PreUse == 1) : null;
                        if (orderCouponList != null)
                        {
                            orderCouponList.ForEach(x =>
                            {
                                if (!onLineconpouNoList.Contains(x.CouponRecord_No) && !offLineconpouNoList.Contains(x.CouponRecord_No))
                                {
                                    x.CouponRecord_Status = 1;
                                    x.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                                    preUseCouponMoney += Utils.ObjectToDecimal(x.CouponRecord_Paid, 0);
                                    sumCouponPaid += Utils.ObjectToDecimal(x.CouponRecord_Paid, 0);
                                    offLineconpouNoList.Add(x.CouponRecord_No);
                                    offLineCouponPayList.Add(x);
                                }
                            });
                        }

                        orderCouponList = orderCouponList?.FindAll(x => x.CouponRecord_PreUse == 1);
                        if (orderCouponList != null)
                        {
                            orderCouponList.ForEach(x =>
                            {
                                if (!onLineconpouNoList.Contains(x.CouponRecord_No) && !offLineconpouNoList.Contains(x.CouponRecord_No))
                                {
                                    x.CouponRecord_Status = 1;
                                    x.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                                    preUseCouponMoney += Utils.ObjectToDecimal(x.CouponRecord_Paid, 0);
                                    sumCouponPaid += Utils.ObjectToDecimal(x.CouponRecord_Paid, 0);
                                    offLineconpouNoList.Add(x.CouponRecord_No);
                                    offLineCouponPayList.Add(x);
                                }
                            });
                        }


                        isChangeMoney = true;
                        //新建支付订单
                        paycOLL = BLL.CommonBLL.AddPayOrder(outTime, payResult, orderNo, null, null, paytype, po.ParkOrder_ParkAreaNo, 2, datePayTime, 1, null, false, null, "", null, null, notModifiedResult);
                        if (paycOLL != null)
                        {
                            if (paycOLL.payOrderList != null)
                            {
                                if (paycOLL.payOrderList.Count > 1 && paycOLL.payOrderList[1].PayOrder_Money == payResult.chuzhiamount)
                                    paycOLL.payOrderList[1].PayOrder_PayTypeCode = EnumPayType.OffLineCash.ToString();
                            }
                        }

                        //线下优惠券  
                        if (paycOLL != null && paycOLL.payOrderList != null && paycOLL.payOrderList.Count > 0 && paycOLL.payOrderList[0].PayOrder_CouponRecordNo != null)
                        {
                            //优惠设置集合   
                            List<Model.CouponRecordIntExt> crieList = null;
                            List<ChargeModels.CouponRecordIntExt> chargeCouponList = payResult.recordlist?.Where(a => a.CouponRecord_Set == 1).ToList();
                            crieList = chargeCouponList == null ? null : TyziTools.Json.ToModel<List<Model.CouponRecordIntExt>>(TyziTools.Json.ToString(chargeCouponList));
                            List<Model.CouponRecordIntExt> parkdiscountsetlist = crieList;

                            //线下优惠券集合遍历
                            paycOLL.payOrderList[0]?.PayOrder_CouponRecordNo?.Split(",").ToList().ForEach(x =>
                            {
                                if (!string.IsNullOrEmpty(x))
                                {
                                    Model.CouponRecord item = new CouponRecord();

                                    item.CouponRecord_Status = 1;
                                    item.CouponRecord_UsedTime = DateTimeHelper.GetNowTime();
                                    item.CouponRecord_Paid = payResult.uselist?.Where(a => a.CouponRecord_No == x).FirstOrDefault()?.DiscountMoney;

                                    //如果是优惠设置，则新建编码
                                    Model.CouponRecordIntExt parkdiscountsetitem = parkdiscountsetlist?.Where(a => a.CouponRecord_No == x).FirstOrDefault();
                                    if (parkdiscountsetitem != null)
                                    {
                                        item.CouponRecord_CouponCode = parkdiscountsetitem.CouponRecord_CouponCode;
                                        item.CouponRecord_ParkOrderNo = po.ParkOrder_No;
                                        item.CouponRecord_IssueCarNo = po.ParkOrder_CarNo;

                                        item.CouponRecord_Value = parkdiscountsetitem.CouponRecord_Value;
                                        item.CouponRecord_StartTime = DateTimeHelper.GetNowTime();
                                        item.CouponRecord_EndTime = parkdiscountsetitem.CouponRecord_EndTime;

                                        //string fixNo = "";
                                        //if (!string.IsNullOrEmpty(item.CouponRecord_IssueCarNo))
                                        //{
                                        //    if (item.CouponRecord_IssueCarNo.Length > 1) fixNo = item.CouponRecord_IssueCarNo.Substring(1, item.CouponRecord_IssueCarNo.Length - 1);
                                        //    else
                                        //    {
                                        //        fixNo = item.CouponRecord_IssueCarNo;
                                        //    }

                                        //    fixNo = "-" + fixNo;
                                        //}

                                        item.CouponRecord_No = "ET" + Utils.CreateNumber_SnowFlake; //新建优惠编码
                                        item.CouponRecord_ParkNo = po.ParkOrder_ParkNo;
                                    }
                                    else
                                    {
                                        item.CouponRecord_No = x;
                                    }

                                    offLineCouponPayList.Add(item);
                                }
                            });
                            paycOLL.payOrderList[0].PayOrder_CouponRecordNo = string.Join(",", offLineCouponPayList.Select(a => a.CouponRecord_No));
                        }

                        if (paycOLL?.payOrderList != null) payModelList.AddRange(paycOLL.payOrderList);
                        if (paycOLL?.payPartList != null) payPartList.AddRange(paycOLL.payPartList);
                    }
                }

                #endregion

                #region 查询已支付的缴费订单

                decimal? alreadyPayMoney = 0, alreadyPayedMoney = 0;
                var payOrders = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{orderNo}' AND PayOrder_Status=1") ?? new List<Model.PayOrder>();
                if (payOrders != null)
                {
                    alreadyPayMoney = Utils.ObjectToDecimal(payOrders.Sum(x => x.PayOrder_Money), 0);
                    alreadyPayedMoney = Utils.ObjectToDecimal(payOrders.Sum(x => x.PayOrder_PayedMoney), 0);
                }

                #endregion

                #region 金额统计

                //当前支付订单金额统计
                decimal? currentMoney = Utils.ObjectToDecimal(payModelList.Sum(x => x.PayOrder_Money), 0);
                decimal? currentPayedMoney = Utils.ObjectToDecimal(payModelList.Sum(x => x.PayOrder_PayedMoney), 0);
                //停车订单金额统计
                po.ParkOrder_TotalAmount = po.ParkOrder_TotalAmount ?? 0;
                po.ParkOrder_TotalPayed = po.ParkOrder_TotalPayed ?? 0;
                po.ParkOrder_TotalAmount = (currentMoney + alreadyPayMoney);
                po.ParkOrder_TotalPayed = (currentPayedMoney + alreadyPayedMoney);

                #endregion

                #region 扣减车辆信息的储值费用

                Model.PayOrder payModel2 = null;
                if (payResult != null && car != null && car.Car_Category == Model.EnumCarType.Prepaid.ToString())
                {
                    if (payResult.payed != 2 && payResult.chuzhiamount > 0)
                    {
                        if (owner != null)
                        {
                            owner.Owner_Balance = Utils.ObjectToDecimal(owner.Owner_Balance, 0) - payResult.chuzhiamount;
                            if (owner.Owner_Balance < 0) owner.Owner_Balance = 0;
                        }
                    }
                }

                #endregion

                //支付订单组合
                Model.PayColl payColl = new PayColl() { payOrderList = payModelList, payPartList = payPartList };
                //无入场记录更新描述
                if (po.ParkOrder_IsNoInRecord == 1)
                {
                    payColl.payOrderList?.ForEach(x => { x.PayOrder_Desc = "*无入场记录"; });
                    payColl.payPartList?.ForEach(x => { x.PayPart_Desc = "*无入场记录"; });
                }

                #region 更新停车明细周期参数

                bool havePayResult = true;
                List<Model.HoursTotal> hoursTotals = null;
                if (payResult == null || payResult.list == null)
                {
                    havePayResult = false;
                }

                if (orderDetailList != null && orderDetailList.Count > 0)
                {
                    if (payResult?.list != null) payResult.list = BLL.CommonBLL.ApportionedAmountByPayPart(payResult.list, payPartList, po.ParkOrder_No);

                    foreach (var item in orderDetailList)
                    {
                        if (!havePayResult)
                        {
                            item.OrderDetail_IsSettle = 1; //结算状态
                            item.OrderDetail_Remark += " 无计费结果";
                            continue;
                        }

                        var pdItem = payResult.list.Where(x => x.orderdetailno == item.OrderDetail_No).FirstOrDefault();
                        if (pdItem != null && !string.IsNullOrEmpty(pdItem.orderdetailno))
                        {
                            item.OrderDetail_NextCycleTime = pdItem.nextcycletime; //下一次周期生效时间
                            item.Orderdetail_CycleMoney = pdItem.NextCyclePaidFees; //下一次停车，周期已累积支付总金额
                            item.Orderdetail_CycleFreeMin = pdItem.nextcyclefreemin; //下一次停车，周期已累积免费分钟
                            if (!string.IsNullOrEmpty(pdItem.nexthourscontent) && pdItem.nexthourstime != null)
                            {
                                hoursTotals = hoursTotals ?? new List<Model.HoursTotal>();
                                hoursTotals.Add(new Model.HoursTotal()
                                {
                                    HoursTotal_No = item.OrderDetail_No,
                                    HoursTotal_CarNo = item.OrderDetail_CarNo,
                                    HoursTotal_ParkOrderNo = item.OrderDetail_ParkOrderNo,
                                    HoursTotal_CarType = item.OrderDetail_CarType,
                                    HoursTotal_CarCardType = item.OrderDetail_CarCardType,
                                    HoursTotal_ParkAreaNo = item.OrderDetail_ParkAreaNo,
                                    HoursTotal_EnterTime = item.OrderDetail_EnterTime,
                                    HoursTotal_PayTime = DateTime.Now,
                                    HoursTotal_BeginTime = pdItem.nexthourstime,
                                    HoursTotal_Content = pdItem.nexthourscontent,
                                });
                            }
                            item.Orderdetail_UseFreeMin = Utils.ObjectToInt(item.Orderdetail_UseFreeMin, 0) + Utils.ObjectToInt(pdItem.currentfreemin, 0);
                            item.OrderDetail_IsSettle = 1; //结算状态
                            if (pdItem.calcbegintime != null)
                            {
                                item.OrderDetail_CurrCalcTime = pdItem.calcbegintime; //当前计费开始时间
                            }

                            item.OrderDetail_TotalAmount = item.OrderDetail_TotalAmount ?? 0;
                            item.OrderDetail_TotalAmount += pdItem.payedamount; //出场总金额
                        }
                    }
                }
                #endregion

                //充电滞留数据更新为已支付
                if (payResult != null && payResult.penaltylist != null && payResult.penaltyamount > 0)
                {
                    List<Model.DetentionPenalty> penaltyList = TyziTools.Json.ToObject<List<Model.DetentionPenalty>>(TyziTools.Json.ToString(payResult.penaltylist));
                    penaltyList.ForEach(item =>
                    {
                        item.DetentionPenalty_PayStatus = 1;
                        item.DetentionPenalty_ParkOrderNo = po.ParkOrder_No;
                    });
                    payColl.penaltyList = penaltyList;
                }

                //更新状态：线上优惠券 +  线下优惠券
                List<Model.CouponRecord> updateCouponList = new List<CouponRecord>();
                if (couponPayList != null) updateCouponList.AddRange(couponPayList);
                if (onLineCouponPayList != null) updateCouponList.AddRange(onLineCouponPayList);
                if (offLineCouponPayList != null) updateCouponList.AddRange(offLineCouponPayList);
                if (SpacialCouponPayList?.Count > 0) updateCouponList.AddRange(SpacialCouponPayList.Copy());

                var isUpdateIncar = true;
                if (po?.ParkOrder_StatusNo == EnumParkOrderStatus.Follow)
                {
                    var inModel = BLL.BaseBLL._GetEntityByWhere(new InCar(), "InCar_ParkOrderNo,InCar_Status,InCar_EnterTime", $"InCar_CarNo='{po.ParkOrder_CarNo}'");
                    if (inModel != null)
                    {
                        if (po.ParkOrder_No == inModel.InCar_ParkOrderNo)
                        {
                            if (inModel.InCar_Status == po.ParkOrder_StatusNo) isUpdateIncar = false;
                        }
                        else if (po.ParkOrder_EnterTime <= inModel.InCar_EnterTime) //入场时间小于当前入场记录时间，不更新Incar状态
                        {
                            isUpdateIncar = false;
                        }
                    }
                }
                else if (po?.ParkOrder_StatusNo == EnumParkOrderStatus.Out)
                {
                    isUpdateIncar = false;
                }

                if (paySpacial != null)
                {
                    if (payColl == null)
                        payColl = paySpacial;
                    else
                    {
                        payColl.payOrderList.AddRange(paySpacial.payOrderList);
                        payColl.payPartList.AddRange(paySpacial.payPartList);
                    }
                }

                //更新数据
                var result = BLL.CommonBLL.PaySuccess(po, payColl, updateCouponList, car, owner, orderDetailList, cev, isUpdateIncar, hoursTotals: hoursTotals, payResults: new List<ChargeModels.PayResult>() { notModifiedResult });
                if (result)
                {
                    DataCache.PayOrder.Set("Online:" + payOrderNo, payOrderNo);

                    //创建计费详情
                    try
                    {
                        DataCache.AskPrice.Del("OrderPrice:" + orderNo);
                        if (!isChangeMoney && notModifiedResult != null)
                        {
                            //计费详情
                            BLL.CommonBLL.CreateCalcDetail(notModifiedResult, po);
                        }
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFileException(null, $"{InstructionName}计费详情生成失败：{e.Message}，{orderNo}");
                    }

                    //缴费记录下发
                    try
                    {
                        bool isPlamtPay = false;
                        if (paytype == EnumPayType.Platform.ToString()) { isPlamtPay = true; }

                        ////线上缴费要生成支付订单事件记录，城市服务上传缴费记录时需要数据
                        //payColl?.payOrderList?.ForEach(x =>
                        //{
                        //    x.PayOrder_Status = 1;
                        //    BLL.PushEvent.PayOrder(AppBasicCache.GetParking?.Parking_Key, x, null, 1);
                        //});

                        var data = new Model.API.PayReq(po, payColl, updateCouponList, car, owner, orderDetailList, 0, cev: cev, ip: AppBasicCache.Ip, isPlamtPay: isPlamtPay, ctlNo: ctlNo, updateCoupon: checkCoupon);
                        var postd = new Model.API.ReqPush
                        {
                            act = Model.API.PushAction.Edit.ToString(),
                            tname = "paysuccess",
                            guid = Guid.NewGuid().ToString(),
                            data = TyziTools.Json.ToString(new List<Model.API.PayReq> { data }, true),
                            time = Utils.CreateNumber
                        };
                        await HandleCommon.UpdateCacheRet(postd);
                        //var r = BLL.MiddlewareApi.OnlinePayment(new Model.API.PayReq(po, payColl, updateCouponList, car, owner, orderDetailList, 0, cev: cev, ip: AppBasicCache.Ip, isPlamtPay: isPlamtPay));
                        //if (!r.success)
                        //{
                        //    LogManagementMap.WriteToFileException(null, $"{InstructionName}缴费记录下发分发失败：{r.errmsg}，{orderNo}");
                        //}
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFileException(null, $"{InstructionName}缴费记录下发成功，分发岗亭异常信息：{e}");
                    }

                    //异步执行
                    _ = Task.Run(async () =>
                      {
                          //上传缴费记录
                          try
                          {
                              if (paycOLL != null && paycOLL.payOrderList != null && paycOLL.payOrderList.Count > 0)
                              {
                                  BLL.PushEvent.PayOrder(paycOLL.payOrderList[0].PayOrder_ParkKey, po, paycOLL.payOrderList[0], offLineCouponPayList.Where(x => paycOLL.payOrderList[0].PayOrder_CouponRecordNo.Contains(x.CouponRecord_No)).ToList());
                              }
                          }
                          catch (Exception e)
                          {
                              LogManagementMap.WriteToFileException(null, $"{InstructionName}上报缴费记录异常：{e}");
                          }


                          try
                          {
                              ////弹出收费框（0.01），再去领取优惠券（30分钟），抵扣0元，还需缴费0.01，然后再去扫车道码（中间件生成0元订单用于上报优惠券）
                              if (paySpacial != null && paySpacial.payOrderList != null && paySpacial.payOrderList.Count > 0 && SpacialCouponPayList.Count > 0)
                              {
                                  BLL.PushEvent.PayOrder(paySpacial.payOrderList[0].PayOrder_ParkKey, po, paySpacial.payOrderList[0], SpacialCouponPayList.Where(x => paySpacial.payOrderList[0].PayOrder_CouponRecordNo.Contains(x.CouponRecord_No)).ToList());
                              }
                          }
                          catch (Exception ex)
                          {
                              LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "上传优惠抵扣记录异常：" + ex.ToString());
                          }

                          //招行支付下发缴费，需要上报停车平台
                          if (paytype == EnumPayType.NetcomPayment.ToString())
                          {
                              try
                              {
                                  if (payModel != null)
                                  {
                                      BLL.PushEvent.PayOrder(payModel.PayOrder_ParkKey, po, payModel, null);
                                  }
                              }
                              catch (Exception e)
                              {
                                  LogManagementMap.WriteToFileException(null, $"{InstructionName}上报招行缴费记录异常：{TyziTools.Json.ToString(payModel)}，{e}");
                              }
                          }

                          //招行支付下发缴费，岗亭车辆出场确认
                          if (payScene == "4" && passway != null)
                          {
                              try
                              {
                                  Model.CarCardType cct = BLL.CarCardType.GetEntity(po.ParkOrder_CarCardType);

                                  Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                                  {
                                      ParkNo = AppCache.GetParking.Parking_No,
                                      PasswayNo = passway.Passway_No,
                                      SentryHostNo = passway.Passway_SentryHostNo,
                                      DataType = "车辆出场确认",
                                      BaseData = null,
                                      sCarplate = po.ParkOrder_CarNo,
                                      sOrderNo = orderNo,
                                      opTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                                      operatorName = "云平台",
                                      SendOptions = Model.API.SendOptionsType.OutCar,
                                      opCarCardType = cct?.CarCardType_Category
                                  };

                                  string retSuccess = ""; string retValue = ""; bool IsSuccess = false;
                                  var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                                  IsSuccess = task.Item1;
                                  sRemark = task.Item2;
                                  retSuccess = task.Item3;
                                  retValue = task.Item4;

                                  //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                                  if (retSuccess.ToLower().Contains("true")) //车辆出场确认
                                  {
                                      LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"招行支付-车辆出场确认成功-{po.ParkOrder_CarNo}," + sRemark);
                                  }
                                  else
                                  {
                                      LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"招行支付-车辆出场确认不成功-{po.ParkOrder_CarNo}," + sRemark);
                                  }
                              }
                              catch (Exception e)
                              {
                                  LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"招行支付-云平台车辆出场回调异常-{TyziTools.Json.ToString(po)}," + e.ToString());
                              }
                          }
                      });

                    SendTCPDatas(context, sendsession, true, "缴费记录下发成功", sActionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}缴费记录下发执行SQL错误：{TyziTools.Json.ToString(po)}");
                    SendTCPDatas(context, sendsession, false, "缴费记录下发失败", sActionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{InstructionName}异常信息:" + ex.ToString());
                SendTCPDatas(context, sendsession, false, $"{sActionName}异常信息：{ex.Message}", sActionName);
            }
            return;
        }
    }

    /// <summary>
    /// 车场状态
    /// </summary>
    public class GetParkingStateHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "GetParkingState";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                IsSuccess = true;
                sRemark = "获取车场状态成功";

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName, data = new { onlinestate = 1, AppCache.GetParking.AppVersion } }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 开启frpc透传
    /// </summary>
    public class ConnectParkingHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "ConnectParking";

        /// <summary>
        /// 超时关闭frpc时间
        /// </summary>
        public static DateTime OverTimeClose = DateTimeHelper.GetNowTime();

        /// <summary>
        /// frpc进程
        /// </summary>
        private static Process processFrpc;

        /// <summary>
        /// 超时关闭frpc任务
        /// </summary>
        public static Task OverTimeTask = null;

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"接收开启远程指令");

                //"version":"1.0",    //版本号`
                //"deviceNo":"4g5dsf2d",    //车场授权KEY
                //"serveraddr":"carpark.szymzh.net",    //frpc地址
                //"serverport":"7000",    //frpc端口
                //"servertoken":"sfm@2021",    //frpc Token
                //"serverduration":"10",    //frpc 时长
                //"address":"4g5dsf2d.carpark.szymzh.net",    //可访问地址

                //Test Frp穿透【岗亭服务程序Frp穿透】 BLL.MiddlewareApi.PushTestFrp(jo, "");
                //SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                //return;

                OverTimeClose = DateTimeHelper.GetNowTime();
                Task.Factory.StartNew(delegate
                {
                    try
                    {
                        if (processFrpc != null && !processFrpc.HasExited)
                        {
                            processFrpc.Kill();
                            processFrpc.Dispose();
                            processFrpc = null;
                        }

                        var linuxPath = "/mnt/sda1/b30/Config/Frpc";
                        var frpcPath = linuxPath + "/frpc_mid";
                        var frpciniPath = linuxPath + "/frpc.ini";

                        string sPath = "";
                        string exeFile = "";

                        AppBasicCache.ConnectionParking_Cid = Convert.ToString(jo["cid"]);
                        AppBasicCache.ConnectionParking_Time = DateTime.Now;
                        AppBasicCache.ConnectionParking_ServerAddr = Convert.ToString(jo["serveraddr"]);
                        AppBasicCache.ConnectionParking_CToken = Utils.MD5Encrypt(Utils.CreateNumber_SnowFlake).ToLower();
                        var onLineSign = Convert.ToString(jo["sign"]);

                        if (string.IsNullOrEmpty(AppBasicCache.ConnectionParking_Cid))
                        {
                            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "未获取密钥", actionName = InstructionName }));
                        }
                        if (string.IsNullOrEmpty(AppBasicCache.ConnectionParking_ServerAddr))
                        {
                            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "服务器错误", actionName = InstructionName }));
                        }
                        string offlineSign = Utils.MD5Encrypt($"{AppBasicCache.ConnectionParking_Cid}{AppBasicCache.GetParking.Parking_Key}{AppBasicCache.ConnectionParking_Cid}").ToLower();
                        if (onLineSign != offlineSign)
                        {
                            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "验签失败", actionName = InstructionName }));
                        }

                        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                        {
                            #region Windows写入Frp配置信息


                            // 获取当前工作目录
                            string currentDirectory = System.AppDomain.CurrentDomain.BaseDirectory;

                            // 拼接相对路径
                            string relativePathToConfig = "..\\Sdk\\Frpc\\frpc.ini";
                            string relativePathToExe = "..\\Sdk\\Frpc\\frpc.exe";

                            // 转换为绝对路径
                            sPath = Path.GetFullPath(Path.Combine(currentDirectory, relativePathToConfig));
                            exeFile = Path.GetFullPath(Path.Combine(currentDirectory, relativePathToExe));

                            Common.IniHelper.SetValue("common", "server_addr", AppBasicCache.ConnectionParking_ServerAddr, sPath);
                            Common.IniHelper.SetValue("common", "server_port", Convert.ToString(jo["serverport"]), sPath);
                            Common.IniHelper.SetValue("common", "authentication_method", "token", sPath);
                            Common.IniHelper.SetValue("common", "token", Convert.ToString(jo["servertoken"]), sPath);
                            //tls_enable = true
                            Common.IniHelper.SetValue("common", "tls_enable", "true", sPath);

                            string webServer = AppCache.GetParking.Parking_Key + AppBasicCache.ConnectionParking_Cid;
                            Common.IniHelper.SetValue(webServer, "type", "http", sPath);
                            Common.IniHelper.SetValue(webServer, "local_ip", "127.0.0.1", sPath);
                            Common.IniHelper.SetValue(webServer, "local_port", Config.AppSettingConfig.SiteDomain_WebPort, sPath);
                            //Common.IniHelper.SetValue(webServer, "remote_port", "2233", sPath);
                            Common.IniHelper.SetValue(webServer, "subdomain", webServer, sPath);

                            List<Model.SentryHost> sentryHosts = BLL.SentryHost.GetAllEntity("SentryHost_No,SentryHost_IP,SentryHost_BSPort,SentryHost_WSPort,SentryHost_Category", $" SentryHost_ParkNo='{AppCache.GetParking.Parking_No}' ");

                            foreach (var sentry in sentryHosts)
                            {
                                if (sentry.SentryHost_Category == 2)
                                {
                                    string bsHost = $"{sentry.SentryHost_No}{AppCache.GetParking.Parking_Key}";
                                    //if (sentry.SentryHost_BSPort != null && sentry.SentryHost_BSPort.Value > 0)
                                    //{
                                    //    Common.IniHelper.SetValue(bsHost, "type", "http", sPath);
                                    //    Common.IniHelper.SetValue(bsHost, "local_ip", sentry.SentryHost_IP, sPath);
                                    //    Common.IniHelper.SetValue(bsHost, "local_port", sentry.SentryHost_BSPort.Value.ToString(), sPath);
                                    //    //Common.IniHelper.SetValue(bsHost, "remote_port", "2233", sPath);
                                    //    Common.IniHelper.SetValue(bsHost, "subdomain", bsHost, sPath);
                                    //}

                                    if (sentry.SentryHost_WSPort != null && sentry.SentryHost_WSPort.Value > 0)
                                    {
                                        string bsWSHost = $"ws{bsHost}";
                                        Common.IniHelper.SetValue(bsWSHost, "type", "http", sPath);
                                        Common.IniHelper.SetValue(bsWSHost, "local_ip", sentry.SentryHost_IP, sPath);
                                        Common.IniHelper.SetValue(bsWSHost, "local_port", sentry.SentryHost_WSPort.Value.ToString(), sPath);
                                        //Common.IniHelper.SetValue(bsWSHost, "remote_port", "7681", sPath);
                                        Common.IniHelper.SetValue(bsWSHost, "subdomain", bsWSHost, sPath);
                                    }
                                }

                                /*BS岗亭收费服务15相机获取视频流使用*/
                                List<string> Cameras15 = Common.CameraDeviceType.driveNameList15;
                                List<Model.DeviceExt> devices = BLL.Device.GetAllEntityExt("*", $"Device_SentryHostNo='{sentry.SentryHost_No}'");
                                foreach (var dv in devices)
                                {
                                    if (Cameras15.Contains(dv.Drive_Name))
                                    {
                                        string dvHost = $"{dv.Device_No}";
                                        Common.IniHelper.SetValue(dvHost, "type", "http", sPath);
                                        Common.IniHelper.SetValue(dvHost, "local_ip", dv.Device_IP, sPath);
                                        Common.IniHelper.SetValue(dvHost, "local_port", "80", sPath);
                                        Common.IniHelper.SetValue(dvHost, "subdomain", dvHost, sPath);

                                        string dvFLVHost = $"{dv.Device_No}flv";
                                        Common.IniHelper.SetValue(dvFLVHost, "type", "http", sPath);
                                        Common.IniHelper.SetValue(dvFLVHost, "local_ip", dv.Device_IP, sPath);
                                        Common.IniHelper.SetValue(dvFLVHost, "local_port", "9080", sPath);
                                        Common.IniHelper.SetValue(dvFLVHost, "subdomain", dvFLVHost, sPath);
                                    }
                                }
                            }

                            #endregion
                        }
                        else
                        {
                            #region 其余环境写入Frp配置文件

                            // 获取当前工作目录
                            string currentDirectory = System.AppDomain.CurrentDomain.BaseDirectory;

                            // 统一使用 Path.Combine() 拼接相对路径，确保兼容性
                            string relativePathToConfig = Path.Combine("..", "Sdk", "Frpc", "frpc.ini");
                            string relativePathToExe = Path.Combine("..", "Sdk", "Frpc", "frpc");

                            // 转换为绝对路径
                            sPath = Path.GetFullPath(Path.Combine(currentDirectory, relativePathToConfig));
                            exeFile = Path.GetFullPath(Path.Combine(currentDirectory, relativePathToExe));

                            LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, "Frp-" + DateTime.Now.ToString());

                            LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, "Frp-" + exeFile);

                            LocalFile.CreateDirectory(linuxPath);
                            if (!File.Exists(frpcPath))
                            {
                                File.Copy(exeFile, frpcPath, true);
                            }

                            if (!File.Exists(frpciniPath))
                            {
                                File.Copy(sPath, frpciniPath, true);
                            }

                            string ini = "";
                            string commonTmpl = "[common]\r\nserver_addr={0}\r\nserver_port={1}\r\nauthentication_method={2}\r\ntoken={3}\r\n";
                            string hostTmpl = "[{0}]\r\ntype={1}\r\nlocal_ip={2}\r\nlocal_port={3}\r\nsubdomain={4}\r\n";
                            ini += string.Format(commonTmpl, AppBasicCache.ConnectionParking_ServerAddr, Convert.ToString(jo["serverport"]), "token", Convert.ToString(jo["servertoken"]));
                            ini += string.Format(hostTmpl, AppCache.GetParking.Parking_Key + AppBasicCache.ConnectionParking_Cid, "http", "127.0.0.1", Config.AppSettingConfig.SiteDomain_WebPort, AppCache.GetParking.Parking_Key + AppBasicCache.ConnectionParking_Cid);
                            //ini += string.Format(hostTmpl, "ssh" + AppCache.GetParking.Parking_Key, "tcp", "127.0.0.1", "22", "ssh" + AppCache.GetParking.Parking_Key);
                            List<Model.SentryHost> sentryHosts = BLL.SentryHost.GetAllEntity("SentryHost_No,SentryHost_IP,SentryHost_BSPort,SentryHost_WSPort,SentryHost_Category", $"");
                            foreach (var sentry in sentryHosts)
                            {
                                if (sentry.SentryHost_Category == 2)
                                {
                                    string bsHost = $"{sentry.SentryHost_No}{AppCache.GetParking.Parking_Key}";
                                    //ini += string.Format(hostTmpl, bsHost, "http", sentry.SentryHost_IP, sentry.SentryHost_BSPort.Value.ToString(), bsHost);

                                    string wsHost = $"ws{bsHost}";
                                    ini += string.Format(hostTmpl, wsHost, "http", sentry.SentryHost_IP, sentry.SentryHost_WSPort.Value.ToString(), wsHost);
                                }

                                List<string> Cameras15 = Common.CameraDeviceType.driveNameList15;
                                List<Model.DeviceExt> devices = BLL.Device.GetAllEntityExt("*", $"Device_SentryHostNo='{sentry.SentryHost_No}'");
                                foreach (var dv in devices)
                                {
                                    if (Cameras15.Contains(dv.Drive_Name))
                                    {
                                        string dvHost = $"{dv.Device_No}";
                                        ini += string.Format(hostTmpl, dvHost, "http", dv.Device_IP, "80", dvHost);

                                        string dvFLVHost = $"{dv.Device_No}flv";
                                        ini += string.Format(hostTmpl, dvHost, "http", dv.Device_IP, "9080", dvHost);
                                    }
                                }
                            }

                            File.WriteAllText(frpciniPath, ini);

                            #endregion

                            #region frpc增加执行权限

                            //创建一个ProcessStartInfo对象 使用系统shell 指定命令和参数 设置标准输出
                            var psi = new ProcessStartInfo("chmod", $"+x {frpcPath}") { RedirectStandardOutput = false };
                            //启动
                            var proc = Process.Start(psi);
                            LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, $"Frpc-" + proc?.ProcessName);

                            #endregion
                        }

                        string sStartP = System.AppDomain.CurrentDomain.BaseDirectory + "\\..\\Sdk\\Frpc\\frpc.exe";
                        if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                        {
                            sStartP = frpcPath;
                            exeFile = frpcPath;
                            sPath = frpciniPath;
                        }

                        LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, ".Frp-" + exeFile);
                        LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, ".Frp-" + sPath);

                        processFrpc = new Process();
                        processFrpc.StartInfo.UseShellExecute = false; //是否使用操作系统shell启动 
                        processFrpc.StartInfo.CreateNoWindow = true; //是否在新窗口中启动该进程的值 (不显示程序窗口)
                        processFrpc.StartInfo.RedirectStandardOutput = true; // 由调用程序获取输出信息
                        processFrpc.StartInfo.RedirectStandardError = true; //重定向标准错误输出
                        processFrpc.StartInfo.FileName = exeFile;
                        processFrpc.StartInfo.Arguments = $"-c {sPath}";
                        processFrpc.StartInfo.Verb = "runas";
                        processFrpc.OutputDataReceived += (s, e) =>
                        {
                            try
                            {
                                if (!string.IsNullOrWhiteSpace(e.Data))
                                {
                                    if (e.Data.Contains("start proxy success"))
                                    {
                                        sRemark = "内网穿透创建成功！";
                                        IsSuccess = true;
                                    }

                                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, e.Data);
                                }
                            }
                            catch
                            {
                            }
                        };
                        processFrpc.ErrorDataReceived += (s, e) =>
                        {
                            try
                            {
                                if (!string.IsNullOrWhiteSpace(e.Data))
                                {
                                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, e.Data);
                                }
                            }
                            catch
                            {
                            }
                        };
                        processFrpc.Start();
                        processFrpc.BeginOutputReadLine();
                        processFrpc.BeginErrorReadLine();
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, $"Frpc启动异常：" + e.ToString());
                    }
                });


                Task.Factory.StartNew(delegate
                {
                    try
                    {
                        int iMax = 30;
                        while ((iMax--) > 0 && !IsSuccess)
                        {
                            Thread.Sleep(500);
                        }

                        if (!IsSuccess)
                        {
                            sRemark = "内网穿透创建不成功！";
                            //processFrpc?.CancelErrorRead();
                            //processFrpc?.CancelOutputRead();
                            processFrpc?.Kill();
                            processFrpc?.Dispose();
                            processFrpc = null;
                        }
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, $"Frpc内网穿透异常：" + e.ToString());
                    }

                    var retparam = new
                    {
                        address = $"http://{AppCache.GetParking.Parking_Key + AppBasicCache.ConnectionParking_Cid}.{AppBasicCache.ConnectionParking_ServerAddr}?token={AppBasicCache.ConnectionParking_CToken}"
                    };

                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName, data = TyziTools.Json.ToString(retparam) }));
                });

                //超时10分钟终止frpc线程
                if (OverTimeTask == null || OverTimeTask.IsCompleted)
                {
                    OverTimeTask = Task.Factory.StartNew(async delegate
                    {
                        try
                        {
                        Rp:
                            if (DateTimeHelper.GetNowTime() > OverTimeClose.AddMinutes(10))
                            {
                                try
                                {
                                    //AppBasicCache.RTSPVideoPlay的播放时间都相差10分钟，则可以kill
                                    var rlt = AppBasicCache.RTSPVideoPlay.Where(m => Math.Abs(DateTimeHelper.GetNowTime().Subtract(m.Value.Item3).TotalMinutes) < 10).ToList();
                                    if (rlt.Count == 0)
                                    {
                                        AppBasicCache.ConnectionParking_CToken = null;
                                        AppBasicCache.ConnectionParking_HttpToken = null;

                                        //processFrpc?.CancelErrorRead();
                                        //processFrpc?.CancelOutputRead();
                                        processFrpc?.Kill();
                                        processFrpc?.Dispose();
                                        processFrpc = null;
                                    }
                                    else
                                    {
                                        await Task.Delay(10000);
                                        goto Rp;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LogManagementMap.WriteToFileException(ex);
                                }
                            }
                            else
                            {
                                await Task.Delay(10000);
                                goto Rp;
                            }
                        }
                        catch (Exception e)
                        {
                            LogManagementMap.WriteToFileException(e, $"{InstructionName}异常信息");
                        }
                    }, TaskCreationOptions.LongRunning);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 线上修改车牌推送到线下指令处理类
    /// </summary>
    public class PullUpdateCarNoHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullUpdateCarNo";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["orderNo"]); //车辆入场时唯一的停车记录订单
                string newCarNo = Convert.ToString(jo["newCarNo"]); //新的车牌号码
                string newCardType = Convert.ToString(jo["carTypeNo"]); //新的计费卡类

                var carType = BLL.CarCardType._GetEntityByWhere(new Model.CarCardType { }, "*", $"CarCardType_Category ='{newCardType}'");
                newCardType = carType?.CarCardType_No ?? newCardType;

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), orderNo);
                if (model == null)
                {
                    sRemark = "修改车牌号码已经存在场内记录，不能修改该车牌号码！";
                }
                else
                {
                    var res = BLL.ParkOrder.UpdateOrder(model, newCarNo, newCardType, UploadMsg: true, CheckSpaceNumByEnterTime: false, lgAdmins: new AdminSession
                    {
                        Admins_Account = "CloudAdmin",
                        Admins_Name = "平台下发"
                    });
                    if (res.code == 1)
                    {
                        DataCache.ParkOrder.Set(model.ParkOrder_No, model);
                        var confirmOrder = BLL.ConfirmRelease.Results.Where(kv => kv.Value.passres?.parkorderno == orderNo).FirstOrDefault();
                        if (confirmOrder.Value != null)
                        {
                            if (confirmOrder.Value.passres != null)
                            {
                                confirmOrder.Value.passres.carno = newCarNo;
                                SentryBox.CommHelper.CheckConfirmResultForCarNo(newCarNo, orderNo, CloseNoInPark: false, mode: 6);
                            }
                        }

                        IsSuccess = true;
                        sRemark = "修改车牌信息成功!";
                    }
                    else
                    {
                        sRemark = res.msg;
                    }
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// APP下发车辆入场通知指令处理类
    /// </summary>
    public class PullAppEnterCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullAppEnterCar";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string key = jo["key"].ToString(); //停车场的Key值 判断下是否有效
                string carNo = jo["carNo"].ToString();
                string orderNo = jo["orderNo"].ToString();
                string enterTime = jo["enterTime"].ToString();
                string carType = jo["carType"].ToString();
                string gateName = jo["gateName"].ToString();
                string operatorName = jo["operatorName"].ToString();

                IsSuccess = ExecuteEnterCar(new PullEnterCarHandle.Param
                {
                    carNo = carNo,
                    carType = carType,
                    enterCode = "2",
                    enterTime = enterTime,
                    gateName = gateName,
                    operatorName = operatorName,
                    orderNo = orderNo,
                }, out sRemark);
                //var rlt = GetMemoryCache.Get<Tuple<string, string, string>>($"{Entitys.CacheType.EstCarIn}=>{orderNo}");
                //if (rlt == null)
                //{
                //    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,"车辆缓存信息未找到，APP推送记录写入失败" + JsonConvert.SerializeObject(new { carNo, orderNo, enterTime, carType, gateName, operatorName }));
                //    sRemark = "车辆缓存信息未找到，保存APP推送入场记录失败";
                //}
                //else
                //{
                //    #region 发送入场确认操作
                //    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                //    {
                //        ParkNo = AppCache.GetParking.Parking_No,
                //        PasswayNo = rlt.Item2,
                //        SentryHostNo = rlt.Item1,
                //        DataType = "车辆确认入场",
                //        BaseData = "appEnterCar",
                //        sCarplate = carNo,
                //        sOrderNo = orderNo,
                //        SendOptions = Model.API.SendOptionsType.EnterCar,
                //        opTime = enterTime,
                //        operatorName = operatorName,
                //        opCarCardType = carType
                //    };

                //    var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                //    if (res.success && res.data != null && res.data.ToLower().Contains("true"))
                //    {
                //        sRemark = res.errmsg;
                //        IsSuccess = true;
                //        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,"入场确认成功-" + sRemark);
                //    }
                //    else
                //    {
                //        sRemark = res.errmsg;
                //        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog,"入场确认不成功-" + sRemark);
                //    }
                //    #endregion
                //}


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }


        /// <summary>
        /// 创建入场记录(不判断入场策略)
        /// </summary>
        public bool ExecuteEnterCar(PullEnterCarHandle.Param param, out string sRemark)
        {
            try
            {
                var passway = BLL.Passway.GetEntityExt("*", $"Passway_Name='{param.gateName}'");
                if (passway != null)
                {
                    param.ctrlNo = passway.Passway_No;
                    var links = BLL.PasswayLink.GetEntity(param.ctrlNo);
                    var gate = links.Find(x => x.PasswayLink_GateType == 1);
                    var area = BLL.ParkArea.GetEntity(gate.PasswayLink_ParkAreaNo);

                    var car = BLL.Car.GetEntityByCarNo(param.carNo);
                    var card = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='{param.carType}' AND CarCardType_IsDefault=1");
                    var cartype = new Model.CarType();
                    //未登记车辆，使用默认参数(临时车A，蓝牌车)
                    if (car != null)
                    {
                        cartype = BLL.CarType.GetEntity(car.Car_VehicleTypeNo);
                        if (card == null)
                            card = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='3651' AND CarCardType_IsDefault=1");
                    }
                    else
                    {
                        cartype = BLL.CarType.GetEntity("*", "CarType_Name='蓝牌车'");
                    }

                    #region 创建订单

                    Model.ParkOrder order = BLL.ParkOrder.CreateParkOrder(
                        AppCache.GetParking.Parking_No
                        , area?.ParkArea_No
                        , area?.ParkArea_Name
                        , param.carNo
                        , card.CarCardType_No
                        , card.CarCardType_Name
                        , cartype.CarType_No
                        , cartype.CarType_Name
                        , Utils.StrToDateTime(param.enterTime)
                        , param.ctrlNo
                        , passway.Passway_Name
                        , 0
                        , 0
                        , car?.Car_OwnerNo
                        , car?.Car_OwnerName
                        , param.orderNo);
                    order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;

                    var detailList = new List<OrderDetail>();
                    Model.OrderDetail estOrderDetail = BLL.OrderDetail.GetEntity("*", $"OrderDetail_ParkOrderNo='{param.orderNo}' and orderdetail_ParkAreaNo='{area?.ParkArea_No}' and orderdetail_StatusNo='{Model.EnumParkOrderStatus.Est}' ");
                    if (estOrderDetail == null)
                    {
                        estOrderDetail = BLL.OrderDetail.CreateOrderDetail(
                            param.orderNo
                            , AppCache.GetParking.Parking_No
                            , area?.ParkArea_No
                            , area?.ParkArea_Name
                            , param.carNo
                            , card?.CarCardType_No
                            , card?.CarCardType_Name
                            , cartype?.CarType_No
                            , cartype?.CarType_Name
                            , Utils.StrToDateTime(param.enterTime)
                            , passway?.Passway_No
                            , passway?.Passway_Name);
                    }

                    detailList.Add(estOrderDetail);

                    #endregion

                    BLL.ParkOrder.EpParkOrder(ref order, null);

                    var orderList = new List<ParkOrder> { order };


                    BLL.ParkOrder.InOutRecordWithOrder(ref orderList, ref detailList);
                    detailList.ForEach(x =>
                    {
                        if (x.OrderDetail_No == estOrderDetail.OrderDetail_No)
                        {
                            x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                        }
                    });

                    var ret = BLL.ParkOrder.CarInComplete(orderList, detailList);
                    if (ret > 0)
                    {
                        Task.Run(() =>
                        {
                            var pushData = new Model.API.PushResultParse.UpdateParkOrder
                            {
                                Item1 = orderList,
                                Item2 = detailList
                            };
                            HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), pushData, "carin");
                            //var resBody = BLL.MiddlewareApi.UpdateOrderList(pushData);
                            //if (!resBody.success)
                            //{
                            //    LogManagementMap.WriteToFileException(null, $"保存平台下发的入场记录成功, 推送到其他岗亭失败：{resBody.errmsg}");
                            //}
                        });
                        sRemark = "保存平台下发的入场记录成功";
                        return true;
                    }
                    else
                    {
                        sRemark = "保存平台下发的入场记录失败";
                        return false;
                    }
                }
                else
                {
                    sRemark = $"未找到[{param.gateName}]的车道信息！";
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"平台推送入场记录异常");

                sRemark = ex.Message;
                return false;
            }
        }
    }

    /// <summary>
    /// APP下发车辆出场通知指令处理类
    /// </summary>
    public class PullAppOutCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullAppOutCar";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string key = jo["key"].ToString(); //停车场的Key值 判断下是否有效
                string carNo = jo["carNo"].ToString();
                string orderNo = jo["orderNo"].ToString();
                string outTime = jo["outTime"].ToString();
                string carType = jo["carType"].ToString();
                string operatorName = jo["operatorName"].ToString();
                string gateName = Convert.ToString(jo["gateName"]);
                //string totalAmount = jo["totalAmount"].ToString();
                //string payType = null == jo["payType"] ? "" : jo["payType"].ToString();

                //var rlt = GetMemoryCache.Get<Tuple<string, string, string>>($"{Entitys.CacheType.EstCarOut}=>{orderNo}");
                //if (rlt == null)
                //{
                //    sRemark = $"[{carNo}]预出场信息不存在";
                //}
                //else
                //{

                #region 发送出场确认操作

                var passway = BLL.Passway.GetEntityExt("*", $"Passway_Name='{gateName}'");

                Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                {
                    ParkNo = AppCache.GetParking.Parking_No,
                    PasswayNo = passway?.Passway_No,
                    SentryHostNo = passway?.Passway_SentryHostNo,
                    DataType = "APP车辆出场确认",
                    BaseData = null,
                    sCarplate = carNo,
                    sOrderNo = orderNo,
                    opTime = outTime,
                    operatorName = operatorName,
                    SendOptions = Model.API.SendOptionsType.OutCar,
                    opCarCardType = carType
                };
                string retSuccess = ""; string retValue = "";
                var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                IsSuccess = task.Item1;
                sRemark = task.Item2;
                retSuccess = task.Item3;
                retValue = task.Item4;
                //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                if (retSuccess.ToLower().Contains("true")) //车辆出场确认
                {
                    IsSuccess = true;
                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "APP车辆出场确认成功-" + sRemark);
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "APP车辆出场确认不成功-" + sRemark);
                }

                #endregion

                //}

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 加载广告流媒体指令处理类
    /// </summary>
    public class PushAdvertHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PushAdvert";


        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                ///version=v2.2,     								/*平台接口版本*/
                ///actionName=’PushAdvert’,							/*action名称*/
                ///key=’12345678’,									/*停车场key*/
                ///laneCtrlNo=’1,2,3,4’,					/*车道控制机号集合（逗号隔开，空值表示所有车道）*/
                ///advertStatus=0, 									/*广告状态：0禁用，1启用*/
                ///advertType=0,									/*广告类型：0 LED显示，1图片，2图片+语音，3视频 */
                ///content=’这是一条LED广告标语’,					/*LED显示屏内容*/
                ///audioUrl=’ http://img.oss-cn-shenzhen.aliyuncs.com/…’,		/*播报语音URL*/
                ///imageUrl=’http://img.oss-cn-shenzhen.aliyuncs.com/…’,		/*广告图片URL*/
                ///videoUrl=’ http://img.oss-cn-shenzhen.aliyuncs.com/…’,		/*广告视频URL*/
                ///startTime=’2019-11-11 16:13:01’,						/*广告开始时间，空值表示立即开始*/
                ///endTime=’2019-12-12 10:15:00’						/*广告结束时间，空值表示无限期*/

                bool IsSuccess = false;
                string sRemark = string.Empty;

                string strCtrlNo = Convert.ToString(jo["laneCtrlNo"]);
                bool isAdvertStatus = Convert.ToString(jo["advertStatus"]) == "1" ? true : false;
                int iAdvertType = Convert.ToInt32(jo["advertType"]);
                string strContent = Convert.ToString(jo["content"]);
                string strAudioUrl = Convert.ToString(jo["audioUrl"]);
                string strImageUrl = Convert.ToString(jo["imageUrl"]);
                string strVideoUrl = Convert.ToString(jo["videoUrl"]);
                string strStartTime = Convert.ToString(jo["startTime"]);
                string strEndTime = Convert.ToString(jo["endTime"]);

                //广告开始时间，空值表示立即开始
                if (string.IsNullOrEmpty(strStartTime))
                {
                    strStartTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");
                }

                //广告结束时间，空值表示无限期
                if (string.IsNullOrEmpty(strEndTime))
                {
                    strEndTime = DateTime.MaxValue.ToString("yyyy-MM-dd HH:mm:ss");
                }


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 跟车记录下发处理结果
    /// </summary>
    /// <summary>
    public class CarFollowUpdataHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CarFollowUpdata";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                var data = jo.ToObject<CarFollowUpdataParam>();
                if (data == null)
                {
                    SendTCPDatas(context, sendsession, true, "跟车记录下发失败", InstructionName);
                    return Task.CompletedTask;
                }

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), data.carFollowNo);
                if (model == null)
                {
                    SendTCPDatas(context, sendsession, true, "跟车记录不存在", InstructionName);
                    return Task.CompletedTask;
                }

                model.ControlEvent_Remark = data.remark;
                model.ControlEvent_Money = data.money;

                if (data.orderStatus?.ToString() == "200")
                {
                    //忽略
                    var ret = BLL.ControlEvent.Ingore(ref model, data.remark, out var order, out var errmsg);
                    if (!ret)
                    {
                        SendTCPDatas(context, sendsession, true, errmsg, InstructionName);
                        return Task.CompletedTask;
                    }

                    var res = BLL.ControlEvent.Handle(model, order, out errmsg);
                    if (res > 0)
                    {
                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                        if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                        {
                            CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);
                        }

                        //BLL.MiddlewareApi.CarFollowUpdata(new Model.API.PushResultParse.ControlEvent()
                        //{
                        //    Item1 = model,
                        //    Item2 = order.Item1,
                        //    Item3 = order.Item2
                        //});
                        HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), new Model.API.PushResultParse.ControlEvent()
                        {
                            Item1 = model,
                            Item2 = order.Item1,
                            Item3 = order.Item2
                        }, "carfollowhandle");

                        SendTCPDatas(context, sendsession, true, "跟车记录下发成功", InstructionName);
                    }
                    else
                    {
                        SendTCPDatas(context, sendsession, true, errmsg, InstructionName);
                    }
                }
                else if (data.orderStatus?.ToString() == "204")
                {
                    (Model.ParkOrder, List<Model.OrderDetail>) order = (null, null);
                    Model.OrderDetail detail = null;
                    string errmsg = string.Empty;
                    //关联订单
                    var ret = false;
                    if (model.ControlEvent_Gate == 0)
                    {
                        ret = BLL.ControlEvent.bindOrder(ref model, data.orderNo, out order, out detail, out errmsg);
                        if (!ret)
                        {
                            SendTCPDatas(context, sendsession, true, errmsg, InstructionName);
                            return Task.CompletedTask;
                        }

                        order.Item1.ParkOrder_TotalAmount = data.money;
                        model.ControlEvent_Status = 2;
                    }
                    else
                    {
                        model.ControlEvent_ParkOrderNo = data.orderNo;
                        model.ControlEvent_CarNo = data.carNo;
                        model.ControlEvent_Status = 3;
                    }

                    var res = BLL.ControlEvent.Handle(model, order, out errmsg);
                    if (res > 0)
                    {
                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);

                        HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), new Model.API.PushResultParse.ControlEvent()
                        {
                            Item1 = model,
                            Item2 = order.Item1,
                            Item3 = order.Item2
                        }, "carfollowhandle");
                        //BLL.MiddlewareApi.CarFollowUpdata(new Model.API.PushResultParse.ControlEvent()
                        //{
                        //    Item1 = model,
                        //    Item2 = order.Item1,
                        //    Item3 = order.Item2
                        //});
                        if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                        {
                            CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);
                        }

                        SendTCPDatas(context, sendsession, true, "跟车记录下发成功", InstructionName);
                    }
                    else
                    {
                        SendTCPDatas(context, sendsession, true, errmsg, InstructionName);
                    }
                }
                else if (data.orderStatus?.ToString() == "201")
                {
                    var parkorder = BLL.ParkOrder.GetDetailByOrderNo(data.orderNo);
                    if (parkorder.Item1 == null)
                    {
                        LogManagementMap.WriteToFileException(null, $"跟车记录下发失败，未找到停车订单");
                        SendTCPDatas(context, sendsession, true, "未找到停车订单", InstructionName);
                        return Task.CompletedTask;
                    }
                    else
                    {
                        parkorder.Item1.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
                        parkorder.Item2?.ForEach(d =>
                        {
                            if (d.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In)
                            {
                                d.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                            }
                        });

                        var res = BLL.ParkOrder.CarInComplete(parkorder.Item1, parkorder.Item2);
                        if (res > 0)
                        {
                            BLL.ControlEvent.Handle(parkorder.Item1.ParkOrder_No);
                            //关闭弹窗
                            PasswayConfirmReleaseUtil.RemoveResultByOrderNo(parkorder.Item1.ParkOrder_No);
                            //刷新弹窗
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);

                            if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                            {
                                CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                                CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                                CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                                CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                                CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                                CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);
                            }
                            //var resBody = BLL.MiddlewareApi.UpdateOrderDetail(parkorder.Item1, parkorder.Item2);
                            //if (!resBody.success)
                            //{
                            //    LogManagementMap.WriteToFileException(null, $"跟车记录下发成功，但是转发给其他岗亭失败");
                            //}
                            HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (parkorder.Item1, parkorder.Item2), "updateorder");
                        }
                        else
                        {
                            SendTCPDatas(context, sendsession, true, "跟车记录下发成功", InstructionName);
                            return Task.CompletedTask;
                        }
                    }
                }
                else if (string.IsNullOrEmpty(data.orderStatus))
                {
                    model.ControlEvent_Status = 1;
                    (Model.ParkOrder, List<Model.OrderDetail>) order = (null, null);
                    string errmsg = string.Empty;
                    BLL.ControlEvent.Handle(model, order, out errmsg);

                    //刷新弹窗
                    SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                    if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);
                    }
                }
                else
                {
                    SendTCPDatas(context, sendsession, true, "记录状态错误", InstructionName);
                    return Task.CompletedTask;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }

        public class CarFollowUpdataParam
        {
            /// <summary>
            /// 版本号
            /// </summary>
            public string version { get; set; }

            public string actionName { get; set; }

            /// <summary>
            /// 停车场key
            /// </summary>
            public string Key { get; set; }

            /// <summary>
            /// 跟车记录编号
            /// </summary>
            public string carFollowNo { get; set; }

            /// <summary>
            /// 0-入口，1-出口
            /// </summary>
            public string gate { get; set; }

            /// <summary>
            /// 跟车时间
            /// </summary>
            public string time { get; set; }

            /// <summary>
            /// 车道编码
            /// </summary>
            public string vehicleLaneNo { get; set; }

            /// <summary>
            /// 记录状态（0-新增，1忽略，2关联订单）
            /// </summary>
            public string status { get; set; }

            /// <summary>
            /// 备注
            /// </summary>
            public string remark { get; set; }

            /// <summary>
            /// 车牌号
            /// </summary>
            public string carNo { get; set; }

            /// <summary>
            /// 车牌颜色
            /// </summary>
            public string plateColor { get; set; }

            /// <summary>
            /// 停车订单号
            /// </summary>
            public string orderNo { get; set; }

            /// <summary>
            /// 订单状态,200-忽略，201-出场，204-关联订单
            /// </summary>
            public string orderStatus { get; set; }

            /// <summary>
            /// 抓拍图片地址
            /// </summary>
            public string image { get; set; }

            /// <summary>
            /// 抓拍视频地址
            /// </summary>
            public string video { get; set; }

            /// <summary>
            ///  出场时间 （状态为204时带有时间值）
            /// </summary>
            public string outTime { get; set; }

            /// <summary>
            /// 出场抓拍图片地址
            /// </summary>
            public string outImage { get; set; }

            /// <summary>
            /// 出场抓拍视频地址
            /// </summary>
            public string outVideo { get; set; }

            /// <summary>
            /// 处理时间
            /// </summary>
            public string handleTime { get; set; }

            /// <summary>
            /// 操作人
            /// </summary>
            public string operatorName { get; set; }

            /// <summary>
            /// 追缴金额
            /// </summary>
            public decimal? money { get; set; }
        }
    }

    /// <summary>
    /// 云平台新增跟车记录
    /// </summary>
    /// <summary>
    public class CarFollowAddHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CarFollowAdd";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                var data = jo.ToObject<CarFollowUpdataHandle.CarFollowUpdataParam>();
                if (data == null)
                {
                    SendTCPDatas(context, sendsession, true, "跟车记录下发失败", InstructionName);
                    return Task.CompletedTask;
                }

                var gate = (data.gate == "0" ? 1 : 0); //与云平台的出入口类型装换
                var passway = BLL.Passway.GetEntity(data.vehicleLaneNo);
                if (passway == null) //追缴订单无需判断车道
                {
                    SendTCPDatas(context, sendsession, true, "车道编号为空", InstructionName);
                    return Task.CompletedTask;
                }
                var link = BLL.PasswayLink.GetEntityExt("PasswayLink_ParkAreaNo", $"PasswayLink_PasswayNo='{data.vehicleLaneNo}' AND PasswayLink_GateType='{gate}'");
                if (link == null)   // 追缴订单无需判断关联区域
                {
                    SendTCPDatas(context, sendsession, true, "未找到车道关联的停车区域", InstructionName);
                    return Task.CompletedTask;
                }
                var device = BLL.Device.GetEntityByPasswayNo(data.vehicleLaneNo);
                if (device == null) // 追缴订单无需判断设备
                {
                    SendTCPDatas(context, sendsession, true, "未找到车道主相机", InstructionName);
                    return Task.CompletedTask;
                }

                Model.ParkOrder parkOrder = new Model.ParkOrder();
                parkOrder.ParkOrder_No = string.IsNullOrEmpty(data.orderNo) ? $"{Utils.CreateNumberWith()}-{(data.carNo?.Length > 6 ? data.carNo.Substring(data.carNo.Length - 6, 6) : "")}" : data.orderNo;
                Model.ControlEvent model = new Model.ControlEvent()
                {
                    ControlEvent_No = data.carFollowNo,
                    ControlEvent_ParkNo = AppCache.GetParking.Parking_No,
                    ControlEvent_ParkName = AppCache.GetParking.Parking_Name,
                    ControlEvent_ParkAreaNo = link?.PasswayLink_ParkAreaNo,
                    ControlEvent_ParkAreaName = link?.ParkArea_Name,
                    ControlEvent_PasswayNo = passway?.Passway_No,
                    ControlEvent_PasswayName = passway?.Passway_Name,
                    ControlEvent_DeviceNo = device?.Device_No,
                    ControlEvent_DeviceName = device?.Device_Name,
                    ControlEvent_Time = Utils.StrToDateTime(data.time),
                    ControlEvent_BigImg = data.image,
                    ControlEvent_Video = data.video,
                    ControlEvent_Status = 0,
                    ControlEvent_Type = 1,
                    ControlEvent_Remark = data.remark,
                    ControlEvent_Gate = gate,
                    ControlEvent_AddTime = DateTimeHelper.GetNowTime(),
                    ControlEvent_CarNo = data.carNo,
                    ControlEvent_ParkOrderNo = parkOrder.ParkOrder_No,
                    ControlEvent_OkTime = Utils.StrToDateTime(data.handleTime),
                    ControlEvent_AdminName = data.operatorName,
                    ControlEvent_Money = data.money,
                };

                var res = BLL.ControlEvent._Add(model);
                if (res > 0)
                {
                    HandleCommon.UpdateCache(Model.API.PushAction.Add.ToString(), model);

                    SendTCPDatas(context, sendsession, true, "新增跟车记录成功", InstructionName);
                    return Task.CompletedTask;
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "新增跟车记录失败", InstructionName);
                    return Task.CompletedTask;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 停车追缴账单记录
    /// </summary>
    /// <summary>
    public class AddOverdueBillHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "AddOverdueBill";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;

                var data = jo.ToObject<AddOverdueBillHandle.OverdueBilldataParam>();
                if (data == null)
                {
                    SendTCPDatas(context, sendsession, true, "停车追缴账单下发失败", InstructionName);
                    return Task.CompletedTask;
                }

                if (string.IsNullOrEmpty(data.carBillNo))
                {
                    SendTCPDatas(context, sendsession, true, "账单编号不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                var obill2 = BLL.BaseBLL._GetEntityByNo(new Model.OverdueBill(), data.carBillNo);
                if (obill2 != null)
                {
                    SendTCPDatas(context, sendsession, true, "账单记录已存在", InstructionName);
                    return Task.CompletedTask;
                }

                Model.OverdueBill obill = new OverdueBill();
                obill.OverdueBill_No = data.carBillNo;
                obill.OverdueBill_PlateColor = Utils.ObjectToInt(data.plateColor, 1);
                obill.OverdueBill_CarNo = data.carNo;
                obill.OverdueBill_EnterImage = data.image;
                obill.OverdueBill_OutImage = data.outImage;
                obill.OverdueBill_EnterVideo = data.video;
                obill.OverdueBill_OutVideo = data.outVideo;
                if (!string.IsNullOrEmpty(data.time)) obill.OverdueBill_EnterTime = Utils.ObjectToDateTime(data.time);
                if (!string.IsNullOrEmpty(data.outTime)) obill.OverdueBill_OutTime = Utils.ObjectToDateTime(data.outTime);
                if (!string.IsNullOrEmpty(data.handleTime)) obill.OverdueBill_HandleTime = Utils.ObjectToDateTime(data.handleTime);
                obill.OverdueBill_Remark = data.remark;
                obill.OverdueBill_Gate = Utils.ObjectToInt(data.gate, 0);
                obill.OverdueBill_Money = data.money;
                obill.OverdueBill_Tag = data.tag;
                obill.OverdueBill_OperationName = data.operatorName;
                obill.OverdueBill_Status = 0;

                var res = BLL.BaseBLL._Add(obill);
                if (res > 0)
                {
                    SendTCPDatas(context, sendsession, true, "新增停车追缴账单成功", InstructionName);
                    return Task.CompletedTask;
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "新增停车追缴账单失败", InstructionName);
                    return Task.CompletedTask;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }

        public class OverdueBilldataParam
        {
            /// <summary>
            /// 版本号
            /// </summary>
            public string version { get; set; }

            public string actionName { get; set; }

            /// <summary>
            /// 平台标识（接入方自定义），支持32位数字、字母、汉字
            /// </summary>
            public string tag { get; set; }

            /// <summary>
            /// 停车场key
            /// </summary>
            public string Key { get; set; }

            /// <summary>
            /// 停车追缴账单编号
            /// </summary>
            public string carBillNo { get; set; }

            /// <summary>
            /// 车牌号
            /// </summary>
            public string carNo { get; set; }

            /// <summary>
            /// 车牌颜色
            /// </summary>
            public string plateColor { get; set; }

            /// <summary>
            /// 追缴金额
            /// </summary>
            public decimal? money { get; set; }

            /// <summary>
            /// 出入口类型（默认0）：0出口、1入口
            /// </summary>
            public string gate { get; set; }

            /// <summary>
            /// 入场时间
            /// </summary>
            public string time { get; set; }

            /// <summary>
            ///  出场时间（格式：yyyy-MM-dd HH:mm:ss）
            /// </summary>
            public string outTime { get; set; }

            /// <summary>
            /// 处理时间（格式：yyyy-MM-dd HH:mm:ss），默认当前时间
            /// </summary>
            public string handleTime { get; set; }

            /// <summary>
            /// 记录状态（30-新增，）
            /// </summary>
            public string status { get; set; }

            /// <summary>
            /// 备注
            /// </summary>
            public string remark { get; set; }

            /// <summary>
            /// 入场视频URL
            /// </summary>
            public string video { get; set; }

            /// <summary>
            /// 出场视频URL
            /// </summary>
            public string outVideo { get; set; }

            /// <summary>
            /// 入场图片URL
            /// </summary>
            public string image { get; set; }

            /// <summary>
            /// 出场图片URL
            /// </summary>
            public string outImage { get; set; }

            /// <summary>
            /// 操作人，默认值为"第三方追缴"
            /// </summary>
            public string operatorName { get; set; }
        }
    }

    /// <summary>
    /// 更新停车追缴账单记录
    /// </summary>
    /// <summary>
    public class UpdateOverdueBillHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "UpdateOverdueBill";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;

                var data = jo.ToObject<AddOverdueBillHandle.OverdueBilldataParam>();
                if (data == null)
                {
                    SendTCPDatas(context, sendsession, true, "更新停车追缴账单下发失败", InstructionName);
                    return Task.CompletedTask;
                }

                var obill = BLL.BaseBLL._GetEntityByNo(new Model.OverdueBill(), data.carBillNo);
                if (obill == null)
                {
                    SendTCPDatas(context, sendsession, true, "账单记录不存在", InstructionName);
                    return Task.CompletedTask;
                }

                if (obill.OverdueBill_Status != 0)
                {
                    SendTCPDatas(context, sendsession, true, "账单记录已经处理，无法更改", InstructionName);
                    return Task.CompletedTask;
                }

                var status = int.Parse(data.status);//新增入口为0, 出口为1, 入口绑定订单10，出口绑定订单11，解除入口绑定20，解除出口绑定21，增加追缴订单30
                obill.OverdueBill_Status = status == 32 ? obill.OverdueBill_Status : (status == 33 ? 1 : 2);
                if (obill.OverdueBill_Status == 1)
                {
                    obill.OverdueBill_Remark = obill.OverdueBill_Remark ?? "";
                    obill.OverdueBill_Remark += " 由第三方完成追缴";
                }

                if (!string.IsNullOrEmpty(data.carNo)) obill.OverdueBill_CarNo = data.carNo;
                if (!string.IsNullOrEmpty(data.image)) obill.OverdueBill_EnterImage = data.image;
                if (!string.IsNullOrEmpty(data.outImage)) obill.OverdueBill_OutImage = data.outImage;
                if (!string.IsNullOrEmpty(data.video)) obill.OverdueBill_EnterVideo = data.video;
                if (!string.IsNullOrEmpty(data.outVideo)) obill.OverdueBill_OutVideo = data.outVideo;

                if (!string.IsNullOrEmpty(data.time)) obill.OverdueBill_EnterTime = Utils.ObjectToDateTime(data.time);
                if (!string.IsNullOrEmpty(data.outTime)) obill.OverdueBill_OutTime = Utils.ObjectToDateTime(data.outTime);
                if (!string.IsNullOrEmpty(data.handleTime)) obill.OverdueBill_HandleTime = Utils.ObjectToDateTime(data.handleTime);

                if (!string.IsNullOrEmpty(data.remark)) obill.OverdueBill_Remark = data.remark;
                if (!string.IsNullOrEmpty(data.gate)) obill.OverdueBill_Gate = Utils.ObjectToInt(data.gate, 0);
                if (!string.IsNullOrEmpty(data.plateColor)) obill.OverdueBill_PlateColor = Utils.ObjectToInt(data.plateColor, 1);

                if (obill.OverdueBill_Status != 1 && data.money != null) obill.OverdueBill_Money = data.money;
                if (!string.IsNullOrEmpty(data.operatorName)) obill.OverdueBill_OperationName = data.operatorName;

                var res = BLL.BaseBLL._AddOrUpdateModel(obill);
                if (res >= 0)
                {
                    //刷新弹窗
                    SentryBox.CommHelper.CheckConfirmResultForCarNo(obill.OverdueBill_CarNo, null, CloseNoInPark: false);
                    if (!string.IsNullOrEmpty(obill.OverdueBill_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + obill.OverdueBill_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + obill.OverdueBill_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + obill.OverdueBill_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + obill.OverdueBill_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + obill.OverdueBill_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + obill.OverdueBill_ParkOrderNo);
                    }

                    SendTCPDatas(context, sendsession, true, "更新停车追缴账单下发成功", InstructionName);
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "更新停车追缴账单失败", InstructionName);
                }

            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 删除车道下发
    /// </summary>
    /// <summary>
    public class SysnVehicleLaneHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "SysnVehicleLane";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                var data = jo.ToObject<Model.API.apiSysnVehicleLane>();
                if (data == null)
                {
                    SendTCPDatas(context, sendsession, true, "删除车道下发失败：参数读取失败", InstructionName);
                    return Task.CompletedTask;
                }

                if (!data.code.Equals("0"))
                {
                    SendTCPDatas(context, sendsession, true, "删除车道下发失败：code值错误", InstructionName);
                    return Task.CompletedTask;
                }

                Model.Passway passway = BLL.Passway.GetAllEntity("*", $"Passway_SentryHostNo='{data.sentryBoxNo}' AND Passway_No='{data.vehicleLaneNo}'")?.FirstOrDefault();
                if (passway == null)
                {
                    SendTCPDatas(context, sendsession, true, "删除车道下发成功：线下未找到车道信息", InstructionName);
                    return Task.CompletedTask;
                }

                var res = BLL.Passway.DeleteAll(passway.Passway_No);
                if (res > 0)
                {
                    //BLL.MiddlewareApi.PasswayDeleteAll(passway);
                    HandleCommon.UpdateCache(Model.API.PushAction.Delete.ToString(), passway, "delpassway");
                    SendTCPDatas(context, sendsession, true, "删除车道下发成功", InstructionName);
                    return Task.CompletedTask;
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "删除车道下发失败", InstructionName);
                    return Task.CompletedTask;
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    #region 云托管

    /// <summary>
    /// 云托管 车牌智能修正开启
    /// </summary>
    public class OpenCloudCorrectionHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "OpenCloudCorrection";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string reliability = Convert.ToString(jo["reliability"]);
                if (!float.TryParse(reliability, out float freliability))
                {
                    freliability = 0.99f;
                }

                string smallImgBucket = Convert.ToString(jo["smallImgBucket"]);

                var sysConfigData = BLL.SysConfig.GetEntity(AppCache.GetParking.Parking_No);
                AppCache.GetSysConfigContent.OpenCloudCorrection = 1;
                AppCache.GetSysConfigContent.freliability = freliability;
                AppCache.GetSysConfigContent.smallImgBucket = smallImgBucket;
                sysConfigData.SysConfig_Content = WebUtility.UrlEncode(Common.TyziTools.Json.ToString(AppCache.GetSysConfigContent));
                if (BLL.SysConfig._AddOrUpdateModel(sysConfigData) > 0)
                {
                    AppBasicCache.CurrentSysConfig = null;
                    //BLL.MiddlewareApi.PushSysconfig(AppCache.GetParking, sysConfigData);
                    var postd = new Model.API.ReqPush
                    {
                        act = Model.API.PushAction.Edit.ToString(),
                        tname = "pushsysconfig",
                        guid = Guid.NewGuid().ToString(),
                        data = string.Empty,
                        time = Utils.CreateNumber
                    };
                    HandleCommon.UpdateCacheRet(postd).ConfigureAwait(false);
                    IsSuccess = true;
                    sRemark = "保存参数成功！";
                }
                else
                {
                    sRemark = "保存参数不成功！";
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 车牌智能修正关闭
    /// </summary>
    public class CloseCloudCorrectionHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CloseCloudCorrection";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                var sysConfigData = BLL.SysConfig.GetEntity(AppCache.GetParking.Parking_No);
                AppCache.GetSysConfigContent.OpenCloudCorrection = 0;
                sysConfigData.SysConfig_Content = WebUtility.UrlEncode(Common.TyziTools.Json.ToString(AppCache.GetSysConfigContent));
                if (BLL.SysConfig._AddOrUpdateModel(sysConfigData) > 0)
                {
                    AppBasicCache.CurrentSysConfigContent = AppCache.GetSysConfigContent;
                    //BLL.MiddlewareApi.PushSysconfig(AppCache.GetParking, sysConfigData);
                    var postd = new Model.API.ReqPush
                    {
                        act = Model.API.PushAction.Edit.ToString(),
                        tname = "pushsysconfig",
                        guid = Guid.NewGuid().ToString(),
                        data = string.Empty,
                        time = Utils.CreateNumber
                    };
                    HandleCommon.UpdateCacheRet(postd).ConfigureAwait(false);
                    IsSuccess = true;
                    sRemark = "保存参数成功！";
                }
                else
                {
                    sRemark = "保存参数不成功！";
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 获取监控信息
    /// </summary>
    public class MonitorStatusHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "MonitorStatus";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                UploadProcessing.UploadMaps.PostMonitorStatus();
                IsSuccess = true;
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    ///// <summary>
    ///// 云托管 进入监控模式
    ///// </summary>
    //public class EnterMonitorHandle : TcpHandleBase
    //{
    //    /// <summary>
    //    /// 平台下发指令名称
    //    /// </summary>
    //    public override string InstructionName { get; set; } = "EnterMonitor";

    //    /// <summary>
    //    /// 执行处理方法
    //    /// </summary>
    //    /// <param name="jo">上下文接收到的json格式数据</param>
    //    /// <param name="context">TCP服务上下文操作通道对象</param>
    //    /// <param name="sendsession">应答TCP服务基本数据</param>
    //    public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
    //    {
    //        try
    //        {
    //            bool IsSuccess = false;
    //            string sRemark = string.Empty;

    //            string videoIp = Convert.ToString(jo["videoIp"]);
    //            string account = Convert.ToString(jo["account"]);
    //            string password = Convert.ToString(jo["password"]);
    //            string ppsvraddr = Convert.ToString(jo["ppsvraddr"]);

    //            var device = BLL.Device.GetEntityExt("*", $"Device_Category='1' and Device_IP ='{videoIp}'  and  Device_Account!='' and Device_Account is not null and Device_Pwd !='' and Device_Pwd is not null");
    //            if (device != null)
    //            {
    //                P2PVideoConfig pVideoConfig = new P2PVideoConfig
    //                {
    //                    enable = 1,
    //                    mode = 0,
    //                    password = password,
    //                    serverAddr = ppsvraddr,
    //                    relaylist = "",
    //                    user = account
    //                };
    //                if (P2PVideoUtil.SetP2PVideoConfig(videoIp, device.Device_Account, device.Device_Pwd, pVideoConfig))
    //                {
    //                    IsSuccess = true;
    //                    sRemark = "执行成功！";
    //                }
    //                else
    //                {
    //                    IsSuccess = true;
    //                    sRemark = "开启异常！";
    //                }
    //            }

    //            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName, data = new { type = "1", focus = "0" } }));
    //        }
    //        catch (Exception ex)
    //        {
    //              LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
    //            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
    //        }


    //    }
    //}

    ///// <summary>
    ///// 云托管 退出监控模式
    ///// </summary>
    //public class ExitMonitorHandle : TcpHandleBase
    //{
    //    /// <summary>
    //    /// 平台下发指令名称
    //    /// </summary>
    //    public override string InstructionName { get; set; } = "ExitMonitor";

    //    /// <summary>
    //    /// 执行处理方法
    //    /// </summary>
    //    /// <param name="jo">上下文接收到的json格式数据</param>
    //    /// <param name="context">TCP服务上下文操作通道对象</param>
    //    /// <param name="sendsession">应答TCP服务基本数据</param>
    //    public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
    //    {
    //        try
    //        {
    //            bool IsSuccess = false;
    //            string sRemark = string.Empty;

    //            string videoIp = Convert.ToString(jo["videoIp"]);
    //            string account = Convert.ToString(jo["account"]);
    //            string password = Convert.ToString(jo["password"]);
    //            string ppsvraddr = Convert.ToString(jo["ppsvraddr"]);

    //            var device = BLL.Device.GetEntityExt("*", $"Device_Category='1' and Device_IP ='{videoIp}'  and  Device_Account!='' and Device_Account is not null and Device_Pwd !='' and Device_Pwd is not null");
    //            if (device != null)
    //            {
    //                P2PVideoConfig pVideoConfig = new P2PVideoConfig
    //                {
    //                    enable = 0,
    //                    mode = 0,
    //                    password = password,
    //                    serverAddr = ppsvraddr,
    //                    relaylist = "",
    //                    user = account
    //                };
    //                if (P2PVideoUtil.SetP2PVideoConfig(videoIp, device.Device_Account, device.Device_Pwd, pVideoConfig))
    //                {
    //                    IsSuccess = true;
    //                    sRemark = "执行成功！";
    //                }
    //                else
    //                {
    //                    IsSuccess = true;
    //                    sRemark = "开启异常！";
    //                }
    //            }

    //            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName, data = new { type = "1", focus = "0" } }));
    //        }
    //        catch (Exception ex)
    //        {
    //              LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
    //            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
    //        }
    //    }
    //}

    ///// <summary>
    ///// 云托管 获取相机线圈设置
    ///// </summary>
    //public class GetCameraCoilHandle : TcpHandleBase
    //{
    //    /// <summary>
    //    /// 平台下发指令名称
    //    /// </summary>
    //    public override string InstructionName { get; set; } = "GetCameraCoil";

    //    /// <summary>
    //    /// 执行处理方法
    //    /// </summary>
    //    /// <param name="jo">上下文接收到的json格式数据</param>
    //    /// <param name="context">TCP服务上下文操作通道对象</param>
    //    /// <param name="sendsession">应答TCP服务基本数据</param>
    //    public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
    //    {
    //        try
    //        {
    //            bool IsSuccess = false;
    //            string sRemark = string.Empty;

    //            string videoIp = Convert.ToString(jo["videoIp"]);
    //            List<Point> area = null;

    //            var device = BLL.Device.GetEntityExt("*", $"Device_Category='1' and Device_IP ='{videoIp}'  and  Device_Account!='' and Device_Account is not null and Device_Pwd !='' and Device_Pwd is not null");
    //            if (device != null)
    //            {
    //                area = P2PVideoUtil.GetRecognArea(videoIp, device.Device_Account, device.Device_Pwd);

    //                if (area != null && area.Count == 4)
    //                {
    //                    IsSuccess = true;
    //                    sRemark = "获取成功";
    //                }
    //                else
    //                {
    //                    IsSuccess = false;
    //                    sRemark = "获取失败";
    //                }
    //            }

    //            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
    //            {
    //                result = IsSuccess ? 1 : 0,
    //                msg = sRemark,
    //                actionName = InstructionName,
    //                data = area == null ? new
    //                {
    //                    pointA = $"0,0",
    //                    pointB = $"0,0",
    //                    pointC = $"0,0",
    //                    pointD = $"0,0"
    //                } : new
    //                {
    //                    pointA = $"{area[0].x},{area[0].y}",
    //                    pointB = $"{area[1].x},{area[1].y}",
    //                    pointC = $"{area[2].x},{area[2].y}",
    //                    pointD = $"{area[3].x},{area[3].y}"
    //                }
    //            }));
    //        }
    //        catch (Exception ex)
    //        {
    //              LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
    //            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
    //        }
    //    }
    //}

    /// <summary>
    /// 云托管 语音播报
    /// </summary>
    public class VoicePlaybackHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "VoicePlayback,DisplayVLQrcode";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                //多指令先获取指令接收的指令名称
                string sActionName = jo.ContainsKey("actionName") ? Convert.ToString(jo["actionName"]) : "VoicePlayback";
                //获取车道编号
                string ctrlNo = Convert.ToString(jo["ctrlNo"]);
                if (!string.IsNullOrWhiteSpace(ctrlNo))
                {
                    var passway = BLL.Passway._GetEntityByNo(new Model.Passway { Passway_No = ctrlNo }, ctrlNo);
                    if (passway != null)
                    {
                        var postd = new Model.API.ReqPush
                        {
                            act = "mid",
                            tname = "VoicePlayback",
                            guid = Guid.NewGuid().ToString(),
                            data = ctrlNo,
                            time = Utils.CreateNumber
                        };
                        var retData = await HandleCommon.UpdateCacheRet(postd);
                        if (!string.IsNullOrWhiteSpace(Convert.ToString(retData)))
                        {
                            JObject pairs = JObject.Parse(Convert.ToString(retData));
                            bool.TryParse(Convert.ToString(pairs["isSuccess"]), out IsSuccess);
                            sRemark = "获取数据成功！";
                            if (!IsSuccess) { sRemark = "请求岗亭执行命令不成功！"; }
                        }
                        //var srlt = BLL.MiddlewareApi.VoicePlayback(AppCache.GetParking.Parking_No, passway.Passway_SentryHostNo, passway.Passway_No);
                        //if (srlt != null && srlt.success)
                        //{
                        //    if (!string.IsNullOrWhiteSpace(Convert.ToString(srlt.data)))
                        //    {
                        //        JObject keys = JObject.Parse(Convert.ToString(srlt.data));
                        //        bool.TryParse(Convert.ToString(keys["isSuccess"]), out IsSuccess);
                        //    }
                        //}
                        //else
                        //{
                        //    sRemark = "请求岗亭执行命令不成功！";
                        //}
                    }
                    else
                    {
                        sRemark = "查询车道信息不存在";
                    }
                }
                else
                {
                    sRemark = "车道编号不能为空！";
                }


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = sActionName
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 云托管 修改价格
    /// </summary>
    public class UpdateParkOrderMoneyHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "UpdateParkOrderMoney";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string ctrlNo = Convert.ToString(jo["ctrlNo"]);
                string orderNo = Convert.ToString(jo["orderNo"]);
                string totalAmoney = Convert.ToString(jo["totalAmoney"]);

                if (!string.IsNullOrWhiteSpace(ctrlNo))
                {
                    var passway = BLL.Passway._GetEntityByNo(new Model.Passway { Passway_No = ctrlNo }, ctrlNo);
                    if (passway != null)
                    {
                        var srlt = BLL.MiddlewareApi.UpdateParkOrderMoney(AppCache.GetParking.Parking_No, passway.Passway_SentryHostNo, passway.Passway_No, orderNo, totalAmoney);
                        if (srlt != null && srlt.success)
                        {
                            if (!string.IsNullOrWhiteSpace(Convert.ToString(srlt.data)))
                            {
                                JObject keys = JObject.Parse(Convert.ToString(srlt.data));
                                bool.TryParse(Convert.ToString(keys["isSuccess"]), out IsSuccess);
                            }
                        }
                        else
                        {
                            sRemark = "请求岗亭执行命令不成功！";
                        }
                    }
                    else
                    {
                        sRemark = "查询车道信息不存在";
                    }
                }
                else
                {
                    sRemark = "车道编号不能为空！";
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = InstructionName
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 打印小票
    /// </summary>
    public class PullOrderHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullOrder";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string ctrlNo = Convert.ToString(jo["ctrlNo"]);
                string orderNo = Convert.ToString(jo["orderNo"]);
                if (!string.IsNullOrWhiteSpace(ctrlNo))
                {
                    var passway = BLL.Passway._GetEntityByNo(new Model.Passway { Passway_No = ctrlNo }, ctrlNo);
                    if (passway != null)
                    {
                        if (BLL.ConfirmRelease.Results.TryGetValue(ctrlNo, out var data1) &&
                            (data1?.resorder?.resOut?.parkorder?.ParkOrder_No == orderNo || data1?.resorder?.resIn?.parkorder?.ParkOrder_No == orderNo))
                        {
                            var parkDuration = "";
                            var enterTime = DateTimeHelper.GetNowTime();
                            var outTime = DateTimeHelper.GetNowTime();
                            if (data1.passres.code == 4)
                            {
                                parkDuration = "无入场记录";
                                if (data1.resorder?.resOut?.parkorder != null)
                                {
                                    enterTime = data1.resorder.resOut.parkorder.ParkOrder_EnterTime.Value;
                                }
                            }
                            else
                            {
                                var ts = TimeSpan.FromMinutes(data1.payres.parktimemin);
                                parkDuration = $"{ts.Days}天{ts.Hours}小时{ts.Minutes}分钟";
                                enterTime = data1.resorder.resOut.parkorder.ParkOrder_EnterTime.Value;
                            }

                            BroadcastUtil.PrintTicket(data1, "", parkDuration, enterTime.ToString("G"), outTime.ToString("G"));
                            IsSuccess = true;
                            sRemark = "语音播报成功！";
                        }
                        else
                        {
                            sRemark = "岗亭未查询到缴费窗口信息！";
                        }

                        //var srlt = BLL.MiddlewareApi.PullOrder(AppCache.GetParking.Parking_No, passway.Passway_SentryHostNo, passway.Passway_No, orderNo);
                        //if (srlt != null && srlt.success)
                        //{
                        //    if (!string.IsNullOrWhiteSpace(Convert.ToString(srlt.data)))
                        //    {
                        //        JObject keys = JObject.Parse(Convert.ToString(srlt.data));
                        //        bool.TryParse(Convert.ToString(keys["isSuccess"]), out IsSuccess);
                        //    }
                        //}
                        //else
                        //{
                        //    sRemark = "请求岗亭执行命令不成功！";
                        //}
                    }
                    else
                    {
                        sRemark = "查询车道信息不存在";
                    }
                }
                else
                {
                    sRemark = "车道编号不能为空！";
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = InstructionName
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }

            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 手动入场
    /// </summary>
    public class PullManualEnterCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullManualEnterCar";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string passNo = Convert.ToString(jo["ctrlNo"]);
                string carNo = Convert.ToString(jo["carNo"]);


                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息！";
                }
                else
                {
                    #region 发送手动入场

                    Model.API.DeviceOptionsModel optionsModel = new Model.API.DeviceOptionsModel
                    {
                        ParkNo = model.Passway_ParkNo,
                        PasswayNo = passNo,
                        SentryHostNo = model.Passway_SentryHostNo,
                        DataType = "云托管手动入场",
                        BaseData = null,
                        sCarplate = carNo,
                        SendOptions = Model.API.SendOptionsType.PullManualEnterCar
                    };

                    //var res = BLL.MiddlewareApi.SendDeviceOptions(optionsModel);
                    string retSuccess = ""; string retValue = "";
                    var task = await TcpCmdHelper.RunDeviceOption(optionsModel);
                    IsSuccess = task.Item1;
                    sRemark = task.Item2;
                    retSuccess = task.Item3;
                    retValue = task.Item4;
                    if (retSuccess.ToLower().Contains("true")) //预入场开闸成功
                    {
                        IsSuccess = true;
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "云托管手动入场成功-" + sRemark);
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, "云托管手动入场不成功-" + sRemark);
                    }

                    #endregion
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = InstructionName
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 云托管 修改车牌号码
    /// </summary>
    public class PullCloudCarCCCarHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullCloudCarCC";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string oldCarNo = Convert.ToString(jo["oldCarNo"]);
                string newCarNo = Convert.ToString(jo["newCarNo"]);
                string carTypeNo = Convert.ToString(jo["carTypeNo"]);
                string type = Convert.ToString(jo["type"]); // 0-AI大脑  1-人工修正
                if (!string.IsNullOrWhiteSpace(oldCarNo) && !string.IsNullOrWhiteSpace(newCarNo))
                {
                    var parkOrder = BLL.ParkOrder._GetEntityByWhere(new Model.ParkOrder { }, "*", $"ParkOrder_StatusNo='{200}' and  ParkOrder_CarNo='{oldCarNo}'");

                    if (parkOrder == null)
                    {
                        sRemark = "需要修改车牌号码已经不在场内！";
                    }
                    else
                    {
                        var carType = BLL.CarCardType._GetEntityByWhere(new Model.CarCardType { }, "*", $"CarCardType_Category ='{carTypeNo}'");
                        var parkNo = BLL.ParkOrder._GetEntityByWhere(new Model.ParkOrder { }, "*", $"ParkOrder_StatusNo='{200}' and  ParkOrder_CarNo='{newCarNo}'");
                        if (parkNo != null)
                        {
                            sRemark = "修改车牌号码已经存在场内记录，不能修改该车牌号码！";
                        }
                        else
                        {
                            parkOrder.ParkOrder_CarNo = newCarNo;
                            parkOrder.ParkOrder_Remark = type == "0" ? "AI修改车牌" : "人工修改车牌";
                            if (carType != null)
                            {
                                parkOrder.ParkOrder_CarCardType = carType.CarCardType_No;
                                parkOrder.ParkOrder_CarCardTypeName = carType.CarCardType_Name;
                            }

                            BLL.ParkOrder._UpdateByModelByID(parkOrder);

                            List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity(parkOrder.ParkOrder_No);
                            if (details != null || details.Count > 0)
                            {
                                details.ForEach(x =>
                                {
                                    if (carType != null)
                                    {
                                        x.OrderDetail_CarCardType = carType.CarCardType_No;
                                        x.OrderDetail_CarCardTypeName = carType.CarCardType_Name;
                                    }

                                    x.OrderDetail_CarNo = newCarNo;
                                    BLL.OrderDetail._UpdateByModelByID(x);
                                });
                            }

                            PassTool.PassHelperBiz.UpdateCarIn(new Model.ParkGateIn
                            {
                                orderno = parkOrder.ParkOrder_No,
                                carno = newCarNo,
                                carcardtypename = parkOrder.ParkOrder_CarCardTypeName,
                                carcardtypeno = parkOrder.ParkOrder_CarCardType,
                                cartypeno = parkOrder.ParkOrder_CarType,
                                cartypename = parkOrder.ParkOrder_CarTypeName,
                                parkno = parkOrder.ParkOrder_ParkNo,
                                iModify = 1,
                            }, out var item1, out var item2, out var incar);

                            var res = BLL.ParkOrder.CarInComplete(item1, item2, null, incar);
                            if (res > 0)
                            {
                                _ = CustomThreadPool.MiddleTaskPool.QueueTask(null, () =>
                                {
                                    item1.ForEach(x =>
                                    {
                                        if (x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                                            BLL.PushEvent.UpdateCar(AppBasicCache.GetParking?.Parking_Key, x.ParkOrder_No, x.ParkOrder_CarNo);
                                    });

                                    if (AppBasicCache.IsSendTcp)
                                    {
                                        var res = Library.PushTools.SendToClient(Model.API.PushAction.Edit, null, (item1, item2), AppBasicCache.GetParking?.Parking_Secret, "carin", DataTypeEnum.EnterCar, $"人工修改入场{newCarNo}");
                                    }
                                    return Task.CompletedTask;
                                });

                                IsSuccess = true;
                                sRemark = "修改车牌信息成功！";
                            }
                            else
                            {

                                IsSuccess = false;
                                sRemark = "修改车牌信息失败！";
                            }

                            ////通知服务器
                            //BLL.ParkApi.UpdateCarIn(new Model.ParkGateIn
                            //{
                            //    orderno = parkOrder.ParkOrder_No,
                            //    carno = newCarNo,
                            //    carcardtypename = parkOrder.ParkOrder_CarCardTypeName,
                            //    carcardtypeno = parkOrder.ParkOrder_CarCardType,
                            //    cartypeno = parkOrder.ParkOrder_CarType,
                            //    cartypename = parkOrder.ParkOrder_CarTypeName,
                            //    parkno = parkOrder.ParkOrder_ParkNo,
                            //    iModify = 1,
                            //});

                            //IsSuccess = true;
                            //sRemark = "修改车牌信息成功！";
                        }
                    }
                }
                else
                {
                    sRemark = "原始车牌或新车牌号码不能为空";
                }


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = InstructionName
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 车牌矫正出场
    /// </summary>
    public class PullMateCarNoHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullMateCarNo";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string orderNo = Convert.ToString(jo["orderNo"]); //订车订单号
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                string newCarNo = Convert.ToString(jo["newCarNo"]); //匹配的车牌号码
                string ctrlNo = Convert.ToString(jo["ctrlNo"]); //控制机号码
                string carTypeNo = Convert.ToString(jo["carTypeNo"]); //卡类

                if (!string.IsNullOrWhiteSpace(ctrlNo))
                {
                    var passway = BLL.Passway._GetEntityByNo(new Model.Passway { Passway_No = ctrlNo }, ctrlNo);
                    if (passway != null)
                    {

                        var postd = new Model.API.ReqPush
                        {
                            act = "mid",
                            tname = "PullMateCarNo",
                            guid = Guid.NewGuid().ToString(),
                            data = Common.TyziTools.Json.ToString(new
                            {
                                spassno = ctrlNo,
                                sorderNo = orderNo,
                                scarNo = carNo,
                                snewCarNo = newCarNo,
                                scarTypeNo = carTypeNo,
                            }),
                            time = Utils.CreateNumber
                        };
                        var retdata = await HandleCommon.UpdateCacheRet(postd);
                        if (!string.IsNullOrWhiteSpace(Convert.ToString(retdata)))
                        {
                            JObject pairs = JObject.Parse(Convert.ToString(retdata));
                            bool.TryParse(Convert.ToString(pairs["isSuccess"]), out IsSuccess);
                            sRemark = "获取数据成功！";
                            if (!IsSuccess) sRemark = "获取数据失败！";
                        }

                        //var srlt = BLL.MiddlewareApi.PullMateCarNo(AppCache.GetParking.Parking_No, passway.Passway_SentryHostNo, passway.Passway_No, orderNo, carNo, newCarNo, carTypeNo);
                        //if (srlt != null && srlt.success)
                        //{
                        //    if (!string.IsNullOrWhiteSpace(Convert.ToString(srlt.data)))
                        //    {
                        //        JObject keys = JObject.Parse(Convert.ToString(srlt.data));
                        //        bool.TryParse(Convert.ToString(keys["isSuccess"]), out IsSuccess);
                        //    }
                        //}
                        //else
                        //{
                        //    sRemark = "请求岗亭执行命令不成功！";
                        //}
                    }
                    else
                    {
                        sRemark = "查询车道信息不存在";
                    }
                }
                else
                {
                    sRemark = "车道编号不能为空！";
                }


                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new
                {
                    result = IsSuccess ? 1 : 0,
                    msg = sRemark,
                    actionName = InstructionName
                }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    /// <summary>
    /// 云托管 同意接听通话
    /// </summary>
    public class AgreeCallHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "AgreeCall";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string callid = Convert.ToString(jo["eventNo"]); //通话ID
                string talkNo = Convert.ToString(jo["terminalId"]); //设备编码

                if (string.IsNullOrEmpty(callid) || string.IsNullOrEmpty(talkNo))
                {
                    SendTCPDatas(context, sendsession, false, "参数不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                Model.CallRecord crModel = BLL.CallRecord._GetEntityByNo(new Model.CallRecord(), callid);
                if (crModel == null)
                {
                    SendTCPDatas(context, sendsession, false, "通话记录不存在", InstructionName);
                    return Task.CompletedTask;
                }

                //crModel.CallRecord_AnswerNo = talkNo;
                crModel.CallRecord_AnswerName = "云托管";
                crModel.CallRecord_AnswerTime = DateTimeHelper.GetNowTime();

                var r = BLL.MiddlewareApi.AgreeCall(crModel);
                if (!r.success)
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}同意接听通话下发失败：{r.errmsg}，{callid}");
                    SendTCPDatas(context, sendsession, false, r.errmsg, InstructionName);
                    return Task.CompletedTask;
                }

                SendTCPDatas(context, sendsession, true, "下发成功", InstructionName);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 向车道设备下发对讲信息
    /// </summary>
    public class StartIntercomHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "StartIntercom";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string callid = Convert.ToString(jo["eventNo"]); //通话ID
                string talkNo = Convert.ToString(jo["terminalId"]); //设备编码
                string account = Convert.ToString(jo["account"]); //通话账号
                string pwd = Convert.ToString(jo["pwd"]); //通话密码

                if (string.IsNullOrEmpty(callid) || string.IsNullOrEmpty(talkNo)
                                                 || string.IsNullOrEmpty(account) || string.IsNullOrEmpty(pwd))
                {
                    SendTCPDatas(context, sendsession, false, "参数不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                Model.CallRecord crModel = BLL.CallRecord._GetEntityByNo(new Model.CallRecord(), callid);
                if (crModel == null)
                {
                    SendTCPDatas(context, sendsession, false, "通话记录不存在", InstructionName);
                    return Task.CompletedTask;
                }

                //crModel.CallRecord_AnswerNo = talkNo;
                crModel.CallRecord_AnswerName = "云托管";
                crModel.CallRecord_AnswerTime = DateTimeHelper.GetNowTime();
                crModel.CallRecord_Account = account;

                var r = BLL.MiddlewareApi.StartIntercom(crModel, pwd);
                if (!r.success)
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}向车道设备下发对讲信息失败：{r.errmsg}，{callid}");
                    SendTCPDatas(context, sendsession, false, r.errmsg, InstructionName);
                    return Task.CompletedTask;
                }

                SendTCPDatas(context, sendsession, true, "下发成功", InstructionName);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 云托管 向车道设备关闭对讲
    /// </summary>
    public class StopIntercomHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "StopIntercom";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                string callid = Convert.ToString(jo["eventNo"]); //通话ID
                string talkNo = Convert.ToString(jo["terminalId"]); //接听编码


                if (string.IsNullOrEmpty(callid) || string.IsNullOrEmpty(talkNo))
                {
                    SendTCPDatas(context, sendsession, false, "参数不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                Model.CallRecord crModel = BLL.CallRecord._GetEntityByNo(new Model.CallRecord(), callid);
                if (crModel == null)
                {
                    SendTCPDatas(context, sendsession, false, "通话记录不存在", InstructionName);
                    return Task.CompletedTask;
                }

                crModel.CallRecord_Status = 0;
                crModel.CallRecord_EndTime = DateTimeHelper.GetNowTime();

                var r = BLL.MiddlewareApi.StopIntercom(crModel);
                if (!r.success)
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}向车道设备下发关闭对讲失败：{r.errmsg}，{callid}");
                    SendTCPDatas(context, sendsession, false, r.errmsg, InstructionName);
                    return Task.CompletedTask;
                }

                SendTCPDatas(context, sendsession, true, "下发成功", InstructionName);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    #endregion

    #region 固定车管理

    /// <summary>
    /// 下发人事信息指令处理类
    /// </summary>
    public class PersonPushHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PersonPush";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;
                string userNo = Convert.ToString(jo["userNo"]); //用户编号-对应车位号
                string userName = Convert.ToString(jo["userName"]); //对应平台联系人
                string sex = Convert.ToString(jo["sex"]); //对应平台性别
                string homeAddress = Convert.ToString(jo["homeAddress"]); //对应平台家庭住址
                string mobNumber = Convert.ToString(jo["mobNumber"]); //对应平台手机号
                string carSpalcesNum = Convert.ToString(jo["carSpalcesNum"]); //车位个数
                string parkspace = Convert.ToString(jo["parkNo"]);

                if (string.IsNullOrEmpty(userNo))
                {
                    SendTCPDatas(context, sendsession, false, "参数错误", InstructionName);
                    return Task.CompletedTask;
                }

                //使用默认参数(月租车A)
                Model.CarCardType card = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='3652' AND CarCardType_IsDefault=1");

                //授权控制机
                string sMachineNo = string.Empty;
                char[] c1 = new char[32];
                if (jo.Property("machineNo") != null)
                {
                    sMachineNo = Convert.ToString(jo["machineNo"]);
                    if (string.IsNullOrEmpty(sMachineNo))
                    {
                        sMachineNo = "ffffffff";
                    }

                    sMachineNo = Convert.ToString(Convert.ToInt32(sMachineNo, 16), 2).ToString().PadLeft(32, '0');
                    c1 = sMachineNo.ToCharArray();
                }

                string spasslink = string.Empty;
                for (int iv = 1; iv <= c1.Length; iv++)
                {
                    if (c1[iv - 1] != null && c1[iv - 1] == '1')
                    {
                        if (string.IsNullOrWhiteSpace(spasslink))
                        {
                            spasslink += $"{iv}";
                        }
                        else
                        {
                            spasslink += $",{iv}";
                        }
                    }
                }

                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"下发人事信息指令机号:{spasslink}", LogLevel.Info);

                var model = BLL.Owner.NewOwner(card?.CarCardType_No, card?.CarCardType_Type, AppCache.GetParking.Parking_No, userNo, userName, sex, mobNumber, homeAddress, carSpalcesNum, spasslink, card?.CarCardType_WhiteEnable, out var stop);
                model.Owner_ParkSpace = parkspace;

                //var data = new Model.API.PushResultParse.CarOwner()
                //{
                //    Item1 = model,
                //    Item3 = new List<Model.StopSpace>() { stop }
                //};

                var result = BLL.Owner.PersonPush(model, stop);
                if (result >= 0)
                {
                    //var r = BLL.MiddlewareApi.PersonPush(data);
                    //if (!r.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}下发人事信息分发失败：{r.errmsg}，{userNo}-{userName}");
                    //}
                    SendTCPDatas(context, sendsession, true, "下发人事信息成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}下发人事信息执行SQL错误：{TyziTools.Json.ToString(model)}");
                    SendTCPDatas(context, sendsession, false, "下发人事信息失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 下发月卡发行信息指令处理类
    /// </summary>
    public class MthCarIssuePushHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "MthCarIssuePush";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                //bool IsSuccess = false;
                string sRemark = jo.ContainsKey("remark") ? Convert.ToString(jo["remark"]) : "平台下发";
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                string carCardType = Convert.ToString(jo["carType"]); //计费卡类（车牌类型）
                string userNo = Convert.ToString(jo["userNo"]); //用户编号（车主编码）
                string beginTime = Convert.ToString(jo["beginTime"]); //有效起始时间
                string endTime = Convert.ToString(jo["endTime"]); //有效截至时间
                string iusseTime = Convert.ToString(jo["iusseTime"]); //发行时间
                string atname = Convert.ToString(jo["actionName"]);
                string carType = "蓝牌车"; //默认蓝牌车

                //查询车牌类型
                Model.CarCardType cct = BLL.CarCardType.GetEntity("*", $"CarCardType_Category='{carCardType}' and CarCardType_IsDefault=1");
                if (cct == null)
                {
                    SendTCPDatas(context, sendsession, false, "未找到计费卡类信息", InstructionName);
                    return Task.CompletedTask;
                }

                //查询车牌颜色
                Model.CarType ct = BLL.CarType.GetEntity("*", $"CarType_IsDefault=1 and CarType_Name='{carType}'");
                if (ct == null)
                {
                    SendTCPDatas(context, sendsession, false, "未找到车牌颜色信息", InstructionName);
                    return Task.CompletedTask;
                }

                List<Model.Car> carList = null;
                var owner = BLL.Owner.GetEntity("*", $"Owner_Space='{userNo}'");
                if (owner != null)
                {
                    owner.Owner_Remark = sRemark;
                    owner.Owner_CardTypeNo = cct.CarCardType_No;
                    owner.Owner_CardType = cct.CarCardType_Type;
                    carList = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{userNo}'");
                }

                //车辆
                Model.Car car = new Model.Car()
                {
                    Car_No = Utils.CreateNumber,
                    Car_CarNo = carNo,
                    Car_BeginTime = Utils.StrToDateTime(beginTime),
                    Car_EndTime = Utils.ObjectToDateTime(endTime),
                    Car_TypeNo = cct.CarCardType_No,
                    Car_VehicleTypeNo = ct.CarType_No,
                    Car_ParkingNo = AppCache.GetParking.Parking_No,
                    Car_AddTime = DateTimeHelper.GetNowTime(),
                    Car_OwnerNo = owner?.Owner_No ?? userNo,
                    Car_OwnerName = owner?.Owner_Name ?? "",
                    Car_OwnerSpace = owner?.Owner_Space ?? userNo,
                    Car_Category = cct.CarCardType_Category,
                    Car_IsMoreCar = cct.CarCardType_IsMoreCar,
                    Car_OnLine = 2,
                    Car_Status = 1,
                    Car_Balance = 0,
                    Car_EnableOffline = owner?.Owner_EnableOffline ?? cct.CarCardType_WhiteEnable ?? 0,
                    //Car_Remark = !string.IsNullOrEmpty(sRemark) ? sRemark : "平台下发"
                };
                car.Car_BeginTime = Utils.StrToDateTime(car.Car_BeginTime.Value.ToString("yyyy-MM-dd 00:00:00"));
                car.Car_EndTime = Utils.StrToDateTime(car.Car_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));

                if (owner != null)
                {
                    owner.Owner_StartTime = car.Car_BeginTime;
                    owner.Owner_EndTime = car.Car_EndTime;
                }

                //新增月租车
                var res = BLL.Car.AddCarOwner(car, owner);
                if (res > 0)
                {
                    DataCache.ExecutionOrder.Set(car.Car_OwnerNo, 1);
                    if (owner != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, owner.Owner_No, owner);
                    AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, car.Car_CarNo, car);

                    _ = CustomThreadPool.MiddleTaskPool.QueueTask(null, async () =>
                    {
                        await Task.Delay(50);
                        //月租车发行信息分发
                        try
                        {
                            int suceess = 0; int fail = 0;
                            HandleCommon.SyncCarWhite(new List<string>() { car.Car_CarNo }, "", ref suceess, ref fail);

                            //var r = BLL.MiddlewareApi.MthCarIssuePush(car, owner);
                            //if (!r.success)
                            //{
                            //    LogManagementMap.WriteToFileException(null, $"{InstructionName}下发月卡发行信息分发失败：{r.errmsg}，{carNo}");
                            //}

                            if (carList != null && carList.Count > 0)
                            {
                                foreach (var carItem in carList)
                                {
                                    if (carItem.Car_CarNo != car.Car_CarNo && carItem.Car_TypeNo != car.Car_TypeNo)
                                    {
                                        carItem.Car_TypeNo = car.Car_TypeNo;
                                        carItem.Car_IsMoreCar = cct.CarCardType_IsMoreCar;
                                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, carItem.Car_CarNo, carItem);

                                        //var ret = BLL.MiddlewareApi.MthCarIssuePush(carItem, null);
                                        //if (!r.success)
                                        //{
                                        //    LogManagementMap.WriteToFileException(null, $"{InstructionName}下发月卡发行信息分发失败：{r.errmsg}，{carNo}");
                                        //}
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(null, $"{InstructionName}下发月卡发行信息分发失败：{carNo},{ex.ToString()}");
                        }

                        //清缴场内费用
                        try
                        {
                            bool orderHandleResult = false;
                            List<Model.AddInParkTempCar> addInCar = new List<Model.AddInParkTempCar>();
                            List<Model.CarExt> allcars = BLL.Car.GetCarExtAllEntity("*", $"Car_OwnerNo='{owner?.Owner_No}'");
                            List<Model.CarExt> cars = new List<CarExt>() { TyziTools.Json.ToObject<Model.CarExt>(TyziTools.Json.ToString(car)) };
                            if (allcars.Find(x => x.Car_CarNo == car.Car_CarNo) == null) allcars.AddRange(cars);
                            List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{userNo}'");
                            string errmsg = "";

                            //清缴场内费用
                            var ret = BLL.Owner.PayedInParkCarFee(null, owner, cct, allcars, cars, spaces, ref addInCar, new AdminSession() { Admins_Name = "平台下发" }, out errmsg, out orderHandleResult);
                            if (orderHandleResult && addInCar.Count > 0)
                            {
                                foreach (var item in addInCar)
                                {
                                    var orders1 = item.orders;
                                    var details = item.details;

                                    var orderRes = BLL.ParkOrder.CarInComplete(orders1, details);
                                    if (orderRes > 0)
                                    {
                                        var outOrder = orders1.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out);
                                        var outDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == outOrder.ParkOrder_No);
                                        //BLL.ParkOrderApi.CarOut("", new ResBodyDataOut(new List<Model.ParkOrder>() { outOrder }, outDetails, null, null, null, null, null, null));
                                        _ = CustomThreadPool.MiddleTaskPool.QueueTask(null, () =>
                                        {
                                            if (AppBasicCache.IsWritePushEventMsg)
                                            {
                                                //上传出场记录到云平台
                                                if (outOrder != null)
                                                {
                                                    var item = outOrder;
                                                    if (item != null)
                                                    {
                                                        int gate = BLL.Passway.GetPasswayGateType(item.ParkOrder_OutPasswayNo);
                                                        if (gate == 0)
                                                        {
                                                            BLL.PushEvent.OutCar(AppBasicCache.GetParking?.Parking_Key, item);
                                                            BLL.PushEvent.SendParkSpace(AppBasicCache.GetParking?.Parking_No);
                                                        }
                                                    }
                                                }
                                            }

                                            var reqData = new ResBodyDataOut(new List<Model.ParkOrder>() { outOrder }, outDetails, null, null, null, null, null, null);

                                            //收费金额小于应收金额
                                            BLL.AbnorOrder.InsertByCarOut(reqData, out var d);
                                            if (d != null)
                                                BLL.PushEvent.AboutCar(AppBasicCache.GetParking?.Parking_Key, d);

                                            if (AppBasicCache.IsSendTcp)
                                            {
                                                var res = Library.PushTools.SendToClient(Model.API.PushAction.Edit, null, reqData, AppBasicCache.GetParking?.Parking_Secret, "carout", DataTypeEnum.OutCar, $"车辆出场{string.Join(",", reqData.Item1?.Select(x => x.ParkOrder_CarNo))}");
                                            }

                                            return Task.CompletedTask;
                                        });

                                        var inOrder = orders1.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In);
                                        var inDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == inOrder.ParkOrder_No);

                                        var rempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { inOrder }, inDetails);
                                        //BLL.ParkOrderApi.CarIn("", rempdata);
                                        _ = CustomThreadPool.MiddleTaskPool.QueueTask(null, () =>
                                        {
                                            if (AppBasicCache.IsWritePushEventMsg)
                                            {
                                                rempdata.Item1.ForEach(item =>
                                                {
                                                    if (item != null)
                                                    {
                                                        if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                                                        {
                                                            BLL.PushEvent.EnterCar(AppBasicCache.GetParking?.Parking_Key, item);
                                                            //BLL.PushThirdEvent.EnterCar(parking.Parking_Key, item);
                                                        }
                                                        else if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.InClose || item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Close)
                                                        {
                                                            BLL.PushEvent.CloseCar(AppBasicCache.GetParking?.Parking_Key, item, item.ParkOrder_Remark);
                                                        }
                                                        else if (item.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out)
                                                        {
                                                            BLL.PushEvent.OutCar(AppBasicCache.GetParking?.Parking_Key, item);
                                                            //BLL.PushThirdEvent.OutCar(parking.Parking_Key, item);
                                                        }
                                                    }
                                                });

                                                BLL.PushEvent.SendParkSpace(AppBasicCache.GetParking?.Parking_No);
                                            }
                                            if (AppBasicCache.IsSendTcp)
                                            {
                                                var res = Library.PushTools.SendToClient(Model.API.PushAction.Edit, null, rempdata, AppBasicCache.GetParking?.Parking_Secret, "carin", DataTypeEnum.EnterCar, $"车辆入场{string.Join(",", rempdata.Item1?.Select(x => x.ParkOrder_CarNo))}");
                                            }
                                            return Task.CompletedTask;
                                        });
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(null, $"{InstructionName}下发月卡发行场内车辆处理失败：{carNo},{ex.ToString()}");
                        }

                        SentryBox.CommHelper.CheckConfirmResultForCarNo(car.Car_CarNo, CloseNoInPark: false);
                    });
                    BLL.UserLogs.AddApiLog("月租车信息下发成功", TyziTools.Json.ToString(jo));
                    SendTCPDatas(context, sendsession, true, "新增月租车成功", InstructionName);
                }
                else
                {
                    BLL.UserLogs.AddApiLog("月租车信息下发失败", TyziTools.Json.ToString(jo));
                    LogManagementMap.WriteToFileException(null, $"{InstructionName} 新增月租车执行SQL失败：{TyziTools.Json.ToString(car)}");
                    SendTCPDatas(context, sendsession, false, "新增月租车失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
                BLL.UserLogs.AddApiLog("月租车信息下发异常", TyziTools.Json.ToString(jo));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 月租车注销指令处理类
    /// </summary>
    public class MthCarFailPushHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "MthCarFailPush";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                string deleteCarSpace = Convert.ToString(jo["deleteCarSpace"]); //是否删除车位信息
                if (string.IsNullOrEmpty(carNo))
                {
                    SendTCPDatas(context, sendsession, false, "车牌号码不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                Model.Car Car = BLL.Car.GetEntityByCarNo(carNo);
                if (Car == null)
                {
                    SendTCPDatas(context, sendsession, true, "车辆信息不存在", InstructionName);
                    return Task.CompletedTask;
                }

                Model.Owner owner = null;
                List<Model.StopSpace> spaces = null;

                bool delOwner = false;
                if (deleteCarSpace.ToLower() == "true")
                {
                    var ortherCar = BLL.Car.GetEntity("Car_No", $"Car_OwnerNo='{Car.Car_OwnerNo}' and Car_CarNo!='{carNo}'");
                    if (ortherCar == null)
                    {
                        owner = BLL.Owner.GetEntity(Car.Car_OwnerNo);
                        if (owner != null) spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "StopSpace_No", $"StopSpace_OwnerNo='{Car.Car_OwnerNo}'");
                        delOwner = true;
                    }
                }

                var result = BLL.Owner.Delete(owner, new List<Car>() { Car }, spaces); //BLL.Car.DeleteByNo(Car, false);
                if (result > 0)
                {
                    try
                    {
                        if (owner == null) owner = BLL.Owner.GetEntity(Car.Car_OwnerNo);
                        if (owner != null)
                        {
                            //若车辆已在场内,则修改场内订单
                            BLL.Owner.DelCarUseSpace(owner, null, new List<Model.Car>() { Car });

                            BLL.CarUnbound.AddOrUpdate(new Model.CarUnbound()
                            {
                                CarUnbound_No = "UC" + Car.Car_No,
                                CarUnbound_CarNo = Car.Car_CarNo,
                                CarUnbound_OwnerNo = Car.Car_OwnerNo,
                                CarUnbound_ParkNo = Car.Car_ParkingNo,
                                CarUnbound_AddTime = DateTimeHelper.GetNowTime(),
                                CarUnbound_OwnerName = Car.Car_OwnerName,
                                CarUnbound_OwnerSpace = owner?.Owner_Space,
                                CarUnbound_CarCardTypeNo = owner?.Owner_CardTypeNo,
                                CarUnbound_AdminName = "平台注销",
                                CarUnbound_BeginTime = owner?.Owner_StartTime ?? Car.Car_BeginTime,
                                CarUnbound_EndTime = owner?.Owner_EndTime ?? Car.Car_EndTime,
                                CarUnbound_CarTypeNo = Car?.Car_VehicleTypeNo,
                                CarUnbound_IsMoreCar = Car.Car_IsMoreCar,
                                CarUnbound_RegTime = owner?.Owner_AddTime ?? Car.Car_AddTime,
                            });

                            HandleCommon.UpdateCache(Model.API.PushAction.Delete.ToString(), (Car, (delOwner ? owner : null), spaces), "MthCarFailPush2");
                            //var r = BLL.MiddlewareApi.MthCarFailPush2(Car, (delOwner ? owner : null), spaces);
                            //if (!r.success)
                            //{
                            //    LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车注销分发失败：{r.errmsg}，{carNo}");
                            //}
                        }
                        else
                        {
                            LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车注销，未找到车位信息：{carNo}");
                        }
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车注销后执行业务逻辑异常：{TyziTools.Json.ToString(Car)}，{e.ToString()}");
                    }

                    SendTCPDatas(context, sendsession, true, "月租车注销成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车注销执行SQL错误：{TyziTools.Json.ToString(Car)}");
                    SendTCPDatas(context, sendsession, false, "月租车注销失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 月租车充值指令处理类
    /// </summary>
    public class MthCarChargePushHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "MthCarChargePush";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                DateTime beginTime = Convert.ToDateTime(jo["beginTime"]); //有效开始日期
                DateTime endTime = Utils.ObjectToDateTime(Utils.ObjectToDateTime(jo["endTime"]).ToString("yyyy-MM-dd 23:59:59")); //有效结束日期
                string chargeTime = Convert.ToString(jo["chargeTime"]); //缴费时间
                decimal chargeMoney = Convert.ToDecimal(jo["chargeMoney"]); //应收金额
                decimal chargePayedMoney = jo["chargePayedMoney"] == null ? chargeMoney : Convert.ToDecimal(jo["chargePayedMoney"]); //实收金额
                string carSpaceNo = Convert.ToString(jo["carSpaceNo"]); //车位编号
                string payType = Convert.ToString(jo["payType"]);

                if (string.IsNullOrEmpty(carNo))
                {
                    SendTCPDatas(context, sendsession, false, "车牌号码不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                //查询车辆信息
                Model.Car car = BLL.Car.GetEntityByCarNo(carNo);
                if (car == null)
                {
                    SendTCPDatas(context, sendsession, true, "车辆信息不存在", InstructionName);
                    return Task.CompletedTask;
                }

                var card = BLL.CarCardType.GetEntity(car.Car_TypeNo);
                if (card == null)
                {
                    SendTCPDatas(context, sendsession, true, "车牌类型不存在", InstructionName);
                    return Task.CompletedTask;
                }

                Model.Owner owner = null;

                var oldEndTime = car.Car_EndTime;
                car.Car_BeginTime = beginTime;
                car.Car_EndTime = endTime;

                owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
                if (owner == null)
                {
                    SendTCPDatas(context, sendsession, true, "车主信息不存在", InstructionName);
                    return Task.CompletedTask;
                }

                owner.Owner_StartTime = beginTime;
                owner.Owner_EndTime = endTime;
                //车辆信息
                List<Model.Car> cars = cars = BLL.Car.GetAllEntity("*", $"Car_OwnerNo='{owner.Owner_No}'");
                ;

                string carSql = string.Empty;
                //遍历cars，拼接sql字符串
                for (var i = 0; i < cars.Count; i++)
                {
                    if (i == 0)
                    {
                        carSql += $"PayOrder_CarNo='{cars[i].Car_CarNo}'";
                    }
                    else
                    {
                        carSql += $" OR PayOrder_CarNo='{cars[i].Car_CarNo}'";
                    }
                }

                //缴费信息
                Model.PayOrder payorder = null;
                //旧的缴费信息
                var oldPayorder = BLL.PayOrder.GetEntity("PayOrder_No", $"PayOrder_PayedTime='{chargeTime}' AND ({carSql}) AND PayOrder_PayedMoney='{chargePayedMoney}' AND PayOrder_Status=1 AND PayOrder_PayTypeCode='{payType}'");
                if (oldPayorder == null)
                {
                    payorder = new Model.PayOrder()
                    {
                        PayOrder_AdminID = 0,
                        PayOrder_CarCardTypeNo = car.Car_TypeNo,
                        PayOrder_CarTypeNo = car.Car_VehicleTypeNo,
                        PayOrder_CarNo = carNo,
                        PayOrder_CouponRecordNo = null,
                        PayOrder_ParkKey = AppCache.GetParking.Parking_Key,
                        PayOrder_ParkNo = AppCache.GetParking.Parking_No,
                        PayOrder_PayedTime = Utils.StrToDateTime(chargeTime),
                        PayOrder_PayType = 0,
                        PayOrder_PayTypeCode = payType,
                        PayOrder_Status = 1,
                        PayOrder_Time = DateTimeHelper.GetNowTime(),
                        PayOrder_MonthBeginTime = car.Car_BeginTime,
                        PayOrder_DayTimeCount = (car.Car_EndTime - car.Car_BeginTime).Value.Days,
                        PayOrder_MonthEndTime = car.Car_EndTime,
                        PayOrder_MonthEffTime = oldEndTime,
                        PayOrder_Money = chargeMoney,
                        PayOrder_PayedMoney = chargePayedMoney,
                        PayOrder_Category = car.Car_Category,
                        PayOrder_TimeCountDesc = "",
                        PayOrder_OwnerSpace = owner.Owner_Space,
                    };
                    payorder = BLL.PayOrder.CreatePayOrder(true, payorder, AppCache.GetParking.Parking_Key, "MC");
                    payorder.PayOrder_DiscountMoney = payorder.PayOrder_Money - payorder.PayOrder_PayedMoney;
                    payorder.PayOrder_OwnerNo = owner?.Owner_No;
                    payorder.PayOrder_OwnerName = owner?.Owner_Name;
                    payorder.PayOrder_OrderTypeNo = Model.EnumOrderType.OwnerCharge.ToString();
                    payorder.PayOrder_Desc = "车位续期";


                    //List<Model.CarCardType> cardTypes = BLL.CarCardType.GetAllEntity("*", $"CarCardType_IsMoreCar=1");
                    //var cardNos = cardTypes.Select(x => x.CarCardType_No).ToList();
                    //var spaceCars = cars.FindAll(x => cardNos.Contains(x.Car_TypeNo)).ToList();
                    payorder.PayOrder_CarNos = string.Join(",", cars?.Take(50).Select(a => a.Car_CarNo).ToList());
                }

                //仅修改[多位多车]类型车辆的有效期
                if (cars != null && cars.Count > 0)
                {
                    cars.ForEach(x =>
                    {
                        x.Car_BeginTime = owner.Owner_StartTime;
                        x.Car_EndTime = owner.Owner_EndTime;
                        if (x.Car_EndTime != null) x.Car_EndTime = Utils.StrToDateTime(x.Car_EndTime.Value.ToString("yyyy-MM-dd 23:59:59"));
                    });
                }

                Model.PayColl payColl = null;
                if (payorder != null)
                {
                    List<Model.PayPart> payPartList = BLL.CommonBLL.CreatePayPartList(null, payorder, 2);
                    payColl = new Model.PayColl() { payOrderList = new List<Model.PayOrder>() { payorder }, payPartList = payPartList };
                }

                var res = BLL.Owner.OnSpacePayCharge(owner, payColl, cars);
                if (res > 0)
                {
                    _ = CustomThreadPool.MiddleTaskPool.QueueTask(null, async () =>
                     {
                         await Task.Delay(1000);
                         var status = DataCache.ExecutionOrder.Get(owner.Owner_No);
                         if (status == 1)
                         {
                             DataCache.ExecutionOrder.Del(owner.Owner_No);
                             await Task.Delay(1000);
                         }

                         //清缴场内费用
                         try
                         {
                             bool orderHandleResult = false;
                             List<Model.AddInParkTempCar> addInCar = new List<Model.AddInParkTempCar>();
                             List<Model.CarExt> allcars = BLL.Car.GetCarExtAllEntity("*", $"Car_OwnerNo='{owner?.Owner_No}'");
                             List<Model.CarExt> cars = new List<CarExt>() { TyziTools.Json.ToObject<Model.CarExt>(TyziTools.Json.ToString(car)) };
                             if (allcars.Find(x => x.Car_CarNo == car.Car_CarNo) == null) allcars.AddRange(cars);
                             List<Model.StopSpace> spaces = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"StopSpace_OwnerNo='{owner.Owner_No}'");
                             string errmsg = "";

                             //清缴场内费用
                             var ret = BLL.Owner.PayedInParkCarFee(null, owner, card, allcars, cars, spaces, ref addInCar, new AdminSession() { Admins_Name = "平台下发" }, out errmsg, out orderHandleResult, true);
                             if (orderHandleResult && addInCar.Count > 0)
                             {
                                 foreach (var item in addInCar)
                                 {
                                     var orders1 = item.orders;
                                     var details = item.details;

                                     var orderRes = BLL.ParkOrder.CarInComplete(orders1, details);
                                     if (orderRes > 0)
                                     {
                                         var outOrder = orders1.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out);
                                         var outDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == outOrder.ParkOrder_No);
                                         BLL.ParkOrderApi.CarOut("", new ResBodyDataOut(new List<Model.ParkOrder>() { outOrder }, outDetails, null, null, null, null, null, null));

                                         var inOrder = orders1.Find(x => x.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In);
                                         var inDetails = details.FindAll(x => x.OrderDetail_ParkOrderNo == inOrder.ParkOrder_No);

                                         var rempdata = new Model.ResBodyDataIn(new List<Model.ParkOrder> { inOrder }, inDetails);
                                         BLL.ParkOrderApi.CarIn("", rempdata);
                                     }
                                 }
                             }
                         }
                         catch (Exception ex)
                         {
                             LogManagementMap.WriteToFileException(null, $"{InstructionName}下发月卡发行场内车辆处理失败：{carNo},{ex.ToString()}");
                         }

                         foreach (var item in cars)
                         {
                             HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), (item, owner, payColl), "mthcarcharge");
                             //var r = BLL.MiddlewareApi.MthCarChargePush(item, owner, payColl);
                             //if (!r.success)
                             //{
                             //    LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车充值下发分发失败：{r.errmsg}，{car.Car_CarNo}");
                             //}
                         }
                     });

                    SendTCPDatas(context, sendsession, true, "月租车充值下发成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车充值下发执行SQL错误：{TyziTools.Json.ToString(car)}");
                    SendTCPDatas(context, sendsession, false, "月租车充值下发失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 黑名单车辆推送指令处理类
    /// </summary>
    public class CarBlackListHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CarBlackList";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                string beginTime = Convert.ToString(jo["beginTime"]); //开始时间
                string endTime = Convert.ToString(jo["endTime"]); //结束时间
                string remark = Convert.ToString(jo["remark"]); //备注
                int status = Convert.ToInt32(Convert.ToString(jo["status"])); //1有效，0为删除注销
                string userName = Convert.ToString(jo["userName"] == null ? "" : jo["userName"]); //联系人
                string phone = Convert.ToString(jo["phone"] == null ? "" : jo["phone"]); //手机号

                if (string.IsNullOrEmpty(carNo))
                {
                    SendTCPDatas(context, sendsession, false, "车牌号码不能为空", InstructionName);
                    return Task.CompletedTask;
                }

                int result = 0;
                if (status == 1)
                {
                    Model.BlackList blModel = new BlackList();
                    blModel.BlackList_No = Utils.CreateNumberWith("BL");
                    blModel.BlackList_CarNo = carNo;
                    blModel.BlackList_BeginTime = Utils.StrToDateTime(beginTime);
                    blModel.BlackList_EndTime = Utils.StrToDateTime(endTime);
                    blModel.BlackList_Remark = remark;
                    blModel.BlackList_AddTime = DateTimeHelper.GetNowTime();
                    blModel.BlackList_ParkNo = AppCache.GetParking.Parking_No;
                    blModel.BlackList_Name = userName;
                    blModel.BlackList_Phone = phone;
                    blModel.Blacklist_Status = 1;
                    result = BLL.BaseBLL._Insert(blModel);
                    if (result >= 0)
                    {
                        var r = BLL.MiddlewareApi.CarBlackList(blModel);
                        if (!r.success)
                        {
                            LogManagementMap.WriteToFileException(null, $"{InstructionName}黑名单车辆分发失败：{r.errmsg}，{carNo}");
                        }
                    }
                }
                else
                {
                    var blackcar = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "*", $"BlackList_CarNo='{carNo}'");
                    if (blackcar == null)
                    {
                        SendTCPDatas(context, sendsession, true, "黑名单车辆推送成功", InstructionName);
                        return Task.CompletedTask;
                    }

                    blackcar.Blacklist_Status = 0;

                    result = BLL.BaseBLL._UpdateByModelByNo(blackcar);
                    if (result >= 0)
                    {
                        HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), blackcar);
                        //var r = BLL.MiddlewareApi.DeleteCarBlack(blackcar);
                        //if (!r.success)
                        //{
                        //    LogManagementMap.WriteToFileException(null, $"{InstructionName}黑名单车辆删除分发失败：{r.errmsg}，{carNo}");
                        //}
                    }
                }

                if (result >= 0)
                {
                    SendTCPDatas(context, sendsession, true, "黑名单车辆推送成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName}黑名单车辆推送执行SQL错误：{carNo}");
                    SendTCPDatas(context, sendsession, false, "黑名单车辆推送失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 月租车发行文件下载
    /// </summary>
    public class MthCarIssueFilePushHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "MthCarIssueFilePush";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            await Task.Run(async () =>
            {
                try
                {
                    string key = Convert.ToString(jo["key"]); //停车场的Key值 判断下是否有效
                    string fileurl = Convert.ToString(jo["fileurl"]); //文件下载路径
                    if (string.IsNullOrEmpty(fileurl))
                    {
                        SendTCPDatas(context, sendsession, false, "文件下载路径不能为空", InstructionName);
                        return;
                    }

                    //文件地址，组成本地路径
                    fileurl = System.Web.HttpUtility.UrlDecode(fileurl);
                    Uri uri = new Uri(fileurl);

                    var filename = System.Web.HttpUtility.UrlDecode(uri.Segments.Last());
                    var tempFile = Environment.CurrentDirectory + "\\TempFiles\\";
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux)) tempFile = Path.Combine("/mnt/sda1/b30", "TempFiles");
                    if (!Directory.Exists(tempFile))
                    {
                        Directory.CreateDirectory(tempFile);
                    }

                    string path = Path.Combine(tempFile, filename);

                    if (File.Exists(path))
                    {
                        SendTCPDatas(context, sendsession, false, "当前月租车发行文件已存在", InstructionName);
                        return;
                    }

                    LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"当前月租车发行文件不存在，开始下载文件，文件地址：{fileurl}！");

                    var loadCount = 0;

                GotoLoad:

                    //从网络下载月租车Excel文件
                    using (var writer = File.OpenWrite(path))
                    {
                        var restClient = new RestClient(uri);
                        var request = new RestRequest(Method.GET);

                        // 不要直接使用 responseStream，避免多次 dispose
                        request.ResponseWriter = responseStream =>
                        {
                            // 将下载的数据写入到 writer
                            responseStream.CopyTo(writer);
                        };

                        // 开始同步下载数据，并确保写入完成后才释放 writer
                        restClient.DownloadData(request);

                        //不要手动关闭writer
                        //writer.Close();
                        //writer.Dispose();
                    }

                    await Task.Delay(500);
                    //Thread.Sleep(100);

                    //下载Excel成功，执行数据入库
                    if (File.Exists(path))
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"月租车发行文件下载成功，保存路径：{path}");

                        await Task.Delay(1000);//等待1秒，确保文件已经写入完成

                        //读取车牌车牌颜色信息
                        List<Model.CarType> carTypeList = BLL.CarType.GetAllEntity("CarType_No,CarType_Name", $"CarType_ParkNo='{AppCache.GetParking.Parking_No}'");
                        List<Model.CarCardType> carCardTypeList = BLL.CarCardType.GetAllEntity("CarCardType_No,CarCardType_Name,CarCardType_Category,CarCardType_IsMoreCar,CarCardType_Type,CarCardType_WhiteEnable", $"CarCardType_ParkNo='{AppCache.GetParking.Parking_No}'");
                        if (carTypeList == null || carTypeList.Count == 0)
                        {
                            SendTCPDatas(context, sendsession, false, "系统未设置车牌颜色，无法导入车辆", InstructionName);
                            return;
                        }

                        if (carCardTypeList == null || carCardTypeList.Count == 0)
                        {
                            SendTCPDatas(context, sendsession, false, "系统未设置车牌类型，无法导入车辆", InstructionName);
                            return;
                        }

                        //读取Excel信息
                        string msg = string.Empty;
                        DataTable dtExcel = null;

                        try
                        {
                            dtExcel = GetExcel(path, ref msg);
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{InstructionName}月租车发行文件读取异常:" + ex.ToString());
                        }

                        if (!string.IsNullOrEmpty(msg))
                        {
                            SendTCPDatas(context, sendsession, false, msg, InstructionName);
                            return;
                        }

                        if (dtExcel == null)
                        {
                            loadCount++;
                            if (loadCount < 3)
                            {
                                await Task.Delay(1000);

                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"月租车发行文件下载失败，第{loadCount}次重试");
                                goto GotoLoad;
                            }
                            SendTCPDatas(context, sendsession, false, "文件不规范,无法读取信息", InstructionName);
                            return;
                        }

                        //重组车辆集合信息
                        List<Model.Car> carList = new List<Model.Car>();
                        List<Model.Owner> ownerList = new List<Model.Owner>();
                        string resultMsg = ImportCar(dtExcel, carTypeList, carCardTypeList, ref carList, ref ownerList, out var spaceList, out var old_stopList);
                        if (!string.IsNullOrEmpty(msg))
                        {
                            SendTCPDatas(context, sendsession, false, msg, InstructionName);
                            return;
                        }

                        var ssList = spaceList.Copy();
                        ssList.AddRange(old_stopList);
                        BLL.ParkOrder.ImportCarToChangeOrder(carList, ownerList, ssList, carCardTypeList, out var parkorderList, out var orderDetailList);

                        //插入数据
                        var result = BLL.Car.AddCarOwnerList(carList, ownerList, ssList, parkorderList, orderDetailList);
                        if (result)
                        {
                            _ = Task.Run(() =>
                            {
                                try
                                {
                                    List<Model.API.PushResultParse.CarPush> pushDataList = new List<Model.API.PushResultParse.CarPush>();
                                    Model.API.PushResultParse.CarPush pushData = new Model.API.PushResultParse.CarPush()
                                    {
                                        Item1 = new List<Model.Car>(),
                                        Item2 = new List<Model.Owner>(),
                                        Item3 = new List<StopSpace>(),
                                        Item7 = new List<Model.ParkOrder>(),
                                        Item8 = new List<Model.OrderDetail>(),
                                    };

                                    foreach (var item in carList)
                                    {
                                        if (pushData.Item1.Count < 20)
                                        {
                                            pushData.Item1.Add(item);

                                            var owner = ownerList.Find(x => x.Owner_No == item.Car_OwnerNo);
                                            if (owner != null)
                                                pushData.Item2.Add(owner);

                                            var stop = ssList.FindAll(x => x.StopSpace_OwnerNo == item.Car_OwnerNo);
                                            if (stop != null && stop.Count > 0)
                                                pushData.Item3.AddRange(stop);

                                            var order = parkorderList?.FindAll(x => x.ParkOrder_CarNo == item.Car_CarNo);
                                            if (order != null && order.Count > 0)
                                                pushData.Item7.AddRange(order);

                                            if (order != null && order.Count > 0)
                                            {
                                                var noes = order.Select(x => x.ParkOrder_No).ToList();
                                                var detail = orderDetailList?.FindAll(x => noes.Contains(x.OrderDetail_ParkOrderNo));
                                                if (detail != null && detail.Count > 0)
                                                    pushData.Item8.AddRange(detail);
                                            }
                                        }

                                        if (carList.IndexOf(item) == carList.Count - 1 || pushData.Item1.Count == 20)
                                        {
                                            pushDataList.Add(pushData);

                                            pushData = new Model.API.PushResultParse.CarPush()
                                            {
                                                Item1 = new List<Model.Car>(),
                                                Item2 = new List<Model.Owner>(),
                                                Item3 = new List<StopSpace>(),
                                                Item7 = new List<Model.ParkOrder>(),
                                                Item8 = new List<Model.OrderDetail>(),
                                            };
                                        }
                                    }

                                    foreach (var dataPush in pushDataList)
                                    {
                                        HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), dataPush, "carimport");
                                        //var r = BLL.MiddlewareApi.MthCarIssueFilePush(carList[0].Car_ParkingNo, dataPush);
                                        //if (!r.success)
                                        //{
                                        //    LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车导入分发失败：{r.errmsg}");
                                        //}
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车导入分发失败：{ex.ToString()}");
                                }
                            });

                            LogManagementMap.WriteToFile(LoggerEnum.MiddleInfoLog, $"{InstructionName}月租车导入成功：{string.Join("，", carList.Select(x => x.Car_CarNo).ToArray())}");
                            SendTCPDatas(context, sendsession, true, "月租车导入成功", InstructionName);
                        }
                        else
                        {
                            LogManagementMap.WriteToFileException(null, $"{InstructionName}月租车导入执行SQL错误：{string.Join("，", carList.Select(x => x.Car_CarNo).ToArray())}");
                            SendTCPDatas(context, sendsession, false, "月租车导入失败", InstructionName);
                        }
                    }
                    else
                    {
                        LogManagementMap.WriteToFileException(null, $"月租车发行文件下载失败，文件地址：{fileurl}");
                        SendTCPDatas(context, sendsession, false, "月租车发行文件下载失败", InstructionName);
                        return;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{InstructionName}月租车发行文件下载异常信息:" + ex.ToString());
                    SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
                }
                return;
            });
        }

        private string ImportCar(DataTable dtExcel, List<Model.CarType> carTypeList, List<Model.CarCardType> carCardTypeList, ref List<Model.Car> carList,
            ref List<Model.Owner> ownerList, out List<Model.StopSpace> stopSpaceList, out List<Model.StopSpace> orther_stopSpaceList)
        {
            DateTime curTime = DateTimeHelper.GetNowTime();
            stopSpaceList = new List<StopSpace>();
            orther_stopSpaceList = new List<StopSpace>();
            List<string> lstNo = new List<string>();
            lstNo = Utils.GetRandomLst(dtExcel.Rows.Count * 100);

            //读取用户编号-对应车位号
            List<string> userNoList = new List<string>();
            if (dtExcel.Columns.Contains("用户编号"))
            {
                foreach (DataRow dr in dtExcel.Rows)
                {
                    var no = dr["用户编号"].ToString().Trim();
                    if (string.IsNullOrEmpty(no))
                    {
                        no = CreateSpaceNo(1, userNoList).First();
                    }

                    userNoList.Add(no);
                }

                userNoList = userNoList?.Distinct<string>().ToList();
                userNoList?.RemoveAll(x => x == "");
            }
            else
            {
                userNoList = CreateSpaceNo(dtExcel.Rows.Count, userNoList);
            }

            //查询车主数据
            List<Model.Owner> oList = BLL.Owner.GetAllEntity("", $"Owner_Space in ('{string.Join("','", userNoList)}')");
            //车位数据
            List<Model.StopSpace> spaceList = BLL.BaseBLL._GetAllEntity(new Model.StopSpace(), "*", $"");
            var totalCount = 0;
            var CurrenRowIndex = 0;
            string errorownermsg = string.Empty; //错误记录


            List<Model.PasswayLink> passlinks = AppBasicCache.ReadWriteCache ? AppBasicCache.GetAllPasswayLink.Values.ToList() : BLL.PasswayLink.GetAllEntity("*", $"1=1"); ;
            List<Model.ParkArea> parkareas = AppBasicCache.ReadWriteCache ? AppBasicCache.GetParkAreas.Values.ToList() : BLL.ParkArea.GetAllEntity("*", $"1=1");

            foreach (DataRow dr in dtExcel.Rows) //循环
            {
                CurrenRowIndex++;
                totalCount++;
                if (dr[0] == null || string.IsNullOrEmpty(dr[0].ToString())) continue; //空行

                try
                {
                    string carno = dr[0].ToString().Trim(); //车牌号 
                    string carcardtype = dr[1].ToString().Trim(); //车牌类型
                    string begindate = dr[2].ToString().Trim(); //开始日期
                    string enddate = dr[3].ToString().Trim(); //有效期
                    string name = dr[4].ToString().Trim(); //车主姓名
                    string phone = dr[5].ToString().Trim(); //手机号码
                    string sex = dr[6].ToString().Trim(); //车主性别
                    string address = dr[7].ToString().Trim(); //车主住址
                    string col8 = dr[8].ToString().Trim(); //机号
                    string col9 = dr[9].ToString().Trim(); //车场车位号
                    string userNo = userNoList[CurrenRowIndex - 1]; //dr[10].ToString().Trim();                          //用户编号-对应车位号
                    string remark = dr[10].ToString().Trim(); //备注
                    string spaceNum = dr[11].ToString().Trim(); //车位数

                    string cartype = "蓝牌车"; //车牌颜色,默认蓝牌车
                    Model.CarCardType card = carCardTypeList.Find(x => x.CarCardType_Category == carcardtype);
                    Model.CarType carType = carTypeList.Find(x => x.CarType_Name == cartype);

                    var owner = oList.Find(x => x.Owner_Space == userNo);
                    if (owner == null)
                    {
                        owner = new Model.Owner()
                        {
                            Owner_No = lstNo.First(),
                            Owner_ParkNo = AppCache.GetParking.Parking_No,
                            Owner_AddTime = curTime,
                            Owner_Space = userNo,
                            Owner_SpaceNum = Utils.ObjectToInt(spaceNum, 1),
                            Owner_EnableOffline = card?.CarCardType_WhiteEnable ?? 1,
                            Owner_Remark = remark
                        };

                        if (!string.IsNullOrEmpty(col8) && col8 != "&&&&&")
                        {
                            col8 = col8.Replace('&', ',');
                            var stop = BLL.Owner.getSpace(spaceNum, col8, owner, parkareas, passlinks);
                            stopSpaceList.Add(stop);
                        }
                        else
                        {
                            //增加可停区域
                            var stop = new Model.StopSpace()
                            {
                                StopSpace_No = Utils.CreateNumber,
                                StopSpace_Number = Utils.ObjectToInt(spaceNum, 1),
                                StopSpace_ParkNo = AppCache.GetParking.Parking_No,
                                StopSpace_OwnerNo = owner.Owner_No,
                                StopSpace_Type = 0,
                                StopSpace_AddTime = DateTimeHelper.GetNowTime()
                            };
                            stopSpaceList.Add(stop);
                        }
                    }
                    else
                    {
                        //增加可停区域
                        var df = spaceList.FindAll(x => x.StopSpace_OwnerNo == owner.Owner_No);
                        if (df == null || df.Count == 0)
                        {
                            if (!string.IsNullOrEmpty(col8) && col8 != "&&&&&")
                            {
                                col8 = col8.Replace('&', ',');
                                var stop = BLL.Owner.getSpace(spaceNum, col8, owner);
                                stopSpaceList.Add(stop);
                            }
                            else
                            {
                                var stop = new Model.StopSpace()
                                {
                                    StopSpace_No = Utils.CreateNumber,
                                    StopSpace_Number = Utils.ObjectToInt(spaceNum, 1),
                                    StopSpace_ParkNo = AppCache.GetParking.Parking_No,
                                    StopSpace_OwnerNo = owner.Owner_No,
                                    StopSpace_Type = 0,
                                    StopSpace_AddTime = DateTimeHelper.GetNowTime()
                                };
                                stopSpaceList.Add(stop);
                            }
                        }
                        else
                        {
                            orther_stopSpaceList.AddRange(df);
                        }
                    }

                    lstNo.RemoveAt(0);
                    if (lstNo.Count == 0)
                    {
                        lstNo = Utils.GetRandomLstByTime(DateTimeHelper.GetNowTime().AddMilliseconds(10), dtExcel.Rows.Count * 100);
                    }

                    owner.Owner_Name = name;
                    owner.Owner_Phone = phone;
                    owner.Owner_Address = address;
                    owner.Owner_Sex = (sex == "男" ? 1 : (sex == "女" ? 2 : 0));
                    owner.Owner_EditTime = curTime;
                    owner.Owner_ParkSpace = col9;

                    owner.Owner_CardTypeNo = card?.CarCardType_No;
                    owner.Owner_CardType = card?.CarCardType_Type;
                    owner.Owner_StartTime = Utils.StrToDateTime(Utils.StrToDateTime(begindate).ToString("yyyy-MM-dd 00:00:00"));
                    owner.Owner_EndTime = Utils.StrToDateTime(Utils.StrToDateTime(enddate).ToString("yyyy-MM-dd 23:59:59"));

                    string[] carNoList = carno.Replace("，", ",").Split(',');
                    foreach (var no in carNoList)
                    {
                        Model.Car car = new Model.Car()
                        {
                            Car_No = lstNo.First(),
                            Car_CarNo = no,
                            Car_OwnerNo = owner.Owner_No,
                            Car_OwnerName = name,
                            Car_ParkingNo = owner.Owner_ParkNo,
                            Car_BeginTime = Utils.StrToDateTime(Utils.StrToDateTime(begindate).ToString("yyyy-MM-dd 00:00:00")),
                            Car_EndTime = Utils.StrToDateTime(Utils.StrToDateTime(enddate).ToString("yyyy-MM-dd 23:59:59")),
                            Car_AddTime = curTime,
                            Car_OnLine = 2,
                            Car_Status = 1,
                            Car_Balance = 0,
                            Car_TypeNo = card.CarCardType_No,
                            Car_VehicleTypeNo = carType.CarType_No,
                            Car_Category = card.CarCardType_Category,
                            Car_IsMoreCar = card.CarCardType_IsMoreCar,
                            Car_EnableOffline = owner.Owner_EnableOffline ?? 0,
                            Car_OwnerSpace = owner.Owner_Space
                        };
                        lstNo.RemoveAt(0);
                        if (lstNo.Count == 0)
                        {
                            lstNo = Utils.GetRandomLstByTime(DateTimeHelper.GetNowTime().AddMilliseconds(10), dtExcel.Rows.Count * 100);
                        }

                        carList.Add(car);
                    }

                    ownerList.Add(owner);
                }
                catch (Exception ex)
                {
                    return "车辆导入发生异常：" + ex.Message;
                }
            }

            return "";
        }

        private DataTable GetExcel(string physicalPath, ref string msg)
        {
            msg = "";
            //读取excel到Table
            NPOIExcelHelper npoi = new NPOIExcelHelper(physicalPath);
            DataTable dtExcel = npoi.ImportToDataTable("", true, 0);
            if (dtExcel == null)
            {
                return null;
            }

            Utils.DelFile(physicalPath); //删除Excel            
            if (dtExcel.Columns.Count < 9)
            {
                msg = string.Format("文件不规范只有{0}列，请下载模板", dtExcel.Columns.Count);
                return null;
            }

            //必填项不能为空 & 判断Excel中车牌号正确性和唯一性 & 判断月租车类型 & 判断开始日期和结束日期正确性 & 判断手机号正确性
            string allCarno = ""; //所有正确车牌号
            string emptyLine = ""; //存在空数据行
            string errorCarno = ""; //无效车牌号
            string repeatCarnoLine = ""; //重复车牌号行
            string errorCartype = ""; //无效月租车类型
            string errorStartdateLine = ""; //无效开始日期行
            string errorEnddateLine = ""; //无效结束日期行
            string errorTimespanLine = ""; //无效时间区间行
            string errorTelLine = ""; //无效手机号行
            string errorUsernameLine = ""; //无效车主姓名
            string errorSexLine = ""; //无效性别
            string errorAddressLine = ""; //无效地址
            string errorMachineNo = ""; //开通机号
            string errorParkCarSpace = ""; //车场车位号
                                           //日期（yyyy-MM-dd）正则表达式
            string regDate = @"^((((1[6-9]|[2-9]\d)\d{2})-(0?[13578]|1[02])-(0?[1-9]|[12]\d|3[01]))|(((1[6-9]|[2-9]\d)\d{2})-(0?[13456789]|1[012])-(0?[1-9]|[12]\d|30))|(((1[6-9]|[2-9]\d)\d{2})-0?2-(0?[1-9]|1\d|2[0-8]))|(((1[6-9]|[2-9]\d)(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00))-0?2-29))$";
            //string regTel = @"^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$";
            foreach (DataRow dr in dtExcel.Rows)
            {
                if ((dr[0].ToString() + dr[1] + dr[2] + dr[3] + dr[4] + dr[5] + dr[6] + dr[8]) != "" &&
                    (string.IsNullOrEmpty(dr[0].ToString()) || string.IsNullOrEmpty(dr[1].ToString()) ||
                     string.IsNullOrEmpty(dr[2].ToString()) || string.IsNullOrEmpty(dr[3].ToString()) ||
                     string.IsNullOrEmpty(dr[4].ToString()) || string.IsNullOrEmpty(dr[5].ToString()) ||
                     string.IsNullOrEmpty(dr[6].ToString()) || string.IsNullOrEmpty(dr[8].ToString()))) //存在空项： 有一项内容不为空，则认定为有效行
                {
                    emptyLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (!string.IsNullOrEmpty(dr[0].ToString()))
                {
                    string carno = dr[0].ToString();
                    carno = carno.Replace("，", ",");
                    string[] carnoList = carno.Split(',');
                    foreach (var c in carnoList)
                    {
                        if (Utils.ValidationCarNo(c))
                        {
                            if (("," + allCarno).Contains("," + c + ","))
                            {
                                repeatCarnoLine += (dtExcel.Rows.IndexOf(dr) + 2) + ","; //重复车牌号
                            }
                            else
                            {
                                allCarno += c + ","; //正确车牌号   
                            }
                        }
                        else
                        {
                            errorCarno += c + ","; //无效车牌号
                        }
                    }
                }

                if (!string.IsNullOrEmpty(dr[1].ToString()) && !",3652,3653,3654,3655,3661,3662,3663,3664,3656,".Contains("," + dr[1] + ",")) //月租车类型无效
                {
                    errorCartype += dr[1] + ",";
                }

                if (!string.IsNullOrEmpty(dr[2].ToString()) && !Regex.IsMatch(dr[2].ToString(), regDate)) //开始日期无效
                {
                    errorStartdateLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (!string.IsNullOrEmpty(dr[3].ToString()) && !Regex.IsMatch(dr[3].ToString(), regDate)) //结束日期无效
                {
                    errorEnddateLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (!string.IsNullOrEmpty(dr[2].ToString()) && !string.IsNullOrEmpty(dr[3].ToString())
                                                            && Regex.IsMatch(dr[2].ToString(), regDate) && Regex.IsMatch(dr[3].ToString(), regDate)
                                                            && DateTime.Compare(DateTime.Parse(dr[2].ToString()), DateTime.Parse(dr[3].ToString())) > 0)
                //时间区间错误，如开始日期大于结束日期
                {
                    errorTimespanLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (!string.IsNullOrEmpty(dr[4].ToString()) && Encoding.Default.GetBytes(dr[4].ToString()).Length > 50) //车主姓名无效
                {
                    errorUsernameLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                //if (!string.IsNullOrEmpty(dr[5].ToString()) && !Common.Utils.IsMobile("86-" + dr[5].ToString(), "CN")) //联系电话无效
                //{
                //    errorTelLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                //}

                if (!string.IsNullOrEmpty(dr[6].ToString()) && !",男,女,".Contains("," + dr[6] + ",")) //性别无效
                {
                    errorSexLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (!string.IsNullOrEmpty(dr[7].ToString()) && Encoding.Default.GetBytes(dr[7].ToString()).Length > 250) //地址无效
                {
                    errorAddressLine += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (string.IsNullOrEmpty(dr[8].ToString()) || Encoding.Default.GetBytes(dr[8].ToString()).Length > 84) //车场车位号太长
                {
                    errorMachineNo += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }

                if (!string.IsNullOrEmpty(dr[9].ToString()) && Encoding.Default.GetBytes(dr[9].ToString()).Length > 50) //车场车位号太长
                {
                    errorParkCarSpace += (dtExcel.Rows.IndexOf(dr) + 2) + ",";
                }
            }

            if (!string.IsNullOrEmpty(errorCarno + errorCartype + repeatCarnoLine + emptyLine + errorStartdateLine + errorEnddateLine
                                      + errorTimespanLine + errorUsernameLine + errorTelLine + errorSexLine + errorAddressLine + errorMachineNo + errorParkCarSpace))
            {
                msg = (!string.IsNullOrEmpty(errorCarno.Trim(',')) ? "车牌号错误: " + errorCarno.Trim(',') + "。 <br/>" : "") +
                      (!string.IsNullOrEmpty(errorCartype.Trim(',')) ? "车类型无效: " + errorCartype.Trim(',') + "。<br/>" : "") +
                      (!string.IsNullOrEmpty(repeatCarnoLine.Trim(',')) ? "车牌号重复: 第" + repeatCarnoLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(emptyLine.Trim(',')) ? "必填项无值: 第" + emptyLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorStartdateLine.Trim(',')) ? "开始日期无效: 第" + errorStartdateLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorEnddateLine.Trim(',')) ? "结束日期无效: 第" + errorEnddateLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorTimespanLine.Trim(',')) ? "时间区间无效: 第" + errorTimespanLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorUsernameLine.Trim(',')) ? "车主姓名无效: 第" + errorUsernameLine.Trim(',') + "行。<br/>" : "") +
                      //(!string.IsNullOrEmpty(errorTelLine.Trim(',')) ? "联系电话无效: 第" + errorTelLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorSexLine.Trim(',')) ? "性别无效: 第" + errorSexLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorAddressLine.Trim(',')) ? "家庭地址无效: 第" + errorAddressLine.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorMachineNo.Trim(',')) ? "开通机号无效: 第" + errorMachineNo.Trim(',') + "行。<br/>" : "") +
                      (!string.IsNullOrEmpty(errorParkCarSpace.Trim(',')) ? "车场车位号无效: 第" + errorParkCarSpace.Trim(',') + "行。<br/>" : "");
            }

            return dtExcel;
        }

        private List<string> CreateSpaceNo(int len, List<string> userNoList)
        {
            List<string> noList = new List<string>();
            string NewNo = string.Empty;
            var owner = BLL.Owner.GetLast();
            if (owner == null)
            {
                for (var i = 1; i <= len; i++)
                {
                    NewNo = i.ToString().PadLeft(5, '0');
                    NewNo = $"C{NewNo}";
                    if (!userNoList.Contains($"C{NewNo}"))
                        noList.Add(NewNo);
                    else
                        len++;
                }
            }
            else
            {
                var has = true;
                string strNo = Regex.Replace(owner.Owner_Space, "[a-z]", "", RegexOptions.IgnoreCase);
                int LastNo = Utils.StrToInt(strNo, 0);

                for (var i = 1; i <= len; i++)
                {
                    while (has)
                    {
                        LastNo = LastNo + 1;
                        NewNo = LastNo.ToString().PadLeft(5, '0');
                        var item = BLL.Owner.GetEntity("Owner_No", $"Owner_Space='C{NewNo}'");
                        if (item == null && !userNoList.Contains($"C{NewNo}"))
                            has = false;
                    }

                    NewNo = $"C{NewNo}";
                    noList.Add(NewNo);
                }
            }

            return noList;
        }
    }

    /// <summary>
    /// 车位预定指令处理类
    /// </summary>
    public class ReserveParkSpaceHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "ReserveParkSpace";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                Model.API.ReserveParam param = jo.ToObject<Model.API.ReserveParam>();

                var isNew = BLL.Reserve.NewCarOwnerReserve(param, out var reserve, out var errmsg);
                if (!isNew)
                {
                    SendTCPDatas(context, sendsession, false, errmsg, InstructionName);
                    return Task.CompletedTask;
                }

                //新增访客车
                var res = BLL.BaseBLL._AddOrUpdateModel(reserve);
                if (res > 0)
                {
                    //var r = BLL.MiddlewareApi.AddReserve(new List<Reserve> { reserve });
                    //if (!r.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}下发访客车行信息分发失败：{r.errmsg}，{param.carNo}");
                    //}
                    //BLL.PushEvent.ReserveSend(AppBasicCache.GetParking?.Parking_Key, reserve.Reserve_CarNo, reserve.Reserve_OrderNo);
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), reserve);
                    //AppBasicCache.AddOrUpdateElement(AppBasicCache.GetReserve, reserve.Reserve_No, reserve);

                    SendTCPDatas(context, sendsession, true, "新增访客车成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName} 新增访客车执行SQL失败：{TyziTools.Json.ToString(reserve)}");
                    SendTCPDatas(context, sendsession, false, "新增访客车失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 取消车位预定指令处理类
    /// </summary>
    public class CancelReserveParkSpaceHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CancelReserveParkSpace";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                //bool IsSuccess = false;
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["orderNo"]); //预约车订单记录号

                Model.Reserve model = BLL.BaseBLL._GetEntityByNo(new Reserve(), orderNo);
                if (model == null || model.Reserve_Status == 3)
                {
                    SendTCPDatas(context, sendsession, true, "取消预约成功", InstructionName);
                    return Task.CompletedTask;
                }

                if (model.Reserve_Status == 1)
                {
                    SendTCPDatas(context, sendsession, true, "取消失败：已到达", InstructionName);
                    return Task.CompletedTask;
                }

                if (model.Reserve_Status == 2)
                {
                    SendTCPDatas(context, sendsession, true, "取消失败：已过期", InstructionName);
                    return Task.CompletedTask;
                }

                model.Reserve_Status = 3;
                model.Reserve_CancelTime = DateTimeHelper.GetNowTime();
                model.Reserve_CancelRemark = "平台取消";

                var res = BLL.BaseBLL._UpdateByModelByNo(model);
                if (res >= 0)
                {
                    HandleCommon.UpdateCache(Model.API.PushAction.Delete.ToString(), model, "cancelreserveparkspace");
                    //var r = BLL.MiddlewareApi.CancelReserveParkSpace(model);
                    //if (!r.success)
                    //{
                    //    LogManagementMap.WriteToFileException(null, $"{InstructionName}取消预约信息分发失败：{r.errmsg}，{model.Reserve_CarNo}");
                    //}

                    SendTCPDatas(context, sendsession, true, "取消预约成功", InstructionName);
                }
                else
                {
                    LogManagementMap.WriteToFileException(null, $"{InstructionName} 取消预约执行SQL失败：{TyziTools.Json.ToString(model)}");
                    SendTCPDatas(context, sendsession, false, "取消预约失败", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, false, $"{InstructionName}异常信息：{ex.Message}", InstructionName);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 访客车辆推送指令处理类（商家车）
    /// </summary>
    public class CarVisitorPushHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CarVisitorPush";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;

                Model.API.BusinessCarParam param = jo.ToObject<Model.API.BusinessCarParam>();

                //创建
                if (param.status.Equals("1"))
                {
                    var isTrue = BLL.BusinessCar.NewCarOwnerReserve(param, out var data, out var errmsg);
                    if (!isTrue)
                    {
                        SendTCPDatas(context, sendsession, false, $"新增商家车失败{errmsg}", InstructionName);
                        return Task.CompletedTask;
                    }

                    var res = BLL.BusinessCar._Insert(data);
                    if (res > 0)
                    {
                        //var r = BLL.MiddlewareApi.BusinessCarPush(new List<BusinessCar> { data });
                        //if (!r.success)
                        //{
                        //    LogManagementMap.WriteToFileException(null, $"{InstructionName}商家车辆信息分发失败：{r.errmsg}，{param.carNo}");
                        //}
                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBusinessCar, data.BusinessCar_No, data);

                        SendTCPDatas(context, sendsession, true, "新增商家车成功", InstructionName);
                    }
                    else
                    {
                        LogManagementMap.WriteToFileException(null, $"{InstructionName} 新增商家车执行SQL失败：{TyziTools.Json.ToString(data)}");
                        SendTCPDatas(context, sendsession, false, "新增商家车失败", InstructionName);
                    }
                }
                else if (param.status.Equals("0"))
                {
                    string sqlWhere = $"BusinessCar_Status=0 AND BusinessCar_CarNo='{param.carNo}' ";
                    if (!string.IsNullOrEmpty(param.beginTime)) sqlWhere += $" AND BusinessCar_StartTime='{param.beginTime}'";

                    var model = BLL.BusinessCar.GetAllEntity("*", sqlWhere)?.FirstOrDefault();
                    if (model == null)
                    {
                        SendTCPDatas(context, sendsession, true, "注销商家车成功：未匹配生效的商家车", InstructionName);
                        return Task.CompletedTask;
                    }

                    var timeTrue = DateTime.TryParse(param.time, out var timeDt);
                    model.BusinessCar_Status = 3;
                    model.BusinessCar_CancelTime = timeTrue ? timeDt : DateTimeHelper.GetNowTime();
                    var res = BLL.BusinessCar._Insert(model);
                    if (res > 0)
                    {
                        AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBusinessCar, model.BusinessCar_No, model);
                        //var r = BLL.MiddlewareApi.BusinessCarPush(new List<BusinessCar> { model });
                        //if (!r.success)
                        //{
                        //    LogManagementMap.WriteToFileException(null, $"{InstructionName}下发注销商家车分发失败：{r.errmsg}，{param.carNo}");
                        //}

                        SendTCPDatas(context, sendsession, true, "注销商家车成功", InstructionName);
                    }
                    else
                    {
                        LogManagementMap.WriteToFileException(null, $"{InstructionName} 注销商家车执行SQL失败：{TyziTools.Json.ToString(model)}");
                        SendTCPDatas(context, sendsession, false, "注销商家车失败", InstructionName);
                    }
                }
                else
                {
                    SendTCPDatas(context, sendsession, false, "参数status值错误", InstructionName);
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 车场信息[是否启用预约车功能]指令处理类
    /// </summary>
    public class SysnParkDataHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "SysnParkData";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;
                bool IsSuccess = false;

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// 重传入场记录指令处理类
    /// </summary>
    public class LoadRecordHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "LoadRecord";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;
                string orderNo = Convert.ToString(jo["parkOrderNo"]); //停车订单号

                if (string.IsNullOrEmpty(orderNo))
                {
                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "停车订单号不能为空", actionName = InstructionName }));
                    return Task.CompletedTask;
                }

                Model.ParkOrder order = BLL.ParkOrder.GetEntity(orderNo);
                if (order == null)
                {
                    //查询弹窗信息，判断是否无入场记录出场查找预入场记录
                    var confirmPass = BLL.ConfirmRelease.Results.FirstOrDefault(passInfo => passInfo.Value?.resorder?.resOut?.parkorder?.ParkOrder_No == orderNo
                                                                                         || passInfo.Value?.resorder?.resOut?.noRecordOrder?.ParkOrder_No == orderNo);
                    if (confirmPass.Value != null)
                    {
                        var cacheOrder = confirmPass.Value?.resorder?.resOut?.parkorder ?? confirmPass.Value?.resorder?.resOut?.noRecordOrder;
                        if (cacheOrder != null)
                        {
                            var reserve = BLL.BaseBLL._GetEntityByWhere(new Model.Reserve(), "Reserve_No", $"Reserve_OrderNo='{orderNo}'");
                            BLL.PlateColorConvert.ToENCode(cacheOrder.ParkOrder_CarTypeName, out var plateColorCode);
                            var remainspace = 0;
                            BLL.Parking.GetParkSpace(cacheOrder.ParkOrder_ParkNo, out var total, out var incount, out remainspace);
                            var temp1 = cacheOrder.ParkOrder_EnterRemark == null ? null : TyziTools.Json.ToString(order.ParkOrder_EnterRemark?.Split(','));

                            var model = new
                            {
                                version = Config.AppSettingConfig.ApiVersion,
                                key = AppBasicCache.GetParking.Parking_Key,
                                orderNo = orderNo,
                                carNo = cacheOrder.ParkOrder_CarNo,
                                enterTime = cacheOrder.ParkOrder_EnterTime,
                                carType = AppBasicCache.GetElement(AppBasicCache.GetCarcardTypes, cacheOrder.ParkOrder_CarCardType)?.CarCardType_Category ?? "",
                                gateName = cacheOrder.ParkOrder_EnterPasswayName ?? "",
                                operatorName = cacheOrder.ParkOrder_EnterAdminName ?? "",
                                reserveOrderNo = reserve?.Reserve_No ?? "",
                                plateColor = plateColorCode.ToString(),
                                imgUrl = "",
                                generalField = System.Web.HttpUtility.UrlEncode((new JObject
                            {
                                { "remainspace", remainspace}, //剩余车位
                                { "temp1", temp1}, //备注说明
                                { "carTypeNo", temp1 }, //车牌颜色
                                { "iNoRecord", 0 }, //是否无入场记录
                                { "gateNo", cacheOrder.ParkOrder_EnterPasswayNo ?? "0" }, //入场车道编号
                                { "imgUrl", "" }, //图片地址
                                { "sentryHostNo", AppBasicCache.SentryHostInfo?.SentryHost_No }, //入场岗亭编号
                            }).ToString()),
                                remainspace = remainspace,
                                temp1 = System.Web.HttpUtility.UrlEncode(temp1),
                                gateNo = cacheOrder.ParkOrder_EnterPasswayNo ?? "0",
                                remark = temp1, //手机号+备注，用于上报订单关联的手机号（车辆扫码登记入场）
                            };

                            var res1 = BLL.PushEvent.Create(AppBasicCache.GetParking.Parking_Key, model, PushEventCommand.SFM_PKCLD_EVENT_EnterCar, MiddlewareEventPriority.Delay_500_2);
                            SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = "处理成功", actionName = InstructionName, data = model }));
                            return Task.CompletedTask;
                        }
                    }

                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "停车订单不存在", actionName = InstructionName }));
                    return Task.CompletedTask;
                }

                if (order.ParkOrder_StatusNo != Model.EnumParkOrderStatus.In)
                {
                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "车辆不在场内", actionName = InstructionName }));
                    return Task.CompletedTask;
                }

                Model.Parking parking = BLL.Parking.GetEntity(order.ParkOrder_ParkNo);
                var result = BLL.PushEvent.EnterCar(parking?.Parking_Key, order, MiddlewareEventPriority.Delay_500_1, true, true);
                //if (result == null || Convert.ToInt32(result) != 1) { SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = "处理失败", actionName = InstructionName })); }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = "处理成功", actionName = InstructionName, data = result }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }


    /// <summary>
    /// 充电车位滞留触发金额记录下发
    /// </summary>
    public class AddStrandedRecordHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "addStrandedRecord";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                string sRemark = string.Empty;
                string carNo = Convert.ToString(jo["carNo"]); //车牌号码
                string strandedRecordStartTime = Convert.ToString(jo["strandedRecordStartTime"]); //驶入车位时间（滞留开始时间）
                string strandedRecordEndTime = Convert.ToString(jo["strandedRecordEndTime"]); //驶出车位时间（滞留结束时间）
                string lastChargeOrderNo = Convert.ToString(jo["lastChargeOrderNo"]); //最后一次充电编号
                string strandedRecordSolution = Convert.ToString(jo["strandedRecordSolution"]); //处罚编号
                string StrandedRecord_StrandedTime = Convert.ToString(jo["StrandedRecord_StrandedTime"]); //滞留时长(分钟)
                string StrandedRecord_StrandedMoney = Convert.ToString(jo["StrandedRecord_StrandedMoney"]); //处罚金额（元）
                string strandedRecordNo = Convert.ToString(jo["strandedRecordNo"]); //处罚订单编号唯一

                DateTime.TryParse(strandedRecordStartTime, out DateTime dateTimestrandedRecordStartTime);
                DateTime.TryParse(strandedRecordEndTime, out DateTime dateTimestrandedRecordEndTime);
                decimal.TryParse(StrandedRecord_StrandedMoney, out decimal dStrandedRecord_StrandedMoney);
                decimal.TryParse(StrandedRecord_StrandedTime, out decimal dStrandedRecord_StrandedTime);

                var rlt = BLL.BaseBLL._GetEntityByNo(new Model.DetentionPenalty { }, strandedRecordNo);
                if (rlt != null)
                {
                    sRemark = $"滞留处罚订单{strandedRecordSolution}已经下发,不再处理！";
                }
                else
                {
                    var model = new Model.DetentionPenalty
                    {
                        DetentionPenalty_CarNo = carNo,
                        DetentionPenalty_InTime = dateTimestrandedRecordStartTime,
                        DetentionPenalty_OutTime = dateTimestrandedRecordEndTime,
                        DetentionPenalty_ChargeNo = lastChargeOrderNo,
                        DetentionPenalty_Money = dStrandedRecord_StrandedMoney,
                        DetentionPenalty_No = strandedRecordNo,
                        DetentionPenalty_StayTime = dStrandedRecord_StrandedTime,
                        DetentionPenalty_AnNo = strandedRecordSolution
                    };
                    var lst = BLL.BaseBLL._Insert(model);
                    sRemark = lst > 0 ? "处理成功" : "添加记录失败";
                    //BLL.MiddlewareApi.PushStrandedRecord(model, AppCache.GetParking.Parking_No);
                    HandleCommon.UpdateCache(Model.API.PushAction.Edit.ToString(), model, "editStrandedRecord");
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }


    /// <summary>
    /// [CommonData]一般由第三方平台或子平台下发指令，由停车平台转发至车场
    /// </summary>
    /// <summary>
    public class CommonDataHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "CommonData";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;

                //这是统一的数据转发接口，具体逻辑根据实际情况处理

                SendTCPDatas(context, sendsession, true, "下发成功", InstructionName);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
            return Task.CompletedTask;
        }
    }


    /// <summary>
    /// 无牌车扫码出入小车场操作指令处理类
    /// </summary>
    public class PullUnCarNoScanHandle : TcpHandleBase
    {
        /// <summary>
        /// 平台下发指令名称
        /// </summary>
        public override string InstructionName { get; set; } = "PullUnCarNoScan";

        /// <summary>
        /// 执行处理方法
        /// </summary>
        /// <param name="jo">上下文接收到的json格式数据</param>
        /// <param name="context">TCP服务上下文操作通道对象</param>
        /// <param name="sendsession">应答TCP服务基本数据</param>
        public override async Task Execute(JObject jo, IChannelHandlerContext context = null, Sendsession sendsession = null)
        {
            try
            {
                bool IsSuccess = false;
                string sRemark = string.Empty;
                string passNo = Convert.ToString(jo["ctrlNo"]); //开闸机号
                string orderNo = Convert.ToString(jo["orderNo"]); //停车订单号
                string userNo = Convert.ToString(jo["userNo"]); //用户号
                string carNo = Convert.ToString(jo["carNo"]); //车牌号

                var model = BLL.Passway._GetEntityByNo(new Model.Passway { }, passNo);
                if (model == null)
                {
                    sRemark = "未找到车道信息";
                }
                else
                {

                    var parkorder = BLL.ParkOrder.GetEntity(orderNo);
                    if (parkorder == null) { sRemark = "未找到停车订单信息"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName })); return; }

                    if (parkorder.ParkOrder_CarNo != carNo) { sRemark = "停车订单信息车牌号不匹配"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName })); return; }

                    var camera = BLL.Device.GetEntityByPasswayNo(passNo);
                    if (camera == null) { sRemark = "未找到车道相机信息"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName })); return; }

                    string imgSrc = "";
                    //var gate = BLL.Passway.GetPasswayGateType(passNo);
                    //if (gate == 2 || gate == 3)
                    //{
                    imgSrc = CameraImageHelper.ImageSaveHSPathBig(carNo, camera.Device_SentryHostNo);
                    string base64 = string.Empty;
                    string errmsg = string.Empty;
                    var ffaa = await LPRTools.GetSnapShootToJpeg(passNo, imgSrc, base64, errmsg);
                    //}

                    Model.ResultPass data = PassTool.PassHelper.OnCheckCarPass(new Model.ParkCarInOut
                    {
                        carno = carNo,
                        cartype = TyziTools.Json.ToString(new List<string>() { PasswayConfirmReleaseUtil.LPRColorChina(1) }),
                        time = DateTime.Now,
                        parkno = AppBasicCache.SentryHostInfo.SentryHost_ParkNo,
                        camerano = camera.Device_No,
                        img = imgSrc,
                        imgsmall = "",
                        mode = 2,
                        licensepoint = "",
                        isreal = 1,
                        isVideoRecord = false,
                        useDeviceTime = true,
                        isSupplement = true,
                    }, AppBasicCache.GetBasicCache);

                    if (data == null)
                    {
                        sRemark = "通行检测异常，禁止通行"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
                        return;
                    }

                    if (data.success)
                    {
                        if (data.payres != null)
                            data.calcdetail = BLL.CommonBLL.GetCalcDetail(data.payres, data?.resorder?.resOut?.parkorder ?? new Model.ParkOrder() { ParkOrder_CarNo = carNo });

                        if (data.unpaidresult != null)
                        {
                            data.unpaidresult.ForEach(x =>
                            {
                                if (data.calcdetail == null) data.calcdetail = BLL.CommonBLL.GetCalcDetail(x.payres, data?.resorder?.resOut?.parkorder, true);
                                else
                                {
                                    var calcDetail = BLL.CommonBLL.GetCalcDetail(x.payres, data?.resorder?.resOut?.parkorder, true);
                                    if (calcDetail != null) data.calcdetail.AddRange(calcDetail);
                                }
                            });
                        }


                        //通行检测结果处理
                        int? code = data.passres.code;

                        #region 控制板发送来车事件

                        SentryBox.BarrierDevice.BarrierDeviceUtilsl.SendCarOrder(data);

                        #endregion

                        #region 遥控开闸处理

                        bool openGate = true; //是否开闸
                        bool takeRecord = false; //是否启用了优先保存记录：true-未启用，false-启用
                        bool takeRecordResult = false; //优先保存记录是否处理成功：true-处理成功，false-处理失败
                        var areaLink = AppBasicCache.GetSentryPasswayLinkDic.Where(m => m.Value.PasswayLink_PasswayNo == passNo);
                        if (code > 0 && data.policy != null && data.policy.takerecord == 1 || (data.policy != null && data.policy.autotakerecord == 1))
                        {
                            if (areaLink.Count() > 0)
                            {
                                if (data.passres.code != 1 && data.policy.autotakerecord != 1) openGate = false;
                                takeRecord = true;
                                //处理信息，优先保存记录（入口，停车订单直接调用入场回调方法，出场直接调用出场回调方法【并且直接保存现金缴费金额，无电子支付】）
                                takeRecordResult = DeviceServerHandle.AutoReleaseTakeRecord(data, camera);

                                LogManagementMap.WriteToFile(LoggerEnum.LPRHandle, $"{carNo} 车牌识别处理，启用了优先保存记录不开闸！");
                            }
                        }

                        #endregion

                        #region 入口追缴

                        if (data.isSupplement && openGate && areaLink.Count() == 1 && areaLink.FirstOrDefault().Value.PasswayLink_GateType == 1 && data.passres.code == 2 && data.policy?.autotakerecord != 1)
                        {
                            //追缴金额
                            decimal? unpayedamount = data.unpaidresult?.Sum(m => m.payres?.payedamount);
                            if (unpayedamount != null && unpayedamount > 0)
                            {
                                sRemark = $"您有待缴费的停车订单，请扫码补缴{unpayedamount}元"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));

                                var lp = data.unpaidresult.FirstOrDefault(m => m.payres.payedamount > 0);
                                string orderno = lp.unpaidorder.ParkOrder_No;
                                string laneQR = string.IsNullOrWhiteSpace(AppBasicCache.CurrentSysConfigContent.SiteDomain_Weixin)
                                    ? ""
                                    : $"{AppBasicCache.CurrentSysConfigContent.SiteDomain_Weixin.TrimEnd('/')}/scpay/Index?carNo={data.passres.carno}&parkKey={AppBasicCache.SentryHostInfo.Parking_Key}";
                                var model1 = new TcpConnPools.ChannelMachine.Robot.RobotSendPacket();
                                model1.type = TcpConnPools.ChannelMachine.Robot.RobotCommandType.licensedPaymentWithPayType;
                                model1.msg = "车辆入场补缴费用信息发送";
                                model1.voiceType = (int)TcpConnPools.ChannelMachine.Robot.RobotVoiceType.ConfirmCurrent;
                                model1.voiceText = $"{data.passres.carno}您有待缴费的停车订单，请扫码补缴{unpayedamount}元";
                                model1.resultCode = 0;
                                model1.data = new
                                {
                                    #region 参数

                                    isCharge = unpayedamount > 0, //是否收费
                                    cph = data.passres.carno, //车牌号码
                                    parkDuration = "", //停车时间
                                    money = unpayedamount, //缴费金额
                                    inTime = lp?.unpaidorder?.ParkOrder_EnterTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), //进场时间
                                    outTime = lp?.unpaidorder?.ParkOrder_OutTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), //出场时间
                                    carType = Common.Convert2CarCard.ConvertToCardType(data.passres.carcardtype.CarCardType_Category),
                                    isEnableCashChange = false, //是否支持现金找零
                                    payID = orderno,
                                    payType = 6,
                                    payQrcode = laneQR

                                    #endregion
                                };
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{carNo} 车辆入场补缴费用信息发送，{TyziTools.Json.ToString(model1)}");
                                DevicePoolsUtil.Device3288Sends(model1, passNo);

                                DeviceServerHandle.ConfirmRelease(new TcpConnPools.Camera.CarPlateInfo { CarPlate = carNo }, data, passNo, camera, true, false);

                                return;
                            }
                        }

                        #endregion

                        #region 外场入口防疫管控

                        if (data.isSupplement && code > 0 && data.passres?.fycode == 1)
                        {
                            if (areaLink.Count() == 1 && areaLink.FirstOrDefault().Value.PasswayLink_GateType == 1)
                            {
                                sRemark = "请扫健康码"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                                DeviceServerHandle.HealthCodeScanPass(data, camera);
                                return;
                            }
                        }

                        #endregion

                        switch (code)
                        {
                            //禁止通行
                            case 0:
                                {
                                    sRemark = "禁止通行";
                                    SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = sRemark, actionName = InstructionName }));
                                    #region 语音播报

                                    if (data.isSupplement) BroadcastUtil.NoEntry(data, camera);

                                    #endregion

                                    #region 通知岗亭

                                    WebSocketUtil.SendWS(data);

                                    #endregion
                                    return;
                                }
                            case 1:
                                sRemark = "请通行"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                                await DeviceServerHandle.SyncAutoRelease(data, camera, openGate, takeRecord, takeRecordResult);
                                return;
                            case 2:
                                sRemark = "车辆通行等待确认中，请稍候"; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                                DeviceServerHandle.ConfirmRelease(new TcpConnPools.Camera.CarPlateInfo { CarPlate = carNo }, data, passNo, camera, openGate, !takeRecord, openGate, takeRecord, takeRecordResult, true, data.policy?.autotakerecord == 1);
                                return;
                            case 3:
                                sRemark = "满位排队通行，请稍候";
                                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                                DeviceServerHandle.WaitInLine(new TcpConnPools.Camera.CarPlateInfo { CarPlate = carNo }, data, passNo, camera);
                                return;
                            case 4:
                                sRemark = "车辆通行等待确认中，请稍候";
                                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 1, msg = sRemark, actionName = InstructionName }));
                                DeviceServerHandle.MinimumCharge(new TcpConnPools.Camera.CarPlateInfo { CarPlate = carNo }, data, passNo, camera, openGate, takeRecord, takeRecordResult);
                                return;
                        }
                    }
                    else
                    {
                        sRemark = string.IsNullOrEmpty(data.errmsg) ? "通行检测异常，禁止通行" : data.errmsg; SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
                        return;
                    }
                }

                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = IsSuccess ? 1 : 0, msg = sRemark, actionName = InstructionName }));
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, $"{InstructionName}异常信息");
                SendTCPDatas(context, sendsession, JsonConvert.SerializeObject(new { result = 0, msg = $"{InstructionName}异常信息：{ex.Message}", actionName = InstructionName }));
            }
        }
    }

    #endregion
}