﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>事件提醒</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }

        .help-btn { position: absolute; width: 20px; height: 20px; right: 5px; top: 10px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }

        .layui-fluid { padding: 0; }
        .layadmin-header .layui-breadcrumb { background-color: #5868e0; color: #fff; }
        .layui-breadcrumb a cite { color: #fff; min-height: 1rem; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; }
        ::-webkit-scrollbar-thumb { border-radius: 10px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }
        .layui-breadcrumb, .layui-tab-title { background-color: #5868e0 !important; }

        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-input.timerange { width: 300px; }
        .layui-layer-btn-l { margin-left: 8.33%; }
        .layui-input { border-color: #ddd !important; font-size: .9rem; }
        .layui-table-cell { font-size: .9rem; }
        .layui-tab-title li { font-size: 1rem; }
        .fastsearch ul li { font-size: 1rem; }
    </style>
</head>
<body>

    <div class="layui-fluid">
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>事件提醒</cite></a>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <select id="Syswarn_Level" name="Syswarn_Level" lay-search>
                                    <option value="">级别</option>
                                    <option value="0">正常</option>
                                    <option value="10">轻度</option>
                                    <option value="100">中度</option>
                                    <option value="1000">严重</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Syswarn_Time0" id="Syswarn_Time0" autocomplete="off" placeholder="发生时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Syswarn_Time1" id="Syswarn_Time1" autocomplete="off" placeholder="发生时间止" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Syswarn_HostIP" id="Syswarn_HostIP" autocomplete="off" placeholder="岗亭IP" value="" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                 <button class="layui-btn layui-btn-sm" id="Update" lay-event="Update"><i class="fa fa-edit"></i><t>已处理</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?3.5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v20230620" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var code = $.getUrlParam("code");
        var comtable = null;

        layui.use(['table', 'form'], function () {

            var table = layui.table;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Syswarn_No', title: '唯一编号', hide: true }
                , {
                    field: 'Syswarn_Level', title: '级别', width: 80, templet: function (d) {
                        if (d.Syswarn_Level == 0) return tempBar(1, "正常");
                        else if (d.Syswarn_Level == 10) return tempBar(7, "轻度");
                        else if (d.Syswarn_Level == 100) return tempBar(3, "中度");
                        else if (d.Syswarn_Level == 1000) return tempBar(0, "严重");
                        else return "";
                    }
                }
                , { field: 'Syswarn_HostNo', title: '岗亭编号', hide: true }
                , { field: 'Syswarn_HostIP', title: '岗亭IP' }
                , { field: 'Syswarn_HostName', title: '岗亭名称' }
                , { field: 'Syswarn_Type_Text', title: '消息分组', hide: true }
                , { field: 'Syswarn_Content', title: '消息内容' }
                , {
                    field: 'Syswarn_IsEmail', title: '邮件提醒', width: 100, hide: true, templet: function (d) {
                        if (d.Syswarn_IsEmail == 0) return tempBar(3, "未发送");
                        else if (d.Syswarn_IsEmail == 1) return tempBar(1, "已发送");
                        else return "";
                    }
                }
                , {
                    field: 'Syswarn_IsSMS', title: '短信提醒', width: 100, hide: true, templet: function (d) {
                        if (d.Syswarn_IsSMS == 0) return tempBar(3, "未发送");
                        else if (d.Syswarn_IsSMS == 1) return tempBar(1, "已发送");
                        else return "";
                    }
                }
                , { field: 'Syswarn_Time', title: '发生时间' }
                , {
                    field: 'Syswarn_Status', title: '处理状态', width: 100, templet: function (d) {
                        if (d.Syswarn_Status == 0) return tempBar(3, "未处理");
                        else if (d.Syswarn_Status == 1) return tempBar(1, "已处理");
                        else return "";
                    }
                }
                , { field: 'Syswarn_ToTime', title: '处理时间', hide: true }
                , { field: 'Syswarn_Account', title: '操作账号', hide: true }
                , { field: 'Syswarn_AddTime', title: '添加时间' }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            conditionParam.code = code;
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetSyswarnList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();

                switch (obj.event) {
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].Syswarn_No);
                        }
                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定已处理报警消息吗?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/Monitoring/SyswarnUpdate", { Syswarn_NoArray: JSON.stringify(NoArray), code: code }, function (json) {
                                    if (json.success) {

                                        layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); window.parent.global.getSyswarnRecord(); });
                                    } else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    default: break;
                }

            });

            tb_row_checkbox();

            pager.init();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["Syswarn_Time0", "Syswarn_Time1"], { type: 'datetime', range: true });
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                conditionParam.code = code;
                comtable.reload({
                    url: '/Monitoring/GetSyswarnList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
            }
        }
    </script>
</body>
</html>
