﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>事件管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }

        /* 修复加载提示样式 */
        .layui-layer-loading .layui-layer-content {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            color: #666;
        }

        /* 自定义加载提示样式 */
        .custom-loading {
            text-align: center;
            padding: 30px 20px;
            font-size: 14px;
            color: #666;
            background: #fff;
            border-radius: 6px;
        }

        .custom-loading .loading-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1E9FFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
            vertical-align: middle;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>事件管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">

                            <div class="layui-inline">
                                <input class="layui-input " name="ControlEvent_CarNo" id="ControlEvent_CarNo" autocomplete="off" placeholder="车牌号" />
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input" name="ControlEvent_Time0" id="ControlEvent_Time0" autocomplete="off" placeholder="事件时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="ControlEvent_Time1" id="ControlEvent_Time1" autocomplete="off" placeholder="事件时间止" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ControlEvent_Status" name="ControlEvent_Status" lay-search>
                                    <option value="">处理状态</option>
                                    <option value="0" selected>未处理</option>
                                    <option value="1">已忽略</option>
                                    <option value="2">欠费出场</option>
                                    <option value="3">新增入场</option>
                                    <option value="4">已缴费</option>
                                    <option value="5">已处理</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>

                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input type="text" class="layui-input" id="ControlEvent_No" name="ControlEvent_No" placeholder="事件编号" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ControlEvent_Type" name="ControlEvent_Type" lay-search>
                                    <option value="">事件类型</option>
                                    <option value="1">跟车事件</option>
                                    <option value="2">异常消息</option>
                                    <option value="3">倒车事件</option>
                                    <option value="5">遥控开闸</option>
                                    <option value="6">遥控关闸</option>
                                    <option value="7">长时间不落杆</option>
                                    <option value="8">道闸开关异常</option>
                                    <option value="9">长时间停车</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="区域名称" class="layui-select" id="ControlEvent_ParkAreaNo" name="ControlEvent_ParkAreaNo" lay-search>
                                    <option value="">区域名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车道名称" class="layui-select" id="ControlEvent_PasswayNo" name="ControlEvent_PasswayNo" lay-search>
                                    <option value="">车道名称</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" id="Plus" lay-event="Plus"><i class="fa fa-plus"></i><t>新增事件</t></button>
                                <button class="layui-btn layui-btn-sm" id="Close" lay-event="Close"><i class="fa fa-ban"></i><t>忽略跟车</t></button>
                                <button class="layui-btn layui-btn-sm" id="Add" lay-event="Add"><i class="fa fa-plus"></i><t>新增入场</t></button>
                                <button class="layui-btn layui-btn-sm" id="Update" lay-event="Update"><i class="fa fa-history"></i><t>关联出场</t></button>
                                <button class="layui-btn layui-btn-sm" id="Audit" lay-event="Audit"><i class="fa fa-check-circle-o"></i><t>确认倒车</t></button>
                                <button class="layui-btn layui-btn-sm" id="Reject" lay-event="Reject"><i class="fa fa-close"></i><t>忽略倒车</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="myVideo" class="layui-hide" style="position:relative;overflow:hidden;">
        <video src="#" controls="controls" autoplay style="margin-left:2px;margin-top:2px;">
            <span>当前浏览器不支持video标签</span>
        </video>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>

    <script>
        topBar.init();

        var comtable = null;
        s_carno_picker.init("ControlEvent_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "ControlEvent_CarNo") {
                $("#ControlEvent_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'ControlEvent_ID', title: 'ID', hide: true }
                , { field: 'ControlEvent_No', title: '事件编号', hide: true }
                , { field: 'ControlEvent_Time', title: '事件时间', width: 170 }
                , { field: 'ControlEvent_PasswayNo', title: '车道编号', hide: true }
                , { field: 'ControlEvent_PasswayName', title: '车道名称' }
                , {
                    field: 'ControlEvent_Gate', title: '出入类型', templet: function (d) {
                        if (d.ControlEvent_Gate == 0) return tempBar(2, '出口');
                        else if (d.ControlEvent_Gate == 1) return tempBar(1, '入口');
                    }
                }
                , {
                    field: 'ControlEvent_Type', title: '事件类型', templet: function (d) {
                        if (d.ControlEvent_Type == 1) return tempBar(1, '跟车事件');
                        else if (d.ControlEvent_Type == 2) return tempBar(3, '异常消息');
                        else if (d.ControlEvent_Type == 3) return tempBar(2, '倒车事件');
                        else if (d.ControlEvent_Type == 5) return tempBar(4, '遥控开闸');
                        else if (d.ControlEvent_Type == 6) return tempBar(5, '遥控关闸');
                        else if (d.ControlEvent_Type == 7) return tempBar(6, '长时间不落杆');
                        else if (d.ControlEvent_Type == 8) return tempBar(0, '道闸开关异常');
                        else if (d.ControlEvent_Type == 9) return tempBar(6, '长时间停车');
                    }
                }
                , {
                    field: 'ControlEvent_Status', title: '处理状态', templet: function (d) {
                        if (d.ControlEvent_Status == 0) return tempBar(0, '未处理');
                        else if (d.ControlEvent_Status == 1) return tempBar(3, '已忽略');
                        else if (d.ControlEvent_Status == 2) return tempBar(6, '欠费出场');
                        else if (d.ControlEvent_Status == 3) return tempBar(2, '新增入场');
                        else if (d.ControlEvent_Status == 4) return tempBar(1, '已缴费');
                        else if (d.ControlEvent_Status == 5) return tempBar(1, '已处理');
                    }
                }
                , { field: 'ControlEvent_CarNo', title: '车牌号' }
                , { field: 'ControlEvent_BigImg', title: '大图', templet: function (d) { return tempImg(d.ControlEvent_BigImg); } }
                , { field: 'ControlEvent_SmallImg', title: '小图', hide: true, templet: function (d) { return tempImg(d.ControlEvent_SmallImg); } }
                , {
                    field: 'ControlEvent_Video', title: '视频', templet: function (d) {
                        if (d.ControlEvent_Video == null || d.ControlEvent_Video == '') {
                            return '暂无';
                        } else {
                            var videoUrl = d.ControlEvent_Video;
                            // 判断是否为YM01回放链接（支持标识URL和真实URL）
                            if (videoUrl.startsWith('vzicloud://playback') || (videoUrl.includes('vzicloud.com') && videoUrl.includes('playback.flv'))) {
                                return "<div class='layui-btn layui-btn-xs' onclick='playVizCloudVideo(\"" + videoUrl + "\",\"" + d.ControlEvent_PasswayNo + "\")'>查看回放</div>";
                            }
                            // 判断是否为萤石云链接
                            else if (videoUrl.indexOf('open.ys7') != -1) {
                                return "<div class='layui-btn layui-btn-xs' onclick='openVideoView1(\"" + videoUrl + "\")'>查看</div>";
                            }
                            // 其他本地视频链接
                            else {
                                return "<div class='layui-btn layui-btn-xs' onclick='openVideoView(\"" + d.ControlEvent_ID + "\")'>查看</div>";
                            }
                        }
                    }
                }
                //, { field: 'ControlEvent_Content', title: '事件内容', hide: true }
                , { field: 'ControlEvent_ParkOrderNo', title: '停车订单号' }
                , { field: 'ControlEvent_Money', title: '应缴金额' }
                , { field: 'ControlEvent_AddTime', title: '创建时间', hide: true }
                , { field: 'ControlEvent_Remark', title: '备注' }
                , { field: 'ControlEvent_ParkNo', title: '车场编码', hide: true }
                , { field: 'ControlEvent_ParkName', title: '车场名称', hide: true }
                , { field: 'ControlEvent_ParkAreaNo', title: '区域编号', hide: true }
                , { field: 'ControlEvent_ParkAreaName', title: '区域名称', hide: true }
                , { field: 'ControlEvent_DeviceNo', title: '相机编号', hide: true }
                , { field: 'ControlEvent_DeviceName', title: '相机名称', hide: true }
                , {
                    field: 'btns', title: '操作', templet: function (d) {
                        if (d.ControlEvent_Type != 1) return ''; // 非跟车事件则忽略显示
                        return "<div class='layui-btn layui-btn-xs' onclick='openCheckFrame(\"" + d.ControlEvent_No + "\")'>信息核对</div>";
                    }
                }

            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ControlEvent/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                //, totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data);
                    pager.data = data.data;
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        if (data.length <= 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data.length > 1) { layer.msg("仅支持单选", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Type != 1) { layer.msg("请选择跟车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Gate != 1) { layer.msg("请选择[入口]跟车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Status == 2 || data[0].ControlEvent_Status == 3) { layer.msg("选择的事件已处理", { icon: 0, time: 1500 }); return; }
                        layer.open({
                            type: 2,
                            title: "新增入场订单",
                            content: "/ControlEvent/Add?ControlEvent_No=" + data[0].ControlEvent_No,
                            area: getIframeArea(["800px", "550px"]),
                            maxmin: false
                        });
                        break;
                    case 'Update':
                        if (data.length <= 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data.length > 1) { layer.msg("仅支持单选", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Type != 1) { layer.msg("请选择跟车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Gate != 0) { layer.msg("请选择[出口]跟车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Status == 2 || data[0].ControlEvent_Status == 3) { layer.msg("选择的事件已处理", { icon: 0, time: 1500 }); return; }
                        pager.carno = data[0].ControlEvent_CarNo;
                        layer.open({
                            type: 2,
                            title: "关联出场订单",
                            content: "/ControlEvent/SelectOrder?ControlEvent_No=" + data[0].ControlEvent_No,
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        break;
                    case 'Close':
                        if (data.length <= 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data.length > 1) { layer.msg("仅支持单选", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Type != 1) { layer.msg("请选择跟车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Status == 1) { layer.msg("事件已忽略", { icon: 0, time: 1500 }); return; }

                        LAYER_OPEN_TYPE_0("已处理的订单将会还原到处理之前的状态<br/>确定忽略此事件?", res => {
                            layer.msg("处理中...", { icon: 16, time: 0 });
                            $.post("IgnoreControlEvent", { ControlEvent_No: data[0].ControlEvent_No, ParkOrder_No: data[0].ParkOrder_No }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                        pager.bindData(pager.pageIndex);
                                    })
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            }, "json");
                        }, res => { })
                        break;
                    case 'Audit':
                        if (data.length <= 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Type != 3) { layer.msg("请选择倒车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Status == 5) { layer.msg("事件已处理", { icon: 0, time: 1500 }); return; }

                        LAYER_OPEN_TYPE_0("<t style='color:red;'>请慎重操作!确定车辆已倒车?</t>", res => {
                            layer.msg("处理中...", { icon: 16, time: 0 });
                            $.post("CompleteBackEvent", { ControlEvent_No: data[0].ControlEvent_No }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                        pager.bindData(pager.pageIndex);
                                    })
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            }, "json");
                        }, res => { })
                        break;
                    case 'Reject':
                        if (data.length <= 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Type != 3) { layer.msg("请选择倒车事件", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Status == 5) { layer.msg("事件已处理", { icon: 0, time: 1500 }); return; }

                        LAYER_OPEN_TYPE_0("确认倒车事件为误触发,车辆并未倒车?", res => {
                            layer.msg("处理中...", { icon: 16, time: 0 });
                            $.post("IngoreBackEvent", { ControlEvent_No: data[0].ControlEvent_No }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                        pager.bindData(pager.pageIndex);
                                    })
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            }, "json");
                        }, res => { })

                        break;
                    case 'Plus':
                        layer.open({
                            type: 2,
                            title: "新增跟车事件",
                            content: "/ControlEvent/AddEvent",
                            area: getIframeArea(["800px", "550px"]),
                            maxmin: false
                        });
                        break;
                    case 'Chekced':
                        if (data.length <= 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data.length > 1) { layer.msg("仅支持单选", { icon: 0, time: 1500 }); return; }
                        if (data[0].ControlEvent_Type != 1) { layer.msg("请选择跟车事件", { icon: 0, time: 1500 }); return; }
                        layer.open({
                            type: 2,
                            title: "查询核对",
                            content: "/ControlEvent/CheckOrder?ControlEvent_No=" + data[0].ControlEvent_No,
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        break;
                };
            });

            tb_row_radio(table)
        });

        function openVideoView(id) {
            var videoUrl = pager.data.find((item, index) => { return item.ControlEvent_ID == id; }).ControlEvent_Video;
            $("#myVideo video").attr("src", PathCheck(videoUrl));
        }
        function openVideoView1(url) {
            layer.open({
                type: 2,
                title: false,
                content: 'Monitor?url=' + url,
                area: getIframeArea(['778px', '443px']),
                maxmin: false
            });
        }

        var video = document.querySelector('video');
        //自适应宽高
        video.addEventListener('canplay', function () {
            var width = this.videoWidth;
            var height = this.videoHeight;
            console.log(width)
            console.log(height)
            if (width > height) {
                while (width > 900) {
                    width = width * 0.8;
                    height = height * 0.8;
                }
            } else {
                while (height > 600) {
                    width = width * 0.8;
                    height = height * 0.8;
                }
            }
            this.width = width - 5;
            this.height = height - 5;

            var html = $("#myVideo").html();
            layer.open({
                id: 'video',
                type: 1,
                title: false,
                content: html,
                area: getIframeArea([width + "px", height + "px"]),
                close: function () {
                    video.pause();
                }
            })
        });
        var isfirst = true;
        video.addEventListener("error", function () {
            if (!isfirst) {
                switch (video.error.code) {
                    case 1:
                        layer.msg("中止获取该视频");
                        break;
                    case 2:
                        layer.msg("该视频下载时发生错误");
                        break;
                    case 3:
                        layer.msg("该视频解码时发生错误");
                        break;
                    case 4:
                        layer.msg("该视频地址加载失败.");
                        break;
                    default:
                        layer.msg("该视频地址加载失败");
                        break;
                }
            }
            isfirst = false;
        });

        var openCheckFrame = function (ControlEvent_No) {
            var data = pager.data.find((item, index) => { return item.ControlEvent_No == ControlEvent_No; });
            if (data == null) { layer.msg("事件不存在", { icon: 0 }); return; }
            if (data.ControlEvent_Gate != 0) { layer.msg("请选择[出口]跟车事件", { icon: 0, time: 1500 }); return; }
            pager.orderno = data.ControlEvent_ParkOrderNo;
            pager.carno = data.ControlEvent_CarNo;
            pager.status = data.ControlEvent_Status;
            layer.open({
                type: 2,
                title: "查询核对",
                content: "/ControlEvent/CheckOrder?ControlEvent_No=" + data.ControlEvent_No,
                area: getIframeArea(["900px", "95%"]),
                maxmin: false
            });
        }

    </script>
    <script>
        var pager = {
            orderno: "",
            carno: "",
            status: null,
            data: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                            $("#ControlEvent_ParkAreaNo").append(option)
                            layui.form.render("select");
                        });
                    }
                }, "json");

                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#ControlEvent_PasswayNo").append(option);
                            layui.form.render("select");
                        });
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["ControlEvent_Time0", "ControlEvent_Time1"], { type: "datetime", range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/ControlEvent/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent?.global?.getBtnPower(window, function (pagePower) {
                    $("#Chekced").removeClass("layui-hide");
                });
            }
        }

        // 播放YM01视频（支持标识URL和真实URL）
        function playVizCloudVideo(url, passwayNo) {
            if (!url) {
                layer.msg("无效的视频地址", { icon: 2 });
                return;
            }

            // 如果是标识URL，需要先获取真实播放地址
            if (url.startsWith('vzicloud://playback')) {
                // 显示自定义加载提示
                var loadingIndex = layer.open({
                    type: 1,
                    title: false,
                    content: '<div class="custom-loading"><div class="loading-icon"></div>正在获取视频播放地址...</div>',
                    shade: 0.3,
                    shadeClose: false,
                    closeBtn: 0,
                    area: ['300px', '120px'],
                    offset: 'auto'
                });

                // 调用后端API获取真实播放地址
                $.ajax({
                    type: 'POST',
                    url: '/ControlEvent/GetVizCloudPlaybackUrl',
                    data: {
                        identifierUrl: url,
                        passwayNo: passwayNo
                    },
                    success: function (response) {
                        layer.close(loadingIndex);
                        if (response.success && response.data) {
                            openVizCloudPlaybackView(response.data, passwayNo);
                        } else {
                            layer.msg(response.message || "获取视频播放地址失败", { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.close(loadingIndex);
                        layer.msg("获取视频播放地址失败", { icon: 2 });
                    }
                });
            } else {
                // 直接播放真实URL
                openVizCloudPlaybackView(url, passwayNo);
            }
        }



        // 打开YM01视频回放窗口
        function openVizCloudPlaybackView(url, passwayNo) {
            if (!url) {
                layer.msg("无效的视频地址", { icon: 2 });
                return;
            }
            // 对 URL 进行编码，防止特殊字符导致问题
            var encodedUrl = encodeURIComponent(url);
            layer.open({
                type: 2, // iframe 层
                title: 'YM01视频回放', // 窗口标题
                // 注意：确保 Controller 中有对应的 Action: /ControlEvent/PlayVizCloudPlayback
                content: '/ControlEvent/PlayVizCloudPlayback?videoUrl=' + encodedUrl + '&passwayNo=' + passwayNo,
                area: getIframeArea(['800px', '600px']), // 设置弹窗大小，可根据需要调整
                maxmin: true // 允许最大化最小化
            });
        }
    </script>
</body>
</html>
