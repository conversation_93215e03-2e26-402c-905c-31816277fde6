﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }
        select[multiple] + .layui-form-select dd { padding: 0; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }
        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }
        .multiSelect a { padding: 2px 5px; background: #5FB878; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }
        .multiSelect a span { float: left; }
        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }
        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }
        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }
        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }
        .multiSelect a i:hover { background-color: #545556; }
        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }
        .multiOption:hover { color: #5FB878 }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            @*<div class="layui-row">
                <div class="layui-col-xs3 edit-label">可停区域</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="btnCombox" id="StopSpace_Type">
                        <ul class="flex">
                            <li class="select" data-value="0">全部区域</li>
                            <li class="" data-value="1">选择区域</li>
                        </ul>
                    </div>
                </div>
            </div>*@
            <div class="layui-row arealist">
                <div class="layui-col-xs3 edit-label">可停区域</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select id="StopSpace_AreaNo" name="StopSpace_AreaNo" multiple lay-tools lay-search>
                        <option value="">可停区域</option>
                        <option value="0">全部区域</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">车位数量</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input v-null v-number" id="StopSpace_Number" name="StopSpace_Number" autocomplete="off" maxlength="3" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">车位信息</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" id="StopSpace_Content" name="StopSpace_Content" autocomplete="off" maxlength="50" />
                </div>
            </div>
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <vbutton class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></vbutton>
                <vbutton class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></vbutton>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        myVerify.init();

        layui.use(['element', 'form'], function () {
            pager.init();
        })
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("no");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            areaAll: [],
            StopSpace_Type: 0,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        pager.areaAll = json.data;

                        var options = "";
                        json.data.forEach(function (d, i) {
                            options += '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                        });
                        $("#StopSpace_AreaNo").append(options);
                        layui.form.render();                        
                    }
                }, "json");
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    var data = null;
                    parent.pager.areaList.forEach(function (item, i) {
                        if (item.StopSpace_No == paramNo) {
                            data = item;
                        }
                    });
                    $("#verifyCheck").fillForm(data, function (data) { });
                    if (data.StopSpace_Type == 1) {
                        pager.StopSpace_Type = 1;
                        $("#StopSpace_AreaNo").val(JSON.parse(data.StopSpace_AreaNo));
                    } else {
                        pager.StopSpace_Type = 0;
                        $("#StopSpace_AreaNo").val(['0']);
                    }
                    layui.form.render();
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.StopSpace_Content = $("#StopSpace_Content").val();
                        data.StopSpace_AreaNo = $("#StopSpace_AreaNo").val();
                        return data;
                    });
                    param.StopSpace_Type = pager.StopSpace_Type;
                    var StopSpace_AreaNo = param.StopSpace_AreaNo;
                    if (param.StopSpace_Type == 1) {
                        if (StopSpace_AreaNo == null || StopSpace_AreaNo.length == 0) {
                            layer.msg("请选择区域");
                            return;
                        }
                        var areaName = [];
                        StopSpace_AreaNo.forEach(function (no, x) {
                            pager.areaAll.forEach(function (item, i) {
                                if (item.ParkArea_No == no) {
                                    areaName[areaName.length] = item.ParkArea_Name;
                                }
                            });
                        });
                        param.StopSpace_AreaNo = JSON.stringify(StopSpace_AreaNo);
                        param.StopSpace_AreaName = areaName.join(',');
                    } else {
                        param.StopSpace_AreaNo = JSON.stringify(StopSpace_AreaNo);
                        param.StopSpace_AreaName = "全部区域";
                    }

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        param.StopSpace_No = new Date().getTime();
                    }
                    else if (paramAct == "Update") {
                        param.StopSpace_No = paramNo;
                    }

                    parent.areaTable.addOrUpdate(param);
                });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "StopSpace_AreaNo") {
                        if (data.current_value == '0') {
                            $("#StopSpace_AreaNo").val(['0']);
                            pager.StopSpace_Type = 0;
                            layui.form.render()
                        } else {
                            pager.StopSpace_Type = 1;
                            var index = data.value.indexOf('0');
                            if (index > -1) {
                                data.value.splice(index, 1);
                                $("#StopSpace_AreaNo").val(data.value);
                                layui.form.render()
                            }
                        }
                    }
                });
            },
        };
    </script>    
</body>
</html>
