﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入黑名单</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 20px; }
        .line-link { color: #0094ff; text-decoration: underline; }
        #Import,#Cancel { min-width: 90px; }
        #download-temp { margin-right:10px; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="layui-card-header"></div>
        <div id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label lan-label" data-lan="Select_Excel">选择文件</div>
                <div class="layui-col-xs9 edit-ipt-ban">
                    <div class="layui-form-item layui-row margin-bottom0">
                        <div class="layui-col-xs9">
                            <input type="text" placeholder="" id="iptFilePath" class="layui-input" readonly="readonly">
                        </div>
                        <div class="layui-col-xs3">
                            <span class="input-group-btn">
                                <label title="选择" for="inputFile" class="layui-btn">
                                    <input type="file" accept=".xlsx" name="file" id="inputFile" class="hide" onchange="chooseFile(this);"><i class="fa fa-folder-open-o"></i> <t class="lan-label" data-lan="Select">选择</t>
                                </label>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs2">&nbsp;</div>
                <div class="layui-col-xs9">
                    <a href="../../Data/blacklist_template.xlsx" id="download-temp" download="" class="btn btn-danger"><i class=" fa fa-download"></i> 下载模板</a>
                    <button class="btn btn-primary" lay-submit lay-filter="formDemo" id="Import"><i class="fa fa-check"></i> 提交</button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2">&nbsp;</div>
                <div class="layui-col-xs9">
                    <label class="label-desc" style="color:#ff5722;display: block;max-width: 100%;margin-bottom: 5px;font-weight: 700;">温馨提示：</label>
                    <label class="label-desc" style="color:#ff5722;display: block;max-width: 100%;margin-bottom: 5px;font-weight: 700;">1、若车牌号已存在，不会重复写入和更新；</label>
                    <label class="label-desc" style="color:#ff5722;display: block;max-width: 100%;margin-bottom: 5px;font-weight: 700;">2、导入完成后，会在3分钟内同步完成，请等待3分钟后再操作删除或更新黑名单信息；</label>
                    <label class="label-desc" style="color:#ff5722;display: block;max-width: 100%;margin-bottom: 5px;font-weight: 700;">3、只允许中文字母数字还有,，.。:：，\-/*+，其它字符都会被替换成空字符串</label>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/ajaxfileupload2.js" asp-append-version="true"></script>
    <script>
        var paramCommunityNo = $.getUrlParam("communityNo");
        var comElement = null;
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'form', 'element'], function () { comElement = layui.element; });
    </script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        function chooseFile(iptFile) {
            $("#iptFilePath").val(iptFile.value);
        }
        function ajaxFileUpload(ctNo) {
            $("#Import").attr("disabled", true);
            layer.msg("耗时较长,请等待..", { icon: 16, time: 0 });
            $.ajaxFileUpload({
                url: '/BlackList/ImportBlackList',
                type: 'post',
                data: {},
                secureuri: false,
                fileElementId: ['inputFile'],
                dataType: 'json',
                success: function (data) {
                    if (data.success) {
                        layer.open({
                            content: data.msg.replace(/\n/g, '<br>'),
                            btn: ["我知道了"],
                            yes: function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            }
                        });
                    } else {
                        var retMsg = data.msg ? data.msg.replace(/\n/g, '<br>') : "导入失败，未知错误";
                        layer.open({ content: retMsg });
                    }
                },
                complete: function () {
                    $("#iptFilePath").val("");
                    $("#inputFile").val("")
                    $("#Import").attr("disabled", false);
                    layer.closeAll('loading');
                },
                error: function (xhr, status, error) {
                    console.error("AJAX错误:", status, error);
                    console.log("响应文本:", xhr.responseText);
                    layer.open({
                        content: "导入失败，请检查网络连接或联系管理员。错误详情：" + error
                    });
                }
            });
            return false;
        }
        var pager = {
            init: function () {
                this.bindSelect();
                this.bindEvent();
            },
            bindSelect: function () {
            },
            bindEvent: function () {
                $("#Import").click(function () {
                    var ctNo = $("#Community_No").val();
                    if ($("#inputFile").val() == "") { layer.msg("请选择文件", { icon: 0 }); return; }
                    ajaxFileUpload(ctNo);
                });
                $("#Cancel").click(function () {
                    parent.layer.close(index);
                });
                $("#download-temp").click(function () {
                    this.href = "../../Data/blacklist_template.xlsx";
                });
            }
        };

        $(function () { pager.init() });
    </script>
</body>
</html>
