﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <style>
        label { padding: 10px 10px 0 0; float: right; }
        .layui-tab-title { background-color: #5868e0 !important; }
        .layui-form { font-size: 1rem; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="layui-form" id="verify-form">
            <div class="layui-row" style="margin-top:1rem;">
                <div class="layui-col-xs4 edit-label">播放模式</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="btnCombox falsemodify" id="isLoadVideo">
                        <ul>
                            <li data-value="0">刷图模式</li>
                            <li data-value="1" class="select">视频模式</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-row" style="margin-top:1.5rem;">
                <div class="layui-col-xs4 edit-label">辅助相机</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="btnCombox falsemodify" id="subsVideo">
                        <ul>
                            <li data-value="0" class="select">禁用视频</li>
                            <li data-value="1">启用视频</li>
                        </ul>
                    </div>
                </div>
            </div>

            @{
                if (carparking.BLL.Cache.AppBasicCache.CurrentSysConfigContent?.SysConfig_PlayerType == 0)
                {
                    <div class="layui-row" style="margin-top:1.5rem;">
                        <div class="layui-col-xs4 edit-label">增强型视频播放</div>
                        <div class="layui-col-xs7 edit-ipt-ban">
                            <div class="btnCombox falsemodify" id="playerType">
                                <ul>
                                    <li data-value="2">禁用</li>
                                    <li data-value="1" class="select">启用</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
    <script>
        var config = {
            isLoadVideo: 1,
            subsVideo: 0,
            playerType: 2
        };

        layui.use(["form"], function () {
            var con = parent.modeConfig.getConfig();
            con.isLoadVideo = con.isLoadVideo ? 1 : 0;
            con.playerType = localStorage.getItem("playerType") ?? 2;
            LoadConfig(con);

            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");
                onEventCombox(idName);
            });
            layui.form.render();
            //$("#isLoadVideo").val(con.isLoadVideo ? 1 : 0);
        });

        var onEventCombox = function (idName) {
            if (idName == 'playerType') {

            }
        }
        var LoadConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }
    </script>

</body>
</html>
