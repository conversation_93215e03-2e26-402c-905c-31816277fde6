﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">型号分类</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select id="Drive_Category" name="Drive_Category" class="layui-select" lay-search>
                        <option value="">型号分类</option>
                        <option value="1">相机</option>
                        <option value="2">道闸</option>
                        <option value="3">显示屏</option>
                        <option value="4">自助设备</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">型号代码</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="32" id="Drive_Code" name="Drive_Code" autocomplete="off" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">设备名称</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="20" id="Drive_Name" name="Drive_Name" autocomplete="off" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">设备别称</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="20" id="Drive_Fileds" name="Drive_Fileds" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">设备IP</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="50" id="Drive_Ip" name="Drive_Ip" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">设备端口</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="5" id="Drive_Port" name="Drive_Port" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">设备账号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="50" id="Drive_Account" name="Drive_Account" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">设备密码</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="50" id="Drive_Pwd" name="Drive_Pwd" autocomplete="off" />
                </div>
            </div>
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
    </div>
   
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        layui.use('form', function () {
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("Drive_No");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $("#Drive_Category").attr("disabled", true);
                    $("#Drive_Name").attr("disabled", true);
                    $.getJSON("GetDetail", { Drive_No: paramNo }, function (json) {
                        if (json.Success) {
                            $("#verifyCheck").fillForm(json.Data, function (data) { });
                        }
                    });
                    layui.form.render("select")
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Drive_Code = $("#Drive_Code").val();
                        data.Drive_Fileds = $("#Drive_Fileds").val();
                        data.Drive_Ip = $("#Drive_Ip").val();
                        data.Drive_Port = $("#Drive_Port").val();
                        data.Drive_Account = $("#Drive_Account").val();
                        data.Drive_Pwd = $("#Drive_Pwd").val();
                        return data;
                    });

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.getJSON("/Drive/Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {
                        param.Drive_No = paramNo;
                        $.getJSON("/Drive/Update", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            },
        };
    </script>
</body>
</html>
