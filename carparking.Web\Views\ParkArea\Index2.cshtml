﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车场/区域设置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .leftmenu, .rightbody { height: 100%; overflow: auto; }
        @@media screen and (max-width: 400px) {
            .leftmenu, .rightbody { height: auto !important;}
        }
        .fa { margin: 7px 4px; float: left; font-size: 16px; }
        html, body { width: 100%; height: 100%; overflow-x: hidden; }
        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }
        .leftmenu { user-select: none; padding: 1rem; }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .content-panel { height: 100%; overflow: auto; }
        .min100 { min-height: 100%; }

        .menu-list { width: 100%; overflow: auto; position: relative; }
        .menu-title { line-height: 1.5rem; padding: 10px 0; border-bottom: 1px dashed #ddd; }
        .menu-items { }
        .menu-item { padding-left: 1rem; }
        .menu-text { padding: .5rem 0; line-height: 1.5rem; position: relative; }
        .park-name { font-weight: bold; }
        .park-name:hover, .menu-text:hover { background-color: #ecf0f5; cursor: pointer; text-decoration: underline;}
        .park-name::before { content: " "; background: url('../../Static/img/icon/icon_p_park1.svg'); background-size: 100% 100%; padding: 0 .5rem;margin:.1rem; }
        .menu-text::before { content: "»"; color: #999; position: absolute; left: -.6rem; line-height: 1.3rem; }
        .menu-text::after { color: #ffffff; position: absolute; right: 0; padding: 0 3px; border-radius: 3px; font-size: 10px; }
        .menu-text.type1::after { content: "外场"; background-color: #0094ff; }
        .menu-text.type2::after { content: "内场"; background-color: #00bcd4; }
        .menu-list div.m-active1 { font-weight: bold; }
        .menu-list div.m-active { font-weight: bold; color: #f6800f; }
        ::-webkit-scrollbar { width: 5px; background-color: rgba(0,0,0,.1); }
        ::-webkit-scrollbar-thumb { background-color: #4facfa; border-radius: 5px; }

        #framePanl { padding: 1rem; }
        #framePanl iframe { border: 0; width: 100%; height: calc(100% - 4px); }
    </style>
</head>
<body class="animated fadeInRight">
    <div class="layui-col-sm3 leftmenu">
        <div class="layui-card min100">
            <div class="layui-card-header padding-15" id="addpanl">
                <button class="layui-btn layui-btn-fluid layui-bg-blue layui-hide" onclick="onOpenAdd()" id="Add">新增停车区域</button>
            </div>
            <div class="layui-card-body">
                <div class="menu-list">
                    <div class="menu-title park-name m-active" onclick="onOpenPark(this)">@ViewBag.Parking.Parking_Name</div>
                    <div class="menu-items">
                        <!--左侧菜单列表-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-sm9 rightbody" id="framePanl">

    </div>
    <script type="text/x-jquery-tmpl" id="tmplpark">
        <div class="content-panel">
            <div class="layui-card min100">
                <div class="layui-card-header padding-15 pan-title"><text>车场信息</text></div>
                <div class="layui-card-body layui-detail">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车场名称</label></div>
                        <div class="layui-col-xs6"><label class="value">${Parking_Name}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车场编号</label></div>
                        <div class="layui-col-xs6"><label class="value">${Parking_No}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车场KEY</label></div>
                        <div class="layui-col-xs6"><label class="value">${Parking_Key}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">云端状态</label></div>
                        <div class="layui-col-xs6" id="Parking_Online">
                            {{if Parking_Online==0 }}
                            <label class="layui-badge">未连接</label>
                            {{else}}
                            <label class="layui-badge layui-bg-blue">已连接</label>
                            {{/if}}
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车位总数</label></div>
                        <div class="layui-col-xs6"><label class="value">${Parking_SpaceNum}</label></div>
                    </div>

                    <hr />
                    <div class="layui-row">
                        <div class="layui-col-xs3">&nbsp;</div>
                        <div class="layui-col-xs8">
                            <button class="layui-btn layui-btn-sm layui-hide" id="SavePark"><i class="fa fa-edit"></i> 编辑</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="text/x-jquery-tmpl" id="tmplparkarea">
        <div class="content-panel">
            <div class="layui-card min100">
                <div class="layui-card-header padding-15 pan-title"><text>车场区域信息</text></div>
                <div class="layui-card-body layui-detail">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车场名称</label></div>
                        <div class="layui-col-xs6"><label class="value">${Parking_Name}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">区域类型</label></div>
                        <div class="layui-col-xs6">
                            {{if ParkArea_Type==0 }}
                            <label class="layui-badge layui-bg-blue">外场</label>
                            {{else}}
                            <label class="layui-badge layui-bg-blue">嵌套内场</label>
                            {{/if}}
                        </div>
                    </div>
                    {{if ParkArea_FID!=0 }}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">父区域</label></div>
                        <div class="layui-col-xs6"><label class="value">${ParkArea_FName}</label></div>
                    </div>
                    {{/if}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">区域名称</label></div>
                        <div class="layui-col-xs6"><label class="value">${ParkArea_Name}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车位数量</label></div>
                        <div class="layui-col-xs6"><label class="value">${ParkArea_SpaceNum}</label></div>
                    </div>

                    <hr />
                    <div class="layui-row">
                        <div class="layui-col-xs3">&nbsp;</div>
                        <div class="layui-col-xs8">
                            <button class="layui-btn layui-btn-sm layui-hide" id="Update" data-no="${ParkArea_No}"><i class="fa fa-edit"></i>编辑</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red layui-hide" id="Delete" data-no="${ParkArea_No}"><i class="fa fa-trash-o"></i>删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/x-jquery-tmpl" id="park_online">
        {{if online==1 }}
        <label class="layui-badge layui-bg-blue">已连接</label>
        {{else}}
        <label class="layui-badge">未连接</label>
        {{/if}}
    </script>
    <script type="text/javascript">
        myVerify.init();

        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index'], function () {
            pager.init();
        });

        //右侧加载停车场信息
        var onOpenPark = function (e) {
            if (e) {
                $(".menu-text,.menu-title").removeClass("m-active");
                $(e).removeClass("m-active").addClass("m-active");
            }
            var data = [pager.parkData];

            $("#framePanl").html($("#tmplpark").tmpl(data));
            pager.bindPower();
            pager.bindEvent();

            $.ajaxSettings.async = true;
            $.post("GetParkingState", {}, function (json) {
                if (json.success) {
                    $("#Parking_Online").html($("#park_online").tmpl([{ online: 1 }]));
                } else {
                    $("#Parking_Online").html($("#park_online").tmpl([{ online: 0 }]));
                }
            });
            $.ajaxSettings.async = false;
        }

        //右侧加载停车场区域信息
        var onOpenArea = function (ParkArea_No) {
            var data = [];
            pager.allArea.forEach(function (item, index) {
                if (item.ParkArea_No == ParkArea_No) {
                    data[0] = item;
                }
            });
            $("#framePanl").html($("#tmplparkarea").tmpl(data));
            pager.bindPower();
            pager.bindEvent();
        }

        var onOpenAdd = function () {
            layer.open({
                type: 2, id: 1,
                title: "新增停车区域",
                content: "EditArea?Act=Add",
                area: getIframeArea(["600px", "500px"]),
                maxmin: false
            });
            parent.top.setScrollTop(document.body, 0);
        }

        var pager = {
            parkData: null, //车场信息
            areaData: null, //区域数据已解析为前段展示格式
            allArea: null,  //全部区域
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindData: function (ParkArea_No) {
                layer.closeAll();
                var that = this;
                $.post("GetParkArea", {}, function (json) {
                    if (json.success) {
                        that.parkData = json.data.parkData;
                        that.areaData = json.data.areaData;
                        that.allArea = json.data.allArea;

                        area.init(that.areaData, ParkArea_No);
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower["Add"]) {
                        $("#addpanl").removeClass("layui-hide")
                    } else {
                        $("#addpanl").removeClass("layui-hide").addClass("layui-hide")
                    }
                });
            },
            bindEvent: function () {
                $("#SavePark").unbind("click").click(function () {
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑车场信息",
                        content: "EditPark",
                        area: getIframeArea(["600px", "550px"]),
                        maxmin: false
                    });
                    parent.top.setScrollTop(document.body, 0);
                });

                $("#Update").unbind("click").click(function () {
                    var ParkAreaNo = $(this).attr("data-no");
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑车场区域信息",
                        content: "EditArea?Act=Update&ParkArea_No=" + ParkAreaNo,
                        area: getIframeArea(["600px", "500px"]),
                        maxmin: false
                    });
                    parent.top.setScrollTop(document.body, 0);
                });

                $("#Delete").unbind("click").click(function () {
                    var ParkAreaNo = $(this).attr("data-no");
                    $("#Update,#Delete").attr("disabled", true);
                    LAYER_OPEN_TYPE_0("确定删除停车区域?", res => {
                        LAYER_LOADING("处理中...");
                        $.post("DelParkArea", { ParkArea_No: ParkAreaNo }, function (json) {
                            if (json.success) {
                                window.pager.bindData();
                                layer.msg("删除成功", { time: 1000 }, function () { })
                            } else {
                                $("#Update,#Delete").removeAttr("disabled")
                                layer.msg(json.msg);
                            }
                        }, "json");
                    }, res => {
                        $("#Update,#Delete").removeAttr("disabled")
                    })
                    parent.top.setScrollTop(document.body, 0);
                })
            },
            //延迟刷新
            bindTimeOut: function () {
                setTimeout(function () { pager.bindData(); }, 3000);
            }
        }

        var area = {
            action: 0,//当前操作：0-编辑，1-新增
            cur: null,//当前显示的停车区域信息
            init: function (areaData, ParkArea_No) {
                area.onload(areaData, ParkArea_No)
            },
            //加载左侧列表数据
            onload: function (data, ParkArea_No) {
                var html = this.onHtml(data);
                $(".menu-items").html(html);

                var menus = $(".menu-list").find(".menu-text")
                if (menus != null && menus.length > 0) {
                    if (ParkArea_No == null) {
                        //area.openAreaItem(menus[0]);
                        onOpenPark();
                    } else {
                        for (var i = 0; i < menus.length; i++) {
                            if ($(menus[i]).attr("data-no") == ParkArea_No) {
                                area.openAreaItem($(menus[i]));
                                break;
                            }
                        }
                    }
                }
            },
            //左侧列表Html
            onHtml: function (data) {
                var htm = "";
                for (var i = 0; i < data.length; i++) {
                    //分辨外场、内场
                    var type = data[i].ParkArea_Type == 0 ? "type1" : "type2";
                    var item = '<ul class="menu-item"><li>'
                    item += '   <div class="menu-text ' + type + '" data-no="' + data[i].ParkArea_No + '" onclick="area.openAreaItem(this)">' + data[i].ParkArea_Name + '</div>'
                    if (data[i].ParkArea_SubList != null && data[i].ParkArea_SubList.length > 0) {
                        item += area.onHtml(data[i].ParkArea_SubList);
                    }
                    item += '</li></ul>';
                    htm += item;
                }
                return htm;
            },
            //点击左侧列表打开详情窗口，并加载数据
            openAreaItem: function (e) {
                var ParkArea_No = $(e).attr("data-no");
                $(".menu-text,.menu-title").removeClass("m-active");
                $(e).removeClass("m-active").addClass("m-active")
                onOpenArea(ParkArea_No)
            }
        }
    </script>
</body>
</html>
