﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>批量授权</title>
	<meta name="keywords" content="">
	<meta name="description" content="">
	<link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
	<link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
	<link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
	<link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
	<link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
	<link href="~/Static/css/com.ui.css" rel="stylesheet" />
	<style>
		.layui-row { margin-bottom: 15px; }
	</style>
	<style>
		.layui-table-cell { overflow: visible !important; }
		.tdSelect { width: 100%; max-width: 200px; height: 30px; border: 1px solid #d9d9d9; border-radius: 3px; color: #0094ff; font-size: 13px; }

		.layui-table-view .layui-table td { overflow: hidden; }
		.ibox-content { padding: 0px 20px 20px; }
		.layui-table { margin: 1px 0; }

		.areaBtn { background-color: #fff; border-color: #1ab394; color: #1ab394; height: 30px; line-height: 30px; padding: 0 10px; }
		.areaBtn:hover { background-color: #1ab394; border-color: #1ab394; color: #fff; }
	</style>
</head>
<body>
	<div style="overflow:hidden;height:0;">
		<!--防止浏览器保存密码后自动填充-->
		<input type="password" />
		<input type="text" />
	</div>
	<div class="ibox-content">
		<div id="verifyCheck" class="layui-form">
			<div class="layui-form-item">
				<div class="layui-col-xs12">
					<script type="text/html" id="toolbarareas">
						<div class="layui-btn-container">
								<button class="layui-btn layui-btn-outline layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i> <t>新增可停区域</t></button>
							</div>
					</script>
					<table class="layui-table" lay-filter="table_area" id="table_area"> </table>

					<div class="label-desc" style="clear:both;color:#d58512;">可停区域至少设置一个。车位数大于0则表示当前有免费车位，无车位则设置0。</div>
				</div>
			</div>

		</div>

		<div class="hr-line-dashed"></div>
		<div class="layui-row">
			<div class="layui-col-xs3 edit-label">&nbsp;</div>
			<div class="layui-col-xs7 edit-ipt-ban">
				<vbutton class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>确定</t></vbutton>
				<vbutton class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></vbutton>
			</div>
		</div>
	</div>
	<!--可停区域表格-->
	<script type="text/x-jquery-tmpl" id="tempParkAreaNo">
		<select class="tdSelect" data-no="{{d.StopSpace_ID}}" data-key="StopSpace_AreaNo" lay-ignore>
			{{# layui.each(pager.areas, function(index,item){ }}
			 <option {{# if (d.StopSpace_AreaNo==item.ParkArea_No){}} selected {{# } }}  value="{{ item.ParkArea_No }}">{{ item.ParkArea_Name }}</option>
			{{# });}}
		</select>
	</script>
	<script type="text/x-jquery-tmpl" id="areaName">
		{{# if(d.StopSpace_Type==0){ }}
		<span>全部区域</span>
		{{# }else{ }}
		<span title="{{d.StopSpace_AreaName}}">{{d.StopSpace_AreaName}}</span>
		{{# } }}
	</script>
	<script type="text/x-jquery-tmpl" id="areabtns">
		<vbutton class="layui-btn layui-btn-xs editarea areaBtn" data-no="{{d.StopSpace_No}}">编辑</vbutton>
		<vbutton class="layui-btn layui-btn-xs layui-btn-danger delarea areaBtn" data-no="{{d.StopSpace_No}}">删除</vbutton>
	</script>
	<script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
	<script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
	<script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
	<script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
	<script>
		var index = parent.layer.getFrameIndex(window.name);
		myVerify.init();
		layui.use(['table', 'element'], function () {
			pager.init()
		});

		var createNumber = function (len, data) {
			var d = '';
			for (var i = 0; i < len; i++) {
				d = d + Math.floor(Math.random() * 10);
			}

			var s = (new Date().getTime()) + d;
			if (data != null && data.length > 0) {
				var str = JSON.stringify(data);
				if (str.indexOf(s) > -1) {
					return createNumber(len, data);
				}
			}
			return s;
		}

		var createId = function (len, data) {
			var d = '';
			for (var i = 0; i < len; i++) {
				d = d + Math.floor(Math.random() * 10);
			}

			var s = d;
			if (data != null && data.length > 0) {
				var str = JSON.stringify(data);
				if (str.indexOf(s) > -1) {
					return createNumber(len, data);
				}
			}
			return s;
		}
	</script>
	<script>

		var pager = {
			areaList: [],
			init: function () {
				areaTable.onload(pager.areaList, true);

				$("#Cancel").click(function () { parent.layer.close(index); });
				$("#Save").click(function () {
					if (!myVerify.check()) return;
					layer.msg("处理中", { icon: 16, time: 0 });
					var spaceJson = JSON.stringify(pager.areaList);

					$("#Save").attr("disabled", true);

					$.post("BathAuthSpace", { spaceJson: spaceJson, ownerNoes: parent.pager.ownerNoList }, function (json) {
						if (json.success) {
							layer.msg("保存成功", { time: 1000 }, function () { window.parent.pager.bindData(window.parent.pager.pageIndex); })
						} else {
							layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
						}
						$("#Save").removeAttr("disabled")
					}, "json");
				});
			}
		}

		var areaTable = {
			onload: function (data, isfirst) {
				data = data || pager.areaList;
				if (!data || data == null || data.length == 0) {
					data = pager.areaList = [{
						LAY_TABLE_INDEX: 0
						, StopSpace_AreaName: "全部区域"
						, StopSpace_AreaNo: '["0"]'
						, StopSpace_Content: ""
						, StopSpace_No: createNumber(3, data)
						, StopSpace_Number: isfirst ? "1" : "0"
						, StopSpace_Type: 0
					}];
				}
				//转换静态表格
				var cols = [[
					{ type: 'numbers' }
					, { field: 'StopSpace_AreaName', title: '可停区域', toolbar: "#areaName" }
					, { field: 'StopSpace_Number', title: '车位数量' }
					, { field: 'StopSpace_Content', title: '车位信息' }
					, { field: 'StopSpace_Btns', title: '操作', toolbar: "#areabtns" }
				]];

				layui.table.render({
					elem: '#table_area',
					cols: cols,
					toolbar: '#toolbarareas',
					defaultToolbar: ["filter"],
					data: data,
					done: function (data) {
						$(".editarea").unbind("click").click(function () {
							var no = $(this).attr("data-no");
							areaTable.openAreaEdit(true, no);
						})

						$(".delarea").unbind("click").click(function () {
							layer.closeAll();
							var no = $(this).attr("data-no");
							pager.areaList.forEach(function (item, i) {
								if (item.StopSpace_No == no) {
									pager.areaList.splice(i, 1);
									return;
								}
							});
							areaTable.onload();
							layer.msg("删除区域,保存后生效.", { time: 1000 });
						})
					}
				});

				//头工具栏事件
				layui.table.on('toolbar(table_area)', function (obj) {
					var checkStatus = layui.table.checkStatus(obj.config.id); //获取选中行状态
					var data = checkStatus.data;  //获取选中行数据
					areaTable.pageIndex = $("#table_area .layui-laypage-curr").text();
					switch (obj.event) {
						case 'Add':
							areaTable.openAreaEdit(false);
							break;
					}
				});
			},
			addOrUpdate: function (item) {
				if (pager.areaList != null && pager.areaList.length > 0) {
					var isedit = false;
					pager.areaList.forEach(function (d, i) {
						if (d.StopSpace_No == item.StopSpace_No) {
							pager.areaList[i] = item;
							isedit = true;
						}
					});
					if (!isedit)
						pager.areaList[pager.areaList.length] = item;
				} else {
					pager.areaList = [item];
				}

				this.onload();
				layer.closeAll();
			},
			openAreaEdit: function (isedit, no) {
				if (isedit) {
					layer.open({
						type: 2,
						title: "编辑车位",
						content: "EditStopSpace?Act=Update&no=" + no,
						area: getIframeArea(["600px", "420px"]),
						maxmin: false
					});
				} else {
					layer.open({
						type: 2,
						title: "新增车位",
						content: "EditStopSpace?Act=Add",
						area: getIframeArea(["600px", "420px"]),
						maxmin: false
					});
				}
			}
		}


	</script>
</body>
</html>
