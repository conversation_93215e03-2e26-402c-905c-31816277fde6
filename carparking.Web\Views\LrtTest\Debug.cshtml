﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>调试23040614</title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <script src="~/Static/plugins/layui/layui.js"></script>
    <script src="~/Static/js/jquery.min.js"></script>
    <script src="~/Static/js/jquery.common.js"></script>
    <style>
        html, body { width: 100%; height: 100%; background-color: #f9f9f9; overflow: hidden; }

        .layui-card { width: 50%; min-height: 100%; float: left; background-color: #f9f9f9; overflow: auto; }

        .card-res { width: 50%; float: right; overflow-y: auto; }

        .card-res::before { content: ""; border: 1px solid #000; position: absolute; width: 0px; height: 100%; }

        .layui-form-label { width: 120px; font-weight: bold; }

        .layui-input-block { margin-left: 150px; }

        .layui-input { color: #300bee; }

        .layui-input[readonly] { background-color: #f5f5f5; }

        .layui-btn[disabled] { background-color: #666 !important; }
    </style>
</head>
<body>
    <div class="layui-card">
        <div class="layui-card-header">Parking Test</div>
        <div class="layui-card-body layui-form" id="form1">
            <div class="layui-form-item">
                <label class="layui-form-label">parkno</label>
                <div class="layui-input-block">
                    <input type="text" id="parkno" name="parkno" placeholder="请输入停车场编码" class="layui-input" value="@ViewBag.ParkNo">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">入场相机编码</label>
                <div class="layui-input-block">
                    @*<input type="text" id="camerano" name="camerano" placeholder="请输入相机编码" class="layui-input" value="@ViewBag.In">*@

                    <select data-placeholder="请选择相机编码" class="form-control chosen-select " id="camerano" name="camerano" lay-search>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">出场相机编码</label>
                <div class="layui-input-block">
                    @*<input type="text" id="camerano_out" name="camerano_out" placeholder="请输入相机编码" class="layui-input" value="@ViewBag.Out">*@
                    <select data-placeholder="请选择相机编码" class="form-control chosen-select " id="camerano_out" name="camerano_out" lay-search>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">车牌号</label>
                <div class="layui-input-block">
                    <input type="text" id="carno" name="carno" placeholder="请输入车牌号" class="layui-input" value="粤ADS865">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">车牌颜色</label>
                <div class="layui-input-block">
                    <input type="text" id="cartype" name="cartype" placeholder="请输入车牌颜色" class="layui-input" value="蓝牌车">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">mode</label>
                <div class="layui-input-block">
                    <select class="layui-select" id="mode" name="mode" lay-search>
                        <option value="1">相机识别</option>
                        <option value="2">扫码</option>
                        <option value="3">ETC</option>
                        <option value="4">刷卡</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">进场时间</label>
                <div class="layui-input-block">
                    <input type="text" id="time" name="time" placeholder="时间" style="font-size: 16px;" class="layui-input" value="@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">出场时间</label>
                <div class="layui-input-block">
                    <input type="text" id="time_out" name="time_out" placeholder="出场时间" style="font-size: 16px;" class="layui-input" value="@DateTime.Now.AddHours(2).ToString("yyyy-MM-dd HH:mm:ss")">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">img</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" id="img" name="img" placeholder="出入场抓拍图"></textarea>
                </div>
            </div>


            <div class="layui-form-item">
                <label class="layui-form-label">orderno</label>
                <div class="layui-input-block">
                    <input type="text" id="orderno" name="orderno" placeholder="停车订单号" class="layui-input" value="">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">orderdetailno</label>
                <div class="layui-input-block">
                    <input type="text" id="orderdetailno" name="orderdetailno" placeholder="停车明细订单号" class="layui-input" value="">
                </div>
            </div>
            @*   <div class="layui-form-item">
            <label class="layui-form-label">account</label>
            <div class="layui-input-block">
            <input type="text" id="account" name="account" placeholder="操作员账号" class="layui-input" value="">
            </div>
            </div>
            <div class="layui-form-item">
            <label class="layui-form-label">name</label>
            <div class="layui-input-block">
            <input type="text" id="name" name="name" placeholder="操作员名称" class="layui-input" value="">
            </div>
            </div>*@
            <div class="layui-form-item">
                <label class="layui-form-label">&nbsp;</label>
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm" id="OnSend" style="max-width:88px"><i class="layui-icon layui-icon-camera"></i><text>相机识别</text></button>&nbsp; &nbsp;
                    <button class="layui-btn layui-btn-sm" id="OnPass" style="max-width:105px"><i class="layui-icon layui-icon-ok"></i><text>入口开闸放行</text></button>&nbsp; &nbsp;
                    <button class="layui-btn layui-btn-sm" id="OnInOrder" style="max-width:105px"><i class="layui-icon layui-icon-ok"></i><text>获取车辆入场订单</text></button>&nbsp; &nbsp;
                    <button class="layui-btn layui-btn-sm" id="OnGetPay" style="max-width:105px"><i class="layui-icon layui-icon-ok"></i><text>获取计费金额</text></button>&nbsp; &nbsp;
                    <button class="layui-btn layui-btn-sm" id="OnPay" style="max-width:105px"><i class="layui-icon layui-icon-ok"></i><text>出口支付放行</text></button>&nbsp; &nbsp;&nbsp; &nbsp;



                    <button class="layui-btn layui-btn-sm" id="OnCheck" style="max-width:125px"><i class="layui-icon layui-icon-ok"></i><text>进出场信息查询</text></button>&nbsp;
                </div>
            </div>
        </div>
    </div>
    <div class="card-res">
        <div class="layui-card-body" style="overflow-y:auto;">
            <div class="layui-form-item">
                <label class="layui-form-label">未加密参数</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:50px;" id="uparam"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">返回结果</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:400px;" id="result"></textarea>
                </div>
            </div>

            <div class="layui-form-item" style="overflow-y:auto;">
                <label class="layui-form-label">计费详情</label>
                <div class="layui-input-block" style="height:auto; min-height:150px; border:1px solid  #e6e6e6;background-color: #fff;overflow-x: auto;">
                    <table class="table table-striped  table-hover" style="width:1700px">
                        <thead>
                            <tr>
                                <th>区域</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>计费金额</th>
                                <th>优惠金额</th>
                                <th>免费分钟</th>
                                <th>上个周期结束时间</th>
                                <th>上个周期累积金额</th>
                                <th>上个周期累计免费分钟</th>
                                <th>当前周期结束时间</th>
                                <th>当前周期累积金额</th>
                                <th>当前周期累计免费分钟</th>
                                <th>超时缴费</th>
                                <th>过期缴费</th>
                            </tr>
                        </thead>
                        <tbody id="data-view-detail"></tbody>
                        <script id="data-tmpl-detail" type="text/x-jquery-tmpl">
                            <tr>
                               <td>${areaname}</td>
                               <td>${starttime}</td>
                               <td>${endtime}</td>
                               <td>${payedamount}</td>
                               <td>${couponamount}</td>
                               <td>${currentfreemin}</td>
                               <td>${preNextcycletime}</td>
                               <td>${preNextCyclePaidFees}</td>
                               <td>${preNextcyclefreemin}</td>
                               <td>${nextcycletime}</td>
                               <td>${NextCyclePaidFees}</td>
                               <td>${nextcyclefreemin}</td>
                               <td>${isovertime}</td>
                               <td>${iscarexpire}</td>
                            </tr>
                        </script>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery.tmpl.min.js"></script>
    <script src="~/Static/js/jquery.md5.js"></script>
    <script type="text/javascript">
        layui.use(['form'], function() {
            pager.init();
            layui.form.render("select");
        });
    </script>
    <script type="text/javascript">

        var pager = {
            order: "",
            payData: null,
            init: function() {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function() {
                $.post("SltCameranoList?r=" + Math.random(), {}, function(json) {
                    if (json.success) {
                        if (json.inList)
                            json.inList.forEach(function(d, i) {
                                var option = '<option value="' + d.Device_No + '">' + d.Device_Name + '</option>';
                                $("#camerano").append(option)
                            });

                        if (json.outList)
                            json.outList.forEach(function(d, i) {
                                var option = '<option value="' + d.Device_No + '">' + d.Device_Name + '</option>';
                                $("#camerano_out").append(option)
                            });
                    } else {
                        layer.msg(json.msg)
                    }
                }, "json");
            },
            bindData: function() {

            },
            bindEvent: function() {
                $("#OnSend").click(function() {
                    if (pager.order == "") {
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定', '取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value, index) {
                            pager.order = $.md5(value);
                            layer.close(index);
                            pager.InOutResult();
                        });
                    } else {
                        pager.InOutResult();
                    }
                });


                $("#OnPass").click(function() {
                    if (pager.order == "") {
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定', '取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value, index) {
                            pager.order = $.md5(value);
                            layer.close(index);
                            pager.PassResult();
                        });
                    } else {
                        pager.PassResult();
                    }
                });

                $("#OnPay").click(function() {
                    if (pager.order == "") {
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定', '取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value, index) {
                            pager.order = $.md5(value);
                            layer.close(index);
                            pager.PayCar();
                        });
                    } else {
                        pager.PayCar();
                    }
                });
                  $("#OnGetPay").click(function() {
                    if (pager.order == "") {
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定', '取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value, index) {
                            pager.order = $.md5(value);
                            layer.close(index);
                            pager.GetPayCar();
                        });
                    } else {
                        pager.GetPayCar();
                    }
                });
                 $("#OnInOrder").click(function() {
                    if (pager.order == "") {
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定', '取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value, index) {
                            pager.order = $.md5(value);
                            layer.close(index);
                            pager.InOrder();
                        });
                    } else {
                        pager.InOrder();
                    }
                });

                $("#OnCheck").click(function() {
                    if (pager.order == "") {
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定', '取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value, index) {
                            pager.order = $.md5(value);
                            layer.close(index);
                            pager.GetInOutCar();
                        });
                    } else {
                        pager.GetInOutCar();
                    }
                });
            },
            InOutResult: function() {
                //获取所有表单元素
                var param = $("#form1").formToJSON(true, function(data) {
                    return data;
                });

                param.img = encodeURIComponent(param.img);
                var jsonModel = JSON.stringify(param);
                $("#uparam").val(jsonModel);

                var order = pager.order;
                pager.order = "";
                layer.msg("正在处理...", { icon: 16, time: 0 });
                $.post("InOutResult", { jsonModel: jsonModel, order: order }, function(json) {
                    layer.closeAll();
                    pager.payData = json.payres;
                    var resultTxt = JSON.stringify(json, undefined, 4);
                    $("#result").val(resultTxt);
                    if (json.passres) {
                        $("#orderno").val(json.passres.parkorderno);
                        $("#orderdetailno").val(json.passres.orderdetailno);
                    }
                    if (resultTxt.indexOf("无权") == -1) {
                        pager.order = order;
                    }
                }, "json");
            },
            PassResult: function() {
                //获取所有表单元素
                var param = $("#form1").formToJSON(true, function(data) {
                    return data;
                });
                param.code = 200;
                param.img = encodeURIComponent(param.img);
                var jsonModel = JSON.stringify(param);
                $("#uparam").val(jsonModel);

                var order = pager.order;
                pager.order = "";
                layer.msg("正在处理...", { icon: 16, time: 0 });
                $.post("PassResult", { jsonModel: jsonModel, order: order }, function(json) {
                    layer.closeAll();
                    var resultTxt = JSON.stringify(json, undefined, 4);
                    $("#result").val(resultTxt);
                    if (resultTxt.indexOf("无权") == -1) {
                        pager.order = order;
                    }
                }, "json");
            },
            PayCar: function() {
                //获取所有表单元素
                var param = $("#form1").formToJSON(true, function(data) {
                    return data;
                });
                param.code = 200;
                param.img = encodeURIComponent(param.img);
                var jsonModel = JSON.stringify(param);
                $("#uparam").val(jsonModel);
                var order = pager.order;
                pager.order = "";
                layer.msg("正在处理...", { icon: 16, time: 0 });
                $.post("PayCar", { carno: $("#carno").val(), camerano: $("#camerano_out").val(), time: $("#time_out").val(), cartype: $("#cartype").val(), order: order }, function(json) {
                    layer.closeAll();
                    var resultTxt = "计费结果：";
                    if (json.success) {
                        var d = json.data;
                        if (d) {
                            var list = d.list;
                            if (list) {
                                delete d["list"];
                                $("#data-view-detail").html("");
                                $('#data-tmpl-detail').tmpl(list).appendTo('#data-view-detail');
                                //var detailTxt="";
                                //var spaceStr="                                                                                                                                                                     ";
                                //$.each(list,function(k,v){
                                //   detailTxt+=((k+1)+"、时间段："+v.starttime+spaceStr+"-"+v.endtime+"，区域编号："+v.areano+"，上个周期结束时间："+v.preNextcycletime+spaceStr);
                                //})
                                //$("#detail").val(JSON.stringify(list, undefined, 4));
                            }


                            if (d.payed == 0) {
                                resultTxt += "无需计费";
                            } else if (d.payed == 1) {
                                resultTxt += "计费成功";
                            } else {
                                resultTxt += "计费失败";
                            }

                            resultTxt += "，订单金额：" + d.orderamount;
                            resultTxt += "，优惠金额：" + d.couponamount;
                            resultTxt += "，储值金额：" + d.chuzhiamount;
                            resultTxt += "，支付金额：" + d.payedamount;
                            resultTxt += "                                                                                                                                                ";
                        }
                    }
                    resultTxt += JSON.stringify(json, undefined, 4);
                    $("#result").val(resultTxt);
                    if (resultTxt.indexOf("无权") == -1) {
                        pager.order = order;
                    }
                }, "json");
            },
            GetPayCar: function() {
                //获取所有表单元素
                var param = $("#form1").formToJSON(true, function(data) {
                    return data;
                });
                param.code = 200;
                param.img = encodeURIComponent(param.img);
                var jsonModel = JSON.stringify(param);
                $("#uparam").val(jsonModel);
                var order = pager.order;
                pager.order = "";
                layer.msg("正在处理...", { icon: 16, time: 0 });
                $.post("GetPayCar", { carno: $("#carno").val(), camerano: $("#camerano_out").val(), time: $("#time_out").val(), cartype: $("#cartype").val(), order: order }, function(json) {
                    layer.closeAll();
                    var resultTxt = "计费结果：";
                    if (json.success) {
                        var d = json.data;
                        if (d) {
                            var list = d.list;
                            if (list) {
                                delete d["list"];
                                $("#data-view-detail").html("");
                                $('#data-tmpl-detail').tmpl(list).appendTo('#data-view-detail');
                                //var detailTxt="";
                                //var spaceStr="                                                                                                                                                                     ";
                                //$.each(list,function(k,v){
                                //   detailTxt+=((k+1)+"、时间段："+v.starttime+spaceStr+"-"+v.endtime+"，区域编号："+v.areano+"，上个周期结束时间："+v.preNextcycletime+spaceStr);
                                //})
                                //$("#detail").val(JSON.stringify(list, undefined, 4));
                            }


                            if (d.payed == 0) {
                                resultTxt += "无需计费";
                            } else if (d.payed == 1) {
                                resultTxt += "计费成功";
                            } else {
                                resultTxt += "计费失败";
                            }

                            resultTxt += "，订单金额：" + d.orderamount;
                            resultTxt += "，优惠金额：" + d.couponamount;
                            resultTxt += "，储值金额：" + d.chuzhiamount;
                            resultTxt += "，支付金额：" + d.payedamount;
                            resultTxt += "                                                                                                                                                ";
                        }
                    }
                    resultTxt += JSON.stringify(json, undefined, 4);
                    $("#result").val(resultTxt);
                    if (resultTxt.indexOf("无权") == -1) {
                        pager.order = order;
                    }
                }, "json");
            },
            GetInOutCar: function() {
                layer.prompt({
                    formType: 0,
                    value: '',
                    title: '请输入车牌号',
                    btn: ['确定', '取消'], //按钮，
                    btnAlign: 'c'
                }, function(value, index) {
                    layer.close(index);
                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    var order = pager.order;
                    pager.order = "";
                    $("#uparam").val(JSON.stringify({ carno: value }));
                    $.post("GetInOutCar", { carno: value, order: order }, function(json) {
                        layer.closeAll();
                        var resultTxt = JSON.stringify(json, undefined, 4);
                        $("#result").val(resultTxt);
                        if (resultTxt.indexOf("无权") == -1) {
                            pager.order = order;
                        }
                        if (json.success) {
                            layer.open({
                                title: "<i class='fa fa-rmb' style='margin-top: 17px;'></i> 进出场详情",
                                type: 2, id: 1,
                                area: getIframeArea(['98%', '98%']),
                                fix: false, //不固定
                                maxmin: false,
                                content: 'Detail?key=' + encodeURIComponent(pager.order) + '&carno=' + encodeURIComponent(value)
                            });
                        }
                    }, "json");
                });
            },
            InOrder: function() {
                layer.prompt({
                    formType: 0,
                    value: '',
                    title: '请输入车牌号',
                    btn: ['确定', '取消'], //按钮，
                    btnAlign: 'c'
                }, function(value, index) {
                    layer.close(index);
                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    var order = pager.order;
                    pager.order = "";
                    $("#uparam").val(JSON.stringify({ carno: value }));
                    $.post("GetInOrder", { carno: value, order: order }, function(json) {
                        layer.closeAll();
                        var resultTxt = JSON.stringify(json, undefined, 4);
                        $("#result").val(resultTxt);
                        if (resultTxt.indexOf("无权") == -1) {
                            pager.order = order;
                        }
                        if (json.success) {
                            $("#orderno").val(order.ParkOrder_No);
                        }
                    }, "json");
                });
            },
        }


        pager.order = '@ViewBag.order';
    </script>
</body>
</html>