﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>导入停车订单</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-card { box-shadow: none; }
        a:hover { text-decoration:none; }
    </style>
</head>
<body>
    <div class="layui-card">
        <div class="layui-card-header">
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label">选择文件</div>
                <div class="layui-col-xs9 edit-ipt-ban">
                    <div class="layui-col-xs8">
                        <input type="text" placeholder="" id="iptFilePath" class="layui-input" readonly="readonly">
                    </div>
                    <div class="layui-col-xs4">
                        <label title="选择" for="inputFile" class="layui-btn">
                            <input type="file" accept=".xlsx" name="file" id="inputFile" class="hide" onchange="chooseFile(this);"><i class="fa fa-folder-open-o"></i> <t class="lan-label" data-lan="Select">选择</t>
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-xs2">&nbsp;</div>
                <div class="layui-col-xs9">
                    <label class="label-desc">温馨提示：若车牌号在场内，不会重复写入和更新</label>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2"><label>&nbsp;</label></div>
                <div class="layui-col-xs9">
                    <a class="layui-btn fa fa-download layui-bg-blue" id="btnDownload"> 下载模板</a>
                    <a class="layui-btn fa fa-check" id="Save"> 提交</a>
                    <a class="layui-btn fa fa-close layui-bg-orange" id="Cancel"> 取消</a>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/ajaxfileupload2.js" asp-append-version="true"></script>
    <script>
        var comElement = null;
        layui.use(['form', 'element'], function () {
            comElement = layui.element;
        });
    </script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        function chooseFile(iptFile) {
            $("#iptFilePath").val(iptFile.value);
        }
        function ajaxFileUpload(ctNo) {
            $("#Import").attr("disabled", true);
            layer.msg("耗时较长,请等待..", { icon: 16, time: 0 });
            $.ajaxFileUpload({
                url: '/InParkCar/Export', //用于文件上传的服务器端请求地址
                type: 'post',
                data: {}, //此参数非常严谨，写错一个引号都不行
                secureuri: false, //一般设置为false
                fileElementId: ['inputFile'], //文件上传空间的id属性
                dataType: 'json', //返回值类型 一般设置为json
                success: function (data) //服务器成功响应处理函数
                {
                    console.log(data);
                    if (data.success) {
                        layer.open({
                            icon: 1,
                            content: "导入成功",
                            btn: ["我知道了"],
                            area: ["300px", "200px"]
                        });
                    } else {
                        layer.open({
                            icon: 0,
                            content: data.msg,
                            btn: ["我知道了"],
                            area: ["300px", "200px"]
                        });
                    }
                },
                complete: function () {
                    $("#iptFilePath").val("");
                    $("#inputFile").val("");
                    $("#btnSubmit").attr("disabled", false);
                },
                error: function (data, status, e) //服务器响应失败处理函数
                {
                    console.log("[" + e.message + "]" + data.responseText)
                    layer.msg(status);
                }
            });
            return false;
        }
        var pager = {
            init: function () {
                this.bindSelect();
                this.bindEvent();
            },
            bindSelect: function () {


            },
            bindEvent: function () {
                $("#Save").click(function () {
                    if ($("#inputFile").val() == "") { return; }
                    ajaxFileUpload();
                });

                $("#Cancel").click(function () { parent.layer.close(index); });

                $("#btnDownload").click(function () {
                    this.href = "../../Data/order_template.xlsx";
                });
            }
        };

        $(function () { pager.init() });
    </script>
</body>
</html>
