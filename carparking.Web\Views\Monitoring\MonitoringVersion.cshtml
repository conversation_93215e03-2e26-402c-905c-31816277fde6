﻿
<!DOCTYPE html>

<html style="width:100%;height:100%;background-color: #3e499d;">
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <style>
        label { padding: 10px 10px 0 0; float: right; }
        .layui-col-xs6 span { line-height: 40px; }
        .edit-label { text-align: inherit; padding-right: 0px; padding-left: 18px; font-size: 15px; margin-top: 6px; }
    </style>
</head>
<body>
    <div class="layui-form" id="verify-form" style="width:100%;height:100%;">
        <div class="layui-col-xs12 edit-label" style="color: #fff !important;">当前软件版本：<span>@Html.Raw(ViewBag.ApiVersion)</span> <span>@Html.Raw(ViewBag.Dog)</span>  <span>@Html.Raw(ViewBag.ApiVersion_FB!=""?"."+ViewBag.ApiVersion_FB:"")</span></div>
        <div class="layui-col-xs12 edit-label" style="color: #fff !important;">加密狗信息：<span>@Html.Raw(ViewBag.DogMsg)</span></div>
        <div class="layui-col-xs12 edit-label" style="color: #fff !important;">@ViewData["SDKTitle"]<span>@ViewData["SDKVersion"]</span></div>
    </div>

</body>
</html>
