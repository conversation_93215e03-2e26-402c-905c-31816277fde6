﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>储值车充值</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/
        .layui-form-select .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #eee; opacity: 1; color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        .changetime { border-color: #60c735; color: #de163b !important; font-weight: 600; }
        .layui-form-select dl dd { white-space: normal; max-width: 550px; }

        .red { color: #de163b; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">系统车位号</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="10" id="Car_OwnerSpace" name="Car_OwnerSpace" value="@ViewBag.Owner_Space" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">绑定车牌</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="10" id="Car_CarNo" name="Car_CarNo" value="@ViewBag.Car_CarNo" disabled />
                </div>
            </div>
            <div class="chuzhi">
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">支付类型</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <select data-placeholder="选择支付类型" class="form-control chosen-input " id="Car_PayType" name="Car_PayType" lay-search>
                            <option value="0">现金支付</option>
                            <option value="1">POS机</option>
                            <option value="2">其他</option>
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">充值金额</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-floatLimit" id="Car_Balance" name="Car_Balance" maxlength="50" value="0.00" />
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">支付金额</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-floatLimit v-null" maxlength="10" id="Car_PayMoney" name="Car_PayMoney" value="0.00" placeholder="请输入支付金额（元）" autocomplete="off" />
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label">&nbsp;</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>确定</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?2.0" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/carnopicker/carnopicker.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function() {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");

            _DATE.bind(layui.laydate, ["Car_BeginTime", "Car_NEndTime"], { type: "date", range: true });

            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramOwner_No = $.getUrlParam("Owner_No");

        var PayFreeType = 0;
        var dt = new Date().Format("yyyy-MM-dd");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var nowDate = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var pager = {
            data: null,
            rules: null,
            init: function() {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function() {

            },
            //数据绑定
            bindData: function() {


            },
            bindEvent: function() {
                $("#Cancel").click(function() { parent.layer.close(index); });
                $("#Save").click(function() {
                    if (!myVerify.check()) return;

                    var paymoney = parseFloat($("#Car_Balance").val());
                    if (paymoney <= 0) {
                        layer.msg("请输入大于0的充值金额", { icon: 0 });
                        return;
                    }
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = { Car_OwnerNo: paramOwner_No };
                    param.Car_Balance = $("#Car_Balance").val();
                    param.Car_PayMoney = $("#Car_PayMoney").val();
                    param.Car_PayType = $("#Car_PayType").val();

                    var msg = "当前系统车位号【<t class='red'>" + $("#Car_OwnerSpace").val() + "</t>】，<br/>";
                    msg += "充值金额：<t class='red'>" + $("#Car_Balance").val() + "</t> 元，<br/>";
                    msg += "支付金额：<t class='red'>" + $("#Car_PayMoney").val() + "</t> 元，<br/>";

                    $("#Save").attr("disabled", true);
                    layer.open({
                        type: 0,
                        title: "充值提示",
                        btn: ["确定", "取消"],
                        content: msg + "确定充值吗?",
                        yes: function(res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $(this).attr("disabled", true);
                            $.getJSON("/Owner/SpaceCharge", { jsonModel: JSON.stringify(param) }, function(json) {
                                if (json.success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function() {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1)window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        },
                        btn2: function() { $("#Save").removeAttr("disabled"); }
                    })
                });
            }
        };
    </script>

</body>
</html>
