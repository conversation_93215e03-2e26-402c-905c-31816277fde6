﻿<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>向导设置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html,
        body { width: 100%; height: 100%; overflow: auto; margin: 0; padding: 0; }

        .layui-tab { margin: 0px !important; }

        .layui-tab-title { user-select: none; }

        .box-table,
        .box-bottom { margin: 0 15px; overflow: auto; }

        .box-bottom { text-align: left; padding: 20px 0; }

        .layui-table,
        .layui-table-view { margin: 0 !important; }

        .layui-tab-content { margin: 0; padding: 0 !important; }

        .layui-table-cell { overflow: visible !important; }

        .tdSelect { width: 100%; max-width: 200px; height: 30px; border: 1px solid #d9d9d9; border-radius: 3px; color: #0094ff; font-size: 13px; }

        .layui-elem-quote { border-left: 0 !important; }

        .layui-nav-item t { color: #000 !important; padding: 0 20px; user-select: none; }

        .layui-this t { color: #5FB878 !important; }
    </style>
</head>

<body class="animated fadeInRight">
    <div class="layui-tab" lay-filter="element">
        <ul class="layui-tab-title layui-hide">
            <li data-step="1" class="layui-this">区域设置</li>
            @* <li data-step="2">岗亭设置</li> *@
            <li data-step="2">车道设置</li>
            <li data-step="3">设备设置</li>
        </ul>
        <div class="layui-tab-content">
            <ul class="layui-nav layui-bg-gray" lay-filter="stephead">
                <li data-index="1" class="layui-nav-item layui-this">
                    <t>1.区域设置</t>
                </li>
             @*    <li data-index="2" class="layui-nav-item">
                    <t>2.岗亭设置</t>
                </li> *@
                <li data-index="2" class="layui-nav-item">
                    <t>2.车道设置</t>
                </li>
                <li data-index="3" class="layui-nav-item">
                    <t>3.设备设置</t>
                </li>
            </ul>
            <!--区域设置-->
            <div class="layui-tab-item layui-show">
                <div class="layui-elem-quote" style="margin:5px 15px;">现场有两个及两个以上的独立外场才需要增加新车场区域.点击单元格可进行编辑.</div>
                <div class="box-table">
                    <table class="layui-table" id="area" lay-filter="area">
                    </table>
                    <script type="text/html" id="toolbar_btns">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="Add" id="Add"><i class="fa fa-plus"></i> <t>新增</t></button>
                        </div>
                    </script>
                </div>
                <div class="box-bottom">
                    <button class="layui-btn layui-btn-sm" id="SaveParkArea">
                        <i class="fa fa-save"></i>
                        <t>&nbsp;保 存&nbsp;</t>
                    </button>
                    <button class="layui-btn layui-btn-sm" id="NextParkArea">
                        <i class="fa fa-arrow-right"></i>
                        <t>下一步</t>
                    </button>
                </div>
            </div>
            <!--岗亭设置-->
       @*      <div class="layui-tab-item">
                <div class="layui-elem-quote" style="margin:5px 15px;">现场有多少台岗亭管理电脑就添加多少个岗亭信息.点击单元格可进行编辑.</div>
                <div class="box-table">
                    <table class="layui-table" id="host" lay-filter="host">
                    </table>
                    <script type="text/html" id="toolbar_host">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="Add" id="Add"><i class="fa fa-plus"></i> <t>新增</t></button>
                        </div>
                    </script>
                </div>
                <div class="box-bottom">
                    <button class="layui-btn layui-btn-sm" id="SaveSentryHost">
                        <i class="fa fa-save"></i>
                        <t>&nbsp;保 存&nbsp;</t>
                    </button>
                    <button class="layui-btn layui-btn-sm" id="PrevSentryHost">
                        <i class="fa fa-arrow-left"></i>
                        <t>上一步</t>
                    </button>
                    <button class="layui-btn layui-btn-sm" id="NextSentryHost">
                        <i class="fa fa-arrow-right"></i>
                        <t>下一步</t>
                    </button>
                </div>
            </div> *@
            <!--车道设置-->
            <div class="layui-tab-item">
                <div class="layui-elem-quote" style="margin:5px 15px;">点击单元格可进行编辑.</div>
                <div class="box-table layui-form">
                    <table class="layui-table" id="passway" lay-filter="passway">
                    </table>
                    <script type="text/html" id="toolbar_passway">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="Add" id="Add"><i class="fa fa-plus"></i> <t>新增</t></button>
                        </div>
                    </script>
                </div>
                <div class="box-bottom">
                    <button class="layui-btn layui-btn-sm" id="SavePassway">
                        <i class="fa fa-save"></i>
                        <t>&nbsp;保 存&nbsp;</t>
                    </button>
                    <button class="layui-btn layui-btn-sm" id="PrevPassway">
                        <i class="fa fa-arrow-left"></i>
                        <t>上一步</t>
                    </button>
                    <button class="layui-btn layui-btn-sm" id="NextPassway">
                        <i class="fa fa-arrow-right"></i>
                        <t>下一步</t>
                    </button>
                </div>
            </div>
            <!--设备设置-->
            <div class="layui-tab-item">
                <div class="layui-elem-quote" style="margin:5px 15px;">点击单元格可进行编辑.</div>
                <div class="box-table layui-form">
                    <table class="layui-table" id="device" lay-filter="device">
                    </table>
                    <script type="text/html" id="toolbar_device">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="Add" id="Add"><i class="fa fa-plus"></i> <t>新增</t></button>
                        </div>
                    </script>
                </div>
                <div class="box-bottom">
                    <button class="layui-btn layui-btn-sm" id="SaveDevice">
                        <i class="fa fa-save"></i>
                        <t>&nbsp;保 存&nbsp;</t>
                    </button>
                    <button class="layui-btn layui-btn-sm" id="PrevDevice">
                        <i class="fa fa-arrow-left"></i>
                        <t>上一步</t>
                    </button>
                </div>
            </div>

        </div>
    </div>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>

        var SysConfigContent = decodeURIComponent('@ViewBag.SysConfigContent');
        var SysConfigDefalutDrive = null;
        try {
            SysConfigDefalutDrive = JSON.parse(SysConfigContent);
        } catch (e) { }

        var onDefaultDrive = function (Device_Category) {
            if (Device_Category == 1 && SysConfigDefalutDrive != null && SysConfigDefalutDrive.SysConfig_CameraDrive != '') {
                return SysConfigDefalutDrive.SysConfig_CameraDrive;
            } else if (Device_Category == 4 && SysConfigDefalutDrive != null && SysConfigDefalutDrive.SysConfig_SelfDrive != '') {
                return SysConfigDefalutDrive.SysConfig_CameraDrive;
            }
            return '';
        }


        layui.use(['element'], function () {
            var element = layui.element;
            element.on('tab(element)', function (data) {
                //console.log(this); //当前Tab标题所在的原始DOM元素
                //console.log(data.index); //得到当前Tab的所在下标
                //console.log(data.elem); //得到当前的Tab大容器
                if (data.index == 0) {
                    pagerArea.bindData(1);
                } else if (data.index == 1) {
                    // pagerHost.bindData(1);
                    pagerPassway.bindData(1);
                } else if (data.index == 2) {
                    pagerDevice.bindData(1);
                } 
            });
        });

        var step = {
            to: function (index) {
                $("li[data-step='" + index + "']").click();

                $('ul[lay-filter="stephead"] li').removeClass("layui-this");
                $('ul[lay-filter="stephead"] li').each(function () {
                    if ($(this).attr('data-index') == index)
                        $(this).addClass("layui-this");
                });
            }
        }

        var createNumber = function (len, data) {
            var d = '';
            for (var i = 0; i < len; i++) {
                d = d + Math.floor(Math.random() * 10);
            }

            var s = (new Date().getTime()) + d;
            if (data != null && data.length > 0) {
                var str = JSON.stringify(data);
                if (str.indexOf(s) > -1) {
                    return createNumber(len, data);
                }
            }
            return s;
        }

        var createId = function (len, data) {
            var d = '';
            for (var i = 0; i < len; i++) {
                d = d + Math.floor(Math.random() * 10);
            }

            var s = d;
            if (data != null && data.length > 0) {
                var str = JSON.stringify(data);
                if (str.indexOf(s) > -1) {
                    return createNumber(len, data);
                }
            }
            return s;
        }


    </script>
    <!--区域表格-->
    <script>
        var pagerArea = {
            tableData: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                //if (pagerArea.tableData.length == 0) {
                //    var n = this.createNewRow();
                //    this.tableData.unshift(n);
                //}
            },
            bindData: function (index) {
                layer.msg("正在查询...", { icon: 16, time: 0 });
                $.post("/FastGuide/GetParkAreaList", {}, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        pagerArea.tableData = json.data;
                    }

                    pagerArea.Refresh(index);
                }, "json");
            },
            bindEvent: function () {
                $("#SaveParkArea").click(function () {
                    //判断车位数量是否为正整数
                    var reg = /^[1-9]\d*$/;
                    var flag = true;
                    var isMaxLegth = true;
                    pagerArea.tableData.forEach((item, index) => {
                        if (!reg.test(item.ParkArea_SpaceNum)) {
                            flag = false;
                        }
                        if (item.ParkArea_SpaceNum.length > 6) {
                            isMaxLegth = false;
                        }
                    });
                    if (!flag) {
                        layer.msg('车位数量只能输入正整数');
                        return;
                    }
                    if (!isMaxLegth) {
                        layer.msg('车位数量长度不得超过6');
                        return;
                    }
                    pagerArea.onSave();
                });

                $("#NextParkArea").click(function () {
                    //判断车位数量是否为正整数
                    var reg = /^[1-9]\d*$/;
                    var flag = true;
                    var isMaxLegth = true;
                    pagerArea.tableData.forEach((item, index) => {
                        if (!reg.test(item.ParkArea_SpaceNum)) {
                            flag = false;
                        }
                        if (item.ParkArea_SpaceNum.length > 6) {
                            isMaxLegth = false;
                        }
                    });
                    if (!flag) {
                        layer.msg('车位数量只能输入正整数');
                        return;
                    }
                    if (!isMaxLegth) {
                        layer.msg('车位数量长度不得超过6');
                        return;
                    }
                    pagerArea.onSave(true);
                });
            },
            Refresh: function (index) {
                curtable.reload({
                    data: pagerArea.tableData
                    , page: { curr: index }
                });
            },
            onSave: function (next) {
                var jsonTable = JSON.stringify(pagerArea.tableData);
                layer.msg("正在保存...", { icon: 16, time: 0 });
                $.post("/FastGuide/SaveParkArea", { jsonModel: jsonTable }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        if (next)
                            step.to(2);
                        else
                            layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                pagerArea.bindData(pagerArea.pageIndex);
                            });

                    } else {
                        layer.msg(json.msg, { icon: 0, time: 1500 });
                    }
                }, "json");
            },
            removeItem: function (no, refresh) {
                pagerArea.tableData.forEach((item, index) => {
                    if (item.ParkArea_No == no) {
                        pagerArea.tableData.splice(index, 1);
                        if (refresh) {
                            pagerArea.Refresh(pagerArea.pageIndex);
                        }
                    }
                });
            },
            createNewRow: function () {
                var no = createNumber(3, pagerArea.tableData);
                var n = { ParkArea_No: no, ParkArea_Name: '新区域', ParkArea_SpaceNum: '500', isNew: true };
                return n;
            },
            Delete: function (no) {
                var item = pagerArea.tableData.find((d, i) => { return d.ParkArea_No == no; });
                if (item == null) return;

                if (item.isNew) {
                    pagerArea.removeItem(no, true);
                } else {
                    layer.open({
                        title: '提示消息',
                        content: "删除成功后刷新表格<br/>若存在未保存的数据则会被刷新掉<br/>确定删除?",
                        btn: ["确定", "取消"],
                        yes: function () {
                            $.post("/FastGuide/DeleteParkArea", { ParkArea_No: no }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg);
                                    pagerArea.removeItem(no, true);
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }
                    });
                }
            },
        }
        var curtable = null;
        layui.use(['table'], function () {
            pagerArea.init();

            var table = layui.table;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'ParkArea_No', title: '区域编号', hide: true }
                , { field: 'ParkArea_Name', edit: 'text', title: '区域名称' }
                , { field: 'ParkArea_SpaceNum', edit: 'text', title: '车位数量' }
                , {
                    field: 'btns', title: '操作', width: 140, templet: function (d) {
                        var h = '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="pagerArea.Delete(\'' + d.ParkArea_No + '\')"><i class="fa fa-trash-o"></i> <t>删除</t></button>';
                        return h;
                    }
                }
            ]];

            curtable = table.render({
                elem: '#area'
                , cols: cols
                , data: []
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: {}
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(area)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pagerArea.pageIndex = $("div[lay-id=area] .layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        var n = pagerArea.createNewRow();
                        pagerArea.tableData.unshift(n);
                        pagerArea.Refresh(pagerArea.pageIndex);
                        break;
                }
            });

            table.on('edit(area)', function (obj) {
                //console.log(obj.value); //得到修改后的值
                //console.log(obj.field); //当前编辑的字段名
                //console.log(obj.data); //所在行的所有相关数据
                //验证数量只能输入数字
                var selector = obj.tr.find('[data-field=' + obj.field + ']');
                var oldtext = $(selector).text();
                if (obj.field == 'ParkArea_SpaceNum') {
                    let reg = /^[1-9]\d*$/;
                    if (!reg.test(obj.value)) {
                        layer.msg('只能输入正整数');
                    } else {
                        if (obj.value.length > 6) {
                            layer.msg('长度不得超过6');
                        }
                    }
                }
                pagerArea.tableData.forEach((item, index) => {
                    if (item.ParkArea_No == obj.data.ParkArea_No) {
                        item[obj.field] = obj.value;
                    }
                });
                console.log(pagerArea.tableData)
            });

            tb_row_checkbox();

            pagerArea.bindData(1);
        });
    </script>

    <!--岗亭表格-->
    <script type="text/x-jquery-tmpl" id="tempSentryHostType">
        <select class="tdSelect" data-no="{{d.SentryHost_No}}" data-key="SentryHost_Category" lay-ignore>
            <option value="1" {{# if (d.SentryHost_Category==1){}} selected {{# } }}>车场岗亭收费软件(CS)</option>
            <option value="2" {{# if (d.SentryHost_Category==2){}} selected {{# } }}>车场岗亭后台服务(BS)</option>
        </select>
    </script>
    <script>
        var pagerHost = {
            tableData: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                var n = this.createNewRow();
                this.tableData.unshift(n);
            },
            bindData: function (index) {
                // layer.msg("正在查询...", { icon: 16, time: 0 });
                // $.post("/FastGuide/GetSentryHostList", {}, function (json) {
                //     layer.closeAll();
                //     if (json.success) {
                //         pagerHost.tableData = json.data;
                //     }

                //     pagerHost.Refresh(index);
                // }, "json");
            },
            bindEvent: function () {
                $("#SaveSentryHost").click(function () {
                    pagerHost.onSave();
                });

                $("#NextSentryHost").click(function () {
                    pagerHost.onSave(true, 3);
                });

                $("#PrevSentryHost").click(function () {
                    pagerHost.onSave(true, 1);
                });
            },
            Refresh: function (index) {
                curhosttable.reload({
                    data: pagerHost.tableData
                    , page: { curr: index }
                });
            },
            onSave: function (next, si) {
                var jsonTable = JSON.stringify(pagerHost.tableData);
                layer.msg("正在保存...", { icon: 16, time: 0 });
                $.post("/FastGuide/SaveSentryHost", { jsonModel: jsonTable }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        if (next)
                            step.to(si);
                        else
                            layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                pagerHost.bindData(pagerHost.pageIndex);
                            });

                    } else {
                        layer.msg(json.msg, { icon: 0, time: 1500 });
                    }
                }, "json");
            },
            removeItem: function (no, refresh) {
                pagerHost.tableData.forEach((item, index) => {
                    if (item.SentryHost_No == no) {
                        pagerHost.tableData.splice(index, 1);
                        if (refresh) {
                            pagerHost.Refresh(pagerHost.pageIndex);
                        }
                    }
                });
            },
            createNewRow: function () {
                var no = createNumber(3, pagerHost.tableData);
                var n = { SentryHost_No: no, SentryHost_Name: '新岗亭', SentryHost_IP: '***************', SentryHost_Port: '33568', SentryHost_Category: 1, isNew: true };
                return n;
            },
            Delete: function (no) {
                var item = pagerHost.tableData.find((d, i) => { return d.SentryHost_No == no; });
                if (item == null) return;

                if (item.isNew) {
                    pagerHost.removeItem(no, true);
                } else {
                    layer.open({
                        title: '提示消息',
                        content: "删除成功后刷新表格<br/>若存在未保存的数据则会被刷新掉<br/>确定删除?",
                        btn: ["确定", "取消"],
                        yes: function () {
                            $.post("/FastGuide/DeleteSentryHost", { SentryHost_No: no }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg);
                                    pagerHost.removeItem(no, true);
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }
                    });
                }
            },
            onSelected: function () {
                $("div[lay-id='host'] .tdSelect").unbind("change").change(function () {
                    var no = $(this).attr("data-no");
                    var key = $(this).attr("data-key");
                    var val = $(this).val();
                    if (key == "SentryHost_Category") {
                        pagerHost.tableData.forEach((item, index) => {
                            if (item.SentryHost_No == no) {
                                item[key] = val;
                            }
                        });
                    }

                    console.log(pagerHost.tableData)
                });
            }
        }
        var curhosttable = null;
        layui.use(['table', 'form'], function () {
            pagerHost.init();

            var table = layui.table;
            var form = layui.form;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'SentryHost_No', title: '岗亭编号', hide: true }
                , { field: 'SentryHost_Name', edit: 'text', title: '岗亭名称' }
                , { field: 'SentryHost_IP', edit: 'text', title: '岗亭IP' }
                , { field: 'SentryHost_Category', title: '主机类型', toolbar: "#tempSentryHostType", hide: true }
                , {
                    field: 'btns', title: '操作', width: 140, templet: function (d) {
                        var h = '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="pagerHost.Delete(\'' + d.SentryHost_No + '\')"><i class="fa fa-trash-o"></i> <t>删除</t></button>';
                        return h;
                    }
                }
            ]];

            curhosttable = table.render({
                elem: '#host'
                , cols: cols
                , data: []
                , toolbar: '#toolbar_host'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: {}
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pagerHost.onSelected();
                }
            });

            //头工具栏事件
            table.on('toolbar(host)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pagerHost.pageIndex = $("div[lay-id=host] .layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        var n = pagerHost.createNewRow();
                        pagerHost.tableData.unshift(n);
                        pagerHost.Refresh(pagerHost.pageIndex);
                        break;
                    case 'Delete':

                        break;
                }
            });

            table.on('edit(host)', function (obj) {
                //console.log(obj.value); //得到修改后的值
                //console.log(obj.field); //当前编辑的字段名
                //console.log(obj.data); //所在行的所有相关数据
                pagerHost.tableData.forEach((item, index) => {
                    if (item.SentryHost_No == obj.data.SentryHost_No) {
                        item[obj.field] = obj.value;
                    }
                });
                console.log(pagerHost.tableData)
            });

            tb_row_checkbox();
        });
    </script>

    <!--车道表格-->
    <script type="text/x-jquery-tmpl" id="tempSltHost">
        <select class="tdSelect" data-no="{{d.Passway_ID}}" data-key="Passway_SentryHostNo" lay-ignore>
            {{# layui.each(pagerPassway.hostData, function(index,item){ }}
            <option {{# if (d.Passway_SentryHostNo==item.SentryHost_No){}} selected {{# } }} value="{{ item.SentryHost_No }}">{{ item.SentryHost_Name }}</option>
            {{# });}}
        </select>
    </script>
    <script type="text/x-jquery-tmpl" id="tempSltArea">
        <select class="tdSelect" data-no="{{d.Passway_ID}}" data-key="PasswayLink_ParkAreaNo" lay-ignore>
            {{# layui.each(pagerPassway.areaData, function(index,item){ }}
            <option {{# if (d.PasswayLink_ParkAreaNo==item.ParkArea_No){}} selected {{# } }} value="{{ item.ParkArea_No }}">{{ item.ParkArea_Name }}</option>
            {{# });}}
        </select>
    </script>
    <script type="text/x-jquery-tmpl" id="tempSltOut">
        <select class="tdSelect" data-no="{{d.Passway_ID}}" data-key="PasswayLink_GateType" lay-ignore>
            <option value="1" {{# if (d.PasswayLink_GateType==1){}} selected {{# } }}>入口</option>
            <option value="0" {{# if (d.PasswayLink_GateType==0){}} selected {{# } }}>出口</option>
        </select>
    </script>
    <script type="text/x-jquery-tmpl" id="tempSltTemp">
        <select class="tdSelect" data-no="{{d.Passway_ID}}" data-key="PolicyPass_PassTempA" lay-ignore>
            <option value="0" {{# if (d.PolicyPass_PassTempA==0){}} selected {{# } }}>禁止通行</option>
            <option value="1" {{# if (d.PolicyPass_PassTempA==1){}} selected {{# } }}>自动放行</option>
            <option value="2" {{# if (d.PolicyPass_PassTempA==2){}} selected {{# } }}>弹框确认</option>
        </select>
    </script>
    <script type="text/x-jquery-tmpl" id="tempSltMonth">
        <select class="tdSelect" data-no="{{d.Passway_ID}}" data-key="PolicyPass_PassMonthA" lay-ignore>
            <option value="0" {{# if (d.PolicyPass_PassMonthA==0){}} selected {{# } }}>禁止通行</option>
            <option value="1" {{# if (d.PolicyPass_PassMonthA==1){}} selected {{# } }}>自动放行</option>
            <option value="2" {{# if (d.PolicyPass_PassMonthA==2){}} selected {{# } }}>弹框确认</option>
        </select>
    </script>
    <script>

        function createPasswayNumber(len, data) {
            var beginNo = '';
            if (data != null && data.length > 0) {
                for (let item of data) {
                    if (!isNaN(item.Passway_No) && (item.Passway_No.length == 1 || item.Passway_No.length == 2)) {
                        beginNo = item.Passway_No;
                        break;
                    }
                }
            }

            if (beginNo == '') {
                var d = '';
                for (var i = 0; i < len; i++) {
                    d = d + Math.floor(Math.random() * 10);
                }

                if (data != null && data.length > 0) {

                    var isexit = false;
                    for (let item of data) {
                        if (item.Passway_No == d) {
                            isexit = true;
                            break;
                        }
                    }

                    if (isexit) {
                        d = createPasswayNumber(len, data);
                    }
                }

                return d;
            } else {
                beginNo = parseInt(beginNo) + 1;
                while (isExitPasswayNo(beginNo, data)) {
                    beginNo = parseInt(beginNo) + 1;
                }
            }

            return beginNo;
        }

        function isExitPasswayNo(beginNo, data) {
            for (let item of data) {
                if (!isNaN(item.Passway_No) && item.Passway_No == beginNo) {
                    return true;
                    break;
                }
            }
            return false;
        }


        var pagerPassway = {
            data: [],
            tableData: [],
            hostData: [],
            areaData: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                var n = this.createNewRow();
                this.tableData.unshift(n);
            },
            bindData: function (index) {
                layer.msg("正在查询...", { icon: 16, time: 0 });
                $.post("/FastGuide/GetPasswayList", {}, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        var passways = json.data.passways;
                        var parkareas = json.data.parkareas;
                        var hosts = json.data.hosts;

                        pagerPassway.data = JSON.parse(JSON.stringify(passways));
                        pagerPassway.tableData = passways;
                        pagerPassway.hostData = hosts;
                        pagerPassway.areaData = parkareas;
                    }

                    pagerPassway.Refresh(index);
                }, "json");
            },
            bindEvent: function () {
                $("#SavePassway").click(function () {
                    pagerPassway.onSave();
                });

                $("#NextPassway").click(function () {
                    pagerPassway.onSave(true, 3);
                });

                $("#PrevPassway").click(function () {
                    pagerPassway.onSave(true, 1);
                });
            },
            Refresh: function (index) {
                curpasswaytable.reload({
                    data: pagerPassway.tableData
                    , page: { curr: index }
                });
            },
            onSave: function (next, si) {
                pagerPassway.tableData.forEach((item, index) => {
                    //item.Passway_SentryHostNo = $("select[data-no='" + item.Passway_ID + "'][data-key='Passway_SentryHostNo']").val() || item.Passway_SentryHostNo;
                    item.PasswayLink_ParkAreaNo = $("select[data-no='" + item.Passway_ID + "'][data-key='PasswayLink_ParkAreaNo']").val() || item.PasswayLink_ParkAreaNo;
                    item.PasswayLink_GateType = $("select[data-no='" + item.Passway_ID + "'][data-key='PasswayLink_GateType']").val() || item.PasswayLink_GateType;
                    item.PolicyPass_PassTempA = $("select[data-no='" + item.Passway_ID + "'][data-key='PolicyPass_PassTempA']").val() || item.PolicyPass_PassTempA;
                    item.PolicyPass_PassMonthA = $("select[data-no='" + item.Passway_ID + "'][data-key='PolicyPass_PassMonthA']").val() || item.PolicyPass_PassMonthA;
                });

                console.log(pagerPassway.tableData)

                var jsonTable = JSON.stringify(pagerPassway.tableData);
                layer.msg("正在保存...", { icon: 16, time: 0 });
                $.post("/FastGuide/SavePassway", { jsonModel: jsonTable }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        if (next)
                            step.to(si);
                        else
                            layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                pagerPassway.bindData(pagerPassway.pageIndex);
                            });

                        parent.global.gotoPage("/BillingRule/Index");
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 1500 });
                    }
                }, "json");
            },
            removeItem: function (no, refresh) {
                pagerPassway.tableData.forEach((item, index) => {
                    if (item.Passway_ID == no) {
                        pagerPassway.tableData.splice(index, 1);
                        if (refresh) {
                            pagerPassway.Refresh(pagerPassway.pageIndex);
                        }
                    }
                });
            },
            createNewRow: function () {
                var no = createPasswayNumber(2, pagerPassway.tableData);
                // var defhost = pagerPassway.hostData.length > 0 ? pagerPassway.hostData[0] : {};
                var defarea = pagerPassway.areaData.length > 0 ? pagerPassway.areaData[0] : {};
                var n = {
                    Passway_ID: no
                    , Passway_No: no
                    , Passway_Name: '新车道'
                    // , Passway_SentryHostNo: defhost.SentryHost_No
                    , PasswayLink_ParkAreaNo: defarea.ParkArea_No
                    , PasswayLink_ParkAreaName: defarea.ParkArea_Name
                    , PasswayLink_GateType: 1
                    , PolicyPass_PassTempA: 1
                    , PolicyPass_PassMonthA: 1
                    , isNew: true
                };
                return n;
            },
            Delete: function (no) {
                var item = pagerPassway.tableData.find((d, i) => { return d.Passway_ID == no; });
                if (item == null) return;

                if (item.isNew) {
                    pagerPassway.removeItem(no, true);
                } else {
                    layer.open({
                        title: '提示消息',
                        content: "删除成功后刷新表格<br/>若存在未保存的数据则会被刷新掉<br/>确定删除?",
                        btn: ["确定", "取消"],
                        yes: function () {
                            $.post("/FastGuide/DeletePassway", { Passway_No: item.Passway_No }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg);
                                    pagerPassway.removeItem(no, true);
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }
                    });
                }
            },
            onSelected: function () {
                $("div[lay-id='passway'] .tdSelect").unbind("change").change(function () {
                    var no = $(this).attr("data-no");
                    var key = $(this).attr("data-key");
                    var val = $(this).val();
                    var text = $(this).find('option:selected').text();
                    if (//key == "Passway_SentryHostNo" ||
                        key == "PasswayLink_ParkAreaNo" ||
                        key == "PasswayLink_GateType" ||
                        key == "PolicyPass_PassTempA" ||
                        key == "PolicyPass_PassMonthA") {
                        pagerPassway.tableData.forEach((item, index) => {
                            if (item.Passway_No == no) {
                                item[key] = val;

                                if (key == "PasswayLink_ParkAreaNo")
                                    item['PasswayLink_ParkAreaName'] = text;
                            }
                        });
                    }
                    console.log(pagerPassway.tableData)
                });
            }
        }
        var curpasswaytable = null;
        layui.use(['table', 'form'], function () {
            pagerPassway.init();

            var table = layui.table;
            var form = layui.form;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Passway_No', edit: 'text', title: '车道编号' }
                , { field: 'Passway_Name', edit: 'text', title: '车道名称' }
                // , { field: 'Passway_SentryHostNo', title: '所属岗亭', templet: "#tempSltHost" }
                , { field: 'Passway_EnterAreaNo', title: '关联区域', templet: "#tempSltArea" }
                , { field: 'Passway_OutAreaNo', title: '出入类型', templet: "#tempSltOut" }
                , { field: 'PolicyPass_PassTempA', title: '临时车A', templet: "#tempSltTemp" }
                , { field: 'PolicyPass_PassMonthA', title: '月租车A', templet: "#tempSltMonth" }
                , {
                    field: 'btns', title: '操作', width: 140, templet: function (d) {
                        var h = '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="pagerPassway.Delete(\'' + d.Passway_ID + '\')"><i class="fa fa-trash-o"></i> <t>删除</t></button>';
                        return h;
                    }
                }
            ]];

            curpasswaytable = table.render({
                elem: '#passway'
                , cols: cols
                , data: []
                , toolbar: '#toolbar_passway'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: {}
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pagerPassway.onSelected();
                }
            });

            //头工具栏事件
            table.on('toolbar(passway)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pagerPassway.pageIndex = $("div[lay-id=host] .layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        var n = pagerPassway.createNewRow();
                        pagerPassway.tableData.unshift(n);
                        pagerPassway.Refresh(pagerPassway.pageIndex);
                        break;
                    case 'Delete':

                        break;
                }
            });

            table.on('edit(passway)', function (obj) {
                var d = pagerPassway.data.find((item, index) => { return item.Passway_ID == obj.data.Passway_ID; });
                if (obj.field == 'Passway_No' && obj.data.Passway_ParkNo != '' && obj.data.Passway_ParkNo != null) {
                    $(this).val(d.Passway_No);
                    pagerPassway.tableData.forEach((item, index) => {
                        if (item.Passway_ID == d.Passway_ID) {
                            item[obj.field] = d.Passway_No;
                        }
                    });
                    console.log(pagerPassway.tableData)
                    layer.msg("已保存的车道不允许修改车道编号", { icon: 0, time: 2000 });
                    return false;
                }
                pagerPassway.tableData.forEach((item, index) => {
                    if (item.Passway_ID == obj.data.Passway_ID) {
                        item[obj.field] = obj.value;
                    }
                });
                console.log(pagerPassway.tableData)
            });
        });
    </script>

    <!--设备表格-->
    <script type="text/x-jquery-tmpl" id="tempDeviceDriveNo">
        <select class="tdSelect" data-no="{{d.Device_ID}}" data-key="Device_DriveNo" lay-ignore>
            {{# layui.each(pagerDevice.driveData, function(index,item){ }}
            <option {{# if (d.Device_DriveNo==item.Drive_No){}} selected {{# } }} value="{{ item.Drive_No }}">{{ item.Drive_Name }}</option>
            {{# });}}
        </select>
    </script>
    <script type="text/x-jquery-tmpl" id="tempDevicePassway">
        <select class="tdSelect" data-no="{{d.Device_ID}}" data-key="Device_PasswayNo" lay-ignore>
            {{# layui.each(pagerDevice.passwayData, function(index,item){ }}
            <option {{# if (d.Device_PasswayNo==item.Passway_No){}} selected {{# } }} value="{{ item.Passway_No }}">{{ item.Passway_Name }}[{{ item.PasswayLink_ParkAreaName }}-{{ (item.PasswayLink_GateType==0?"出口":"入口") }}]</option>
            {{# });}}
        </select>
    </script>
    <script>
        var pagerDevice = {
            tableData: [],
            passwayData: [],
            driveData: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                var n = this.createNewRow();
                this.tableData.unshift(n);
            },
            bindData: function (index) {
                layer.msg("正在查询...", { icon: 16, time: 0 });
                $.post("/FastGuide/GetDeviceList", {}, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        pagerDevice.tableData = json.data.devices;
                        pagerDevice.passwayData = json.data.passways;
                        pagerDevice.driveData = json.data.drives;
                    }

                    pagerDevice.Refresh(index);
                }, "json");
            },
            bindEvent: function () {
                $("#SaveDevice").click(function () {
                    pagerDevice.onSave();
                });

                $("#PrevDevice").click(function () {
                    pagerDevice.onSave(true, 2);
                });
            },
            Refresh: function (index) {
                curdevicetable.reload({
                    data: pagerDevice.tableData
                    , page: { curr: index }
                });
            },
            onSave: function (next, si) {
                pagerDevice.tableData.forEach((item, index) => {
                    item.Device_DriveNo = $("select[data-no='" + item.Device_ID + "'][data-key='Device_DriveNo']").val() || item.Device_DriveNo;
                    item.Device_PasswayNo = $("select[data-no='" + item.Device_ID + "'][data-key='Device_PasswayNo']").val() || item.Device_PasswayNo;
                });
                console.log(pagerDevice.tableData)
                var jsonTable = JSON.stringify(pagerDevice.tableData);
                layer.msg("正在保存...", { icon: 16, time: 0 });
                $.post("/FastGuide/SaveDevice", { jsonModel: jsonTable }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        if (next)
                            step.to(si);
                        else {
                            window.parent.global.gotoPage("BillingRule/Index");
                        }
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 1500 });
                    }
                }, "json");
            },
            removeItem: function (no, refresh) {
                pagerDevice.tableData.forEach((item, index) => {
                    if (item.Device_ID == no) {
                        pagerDevice.tableData.splice(index, 1);
                        if (refresh) {
                            pagerDevice.Refresh(pagerDevice.pageIndex);
                        }
                    }
                });
            },
            createNewRow: function () {
                var id = createId(8, pagerDevice.tableData);
                var no = createNumber(3, pagerDevice.tableData);
                var Drive_No = pagerDevice.driveData.length > 0 ? (onDefaultDrive(1) != '' ? onDefaultDrive(1) : pagerDevice.driveData[0].Drive_No) : '';

                console.log('Drive_No:' + Drive_No)
                var defpassway = pagerDevice.passwayData.length > 0 ? pagerDevice.passwayData[0] : {};
                var n = {
                    Device_ID: id,
                    Device_No: no,
                    Device_Name: '新设备',
                    Device_IP: '***************',
                    Device_DriveNo: Drive_No,
                    Device_PasswayNo: defpassway.Passway_No,
                    Device_ParkNo: null,
                    Device_Category: 1,
                    isNew: true
                };
                return n;
            },
            Delete: function (no) {
                var item = pagerDevice.tableData.find((d, i) => { return d.Device_ID == no; });
                if (item == null) return;

                if (item.isNew) {
                    pagerDevice.removeItem(no, true);
                } else {
                    layer.open({
                        title: '提示消息',
                        content: "删除成功后刷新表格<br/>若存在未保存的数据则会被刷新掉<br/>确定删除?",
                        btn: ["确定", "取消"],
                        yes: function () {
                            $.post("/FastGuide/DeleteDevice", { Device_ID: no }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg);
                                    pagerDevice.removeItem(no, true);
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }
                    });
                }
            },
            onSelected: function () {
                $("div[lay-id='device'] .tdSelect").unbind("change").change(function () {
                    pagerDevice.pageIndex = $("div[lay-id=device]  .layui-laypage-curr").text()
                    var no = $(this).attr("data-no");
                    var key = $(this).attr("data-key");
                    var val = $(this).val();
                    var text = $(this).find('option:selected').text();
                    if (key == "Device_DriveNo" ||
                        key == "Device_PasswayNo") {
                        var isfresh = false;
                        pagerDevice.tableData.forEach((item, index) => {
                            if (item.Device_ID == no) {
                                item[key] = val;

                                if (key == "Device_DriveNo" && item.isNew) {
                                    isfresh = true;
                                    var dri = pagerDevice.driveData.find((drv, drvi) => { return drv.Drive_No == val; });
                                    item.Device_IP = dri.Drive_Ip;
                                }
                            }
                        });
                        if (isfresh) pagerDevice.Refresh(pagerDevice.pageIndex);
                    }
                });
            }
        }
        var curdevicetable = null;
        layui.use(['table', 'form'], function () {
            pagerDevice.init();

            var table = layui.table;
            var form = layui.form;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Device_No', title: '设备编号', hide: true }
                , { field: 'Device_Name', edit: 'text', title: '设备名称' }
                , { field: 'Device_PasswayNo', title: '所属车道', toolbar: "#tempDevicePassway" }
                , { field: 'Device_DriveNo', title: '设备型号', toolbar: "#tempDeviceDriveNo" }
                , { field: 'Device_IP', edit: 'text', title: '设备IP' }
                , {
                    field: 'btns', title: '操作', width: 140, templet: function (d) {
                        var h = '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="pagerDevice.Delete(\'' + d.Device_ID + '\')"><i class="fa fa-trash-o"></i> <t>删除</t></button>';
                        return h;
                    }
                }
            ]];

            curdevicetable = table.render({
                elem: '#device'
                , cols: cols
                , data: []
                , toolbar: '#toolbar_device'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: {}
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pagerDevice.onSelected();
                }
            });

            //头工具栏事件tdSelect
            table.on('toolbar(device)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pagerDevice.pageIndex = $("div[lay-id=device]  .layui-laypage-curr").text()
                switch (obj.event) {
                    case 'Add':
                        var n = pagerDevice.createNewRow();
                        pagerDevice.tableData.unshift(n);
                        pagerDevice.Refresh(pagerDevice.pageIndex);
                        break;
                    case 'Delete':

                        break;
                }
            });

            table.on('edit(device)', function (obj) {
                //console.log(obj.value); //得到修改后的值
                //console.log(obj.field); //当前编辑的字段名
                //console.log(obj.data); //所在行的所有相关数据
                pagerDevice.tableData.forEach((item, index) => {
                    if (item.Device_ID == obj.data.Device_ID) {
                        item[obj.field] = obj.value;
                    }
                });
                console.log(pagerDevice.tableData)
            });

        });
    </script>
</body>

</html>
