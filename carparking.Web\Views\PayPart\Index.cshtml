﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>缴费明细查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 2px 4px; float: left; font-size: 14px; font: normal normal normal 14px/1 FontAwesome; }
        .layui-form-select .layui-input { width: 182px; }
        .layui-btn { line-height: normal !important; padding: 0 12px; }
        .layui-bg-wxgreen { background-color: #04BE02 !important; }
        .layui-bg-alipayblue { background-color: #1678ff !important; }
        .layui-bg-ylblue { background-color: #1678ff !important; }
        .colorRed { color: red; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>缴费明细</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            
                            <div class="layui-inline">
                                <input class="layui-input " name="PayPart_CarNo" id="PayPart_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            
                            <div class="layui-inline">
                                <select data-placeholder="订单类型" class="layui-select" id="PayPart_OrderTypeNo" name="PayPart_OrderTypeNo" lay-search>
                                    <option value="">订单类型</option>
                                    <option value="5901">临停车缴费</option>
                                    <option value="5902">月租车充值</option>
                                    <option value="5919">月租车缴费</option>
                                    <option value="5903">储值车充值</option>
                                    <option value="5905">储值车扣费</option>
                                    @*<option value="5904">商家自助充值</option>
                                    <option value="5905">储值车扣费</option>*@
                                    <option value="5910">车位续期</option>
                                    <option value="0">其他</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayPart_PayedTime0" id="PayPart_PayedTime0" autocomplete="off" placeholder="支付时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayPart_PayedTime1" id="PayPart_PayedTime1" autocomplete="off" placeholder="支付时间止" />
                            </div>
                           
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="PayPart_PayOrderNo" id="PayPart_PayOrderNo" autocomplete="off" placeholder="支付订单号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayPart_ParkOrderNo" id="PayPart_ParkOrderNo" autocomplete="off" placeholder="停车订单号" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付状态" class="layui-select" id="PayPart_Status" name="PayPart_Status" lay-search>
                                    <option value="">支付状态</option>
                                    <option value="0">未支付</option>
                                    <option value="1">支付成功</option>
                                    <option value="2">支付失败</option>
                                    <option value="3">用户取消</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付方式" class="layui-select" id="PayPart_PayTypeCode" name="PayPart_PayTypeCode" lay-search>
                                    <option value="">支付方式</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="layui-select" id="PayPart_CarCardTypeNo" name="PayPart_CarCardTypeNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="layui-select" id="PayPart_CarTypeNo" name="PayPart_CarTypeNo" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select id="PayPart_ParkAreaNo" name="PayPart_ParkAreaNo" lay-search>
                                    <option value="">区域名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="默认数据" class="form-control chosen-select " id="dataType" name="dataType" lay-search>
                                    <option value="0">默认数据</option>
                                    <option value="1">历史数据</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.PayPart.Export){}}<button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplimg">
        <a href="{{d.CarRecog_Img}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看"><i class="layui-icon layui-icon-picture"></i>预览</a>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.PayPart_Status==0){}}
        <span class="layui-badge layui-bg-orange">未支付</span>
        {{# }else if(d.PayPart_Status==1){ }}
        <span class="layui-badge layui-bg-green">支付成功</span>
        {{# }else if(d.PayPart_Status==2){ }}
        <span class="layui-badge layui-bg-red">支付失败</span>
        {{# }else if(d.PayPart_Status==3){ }}
        <span class="layui-badge layui-bg-cyan">用户取消</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplordertype">
            {{# if(d.PayPart_OrderTypeNo==5902){}}
            <span class="layui-badge layui-bg-blue">月租车充值</span>
            {{# }else if(d.PayPart_OrderTypeNo==5901){ }}
            <span class="layui-badge layui-bg-green">临停车缴费</span>
            {{# }else if(d.PayPart_OrderTypeNo==5903){ }}
            <span class="layui-badge layui-bg-cyan">储值车充值</span>
            {{# }else if(d.PayPart_OrderTypeNo==5904){ }}
            <span class="layui-badge layui-bg-orange">商家自助充值</span>
            {{# }else if(d.PayPart_OrderTypeNo==5905){ }}
            <span class="layui-badge layui-bg-black">储值车扣费</span>
            {{# }else if(d.PayPart_OrderTypeNo==5910){ }}
            <span class="layui-badge layui-bg-green">车位续期</span>
            {{# }else if(d.PayPart_OrderTypeNo==5919){ }}
        <span class="layui-badge layui-bg-blue">月租车缴费</span>
            {{# }else{ }}
            <span class="layui-badge layui-bg-gray">其他</span>
            {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpaytypecode">
        {{# if(d.PayPart_PayTypeCode==79001){}}
        <span class="layui-badge layui-bg-red">线下现金支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79002){ }}
        <span class="layui-badge layui-bg-red">平台现金支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79003){ }}
        <span class="layui-badge layui-bg-wxgreen">微信支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79010){ }}
        <span class="layui-badge layui-bg-wxgreen">线下微信</span>
        {{# }else if(d.PayPart_PayTypeCode==79013){ }}
        <span class="layui-badge layui-bg-wxgreen">微信无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79007){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79011){ }}
        <span class="layui-badge layui-bg-alipayblue">线下支付宝</span>
        {{# }else if(d.PayPart_PayTypeCode==79012){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79004){ }}
        <span class="layui-badge layui-bg-cyan">Android端支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79006){ }}
        <span class="layui-badge layui-bg-cyan">终端设备支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79009){ }}
        <span class="layui-badge layui-bg-cyan">第三方支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79014){ }}
        <span class="layui-badge layui-bg-ylblue">建行支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79015){ }}
        <span class="layui-badge layui-bg-ylblue">招行一网通支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79016){ }}
        <span class="layui-badge layui-bg-ylblue">银联无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79017){ }}
        <span class="layui-badge layui-bg-ylblue">建行无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79018){ }}
        <span class="layui-badge layui-bg-ylblue">威富通聚合支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79019){ }}
        <span class="layui-badge layui-bg-ylblue">招行无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79020){ }}
        <span class="layui-badge layui-bg-ylblue">工行无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79021){ }}
        <span class="layui-badge layui-bg-ylblue">工行支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79022){ }}
        <span class="layui-badge layui-bg-ylblue">农行无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79023){ }}
        <span class="layui-badge layui-bg-ylblue">农行支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79024){ }}
        <span class="layui-badge layui-bg-ylblue">ETC支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79025){ }}
        <span class="layui-badge layui-bg-ylblue">中行支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79026){ }}
        <span class="layui-badge layui-bg-ylblue">中行无感支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79027){ }}
        <span class="layui-badge layui-bg-ylblue">乐聚合支付</span>
        {{# }else if(d.PayPart_PayTypeCode==79039){ }}
        <span class="layui-badge layui-bg-ylblue">随行付</span>
        {{# }else if(d.PayPart_PayTypeCode==79028){ }}
        <span class="layui-badge layui-bg-ylblue">银联商务</span>
         {{# }else if(d.PayPart_PayTypeCode==79029){ }}
        <span class="layui-badge layui-bg-ylblue">充电抵扣</span>
        {{# }else if(d.PayPart_PayTypeCode==80002){ }}
        <span class="layui-badge layui-bg-ylblue">自助缴费</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplname">
        {{# if (d.PayPart_OrderTypeNo==5910){}}
        <span>{{d.PayPart_OwnerName}}</span>
        {{# }else{ }}
        <span>{{d.PayPart_CarNo}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplDiscountMoney">
        {{# if (d.PayPart_CouponMoney==null){}}
        0
        {{# }else if(d.PayPart_CouponMoney<0){ }}
        <t class="colorRed">{{d.PayPart_CouponMoney}}</t>
        {{# }else { }}
        {{d.PayPart_CouponMoney}}
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        topBar.init();

        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");

            searchFile.bindData(2);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            pager.conditionParam = conditionParam;
            pager.dataType = $("#dataType").val();

            var cols = [[
                { type: 'radio' }
                , { field: 'PayPart_ID', title: 'ID', hide: true }
                , { field: 'PayPart_No', title: '支付明细号', hide: true }
                , { field: 'PayPart_PayOrderNo', title: '支付订单号', hide: true }
                , { field: 'PayPart_ParkOrderNo', title: '停车订单号', hide: true }
                , { field: 'PayPart_CarNo', title: '车牌号', width: 110 }
                , { field: 'PayPart_OrderTypeNo', title: '订单类型', toolbar: "#tmplordertype" }
                , { field: 'PayPart_CarCardTypeName', title: '车牌类型' }
                , { field: 'PayPart_CarTypeName', title: '车牌颜色'}
                , { field: 'PayPart_ParkAreaName', title: '区域名称' }
                , { field: 'PayPart_OrderMoney', title: '应收金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayPart_OrderMoney); }, sort: true }
                , { field: 'PayPart_PayedMoney', title: '实收金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayPart_PayedMoney); }, sort: true }
                , { field: 'PayPart_CouponMoney', title: '优惠金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayPart_CouponMoney); }, sort: true }
                , { field: 'PayPart_StoredMoney', title: '储值金额抵扣', totalRow: true, templet: function (d) { return ToFixed2(d.PayPart_StoredMoney); }, sort: true }
                , { field: 'PayPart_Status', title: '支付状态', toolbar: "#tmplstatus" }
                , { field: 'PayPart_EditTime', title: '支付时间' }
                , { field: 'PayPart_PayTypeCode', title: '支付方式', toolbar: "#tmplpaytypecode" }
                , { field: 'PayPart_TimeCount', title: '停车时长', templet: function (d) { return _DATE.getZhTimesbyMin(Math.ceil(d.PayPart_TimeCount || 0)); } }
                , { field: 'PayPart_Desc', title: '备注', }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/PayPart/GetPayPartList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    pager.dataCount = data.count;
                    tb_page_set(data);
                    if (data.code == 0) {
                        //追加总计行，需要查询时在msg中返回总计数据
                        var total = JSON.parse(data.msg);
                        if (total) {
                            var tr = $(".layui-table-total table tbody").find("tr").first();
                            tr.find('td[data-key="1-0-0"] div').text("合计");
                            $(".layui-table-total table tbody").append('<tr>' + tr.html() + '</tr>');
                            tr = $(".layui-table-total table tbody").find("tr").last();
                            tr.find('td[data-key="1-0-0"] div').text("总计").css({ "color": "red" });
                            tr.find('td[data-field="PayPart_OrderMoney"] div').text(total.PayPart_OrderMoney.toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayPart_PayedMoney"] div').text(total.PayPart_PayedMoney.toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayPart_CouponMoney"] div').text(total.PayPart_CouponMoney.toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayPart_StoredMoney"] div').text(total.PayPart_StoredMoney.toFixed(2)).css({ "color": "red" });
                        }
                    }
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var orderno = data[0].PayPart_No;
                        pager.dataTime = data[0].PayPart_AddTime;
                        pager.dataType = $("#dataType").val();
                        layer.open({
                            title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 支付详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'Detail?PayPart_No=' + encodeURIComponent(orderno)
                        });
                        break;
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                        if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                            if (conditionParam.PayPart_PayedTime0 == null) { layer.msg("请选择支付时间起", { icon: 0 }); return; }
                            if (conditionParam.PayPart_PayedTime1 == null) { layer.msg("请选择支付时间止", { icon: 0 }); return; }
                            if (_DATE.diffDay(new Date(conditionParam.PayPart_PayedTime0), new Date(conditionParam.PayPart_PayedTime1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                        }
                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/PayPart/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                };
            });

              //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            sortField: null,
            orderField: null,
            dataField: null,
            pageIndex: 1,
            dataType: 0,
            conditionParam: null,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PayPart_PayedTime0", "PayPart_PayedTime1"], { type: "datetime", range: true });

                $.post("SltCarCardTypeList2", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#PayPart_CarCardTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#PayPart_CarTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltPayTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            if (item.PayType_Enable == 1) {
                                var option = '<option value="' + item.PayType_No + '">' + item.PayType_Name + '</option>';
                                $("#PayPart_PayTypeCode").append(option);
                            }
                        });
                    }
                }, "json");

                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            var option = '<option value="' + item.ParkArea_No + '">' + item.ParkArea_Name + '</option>';
                            $("#PayPart_ParkAreaNo").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                pager.dataType = $("#dataType").val();
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/PayPart/GetPayPartList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "dataType" && data.value == "1") {
                        var layerDataType = layer.open({
                            id: 2,
                            type: 0,
                            title: "查询数据须知",
                            btn: ["知道了"],
                            content: "默认查询范围：当您未指定时间条件时，系统将自动查询当前年份的全部数据。<br/>跨年查询限制：若选择历史数据，查询时间范围需在同一年内。<br/>例如，若开始时间设定为2024年，则仅能查询2024年的相关数据，无法跨年查询2023年或2025年的数据。",
                            yes: function (res) {
                                layer.close(layerDataType)
                            },
                            btn2: function () { }
                        })
                    }
                })
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });

                s_carno_picker.init("PayPart_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "PayPart_CarNo") {
                        $("#PayPart_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }

    </script>
</body>
</html>
