<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>车牌特殊处理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            overflow: auto;
        }

        .tip-hint {
            border-radius: 4px;
            font-size: 14px;
            background-color: #ffe0e0; /* 计算出与#ff5722相关的背景颜色 */
            border: 1px solid #ff8a3d; /* 计算出与#ff5722相关的边框颜色 */
            color: #ff5722; /* 修改文字颜色为#ff5722 */
            margin-left: 15px;
            display: inline-block;
            vertical-align: middle;
            white-space: nowrap; /* 添加此行以防止文字换行 */
            height: 30px; /* 设置高度为30 */
            padding: 0px 10px;
        }

        .layui-tab {
            margin: 0;
            background: #fff;
            height: 100%;
            position: relative;
        }

        .layui-tab-title {
            padding-left: 2rem;
            padding-top: 15px;
        }

        .layui-tab-title li.layui-this {
            color: #336afc;
            font-weight: bold;
        }

        .layui-tab-title li {
            padding-left: 2rem;
        }

        .layui-tab-title li::before {
            content: "";
            position: absolute;
            padding: .5rem;
            left: .8rem;
            top: .75rem;
            background-size: 100% 100%;
        }

        .layui-tab-title li.type1::before {
            background-image: url('../../Static/img/icon/icon_p_card.svg');
        }

        .layui-tab-title li.type2::before {
            background-image: url('../../Static/img/icon/icon_type.svg');
        }

        .layui-tab-title li.layui-this.type1::before {
            background-image: url('../../Static/img/icon/icon_p_card1.svg');
        }

        .layui-tab-title li.layui-this.type2::before {
            background-image: url('../../Static/img/icon/icon_type1.svg');
        }

        .layui-tab-content {
            padding: 0;
            position: absolute;
            bottom: 0;
            top: 60px;
            left: 0;
            right: 0;
        }

        .layui-select-title input {
            color: #0094ff;
        }

        .layui-inline .layui-form-select .layui-input {
            width: 182px;
        }

        .layui-tab-item.full {
            width: 100%;
            height: 100%;
        }

        .layui-tab-item.full iframe {
            width: 100%;
            height: 100%;
            border: 0;
        }

        .rulebox .layui-row {
            margin-top: 10px;
        }

        .rulebox label {
            float: left;
            padding: 9px 5px 0;
        }

        .layui-input.small {
            width: 70px;
            float: left;
        }

        .select-small {
            float: left;
            margin-right: 5px;
        }


        .layui-form-select .layui-input {
            width: 182px;
        }

        .layui-btn-container {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
    </style>
</head>

<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>车牌特殊处理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input " name="SpecialPlate_CarNo" id="SpecialPlate_CarNo"
                                    autocomplete="off" placeholder="车牌号码" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="处理方式" class="form-control chosen-select "
                                    id="SpecialPlate_Type" name="SpecialPlate_Type" lay-search>
                                    <option value="">处理方式</option>
                                    <option value="1">按指定车牌处理</option>
                                    <option value="2">车牌+车牌颜色匹配</option>
                                    <option value="3">全字母车牌不处理</option>
                                    <option value="4">车牌完全一样不处理</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="启用状态" class="form-control chosen-select "
                                    id="SpecialPlate_Enable" name="SpecialPlate_Enable" lay-search>
                                    <option value="">启用状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="BtnSearch"><i
                                        class="layui-icon layui-icon-search inbtn"></i>
                                    <t>搜索</t>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <div style="display: flex; ">
                                    {{# if(Power.SpecialPlate.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                    {{# if(Power.SpecialPlate.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                    {{# if(Power.SpecialPlate.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                                    {{# if(Power.SpecialPlate.Enable){}} <button class="layui-btn layui-btn-sm" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>启用</t></button>{{# } }}
                                    {{# if(Power.SpecialPlate.Disable){}}<button class="layui-btn layui-btn-sm" lay-event="Disable"><i class="fa fa-ban"></i><t>禁用</t></button>{{# } }}
                                    <div class="tip-hint">
                                        温馨提示：车牌特殊处理规则只对相机识别结果处理生效。
                                    </div>
                                </div>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?t=@DateTime.Now.Ticks"
        asp-append-version="true"></script>
    <script>

        //添加车牌号码选择器
        s_carno_picker.init("SpecialPlate_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "SpecialPlate_CarNo") {
                $("#SpecialPlate_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;
            var form = layui.form;
            layuiForm = form;
            var $ = layui.jquery;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'SpecialPlate_No', title: '特殊处理编码', hide: true }
                , {
                    field: 'SpecialPlate_CarNo', width: 160, title: '车牌号码', templet: function (d) {
                        if (d.SpecialPlate_Type == 3 || d.SpecialPlate_Type == 4) {
                            return '';
                        }
                        return d.SpecialPlate_CarNo;
                    }
                }
                , {
                    field: 'SpecialPlate_Type', width: 200, title: '处理类型', templet: function (d) {
                        var types = {
                            1: '<span class="layui-badge layui-bg-blue">按指定车牌处理</span>',
                            2: '<span class="layui-badge layui-bg-green">车牌+车牌颜色完全匹配</span>',
                            3: '<span class="layui-badge layui-bg-orange">全字母车牌不处理</span>',
                            4: '<span class="layui-badge layui-bg-red">车牌完全一样不处理</span>'
                        };
                        return types[d.SpecialPlate_Type] || '';
                    }
                }
                , {
                    field: 'SpecialPlate_Enable', width: 140, title: '启用状态', templet: function (d) {
                        return d.SpecialPlate_Enable == 1 ?
                            '<span class="layui-badge layui-bg-green">启用</span>' :
                            '<span class="layui-badge layui-bg-red">禁用</span>';
                    }
                }
                , {
                    field: 'SpecialPlate_AdditionalData', title: '附加数据', templet: function (d) {
                        if (d.SpecialPlate_Type == 2) {
                            var colors = {
                                1: '<span class="layui-badge" style="background-color: #0000FF; color: #FFFFFF; border-radius: 2px; display: inline-block;">蓝色车牌</span>',
                                2: '<span class="layui-badge" style="background-color: #FFFF00; color: #000000; border: 1px solid #000000;border-radius: 2px; display: inline-block;">黄色车牌</span>',
                                3: '<span class="layui-badge" style="background-color: #FFFFFF; color: #000000; border: 1px solid #000000;border-radius: 2px; display: inline-block;">白色车牌</span>',
                                4: '<span class="layui-badge" style="background-color: #000000; color: #FFFFFF; border-radius: 2px; display: inline-block;">黑色车牌</span>',
                                5: '<span class="layui-badge" style="background-color: #00FF00; color: #000000; border: 1px solid #000000;border-radius: 2px; display: inline-block;">绿色车牌</span>',
                                6: '<span class="layui-badge" style="background-color: #9ACD32; color: #000000; border: 1px solid #000000;border-radius: 2px; display: inline-block;">黄绿色车牌</span>'
                            };
                            return '车牌颜色: ' + (colors[d.SpecialPlate_PlateColor] || '未知');
                        } else if (d.SpecialPlate_Type == 1) {
                            return '结果包含: ' + '<span class="layui-badge" style="background-color: #1e9fff; color: #ffffff; border-radius: 2px; display: inline-block;">' + (d.SpecialPlate_Recognition || '') + '</span>';
                        } else {
                            return '';
                        }
                    }
                }
                , { field: 'SpecialPlate_Remark', title: '备注' }
                , { field: 'SpecialPlate_AddTime', title: '添加时间', templet: function (d) { return d.SpecialPlate_AddTime ? layui.util.toDateString(d.SpecialPlate_AddTime, 'yyyy-MM-dd HH:mm:ss') : ""; } }
            ]];
            cols = tb_page_cols(cols);
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/SpecialPlate/GetSpecialPlateList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id);
                var data = checkStatus.data;
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);

                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2,
                            title: '新增特殊处理',
                            content: 'Edit?Act=Add',
                            area: getIframeArea(['650px', '520px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2,
                            title: '编辑特殊处理',
                            content: 'Edit?Act=Update&SpecialPlate_No=' + data[0].SpecialPlate_No,
                            area: getIframeArea(['650px', '520px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.ajax({
                                    url: "Delete",
                                    type: "POST",
                                    data: { SpecialPlate_No: data[0].SpecialPlate_No },
                                    dataType: "json",
                                    success: function (json) {
                                        if (json.success)
                                            layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                        else
                                            layer.msg(json.msg, { icon: 0, time: 1500 });
                                    }
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定启用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.ajax({
                                    url: "EnableSpecialPlate",
                                    type: "POST",
                                    data: { SpecialPlate_No: data[0].SpecialPlate_No },
                                    dataType: "json",
                                    success: function (json) {
                                        if (json.success)
                                            layer.msg("启用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                        else
                                            layer.msg(json.msg, { icon: 0, time: 1500 });
                                    }
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定禁用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.ajax({
                                    url: "DisableSpecialPlate",
                                    type: "POST",
                                    data: { SpecialPlate_No: data[0].SpecialPlate_No },
                                    dataType: "json",
                                    success: function (json) {
                                        if (json.success)
                                            layer.msg("禁用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                        else
                                            layer.msg(json.msg, { icon: 0, time: 1500 });
                                    }
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/SpecialPlate/GetSpecialPlateList'
                    , where: { conditionParam: JSON.stringify(conditionParam) }
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>

</html>
