﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>YM01视频监控</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/js/jquery.min.js"></script>
    <!-- 使用JessibucaPro播放器 -->
    <script src="~/Static/flveee/decoder-pro.js?v=@(new Random().Next())"></script>
    <script src="~/Static/flveee/flveee.js"></script>
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
        }

        .video-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        #player {
            width: 100%;
            height: 100%;
        }

        .stream-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: #fff;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 4px;
            z-index: 1000;
        }

        .countdown {
            color: #ff9800;
            font-weight: bold;
        }

        .stream-warning {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #ff9800;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 1000;
            display: none;
        }
    </style>
</head>

<body>
    <div class="video-container">
        <div id="player"></div>
        <div class="stream-info">
            视频将在 <span class="countdown">5:00</span> 后自动断开
        </div>
        <div class="stream-warning">
            视频即将在 <span class="final-countdown">30</span> 秒后断开
        </div>
    </div>

    <script type="text/javascript">
        $(function () {
            var player = null;
            var deviceNo = '@ViewBag.DeviceNo';
            var countdownInterval;
            var warningTimeout;
            var disconnectTimeout;
            var countdownStarted = false;

            function startCountdown(duration) {
                if (countdownStarted) return;
                countdownStarted = true;
                
                var timer = duration;
                var minutes, seconds;

                clearInterval(countdownInterval);
                countdownInterval = setInterval(function () {
                    minutes = parseInt(timer / 60, 10);
                    seconds = parseInt(timer % 60, 10);

                    minutes = minutes < 10 ? "0" + minutes : minutes;
                    seconds = seconds < 10 ? "0" + seconds : seconds;

                    $('.countdown').text(minutes + ":" + seconds);

                    if (--timer < 0) {
                        clearInterval(countdownInterval);
                        // 倒计时结束时执行断流和关闭窗口
                        if (player) {
                            player.destroy();
                            // 调用断流接口
                            $.ajax({
                                url: '/Device/StopVizCloudStream',
                                type: 'POST',
                                data: { deviceNo: deviceNo },
                                async: false,
                                complete: function() {
                                    var index = parent.layer.getFrameIndex(window.name);
                                    parent.layer.close(index);
                                }
                            });
                        } else {
                            var index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        }
                    }

                    // 当剩余30秒时显示警告
                    if (timer === 30) {
                        $('.stream-info').fadeOut(); // 隐藏普通倒计时
                        $('.stream-warning').fadeIn(); // 显示警告倒计时
                        startFinalCountdown(30);
                    }
                }, 1000);
            }

            function startFinalCountdown(duration) {
                var timer = duration;
                var finalCountdownInterval = setInterval(function () {
                    $('.final-countdown').text(timer);
                    if (--timer < 0) {
                        clearInterval(finalCountdownInterval);
                        $('.stream-warning').fadeOut();
                        $('.stream-info').fadeIn(); // 如果警告倒计时结束但还未到5分钟，显示回普通倒计时
                    }
                }, 1000);
            }

            function initPlayer(url) {
                if (player) {
                    player.destroy();
                    player = null;
                }

                var showOperateBtns = false;
                player = new JessibucaPro({
                    container: document.getElementById('player'),
                    decoder: '/Static/flveee/decoder-pro.js',
                    videoBuffer: 0.2,
                    isResize: false,
                    text: "",
                    loadingText: "加载中",
                    debug: true,
                    debugLevel: '',
                    isMulti: true,
                    useMSE: true,
                    decoderErrorAutoWasm: false,
                    useSIMD: true,
                    useWCS: true,
                    useMThreading: true,
                    hasAudio: false,
                    useVideoRender: true,
                    controlAutoHide: true,
                    showBandwidth: showOperateBtns,
                    showPerformance: showOperateBtns,
                    isFlv: true,
                    heartTimeoutReplayTimes: -1,
                    loadingTimeoutReplayTimes: -1,
                    operateBtns: {
                        fullscreen: showOperateBtns,
                        screenshot: showOperateBtns,
                        play: showOperateBtns,
                        audio: showOperateBtns,
                    },
                    watermarkConfig: {
                        text: {
                            content: ''
                        },
                        right: 10,
                        top: 10
                    },
                    demuxUseWorker: true,
                    mseDecoderUseWorker: true,
                    websocketOpenTimeout: 5,
                    loadingTimeout: 10,
                    streamErrorReplay: true,
                    streamEndReplay: true,
                });

                player.on(JessibucaPro.EVENTS.playFailedAndPaused, function (err) {
                    console.error('Player Error:', err);
                    player.showErrorMessageTips('视频加载失败,请检查设备状态');
                });

                player.on(JessibucaPro.EVENTS.start, function () {
                    if (!countdownStarted) {
                        startCountdown(5 * 60);
                    }
                });

                player.on(JessibucaPro.EVENTS.crashLog, function (log) {
                    //console.error('crashLog', log);
                });

                player.on('ptz', function (arrow) {
                    //console.log('ptz', arrow);
                });

                player.on('streamQualityChange', function (value) {
                    //console.log('streamQualityChange', value);
                });

                player.on('timeUpdate', function (value) {
                    //console.log('timeUpdate', value);
                });

                player.on('stats', function (stats) {
                    //console.log('stats', stats);
                });

                setTimeout(function() {
                    player && player.play(url).then(function() {
                        console.log("成功开始播放视频");
                    }).catch(function(error) {
                        console.error("播放视频失败:", error);
                    });
                }, 0);
            }

            // 获取视频流地址
            $.ajax({
                url: '/Device/GetVizCloudStreamUrl',
                type: 'POST',
                data: { deviceNo: deviceNo },
                success: function (res) {
                    if (res.success && res.data) {
                        initPlayer(res.data);
                    } else {
                        if (!player) {
                            var showOperateBtns = false;
                            player = new JessibucaPro({
                                container: document.getElementById('player'),
                                decoder: '/Static/flveee/decoder-pro.js',
                                videoBuffer: 0.2,
                                isResize: false,
                                text: "",
                                loadingText: "加载中",
                                debug: true,
                                debugLevel: '',
                                isMulti: true,
                                useMSE: true,
                                decoderErrorAutoWasm: false,
                                useSIMD: true,
                                useWCS: true,
                                useMThreading: true,
                                hasAudio: false,
                                useVideoRender: true,
                            });
                        }
                        player.showErrorMessageTips(res.msg || '获取视频流失败');
                        $('.stream-info').hide();
                    }
                },
                error: function () {
                    if (!player) {
                        var showOperateBtns = false;
                        player = new JessibucaPro({
                            container: document.getElementById('player'),
                            decoder: '/Static/flveee/decoder-pro.js',
                            videoBuffer: 0.2,
                            isResize: false,
                            text: "",
                            loadingText: "加载中",
                            debug: true,
                            debugLevel: '',
                            isMulti: true,
                            useMSE: true,
                            decoderErrorAutoWasm: false,
                            useSIMD: true,
                            useWCS: true,
                            useMThreading: true,
                            hasAudio: false,
                            useVideoRender: true,
                        });
                    }
                    player.showErrorMessageTips('网络请求失败');
                    $('.stream-info').hide();
                }
            });

            // 页面关闭时清理资源
            window.onbeforeunload = function () {
                if (player) {
                    player.destroy();
                    clearInterval(countdownInterval);
                    countdownStarted = false;

                    // 同步调用断流接口
                    $.ajax({
                        url: '/Device/StopVizCloudStream',
                        type: 'POST',
                        data: { deviceNo: deviceNo },
                        async: false
                    });
                }
            };
        });
    </script>
</body>

</html>
