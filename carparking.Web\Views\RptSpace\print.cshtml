﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车位续期统计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min.css" rel="stylesheet" />
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/plugins/bootstrap-table/bootstrap-table.min.css?v=12" rel="stylesheet" />
    <style>
        .table thead > tr > th {
            text-align: center;
            border-bottom: 1.5px solid #dddddd;
            padding: 0px;
            margin: 0px;
            height: 36px;
        }

        td {
            padding: 0px;
            margin: 0px;
        }

        body {
            max-width: 1200px;
            font-size: 12px;
        }
    </style>
</head>
<body>

    <div class="wrapper wrapper-content col-lg-10 col-md-11 col-sm-12 table-center" style="padding: 0px 20px 0px 20px;">
        <div style="line-height:70px;height:70px;margin:0px;padding:0px;">
            <div style="font-size:16px;font-weight:bolder;height:33px;line-height:33px;">车位续期统计</div>
            <div class="print-time">
                <div class="left" style="line-height:37px">打印时间：<span id="printTime"></span></div>
                <div class="right" style="line-height:37px">统计日期：<span id="printDate"></span></div>
            </div>
            <div style="clear:both;"></div>
        </div>
        <table class="table" style="border-collapse: inherit;border: 1.5px #dddddd solid;"></table>
    </div>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/bootstrap-table/bootstrap-table.min.js" asp-append-version="true"></script>
    <script>
        var head = sessionStorage.tbHead;
        var arrayall = JSON.parse(sessionStorage.tbArrayAll);
        var htm = "";
        for (var i = 0; i < arrayall.length; i++) {
            htm += "<tr>";
            for (val in arrayall[i]) {
                htm += '<td style="height:36px;border: 1.5px #dddddd solid;">' + arrayall[i][val] + '</td>';
            }
            htm += "</tr>";
        }
        $(".table").html(head + htm);
        $("#printTime").html(getCNDateTime());
        $("#printDate").html(sessionStorage.statTime);

        window.print();
    </script>
</body>
</html>
