﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量延期</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/
        .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #eee; opacity: 1; color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        .red { color: #de163b; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <input type="hidden" id="OwnerNoList" name="OwnerNoList" />
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">延期时间</div>
                <div class="layui-col-xs4 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-number" id="unitNum" name="unitNum" maxlength="4" value="" />
                </div>
                <div class="layui-col-xs3 edit-ipt-ban">
                    <select data-placeholder="延期单位" class="form-control chosen-input " id="unit" name="unit" lay-search>
                        <option value="1">天</option>
                        <option value="2" selected>月</option>
                        <option value="3">年</option>
                    </select>
                </div>
            </div>
            <div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">充值金额</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-floatLimit" id="money" name="money" maxlength="8" value="0.00" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label ">支付金额</div>
                    <div class="layui-col-xs8 edit-ipt-ban">
                        <input type="text" class="layui-input v-floatLimit v-null" maxlength="8" id="payedMoney" name="payedMoney" value="0.00" placeholder="请输入支付金额（元）" autocomplete="off" />
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车位号</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="10" id="noList" name="noList" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">备注</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="100" id="remark" name="remark" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label "></div>
                <div class="layui-col-xs8">
                    <div style=" font-size: 13px; color:#557197 ;"> 温馨提示：<br />1.批量延期充值默认按过期时间进行后延；<br /> 2.延期时将不判断车辆是否在场内；<br />3.延期后，缴费记录和缴费明细将会生成对应的车位续期记录</div>
                </div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function () {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramType = $.getUrlParam("type");
        var paramNo = localStorage.getItem("BathPayDate_OwnerNoList");
        var paramSpaceNo = localStorage.getItem("BathPayDate_SpaceNoList");
        var PayFreeType = 0;
        var index = parent.layer.getFrameIndex(window.name);
        var isOpen = true;

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindData();
                this.bindSelect();
                this.bindEvent();
                if (paramSpaceNo) $("#noList").val(JSON.parse(paramSpaceNo).join(","));
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.noList = paramNo;
                        return data;
                    });

                    var carStr = $("#noList").val();
                    if (carStr.length > 100) carStr = carStr.substring(0, 100) + "..." + " 共" + carStr.split(",").length + "个车位号";

                    var msg = "车 位 号：<t class='red'>" + carStr + "</t><br/>";
                    msg += "充值金额：<t class='red'>" + $("#money").val() + "</t> 元，<br/>";
                    msg += "支付金额：<t class='red'>" + $("#payedMoney").val() + "</t> 元，<br/>";
                    + "</t><br/>"
                    layer.open({
                        type: 0,
                        title: "批量延期提示",
                        btn: ["确定", "取消"],
                        content: msg + "确定批量延期吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Save").attr("disabled", true);
                            $.getJSON("/Owner/BathSpacePayCharge", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); }
                    })
                });

            },
        };


    </script>
</body>
</html>
