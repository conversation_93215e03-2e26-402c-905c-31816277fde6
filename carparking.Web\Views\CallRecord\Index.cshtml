﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>通话记录设置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }

        .layui-bg0-status { background-color: #00E5EE; }

        .layui-bg1-status { background-color: #96CDCD; }

        .layui-bg2-status { background-color: #FFA07A; }

        .layui-bg5-status { background-color: #023378; }

        .layui-bg6-status { background-color: #1f5141; }

        .layui-bg7-status { background-color: #7E95FB; }

        .layui-bg8-status { background-color: #969696; }

        .layui-bg9-status { background-color: #66FF99; }

        .layui-bg10-status { background-color: #FF9900; }

        .layui-bg11-status { background-color: #A48277; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>通话记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" data-lanp="CallRecord_DeviceNo" name="CallRecord_DeviceNo" id="CallRecord_DeviceNo" autocomplete="off" placeholder="设备编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" data-lanp="CallRecord_Account" name="CallRecord_Account" id="CallRecord_Account" autocomplete="off" placeholder="通话账号" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" data-lanp="CallRecord_AnswerNo" name="CallRecord_AnswerNo" id="CallRecord_AnswerNo" autocomplete="off" placeholder="接听端编号" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" data-lanp="CallRecord_AnswerName" name="CallRecord_AnswerName" id="CallRecord_AnswerName" autocomplete="off" placeholder="接听端名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" data-lanp="BeginTime" name="BeginTime" id="BeginTime" autocomplete="off" placeholder="记录时间起" value="" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input lan-placeholder" data-lanp="EndTime" name="EndTime" id="EndTime" autocomplete="off" placeholder="记录时间止" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="通话状态" class="form-control chosen-select lan-select" data-lan="CallRecord_Status" id="CallRecord_Status" name="CallRecord_Status">
                                    <option value="" class="lan-label" selected="selected" data-lan="CallRecord_Status">通话状态</option>
                                    <option value="0" class="lan-label" data-lan="">正常挂断</option>
                                    <option value="1" class="lan-label" data-lan="">线路忙</option>
                                    <option value="2" class="lan-label" data-lan="">用户拒绝</option>
                                    <option value="3" class="lan-label" data-lan="">用户无应答</option>
                                    <option value="4" class="lan-label" data-lan="">通话超时</option>
                                    <option value="5" class="lan-label" data-lan="">取消通话</option>
                                    <option value="6" class="lan-label" data-lan="">无用户</option>
                                    <option value="7" class="lan-label" data-lan="">网络异常</option>
                                    <option value="8" class="lan-label" data-lan="">请求通话</option>
                                    <option value="9" class="lan-label" data-lan="">获取账号</option>
                                    <option value="10" class="lan-label" data-lan="">其它</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.CallRecord.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.CallRecord.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.CallRecord.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/html" id="TmplCallStatus">
        {{#  if(d.CallRecord_Status=='0'){ }}
        <span class="layui-badge layui-bg0-status lan-status">正常挂断</span>
        {{#  } else if (d.CallRecord_Status=='1') { }}
        <span class="layui-badge layui-bg1-status lan-status">线路忙</span>
        {{#  } else if (d.CallRecord_Status=='2') { }}
        <span class="layui-badge layui-bg2-status lan-status">用户拒绝</span>
        {{#  } else if (d.CallRecord_Status=='3') { }}
        <span class="layui-badge layui-bg-blue lan-status" >用户无应答</span>
        {{#  } else if (d.CallRecord_Status=='4') { }}
        <span class="layui-badge layui-bg5-status lan-status" >通话超时</span>
        {{#  } else if (d.CallRecord_Status=='5') { }}
        <span class="layui-badge layui-bg6-status lan-status">取消通话</span>
        {{#  } else if (d.CallRecord_Status=='6') { }}
        <span class="layui-badge layui-bg7-status lan-status">无用户</span>
        {{#  } else if (d.CallRecord_Status=='7') { }}
        <span class="layui-badge layui-bg3-status lan-status">网络异常</span>
        {{#  } else if (d.CallRecord_Status=='8') { }}
        <span class="layui-badge layui-bg0-status lan-status">请求通话</span>
        {{#  } else if (d.CallRecord_Status=='9') { }}
        <span class="layui-badge layui-bg0-status lan-status">获取账号</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-cyan lan-status">其它</span>
        {{#  } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'CallRecord_No', title: '记录编号', hide: true }
                , { field: 'CallRecord_DeviceNo', title: '设备编码', hide: true }
                , { field: 'CallRecord_DeviceName', title: '设备名称' }
                , { field: 'CallRecord_Account', title: '通话账号', hide: true}
                , { field: 'CallRecord_UsersNo', title: '用户编码', hide: true }
                , { field: 'CallRecord_AnswerNo', title: '接听端编号', hide: true }
                , { field: 'CallRecord_AnswerName', title: '接听端名称' }
                , { field: 'CallRecord_StartTime', title: '通话发起时间' }
                , { field: 'CallRecord_AnswerTime', title: '应答时间' }
                , { field: 'CallRecord_EndTime', title: '结束时间' }
                , { field: 'CallRecord_TotalTime', title: '通话时长(秒)', totalRow: true }
                , { field: 'CallRecord_Status', title: '通话状态', toolbar: '#TmplCallStatus' }
                , { field: 'CallRecord_Remark', title: '备注', hide: true }
                , { field: 'CallRecord_AddTime', title: '记录时间' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CallRecord/GetCallRecordList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                   
                };
            });

            tb_row_radio(table);

            pager.init();

            layui.form.render("select");
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["BeginTime", "EndTime"], { type: 'datetime', range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/CallRecord/GetCallRecordList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
