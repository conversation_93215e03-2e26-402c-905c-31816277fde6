﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车详情</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
    <link href="~/Static/css/fishBone.css?v1.0" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        h3, span {
            color: black;
            font-weight: normal !important;
        }

        .ibox-content {
            padding-top: 0px !important;
        }

        body {
            margin-top: 0;
        }

        .fishBone {
            border: 1px solid #f5f5f5;
        }

        .form-group {
            margin-bottom: 10px;
        }

        .gray-bg {
            background-color: #fdf8f8;
            margin-top: 5px;
        }

        h3 {
            padding-top: 5px;
        }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>停车订单列表</h3>
                </div>
            </div>
            <div class="form-group-sm center-block">
                <table class="table table-striped  table-hover">
                    <thead>
                        <tr>
                            <th>停车订单号</th>
                            <th>进场时间</th>
                            <th>出场时间</th>
                            <th>订单金额</th>
                        </tr>
                    </thead>
                    <tbody id="data-view-order"></tbody>
                    <script id="data-tmpl-order" type="text/x-jquery-tmpl">
                        <tr>
                            <td>${ParkOrder_No}</td>
                            <td>${ParkOrder_EnterTime}</td>
                            <td>${ParkOrder_OutTime}</td>
                            <td>${ParkOrder_TotalAmount}</td>
                        </tr>
                    </script>
                </table>
            </div>

            <div class="form-group ">
                <div class="col-sm-12 gray-bg">
                    <h3 class="dbclick">停车详情列表</h3>
                </div>
            </div>
            <div class="form-group-sm center-block">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>车牌号</th>
                            <th>区域名称</th>
                            <th>进场时间</th>
                            <th>出场时间</th>
                            <th>订单金额</th>
                            <th>周期结束时间</th>
                            <th>周期累计金额</th>
                            <th>周期累计免费分钟</th>
                            <th>使用的免费分钟</th>
                            <th>时段累计金额</th>
                            <th>入口</th>
                            <th>出口</th>
                        </tr>
                    </thead>
                    <tbody id="data-view-detail"></tbody>

                    <script id="data-tmpl-detail" type="text/x-jquery-tmpl">
                        <tr>
                            <td>${orderdetail_CarNo}</td>
                           <td>${orderdetail_ParkAreaName }</td>
                           <td>${ orderdetail_EnterTime}</td>
                           <td>${orderdetail_OutTime }</td>
                           <td>${ orderdetail_TotalAmount}</td>
                           <td>${orderdetail_NextCycleTime }</td>
                           <td>${ orderdetail_CycleMoney}</td>
                           <td>${orderdetail_CycleFreeMin }</td>
                           <td>${ orderdetail_UseFreeMin}</td>
                           <td>${ orderdetail_HoursContent}</td>
                           <td>${orderdetail_EnterPasswayName }</td>
                           <td>${orderdetail_OutPasswayName }</td>
                        </tr>
                    </script>
                </table>
            </div>
        </div>
    </div>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/content.min.js?v=1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/js/fishBone.js?v1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.SuperSlide.2.1.1.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var data3 = null;
        var order = decodeURIComponent($.getUrlParam("key"));
        var carno = decodeURIComponent($.getUrlParam("carno"));

        var pager = {
            order:"",
            init: function () {
                this.bindData();
            },
            bindEvent: function () {


            },

            //数据绑定
            bindData: function () {
                var order=pager.order;
                pager.order="";
                layer.msg('加载中...', { icon: 16, time: 0 });
                $.post("GetParkOrderDeatail", { carno: carno,order:order  }, function (json) {
                    layer.closeAll();
                    var  resultTxt=JSON.stringify(json, undefined, 4);
                    $("#result").val(resultTxt);
                    if(resultTxt.indexOf("无权")==-1){
                            pager.order=order;
                    }
                    if(json.success){
                        var parkorder=json.data.olist;
                        var detail=json.data.dlist;
                     
                        $("#data-view-order").html("");
                        $('#data-tmpl-order').tmpl(parkorder).appendTo('#data-view-order');

                        $("#data-view-detail").html("");
                        $('#data-tmpl-detail').tmpl(detail).appendTo('#data-view-detail');
                    }
                }, "json");

            }
        };
        pager.order=order;
        $(function () { pager.init() });

    </script>
</body>
</html>
