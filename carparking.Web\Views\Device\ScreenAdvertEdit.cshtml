﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示屏</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 10px; }
        .divInLine { display: inline-block; }
        .divInLine input { max-height: 30px; }
        .divInLine input.normal { max-width: 100px; max-height: 38px !important; text-align: center; padding-left: 3px; }
        .divInLine button { max-height: 32px !important; line-height: 30px; padding: 0px 10px; margin-bottom: 5px; }
        .divInLine label { height: 15px; padding: 0; }
        .layui-laydate-content > .layui-laydate-list { padding-bottom: 0px; overflow: hidden; }
        .layui-laydate-content > .layui-laydate-list > li { width: 50% }
        .layui-card { margin-bottom: 50px; height: 100%; }
        html, body { height: 100%; }
        .layui-layout-body, .layui-tab-item, .layui-layout, .layui-card, .layui-card-body { height: 90% !important; }
        .layui-layout-admin .layui-footer { width: 100%; left: 10px; }
        .layui-layout-admin .layui-footer { position: relative; }
        .layui-form-mid { width: 100%; }
        label.desc { color: #0af394; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div>
            <div>&nbsp;</div>
            <div style="overflow:hidden;height:0;">
                <!--防止浏览器保存密码后自动填充-->
                <input type="password" />
                <input type="text" />
                <input type="text" name="email" />
            </div>
        </div>
        <div class="layui-card">
            <div id="verifyCheck" class="layui-form layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">设备名称</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" data-minlen="2" maxlength="22" id="DisplayScreen_Name" name="DisplayScreen_Name" placeholder="" />
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">显示类型</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <select class="layui-select" lay-search id="DisplayScreen_DisplayType" name="DisplayScreen_DisplayType">
                            <option value="0" selected>双行横屏</option>
                            <option value="1">四行横屏</option>
                            <option value="2">双列竖屏</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                    <div class="layui-col-sm2 edit-label">显示模式</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <select class="layui-select" lay-search id="DisplayScreen_DisplayPattern" name="DisplayScreen_DisplayPattern">
                            <option value="0">时间</option>
                            <option value="1" selected>广告</option>
                            <option value="2">空车位</option>
                        </select>
                        <div class="layui-form-mid layui-word-aux layui-col-sm3 edit-ipt-ban">空车位模式暂时不支持线上,支持线下设备切换</div>
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>


                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">LED屏广告内容1</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-minlen" data-minlen="2" maxlength="32" id="DisplayScreen_AdvertText1" name="DisplayScreen_AdvertText1" placeholder="使用汉字，字数8，16，24，32" />
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                    <div class="layui-col-sm2 edit-label DisplayType0 layui-hide">LED屏广告内容2</div>
                    <div class="layui-col-sm3 edit-ipt-ban DisplayType0 layui-hide">
                        <input type="text" class="layui-input v-minlen" data-minlen="2" maxlength="32" id="DisplayScreen_AdvertText2" name="DisplayScreen_AdvertText2" placeholder="汉字，数字，字母" />
                        <label class="desc">请注意：汉字最多显示28个</label>
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>

                <div class="hr-line-dashed" style="clear:both;"></div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <button class="btn btn-primary" id="SaveType"><i class="fa fa-check"></i> <t>设置显示类型</t></button>
                        <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>设置广告内容</t></button>
                        <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}" data-sentryHostNo="${Passway_SentryHostNo}">${Passway_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>

        myVerify.init();
        var xmSelect = null;
        var laydate = null;
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['table', 'element', 'form', 'xmSelect', 'laydate'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            laydate = layui.laydate;
            table = layui.table;
            pager.init();
        });

    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramDeviceNo = parent.pager.SelDeviceNoes;  //decodeURIComponent($.getUrlParam("Device_No"));
        var paramDeviceName = parent.pager.SelDeviceNames;// decodeURIComponent($.getUrlParam("Device_Name"));
        var index = parent.index;

        $("#DisplayScreen_Name").val(paramDeviceName);

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {

                layui.form.on("select", function (data) {
                    if (data.elem.id == "DisplayScreen_DisplayType") {
                        var no = data.value;
                        if (no == '1') {
                            $(".DisplayType0").removeClass("layui-hide");
                        }
                        else {
                            $(".DisplayType0").addClass("layui-hide");
                        }
                    }
                })

                layui.form.render("select");
            },
            //数据绑定
            bindData: function () {
                $("#DisplayScreen_Name").attr("disabled", true);
                if (paramAct == "Update") {
                    $("#DisplayScreen_PasswayNo").attr("disabled", true);
                    $.post("GetDeviceDetail", { DisplayScreen_Key: paramDisplayScreenKey }, function (json) {
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data.model, function (data) { });
                            if (json.data.model.DisplayScreen_DisplayType == '1') {
                                $(".DisplayType0").removeClass("layui-hide");
                            }
                            else {
                                $(".DisplayType0").addClass("layui-hide");
                            }
                            layui.form.render("select");
                        } else {
                            console.log(json.msg);
                        }
                    }, "json");

                }
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.DisplayScreen_AdvertText1 = $("#DisplayScreen_AdvertText1").val();
                        data.DisplayScreen_AdvertText2 = $("#DisplayScreen_AdvertText2").val();
                        data.DisplayScreen_PasswayNo = $("#DisplayScreen_PasswayNo").val();
                        return data;
                    });

                    //if (param.DisplayScreen_AdvertText1 == "") { layer.msg("LED屏广告内容1不能为空", { icon: 0 }); return; }
                    if (param.DisplayScreen_AdvertText1 != "" && !validateChineseString(param.DisplayScreen_AdvertText1)) { layer.msg("仅支持汉字，汉字字数可为8，或者16，或者24，或者32", { icon: 0 }); return; }

                    param.DisplayScreen_DeviceNo = paramDeviceNo;
                    $("#Save").attr("disabled", true);
                    $.getJSON("/Device/SetScreenDevice", { jsonModel: JSON.stringify(param), act: "3" }, function (json) {
                        if (json.success) {
                            layer.msg("已下发", { icon: 1, time: 1500 }, function () {
                                //window.parent.parent.pager.bindData(1);
                                $("#Save").removeAttr("disabled");
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });
                });
                $("#SaveType").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });

                    param.DisplayScreen_DeviceNo = paramDeviceNo;
                    $("#SaveType").attr("disabled", true);
                    $.getJSON("/Device/SetScreenDevice", { jsonModel: JSON.stringify(param), act: "4" }, function (json) {
                        if (json.success) {
                            layer.msg("已下发", { icon: 1, time: 1500 }, function () {
                                //window.parent.parent.pager.bindData(1);
                                $("#SaveType").removeAttr("disabled");
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0 });
                            $("#SaveType").removeAttr("disabled");
                        }
                    });
                });

            },
        };

        function validateChineseString(str) {
            // 验证是否为汉字
            var pattern = /^[\u4e00-\u9fa5]+$/;
            if (!pattern.test(str)) {
                return false;
            }

            // 验证长度是否为 8、16、24 或 32
            var len = str.length;
            if (len !== 8 && len !== 16 && len !== 24 && len !== 32) {
                return false;
            }

            return true;
        }


    </script>
</body>
</html>
