﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>日期设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/admin/calendar/laydateNote/laydateNote.css?1.2.8" rel="stylesheet">

    <style>
        html, body {
            height: 100%;
            width: 100%;
            overflow: auto;
        }

        .fa {
            margin: 6px 4px;
            float: left;
            font-size: 16px;
        }

        .layui-select-title input {
            color: #0094ff;
        }

        .layui-inline .layui-form-select .layui-input {
            width: 182px;
        }

        .rulebox .layui-row {
            margin-top: 10px;
        }

        .rulebox label {
            float: left;
            padding: 9px 5px 0;
        }

        .layui-input.small {
            width: 70px;
            float: left;
        }

        .select-small {
            float: left;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>日期设置</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">

                <div class="layui-tab">
                    <ul class="layui-tab-title">
                        <li class="layui-this dateshow">日历展示</li>
                        <li class="datashow">数据操作</li>
                    </ul>
                    <div class="layui-tab-content">
                        <div class="layui-tab-item layui-show">
                            <div class="layui-inline laydateNote" id="calendar"></div>
                        </div>
                        <div class="layui-tab-item">
                            <div class="layui-card">
                                <div class="layui-card-header layui-form" id="searchForm">
                                    <div class="layui-row">
                                        <div class="layui-inline">
                                            <input type="text" class="layui-input" id="DateSet_Year" name="DateSet_Year" placeholder="年份设置" value="" readonly />
                                        </div>
                                        <div class="layui-inline">
                                            <select class="layui-select" lay-search id="DateSet_Type" name="DateSet_Type">
                                                <option value="">日期类型</option>
                                                <option value="0">节假日</option>
                                                <option value="1">工作日</option>
                                            </select>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                        </div>
                                    </div>

                                </div>

                                <div class="layui-card-body">
                                    <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                                    <script type="text/html" id="toolbar_btns">
                                        <div class="layui-btn-container">
                                            {{# if(Power.DateSet.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>设置日期</t></button>{{# } }}
                                            {{# if(Power.DateSet.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-calendar-plus-o"></i><t>设置周期</t></button>{{# } }}
                                            {{# if(Power.DateSet.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                                        </div>
                                    </script>

                                </div>
                            </div>

                        </div>

                    </div>
                </div>


            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplType">
        {{# if(d.DateSet_Type==0){ }}
        <span class="layui-badge">节假日</span>
        {{# }else if(d.DateSet_Type==1){ }}
        <span class="layui-badge layui-bg-blue">工作日</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?2" asp-append-version="true"></script>
    <script>
        var Power = window.parent.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;

        layui.config({base: '../Static/admin/' }).extend({
              laydateNote: 'calendar/laydateNote/laydateNote', //如果 mymod.js 是在根目录，也可以不用设定别名
              artTemplate: 'calendar/artTemplate/artTemplate'
        })

        layui.use(['table', 'jquery', 'form', 'laydate','laydateNote'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            pager.GetCalendar();
            pager.init();

            //$('#DateSet_Year').val(new Date().getFullYear());
            layui.laydate.render({
                elem: '#DateSet_Year'
                , type: "year"
                , done: function (value, date, end) {
                    pager.bindData(1);
                }
            });

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'DateSet_No', title: '编码' , hide: true}
                , { field: 'DateSet_Year', title: '年份' }
                , { field: 'DateSet_Date', title: '日期' }
                , { field: 'DateSet_Date', title: '星期', templet: function (d) { return _DATE.GetWeekDay(d.DateSet_Date).value; } }
                , { field: 'DateSet_Type', title: '类型', toolbar: "#tmplType" }
                , { field: 'DateSet_Desc', title: '描述' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: 'GetDateSetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        var year = $("#DateSet_Year").val();
                        layer.open({
                            type: 2, id: 1,
                            title: "新增节假日/工作日",
                            content: 'Edit?Act=Add&year=' + year,
                            area: getIframeArea(['600px', '520px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        var year = $("#DateSet_Year").val();
                        layer.open({
                            type: 2, id: 1,
                            title: "按周期设置工作日/节假日",
                            content: 'EditCycle?Act=Add&year=' + year,
                            area: getIframeArea(['600px', '520px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        data.forEach(function (item, index) { NoArray[NoArray.length] = item.DateSet_No; });
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("DelDateSet", { DateSet_No: NoArray.join(',') }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                            pager.bindData(pager.pageIndex);
                                            pager.GetCalendar();
                                        });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_checkbox();

        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: 'GetDateSetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                layuiForm.on("select", function (data) {
                    if(data&&data.elem&&data.elem.id!='dtype')pager.bindData(1);
                });
                $("#Search").click(function () { pager.bindData(1); });

                $(".datashow").click(function () { pager.bindData(1); });
            },
            GetCalendar:function(){
                 var laydateNote = layui.laydateNote
                 $("#calendar").html("");
                 laydateNote({
                       elem: '#calendar', //容器id,CLass
                       url: 'GetMonthDateList', //异步数据接口,本地浏览时可不设置
                       sort: 'up', //日期排序，默认不设置不排序，up 日期升序，down 日期降序
                       fine: '.laydateNotebook', //开启详细模式
                       done: function(data, chooseData, type) {
                         //回调数据，这里发送你的请求ajax
                         //console.log(data, chooseData, type)
                       }
                 })
            }
        }
    </script>
</body>
</html>
