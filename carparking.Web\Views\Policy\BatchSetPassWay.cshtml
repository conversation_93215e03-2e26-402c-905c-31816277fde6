﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title></title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <style>
        html, body { padding: 0; }

        .layui-tab-content { padding: 0; }

        .layui-select-title input { color: #0094ff; }

        .layui-card-body .layui-row { margin-top: 15px; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }

        select[multiple] + .layui-form-select dd { padding: 0; }

        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }

        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }

        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }

        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }

        .multiSelect a { padding: 2px 5px; background: #5FB878; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }

        .multiSelect a span { float: left; }

        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }

        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }

        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }

        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }

        .multiSelect a i:hover { background-color: #545556; }

        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }

        .multiOption:hover { color: #5FB878 }
    </style>
</head>
<body>

    <div class="layui-card layui-form">
        <div class="layui-card-header">
            <blockquote class="layui-elem-quote">请谨慎操作，当前批量设置将会覆盖之前的单个设置。</blockquote>
            <div class="layui-row">
                <div class="layui-col-xs12">
                    <select class="layui-select" id="PolicyPass_PasswayNo" name="PolicyPass_PasswayNo" multiple lay-search lay-tools>
                        <option value="">请选择车道</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-xs4">车道通行控制</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_IsAuthorize" name="PolicyPassway_IsAuthorize">
                        <option value="1">开启</option>
                        <option value="0" selected>关闭</option>
                    </select>
                </div>
            </div>
            <div class="layui-row ingate">
                <div class="layui-col-xs4">车道尾号限行控制</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_IsNumAuthorize" name="PolicyPassway_IsNumAuthorize">
                        <option value="0">关闭</option>
                        <option value="1">开启</option>
                    </select>
                </div>
            </div>
            <div class="layui-row ingate">
                <div class="layui-col-xs4">默认车牌计费类型</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_DefaultCarCardType" name="PolicyPassway_DefaultCarCardType">
                        <option value="">不设置</option>
                    </select>
                </div>
            </div>
            <div class="layui-row ingate">
                <div class="layui-col-xs4">默认车牌颜色</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_DefaultCarType" name="PolicyPassway_DefaultCarType">
                        <option value="">不设置</option>
                    </select>
                </div>
            </div>
            <div class="layui-row outgate">
                <div class="layui-col-xs4">车牌模糊匹配</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_CarMatch" name="PolicyPassway_CarMatch">
                        <option value="1">完全匹配</option>
                        <option value="2" selected>仅允许首字符错误</option>
                        <option value="3">允许任意一位字符错误</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">无效车牌过滤</div>
                <div class="layui-col-xs8">
                    <input type="text" class="layui-input" id="PolicyPassway_CarInvalid" name="PolicyPassway_CarInvalid" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">显示播报</div>
                <div class="layui-col-xs8">
                    <select class="layui-input" id="PolicyPassway_ShowOption" name="PolicyPassway_ShowOption">
                        <option value="1">欢迎光临/一路顺风</option>
                        <option value="2">您好/一路平安</option>
                        <option value="3">欢迎光临/一路平安</option>
                        <option value="4">您好/一路顺风</option>
                        <option value="5">自定义语音</option>
                    </select>
                    <input type="text" class="layui-input layui-hide" id="PolicyPassway_Show" name="PolicyPassway_Show" placeholder="请输入自定义语音" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">播报并显示停车时长</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_Broadparktime" name="PolicyPassway_Broadparktime">
                        <option value="0">关闭</option>
                        <option value="1" selected>开启</option>
                    </select>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs4">显示发布信息</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_Broadpushinfo" name="PolicyPassway_Broadpushinfo">
                        <option value="0">关闭</option>
                        <option value="1">开启</option>
                    </select>
                    <input type="text" class="layui-input layui-hide" id="PolicyPassway_Broadpushtext" name="PolicyPassway_Broadpushtext" placeholder="请输入发布内容" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">显示剩余车位</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_Broadspacenum" name="PolicyPassway_Broadspacenum">
                        <option value="0">关闭</option>
                        <option value="1">开启</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">显示车位号</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_Broadspace" name="PolicyPassway_Broadspace">
                        <option value="0">关闭</option>
                        <option value="1">开启</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">无牌车扫码</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_Scan" name="PolicyPassway_Scan">
                        <option value="1">有车允许扫码</option>
                        <option value="2" selected>有无车均可扫码</option>
                        <option value="3">识别无牌车可扫码</option>
                        <option value="4">识别无牌车+有车可扫码</option>
                        <option value="0">有无车均禁止扫码</option>
                    </select>
                </div>
            </div>
           @*  <div class="layui-row">
                <div class="layui-col-xs4">无牌车计数器</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_HasCount" name="PolicyPassway_HasCount">
                        <option value="0">禁用</option>
                        <option value="1">启用</option>
                    </select>
                </div>
            </div> *@
            <div class="layui-row">
                <div class="layui-col-xs4">倒车事件处理模式</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_BackCarMode" name="PolicyPassway_BackCarMode">
                        <option value="1">智能处理</option>
                        <option value="2" selected>人工处理</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">倒车事件处理车道弹窗</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_ClosePasswayWin" name="PolicyPassway_ClosePasswayWin">
                        <option value="0">不处理</option>
                        <option value="1" selected>自动关闭车道弹窗</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">无牌车播报</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_BroadcastNoCar" name="PolicyPassway_BroadcastNoCar">
                        <option value="0">禁用</option>
                        <option value="1" selected>启用</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">限时等待确认放行</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_WaitTimeOut" name="PolicyPassway_WaitTimeOut">
                        <option value="1">1分钟</option>
                        <option value="2">2分钟</option>
                        <option value="3">3分钟</option>
                        <option value="4">4分钟</option>
                        <option value="5" selected>5分钟</option>
                        <option value="6">6分钟</option>
                        <option value="7">7分钟</option>
                        <option value="8">8分钟</option>
                        <option value="9">9分钟</option>
                        <option value="10">10分钟</option>
                        <option value="11">11分钟</option>
                        <option value="12">12分钟</option>
                        <option value="13">13分钟</option>
                        <option value="14">14分钟</option>
                        <option value="15">15分钟</option>
                        <option value="30">30分钟</option>
                        <option value="-1">不限时</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">确认框逾期未处理</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_TimeOutHandle" name="PolicyPassway_TimeOutHandle">
                        <option value="0">当取消放行处理</option>
                        <option value="1">当确认放行处理</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">启用优先保存记录</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_TakeRecord" name="PolicyPassway_TakeRecord">
                        <option value="0">禁用</option>
                        <option value="1">启用</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">遥控开闸放行</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_OpenGateForOrder" name="PolicyPassway_OpenGateForOrder">
                        <option value="0" selected>禁用</option>
                        <option value="1">启用</option>
                    </select>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs4">通行需要补缴欠费金额</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_IsPayClearing" name="PolicyPassway_IsPayClearing">
                        <option value="0">禁用</option>
                        <option value="1">启用</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">车辆出场播报语音</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_IsInoutSound" name="PolicyPassway_IsInoutSound">
                        <option value="0">禁用</option>
                        <option value="1" selected>启用</option>
                    </select>
                </div>
            </div>
             <div class="layui-row">
                <div class="layui-col-xs4">显示车道二维码</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_S017UQR" name="PolicyPassway_S017UQR">
                        <option value="0" selected>禁用</option>
                        <option value="1">启用</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4">指定车牌颜色开双闸</div>
                <div class="layui-col-xs8">
                    <select class="layui-select" id="PolicyPassway_DoubleGateColorCodes" name="PolicyPassway_DoubleGateColorCodes" lay-filter="doubleGateColors" multiple lay-search>
                        <option value="">请选择车牌颜色</option>
                    </select>
                </div>
            </div>

        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row">
            <div class="layui-col-xs8 layui-col-xs-offset4">
                <button id="Save" class="layui-btn layui-btn-sm"><i class="fa fa-check"></i> 保存</button>
                <button id="Cancel" class="layui-btn layui-btn-sm layui-bg-orange"><i class="fa fa-close"></i> 取消</button>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js"></script>
    <script src="~/Static/js/bootstrap.min.js"></script>
    <script src="~/Static/js/jquery.common.js"></script>
    <script src="~/Static/js/jquery.verify.js"></script>
    <script src="~/Static/js/jquery.tmpl.min.js"></script>
    <script src="~/Static/plugins/layui/layui.js"></script>
    <script type="text/javascript">
        myVerify.init();

        layui.use(['element', 'form'], function () {

            pager.init();
        })


        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//免费车类型

        var pager = {
            data: null,
            passways: [],
            multi: [],
            gateBase: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllPasswayAndCarCardType", {}, function (json) {
                    if (json.success) {
                        pager.data = json.data;
                        var passways = json.data.passways;
                        var carCardTypes = json.data.carCardTypes;
                        passways.forEach(function (item, index) {
                            var gate = [];
                            pager.data.links.forEach(function (d, i) {
                                if (d.PasswayLink_PasswayNo == item.Passway_No) {
                                    gate[gate.length] = d;
                                }
                            });
                            var gateType = 1;
                            if (gate.length == 1) { gateType = gate[0].PasswayLink_GateType; }

                            var option = '<option value="' + item.Passway_No + '" data-gate="' + gateType + '">' + item.Passway_Name + '</option>';
                            $("#PolicyPass_PasswayNo").append(option);
                        });

                        $("#PolicyPassway_DefaultCarCardType").append($("#tmplcarcardtype").tmpl(json.data.carCardTypes));
                        var cct = json.data.carCardTypes.find((item, index) => { return item.CarCardType_Category == 3651; });
                        if (cct != null && cct.CarCardType_No)
                            $("#PolicyPassway_DefaultCarCardType").val(cct.CarCardType_No);
                        else {
                            var option1 = $("#PolicyPassway_DefaultCarCardType").find("option[value!='']").first();
                            if (option1 && $(option1).val() != "") $("#PolicyPassway_DefaultCarCardType").val($(option1).val());
                        }

                        $("#PolicyPassway_DefaultCarType").append($("#tmplcartype").tmpl(json.data.carTypes));
                        var blueCarType = json.data.carTypes.find((item) => item.CarType_Name === "蓝牌车");
                        if (blueCarType && blueCarType.CarType_No) {
                            $("#PolicyPassway_DefaultCarType").val(blueCarType.CarType_No);
                        } else {
                            var option2 = $("#PolicyPassway_DefaultCarType").find("option[value!='']").first();
                            if (option2 && $(option2).val() != "") $("#PolicyPassway_DefaultCarType").val($(option2).val());
                        }
                        // 添加车牌颜色选项
                         $("#PolicyPassway_DoubleGateColorCodes").append($("#tmplcartype").tmpl(json.data.carTypes));
                    }
                }, "json");

                layui.form.render();
            },
            bindData: function () {

            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    var val = data.value;
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        pager.passways = data.value;
                    }
                    //语音
                    else if (data.elem.id == "PolicyPassway_ShowOption") {
                        if (val == 5)
                            $("#PolicyPassway_Show").removeClass("layui-hide");
                        else
                            $("#PolicyPassway_Show").removeClass("layui-hide").addClass("layui-hide");
                    }
                    //发布内容
                    else if (data.elem.id == "PolicyPassway_Broadpushinfo") {
                        if (val == 1)
                            $("#PolicyPassway_Broadpushtext").removeClass("layui-hide");
                        else
                            $("#PolicyPassway_Broadpushtext").removeClass("layui-hide").addClass("layui-hide");
                    }
                });

                $("#Cancel").click(function () { parent.layer.closeAll(); });

                $("#Save").click(function () {
                    var passwayno = $("#PolicyPass_PasswayNo").val();
                    if (!passwayno) { layer.msg("请选择车道"); return; }
                    var param = {};
                    $(".layui-card-body").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide"))
                                param[$(this).attr('id')] = $(this).val();
                        }
                    });
                    param.PolicyPassway_DefaultCarCardType = $("#PolicyPassway_DefaultCarCardType").val();
                    param.PolicyPassway_DefaultCarType = $("#PolicyPassway_DefaultCarType").val();

                    debugger;
                    // 处理PolicyPassway_DoubleGateColorCodes的值，过滤掉空值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    if (selectedColors && selectedColors.length > 0) {
                        selectedColors = selectedColors.filter(function(color) {
                            return color !== "" && color !== null && color !== undefined;
                        });
                    } else {
                        selectedColors = [];
                    }
                    param.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));



                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "当前批量设置将会覆盖之前的单个设置，确定保存?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("BatchSetForPassWay", {
                                PasswayNoList: JSON.stringify(pager.passways),
                                data: JSON.stringify(param)
                            }, function (json) {
                                if (json.success)
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        parent.policy.passway.onload();
                                        parent.layer.closeAll();
                                    });
                                else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
            },
            bindPower: function () {
                window.parent.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        }
    </script>

</body>
</html>