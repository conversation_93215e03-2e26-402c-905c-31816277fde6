﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .btnUnit { border-color: #d9d8d8 !important; border-top-right-radius: 5px; border-bottom-right-radius: 5px; color: #5db587 }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>

            <div class="layui-row">
                <div class="edit-label  layui-col-xs2">规则名称</div>
                <div class="edit-ipt-ban layui-col-xs8">
                    <input type="text" class="layui-input v-null" id="MonthRule_Name" name="MonthRule_Name" data-minlen="2" maxlength="50" value="" placeholder="请填写规则名称" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label ">规则有效期</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-submit" value="" id="MonthRule_Time" name="MonthRule_Time" data-key="MonthRule_Time" placeholder="请选择规则有效期" readonly />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>

            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label ">车牌类型</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <select class="layui-select" lay-search id="MonthRule_CarCardTypeNo" name="MonthRule_CarCardTypeNo" data-key="MonthRule_CarCardTypeNo">
                        <option value="" data-category="">车牌类型</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row category3 month">
                <div class="layui-col-xs2 edit-label ">充值时段</div>
                <div class="layui-col-xs4 edit-ipt-ban">
                    <div class="input-group">
                        <input type="text" class="layui-input v-null v-number" maxlength="4" id="MonthRule_Cycle" name="MonthRule_Cycle" />
                        <span class="input-group-btn" style="width: 35%; font-size:14px;">
                            <select class="layui-select" lay-search id="MonthRule_Unit" name="MonthRule_Unit">
                                <option value="1">天</option>
                                <option value="2" selected>月</option>
                                <option value="3">年</option>
                            </select>
                        </span>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-xs1 edit-label ">&nbsp;</div>
                <div class="layui-col-xs2 edit-ipt-ban">
                    <select class="layui-select" lay-search id="MonthRule_AddEnable" name="MonthRule_AddEnable" data-key="">
                        <option value="0">无赠送</option>
                        <option value="1">赠送优惠</option>
                    </select>
                </div>
            </div>
            <div class="layui-row category3 month_zs layui-hide">
                <div class="layui-col-xs2 edit-label ">赠送</div>
                <div class="layui-col-xs4 edit-ipt-ban">
                    <div class="input-group">
                        <input type="text" class="layui-input v-null v-number" maxlength="4" id="MonthRule_AddCycle" name="MonthRule_AddCycle" />
                        <span class="input-group-btn" style="width: 35%; font-size:14px;">
                            <select class="layui-select" lay-search id="MonthRule_AddUnit" name="MonthRule_AddUnit">
                                <option value="1">天</option>
                                <option value="2">月</option>
                                <option value="3">年</option>
                            </select>
                        </span>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row chuzhi layui-hide">
                <div class="layui-col-xs2 edit-label ">充值金额</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="input-group">
                        <input type="text" class="layui-input v-null v-float" maxlength="8" id="MonthRule_EMoney" name="MonthRule_EMoney" />
                        <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label ">收费金额</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="input-group">
                        <input type="text" class="layui-input v-null v-float" maxlength="8" id="MonthRule_Money" name="MonthRule_Money" />
                        <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row month">
                <div class="layui-col-xs2 edit-label ">开始类型</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="MonthRule_Type">
                        <ul>
                            <li data-value="1">本月1日</li>
                            <li data-value="2">当前时间</li>
                            <li data-value="3">过期时间</li>
                        </ul>
                    </div>
                </div>
            </div>
           @* <div class="layui-row month">
                <div class="layui-col-xs2 edit-label ">按车位计费</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="MonthRule_PayType">
                        <ul>
                            <li data-value="0">否</li>
                            <li data-value="1">是</li>
                        </ul>
                    </div>
                </div>
            </div>*@
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs2 edit-label">&nbsp;</div>
            <div class="layui-col-xs6 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layuiForm = null;
        layui.use(['laydate', 'form'], function () {
            layuiForm = layui.form;
            layui.laydate.render({
                elem: '#MonthRule_Time', range: true, done: function (value, date, endDate) {
                    pager.MonthRule_BeginTime = new Date(date.year, date.month - 1, date.date).Format("yyyy-MM-dd");
                    pager.MonthRule_EndTime = new Date(endDate.year, endDate.month - 1, endDate.date).Format("yyyy-MM-dd");
                }
            });
            pager.init();
        })

    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("MonthRule_No");
        var cardno = $.getUrlParam("cardno");
        var carno = $.getUrlParam("carno");
        var category = $.getUrlParam("category");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            MonthRule_BeginTime: null,
            MonthRule_EndTime: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetCarCardType", {}, function (json) {
                    if (json.success) {
                        $("#MonthRule_CarCardTypeNo").html($("#tmplcarcardtype").tmpl(json.data));
                    }
                    else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("GetMonthRuleDetail", { MonthRule_No: paramNo }, function (json) {
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data, function (data) { });
                            pager.MonthRule_BeginTime = json.data.MonthRule_BeginTime;
                            pager.MonthRule_EndTime = json.data.MonthRule_EndTime;
                            if (json.data.MonthRule_BeginTime && json.data.MonthRule_EndTime)
                                $("#MonthRule_Time").val(new Date(json.data.MonthRule_BeginTime).Format("yyyy-MM-dd") + " - " + new Date(json.data.MonthRule_EndTime).Format("yyyy-MM-dd"));
                      
                            showType("MonthRule_CarCardTypeNo", json.data.MonthRule_Category);
                            showType("MonthRule_AddEnable", "", json.data.MonthRule_AddEnable);

                            if (!json.data.MonthRule_Type) json.data.MonthRule_Type = 3;
                            if (!json.data.MonthRule_PayType) json.data.MonthRule_PayType = 0;
                            config.MonthRule_Type = json.data.MonthRule_Type;
                            config.MonthRule_PayType = json.data.MonthRule_PayType;
                            LoadConfig(config);
                        }
                    });
                } else {
                    LoadConfig(config);
                    showType("MonthRule_CarCardTypeNo", $("#MonthRule_CarCardTypeNo").find("option:selected").attr("data-category"));
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.MonthRule_BeginTime = pager.MonthRule_BeginTime;
                        data.MonthRule_EndTime = pager.MonthRule_EndTime;
                        data.MonthRule_Type = config.MonthRule_Type;
                        data.MonthRule_PayType = config.MonthRule_PayType;
                        return data;
                    });

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.getJSON("AddMonthRule", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {
                        param.MonthRule_No = paramNo;
                        $.getJSON("UpdateMonthRule", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

                $(".btnCombox ul li").click(function () {
                    /*  if ($(this).hasClass("select")) return;*/
                    var idName = $(this).parent().parent().attr("id");
                    $(this).siblings().removeClass("select");
                    $(this).addClass("select");
                    config[idName] = $(this).attr("data-value");
                });

                layuiForm.render("select");
                layuiForm.on("select", function (data) {
                    var val = data.value;
                    var id = data.elem.id;
                    var key = $(data.elem).attr("data-key");
                    var cg = $(data.elem).find("option:selected").attr("data-category");

                    showType(id, cg, val);
                })
            },
        };

        function showType(id, cg, val) {
            if (id == "MonthRule_CarCardTypeNo") {
                if (cg == 3652 || cg == 3653 || cg == 3654
                    || cg == 3655 || cg == 3661 || cg == 3662
                    || cg == 3663 || cg == 3664|| cg == 3656) {
                    if ($("#MonthRule_AddEnable").val() == "1") {
                        $(".month_zs").removeClass("layui-hide");
                    } else {
                        $(".month_zs").addClass("layui-hide");
                    }
                    $(".month,.chuzhi").addClass("layui-hide");
                    $(".month").removeClass("layui-hide");
                } else {
                    $(".month,.chuzhi,.month_zs").addClass("layui-hide");
                    $(".chuzhi").removeClass("layui-hide");
                }
            } else if (id == "MonthRule_AddEnable") {
                if (val == 0) {
                    $(".month_zs").addClass("layui-hide");
                } else {
                    $(".month_zs").removeClass("layui-hide");
                }
            }
        }
        function linkTime(BeginTime, EndTime) {
            if (BeginTime && EndTime) {
                var time = new Date(BeginTime).Format("yyyy-MM-dd") + " - " + new Date(EndTime).Format("yyyy-MM-dd");
                return time;
            } else
                return "";
        }
    </script>
    <script>
        var config = {
            MonthRule_Type: 3,
            MonthRule_PayType:0,
        };

        var LoadConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];
            });
        }
    </script>
</body>
</html>
