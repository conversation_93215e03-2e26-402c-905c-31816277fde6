﻿<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车道管理</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            overflow-x: hidden;
        }

        body {
            background-color: #ecf0f5;
            font-family: 'Microsoft YaHei';
        }

        .fa {
            margin: 7px 4px;
            float: left;
            font-size: 16px;
        }

        .layui-card {
            margin: 15px;
        }

        .layui-form-select .layui-input {
            width: 182px;
        }

        .layui-table-tool-temp {
            padding-right: 30px;
        }
    </style>
</head>

<body class="animated fadeInRight">
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场配置</cite></a>
                <a><cite>车道管理</cite></a>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="test-table-reload-btn layui-form form-group" id="searchForm">
                <div class="layui-inline">
                    <select data-placeholder="岗亭名称" class="layui-input" id="Passway_SentryHostNo"
                        name="Passway_SentryHostNo" lay-search>
                        <option value="">岗亭名称</option>
                    </select>
                </div>
                <div class="layui-inline">
                    <input class="layui-input " name="Passway_No" id="Passway_No" autocomplete="off"
                        placeholder="车道编码" />
                </div>
                <div class="layui-inline">
                    <input class="layui-input " name="Passway_Name" id="Passway_Name" autocomplete="off"
                        placeholder="车道名称" />
                </div>
                <div class="layui-inline">
                    <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i>
                        <t>搜索</t>
                    </button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <table class="layui-table" id="com-table-base" lay-filter="com-table-base">
            </table>
            <script type="text/html" id="toolbar_btns">
                <div class="layui-btn-container">
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Add" id="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Update" id="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Delete" id="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Open" id="Open"><i class="fa fa-toggle-right"></i><t>开闸</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Close" id="Close"><i class="fa fa-toggle-down"></i><t>关闸</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="GateSwitch" id="GateSwitch"><i class="fa fa-square-o"></i><t>道闸常开</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Synchronous" id="Synchronous"><i class="fa fa-retweet"></i><t>同步白名单</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="UnBind" id="UnBind"><i class="fa fa-trash-o"></i><t>清空黑白名单</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="SyncInOut" id="SyncInOut"><i class="fa fa-retweet"></i><t>同步进出口及小数点位数</t></button>
                </div>
            </script>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="type">
        {{# if(d.Passway_Type==1){}}
        <span class="layui-badge layui-bg-cyan">汽车通道</span>
        {{# }else if(d.Passway_Type==2){ }}
        <span class="layui-badge layui-bg-red">人行通道</span>
        {{# }else if(d.Passway_Type==3){ }}
        <span class="layui-badge layui-bg-orange">非机动车道</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="dmode">
        {{# if(d.Passway_DutyMode==0){}}
        <span class="layui-badge layui-bg-cyan">无人值守</span>
        {{# }else if(d.Passway_DutyMode==1){ }}
        <span class="layui-badge layui-bg-red">现场值守</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="cmode">
        {{# if(d.Passway_CameraMode==1){}}
        <span class="layui-badge layui-bg-blue">单相机模式</span>
        {{# }else if(d.Passway_CameraMode==2){ }}
        <span class="layui-badge layui-bg-orange">双相机模式</span>
        {{# }else if(d.Passway_CameraMode==3){ }}
        <span class="layui-badge layui-bg-red">多相机模式</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="time">
        {{# if(d.Passway_CameraMode!=1){}}
        {{d.Passway_IdInterval}}
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="charge">
        {{# if(d.Passway_IsCharge==1){}}
        <span class="layui-badge layui-bg-blue">启用</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange">禁用</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="sense">
        {{# if(d.Passway_Sense==1){}}
        <span class="layui-badge layui-bg-blue">启用</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange">禁用</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplhost">
        <option value="${SentryHost_No}">${SentryHost_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="backcar">
        {{# if(d.Passway_EnableBoard==1&&d.Passway_IsBackCar==1){}}
        <span class="layui-badge layui-bg-blue">启用</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange">禁用</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();
        var comtable = null;
        layui.use(['form', 'laytpl'], function () {
            var table = layui.table;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Passway_No', title: '车道编号', hide: true }
                , { field: 'Passway_Name', title: '车道名称' }
                , {
                    field: 'AreaPassways', title: '区域'
                    , templet: function (d) {
                        var htm = [];
                        var out = '<t style="color:green;">[出]</t>';
                        var inr = '<t style="color:blue;">[入]</t>';
                        d.AreaPassways.forEach(function (item, index) {
                            if (item.PasswayLink_GateType == 0)
                                htm[htm.length] = out + item.ParkArea_Name;
                            else
                                htm[htm.length] = inr + item.ParkArea_Name;
                        });
                        return htm.join("、");
                    }
                }
                , { field: 'Passway_SentryHostNo', title: '岗亭编码', hide: true }
                , { field: 'SentryHost_Name', title: '岗亭名称' }
                , { field: 'Passway_Type', title: '车道类型', toolbar: "#type", hide: true }
                //, { field: 'Passway_DutyMode', title: '值守模式', toolbar: "#dmode", hide: true }
                , { field: 'Passway_CameraMode', title: '相机模式', toolbar: "#cmode" }
                , { field: 'Passway_IdInterval', title: '识别间隔', toolbar: '#time', hide: true }
                , { field: 'Passway_IsBackCar', title: '防倒车', toolbar: '#backcar' }
                , { field: 'Passway_IsCharge', title: '启用收费', toolbar: '#charge' }
                , { field: 'Passway_Sense', title: '无牌车地感判断', toolbar: "#sense", hide: true }
                , { field: 'Passway_Addtime', title: '注册时间' }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Passway/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增车道",
                            content: "Edit?Act=Add",
                            area: getIframeArea(["800px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var paramNo = data[0].Passway_No;
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑车道",
                            content: "Edit?Act=Update&Passway_No=" + paramNo,
                            area: getIframeArea(["800px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var paramNoList = [];
                        $.each(data, function (k, v) { paramNoList.push(v.Passway_No); });
                        LAYER_OPEN_TYPE_0("确定删除车道?", res => {
                            LAYER_LOADING("处理中...");
                            $.post("DelPassway", { Passway_No: JSON.stringify(paramNoList) }, function (json) {
                                if (json.success) {
                                    window.pager.bindData(pager.pageIndex);
                                    layer.msg("删除成功", { time: 1000 }, function () {
                                    })
                                } else {                               
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                        })
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Open':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var paramNo = data[0].Passway_No;
                        LAYER_OPEN_TYPE_0("确定开闸?", res => {
                            LAYER_LOADING("处理中...");
                            $.getJSON("ExcuteOpen", { Passway_No: paramNo }, function (json) {
                                //json.msg 不包含“请取消道闸常开”
                                if (json.success && json.msg.indexOf("请取消道闸常开") < 0) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 });
                                }
                                else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            });
                        }, res => {
                        })
                        break;
                    case 'Close':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var paramNo = data[0].Passway_No;
                        LAYER_OPEN_TYPE_0("确定关闸?", res => {
                            LAYER_LOADING("处理中...");
                            $.getJSON("ExcuteClose", { Passway_No: paramNo }, function (json) {
                                //json.msg 不包含“请取消道闸常开”
                                if (json.success && json.msg.indexOf("请取消道闸常开") < 0) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 });
                                }
                                else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            });
                        }, res => {
                        })
                        break;
                    case 'GateSwitch':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var paramNo = data[0].Passway_No;
                        layer.open({
                            title: "消息提示",
                            type: 0,
                            btn: ["确定", "取消"],
                            shade: 0,
                            area: getIframeArea(["280px", "220px"]),
                            content: "确定操作?<br/><t style='color: red;'>重要提示：道闸常开后,再次点击道闸常开可以取消道闸常开</t>",
                            yes: function () {
                                LAYER_LOADING("处理中...");
                                $.getJSON("ExcuteGate", { Passway_No: paramNo }, function (json) {
                                    if (json.success) {
                                        layer.msg(json.msg, { icon: 1, time: 1500 });
                                    }
                                    else {
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                    }
                                });
                            }
                        })
                        break;
                    case 'UnBind':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var paramNo = data[0].Passway_No;
                        layer.open({
                            title: "消息提示",
                            type: 0,
                            btn: ["确定", "取消"],
                            shade: 0,
                            area: getIframeArea(["280px", "220px"]),
                            content: "确定执行清空黑白名单操作?",
                            yes: function () {
                                LAYER_LOADING("处理中...");
                                $.getJSON("ClearDeviceOrder", { Passway_No: paramNo }, function (json) {
                                    if (json.success) {
                                        layer.msg("操作成功", { icon: 1, time: 1500 });
                                    }
                                    else {
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageIndex); } });
                                    }
                                });
                            }
                        })
                        break;
                    case 'Synchronous':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var paramNo = data[0].Passway_No;
                        LAYER_LOADING("处理中...");
                        $.getJSON("/Passway/SyncDeviceOrder", { Passway_No: paramNo, act: "0" }, function (json) {
                            if (json.success) {
                                var l = layer.open({
                                    title: "消息提示",
                                    type: 0,
                                    btn: ["确定", "取消"],
                                    shade: 0,
                                    area: getIframeArea(["280px", "280px"]),
                                    content: "有 " + json.data + " 个白名单，确定让岗亭执行同步白名单操作?<br/><t style='color: red;'>重要提示：请确保相机设备正常连接，且该操作会占用较多服务器资源，请在车流量较少时操作</t>",
                                    yes: function () {
                                        LAYER_LOADING("处理中...");
                                        $.post("/Passway/SyncDeviceOrder", { Passway_No: paramNo, act: "1" }, function (json) {
                                            if (json.success) {
                                                //layer.msg("同步指令成功下发到岗亭", { icon: 1, time: 1500 });
                                                layer.closeAll();
                                                layer.open({
                                                    type: 2,
                                                    title: false,
                                                    closeBtn: 0,
                                                    skin: "diy-class-layer",
                                                    area: ["220px", "20px"],
                                                    content: "/ZProgress/Index"
                                                });
                                                //layer.close(l);
                                            } else {
                                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageIndex); } });
                                            }
                                        }, "json");
                                    }
                                })

                            }
                            else {
                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageIndex); } });
                            }
                        });



                        break;
                    case 'SyncInOut':
                        if (data.length === 0) { layer.msg("请选择"); return; }
                        LAYER_LOADING("处理中...");
                        var paramNos = [];  //车道编号
                        $.each(data, function (k, v) { paramNos.push(v.Passway_No); });   
                        $.getJSON("/Passway/SyncInOut", { Nos: JSON.stringify(paramNos) }, function (json) {
                            if (json.success) {
                                layer.msg("同步指令成功下发到岗亭", { icon: 1, time: 1500 });
                            }
                            else {
                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageIndex); } });
                            }
                        });
                        break;
                };
            });

            tb_row_checkbox(table);

            pager.init();
        });

        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                //this.bindPower();
                this.bindSelect();
                this.bindData(1);
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllSentryHost", {}, function (json) {
                    if (json.success) {
                        var data = json.data.sentryHosts;
                        $("#Passway_SentryHostNo").append($("#tmplhost").tmpl(data));
                        layui.form.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");
            },
            bindData: function (index, noclose) {
                if (!noclose) layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Passway/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });

            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });
            },
            bindEvent: function () {
                $("#Search").unbind("click").click(function () {
                    pager.bindData(pager.pageIndex);
                });
            }
        }

    </script>
</body>

</html>
