﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开通城市服务</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        table {
            margin-bottom: 70px !important;
        }


        .fix-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            height: 50px;
            line-height: 50px;
            background-color: #fff;
            width: 100%;
            box-shadow: 0 -1px 2px 0 rgb(0 0 0 / 5%);
            padding: 5px 40px;
        }
    </style>
</head>

<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content layui-form">
        <div id="verifyCheck">
            <table class="layui-table">
                <thead>
                    <tr>
                        <th width="25%">参数</th>
                        <th width="35%">值</th>
                        <th width="40%">说明</th>
                    </tr>
                </thead>
                <tbody id="cityserver_field">
                </tbody>
            </table>

            <div class="layui-row fix-footer">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i>
                    <t>开通上报</t>
                </button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i>
                    <t>取消</t>
                </button>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplhost">
        <option value="${CityServer_No}">${CityServer_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?3" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>

    <script>
        var CityServer_No = $.getUrlParam("CityServer_No");
        var index = parent.layer.getFrameIndex(window.name);


        /**
         * 格式化数字，限制输入的数字长度不超过指定的最大长度
         * input - 输入的数字字符串
         * maxLength - 最大长度限制
         * defaultValue - 默认值
         * flotNum -小数点位数
         */
        function formatNumber(input, maxLength, defaultValue,flotNum) {
            // 去除非数字和小数点的字符，保留第一个小数点
            input = input.replace(/[^\d.]/g, '').replace(/^\./g, '').replace(/\.(?=.*\.)/g, '');
            //如果小数点为0，则不允许输入小数点
            if (flotNum == 0) {
                input = input.replace(/\./g, '');
            }

            // 如果输入为空，恢复默认值
            if (input === '') {
                input = defaultValue;
            }
            // 如果输入的数字长度超过了最大长度，则截取前 maxLength 位数字
            if (input.length > maxLength) {
                input = input.substring(0, maxLength);
            }
            //如果小数点大于0，则格式化小数点位数
            if (flotNum > 0) {
                input = parseFloat(input).toFixed(flotNum);
            }
            else {
                input = parseInt(input);
            }
            return input;
        }

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {
                $.getJSON("/CityServer/GetCityServer", { CityServer_No: CityServer_No }, function (json) {
                    var field_list = "";
                    if (json.Success) {
                        var pt = $.parseJSON(json.Data.CityServer_ParamTemplate);
                        if (pt) {
                            var len = Object.keys(pt).length;
                            for (var key in pt) {
                                var paramFieldName = pt[key].ParamFieldName;
                                var paramName = pt[key].ParamName;
                                var paramDesc = pt[key].ParamDesc;
                                var paramType = pt[key].ParamType;
                                var paramValue = pt[key].ParamValue;
                                var maxLength = pt[key].MaxLength;
                                var defaultValue = pt[key].ParamValue;
                                var arrayParams = pt[key].ArrayParams;
                                var tmpl = "<tr>";
                                tmpl += "<td>" + paramName + "</td>";
                                if (paramType == "String") {
                                    //记得添加最大长度限制
                                    tmpl += `
                                        <td>
                                            <input type="text" class="layui-input v-null" id="${paramFieldName}" name="${paramFieldName}" maxlength="${maxLength}" value="${defaultValue}" />
                                        </td>`;

                                } else if (paramType == "Int") {
                                    tmpl += `
                                        <td>
                                            <input type="text" class="layui-input v-null" id="${paramFieldName}" value="${defaultValue}" name="${paramFieldName}" maxlength="${maxLength}" onblur="value=formatNumber(this.value,'${maxLength}','${defaultValue}',0)" />
                                        </td>`;
                                } else if (paramType == "Double") {
                                    tmpl += `
                                        <td>
                                            <input type="text" class="layui-input v-null" id="${paramFieldName}" value="${defaultValue}" name="${paramFieldName}" maxlength="${maxLength}" onblur="value=formatNumber(this.value,'${maxLength}','${defaultValue}',6)" />
                                        </td>`;
                                } else if (paramType == "Bool") {
                                    //使用下拉框
                                    tmpl += `
                                        <td>
                                            <select class="layui-select" id="${paramFieldName}" name="${paramFieldName}">
                                                <option value="0" ${defaultValue == "0" ? "selected" : ""}>禁用</option>
                                                <option value="1" ${defaultValue == "1" ? "selected" : ""}>启用</option>
                                            </select>
                                        </td>`;
                                } else if (paramType == "Array") {
                                    //使用下拉框
                                    //判断arrayParams数据绑定到下拉框
                                    var arrayParamsHtml = "";
                                    for (var i = 0; i < arrayParams.length; i++) {
                                        arrayParamsHtml += `<option value="${arrayParams[i].Key}" ${arrayParams[i].Key == defaultValue ? "selected" : ""}>${arrayParams[i].Value}</option>`;
                                    }
                                    tmpl += `
                                        <td>
                                            <select class="layui-select" id="${paramFieldName}" name="${paramFieldName}">
                                                ${arrayParamsHtml}
                                            </select>
                                        </td>`;
                                }

                                tmpl += "<td>" + paramDesc + "</td>";
                                tmpl += "</tr>";
                                field_list += tmpl;
                            }
                        }
                    }
                    $("#cityserver_field").html(field_list);
                });
            },
            //数据绑定
            bindData: function () {
                $.getJSON("/CityServer/GetCityParking", { CityServer_No: CityServer_No }, function (json) {
                    if (json.Success) {
                        $("#verifyCheck").fillForm($.parseJSON(json.Data.CityParking_Param), function (data) { });
                    }
                });
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    if (CityServer_No == "") { layer.msg("请选择城市服务平台"); return; }
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.CityServer_No = CityServer_No;
                        return data;
                    });

                    $("#Save").attr("disabled", true);

                    $.getJSON("/CityServer/Open", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.Success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                window.parent.pager.bindData(1);
                            });
                        } else {
                            layer.msg(json.Message, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });

                });

            },
        };

        layui.use('form', function () {
            pager.init();
            var form = layui.form;
            form.render();
        })
    </script>
</body>

</html>
