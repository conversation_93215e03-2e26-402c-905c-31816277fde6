﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车道列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input{width:182px;}
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-elem-quote" style="background-color:#fff;padding:5px 10px;color:red;">
                    车道绑定仅对BS岗亭生效;绑定后在BS岗亭端登录账号只会显示已绑定的车道;<br />
                    若未绑定任何车道,则表示当前账号管理所有车道(暂不支持账号不管理车道)
                </div>
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>绑定</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>解绑</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>    
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var paramID = $.getUrlParam("Admins_ID");
        var comtable = null;
        layui.use(['table', 'form'], function () {
            var table = layui.table;

            //var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            var conditionParam = { Admins_ID: paramID  };

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Passway_No', title: '车道编号', hide: true  }
                , { field: 'Passway_Name', title: '车道名称' }
                , {
                    field: 'AreaPassways', title: '区域', templet: function (d) {
                        var htm = [];
                        var out = '<t style="color:green;">[出]</t>';
                        var inr = '<t style="color:blue;">[入]</t>';
                        d.AreaPassways.forEach(function (item, index) {
                            if (item.PasswayLink_GateType == 0)
                                htm[htm.length] = out + item.ParkArea_Name;
                            else
                                htm[htm.length] = inr + item.ParkArea_Name;
                        });
                        return htm.join("、");
                    }
                }
                , { field: 'Passway_SentryHostNo', title: '岗亭编码', hide: true }
                , { field: 'SentryHost_Name', title: '岗亭名称' }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Admins/GetPasswayList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "绑定车道",
                            content: '/Admins/PasswayBind?Admins_ID=' + paramID,
                            area: getIframeArea(['850px', '550px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var jsonNo = [];
                        data.forEach((item, index) => { jsonNo.push(item.Passway_No); });
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定解绑车道?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/Admins/UnBindPassway", { Admins_ID: paramID, jsonNo: JSON.stringify(jsonNo) }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                            pager.bindData(pager.pageIndex);
                                        });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_checkbox();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {

            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = { Admins_ID: paramID };
                comtable.reload({
                    url: '/Admins/GetPasswayList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
