﻿
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>执行进度</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <style>
        .everybox { }
        .zhead { padding: 5px; font-size: 13px; }
        .faillist { max-height: 100px; overflow: auto; margin: 10px 0; padding: 3px 10px; box-shadow: 0 0 3px #999; border-radius: 5px; }
        .faillist::-webkit-scrollbar { /*滚动条整体样式*/ width: 4px; /*高宽分别对应横竖滚动条的尺寸*/ height: 1px; }
        .faillist::-webkit-scrollbar-thumb { /*滚动条里面小方块*/ border-radius: 4px; -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2); background: #0aa2a7; }
        .faillist::-webkit-scrollbar-track { /*滚动条里面轨道*/ -webkit-box-shadow: inset 0 0 4px rgba(0,0,0,0.2); border-radius: 4px; background: #EDEDED; }
    </style>
</head>
<body>
    <div class="everybox">
        <div class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="demo">
            <div class="layui-progress-bar layui-bg-red" lay-percent="0%"></div>
        </div>
    </div>
    <script type="text/javascript">
        var skin = $.getUrlParam("skin");   //不传或=1则显示总数
        var ok = $.getUrlParam("ok");       //不传或=1则显示成功数
        var fail = $.getUrlParam("fail");   //不传或=1则显示失败数
        layui.use(["element", "layer"], function () {
            var element = layui.element;
            var layer = layui.layer;

            var timer = setInterval(function () {
                $.post("/ZProgress/GetProgress", {}, function (json) {
                    if (json.Success) {
                        var value = 0;
                        if (json.Data.State == 1) {
                            value = 100;
                            clearInterval(timer);
                        } else if (json.Data.State == 2) {
                            value = 100;
                            clearInterval(timer);
                        } else {
                            value = ((parseFloat(json.Data.Index) / parseFloat(json.Data.Total)) * 100);
                        }

                        var tip = value.toFixed(1) + "%";
                        element.progress('demo', tip);
                        if (value == 100 || json.Data.Index == json.Data.Total) {
                            clearInterval(timer);
                            var content = "";
                            if (!skin || skin == "1") {
                                content += "总数：" + json.Data.Total;
                                if (!ok || ok == "1") {
                                    content += "<BR />";
                                    content += "成功：" + json.Data.Success;
                                }
                                if (!fail || fail == "1") {
                                    content += "<BR />";
                                    content += "失败：" + json.Data.Fail;
                                }
                            } else {
                                content = "批量操作完成";
                            }

                            var icon = parseInt(json.Data.Fail) > 0 ? 0 : 1;
                            window.parent.layer.open({
                                type: 0,
                                title: "执行完毕",
                                content: content,
                                btn: ["我知道了"],
                                btnAlign: "c",
                                icon: icon,
                                time: 0,
                                end: function () {
                                    try {
                                        window.parent.layer.closeAll();
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    } catch (e) {

                                    }
                                }
                            });
                        }
                    }
                });
            }, 500);
        });
    </script>
</body>
</html>
