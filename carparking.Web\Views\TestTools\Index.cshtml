﻿<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>系统测试</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <link href="~/Static/css/plugins/iCheck/custom.css" rel="stylesheet">

    <style>
        .reSend { background-color: #f8ac59 !important; border-color: #f8ac59 !important; }
        .layui-fluid { padding-left: 10px !important; }
        .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group { margin-left: 30px !important; height: 47px; }

        input#carno { background-color: rgb(249 242 242 / 100%) !important; }
        input#carno:disabled { background-color: rgb(0 0 0) !important; color: #fff; }
        input#time:disabled { background-color: rgb(0 0 0) !important; color: #fff; }

        .layui-form-item .layui-form-checkbox { margin-top: -1px !important; }
        .layui-form-checkbox { height: 39px !important; line-height: 39px !important; }
        .layui-form-checkbox i { position: absolute; right: 0; top: 0; width: 30px; height: 36px; border: 1px solid #d7eaeb; border-left: none; border-radius: 0 2px 2px 0; font-size: 20px; text-align: center; }
        .descspan { position: absolute; right: 0; top: 0; width: 30px; padding-top: 5px; height: 36px; border: 0px solid #d7eaeb; border-left: none; border-radius: 0 2px 2px 0; font-size: 20px; text-align: center; }
        .layui-card-header .layui-icon { top: 30%; margin-top: -12px; height: 37px; right: 0px !important; }
        h1 { margin-left: 50px !important; }
        h2 { margin-left: 60px !important; }
        .desc { font-size: 12px; margin-left: 60px; color: #898484; width: 100%; }

        .landIn { display: flex; flex-wrap: wrap; font-family: Lora, serif; white-space: pre; }
        .landIn span { animation: landIn 0.8s ease-out both; }
        @@keyframes landIn {
            from { opacity: 0; transform: translateY(-20%); }

            to { opacity: 1; transform: translateY(0); }
        }
        .layui-fluid { margin-left: 15px; }
    </style>
</head>
<body class="gray-bg">
    <div class="layui-fluid animated fadeInRight">
        <div class="row">
            <div class="col-sm-12">
                <!--系统测试-->
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <ol class="col-lg-11 breadcrumb">
                            <li>
                                <h1 class="">系统测试 <t style="font-size:13px;">[测试配置]</t></h1><div class="desc landIn"><div>1、修改appsettings.json文件，增加TCP服务端测试端口的配置；2、配置好车道、相机、计费等信息（目前只会自动向外场的车道入场出场[仅支持外场车道，不要做内外场关联]，可配多车道测试）；3、系统状态必须正常后，才能启动测试;4、点击【启动】按钮，岗亭则会自动车辆入场、中间件下发缴费信息、出场</div></div>
                            </li>
                        </ol>
                        <div class="ibox-tools">
                            <a class="dropdown-toggle" data-toggle="dropdown" href="#">
                                <i class="fa fa-wrench"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-user">
                                <li>
                                    <a href="#" onclick="location.reload()"><i class="fa fa-refresh animated rotateIn"></i>&nbsp; 重新载入</a>
                                </li>
                            </ul>
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                        </div>
                    </div>

                </div>
                <!--系统状态-->
                <div class="ibox-content">
                    <div class="form-horizontal" id="verifyCheck">
                        <div style="position: absolute;left: 155px;z-index: 9999;padding-top:10px;" class="desc">提示：测试模式下，岗亭不再接收相机的车牌识别回调消息</div>
                        <div class="form-group">
                            <h2>系统状态：</h2>
                            <label class=" col-sm-2 control-label">测试模式：<t style="color:red;">@(carparking.BLL.Cache.AppBasicCache.GetParking.Parking_TestMode?.ToString() == "1" ? "已启用" : "已禁用")</t> </label>
                            <label class=" col-sm-2 control-label">云平台：<t style="color:red;">@(carparking.BLL.Cache.AppBasicCache.GetParking.Parking_EnableNet == 1 ? "已启用" : "已禁用")</t> </label>
                            <label class=" col-sm-3 control-label">工具服务端：<t style="color:red;">@carparking.Library.TestServerChannel.StatusMsg</t> </label>
                            <label class=" col-sm-3 control-label">中间件客户端：<t style="color:red;" id="ConnectionMsg">@carparking.Library.TestServerChannel.ConnectionMsg</t> </label>
                        </div>
                    </div>
                </div>
                <!--软件自动车牌识别+入场+弹窗缴费+出场-->
                <div class="ibox-content">
                    <div class="form-horizontal layui-tab-content layui-form" id="verifyCheck">

                        <div class="form-group">
                            <h2>场景1、[软件自动车牌识别+入场+弹窗缴费+出场] 无需相机支持：</h2>
                            <div style="" class="desc">提示：点击【启动】则循环向岗亭发送车辆进出场消息，在WEB端日志【LPRHandle.log】可查看失败信息；点击【关闭】，则停止。</div>
                            <div class="layui-row">
                                <div style="width: 100%; height: 50px; display: flex; margin-left: 50px; padding: 10px; ">
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="incar" id="incar" title="自动入场" checked>
                                        </span>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="paycar" id="paycar" title="自动平台缴费" checked>
                                        </span>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="outcar" id="outcar" title="自动出场" checked>
                                        </span>
                                    </div>
                                    <span class="input-group-btn">
                                        <button type="button" id="btnbegin" class="btn btn-outline btn-primary btn_map" style="height: 5vh;"><i class="fa fa-level-up"></i> &nbsp;<t class="taskact">@(carparking.Library.Test.Setting.TaskTestInOutStatus ? "关闭" : "启动")</t></button>
                                    </span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <!--软件自动检测弹窗缴费-->
                <div class="ibox-content">
                    <div class="form-horizontal layui-tab-content layui-form" id="verifyCheck">

                        <div class="form-group">
                            <h2>场景2、[软件自动检测弹窗缴费] 需相机支持：</h2>
                            <div style="" class="desc">提示：只支持外场区域，只支持按次计费。点击【启动】自动检测弹窗，自动缴费，在WEB端日志【LPRHandle.log】可查看失败信息；点击【关闭】，则停止。</div>
                            <div class="layui-row">
                                <div style="width: 100%; height: 50px; display: flex; margin-left: 50px; padding: 10px; ">
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="offlinepay" id="offlinepay" title="线下现金" checked>
                                        </span>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="onlinepay" id="onlinepay" title="平台缴费">
                                        </span>
                                    </div>
                                    <span class="input-group-btn">
                                        <button type="button" id="btnautopay" class="btn btn-outline btn-primary btn_map" style="height: 5vh;"><i class="fa fa-level-up"></i> &nbsp;<t class="taskautopay">@(carparking.Library.Test.Setting.TaskTestAutoPayStatus ? "关闭" : "启动")</t></button>
                                    </span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <!--软件自动车牌识别+入场+弹窗缴费+出场-->
                <div class="ibox-content">
                    <div class="form-horizontal layui-tab-content layui-form" id="verifyCheck">

                        <div class="form-group">
                            <h2>场景3、[软件自动定时2秒识别车牌随机入口车道入场+随机出口车道+随机时间出场+弹窗自动缴费] 无需相机支持：</h2>
                            <div style="" class="desc">提示：随机时长为30秒到1小时范围之间进出内外场，可设置按次或按计时单位的周期计费。点击【启动】则循环向岗亭发送车辆进出场消息，在WEB端日志【LPRHandle.log】可查看失败信息；点击【关闭】，则停止。</div>
                            <div class="layui-row">
                                <div style="width: 100%; height: 50px; display: flex; margin-left: 50px; padding: 10px; ">
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="incar" id="incar" title="自动入场" checked>
                                        </span>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="paycar" id="paycar" title="自动平台缴费" checked>
                                        </span>
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-btn" style="vertical-align: top;">
                                            <input type="checkbox" name="outcar" id="outcar" title="自动出场" checked>
                                        </span>
                                    </div>
                                    <span class="input-group-btn">
                                        <button type="button" id="btnbeginuntime" class="btn btn-outline btn-primary btn_map" style="height: 5vh;"><i class="fa fa-level-up"></i> &nbsp;<t class="taskactuntime">@(carparking.Library.Test.Setting.TaskTestInOutUnTime ? "关闭" : "启动")</t></button>
                                    </span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <!--数据清理重设触发条件-->
                <div class="ibox-content">
                    <div class="form-horizontal layui-tab-content layui-form" id="verifyCheck">

                        <div class="form-group">
                            <h2>场景4、[盒子数据清理]-重设触发条件：</h2>
                            <div style="" class="desc">提示：自定义盒子数据清理的触发条件，超过多少条数据，凌晨就会触发清理。</div>
                            <div class="layui-row layui-col-space30">
                                <div class="layui-col-md12">
                                    <div class="layui-card">
                                        <div class="layui-card-body">
                                            <div class="layui-row" style="margin-bottom:5px;">

                                                <div class="col-xs-1 control-label"><label>清理出场记录超过：</label></div>
                                                <div class="layui-col-xs2 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="DelExitRecord" name="DelExitRecord" maxlength="9" value="@carparking.Library.Test.Setting.DelExitRecord" />
                                                        <span class="input-group-btn descspan">
                                                            条
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-xs-1 control-label"><label>清理缴费记录超过：</label></div>
                                                <div class="layui-col-xs2 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="DelPayRecord" name="DelPayRecord" maxlength="9" value="@carparking.Library.Test.Setting.DelPayRecord" />
                                                        <span class="input-group-btn descspan">
                                                            条
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-xs-1 control-label"><label>清理优惠券记录：</label></div>
                                                <div class="layui-col-xs2 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="DelCouponRecord" name="DelCouponRecord" maxlength="9" value="@carparking.Library.Test.Setting.DelCouponRecord" />
                                                        <span class="input-group-btn descspan">
                                                            条
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-row" style="margin-bottom:5px;">
                                                <div class="col-xs-1 control-label"><label>清理开闸记录超过：</label></div>
                                                <div class="layui-col-xs2 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="DelOpenGateRecord" name="DelOpenGateRecord" maxlength="9" value="@carparking.Library.Test.Setting.DelOpenGateRecord" />
                                                        <span class="input-group-btn descspan">
                                                            条
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-xs-1 control-label"><label>清理交班记录超过：</label></div>
                                                <div class="layui-col-xs2 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="DelShiftRecord" name="DelShiftRecord" maxlength="9" value="@carparking.Library.Test.Setting.DelShiftRecord" />
                                                        <span class="input-group-btn descspan">
                                                            条
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-xs-1 control-label"><label>清理倒车记录超过：</label></div>
                                                <div class="layui-col-xs2 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="DelReverseRecord" name="DelReverseRecord" maxlength="9" value="@carparking.Library.Test.Setting.DelReverseRecord" />
                                                        <span class="input-group-btn descspan">
                                                            条
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="layui-row">
                                                <div style="white-space: nowrap;margin-left:30px;" class="desc">清理出场记录超过：1、硬盘空间剩余超过10G，但检测到停车订单超过当前设置的条数，会触发清理；2、停车订单超过当前设置的条数，会清理最早一天的订单数据；</div>
                                            </div>

                                            <div class="layui-row" style="margin-top:20px;">
                                                <div class="layui-col-sm9" style="text-align:center;">
                                                    <button class="btn btn-primary" id="DelRecordSave"><i class="fa fa-check"></i> <t>保存清理数据设置</t></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4"></div>
                        </div>

                    </div>
                </div>
                <!--中间件消息-->
                <div class="ibox-content">
                    <div class="form-horizontal" id="verifyCheck">
                        <div style="position: absolute;left: 255px;z-index: 9999;padding-top:5px;" class="desc">提示：手动编写协议(Json格式)，模拟云平台向中间件发送指令</div>
                        <div class="form-group">
                            <h2>场景5、[中间件消息]：</h2>
                            <label class="col-sm-1 control-label">发送消息：</label>
                            <div class="col-sm-7">
                                <div class="input-group ">
                                    <input type="text" id="MidMsg" name="MidMsg" value="{}" class="form-control required">
                                    <span class="input-group-btn">
                                        <button type="button" id="btnMidMsg" class="btn btn-outline btn-primary btn_map"><i class="fa fa-level-up"></i> &nbsp;发送</button>
                                    </span>
                                </div>
                                <label class="focus valid error " id="MidMsgCheck" style="font-size:11px;">示例：{"version":"V1.9","actionName":"OnlinePayment","key":"e2173phk","payOrderNo":"OT202402271012573376404ktn49ura","orderNo":"1709026768636701-P23H12","paidAmount":"1.00","receAmount":"1.00","payTime":"2024-02-27 10:13:03","payType":"79009","couponKey":"","payScene":2,"couponList":[]}</label>

                            </div>

                            <div class="col-sm-10">
                                <label class=" col-sm-1 control-label" style="margin-right:14px;">接收消息：</label>
                                <div id="tcpmsg" style="width:80%;height:60px;display: flex;overflow:auto;"></div>

                            </div>
                        </div>

                    </div>
                </div>
                <!--车牌识别-->
                <div class="ibox-content">
                    <div class="form-horizontal layui-tab-content layui-form" id="verifyCheck">
                        <div style="position: absolute;left: 245px;z-index: 9999;padding-top:10px;" class="desc">提示：点击【单次识别】则向岗亭发送车牌识别消息</div>
                        <div class="form-group">
                            <h2>场景6、[车牌识别]：</h2>
                            <div class="layui-row layui-col-space30">
                                <div class="layui-col-md12">
                                    <div class="layui-card">
                                        <div class="layui-card-body">
                                            <div class="layui-row">
                                                <div class="col-sm-1 control-label"><label>车牌号码：</label></div>
                                                <div class="layui-col-sm3 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="carno" name="carno" maxlength="18" />
                                                        <span class="input-group-btn" style="vertical-align: top;">
                                                            <input type="checkbox" name="carnoAuto" id="carnoAuto" title="自动">
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-sm-1 control-label"><label>识别时间：</label></div>
                                                <div class="layui-col-sm3 edit-ipt-ban">
                                                    <div class="input-group">
                                                        <input type="text" class="layui-input" id="time" name="time" value="@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")" disabled />
                                                        <span class="input-group-btn" style="vertical-align: top;">
                                                            <input type="checkbox" name="timeAuto" id="timeAuto" title="自动" checked>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="layui-row" style="margin-top:20px;">
                                                <div class="col-sm-1 control-label"><label>识别车道：</label></div>
                                                <div class="layui-col-sm3 edit-ipt-ban">
                                                    <select data-placeholder="识别车道" class="form-control chosen-select " id="passwayno" name="passwayno" lay-search style="max-height:300px;">
                                                        <option value="">识别车道</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="layui-row" style="margin-top:20px;">
                                                <div class="layui-col-sm9" style="text-align:center;">
                                                    <button class="btn btn-primary" id="BathSave"><i class="fa fa-check"></i> <t>批量识别</t></button>
                                                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>单次识别</t></button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4"></div>
                        </div>

                    </div>
                </div>

                <!--岗亭监测-->
                <div class="ibox-content">
                    <div class="form-horizontal layui-tab-content layui-form" id="verifyCheck">

                        <div class="form-group">
                            <h2>岗亭监测：</h2>
                            <div style="" class="desc">提示：启动模拟则会令监测程序得不到岗亭任务结果响应（不会影响岗亭正常运行），判定未岗亭线程异常，三次异常则重启服务。</div>
                            <div class="layui-row">
                                <div style="width: 100%; height: 50px; display: flex; margin-left: 50px; padding: 10px; ">
                                    <span class="input-group-btn">
                                        <button type="button" id="btnThreadPoolMonitor" class="btn btn-outline btn-primary btn_map" style="height: 5vh;"><i class="fa fa-level-up"></i> &nbsp;<t class="threadPoolMonitor">@(carparking.Common.ThreadPoolMonitor.TestMode == 0 ? "启动模拟" : "关闭模拟")</t></button>
                                    </span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v20230620" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>

        function titleShow() {
            var landInTexts = document.querySelectorAll(".landIn");
            landInTexts.forEach(function (landInText) {
                var letters = landInText.textContent.split("");
                landInText.textContent = "";
                letters.forEach(function (letter, i) {
                    var span = document.createElement("span");
                    span.textContent = letter;
                    span.style.animationDelay = i * 0.05 + "s";
                    landInText.append(span);
                });
            });
        }
        titleShow();

        sessionStorage.setItem('ctrlPressed', 'false');

        var carnoAuto = false;
        var timeAuto = true;

        var incar = true;
        var outcar = true;
        var paycar = true;
        var offlinepay = true;
        var onlinepay = false;

        s_carno_picker.init("carno", function (text, carno) {
            if (s_carno_picker.eleid == "carno") {
                $("#carno").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['element', 'table', 'form', 'laydate'], function () {

            if (typeof window.parent.global == "undefined") {
                window.location.href = 'about:blank';
                return;
            }

            var table = layui.table;
            layuiForm = layui.form;
            _DATE.bind(layui.laydate, ["time"], { type: "datetime", range: false });


            layuiForm.on('checkbox', function (obj) {
                if (obj.elem.checked == true) {
                    if (obj.elem.id == "carnoAuto") {
                        carnoAuto = true;
                        $("#carno").attr("disabled", true);
                    }
                    if (obj.elem.id == "timeAuto") {
                        timeAuto = true;
                        $("#time").attr("disabled", true);
                    }
                    if (obj.elem.id == "incar") {
                        incar = true;
                    }
                    if (obj.elem.id == "outcar") {
                        outcar = true;
                    }
                    if (obj.elem.id == "paycar") {
                        paycar = true;
                    }
                    if (obj.elem.id == "offlinepay") {
                        offlinepay = true;
                    }
                    if (obj.elem.id == "onlinepay") {
                        onlinepay = true;
                    }
                } else {
                    if (obj.elem.id == "carnoAuto") {
                        carnoAuto = false;
                        $("#carno").removeAttr("disabled");
                    }
                    if (obj.elem.id == "timeAuto") {
                        timeAuto = false;
                        $("#time").removeAttr("disabled");
                    }
                    if (obj.elem.id == "incar") {
                        incar = false;
                    }
                    if (obj.elem.id == "outcar") {
                        outcar = false;
                    }
                    if (obj.elem.id == "paycar") {
                        paycar = false;
                    }
                    if (obj.elem.id == "offlinepay") {
                        offlinepay = false;
                    }
                    if (obj.elem.id == "onlinepay") {
                        onlinepay = false;
                    }
                }
            });


            pager.init()
        });

        var TalkingInterval = null;

        var pager = {
            pageDetail: true,
            pageIndexCurrent: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindEvent: function () {

                $.post("/TestTools/SltPasswayList3", {}, function (json) {
                    if (json.success && json.data && json.data.length > 0) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#passwayno").append(option);
                        });
                        $("#passwayno").val(json.data[0].Passway_No);
                        layui.form.render("select");
                    } else {
                        layui.form.render("select");
                        timeOutLogin();
                    }
                }, "json");

                if (TalkingInterval != null) {
                    clearInterval(TalkingInterval);
                    TalkingInterval = null;
                }
                TalkingInterval = setInterval(() => {
                    try {
                        // $.getJSON("/TestTools/GetTcpMsg", {}, function (json) {
                        //     if (json.success) {
                        //         $("#tcpmsg").html(json.data);
                        //         $('#ConnectionMsg').val(json.msg);
                        //     }
                        // });

                    } catch (e) { console.log("获取TCP消息异常：" + e.message); }
                }, 5000);

                layui.form.render();

                //中间件消息
                $("#btnMidMsg").click(function () {
                    pager.MidMsg();
                });

                //软件自动车牌识别+入场+弹窗缴费+出场
                $("#btnbegin").click(function () {
                    pager.beginTest();
                });
                //软件自动车牌识别+入场+弹窗缴费+出场
                $("#btnbeginuntime").click(function () {
                    pager.beginUntimeTest();
                });

                $("#btnThreadPoolMonitor").click(function () {
                    pager.checkThreadPoolHealth();
                });

                //软件自动检测弹窗缴费
                $("#btnautopay").click(function () {
                    pager.beginTestAutoPay();
                });

                $("#Save").click(function () {

                    if (carnoAuto) {
                        $("#carno").val(getcity() + getarea() + getend5(5));
                    }

                    if (timeAuto) {
                        $("#time").val(new Date().Format("yyyy-MM-dd hh:mm:ss"));
                    }

                    var passwayno = $("#passwayno").val();
                    var carno = $("#carno").val();
                    var time = $("#time").val();
                    $("#Save").attr("disabled", true)
                    $.post("/TestTools/InCarByPassNo", { passwayno: passwayno, carno: carno, time: time }, function (json) {
                        if (json.success) {
                            layer.msg("识别成功", { icon: 1, time: 1500 }, function () { });
                        } else {
                            layer.alert(json.msg, { icon: 0 });
                        }
                        $("#Save").removeAttr("disabled");
                    }, "json").error(function () {
                        $("#Save").removeAttr("disabled");
                    });
                })


                //数据清理
                $("#DelRecordSave").click(function () {
                    $("#DelRecordSave").attr("disabled", true)
                    $.post("/TestTools/SetDelData", {
                        DelExitRecord: $("#DelExitRecord").val(),
                        DelPayRecord: $("#DelPayRecord").val(),
                        DelCouponRecord: $("#DelCouponRecord").val(),
                        DelOpenGateRecord: $("#DelOpenGateRecord").val(),
                        DelShiftRecord: $("#DelShiftRecord").val(),
                        DelReverseRecord: $("#DelReverseRecord").val()
                    }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () { });
                        } else {
                            layer.alert(json.msg, { icon: 0 });
                        }
                        $("#DelRecordSave").removeAttr("disabled");
                    }, "json").error(function () {
                        $("#DelRecordSave").removeAttr("disabled");
                    });
                })

                $("#BathSave").click(function () {
                    layer.prompt({
                        formType: 3,
                        value: '',
                        title: '请输入批量次数（间隔100毫秒执行1次，请注意谨慎使用）',
                        btn: ['确定', '取消'], //按钮，
                        btnAlign: ''
                    }, function (value, index) {
                        // 检查输入是否只包含数字
                        if (/^\d+$/.test(value)) {
                            for (let i = 0; i < value; i++) {
                                setTimeout(function () {
                                    if (carnoAuto) {
                                        $("#carno").val(getcity() + getarea() + getend5(5));
                                    }

                                    if (timeAuto) {
                                        $("#time").val(new Date().Format("yyyy-MM-dd hh:mm:ss"));
                                    }

                                    var passwayno = $("#passwayno").val();
                                    var carno = $("#carno").val();
                                    var time = $("#time").val();
                                    $("#Save").attr("disabled", true)
                                    $.post("/TestTools/InCarByPassNo", { passwayno: passwayno, carno: carno, time: time }, function (json) {
                                        if (json.success) {
                                            layer.msg("识别成功", { icon: 1, time: 1500 }, function () { });
                                        } else {
                                            layer.alert(json.msg, { icon: 0 });
                                        }
                                        $("#Save").removeAttr("disabled");
                                    }, "json").error(function () {
                                        $("#Save").removeAttr("disabled");
                                    });
                                }, 100 * i);
                            }
                            layer.close(index);
                        } else {
                            layer.msg('请输入数字');
                        }


                    });
                })
            },
            //MidMsg 发送中间件消息
            MidMsg: function () {
                var msg = $('#MidMsg').val();
                if (msg == '' || msg == null) {
                    layer.msg("请输入发送的消息");
                    return;
                }
                $('#btnMidMsg').attr("disabled", true);
                layer.msg('处理中...', { icon: 16, time: 0 });
                $.getJSON("/TestTools/MidMsg", { msg: msg }, function (json) {
                    $('#btnMidMsg').removeAttr("disabled");
                    if (json.success) {
                        $("#tcpmsg").html(json.msg);
                        layer.msg(json.msg, { icon: 1, time: 2000 }, function () { });
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] });
                    }
                });

            },
            beginTest: function () {

                if (!(incar || outcar || paycar)) {
                    layer.msg("请勾选", { icon: 0, time: 0, btn: ['确定'] });
                    return;
                }
                $('#btnbegin').attr("disabled", true);
                layer.msg('处理中...', { icon: 16, time: 0 });

                $.getJSON("/TestTools/InOutPayCar", { incar: incar, paycar: paycar, outcar: outcar }, function (json) {
                    $('#btnbegin').removeAttr("disabled");
                    if (json.success) {
                        debugger
                        var txt = $(".taskact").text();
                        if (txt == "启动") $(".taskact").text("关闭"); else $(".taskact").text("启动");
                        layer.msg(json.msg, { icon: 1, time: 2000 }, function () { });
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] });
                    }
                });

            },
            beginUntimeTest: function () {

                if (!(incar || outcar || paycar)) {
                    layer.msg("请勾选", { icon: 0, time: 0, btn: ['确定'] });
                    return;
                }
                $('#btnbeginuntime').attr("disabled", true);
                layer.msg('处理中...', { icon: 16, time: 0 });

                $.getJSON("/TestTools/InOutUntime", { incar: incar, paycar: paycar, outcar: outcar }, function (json) {
                    $('#btnbeginuntime').removeAttr("disabled");
                    if (json.success) {
                        debugger
                        var txt = $(".taskactuntime").text();
                        if (txt == "启动") $(".taskactuntime").text("关闭"); else $(".taskactuntime").text("启动");
                        layer.msg(json.msg, { icon: 1, time: 2000 }, function () { });
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] });
                    }
                });

            },
            beginTestAutoPay: function () {

                if (!(offlinepay || onlinepay)) {
                    layer.msg("请勾选缴费类型", { icon: 0, time: 0, btn: ['确定'] });
                    return;
                }

                $('#btnautopay').attr("disabled", true);
                layer.msg('处理中...', { icon: 16, time: 0 });

                $.getJSON("/TestTools/AutoPay", { offlinepay: offlinepay, onlinepay: onlinepay }, function (json) {
                    $('#btnautopay').removeAttr("disabled");
                    if (json.success) {
                        debugger
                        var txt = $(".taskautopay").text();
                        if (txt == "启动") $(".taskautopay").text("关闭"); else $(".taskautopay").text("启动");
                        layer.msg(json.msg, { icon: 1, time: 2000 }, function () { });
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] });
                    }
                });
            },
            checkThreadPoolHealth: function () {

                $('#btnThreadPoolMonitor').attr("disabled", true);
                layer.msg('处理中...', { icon: 16, time: 0 });
                var r = $(".threadPoolMonitor").text();
                $.getJSON("/TestTools/CheckThreadPoolHealth", { testMode: r == "启动模拟" ? 1 : 0 }, function (json) {
                    $('#btnThreadPoolMonitor').removeAttr("disabled");
                    if (json.success) {
                        var txt = $(".threadPoolMonitor").text();
                        if (txt == "启动模拟") $(".threadPoolMonitor").text("关闭模拟"); else $(".threadPoolMonitor").text("启动模拟");
                        layer.msg(json.msg, { icon: 1, time: 2000 }, function () { });
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] });
                    }
                });

            },
        };

    </script>

    <!--车牌生成-->
    <script type="text/javascript">
        var citys = ["京", "津", "晋", "湘", "赣", "沪", "渝", "冀", "辽", "吉", "黑", "苏", "浙", "皖"
            , "闽", "鲁", "豫", "鄂", "青", "粤", "琼", "川", "贵", "云", "陕", "甘", "藏", "桂", "蒙", "宁", "新", "港", "台",
            "澳", "电"
        ];
        //车牌号所在地区
        var area = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "J", "L", "M", "N", "O", "P", "Q"]
        //车牌号后5位
        var nums = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "J", "L", "M", "N", "O", "P", "Q", "R", "S",
            "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        ]
        //省份生成
        function getcity() {
            var id = Math.floor((Math.random() * 34));
            return citys[id];
        }
        //地区生成
        function getarea() {
            var id = Math.floor((Math.random() * 17));
            return area[id];
        }
        //车牌后五位
        function getend5(n) {
            var res = "";
            for (var i = 0; i < n; i++) {
                var id = Math.floor((Math.random() * 36));
                res += nums[id]
            }
            return res;
        }

        $("#carno").val(getcity() + getarea() + getend5(5));
    </script>
</body>
</html>