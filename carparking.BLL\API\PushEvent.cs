﻿using carparking.BLL.Cache;
using carparking.Common;
using carparking.Config;
using carparking.Model;
using carparking.Model.API;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HSSF.Record.Chart;
using Org.BouncyCastle.Asn1.Cmp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace carparking.BLL
{
    /// <summary>
    /// 推送消息到云平台
    /// </summary>
    public class PushEvent
    {
        DAL.PushEvent dal = new DAL.PushEvent();

        /// <summary>
        /// 是否需要连接云平台
        /// </summary>
        public static bool IsConnectCloudPark = true;

        #region 数据操作

        public static PushEvent GetInstance(DateTime time)
        {
            return new PushEvent(time);
        }

        public PushEvent(DateTime time)
        {
            dal = new DAL.PushEvent(time);
        }

        public int Add(Model.API.PushEvent model)
        {
            return dal.Add(model);
        }

        public int UpdateByModel(Model.API.PushEvent model, bool isOnlyUpdate = false)
        {
            return dal.UpdateByModel(model, isOnlyUpdate);
        }

        /// <summary>
        /// 根据条件更新指定字段
        /// </summary>
        /// <param name="UpdateStr">更新的字段</param>
        /// <param name="strWhere">更新条件</param>
        /// <returns></returns>
        public int UpdateByWhere(string UpdateStr, string strWhere)
        {
            return dal.UpdateByWhere(UpdateStr, strWhere);
        }

        public Model.API.PushEvent GetEntity(int? PushEvent_ID)
        {
            return dal.GetEntity(PushEvent_ID);
        }

        public Model.API.PushEvent GetEntity(string PushEvent_No)
        {
            return dal.GetEntity(PushEvent_No);
        }

        public Model.API.PushEvent GetEntity(string showFields, string selectWhere)
        {
            return dal.GetEntity(showFields, selectWhere);
        }

        public List<Model.API.PushEvent> GetAllEntity(string showFields, string selectWhere, object parameters = null)
        {
            return dal.GetAllEntity(showFields, selectWhere, parameters);
        }

        public List<Model.API.PushEvent> GetList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord, object parameters = null)
        {
            return dal.GetList(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters);
        }

        public int Insert(List<Model.API.PushEvent> models)
        {
            return dal.Insert(models);
        }


        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="models">数据实体集合</param>
        /// <returns></returns>
        public int UpadateModels(List<Model.API.PushEvent> models, DateTime dateTime = default)
        {
            return dal.UpadateModels(models);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <returns></returns>
        public int DeleteDatas(DateTime dateTime)
        {
            return dal.DeleteDatas(dateTime);
        }

        #endregion

        #region 保存到数据库，不推送消息到云平台

        #region 上传岗亭&车道信息

        /// <summary>
        /// 上传岗亭&车道信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="host_no"></param>
        /// <returns></returns>
        public static object SentryBoxSend(string parkKey, string host_no)
        {
            if (!IsConnectCloudPark) return null;
            if (string.IsNullOrWhiteSpace(host_no)) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                //Model.SentryHost host = BLL.SentryHost.GetEntity(host_no);
                return SentryBoxSend(parkKey, AppBasicCache.GetSentryHost());
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 上传岗亭&车道信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="host"></param>
        /// <returns></returns>
        public static object SentryBoxSend(string parkKey, Model.SentryHost host)
        {
            if (!IsConnectCloudPark) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                List<Model.Passway> passways = BLL.Passway._GetAllEntity(new Model.Passway(), "*", $"Passway_SentryHostNo='{host?.SentryHost_No}'");
                List<Model.PasswayLink> passwayLinks = null;
                List<Model.Device> devices = null;
                if (passways != null && passways.Count > 0)
                {
                    List<string> passwayNoList = passways.Select(x => x.Passway_No).ToList();
                    passwayLinks = BLL.PasswayLink._GetAllEntity(new Model.PasswayLink(), "*", $"PasswayLink_PasswayNo in ('{string.Join("','", passwayNoList)}')");
                    devices = BLL.Device._GetAllEntity(new Model.Device(), "*", $"Device_PasswayNo in ('{string.Join("','", passwayNoList)}')");
                }

                passwayLinks = passwayLinks ?? new List<Model.PasswayLink>();
                devices = devices ?? new List<Model.Device>();
                return SentryBoxSend(parkKey, host, passways, passwayLinks, devices);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 上传岗亭&车道信息
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="host">岗亭信息</param>
        /// <param name="passways">车道列表</param>
        /// <param name="passwayLinks">车道关联区域列表</param>
        /// <param name="devices">车道相机列表</param>
        /// <returns></returns>
        public static object SentryBoxSend(string parkKey, Model.SentryHost host, List<Model.Passway> passways, List<Model.PasswayLink> passwayLinks, List<Model.Device> devices)
        {
            if (!IsConnectCloudPark) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                //车道列表
                List<apiModel.vehichlemodel> vList = new List<apiModel.vehichlemodel>();
                passways.ForEach(a =>
                {
                    var links = passwayLinks.FindAll(x => x.PasswayLink_PasswayNo == a.Passway_No);
                    string vlType = null;
                    string bigPark = null;

                    var gate = BLL.Passway.GetPasswayGateType(a.Passway_No);
                    if (gate == 0)
                    {
                        bigPark = apiModel.vehichlemodel.EnumvlbigPark.Big;
                        vlType = apiModel.vehichlemodel.EnumvlType.Out;
                    }
                    else if (gate == 1)
                    {
                        bigPark = apiModel.vehichlemodel.EnumvlbigPark.Big;
                        vlType = apiModel.vehichlemodel.EnumvlType.In;
                    }
                    else if (gate == 2)
                    {
                        bigPark = apiModel.vehichlemodel.EnumvlbigPark.Small;
                        vlType = apiModel.vehichlemodel.EnumvlType.In;
                    }
                    else if (gate == 3)
                    {
                        bigPark = apiModel.vehichlemodel.EnumvlbigPark.Small;
                        if (a.Passway_IsCharge == 1)
                        {
                            vlType = apiModel.vehichlemodel.EnumvlType.Out;
                        }
                        else
                        {
                            vlType = apiModel.vehichlemodel.EnumvlType.In;
                        }
                    }
                    else
                    {
                        bigPark = apiModel.vehichlemodel.EnumvlbigPark.Big;
                        vlType = apiModel.vehichlemodel.EnumvlType.In;
                    }

                    var camera = devices.FindAll(x => x.Device_PasswayNo == a.Passway_No && x.Device_IO == 1 && x.Device_Category == 1)?.FirstOrDefault() ?? new Model.Device();

                    apiModel.vehichlemodel item = new apiModel.vehichlemodel()
                    {
                        vehicleLane_No = a.Passway_No,
                        vlName = a.Passway_Name,
                        vlctrlNo = a.Passway_No,
                        vlType = vlType,
                        vlctrlIp = camera.Device_IP,
                        vlvideoIp = camera.Device_IP,
                        bigPark = bigPark
                    };

                    vList.Add(item);
                });

                string vehichlejson = TyziTools.Json.ToString(vList);
                string eidtTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");
                return SentryBoxSend(parkKey, host?.SentryHost_No, vehichlejson, eidtTime);
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 同步车道信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="code">0：删除， 1：新增，2：修改</param>
        /// <param name="passway"></param>
        /// <param name="device"></param>
        /// <returns></returns>
        public static object SysnVehicleLane(string parkKey, string code, Model.Passway passway, Model.Device device = null)
        {
            if (device == null) device = BLL.Device.GetEntityByPasswayNo(passway.Passway_No);

            if (AppBasicCache.IsWritePushEventMsg)
            {
                Model.API.apiSysnVehicleLane data = new apiSysnVehicleLane()
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    actionName = "SysnVehicleLane",
                    key = parkKey,
                    sentryBoxNo = passway.Passway_SentryHostNo,
                    vehicleLaneNo = passway.Passway_No,
                    vehicleLaneName = passway.Passway_Name,
                    vehicleLaneCtrlNo = passway.Passway_No,
                    vehicleLaneCtrlIp = device.Device_IP,
                    code = code
                };

                return Create(parkKey, data, PushEventCommand.SFM_PKCLD_EVENT_SysnVehicleLane, MiddlewareEventPriority.Delay_1000_4);
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 上传车位数

        public static object SendParkSpace(string Parking_No)
        {
            if (!AppBasicCache.IsWritePushEventMsg) return 1;
            Model.Parking parking = AppBasicCache.GetParking;

            if (parking != null && parking.Parking_EnableNet == 1)
            {
                //上传车位信息
                BLL.Parking.GetParkSpace(Parking_No, out var total, out var innum, out var remain);
                return BLL.PushEvent.SendParkSpace(parking.Parking_Key, total.ToString(), remain.ToString());
            }
            else
            {
                return -1;
            }
        }

        #endregion

        #region 上报交接班记录

        /// <summary>
        /// 上报交接班记录
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="data">交班数据</param>
        /// <returns></returns>
        public static object PutWorkHandover(string parkKey, Model.WorkShift data)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    Parking_Key = parkKey,
                    GoPersonName = data.WorkShift_OffName,
                    ToPersonName = data.WorkShift_OnName,
                    WorkStartTime = data.WorkShift_OnTime,
                    WorkEndTime = data.WorkShift_OffTime,
                    PayableMoney = data.WorkShift_PayableMoney,
                    ReceivedMoney = data.WorkShift_Money,
                    InAllCount = data.WorkShift_InCount,
                    OutAllCount = data.WorkShift_OutCount,
                    InTempCarCount = data.WorkShift_InTempCount,
                    OutTempCarCount = data.WorkShift_OutTempCount,
                    StoreCarDeductMoney = data.WorkShift_StoreMoney,
                    ACarReceiptMoney = 0,
                    BCarReceiptMoney = 0,
                    CCarReceiptMoney = 0,
                    DCarReceiptMoney = 0,
                    OutACarCount = 0,
                    OutBCarCount = 0,
                    OutCCarCount = 0,
                    OutDCarCount = 0,
                    SoftOpenGateCount = data.WorkShift_SoftOpenGateCount,
                    HandOpenGateCount = data.WorkShift_HandOpenGateCount,
                    ChargeCarCount = data.WorkShift_ChargeCarCount,
                    FreeCarCount = data.WorkShift_FreeCarCount,
                    IntoFreeCarCount = data.WorkShift_IntoFreeCarCount,
                    PutDateTime = data.WorkShift_AddTime
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_PutWorkHandover, MiddlewareEventPriority.Delay_1000_4);
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 上传车辆入场信息

        /// <summary>
        /// 上传车辆入场信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <param name="priority"></param>
        /// <param name="respace"></param>
        /// <param name="isNowGet"></param>
        /// <returns></returns>
        public static object EnterCar(string parkKey, Model.ParkOrder order, MiddlewareEventPriority priority = MiddlewareEventPriority.Delay_500_2, bool respace = true, bool isNowGet = false)
        {
            if (!IsConnectCloudPark) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                var card = BLL.CarCardType.GetEntity("CarCardType_Category,CarCardType_No", $"CarCardType_No='{order.ParkOrder_CarCardType}'");

                var reserve = BLL.BaseBLL._GetEntityByWhere(new Model.Reserve(), "Reserve_No", $"Reserve_OrderNo='{order.ParkOrder_No}'");

                var orderNo = order.ParkOrder_No;
                var carNo = order.ParkOrder_CarNo;

                //招行车牌特殊处理
                if (carNo.Length == 10 && carNo.ToLower().IndexOf("cmb", StringComparison.Ordinal) == 0)
                {
                    carNo = carNo.Replace("cmb", "").Replace("CMB", "");
                }

                var enterTime = order.ParkOrder_EnterTime?.ToString("yyyy-MM-dd HH:mm:ss");
                var carType = card?.CarCardType_Category ?? "";
                var gateNo = order.ParkOrder_EnterPasswayNo ?? "0";
                var gateName = order.ParkOrder_EnterPasswayName ?? "";
                var operatorName = order.ParkOrder_EnterAdminName ?? "";
                var reserveOrderNo = reserve?.Reserve_No ?? "";
                PlateColorConvert.ToENCode(order.ParkOrder_CarTypeName, out var plateColorCode);
                var imgUrl = order.ParkOrder_EnterImgPath;
                var temp1 = order.ParkOrder_EnterRemark == null ? null : TyziTools.Json.ToString(order.ParkOrder_EnterRemark?.Split(','));


                var passway = BLL.Passway.GetEntity(gateNo);
                var sentryHostNo = passway?.Passway_SentryHostNo;


                var remainspace = 0;
                if (respace)
                {
                    BLL.Parking.GetParkSpace(order.ParkOrder_ParkNo, out var total, out var incount, out remainspace);
                }

                var res1 = EnterCar(parkKey, orderNo, carNo, enterTime, carType, gateName, operatorName, reserveOrderNo, plateColorCode.ToString(), imgUrl, priority, remainspace, temp1, isNowGet, order.ParkOrder_CarCardType, order.ParkOrder_IsNoInRecord ?? 0, gateNo, sentryHostNo, order.ParkOrder_EnterImg);
                if (!string.IsNullOrWhiteSpace(imgUrl))
                {
                    //入场图片事件不用做上传平台，用作第三方转发
                    var res2 = EnterCarImg(imgUrl, parkKey, orderNo, carNo, enterTime, carType, gateName, operatorName, reserveOrderNo, plateColorCode.ToString(), priority, remainspace, temp1, isNowGet, order.ParkOrder_CarCardType, order.ParkOrder_IsNoInRecord ?? 0, gateNo);
                }

                return res1;
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 上传车辆入场信息(智联云)

        /// <summary>
        /// 上传车辆入场信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <param name="priority"></param>
        /// <param name="respace"></param>
        /// <param name="isNowGet"></param>
        /// <returns></returns>
        public static object CloudEnterCar(string parkKey, Model.ParkOrder order, string account, string deviceno, MiddlewareEventPriority priority = MiddlewareEventPriority.Delay_500_2, bool respace = true, bool isNowGet = false)
        {
            try
            {
                if (!IsConnectCloudPark) return null;
                if (order.ParkOrder_StatusNo == (int)ParkOrderStatusEnum.InClose || order.ParkOrder_StatusNo == (int)ParkOrderStatusEnum.Close) return null;
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && AppBasicCache.CurrentSysConfigContent.SysConfig_ConnMode == 1 && !(AppBasicCache.CurrentSysConfigContent?.SysConfig_CloudBoxLprEmergency ?? false)) { return null; }
                //var reserve = BLL.BaseBLL._GetEntityByWhere(new Model.Reserve(), "Reserve_No", $"Reserve_OrderNo='{order.ParkOrder_No}'");

                var orderNo = order.ParkOrder_No;
                var carNo = order.ParkOrder_CarNo;

                //招行车牌特殊处理
                if (carNo.Length == 10 && carNo.ToLower().IndexOf("cmb", StringComparison.Ordinal) == 0)
                {
                    carNo = carNo.Replace("cmb", "").Replace("CMB", "");
                }

                var enterTime = order.ParkOrder_EnterTime?.ToString("yyyy-MM-dd HH:mm:ss");
                var carType = order.ParkOrder_CarType ?? "";
                var carcardType = order.ParkOrder_CarCardType ?? "";
                var gateNo = order.ParkOrder_EnterPasswayNo ?? "0";
                var gateName = order.ParkOrder_EnterPasswayName ?? "";
                var operatorName = order.ParkOrder_EnterAdminName ?? "";
                var imgUrl = order.ParkOrder_EnterImg;
                var remark = order.ParkOrder_EnterRemark == null ? null : (order.ParkOrder_EnterRemark.Contains(',') ? TyziTools.Json.ToString(order.ParkOrder_EnterRemark?.Split(',')) : order.ParkOrder_EnterRemark);
                var res1 = CloudEnterCar(parkKey, orderNo, carNo, enterTime, carType, carcardType, account, gateNo, imgUrl, operatorName, remark, deviceno, order?.ParkOrder_ReserveNo ?? "", priority);
                return res1;
            }
            catch (Exception e)
            {
                LogManagementMap.WriteToFile(LoggerEnum.SyncData, $"上传车辆入场信息异常[{order?.ParkOrder_CarNo}][{order?.ParkOrder_No}]：" + e.ToString(), LogLevel.Info);
                return null;
            }
        }

        #endregion

        #region 上传车辆出场信息

        /// <summary>
        /// 上传车辆出场信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        public static object OutCar(string parkKey, Model.ParkOrder order, string sFreeReason = "")
        {
            if (!IsConnectCloudPark) return null;
            if (order == null) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                var card = BLL.CarCardType.GetEntity("CarCardType_Category,CarCardType_No", $"CarCardType_No='{order.ParkOrder_CarCardType}'");

                DateTime oTime = order.ParkOrder_OutTime == null ? DateTimeHelper.GetNowTime() : order.ParkOrder_OutTime.Value;
                DateTime iTime = order.ParkOrder_EnterTime == null ? DateTimeHelper.GetNowTime() : order.ParkOrder_EnterTime.Value;
                decimal totalMoney = order.ParkOrder_TotalPayed ?? 0;

                string orderNo = order.ParkOrder_No;
                string carNo = order.ParkOrder_CarNo;

                //招行车牌特殊处理
                if (carNo.Length == 10 && carNo.ToLower().IndexOf("cmb") == 0)
                {
                    carNo = carNo.Replace("cmb", "").Replace("CMB", "");
                }

                string inTime = iTime.ToString("yyyy-MM-dd HH:mm:ss");
                string outTime = oTime.ToString("yyyy-MM-dd HH:mm:ss");
                string carType = card?.CarCardType_Category ?? "";
                string ingateNo = order.ParkOrder_EnterPasswayNo ?? "1";
                string gateNo = order.ParkOrder_OutPasswayNo ?? "1";
                string gateName = order.ParkOrder_OutPasswayName ?? "";
                string operatorName = order.ParkOrder_OutAdminName ?? "";
                string totalAmount = totalMoney.ToString();
                string couponKey = "";
                string couponMoney = "0";
                string walletPayMoney = "0";
                PlateColorConvert.ToENCode(order.ParkOrder_CarTypeName, out int plateColorCode);
                string freeReason = order.ParkOrder_IsFree == 1 ? (string.IsNullOrWhiteSpace(sFreeReason) ? (order.ParkOrder_FreeReason ?? "") : sFreeReason) : "";
                string imgUrl = order.ParkOrder_OutImgPath;
                string temp1 = order.ParkOrder_EnterRemark == null ? "" : TyziTools.Json.ToString(order.ParkOrder_EnterRemark?.Split(','));

                BLL.Parking.GetParkSpace(order.ParkOrder_ParkNo, out var total, out var incount, out var remainspace);

                var passway = BLL.Passway.GetEntity(gateNo);
                var sentryHostNo = passway?.Passway_SentryHostNo;


                var res1 = OutCar(parkKey, carNo, orderNo, inTime, outTime, carType, gateName, operatorName, totalAmount, couponKey, couponMoney, plateColorCode.ToString(), imgUrl, walletPayMoney, freeReason, remainspace, temp1, order.ParkOrder_CarCardType, order.ParkOrder_IsNoInRecord ?? 0, gateNo, ingateNo, sentryHostNo, order.ParkOrder_OutImg);

                if (!string.IsNullOrWhiteSpace(imgUrl))
                {
                    //出场图片事件不用做上传平台，用作第三方转发
                    var res2 = OutCarImg(imgUrl, parkKey, carNo, orderNo, inTime, outTime, carType, gateName, operatorName, totalAmount, couponKey, couponMoney, plateColorCode.ToString(), walletPayMoney, freeReason, remainspace, temp1, order.ParkOrder_CarCardType, order.ParkOrder_IsNoInRecord ?? 0, gateNo, ingateNo);
                }

                return res1;
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 上传车辆出场信息(智联云)

        /// <summary>
        /// 上传车辆出场信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        public static object CloudOutCar(string parkKey, Model.ParkOrder order, string account, string deviceno)
        {
            try
            {
                if (!IsConnectCloudPark) return null;
                if (order == null) return null;
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && AppBasicCache.CurrentSysConfigContent.SysConfig_ConnMode == 1 && !(AppBasicCache.CurrentSysConfigContent?.SysConfig_CloudBoxLprEmergency ?? false)) { return null; }
                if (order.ParkOrder_StatusNo == (int)ParkOrderStatusEnum.InClose || order.ParkOrder_StatusNo == (int)ParkOrderStatusEnum.Close) return null;
                DateTime oTime = order.ParkOrder_OutTime == null ? DateTimeHelper.GetNowTime() : order.ParkOrder_OutTime.Value;
                string orderNo = order.ParkOrder_No;
                string carNo = order.ParkOrder_CarNo;

                //招行车牌特殊处理
                if (carNo.Length == 10 && carNo.ToLower().IndexOf("cmb") == 0)
                {
                    carNo = carNo.Replace("cmb", "").Replace("CMB", "");
                }

                var outTime = oTime.ToString("yyyy-MM-dd HH:mm:ss");
                var carType = order.ParkOrder_CarType ?? "";
                var carcardType = order.ParkOrder_CarCardType ?? "";
                var gateNo = order.ParkOrder_OutPasswayNo ?? "0";
                var gateName = order.ParkOrder_EnterPasswayName ?? "";
                var operatorName = order.ParkOrder_OutAdminName ?? "";
                var imgUrl = order.ParkOrder_OutImg;
                var remark = order.ParkOrder_EnterRemark == null ? null : (order.ParkOrder_EnterRemark.Contains(',') ? TyziTools.Json.ToString(order.ParkOrder_EnterRemark?.Split(',')) : order.ParkOrder_EnterRemark);

                var res1 = CloudOutCar(parkKey, orderNo, carNo, outTime, carType, carcardType, account, gateNo, imgUrl, operatorName, remark, deviceno, order.ParkOrder_UserNo, order.ParkOrder_ReserveNo ?? "");
                return res1;
            }
            catch (Exception e)
            {
                LogManagementMap.WriteToFile(LoggerEnum.SyncData, $"上传车辆出场信息异常[{order?.ParkOrder_CarNo}][{order?.ParkOrder_No}]：" + e.ToString(), LogLevel.Info);
                return null;
            }
        }

        #endregion

        #region 上传支付订单(智联云)

        /// <summary>
        /// 上传支付订单
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        public static object CloudPayOrder(string parkKey, Model.PayOrder order)
        {
            try
            {
                if (!IsConnectCloudPark) return null;
                if (order == null) return null;
                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && AppBasicCache.CurrentSysConfigContent.SysConfig_ConnMode == 1 && !(AppBasicCache.CurrentSysConfigContent?.SysConfig_CloudBoxLprEmergency ?? false)) { return null; }

                var res1 = CloudPayOrder(parkKey, order.PayOrder_CarNo, order.PayOrder_OrderTypeNo ?? "", order.PayOrder_Category ?? "", order.PayOrder_ParkOrderNo ?? "", order.PayOrder_No ?? "", order.PayOrder_TempTimeCount ?? 0, order.PayOrder_Money ?? 0, order.PayOrder_PayedMoney ?? 0,
                    order.PayOrder_PayType?.ToString() ?? "", order.PayOrder_PayTypeCode ?? "", order.PayOrder_PayedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "", order.PayOrder_Account ?? "", order.PayOrder_OperatorName, order.PayOrder_Desc);
                return res1;
            }
            catch (Exception e)
            {
                LogManagementMap.WriteToFile(LoggerEnum.SyncData, $"上传支付订单异常[{order?.PayOrder_CarNo}][{order?.PayOrder_No}]：" + e.ToString(), LogLevel.Info);
                return null;
            }
        }

        #endregion


        #region 重传阿里云图片

        /// <summary>
        /// 重传阿里云图片
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        public static object UploadImg(string parkKey, Model.API.UploadImg data)
        {
            if (!IsConnectCloudPark) return null;
            if (data == null) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                return Create(parkKey, data, PushEventCommand.SFM_UPDATE_ALIYUNIMG, MiddlewareEventPriority.Delay_2000_2);
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 上传订单关闭

        /// <summary>
        /// 上传订单关闭
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <returns></returns>
        public static object CloseCar(string parkKey, Model.ParkOrder order, string reason = null)
        {
            if (!IsConnectCloudPark || order == null) return null;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                DateTime oTime = DateTimeHelper.GetNowTime();
                string orderNo = order.ParkOrder_No;
                string carNo = order.ParkOrder_CarNo;
                string orderStatus = order.ParkOrder_StatusNo.Value.ToString();
                string time = oTime.ToString("yyyy-MM-dd HH:mm:ss");
                string remark = reason ?? order.ParkOrder_Remark;

                return CarOrderStatus(parkKey, carNo, orderNo, orderStatus, time, remark);
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 黔通支付订单上传

        public static int QianTongTransactionFlow(string sJsonContent, string parkKey)
        {
            //if (string.IsNullOrWhiteSpace(parkKey)) return 0;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                Model.API.PushEvent eve = new Model.API.PushEvent()
                {
                    PushEvent_No = Utils.CreateNumber,
                    PushEvent_ParkKey = parkKey,
                    PushEvent_ReqAct = ((int)PushEventCommand.SFM_UPDATE_QianTingPayRecord).ToString(),
                    PushEvent_ReqName = PushEventCommand.SFM_UPDATE_QianTingPayRecord.ToString(),
                    PushEvent_Success = 0,
                    PushEvent_Errcode = "0",
                    PushEvent_Errmsg = "",
                    PushEvent_JsonData = sJsonContent,
                    PushEvent_Time = DateTimeHelper.GetNowTime(),
                    PushEvent_Level = (int)MiddlewareEventPriority.Delay_1000_5,
                    PushEvent_Remark = "",
                    PushEvent_RepeatCount = 0,
                    PushEvent_Three_Count = 0,
                    PushEvent_Three_LastTime = DateTimeHelper.GetNowTime(),
                    PushEvent_Three_Message = string.Empty,
                    PushEvent_Three_Success = 0
                };

                var res = GetInstance(eve.PushEvent_Time.Value).Add(eve);

                return res;
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 云托管智能匹配

        public static int CloudHostingPost(Model.Record.CloudHostingPostData model, string parkKey)
        {
            var rlt = CloudHostingPost(parkKey, model);
            if (!int.TryParse(Convert.ToString(rlt), out int i))
            {
                i = 0;
            }

            return i;
        }

        #endregion

        #endregion

        #region 创建数据Model

        #region 保存数据

        public static object Create(string parkKey, object data, PushEventCommand cmd, MiddlewareEventPriority iLevel, int iPushEvent_Three_Success = 0, int iState = 0, bool isUploadZLY = false)
        {
            if (AppSettingConfig.SentryMode == VersionEnum.CloudServer && !isUploadZLY) return 1;
            //if (string.IsNullOrWhiteSpace(parkKey)) return 0;
            string json = TyziTools.Json.ToString(data, true);
            Model.API.PushEvent eve = new Model.API.PushEvent()
            {
                PushEvent_No = Utils.CreateNumber_SnowFlake,
                PushEvent_ParkKey = parkKey,
                PushEvent_ReqAct = ((int)cmd).ToString(),
                PushEvent_ReqName = cmd.ToString(),
                PushEvent_Success = iState,
                PushEvent_Errcode = "0",
                PushEvent_Errmsg = "",
                PushEvent_JsonData = json,
                PushEvent_Time = DateTimeHelper.GetNowTime(),
                PushEvent_Level = (int)iLevel,
                PushEvent_Remark = "",
                PushEvent_RepeatCount = 0,
                PushEvent_Three_Count = 0,
                PushEvent_Three_LastTime = DateTimeHelper.GetNowTime(),
                PushEvent_Three_Message = string.Empty,
                PushEvent_Three_Success = iPushEvent_Three_Success,
                PushEvent_CityServerYongChengBoChe = 0,
                PushEvent_CityServerGanZhouTingChe = 0,
                PushEvent_CityServerShangHaiJinBoHui = 0,
                PushEvent_CityServerShanDongQingDao = 0,
                PushEvent_CityServerMaoCooParking = 0,
                PushEvent_CityServerTianYaXingParking = 0,
                PushEvent_CityServerWisdomBlockParking = 0,
                PushEvent_CityServerAnHuiHeFeiParking = 0,
                PushEvent_CityServerAnHuiMaAnShanParking = 0,
                PushEvent_CityServerAnHuiSuZhouParking = 0,
                PushEvent_CityServerBeiJingChaoYangParking = 0,
                PushEvent_CityServerBeiJingDongChengParking = 0,
                PushEvent_CityServerBeiJingFengTaiParking = 0,
                PushEvent_CityServerBeiJingHaiDianParking = 0,
                PushEvent_CityServerBeiJingTongZhouParking = 0,
                PushEvent_CityServerBeiJingXiChengParking = 0,
                PushEvent_CityServerDongGuanJiaoJing = 0,
                PushEvent_CityServerGuangZhouBaiYunGonganJu = 0,
                PushEvent_CityServerGuangZhouJiaoWei = 0,
                PushEvent_CityServerGuangZhouZengCheng = 0,
                PushEvent_CityServerShenZhenJiaoJing = 0,
                PushEvent_CityServerJiangMenPengJiangFenJu = 0,
                PushEvent_CityServerShenZhenNanShanZhuJianJu = 0,
                PushEvent_CityServeranxinjiaparking = 0,
                PushEvent_CityServerBingJiangWuYeParking = 0,
                PushEvent_CityServerJiangSuKunShanParking = 0,
                PushEvent_CityServerWuXiXinWuParking = 0,
                PushEvent_CityServerHangZhouCityBrain = 0,
                PushEvent_CityServerZheJiangJiaXing = 0
            };
            var res = GetInstance(eve.PushEvent_Time.Value).Add(eve);
            return res;
        }

        public static object CreateOtherData(string parkKey, string data, PushEventCommand command)
        {
            //if (string.IsNullOrWhiteSpace(parkKey)) return 0;
            string json = data;
            Model.API.PushEvent eve = new Model.API.PushEvent()
            {
                PushEvent_No = Utils.CreateNumber,
                PushEvent_ParkKey = parkKey,
                PushEvent_ReqAct = ((int)command).ToString(),
                PushEvent_ReqName = command.ToString(),
                PushEvent_Success = 1,
                PushEvent_Errcode = "0",
                PushEvent_Errmsg = "",
                PushEvent_JsonData = json,
                PushEvent_Time = DateTimeHelper.GetNowTime(),
                PushEvent_Level = 0,
                PushEvent_Remark = "附加条件数据",
                PushEvent_RepeatCount = 0,
                PushEvent_Three_Count = 0,
                PushEvent_Three_LastTime = DateTimeHelper.GetNowTime(),
                PushEvent_Three_Message = string.Empty,
                PushEvent_Three_Success = 1,
            };
            var res = GetInstance(eve.PushEvent_Time.Value).Add(eve);
            return res;
        }

        /// <summary>
        /// 创建上云平台数据
        /// </summary>
        /// <param name="pushEvent">事件信息</param>
        /// <returns></returns>
        public static object Create(Model.API.PushEvent pushEvent)
        {
            if (!AppBasicCache.IsWritePushEventMsg) return 0;
            return GetInstance(pushEvent.PushEvent_Time.Value).Add(pushEvent);
        }

        public static string UploadImgToAliyun(string imgUrl, string parkKey, string deviceno = "", bool isSmall = false, bool isUploadZLY = false, string localImgPath = "", string orderNo = "")
        {
            var park = BLL.Parking.GetEntityByKey(parkKey);
            if (park?.IsUpLoadPic != 1 && !isUploadZLY) return "";
            if (AppBasicCache.IsWritePushEventMsg)
            {
                //LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, $"上传图片：{imgUrl}");
                string img = string.Empty;
                if (!string.IsNullOrWhiteSpace(imgUrl))
                {
                    if ((imgUrl.Contains("http://") || imgUrl.Contains("https://")) && imgUrl.Contains("aliyuncs.com"))
                        return imgUrl; //已经是网络地址访问无需上传

                    string fileName = Path.GetFileName(imgUrl.Replace('\\', '/'));

                    if (isUploadZLY)
                    {
                        //文件图片存储路径规则：/picture/停车场ID/摄像机SN/日期/当前小时（两位数）/日期_时分秒_随机三位数.jpg
                        fileName = $"picture/{deviceno}/{DateTime.Now:yyyyMMdd}/{DateTime.Now:HH}/{DateTime.Now:yyyyMMdd_HHmmss}_{new Random().Next(100, 999)}.jpg";
                    }

                    Model.Parking parking = BLL.Parking.GetEntityByKey(parkKey);
                    Model.SysConfig sysConfig = BLL.SysConfig.GetEntity(parking?.Parking_No);
                    Model.SysConfigContent content = TyziTools.Json.ToObject<Model.SysConfigContent>(BLL.SysConfig.GetUrlDecode(sysConfig?.SysConfig_Content));
                    if (content == null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "上传图片失败：阿里云存储配置错误。", LogLevel.Error);
                        return "";
                    }

                    if (content.SysConfig_AliyunEndpoint == null || content.SysConfig_AliyunAccessKeyId == null || content.SysConfig_AliyunAccessKeySecret == null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "上传图片失败：阿里云存储配置错误。", LogLevel.Error);
                        return "";
                    }

                    var instance = AliFileHelper.GetIntance(content, isSmall);
                    if (instance != null)
                    {
                        img = instance.GetAliyunFileUrl(fileName, DateTimeHelper.GetNowTime().AddYears(100));
                    }

                    var bt = string.IsNullOrEmpty(localImgPath) ? Common.AliFileHelper.GetStreamByUrl(imgUrl) : AliFileHelper.GetLocalFileByte(localImgPath);
                    if (bt == null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "阿里云重传缓存：" + img, LogLevel.Info);
                        //DataCache.Aliyun.Set(fileName, img);
                        BLL.PushEvent.UploadImg(AppBasicCache.GetParking?.Parking_Key, new Model.API.UploadImg()
                        {
                            ImgName = fileName,
                            LocalPath = localImgPath,
                            imgUrl = imgUrl,
                            orderNo = orderNo
                        });
                    }
                    else
                    {
                        if (instance != null && CustomThreadPool.PictureTaskPool != null) _ = CustomThreadPool.PictureTaskPool?.QueueTask(null, () =>
                        {
                            var ret = instance.UploadByByte(fileName, bt, DateTimeHelper.GetNowTime().AddYears(100), false, localImgPath);
                            if (string.IsNullOrEmpty(ret))
                            {
                                BLL.PushEvent.UploadImg(AppBasicCache.GetParking?.Parking_Key, new Model.API.UploadImg()
                                {
                                    ImgName = fileName,
                                    LocalPath = localImgPath,
                                    imgUrl = ret,
                                    orderNo = orderNo
                                });
                            }
                            return Task.CompletedTask;
                        });
                        else if (instance != null)
                        {
                            Task.Run(() =>
                            {
                                var ret = instance.UploadByByte(fileName, bt, DateTimeHelper.GetNowTime().AddYears(100), false);
                                if (string.IsNullOrEmpty(ret))
                                {
                                    BLL.PushEvent.UploadImg(AppBasicCache.GetParking?.Parking_Key, new Model.API.UploadImg()
                                    {
                                        ImgName = fileName,
                                        LocalPath = localImgPath,
                                        imgUrl = ret,
                                        orderNo = orderNo
                                    });
                                }
                            });
                        }
                    }
                }

                return img;
            }
            else
            {
                return "";
            }
        }

        public static string UploadFileToAliyun(string Url, string parkKey, bool isSmall = false)
        {
            var park = BLL.Parking.GetEntityByKey(parkKey);
            if (park?.IsUpLoadPic != 1) return "";

            string img = string.Empty;
            if (!string.IsNullOrWhiteSpace(Url))
            {
                string fileName = Url.Split('/')[Url.Split('/').Length - 1];
                var stream = Common.AliFileHelper.GetFileStreamByUrl(Url);
                if (stream == null || stream.Length <= 0) return "";
                stream.Seek(0, System.IO.SeekOrigin.Begin);

                Model.Parking parking = BLL.Parking.GetEntityByKey(parkKey);
                Model.SysConfig sysConfig = BLL.SysConfig.GetEntity(parking?.Parking_No);
                Model.SysConfigContent content = TyziTools.Json.ToObject<Model.SysConfigContent>(BLL.SysConfig.GetUrlDecode(sysConfig?.SysConfig_Content));
                if (content == null)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebAPILog, "上传文件失败：阿里云存储配置错误。", LogLevel.Error);
                    return "";
                }

                var instance = AliFileHelper.GetIntance(content, isSmall);
                img = instance.GetAliyunFileUrl(fileName, DateTimeHelper.GetNowTime().AddYears(100));
                if (CustomThreadPool.PictureTaskPool != null) _ = CustomThreadPool.PictureTaskPool?.QueueTask(null, () => { instance.UploadByStream(fileName, stream, DateTimeHelper.GetNowTime().AddYears(100)); return Task.CompletedTask; });
                else Task.Run(() => { instance.UploadByStream(fileName, stream, DateTimeHelper.GetNowTime().AddYears(100)); });
            }

            return img;
        }

        #endregion

        #region 上传岗亭&车道信息

        /// <summary>
        /// 上传岗亭&车道信息
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="host">岗亭信息</param>
        /// <param name="passways">车道列表</param>
        /// <param name="passwayLinks">车道关联区域列表</param>
        /// <param name="devices">车道相机列表</param>
        /// <returns></returns>
        public static object SentryBoxSend(string parkKey, string sentrybox_No, string vehichlejson, string eidtTime)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var data = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    sentrybox_No = sentrybox_No,
                    eidtTime = eidtTime,
                    vehichlejson = vehichlejson
                };

                //发送消息
                return Create(parkKey, data, PushEventCommand.SFM_PKCLD_EVENT_SentryBoxSend, MiddlewareEventPriority.Delay_1000_4);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上传车位数

        /// <summary>
        /// 上传车位数
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="total_spaces">总车位数</param>
        /// <param name="remainder_spaces">剩余车位数</param>
        /// <returns></returns>
        public static object SendParkSpace(string parkKey, string total_spaces, string remainder_spaces)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var data = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    total_spaces = total_spaces,
                    remainder_spaces = remainder_spaces
                };

                return Create(parkKey, data, PushEventCommand.SFM_PKCLD_EVENT_SendParkSpace, MiddlewareEventPriority.Delay_500_3);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上报取消预定车位(多车位多车)

        /// <summary>
        /// 上报取消预定车位(多车位多车)
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="orderNo">交班数据</param>
        /// <returns></returns>
        public static object CancelReserveParkSpace(string parkKey, string orderNo)
        {
            var model = new
            {
                version = Config.AppSettingConfig.ApiVersion,
                key = parkKey,
                orderNo = orderNo
            };

            return Create(parkKey, model, PushEventCommand.SFM_UPDATE_CANCELRESERVEPARKSPACE, MiddlewareEventPriority.Delay_1000_4);
        }

        #endregion

        #region 上传图片相机抓拍

        /// <summary>
        /// 上传图片相机抓拍
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="model">抓拍数据</param>
        /// <returns></returns>
        public static object SendCameraPhotoInfo(string parkKey, apiEventModel.SendCameraPhotoInfo data)
        {
            var model = new
            {
                version = Config.AppSettingConfig.ApiVersion,
                key = parkKey,
                videoIp = data.videoIp,
                pointA = data.pointA,
                pointB = data.pointB,
                pointC = data.pointC,
                pointD = data.pointD,
                imgUrl = data.imgUrl,
            };

            return Create(parkKey, model, PushEventCommand.SFM_UPDATE_SENDCAMERAPHOTOINFO, MiddlewareEventPriority.Delay_1000_4);
        }

        #endregion

        #region 设备（停车场）上下线记录接口

        /// <summary>
        /// 设备（停车场）上下线记录接口
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="status">状态</param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static object DeviceOnlineRecord(string parkKey, string status, string remark)
        {
            var model = new
            {
                version = Config.AppSettingConfig.ApiVersion,
                key = parkKey,
                status = status,
                remark = remark
            };

            return Create(parkKey, model, PushEventCommand.SFM_UPDATE_DEVICEONLINERECORD, MiddlewareEventPriority.Delay_500_3);
        }

        #endregion

        #region 上传入场车辆车牌修正

        /// <summary>
        /// 上传入场车辆车牌修正
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="orderNo"></param>
        /// <param name="carNo"></param>
        /// <returns></returns>
        public static object UpdateCar(string parkKey, string orderNo, string carNo)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    orderNo = orderNo,
                    carNo = carNo
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_UpdateCar, MiddlewareEventPriority.Delay_1000_1);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上传车辆入场信息

        /// <summary>
        /// 上传车辆入场信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="orderNo"></param>
        /// <param name="carNo"></param>
        /// <param name="enterTime"></param>
        /// <param name="carType"></param>
        /// <param name="gateName"></param>
        /// <param name="operatorName"></param>
        /// <param name="reserveOrderNo"></param>
        /// <param name="plateColor"></param>
        /// <param name="imgUrl"></param>
        /// <param name="remainspace"></param>
        /// <param name="temp1"></param>
        /// <param name="isNowGet"></param>
        /// <param name="carTypeNo"></param>
        /// <param name="iNoRecord"></param>
        /// <param name="gateNo"></param>
        /// <returns></returns>
        public static object EnterCar(string parkKey, string orderNo, string carNo, string enterTime, string carType, string gateName, string operatorName, string reserveOrderNo, string plateColor, string imgUrl, MiddlewareEventPriority priority = MiddlewareEventPriority.Delay_500_2, int remainspace = 0, string temp1 = "", bool isNowGet = false, string carTypeNo = "", int iNoRecord = 0, string gateNo = "0", string sentryHostNo = "", string localImgPath = "",
            string phone = "")
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                if (carNo?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;
                var img = UploadImgToAliyun(imgUrl, parkKey, localImgPath: localImgPath, orderNo: orderNo);

                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    orderNo = orderNo,
                    carNo = carNo,
                    enterTime = enterTime,
                    carType = carType,
                    gateName = gateName,
                    operatorName = operatorName,
                    reserveOrderNo = reserveOrderNo,
                    plateColor = plateColor,
                    imgUrl = img,
                    generalField = System.Web.HttpUtility.UrlEncode((new JObject
                    {
                        { "remainspace", remainspace }, //剩余车位
                        { "temp1", temp1 }, //备注说明
                        { "carTypeNo", temp1 }, //车牌颜色
                        { "iNoRecord", iNoRecord }, //是否无入场记录
                        { "gateNo", gateNo }, //入场车道编号
                        { "imgUrl", imgUrl }, //图片地址
                        { "sentryHostNo", sentryHostNo }, //入场岗亭编号
                    }).ToString()),
                    remainspace = remainspace,
                    temp1 = System.Web.HttpUtility.UrlEncode(temp1),
                    gateNo = gateNo,
                    remark = temp1, //手机号+备注，用于上报订单关联的手机号（车辆扫码登记入场）
                };

                return isNowGet ? model : Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_EnterCar, priority);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上传车辆入场信息（智联云）

        /// <summary>
        /// 上传车辆入场信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="orderNo"></param>
        /// <param name="carNo"></param>
        /// <param name="enterTime"></param>
        /// <param name="carType"></param>
        /// <param name="gateName"></param>
        /// <param name="operatorName"></param>
        /// <param name="reserveOrderNo"></param>
        /// <param name="plateColor"></param>
        /// <param name="imgUrl"></param>
        /// <param name="remainspace"></param>
        /// <param name="temp1"></param>
        /// <param name="isNowGet"></param>
        /// <param name="carTypeNo"></param>
        /// <param name="iNoRecord"></param>
        /// <param name="gateNo"></param>
        /// <returns></returns>
        public static object CloudEnterCar(string parkKey, string orderNo, string carNo, string time, string carTypeNo, string carcardno, string account, string passwayno, string imgUrl,
            string operatorName, string remark, string deviceno, string reserveno, MiddlewareEventPriority priority = MiddlewareEventPriority.Delay_500_2)
        {
            //var img = UploadImgToAliyun(imgUrl, parkKey, deviceno, isUploadZLY: true);
            if (carNo?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;

            var model = new
            {
                orderno = orderNo,
                carno = carNo,
                time = time,
                cartypeno = carTypeNo,
                carcardno = carcardno,
                account = account,
                opname = operatorName,
                img = imgUrl,
                passwayno = passwayno,
                remark = remark,
                deviceno = deviceno,
                reserveno = reserveno
            };

            if (string.IsNullOrWhiteSpace(parkKey))
            {
                parkKey = BLL.Parking.GetEntityByWhere(null)?.Parking_Key;
            }

            return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_EnterCar, priority, isUploadZLY: true);
        }

        #endregion

        #region 云托管车牌智能匹配

        public static object CloudHostingPost(string parkKey, Model.Record.CloudHostingPostData data)
        {
            string img = UploadImgToAliyun(data.enterimg, parkKey, orderNo: data.orderno);
            string img1 = UploadImgToAliyun(data.entersmallimg, parkKey, orderNo: data.orderno);
            data.enterimg = img;
            data.entersmallimg = img1;

            return Create(parkKey, data, PushEventCommand.SFM_UPDATE_CloudHostingCarEnter, MiddlewareEventPriority.Delay_CloudHostingCarEnter);
        }

        #endregion

        #region 上传车辆出场信息

        /// <summary>
        /// 上传车辆出场信息
        /// </summary>
        /// <param name="carNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="outTime"></param>
        /// <param name="carType"></param>
        /// <param name="gateName"></param>
        /// <param name="operatorName"></param>
        /// <param name="totalAmount"></param>
        /// <param name="couponKey"></param>
        /// <param name="couponMoney"></param>
        /// <param name="imgUrl"></param>
        /// <param name="walletPayMoney"></param>
        /// <param name="freeReason"></param>
        /// <returns></returns>
        public static object OutCar(string parkKey, string carNo, string orderNo, string inTime, string outTime, string carType, string gateName, string operatorName, string totalAmount, string couponKey, string couponMoney, string plateColor, string imgUrl, string walletPayMoney, string freeReason, int remainspace = 0, string temp1 = "", string carTypeNo = "", int iNoRecord = 0, string gateNo = "1", string ingateNo = "0", string sentryHostNo = "", string localImgPath = "")
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                if (carNo?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;
                string img = UploadImgToAliyun(imgUrl, parkKey, localImgPath: localImgPath, orderNo: orderNo);

                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    orderNo = orderNo,
                    carNo = carNo,
                    outTime = outTime,
                    carType = carType,
                    gateName = gateName,
                    operatorName = operatorName,
                    totalAmount = totalAmount,
                    couponKey = couponKey,
                    couponMoney = couponMoney,
                    plateColor = plateColor,
                    imgUrl = img,
                    walletPayMoney = walletPayMoney,
                    freeReason = freeReason,
                    generalField = System.Web.HttpUtility.UrlEncode((new JObject
                    {
                        { "remainspace", remainspace },
                        { "temp1", temp1 },
                        { "inTime", inTime },
                        { "carTypeNo", carTypeNo },
                        { "iNoRecord", iNoRecord },
                        { "gateNo", gateNo },
                        { "ingateNo", ingateNo },
                        { "imgUrl", imgUrl },
                        { "sentryHostNo", sentryHostNo }
                    }).ToString()),
                    remainspace = remainspace,
                    temp1 = System.Web.HttpUtility.UrlEncode(temp1),
                    ingateNo = ingateNo,
                    gateNo = gateNo,
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_OutCar, MiddlewareEventPriority.Delay_1000_1);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上传车辆出场信息（智联云）

        /// <summary>
        /// 上传车辆出场信息
        /// </summary>
        /// <param name="carNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="outTime"></param>
        /// <param name="carType"></param>
        /// <param name="gateName"></param>
        /// <param name="operatorName"></param>
        /// <param name="totalAmount"></param>
        /// <param name="couponKey"></param>
        /// <param name="couponMoney"></param>
        /// <param name="imgUrl"></param>
        /// <param name="walletPayMoney"></param>
        /// <param name="freeReason"></param>
        /// <returns></returns>
        public static object CloudOutCar(string parkKey, string orderNo, string carNo, string time, string carTypeNo, string carcardno, string account, string passwayno, string imgUrl,
            string operatorName, string remark, string deviceno, string ordertype, string reserveno)
        {
            //string img = UploadImgToAliyun(imgUrl, parkKey, deviceno, isUploadZLY: true);
            if (carNo?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;

            var model = new
            {
                orderno = orderNo,
                carno = carNo,
                time = time,
                cartypeno = carTypeNo,
                carcardno = carcardno,
                account = account,
                opname = operatorName,
                img = imgUrl,
                passwayno = passwayno,
                remark = remark,
                deviceno = deviceno,
                ordertype = ordertype,
                reserveno = reserveno
            };

            if (string.IsNullOrWhiteSpace(parkKey))
            {
                parkKey = BLL.Parking.GetEntityByWhere(null)?.Parking_Key;
            }

            return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_OutCar, MiddlewareEventPriority.Delay_1000_1, isUploadZLY: true);
        }

        #endregion

        #region 上传支付订单（智联云）

        /// <summary>
        /// 上传支付订单
        /// </summary>
        /// <param name="carNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="outTime"></param>
        /// <param name="carType"></param>
        /// <param name="gateName"></param>
        /// <param name="operatorName"></param>
        /// <param name="totalAmount"></param>
        /// <param name="couponKey"></param>
        /// <param name="couponMoney"></param>
        /// <param name="imgUrl"></param>
        /// <param name="walletPayMoney"></param>
        /// <param name="freeReason"></param>
        /// <returns></returns>
        public static object CloudPayOrder(string parkKey, string carno, string orderTypeNo, string category, string orderno, string payOrderNo, int parktimemin, decimal paidAmount, decimal receAmount,
            string payType, string payTypeCode, string time, string account, string opname, string remark)
        {
            if (carno?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;

            var model = new
            {
                carno = carno,
                orderTypeNo = orderTypeNo,
                category = category,
                orderno = orderno,
                payOrderNo = payOrderNo,
                parktimemin = parktimemin,
                paidAmount = paidAmount,
                receAmount = receAmount,
                payType = payType,
                payTypeCode = payTypeCode,
                time = time,
                account = account,
                opname = opname,
                remark = remark,
            };

            if (string.IsNullOrWhiteSpace(parkKey))
            {
                parkKey = BLL.Parking.GetEntityByWhere(null)?.Parking_Key;
            }

            return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_PayOrder, MiddlewareEventPriority.Delay_1000_2, isUploadZLY: true);
        }

        #endregion

        #region 上传车辆入场图片

        /// <summary>
        /// 上传车辆入场图片
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="imgUrl"></param>
        /// <returns></returns>
        public static object EnterCarImg(string imgUrl, string parkKey, string orderNo, string carNo, string enterTime, string carType, string gateName, string operatorName, string reserveOrderNo, string plateColor, MiddlewareEventPriority priority = MiddlewareEventPriority.Delay_500_2, int remainspace = 0, string temp1 = "", bool isNowGet = false, string carTypeNo = "", int iNoRecord = 0, string gateNo = "0", int iState = 1)
        {
            if (carNo?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    orderNo = orderNo,
                    carNo = carNo,
                    enterTime = enterTime,
                    carType = carType,
                    gateName = gateName,
                    operatorName = operatorName,
                    reserveOrderNo = reserveOrderNo,
                    plateColor = plateColor,
                    imgUrl = imgUrl,
                    generalField = System.Web.HttpUtility.UrlEncode((new JObject
                {
                    { "remainspace", remainspace },
                    { "temp1", temp1 },
                    { "carTypeNo", temp1 },
                    { "iNoRecord", iNoRecord },
                }).ToString()),
                    remainspace = remainspace,
                    temp1 = System.Web.HttpUtility.UrlEncode(temp1),
                    gateNo = gateNo
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_EnterCarImg, MiddlewareEventPriority.Delay_500_2, iState: iState);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上传车辆出场图片

        /// <summary>
        /// 上传车辆出场图片
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="imgUrl"></param>
        /// <returns></returns>
        public static object OutCarImg(string imgUrl, string parkKey, string carNo, string orderNo, string inTime, string outTime, string carType, string gateName, string operatorName, string totalAmount, string couponKey, string couponMoney, string plateColor, string walletPayMoney, string freeReason, int remainspace = 0, string temp1 = "", string carTypeNo = "", int iNoRecord = 0, string gateNo = "1", string ingateNo = "0")
        {
            if (carNo?.ToUpper() == AppBasicCache.preheatCarNo.ToUpper()) return 0;

            var model = new
            {
                version = Config.AppSettingConfig.ApiVersion,
                key = parkKey,
                orderNo = orderNo,
                carNo = carNo,
                outTime = outTime,
                carType = carType,
                gateName = gateName,
                operatorName = operatorName,
                totalAmount = totalAmount,
                couponKey = couponKey,
                couponMoney = couponMoney,
                plateColor = plateColor,
                imgUrl = imgUrl,
                walletPayMoney = walletPayMoney,
                freeReason = freeReason,
                generalField = System.Web.HttpUtility.UrlEncode((new JObject
                {
                    { "remainspace", remainspace },
                    { "temp1", temp1 },
                    { "inTime", inTime },
                    { "carTypeNo", carTypeNo },
                    { "iNoRecord", iNoRecord },
                }).ToString()),
                remainspace = remainspace,
                temp1 = temp1,
                ingateNo = ingateNo,
                gateNo = gateNo,
            };
            return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_OutCarImg, MiddlewareEventPriority.Delay_1000_1, iState: 1);
        }

        #endregion

        #region 车辆异常放行图片上传

        /// <summary>
        /// 车辆异常放行图片上传
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="imgUrl"></param>
        /// <returns></returns>
        public static object AbnorOutCarImg(string parkKey, string carNo, string orderNo, string imgUrl)
        {
            if (string.IsNullOrWhiteSpace(imgUrl)) return null;

            string img = UploadImgToAliyun(imgUrl, parkKey, orderNo: orderNo);
            if (string.IsNullOrWhiteSpace(img)) return null;

            var model = new
            {
                version = Config.AppSettingConfig.ApiVersion,
                key = parkKey,
                carNo = carNo ?? "",
                orderNo = orderNo ?? "",
                imgUrl = img ?? ""
            };

            return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_AbnorOutOutCarImg, MiddlewareEventPriority.Delay_1000_4);
        }

        #endregion

        #region 预约车辆上报

        /// <summary>
        /// 预约车辆上报
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="orderNo">预约订单号，不是停车订单号</param>
        /// <returns></returns>
        public static object ReserveSend(string parkKey, string carNo, string orderNo)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    orderNo = orderNo
                };

                return Create(parkKey, model, PushEventCommand.SFM_UPDATE_ReserveSend, MiddlewareEventPriority.Delay_1000_4);
            }

            return 0;
        }

        #endregion

        #region 上传车辆异常出场

        /// <summary>
        /// 上传车辆异常出场
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="orderNo"></param>
        /// <param name="carNo"></param>
        /// <param name="outTime"></param>
        /// <param name="carType"></param>
        /// <param name="gateName"></param>
        /// <param name="operatorName"></param>
        /// <param name="imgUrl"></param>
        /// <param name="totalAmount"></param>
        /// <returns></returns>
        public static object AboutCar(string parkKey, string orderNo, string carNo, string outTime, string carType, string gateName, string operatorName, string imgUrl, string totalAmount, string remark, Model.API.apiEventModel.CarOutOpenGate exc = null)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                //string img = UploadFileToAliyun(imgUrl, parkKey);
                string entImgSrc = UploadImgToAliyun(exc?.entImg, parkKey);
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    orderNo = orderNo ?? "",
                    carNo = carNo ?? "",
                    outTime = outTime ?? "",
                    carType = carType ?? "",
                    gateName = gateName ?? "",
                    operatorName = operatorName ?? "",
                    imgUrl = "",
                    totalAmount = totalAmount ?? "",
                    remark = remark ?? "",

                    //新增加字段
                    eventTime = exc?.eventTime, //事件时间
                    entTime = exc?.entTime, //入场时间
                    entGateName = exc?.entGateName, //入口名称
                    entOperatorName = exc?.entOperatorName, //入口操作员
                    entImg = entImgSrc, //入口图片
                    money = exc?.money, //应付
                    vehicleLaneType = exc?.vehicleLaneType, //出入口类型
                    vehicleLaneNo = exc?.vehicleLaneNo, //车道号
                    openType = exc?.openType, //开闸类型
                    openReason = exc?.openReason, //开闸原因
                    parkOrderNo = exc?.parkOrderNo //停车订单号
                };

                var res1 = Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_AboutCar, MiddlewareEventPriority.Delay_1000_4);

                var res2 = BLL.PushEvent.AbnorOutCarImg(parkKey, carNo, orderNo, imgUrl);

                return res2;
            }
            else
            {
                return 0;
            }
        }

        public static object AboutCar(string parkKey, Model.AbnorOrder data)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                string eventTime = null;
                string entTime = null;
                string outTime = null;
                if (data?.AbnorOrder_Time != null) eventTime = data?.AbnorOrder_Time.Value.ToString("yyyy-MM-dd HH:mm:ss");
                if (data?.AbnorOrder_EnterTime != null) entTime = data?.AbnorOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                if (data?.AbnorOrder_OutTime != null) outTime = data?.AbnorOrder_OutTime.Value.ToString("yyyy-MM-dd HH:mm:ss");

                return AboutCar(parkKey
                    , data?.AbnorOrder_No
                    , data?.AbnorOrder_CarNo
                    , outTime ?? eventTime
                    , data?.AbnorOrder_CardCategory.Value.ToString()
                    , data?.AbnorOrder_Gate == 0 ? data?.AbnorOrder_OutGateName : data?.AbnorOrder_EntGateName
                    , data?.AbnorOrder_Gate == 0 ? data?.AbnorOrder_OutOperatorName : data?.AbnorOrder_EntOperatorName
                    , data?.AbnorOrder_Gate == 0 ? data?.AbnorOrder_OutImgPath : data?.AbnorOrder_EntImgPath
                    , data?.AbnorOrder_TotalAmount.Value.ToString()
                    , data?.AbnorOrder_Remark
                    , new apiEventModel.CarOutOpenGate
                    {
                        eventTime = (eventTime ?? outTime) ?? "",
                        entTime = entTime ?? "",
                        entGateName = data?.AbnorOrder_EntGateName ?? "",
                        entOperatorName = data?.AbnorOrder_EntOperatorName ?? "",
                        entImg = data?.AbnorOrder_EntImgPath ?? "",
                        money = data?.AbnorOrder_Money.Value.ToString(),
                        vehicleLaneType = data?.AbnorOrder_Gate.Value == 0 ? "1" : "0",
                        vehicleLaneNo = data?.AbnorOrder_PasswayNo ?? "",
                        openType = data?.AbnorOrder_Type.Value.ToString(),
                        openReason = data?.AbnorOrder_OpenReason.Value.ToString(),
                        parkOrderNo = data?.AbnorOrder_OrderNo ?? ""
                    });
            }
            else
            {
                return 0;
            }
        }

        #endregion

        /// <summary>
        /// 月租车登记上报 
        /// 登记时owner不为null，payorder不为null 
        /// 仅上报续费时车主信息为null
        /// 仅修改车辆上报时缴费信息为null
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="car">车辆信息</param>
        /// <param name="owner">车主信息，仅上报续费时为null</param>
        /// <param name="payorder">缴费信息，仅修改车辆上报时为null</param>
        /// <returns></returns>
        public static object MthCarRegister(string parkKey, Model.Car car, Model.Owner owner, Model.PayOrder payorder = null, List<Model.StopSpace> stopSpaceList = null)
        {
            //if (string.IsNullOrWhiteSpace(parkKey)) return 0;
            if (car == null || string.IsNullOrWhiteSpace(car.Car_CarNo)) return 0;

            if (AppBasicCache.IsWritePushEventMsg)
            {
                object res = 0;
                if (car != null && owner != null)
                {
                    if (string.IsNullOrWhiteSpace(car.Car_Category) || string.IsNullOrWhiteSpace(owner.Owner_Space) || car.Car_BeginTime == null || car.Car_EndTime == null) return 0;

                    var machineNo = "";

                    //查询车辆车主的可停车区域
                    if (stopSpaceList == null || stopSpaceList.Count == 0) stopSpaceList = BaseBLL._GetAllEntity(new StopSpace(), "StopSpace_AreaNo,StopSpace_Type,StopSpace_Number", $"StopSpace_OwnerNo='{car.Car_OwnerNo}'");
                    if (stopSpaceList.Find(x => x.StopSpace_Type == 0 && x.StopSpace_Number > 0) != null)
                    {
                        machineNo = "ffffffff";
                    }
                    else
                    {
                        var SendPasswayNoList = new List<string>();
                        var SendAreaNoList = new List<string>();

                        foreach (var stopSpace in stopSpaceList)
                        {
                            if (stopSpace != null && stopSpace.StopSpace_Number > 0)
                            {
                                if (stopSpace.StopSpace_Type == 1)
                                {
                                    if (!string.IsNullOrWhiteSpace(stopSpace.StopSpace_AreaNo))
                                    {
                                        var sare1 = JsonConvert.DeserializeObject<List<string>>(stopSpace.StopSpace_AreaNo);
                                        SendAreaNoList.AddRange(sare1);
                                        SendAreaNoList = SendAreaNoList.Distinct().ToList();
                                        var fares = AppBasicCache.GetAllPasswayLink.Values.Where(pare => stopSpace.StopSpace_AreaNo.Contains(pare.PasswayLink_ParkAreaNo)).ToList();
                                        if (fares.Count() > 0 && sare1.Count > 0)
                                        {
                                            var noes = fares.Select(x => x.PasswayLink_PasswayNo).ToList();
                                            SendPasswayNoList.AddRange(noes);
                                            SendPasswayNoList = SendPasswayNoList.Distinct().ToList();
                                        }
                                    }
                                }
                            }
                        }

                        if (SendPasswayNoList.Count > 0)
                        {
                            //既是外场出口，又是内场入口,并且没有内场区域授权的车道，不绑定机号
                            for (var i = 0; i < SendPasswayNoList.Count; i++)
                            {
                                var passwayno = SendPasswayNoList[i];
                                var passway = AppBasicCache.GetElement(AppBasicCache.GetAllPassway, passwayno);
                                if (passway != null)
                                {
                                    int gate = BLL.Passway.GetPasswayGateType(passwayno);
                                    if (gate == 2 || gate == 3)
                                    {
                                        //车道绑定的区域
                                        var areas = AppBasicCache.GetAllPasswayLink.Values.Where(pare => pare.PasswayLink_PasswayNo == passwayno).Select(x => x.PasswayLink_ParkAreaNo).ToList();
                                        if (areas?.Count > 1)
                                        {
                                            var area1 = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, areas[0]);
                                            var area2 = AppBasicCache.GetElement(AppBasicCache.GetParkAreas, areas[1]);
                                            if (area1 != null && area2 != null)
                                            {
                                                if (area2.ParkArea_Level > area1.ParkArea_Level)
                                                {
                                                    if (!SendAreaNoList.Contains(area2.ParkArea_No)) { SendPasswayNoList.Remove(passwayno); i--; }
                                                }
                                                else if (area2.ParkArea_Level < area1.ParkArea_Level)
                                                {
                                                    if (!SendAreaNoList.Contains(area1.ParkArea_No)) { SendPasswayNoList.Remove(passwayno); i--; }
                                                }
                                                else
                                                {
                                                    if (!SendAreaNoList.Contains(area2.ParkArea_No)) { SendPasswayNoList.Remove(passwayno); i--; }
                                                }
                                            }
                                        }
                                    }
                                }
                            }


                            foreach (var item in SendPasswayNoList)
                            {
                                if (item.Length < 4 && Utils.ObjectToInt(item, 0) > 0 && Utils.ObjectToInt(item, 0) <= 32)
                                    machineNo += (item + "&");
                            }
                            machineNo = machineNo.TrimEnd('&');
                            machineNo = Utils.MachineNoConvert(machineNo);
                        }
                    }

                    res = BLL.PushEvent.MthCarIssueSend(
                        parkKey
                        , car.Car_CarNo
                        , car.Car_Category
                        , owner.Owner_Space
                        , car.Car_BeginTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , car.Car_EndTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , car.Car_AddTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , (owner.Owner_SpaceNum ?? 1).ToString()
                        , owner.Owner_Name ?? ""
                        , AESHelper.AesDecrypt_DB(owner.Owner_Phone) ?? ""
                        , owner.Owner_Address ?? ""
                        , owner.Owner_ParkSpace ?? ""
                        , machineNo);
                }

                if (car != null && payorder != null)
                {
                    res = BLL.PushEvent.MthCarChargeSend(
                        parkKey
                        , car.Car_CarNo
                        , car.Car_Category
                        , car.Car_BeginTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , car.Car_EndTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , payorder.PayOrder_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , payorder.PayOrder_PayedMoney.Value.ToString()
                        , payorder.PayOrder_OperatorName ?? ""
                        , payorder.PayOrder_No);
                }

                return res;
            }
            else
            {
                return 0;
            }
        }

        #region 月租车辆登记上报

        /// <summary>
        /// 月租车辆登记上报
        /// </summary>
        /// <param name="parkKey">停车场KEY</param>
        /// <param name="carNo">车牌号</param>
        /// <param name="carType">车牌类型</param>
        /// <param name="carSpaceNo">系统车位号</param>
        /// <param name="beginTime">有效期开始</param>
        /// <param name="endTime">有效期截止</param>
        /// <param name="iusseTime">登记时间</param>
        /// <param name="carSpalcesNum">车位数</param>
        /// <param name="userName">车主名</param>
        /// <param name="mobNumber">手机号</param>
        /// <param name="homeAddress">住址</param>
        /// <param name="parkSpace">车场车位号</param>
        /// <returns></returns>
        public static object MthCarIssueSend(string parkKey, string carNo, string carType, string carSpaceNo, string beginTime, string endTime, string iusseTime, string carSpalcesNum, string userName, string mobNumber, string homeAddress, string parkSpace = "", string machineNo = "")
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    carType = carType,
                    carSpaceNo = carSpaceNo,
                    beginTime = beginTime,
                    endTime = endTime,
                    iusseTime = iusseTime,
                    carSpalcesNum = carSpalcesNum,
                    userName = userName,
                    mobNumber = mobNumber,
                    homeAddress = homeAddress,
                    parkNo = parkSpace,
                    machineNo = machineNo
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_MthCarIssueSend, MiddlewareEventPriority.Delay_1000_3);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 月租车辆注销上报

        /// <summary>
        /// 月租车辆注销上报
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="cancelUser"></param>
        /// <param name="cancelTime"></param>
        /// <returns></returns>
        public static object MthCarFailSend(string parkKey, string carNo, string cancelUser, string cancelTime)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    cancelUser = cancelUser ?? "",
                    cancelTime = cancelTime
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_MthCarFailSend, MiddlewareEventPriority.Delay_1000_3);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 月租车充值上报

        /// <summary>
        /// 月租车充值上报
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="chargeTime"></param>
        /// <param name="chargeMoney"></param>
        /// <param name="operatorName"></param>
        /// <returns></returns>
        public static object MthCarChargeSend(string parkKey, string carNo, string carType, string beginTime, string endTime, string chargeTime, string chargeMoney, string operatorName, string payOrderNo)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    carType = carType,
                    beginTime = beginTime,
                    endTime = endTime,
                    chargeTime = chargeTime,
                    chargeMoney = chargeMoney,
                    operatorName = operatorName,
                    payOrderNo = payOrderNo
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_MthCarChargeSend, MiddlewareEventPriority.Delay_1000_3);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 车辆黑名单上报

        /// <summary>
        /// 车辆黑名单上报
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="userName"></param>
        /// <param name="phone"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="iusseTime"></param>
        /// <param name="remark"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public static object CarBlackList(string parkKey, string carNo, string userName, string phone, string beginTime, string endTime, string iusseTime, string remark, string status, string blackListNo)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    userName = userName,
                    phone = phone,
                    beginTime = beginTime,
                    endTime = endTime,
                    iusseTime = iusseTime,
                    remark = remark,
                    status = status,
                    blackListNo = blackListNo
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_BlackCar, MiddlewareEventPriority.Delay_1000_4);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 新增人事信息上报

        /// <summary>
        /// 新增人事信息上报
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="carNo"></param>
        /// <param name="userNo"></param>
        /// <param name="userName"></param>
        /// <param name="sex"></param>
        /// <param name="homeAddress"></param>
        /// <param name="mobNumber"></param>
        /// <param name="carSpalcesNum"></param>
        /// <returns></returns>
        public static object PersonSend(string parkKey, string carNo, string userNo, string userName, string sex, string homeAddress, string mobNumber, string carSpalcesNum)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    userName = userName,
                    userNo = userNo,
                    sex = sex,
                    homeAddress = homeAddress,
                    mobNumber = mobNumber,
                    carSpalcesNum = carSpalcesNum
                };

                return Create(parkKey, model, PushEventCommand.SFM_UPDATE_PersonSend, MiddlewareEventPriority.Delay_1000_3);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 上传线下缴费记录

        /// <summary>
        /// 上传线下缴费记录
        /// </summary>
        public static object PayOrder(string parkkey, Model.PayOrder Item3, List<Model.CouponRecord> Item4, int pushEventSuccess = 0, string payOrderDesc = "")
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                string PayOrder_EnterTime = null;
                if (Item3 != null && Item3.PayOrder_EnterTime != null)
                {
                    PayOrder_EnterTime = Item3.PayOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }

                if (Item4 != null && Item4.Count == 0) Item4 = null;

                string payedTime = Item3?.PayOrder_PayedTime == null ? "" : Item3?.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                string moeny = Item3?.PayOrder_Money == null ? "" : Item3?.PayOrder_Money.Value.ToString();
                //string couponno = Item3.PayOrder_CouponRecordNo;
                //if (string.IsNullOrWhiteSpace(Item3.PayOrder_CouponRecordNo))
                //{
                //    couponno = Item4?.Find(x => x.CouponRecord_ParkOrderNo == Item3?.PayOrder_ParkOrderNo)?.CouponRecord_No ?? "";
                //}
                string dismoney = Item3?.PayOrder_DiscountMoney == null ? "" : Item3?.PayOrder_DiscountMoney.Value.ToString();
                string payedmoney = Item3?.PayOrder_PayedMoney == null ? "" : Item3?.PayOrder_PayedMoney.Value.ToString();
                string name = Item3?.PayOrder_OperatorName ?? "";
                string paycode = Item3?.PayOrder_PayTypeCode ?? "";

                return BLL.PushEvent.PayOrder(parkkey, Item3?.PayOrder_No, Item3?.PayOrder_ParkOrderNo, Item3.PayOrder_CarNo, Item3.PayOrder_Category, payedTime, moeny, PayOrder_EnterTime, payedmoney, dismoney, name, paycode, Item4, pushEventSuccess, payOrderDesc);
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 上传线下缴费记录
        /// </summary>
        public static object PayOrder(string parkkey, Model.ParkOrder Item1, Model.PayOrder Item3, List<Model.CouponRecord> Item4)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                Model.CarCardType carCardType = BLL.CarCardType.GetEntity(Item1?.ParkOrder_CarCardType) ?? new Model.CarCardType();

                string PayOrder_EnterTime = null;
                if (Item3 != null && Item3.PayOrder_EnterTime != null)
                {
                    PayOrder_EnterTime = Item3.PayOrder_EnterTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }

                if (Item4 != null && Item4.Count == 0) Item4 = null;

                string payOrderDesc = "";
                if (!string.IsNullOrEmpty(Item1?.ParkOrder_CarNo))
                {
                    var car = AppBasicCache.GetElement(AppBasicCache.GetCar, Item1?.ParkOrder_CarNo);
                    if (car != null && car.Car_Category == "3657")
                    {
                        payOrderDesc = AppBasicCache.GetElement(AppBasicCache.GetOwner, car.Car_OwnerNo)?.Owner_Balance?.ToString() ?? "";
                        if (!string.IsNullOrEmpty(payOrderDesc)) payOrderDesc = "储值余额:" + payOrderDesc;
                    }
                }

                return BLL.PushEvent.PayOrder(
                    parkkey,
                    Item3?.PayOrder_No,
                    Item1?.ParkOrder_No,
                    Item1?.ParkOrder_CarNo,
                    carCardType.CarCardType_Category,
                    Item3?.PayOrder_PayedTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                    Item3?.PayOrder_Money.Value.ToString(),
                    PayOrder_EnterTime,
                    Convert.ToString(Item3?.PayOrder_PayedMoney ?? (decimal)(0)),
                    Item3?.PayOrder_DiscountMoney.Value.ToString(),
                    Item1?.ParkOrder_OutAdminName,
                    Item3?.PayOrder_PayTypeCode,
                    Item4,
                    0,
                    payOrderDesc);
            }
            else
            {
                return 0;
            }
        }


        /// <summary>
        /// 上传线下缴费记录
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="payOrderNo"></param>
        /// <param name="orderNo"></param>
        /// <param name="carNo"></param>
        /// <param name="carType"></param>
        /// <param name="payTime"></param>
        /// <param name="payMoney"></param>
        /// <param name="enterTime"></param>
        /// <param name="conponList"></param>
        /// <param name="payedMoney"></param>
        /// <param name="operatorName"></param>
        /// <param name="payType"></param>
        /// <returns></returns>
        public static object PayOrder(string parkKey, string payOrderNo, string orderNo, string carNo, string carType, string payTime, string payMoney,
            string enterTime, string payedMoney, string payOrderDiscountMoney, string operatorName, string payType,
            List<Model.CouponRecord> conponList = null, int pushEventSuccess = 0, string payOrderDesc = "")
        {
            if (string.IsNullOrWhiteSpace(payOrderNo)) return 0;
            if (!string.IsNullOrWhiteSpace(parkKey))
                payOrderNo = payOrderNo.Replace(parkKey, ""); //上传智慧停车平台去掉parkKey

            if (AppBasicCache.IsWritePushEventMsg)
            {
                if (payType == Model.EnumOrderType.StorePayed.ToString()) payType = Model.EnumOrderType.Temp.ToString();

                var couponKey = "";
                List<object> couponList = new List<object>();
                conponList?.ForEach(conpou =>
                {
                    couponKey = conpou.CouponRecord_No;
                    if (conpou.CouponRecord_OnLine == 2)//时段全免(线下绑定)
                    {
                        couponKey = conpou.CouponRecord_ParkDiscountSetNo;
                    }

                    var useMin = Utils.ObjectToInt(conpou.CouponRecord_DiscountMin ?? 0);
                    if (conpou.CouponRecord_Paid > 0 && useMin == 0) useMin = 1;
                    var coupon1 = new
                    {
                        couponKey = couponKey,
                        virtualKey = (conpou.CouponRecord_CouponCode == "105" ? conpou.CouponRecord_No : ""),
                        paidMoney = conpou.CouponRecord_Paid ?? 0,
                        useMinute = useMin
                    };
                    couponList.Add(coupon1);
                });

                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    payOrderNo = payOrderNo,
                    orderNo = orderNo ?? "",
                    carNo = carNo ?? "",
                    carType = carType ?? "",
                    payTime = payTime ?? "",
                    payMoney = payMoney ?? "",
                    enterTime = enterTime ?? "",
                    couponKey = couponKey,
                    couponMoney = payOrderDiscountMoney ?? "",
                    payedMoney = payedMoney ?? "",
                    operatorName = operatorName ?? "",
                    payType = payType ?? "",
                    couponList = TyziTools.Json.ToString(couponList),
                    payOrderDesc = payOrderDesc ?? ""
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_PayOrder, MiddlewareEventPriority.Delay_1000_2, 0, pushEventSuccess);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 跟车记录新增上报

        public static object CarFollowAdd(string parkKey, Model.ControlEvent model)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                string gate = (model.ControlEvent_Gate == 0 ? 1 : 0).ToString();
                string time = model.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss");
                string image = model.ControlEvent_BigImg;
                string status = "0";
                var res = CarFollowAdd(parkKey, model.ControlEvent_No, gate, time, model.ControlEvent_PasswayNo, image, model.ControlEvent_Video, status, model.ControlEvent_Remark, model.ControlEvent_ParkOrderNo);

                return res;
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 添加完整追缴事件
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="model"></param>
        /// <param name="outTime"></param>
        /// <param name="handleTime"></param>
        /// <param name="operatorName"></param>
        /// <param name="money"></param>
        /// <returns></returns>
        public static object CarFollowFullAdd(string parkKey, Model.ControlEvent model, string outTime = null, string outImage = "", string outVideo = "", string handleTime = null, string operatorName = "", string money = "0")
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                string gate = (model.ControlEvent_Gate == 0 ? 1 : 0).ToString();
                string time = model.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss");
                string image = model.ControlEvent_BigImg;
                string video = model.ControlEvent_Video;
                var obj = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carFollowNo = model.ControlEvent_No,
                    carNo = model.ControlEvent_CarNo,
                    orderNo = model.ControlEvent_ParkOrderNo ?? "",
                    gate = model.ControlEvent_Gate,
                    time = time,
                    outTime = outTime,
                    handleTime = handleTime,
                    image = image ?? "",
                    outImage = outImage,
                    video = video ?? "",
                    outVideo = outVideo,
                    status = (model.ControlEvent_Status == 4 ? 2 : (model.ControlEvent_Status != 0 && model.ControlEvent_Status != 1 ? 0 : model.ControlEvent_Status)), //兼容平台处理状态：0未处理,1忽略,2已处理
                    payStatus = model.ControlEvent_Status == 4 ? 1 : 0, //兼容平台支付状态：0未缴,1已缴
                    operatorName = operatorName,
                    money = money,
                    remark = model.ControlEvent_Remark
                };

                return Create(parkKey, obj, PushEventCommand.SFM_UPDATE_CarFollowAdd, MiddlewareEventPriority.Delay_500_2);
            }
            else
            {
                return 0;
            }
        }


        /// <summary>
        /// 跟车记录新增上报
        /// </summary>
        /// <param name="parkKey">车场key</param>
        /// <param name="carFollowNo">跟车记录编号</param>
        /// <param name="gate">进出口标识  （1入口 2出口）</param>
        /// <param name="time">记录时间</param>
        /// <param name="passwayno">车道编号</param>
        /// <param name="image">图片链接</param>
        /// <param name="video">视频链接</param>
        /// <param name="status">状态，0-未处理，1-已忽略，2-已处理</param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static object CarFollowAdd(string parkKey, string carFollowNo, string gate, string time, string passwayno, string image, string video, string status, string remark, string orderNo = "")
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                string img = UploadImgToAliyun(image, parkKey, orderNo: orderNo);
                string vd = UploadFileToAliyun(video, parkKey);
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carFollowNo = carFollowNo,
                    gate = gate,
                    time = time,
                    vehicleLaneNo = passwayno ?? "",
                    image = img ?? "",
                    video = vd ?? "",
                    status = status,
                    remark = remark ?? ""
                };

                return Create(parkKey, model, PushEventCommand.SFM_UPDATE_CarFollowAdd, MiddlewareEventPriority.Delay_500_2);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 跟车记录变更上报

        /// <summary>
        /// 跟车记录变更上报
        /// </summary>
        /// <param name="parkKey">车场key</param>
        /// <param name="carFollowNo">跟车记录编号</param>
        /// <param name="carNo">车牌号</param>
        /// <param name="orderNo">停车订单号</param>
        /// <param name="outTime">出场时间</param>
        /// <param name="handleTime">处理时间</param>
        /// <param name="status">记录状态（1忽略，2关联订单）</param>
        /// <param name="operatorName">操作人</param>
        /// <param name="remark">备注</param>
        /// <param name="payStatus">支付状态：0-未追缴，1-已追缴</param>
        /// <returns></returns>
        public static object CarFollowUpdate(string parkKey, string carFollowNo, string carNo, string orderNo, string outTime, string handleTime, string status, string operatorName, string image, string video, string remark, string money, int payStatus = 0)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                string img = UploadImgToAliyun(image, parkKey, orderNo: orderNo);
                string vd = UploadFileToAliyun(video, parkKey);

                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carFollowNo = carFollowNo,
                    carNo = carNo,
                    orderNo = orderNo,
                    outTime = outTime,
                    handleTime = handleTime,
                    status = status,
                    operatorName = operatorName ?? "",
                    image = img ?? "",
                    video = vd ?? "",
                    remark = remark ?? "",
                    money = money,
                    payStatus = payStatus
                };

                return Create(parkKey, model, PushEventCommand.SFM_UPDATE_CarFollowUpdate, MiddlewareEventPriority.Delay_500_2);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 倒车记录上报

        /// <summary>
        /// 倒车记录上报
        /// </summary>
        /// <param name="parkKey">车场key</param>
        /// <param name="carBackNo">倒车记录编号</param>
        /// <param name="carNo">车牌号</param>
        /// <param name="orderNo">停车订单号</param>
        /// <param name="gate">进出口标识  （1入口 2出口）</param>
        /// <param name="time">记录时间</param>
        /// <param name="vehicleLaneNo">车道编号</param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static object CarBack(string parkKey, string carBackNo, string carNo, string orderNo, string gate, string time, string vehicleLaneNo, string remark)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carBackNo = carBackNo,
                    carNo = carNo,
                    orderNo = orderNo,
                    gate = gate,
                    time = time,
                    vehicleLaneNo = vehicleLaneNo,
                    remark = remark ?? ""
                };

                return Create(parkKey, model, PushEventCommand.SFM_UPDATE_CarBack, MiddlewareEventPriority.Delay_1000_3);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 停车订单状态变更

        /// <summary>
        /// 停车订单状态变更
        /// </summary>
        /// <param name="parkKey">车场key</param>
        /// <param name="carBackNo">倒车记录编号</param>
        /// <param name="carNo">车牌号</param>
        /// <param name="orderNo">停车订单号</param>
        /// <param name="gate">进出口标识  （1入口 2出口）</param>
        /// <param name="time">记录时间</param>
        /// <param name="vehicleLaneNo">车道编号</param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static object CarOrderStatus(string parkKey, string carNo, string orderNo, string orderStatus, string time, string remark)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    carNo = carNo,
                    orderNo = orderNo,
                    orderStatus = orderStatus,
                    time = time,
                    remark = remark ?? ""
                };

                return Create(parkKey, model, PushEventCommand.SFM_UPDATE_CarOrderStatus, MiddlewareEventPriority.Delay_2000_2);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region 云托管事件信息上传

        /// <summary>
        /// 云托管事件信息上传
        /// </summary>
        /// <param name="parkKey">车场key</param>
        /// <param name="alarmno">事件编号</param>
        /// <param name="heppentime">发生时间</param>
        /// <param name="lineboxip">岗亭IP地址</param>
        /// <param name="lineboxname">岗亭名称</param>
        /// <param name="content">内容信息</param>
        /// <param name="level">等级</param>
        /// <param name="remark">备注</param>
        /// <returns></returns>
        public static object CloudUploadEvent(string parkKey, string alarmno, string heppentime, string lineboxip, string lineboxname, string content, string level, string remark)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    key = parkKey,
                    alarmno,
                    addtime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"),
                    heppentime,
                    lineboxip,
                    lineboxname,
                    content,
                    level,
                    remark
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_CloudUploadEvent, MiddlewareEventPriority.Delay_CloudUploadEvent);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #region

        /// <summary>
        /// 充电桩记录出场
        /// </summary>
        /// <param name="parkKey">车场KEY</param>
        /// <param name="carNo">车牌号码</param>
        /// <param name="outTime">出场时间</param>
        /// <returns></returns>
        public static object ChargePieCarOut(string parkKey, string carNo, string outTime)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var model = new
                {
                    carNo,
                    outTime
                };

                return Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_ChargePieCarOut, MiddlewareEventPriority.Delay_1000_5);
            }
            else
            {
                return 0;
            }
        }

        #endregion

        #endregion


        #region 上传停车订单

        /// <summary>
        /// 上传车辆停车订单信息
        /// </summary>
        /// <param name="parkKey"></param>
        /// <param name="order"></param>
        /// <param name="priority"></param>
        /// <param name="respace"></param>
        /// <param name="isNowGet"></param>
        /// <returns></returns>
        public static object UploadParkOrder(string parkKey, Model.ParkOrder order, MiddlewareEventPriority priority = MiddlewareEventPriority.Delay_500_1, bool respace = true, bool isNowGet = false)
        {
            if (AppBasicCache.IsWritePushEventMsg)
            {
                var card = BLL.CarCardType.GetEntity("CarCardType_Category,CarCardType_No", $"CarCardType_No='{order.ParkOrder_CarCardType}'");
                PlateColorConvert.ToENCode(order.ParkOrder_CarTypeName, out var plateColorCode);

                var model = new
                {
                    version = Config.AppSettingConfig.ApiVersion,
                    key = parkKey,
                    orderNo = order.ParkOrder_No,
                    carNo = order.ParkOrder_CarNo,
                    plateColor = plateColorCode.ToString(),
                    money = order.ParkOrder_TotalAmount?.ToString() ?? "0.00",
                    enterTime = order.ParkOrder_EnterTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    outTime = order.ParkOrder_OutTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    carType = card?.CarCardType_Category ?? "",
                    enterGateName = order.ParkOrder_EnterPasswayName ?? "",
                    outGateName = order.ParkOrder_OutPasswayName ?? "",
                    operatorName = order.ParkOrder_EnterAdminName ?? "",
                    enterImage = order.ParkOrder_EnterImgPath,
                    outImage = order.ParkOrder_OutImgPath,
                    status = order.ParkOrder_StatusNo,
                    remark = order.ParkOrder_Remark
                };
                var res1 = Create(parkKey, model, PushEventCommand.SFM_PKCLD_EVENT_ParkOrder, priority);
                return res1;
            }
            else
            {
                return null;
            }
        }

        #endregion

    }

    /// <summary>
    /// 跟车记录变更上报 状态值
    /// </summary>
    public class CarFollowUpdate_Status
    {
        /// <summary>
        /// 忽略
        /// </summary>
        public const string Ignore = "1";

        /// <summary>
        /// 关联订单
        /// </summary>
        public const string BindOrder = "2";
    }

    /// <summary>
    /// 车牌颜色转换
    /// </summary>
    public class PlateColorConvert
    {
        /// <summary>
        /// 数值代码转换成颜色名称，
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static string ToName(int code)
        {
            if (code == 0)
                return "无牌车";

            else if (code == 1)
                return "蓝牌车";

            else if (code == 2)
                return "黄牌车";

            else if (code == 3)
                return "白牌车";

            else if (code == 4)
                return "黑牌车";

            else if (code == 5)
                return "绿牌车";

            else if (code == 6)
                return "黄绿牌车";
            else if (code == 7)
                return "0牌车";

            return "无牌车";
        }

        /// <summary>
        /// 颜色名称转字母代码(兼容微信、支付宝)
        /// </summary>
        /// <param name="name">
        /// BLUE：蓝 - 1。
        /// GREEN：绿 - 5。
        /// YELLOW：黄 - 2。
        /// WHITE：白 - 3。
        /// BLACK：黑 - 4。
        /// LIMEGREEN：黄绿色 - 6。
        /// </param>
        /// <returns></returns>
        public static string ToENCode(string name, out int code)
        {
            string EnCode = "BLUE"; //默认蓝色
            code = 1;

            if (name.Contains("蓝"))
            {
                EnCode = "BLUE";
                code = 1;
            }
            else if (name.Contains("绿"))
            {
                EnCode = "GREEN";
                code = 5;
                if (name.Contains("黄绿"))
                {
                    EnCode = "LIMEGREEN";
                    code = 6;
                }
            }
            else if (name.Contains("黄"))
            {
                EnCode = "YELLOW";
                code = 2;
                if (name.Contains("黄绿"))
                {
                    EnCode = "LIMEGREEN";
                    code = 6;
                }
            }
            else if (name.Contains("白"))
            {
                EnCode = "WHITE";
                code = 3;
            }
            else if (name.Contains("黑"))
            {
                EnCode = "BLACK";
                code = 4;
            }

            return EnCode;
        }

        /// <summary>
        /// 数值代码转换成字母代码(兼容微信、支付宝)
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        public static string ToENCode(int code)
        {
            var name = ToName(code);

            return ToENCode(name, out code);
        }
    }
}