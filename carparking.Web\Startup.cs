﻿using carparking.BLL;
using carparking.Common;
using carparking.Config;
using carparking.Library;
using carparking.Web.OpenApiV2;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using carparking.BLL.Cache;
using carparking.PassTool;
using System.Threading.Tasks;
using TcpConnPools.Camera;
using System.Timers;
using carparking.SentryBox;
using Asp.Versioning;
using TcpConnPools;
using carparking.AutoSentryBox;
using System.Net;
using carparking.SentryBox.Server;

namespace carparking.Web
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.Configure<CookiePolicyOptions>(options =>
            {
                // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                //options.CheckConsentNeeded = context => true;
                //options.MinimumSameSitePolicy = SameSiteMode.None;
                options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
                options.OnAppendCookie = cookieContext =>
                    CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
                options.OnDeleteCookie = cookieContext =>
                    CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
            });
            services.AddMemoryCache(); //使用本地缓存必须添加

            services.AddControllersWithViews();
            services.AddControllersWithViews().AddRazorRuntimeCompilation();//view编辑后刷新
            services.AddControllers().AddNewtonsoftJson(opt =>
            {
                opt.SerializerSettings.ContractResolver = new Newtonsoft.Json.Serialization.DefaultContractResolver();
                opt.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
            });

            services.Configure<FormOptions>(options =>
            {
                //64M
                options.MultipartBodyLengthLimit = 1024 * 1024 * 64;
            });
            services.Configure<KestrelServerOptions>(options => { options.AllowSynchronousIO = true; });
            services.Configure<IISServerOptions>(options => { options.AllowSynchronousIO = true; });

            services.AddCors(options => { options.AddPolicy("any", builder => { builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader(); }); });

            //依赖注入
            //AddTransient 瞬时模式：每次请求，都获取一个新的实例。即使同一个请求获取多次也会是不同的实例
            //AddScoped：每次请求，都获取一个新的实例。同一个请求获取多次会得到相同的实例
            //AddSingleton 单例模式：每次都获取同一个实例
            services.AddHttpContextAccessor();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            services.AddControllersWithViews(option => { option.Filters.Add<Filters.ExceptionFilter>(); });

            //services.AddHostedService<SentryBoxInit>();

            AppBasicCache.SystemRunTime = DateTime.Now;
            LogManagementMap.WriteToFile("当前版本号：" + AppSettingConfig.ApiVersion + " " + AppSettingConfig.ApiVersion_FB);

            //添加版本号
            services.AddApiVersioning(options =>
            {
                options.DefaultApiVersion = new ApiVersion(2, 0);
            });

            services.AddControllers(options =>
            {
                options.Filters.Add<InputSanitizationFilter>();
            });

            if (AppBasicCache.IsWindows)
            {
                try
                {
                    string sqlitePath = AppContext.BaseDirectory + @"/B30Cache";
                    if (!Directory.Exists(sqlitePath))
                    {
                        Directory.CreateDirectory(sqlitePath);
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"创建目录B30Cache异常：{ex.ToString()}");
                }

                try
                {
                    // 检查表是否存在
                    bool tableExists = BLL.BaseBLL._SQLiteExecute(
                        @"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='CalcOrder';"
                    ) > 0;

                    //if (!tableExists)
                    //{
                    //    string createTableSql = @"
                    //                           CREATE TABLE IF NOT EXISTS calcorder (
                    //                                CalcOrder_ParkOrderNo TEXT NOT NULL,
                    //                                CalcOrder_PasswayNo TEXT,
                    //                                CalcOrder_Type INTEGER,
                    //                                CalcOrder_CalcContent TEXT,
                    //                                CalcOrder_AddTime DATETIME,
                    //                                PRIMARY KEY (CalcOrder_ParkOrderNo, CalcOrder_Type)
                    //                            );
                    //                            -- 为了保留索引（虽然主键已有索引），其余索引可单独创建：
                    //                            CREATE INDEX IF NOT EXISTS idx_CalcOrder_PasswayNo ON calcorder (CalcOrder_PasswayNo);
                    //                            CREATE INDEX IF NOT EXISTS idx_CalcOrder_AddTime ON calcorder (CalcOrder_AddTime);
                    //                        ";

                    //    var r = BLL.BaseBLL._SQLiteExecute(createTableSql);
                    //    if (r < 1)
                    //    {
                    //        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"创建表calcorder，执行失败！");
                    //    }
                    //}

                    AppBasicCache.IsInitSQLite = true;
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"创建表CalcOrder异常：{ex.ToString()}");
                }
            }

            try
            {
                //初始化跨平台相机设备连接
                DevicePool.Instance.Initialize();
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"初始化相机设备连接异常：{ex.ToString()}");
            }

            _ = Task.Run(async () =>
            {
                await Task.Delay(1000);

            CheckOne:
                try
                {
                    AppBasicCache.IpList = IpAddressUtil.GetSentryIP();
                    AppBasicCache.Ip = AppBasicCache.IpList?.FirstOrDefault() ?? "127.0.0.1";
                    string path = AppDomain.CurrentDomain.BaseDirectory;

                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                    {
                        var configIp = ConfigurationMap.GetModel.SentryHostIP;
                        path = path.Substring(0, path.LastIndexOf("\\"));
                        path = path.Substring(0, path.LastIndexOf("\\"));
                        ConfigurationMap.Path = path + "\\Config\\ManualSentryBox.config";
                        BLL.CommonBLL.CopyManualSentryBoxConfig(ConfigurationMap.Path, Path.Combine(path + "\\Config", "ManualSentryBox_backup.config"));
                    }
                    else
                    {
                        var baseDir1 = $"{AppDomain.CurrentDomain.BaseDirectory}../Config/";
                        var baseDir2 = $"/mnt/sda1/b30/Config/";
                        if (!Directory.Exists(baseDir2))
                            Directory.CreateDirectory(baseDir2);

                        ConfigurationMap.Path = baseDir2 + "ManualSentryBox.config";

                    }
                    LinuxApi.GetHttpDeviceSN();
                    AppBasicCache.idToken = "1000";
                    //写入版本号
                    SentryBoxInit.LinuxSaveVersionFile();
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "系统启动异常：" + ex.ToString());
                    await Task.Delay(10000);
                    goto CheckOne;
                }


                try
                {
                    #region 启用线程池

                    var syncNum = Environment.ProcessorCount * 4;
                    CustomThreadPool.SyncTaskPool = new CustomThreadPool("岗亭", syncNum < 10 ? syncNum + 5 : syncNum);
                    CustomThreadPool.SyncTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"启用岗亭异步处理线程数：{(syncNum < 10 ? syncNum + 5 : syncNum)}");

                    CustomThreadPool.PictureTaskPool = new CustomThreadPool("识别图片", 1);
                    CustomThreadPool.PictureTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用识别图片异步处理线程数：1");

                    CustomThreadPool.SystemTaskPool = new CustomThreadPool("心跳线程", 1);
                    CustomThreadPool.SystemTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用心跳线程数：1");

                    CustomThreadPool.ScheduledTaskPool = new CustomThreadPool("定时任务", 4);
                    CustomThreadPool.ScheduledTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用定时任务异步处理线程数：4");

                    CustomThreadPool.WebSocketMessageTaskPool = new CustomThreadPool("WS消息", 2);
                    CustomThreadPool.WebSocketMessageTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用WS消息任务异步处理线程数：2");

                    CustomThreadPool.ApiTaskPool = new CustomThreadPool("API消息", 2);
                    CustomThreadPool.ApiTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用API消息任务异步处理线程数：2");

                    CustomThreadPool.MiddleTaskPool = new CustomThreadPool("中间件", 3);
                    CustomThreadPool.MiddleTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用中间件任务异步处理线程数：3");

                    CustomThreadPool.WebTaskPool = new CustomThreadPool("Web", 3);
                    CustomThreadPool.WebTaskPool.Start();
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "启用Web任务异步处理线程数：3");
                    #endregion

                    ThreadPoolMonitor.StartMonitoring();//异步线程监控

                    if (AppBasicCache.GetParking?.Parking_TestMode?.ToString() != "1" && !AppBasicCache.IsWindows)
                    {
                        ParkTimer.timer1 = new System.Timers.Timer
                        {
                            Interval = 1000,
                            AutoReset = true,
                            Enabled = true
                        };
                        ParkTimer.timer1.Elapsed += ParkTimer.TimerHeart;
                        ParkTimer.timer1.Start();
                    }

                    Task timeTask = new Task(delegate { PassTool.GoHelper.TimeAuth(); }, GoHelper.sv.Token, TaskCreationOptions.LongRunning);
                    timeTask.Start();
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "系统启动异常1，准备重启服务：" + ex.ToString());
                    ServiceHelper.SendServiceReboot(ServicesEnum.Web);
                    await Task.Delay(60000 * 5);
                }

            CheckThree:
                try
                {
                    int checkCount = 0;

                    if (!AppBasicCache.IsWindows && AppSettingConfig.SentryMode != VersionEnum.WindowsStandard)
                    {
                        //连接MYSQL失败，则检测MYSQL密码
                        bool isSuccess = false;
                    SetMySQLPwd:
                        try
                        {
                            var setpwdResult = Common.CmdHelper.RunCmdToLinux(AppDomain.CurrentDomain.BaseDirectory + $"/wwwroot/config/mysqlsetpwd.sh");
                            setpwdResult = setpwdResult?.Trim() ?? "";
                            if (setpwdResult.Contains("ERROR_TYPE=AUTH"))
                            {
                                isSuccess = true;
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"连接MYSQL密码错误,{setpwdResult}");
                            }
                            else if (setpwdResult.Contains("ERROR_TYPE=CONNECTION"))
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"无法连接 MySQL 服务器,{setpwdResult}");
                            else if (setpwdResult.Contains("ERROR_TYPE=CHANGE_FAIL"))
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新MYSQL密码失败,{setpwdResult}");
                            else if (setpwdResult.Contains("RESULT=SUCCESS"))
                            {
                                isSuccess = true;
                                LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "更新MYSQL密码成功");
                                ServiceHelper.SendServiceReboot(ServicesEnum.Web);//重启服务
                            }
                            else
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新MYSQL密码出现未知错误：{setpwdResult}");
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"更新MYSQL密码异常,{ex.ToString()}");
                        }

                        if (!isSuccess)
                        {
                            await Task.Delay(5000);
                            goto SetMySQLPwd;
                        }

                        await Task.Delay(2000);
                    }

                CheckDB:

                    if (!await BLL.CommonBLL.CheckMySQLConnection())
                    {
                        checkCount = 0;
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "数据库未连接，正在检测中...");
                        await Task.Delay(2000);
                        goto CheckDB;
                    }
                    else
                    {

                        try
                        {
                            Model.Parking parking = BLL.Parking.GetEntityByWhere("");
                        }
                        catch (Exception ex)
                        {
                            checkCount = 0;
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "数据库异常：" + ex.Message);
                            await Task.Delay(2000);
                        }

                        checkCount++;
                        if (checkCount < 3)
                        {
                            await Task.Delay(500);
                            goto CheckDB;
                        }
                    }

                    BLL.Parking.InitCloudParking();

                    ParkTimer.CheckIP(AppBasicCache.IpList);

                    RSocket.PrintLocalHost();

                    //建库 BLL.DataBase.InitTable();

                    //更新数据库
                    var r = new DataBase.Altertable().Execute();
                    if (!r?.success ?? false)
                    {
                        await Task.Delay(2000);
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "更新数据结构失败，准备重新检测");
                        goto CheckThree;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "系统启动异常3：" + ex.ToString());
                    await Task.Delay(10000);
                    goto CheckThree;
                }

            CheckFour:
                try
                {
                    //判断是否是windows系统
                    if (AppBasicCache.IsWindows)
                    {
                        //设置配置文件路径
                        string path1 = AppDomain.CurrentDomain.BaseDirectory; //程序启动路径
                        path1 = path1.Substring(0, path1.LastIndexOf("\\"));
                        path1 = path1.Substring(0, path1.LastIndexOf("\\"));
                        ConfigurationMap.Path = path1 + "\\Config\\ManualSentryBox.config";
                        BLL.CommonBLL.CopyManualSentryBoxConfig(ConfigurationMap.Path, Path.Combine(path1 + "\\Config", "ManualSentryBox_backup.config"));
                    }
                    else
                    {
                        //设置配置文件路径
                        var baseDir1 = $"{AppDomain.CurrentDomain.BaseDirectory}../Config/";
                        var baseDir2 = $"/mnt/sda1/b30/Config/";
                        if (!Directory.Exists(baseDir2))
                            Directory.CreateDirectory(baseDir2);
                        ConfigurationMap.Path = baseDir2 + "ManualSentryBox.config";

                        BLL.CommonBLL.CopyManualSentryBoxConfig(ConfigurationMap.Path, Path.Combine("/mnt/app2/b30/Config/", "ManualSentryBox.config"));
                    }

                    Model.SentryHost sentryHost = AppBasicCache.GetSentryHost();
                    if (sentryHost != null)
                    {
                        if (!AppBasicCache.IpList.Contains(AppSettingConfig.SiteDomain_WebIP))
                            sentryHost.SentryHost_IP = AppBasicCache.Ip;
                        else
                            AppBasicCache.Ip = sentryHost.SentryHost_IP;
                    }
                    if (sentryHost != null && sentryHost.SentryHost_ID != null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "Sentry:" + TyziTools.Json.ToString(sentryHost));
                        BLL.SentryHost._UpdateByModelByID(TyziTools.Json.ToObject<Model.SentryHost>(TyziTools.Json.ToString(sentryHost)));
                    }

                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "系统启动异常4：" + ex.ToString());
                    await Task.Delay(10000);
                    goto CheckFour;
                }

                try
                {
                    if (File.Exists(ConfigurationMap.Path))
                    {
                        await SentryBoxInit.StartSentryService();

                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"岗亭初始化服务已启动完成！");
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"岗亭配置文件未找到：{ConfigurationMap.Path}");
                    }

                    Library.ParkTimer.ForNetWorker();
                    //检查缓存
                    if (AppSettingConfig.SentryMode != VersionEnum.CloudServer) SentryCacheWorker();

                    // 初始化YM01设备绑定服务
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "YM01设备绑定服务已初始化");

                    // 启动后台任务等待系统完全初始化后执行YM01设备自动解绑
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            // 等待系统完全初始化
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "等待系统完全初始化后执行YM01设备自动解绑...");

                            var maxWaitTime = TimeSpan.FromMinutes(10); // 最大等待10分钟
                            var startTime = DateTime.Now;

                            while (!AppBasicCache.SysInitialization && DateTime.Now - startTime < maxWaitTime)
                            {
                                await Task.Delay(1000); // 每秒检查一次
                            }

                            if (!AppBasicCache.SysInitialization)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog,
                                    "等待系统初始化超时，跳过YM01设备自动解绑");
                                return;
                            }

                            // 系统已完全初始化，再等待额外的2秒确保稳定
                            await Task.Delay(2000);

                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog,
                                "系统已完全初始化，开始执行YM01设备启动时自动解绑");

                            var result = await VziCloudStartupUnbindService.ExecuteStartupUnbindAsync();
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog,
                                $"YM01设备启动时自动解绑执行完成，结果：{(result ? "成功" : "部分失败")}");
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog,
                                $"YM01设备启动时自动解绑执行异常：{ex.Message}");
                        }
                    });
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "系统启动异常4：" + ex.ToString());
                }

                try
                {
                    if (AppBasicCache.GetParking?.Parking_EnableNet == 1
                    || (AppBasicCache.CurrentSysConfigContent?.SysConfig_ThreeApiEnable == 1
                       && !string.IsNullOrEmpty(AppBasicCache.CurrentSysConfigContent?.SysConfig_ThreeApiUrl) && !string.IsNullOrEmpty(AppBasicCache.CurrentSysConfigContent?.SysConfig_Method)))
                        _ = carparking.CloudLink.CloudTask.ManagerMiddle(1, "Startup.Configure");//启动中间件
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "启动中间件异常：" + ex.ToString());
                }

            CheckSex:
                try
                {
                    var lst = BLL.BaseBLL._GetAllEntity(new Model.DeviceAuth(), "*", "");
                    lst.ForEach(x =>
                    {
                        AppBasicCache.AddOrUpdateElement(DevicePool.Instance.DeviceAuths, x.DeviceAuth_Key, x.DeviceAuth_Type + "," + x.DeviceAuth_Secret);
                    });
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "设备授权加载异常：" + ex.ToString());
                    await Task.Delay(10000);
                    goto CheckSex;
                }

                //盒子禁用定时备份
                if (!AppBasicCache.IsWindows && AppSettingConfig.SentryMode == "1")
                {
                    try
                    {
                        if (AppBasicCache.CurrentSysConfigContent.SysConfig_DBBack != 0)
                        {
                            AppBasicCache.CurrentSysConfigContent.SysConfig_DBBack = 0;
                            AppBasicCache.CurrentSysConfig.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(AppBasicCache.CurrentSysConfigContent));
                            var res = BaseBLL._Insert(AppBasicCache.CurrentSysConfig);
                            if (res < 0)
                            {
                                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "禁用数据库自动备份保存失败！");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "禁用数据库自动备份保存异常！" + ex.ToString());
                    }
                }


                try
                {
                    if (AppBasicCache.CurrentSysConfigContent.SysConfig_PwdType == null)
                    {
                        AppBasicCache.CurrentSysConfigContent.SysConfig_PwdType = 0;
                        AppBasicCache.CurrentSysConfig.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(AppBasicCache.CurrentSysConfigContent));
                        var res = BaseBLL._Insert(AppBasicCache.CurrentSysConfig);
                        if (res < 0)
                        {
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "启用强密码保存配置失败！");
                        }
                    }
                }
                catch (Exception ex)
                {

                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "启用强密码配置异常！" + ex.ToString());
                }

                try
                {
                    if (AppBasicCache.CurrentSysConfigContent?.SysConfig_ThreeApiEnable == 1)
                    {
                        await Task.Delay(5000);
                        //添加第三方接口服务对接
                        OpenApiServiceCollectionExtensions.AddOpenApiService();
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "启动第三方接口服务异常：" + ex.ToString());
                }

                if (AppBasicCache.GetParking?.Parking_TestMode?.ToString() == "1")
                {
                    LogManagementMap.WriteToFile(LoggerEnum.CallCenterLog, "当前属于测试模式");
                    AppSettingConfig.TestMode = "1";

                    try
                    {
                        //Parking_TestContent {"TaskTestInOutStatus":true,"TaskTestAutoPayStatus":false,"AutoInCar":true,"AutoOutCar":true,"AutoPay":true,"AutoPayType":0,
                        //"DelExitRecord":1000.0,"DelPayRecord":0.0,"DelCouponRecord":0.0,"DelOpenGateRecord":0.0,"DelShiftRecord":0.0,"DelReverseRecord":0.0}
                        if (!string.IsNullOrEmpty(AppBasicCache.GetParking.Parking_TestContent))
                        {
                            Test.Setting = TyziTools.Json.ToObject<TestSetting>(AppBasicCache.GetParking.Parking_TestContent);
                            LogManagementMap.WriteToFile(LoggerEnum.CallCenterLog, "测试模式配置：" + TyziTools.Json.ToString(Test.Setting));
                        }
                        else
                        {
                            Test.Setting = new TestSetting();
                            LogManagementMap.WriteToFile(LoggerEnum.CallCenterLog, "测试模式配置：" + TyziTools.Json.ToString(Test.Setting));
                        }

                        ParkTimer.READ_TIME = default;//心跳进程启动比较快，已经读取过一次硬盘信息。重置读取时间
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "测试模式配置加载异常：" + ex.Message, LogLevel.Error);
                    }

                    //ParkTimer.ClearBusiness();//本地调试

                    if (!AppBasicCache.IsWindows)
                    {
                        ParkTimer.timer1 = new System.Timers.Timer
                        {
                            Interval = 1000,
                            AutoReset = true,
                            Enabled = true
                        };
                        ParkTimer.timer1.Elapsed += ParkTimer.TimerHeart;
                        ParkTimer.timer1.Start();
                    }

                    try
                    {
                        await TestServerChannel.RunServerAsyn();
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "TCP测试服务启动异常：" + ex.Message, LogLevel.Error);
                    }

                    try
                    {
                        var count = AppBasicCache.GetAllPassway.Count == 0 ? 30 : AppBasicCache.GetAllPassway.Count;
                        CustomThreadPool.RecogTaskPool = new CustomThreadPool("测试模式", AppBasicCache.GetAllPassway.Count);
                        CustomThreadPool.RecogTaskPool.Start();
                        LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"启用测试模式异步处理线程数：{30}");
                    }
                    catch (Exception e)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "启用测试模式异常：" + e.Message, LogLevel.Error);
                    }

                    try
                    {
                        if (Test.Setting.TaskTestInOutStatus)
                        {
                            TestPlan.BenginInOutTask();
                        }
                        else if (Test.Setting.TaskTestInOutUnTime)
                        {
                            TestPlan.UntimeInOutTask();
                        }
                        else if (Test.Setting.TaskTestAutoPayStatus)
                        {
                            TestPlan.BenginAutoPayTask();
                        }
                    }
                    catch (Exception ex)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "测试模式自动启动异常：" + ex.Message, LogLevel.Error);
                    }
                }
                else
                {
                    AppSettingConfig.TestMode = "0";
                }
            });

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IHostApplicationLifetime lifetime)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
            }

            Common.HttpHelper.Configure(app.ApplicationServices.GetRequiredService<IHttpContextAccessor>());
            app.UseCors("any");
            app.UseStaticFiles();
            UseLocalService(app);

            app.UseCookiePolicy(); //部分浏览器不支持Cookie GDPR规范，例如IE、支付宝等
                                   //app.UseStaticHttpContext();
            app.Use(async (context, next) =>
            {
                try
                {
                    if (context.Request.Method == HttpMethods.Post && context.Request.ContentType == "application/json")
                    {
                        // 设置缓冲区阈值和总限制
                        context.Request.EnableBuffering(bufferThreshold: 1024 * 1024 * 30, bufferLimit: 1024 * 1024 * 100);

                        // 读取请求正文并重置位置
                        using (var reader = new StreamReader(context.Request.Body, leaveOpen: true))
                        {
                            var body = await reader.ReadToEndAsync();
                            context.Request.Body.Position = 0; // 重置流位置
                        }
                    }
                    await next();
                }
                catch (Exception ex)
                {
                    if (!context.Response.HasStarted)
                    {
                        context.Response.StatusCode = 500;
                        await context.Response.WriteAsync("An internal server error occurred.");
                    }
                }
            });
            app.UseRouting();
            app.UseEndpoints(endpoints =>
            {

                //endpoints.MapControllerRoute(
                //  name: "Sentry",
                //  pattern: "{controller=SentryLogin}/{action=Index}/{id?}"
                //);

                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Login}/{action=Index}/{id?}"
                );

            });

            //注册应用程序关闭事件
            lifetime.ApplicationStopping.Register(OnShutdown);
        }

        /// <summary>
        /// 应用程序关闭事件
        /// </summary>
        private void OnShutdown()
        {
            // 释放对象的代码
            //OpenApiV2.OpenApiServiceCollectionExtensions.StopOpenApiService();
        }

        /// <summary>
        /// 处理新版浏览器cookie策略
        /// </summary>
        /// <param name="httpContext"></param>
        /// <param name="options"></param>
        private void CheckSameSite(HttpContext httpContext, CookieOptions options)
        {
            if (options.SameSite == SameSiteMode.None)
            {
                var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
                // TODO: Use your User Agent library of choice here.
                //if (/* UserAgent doesn't support new behavior */)
                //{

                //}

                options.SameSite = SameSiteMode.Unspecified;
            }
        }

        /// <summary>
        /// 设置自定义存储目录访问路径
        /// </summary>
        /// <param name="app"></param>
        private void UseLocalService(IApplicationBuilder app)
        {
            List<LocalFile.UseLocalStaticFiles> paths = new List<LocalFile.UseLocalStaticFiles>();

            //string defaultPath = LocalFile.GetRootPath();
            //paths.Add(new LocalFile.UseLocalStaticFiles() { directory = defaultPath, requestPath = "DOCM" });
            //paths.Add(new LocalFile.UseLocalStaticFiles() { directory = LocalFile.GetRootUserImage(), requestPath = "USERIMAGE" });
            if (!AppBasicCache.IsWindows)
            {
                paths.Add(new LocalFile.UseLocalStaticFiles() { directory = "/mnt/sda2/b30/backup", requestPath = "Backup" });
            }

            var path1 = AppBasicCache.IsWindows ? AppDomain.CurrentDomain.BaseDirectory : "/mnt/sda1/b30/CameraCaptures";
            if (path1.Contains("Web"))
            {
                path1 = path1.Substring(0, path1.LastIndexOf("Web")) + "CameraCaptures";
            }

            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "本地路径：" + path1, LogLevel.Info);
            BLL.ImageTools.LocalFilePath = AppBasicCache.strImgpath = CameraGlobal.strImgpath = path1;

            //if (AppCache.IsWindows)
            //{
            Model.SysConfig sysConfig = BLL.SysConfig.GetEntity();
            if (sysConfig != null)
            {
                Model.SysConfigContent content = Model.SysConfigContent.GetIntance(sysConfig);
                AppBasicCache.CurrentSysConfigContent = content;
                if (content != null && content.SysConfig_EnableImgPath == 1)
                {
                    if (!string.IsNullOrEmpty(content.SysConfig_ImagePath))
                    {
                        string imgPath = System.Web.HttpUtility.UrlDecode(content.SysConfig_ImagePath);
                        string requestPath = LocalFile.UseLocalStaticFiles.GetRequestPath(imgPath);
                        if (Directory.Exists(requestPath))
                        {
                            BLL.ImageTools.LocalFilePath = AppBasicCache.strImgpath = CameraGlobal.strImgpath = requestPath;
                            LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "本地路径：" + BLL.ImageTools.LocalFilePath, LogLevel.Info);
                            paths.Add(new LocalFile.UseLocalStaticFiles() { directory = imgPath, requestPath = "CameraCaptures" });
                        }
                        else
                        {
                            paths.Add(new LocalFile.UseLocalStaticFiles() { directory = BLL.ImageTools.LocalFilePath, requestPath = "CameraCaptures" });
                            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "图片路径配置错误：" + requestPath, LogLevel.Info);
                        }
                    }
                    else
                    {
                        paths.Add(new LocalFile.UseLocalStaticFiles() { directory = path1, requestPath = "CameraCaptures" });
                    }
                }
                else
                {
                    paths.Add(new LocalFile.UseLocalStaticFiles() { directory = path1, requestPath = "CameraCaptures" });
                }

                if (content != null && content.SysConfig_DBBack == 1 && !string.IsNullOrEmpty(content.SysConfig_DBBackPath))
                {
                    string dbback = System.Web.HttpUtility.UrlDecode(content.SysConfig_DBBackPath);
                    if (Directory.Exists(dbback))
                    {
                        string requestPath = LocalFile.UseLocalStaticFiles.GetRequestPath(dbback);
                        paths.Add(new LocalFile.UseLocalStaticFiles() { directory = dbback, requestPath = requestPath });
                    }
                    else
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "数据库备份路径配置错误：" + dbback, LogLevel.Info);
                    }
                }

                LogManagementMap.SetOverDays = content?.SysConfig_LogSaveDay ?? 20;
                LogManagementMap.SetLogOpen = content?.SysConfig_LogOpen ?? 20;
            }
            else
            {
                paths.Add(new LocalFile.UseLocalStaticFiles() { directory = path1, requestPath = "CameraCaptures" });
            }
            //}
            //else
            //{
            //    paths.Add(new LocalFile.UseLocalStaticFiles() { directory = path1, requestPath = "CameraCaptures" });
            //}

            foreach (var path in paths)
            {
                if (string.IsNullOrEmpty(path.directory)) continue;

                var ret = CreateDir(path, app);
                if (!ret && path.requestPath == "CameraCaptures")
                {
                    BLL.ImageTools.LocalFilePath = AppBasicCache.strImgpath = CameraGlobal.strImgpath = path1;
                    CreateDir(new LocalFile.UseLocalStaticFiles() { directory = path1, requestPath = "CameraCaptures" }, app);
                }
            }

            try
            {
                string s1 = AppDomain.CurrentDomain.BaseDirectory + "\\..\\Local\\ExtResources.plogo";
                if (File.Exists(s1))
                {
                    var fv = SlnUtil.LoadModel<Model.PlModelBase>(s1, true);
                    if (fv.Item2 != null)
                    {
                        GetPlModelBase = fv.Item2;
                    }
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex);
            }
        }

        private bool CreateDir(LocalFile.UseLocalStaticFiles path, IApplicationBuilder app)
        {
            try
            {
                LocalFile.CreateDirectory(path.directory);

                var staticFileOptions = new StaticFileOptions
                {
                    FileProvider = new PhysicalFileProvider(path.directory),
                    RequestPath = new PathString("/" + path.requestPath)
                };

                app.UseStaticFiles(staticFileOptions);
                return true;
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex);
                return false;
            }
        }

        /// <summary>
        /// 个性化资源
        /// </summary>
        public static Model.PlModelBase GetPlModelBase { set; get; }



        private static System.Timers.Timer sentryCacheTimer = null;
        /// <summary>
        /// 检查缓存任务
        /// </summary>
        private static void SentryCacheWorker()
        {

            sentryCacheTimer = new System.Timers.Timer()
            {
                Interval = 1000,
                AutoReset = true,
                Enabled = true
            };
            sentryCacheTimer.Elapsed += TimerCache;
            sentryCacheTimer.Start();
        }

        private static void TimerCache(object sender, ElapsedEventArgs e)
        {
            sentryCacheTimer.Enabled = false;
            CheckCache();
            sentryCacheTimer.Interval = 1000 * 60 * 15;//15分钟
            sentryCacheTimer.Enabled = true;
        }

        /// <summary>
        /// 岗亭缓存检查
        /// </summary>
        private static void CheckCache()
        {
            try
            {
                //启用了岗亭业务缓存
                //if (AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache == 1)
                //{

                bool checkCarStatus = true;//检查车辆信息缓存状态
                List<Model.Car> carList = null;
                try
                {
                    carList = BLL.Car.GetAllEntity("*", "");
                    var cacheList = AppBasicCache.GetCar.Values.ToList();

                    var newCars = TyziTools.Json.ToObject<List<CarCacheExt>>(TyziTools.Json.ToString(carList, true));
                    var oldCars = TyziTools.Json.ToObject<List<CarCacheExt>>(TyziTools.Json.ToString(cacheList, true));

                    var extCarList1 = newCars.Except(oldCars).ToList();
                    var extCarList2 = oldCars.Except(newCars).ToList();

                    if (extCarList1.Count > 0 || extCarList2.Count > 0)
                    {
                        checkCarStatus = false;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, "检查车辆信息缓存状态");
                }

                bool checkOwnerStatus = true;//检查车主信息缓存状态
                List<Model.Owner> ownerList = null;
                try
                {
                    ownerList = BLL.Owner.GetAllEntity("*", "");
                    var cacheList = AppBasicCache.GetOwner.Values.ToList();

                    var newOwners = TyziTools.Json.ToObject<List<OwnerCacheExt>>(TyziTools.Json.ToString(ownerList, true));
                    var oldOwners = TyziTools.Json.ToObject<List<OwnerCacheExt>>(TyziTools.Json.ToString(cacheList, true));

                    var extOwnerList1 = newOwners.Except(oldOwners).ToList();
                    var extOwnerList2 = oldOwners.Except(newOwners).ToList();

                    if (extOwnerList1.Count > 0 || extOwnerList2.Count > 0)
                    {
                        checkOwnerStatus = false;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, "检查车主信息缓存状态");
                }

                bool checkBusinessCarStatus = true;//检查商家车辆信息缓存状态
                List<Model.BusinessCar> businessCarList = null;
                try
                {
                    businessCarList = BLL.BusinessCar.GetAllEntity("*", "BusinessCar_Status=0");
                    var cacheList = AppBasicCache.GetBusinessCar.Values.ToList();

                    var newBusinessCars = TyziTools.Json.ToObject<List<BusinessCarCacheExt>>(TyziTools.Json.ToString(businessCarList, true));
                    var oldBusinessCars = TyziTools.Json.ToObject<List<BusinessCarCacheExt>>(TyziTools.Json.ToString(cacheList, true));

                    var extBusinessCarList1 = newBusinessCars.Except(oldBusinessCars).ToList();
                    var extBusinessCarList2 = oldBusinessCars.Except(newBusinessCars).ToList();

                    if (extBusinessCarList1.Count > 0 || extBusinessCarList2.Count > 0)
                    {
                        checkBusinessCarStatus = false;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, "检查商家车辆信息缓存状态");
                }

                bool checkReserveStatus = true;//检查预约信息缓存状态
                List<Model.Reserve> reserveList = null;
                try
                {
                    reserveList = BLL.Reserve.GetAllEntity("*", $"Reserve_StartTime>='{DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss")}' or Reserve_Status=1");
                    var cacheList = AppBasicCache.GetReserve.Values.ToList();

                    var newReserves = TyziTools.Json.ToObject<List<ReserveCacheExt>>(TyziTools.Json.ToString(reserveList, true));
                    var oldReserves = TyziTools.Json.ToObject<List<ReserveCacheExt>>(TyziTools.Json.ToString(cacheList, true));

                    var extReserveList1 = newReserves.Except(oldReserves).ToList();
                    var extReserveList2 = oldReserves.Except(newReserves).ToList();

                    if (extReserveList1.Count > 0 || extReserveList2.Count > 0)
                    {
                        checkReserveStatus = false;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, "检查预约信息缓存状态");
                }

                bool checkBlackListStatus = true;//检查黑名单信息缓存状态
                List<Model.BlackList> blackList = null;
                try
                {
                    blackList = BLL.BaseBLL._GetAllEntity(new Model.BlackList(), "*", "Blacklist_Status=1 OR Blacklist_Status IS NULL");
                    var cacheList = AppBasicCache.GetBlackList.Values.ToList();

                    var newBlackLists = TyziTools.Json.ToObject<List<BlackListCacheExt>>(TyziTools.Json.ToString(blackList, true));
                    var oldBlackLists = TyziTools.Json.ToObject<List<BlackListCacheExt>>(TyziTools.Json.ToString(cacheList, true));

                    var extBlackList1 = newBlackLists.Except(oldBlackLists).ToList();
                    var extBlackList2 = oldBlackLists.Except(newBlackLists).ToList();

                    if (extBlackList1.Count > 0 || extBlackList2.Count > 0)
                    {
                        checkBlackListStatus = false;
                    }
                }
                catch (Exception ex)
                {
                    LogManagementMap.WriteToFileException(ex, "检查黑名单信息缓存状态");
                }

                //如果有任何一个缓存状态不正常,关闭缓存,清空并且重新加载
                if (!checkCarStatus || !checkOwnerStatus || !checkReserveStatus || !checkBusinessCarStatus || !checkBlackListStatus)
                {
                    //AppBasicCache.GetPolicyPark.PolicyPark_BusinessCache = 1;//缓存状态不正常,关闭缓存,清空并且重新加载

                    if (!checkCarStatus)
                    {
                        try
                        {
                            AppBasicCache.ClearElement(AppBasicCache.GetCar);
                            carList.ForEach(x =>
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x, true);
                            });
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, $"清空并且重新加载车辆信息缓存异常");
                        }
                    }

                    if (!checkOwnerStatus)
                    {
                        try
                        {
                            AppBasicCache.ClearElement(AppBasicCache.GetOwner);
                            ownerList.ForEach(x =>
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x, true);
                            });
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, $"清空并且重新加载车主信息缓存异常");
                        }
                    }

                    if (!checkBusinessCarStatus)
                    {
                        try
                        {
                            AppBasicCache.ClearElement(AppBasicCache.GetBusinessCar);
                            businessCarList.ForEach(x =>
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBusinessCar, x.BusinessCar_No, x, true);
                            });
                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, $"清空并且重新加载商家车辆信息缓存异常");
                        }
                    }

                    if (!checkReserveStatus)
                    {
                        try
                        {
                            AppBasicCache.ClearElement(AppBasicCache.GetReserve);
                            reserveList.ForEach(x =>
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetReserve, x.Reserve_No, x, true);
                            });

                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, $"清空并且重新加载预约信息缓存异常");
                        }
                    }

                    if (!checkBlackListStatus)
                    {
                        try
                        {
                            AppBasicCache.ClearElement(AppBasicCache.GetBlackList);
                            blackList.ForEach(x =>
                            {
                                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetBlackList, x.BlackList_No, x, true);
                            });

                        }
                        catch (Exception ex)
                        {
                            LogManagementMap.WriteToFileException(ex, $"清空并且重新加载黑名单信息缓存异常");
                        }
                    }

                    AppBasicCache.GetPolicyPark.PolicyPark_BusinessCache = 1;//重新加载完成,开启缓存
                }
                //}


                TimedSemaphore.CleanUpExpiredLocks(AppBasicCache._keyLocks);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.WebTimeLog, $"检测岗亭缓存异常", LogLevel.Error, ex);
            }
        }

    }
}