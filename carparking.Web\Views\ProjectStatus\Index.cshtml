﻿@using carparking.BLL.Cache﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <link href="~/Static/css/plugins/iCheck/custom.css" rel="stylesheet">

    <style>
        .reSend { background-color: #f8ac59 !important; border-color: #f8ac59 !important; }
        .layui-fluid { padding-left: 10px !important; }
        .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group { margin-left: 30px !important; height: 47px; }

        input#carno { background-color: rgb(249 242 242 / 100%) !important; }
        input#carno:disabled { background-color: rgb(0 0 0) !important; color: #fff; }
        input#time:disabled { background-color: rgb(0 0 0) !important; color: #fff; }

        .layui-form-item .layui-form-checkbox { margin-top: -1px !important; }
        .layui-form-checkbox { height: 39px !important; line-height: 39px !important; }
        .layui-form-checkbox i { position: absolute; right: 0; top: 0; width: 30px; height: 36px; border: 1px solid #d7eaeb; border-left: none; border-radius: 0 2px 2px 0; font-size: 20px; text-align: center; }
        .layui-card-header .layui-icon { top: 30%; margin-top: -12px; height: 37px; right: 0px !important; }
        h1 { margin-left: 50px !important; }
        h2 { margin-left: 60px !important; }
        .desc { font-size: 11px; margin-left: 60px; color: #898484; width: 100%; }
    </style>
</head>
<body class="gray-bg">
    <div class="layui-fluid animated fadeInRight">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-content" id="searchForm">
                        <div class="form-horizontal" id="verifyCheck">
                            <div class="form-group">
                                <h2>接收数据处理：</h2>
                                <label class=" col-sm-4 control-label">最新执行时间：<t style="color:red;">@(carparking.SentryBox.SentryBoxHelper.RecordSyncConsumersTime.ToString("yyyy-MM-dd HH:mm:ss"))</t> </label>
                                <label class=" col-sm-4 control-label">当前轮询数据ID：<t style="color:red;">@(carparking.SentryBox.SentryBoxHelper.RecordSyncConsumersReceiveId)</t> </label>
                            </div>
                        </div>


                        <div class="form-horizontal" id="verifyCheck">
                            <div class="form-group">
                                <h2>分发数据处理：</h2>
                                <label class=" col-sm-4 control-label">最新执行时间：<t style="color:red;">@(carparking.Library.ParkTimer.RecordSyncSentryTime.ToString("yyyy-MM-dd HH:mm:ss"))</t> </label>
                                <label class=" col-sm-4 control-label">当前轮询数据ID：<t style="color:red;">@(carparking.Library.ParkTimer.RecordSyncSentrySendId)</t> </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v20230620" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>

        layui.use(['element', 'table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
      
        });

    </script>

</body>
</html>