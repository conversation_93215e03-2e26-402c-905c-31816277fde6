﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>功能策略设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <script src="~/Static/plugins/carnopicker/carnopicker.js" asp-append-version="true"></script>
    <style>
        html, body { }
        .layui-tab-title { padding-left: 2rem; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }
        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }
        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_park1.svg'); }

        .layui-tab-content { padding: 2rem; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }
        .layui-disabled {
            background-color: #eee;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="layui-tab">
        <ul class="layui-tab-title">
            <li class="type1 layui-this">放行策略</li>
            <li class="type2">通道策略</li>
            <li class="type3">卡牌类型策略</li>
            <li class="type4">停车场策略</li>
        </ul>
        <div class="layui-tab-content layui-form">
            <!--放行策略-->
            <div class="layui-tab-item layui-show">
                <div class="layui-row">
                    <div class="layui-inline">
                        <select class="layui-select" lay-search id="PolicyPass_PasswayNo" name="PolicyPass_PasswayNo">
                            <option value="">出入通道</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <select class="layui-select" lay-search id="PolicyPass_CarCardTypeNo" name="PolicyPass_CarCardTypeNo">
                            <option value="">车牌类型</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn pass"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                    </div>
                </div>
                <table class="layui-table" id="tablepass" lay-filter="tablepass">
                    <thead>
                        <tr>
                            <th width="120">车道名称</th>
                            <th width="120">车牌类型</th>
                            <th width="120">开闸方式</th>
                            <th width="120">无入场记录</th>
                            <th width="120">重复识别</th>
                            <th width="120">重复扫码</th>
                            <th width="120">车场满位</th>
                            <th width="120">登记车过期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>车道1</td>
                            <td>临时车A</td>
                            <td><select class="layui-input opengateOption" lay-search></select></td>
                            <td><select class="layui-input opengateOption" lay-search></select></td>
                            <td><select class="layui-input opengateOption" lay-search></select></td>
                            <td><select class="layui-input opengateOption" lay-search></select></td>
                            <td><select class="layui-input opengateOption" lay-search></select></td>
                            <td><select class="layui-input opengateOption" lay-search></select></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="layui-tab-item">
            
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="opengateOption">
        <option value="${value}">${name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script type="text/javascript">
        if ('@carparking.Config.PubVar.iParkingType' == "1") {
            $("#PolicyPark_MaxDiscount").attr("disabled", true);
        }
        myVerify.init();

        $(function () {
            var options = [
                { value: "0", name: "禁止通行" },
                { value: "1", name: "自动放行" },
                { value: "2", name: "弹框确认" },
                { value: "3", name: "排队等候" },
                { value: "4", name: "最低收费标准" }];
            $(".opengateOption").html($("#opengateOption").tmpl(options));
            layui.form.render("select");
        });
    </script>
</body>
</html>
