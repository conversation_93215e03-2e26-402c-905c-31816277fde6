﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>系统设置</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { }

        a.mark { background: rgba(0, 0, 0, .0); }

        .button-separator { position: absolute; top: 5px; bottom: 3px; left: 50%; width: 1px; background-color: rgba(0, 0, 0, 0.2); transform: translateX(-50%); }

        .layui-tab-title { padding-left: 2rem; }

        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }

        .layui-tab-title li { padding-left: 2rem; text-align: left; }

        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }

        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }

        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }

        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_payed.svg'); }

        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_nopwd.svg'); }

        .layui-tab-title li.type6::before { background-image: url('../../Static/img/icon/icon_p_fapiao.svg'); }

        .layui-tab-title li.type7::before { background-image: url('../../Static/img/icon/icon_p_apply.svg'); }

        .layui-tab-title li.type8::before { background-image: url('../../Static/img/icon/icon_p_aliyun.svg'); }

        .layui-tab-title li.type10::before { background-image: url('../../Static/img/icon/icon_p_apply.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }

        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }

        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_payed1.svg'); }

        .layui-tab-title li.layui-this.type5::before { background-image: url('../../Static/img/icon/icon_p_nopwd1.svg'); }

        .layui-tab-title li.layui-this.type6::before { background-image: url('../../Static/img/icon/icon_p_fapiao1.svg'); }

        .layui-tab-title li.layui-this.type7::before { background-image: url('../../Static/img/icon/icon_p_apply1.svg'); }

        .layui-tab-title li.layui-this.type8::before { background-image: url('../../Static/img/icon/icon_p_aliyun1.svg'); }

        .layui-tab-title li.layui-this.type10::before { background-image: url('../../Static/img/icon/icon_p_apply1.svg'); }

        .layui-tab-content { padding: .5rem 2rem; }

        .layui-inline .layui-form-select .layui-input { width: 182px; }

        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }

        .layui-disabled { color: #000 !important; }

        input[value='禁用'], input[value='停用'], input[value='停用收款'] { color: brown !important; }

        .heightfull { height: 38px; line-height: 38px; }

        .td-diymenu:hover { text-decoration: underline; color: #1E9FFF; cursor: pointer; user-select: none; }

        .td-diymenu:active { font-weight: bold; }

        .layui-btn { background-color: #1e9fff; }

        input.rmb { padding-right: 2.5rem; position: relative; }

        span.rmb { position: absolute; width: 2.5rem; right: 0; top: 0; height: 100%; line-height: 38px; text-align: center; background-color: #b09b9b; color: #fff; border-top-right-radius: 2px; border-bottom-right-radius: 2px; }

        td { position: relative; }

        .password { user-select: none; padding-right: 40px !important; }

        .password + .icon { user-select: none; position: absolute; right: 15px; top: 9px; width: 38px; height: 38px; line-height: 38px; text-align: center; background-color: #48d1cc; color: #fff; }

        .password + .icon:active { background-color: #0094ff; }
    </style>

    <style>
        html, body { height: 100%; overflow: hidden; }

        .layui-tab.fs { height: 100%; padding: 0; }

        .layui-tab-content.fs { height: calc(100% - 44px); padding: 0; }

        .layui-tab-item.fs { height: 100%; overflow: hidden; }

        .leftmenu { width: 220px; position: fixed; background-color: #fff; top: 41px; bottom: 50px; z-index: 9; right: 0; padding: 10px; overflow: auto; }

        .leftmenu ul li { list-style: decimal inside; }

        .rightbody { position: fixed; top: 41px; bottom: 50px; right: 240px; left: 0; overflow: auto; padding: 0 10px; }

        a.mark { position: absolute; top: 0; }

        td.active { color: red; font-weight: bold; }

        .moresetting { display: none; }

        .headmoresetting { cursor: pointer !important; color: #1e9fff !important; }

        .headmoresetting:hover { font-weight: 600; }
        .divInLine { display: inline-block; margin-top: 5px; }
        .divInLine input { max-height: 30px; }
        .divInLine input.normal { max-height: 30px !important; text-align: center; padding-left: 3px; }
        .divInLine button { max-height: 32px !important; line-height: 30px; padding: 0px 10px; margin-bottom: 5px; }
        .divInLine label { height: 15px; padding: 0; }
        .headmoresetting:hover { font-weight: 600; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-tab fs">
        <ul class="layui-tab-title">
            <li class="type1 layui-this">车场配置</li>
            <li class="type3 layui-hide">个性化设置</li>
            <li class="type4 layui-hide">收款参数</li>
            <li class="type6 layui-hide">电子发票</li>
            <li class="type7 layui-hide">云平台服务</li>
            <li class="type10 layui-hide">第三方服务</li>
            <li class="type11 layui-hide">数据清理</li>
        </ul>
        <div class="layui-tab-content layui-form fs" id="Setting">
            <!--基础配置-->
            <div class="layui-tab-item layui-show fs" id="baseDiv">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="180">配置项名称</th>
                                <th width="380">配置项值</th>
                                <th width="280">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="">
                                <td>优惠券使用顺序</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_CouponSort" name="SysConfig_CouponSort">
                                        <option value="1">优先使用减免金额优惠</option>
                                        <option value="2">优先使用折扣比例优惠</option>
                                    </select>
                                </td>
                                <td>用于设置存在多种优惠方式的优惠券时，使用的先后顺序。</td>
                            </tr>
                            <tr class="">
                                <td>图片抓拍分辨率</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_CameraImageQuality" name="SysConfig_CameraImageQuality">
                                        <option value="1">流畅(704*576)</option>
                                        <option value="2">标清(1920*1080)</option>
                                        <option value="3">高清(2304*1296)</option>
                                    </select>
                                </td>
                                <td>
                                    用于设置软件连接相机设备后默认设置抓拍图片的分辨率。<br>
                                    <t style="color:red;">温馨提示：为了保障系统流畅运行，默认设置流畅（704*576），若需查看高清图片可将图片设置为其它选项（建议根据实际硬盘大小定义）。</t>
                                </td>
                            </tr>
                            <tr class="preferential">
                                <td>免费原因</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_FreeReasons" name="SysConfig_FreeReasons" />
                                </td>
                                <td>
                                    用于设置出口弹出确认框，值班人员操作免费放行时，可快捷选择预设的免费原因。<br>
                                    <t style="color:red;">注意：多个原因请用英文格式的分号“;”分隔开，示例：特殊车辆执勤;保洁车辆;三轮车;其他</t>
                                </td>
                            </tr>
                            <tr class="">
                                <td>入场原因</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_EnterRemarks" name="SysConfig_EnterRemarks" />
                                </td>
                                <td>
                                    用于设置入口弹出确认框，值班人员操作入场放行时，可快捷选择预设的放行原因。<br>
                                    <t style="color:red;">注意：多个原因请用英文格式的分号“;”分隔开，示例：特殊车辆执勤;保洁车辆;三轮车;其他</t>
                                </td>
                            </tr>
                            <tr>
                                <td>自定义图片存储</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EnableImgPath" name="SysConfig_EnableImgPath" lay-search>
                                        <option value="1">启用</option>
                                        <option value="0" selected>禁用</option>
                                    </select>
                                </td>
                                <td>用于设置是否启用图片的自定义存储路径。</td>
                            </tr>
                            <tr>
                                <td>自定义停车场图片存储地址</td>
                                <td>
                                    @{
                                        if (carparking.Config.AppSettingConfig.SentryMode == "0")
                                        {
                                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ImagePath" name="SysConfig_ImagePath" maxlength="255" />
                                        }
                                        else
                                        {
                                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ImagePath" name="SysConfig_ImagePath" maxlength="255" disabled />
                                        }
                                    }
                                </td>
                                <td>
                                    用于设置图片的自定义存储路径，需启用【自定义图片存储】设置项才生效。软件默认路径：@ViewBag.Path <br>
                                    <t style="color:red;">注意：暂不支持设置共享网络路径。</t>
                                </td>
                            </tr>

                            @{
                                if (carparking.Config.DogCommon.bAuthorization)
                                {
                                    <tr>
                                        <td>软件识别码</td>
                                        <td>
                                            <input type="text" placeholder="" id="SysConfig_SoftAuthCode" name="SysConfig_SoftAuthCode" class="layui-input" value="@carparking.PassTool.GoHelper.GetSoftAuthCode()" readonly>
                                        </td>
                                        <td>软件的唯一标识，作为软授权密钥生成的依据</td>
                                    </tr>
                                    <tr>
                                        <td>软件授权密钥</td>
                                        <td>
                                            <div class="layui-col-xs9">
                                                <input type="text" placeholder="" id="SysConfig_SoftAuth" name="SysConfig_SoftAuth" class="layui-input">
                                            </div>
                                            <div class="layui-col-xs3">
                                                <span class="input-group-btn">
                                                    <label title="选择" for="inputFile" class="layui-btn softauthfile">
                                                        <input type="file" accept=".txt" name="file" id="inputFile" class="hide" onchange="chooseFile(this);"><i class="fa fa-folder-open-o"></i> <t class="lan-label" data-lan="Select">导入密钥</t>
                                                    </label>
                                                </span>
                                            </div>
                                        </td>
                                        <td>根据软件识别码生成，用于验证软件的合法使用权限。在试用期结束后，若未插加密狗或进行软授权，软件的部分功能或全部功能会受到限制，影响正常使用</td>
                                    </tr>
                                }
                            }

                            <tr class="gdsz">
                                <td colspan="3">
                                    <div class="layui-row headmoresetting"><t class="content">更多设置</t>&nbsp;<i class="layui-icon layui-icon-down"></i></div>
                                </td>
                            </tr>
                            @* <tr>
                            <td>自定义停车场岗亭图片存储地址</td>
                            <td>
                            @{
                            if (carparking.Config.AppSettingConfig.SentryMode == "0")
                            {
                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_SentryImagePath" name="SysConfig_SentryImagePath" maxlength="255"/>
                            }
                            else
                            {
                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_SentryImagePath" name="SysConfig_SentryImagePath" maxlength="255" disabled/>
                            }
                            }
                            </td>
                            <td>实例：D:\cameraImages。自定义文件存储路径，需启用自定义图片存储才生效。当前存储图片路径：@ViewBag.SentryPath</td>
                            </tr> *@

                            <tr class="moresetting">
                                <td>第三方相机授权密钥</td>
                                <td>
                                    <div class="layui-col-xs3">
                                        <span class="input-group-btn">
                                            <button class="layui-btn" id="Btn_SysConfig_ThirdCameraAuth"><i class="layui-icon layui-icon-edit"></i> 授权密钥</button>
                                        </span>
                                    </div>
                                </td>
                                <td>用于根据第三方相机的序列号进行授权后，软件判断【设备管理】添加的第三方相机是否已授权，若已授权则可正常连接，若未授权则无法使用</td>
                            </tr>
                            @{
                                if (carparking.Config.AppSettingConfig.SentryMode != "1")
                                {
                                    <tr class="moresetting">
                                        <td>自动备份数据库</td>
                                        <td>
                                            <select class="layui-select" id="SysConfig_DBBack" name="SysConfig_DBBack" lay-search>
                                                <option value="1" selected>定时备份</option>
                                                <option value="2" selected>定时备份+车流量判断</option>
                                                <option value="0">禁用</option>
                                            </select>
                                        </td>
                                        <td>
                                            用于设置是否启用数据库的自动备份。<br>
                                            <t style="color:red;">注意：备份数据将会占用系统资源，在备份过程中可能会导致系统卡顿，请谨慎开启。</t>
                                        </td>
                                    </tr>
                                    <tr class="moresetting carflow layui-hide ">
                                        <td>车流量判断</td>
                                        <td style="padding:9px 1px;">
                                            <div class=" layui-col-xs12">
                                                <div class="divInLine"><input type="text" class="layui-input normal  v-number" style="width:5vw;" min="1" maxlength="3" id="SysConfig_DBBackFlowMin" name="SysConfig_DBBackFlowMin" /></div>
                                                <div class="divInLine"><label>分钟内超过</label></div>
                                                <div class="divInLine"><input type="text" class="layui-input normal  v-number" style="width:5vw;" min="1" maxlength="3" id="SysConfig_DBBackFlowCar" name="SysConfig_DBBackFlowCar" /></div>
                                                <div class="divInLine"><label>辆车识别出入场，延迟备份数据</label></div>
                                            </div>
                                        </td>
                                        <td><t style="color:red;">注意：在备份数据库的时间节点，如果触发设置的车流量判断条件则会延迟数据库备份。</t></td>
                                    </tr>
                                    <tr class="moresetting">
                                        <td>备份数据库文件存储路径</td>
                                        <td>
                                            @{
                                                if (carparking.Config.AppSettingConfig.SentryMode == "0")
                                                {
                                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DBBackPath" name="SysConfig_DBBackPath" maxlength="255" value="@ViewBag.InitPath" />
                                                }
                                                else
                                                {
                                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DBBackPath" name="SysConfig_DBBackPath" maxlength="255" value="@ViewBag.InitPath" disabled />
                                                }
                                            }

                                        </td>
                                        <td>
                                            用于设置数据库备份文件的存储路径，需启用【自动备份数据库】设置项才生效。软件默认路径：@ViewBag.InitPath <br>
                                            <t style="color:red;">注意：暂不支持设置共享网络路径。</t>
                                        </td>
                                    </tr>
                                    <tr class="moresetting">
                                        <td>备份数据库的周期</td>
                                        <td>
                                            <select class="layui-select" id="SysConfig_DBBackPeriod" name="SysConfig_DBBackPeriod" lay-search>
                                                <option value="1">每天</option>
                                                <option value="2">每周</option>
                                                <option value="3">间隔</option>
                                            </select>
                                        </td>
                                        <td>用于设置数据库备份的周期按每天/每周/间隔天数执行。</td>
                                    </tr>
                                    <tr class="period weekday layui-hide moresetting">
                                        <td>每周备份时间</td>
                                        <td>
                                            <select class="layui-select" id="SysConfig_DBBackWeekDay" name="SysConfig_DBBackWeekDay" lay-search>
                                                <option value="1">周一</option>
                                                <option value="2">周二</option>
                                                <option value="3">周三</option>
                                                <option value="4">周四</option>
                                                <option value="5">周五</option>
                                                <option value="6">周六</option>
                                                <option value="0">周日</option>
                                            </select>
                                        </td>
                                        <td>设置周期为每周时，指定每周几进行备份。</td>
                                    </tr>
                                    <tr class="period interval layui-hide moresetting">
                                        <td>备份数据库的间隔天数</td>
                                        <td>
                                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DBBackInterval" name="SysConfig_DBBackInterval" maxlength="3" />
                                        </td>
                                        <td>设置周期为间隔时，备份数据库的间隔天数。</td>
                                    </tr>
                                    <tr class="moresetting">
                                        <td>备份数据库的时间节点</td>
                                        <td>
                                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DBBackTime" name="SysConfig_DBBackTime" value="00:00:00" />
                                        </td>
                                        <td>设置备份日的时间点。备份将在此时间点到之后一小时内执行，其余时间不触发自动备份。</td>
                                    </tr>
                                    <tr class="moresetting">
                                        <td>备份数据库的文件保留天数</td>
                                        <td>
                                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DBBackRetent" name="SysConfig_DBBackRetent" value="7" />
                                        </td>
                                        <td>设置数据库备份文件保留天数。设置为0则表示不删除。超过设定天数后，系统将自动删除备份的文件，请谨慎操作。</td>
                                    </tr>
                                }
                            }
                            <tr class="moresetting">
                                <td>是否启用相机视频</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_OpenCameraVideo" name="SysConfig_OpenCameraVideo">
                                        <option value="1">启用</option>
                                        <option value="2">禁用</option>
                                    </select>
                                </td>
                                <td>用于设置值班中心是否显示相机视频</td>
                            </tr>
                            <tr class="moresetting">
                                <td>显示屏卡类型</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ScreenDrive" name="SysConfig_ScreenDrive">
                                        <option value="1">系统自带屏卡</option>
                                        <option value="2">宇视显示屏卡</option>
                                    </select>
                                </td>
                                <td>用于设置系统默认的显示屏卡类型。</td>
                            </tr>
                            @*   <tr class="">
                            <td>视频播放分辨率使能</td>
                            <td>
                            <select class="layui-select" id="SysConfig_VideoResolutionEnable" name="SysConfig_VideoResolutionEnable">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                            </select>
                            </td>
                            <td>启用该配置时，软件连接设备会设置视频播放分辨率，只支持T30拉取主码流视频，B30暂不支持，且只支持06，15系列相机，其它相机暂不支持。</td>
                            </tr>
                            <tr class="">
                            <td>视频播放分辨率</td>
                            <td>
                            <select class="layui-select" id="SysConfig_VideoResolution" name="SysConfig_VideoResolution">
                            <option value="1">流畅(704*576)</option>
                            <option value="2">标清(1920*1080)</option>
                            <option value="3">高清(2304*1296)</option>
                            </select>
                            </td>
                            <td>需要根据电脑的配置选择适应的视频分辨率</td>
                            </tr> *@

                            <tr class="moresetting">
                                <td>默认自助停车设备系列</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_SelfDrive" name="SysConfig_SelfDrive">
                                        <option value="">不设置默认</option>
                                    </select>
                                </td>
                                <td>新增自助停车设备时，默认选择</td>
                            </tr>
                            @*    <tr class="moresetting">
                            <td>版权信息</td>
                            <td>
                            <input type="text" class="layui-input" autocomplete="off" id="SysConfig_CopyRight" name="SysConfig_CopyRight" />
                            </td>
                            <td>在T30软件显示自定义版权信息</td>
                            </tr> *@



                            @* <tr>
                            <td>岗亭电脑历史记录保存天数</td>
                            <td>
                            <input type="text" class="layui-input v-number" autocomplete="off" id="SysConfig_ClientRecordDay" name="SysConfig_ClientRecordDay" value="0" maxlength="5"/>
                            </td>
                            <td>天, 岗亭客户端历史出入、收费等记录最大保存天数。</td>
                            </tr> *@

                            <tr class="moresetting">
                                <td>岗亭电脑历史图片保存天数</td>
                                <td>
                                    <input type="text" class="layui-input v-number" autocomplete="off" id="SysConfig_ClientImgDay" name="SysConfig_ClientImgDay" value="30" maxlength="5" />
                                </td>
                                <td>设置图片保留天数。设置为0则表示不删除。超过设定天数的图片，系统将自动删除，请谨慎操作。<t style="color:red;">（建议根据实际硬盘大小定义）</t></td>
                            </tr>
                            <tr class="moresetting">
                                <td>岗亭/服务器日志文件保留天数</td>
                                <td>
                                    <input type="text" class="layui-input v-number" autocomplete="off" id="SysConfig_LogSaveDay" name="SysConfig_LogSaveDay" value="30" maxlength="5" />
                                </td>
                                <td>设置日志保留天数。设置为0则表示不删除。超过设定天数的日志，系统将自动删除，请谨慎操作。<t style="color:red;">（建议根据实际硬盘大小定义）</t></td>
                            </tr>
                            <tr class="moresetting">
                                <td>保存本地全量日志</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_LogOpen" name="SysConfig_LogOpen" lay-search>
                                        <option value="1" selected>是</option>
                                        <option value="0">否</option>
                                    </select>
                                </td>
                                <td>设置是否保存系统所有日志，选“否”时只保存部分重要业务日志。</td>
                            </tr>
                            @if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.WindowsStandard)
                            {
                                <tr class="moresetting">
                                    <td>岗亭同步数据记录保存月数</td>
                                    <td>
                                        <input type="text" class="layui-input v-number" autocomplete="off" id="SysConfig_SerSyncData" name="SysConfig_SerSyncData" value="0" maxlength="3" />
                                    </td>
                                    <td>设置服务器同步数据到云平台的历史记录保存时长。设置为0则表示不删除。超过设定月数的记录，系统将自动删除，请谨慎操作。<t style="color:red;">（建议根据实际硬盘大小定义）</t></td>
                                </tr>
                            }

                            <tr class="moresetting">
                                <td>磁盘空间剩余百分比</td>
                                <td>
                                    <input type="text" class="layui-input v-null v-number v-min v-max" min="0" max="100" autocomplete="off" id="SysConfig_WarmDisk" name="SysConfig_WarmDisk" value="5" maxlength="5" />
                                </td>
                                <td>设置磁盘剩余空间低于设定值，将发送报警信息到邮箱。设置0表示不发送警报，例如：0-100。</td>
                            </tr>
                            <tr class="moresetting">
                                <td>电脑内存剩余百分比</td>
                                <td>
                                    <input type="text" class="layui-input v-null v-number v-min v-max" min="0" max="100" autocomplete="off" id="SysConfig_WarmMemory" name="SysConfig_WarmMemory" value="10" maxlength="5" />
                                </td>
                                <td>设置电脑内存剩余低于设定值，将发送报警信息到邮箱。设置0表示不发送警报，例如：0-100。</td>
                            </tr>
                            <tr class="moresetting">
                                <td>CPU使用率超出百分比</td>
                                <td>
                                    <input type="text" class="layui-input v-null v-number v-min v-max" min="0" max="100" autocomplete="off" id="SysConfig_WarmCPUUse" name="SysConfig_WarmCPUUse" value="90" maxlength="5" />
                                </td>
                                <td>CPU使用率超出设定值，将发送报警信息到邮箱。设置0表示不发送警报，例如：0-100。</td>
                            </tr>
                            <tr class="moresetting">
                                <td>网络状态</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_WarmNet" name="SysConfig_WarmNet">
                                        <option value="1">启用</option>
                                        <option value="0" selected>禁用</option>
                                    </select>
                                </td>
                                <td>启用后，网络状态断开连接时，将发送报警信息到邮箱。</td>
                            </tr>
                            <tr class="moresetting">
                                <td>CPU温度</td>
                                <td>
                                    <input type="text" class="layui-input v-null v-number  v-min v-max" min="0" max="200" autocomplete="off" id="SysConfig_WarmCPUTemp" name="SysConfig_WarmCPUTemp" value="85" maxlength="5" />
                                </td>
                                <td>℃(0-200)。CPU温度超出设定值，将发送报警信息到邮箱。设置0表示不发送警报</td>
                            </tr>

                            <tr class="moresetting">
                                <td>邮箱地址</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_SenderServerHost" name="SysConfig_SenderServerHost" maxlength="255" />
                                </td>
                                <td>使用邮箱SMTP地址(QQ邮箱：smtp.qq.com)</td>
                            </tr>
                            <tr class="moresetting">
                                <td>邮箱端口号</td>
                                <td>
                                    <input type="text" class="layui-input v-number v-min v-max" min="1" max="65535" autocomplete="off" id="SysConfig_Senderport" name="SysConfig_Senderport" maxlength="5" value="25" />
                                </td>
                                <td>使用邮箱SMTP地址对应的端口，请使用非SSL协议端口号.默认使用25端口</td>
                            </tr>
                            <tr class="moresetting">
                                <td>邮箱账号</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_FromMail" name="SysConfig_FromMail" maxlength="255" />
                                </td>
                                <td>发送邮件时使用的邮箱账号</td>
                            </tr>
                            <tr class="moresetting">
                                <td>邮箱授权码</td>
                                <td>
                                    <input type="password" class="layui-input" autocomplete="off" id="SysConfig_SenderPassword" name="SysConfig_SenderPassword" maxlength="50" />
                                </td>
                                <td>
                                    发送邮件时使用的邮箱密码(授权码) QQ邮箱授权码</a>
                                </td>
                            </tr>
                            <tr class="moresetting">
                                <td>邮箱用户名</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_SenderUsername" name="SysConfig_SenderUsername" maxlength="50" />
                                </td>
                                <td>（即@符号前面的字符串，例如：<EMAIL>，用户名为：hello）</td>
                            </tr>

                            <tr class="moresetting">
                                <td>增强型视频播放</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PlayerType" name="SysConfig_PlayerType" lay-search>
                                        <option value="0">不设置</option>
                                        <option value="2" selected>禁用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td>岗亭使用增强型视频播放</td>
                            </tr>
                            <tr class="moresetting">
                                <td>锁单分钟数(分钟)</td>
                                <td>
                                    <input type="text" class="layui-input v-number v-min v-max" min="0" max="360" autocomplete="off" id="SysConfig_LockOrderMin" name="SysConfig_LockOrderMin" maxlength="5" value="0" />
                                </td>
                                <td>发起无感和扫码查费操作可进行锁单，0为不锁单</td>
                            </tr>
                            <tr class="moresetting">
                                <td>强密码</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PwdType" name="SysConfig_PwdType" lay-search>
                                        <option value="0" selected>禁用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td>账号登录密码强制使用强密码</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--个性化设置-->
            <div class="layui-tab-item fs">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="180">配置项名称</th>
                                <th width="280">配置项值</th>
                                <th width="380">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>启用自定义设置</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_DIYEnable" name="SysConfig_DIYEnable">
                                        <option value="0">禁用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td>用于设置是否启用自定义软件相关界面。</td>
                            </tr>
                            <tr>
                                <td>自定义管理后台名称</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DIYName" name="SysConfig_DIYName" maxlength="20" />
                                </td>
                                <td>自定义当前网站标题名称。</td>
                            </tr>

                            @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.EPSServer)
                            {
                                <tr>
                                    <td>自定义岗亭端名称</td>
                                    <td>
                                        <input type="text" class="layui-input" autocomplete="off" id="SysConfig_DIYSoftName" name="SysConfig_DIYSoftName" maxlength="20" />
                                    </td>
                                    <td>自定义岗亭端名称。</td>
                                </tr>
                            }
                            <tr>
                                <td>自定义LOGO</td>
                                <td>
                                    <div class="layui-com-upload" com-input="SysConfig_DIYLogo" url="/SystemSetting/UploadImage" accept="images" data="SysConfig_DIYLogo" auto="true"></div>
                                </td>
                                <td>自定义当前网站LOGO图片。最大建议尺寸200*200</td>
                            </tr>
                            <tr>
                                <td>自定义登录页背景图</td>
                                <td>
                                    <div class="layui-com-upload" com-input="SysConfig_DIYBackImage" url="/SystemSetting/UploadImage" accept="images" data="SysConfig_DIYBackImage" auto="true"></div>
                                </td>
                                <td>自定义当前网站登录页的背景图片。最大建议尺寸1920*1080</td>
                            </tr>
                            <tr>
                                <td class="td-diymenu">自定义菜单名称 <br /><span style="color:#999;">(点击收起/展开右侧列表)</span></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs10" id="SysConfig_DIYMenu"></div>
                                        <div class="layui-col-xs2">
                                            <a class="layui-btn layui-btn-xs" style="margin-top:10px;" onclick="diymenu.add()">添加</a>
                                        </div>
                                    </div>
                                </td>
                                <td>自定义左侧菜单名称。如添加(车场配置:停车场资料),表示将车场配置菜单名称自定义为停车场资料。</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--收款设置-->
            <div class="layui-tab-item layui-form fs" lay-filter="payedform">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="100">配置项名称</th>
                                <th width="400">配置项值</th>
                                <th width="100">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>微信收款方式</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PayWeiXin" name="SysConfig_PayWeiXin">
                                        <option value="0">停用</option>
                                        <option value="2">子商户收款</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="payweixin">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">子商户帐户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input v-null" id="SysConfig_WXSubMchId" name="SysConfig_WXSubMchId" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>支付宝收款方式</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PayAlipay" name="SysConfig_PayAlipay">
                                        <option value="0">停用</option>
                                        <option value="1">服务商待签约收款</option>
                                        <option value="2">签约商户收款</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签约商户账号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_AliPayAccountNo" name="SysConfig_AliPayAccountNo" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v1 v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签约商户PID：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_AliPid" name="SysConfig_AliPid" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签约应用AppId：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_AliAppid" name="SysConfig_AliAppid" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">支付宝公钥(服务窗)：</div>
                                        <div class="layui-col-xs9">
                                            <textarea class="layui-textarea" id="SysConfig_AliPublicKey" name="SysConfig_AliPublicKey"></textarea>
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v1 v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">应用授权Token：</div>
                                        <div class="layui-col-xs7">
                                            <input type="text" class="layui-input" id="SysConfig_AliAppAuthToken" name="SysConfig_AliAppAuthToken" />
                                        </div>
                                        <div class="layui-col-xs2">
                                            <button class="layui-btn" id="OnScanAliAppAuthToken"><i class="layui-icon layui-icon-code-circle"></i> 扫码获取</button>
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v1 v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">
                                            <input type="checkbox" id="SysConfig_AliPayEcoStatus" name="SysConfig_AliPayEcoStatus" title="启用城市服务" lay-skin="primary">
                                        </div>
                                        <div class="layui-col-xs3" style="padding:0 5px;">
                                            高德POIID<a href="https://www.amap.com/" target="_blank" style="color: #48d1cc">获取</a><br />
                                            <input type="text" class="layui-input" id="SysConfig_CityCode" name="SysConfig_CityCode" />
                                        </div>
                                        <div class="layui-col-xs3" style="padding:0 5px;">
                                            停车场性质<br />
                                            <select class="layui-select" lay-search id="SysConfig_AliPayLotType" name="SysConfig_AliPayLotType">
                                                <option value="">请选择..</option>
                                                <option value="1">小区停车场</option>
                                                <option value="2">商圈停车场</option>
                                                <option value="3">路面停车场</option>
                                                <option value="4">园区停车场</option>
                                                <option value="5">写字楼停车场</option>
                                                <option value="6">私人停车场</option>
                                            </select>
                                        </div>
                                        <div class="layui-col-xs3" style="padding:0 5px;">
                                            支付方式<br />
                                            <input type="checkbox" name="" title="支付宝在线缴费" lay-skin="primary" disabled checked>
                                            <input type="checkbox" name="" title="支付宝代扣缴费" lay-skin="primary" disabled checked>
                                            <input type="checkbox" name="" title="当面付" lay-skin="primary" disabled checked>
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipay v1 v2">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">
                                            <input type="checkbox" id="SysConfig_ChannelServices" name="SysConfig_ChannelServices" title="启用渠道服务" lay-skin="primary">
                                        </div>
                                        <div class="layui-col-xs3" style="padding:0 5px;">
                                            渠道POIID<a href="https://www.amap.com/" target="_blank" style="color: #48d1cc">获取</a><br />
                                            <input type="text" class="layui-input" id="SysConfig_AliChannelPOIID" name="SysConfig_AliChannelPOIID" />
                                        </div>
                                        <div class="layui-col-xs3" style="padding:0 5px;">
                                            渠道PID<br />
                                            <input type="text" class="layui-input" id="SysConfig_AliChannelPID" name="SysConfig_AliChannelPID" />
                                        </div>
                                        <div class="layui-col-xs3" style="padding:0 5px;">
                                            渠道车场ID<br />
                                            <input type="text" class="layui-input" id="SysConfig_AliChannelParkingID" name="SysConfig_AliChannelParkingID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>建行聚合支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PayCCB" name="SysConfig_PayCCB">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccb">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">分行代码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBMashupBranchID" name="SysConfig_CCBMashupBranchID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccb">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">柜台代码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBMashupPosID" name="SysConfig_CCBMashupPosID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccb">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBMashupMerchantID" name="SysConfig_CCBMashupMerchantID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccb">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户密码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBMerchantPwd" name="SysConfig_CCBMerchantPwd" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccb">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">建行公钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBMashupSecret" name="SysConfig_CCBMashupSecret" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>乐聚合支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PayLepos" name="SysConfig_PayLepos">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="lepos">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户编号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_LeposMerchantId" name="SysConfig_LeposMerchantId" />
                                        </div>
                                        @*<div class="layui-col-xs3" style="padding:10px 0 0 5px ;"><input type="checkbox" id="SysConfig_LeposMerType" name="SysConfig_LeposMerType" title="渠道商户 (简易支付)" lay-skin="primary"></div>*@
                                    </div>
                                </td>
                                <td></td>
                            </tr>

                            <tr>
                                <td>随行付支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_SxPayMode" name="SysConfig_SxPayMode">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>

                            <tr class="sxpay">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户编号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_SxPayMerId" name="SysConfig_SxPayMerId" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>

                        <tbody>
                            <tr>
                                <td>微信无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_WXPayNoPwd" name="SysConfig_WXPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="wxpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">子商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input v-null" id="SysConfig_WXPayNoPwdNo" name="SysConfig_WXPayNoPwdNo" />
                                        </div>
                                        <!--<div class="layui-col-xs3" style="padding:10px 0 0 5px ;"><input type="checkbox" id="SysConfig_WXPayNoPwdInPaying" name="SysConfig_WXPayNoPwdInPaying" title="启用支付中可开通无感支付" lay-skin="primary"></div>-->
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>支付宝无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_AliPayNoPwd" name="SysConfig_AliPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签约商户账号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_AliPayNoPwdNo" name="SysConfig_AliPayNoPwdNo" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="alipaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签约商户PID：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_AliPayNoPwdAppID" name="SysConfig_AliPayNoPwdAppID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>建行无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_PayNoCCB" name="SysConfig_PayNoCCB">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccbnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">分行代码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBNoMashupBranchID" name="SysConfig_CCBNoMashupBranchID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccbnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">柜台代码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBNoMashupPosID" name="SysConfig_CCBNoMashupPosID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccbnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBNoMashupMerchantID" name="SysConfig_CCBNoMashupMerchantID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="ccbnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">建行公钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CCBNoMashupSecret" name="SysConfig_CCBNoMashupSecret" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>银联无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_UnionPayNoPwd" name="SysConfig_UnionPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="unionnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">业务代码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_UnionBusinessCode" name="SysConfig_UnionBusinessCode" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="unionnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">密钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_UnionSecret" name="SysConfig_UnionSecret" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>招行无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_CMBPayNoPwd" name="SysConfig_CMBPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="cmbpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">车场编码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CMBParkCode" name="SysConfig_CMBParkCode" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="cmbpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">应用APPID：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CMBAppId" name="SysConfig_CMBAppId" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="cmbpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">应用AppSecret：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_CMBAppSecret" name="SysConfig_CMBAppSecret" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>

                            <tr>
                                <td>工行无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ICBCPayNoPwd" name="SysConfig_ICBCPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="icbcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">应用APPID：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ICBCAppID" name="SysConfig_ICBCAppID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="icbcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户ID：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ICBCMerchatID" name="SysConfig_ICBCMerchatID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="icbcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户入账账号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ICBCMerchantAcct" name="SysConfig_ICBCMerchantAcct" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="icbcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2"> 网关公钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ICBCApigwPublicKey" name="SysConfig_ICBCApigwPublicKey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="icbcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2"> 私钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ICBCMyPrivateKey" name="SysConfig_ICBCMyPrivateKey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>农行无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ABCPayNoPwd" name="SysConfig_ABCPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="abcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCMerchantID" name="SysConfig_ABCMerchantID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="abcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">入账商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCMerchantAcct" name="SysConfig_ABCMerchantAcct" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="abcpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">证书密码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCCertificatePwd" name="SysConfig_ABCCertificatePwd" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>中行无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_BOCPayNoPwd" name="SysConfig_BOCPayNoPwd">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="bocpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户代码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_BOCPayNoMerchantID" name="SysConfig_BOCPayNoMerchantID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="bocpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">经营类别：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_BOCPayNoCategory" name="SysConfig_BOCPayNoCategory" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="bocpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">中行签名密钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_BOCPayNoZkey" name="SysConfig_BOCPayNoZkey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="bocpaynopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">中行加密密钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_BOCPayNoDkey" name="SysConfig_BOCPayNoDkey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>

                        <tbody class="editEnable">
                            <tr>
                                <td>黔通ETC</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_QianTong_Enable" name="SysConfig_QianTong_Enable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">中间件服务地址：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QianTong_Address" name="SysConfig_QianTong_Address" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC中间件服务地址</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">中间件服务端口：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QianTong_Port" name="SysConfig_QianTong_Port" value="0" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC中间件服务端口</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">交易流水上传地址：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QianTongTransactionFlowAddress" name="SysConfig_QianTongTransactionFlowAddress" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC交易流水上传地址</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户编号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QingTongMerchantNo" name="SysConfig_QingTongMerchantNo" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC商户信息</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">停车场编号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QingTongParkNo" name="SysConfig_QingTongParkNo" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC对接停车场编号</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">最大扣费金额：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QingTongMaxMoney" name="SysConfig_QingTongMaxMoney" value="0" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC最大扣费金额</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">接入码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QingTongAccessCode" name="SysConfig_QingTongAccessCode" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC接入码</td>
                            </tr>
                            <tr class="qtetcnopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签名码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_QingTongSignCode" name="SysConfig_QingTongSignCode" />
                                        </div>
                                    </div>
                                </td>
                                <td>黔通ETC签名码</td>
                            </tr>
                        </tbody>

                        <tbody class="editEnable">
                            <tr>
                                <td>启用第三方无感支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ThirdNoPwdEnable" name="SysConfig_ThirdNoPwdEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="othernopwd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <input type="text" class="layui-input" placeholder="请填写支付地址" id="SysConfig_ThirdNoPwdAddress" name="SysConfig_ThirdNoPwdAddress" />
                                    </div>
                                </td>
                                <td>第三方无感支付地址</td>
                            </tr>
                        </tbody>

                        <tbody class="editEnable">
                            <tr>
                                <td>山东信联ETC</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_XinLianEnable" name="SysConfig_XinLianEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinlianenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联上传地址：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianUrl" name="SysConfig_XinLianUrl" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinlianenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联appid：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianAppid" name="SysConfig_XinLianAppid" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinlianenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联商户编号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianMerChantNo" name="SysConfig_XinLianMerChantNo" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinlianenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联秘钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianPriKey" name="SysConfig_XinLianPriKey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinlianenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联公钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianPubKey" name="SysConfig_XinLianPubKey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                        <tbody class="editEnable">
                            <tr>
                                <td>山东信联ETC(云端扣费)</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_XinLianCloudEnable" name="SysConfig_XinLianCloudEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinliancloudenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联上传地址：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianCloudUrl" name="SysConfig_XinLianCloudUrl" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinliancloudenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">信联appid：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianCloudAppID" name="SysConfig_XinLianCloudAppID" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="xinliancloudenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">签名私钥：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_XinLianCloudPrivateKey" name="SysConfig_XinLianCloudPrivateKey" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                        </tbody>

                        <tbody class="editEnable">
                            <tr>
                                <td>农行聚合支付</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ABCJHEnable" name="SysConfig_ABCJHEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="abcjhenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">接口地址：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCJHUrl" name="SysConfig_ABCJHUrl" />
                                        </div>
                                    </div>
                                </td>
                                <td>接口请求访问地址</td>
                            </tr>
                            <tr class="abcjhenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCJHMerchantNo" name="SysConfig_ABCJHMerchantNo" />
                                        </div>
                                    </div>
                                </td>
                                <td>商户编号</td>
                            </tr>
                            <tr class="abcjhenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">入账商户号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCJHInMerchantNo" name="SysConfig_ABCJHInMerchantNo" />
                                        </div>
                                    </div>
                                </td>
                                <td>商入账户编号</td>
                            </tr>
                            <tr class="abcjhenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">PFX文件路径：</div>
                                        <div class="layui-col-xs9 layui-com-upload" com-input="SysConfig_ABCJHPFXPath"></div>
                                    </div>
                                </td>
                                <td>商户证书储存目录文件路径</td>
                            </tr>
                            <tr class="abcjhenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">CER文件路径：</div>
                                        <div class="layui-col-xs9 layui-com-upload" com-input="SysConfig_ABCJHCERPath"></div>
                                    </div>
                                </td>
                                <td>线上支付平台证书文件路径</td>
                            </tr>
                            <tr class="abcjhenable">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">证书密码：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ABCJHPwd" name="SysConfig_ABCJHPwd" />
                                        </div>
                                    </div>
                                </td>
                                <td>商户证书私钥加密密码</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--电子发票-->
            <div class="layui-tab-item fs" lay-filter="evfpform">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="100">配置项名称</th>
                                <th width="400">配置项值</th>
                                <th width="100">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>是否启用电子发票</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_Fpenable" name="SysConfig_Fpenable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">开票税率：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input rmb number v-max" max="100" id="SysConfig_FpRate" name="SysConfig_FpRate" /><span class="rmb">%</span>
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">分类编码（税目编码）：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_FpTaxCode" name="SysConfig_FpTaxCode" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>中税电子发票</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ZSEnable" name="SysConfig_ZSEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="zs">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">AppID：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ZSFpAppId" name="SysConfig_ZSFpAppId" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="zs">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">AppSecret：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_ZSFpAppSecret" name="SysConfig_ZSFpAppSecret" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>航信电子发票</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_HXEnable" name="SysConfig_HXEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="hx">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">身份认证(identity)：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_HxIdentity" name="SysConfig_HxIdentity" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="hx">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">公司电话(salephone)：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_HxSalePhone" name="SysConfig_HxSalePhone" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="hx">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">公司地址(saleaddress)：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_HxSaleAddress" name="SysConfig_HxSaleAddress" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="hx">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">公司税号(saletaxnum)：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_HxSaleTaxnum" name="SysConfig_HxSaleTaxnum" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="hx">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">开票员(clerk)：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_HxClerk" name="SysConfig_HxClerk" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="hx">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">银行账号(saleaccount)：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_HxSaleAccount" name="SysConfig_HxSaleAccount" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>高灯电子发票</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_GDEnable" name="SysConfig_GDEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="gd">
                                <td></td>
                                <td>
                                    <div class="layui-row">
                                        <div class="layui-col-xs3 edit-label2">纳税人识别号：</div>
                                        <div class="layui-col-xs9">
                                            <input type="text" class="layui-input" id="SysConfig_TaxPayerNum" name="SysConfig_TaxPayerNum" />
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--云车场服务-->
            <div class="layui-tab-item fs" lay-filter="applyform">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="200">配置项名称</th>
                                <th width="300">配置项值</th>
                                <th width="300">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>连接平台类型</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ApiType" name="SysConfig_ApiType" value="" maxlength="255" />
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>第三方转发地址</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ApiUrl" name="SysConfig_ApiUrl" value="" maxlength="255" />
                                </td>
                                <td>数据转发地址</td>
                            </tr>
                            <tr>
                                <td>第三方应用APPID</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ApiAppID" name="SysConfig_ApiAppID" value="" maxlength="255" />
                                </td>
                                <td>数据转发应用标识</td>
                            </tr>
                            <tr>
                                <td>第三方应用APPSECRET</td>
                                <td>
                                    <input type="password" class="layui-input" autocomplete="off" id="SysConfig_ApiAppSecret" name="SysConfig_ApiAppSecret" value="" maxlength="255" />
                                </td>
                                <td>数据转发应用密钥,用于生成数据签名</td>
                            </tr>
                        </tbody>

                        <tbody>
                            <tr>
                                <td>阿里云存储Endpoint</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_AliyunEndpoint" name="SysConfig_AliyunEndpoint" value="" maxlength="255" />
                                </td>
                                <td>阿里云存储节点</td>
                            </tr>
                            <tr>
                                <td>阿里云存储AccessKeyId</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_AliyunAccessKeyId" name="SysConfig_AliyunAccessKeyId" value="" maxlength="255" />
                                </td>
                                <td>云存储调用Key</td>
                            </tr>
                            <tr>
                                <td>阿里云存储AccessKeySecret</td>
                                <td>
                                    <input type="password" class="layui-input password" autocomplete="off" id="SysConfig_AliyunAccessKeySecret" name="SysConfig_AliyunAccessKeySecret" value="" maxlength="255" />
                                    @*<div class="icon layui-icon layui-icon-password"></div>*@
                                </td>
                                <td>云存储调用密钥</td>
                            </tr>
                            <tr>
                                <td>阿里云存储ImgBucket</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_AliyunImgBucket" name="SysConfig_AliyunImgBucket" value="" maxlength="255" />
                                </td>
                                <td>图片存储空间名称</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!--第三方服务-->
            <div class="layui-tab-item fs" lay-filter="threeApiform">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="200">配置项名称</th>
                                <th width="300">配置项值</th>
                                <th width="300">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody class="editEnable">
                            <tr>
                                <td>启用第三方服务</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ThreeApiEnable" name="SysConfig_ThreeApiEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td></td>
                            </tr>
                            <tr class="threeservers">
                                <td>停车场编号</td>
                                <td>
                                    <div class="input-group">
                                        <input type="text" class="layui-input" readonly="readonly" autocomplete="off" id="SysConfig_ParkNo" name="SysConfig_ParkNo" value="" maxlength="255" style="display: inline-block;" />
                                        <span class="input-group-btn" style="vertical-align: top; top: -5px;left:-2px;">
                                            <button type="button" class="layui-btn" id="OnCopySysConfigParkNo" style="display: inline-block;margin-top:5px;margin-bottom:3px; width:80px; border-radius:0px"><i class="layui-icon layui-icon-file"></i>复制</button>
                                        </span>
                                    </div>
                                </td>
                                <td>本停车场的编号</td>
                            </tr>
                            <tr class="threeservers">
                                <td>第三方服务AppID</td>
                                <td>
                                    <div class="input-group">
                                        <input type="text" class="layui-input " autocomplete="off" id="SysConfig_ThreeApiAppID" name="SysConfig_ThreeApiAppID" value="" maxlength="150" style="display: inline-block;" />
                                        <span class="input-group-btn" style="vertical-align: top; top: -5px;left:-2px;">
                                            <button type="button" class="layui-btn" id="OnCopyThreeApiAppID" style="display: inline-block;margin-top:5px;margin-bottom:3px; width:80px; border-radius:0px"><i class="layui-icon layui-icon-file"></i>复制</button>
                                            <div class="button-separator"></div>
                                            <button type="button" class="layui-btn" id="OnRestThreeApiAppID" style="display: inline-block; margin-top:5px;margin-left: 0px;margin-bottom:3px; width:80px; border-radius:0px"><i class="layui-icon layui-icon-refresh"></i>生成</button>
                                        </span>
                                    </div>
                                </td>
                                <td>第三方服务AppID</td>
                            </tr>
                            <tr class="threeservers">
                                <td>第三方服务AppSecret</td>
                                <td>
                                    <div class="input-group">
                                        <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeApiAppSecret" name="SysConfig_ThreeApiAppSecret" value="" maxlength="255" style="display: inline-block;" />
                                        <span class="input-group-btn" style="vertical-align: top;top: -5px;left:-2px;">
                                            <button type="button" class="layui-btn" id="OnCopyThreeApiAppSecret" style="display: inline-block;margin-top:5px;margin-bottom:3px; width:80px; border-radius:0px"><i class="layui-icon layui-icon-file"></i>复制</button>
                                            <div class="button-separator"></div>
                                            <button type="button" class="layui-btn" id="OnRestThreeApiAppSecret" style="display: inline-block; margin-top:5px;margin-left: 0px;margin-bottom:3px; width:80px; border-radius:0px"><i class="layui-icon layui-icon-refresh"></i>生成</button>
                                        </span>
                                    </div>
                                </td>
                                <td>第三方服务AppSecret</td>
                            </tr>
                            <tr class="threeservers" style="background-color:#fafafa;color: #1e9fff;">
                                <td>
                                    开放接口版本
                                </td>
                                <td colspan="2">
                                    <select class="layui-select" id="SysConfig_ThreeApiVersion" name="SysConfig_ThreeApiVersion">
                                        <option value="0">V1 （版本接口已停止维护）</option>
                                        <option value="1">V2 （最新）</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>

                        <tbody class="threeservers v2">
                            <tr class="threeservers">
                                <td>第三方服务订阅回调</td>
                                <td>
                                    <button type="button" class="layui-btn" id="OnEditeCallBack" style="width:86px;"><i class="layui-icon layui-icon-edit"></i>编辑</button>
                                </td>
                                <td></td>
                            </tr>
                        </tbody>

                        <!--在这里标记一个tbody控件显示遍历的订阅回调的服务-->
                        <tbody class="threeservers v2" id="threeapicallback"> </tbody>

                        <tbody class="threeservers v2" id="threeserversmqeditEnable">
                            <tr class="threeservers" style="background-color:#fafafa;color: #1e9fff;"><td colspan="3">MQTT客户端配置</td></tr>
                            <tr>
                                <td>启用MQTT客户端</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ThreeMqttEnable" name="SysConfig_ThreeMqttEnable">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td>请选择是否启用MQTT客户端</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT服务器地址</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttServerUrl" name="SysConfig_ThreeMqttServerUrl" value="" maxlength="255" />
                                </td>
                                <td>请填写MQTT服务器地址</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT服务器端口</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttServerPort" name="SysConfig_ThreeMqttServerPort" value="1883" maxlength="255" />
                                </td>
                                <td>请填写MQTT服务器端口</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT用户名</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttUserName" name="SysConfig_ThreeMqttUserName" value="" maxlength="255" />
                                </td>
                                <td>请填写MQTT用户名</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT密码</td>
                                <td>
                                    <input type="password" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttPassword" name="SysConfig_ThreeMqttPassword" value="" maxlength="255" />
                                </td>
                                <td>请填写MQTT密码</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT客户端ID</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttClientId" name="SysConfig_ThreeMqttClientId" value="" maxlength="255" />
                                </td>
                                <td>请填写MQTT客户端ID</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT订阅主题</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttTopic" name="SysConfig_ThreeMqttTopic" value="" maxlength="255" />
                                </td>
                                <td>请填写MQTT订阅主题</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT发布主题</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttPublishTopic" name="SysConfig_ThreeMqttPublishTopic" value="" maxlength="255" />
                                </td>
                                <td>请填写MQTT发布主题</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT重连间隔</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeMqttReconnectInterval" name="SysConfig_ThreeMqttReconnectInterval" value="10" maxlength="255" />
                                </td>
                                <td>请填写MQTT重连间隔，单位：秒 5~180以内</td>
                            </tr>
                            <tr class="threeserversmq">
                                <td>MQTT服务协议版本</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ThreeMqttVersion" name="SysConfig_ThreeMqttVersion">
                                        <option value="3">V310</option>
                                        <option value="4">V311</option>
                                        <option value="5">V500</option>
                                    </select>
                                </td>
                                <td>请选择MQTT服务协议版本</td>
                            </tr>
                        </tbody>


                        <tbody class="editEnable v1">
                            <tr class="threeservers">
                                <td>第三方服务地址</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_ThreeApiUrl" name="SysConfig_ThreeApiUrl" value="" placeholder="使用V1版本必须填第三方服务地址,否则无法使用" maxlength="255" />
                                </td>
                                <td>接收入场或出场记录JSON数据的统一上报地址</td>
                            </tr>
                            <tr class="threeservers">
                                <td style="min-width:300px;">第三方服务Method</td>
                                <td>
                                    <input type="text" class="layui-input" autocomplete="off" id="SysConfig_Method" name="SysConfig_Method" value="全部" maxlength="255" />
                                </td>
                                <td>EnterCar为入场记录，OutCar为出场记录，MthCarIssue为月租车登记，MthCarCharge为月租车续期，MthCarFail为月租车注销，PayOrder为缴费记录，Person为月租车用户，ParkSpace为车位数，多个以|拼接，都启用填写全部</td>
                            </tr>

                        </tbody>

                    </table>
                </div>
            </div>
            <!--数据清理-->
            <div class="layui-tab-item fs" lay-filter="clearform">
                <div class="leftmenu">
                    <ul></ul>
                </div>
                <div class="rightbody">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th width="200">配置项名称</th>
                                <th width="300">配置项值</th>
                                <th width="300">配置项描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>数据清理</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_ClearData" name="SysConfig_ClearData">
                                        <option value="0">停用</option>
                                        <option value="1">启用</option>
                                    </select>
                                </td>
                                <td>清理数据：一个月之前的记录，三个月之前的记录，半年之前的记录，一年之前的记录，按照设定的时间清理旧数据。<t style="color:red;font-weight: 700;">数据清理后不可恢复，请谨慎操作！！！</t></td>
                            </tr>
                            <tr class="entryrecord">
                                <td>入场记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryRecord" name="SysConfig_EntryRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理入场记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>出场记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryExitRecord" name="SysConfig_EntryExitRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理出场记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>缴费记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryCarCharge" name="SysConfig_EntryCarCharge">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理缴费记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>识别记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryRecognitionRecord" name="SysConfig_EntryRecognitionRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理识别记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>优惠券记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryCouponRecord" name="SysConfig_EntryCouponRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理优惠券记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>开闸记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryOpenGateRecord" name="SysConfig_EntryOpenGateRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理开闸记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>交班记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryShiftRecord" name="SysConfig_EntryShiftRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理交班记录</td>
                            </tr>
                            <tr class="entryrecord">
                                <td>倒车记录</td>
                                <td>
                                    <select class="layui-select" id="SysConfig_EntryReverseRecord" name="SysConfig_EntryReverseRecord">
                                        <option value="0">停用</option>
                                        <option value="1">一个月之前的记录</option>
                                        <option value="2">三个月之前的记录</option>
                                        <option value="3">半年之前的记录</option>
                                        <option value="4">一年之前的记录</option>
                                    </select>
                                </td>
                                <td>清理倒车记录</td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-row" style="padding:0.3rem calc(2rem + 15px);position:fixed;bottom:0;left:0;right:0; background-color: #f7f5f5;">
                <button class="layui-btn layui-btn-sm layui-bg-blue save" data-index="1"><i class="layui-icon layui-icon-ok-circle"></i> 保存配置</button>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="diymenu">
        <div class="layui-row diymenuitem" style="margin-bottom:3px;" data-index="${$index}">
            <div class="layui-col-xs5">
                <select class="layui-input" lay-search>
                    <option value="">请选择</option>
                    <option value="车场配置" {{if key=="车场配置"}} selected {{/if}}>车场配置</option>
                    <option value="场区设置" {{if key=="场区设置"}} selected {{/if}}>场区设置</option>
                    <option value="岗亭管理" {{if key=="岗亭管理"}} selected {{/if}}>岗亭管理</option>
                    <option value="车道管理" {{if key=="车道管理"}} selected {{/if}}>车道管理</option>
                    <option value="设备管理" {{if key=="设备管理"}} selected {{/if}}>设备管理</option>
                    <option value="车场管理" {{if key=="车场管理"}} selected {{/if}}>车场管理</option>
                    <option value="车牌类型" {{if key=="车牌类型"}} selected {{/if}}>车牌类型</option>
                    <option value="车牌颜色" {{if key=="车牌颜色"}} selected {{/if}}>车牌颜色</option>
                    <option value="车位类型" {{if key=="车位类型"}} selected {{/if}}>车位类型</option>
                    <option value="通行控制" {{if key=="通行控制"}} selected {{/if}}>通行控制</option>
                    <option value="特殊车辆" {{if key=="特殊车辆"}} selected {{/if}}>特殊车辆</option>
                    <option value="车场设置" {{if key=="车场设置"}} selected {{/if}}>车场设置</option>
                    <option value="日期设置" {{if key=="日期设置"}} selected {{/if}}>日期设置</option>
                    <option value="充值规则" {{if key=="充值规则"}} selected {{/if}}>充值规则</option>
                    <option value="计费规则" {{if key=="计费规则"}} selected {{/if}}>计费规则</option>
                    <option value="车辆管理" {{if key=="车辆管理"}} selected {{/if}}>车辆管理</option>
                    <option value="车辆登记" {{if key=="车辆登记"}} selected {{/if}}>车辆登记</option>
                    <option value="车主车位" {{if key=="车主车位"}} selected {{/if}}>车主车位</option>
                    <option value="黑名单管理" {{if key=="黑名单管理"}} selected {{/if}}>黑名单管理</option>
                    <option value="车辆注销记录" {{if key=="车辆注销记录"}} selected {{/if}}>车辆注销记录</option>
                    <option value="商家优惠" {{if key=="商家优惠"}} selected {{/if}}>商家优惠</option>
                    <option value="优惠设置" {{if key=="优惠设置"}} selected {{/if}}>优惠设置</option>
                    <option value="车牌优惠" {{if key=="车牌优惠"}} selected {{/if}}>车牌优惠</option>
                    <option value="记录查询" {{if key=="记录查询"}} selected {{/if}}>记录查询</option>
                    <option value="停车收费" {{if key=="停车收费"}} selected {{/if}}>停车收费</option>
                    <option value="缴费记录" {{if key=="缴费记录"}} selected {{/if}}>缴费记录</option>
                    <option value="缴费明细" {{if key=="缴费明细"}} selected {{/if}}>缴费明细</option>
                    <option value="优惠券记录" {{if key=="优惠券记录"}} selected {{/if}}>优惠券记录</option>
                    <option value="场内记录" {{if key=="场内记录"}} selected {{/if}}>场内记录</option>
                    <option value="车牌识别记录" {{if key=="车牌识别记录"}} selected {{/if}}>车牌识别记录</option>
                    <option value="储值车扣费记录" {{if key=="储值车扣费记录"}} selected {{/if}}>储值车扣费记录</option>
                    <option value="特殊车辆放行记录" {{if key=="特殊车辆放行记录"}} selected {{/if}}>特殊车辆放行记录</option>
                    <option value="人工开闸记录" {{if key=="人工开闸记录"}} selected {{/if}}>人工开闸记录</option>
                    <option value="开闸放行记录" {{if key=="开闸放行记录"}} selected {{/if}}>开闸放行记录</option>
                    <option value="出入场记录" {{if key=="出入场记录"}} selected {{/if}}>出入场记录</option>
                    <option value="交班记录" {{if key=="交班记录"}} selected {{/if}}>交班记录</option>
                    <option value="倒车记录" {{if key=="倒车记录"}} selected {{/if}}>倒车记录</option>
                    <option value="事件管理" {{if key=="事件管理"}} selected {{/if}}>事件管理</option>
                    <option value="汇总报表" {{if key=="汇总报表"}} selected {{/if}}>汇总报表</option>
                    <option value="临时车收费统计" {{if key=="临时车收费统计"}} selected {{/if}}>临时车收费统计</option>
                    <option value="月租车收费统计" {{if key=="月租车收费统计"}} selected {{/if}}>月租车收费统计</option>
                    <option value="储值车缴费统计" {{if key=="储值车缴费统计"}} selected {{/if}}>储值车缴费统计</option>
                    <option value="车位续期统计" {{if key=="车位续期统计"}} selected {{/if}}>车位续期统计</option>
                    <option value="车场日报表" {{if key=="车场日报表"}} selected {{/if}}>车场日报表</option>
                    <option value="车场月报表" {{if key=="车场月报表"}} selected {{/if}}>车场月报表</option>
                    <option value="车场年报表" {{if key=="车场年报表"}} selected {{/if}}>车场年报表</option>
                    <option value="系统管理" {{if key=="系统管理"}} selected {{/if}}>系统管理</option>
                    <option value="账号管理" {{if key=="账号管理"}} selected {{/if}}>账号管理</option>
                    <option value="权限管理" {{if key=="权限管理"}} selected {{/if}}>权限管理</option>
                    <option value="操作日志" {{if key=="操作日志"}} selected {{/if}}>操作日志</option>
                    <option value="系统日志" {{if key=="系统日志"}} selected {{/if}}>系统日志</option>
                    <option value="系统设置" {{if key=="系统设置"}} selected {{/if}}>系统设置</option>
                    <option value="记录管理" {{if key=="记录管理"}} selected {{/if}}>记录管理</option>
                    <option value="设备型号" {{if key=="设备型号"}} selected {{/if}}>设备型号</option>
                </select>
            </div>
            <div class="layui-col-xs5">
                <input type="text" class="layui-input diytext" value="${text}" maxlength="32"/>
            </div>
            <div class="layui-col-xs2" style="padding:0 10px;">
                <a class="layui-btn layui-btn-xs layui-bg-red" style="margin-top:10px;" onclick="diymenu.del(this, '${$index}')">删除</a>
            </div>
        </div>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?v=1.2" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/com.file.input.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.serializejson.js" asp-append-version="true"></script>
    <script src="~/Static/js/ajaxfileupload2.js" asp-append-version="true"></script>
    <script type="text/javascript">
        var imgPath = "@System.Web.HttpUtility.UrlEncode(ViewBag.Path)";//图片存储路径
        var dbBackPath = "@System.Web.HttpUtility.UrlEncode(ViewBag.InitPath)";//数据库备份路径

        if ('@carparking.Config.PubVar.iParkingType' == "0") {
            $(".type3,.type4,.type6,.type7,.type10,.type11").removeClass("layui-hide");
            $("#baseDiv .layui-colla-item").removeClass("layui-hide");
            $("#baseitem tr").removeClass("layui-hide");
        }
        myVerify.init();
        comFileUpload.init();
        comFileUpload.removeBtnView($('div[com-input=SysConfig_ABCJHPFXPath]'))
        comFileUpload.removeBtnView($('div[com-input=SysConfig_ABCJHCERPath]'))
        layui.use(['element', 'form'], function () {
            createFastGuide();
            pager.init();

            $(".headmoresetting").unbind("click").click(function () {
                var table = $(this).parent().parent().parent().find(".moresetting");
                if ($(table).is(":hidden")) {
                    $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                    $(this).find("t").text("隐藏更多设置");
                    var versionType1 = localStorage.getItem("versionType");
                    if (versionType1 == "simple") {
                        $(".simple").removeClass("layui-hide").removeClass("versionHide").addClass("layui-hide").addClass("versionHide");
                    } else {
                        $(".simple").removeClass("layui-hide").removeClass("versionHide");
                    }
                } else {
                    $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                    $(this).find("t").text("更多设置");
                }
                $(table).toggle("normal");
            });
        })

        function chooseFile(iptFile) {
            $("#SysConfig_SoftAuth").val(iptFile.value);
            $(".softauthfile").addClass("layui-btn-disabled").attr("disabled", true);
            $(".softauthfile input").attr("disabled", true);

            // 创建FormData对象，将文件添加到其中
            var formData = new FormData();
            formData.append('file', iptFile.files[0]);

            //等待500毫秒再执行
            setTimeout(() => {
                layer.msg("正在导入..", { icon: 16, time: 0 });
                $.ajax({
                    url: '/SystemSetting/SoftAuth',
                    type: 'POST',
                    data: formData,
                    processData: false,  // 告诉jQuery不要处理发送的数据
                    contentType: false,  // 告诉jQuery不要设置contentType
                    success: function (data) {
                        if (data.Success) {
                            $("#SysConfig_SoftAuth").val(data.Data);
                            var index = layer.open({
                                content: "导入成功",
                                btn: ["我知道了"],
                                yes: function () {
                                    layer.close(index);
                                }
                            });
                        } else {
                            $("#SysConfig_SoftAuth").val('');
                            var msg = data.Message;
                            var retMsg = msg.replace(new RegExp("&lt;br/&gt;", "gm"), "<br/>");
                            layer.open({ content: retMsg });
                        }
                    },
                    complete: function () {
                        $("#iptFilePath").val("");
                        $("#inputFile").val("");
                        $(".softauthfile").removeClass("layui-btn-disabled").attr("disabled", false);
                        $(".softauthfile input").attr("disabled", false);
                    },
                    error: function (xhr, status, error) {
                        $("#SysConfig_SoftAuth").val('');
                        console.log("[" + error + "]" + xhr.responseText);
                        layer.msg(status);
                    }
                });
            }, 500);
        }

        var _ThreeApiEnable = 0;
        var layuiIndex = 0;

        var IsWindows = @carparking.Config.AppSettingConfig.SentryMode == "0";
        var pager = {
            model: null,
            syscontent: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltDriveList", {}, function (json) {
                    if (json.success) {
                        var camera = [];
                        var screen = [];
                        var self = [];

                        json.data.forEach(function (item, index) {
                            if (item.Drive_Category == 1) {
                                camera[camera.length] = item;
                                $("#SysConfig_CameraDrive").append('<option value="' + item.Drive_No + '">' + item.Drive_Name + '</option>');
                            }
                            if (item.Drive_Category == 3) {
                                //跳过c08显示屏卡
                                if (item.Drive_Name.indexOf("C08显示屏") >= 0) return;
                                screen[screen.length] = item;
                                $("#SysConfig_ScreenDrive").append('<option value="' + item.Drive_No + '">' + item.Drive_Name + '</option>');
                            }
                            if (item.Drive_Category == 4) {
                                self[self.length] = item;
                                $("#SysConfig_SelfDrive").append('<option value="' + item.Drive_No + '">' + item.Drive_Name + '</option>');
                            }
                        });
                        layui.form.render("select");
                    }
                }, "json");

                layui.laydate.render({ elem: "#SysConfig_DBBackTime", type: "time", trigger: 'click' });

                //objVisible.init(".payweixin", $("#SysConfig_PayWeiXin").val());
                //objVisible.init(".ccb", $("#SysConfig_PayCCB").val());
                //objVisible.init(".lepos", $("#SysConfig_PayLepos").val());
                //objVisible.temp(".alipay", $("#SysConfig_PayAlipay").val());
            },
            bindData: function () {
                LAYER_LOADING("加载中...")
                $.post("GetSysConfig", {}, function (json) {
                    layer.closeAll();
                    if (json.success) {

                        pager.model = json.data;
                        if (pager.model.SysConfig_Content == null || pager.model.SysConfig_Content == "") pager.model.SysConfig_Content = "{}";
                        // pager.model.SysConfig_Content = pager.model.SysConfig_Content.replace(/[\r\n\s+]/g, ' ');
                        var showdata = JSON.parse(pager.model.SysConfig_Content);
                        pager.syscontent = showdata;
                        showdata.SysConfig_QianTong_Enable = showdata.SysConfig_QianTong_Enable || 0;
                        showdata.SysConfig_ThirdNoPwdEnable = showdata.SysConfig_ThirdNoPwdEnable || 0;
                        showdata.SysConfig_XinLianEnable = showdata.SysConfig_XinLianEnable || 0;
                        showdata.SysConfig_ABCJHEnable = showdata.SysConfig_ || 0;
                        showdata.SysConfig_EnableImgPath = showdata.SysConfig_EnableImgPath || 0;
                        _ThreeApiEnable = showdata.SysConfig_ThreeApiEnable = showdata.SysConfig_ThreeApiEnable || 0;
                        showdata.SysConfig_ThreeMqttEnable = showdata.SysConfig_ThreeMqttEnable || 0;
                        if (showdata.SysConfig_EnableImgPath == 0) $("#SysConfig_ImagePath,#SysConfig_SentryImagePath").attr("disabled", true);
                        showdata.SysConfig_DBBack = showdata.SysConfig_DBBack || 0;
                        if (showdata.SysConfig_ImagePath) showdata.SysConfig_ImagePath = decodeURIComponent(showdata.SysConfig_ImagePath);
                        else showdata.SysConfig_ImagePath = decodeURIComponent(imgPath);
                        if (showdata.SysConfig_SentryImagePath) showdata.SysConfig_SentryImagePath = decodeURIComponent(showdata.SysConfig_SentryImagePath);
                        if (showdata.SysConfig_DBBackPath) showdata.SysConfig_DBBackPath = decodeURIComponent(showdata.SysConfig_DBBackPath);
                        else showdata.SysConfig_DBBackPath = decodeURIComponent(dbBackPath);
                        if (showdata.SysConfig_DBBack == 0) $("#SysConfig_DBBackPath").attr("disabled", true);

                        $(".layui-form").fillForm(showdata, function (data) { });

                        if (showdata.SysConfig_ApiType == '1') $("#SysConfig_ApiType").val("智慧停车开放平台");
                        else if (showdata.SysConfig_ApiType == '2') $("#SysConfig_ApiType").val("智慧停车管理平台");

                        diymenu.data = JSON.parse(decodeURIComponent(showdata.SysConfig_DIYMenu || "[]"));
                        diymenu.onload();

                        comFileUpload.setDefault("SysConfig_DIYLogo", showdata.SysConfig_DIYLogo);
                        comFileUpload.setDefault("SysConfig_DIYBackImage", showdata.SysConfig_DIYBackImage);
                        comFileUpload.setDefault("SysConfig_ABCJHPFXPath", showdata.SysConfig_ABCJHPFXPath);
                        comFileUpload.setDefault("SysConfig_ABCJHCERPath", showdata.SysConfig_ABCJHCERPath);
                        comFileUpload.setDefault("SysConfig_ABCJHPFXBase64", showdata.SysConfig_ABCJHPFXBase64);
                        comFileUpload.setDefault("SysConfig_ABCJHCERBase64", showdata.SysConfig_ABCJHCERBase64);

                        layui.form.val("payedform", showdata);

                        objVisible.init(".payweixin", $("#SysConfig_PayWeiXin").val());
                        objVisible.init(".ccb", $("#SysConfig_PayCCB").val());
                        objVisible.init(".lepos", $("#SysConfig_PayLepos").val());
                        objVisible.init(".sxpay", $("#SysConfig_SxPayMode").val());

                        objVisible.temp(".alipay", $("#SysConfig_PayAlipay").val());
                        objVisible.temp(".zs", $("#SysConfig_ZSEnable").val());
                        objVisible.temp(".hx", $("#SysConfig_HXEnable").val());
                        objVisible.temp(".gd", $("#SysConfig_GDEnable").val());

                        objVisible.init(".wxpaynopwd", $("#SysConfig_WXPayNoPwd").val());
                        objVisible.init(".alipaynopwd", $("#SysConfig_AliPayNoPwd").val());
                        objVisible.init(".ccbnopwd", $("#SysConfig_PayNoCCB").val());
                        objVisible.init(".unionnopwd", $("#SysConfig_UnionPayNoPwd").val());
                        objVisible.init(".cmbpaynopwd", $("#SysConfig_CMBPayNoPwd").val());
                        objVisible.init(".icbcpaynopwd", $("#SysConfig_ICBCPayNoPwd").val());
                        objVisible.init(".abcpaynopwd", $("#SysConfig_ABCPayNoPwd").val());
                        objVisible.init(".bocpaynopwd", $("#SysConfig_BOCPayNoPwd").val());
                        objVisible.init(".qtetcnopwd", $("#SysConfig_QianTong_Enable").val());
                        objVisible.init(".othernopwd", $("#SysConfig_ThirdNoPwdEnable").val());
                        objVisible.init(".xinlianenable", $("#SysConfig_XinLianEnable").val());
                        objVisible.init(".abcjhenable", $("#SysConfig_ABCJHEnable").val());
                        objVisible.init(".xinliancloudenable", $("#SysConfig_XinLianCloudEnable").val());
                        objVisible.init(".threeservers", $("#SysConfig_ThreeApiEnable").val());
                        objVisible.init(".threeserversmq", $("#SysConfig_ThreeMqttEnable").val());
                        oSelect("SysConfig_DBBackPeriod", $("#SysConfig_DBBackPeriod").val());
                        oSelect("SysConfig_DBBack", $("#SysConfig_DBBack").val());

                        //设置停车场编号SysConfig_ParkNo
                        $("#SysConfig_ParkNo").val(json.data.SysConfig_ParkNo);

                        //收款设置、无感、发票、第三方应用设置为只读
                        $('div[lay-filter="payedform"]').find("select,input").attr("disabled", true);
                        $('div[lay-filter="nopayedform"]').find("select,input").attr("disabled", true);
                        $('div[lay-filter="evfpform"]').find("select,input").attr("disabled", true);
                        $('div[lay-filter="applyform"]').find("select,input").attr("disabled", true);
                        $('div[lay-filter="aliyunform"]').find("select,input").attr("disabled", true);
                        $('div[lay-filter="threeApiform"]').find("select,input").attr("disabled", true);
                        $(".editEnable").find("select,input").removeAttr("disabled");
                        //threeserversmqeditEnable
                        $("#threeserversmqeditEnable").find("select,input").removeAttr("disabled");

                        $("#SysConfig_ThreeApiAppID").removeAttr("disabled");
                        $("#SysConfig_ThreeApiAppSecret").removeAttr("disabled");
                        $("#SysConfig_ThreeApiUrl").removeAttr("disabled");

                        console.log(showdata.SysConfig_ThreeApiVersion)
                        if (showdata.SysConfig_ThreeApiVersion == undefined) showdata.SysConfig_ThreeApiVersion = 1;
                        $("#SysConfig_ThreeApiVersion").val(showdata.SysConfig_ThreeApiVersion);
                        if (showdata.SysConfig_ThreeApiVersion != 1) {
                            $(".v2").hide()
                            $(".v1").show();
                        } else {
                            $(".v2").show();
                            $(".v1").hide()
                        }

                        if (showdata.SysConfig_ThirdCameraEnable == 1) {
                            $(".thirdauth").removeClass("hide")
                        } else {
                            $(".thirdauth").addClass("hide")
                        }
                        if (showdata.SysConfig_ClearData == 1) {
                            $(".entryrecord").removeClass("hide")
                        } else {
                            $(".entryrecord").addClass("hide")
                        }

                        var html = "";
                        html += '<tr><td></td><td>回调服务名称</td><td>回调服务地址</td></tr>';
                        //查询订阅的第三方回调信息输出到id=threeapicallback的tbody中
                        if (showdata.SysConfig_ThreeCallBacks == null || showdata.SysConfig_ThreeCallBacks.length == 0) {
                            html += "<tr><td></td><td colspan='2'>当前未订阅任何回调通知</td></tr>";
                            //判断不存在数据则提示未订阅任何回调通知
                            $("#threeapicallback").html(html);
                        }
                        else {
                            var iCount = 0;
                            //添加一个链接，用作编辑回调通知
                            for (var i = 0; i < showdata.SysConfig_ThreeCallBacks.length; i++) {
                                //判断是否启用
                                if (showdata.SysConfig_ThreeCallBacks[i].IsEnable) {
                                    html += '<tr><td></td><td style=\'color:#009688;\'>' + showdata.SysConfig_ThreeCallBacks[i].ServiceName + '</td><td style=\'color:#009688;\'>' + showdata.SysConfig_ThreeCallBacks[i].ServiceUrl + '</td></tr>';
                                    iCount++;
                                }
                            }
                            if (iCount == 0) {
                                html += "<tr><td></td><td colspan='2'>当前未订阅任何回调通知</td></tr>";
                            }

                            $("#threeapicallback").html(html);
                        }

                        layui.form.render("select");
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    console.log(data.elem.id);
                    var val = data.value;
                    if (data.elem.id == 'SysConfig_PayWeiXin') { objVisible.init(".payweixin", val); }
                    else if (data.elem.id == 'SysConfig_PayCCB') { objVisible.init(".ccb", val); }
                    else if (data.elem.id == 'SysConfig_PayLepos') { objVisible.init(".lepos", val); }
                    else if (data.elem.id == 'SysConfig_SxPayMode') { objVisible.init(".sxpay", val); }
                    else if (data.elem.id == "SysConfig_PayAlipay") { objVisible.temp(".alipay", val); }
                    else if (data.elem.id == "SysConfig_ZSEnable" || data.elem.id == "SysConfig_HXEnable" || data.elem.id == "SysConfig_GDEnable") {
                        objVisible.enable(data.elem.id, val);
                    }
                    else if (data.elem.id == "SysConfig_Fpenable") { objVisible.enable(null, val); }
                    else if (data.elem.id == 'SysConfig_WXPayNoPwd') { objVisible.init(".wxpaynopwd", val); }
                    else if (data.elem.id == 'SysConfig_AliPayNoPwd') { objVisible.init(".alipaynopwd", val); }
                    else if (data.elem.id == 'SysConfig_PayNoCCB') { objVisible.init(".ccbnopwd", val); }
                    else if (data.elem.id == 'SysConfig_UnionPayNoPwd') { objVisible.init(".unionnopwd", val); }
                    else if (data.elem.id == 'SysConfig_CMBPayNoPwd') { objVisible.init(".cmbpaynopwd", val); }
                    else if (data.elem.id == 'SysConfig_ICBCPayNoPwd') { objVisible.init(".icbcpaynopwd", val); }
                    else if (data.elem.id == 'SysConfig_ABCPayNoPwd') { objVisible.init(".abcpaynopwd", val); }
                    else if (data.elem.id == 'SysConfig_BOCPayNoPwd') { objVisible.init(".bocpaynopwd", val); }
                    else if (data.elem.id == 'SysConfig_QianTong_Enable') { objVisible.init(".qtetcnopwd", val); }
                    else if (data.elem.id == 'SysConfig_ThirdNoPwdEnable') { objVisible.init(".othernopwd", val); }
                    else if (data.elem.id == 'SysConfig_XinLianEnable') { objVisible.init(".xinlianenable", val); }
                    else if (data.elem.id == 'SysConfig_ABCJHEnable') { objVisible.init(".abcjhenable", val); }
                    else if (data.elem.id == 'SysConfig_ThreeApiEnable') { objVisible.init(".threeservers", val); }
                    else if (data.elem.id == 'SysConfig_ThreeMqttEnable') { objVisible.init(".threeserversmq", val); }
                    else if (data.elem.id == 'SysConfig_XinLianCloudEnable') { objVisible.init(".xinliancloudenable", val); }
                    else if (data.elem.id == 'SysConfig_EnableImgPath') {
                        if (IsWindows) {
                            if (val == '0') { $("#SysConfig_ImagePath,#SysConfig_SentryImagePath").attr("disabled", true); }
                            else { $("#SysConfig_ImagePath,#SysConfig_SentryImagePath").removeAttr("disabled"); }
                        } else {
                            $("#SysConfig_ImagePath,#SysConfig_SentryImagePath").attr("disabled", true);
                        }
                    }
                    else if (data.elem.id == 'SysConfig_DBBack') {
                        if (IsWindows) {
                            if (val == '0') { $("#SysConfig_DBBackPath").attr("disabled", true); }
                            else { $("#SysConfig_DBBackPath").removeAttr("disabled"); }

                            if (val == "2") {
                                var layerDataType = layer.open({
                                    id: 2,
                                    type: 0,
                                    title: "温馨提示",
                                    btn: ["知道了"],
                                    content: "<t style='color:red;'>定时备份+车流量判断</t>：请根据实际业务情况合理设置车流量阈值。<br/>建议您定期检查备份日志（Logs\\Web\\日期\\WebTimeLog.log），确认备份操作是否按预期执行。<br/>如果发现备份操作频繁因车流量过高而跳过，可以考虑调整备份时间或车流量阈值。",
                                    yes: function (res) {
                                        layer.close(layerDataType)
                                    },
                                    btn2: function () { }
                                })
                            } else if (val == "1") {
                                var layerDataType = layer.open({
                                    id: 2,
                                    type: 0,
                                    title: "温馨提示",
                                    btn: ["知道了"],
                                    content: "<t style='color:red;'>定时备份</t>：建议您选择在系统低峰时段进行备份，以减少对正常业务的影响。<br/>例如，如果您预计夜间车流量较低，可以将备份时间设置在夜间进行。<br/>",
                                    yes: function (res) {
                                        layer.close(layerDataType)
                                    },
                                    btn2: function () { }
                                })
                            }
                        } else {
                            $("#SysConfig_DBBackPath").attr("disabled", true);
                        }

                        oSelect("SysConfig_DBBack", val);
                    }
                    else if (data.elem.id == 'SysConfig_DBBackPeriod') {
                        oSelect("SysConfig_DBBackPeriod", val);
                    }
                    else if (data.elem.id == "SysConfig_ThreeApiVersion") {
                        if (val == 1) {
                            $(".v1").hide()
                            $(".v2").show();
                        } else {
                            $(".v1").show();
                            $(".v2").hide()
                        }
                    }
                    else if (data.elem.id == "SysConfig_ThirdCameraEnable") {
                        if (val == 1) {
                            $(".thirdauth").removeClass("hide")
                        } else {
                            $(".thirdauth").addClass("hide")
                        }
                    } else if (data.elem.id == "SysConfig_ClearData") {
                        if (val == 1) {
                            layer.open({
                                type: 1,
                                title: "⚠️ 启用数据清理功能",
                                area: ['400px', 'auto'],
                                btn: ['确认启用', '取消操作'],
                                content: `
                                                <div style="padding: 20px;">
                                                  <p style="font-size: 14px; margin-bottom: 10px;">
                                                    该操作 <strong>不可恢复</strong>，涉及 <span style="color:red;">敏感数据</span>，
                                                    请输入当前登录密码以确认操作：
                                                  </p>
                                                  <input type="password" id="clearPwd" placeholder="请输入登录密码"
                                                         style="width: 100%; padding: 8px; box-sizing: border-box; border: 1px solid #ccc;" />
                                                </div>
                                              `,
                                yes: function (index, layero) {
                                    var password = $("#clearPwd").val().trim();
                                    // 显示加载动画
                                    var loadIndex = layer.load(2, { shade: 0.2 });
                                    $.post("/SystemSetting/EnterPwd", { Admins_Pwd: password }, function (res) {
                                        layer.close(loadIndex); // 关闭 loading
                                        if (res.success) {
                                            $(".entryrecord").removeClass("hide");
                                            layer.close(index);
                                        } else {
                                            layer.msg("密码错误，请重试", { icon: 2 });
                                        }
                                    }).fail(function () {
                                        layer.close(loadIndex);
                                        layer.msg("请求失败，请稍后重试", { icon: 2 });
                                    });
                                },
                                btn2: function () {
                                    $(".entryrecord").addClass("hide");
                                    $("#SysConfig_ClearData").val(0);
                                    layui.form.render("select");
                                }
                            });

                        } else {
                            $(".entryrecord").addClass("hide")
                        }
                    }

                    layui.form.render("select");
                });

                // 复制车场编码
                $("#OnCopySysConfigParkNo").click(function () {
                    var input = document.getElementById("SysConfig_ParkNo");
                    var textToCopy = input.value;

                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        // 使用 Clipboard API 复制文本
                        navigator.clipboard.writeText(textToCopy).then(function () {
                            layer.msg('复制成功');
                        }).catch(function (err) {
                            console.error('复制失败', err);
                            layer.msg('复制失败');
                        });
                    } else {
                        // 回退方法：使用旧的 execCommand 方式
                        input.select();
                        try {
                            document.execCommand('copy');
                            layer.msg('复制成功');
                        } catch (err) {
                            console.error('复制失败', err);
                            layer.msg('复制失败');
                        }
                        // 取消选中
                        window.getSelection().removeAllRanges();
                    }

                    // 取消输入框的焦点
                    input.blur();
                });

                // OnCopyThreeApiAppSecret 复制第三方服务AppSecret
                $('#OnCopyThreeApiAppSecret').click(function () {
                    var input = document.getElementById("SysConfig_ThreeApiAppSecret");
                    var textToCopy = input.value;

                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        // 使用 Clipboard API 复制文本
                        navigator.clipboard.writeText(textToCopy).then(function () {
                            layer.msg('复制成功');
                        }).catch(function (err) {
                            console.error('复制失败', err);
                            layer.msg('复制失败');
                        });
                    } else {
                        // 回退方法：使用旧的 execCommand 方式
                        input.select();
                        try {
                            document.execCommand('copy');
                            layer.msg('复制成功');
                        } catch (err) {
                            console.error('复制失败', err);
                            layer.msg('复制失败');
                        }
                        // 取消选中
                        window.getSelection().removeAllRanges();
                    }

                    // 取消输入框的焦点
                    input.blur();
                });

                // OnRestThreeApiAppSecret 重置第三方服务AppSecret
                $("#OnRestThreeApiAppSecret").click(function () {
                    layer.confirm('确认生成第三方服务AppSecret? 保存该参数后，之前的参数将失效！', {
                        btn: ['确认', '取消']
                    }, function () {
                        // 生成一个随机字符串
                        var randomString = Math.random().toString(36).slice(2, 10);
                        randomString += Math.random().toString(36).slice(2, 10);

                        // 设置新的AppSecret
                        $("#SysConfig_ThreeApiAppSecret").val(randomString);
                        layer.msg('生成参数成功,保存后生效！');
                    }, function () {
                        // 取消时不做任何处理
                    });
                });

                //OnCopyThreeApiAppID 复制第三方服务AppID
                $('#OnCopyThreeApiAppID').click(function () {

                    var input = document.getElementById("SysConfig_ThreeApiAppID");
                    var textToCopy = input.value;

                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        // 使用 Clipboard API 复制文本
                        navigator.clipboard.writeText(textToCopy).then(function () {
                            layer.msg('复制成功');
                        }).catch(function (err) {
                            console.error('复制失败', err);
                            layer.msg('复制失败');
                        });
                    } else {
                        // 回退方法：使用旧的 execCommand 方式
                        input.select();
                        try {
                            document.execCommand('copy');
                            layer.msg('复制成功');
                        } catch (err) {
                            console.error('复制失败', err);
                            layer.msg('复制失败');
                        }
                        // 取消选中
                        window.getSelection().removeAllRanges();
                    }

                    // 取消输入框的焦点
                    input.blur();
                });

                //OnRestThreeApiAppID 重置第三方服务AppID
                $("#OnRestThreeApiAppID").click(function () {
                    layer.confirm('确认生成第三方服务AppID? 保存该参数后，之前的参数将失效！', {
                        btn: ['确认', '取消']
                    }, function () {
                        var randomString = Math.random().toString(36).slice(2, 6);
                        randomString += Math.random().toString(36).slice(2, 6);
                        $("#SysConfig_ThreeApiAppID").val(randomString);
                        layer.msg('生成参数成功，保存后生效！');
                    }, function () {
                    });
                });

                //OnEditeCallBack 编辑第三方服务回调信息
                $("#OnEditeCallBack").click(function () {
                    layer.open({
                        type: 2,
                        title: "<i class='fa fa-edit'></i> 编辑服务回调信息",
                        shadeClose: true,
                        shade: 0.8,
                        area: ['810px', '590px'],
                        content: 'EditCallBack?1'
                    });
                });

                //保存参数配置
                $("button.save").click(function () {
                    debugger
                    var that = $(this);
                    if (!myVerify.check()) return;

                    var param = $(".layui-form").formToJSON(true, function (data) {
                        return data;
                    }, false);

                    //SysConfig_CouponSort 设置优惠券使用顺序
                    param.SysConfig_CouponSort = $("#SysConfig_CouponSort").val();
                    //SysConfig_CameraDrive 设置默认相机设备系列
                    param.SysConfig_CameraDrive = $("#SysConfig_CameraDrive").val();
                    //SysConfig_OpenCameraVideo 设置默认打开相机视频
                    param.SysConfig_OpenCameraVideo = $("#SysConfig_OpenCameraVideo").val();
                    //SysConfig_ScreenDrive 设置宇视屏卡
                    param.SysConfig_ScreenDrive = $("#SysConfig_ScreenDrive").val();
                    //SysConfig_CameraImageQuality 设置图片抓拍分辨率
                    param.SysConfig_CameraImageQuality = $("#SysConfig_CameraImageQuality").val();
                    //SysConfig_SelfDrive 设置默认自助设备系列
                    param.SysConfig_SelfDrive = $("#SysConfig_SelfDrive").val();
                    //SysConfig_CopyRight 设置版权信息
                    param.SysConfig_CopyRight = $("#SysConfig_CopyRight").val();
                    //SysConfig_FreeReasons 设置免费原因
                    param.SysConfig_FreeReasons = $("#SysConfig_FreeReasons").val();
                    //SysConfig_EnterReasons 设置入场原因
                    param.SysConfig_EnterRemarks = $("#SysConfig_EnterRemarks").val();
                    //SysConfig_SerRecordDay 服务器历史记录保存天数
                    param.SysConfig_SerRecordDay = $("#SysConfig_SerRecordDay").val();
                    //SysConfig_SerImgDay 服务器图片保存天数
                    param.SysConfig_SerImgDay = $("#SysConfig_SerImgDay").val();
                    //SysConfig_ClientRecordDay 岗亭电脑历史记录保存天数
                    param.SysConfig_ClientRecordDay = $("#SysConfig_ClientRecordDay").val();
                    //SysConfig_ClientImgDay 岗亭电脑历史图片保存天数
                    param.SysConfig_ClientImgDay = $("#SysConfig_ClientImgDay").val();
                    //SysConfig_LogSaveDay 岗亭/服务器日志文件保留天数
                    param.SysConfig_LogSaveDay = $("#SysConfig_LogSaveDay").val();
                    //SysConfig_LogOpen 保存本地全量日志
                    param.SysConfig_LogOpen = $("#SysConfig_LogOpen").val();
                    //SysConfig_SerSyncData 岗亭同步数据记录保存月数
                    param.SysConfig_SerSyncData = $("#SysConfig_SerSyncData").val();
                    //SysConfig_EnableImgPath 自定义图片存储
                    param.SysConfig_EnableImgPath = $("#SysConfig_EnableImgPath").val();
                    //SysConfig_ImagePath 自定义停车场后台图片存储地址
                    param.SysConfig_ImagePath = $("#SysConfig_ImagePath").val();
                    //SysConfig_SentryImagePath 自定义停车场岗亭图片存储地址
                    param.SysConfig_SentryImagePath = $("#SysConfig_SentryImagePath").val();
                    //SysConfig_DBBack 自动备份数据库
                    param.SysConfig_DBBack = $("#SysConfig_DBBack").val();
                    param.SysConfig_DBBackFlowMin = $("#SysConfig_DBBackFlowMin").val();
                    param.SysConfig_DBBackFlowCar = $("#SysConfig_DBBackFlowCar").val();
                    //SysConfig_DBBackPath 备份数据库文件存储路径
                    param.SysConfig_DBBackPath = $("#SysConfig_DBBackPath").val();
                    //SysConfig_DBBackPeriod 备份数据库的周期
                    param.SysConfig_DBBackPeriod = $("#SysConfig_DBBackPeriod").val();
                    //SysConfig_DBBackInterval 备份数据库的间隔天数
                    param.SysConfig_DBBackInterval = $("#SysConfig_DBBackInterval").val();
                    //SysConfig_DBBackTime 备份数据库的时间节点
                    param.SysConfig_DBBackTime = $("#SysConfig_DBBackTime").val();
                    //SysConfig_DBBackRetent 备份数据库的文件保留天数
                    param.SysConfig_DBBackRetent = $("#SysConfig_DBBackRetent").val();
                    //SysConfig_WarmDisk 告警磁盘空间
                    param.SysConfig_WarmDisk = $("#SysConfig_WarmDisk").val();
                    //SysConfig_WarmMemory 告警内存
                    param.SysConfig_WarmMemory = $("#SysConfig_WarmMemory").val();
                    //SysConfig_WarmCPUUse 告警CPU
                    param.SysConfig_WarmCPUUse = $("#SysConfig_WarmCPUUse").val();
                    //SysConfig_WarmNet 告警网络
                    param.SysConfig_WarmNet = $("#SysConfig_WarmNet").val();
                    //SysConfig_WarmCPUTemp 告警CPU温度
                    param.SysConfig_WarmCPUTemp = $("#SysConfig_WarmCPUTemp").val();
                    //SysConfig_SenderServerHost 邮箱地址
                    param.SysConfig_SenderServerHost = $("#SysConfig_SenderServerHost").val();
                    //SysConfig_Senderport 邮箱端口号
                    param.SysConfig_Senderport = $("#SysConfig_Senderport").val();
                    //SysConfig_FromMail 邮箱账号
                    param.SysConfig_FromMail = $("#SysConfig_FromMail").val();
                    //SysConfig_SenderPassword 邮箱密码
                    param.SysConfig_SenderPassword = $("#SysConfig_SenderPassword").val();
                    //SysConfig_SenderUsername 邮箱用户名
                    param.SysConfig_SenderUsername = $("#SysConfig_SenderUsername").val();
                    //SysConfig_DIYEnable 启用自定义设置
                    param.SysConfig_DIYEnable = $("#SysConfig_DIYEnable").val();
                    //SysConfig_DIYName 自定义管理后台名称
                    param.SysConfig_DIYName = $("#SysConfig_DIYName").val();
                    //SysConfig_DIYSoftName 自定义岗亭端名称
                    param.SysConfig_DIYSoftName = $("#SysConfig_DIYSoftName").val();
                    //SysConfig_ThreeApiEnable 第三方服务开启
                    param.SysConfig_ThreeApiEnable = $("#SysConfig_ThreeApiEnable").val();
                    //SysConfig_ThreeApiUrl 第三方服务地址
                    param.SysConfig_ThreeApiUrl = $("#SysConfig_ThreeApiUrl").val();
                    //SysConfig_ThreeApiAppID 第三方服务AppID
                    param.SysConfig_ThreeApiAppID = $("#SysConfig_ThreeApiAppID").val();
                    //SysConfig_ThreeApiAppSecret 第三方服务AppSecret
                    param.SysConfig_ThreeApiAppSecret = $("#SysConfig_ThreeApiAppSecret").val();
                    //SysConfig_Method 第三方服务Method(仅V1版本可用)
                    param.SysConfig_Method = $("#SysConfig_Method").val();
                    //黔通ETC配置
                    //SysConfig_QianTong_Enable
                    param.SysConfig_QianTong_Enable = $("#SysConfig_QianTong_Enable").val();
                    //SysConfig_QianTong_Address
                    param.SysConfig_QianTong_Address = $("#SysConfig_QianTong_Address").val();
                    //SysConfig_QianTong_Port
                    param.SysConfig_QianTong_Port = $("#SysConfig_QianTong_Port").val();
                    //SysConfig_QianTongTransactionFlowAddress
                    param.SysConfig_QianTongTransactionFlowAddress = $("#SysConfig_QianTongTransactionFlowAddress").val();
                    //SysConfig_QingTongMerchantNo
                    param.SysConfig_QingTongMerchantNo = $("#SysConfig_QingTongMerchantNo").val();
                    //SysConfig_QingTongParkNo
                    param.SysConfig_QingTongParkNo = $("#SysConfig_QingTongParkNo").val();
                    //SysConfig_QingTongMaxMoney
                    param.SysConfig_QingTongMaxMoney = $("#SysConfig_QingTongMaxMoney").val();
                    //SysConfig_QingTongAccessCode
                    param.SysConfig_QingTongAccessCode = $("#SysConfig_QingTongAccessCode").val();
                    //SysConfig_QingTongSignCode
                    param.SysConfig_QingTongSignCode = $("#SysConfig_QingTongSignCode").val();
                    //启用第三方无感支付
                    //SysConfig_ThirdNoPwdEnable
                    param.SysConfig_ThirdNoPwdEnable = $("#SysConfig_ThirdNoPwdEnable").val();
                    //SysConfig_ThirdNoPwdAddress
                    param.SysConfig_ThirdNoPwdAddress = $("#SysConfig_ThirdNoPwdAddress").val();
                    //山东信联ETC
                    //SysConfig_XinLianEnable
                    param.SysConfig_XinLianEnable = $("#SysConfig_XinLianEnable").val();
                    //SysConfig_XinLianUrl
                    param.SysConfig_XinLianUrl = $("#SysConfig_XinLianUrl").val();
                    //SysConfig_XinLianAppid
                    param.SysConfig_XinLianAppid = $("#SysConfig_XinLianAppid").val();
                    //SysConfig_XinLianMerChantNo
                    param.SysConfig_XinLianMerChantNo = $("#SysConfig_XinLianMerChantNo").val();
                    //SysConfig_XinLianPriKey
                    param.SysConfig_XinLianPriKey = $("#SysConfig_XinLianPriKey").val();
                    //SysConfig_XinLianPubKey
                    param.SysConfig_XinLianPubKey = $("#SysConfig_XinLianPubKey").val();
                    //山东信联ETC(云端扣费)
                    //SysConfig_XinLianCloudEnable
                    param.SysConfig_XinLianCloudEnable = $("#SysConfig_XinLianCloudEnable").val();
                    //SysConfig_XinLianCloudUrl
                    param.SysConfig_XinLianCloudUrl = $("#SysConfig_XinLianCloudUrl").val();
                    //SysConfig_XinLianCloudAppID
                    param.SysConfig_XinLianCloudAppid = $("#SysConfig_XinLianCloudAppID").val();
                    //SysConfig_XinLianCloudPrivateKey
                    param.SysConfig_XinLianCloudPrivateKey = $("#SysConfig_XinLianCloudPrivateKey").val();
                    //农行聚合支付
                    //SysConfig_ABCJHEnable
                    param.SysConfig_ABCJHEnable = $("#SysConfig_ABCJHEnable").val();
                    //SysConfig_ABCJHUrl
                    param.SysConfig_ABCJHUrl = $("#SysConfig_ABCJHUrl").val();
                    //SysConfig_ABCJHMerchantNo
                    param.SysConfig_ABCJHMerchantNo = $("#SysConfig_ABCJHMerchantNo").val();
                    //SysConfig_ABCJHInMerchantNo
                    param.SysConfig_ABCJHInMerchantNo = $("#SysConfig_ABCJHInMerchantNo").val();
                    //SysConfig_ABCJHPwd
                    param.SysConfig_ABCJHPwd = $("#SysConfig_ABCJHPwd").val();
                    //第三方mqtt客户端配置
                    //SysConfig_ThreeMqttTopic
                    param.SysConfig_ThreeMqttTopic = $("#SysConfig_ThreeMqttTopic").val();
                    //SysConfig_ThreeMqttClientId
                    param.SysConfig_ThreeMqttClientId = $("#SysConfig_ThreeMqttClientId").val();
                    //SysConfig_ThreeMqttPassword
                    param.SysConfig_ThreeMqttPassword = $("#SysConfig_ThreeMqttPassword").val();
                    //SysConfig_ThreeMqttUserName
                    param.SysConfig_ThreeMqttUserName = $("#SysConfig_ThreeMqttUserName").val();
                    //SysConfig_ThreeMqttServerPort
                    param.SysConfig_ThreeMqttServerPort = $("#SysConfig_ThreeMqttServerPort").val();
                    //SysConfig_ThreeMqttServerUrl
                    param.SysConfig_ThreeMqttServerUrl = $("#SysConfig_ThreeMqttServerUrl").val();
                    //SysConfig_ThreeMqttPublishTopic
                    param.SysConfig_ThreeMqttPublishTopic = $("#SysConfig_ThreeMqttPublishTopic").val();
                    //SysConfig_ThreeMqttReconnectInterval
                    param.SysConfig_ThreeMqttReconnectInterval = $("#SysConfig_ThreeMqttReconnectInterval").val();

                    if (param.SysConfig_DBBack == 2) {
                        if (param.SysConfig_DBBackFlowMin == "") { layer.msg("请填写车流量判断分钟"); return; }
                        if (param.SysConfig_DBBackFlowCar == "") { layer.msg("请填写车流量判断车辆数"); return; }
                        if (!/^[0-9]*$/.test(param.SysConfig_DBBackFlowMin) || param.SysConfig_DBBackFlowMin <= 0 || param.SysConfig_DBBackFlowMin > 5) {
                            layer.msg("车流量判断：分钟只能输入数字并且在1-5之间");
                            return;
                        }
                        if (!/^[0-9]*$/.test(param.SysConfig_DBBackFlowCar) || param.SysConfig_DBBackFlowCar <= 0 || param.SysConfig_DBBackFlowCar > 10) {
                            layer.msg("车流量判断：车辆数只能输入数字并且在1-10之间");
                            return;
                        }
                    }
                    param.SysConfig_SoftAuth = $("#SysConfig_SoftAuth").val();
                    //判断是否启用mqtt
                    if (param.SysConfig_ThreeMqttEnable == 1) {
                        //判断端口号只能输入数字并且在0-65535之间
                        if (!/^[0-9]*$/.test(param.SysConfig_ThreeMqttServerPort) || param.SysConfig_ThreeMqttServerPort < 0 || param.SysConfig_ThreeMqttServerPort > 65535) {
                            layer.msg("第三方mqtt服务启用时，mqtt服务端口号只能输入数字并且在0-65535之间");
                            return;
                        }

                        //判断mqtt服务地址是否为空
                        if (param.SysConfig_ThreeMqttServerUrl == "") {
                            layer.msg("第三方mqtt服务启用时，mqtt服务地址不能为空");
                            return;
                        }

                        //判断mqtt客户端ID是否为空
                        if (param.SysConfig_ThreeMqttClientId == "") {
                            layer.msg("第三方mqtt服务启用时，mqtt客户端ID不能为空");
                            return;
                        }

                        //判断mqtt用户名是否为空
                        if (param.SysConfig_ThreeMqttUserName == "") {
                            layer.msg("第三方mqtt服务启用时，mqtt用户名不能为空");
                            return;
                        }

                        //判断mqtt密码是否为空
                        if (param.SysConfig_ThreeMqttPassword == "") {
                            layer.msg("第三方mqtt服务启用时，mqtt密码不能为空");
                            return;
                        }

                        //判断mqtt主题是否为空
                        if (param.SysConfig_ThreeMqttTopic == "") {
                            layer.msg("第三方mqtt服务启用时，mqtt订阅主题不能为空");
                            return;
                        }

                        //判断mqtt发布主题是否为空
                        if (param.SysConfig_ThreeMqttPublishTopic == "") {
                            layer.msg("第三方mqtt服务启用时，mqtt发布主题不能为空");
                            return;
                        }
                        //判断mqtt重连间隔时间是否为空,并且只能输入数字5-180秒之间
                        if (!/^[0-9]*$/.test(param.SysConfig_ThreeMqttReconnectInterval) || param.SysConfig_ThreeMqttReconnectInterval < 5 || param.SysConfig_ThreeMqttReconnectInterval > 180) {
                            layer.msg("第三方mqtt服务启用时，mqtt重连间隔时间只能输入数字并且在5-180之间");
                            return;
                        }
                    }

                    param.SysConfig_DIYMenu = encodeURIComponent(diymenu.getJson());
                    param.SysConfig_DIYLogo = comFileUpload.data.SysConfig_DIYLogo;
                    param.SysConfig_DIYBackImage = comFileUpload.data.SysConfig_DIYBackImage;

                    if (param.SysConfig_ABCJHPFXPath == comFileUpload.data.SysConfig_ABCJHPFXPath) {
                        param.SysConfig_ABCJHPFXBase64 = comFileUpload.data.SysConfig_ABCJHPFXBase64;
                    }
                    else {
                        param.SysConfig_ABCJHPFXBase64 = comFileUpload.data.SysConfig_ABCJHPFXPath;
                    }

                    if (param.SysConfig_ABCJHCERPath == comFileUpload.data.SysConfig_ABCJHCERPath) {
                        param.SysConfig_ABCJHCERBase64 = comFileUpload.data.SysConfig_ABCJHCERBase64;
                    }
                    else {
                        param.SysConfig_ABCJHCERBase64 = comFileUpload.data.SysConfig_ABCJHCERPath;
                    }

                    pager.model.SysConfig_Content = JSON.stringify(param);
                    if (isUpdateImgPath(param.SysConfig_EnableImgPath, param.SysConfig_ImagePath, param.SysConfig_SentryImagePath, param.SysConfig_LogOpen)) {
                        var cotmsg = '<b style="color: red; ">您修改了图片存储路径或本地日志全量存储，需要在重启软件服务后生效.<br/>'
                            + '否则修改后的路径不可访问.<br/>'
                            + '确定保存?</b><br/>';
                        layer.open({
                            title: "消息提示",
                            content: cotmsg,
                            btn: ["确定", "取消"],
                            yes: function () {
                                that.attr("disabled", true);
                                LAYER_LOADING("处理中...")
                                $.post("SaveSysConfig", { jsonModel: JSON.stringify(pager.model) }, function (json) {
                                    that.removeAttr("disabled")
                                    if (json.success) {
                                        pager.syscontent.SysConfig_ImagePath = param.SysConfig_ImagePath;
                                        // pager.syscontent.SysConfig_SentryImagePath = param.SysConfig_SentryImagePath;
                                        pager.syscontent.SysConfig_EnableImgPath = param.SysConfig_EnableImgPath;

                                        layer.msg("保存成功", { icon: 1, time: 1000 });

                                        var cotmsg = '<b style="color: red; ">您修改了图片存储路径或本地日志全量存储，需要在重启软件服务后生效.<br/>'
                                            + '否则修改后的路径不可访问.建议您立即重启软件服务.<br/>'
                                            + '重启过程大约耗费1分钟.<br/>请根据停车场出入口实际情况合理安排重启.</b>';
                                        var findex = layer.open({
                                            title: "消息提示",
                                            content: cotmsg,
                                            btn: ["立即重启", "稍候重启"],
                                            yes: function () {
                                                $.post("StopWebService", {}, function (json) {
                                                    layer.msg(json.msg, { icon: 1, time: 1000 }, function () {
                                                        window.top.location.href = '/Login';
                                                    });
                                                }, "json");

                                                setTimeout(() => { window.top.location.href = '/Login'; }, 3000);
                                                layer.close(findex);
                                            }
                                        });

                                    } else {
                                        layer.msg(json.msg, { title: "消息提示", btn: ['我知道了'], icon: 0, time: 0, area: ["260px", "auto"] });
                                    }
                                }, "json");
                            }
                        });
                    } else {
                        var cotmsg = '';

                        if (param.SysConfig_ThreeApiEnable == 1 && _ThreeApiEnable != param.SysConfig_ThreeApiEnable) { cotmsg += "第三方服务已启用，需要在重启软件服务后生效，确定保存?" }
                        else { cotmsg += "确定保存?" }

                        layer.open({
                            title: "消息提示",
                            content: cotmsg,
                            btn: ["确定", "取消"],
                            yes: function () {

                                LAYER_LOADING("处理中...")

                                $.post("SaveSysConfig", { jsonModel: JSON.stringify(pager.model) }, function (json) {
                                    that.removeAttr("disabled")
                                    if (json.success) {
                                        layer.msg("保存成功", { icon: 1, time: 1000 });
                                        if (param.SysConfig_ThreeApiEnable == 1 && _ThreeApiEnable != param.SysConfig_ThreeApiEnable) {
                                            cotmsg = '<b style="color: red; ">您启用了第三方服务，需要在重启软件服务后生效.<br/>'
                                                + '重启过程大约耗费1分钟.<br/>请根据停车场出入口实际情况合理安排重启.</b>';

                                            var findex = layer.open({
                                                title: "消息提示",
                                                content: cotmsg,
                                                btn: ["立即重启", "稍候重启"],
                                                yes: function () {
                                                    $.post("StopWebService", {}, function (json) {
                                                        layer.msg(json.msg, { icon: 1, time: 1000 }, function () {
                                                            window.top.location.href = '/Login';
                                                        });
                                                    }, "json");

                                                    setTimeout(() => { window.top.location.href = '/Login'; }, 3000);
                                                    layer.close(findex);
                                                }
                                            });
                                        }

                                    } else {
                                        layer.msg(json.msg, { title: "消息提示", btn: ['我知道了'], icon: 0, time: 0, area: ["260px", "auto"] });
                                    }
                                }, "json")
                            }
                        });

                    }
                });

                $(".td-diymenu").click(function () {
                    if ($("#SysConfig_DIYMenu").hasClass("layui-hide"))
                        $("#SysConfig_DIYMenu").removeClass("layui-hide")
                    else
                        $("#SysConfig_DIYMenu").addClass("layui-hide")
                });

                //第三方相机授权
                $("#Btn_SysConfig_ThirdCameraAuth").click(function () {
                    layuiIndex = layer.open({
                        type: 2, id: "ThirdCameraAuth",
                        title: "授权信息",
                        content: "DeviceAuth",
                        area: getIframeArea(["800px", "700px"]),
                        maxmin: false
                    });
                });

            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                        $("#OnScanAliAppAuthToken").removeClass("layui-hide");
                        $("#Btn_SysConfig_ThirdCameraAuth").removeClass("layui-hide");
                        $("#Btn_SysConfig_SoftAuth").removeClass("layui-hide");
                        $("#OnCopyThreeApiAppSecret").removeClass("layui-hide");
                        $("#OnRestThreeApiAppSecret").removeClass("layui-hide");
                        $("#OnCopyThreeApiAppID").removeClass("layui-hide");
                        $("#OnRestThreeApiAppID").removeClass("layui-hide");
                        $("#OnCopySysConfigParkNo").removeClass("layui-hide");
                        $("#OnEditeCallBack").removeClass("layui-hide");
                    }
                    $(".layui-com-upload button").removeClass("layui-hide");
                    $(".btncarflow").removeClass("layui-hide");
                });
            },
            bindUpdateCallBack: function (callback) {
                var html = "";
                html += '<tr><td></td><td>回调服务名称</td><td>回调服务地址</td></tr>';
                //查询订阅的第三方回调信息输出到id=threeapicallback的tbody中
                if (callback == null || callback.length == 0) {
                    html += "<tr><td></td><td colspan='2'>当前未订阅任何回调通知</td></tr>";
                    //判断不存在数据则提示未订阅任何回调通知
                    $("#threeapicallback").html(html);
                }
                else {
                    var iCount = 0;
                    //添加一个链接，用作编辑回调通知
                    for (var i = 0; i < callback.length; i++) {
                        //判断是否启用
                        if (callback[i].IsEnable) {
                            html += '<tr><td></td><td style=\'color:#009688;\'>' + callback[i].ServiceName + '</td><td style=\'color:#009688;\'>' + callback[i].ServiceUrl + '</td></tr>';
                            iCount++;
                        }
                    }
                    if (iCount == 0) {
                        html += "<tr><td></td><td colspan='2'>当前未订阅任何回调通知</td></tr>";
                    }

                    $("#threeapicallback").html(html);
                }
            },
            closeAuth: function () {
                layer.close(layuiIndex);
            },
        }

        //判断是否修改了图片存储路径
        var isUpdateImgPath = function (SysConfig_EnableImgPath, SysConfig_ImagePath, SysConfig_SentryImagePath, SysConfig_LogOpen) {
            if (pager.syscontent == null) return true;
            if (pager.syscontent.SysConfig_EnableImgPath != SysConfig_EnableImgPath) return true;
            if (SysConfig_EnableImgPath == '1' && pager.syscontent.SysConfig_ImagePath != SysConfig_ImagePath) return true;
            // if (SysConfig_EnableImgPath == '1' && pager.syscontent.SysConfig_SentryImagePath != SysConfig_SentryImagePath) return true;
            if (pager.syscontent.SysConfig_LogOpen != SysConfig_LogOpen) return true;
        }

        var objVisible = {
            init: function (e, v) {
                if (v == 0) {
                    $(e).removeClass("layui-hide").addClass("layui-hide")
                }
                else {
                    $(e).removeClass("layui-hide");
                }
            },
            temp: function (e, v) {
                if (v == 0) {
                    $(e).removeClass("layui-hide").addClass("layui-hide");
                } else {
                    $(e).removeClass("layui-hide").addClass("layui-hide");
                    $(e + ".v" + v).removeClass("layui-hide");
                }
            },
            enable: function (e, v) {
                if (e == null) {
                    if (v == 0) {
                        $("#SysConfig_ZSEnable,#SysConfig_HXEnable,#SysConfig_GDEnable").val(0);
                        $("#SysConfig_ZSEnable,#SysConfig_HXEnable,#SysConfig_GDEnable").attr("disabled", true);
                        objVisible.init(".zs", 0);
                        objVisible.init(".hx", 0);
                        objVisible.init(".gd", 0);
                    } else {
                        $("#SysConfig_ZSEnable,#SysConfig_HXEnable,#SysConfig_GDEnable").removeAttr("disabled");
                    }
                } else {
                    var eclass;
                    if (e == "SysConfig_ZSEnable") {
                        if (v == 0) { objVisible.init(".zs", 0); }
                        else {
                            $("#SysConfig_HXEnable,#SysConfig_GDEnable").val(0);
                            objVisible.init(".zs", 1);
                            objVisible.init(".hx", 0);
                            objVisible.init(".gd", 0);
                        }
                    } else if (e == "SysConfig_HXEnable") {
                        if (v == 0) { objVisible.init(".hx", 0); }
                        else {
                            $("#SysConfig_ZSEnable,#SysConfig_GDEnable").val(0);
                            objVisible.init(".zs", 0);
                            objVisible.init(".hx", 1);
                            objVisible.init(".gd", 0);
                        }
                    } else if (e == "SysConfig_GDEnable") {
                        if (v == 0) { objVisible.init(".gd", 0); }
                        else {
                            $("#SysConfig_ZSEnable,#SysConfig_HXEnable").val(0);
                            objVisible.init(".zs", 0);
                            objVisible.init(".hx", 0);
                            objVisible.init(".gd", 1);
                        }
                    }
                }
                layui.form.render("select");
            }
        }

        var oSelect = function (id, val) {
            if (id == "SysConfig_DBBackPeriod") {
                $(".period").removeClass("layui-hide").addClass("layui-hide");
                if (val == 2) { $(".weekday").removeClass("layui-hide") }
                else if (val == 3) { $(".interval").removeClass("layui-hide") }
            } else if (id == "SysConfig_DBBack") {
                if (val == 2) {
                    var flowmin = $("#SysConfig_DBBackFlowMin").val();
                    var flowcar = $("#SysConfig_DBBackFlowCar").val();
                    if (flowmin == "") { $("#SysConfig_DBBackFlowMin").val(3); }
                    if (flowcar == "") { $("#SysConfig_DBBackFlowCar").val(1); }
                    $(".carflow").removeClass("hide").removeClass("layui-hide");
                } else {
                    $(".carflow").removeClass("hide").removeClass("layui-hide").addClass("layui-hide");
                }
            }
        }

        //自定义菜单设置
        var diymenu = {
            data: [
                { key: "车牌类型", text: "月租类型" },
                { key: "进出权限", text: "出入时间控制" },
                { key: "车场设置", text: "车场策略" },
            ],
            onload: function () {
                if (this.data && this.data.length > 0) {
                    $("#SysConfig_DIYMenu").html($("#diymenu").tmpl(this.data));
                    layui.form.render("select");
                }
            },
            add: function () {
                $("#SysConfig_DIYMenu").removeClass("layui-hide");
                var item = { key: "", text: "" };
                this.data[this.data.length] = item;
                $("#SysConfig_DIYMenu").append($("#diymenu").tmpl([item]));
                layui.form.render("select");

                this.getJson();
            },
            del: function (e, index) {
                this.data.splice(index, 1);
                $(e).parent().parent().remove();
            },
            getData: function () {
                var data = [];
                $(".diymenuitem").each(function () {
                    var key = $($(this).find("select")[0]).val();
                    var text = $($(this).find("input.diytext")[0]).val();
                    if (key && text) {
                        var item = { key: key, text: text };
                        data[data.length] = item;
                    }
                });
                return data;
            },
            getJson: function () {
                var data = this.getData();
                return JSON.stringify(data);
            }
        }

        var createFastGuide = function () {
            $('.leftmenu').each(function () {
                var that = $(this);
                var trs = $(this).siblings('.rightbody').find("tbody tr").not('.gdsz');

                var lis = [];
                $(trs).each(function () {
                    var td = $(this).find('td').first();
                    var text = td.text();
                    if (text != "" && text != " " && text != "") {
                        lis.push('<li><a href="#' + text + '">' + text + '</a></li>');
                        td.html('<a name="' + text + '" class="mark"></a>' + text);
                    }
                });

                that.find("ul").html("<div style='color:red;font-weight:blod;line-height:1.5rem;'><i class='layui-icon layui-icon-list'></i>点下方列表导航</div>")
                that.find("ul").append(lis.join(''));

                that.find("ul li a").unbind("click").click(function () {
                    var name = $(this).attr("href").replace("#", "");
                    $("a.mark").parent().removeClass("active");
                    var atd = $("a[name='" + name + "']").parent();
                    atd.addClass("active");
                });
            });
        }
    </script>
</body>
</html>