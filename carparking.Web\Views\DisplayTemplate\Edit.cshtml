<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>屏显模板编辑</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            background-color: #fff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .canvas-container {
            flex: 1;
            width: 100%;
            position: relative;
            overflow: hidden;
            background-color: white;
            transform: translateZ(0);
            will-change: transform;
        }

        .grid-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(45deg, rgba(0, 0, 0, 0.04) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(0, 0, 0, 0.04) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(0, 0, 0, 0.04) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(0, 0, 0, 0.04) 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0;
            opacity: 0.8;
        }

        .grid-lines {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                linear-gradient(to right, rgba(0, 0, 0, 0.08) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(0, 0, 0, 0.08) 1px, transparent 1px);
            background-size: 80px 40px;
            pointer-events: none;
        }

        .drawing-area {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: crosshair;
            background-color: transparent;
        }

        .selection-area {
            position: absolute;
            border: 2px solid #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
            pointer-events: none;
            display: none;
            z-index: 1000;
            box-sizing: border-box;
            border-radius: 0;
            transform: translateZ(0);
            will-change: transform;
        }

        .selection-info {
            color: #1e9fff;
            font-size: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 4px;
            border-radius: 0;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            white-space: nowrap;
            pointer-events: none;
        }

        /* 向导步骤样式 */
        .wizard-steps {
            position: fixed;
            left: 20px;
            top: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 12px;
            z-index: 1000;
            width: 180px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
            gap: 8px;
            transform: translateZ(0);
            will-change: transform;
        }

        .wizard-steps.collapsed {
            width: 36px;
            padding: 8px 6px;
        }

        .step {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 8px;
            color: #999;
            opacity: 0.6;
        }

        .step:hover {
            background: #f5f5f5;
        }

        .step.active {
            background: #e6f7ff;
            color: #1890ff;
            opacity: 1;
            cursor: default;
            font-weight: 500;
        }

        .step.completed {
            color: #52c41a;
            opacity: 1;
        }

        .step.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            flex-shrink: 0;
            transition: all 0.2s ease;
            font-size: 13px;
        }

        .step.active .step-number {
            background: #1890ff;
            color: white;
        }

        .step.completed .step-number {
            background: #52c41a;
            color: white;
        }

        .step-content {
            font-size: 13px;
            transition: opacity 0.2s ease;
            white-space: nowrap;
        }

        .wizard-steps.collapsed .step {
            padding: 6px;
            justify-content: center;
        }

        .wizard-steps.collapsed .step-content {
            display: none;
        }

        .wizard-steps.collapsed .step-number {
            margin: 0;
            width: 24px;
            height: 24px;
            font-size: 12px;
        }

        .wizard-toggle {
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 40px;
            background: white;
            border-radius: 0 6px 6px 0;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
            border: 1px solid #eee;
            border-left: none;
        }

        .wizard-toggle:hover {
            color: #1890ff;
            background: #f9f9f9;
        }

        .wizard-toggle i {
            font-size: 10px;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: -1px;
        }

        .wizard-steps.collapsed .wizard-toggle i {
            transform: rotate(180deg);
        }

        /* 底部工具栏样式 */
        .bottom-toolbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            padding: 8px 12px;
            z-index: 1001;
            display: flex;
            gap: 10px;
            align-items: center;
            height: 56px;
            box-sizing: border-box;
        }

        .tool-button {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s;
        }

        .tool-button:hover {
            background: #f5f5f5;
        }

        .tool-button.active {
            background: #e6f7ff;
            color: #1890ff;
        }

        .tool-divider {
            width: 1px;
            height: 24px;
            background: rgba(0, 0, 0, 0.1);
            margin: 0 4px;
        }

        .tool-button i {
            font-size: 18px;
        }

        /* 禁用状态的工具按钮 */
        .tool-button.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* 新增和删除钮的特殊样式 */
        .tool-button[data-tool="add"] {
            color: #52c41a;
        }

        .tool-button[data-tool="delete"] {
            color: #ff4d4f;
        }

        .tool-button[data-tool="add"]:hover {
            background: #f6ffed;
        }

        .tool-button[data-tool="delete"]:hover {
            background: #fff1f0;
        }

        /* 修改下一步和上一步按钮的共同样式 */
        .tool-button[data-tool="next"],
        .tool-button[data-tool="prev"] {
            padding: 0 10px;
            width: auto;
            height: 32px;
            font-size: 14px;
            border-radius: 6px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 下一步按钮特有样式 */
        .tool-button[data-tool="next"] {
            background: #1890ff;
            color: white;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        }

        .tool-button[data-tool="next"]:hover {
            background: #40a9ff;
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
            transform: translateY(-1px);
        }

        .tool-button[data-tool="next"]:active {
            background: #096dd9;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
        }

        /* 上一步按钮特有样式 */
        .tool-button[data-tool="prev"] {
            background: #ff5722;
            color: white;
            border: none;
            box-shadow: 0 2px 4px rgba(255, 87, 34, 0.2);
        }

        .tool-button[data-tool="prev"]:hover {
            background: #ff7043;
            color: white;
            box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
            transform: translateY(-1px);
        }

        .tool-button[data-tool="prev"]:active {
            background: #f4511e;
            color: white;
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(255, 87, 34, 0.2);
        }

        /* 按钮中的图标样式 */
        .tool-button[data-tool="next"] i,
        .tool-button[data-tool="prev"] i {
            font-size: 14px;
        }

        .tool-button[data-tool="prev"] i {
            margin-right: 6px;
        }

        .tool-button[data-tool="next"] i {
            margin-left: 6px;
        }

        /* 添加节目区域列表的样式 */
        .program-areas-list {
            position: fixed;
            left: 20px;
            top: 240px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 12px;
            width: 180px;
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-height: calc(100vh - 322px);
            display: flex;
            flex-direction: column;
        }

        .program-areas-list.collapsed {
            width: 36px;
            padding: 8px 6px;
        }

        .program-areas-toggle {
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 40px;
            background: white;
            border-radius: 0 6px 6px 0;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: all 0.2s ease;
            border: 1px solid #eee;
            border-left: none;
        }

        .program-areas-toggle:hover {
            color: #1890ff;
            background: #f9f9f9;
        }

        .program-areas-toggle i {
            font-size: 10px;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-left: -1px;
        }

        .program-areas-list.collapsed .program-areas-toggle i {
            transform: rotate(180deg);
        }

        .program-areas-list .title {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .program-areas-list .title span {
            font-weight: 500;
        }

        .program-areas-list .title .count {
            color: #1890ff;
            font-size: 12px;
        }

        .program-area-item {
            display: flex;
            align-items: center;
            border-radius: 4px;
            cursor: pointer;
            padding: 5px 8px;
            transition: all 0.2s;
        }

        .program-area-item:hover {
            background: #f5f5f5;
        }

        .program-area-item.active {
            background: #e6f7ff;
        }

        .program-area-item:hover svg {
            transform: scale(1.3);
            transition: all 0.2s ease;
        }

        .program-area-item.active svg {
            transform: scale(1.4);
            transition: all 0.2s ease;
        }

        .program-area-color {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
            position: relative;
            transition: all 0.2s ease;
        }

        /* 用伪元素创建双层圆圈效果 */
        .program-area-color::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            opacity: 0;
            transition: all 0.2s ease;
        }

        /* 悬停状态样式 */
        .program-area-item:hover .program-area-color {
            transform: scale(1.1);
        }

        /* 选中状态样式 */
        .program-area-item.active .program-area-color {
            transform: scale(1);
        }

        .program-area-item.active .program-area-color::before {
            background: currentColor;
            opacity: 0.2;
        }

        .program-area-info {
            flex: 1;
            font-size: 12px;
            color: #666;
            display: block;
            margin-left: 8px;
        }

        .program-area-size {
            font-size: 11px;
            color: #999;
            margin-top: 2px;
        }

        .no-areas-tip {
            color: #999;
            font-size: 12px;
            text-align: center;
            padding: 8px 0;
        }

        /* 修改区域容器样式，添加滚动 */
        .areas-container {
            overflow-y: auto;
            overflow-x: hidden;
            flex: 1;
            min-height: 60px;
        }

        /* 自定义滚动条样式 - Webkit浏览器 */
        .areas-container::-webkit-scrollbar {
            width: 4px;
        }

        .areas-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .areas-container::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        .areas-container::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.3);
        }

        /* 收起状态下的样式调整 */
        .program-areas-list.collapsed .title,
        .program-areas-list.collapsed .no-areas-tip {
            border-bottom: none;
            /* 收起状态下移除分割线 */
            margin-bottom: 0;
            padding: 0;
            width: 32px;
        }

        .program-areas-list.collapsed .program-area-info,
        .program-areas-list.collapsed .title span,
        .program-areas-list.collapsed .title .count {
            display: none;
        }

        .program-areas-list.collapsed .no-areas-tip {
            writing-mode: vertical-lr;
            text-orientation: upright;
            padding: 8px 0;
            letter-spacing: 2px;
            white-space: nowrap;
            min-height: auto;
            text-align: center;
            align-content: space-around;
        }

        /* 修改收起状态下区域项样式 */
        .program-areas-list.collapsed .program-area-item {
            padding: 6px;
            justify-content: center;
        }

        .program-areas-list.collapsed .program-area-color {
            margin: 0;
        }

        /* 确保收起状态下的容器样式正确 */
        .program-areas-list.collapsed .areas-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0;
            min-height: auto;
        }

        /* 向导步骤提示样式 */
        .step-tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.75);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            margin-left: 10px;
            z-index: 1010;
        }

        .step-tooltip::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            border-style: solid;
            border-width: 4px 4px 4px 0;
            border-color: transparent rgba(0, 0, 0, 0.75) transparent transparent;
        }

        /* 节目区域提示样式 */
        .area-tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.75);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            margin-left: 10px;
            z-index: 1010;
            min-width: 120px;
        }

        .area-tooltip::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            border-style: solid;
            border-width: 4px 4px 4px 0;
            border-color: transparent rgba(0, 0, 0, 0.75) transparent transparent;
        }

        /* 仅在收起状态下显示提示 */
        .wizard-steps.collapsed .step:hover .step-tooltip,
        .program-areas-list.collapsed .program-area-item:hover .area-tooltip {
            opacity: 1;
        }

        /* 确保展开状态下不显示提示 */
        .wizard-steps:not(.collapsed) .step-tooltip {
            display: none;
        }

        /* 添加到已有的 style 标签中 */
        .program-functions-container {
            transition: all 0.3s ease;
        }

        .program-functions-container .area-functions {
            background: #f9f9f9;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 12px;
        }

        .program-functions-container select {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 8px;
            font-size: 13px;
            color: #666;
            transition: all 0.3s;
        }

        .program-functions-container select:hover {
            border-color: #40a9ff;
        }

        .program-functions-container select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
    </style>
</head>

<body>
    <div class="canvas-container">
        <div class="grid-background"></div>
        <div class="grid-lines"></div>
        <div class="drawing-area" id="drawingArea"></div>
        <div class="selection-area" id="selectionArea">
            <!-- 添加显示信息的元素 -->
            <div class="selection-info"></div>
        </div>
        <div class="bottom-toolbar">
            <button class="tool-button" data-tool="reset" title="重置选择">
                <i class="layui-icon layui-icon-refresh"></i>
            </button>
            <div class="tool-divider"></div>
            <button class="tool-button" data-tool="next">
                下一步 <i class="layui-icon layui-icon-right"></i>
            </button>
        </div>
    </div>

    <!-- 修改向导面板的HTML结构 -->
    <div class="wizard-steps">
        <div class="wizard-toggle">
            <i class="layui-icon layui-icon-left"></i>
        </div>
        <div class="step active" data-title="选择屏区域">
            <div class="step-number">1</div>
            <div class="step-content">选择屏显区域</div>
        </div>
        <div class="step" data-title="分节目区域">
            <div class="step-number">2</div>
            <div class="step-content">分配节目区域</div>
        </div>
        <div class="step" data-title="分配节目功能">
            <div class="step-number">3</div>
            <div class="step-content">分配节目功能</div>
        </div>
        <div class="step" data-title="保存屏显">
            <div class="step-number">4</div>
            <div class="step-content">保存屏显模板</div>
        </div>
    </div>

    <!-- 修改已分配区域表的 HTML 结构 -->
    <div class="program-areas-list" style="display:none">
        <div class="program-areas-toggle">
            <i class="layui-icon layui-icon-left"></i>
        </div>
        <div class="title">
            <span>已分配节目区域</span>
            <div class="count">0个</div>
        </div>
        <div class="areas-container">
            <div class="no-areas-tip">暂无分配的节目区域</div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script>
        // 在 script 标签的开始处添加全局函数声明
        window.updateProgramAreaStyle = null; // 先声明一个占位符

        layui.use(['layer'], function () {
            var $ = layui.jquery;
            var layer = layui.layer;
            let tipIndex, enterTimer, leaveTimer; // 移至此处，统一管理
 
             // 定义网格相关的常量
            const GRID = {
                WIDTH: 80,
                HEIGHT: 40,
                PATTERN_SIZE: 20,
                LINE_SIZE: 20
            };

            const CIRCLE_SVG_TEMPLATE = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16"  height="16" viewBox="0 0 1024 1024">
                    <path d="M511.874232 511.874232m-511.874232 0a511.874232 511.874232 0 1 0 1023.748465 0 511.874232 511.874232 0 1 0-1023.748465 0Z" fill="{\{color\}}" fill-opacity="0.3"/>
                    <path d="M511.874232 511.874232m-294.044706 0a294.044706 294.044706 0 1 0 588.089413 0 294.044706 294.044706 0 1 0-588.089413 0Z" fill="{\{color\}}" />
                </svg>
            `;

            // 初始化当前工具为选择工具
            let currentTool = 'select';
            let isSelecting = false;
            let startGrid = { x: 0, y: 0 };
            let currentGrid = { x: 0, y: 0 };
            let selectedDisplayArea;//选择节目区域

            const selectionArea = $('#selectionArea');
            const drawingArea = $('#drawingArea');
            // 优化网格位置计算
            const gridCache = {
                rect: null,
                lastUpdate: 0
            };
            // 窗口大小改变时更新
            let resizeTimer;
            // 向导步骤相关代码
            let currentStep = 1;

            // 初始化数据
            initializeData();

            // 初始化事件绑定
            $(document).ready(function () {
                // 初始化面板状态
                if (localStorage.getItem('wizardPanelState') === 'collapsed') {
                    $('.wizard-steps').addClass('collapsed');
                }

                // 初始化已分配区域列表的状态
                if (localStorage.getItem('programAreasState') === 'collapsed') {
                    $('.program-areas-list').addClass('collapsed');
                }
                //工具栏按钮事件处理
                $(document).on('click', '.tool-button', function (e) {
                    e.preventDefault();
                    const tool = $(this).data('tool');

                    if (tool === 'prev') {
                        if (currentStep === 2) {
                            // 检查是否在第二且存在已分配的节目区域
                            const hasAssignedAreas = pager.programAreas &&
                                pager.programAreas.some(area => area.ProgramArea_UseStatus === 1);

                            if (hasAssignedAreas) {
                                layer.confirm('返回上一步将清空所有已分配的节目区域，是否继续？', {
                                    title: '警告',
                                    btn: ['继续', '取消'],
                                    icon: 3,
                                    closeBtn: 0
                                }, function (index) {
                                    currentStep = 1;
                                    updateSteps();
                                    // 清理所有节目区域相关元素
                                    cleanupProgramAreas();
                                    layer.close(index);
                                });
                            } else {
                                // 如果不在二步或没有已分配的节目区域，直接返回上一步
                                currentStep = 1;
                                updateSteps();
                                // 清理所有节目区域相关元素
                                cleanupProgramAreas();
                            }
                        }
                        else if (currentStep === 3) {
                            // 从第三步返回第二步
                            currentStep = 2;
                            updateSteps();

                            // 显示节目区域列表面板
                            $('.program-areas-list').fadeIn(200);

                            // 重新显示已分配的区域
                            showAssignedAreas();

                            // 启用区域选择
                            enableAreaSelection();

                            //获取选中节目区域
                            const selectedProgramArea = $('.program-area-item.active');
                            if (selectedProgramArea.length <= 0) {
                                // 恢复默认选择一个节目区域
                                $('.program-area-item').first().trigger('click');
                            }


                        }
                        else if (currentStep === 4) {
                            // 移除保存模板界面
                            $('.save-template-container').remove();

                            // 返回到第三步
                            currentStep = 3;
                            updateSteps();

                            // 重新显示已分配区域，并使其可点击
                            showAssignedAreas();
                            makeAreasClickable();
                        }
                    }
                    else if (tool === 'reset') {
                        if (currentStep === 1) {
                            resetSelection();
                        } else if (currentStep === 2) {
                            resetProgramAreas();
                        }
                    }
                    else if (tool === 'add' && currentStep === 2) {
                        addProgramArea();
                    }
                    else if (tool === 'delete' && currentStep === 2) {
                        deleteSelectedProgramArea();
                    }
                    else if (tool === 'next') {
                        handleNextStep();
                    }
                });

                // 使用事件委托绑定节目区域项的点击事件
                $(document).on('click', '.program-area-item', function (e) {
                    e.stopPropagation(); // 阻止事件冒泡

                    const clickedItem = $(this);
                    const isCurrentlyActive = clickedItem.hasClass('active');

                    if (!isCurrentlyActive) {
                        // 移除所有项的中状态
                        $('.program-area-item').removeClass('active');

                        // 如果击的不是前选中项，则选中它
                        clickedItem.addClass('active');

                        // 启用删除按钮
                        $('.tool-button[data-tool="delete"]').removeClass('disabled');

                        enableAreaSelection();

                        const index = clickedItem.data('index');
                        // 高亮显示当前选中区域
                        showAssignedAreas(index);
                    }
                });

                // 已分配区域列表的收起功能
                $('.program-areas-toggle').on('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $('.program-areas-list').toggleClass('collapsed');
                    localStorage.setItem('programAreasState', $('.program-areas-list').hasClass('collapsed') ? 'collapsed' : 'expanded');
                });

                // 向导板收起功能
                $('.wizard-toggle').on('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $('.wizard-steps').toggleClass('collapsed');

                    // 保存状态到本地存储
                    localStorage.setItem('wizardPanelState',
                        $('.wizard-steps').hasClass('collapsed') ? 'collapsed' : 'expanded'
                    );
                });

                // 窗口大小改变时更新
                window.addEventListener('resize', function () {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(() => {
                        gridCache.rect = null;
                        if (isSelecting) {
                            updateSelectionArea();
                        }
                    }, 100);
                });

                // 绑定鼠标事件
                bindMouseEvents();

                // 初始化向导步骤提示
                initializeWizardTooltips();
            });

            // 初始化数据
            function initializeData() {
                // 获取URL参数
                const urlParams = new URLSearchParams(window.location.search);
                const id = urlParams.get('Id'); // 修改为大写I,与Controller参数名保持一致


                var loadIndex = layer.msg('加载中', {
                    icon: 16,
                    shade: 0.01
                });

                // 如果是编辑模式，获取模板数据
                $.ajax({
                    url: '/DisplayTemplate/InitializeData',
                    type: 'GET',
                    data: { id: id },
                    success: function (res) {
                        if (res.success) {
                            // 初始化数据
                            pager.displayTemplate = res.data.displayTemplate;
                            pager.programAreas = res.data.programAreas || [];
                            pager.programAreaActions = res.data.programAreaActions || [];
                            pager.parkAreas = res.data.areaInfo || [];
                            pager.passways = res.data.passwayInfo || [];
                            pager.arealinkpassway = res.data.passwayLinkInfo || [];

                            // 如果有已保存的屏显区域，恢复它
                            if (pager.displayTemplate && !(pager.displayTemplate.DisplayTemplate_X == 0 && pager.displayTemplate.DisplayTemplate_Y == 0 && pager.displayTemplate.DisplayTemplate_Width == 0 && pager.displayTemplate.DisplayTemplate_Height == 0)) {
                                selectedDisplayArea = {
                                    x: pager.displayTemplate.DisplayTemplate_X,
                                    y: pager.displayTemplate.DisplayTemplate_Y,
                                    width: pager.displayTemplate.DisplayTemplate_Width,
                                    height: pager.displayTemplate.DisplayTemplate_Height
                                };

                                // 恢复选择区域显示
                                restoreDisplayAreaSelection();

                                if (pager.programAreas.length > 0) {

                                    //如果所有的节目区域都分配完成，则跳转到步骤3
                                    if (pager.programAreas.every(area => area.ProgramArea_UseStatus === 1)) {
                                        currentStep = 2;
                                        showDisplayArea();
                                        updateProgramAreasList();
                                        handleNextStep(true);
                                    }
                                    else {

                                        handleNextStep(true);
                                    }
                                }
                            }
                        } else {
                            layer.confirm('获取模板数据失败: ' + res.msg, {
                                btn: ['确定']
                            }, function (index) {
                                layer.close(index);
                                window.close();
                            });
                        }
                    },
                    error: function () {
                        layer.confirm('获取模板数据失败', {
                            btn: ['确定']
                        }, function (index) {
                            layer.close(index);
                            window.close();
                        });
                    },
                    complete: function () {
                        layer.close(loadIndex);
                    }
                });
            }

            // 重置选择区域的函数
            function resetSelection() {
                // 判断当前是否有选择区域
                if (!$('#selectionArea').is(':visible')) {
                    return;
                }

                layer.confirm('确定要重置选择区域吗?', {
                    title: '提示',
                    btn: ['确定', '取消'],
                    icon: 3,
                    closeBtn: 0
                }, function (index) {
                    $('#selectionArea').hide();
                    isSelecting = false;
                    startGrid = { x: 0, y: 0 };
                    currentGrid = { x: 0, y: 0 };
                    currentTool = 'select';
                    $('#drawingArea').css('cursor', 'crosshair');
                    layer.close(index);
                });
            }

            // 获取网格位置的函数
            function getGridPosition(clientX, clientY) {
                const now = Date.now();
                if (!gridCache.rect || now - gridCache.lastUpdate > 100) {
                    gridCache.rect = drawingArea[0].getBoundingClientRect();
                    gridCache.lastUpdate = now;
                }

                const mouseOffsetX = clientX - gridCache.rect.left;
                const mouseOffsetY = clientY - gridCache.rect.top;

                return {
                    x: Math.floor(mouseOffsetX / GRID.WIDTH),
                    y: Math.floor(mouseOffsetY / GRID.HEIGHT)
                };
            }

            // 更新选择区域的函数
            function updateSelectionArea() {
                const left = Math.min(startGrid.x, currentGrid.x) * GRID.WIDTH;
                const top = Math.min(startGrid.y, currentGrid.y) * GRID.HEIGHT;
                const width = (Math.abs(currentGrid.x - startGrid.x) + 1) * GRID.WIDTH;
                const height = (Math.abs(currentGrid.y - startGrid.y) + 1) * GRID.HEIGHT;

                const containerRect = $('.canvas-container')[0].getBoundingClientRect();
                const relativeLeft = left - containerRect.left;
                const relativeTop = top - containerRect.top;

                selectionArea.css({
                    left: relativeLeft + 'px',
                    top: relativeTop + 'px',
                    width: width + 'px',
                    height: height + 'px',
                    display: 'block'
                });

                // 计算实际的网格大小
                const gridWidth = Math.abs(currentGrid.x - startGrid.x) + 1;
                const gridHeight = Math.abs(currentGrid.y - startGrid.y) + 1;

                // 更新信息显示框
                $('.selection-info').css({
                    display: 'block'
                }).text(`(${gridWidth}x${gridHeight})${gridWidth * gridHeight}个模组`);
            }

            // 更新步骤显示
            function updateSteps() {
                // 移除所有状态类
                $('.step').removeClass('active completed disabled');

                // 更新每个步骤的状态
                $('.step').each(function (index) {
                    const stepNumber = index + 1;
                    if (stepNumber < currentStep) {
                        $(this).addClass('completed');
                    } else if (stepNumber === currentStep) {
                        $(this).addClass('active');
                    } else {
                        $(this).addClass('disabled');
                    }
                });

                // 根据当前步骤更新工具栏
                updateToolbar();

                // 根据当前步骤控制节目区域列表的显示
                if (currentStep === 2) {
                    $('.program-areas-list').fadeIn(200); // 使用淡入效果显示面板
                } else {
                    $('.program-areas-list').fadeOut(200); // 使用淡出效果隐藏面板
                }

                $('.step').each(function () {
                    // 添加提示元素
                    if (!$(this).find('.step-tooltip').length) {
                        $(this).append(`<div class="step-tooltip">${$(this).data('title')}</div>`);
                    }
                });
            }

            // 添加更新工具栏函数
            function updateToolbar() {
                const toolbar = $('.bottom-toolbar');

                if (currentStep === 1) {
                    toolbar.html(`
                    <button class="tool-button" data-tool="reset" title="重置选择">
                        <i class="layui-icon layui-icon-refresh"></i>
                    </button>
                    <div class="tool-divider"></div>
                    <button class="tool-button" data-tool="next">
                        下一步 <i class="layui-icon layui-icon-right"></i>
                    </button>
                `);
                    //显示工具栏
                    toolbar.show();
                }
                else if (currentStep === 2) {
                    toolbar.html(`

                    <button class="tool-button" data-tool="add" title="新增节目区域">
                        <i class="layui-icon layui-icon-add-1"></i>
                    </button>
                    <button class="tool-button" data-tool="delete" title="删除选中区域">
                        <i class="layui-icon layui-icon-delete"></i>
                    </button>
                     <button class="tool-button" data-tool="reset" title="重置节目区域">
                        <i class="layui-icon layui-icon-refresh"></i>
                    </button>
                    <div class="tool-divider"></div>
                    <button class="tool-button" data-tool="prev">
                        <i class="layui-icon layui-icon-left"></i>上一步
                    </button>
                    <button class="tool-button" data-tool="next">
                        下一步<i class="layui-icon layui-icon-right"></i>
                    </button>
                `);
                    //显示工具栏
                    toolbar.show();
                }
                else if (currentStep === 3) {
                    toolbar.html(`
                        <button class="tool-button" data-tool="prev">
                            <i class="layui-icon layui-icon-left"></i>上一步
                        </button>
                        <div class="tool-divider"></div>
                        <button class="tool-button" data-tool="next">
                            下一步<i class="layui-icon layui-icon-right"></i>
                        </button>
                        `);
                    //显示工具栏
                    toolbar.show();
                }
                else {
                    //隐藏工具栏
                    toolbar.hide();
                }
            }

            // bindMouseEvents 函数
            function bindMouseEvents() {
                // 先解绑所有事件，防止重复绑定
                drawingArea.off('mousedown mousemove');
                $(document).off('mouseup.areaSelection');

                // 绑定 mousedown 事件
                drawingArea.on('mousedown', function (e) {
                    // 只响应鼠标左键 (e.button === 0)
                    if (e.button !== 0) return;

                    if (currentStep === 1) {
                        if (currentTool === 'select') {
                            isSelecting = true;
                            startGrid = getGridPosition(e.clientX, e.clientY);
                            currentGrid = startGrid;
                            updateSelectionArea();
                            e.preventDefault();
                        }
                    } else if (currentStep === 2) {
                        const activeItem = $('.program-area-item.active');
                        if (activeItem.length) {
                            const clickGrid = getGridPosition(e.clientX, e.clientY);
                            // 检查点击位置是否在屏显区域内
                            if (clickGrid.x >= selectedDisplayArea.x &&
                                clickGrid.x < selectedDisplayArea.x + selectedDisplayArea.width &&
                                clickGrid.y >= selectedDisplayArea.y &&
                                clickGrid.y < selectedDisplayArea.y + selectedDisplayArea.height) {
                                isSelecting = true;
                                startGrid = clickGrid;
                                currentGrid = clickGrid;
                                updateProgramSelectionArea(activeItem.data('index'));
                                e.preventDefault();
                            } else {
                                layer.msg('请在屏显区域内选择', { time: 1500 });
                            }
                        } else {
                            layer.msg('请先选择一个节目区域', { time: 1500 });
                        }
                    }
                });

                // 绑定 mousemove 事件
                drawingArea.on('mousemove', function (e) {
                    if (!isSelecting) return;

                    if (currentStep === 1) {
                        if (currentTool === 'select') {
                            currentGrid = getGridPosition(e.clientX, e.clientY);
                            updateSelectionArea();
                        }
                    } else if (currentStep === 2) {
                        const activeItem = $('.program-area-item.active');
                        if (activeItem.length) {
                            currentGrid = getGridPosition(e.clientX, e.clientY);
                            updateProgramSelectionArea(activeItem.data('index'));
                        }
                    }
                });

                // 绑定 mouseup 事件 - 注意这里绑定到 document 上
                $(document).on('mouseup.areaSelection', function () {
                    if (!isSelecting) return;

                    if (currentStep === 1) {
                        isSelecting = false;
                    } else if (currentStep === 2) {
                        const activeItem = $('.program-area-item.active');
                        if (activeItem.length) {
                            const index = activeItem.data('index');
                            const area = pager.programAreas[index];
                            // 检查是否可以分配该区域
                            if (updateProgramSelectionArea(index)) {
                                // 计算选择区域的尺寸和位置
                                const selectedWidth = Math.abs(currentGrid.x - startGrid.x) + 1;
                                const selectedHeight = Math.abs(currentGrid.y - startGrid.y) + 1;
                                const selectedX = Math.min(startGrid.x, currentGrid.x);
                                const selectedY = Math.min(startGrid.y, currentGrid.y);

                                // 更新节目区域的属性
                                area.ProgramArea_Width = selectedWidth;
                                area.ProgramArea_Height = selectedHeight;
                                area.ProgramArea_X = selectedX;
                                area.ProgramArea_Y = selectedY;
                                area.ProgramArea_UseStatus = 1; // 标记为已分配

                                // 隐藏选择区域
                                $('#selectionArea').hide();

                                // 更新列表显示
                                updateProgramAreasList();

                                // 保持当前区域选中状态
                                $(`.program-area-item[data-index="${index}"]`).addClass('active');

                                // 更新已分配域显示
                                showAssignedAreas();
                            } else {
                                // 区域重叠，不进行分配，清除选择区域
                                $('#selectionArea').hide();
                                // 显示之前已分配的区域（如果有）
                                if (area.ProgramArea_UseStatus === 1) {
                                    showAssignedAreas();
                                }
                                layer.msg('节目区域重叠，请选择未分配区域或当前节目区域', { time: 1500 });
                            }
                        }
                        isSelecting = false;
                    }
                });
            }

            // disableAreaSelection 函数
            function disableAreaSelection() {
                isSelecting = false;
                $('#drawingArea').css('cursor', 'not-allowed');

                // 解绑所有鼠标事件，使用正地命名空
                drawingArea.off('mousedown mousemove');
                $(document).off('mouseup.areaSelection');

                // 隐藏选中区域的提示信息
                $('.selection-info').hide();
                $('#selectionArea').hide();
            }

            // enableAreaSelection 函数
            function enableAreaSelection() {
                if (currentStep === 1) {
                    $('#drawingArea').css('cursor', 'crosshair');
                } else if (currentStep === 2) {
                    const activeItem = $('.program-area-item.active');
                    if (activeItem.length) {
                        const index = activeItem.data('index');
                        updateMouseCursor(pager.programAreas[index].ProgramArea_Color);
                    } else {
                        $('#drawingArea').css('cursor', 'default');
                    }
                }

                currentTool = 'select';
                bindMouseEvents();

                if ($('#selectionArea').is(':visible')) {
                    $('.selection-info').show();
                }
            }

            // 加第二步相关的功能函数
            function resetProgramAreas() {
                // 检查是否存在已分配的节目区域
                const hasAssignedAreas = pager.programAreas && pager.programAreas.some(area => area.ProgramArea_UseStatus === 1);

                if (!hasAssignedAreas) {
                    return;
                }

                // 添加确认对话框
                layer.confirm('确定要重置所有节目区域分配信息吗?', {
                    title: '提示',
                    btn: ['确定', '取消'],
                    icon: 3,
                    closeBtn: 0
                }, function (index) {
                    pager.programAreas.forEach(area => {
                        area.ProgramArea_Width = 0;
                        area.ProgramArea_Height = 0;
                        area.ProgramArea_X = 0;
                        area.ProgramArea_Y = 0;
                        area.ProgramArea_UseStatus = 0;
                    });

                    // 更新列表显示
                    updateProgramAreasList();

                    // 清除已分配区域的显示
                    $('.assigned-area').remove();

                    // 隐藏选择区域
                    $('#selectionArea').hide();

                    // 如果还有节目区域，默认选中第一个
                    if (pager.programAreas && pager.programAreas.length > 0) {
                        setTimeout(() => {
                            $('.program-area-item:first').click();
                            enableAreaSelection();
                        }, 100);
                    } else {
                        // 如果没有区域了，禁用删除按钮
                        $('.tool-button[data-tool="delete"]').addClass('disabled');
                        // 重置鼠标样式
                        resetMouseCursor();
                    }

                    layer.close(index);
                });
            }

            // updateProgramAreasList 函数，移除可能的重复事件绑定
            function updateProgramAreasList() {
                const container = $('.areas-container');
                const count = $('.program-areas-list .count');

                if (!pager.programAreas || pager.programAreas.length === 0) {
                    container.html('<div class="no-areas-tip">暂无分配的节目区域</div>');
                    count.text('0个');
                    return;
                }

                let html = '';
                pager.programAreas.forEach((area, index) => {
                    const tooltipContent = area.ProgramArea_UseStatus === 1
                        ? `${area.ProgramArea_Name}<br>${area.ProgramArea_Width}x${area.ProgramArea_Height}`
                        : `${area.ProgramArea_Name}<br>未分配区域`;
                    html += `
                        <div class="program-area-item ${area.ProgramArea_UseStatus === 1 ? 'assigned' : ''}" data-index="${index}" data-id="${area.ProgramArea_Id}">
                            ${CIRCLE_SVG_TEMPLATE.replace(/\{\{color}}/g, area.ProgramArea_Color)}
                            <div class="program-area-info">
                                <div>${area.ProgramArea_Name}</div>
                                <div class="program-area-size">${area.ProgramArea_UseStatus === 1 ? `${area.ProgramArea_Width}x${area.ProgramArea_Height}` : '未分配'}</div>
                            </div>
                            <div class="area-tooltip">${tooltipContent}</div>
                        </div>
                    `;
                });

                container.html(html);
                count.text(`${pager.programAreas.length}个`);
            }

            // addProgramArea 函数
            function addProgramArea() {
                if (!pager.programAreas) {
                    pager.programAreas = [];
                }

                // 获取选中区域的单元格数量
                const totalCells = selectedDisplayArea.width * selectedDisplayArea.height;
                const maxAreas = Math.min(14, totalCells);

                // 检查是否达到最大区域数
                if (pager.programAreas.length >= maxAreas) {
                    layer.msg(`已达到最大节目区域数量（${maxAreas}个）`, {
                        icon: 2,
                        time: 2000
                    });
                    return false;
                }

                // 显示加载动画
                const loadIndex = layer.msg('添加中', {
                    icon: 16,
                    shade: 0.01
                });

                // 调用后台接口添加节目区域
                $.ajax({
                    url: '/DisplayTemplate/AddOneProgramArea',
                    type: 'POST',
                    data: { displayTemplateId: pager.displayTemplate.DisplayTemplate_Id },
                    success: function (res) {
                        if (res.success) {
                            // 添加新区域到数组
                            pager.programAreas.push(res.data);
                            updateProgramAreasList();

                            // 移除其他项的选中状态
                            $('.program-area-item').removeClass('active');

                            // 自动选中新添加的区域
                            const newIndex = pager.programAreas.length - 1;
                            setTimeout(() => {
                                const newItem = $(`.program-area-item[data-index="${newIndex}"]`);
                                newItem.addClass('active');

                                // 确保新增的项在可视区域内
                                const container = $('.areas-container');
                                const itemPosition = newItem.position().top;
                                const containerHeight = container.height();
                                const scrollPosition = itemPosition - (containerHeight / 2) + (newItem.height() / 2);

                                container.animate({
                                    scrollTop: container.scrollTop() + scrollPosition
                                }, 300);

                                // 启用删除按钮
                                $('.tool-button[data-tool="delete"]').removeClass('disabled');

                                enableAreaSelection();
                            }, 100);

                            layer.msg('添加成功', { time: 1500 });
                        } else {
                            layer.msg(res.msg || '添加失败', { icon: 2, time: 2000 });
                        }
                    },
                    error: function () {
                        layer.msg('添加失败，请稍后重试', { icon: 2, time: 2000 });
                    },
                    complete: function () {
                        layer.close(loadIndex);
                    }
                });

                return true;
            }

            // deleteSelectedProgramArea 函数
            function deleteSelectedProgramArea() {
                const activeItem = $('.program-area-item.active');
                if (activeItem.length) {
                    const index = activeItem.data('index');
                    const programArea = pager.programAreas[index];

                    // 调用后台删除接口
                    // 显示删除中的加载动画
                    const loadIndex = layer.msg('删除中', {
                        icon: 16,
                        shade: 0.01
                    });

                    $.ajax({
                        url: '/DisplayTemplate/DeleteProgramArea',
                        type: 'POST',
                        data: { programAreaId: programArea.ProgramArea_Id },
                        success: function (res) {
                            if (res.success) {
                                // 删除对应的已分配区域显示
                                $(`.assigned-area[data-index="${index}"]`).remove();

                                // 从数组中删除该区域
                                pager.programAreas.splice(index, 1);

                                // 更新列表显示
                                updateProgramAreasList();

                                // 隐藏选择区域（如果正在显示）
                                $('#selectionArea').hide();

                                // 如果还有其他区，选中第一个
                                if (pager.programAreas.length > 0) {
                                    setTimeout(() => {
                                        $('.program-area-item:first').click();
                                        // 重新显示所有已分配区域（会更新索引）
                                        showAssignedAreas();
                                    }, 100);
                                } else {
                                    // 如果没有区域了，禁用删除按钮
                                    $('.tool-button[data-tool="delete"]').addClass('disabled');
                                    // 重置鼠标样式
                                    resetMouseCursor();
                                }

                                // 显示提示信息
                                layer.msg('已删除选中区域', { time: 1500 });
                            } else {
                                layer.msg(res.msg || '删除失败', { time: 1500 });
                            }
                        },
                        error: function () {
                            layer.msg('删除失败，请稍后重试', { time: 1500 });
                        },
                        complete: function () {
                            layer.close(loadIndex);
                        }
                    });
                } else {
                    layer.msg('请先选择要删除的区域', { time: 1000 });
                }
            }

            // handleNextStep 函数，移除添加按钮状态相关代码
            function handleNextStep(init) {
                let loadIndex;
                if (currentStep === 1) {
                    if (!$('#selectionArea').is(':visible')) {
                        layer.msg('请使用鼠标左键点击选择屏显区域后，再点击继续', {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }

                    // 更新模板数据
                    pager.displayTemplate.DisplayTemplate_X = Math.min(startGrid.x, currentGrid.x);
                    pager.displayTemplate.DisplayTemplate_Y = Math.min(startGrid.y, currentGrid.y);
                    pager.displayTemplate.DisplayTemplate_Width = Math.abs(currentGrid.x - startGrid.x) + 1;
                    pager.displayTemplate.DisplayTemplate_Height = Math.abs(currentGrid.y - startGrid.y) + 1;

                    if (init) {
                        // 如果是初始化,直接进入第二步
                        currentStep = 2;
                        updateSteps();

                        // 隐藏选择区域,显示屏显区域
                        $('#selectionArea').hide().css({
                            'borderColor': '#1890ff',  // 保存原始的选择器样式
                            'backgroundColor': 'rgba(24, 144, 255, 0.1)'
                        });
                        showDisplayArea();

                        // 显示已分配节目区域面板
                        $('.program-areas-list').fadeIn(200);

                        if (!pager.programAreas || pager.programAreas.length === 0) {
                            addProgramArea();
                        } else {
                            updateProgramAreasList();
                            //默认选中第一个节目区域
                            $('.program-area-item:first').click();
                            showAssignedAreas(); // 显示已分配的区域
                            enableAreaSelection();
                        }
                        return;
                    }

                    // 保存屏显模板信息
                    loadIndex = layer.msg('保存中', {
                        icon: 16,
                        shade: 0.01
                    });
                    $.ajax({
                        url: '/DisplayTemplate/SaveDisplayTemplate',
                        type: 'POST',
                        data: { jsonModel: JSON.stringify(pager.displayTemplate) },
                        success: function (res) {
                            if (res.success) {
                                pager.displayTemplate = res.data.displayTemplate;
                                pager.programAreas = res.data.programAreas;
                                //保存成功后，更新当前屏显区域
                                selectedDisplayArea = {
                                    x: pager.displayTemplate.DisplayTemplate_X,
                                    y: pager.displayTemplate.DisplayTemplate_Y,
                                    width: pager.displayTemplate.DisplayTemplate_Width,
                                    height: pager.displayTemplate.DisplayTemplate_Height
                                };

                                currentStep = 2;
                                updateSteps();

                                // 隐藏选择区域,显示屏显区域
                                $('#selectionArea').hide().css({
                                    'borderColor': '#1890ff',  // 保存原始的选择器样式
                                    'backgroundColor': 'rgba(24, 144, 255, 0.1)'
                                });
                                showDisplayArea();

                                // 显示已分配节目区域面板
                                $('.program-areas-list').fadeIn(200);

                                if (!pager.programAreas || pager.programAreas.length === 0) {
                                    addProgramArea();
                                } else {
                                    updateProgramAreasList();
                                    //默认选中第一个节目区域
                                    $('.program-area-item:first').click();
                                    showAssignedAreas(); // 显示已分配的区域
                                    enableAreaSelection();
                                }
                            } else {
                                layer.msg(res.msg || '保存失败', {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function () {
                            layer.msg('保存失败，请稍后重试', {
                                icon: 2,
                                time: 2000
                            });
                        },
                        complete: function () {
                            layer.close(loadIndex);
                        }
                    });
                }
                else if (currentStep === 2) {
                    // 检查是否有未分配的节目区域
                    const hasUnassignedAreas = pager.programAreas.some(area => area.ProgramArea_UseStatus === 0);
                    if (hasUnassignedAreas) {
                        layer.msg('请先完成所有节目区域的分配', {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }

                    // 如果没有分配任何节目区域
                    if (pager.programAreas.length === 0) {
                        layer.msg('请至少分配一个节目区域', {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }

                    if (init) {
                        // 进入第三步
                        currentStep = 3;
                        updateSteps();

                        // 禁用区域选择
                        disableAreaSelection();

                        // 显示已分配区域，并使其可点击
                        showAssignedAreas();
                        makeAreasClickable();

                        // 隐藏节目区域列表面板
                        $('.program-areas-list').fadeOut(200);
                        return;
                    }

                    // 显示加载层
                    loadIndex = layer.msg('保存中', {
                        icon: 16,
                        shade: 0.01,
                        time: 0
                    });

                    // 调用保存接口
                    $.ajax({
                        url: '/DisplayTemplate/SaveProgramArea',
                        type: 'POST',
                        data: { jsonModel: JSON.stringify(pager.programAreas) },
                        success: function (res) {
                            if (res.success) {
                                // 进入第三步
                                currentStep = 3;
                                updateSteps();

                                // 禁用区域选择
                                disableAreaSelection();

                                // 显示已分配区域，并使其可点击
                                showAssignedAreas();
                                makeAreasClickable();

                                // 隐藏节目区域列表面板
                                $('.program-areas-list').fadeOut(200);
                            } else {
                                layer.msg(res.msg || '保存失败', {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function () {
                            layer.msg('保存失败，请稍后重试', {
                                icon: 2,
                                time: 2000
                            });
                        },
                        complete: function () {
                            layer.close(loadIndex);
                        }
                    });
                }
                else if (currentStep === 3) {
                    // 检查是否所有节目区域都已配置功能
                    const hasUnassignedPrograms = pager.programAreas.some(area => {
                        // 检查该区域是否有对应的功能配置
                        const hasAction = pager.programAreaActions.some(action =>
                            action.ProgramAreaAction_ProgramAreaId === area.ProgramArea_Id
                        );
                        return !hasAction;
                    });

                    if (hasUnassignedPrograms) {
                        // 找出未配置的节目区域名称
                        const unassignedAreas = pager.programAreas.filter(area => {
                            const hasAction = pager.programAreaActions.some(action =>
                                action.ProgramAreaAction_ProgramAreaId === area.ProgramArea_Id
                            );
                            return !hasAction;
                        });

                        // 构建新的提示界面
                        const reminderHtml = `
                            <div class="program-reminder">
                                <div class="reminder-header">
                                    <i class="layui-icon layui-icon-tips"></i>
                                    <span>待配置节目区域</span>
                                </div>
                                <div class="reminder-body">
                                    <div class="reminder-desc">
                                        以下节目区域尚未配置功能：
                                    </div>
                                    <div class="area-list">
                                        ${unassignedAreas.map(area => `
                                            <div class="area-item" style="
                                                background: #f8f9fa;
                                                border-radius: 4px;
                                                padding: 10px 12px;
                                                border-left: 3px solid ${area.ProgramArea_Color};
                                                transition: all 0.3s ease;
                                                margin-bottom: 8px;
                                            ">
                                                <div class="area-info" style="
                                                    display: flex;
                                                    align-items: center;
                                                    gap: 8px;
                                                    margin-bottom: 6px;
                                                ">
                                                    <span class="area-dot" style="
                                                        width: 8px;
                                                        height: 8px;
                                                        border-radius: 50%;
                                                        background: ${area.ProgramArea_Color};
                                                        flex-shrink: 0;
                                                    "></span>
                                                    <span class="area-name" style="
                                                        color: #333;
                                                        font-weight: 500;
                                                        font-size: 13px;
                                                    ">${area.ProgramArea_Name}</span>
                                                </div>
                                                <div class="area-size" style="
                                                    display: flex;
                                                    gap: 12px;
                                                    color: #666;
                                                    font-size: 12px;
                                                ">
                                                    <span style="display: flex; align-items: center;">
                                                        <i class="layui-icon layui-icon-screen-full" style="margin-right: 4px; font-size: 12px;"></i>
                                                        ${area.ProgramArea_Width} x ${area.ProgramArea_Height}
                                                    </span>
                                                    <span style="display: flex; align-items: center;">
                                                        <i class="layui-icon layui-icon-location" style="margin-right: 4px; font-size: 12px;"></i>
                                                        (${area.ProgramArea_X}, ${area.ProgramArea_Y})
                                                    </span>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                                <div class="reminder-footer">
                                    <i class="layui-icon layui-icon-about"></i>
                                    <span>点击画布中对应的区域进行配置</span>
                                </div>
                            </div>
                            <style>
                                .program-reminder {
                                    width: 380px;
                                    background: white;
                                    border-radius: 8px;
                                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                    overflow: hidden;
                                }

                                .reminder-header {
                                    background: #ff4d4f;
                                    color: white;
                                    padding: 12px 16px;
                                    font-size: 14px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                }

                                .reminder-header i {
                                    font-size: 16px;
                                }

                                .reminder-body {
                                    padding: 16px;
                                }

                                .reminder-desc {
                                    color: #666;
                                    font-size: 13px;
                                    margin-bottom: 12px;
                                }

                                .area-list {
                                    display: flex;
                                    flex-direction: column;
                                    max-height: 240px;
                                    overflow-y: auto;
                                    overflow-x: hidden;
                                    padding-right: 2px;
                                }

                                .area-list .area-item {
                                    position: relative;
                                    transform: translateX(0);
                                }

                                .area-list .area-item:hover {
                                    background: #f0f2f5;
                                    transform: translateX(4px);
                                }

                                .area-list .area-item:last-child {
                                    margin-bottom: 0;
                                }

                                .reminder-footer {
                                    background: #fffbe6;
                                    padding: 12px 16px;
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    color: #faad14;
                                    font-size: 12px;
                                }

                                .reminder-footer i {
                                    font-size: 14px;
                                }

                                /* 自定义滚动条样式 */
                                .area-list::-webkit-scrollbar {
                                    width: 4px;
                                }

                                .area-list::-webkit-scrollbar-track {
                                    background: #f5f5f5;
                                }

                                .area-list::-webkit-scrollbar-thumb {
                                    background: #ddd;
                                    border-radius: 2px;
                                }

                                .area-list::-webkit-scrollbar-thumb:hover {
                                    background: #ccc;
                                }
                            </style>
                        `;

                        layer.open({
                            type: 1,
                            title: false,
                            closeBtn: 1,
                            shade: 0.3,
                            shadeClose: true,
                            anim: 2,
                            content: reminderHtml,
                            area: ['380px', 'auto'],
                            offset: 'auto',
                            skin: 'layui-layer-custom'
                        });
                        return;
                    }

                    // 如果所有区域都已配置，进入第四步
                    currentStep = 4;
                    updateSteps();

                    // 显示保存配置的界面
                    showSaveTemplateDialog();
                }
                else if (currentStep === 4) {
                    //模板名称不能为空
                    if (pager.displayTemplate.DisplayTemplate_Name === '') {
                        layer.msg('模板名称不能为空', {
                            icon: 2,
                            time: 2000
                        });
                        return;
                    }

                    // 显示加载层
                    const loadIndex = layer.msg('正在保存...', {
                        icon: 16,
                        shade: 0.01,
                        time: 0
                    });

                    // 调用保存接口
                    $.ajax({
                        url: '/DisplayTemplate/SaveTemplate',
                        type: 'POST',
                        data: {
                            displayTemplate: JSON.stringify(pager.displayTemplate),
                            programAreas: JSON.stringify(pager.programAreas),
                            programAreaActions: JSON.stringify(pager.programAreaActions)
                        },
                        success: function (res) {
                            if (res.success) {
                                layer.close(loadIndex);
                                layer.msg('保存成功', {
                                    icon: 1,
                                    time: 1500
                                });
                                // 关闭当前弹窗并跳转到列表页
                                var index = parent.layer.getFrameIndex(window.name);
                                parent.layer.close(index);
                            } else {
                                layer.close(loadIndex);
                                layer.msg(res.msg || '保存失败', {
                                    icon: 2,
                                    time: 2000
                                });
                            }
                        },
                        error: function () {
                            layer.close(loadIndex);
                            layer.msg('保存失败，请稍后重试', {
                                icon: 2,
                                time: 2000
                            });
                        }
                    });

                }
            }


            //更新鼠标样式的函数
            function updateMouseCursor(color) {
                if (currentStep !== 2) return;
                const cursorSvg = CIRCLE_SVG_TEMPLATE.replace(/\{\{color}}/g, color);
                const svgBase64 = btoa(cursorSvg);

                // 应用自定义光标到绘图区域，调整偏移量使光标居中
                $('#drawingArea').css({
                    cursor: `url(data:image/svg+xml;base64,${svgBase64}) 8 8, auto`
                });
            }

            //重置鼠标样式的函数
            function resetMouseCursor() {
                if (currentStep !== 2) return;

                $('#drawingArea').css({
                    cursor: 'default'
                });
            }

            // 添加新的函数用于更新节目区域选择
            function updateProgramSelectionArea(activeAreaIndex) {
                // 确保当前网格位置不超出显示区域范围
                currentGrid = {
                    x: Math.max(selectedDisplayArea.x, Math.min(selectedDisplayArea.x + selectedDisplayArea.width - 1, currentGrid.x)),
                    y: Math.max(selectedDisplayArea.y, Math.min(selectedDisplayArea.y + selectedDisplayArea.height - 1, currentGrid.y))
                };

                // 确保起始网格位置也在显示区域范围内
                startGrid = {
                    x: Math.max(selectedDisplayArea.x, Math.min(selectedDisplayArea.x + selectedDisplayArea.width - 1, startGrid.x)),
                    y: Math.max(selectedDisplayArea.y, Math.min(selectedDisplayArea.y + selectedDisplayArea.height - 1, startGrid.y))
                };

                const selectedX = Math.min(startGrid.x, currentGrid.x);
                const selectedY = Math.min(startGrid.y, currentGrid.y);
                const selectedWidth = Math.abs(currentGrid.x - startGrid.x) + 1;
                const selectedHeight = Math.abs(currentGrid.y - startGrid.y) + 1;

                // 检查是否与其他已分配区域重叠
                let hasCollision = false;
                pager.programAreas.forEach((area, index) => {
                    if (index !== activeAreaIndex && area.ProgramArea_UseStatus === 1) {
                        if (!(selectedX >= area.ProgramArea_X + area.ProgramArea_Width ||
                            selectedX + selectedWidth <= area.ProgramArea_X ||
                            selectedY >= area.ProgramArea_Y + area.ProgramArea_Height ||
                            selectedY + selectedHeight <= area.ProgramArea_Y)) {
                            hasCollision = true;
                        }
                    }
                });

                if (hasCollision) {
                    // 如果检测到重叠，立即隐藏选择区域
                    selectionArea.hide();
                    // 显示之前已分配的区域（如果有）
                    const currentArea = pager.programAreas[activeAreaIndex];
                    if (currentArea.ProgramArea_UseStatus === 1) {
                        $(`.assigned-area[data-index="${activeAreaIndex}"]`).show();
                    }
                    return false;
                }

                const left = selectedX * GRID.WIDTH;
                const top = selectedY * GRID.HEIGHT;
                const width = selectedWidth * GRID.WIDTH;
                const height = selectedHeight * GRID.HEIGHT;

                const containerRect = $('.canvas-container')[0].getBoundingClientRect();
                const relativeLeft = left - containerRect.left;
                const relativeTop = top - containerRect.top;

                const areaColor = pager.programAreas[activeAreaIndex].ProgramArea_Color;

                // 在显示选择区域时，隐藏对应位置的已分配区域
                $(`.assigned-area[data-index="${activeAreaIndex}"]`).hide();

                selectionArea.css({
                    left: relativeLeft + 'px',
                    top: relativeTop + 'px',
                    width: width + 'px',
                    height: height + 'px',
                    display: 'block',
                    borderColor: 'transparent',  // 移除边框
                    backgroundColor: hasCollision ? 'rgba(255, 77, 79, 0.2)' : `${areaColor}35`,
                    zIndex: 1001  // 确保选择区域在已分配区域之上，但在屏显区域之下
                });

                // 在选择过程中隐藏信息示
                $('.selection-info').hide();

                return !hasCollision;
            }

            // showDisplayArea 函数
            function showDisplayArea() {
                if (!selectedDisplayArea) return;

                const left = selectedDisplayArea.x * GRID.WIDTH;
                const top = selectedDisplayArea.y * GRID.HEIGHT;
                const width = selectedDisplayArea.width * GRID.WIDTH;
                const height = selectedDisplayArea.height * GRID.HEIGHT;

                const containerRect = $('.canvas-container')[0].getBoundingClientRect();
                const relativeLeft = left - containerRect.left;
                const relativeTop = top - containerRect.top;

                // 创建或更新屏显区域元素
                let displayAreaEl = $('#displayAreaOverlay');
                if (displayAreaEl.length === 0) {
                    displayAreaEl = $('<div id="displayAreaOverlay"></div>').appendTo('.canvas-container');
                    displayAreaEl.css({
                        'position': 'absolute',
                        'border': '2px solid #1890ff',
                        'background-color': 'rgba(24, 144, 255, 0.05)',
                        'pointer-events': 'none',
                        'z-index': 1002,
                        'box-sizing': 'border-box'  // 改为 border-box
                    });
                }

                displayAreaEl.css({
                    left: relativeLeft + 'px',
                    top: relativeTop + 'px',
                    width: width + 'px',
                    height: height + 'px',
                    display: 'block'
                });
            }

            // 添加显示所有已分配区域的函数
            function showAssignedAreas(highlightIndex) {
                // 清除所有已存在的已分配区域显示
                $('.assigned-area').remove();

                // 隐藏选择区域
                $('#selectionArea').hide();

                // 遍历所有已分配的节目区域并显示
                pager.programAreas.forEach((area, index) => {
                    if (area.ProgramArea_UseStatus === 1) { // 只显示已分配的区域
                        const areaElement = $(`<div class="assigned-area" data-index="${index}"></div>`);

                        const containerRect = $('.canvas-container')[0].getBoundingClientRect();
                        const relativeLeft = (area.ProgramArea_X * GRID.WIDTH) - containerRect.left;
                        const relativeTop = (area.ProgramArea_Y * GRID.HEIGHT) - containerRect.top;

                        //获取当前选中区域
                        const activeIndex = highlightIndex !== undefined ? highlightIndex : $('.program-area-item.active').data('index');
                        //当前是否选中
                        const isActive = currentStep == 2 && (activeIndex === index);

                        areaElement.css({
                            'position': 'absolute',
                            'left': relativeLeft + 'px',
                            'top': relativeTop + 'px',
                            'width': (area.ProgramArea_Width * GRID.WIDTH) + 'px',
                            'height': (area.ProgramArea_Height * GRID.HEIGHT) + 'px',
                            'backgroundColor': isActive ? `${area.ProgramArea_Color}45` : `${area.ProgramArea_Color}33`,
                            'pointerEvents': 'none',
                            'zIndex': 997,
                            'transition': 'all 0.3s ease-in-out', // 添加所有属性的过渡动画
                        });

                        // 添加区域标识
                        const label = $(`<div class="area-label">${isActive ? '*' : ''}${area.ProgramArea_Name}</div>`);
                        label.css({
                            'position': 'absolute',
                            'left': '50%',
                            'top': '50%',
                            'transform': 'translate(-50%, -50%)',
                            'color': area.ProgramArea_Color,
                            'fontSize': '12px',
                            'textShadow': '0 0 2px white',
                            'whiteSpace': 'nowrap',
                            'zIndex': 999,
                            'textDecoration': isActive ? 'underline' : 'none',
                            'transition': 'all 0.3s ease-in-out', // 添加标签的过渡动画
                        });

                        areaElement.append(label);
                        $('.canvas-container').append(areaElement);
                    }
                });
            }

            // 添加清理节目区域的函数
            function cleanupProgramAreas() {
                // 显示加载界面
                var loadIndex = layer.msg('清理中', {
                    icon: 16,
                    shade: 0.01
                });

                // 调用后台接口清空节目区域
                $.ajax({
                    url: '/DisplayTemplate/ClearProgramAreas',
                    type: 'POST',
                    data: { displayTemplateId: pager.displayTemplate.DisplayTemplate_Id },
                    success: function (res) {
                        if (res.success) {
                            pager.programAreas = []; // 空节目区域
                            // 删除所有已分配区域的显示
                            $('.assigned-area').remove();

                            // 删除屏显区域覆盖层
                            $('#displayAreaOverlay').remove();

                            // 清除选择区域的节目区域样式
                            $('#selectionArea').css({
                                'borderColor': '',
                                'backgroundColor': ''
                            }).hide();

                            // 重置鼠标样式
                            $('#drawingArea').css('cursor', 'crosshair');
                            restoreDisplayAreaSelection();
                        } else {
                            layer.msg(res.msg || '清理失败', { time: 1500 });
                        }
                    },
                    error: function () {
                        layer.msg('清理失败，请稍后重试', { time: 1500 });
                    },
                    complete: function () {
                        layer.close(loadIndex);
                    }
                });
            }

            // 添加恢复屏显区选择的函数
            function restoreDisplayAreaSelection() {
                if (selectedDisplayArea) {
                    startGrid = {
                        x: selectedDisplayArea.x,
                        y: selectedDisplayArea.y
                    };
                    currentGrid = {
                        x: selectedDisplayArea.x + selectedDisplayArea.width - 1,
                        y: selectedDisplayArea.y + selectedDisplayArea.height - 1
                    };

                    // 恢复选择区域的原始样式
                    $('#selectionArea').css({
                        'borderColor': '#1890ff',
                        'backgroundColor': 'rgba(24, 144, 255, 0.1)',
                        'display': 'block',
                        'left': (selectedDisplayArea.x * GRID.WIDTH) + 'px',
                        'top': (selectedDisplayArea.y * GRID.HEIGHT) + 'px',
                        'width': (selectedDisplayArea.width * GRID.WIDTH) + 'px',
                        'height': (selectedDisplayArea.height * GRID.HEIGHT) + 'px'
                    });

                    // 更新选择区域信息显示
                    $('.selection-info').css({
                        'color': '#1e9fff'
                    }).text(
                        `(${selectedDisplayArea.width}x${selectedDisplayArea.height})${selectedDisplayArea.width * selectedDisplayArea.height}个模组`
                    ).show();
                }
                enableAreaSelection();
            }

            // 添加初始化向导提示的函数
            function initializeWizardTooltips() {
                // 移除可能存在的旧提示
                $('.step-tooltip').remove();

                // 为每个步骤添加提示元素
                $('.step').each(function () {
                    const tooltipText = $(this).data('title');
                    $(this).append(`<div class="step-tooltip">${tooltipText}</div>`);
                });
            }

            // 添加使区域可点击的函数
            function makeAreasClickable() {
                // 移除现有的已分配区域
                $('.assigned-area').remove();

                // 重新创建可点击的区域
                pager.programAreas.forEach((area, index) => {
                    if (area.ProgramArea_UseStatus === 1) {
                        const areaElement = $(`<div class="assigned-area" data-index="${index}"></div>`);

                        const containerRect = $('.canvas-container')[0].getBoundingClientRect();
                        const relativeLeft = (area.ProgramArea_X * GRID.WIDTH) - containerRect.left;
                        const relativeTop = (area.ProgramArea_Y * GRID.HEIGHT) - containerRect.top;

                        areaElement.css({
                            'position': 'absolute',
                            'left': relativeLeft + 'px',
                            'top': relativeTop + 'px',
                            'width': (area.ProgramArea_Width * GRID.WIDTH) + 'px',
                            'height': (area.ProgramArea_Height * GRID.HEIGHT) + 'px',
                            'backgroundColor': `${area.ProgramArea_Color}33`,
                            'cursor': 'pointer',
                            'zIndex': 997,
                            'transition': 'all 0.2s ease',
                            'pointerEvents': 'auto' // 启用点击事件
                        });

                        // 添加悬停效果
                        areaElement.hover(
                            function () {
                                $(this).css({
                                    'backgroundColor': `${area.ProgramArea_Color}66`,
                                    'transform': 'scale(1.01)'
                                });
                            },
                            function () {
                                $(this).css({
                                    'backgroundColor': `${area.ProgramArea_Color}33`,
                                    'transform': 'scale(1)'
                                });
                            }
                        );

                        // 添加区域标识
                        const label = $(`<div class="area-label">${area.ProgramArea_Name}</div>`);
                        label.css({
                            'position': 'absolute',
                            'left': '50%',
                            'top': '50%',
                            'transform': 'translate(-50%, -50%)',
                            'color': area.ProgramArea_Color,
                            'fontSize': '12px',
                            'textShadow': '0 0 2px white',
                            'whiteSpace': 'nowrap',
                            'zIndex': 999,
                            'pointerEvents': 'none' // 防止标签影响点击
                        });

                        areaElement.append(label);

                        // 添加点击事件
                        areaElement.on('click', function () {
                             // 立即关闭所有提示层
                            layer.closeAll('tips');
                            tipIndex = null;
                            showProgramFunctionDialog(area);
                        });

                        $('.canvas-container').append(areaElement);

                        // 检查否已配置节目功能
                        const hasProgram = pager.programAreaActions.some(action =>
                            action.ProgramAreaAction_ProgramAreaId === area.ProgramArea_Id
                        );

                        if (hasProgram) {
                            updateProgramAreaStyle(area.ProgramArea_Id);
                        }
                    }
                });
            }

            // 修改 updateProgramAreaStyle 函数中的工具提示相关代码
            window.updateProgramAreaStyle = function (programAreaId) {
                // 找到对应的节目区域
                const area = pager.programAreas.find(a => a.ProgramArea_Id === programAreaId);
                if (!area) return;

                // 找到对应的节目区域动作配置
                const areaAction = pager.programAreaActions.find(a =>
                    a.ProgramAreaAction_ProgramAreaId === programAreaId
                );

                // 获取区域元素
                const areaElement = $(`.assigned-area[data-index="${pager.programAreas.indexOf(area)}"]`);
                if (!areaElement.length) return;

                if (areaAction) {
                    // 添加已配置的视觉效果
                    areaElement.addClass('has-program');

                    // 更新区域的样式
                    areaElement.css({
                        'backgroundColor': `${area.ProgramArea_Color}33`,
                        'background': `linear-gradient(135deg, 
                            ${area.ProgramArea_Color}44 0%, 
                            ${area.ProgramArea_Color}33 50%, 
                            ${area.ProgramArea_Color}22 100%
                        )`,
                        'boxShadow': 'none'
                    });

                    // 更新标签样式
                    const label = areaElement.find('.area-label');
                    label.css({
                        'position': 'absolute',
                        'left': '50%',
                        'top': '50%',
                        'transform': 'translate(-50%, -50%)',
                        'color': area.ProgramArea_Color,
                        'fontSize': '13px',
                        'fontWeight': '500',
                        'textShadow': '0 0 3px rgba(255, 255, 255, 0.8)',
                        'whiteSpace': 'nowrap',
                        'zIndex': 999,
                        'display': 'flex',
                        'alignItems': 'center',
                        'gap': '4px'
                    });
                    // 添加配置指示器 - 改为内联图标
                    if (!label.find('.layui-icon-ok').length) {
                        label.prepend(`<i class="layui-icon layui-icon-ok" style="
                       font-size: 12px;
                            color: ${area.ProgramArea_Color};
                            margin-right: 2px;
                        "></i>`);
                    }

                    // 更新悬停效果
                    areaElement.hover(
                        function () {
                            $(this).css({
                                'background': `linear-gradient(135deg, 
                                    ${area.ProgramArea_Color}66 0%, 
                                    ${area.ProgramArea_Color}55 50%, 
                                    ${area.ProgramArea_Color}44 100%
                                )`,
                                'transform': 'scale(1.01)'
                            });
                            $(this).find('.area-label').css({
                                'transform': 'translate(-50%, -50%) scale(1.05)'
                            });
                        },
                        function () {
                            $(this).css({
                                'background': `linear-gradient(135deg, 
                                    ${area.ProgramArea_Color}44 0%, 
                                    ${area.ProgramArea_Color}33 50%, 
                                    ${area.ProgramArea_Color}22 100%
                                )`,
                                'transform': 'scale(1)'
                            });
                            $(this).find('.area-label').css({
                                'transform': 'translate(-50%, -50%) scale(1)'
                            });
                        }
                    );

                    // 解析预设内容
                    let idleContent = [];
                    let busyContent = [];
                    try {
                        if (areaAction.ProgramAreaAction_IdleContent) {
                            idleContent = JSON.parse(areaAction.ProgramAreaAction_IdleContent);
                        }
                        if (areaAction.ProgramAreaAction_NonIdleContent) {
                            busyContent = JSON.parse(areaAction.ProgramAreaAction_NonIdleContent);
                        }
                    } catch (e) {
                        console.error('解析预设内容失败:', e);
                    }

                    // 生成预设内容的HTML - 显示前6个预设内容，超过则显示展开按钮
                    function generatePresetHtml(presets) {
                        if (!presets || presets.length === 0) {
                            return '<div class="empty-preset">未设置</div>';
                        }

                        let html = '';
                        const visiblePresets = presets.slice(0, 8);
                        const hiddenPresets = presets.slice(8);

                        // 显示前6个预设
                        visiblePresets.forEach(p => {
                            html += `
                                <span class="preset-tag">
                                    ${p.text.length > 12 ? p.text.substring(0, 12) + '...' : p.text}
                                </span>
                            `;
                        });

                        // 如果有更多预设，添加展开按钮
                        if (hiddenPresets.length > 0) {
                            html += `
                                <span class="preset-tag expand-tag" onclick="toggleHiddenPresets(this)">
                                    +${hiddenPresets.length}
                                </span>
                            `;
                        }

                        return html;
                    }

                    // 修改工具提示内容的样式
                    const tooltipContent = `
                        <div class="program-tooltip">
                            <div class="tooltip-header" style="color:${area.ProgramArea_Color};">
                                <i class="layui-icon layui-icon-set"></i>
                                <span>${area.ProgramArea_Name} - 节目功能配置信息</span>
                            </div>
                            <div class="tooltip-body">
                                <!-- 空闲时配置 -->
                                <div class="tooltip-section">
                                    <div class="section-title">
                                        <i class="layui-icon layui-icon-star" style="color: #52c41a;"></i>
                                        空闲时显示
                                    </div>
                                    <div class="section-content preset-tags">
                                        ${generatePresetHtml(idleContent)}
                                    </div>
                                    <div class="section-footer footer-tags">
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-fonts-code"></i>
                                            字体大小：${areaAction.ProgramAreaAction_FontSize || '默认'}px
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-theme"></i>
                                            字体颜色：${areaAction.ProgramAreaAction_FontColor === 255 ? '红色' :
                                                areaAction.ProgramAreaAction_FontColor === 65280 ? '绿色' : '黄色'}
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-chart"></i>
                                            显示特效：${getEffectName(areaAction.ProgramAreaAction_Effect)}
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-time"></i>
                                            显示时长：${areaAction.ProgramAreaAction_DisplayTime || '默认'}秒
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-release"></i>
                                            特效速度：${areaAction.ProgramAreaAction_EffectSpeed || '默认'}
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-notice"></i>
                                            车位不足变红：${areaAction.ProgramAreaAction_UseRedForNotEnoughSpace ? '是' : '否'}
                                        </span>
                                    </div>
                                </div>
                                <!-- 非空闲时配置 -->
                                <div class="tooltip-section">
                                    <div class="section-title">
                                        <i class="layui-icon layui-icon-star" style="color: #ff4d4f;"></i>
                                        非空闲时显示
                                    </div>
                                    <div class="section-content preset-tags">
                                        ${generatePresetHtml(busyContent)}
                                    </div>
                                    <div class="section-footer footer-tags">
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-fonts-code"></i>
                                            字体大小：${areaAction.ProgramAreaAction_NonIdleFontSize || '默认'}px
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-theme"></i>
                                            字体颜色：${areaAction.ProgramAreaAction_NonIdleFontColor === 255 ? '红色' :
                                                areaAction.ProgramAreaAction_NonIdleFontColor === 65280 ? '绿色' : '黄色'}
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-chart"></i>
                                            显示特效：${getEffectName(areaAction.ProgramAreaAction_NonIdleEffect)}
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-time"></i>
                                            显示时长：${areaAction.ProgramAreaAction_NonIdleDisplayTime || '默认'}秒
                                        </span>
                                        <span class="footer-tag">
                                            <i class="layui-icon layui-icon-release"></i>
                                            特效速度：${areaAction.ProgramAreaAction_NonIdleEffectSpeed || '默认'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 更新工具提示样式
                    const tooltipStyles = `
                        <style>
                            .layui-layer-tips {
                                background: none !important;
                                box-shadow: none !important;
                            }

                            .layui-layer-content {
                                background: none !important;
                            }

                            .program-tooltip {
                                background: rgba(255, 255, 255, 0.98);
                                border-radius: 8px;
                                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                                overflow: hidden;
                                min-width: 300px;
                                max-width: 450px;
                                max-height: 85vh;
                                overflow-y: auto;
                                backdrop-filter: blur(10px);
                            }

                            .tooltip-header {
                                background: #f8f9fa;
                                margin-bottom: 0;
                                padding: 10px 12px;
                                border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                                /* color: #1890ff; */ /* 颜色将通过内联样式设置 */
                                font-weight: 500;
                                font-size: 14px;
                                display: flex;
                                align-items: center;
                            }

                            .tooltip-header i {
                                margin-right: 8px;
                                font-size: 16px;
                            }

                            .tooltip-body {
                                padding: 12px;
                            }

                            .tooltip-section {
                                margin-bottom: 12px;
                                background: #fff;
                                border-radius: 6px;
                                border: 1px solid rgba(0,0,0,0.06);
                                overflow: hidden;
                            }

                            .tooltip-section:last-child {
                                margin-bottom: 0;
                            }

                            .section-title {
                                color: #333;
                                font-size: 13px;
                                font-weight: 500;
                                display: flex;
                                align-items: center;
                                gap: 6px;
                                padding: 8px 10px;
                                background: #f8f9fa;
                            }

                            .section-content {
                                padding: 10px;
                                width: 100%;
                                box-sizing: border-box;
                            }

                            .preset-tags {
                                display: flex;
                                flex-wrap: wrap;
                                gap: 6px;
                            }

                            .preset-tag {
                                background: #e6f7ff;
                                border: 1px solid #91d5ff;
                                border-radius: 4px;
                                color: #1890ff;
                                font-size: 12px;
                                padding: 2px 8px;
                                line-height: 1.2;
                                transition: all 0.3s;
                            }

                            .expand-tag {
                                cursor: pointer;
                                background: #f0f0f0;
                                border-color: #d9d9d9;
                                color: #666;
                            }

                            .expand-tag:hover {
                                background: #e6e6e6;
                            }

                            .empty-preset {
                                color: #ff4d4f;
                                font-size: 12px;
                                padding: 4px 0;
                            }

                            .section-footer {
                                padding: 8px 10px;
                                background: #f8f9fa;
                                border-top: 1px solid rgba(0, 0, 0, 0.06);
                            }

                            .footer-tags {
                                display: flex;
                                flex-wrap: wrap;
                                gap: 8px;
                            }
                            .footer-tag {
                                color: #666;
                                font-size: 12px;
                                background: #f0f0f0;
                                padding: 4px 8px;
                                border-radius: 4px;
                                display: inline-flex;
                                align-items: center;
                                gap: 4px;
                            }

                            .footer-tag i {
                                font-size: 14px;
                                color: #1890ff;
                            }

                            .layui-layer-TipsG {
                                display: none !important;
                            }
                        </style>
                    `;

                    // 添加样式到页面
                    if (!document.getElementById('tooltip-styles')) {
                        const styleElement = document.createElement('style');
                        styleElement.id = 'tooltip-styles';
                        styleElement.innerHTML = tooltipStyles;
                        document.head.appendChild(styleElement);
                    }
                    // 根据节目区域的位置判断显示位置
                    const areaRect = areaElement[0].getBoundingClientRect();
                    const windowWidth = window.innerWidth;
                    const windowHeight = window.innerHeight;

                    // 计算提示框大小(假设最大宽度600,最大高度400)
                    const tipWidth = 600;
                    const tipHeight = 400;

                    // 根据元素中心点位置和窗口边界决定提示框方向
                    let tipsPosition;
                    // 优先判断上下空间
                    let hasSpaceAbove = areaRect.top - tipHeight >= 0;
                    let hasSpaceBelow = areaRect.bottom + tipHeight <= windowHeight;
                    let hasSpaceLeft = areaRect.left - tipWidth >= 0;
                    let hasSpaceRight = areaRect.right + tipWidth <= windowWidth;

                    // 默认显示在下方
                    tipsPosition = 3;

                    // 如果下方空间不足,但上方空间足够,则显示在上方
                    if (!hasSpaceBelow && hasSpaceAbove) {
                        tipsPosition = 1;
                    }

                    // 如果上下都不够空间,则判断左右
                    if (!hasSpaceAbove && !hasSpaceBelow) {
                        // 优先显示在右侧
                        if (hasSpaceRight) {
                            tipsPosition = 2;
                        }
                        // 右侧不够则显示在左侧
                        else if (hasSpaceLeft) {
                            tipsPosition = 4;
                        }
                        // 如果四个方向都不够,则强制显示在下方
                        else {
                            tipsPosition = 3;
                        }
                    }

                    // 使用 layer 添加工具提示
                    areaElement.attr('lay-tips', tooltipContent);
 
                    areaElement.off('mouseenter mouseleave').on('mouseenter', function () {
                        const self = this;
                        clearTimeout(leaveTimer);
                        clearTimeout(enterTimer);
 
                        if (tipIndex) {
                            layer.close(tipIndex);
                            tipIndex = null;
                        }
 
                        enterTimer = setTimeout(function () {
                            tipIndex = layer.tips($(self).attr('lay-tips'), self, {
                                tips: [1, 'transparent'],
                                time: 0,
                                maxWidth: 450,
                                success: function (layero, index) {
                                    $(layero).css({ 'background': 'none', 'box-shadow': 'none' });
                                    $(layero).find('.layui-layer-content').css({ 'background': 'none', 'padding': '0' });
                                    $(layero).find('.layui-layer-TipsG').hide();
 
                                    const tipWidth = layero.outerWidth();
                                    const tipHeight = layero.outerHeight();
                                    const areaRect = self.getBoundingClientRect();
                                    const win = { width: window.innerWidth, height: window.innerHeight };
                                    const margin = 10;
                                    const offset = 8;
 
                                    let top, left;
 
                                    if (win.height - areaRect.bottom >= tipHeight + margin) {
                                        top = areaRect.bottom + offset;
                                        left = areaRect.left + (areaRect.width / 2) - (tipWidth / 2);
                                    } else if (areaRect.top >= tipHeight + margin) {
                                        top = areaRect.top - tipHeight - offset;
                                        left = areaRect.left + (areaRect.width / 2) - (tipWidth / 2);
                                    } else if (win.width - areaRect.right >= tipWidth + margin) {
                                        top = areaRect.top + (areaRect.height / 2) - (tipHeight / 2);
                                        left = areaRect.right + offset;
                                    } else if (areaRect.left >= tipWidth + margin) {
                                        top = areaRect.top + (areaRect.height / 2) - (tipHeight / 2);
                                        left = areaRect.left - tipWidth - offset;
                                    } else {
                                        top = areaRect.bottom + offset;
                                        left = areaRect.left + (areaRect.width / 2) - (tipWidth / 2);
                                    }
 
                                    if (left < margin) left = margin;
                                    if (left + tipWidth > win.width - margin) left = win.width - tipWidth - margin;
                                    if (top < margin) top = margin;
                                    if (top + tipHeight > win.height - margin) top = win.height - tipHeight - margin;
 
                                    layero.css({ top: top + 'px', left: left + 'px' });
 
                                    $(layero).off('mouseenter mouseleave').on('mouseenter', function () {
                                        clearTimeout(leaveTimer);
                                    }).on('mouseleave', function () {
                                        leaveTimer = setTimeout(function () {
                                            layer.close(tipIndex);
                                            tipIndex = null;
                                        }, 200);
                                    });
                                }
                            });
                        }, 200);
                    }).on('mouseleave', function () {
                        clearTimeout(enterTimer);
                        leaveTimer = setTimeout(function () {
                            layer.close(tipIndex);
                            tipIndex = null;
                        }, 200);
                    });
                }
            };

            // 添加获取特效名称的辅助函数 - 根据C08协议文档修正
            function getEffectName(effectId) {
                const effects = {
                    // C08协议支持的进场特效 (D17字段)
                    1: '立即显示',
                    2: '向左移动',
                    3: '向上移动',
                    4: '向右移动',
                    5: '向下移动',
                    6: '闪烁',
                    94: '向上连移',
                    95: '向下连移',
                    96: '向右连移',
                    97: '向左连移'
                };
                return effects[effectId] || '未知特效';
            }

            //显示节目功能对话框的函数
            function showProgramFunctionDialog(area) {
                // 创建自定义弹窗容器
                const dialogHtml = `
                    <div class="custom-dialog">
                        <div class="dialog-header" style="color:${area.ProgramArea_Color}">
                            <i class="layui-icon layui-icon-component"></i>
                            <span>${area.ProgramArea_Name} - 节目功能配置</span>
                            <i class="layui-icon layui-icon-close dialog-close"></i>
                        </div>
                        <div class="dialog-content">
                            <iframe src="/DisplayTemplate/AssignProgramArea?programAreaId=${area.ProgramArea_Id}" style="border:0;" ></iframe>
                        </div>
                    </div>
                    <div class="dialog-mask"></div>
                `;

                // 添加弹窗到页面
                $('body').append(dialogHtml);

                // 获取弹窗元素
                const $dialog = $('.custom-dialog');
                const $mask = $('.dialog-mask');
                const $iframe = $dialog.find('iframe');

                // 设置弹窗样式
                $dialog.css({
                    'position': 'fixed',
                    'top': '50%',
                    'left': '50%',
                    'transform': 'translate(-50%, -50%)',
                    'width': '700px',
                    'background': '#fff',
                    'border-radius': '4px',
                    'box-shadow': '0 3px 6px rgba(0,0,0,0.16)',
                    'z-index': 99999 //为更高的z-index值
                });

                // 设置遮罩层样式
                $mask.css({
                    'position': 'fixed',
                    'top': 0,
                    'left': 0,
                    'right': 0,
                    'bottom': 0,
                    'background': 'rgba(0,0,0,0.45)',
                    'z-index': 99998 //为比弹窗低1的z-index值
                });

                // 设置头部样式
                $('.dialog-header').css({
                    'padding': '0 24px',
                    'border-bottom': '1px solid #f0f0f0',
                    'display': 'flex',
                    'align-items': 'center',
                    'font-size': '14px', //标题字体大小为14px
                    'height': '38px' // 设置标题栏高度为38px
                });

                $('.dialog-header i.layui-icon-component').css('margin-right', '8px');

                $('.dialog-header i.layui-icon-close').css({
                    'margin-left': 'auto',
                    'cursor': 'pointer'
                });

                // 设置内容区域样式
                $('.dialog-content').css({
                    'height': '560px',
                    'overflow': 'hidden'
                });

                $iframe.css({
                    'width': '100%',
                    'height': '100%'
                });

                // 绑定事件处理
                $('.dialog-close').on('click', function () {
                    $dialog.remove();
                    $mask.remove();
                });
            }

            // 修改 showSaveTemplateDialog 函数中的对话框样式和尺寸
            function showSaveTemplateDialog() {
                // 隐藏工具栏
                $('.bottom-toolbar').hide();
                // 隐藏屏显区域选择器
                $('#selectionArea').hide();
                $('#displayAreaOverlay').hide();

                // 隐藏已分配的节目区域
                $('.assigned-area').hide();

                // 创建保存模板的界面元素
                const saveTemplateHtml = `
        <div class="save-template-container" style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 450px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 16px;
            z-index: 1000;
        ">
            <div style="margin-bottom: 15px;">
                <h3 style="
                    margin: 0 0 15px 0;
                    color: #333;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <i class="layui-icon layui-icon-template-1"></i>
                    屏显模板信息
                </h3>
                <div style="
                    background: #f8f9fa;
                    border-radius: 4px;
                    padding: 12px;
                ">
                    <div style="margin-bottom: 12px;">
                        <div style="
                            color: #666;
                            font-size: 13px;
                            margin-bottom: 6px;
                        ">模板名称：</div>
                        <input type="text" 
                            value="${pager.displayTemplate.DisplayTemplate_Name || ''}" 
                            placeholder="请输入模板名称"
                            maxlength="20"
                            style="
                                width: 100%;
                                border: none;
                                outline: none;
                                font-size: 13px;
                                color: #333;
                                background: white;
                                padding: 8px;
                                border-radius: 4px;
                                box-sizing: border-box;
                            "
                            onchange="pager.displayTemplate.DisplayTemplate_Name = this.value"
                        >
                    </div>
                    <div>
                        <div style="
                            color: #666;
                            font-size: 13px;
                            margin-bottom: 6px;
                        ">模板尺寸：</div>
                        <div style="
                            background: white;
                            padding: 8px;
                            border-radius: 4px;
                            color: #333;
                            font-size: 13px;
                        ">
                            ${pager.displayTemplate.DisplayTemplate_Width} x ${pager.displayTemplate.DisplayTemplate_Height}
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-bottom: 15px;">
                <h3 style="
                    margin: 0 0 15px 0;
                    color: #333;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <i class="layui-icon layui-icon-component"></i>
                    节目区域信息
                </h3>
                <div class="areas-preview" style="
                    max-height: 300px;
                    overflow-y: auto;
                    background: #f8f9fa;
                    border-radius: 4px;
                    padding: 12px;
                ">
                    ${pager.programAreas.map((area, index) => `
                        <div class="area-item" style="
                            margin-bottom: 8px;
                            padding: 10px;
                            background: white;
                            border-radius: 4px;
                            border-left: 3px solid ${area.ProgramArea_Color};
                        ">
                            <div style="
                                display: flex;
                                align-items: center;
                                gap: 8px;
                                margin-bottom: 6px;
                            ">
                                <span style="
                                    width: 8px;
                                    height: 8px;
                                    border-radius: 50%;
                                    background: ${area.ProgramArea_Color};
                                "></span>
                                <span style="
                                    color: #333;
                                    font-weight: 500;
                                    font-size: 13px;
                                ">${area.ProgramArea_Name}</span>
                            </div>
                            <div style="
                                display: flex;
                                gap: 12px;
                                color: #666;
                                font-size: 12px;
                            ">
                                <span>
                                    <i class="layui-icon layui-icon-screen-full" style="margin-right: 4px;"></i>
                                    ${area.ProgramArea_Width} x ${area.ProgramArea_Height}
                                </span>
                                <span>
                                    <i class="layui-icon layui-icon-location" style="margin-right: 4px;"></i>
                                    (${area.ProgramArea_X}, ${area.ProgramArea_Y})
                                </span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div style="
                display: flex;
                gap: 10px;
                justify-content: center;
                margin-top: 20px;
                padding-top: 15px;
                border-top: 1px solid #f0f0f0;
            ">
                <button type="button" class="tool-button" data-tool="prev"">
                    <i class="layui-icon layui-icon-left"></i>上一步
                </button>
                <div class="tool-divider"></div>
                <button type="button" class="tool-button" data-tool="next"">
                    保存模板<i class="layui-icon layui-icon-right"></i>
                </button>
            </div>
        </div>
    `;

                // 添加到页面
                $('.canvas-container').append(saveTemplateHtml);

                // 添加滚动条样式
                const scrollbarStyle = `
        <style>
            .areas-preview::-webkit-scrollbar {
                width: 4px;
            }
            .areas-preview::-webkit-scrollbar-track {
                background: #f5f5f5;
                border-radius: 2px;
            }
            .areas-preview::-webkit-scrollbar-thumb {
                background: #ddd;
                border-radius: 2px;
            }
            .areas-preview::-webkit-scrollbar-thumb:hover {
                background: #ccc;
            }
            .area-item:last-child {
                margin-bottom: 0;
            }
        </style>
    `;
                $('head').append(scrollbarStyle);
            }
        });
    </script>
    <script>
        var pager = {
            parkAreas: [],
            passways: [],
            arealinkpassway: [],
            displayTemplate: {},
            programAreas: [],
            programAreaActions: [],
        }
    </script>
</body>

</html>
