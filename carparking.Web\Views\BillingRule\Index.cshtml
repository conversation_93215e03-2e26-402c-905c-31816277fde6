﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>计费规则</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-input.timerange { width: 300px; }
        .layui-layer-btn-l { margin-left: 8.33%; }

        .input-group-btn { background-color: #337ab7 !important; border-top-right-radius: 5px !important; border-bottom-right-radius: 5px !important; }
        .btnUnit { color: #fff !important; }

        .layui-table-cell { height: auto !important; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>计费规则</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ChargeRules_Name" id="ChargeRules_Name" autocomplete="off" placeholder="规则名称" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="ChargeRules_CarCardTypeNo" name="ChargeRules_CarCardTypeNo" data-key="BillRuleTemp_CarCardType">
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="ChargeRules_CarTypeNo" name="ChargeRules_CarTypeNo" data-key="BillRuleTemp_CarType">
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="ChargeRules_ParkAreaNo" name="ChargeRules_ParkAreaNo" data-key="BillRuleTemp_ParkArea">
                                    <option value="">停车区域</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" lay-event="Add" id="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Update" id="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Delete" id="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Test" id="Test"><i class="fa fa-leaf"></i><t>测试</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Copy" id="Copy"><i class="fa fa-recycle"></i><t>复制</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplarea">
        <option value="${ParkArea_No}" data-type="${ParkArea_Type}">${ParkArea_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.ChargeRules_Status ==1){}}
        <span class="layui-badge layui-bg-blue">启用</span>
        {{# }else{}}
        <span class="layui-badge layui-bg-red">禁用</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?*******" asp-append-version="true"></script>
    <script>
        var Power = window.parent.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var comtable = null;
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd 00:00:00"));
        var type = '@ViewBag.ChargeRulesType';

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            layuiForm = layui.form;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'ChargeRules_No', title: '计费规则编码', hide: true }
                , { field: 'ChargeRules_Name', title: '规则名称', sort: true }
                , {
                    field: 'ChargeRules_CarCardTypeName', title: '车牌类型', templet: function (d) {
                        return '<div style="white-space: normal;">' + d.ChargeRules_CarCardTypeName + '</div>';
                    }
                }
                , {
                    field: 'ChargeRules_CarTypeName', title: '车牌颜色', templet: function (d) {
                        return '<div style="white-space: normal;">' + d.ChargeRules_CarTypeName + '</div>';
                    }
                }
                , {
                    field: 'ChargeRules_ParkAreaName', title: '停车区域', templet: function (d) {
                        return '<div style="white-space: normal;">' + d.ChargeRules_ParkAreaName + '</div>';
                    }
                }
            ]];

            if (type != "0") {
                cols[0].push({
                    field: 'ChargeRules_OverTime', title: '规则类型', templet: function (d) {
                        if (d.ChargeRules_Type != 0) {
                            if (d.ChargeRules_OverTime == 1) return tempBar(3, "超时计费规则");
                            else if (d.ChargeRules_OverTime == 2) return tempBar(2, "通用计费规则");
                            else return tempBar(1, "标准计费规则");
                        } else {
                            return "";
                        }
                    }
                });
                cols[0].push({
                    field: 'ChargeRules_BeginTime', title: '有效期起', width: 160, templet: function (d) {
                        if (d.ChargeRules_Type != 0) {
                            return '<span>' + d.ChargeRules_BeginTime + '</span>';
                        } else { return ""; }
                    }
                });
                cols[0].push({
                    field: 'ChargeRules_EndTime', title: '有效期止', width: 160, templet: function (d) {
                        if (d.ChargeRules_Type != 0) {
                            var s = '';
                            if (new Date(d.ChargeRules_EndTime) < nowDate) s = 'color:red;';
                            return '<span style="' + s + '">' + d.ChargeRules_EndTime + '</span>';
                        } else { return ""; }
                    }
                });
                cols[0].push({ field: 'ChargeRules_Remark', title: '规则描述' });
            } else {
                cols[0].push({ field: 'ChargeRules_Remark', title: '规则描述' });
            }

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/BillingRule/GetChargeRulesList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100, 1000]
                , done: function (data) {
                    pager.dataCount = data.count;
                    tb_page_set(data);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                localStorage.removeItem("ownerarea");
                switch (obj.event) {
                    case 'Add':
                        var vType = localStorage.getItem("versionType");
                        var action_name = "TempRule";
                        if (vType != 'simple') { action_name = "TempRule"; }
                        else { action_name = "StandardRule"; }
                        layer.open({
                            type: 2, id: 1,
                            title: "新增计费规则",
                            content: action_name + '?Act=Add',
                            area: getIframeArea(['65%', '99%']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }

                        var ChargeRules_No = data[0].ChargeRules_No;
                        var ChargeRules_Type = data[0].ChargeRules_Type;

                        var action_name = "TempRule";
                        if (ChargeRules_Type == 0) { action_name = "StandardRule"; }
                        if (ChargeRules_Type == 1) { action_name = "TempRule"; }
                        if (ChargeRules_Type == 2) { action_name = "YinChuanRule"; }

                        layer.open({
                            type: 2, id: 1,
                            title: "编辑计费规则",
                            content: action_name + '?Act=Update&ChargeRules_No=' + ChargeRules_No,
                            area: getIframeArea(['65%', '99%']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        //if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var ChargeRules_No = data[0].ChargeRules_No;
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $(this).attr("disabled", true);
                                $.post("DelChargeRules", { ChargeRules_No: ChargeRules_No }, function (json) {
                                    $(this).removeAttr("disabled");
                                    if (json.success) {
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                            pager.bindData();
                                        });
                                    } else {
                                        layer.msg(json.msg, { icon: 0 });
                                    }
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Test':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        var ChargeRules_No = data[0].ChargeRules_No;
                        layer.open({
                            type: 2, id: 1,
                            title: "测试计费规则",
                            content: 'Debug?Act=Debug&ChargeRules_No=' + ChargeRules_No,
                            area: getIframeArea(['65%', '85%']),
                            maxmin: true
                        });

                        break;
                    case 'Copy':
                        layer.open({
                            type: 2, id: 1,
                            title: "复制计费规则",
                            content: 'CopyRule?Act=Add',
                            area: getIframeArea(['65%', '80%']),
                            maxmin: true
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_radio(table)

            pager.init();
        });


        function linkTime(ChargeRules_BeginTime, ChargeRules_EndTime) {
            if (ChargeRules_BeginTime && ChargeRules_EndTime) {
                var time = new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd") + " - " + new Date(ChargeRules_EndTime).Format("yyyy-MM-dd");
                return time;
            } else
                return "";
        }
        function getStatus(ChargeRules_BeginTime, ChargeRules_EndTime) {

            if (ChargeRules_BeginTime && ChargeRules_EndTime) {

                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd 00:00:00"));
                ChargeRules_EndTime = new Date(new Date(ChargeRules_EndTime).Format("yyyy-MM-dd 00:00:00"));

                if (ChargeRules_EndTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }
    </script>
    <script>



        var pager = {
            dataCount: 0,
            ChargeRules_No: null,
            parkAreas: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            category: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                pager.bindPower();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.carCardTypes = json.data.carCardTypes;
                        pager.carTypes = json.data.carTypes;
                        pager.parkAreas = json.data.parkAreas;

                        $("#ChargeRules_CarCardTypeNo").html('<option value="">车牌类型</option>');
                        $("#ChargeRules_CarTypeNo").html('<option value="">车牌颜色</option>');
                        $("#ChargeRules_ParkAreaNo").html(' <option value="">停车区域</option>');

                        $("#ChargeRules_CarCardTypeNo").append($("#tmplcarcardtype").tmpl(pager.carCardTypes));
                        $("#ChargeRules_CarTypeNo").append($("#tmplcartype").tmpl(pager.carTypes));
                        $("#ChargeRules_ParkAreaNo").append($("#tmplarea").tmpl(pager.parkAreas));

                        layuiForm.render("select")
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/BillingRule/GetChargeRulesList'
                    , where: { conditionParam: JSON.stringify(conditionParam) }
                    , page: { curr: index }
                });

                //tmplcards
                //$("#Search").attr("disabled", true);
                //layer.msg("处理中", { icon: 16, time: 0 });
                //$.ajax({
                //    type: 'post',
                //    url: '/BillingRule/GetChargeRulesList?r=' + Math.random(),
                //    dataType: 'json',
                //    data: { conditionParam: JSON.stringify(conditionParam) },
                //    success: function (json) {
                //        if (json.code == 0) {
                //            layer.msg("查询成功", { icon: 1, time: 1000 });
                //            if (json.data == null || json.data.length == 0) {
                //                $(".layui-none").removeClass("layui-hide");
                //                $(".cards").html("");
                //            } else {
                //                $(".layui-none").addClass("layui-hide");
                //                $(".cards").html($("#tmplcards").tmpl(json.data));
                //            }

                //            pager.bindItemEvent();
                //            pager.bindPower();
                //        } else {
                //            layer.msg("查询失败：" + json.msg, { icon: 0 });
                //        }
                //    },
                //    complete: function () {
                //        $("#Search").attr("disabled", false);
                //    },
                //    error: function () {
                //        layer.msg("查询失败", { icon: 2 });
                //    }
                //});


            },
            bindEvent: function () {
                layuiForm.on("select", function (data) {
                    pager.bindData(1);
                });
                $("#Search").click(function () { pager.bindData(1); });

                if (Power.BillingRule.Add != "true") {
                    document.getElementById("Add").style.display = "none";
                }
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });
            },
            bindItemEvent: function () {

            }
        }

    </script>
</body>
</html>
