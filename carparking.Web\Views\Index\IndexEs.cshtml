@using carparking.BLL.Cache
<!DOCTYPE html>
<html>
<head>
    <script>
        var model = '@Html.Raw(ViewBag.model)';
        localStorage.setItem('sysconfig', model);
    </script>
    <meta charset="utf-8">
    <title>@ViewBag.SysConfig_DIYName</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="~/Static/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/Static/admin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="~/Static/admin/style/admin.css" media="all">
    <link href="~/Static/plugins/notification/snackbar/snackbar.min.css" rel="stylesheet" />
    
    <style>
        html { height: 100%;}
        body { height: 100%; background-image: url('../Static/img/INDEX_BACKGROUND.png'); background-size: cover; background-repeat: no-repeat; background-position: center; }
        .dashboard-container { padding: 20px; /* background: #f0f2f5; */ /* min-height: 100%;  */ }
        .module-section { margin-bottom: 30px; }
        .module-title { font-size: 18px; font-weight: bold; color: #fffefe; margin-bottom: 20px; padding-left: 10px; border-left: 4px solid #009688; }
        .menu-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; }
        .menu-item { background: #fff; border-radius: 8px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.3s; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .menu-item:hover { transform: translateY(-5px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .menu-icon { font-size: 32px; color: #009688; margin-bottom: 10px; }
        .menu-text { color: #333; font-size: 16px; }
        .top-toolbar { background: rgb(7 10 78); padding: 10px 20px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .toolbar-left { display: flex; align-items: center; color: #fff; }
        .toolbar-right { display: flex; align-items: center; gap: 20px; color: #fff; }
        .user-info { display: flex; align-items: center; gap: 10px; }
        
        /* 搜索框样式 */
        .searchBox { 
            position: absolute; 
            top: 50px; 
            width: 500px; 
            background: #fff; 
            z-index: 10000; 
            box-shadow: 0 0 6px #bbb; 
            padding: 20px; 
            font-size: 1.6rem; 
        }
        .searchBox .selectmenu { 
            display: block; 
            border: 1px solid #eee; 
            border-radius: 4px; 
            min-height: 200px; 
            max-height: 400px; 
            overflow: auto; 
        }
        .searchBox .history { 
            margin-bottom: 10px; 
            padding: 10px 0; 
        }
        .searchBox .history li { 
            float: left; 
            background-color: #f2f5f7; 
            padding: 5px 10px; 
            margin-right: 10px; 
            border-radius: 4px; 
        }
        .searchBox .selectmenu ul li { 
            padding: 10px; 
            cursor: pointer; 
        }
        .searchBox .selectmenu ul li:hover { 
            background-color: #f5f5f5; 
        }
        .searchTip { 
            margin-top: 10px; 
            color: #999; 
            font-size: 12px; 
        }
    </style>
</head>

<body class="layui-layout-body">
    <!-- 顶部工具栏 -->
    <div class="top-toolbar">
        <div class="toolbar-left">
            <h1>停车场后台管理系统</h1>
        </div>
        <div class="toolbar-right">
            <div class="user-info">
                <i class="layui-icon layui-icon-username"></i>
                <span>@Html.Raw(ViewBag.lgAdmins != null ? ViewBag.lgAdmins.Admins_Account : "")</span>
            </div>
            <div id="UpdatePark">
                <i class="layui-icon layui-icon-set"></i>
                <span>云平台配置</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-container">
        <!-- 车场配置模块 -->
        <div class="module-section">
            <h2 class="module-title">车场配置</h2>
            <div class="menu-grid">
                <div class="menu-item" lay-href="FastGuide/Index">
                    <i class="layui-icon layui-icon-set menu-icon"></i>
                    <div class="menu-text">快速配置</div>
                </div>
                <div class="menu-item" lay-href="Device/Index">
                    <i class="layui-icon layui-icon-component menu-icon"></i>
                    <div class="menu-text">设备管理</div>
                </div>
                <div class="menu-item" lay-href="SentryHost/Index">
                    <i class="layui-icon layui-icon-home menu-icon"></i>
                    <div class="menu-text">岗亭管理</div>
                </div>
                <div class="menu-item" lay-href="ParkArea/Index">
                    <i class="layui-icon layui-icon-template menu-icon"></i>
                    <div class="menu-text">区域管理</div>
                </div>
                <div class="menu-item" lay-href="Passway/Index">
                    <i class="layui-icon layui-icon-road menu-icon"></i>
                    <div class="menu-text">车道管理</div>
                </div>
            </div>
        </div>

        <!-- 车场管理模块 -->
        <div class="module-section">
            <h2 class="module-title">车场管理</h2>
            <div class="menu-grid">
                <div class="menu-item" lay-href="Owner/Index">
                    <i class="layui-icon layui-icon-user menu-icon"></i>
                    <div class="menu-text">车辆登记</div>
                </div>
                <div class="menu-item" lay-href="BillingRule/Index">
                    <i class="layui-icon layui-icon-rmb menu-icon"></i>
                    <div class="menu-text">计费规则</div>
                </div>
                <div class="menu-item" lay-href="Policy/Index">
                    <i class="layui-icon layui-icon-form menu-icon"></i>
                    <div class="menu-text">功能策略</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索框 -->
    <div class="searchBox tdselect-div" style="display: none;">
        <div class="history">
            <label style="font-size: 12px;float: left;">历史搜索：</label>
            <ul id="history">
                <li>
                    <a lay-href="FastGuide/Index">
                        <cite><t>快速配置</t></cite>
                    </a>
                </li>
            </ul>
        </div>
        <div class="selectmenu">
            <ul id="selectmenu">
                <li>
                    <a lay-href="FastGuide/Index">
                        <cite><t>快速配置</t></cite>
                    </a>
                </li>
            </ul>
        </div>
        <div class="searchTip">
            <label>提示：仅支持一级菜单项搜索</label>
        </div>
        <div style="padding:10px 0; text-align:center;">
            <button class="layui-btn layui-btn-md" style="background-color: #26817f;" id="closeparkHistory">关闭</button>
        </div>
    </div>

    <!-- 云平台状态模板 -->
    <script type="text/x-jquery-tmpl" id="park_online">
        {{if online==1 }}
        <span class="layui-badge layui-bg-blue" title="已连接云平台">云平台：已连接</span>
        {{else online==2 }}
        <span class="layui-badge layui-bg-gray" title="未启用云平台">云平台：未启用</span>
        {{else online==-1 }}
        <span class="layui-badge layui-bg-gray" title="未知">云平台：未知</span>
        {{else}}
        <span class="layui-badge layui-bg-orange" title="未连接云平台">云平台：未连接</span>
        {{/if}}
    </script>

    <!-- 脚本引用 -->
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/blockui/jquery.blockUI.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/notification/snackbar/snackbar.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>

    <script>
        layui.use(['layer'], function() {
            var layer = layui.layer;

            // 菜单项点击事件
            $('.menu-item').on('click', function() {
                var href = $(this).attr('lay-href');
                if(href) {
                   layer.open({
                        title: false,
                        type: 2, // 使用 iframe 方式加载外部 URL
                        content: '../'+href, // 替换为你的 URL
                        btnAlign: 'c',
                        shade: 0,
                        area: ['95%', '90%'],
                        success: function (layero, index) {
                            $(layero).css({
                                'background-color': '#f2f2f2',
                                'border-radius': '10px',
                                'box-shadow': '0 0 10px rgba(0, 0, 0, 0.5)',
                            });
                        }
                    });
                }
            });

            // 云平台配置点击事件
            $('#UpdatePark').on('click', function() {
                layer.open({
                    type: 2,
                    title: '云平台配置',
                    content: '/CloudPlatform/Config',
                    area: ['800px', '600px']
                });
            });

            // 关闭搜索框
            $('#closeparkHistory').on('click', function() {
                $('.searchBox').hide();
            });
        });
    </script>
</body>
</html>