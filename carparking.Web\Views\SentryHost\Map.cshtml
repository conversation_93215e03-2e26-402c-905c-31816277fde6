﻿﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择微信管理员</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/css/myApp.css?33" rel="stylesheet" />
    <style type="text/css">
        #allmap { width: 100%; height: 290px; overflow: hidden; margin: 0; font-family: "微软雅黑"; }
    </style>
    <script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=BOqLbj5By5RdVMdTdwyL88Cw"></script>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">

            <div class="form-group">
                <div class="col-sm-12">
                    <div class="input-group">
                        <input type="text" id="addName" class="layui-input" placeholder="输入查询地址，例如：深圳市龙华汽车站">
                        <span class="input-group-btn">
                            <button type="button" id="MapSearch" class="btn btn-primary" style="height: 38px;">
                                查询位置
                            </button>
                        </span>
                    </div>
                </div>
            </div>

            <div class="ibox-content text-center" id="allmap">
            </div>

            <div class="form-group" style="margin-top: 20px;">
                <div class="col-sm-4" style="color: #18a689;">
                    Y:<span id="Parking_Y"></span>&nbsp;&nbsp; X:<span id="Parking_X"></span>
                </div>
                <div class="col-sm-4">
                    <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> 确定</button>&nbsp;
                    <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery.min.js?v=2.1.4"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6"></script>
    <script src="~/Static/js/content.min.js?v=1.0.0"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5"></script>
    <script src="~/Static/js/jquery.common.js"></script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        var map = new BMap.Map("allmap");
        var pager = {
            init: function () {
                this.bindData();
                this.bindEvent();
            },
            //数据绑定
            bindData: function () {
                var point = new BMap.Point(116.331398, 39.897445); //默认坐标
                var myIcon = new BMap.Icon("../Static/img/icon_park.svg", new BMap.Size(32, 32)); //自定义图标

                function myFun(result) {
                    var cityName = result.name;
                    map.setCenter(cityName);
                }
                //添加覆盖物
                function add_overlay(marker) {
                    map.addOverlay(marker); //增加点
                }
                //清除覆盖物
                function remove_overlay() {
                    map.clearOverlays();
                }


                var x = $.getUrlParam("x");
                var y = $.getUrlParam("y");
                if (x != "undefined" && y != "undefined" && x != null && y != null && x != "" && y != "") {
                    $("#Parking_Y").html(y);
                    $("#Parking_X").html(x);
                    point = new BMap.Point(y, x);
                    map.centerAndZoom(point, 12);
                    var marker = new BMap.Marker(point, { icon: myIcon });  // 创建标注
                    add_overlay(marker);
                }
                else {
                    map.centerAndZoom(point, 12);
                    var myCity = new BMap.LocalCity(); //IP定位到当前城市
                    myCity.get(myFun);
                }
                map.enableScrollWheelZoom(true);

                var top_left_control = new BMap.ScaleControl({ anchor: BMAP_ANCHOR_TOP_LEFT });// 左上角，添加比例尺
                var top_left_navigation = new BMap.NavigationControl();  //左上角，添加默认缩放平移控件
                map.addControl(top_left_control);
                map.addControl(top_left_navigation);

                //单击获取点击的经纬度
                map.addEventListener("click", function (e) {
                    remove_overlay();//清除图标
                    point = new BMap.Point(e.point.lng, e.point.lat); //创建小图标
                    var marker = new BMap.Marker(point, { icon: myIcon });  // 创建标注
                    add_overlay(marker);

                    //逆地址解析
                    var geoc = new BMap.Geocoder();
                    geoc.getLocation(point, function (rs) {
                        var addComp = rs.addressComponents;
                        $("#addName").val(addComp.province + addComp.city + addComp.district + addComp.street + addComp.streetNumber);
                    });

                    $("#Parking_Y").html(point.lng);
                    $("#Parking_X").html(point.lat);
                });
            },
            bindEvent: function () {
                $("#Save").click(function () {
                    var py = parent.$("#SentryHost_Latitude");
                    var px = parent.$("#SentryHost_Longitude");
                    var y = $("#Parking_Y").html();
                    var x = $("#Parking_X").html();
                    if (y != py.val() || x != px.val()) {
                        parent.$("#SentryHost_Latitude").val(x).css("color", "#18a689");
                        parent.$("#SentryHost_Longitude").val(y).css("color", "#18a689");
                    }
                    parent.layer.close(index);
                });

                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#MapSearch").click(function () {
                    var addname = $("#addName").val();
                    if (addname.length <= 2) {
                        layer.msg('字符不能少于2位', { icon: 2 });
                        return;
                    }
                    // 创建地址解析器实例
                    var myGeo = new BMap.Geocoder();
                    // 将地址解析结果显示在地图上,并调整地图视野
                    myGeo.getPoint(addname, function (point) {
                        if (point) {
                            map.centerAndZoom(point, 16);
                            map.addOverlay(new BMap.Marker(point));
                        } else {
                            layer.msg('您选择地址没有解析到结果!', { icon: 5 });
                        }
                    }, "");
                });
            }
        };

        $(function () { pager.init() });
    </script>
</body>
</html>