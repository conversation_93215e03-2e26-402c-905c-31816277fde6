﻿using SqlSugar;
using System;

namespace carparking.Web.OpenApiV2.Models.Entitys
{
    /// <summary>
    /// 车辆信息表
    /// </summary>
    [SugarTable("Car")]
    public class CarEntity
    {
        /// <summary>
        /// 车辆ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Car_ID { get; set; }

        /// <summary>
        /// 车辆编码
        /// </summary>
        public string Car_No { get; set; }

        /// <summary>
        /// 车牌号码
        /// </summary>
        public string Car_CarNo { get; set; }

        /// <summary>
        /// 车身颜色
        /// </summary>
        public string Car_Colour { get; set; }

        /// <summary>
        /// 车牌颜色(如小型轿车)
        /// </summary>
        public string Car_VehicleTypeNo { get; set; }

        /// <summary>
        /// 品牌型号
        /// </summary>
        public string Car_Model { get; set; }

        /// <summary>
        /// 停车位预约号
        /// </summary>
        public string Car_OrderSpace { get; set; }

        /// <summary>
        /// 行驶证号
        /// </summary>
        public string Car_License { get; set; }

        /// <summary>
        /// 车牌类型
        /// </summary>
        public string Car_TypeNo { get; set; }

        /// <summary>
        /// 车牌状态：1-正常，2-黑名单，3-停用
        /// </summary>
        public int Car_Status { get; set; }

        /// <summary>
        /// 新增时间
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(Model.JsonConvertDateTimeOverride_DateTime))]
        public DateTime Car_AddTime { get; set; }

        /// <summary>
        /// 添加人ID
        /// </summary>
        public int Car_AddID { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(Model.JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_EditTime { get; set; }

        /// <summary>
        /// 修改人ID
        /// </summary>
        public int Car_EditID { get; set; }

        /// <summary>
        /// 车场编码
        /// </summary>
        public string Car_ParkingNo { get; set; }

        /// <summary>
        /// 账户余额
        /// </summary>
        public decimal Car_Balance { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(Model.JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_BeginTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Newtonsoft.Json.JsonConverter(typeof(Model.JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_EndTime { get; set; }

        /// <summary>
        /// 车主编码
        /// </summary>
        public string Car_OwnerNo { get; set; }

        /// <summary>
        /// 车主姓名
        /// </summary>
        public string Car_OwnerName { get; set; }

        /// <summary>
        /// 在线充值：0-禁止，1-开启
        /// </summary>
        public int Car_OnLine { get; set; }

        /// <summary>
        /// 是否多位多车，0-不是，1-是
        /// </summary>
        public int Car_IsMoreCar { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Car_Remark { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        public string Car_Category { get; set; }

        /// <summary>
        /// 相机离线自动开闸(白名单)：0-禁用，1-启用
        /// </summary>
        public int Car_EnableOffline { get; set; }

        /// <summary>
        /// 下发到相机记录状态
        /// </summary>
        public string Car_CameraBody { get; set; }

        /// <summary>
        /// 车位号
        /// </summary>
        public string Car_OwnerSpace { get; set; }

        /// <summary>
        /// 卡号
        /// </summary>
        public string Car_CardNo { get; set; }
    }
}