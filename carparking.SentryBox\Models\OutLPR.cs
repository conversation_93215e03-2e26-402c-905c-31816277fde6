﻿using System;
using System.Collections.Generic;
using System.Text;
using carparking.BLL.Cache;
using carparking.Common;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;

namespace carparking.SentryBox
{
    /// <summary>
    /// 出口识别结果[用于ws通知客户端]
    /// </summary>
    public class OutLPR
    {
        public OutLPR() { }
        public OutLPR(Model.ResultPass data)
        {
            Model.ResultPass rs = JsonConvert.DeserializeObject<Model.ResultPass>(JsonConvert.SerializeObject(data));
            string parktime = "";
            string payedamount = "";
            DateTime? enterTime = rs.resorder?.resOut?.parkorder?.ParkOrder_EnterTime.Value ?? null;
            var outTime = Common.Utils.StrToDateTime(DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss"));
            string etime = enterTime == null ? "" : enterTime.Value.ToString("yyyy-MM-dd HH:mm:ss");
            if (rs.payres != null)
            {
                payedamount = rs.payres.payedamount.ToString();
                var ts = TimeSpan.FromMinutes(rs.payres.parktimemin);
                parktime = $"{ts.Days}天{ts.Hours}小时{ts.Minutes}分钟";

                if (rs.resorder?.resOut?.onenter == 0)
                {
                    DateTime end, start;
                    if (rs.resorder.resOut.onmachorder == 1)
                    {
                        enterTime = rs.resorder?.resOut?.noRecordOrder?.ParkOrder_EnterTime.Value ?? null;
                        outTime = rs.time.Value;
                        DateTime.TryParse(outTime.ToString("yyyy-MM-dd HH:mm:ss"), out end);
                        DateTime.TryParse(enterTime.Value.ToString("yyyy-MM-dd HH:mm:ss"), out start);
                    }
                    else
                    {
                        DateTime.TryParse(outTime.ToString("yyyy-MM-dd HH:mm:ss"), out end);
                        if (end != null && enterTime == null) enterTime = end.AddMinutes(-5);
                        DateTime.TryParse(enterTime.Value.ToString("yyyy-MM-dd HH:mm:ss"), out start);
                    }

                    rs.payres.parktimemin = (end - start).TotalMinutes;
                    parktime = Common.Utils.DateDiff(enterTime.Value, outTime);
                }
            }
            else
            {
                if (enterTime != null)
                    parktime = Common.Utils.DateDiff(enterTime.Value, outTime);
            }

            this.carno = rs.passres.carno;
            this.cartype = rs.resorder?.resOut?.parkorder?.ParkOrder_CarTypeName ?? rs.passres.cartype.CarType_Name;
            this.carcardtype = rs.resorder?.resOut?.parkorder?.ParkOrder_CarCardTypeName ?? rs.passres.carcardtype.CarCardType_Name;
            this.carcardtypeno = rs.resorder?.resOut?.parkorder?.ParkOrder_CarCardType ?? rs.passres.carcardtype.CarCardType_No;
            this.passwayno = rs.passres.passway.Passway_No;
            this.passcode = rs.passres.code.ToString();
            this.passmsg = rs.errmsg;
            this.orderno = rs.passres.parkorderno;
            this.entertime = etime;
            this.enterimg = Util.LPRTools.GetSentryHostImg(rs.resorder?.resOut?.parkorder?.ParkOrder_EnterImg ?? rs.resorder?.resIn?.parkorder?.ParkOrder_EnterImg);
            if (!string.IsNullOrWhiteSpace(this.enterimg) && this.enterimg.Contains(AppBasicCache.SentryHostInfo.SentryHost_No))
            {
                if (rs.resorder?.resOut?.parkorder != null) rs.resorder.resOut.parkorder.ParkOrder_EnterImgPath = this.enterimg;
            }
            this.outtime = rs.time.Value.ToString("yyyy-MM-dd HH:mm:ss");
            this.outimg = Util.LPRTools.GetSentryHostImg(rs.passres.localimage);
            if (!string.IsNullOrWhiteSpace(this.outimg) && !string.IsNullOrWhiteSpace(rs.resorder?.resOut?.parkorder?.ParkOrder_OutImg))
            {
                rs.resorder.resOut.parkorder.ParkOrder_OutImgPath = Util.LPRTools.GetSentryHostImg(rs.resorder?.resOut?.parkorder?.ParkOrder_OutImg);
            }
            rs.passres.img = Util.LPRTools.GetSentryHostImg(rs.passres.localimage);
            this.parktime = parktime;
            this.payedamount = payedamount;
            this.expday = rs.passres.expday > 0 ? rs.passres.expday.ToString() : "0";
            this.data = rs;
            this.name = rs.passres?.owner?.Owner_Name ?? rs.passres?.car?.Car_OwnerName;
            this.remark = rs.passres?.owner?.Owner_Remark ?? rs.passres?.car?.Car_Remark;
            if (rs.passres?.owner?.Owner_CardType == 2)
            {
                decimal dMoney = 0;
                decimal deductionAmount = 0;

                decimal? dl = data.payres?.chuzhiremainingamount ?? data.passres.owner?.Owner_Balance;
                decimal dBalance = dl != null ? dl.Value : 0;
                if (data.passres.car != null && data.passres.car.Car_Category == "3657" && data.payres != null)
                {
                    dMoney = data.payres.chuzhiremainingamount ?? dBalance;
                    deductionAmount = data.payres.chuzhiamount;
                }

                this.balance = dMoney.ToString();
                this.deduction = deductionAmount.ToString();
            }

        }

        /// <summary>
        /// 识别车牌
        /// </summary>
        public string carno { get; set; }

        /// <summary>
        /// 车牌颜色
        /// </summary>
        public string cartype { get; set; }

        /// <summary>
        /// 车牌类型名称
        /// </summary>
        public string carcardtype { get; set; }
        /// <summary>
        /// 车牌类型编号
        /// </summary>
        public string carcardtypeno { get; set; }

        /// <summary>
        /// 通行车道编码
        /// </summary>
        public string passwayno { get; set; }

        /// <summary>
        /// 通行结果：0[禁止通行],1[自动放行],2[确认放行],3[排队等候],4[最低收费缴费通行]
        /// </summary>
        public string passcode { get; set; }

        /// <summary>
        /// 通行信息
        /// </summary>
        public string passmsg { get; set; }

        /// <summary>
        /// 停车订单号
        /// </summary>
        public string orderno { get; set; }

        /// <summary>
        /// 入场时间
        /// </summary>
        public string entertime { get; set; }

        /// <summary>
        /// 入场图片
        /// </summary>
        public string enterimg { get; set; }

        /// <summary>
        /// 识别时间
        /// </summary>
        public string outtime { get; set; }

        /// <summary>
        /// 出场图片
        /// </summary>
        public string outimg { get; set; }

        /// <summary>
        /// 停车时长
        /// </summary>
        public string parktime { get; set; }

        /// <summary>
        /// 待缴费用
        /// </summary>
        public string payedamount { get; set; }

        /// <summary>
        /// 月卡剩余有效期（单位：/天）
        /// </summary>
        public string expday { get; set; }

        /// <summary>
        /// 储值车余额
        /// </summary>
        public string balance { get; set; } = "";
        /// <summary>
        /// 储值车抵扣金额
        /// </summary>
        public string deduction { get; set; } = "";

        /// <summary>
        /// 通行检测结果
        /// </summary>
        public Model.ResultPass data { get; set; }

        /// <summary>
        /// 车主姓名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
    }
}
