<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<!--    <meta http-equiv="origin-trial"-->
<!--          content="申请sharedarraybuffer的key">-->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Jessica demo</title>
    <script src="~/Static/flveee3/flveee.js"></script>
    <style>
        .container-shell:before { content: "jessibuca Pro 4x4 demo player"; }

        html, body { width: 100%; height: 100%; }

        html { -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: transparent; }

        body { line-height: 1.6; position: relative; font-family: "Microsoft Yahei", tahoma, arial, "Hiragino Sans GB"; }




        .root { display: flex; place-content: center; margin-top: 3rem; }

        .container-shell { backdrop-filter: blur(5px); background: hsla(0, 0%, 50%, 0.5); padding: 30px 4px 10px 4px; /* border: 2px solid black; */ width: auto; position: relative; border-radius: 5px; box-shadow: 0 10px 20px; }

        .container-shell:before { content: "jessibuca demo player"; position: absolute; color: darkgray; top: 4px; left: 10px; text-shadow: 1px 1px black; }

        #container { background: rgba(13, 14, 27, 0.7); width: 960px; height: 597px; }

        .container { background: rgba(13, 14, 27, 0.7); width: 320px; height: 199px; display: inline-block; margin-right: 10px; margin-bottom: 10px; }

        .input { display: flex; margin-top: 10px; max-width: 960px; color: white; place-content: stretch; }

        .input2 { bottom: 0px; }

        .input input { flex: auto; }

        .err { position: absolute; top: 40px; left: 10px; color: red; }

        .option { position: absolute; top: 4px; right: 10px; display: flex; place-content: center; font-size: 12px; }

        .option span { color: white; }

        .page { background: url('./bg.jpg'); background-repeat: no-repeat; background-position: top; }

        button { font-size: 12px; padding: 4px 8px; border-radius: 4px; }

        .container-multi { text-align: center; }


        .audio-container { width: 960px; }

        @@media (max-width: 720px) {
            input { outline: 0; }

            * { margin: 0; padding: 0; }

            * { -webkit-tap-highlight-color: transparent }

            a img { border: 0; }

            a { text-decoration: none; }


            li { list-style: none; }

            ol,
            ul { margin: 0; padding: 0; list-style: none; }


            #container { width: 95vw; height: 52.7vw; margin: 0 auto; }

            .input { max-width: 95vw; }

            .audio-container { width: 95vw; }

            .container { width: 95vw; height: 52.7vw; margin: 0 auto; margin-bottom: 10px; display: block; }
        }</style>
</head>
<body class="page">
<div class="root">
    <div class="container-shell">
        <div id="container"></div>
        <div class="input">
            <div>
                当前浏览器：
                <span id="mseSupport264" style="color: green;display: none">支持MSE H264解码；</span>
                <span id="mseSupport" style="color: green;display: none">支持MSE H265解码；</span>
                <span id="mseNotSupport264" style="color: red;display: none">不支持MSE H264解码；</span>
                <span id="mseNotSupport"
                      style="color: red;display: none">不支持MSE H265解码,会自动切换成wasm解码；</span>
            </div>
        </div>
        <div class="input">
            <div>
                当前浏览器：
                <span id="wcsSupport264" style="color: green;display: none">支持Webcodecs H264解码；</span>
                <span id="wcsSupport" style="color: green;display: none">支持Webcodecs H265解码（不一定准确）；</span>
                <span id="wcsNotSupport264"
                      style="color: red;display: none">不支持Webcodecs H264解码(https/localhost)；</span>
                <span id="wcsNotSupport" style="color: red;display: none">不支持Webcodecs H265解码(https/localhost),会自动切换成wasm解码</span>
            </div>
        </div>
        <div class="input">
            <div>
                当前浏览器：
                <span id="wasmSupport" style="color: green;display: none">支持WASM解码；</span>
                <span id="simdSupport" style="color: green;display: none">支持WASM SIMD解码；</span>
                <span id="simdMtSupport" style="color: green;display: none">支持WASM WASM(SIMD) 多线程解码；</span>
                <span id="wasmNotSupport" style="color: red;display: none">不支持WASM解码；</span>
                <span id="simdNotSupport"
                      style="color: red;display: none">不支持WASM SIMD解码,会自动切换成wasm解码；</span>
                <span id="simdMtNotSupport" style="color: red;display: none">不支持WASM WASM(SIMD) 多线程解码；</span>
            </div>
        </div>
        <div class="input">
            <div><input
                    type="checkbox"
                    checked
                    id="useMSE"
            /><span>MediaSource</span>
                <input
                        type="checkbox"
                        id="useWCS"
                /><span>Webcodec</span>
                <input
                        type="checkbox"
                        id="useWASM"
                /><span>WASM</span>
                <input
                        type="checkbox"
                        id="useSIMD"
                /><span>WASM(SIMD)</span>
                <input
                        type="checkbox"
                        id="useSIMDMThreading"
                        onclick="replay()"
                /><span>启动多线程（WASM,WASM(SIMD)解码支持）</span>
                <span style="color: green">默认使用wasm解码器</span>
            </div>
        </div>
        <div class="input">
            <span>渲染标签：</span>
            <select id="renderDom" onchange="replay()">
                <option value="video" selected>video</option>
                <option value="canvas">canvas</option>
            </select>

            <span>canvas渲染技术：</span>
            <select id="isUseWebGPU" onchange="replay()">
                <option value="webgl">webgl</option>
                <option value="webgpu" selected>webgpu</option>
            </select>
            <span id="supportWebgpu"></span>
        </div>
        <div class="input">
            <div>
                <span>缓存时长：</span>
                <input placeholder="单位：秒" type="text" id="videoBuffer" style="width: 50px" value="0.2">秒
                <span>缓存延迟(延迟超过会触发丢帧)：</span>
                <input placeholder="单位：秒" type="text" id="videoBufferDelay" style="width: 50px" value="1">秒
                <button id="replay">重播</button>
            </div>
        </div>
        <div class="input">
            <div>
                <input
                        checked
                        onclick="replay()"
                        type="checkbox"
                        id="demuxUseWorker"
                /><span>硬解码(MediaSource，Webcodec)worker解封装</span>
            </div>
        </div>

        <div class="input">
            <div>输入URL：</div>
            <input
                    autocomplete="on"
                    id="playUrl"
                       value="ws://**************:9080/ws.flv" />
            <button id="play">播放</button>
            <button id="pause" style="display: none">停止</button>
        </div>
        <div class="input" style="line-height: 30px">
            <button id="destroy">销毁</button>
            <span class="fps-inner"></span>
        </div>
    </div>
</div>

<script>
    var $player = document.getElementById('play');
    var $pause = document.getElementById('pause');
    var $playHref = document.getElementById('playUrl');
    var $container = document.getElementById('container');
    var $destroy = document.getElementById('destroy');
    var $fps = document.querySelector('.fps-inner');
    var $useMSE = document.getElementById('useMSE');
    var $useSIMD = document.getElementById('useSIMD');
    var $useWASM = document.getElementById('useWASM');
    var $useWCS = document.getElementById('useWCS');
    var $videoBuffer = document.getElementById('videoBuffer');
    var $videoBufferDelay = document.getElementById('videoBufferDelay');
    var $replay = document.getElementById('replay');
    var $renderDom = document.getElementById('renderDom');
    var $isUseWebGPU = document.getElementById('isUseWebGPU');
    var $demuxUseWorker = document.getElementById('demuxUseWorker');
    var $useSIMDMThreading = document.getElementById('useSIMDMThreading');
    var showOperateBtns = true; // 是否显示按钮
    var forceNoOffscreen = true; //
    var jessibuca = null;

    function create() {
        jessibuca = new JessibucaPro({
            container: $container,
            videoBuffer: Number($videoBuffer.value), // 缓存时长
            videoBufferDelay: Number($videoBufferDelay.value), // 1000s
            decoder: '/static/flveee/decoder-pro.js',
            isResize: false,
            text: "",
            loadingText: "加载中",
            debug: true,
            debugLevel: "debug",
            useMSE: $useMSE.checked === true,
            useSIMD: $useSIMD.checked === true,
            useWCS: $useWCS.checked === true,
            useMThreading: $useSIMDMThreading.checked === true,
            showBandwidth: showOperateBtns, // 显示网速
            showPerformance: showOperateBtns, // 显示性能
            operateBtns: {
                fullscreen: showOperateBtns,
                screenshot: showOperateBtns,
                play: showOperateBtns,
                audio: showOperateBtns,
                ptz: showOperateBtns,
                quality: showOperateBtns,
                performance: showOperateBtns,
            },
            timeout: 10,
            heartTimeoutReplayUseLastFrameShow: true,
            audioEngine: "worklet",
            qualityConfig: ['普清', '高清', '超清', '4K', '8K'],
            forceNoOffscreen: forceNoOffscreen,
            isNotMute: false,
            heartTimeout: 10,
            ptzClickType: 'mouseDownAndUp',
            ptzZoomShow: true,
            ptzMoreArrowShow: true,
            ptzApertureShow: true,
            ptzFocusShow: true,
            isDropSameTimestampGop: true,
            useCanvasRender: $renderDom.value === 'canvas',
            useWebGPU: $isUseWebGPU.value === 'webgpu',
            demuxUseWorker: $demuxUseWorker.checked === true,
            controlHtml: '<div>我是 <span style="color: red">test</span>文案</div>',
            // audioEngine:"worklet",
            // isFlv: true
            // mseDecodeAudio: true
        },);


        jessibuca.on('ptz', (arrow) => {
            // console.log('ptz', arrow);
        })

        jessibuca.on('streamQualityChange', (value) => {
            console.log('streamQualityChange', value);
        })

        jessibuca.on('timeUpdate', (value) => {
            // console.log('timeUpdate', value);
        })

        jessibuca.on('stats', (stats) => {
            // console.log('stats', stats);
            $fps.textContent = `FPS: ${stats.fps} DFPS: ${stats.dfps}`
        })


        $player.style.display = 'inline-block';
        $pause.style.display = 'none';
        $destroy.style.display = 'none';
        $fps.textContent = '';
    }


    create();

    function play() {
        var href = $playHref.value;
        if (href) {
            jessibuca.play(href);
            $player.style.display = 'none';
            $pause.style.display = 'inline-block';
            $destroy.style.display = 'inline-block';
        }
    }


    function replay() {
        if (jessibuca) {
            jessibuca.destroy().then(() => {
                create();
                play();
            });
        } else {
            create();
            play();
        }
    }

    $replay.addEventListener('click', function () {
        replay();
    })

    $player.addEventListener('click', function () {
        play();
    }, false)


    $pause.addEventListener('click', function () {
        $player.style.display = 'inline-block';
        $pause.style.display = 'none';
        $fps.textContent = '';
        jessibuca.pause();
    })

    $destroy.addEventListener('click', function () {
        if (jessibuca) {
            jessibuca.destroy().then(() => {
                create();
            });
        } else {
            create();
        }

    })

    $useMSE.addEventListener('click', function () {
        const checked = $useMSE.checked;
        if (checked) {
            $useSIMD.checked = false
            $useWCS.checked = false
            $useWASM.checked = false
        }
        replay();
    })

    $useSIMD.addEventListener('click', function () {
        const checked = $useSIMD.checked;
        if (checked) {
            $useMSE.checked = false
            $useWCS.checked = false
            $useWASM.checked = false
        }
        replay();
    })

    $useWCS.addEventListener('click', function () {
        const checked = $useWCS.checked;
        if (checked) {
            $useMSE.checked = false
            $useSIMD.checked = false
            $useWASM.checked = false
        }
        replay();
    })

    $useWASM.addEventListener('click', function () {
        const checked = $useWASM.checked;
        if (checked) {
            $useMSE.checked = false
            $useSIMD.checked = false
            $useWCS.checked = false
        }
        replay();
    })

</script>

</body>
</html>
