﻿
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>车辆注销记录</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
	<link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
	<link href="~/Static/css/animate.min.css" rel="stylesheet">
	<link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
	<style>
	</style>
</head>
<body>

	<div class="layui-fluid animated fadeInRight">
		<div class="layui-card layadmin-header height0"></div>
		<div class="layui-card layadmin-header">
			<div class="layui-breadcrumb" lay-filter="breadcrumb">
				<a><cite>记录查询</cite></a>
				<a><cite>车辆注销记录</cite></a>
			</div>
		</div>
		<div class="layui-row layui-col-space30">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header layui-form" id="searchForm">
						<div class="layui-row">
							<div class="layui-inline">
								<input class="layui-input" name="CarUnbound_CarNo" id="CarUnbound_CarNo" autocomplete="off" placeholder="车牌号" />
							</div>
						
							<div class="layui-inline">
								<input class="layui-input" name="CarUnbound_AddTime1" id="CarUnbound_AddTime1" autocomplete="off" placeholder="注销时间起" />
							</div>
							<div class="layui-inline">
								<input class="layui-input" name="CarUnbound_AddTime2" id="CarUnbound_AddTime2" autocomplete="off" placeholder="注销时间止" />
							</div>
							<div class="layui-inline">
								<div class="operabar-if">更多条件</div>
							</div>

							<div class="layui-inline">
								<button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
							</div>
						</div>
						<div class="layui-row search-more layui-hide">
							<div class="layui-inline">
								<input class="layui-input" name="CarUnbound_OwnerSpace" id="CarUnbound_OwnerSpace" autocomplete="off" placeholder="车位号" />
							</div>
							<div class="layui-inline">
								<input class="layui-input" name="CarUnbound_OwnerName" id="CarUnbound_OwnerName" autocomplete="off" placeholder="车主名称" />
							</div>
						</div>
					</div>

					<div class="layui-card-body">
						<table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

						<script type="text/html" id="toolbar_btns">
							<div class="layui-btn-container">
								<button class="layui-btn layui-btn-sm layui-hide" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i> <t>删除</t></button>
								<button class="layui-btn layui-btn-sm layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
							</div>
						</script>

					</div>
				</div>
			</div>
		</div>
	</div>

	<script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
	<script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
	<script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
	<script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.download.js" asp-append-version="true"></script>
	<script>
		topBar.init();

		var comtable = null;
		layui.use(['table', 'form', 'laydate'], function () {
			var table = layui.table;
			var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

			var cols = [[
				{ type: "checkbox" }
				, { field: 'CarUnbound_No', title: '记录编号', hide: true }
				, { field: 'CarUnbound_ParkNo', title: '车场编号', hide: true }
				, { field: 'CarUnbound_CarNo', title: '车牌号' }
				, { field: 'CarUnbound_OwnerSpace', title: '车位号' }
				, { field: 'CarUnbound_OwnerNo', title: '车主编号', hide: true }
				, { field: 'CarUnbound_CarCardTypeName', title: '车牌类型' }
				, { field: 'CarUnbound_CarCardTypeNo', title: '车牌类型编码', hide: true }
				, { field: 'CarUnbound_CarTypeName', title: '车牌颜色' }
				, { field: 'CarUnbound_CarTypeNo', title: '车牌颜色编码', hide: true }
				, { field: 'CarUnbound_BeginTime', title: '有效期起' }
				, { field: 'CarUnbound_EndTime', title: '有效期止' }
				, {
					field: 'CarUnbound_IsMoreCar', title: '一位多车', templet: function (d) {
						if (d.CarUnbound_IsMoreCar == 1) return tempBar(1, "是");
						else return tempBar(3, "否");
					}
				}
				, { field: 'CarUnbound_OwnerName', title: '车主名称' }
				, { field: 'CarUnbound_RegTime', title: '登记时间' }
				, { field: 'CarUnbound_AdminName', title: '操作员' }
				, { field: 'CarUnbound_AddTime', title: '注销时间' }
				//, { field: 'CarUnbound_Status', title: '注销状态', templet: function (d) { return '<span class="layui-badge layui-bg-red">已注销</span>' } }
			]];

			cols = tb_page_cols(cols);

			comtable = table.render({
				elem: '#com-table-base'
				, url: '/CarUnbound/GetList'
				, method: 'post'
				, toolbar: '#toolbar_btns'
				, defaultToolbar: ["filter"]
				, cols: cols
				, page: {
					layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
					groups: 3,
				}
				, request: { pageName: 'pageIndex', limitName: 'pageSize' }
				, where: { conditionParam: JSON.stringify(conditionParam) }
				, limit: tb_page_limit(), limits: [10, 20, 50, 100]
				, done: function (d) {
					tb_page_set(d);
					pager.bindPower();
				}
			});

			//头工具栏事件
			table.on('toolbar(com-table-base)', function (obj) {
				var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
				var data = checkStatus.data;  //获取选中行数据
				var pageindex = $(".layui-laypage-curr").text();
				pager.pageIndex = pageindex;
				switch (obj.event) {
					case 'Delete':
						if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
						if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
						var CarUnBoundNoList = [];
						$.each(data, function (k, v) {
							CarUnBoundNoList.push(v.CarUnbound_No);
						})
						layer.open({
							type: 0,
							title: "消息提示",
							btn: ["确定", "取消"],
							content: "确定删除车辆注销记录?",
							yes: function (res) {
								layer.msg("处理中", { icon: 16, time: 0 });
								$.post("/CarUnbound/Delete", { CarUnBound_No: CarUnBoundNoList.join(",") }, function (json) {
									if (json.success)
										layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
									else
										layer.msg(json.msg, { icon: 0, time: 1500 });
								}, "json");
							},
							btn2: function () { }
						})
						break;

					case 'Export':

						pager.dataField = [];
						obj.config.cols[0].forEach((item) => {
							if (item.title)
								pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
						});

						layer.open({
							id: "x_edit_iframe",
							type: 2,
							title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
							content: '/ExportExcel/Index?Act=Update&Owner_No=',
							area: getIframeArea(['1100px', '400px']),
							maxmin: false,
							end: function () {
								if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
									var conditionParam = $("#searchForm").formToJSON(true, function (data) {
										return data;
									});
									var field = pager.sortField == null ? "" : pager.sortField;
									var order = pager.orderField == null ? "" : pager.orderField;
									conditionParam.SearchType = topBar.config.SearchType;

									//实现Ajax下载文件
									$.fileDownload('/CarUnbound/ExportExcel?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
										httpMethod: 'GET',
										headers: {},
										data: null,
										prepareCallback: function (url) {
											$("#Export").attr("disabled", true);
											layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
										},
										successCallback: function (url) {
											$("#Export").attr("disabled", false);
											layer.msg('导出成功');
										},
										failCallback: function (html, url) {
											$("#Export").attr("disabled", false);
											layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
										}
									});
								}
							}
						});
						break;
				};
			});

			tb_row_checkbox();
			pager.init();
		});
	</script>
	<script>
		var pager = {
			dataField: null,
			sortField: null,
			orderField: null,
			pageIndex: 1,
			init: function () {
				$.ajaxSettings.async = false;
				pager.bindPower();
				pager.bindSelect();
				pager.bindEvent();
				$.ajaxSettings.async = true;
			},
			//重新加载数据
			bindSelect: function () {
				_DATE.bind(layui.laydate, ["CarUnbound_AddTime1", "CarUnbound_AddTime2"], { type: 'datetime', range: true });

			},
			bindData: function (index) {
				layer.closeAll();
				var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
				comtable.reload({
					url: '/CarUnbound/GetList'
					, where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
					, page: { curr: index }
				});
			},
			bindEvent: function () {
				$("#Search").click(function () { pager.bindData(1); });
			},
			bindPower: function () {
				window.parent.global.getBtnPower(window, function (pagePower) { });

				s_carno_picker.init("CarUnbound_CarNo", function (text, carno) {
					$("#CarUnbound_CarNo").val(carno.join(''));
				}, "web").bindkeyup();
			}
		}
	</script>
</body>
</html>
