﻿using System;
using Newtonsoft.Json;

namespace carparking.Model
{
    /// <summary>
    ///Car(车辆管理）数据实体
    /// </summary>
    [Serializable]
    public class Car
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int? Car_ID { get; set; }
        /// <summary>
        /// 车辆编码
        /// </summary>
        public string Car_No { get; set; }
        /// <summary>
        /// 车牌号码
        /// </summary>
        public string Car_CarNo { get; set; }
        /// <summary>
        /// 车身颜色
        /// </summary>
        public string Car_Colour { get; set; }
        /// <summary>
        /// 车牌颜色(如小型轿车)
        /// </summary>
        public string Car_VehicleTypeNo { get; set; }
        /// <summary>
        /// 品牌型号
        /// </summary>
        public string Car_Model { get; set; }
        /// <summary>
        /// 停车位预约号
        /// </summary>
        public string Car_OrderSpace { get; set; }
        /// <summary>
        /// 行驶证号
        /// </summary>
        public string Car_License { get; set; }
        /// <summary>
        /// 车牌类型编码
        /// </summary>
        public string Car_TypeNo { get; set; }
        /// <summary>
        /// 车牌状态：1-正常，3-停用
        /// </summary>
        public int? Car_Status { get; set; }
        /// <summary>
        /// 添加人
        /// </summary>
        public int? Car_AddID { get; set; }
        /// <summary>
        /// 添加时间（默认系统当前时间）
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_AddTime { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        public int? Car_EditID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_EditTime { get; set; }
        /// <summary>
        /// 车场编码
        /// </summary>
        public string Car_ParkingNo { get; set; }
        /// <summary>
        /// 账户余额
        /// </summary>
        public decimal? Car_Balance { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_BeginTime { get; set; }
        /// <summary>
        /// 有效期
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_EndTime { get; set; }
        /// <summary>
        /// 车主编码
        /// </summary>
        public string Car_OwnerNo { get; set; }
        /// <summary>
        /// 车主姓名
        /// </summary>
        public string Car_OwnerName { get; set; }
        /// <summary>
        /// 来源：1-T30软件，2-云车场
        /// </summary>
        public int? Car_OnLine { get; set; }

        public string Car_Remark { get; set; }
        /// <summary>
        /// 分类，3652-月租车A, 3653-月租车B, 3654-月租车C, 3655-月租车D, 3661-月租车E, 3662-月租车F, 3663-月租车G, 3664-月租车H, 3651-临时车A, 3650-临时车B, 3649-临时车C, 3648-临时车D, 3647-临时车E, 3646-临时车F, 3645-临时车G, 3644-临时车H, 3656-免费车, 3657-储值车, 3658-访客车, 3656-商家车
        /// </summary>
        public string Car_Category { get; set; }

        /// <summary>
        /// 是否绑定车位，0-否，1-是
        /// </summary>
        public int? Car_IsMoreCar { get; set; }

        /// <summary>
        /// 车位号
        /// </summary>
        public string Car_OwnerSpace { get; set; }

        /// <summary>
        /// 相机离线自动开闸(白名单)：0-禁用，1-启用
        /// </summary>
        public int? Car_EnableOffline { get; set; }

        /// <summary>
        /// 下发到相机记录状态
        /// </summary>
        public string Car_CameraBody { get; set; }
        /// <summary>
        /// 卡号
        /// </summary>
        public string Car_CardNo { get; set; }
    }
    
    /// <summary>
    /// 车主扩展信息
    /// </summary>
    public class CarOwnerExt : Car
    {
        /// <summary>
        /// 车主编号
        /// </summary>
        public string Owner_No { get; set; }
        /// <summary>
        /// 车牌类型编码 
        /// </summary>
        public string Owner_CardTypeNo { get; set; }
        /// <summary>
        /// 车牌类别：1-临停车,2-储值车,3-月租车,4-免费车,5-访客车,6-商家车 
        /// </summary>
        public string Owner_CardType { get; set; }
        /// <summary>
        /// 车主姓名    
        /// </summary>
        public string Owner_Name { get; set; }
        /// <summary>
        /// 系统车位号
        /// </summary>
        public string Owner_Space { get; set; }
        /// <summary>
        /// 车场车位号
        /// </summary>
        public string Owner_ParkSpace { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string Owner_IDCard { get; set; }

        /// <summary>
        /// 驾驶证号
        /// </summary>
        public string Owner_License { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Owner_Phone { get; set; }

        /// <summary>
        /// 手机号后四位
        /// </summary>
        public string Owner_PhoneLastFour { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Owner_Address { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Owner_Email { get; set; }

        /// <summary>
        /// 车主性别：0-未知，1-男，2-女
        /// </summary>
        public int? Owner_Sex { get; set; }

        /// <summary>
        /// 车位数量
        /// </summary>
        public int? Owner_SpaceNum { get; set; }
        /// <summary>
        /// 车位有效期起
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_Date))]
        public DateTime? Owner_StartTime { get; set; }
        /// <summary>
        /// 车位有效期止
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_Date))]
        public DateTime? Owner_EndTime { get; set; }

        /// <summary>
        /// 储值车余额
        /// </summary>
        public decimal? Owner_Balance { set; get; }
        /// <summary>
        /// 相机离线自动开闸(白名单)：0-禁用，1-启用 
        /// </summary>
        public int? Owner_EnableOffline { set; get; }
    }

    /// <summary>
    /// 车辆扩展信息
    /// </summary>
    public class CarExt : CarOwnerExt
    {
        /// <summary>
        /// 车牌类型名称
        /// </summary>
        public string CarCardType_Name { get; set; }
        /// <summary>
        /// 车牌类型
        /// </summary>
        public string CarCardType_Category { get; set; }
        /// <summary>
        /// 车牌分类，[1-临停类型，2-储值类型，3-月租类型，4-免费类型，5-访客类型]
        /// </summary>
        public string CarCardType_Type { get; set; }
        /// <summary>
        /// 车牌颜色名称
        /// </summary>
        public string CarType_Name { get; set; }

        /// <summary>
        /// 是否是一卡多车类型，1-是，0-否
        /// </summary>
        public int? CarCardType_IsMoreCar { get; set; }

        /// <summary>
        /// 定制语音显示
        /// </summary>
        public string CarCardType_ShowText { set; get; }
    }

    /// <summary>
    /// 扩展获取储值车余额
    /// </summary>
    public class CarExtBalance : CarExt
    {
        /// <summary>
        /// 储值车余额
        /// </summary>
        public decimal? Balance => Owner_Balance;
        /// <summary>
        /// 开始时间
        /// </summary>
        public string dateStart => Car_Category == "3657" ? "--" : $"{Car_BeginTime:yyyy-MM-dd HH:mm:ss}";
        /// <summary>
        /// 结束时间
        /// </summary>
        public string dateEnd => Car_Category == "3657" ? "--" : $"{Car_EndTime:yyyy-MM-dd HH:mm:ss}";
    }

    /// <summary>
    /// 车辆扩展信息
    /// </summary>
    public class CarExtLst : CarExt
    {
        /// <summary>
        /// 车主性别
        /// </summary>
        public string Owner_SexName { get; set; }
    }

    /// <summary>
    /// 车辆查询条件
    /// </summary>
    public class CarWhere : CarExt
    {
        /// <summary>
        /// 开始时间起
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_BeginTime0 { get; set; }
        /// <summary>
        /// 开始时间止
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_BeginTime1 { get; set; }

        /// <summary>
        /// 结束时间起
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_EndTime0 { get; set; }
        /// <summary>
        /// 结束时间止
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_EndTime1 { get; set; }

        /// <summary>
        /// 查询结束时间范围(天数，如：30，应查询出结束时间在-30 ~ 30 天之内的车辆记录)
        /// </summary>
        public int? Car_EndDay { get; set; }

        /// <summary>
        /// 创建时间起
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_AddTime0 { get; set; }
        /// <summary>
        /// 创建时间止
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Car_AddTime1 { get; set; }

    }

    public class CarEdit : CarWhere
    {
        /// <summary>
        /// 支付金额(新增时)
        /// </summary>
        public decimal? Car_PayMoney { get; set; }

        /// <summary>
        /// 停车订单编号(车辆登记时已在场内)
        /// </summary>
        public string ParkOrder_No { get; set; }
        /// <summary>
        /// 车主性别
        /// </summary>
        public string Owner_SexName { get; set; }
    }

    /// <summary>
    /// 车辆导入信息
    /// </summary>
    public class CarImport
    {
        /// <summary>
        /// 车牌号码
        /// </summary>
        public string Car_CarNo { get; set; }
        /// <summary>
        /// 车主姓名
        /// </summary>
        public string Car_CardName { get; set; }
        /// <summary>
        /// 车牌类型名称
        /// </summary>
        public string Car_CarTypeName { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? Car_BeginTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? Car_EndTime { get; set; }
        /// <summary>
        /// 车主姓名
        /// </summary>
        public string Car_OwnerName { get; set; }
        /// <summary>
        /// 系统车位号
        /// </summary>
        public string Car_OwnerSpace { get; set; }
        /// <summary>
        /// 车场车位号
        /// </summary>
        public string Car_ParkSpace { get; set; }
        /// <summary>
        /// 车辆卡号
        /// </summary>
        public string Car_CardNo { get; set; }
        /// <summary>
        /// 车位数量
        /// </summary>
        public int? Car_SpaceNum { get; set; }
        /// <summary>
        /// 车主地址
        /// </summary>
        public string Car_OwnerAddress { get; set; }
        /// <summary>
        /// 车主电话
        /// </summary>
        public string Car_OwnerPhone { get; set; }
        /// <summary>
        /// 车主性别
        /// </summary>
        public string Car_OwnerSex { get; set; }
        /// <summary>
        /// 车主余额
        /// </summary>
        public decimal? Car_Balance { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Car_Remark { get; set; }
    }
}
