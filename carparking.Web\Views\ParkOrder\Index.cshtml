﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>停车收费</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 3px 4px 0 0; float: left;}
        .layui-form-select .layui-input { width: 182px; }
        .layui-btn { line-height: normal !important; padding: 0 12px; }
        .layui-bg-wxgreen { background-color: #04BE02 !important; }
        .layui-bg-alipayblue { background-color: #1678ff !important; }
        .layui-bg-ylblue { background-color: #1678ff !important; }
        .fancybox-container { z-index: 19891222 !important; }
    </style>
    <style data-mark="表格列数量多的时候使用此样式展示列选择">
        .layui-table-tool-panel { width: 500px; }
        .layui-table-tool-panel li { width: 33.33%; float: left; }

        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>停车收费</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_No" id="ParkOrder_No" autocomplete="off" placeholder="订单编号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="订单状态" class="form-control chosen-select " id="ParkOrder_StatusNo" name="ParkOrder_StatusNo" lay-search>
                                    <option value="">订单状态</option>
                                    <option value="200" selected>已入场</option>
                                    <option value="201">已出场</option>
                                    <option value="199">预入场</option>
                                    <option value="202">自动关闭</option>
                                    <option value="203">场内关闭</option>
                                    <option value="204">欠费出场</option>
                                    <option value="0">预出场</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="form-control chosen-select " id="ParkOrder_CarType" name="ParkOrder_CarType" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入场车道" class="form-control chosen-select " id="ParkOrder_EnterPasswayNo" name="ParkOrder_EnterPasswayNo" lay-search>
                                    <option value="">入场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterAdminName" id="ParkOrder_EnterAdminName" autocomplete="off" placeholder="入口操作员" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="锁车状态" class="form-control chosen-select " id="ParkOrder_Lock" name="ParkOrder_Lock" lay-search>
                                    <option value="">锁车状态</option>
                                    <option value="0">未锁车</option>
                                    <option value="1">已锁车</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime0" id="ParkOrder_OutTime0" autocomplete="off" placeholder="出场时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime1" id="ParkOrder_OutTime1" autocomplete="off" placeholder="出场时间止" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_OutPasswayNo" name="ParkOrder_OutPasswayNo" lay-search>
                                    <option value="">出场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutAdminName" id="ParkOrder_OutAdminName" autocomplete="off" placeholder="出口操作员" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_ParkAreaNo" name="ParkOrder_ParkAreaNo" lay-search>
                                    <option value="">停车区域</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_IsNoInRecord" name="ParkOrder_IsNoInRecord" lay-search>
                                    <option value="">无入场记录</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_IsEpCar" name="ParkOrder_IsEpCar" lay-search>
                                    <option value="">重点地区车辆</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>  
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarLogo" id="ParkOrder_CarLogo" autocomplete="off" placeholder="车辆车标" />
                            </div>                            
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                        </div>

                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.ParkOrder.Payment){}}<button class="layui-btn layui-btn-sm" lay-event="Payment"><i class="fa fa-cny"></i><t>支付</t></button>{{# } }}
                                {{# if(Power.ParkOrder.Detail){}}<button class="layui-btn layui-btn-sm" lay-event="Detail"><i class="fa fa-list-alt"></i><t>详情</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplouttype">
        {{# if(d.ParkOrder_OutType==0){}}
        <span class="layui-badge layui-bg-gray">未出场</span>
        {{# }else if(d.ParkOrder_OutType==1){}}
        <span class="layui-badge layui-bg-blue">预出场</span>
        {{# }else if(d.ParkOrder_OutType==2){}}
        <span class="layui-badge layui-bg-green">已出场</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplqrcodetype">
        {{# if(d.ParkOrder_QrCodeType==0){}}
        <span class="layui-badge layui-bg-green">动态码</span>
        {{# }else if(d.ParkOrder_QrCodeType==1){}}
        <span class="layui-badge layui-bg-orange">车道固定码</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'ParkOrder_ID', title: '订单ID', hide: true }
                , { field: 'ParkOrder_No', title: '订单号', hide: true }
                , { field: 'ParkOrder_ParkNo', title: '车场编码', hide: true }
                //, { field: 'Parking_Name', title: '车场名称', hide: true }
                , { field: 'ParkOrder_CarNo', title: '车牌号', width: 100 }
                , { field: 'ParkOrder_CarCardType', title: '车牌类型编码', hide: true }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型' }
                , { field: 'ParkOrder_CarType', title: '车牌颜色编码', hide: true }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色' }
                , {
                    field: 'ParkOrder_StatusNo', title: '订单状态', templet: function (d) {
                        if (d.ParkOrder_StatusNo == 199) return tempBar(2, "预入场");
                        else if (d.ParkOrder_StatusNo == 200) {
                            if (d.ParkOrder_OutType == 0) return tempBar(1, "已入场");
                            if (d.ParkOrder_OutType == 1) return tempBar(3, "预出场");
                        }
                        else if (d.ParkOrder_StatusNo == 201) return tempBar(4, "已出场");
                        else if (d.ParkOrder_StatusNo == 202) return tempBar(0, "自动关闭");
                        else if (d.ParkOrder_StatusNo == 203) return tempBar(0, "场内关闭");
                        else if (d.ParkOrder_StatusNo == 204) return tempBar(6, "欠费出场");
                    }
                }
                , { field: 'ParkOrder_ParkAreaNo', title: '停车区域编码', hide: true }
                , { field: 'ParkOrder_ParkAreaName', title: '停车区域' }
                , {
                    field: 'ParkOrder_CarLogo', title: '车辆车标', templet: function (d) {
                        return ((d.ParkOrder_CarLogo == "0" || d.ParkOrder_CarLogo == null) ? "" : d.ParkOrder_CarLogo);
                    }
                }
                , { field: 'ParkOrder_EnterTime', title: '入场时间',width:160  ,sort:true}
                , { field: 'ParkOrder_EnterPasswayNo', title: '入场车道编码', hide: true }
                , { field: 'ParkOrder_EnterPasswayName', title: '入场车道名称', hide: true }
                , { field: 'ParkOrder_EnterAdminAccount', title: '入口操作员账号', hide: true }
                , { field: 'ParkOrder_EnterAdminName', title: '入口操作员', hide: true }
                , {
                    field: 'ParkOrder_EnterImgPath', title: '入场图片', templet: function (d) {
                        //return tempImg(d.ParkOrder_EnterImgPath);

                    }
                }
                , { field: 'ParkOrder_EnterRemark', title: '入场备注', hide: true }
                , { field: 'ParkOrder_OutTime', title: '出场时间', width: 160 ,sort:true}
                , { field: 'ParkOrder_OutPasswayNo', title: '出场车道编码', hide: true }
                , { field: 'ParkOrder_OutPasswayName', title: '出场车道名称', hide: true }
                , { field: 'ParkOrder_OutAdminAccount', title: '出口操作员账号', hide: true }
                , { field: 'ParkOrder_OutAdminName', title: '出口操作员', hide: true }
                , { field: 'ParkOrder_OutImgPath', title: '出场图片', templet: function (d) { return tempImg(d.ParkOrder_OutImgPath); }}
                , { field: 'ParkOrder_FreeReason', title: '免费原因' }
                , { field: 'ParkOrder_OwnerNo', title: '车主编号', hide: true }
                , { field: 'ParkOrder_OwnerName', title: '车主姓名' }
                , {
                    field: 'ParkOrder_Lock', title: '锁车状态', templet: function (d) {
                        if (d.ParkOrder_Lock == 1) return tempBar(1, "已锁车");
                        else if (d.ParkOrder_Lock == 0) return tempBar(3, "未锁车");
                    }
                }
                , { field: 'ParkOrder_TotalAmount', title: '应收总金额', totalRow: true, templet: function (d) { return ToFixed2(d.ParkOrder_TotalAmount); }, sort: true }
                , { field: 'ParkOrder_TotalPayed', title: '实收总金额', totalRow: true, templet: function (d) { return ToFixed2(d.ParkOrder_TotalPayed); }, sort: true }
                , { field: 'ParkOrder_UserNo', title: '用户', hide: true }
                , {
                    field: 'ParkOrder_PayScene', title: '支付场景', hide: true, templet: function (d) {
                        if (d.ParkOrder_PayScene == 1) return tempBar(1, "场内缴费");
                        else if (d.ParkOrder_PayScene == 2) return tempBar(2, "出口缴费");
                        else return "";
                    }
                }
                , { field: 'ParkOrder_QrCodeType', title: '入场二维码类型', hide: true, toolbar: "#tmplqrcodetype" }
                , { field: 'ParkOrder_Remark', title: '订单备注', hide: true }
                , {
                    field: 'ParkOrder_IsNoInRecord', title: '无入场记录', hide: true, templet: function (d) {
                        if (d.ParkOrder_IsNoInRecord == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }, {
                    field: 'ParkOrder_IsEpCar', title: '重点地区车辆', hide: true, templet: function (d) {
                        if (d.ParkOrder_IsEpCar == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ParkOrder/GetParkOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Payment':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 1500 }); return; }
                        if (data[0].ParkOrder_StatusNo != 200 && data[0].ParkOrder_StatusNo != 204) { layer.msg("不在场内无需支付", { icon: 0, time: 1500 }); return; }
                        var orderno = data[0].ParkOrder_No;
                        layer.open({
                            title: "<i class='fa fa-rmb' style='margin-top: 17px;'></i> 停车支付",
                            type: 2, id: 1,
                            area: getIframeArea(['960px', '620px']),
                            fix: false, //不固定
                            maxmin: false,
                            content: 'Payment?ParkOrder_No=' + encodeURIComponent(orderno)
                        });
                        break;
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var orderno = data[0].ParkOrder_No;
                        layer.open({
                            title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 停车详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'Detail?From=ParkOrder&ParkOrder_No=' + encodeURIComponent(orderno)
                        });
                        break;
                };
            });

              //排序
            table.on('sort(com-table-base)', function(obj){
                  if(obj.type==null) obj.field=null;
                  pager.sortField=obj.field;
                  pager.orderField=obj.type;
                  pager.bindData(1);
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            sortField:null,
            orderField:null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#ParkOrder_CarType").append(option)
                        });
                    }
                }, "json");

                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#ParkOrder_EnterPasswayNo").append(option);
                            $("#ParkOrder_OutPasswayNo").append(option);
                        });
                    }
                }, "json");

                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                            $("#ParkOrder_ParkAreaNo").append(option);
                        });
                    }
                }, "json");

                layuiForm.render("select");

                $("#ParkOrder_EnterTime0").val(new Date().Format("yyyy-MM-dd 00:00:00"));
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["ParkOrder_OutTime0", "ParkOrder_OutTime1"], { type: 'datetime', range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                var field=pager.sortField==null?"":pager.sortField;
                var order=pager.orderField==null?"":pager.orderField;
                comtable.reload({
                    url: '/ParkOrder/GetParkOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam) ,field: field,order: order} //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                        $("#ParkOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }
    </script>
</body>
</html>
