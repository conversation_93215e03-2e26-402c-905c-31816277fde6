﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>新增与编辑临停计费规则</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .layui-form { padding: 0 15px; }

        .layui-row.primary { padding: 1rem 0; }
        .layui-row.primary label { font-weight: bold; background-color: #18605a; color: #fff; padding: 3px 10px; }

        .layui-select-title input { }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }

        .layui-col-xs12 { margin-bottom: 5px; padding-bottom: 10px; }
        .layui-row.panel { border-bottom: 1px solid #bbb; }

        .workday_item, .workday_holidayItem { border-top: 1px solid #54c8d8; }
        .workday_item:last-child { border-bottom: 1px solid #dcea62; }
        .workday_holidayItem:last-child, .tmplnoworkday_notimepart_numbertime_item:last-child, .tmplnoworkday_notimepart_spacetime_item:last-child, .tmplnoworkday_notimepart_rangtime_item:last-child, .tmplnoworkday_timepart_item:last-child { border-bottom: 1px solid #bbb; }
        .tmplnoworkday_timepart_item:not(:last-child) { border-bottom: 1px solid #54c8d8; }

        .divInLine { display: inline-block; margin-top: 5px; }
        .divInLine .normal { max-width: 50px; }
        .divInLine label { height: 15px; padding: 0; }
        .workday_delitem, .workday_holidayDelitem, .tmplnoworkday_notimepart_rangtime_item_rule_delitem, .noworkday_timepart_delitem, .tmplworkday_rangtime_item_rule_delitem { background-color: #ff6a00; }
        .workday_header, .workday_holiday_header { margin-bottom: 5px; }

        .workday_holiday_header span, .workday_header span { font-weight: 600; }
        input[type=time] { max-width: 80px !important; }
        /*  input[type=time]::-webkit-calendar-picker-indicator { visibility: hidden; }*/
    </style>
</head>
<body>
    <div class="layui-form">
        <div class="layui-row primary">
            <label id="CarCardType_Name"></label>
            <label id="CarType_Name"></label>
            <label id="ParkArea_Name"></label>
        </div>
        <div class="layui-row">
            <div class="layui-inline">
                <select class="layui-select" lay-search id="BillRuleTemp_IsDiffHoliday" name="BillRuleTemp_IsDiffHoliday" lay-filter="BillRuleTemp_IsDiffHoliday" data-key="BillRuleTemp_IsDiffHoliday">
                    <option value="0">不分工作日节假日</option>
                    <option value="1">分工作日节假日</option>
                </select>
            </div>
            <div class="layui-inline divTopMoneySet">
                <select class="layui-select" lay-search id="BillRuleTemp_TopMoneySet" name="BillRuleTemp_TopMoneySet" lay-filter="BillRuleTemp_TopMoneySet">
                    <option value="0">一天封顶金额</option>
                    <option value="1">周期封顶金额</option>
                </select>
            </div>
            <div class="layui-row divInLine divTopMoneySetItem layui-hide">
                <div class="layui-inline">
                    <div class="divInLine"><input type="text" class="layui-input normal" value="24" /></div>
                    <div class="divInLine"><label>小时最高收</label></div>
                    <div class="divInLine"><input type="text" class="layui-input normal" value="0" /></div>
                    <div class="divInLine"><label>元。</label></div>
                </div>
            </div>
            <div class="layui-inline divBillRuleTemp_IsDiffTime">
                <select class="layui-select" lay-search id="BillRuleTemp_IsDiffTime" name="BillRuleTemp_IsDiffTime" lay-filter="BillRuleTemp_IsDiffTime" data-key="BillRuleTemp_IsDiffTime">
                    <option value="0">不分时段</option>
                    <option value="1">分时段</option>
                </select>
            </div>
            <div class="layui-inline divBillRuleTemp_IsDiffHoliday">
                <select class="layui-select" lay-search id="BillRuleTemp_Free" name="BillRuleTemp_Free" lay-filter="BillRuleTemp_Free" data-key="BillRuleTemp_Free">
                    <option value="1">按间隔时长收费</option>
                    <option value="2">按时长范围收费</option>
                    <option value="3">按次收费</option>
                </select>
            </div>
        </div>
        <hr class="hr-line-solid layui-border-cyan" />
        <div class="layui-row">
            <div class="rulebox-items rulebox" id="rulebox">
                <div class="workday">
                    <div class="workday_work  layui-hide">
                        <div class="workday_header">
                            <span>工作日</span>
                            <div class="divInLine divTopMoneySetItem2">
                                <select class="layui-select" lay-search id="BillRuleTemp_MaxDayFree" name="BillRuleTemp_MaxDayFree" lay-filter="BillRuleTemp_MaxDayFree" data-key="BillRuleTemp_MaxDayFree">
                                    <option value="1">不设一天最高收费</option>
                                    <option value="2">一天最高收费</option>
                                </select>
                            </div>
                            <div class="divInLine workday_header_value layui-hide">
                                <input type="text" class="layui-input normal" value="0" />
                            </div>
                            <div class="divInLine workday_header_value layui-hide">
                                <label>元</label>
                            </div>
                        </div>
                    </div>
                    <div class="workday_holiday  layui-hide">
                        <div class="workday_holiday_header">
                            <span>节假日</span>
                            <div class="divInLine">
                                <select class="layui-select" lay-search id="BillRuleTemp_MaxHolidayDayFree" name="BillRuleTemp_MaxHolidayDayFree" lay-filter="BillRuleTemp_MaxHolidayDayFree" data-key="BillRuleTemp_MaxHolidayDayFree">
                                    <option value="1">不设一天最高收费</option>
                                    <option value="2">一天最高收费</option>
                                </select>
                            </div>
                            <div class="divInLine workday_holiday_header_value layui-hide">
                                <input type="text" class="layui-input normal" value="0" />
                            </div>
                            <div class="divInLine workday_holiday_header_value layui-hide">
                                <label>元</label>
                            </div>
                        </div>
                    </div>

                    <div class="timepart layui-hide">
                        <div class="spacetime  layui-hide"></div>
                        <div class="rangtime layui-hide"></div>
                        <div class="numbertime layui-hide"></div>
                    </div>
                    <div class="notimepart layui-hide">
                        <div class="spacetime  layui-hide"></div>
                        <div class="rangtime layui-hide"></div>
                        <div class="numbertime layui-hide"></div>
                    </div>
                </div>

                <div class="noworkday layui-hide">
                    <div class="timepart layui-hide">
                        <div class="spacetime  layui-hide"></div>
                        <div class="rangtime layui-hide"></div>
                        <div class="numbertime layui-hide"></div>
                    </div>
                    <div class="notimepart layui-hide">
                        <div class="spacetime  layui-hide"></div>
                        <div class="rangtime layui-hide"></div>
                        <div class="numbertime layui-hide"></div>
                    </div>
                </div>

            </div>

            <!--周期多次累计计费-->
            <div class="layui-row panel rulebox">
                <div class="layui-col-xs12">
                    <div class="layui-inline">
                        <select class="layui-select" data-key="IsGrandtotal" id="BillRuleTemp_IsGrandtotal" name="BillRuleTemp_IsGrandtotal">
                            <option value="1">启用周期内多次进出累计计费</option>
                            <option value="0">不启用周期多次进出累计计费</option>
                        </select>
                    </div>
                </div>
                <div class="layui-col-xs12 IsGrandtotal">
                    <div class="layui-col-xs1"><label>周期时长(小时)</label></div>
                    <div class="layui-col-xs1"><input type="text" class="layui-input" value="0" /></div>
                    <div class="layui-col-xs1"><label>最高收费(元)</label></div>
                    <div class="layui-col-xs1"><input type="text" class="layui-input" value="0" /></div>
                </div>
                <div class="layui-col-xs12 IsGrandtotal">
                    <div class="layui-col-xs2"><label>多次出入累计周期开始时间</label></div>
                    <div class="layui-col-xs2">
                        <select class="layui-select">
                            <option value="1">入场时间</option>
                            <option value="0">每日0点</option>
                        </select>
                    </div>
                    <div class="layui-col-xs2">
                        <select class="layui-select">
                            <option value="2">每次都有免费时长</option>
                            <option value="1">周期内一次免费时长</option>
                        </select>
                    </div>
                    <div class="layui-col-xs2">
                        <select class="layui-select">
                            <option value="2">每次都有首段时长</option>
                            <option value="1">周期内一次首段时长</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-row" style="margin-top:20px;padding-left:15px;">
        <div class="layui-col-xs12 edit-ipt-ban">
            <button class="layui-btn" id="Save"><i class="layui-icon layui-icon-ok-circle"></i> <t>保存</t></button>
            <button class="layui-btn layui-bg-orange" id="Cancel"><i class="layui-icon layui-icon-close-fill"></i> <t>取消</t></button>
        </div>
    </div>
    @*不分工作节假日-不分时段-按次计费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_numbertime">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_numbertime_item">
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="0" /></div>
            <div class="divInLine"><label>分钟内免费，每次收</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-float" value="0" /></div>
            <div class="divInLine"><label>元。</label></div>
            <div style="display:block;">
                <div class="divInLine">
                    <select class="layui-select" lay-search id="BillRuleTemp_Cycle" name="BillRuleTemp_Cycle" lay-filter="BillRuleTemp_Cycle">
                        <option value="1">限定周期</option>
                        <option value="2">不限定周期</option>
                    </select>
                </div>
                <div class="divInLine"><label>连续停车超过</label>  </div>
                <div class="divInLine">   <input type="text" class="layui-input normal" value="24" />   </div>
                <div class="divInLine"><label>小时,重新计费。</label></div>
            </div>
        </div>
    </script>
    @*不分工作节假日-不分时段-按次间隔时长-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_spacetime">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_spacetime_item">
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="15" /></div>
            <div class="divInLine"><label>分钟内免费，首</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="60" /></div>
            <div class="divInLine"><label>分钟，收</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-float" value="10.00" /></div>
            <div class="divInLine"><label>元，以后每隔</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="30" /></div>
            <div class="divInLine"><label>分钟，收</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-float" value="10" /></div>
            <div class="divInLine"><label>元。</label></div>

            <div style="display:block;">
                <div class="divInLine">
                    <select class="layui-select" lay-search id="BillRuleTemp_TopMoney" name="BillRuleTemp_TopMoney" lay-filter="BillRuleTemp_TopMoney">
                        <option value="1">封顶金额</option>
                        <option value="2">不封顶金额</option>
                    </select>
                </div>
                <div class="divInLine">   <input type="text" class="layui-input normal" value="24" />   </div>
                <div class="divInLine"><label>小时最高收</label>  </div>
                <div class="divInLine">   <input type="text" class="layui-input normal" value="100" />   </div>
                <div class="divInLine"><label>元。</label></div>
            </div>
        </div>
    </script>
    @*不分工作节假日-不分时段-按时长范围收费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_rangtime">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_rangtime_item">
            <div class="divInLine"><input type="text" class="layui-input normal  v-number" value="15" /></div>
            <div class="divInLine"><label>分钟内免费，周期:</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="60" /></div>
            <div class="divInLine"><label>小时。当停车时长：</label></div>
            <div class="divInLine">
                <select class="layui-select" lay-search id="BillRuleTemp_MaxEqual" name="BillRuleTemp_MaxEqual" lay-filter="BillRuleTemp_MaxEqual">
                    <option value="1">>=</option>
                    <option value="2"><=</option>
                </select>
            </div>
            <div style="display:block;">
                <div class="divInLine tmplnoworkday_notimepart_rangtime_rule">

                </div>
            </div>
        </div>
    </script>
    @*不分工作节假日-不分时段-按时长范围收费->规则-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_rangtime_rule">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_rangtime_item_rule">
            <div class="divInLine"><input type="text" class="layui-input normal  v-number" value="15" /></div>
            <div class="divInLine"><label>小时</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal  v-number" value="60" /></div>
            <div class="divInLine"><label>分钟，收费</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal  v-float" value="60" /></div>
            <div class="divInLine"><label>元。</label></div>
            <div class="divInLine"><button class="layui-btn tmplnoworkday_notimepart_rangtime_item_rule_additem"><t>新增规则</t></button></div>
        </div>
    </script>
    @*不分工作节假日-分时段-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_timepart">
        <div class="layui-col-xs12 tmplnoworkday_timepart_item">
            <div class="divInLine"><label>时间段</label></div>
            <div class="divInLine"><input type="time" class="layui-input normal" value="00:00" /></div>
            <div class="divInLine"><label>-</label></div>
            <div class="divInLine"><input type="time" class="layui-input normal" value="00:00" /></div>
            <div class="divInLine">
                <select class="layui-select" lay-search id="BillRuleTemp_TimePartTime" name="BillRuleTemp_TimePartTime" lay-filter="BillRuleTemp_TimePartTime" data-key="BillRuleTemp_TimePartTime">
                    <option value="1">按间隔时长计费</option>
                    <option value="2">按停车时长范围计费</option>
                    <option value="3">按次计费</option>
                </select>
            </div>
            <div class="divInLine"><button class="layui-btn noworkday_timepart_additem"><t>新增时段</t></button></div>
            <div class="content">
                <div style="display:block;">
                    <div class="divInLine"><input type="text" class="layui-input normal v-number" value="0" /></div>
                    <div class="divInLine"><label>分钟内免费，首</label>  </div>
                    <div class="divInLine">   <input type="text" class="layui-input normal v-number" value="0" />   </div>
                    <div class="divInLine"><label>分钟，收</label></div>
                    <div class="divInLine"><input type="text" class="layui-input normal v-float" value="0" /></div>
                    <div class="divInLine"> <label>元，以后每隔</label></div>
                    <div class="divInLine">  <input type="text" class="layui-input normal v-number" value="0" />    </div>
                    <div class="divInLine">  <label>分钟，收</label></div>
                    <div class="divInLine">  <input type="text" class="layui-input normal v-float" value="0" />   </div>
                    <div class="divInLine"> <label>元，</label></div>
                </div>
                <div style="display:block;">
                    <div class="divInLine">
                        <select class="layui-select" lay-search id="BillRuleTemp_TopMoney" name="BillRuleTemp_TopMoney" lay-filter="BillRuleTemp_TopMoney">
                            <option value="1">时段最高收费</option>
                            <option value="2">不设时段最高</option>
                        </select>
                    </div>
                    <div class="divInLine">  <input type="text" class="layui-input normal v-float" value="0" />   </div>
                    <div class="divInLine"> <label>元</label></div>
                </div>
            </div>
        </div>
    </script>

    @*分工作节假日-工作日-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_work">
        <div class="layui-col-xs12 workday_item">
            <div class="divInLine"><label>时间段</label></div>
            <div class="divInLine"><input type="time" class="layui-input normal" value="00:00" data-key="startTime" /></div>
            <div class="divInLine"><label>-</label></div>
            <div class="divInLine"><input type="time" class="layui-input normal" value="00:00" /></div>
            <div class="divInLine">
                <select class="layui-select" lay-search id="BillRuleTemp_WorkTime" name="BillRuleTemp_WorkTime" lay-filter="BillRuleTemp_WorkTime" data-key="BillRuleTemp_WorkTime">
                    <option value="1">按间隔时长计费</option>
                    <option value="2">按停车时长范围计费</option>
                    <option value="3">按次计费</option>
                </select>
            </div>
            <div class="divInLine"><button class="layui-btn workday_additem"><t>新增时段</t></button></div>
            <div class="content" style="display:block;"></div>
        </div>
    </script>
    @*分工作节假日-节假日-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_holiday">
        <div class="layui-col-xs12 workday_holidayItem">
            <div class="divInLine"><label>时间段</label></div>
            <div class="divInLine"><input type="time" class="layui-input normal" value="00:00" /></div>
            <div class="divInLine"><label>-</label></div>
            <div class="divInLine"><input type="time" class="layui-input normal" value="00:00" /></div>
            <div class="divInLine">
                <select class="layui-select" lay-search id="BillRuleTemp_HolidayTime" name="BillRuleTemp_HolidayTime" lay-filter="BillRuleTemp_HolidayTime" data-key="BillRuleTemp_HolidayTime">
                    <option value="1">按间隔时长计费</option>
                    <option value="2">按停车时长范围计费</option>
                    <option value="3">按次计费</option>
                </select>
            </div>
            <div class="divInLine"><button class="layui-btn workday_holidayAdditem"><t>新增时段</t></button></div>
            <div class="content" style="display:block;"></div>
        </div>
    </script>
    @*分工作节假日-按间隔时长-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_spacetime">
        <div class="layui-col-xs12 tmplworkday_spacetime_item">
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="15" /></div>
            <div class="divInLine"><label>分钟内免费，首</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="60" /></div>
            <div class="divInLine"><label>分钟，收</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-float" value="10.00" /></div>
            <div class="divInLine"><label>元，以后每隔</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="30" /></div>
            <div class="divInLine"><label>分钟，收</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-float" value="10" /></div>
            <div class="divInLine"><label>元。</label></div>

            <div style="display:block;">
                <div class="divInLine">
                    <select class="layui-select" lay-search id="BillRuleTemp_TopMoney" name="BillRuleTemp_TopMoney" lay-filter="BillRuleTemp_TopMoney">
                        <option value="1">时段最高收费</option>
                        <option value="2">不设时段最高收费</option>
                    </select>
                </div>
                <div class="divInLine">   <input type="text" class="layui-input normal v-float" value="0.00" />   </div>
                <div class="divInLine"><label>元。</label></div>
            </div>
        </div>
    </script>
    @*分工作节假日-按时长范围收费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_rangtime">
        <div class="layui-col-xs12 tmplworkday_rangtime_item">
            <div class="divInLine"><input type="text" class="layui-input normal" value="15" /></div>
            <div class="divInLine"><label>分钟内免费，</label></div>
            <div class="divInLine"><label>当停车时长：</label></div>
            <div class="divInLine">
                <select class="layui-select" lay-search id="BillRuleTemp_MaxEqual" name="BillRuleTemp_MaxEqual" lay-filter="BillRuleTemp_MaxEqual" data-key="BillRuleTemp_MaxEqual">
                    <option value="1">>=</option>
                    <option value="2"><=</option>
                </select>
            </div>
            <div style="display:block;">
                <div class="divInLine tmplworkday_rangtime_rule">
                    <div class="layui-col-xs12 tmplworkday_rangtime_item_rule">
                        <div class="divInLine"><input type="text" class="layui-input normal v-number" value="15" /></div>
                        <div class="divInLine"><label>小时</label></div>
                        <div class="divInLine"><input type="text" class="layui-input normal v-number" value="60" /></div>
                        <div class="divInLine"><label>分钟，收费</label></div>
                        <div class="divInLine"><input type="text" class="layui-input normal v-float" value="60" /></div>
                        <div class="divInLine"><label>元。</label></div>
                        <div class="divInLine"><button class="layui-btn tmplworkday_rangtime_item_rule_additem"><t>新增规则</t></button></div>
                    </div>
                </div>
            </div>
            <div class="divInLine"><label>跨段时长：</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="0" /></div>
            <div class="divInLine"><label>分钟,当停车时长出现跨段(由本段跨入下段),且小于等于设置值,按照本段计费</label></div>
        </div>
    </script>
    @*分工作节假日-按时长范围收费->规则-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_rangtime_rule">
        <div class="layui-col-xs12 tmplworkday_rangtime_item_rule">
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="15" /></div>
            <div class="divInLine"><label>小时</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="60" /></div>
            <div class="divInLine"><label>分钟，收费</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal v-float" value="60" /></div>
            <div class="divInLine"><label>元。</label></div>
            <div class="divInLine"><button class="layui-btn tmplworkday_rangtime_item_rule_additem"><t>新增规则</t></button></div>
        </div>
    </script>
    @*分工作节假日-按次计费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_numbertime">
        <div class="layui-col-xs12 tmplworkday_numbertime_item">
            <div class="divInLine"><input type="text" class="layui-input normal v-number" value="0" /></div>
            <div class="divInLine"><label>分钟内免费，每次收</label></div>
            <div class="divInLine"><input type="text" class="layui-input normal  v-float" value="0" /></div>
            <div class="divInLine"><label>元。</label></div>
            <div style="display:block;">
                <div class="divInLine"><label>跨段时长：</label></div>
                <div class="divInLine"><input type="text" class="layui-input normal v-number" value="0" /></div>
                <div class="divInLine"><label>分钟,当停车时长出现跨段(由本段跨入下段),且小于等于设置值,按照次收费</label></div>
            </div>
        </div>
    </script>


    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?v1.110" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script type="text/javascript">

        myVerify.init();

        var layuiForm = null;
        layui.use(['element', 'form'], function () {
            layuiForm = layui.form;

            pager.init();
        })

        var pager = {
            parkareas: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            drives: null,       //设备型号列表
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindShow();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindShow: function () {
                var tempruleprimary = localStorage.getItem("tempruleprimary");
                if (tempruleprimary != null && tempruleprimary != '') {
                    var obj = JSON.parse(tempruleprimary);
                    $("#CarCardType_Name").text(obj.CarCardType_Name);
                    $("#CarType_Name").text(obj.CarType_Name);
                    $("#ParkArea_Name").text(obj.ParkArea_Name);
                } else {

                }

                $(".divTopMoneySet").addClass("layui-hide");
                $(".tmplnoworkday_notimepart_spacetime_item").remove();
                _noworkdayNotimepart_spacetime_BLL.show();

                _selectChangeBLL.bindChange();
                // _slt.onchange("IsDiffTime", $("#BillRuleTemp_IsDiffTime").val());
            },
            bindSelect: function () {

            },
            bindData: function () {

            },
            bindEvent: function () {
                //layuiForm.on("select", function (data) {
                //    var val = data.value;
                //    var id = data.elem.id;
                //    var key = $(data.elem).attr("data-key");

                //    _slt.onchange(key, val);
                //})

                $("#Cancel").click(function () {
                    parent.layer.closeAll();
                })
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                    }
                });
            }
        }

        //隐藏所有
        var _hideAll = {
            process: function () {
                $(".workday,.noworkday,.workday_work,.workday_holiday,.timepart,.notimepart,.spacetime,.rangtime,.numbertime").addClass("layui-hide");
            }
        }

        //分工作节假日
        var _workdayBLL = {
            bindChange: function () {
                _selectChangeBLL.bindChange();
            },
        }
        //分工作节假日->按工作日
        var _workday_work_BLL = {
            onload: function (data) {

            },
            show: function (index) {
                $(".workday,.workday_work").removeClass("layui-hide");
                if ($(".workday_item").length == 0) {
                    $(".workday_work").append($("#tmplworkday_work").tmpl([{}]));
                    layuiForm.render("select");
                    _workday_work_BLL.bindAddItem();

                    var content = $(".workday_item").last().find(".content");
                    _workday_numbertime_BLL.show(content);
                    myVerify.init();
                }
            },
            bindAddItem: function () {
                $(".workday_additem").unbind("click").click(function () {
                    var htm = $("#tmplworkday_work").tmpl([{ index: 0 }]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("workday_additem", "workday_delitem").replace("新增时段", "删除时段")
                    $(".workday_work").append(htm);
                    layuiForm.render("select");
                    _workday_work_BLL.delItem();

                    var content = $(".workday_item").last().find(".content");
                    _workday_numbertime_BLL.show(content);
                });
            },
            delItem: function () {
                $(".workday_delitem").unbind("click").click(function () {
                    $(this).parent().parent().remove();
                });
            },
        }
        //分工作节假日—>按节假日
        var _workday_holiday_BLL = {
            onload: function (data) {

            },
            show: function (index) {
                $(".workday,.workday_holiday").removeClass("layui-hide");
                if ($(".workday_holidayItem").length == 0) {
                    $(".workday_holiday").append($("#tmplworkday_holiday").tmpl([{}]));
                    layuiForm.render("select");
                    _workday_holiday_BLL.bindAddItem();

                    var content = $(".workday_holidayItem").last().find(".content");
                    _workday_numbertime_BLL.show(content);
                    _selectChangeBLL.bindChange();
                    myVerify.init();
                }
            },
            bindAddItem: function () {
                $(".workday_holidayAdditem").unbind("click").click(function () {
                    var htm = $("#tmplworkday_holiday").tmpl([{ index: 0 }]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("workday_holidayAdditem", "workday_holidayDelitem").replace("新增时段", "删除时段")
                    $(".workday_holiday").append(htm);
                    layuiForm.render("select");
                    _workday_holiday_BLL.delItem();

                    var content = $(".workday_holidayItem").last().find(".content");
                    _workday_numbertime_BLL.show(content);
                    _selectChangeBLL.bindChange();
                });
            },
            delItem: function () {
                $(".workday_holidayDelitem").unbind("click").click(function () {
                    $(this).parent().parent().remove();
                });
            },
           
        }
        //分工作日->按次收费
        var _workday_numbertime_BLL = {
            show: function (id) {
                $(id).html($("#tmplworkday_numbertime").tmpl([{}]));
                layuiForm.render("select");
                myVerify.init();
            },
        }
        //分工作日->按间隔时长
        var _workday_spacetime_BLL = {
            show: function (id) {
                $(id).html($("#tmplworkday_spacetime").tmpl([{}]));
                layuiForm.render("select");
                myVerify.init();
            }
        }
        //分工作日->按时长范围收费
        var _workday_rangtime_BLL = {
            show: function (id) {
                $(id).html($("#tmplworkday_rangtime").tmpl([{}]));
                layuiForm.render("select");
                _workday_rangtime_BLL.bindAddItem(id);
                myVerify.init();
            },
            bindAddItem: function (id) {

                $(id).find("button").unbind("click").click(function () {
                    var htm = $("#tmplworkday_rangtime_rule").tmpl([{}]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("tmplworkday_rangtime_item_rule_additem", "tmplworkday_rangtime_item_rule_delitem").replace("新增规则", "删除规则")
                    $(this).closest(".tmplworkday_rangtime_rule").append(htm);
                    layuiForm.render("select");
                    _workday_rangtime_BLL.delItem();
                    myVerify.init();
                });
            },
            delItem: function () {
                $(".tmplworkday_rangtime_item_rule_delitem").unbind("click").click(function () {
                    $(this).parent().parent().remove();
                });
            },
        }

        //不分工作日->不分时段
        var _noworkdayNotimepartBLL = {
            bindChange: function () {

            },
        }
        //不分工作日->不分时段->按次收费
        var _noworkdayNotimepart_numbertime_BLL = {
            show: function (index) {
                $(".noworkday,.notimepart,.numbertime").removeClass("layui-hide");
                if ($(".tmplnoworkday_notimepart_numbertime_item").length == 0) {
                    $(".noworkday>.notimepart>.numbertime").append($("#tmplnoworkday_notimepart_numbertime").tmpl([{}]));
                    layuiForm.render("select");
                    myVerify.init();
                }
            }
        }
        //不分工作日->不分时段->按间隔时长收费
        var _noworkdayNotimepart_spacetime_BLL = {
            show: function () {
                $(".noworkday,.notimepart,.spacetime").removeClass("layui-hide");
                if ($(".tmplnoworkday_notimepart_spacetime_item").length == 0) {
                    $(".noworkday>.notimepart>.spacetime").append($("#tmplnoworkday_notimepart_spacetime").tmpl([{}]));
                    layuiForm.render("select");
                    myVerify.init();
                }
            }
        }
        //不分工作日->不分时段->按时长范围收费
        var _noworkdayNotimepart_rangtime_BLL = {
            show: function (index) {
                $(".noworkday,.notimepart,.rangtime").removeClass("layui-hide");
                if ($(".tmplnoworkday_notimepart_rangtime_item").length == 0) {
                    $(".noworkday>.notimepart>.rangtime").append($("#tmplnoworkday_notimepart_rangtime").tmpl([{}]));

                    $(".tmplnoworkday_notimepart_rangtime_rule").append($("#tmplnoworkday_notimepart_rangtime_rule").tmpl([{}]));

                    layuiForm.render("select");

                    _noworkdayNotimepart_rangtime_BLL.bindAddItem();
                    myVerify.init();
                }
            },
            bindAddItem: function () {
                $(".tmplnoworkday_notimepart_rangtime_item_rule_additem").unbind("click").click(function () {
                    var htm = $("#tmplnoworkday_notimepart_rangtime_rule").tmpl([{}]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("tmplnoworkday_notimepart_rangtime_item_rule_additem", "tmplnoworkday_notimepart_rangtime_item_rule_delitem").replace("新增规则", "删除规则")
                    $(".tmplnoworkday_notimepart_rangtime_rule").append(htm);
                    layuiForm.render("select");
                    _noworkdayNotimepart_rangtime_BLL.delItem();
                    myVerify.init();
                });
            },
            delItem: function () {
                $(".tmplnoworkday_notimepart_rangtime_item_rule_delitem").unbind("click").click(function () {
                    $(this).parent().parent().remove();
                });
            },

        }
        //不分工作日->分时段
        var _noworkdayTimepartBLL = {
            bindChange: function () {

            },
            show: function (index) {
                $(".noworkday,.timepart").removeClass("layui-hide");
                if ($(".tmplnoworkday_timepart_item").length == 0) {
                    $(".timepart").append($("#tmplnoworkday_timepart").tmpl([{}]));
                    layuiForm.render("select");
                }
                _noworkdayTimepartBLL.bindAddItem();
                _selectChangeBLL.bindChange();
                myVerify.init();
            },
            bindAddItem: function () {
                $(".noworkday_timepart_additem").unbind("click").click(function () {
                    var htm = $("#tmplnoworkday_timepart").tmpl([{ index: 0 }]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("noworkday_timepart_additem", "noworkday_timepart_delitem").replace("新增时段", "删除时段")
                    $(".timepart").append(htm);
                    layuiForm.render("select");
                    _noworkdayTimepartBLL.delItem();
                });
            },
            delItem: function () {
                $(".noworkday_timepart_delitem").unbind("click").click(function () {
                    $(this).parent().parent().remove();
                });
            },

        }

        //下拉控制
        var _selectChangeBLL = {
            bindChange: function () {
                layuiForm.on("select", function (data) {
                    var val = data.value;
                    var id = data.elem.id;
                    var key = $(data.elem).attr("data-key");

                    _selectChangeBLL.show(this, key, val);
                })
            },
            show: function (obj, key, val) {
                if (key == "BillRuleTemp_IsDiffHoliday") {//是否分工作日下拉
                    _hideAll.process();
                    if (val == 1) {//分
                        $(".divBillRuleTemp_IsDiffTime,.divBillRuleTemp_IsDiffHoliday").addClass("layui-hide")
                        $(".divTopMoneySet").removeClass("layui-hide")
                        _workday_work_BLL.show();
                        _workday_holiday_BLL.show();
                    } else {
                        $(".divTopMoneySet").addClass("layui-hide")
                        $(".divBillRuleTemp_IsDiffTime,.divBillRuleTemp_IsDiffHoliday").removeClass("layui-hide");

                        //layuiForm.render("select");

                        $("#BillRuleTemp_Free").val(1);
                        $("#BillRuleTemp_IsDiffTime").val(0);
                        layuiForm.render("select");
                        _noworkdayNotimepart_spacetime_BLL.show();
                    }
                }
                else if (key == "BillRuleTemp_TopMoneySet") {////周期封顶金额下拉
                    if (val == 1) {
                        $(".divTopMoneySetItem").removeClass("layui-hide")
                        $(".divTopMoneySetItem2").addClass("layui-hide")
                    } else {
                        $(".divTopMoneySetItem").addClass("layui-hide")
                        $(".divTopMoneySetItem2").removeClass("layui-hide")
                    }
                }
                else if (key == "BillRuleTemp_TimePartTime") {//不分工作节假日 ->分时段->收费下拉
                    _hideAll.process();
                    $(".noworkday,.timepart").removeClass("layui-hide");
                    if (val == 1) {
                        $(".noworkday>.timepart>.spacetime").removeClass("layui-hide");
                       
                        var content = $(obj).closest(".tmplnoworkday_timepart_item").find(".content");
                        _workday_spacetime_BLL.show(content);
                    } else if (val == 2) {
                        $(".noworkday>.timepart>.rangtime").removeClass("layui-hide");
                        var content = $(obj).closest(".tmplnoworkday_timepart_item").find(".content");
                        _workday_rangtime_BLL.show(content);
                    } else {
                        $(".noworkday>.timepart>.numbertime").removeClass("layui-hide");
                        var content = $(obj).closest(".tmplnoworkday_timepart_item").find(".content");
                        _workday_numbertime_BLL.show(content);
                    }
                } else if (key == "BillRuleTemp_WorkTime") {//分工作节假日
                    _hideAll.process();
                    $(".workday,.workday_work,.workday_holiday").removeClass("layui-hide");
                    var content = $(obj).closest(".workday_item").find(".content");
                    if (val == 1) {
                        _workday_spacetime_BLL.show(content);
                    } else if (val == 2) {
                        _workday_rangtime_BLL.show(content);
                    } else {
                        _workday_numbertime_BLL.show(content);
                    }
                } else if (key == "BillRuleTemp_IsDiffTime") {//不分工作节假日 ->是否分时段下拉
                    _hideAll.process();
                    $("#BillRuleTemp_Free").val(1);
                    layuiForm.render("select");
                    if (val == 1) {
                        $(".divBillRuleTemp_IsDiffHoliday").addClass("layui-hide");
                        _noworkdayTimepartBLL.show();
                    } else {
                        $(".divBillRuleTemp_IsDiffHoliday").removeClass("layui-hide");
                        _noworkdayNotimepart_spacetime_BLL.show();
                    }
                } else if (key == "BillRuleTemp_Free") {//不分工作日->不分时段->收费下拉
                    _hideAll.process();
                    if (val == 3) {
                        _noworkdayNotimepart_numbertime_BLL.show();
                    } else if (val == 1) {
                        _noworkdayNotimepart_spacetime_BLL.show();
                    } else {
                        _noworkdayNotimepart_rangtime_BLL.show();
                    }
                } else if (key == "BillRuleTemp_MaxDayFree") {//是否设置最高收费
                    if (val == 2) {
                        $(".workday_header_value").removeClass("layui-hide");
                    } else {
                        $(".workday_header_value").addClass("layui-hide");
                    }
                } else if (key == "BillRuleTemp_MaxHolidayDayFree") {//是否设置最高收费
                    if (val == 2) {
                        $(".workday_holiday_header_value").removeClass("layui-hide");
                    } else {
                        $(".workday_holiday_header_value").addClass("layui-hide");
                    }
                } else if (key == "BillRuleTemp_HolidayTime") {//分工作节假日-节假日->收费下拉
                    _hideAll.process();
                    $(".workday,.workday_work,.workday_holiday").removeClass("layui-hide");
                    var content = $(obj).closest(".workday_holidayItem").find(".content");
                    if (val == 1) {
                        _workday_spacetime_BLL.show(content);
                    } else if (val == 2) {
                        _workday_rangtime_BLL.show(content);
                    } else {
                        _workday_numbertime_BLL.show(content);
                    }

                }
            }
        }

    </script>
</body>
</html>
