﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>优惠券记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>优惠券记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            
                            <div class="layui-inline">
                                <input class="layui-input " name="CouponRecord_IssueCarNo" id="CouponRecord_IssueCarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="优惠券状态" class="form-control chosen-select " id="CouponRecord_Status" name="CouponRecord_Status" lay-search>
                                    <option value="">优惠券状态</option>
                                    <option value="0">未使用</option>
                                    <option value="1">已使用</option>
                                    <option value="2">已注销</option>
                                    @*<option value="3">已过期</option>*@
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CouponRecord_UsedTime0" id="CouponRecord_UsedTime0" autocomplete="off" placeholder="消费时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CouponRecord_UsedTime1" id="CouponRecord_UsedTime1" autocomplete="off" placeholder="消费时间止" />
                            </div>
                           
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>

                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="CouponRecord_No" id="CouponRecord_No" autocomplete="off" placeholder="优惠券编号" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="优惠方式" class="form-control chosen-select " id="CouponRecord_CouponCode" name="CouponRecord_CouponCode" lay-search>
                                    <option value="">优惠方式</option>
                                    <option value="101">优惠金额</option>
                                    <option value="102">优惠时长</option>
                                    <option value="103">优惠比例</option>
                                    <option value="104">免费到指定时间</option>
                                    <option value="105">时段全免</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CouponRecord_ParkOrderNo" id="CouponRecord_ParkOrderNo" autocomplete="off" placeholder="停车订单编号" maxlength="32" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CouponRecord_MerchantName" id="CouponRecord_MerchantName" autocomplete="off" placeholder="商家名称" maxlength="50" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.CouponRecord.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete" id="Delete"><i class="fa fa-ban"></i><t>注销</t></button>{{# } }}
                                {{# if(Power.CouponRecord.Export){}}<button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcouponcode">
        {{# if(d.CouponRecord_CouponCode==0||d.CouponRecord_CouponCode==null){}}
        <span class="layui-badge layui-bg-gray">未知</span>
        {{# }else if(d.CouponRecord_CouponCode==101){}}
        <span class="layui-badge layui-bg-blue">优惠金额</span>
        {{# }else if(d.CouponRecord_CouponCode==102){}}
        <span class="layui-badge layui-bg-cyan">优惠时长</span>
        {{# }else if(d.CouponRecord_CouponCode==103){}}
        <span class="layui-badge layui-bg-orange">优惠比例</span>
        {{# }else if(d.CouponRecord_CouponCode==104){}}
        <span class="layui-badge layui-bg-green">免费到指定时间</span>
        {{# }else if(d.CouponRecord_CouponCode==105){}}
         <span class="layui-badge layui-bg-green">时段全免</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplvalue">
        {{# if(d.CouponRecord_CouponCode==null||d.CouponRecord_CouponCode==0){}}
        <span class="layui-badge layui-bg-gray"></span>
        {{# }else if(d.CouponRecord_CouponCode==101){}}
        <span>{{d.CouponRecord_Value}}元</span>
        {{# }else if(d.CouponRecord_CouponCode==102){}}
        <span>{{d.CouponRecord_Value}}分钟</span>
        {{# }else if(d.CouponRecord_CouponCode==103){}}
        <span>{{d.CouponRecord_Value}}折</span>
        {{# }else if(d.CouponRecord_CouponCode==104){}}
        <span>{{d.CouponRecord_EndTime}}</span>
        {{# }else if(d.CouponRecord_CouponCode==105){}}
        <span>{{d.CouponRecord_EndTime}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpaid">
        {{# if(d.CouponRecord_Paid!=null){}}
        <span>{{d.CouponRecord_Paid}}元</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.CouponRecord_Status ==0){}}
        <span class="layui-badge layui-bg-green">未使用</span>
        {{# }else if(d.CouponRecord_Status ==1){}}
        <span class="layui-badge layui-bg-blue">已使用</span>
        {{# }else if(d.CouponRecord_Status ==2){}}
        <span class="layui-badge layui-bg-cyan">已注销</span>
        {{# } else if( d.CouponRecord_Status == 3){}}
        <span class="layui-badge layui-bg-black">已过期</span>
        {{# } else {}}
        <span class="layui-badge layui-bg-gray">已失效</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplonline">
        {{# if(d.CouponRecord_OnLine ==0){}}
        <span class="layui-badge layui-bg-cyan">线下优惠</span>
        {{# }else if(d.CouponRecord_OnLine ==1){}}
        <span class="layui-badge layui-bg-orange">平台优惠</span>
        {{# }else if(d.CouponRecord_OnLine ==2){}}
        <span class="layui-badge layui-bg-orange">平台优惠(线下绑定)</span>
        {{# } else {}}
        <span class="layui-badge layui-bg-cyan">线下优惠</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        topBar.init();

        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'CouponRecord_ID', title: '订单ID', hide: true }
                , { field: 'CouponRecord_No', title: '优惠券编号', hide: true }
                , { field: 'CouponRecord_ParkOrderNo', title: '停车订单编号', hide: true }
                , { field: 'CouponRecord_IssueCarNo', title: '车牌号' }
                , { field: 'CouponRecord_ParkNo', title: '车场编码', hide: true }
                , { field: 'CouponRecord_CouponCode', title: '优惠方式', toolbar: "#tmplcouponcode" }
                , { field: 'CouponRecord_Value', title: '优惠额度', toolbar: "#tmplvalue" }
                , { field: 'CouponRecord_UsedTime', title: '消费时间', sort: true }
                , { field: 'CouponRecord_Paid', title: '实际抵扣金额', toolbar: "#tmplpaid", totalRow: true }
                , { field: 'CouponRecord_OnLine', title: '优惠类型', toolbar: "#tmplonline" }
                , { field: 'CouponRecord_Status', title: '状态', toolbar: "#tmplstatus" }
                , { field: 'CouponRecord_MerchantId', title: '商家ID', hide: true }
                , { field: 'CouponRecord_MerchantName', title: '商家名称' }
            ]];
        
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CouponRecord/GetCouponRecordList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);

                    if (d.code == 0) {
                        //追加总计行，需要查询时在msg中返回总计数据
                        var total = JSON.parse(d.msg);
                        if (total) {
                            var tr = $(".layui-table-total table tbody").find("tr").first();
                            tr.find('td[data-key="1-0-0"] div').text("合计");
                            $(".layui-table-total table tbody").append('<tr>' + tr.html() + '</tr>');
                            tr = $(".layui-table-total table tbody").find("tr").last();
                            tr.find('td[data-key="1-0-0"] div').text("总计").css({ "color": "red" });
                            tr.find('td[data-field="CouponRecord_Paid"] div').text(parseFloat(total.CouponRecord_Paid).toFixed(2)).css({ "color": "red" });
                        }
                    }
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data[0].CouponRecord_Status == 1) { layer.msg("已使用不允许注销"); return; }
                        if (data[0].CouponRecord_Status == 2) { layer.msg("无需重复注销"); return; }
                        var CouponRecord_No = data[0].CouponRecord_No;
                        $("#Update,#Delete").attr("disabled", true);
                        LAYER_OPEN_TYPE_0("确定注销该优惠券吗?", res => {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("Delete", { CouponRecord_No: CouponRecord_No }, function (json) {
                                if (json.success) {
                                    layer.msg("注销成功", { icon: 1, time: 1500 }, function () {
                                        window.pager.bindData();
                                    });
                                }
                                else {
                                    $("#Update,#Delete").removeAttr("disabled")
                                    layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                                }
                            });
                        }, res => {
                            $("#Update,#Delete").removeAttr("disabled")
                        })
                        break;
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                      
                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/CouponRecord/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            sortField: null,
            orderField: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                //$.post("SltCarCardTypeList", {}, function (json) {
                //    if (json.success) {
                //        json.data.forEach(function (d, i) {
                //            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                //            $("#CouponRecord_CarCardType").append(option)
                //            layuiForm.render("select");
                //        });
                //    }
                //}, "json");

                //$.post("SltPasswayList", {}, function (json) {
                //    if (json.success) {
                //        json.data.forEach(function (d, i) {
                //            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                //            $("#CouponRecord_EnterPasswayNo").append(option);
                //            $("#CouponRecord_OutPasswayNo").append(option);
                //            layuiForm.render("select");
                //        });
                //    }
                //}, "json");

                _DATE.bind(layui.laydate, ["CouponRecord_UsedTime0", "CouponRecord_UsedTime1"], { type: "datetime", range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/CouponRecord/GetCouponRecordList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("CouponRecord_IssueCarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "CouponRecord_IssueCarNo") {
                        $("#CouponRecord_IssueCarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }

        var nowDate = new Date();
        function getCouponRecordStatus(CouponRecord_EndTime, CouponRecord_Status) {
            if (CouponRecord_EndTime && CouponRecord_EndTime != "") {
                if (Date.parse(CouponRecord_EndTime.replace(/-/g, "/")) >= nowDate && CouponRecord_Status == -1) { //未领取
                    return -1;
                }
                else if (Date.parse(CouponRecord_EndTime.replace(/-/g, "/")) >= nowDate && CouponRecord_Status == 0) { //未使用
                    return 0;
                }
                else if (CouponRecord_Status == 1) { //已使用
                    return 1;
                }
                else if (CouponRecord_Status == 2) { //已注销
                    return 2;
                }
                else //已过期
                {
                    return 3;
                }
            } else {
                return null;
            }
        }
    </script>
</body>
</html>
