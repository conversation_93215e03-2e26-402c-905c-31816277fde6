﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .layui-row { margin-bottom: 15px; }
        .ParkDiscountSet_Type { display: none; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label ">优惠标题</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-minlen" data-minlen="2" maxlength="32" id="ParkDiscountSet_Name" name="ParkDiscountSet_Name" placeholder="长度限制(2~32)" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label ">优惠类型</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <select data-placeholder="优惠类型" class="layui-input v-null" id="ParkDiscountSet_Type" name="ParkDiscountSet_Type" lay-search>
                        <option value="101">优惠金额</option>
                        <option value="102">优惠时长</option>
                        <option value="103">优惠比例</option>
                        <option value="104">免费到指定时间</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div id="ParkDiscountSet_Type_101" class="ParkDiscountSet_Type">
                    <div class="layui-col-xs4 edit-label ">优惠金额(元)</div>
                    <div class="layui-col-xs6 edit-ipt-ban">
                        <input type="text" class="layui-input v-float v-null" min="0" max="999999" maxlength="6" id="ParkDiscountSet_Amount" name="ParkDiscountSet_Amount" placeholder="优惠金额(元)" />
                        <span style="color:#aaa;padding-top:5px;font-size:13px;">最小为0，最大为999999。实例：60元填60</span>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div id="ParkDiscountSet_Type_102" class="ParkDiscountSet_Type">
                    <div class="layui-col-xs4 edit-label ">优惠时长(分钟)</div>
                    <div class="layui-col-xs6 edit-ipt-ban">
                        <input type="text" class="layui-input v-number v-min v-max v-null" min="0" max="999999" maxlength="6" id="ParkDiscountSet_Duration" name="ParkDiscountSet_Duration" placeholder="优惠时长(分钟)" />
                        <span style="color:#aaa;padding-top:5px;font-size:13px;">最小为0，最大为999999。实例：60分钟填60</span>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div id="ParkDiscountSet_Type_103" class="ParkDiscountSet_Type">
                    <div class="layui-col-xs4 edit-label ">优惠比例(%)</div>
                    <div class="layui-col-xs6 edit-ipt-ban">
                        <input type="text" class="layui-input v-float v-max v-min v-null" min="0" max="9.99" maxlength="4" id="ParkDiscountSet_Ratio" name="ParkDiscountSet_Ratio" placeholder="优惠比例(%)" />
                        <span style="color:#aaa;padding-top:5px;font-size:13px;">最小为0，小于10。实例：八五折填8.5</span>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div id="ParkDiscountSet_Type_104" class="ParkDiscountSet_Type">
                    <div class="layui-col-xs4 edit-label ">免费到指定时间</div>
                    <div class="layui-col-xs6 edit-ipt-ban">
                        <input class="layui-input v-null v-submit" name="ParkDiscountSet_AppointHour" id="ParkDiscountSet_AppointHour" autocomplete="off" placeholder="免费到指定时间" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label ">使用场景</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <select data-placeholder="使用场景" class="layui-input v-null" id="ParkDiscountSet_Scene" name="ParkDiscountSet_Scene" lay-search>
                        <option value="0">只用于场内打折</option>
                        <option value="1">只用于出口打折</option>
                        <option value="2">出口与场内打折</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>


            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label">&nbsp;</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>

    <script>
        myVerify.init();
        layui.use('form', function () {
            pager.init()
        });
        $(function () {      
            var laydate = layui.laydate;
            laydate.render({ elem: '#ParkDiscountSet_AppointHour' , type: 'datetime' });
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("ParkDiscountSet_No");
        var index = parent.layer.getFrameIndex(window.name);
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layui.form.on("select", function (data) {
                    if (data.elem.id == "ParkDiscountSet_Type") {
                        var no = data.value;
                        $(".ParkDiscountSet_Type").hide();
                        $("#ParkDiscountSet_Type_" + no).show();
                    }
                })
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("GetDetail", { ParkDiscountSet_No: paramNo }, function (json) {
                        if (json.Success) {
                            if (json.Data.ParkDiscountSet_AppointHour != null) {
                                json.Data.ParkDiscountSet_AppointHour = json.Data.ParkDiscountSet_AppointHour.replace("T", " ");
                            }
                            $("#verifyCheck").fillForm(json.Data, function (data) { });

                            var no = json.Data.ParkDiscountSet_Type;
                            $(".ParkDiscountSet_Type").hide();
                            $("#ParkDiscountSet_Type_" + no).show();
                            layui.form.render("select");
                        }
                    });
                }
                else {
                    $("#ParkDiscountSet_Type_101").show();
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.ParkDiscountSet_Major = $("#ParkDiscountSet_Major").val();
                        data.ParkDiscountSet_Remark = $("#ParkDiscountSet_Remark").val();
                        return data;
                    });
                    if (param.ParkDiscountSet_Type == 104) {
                        if (new Date(new Date(param.ParkDiscountSet_AppointHour).Format("yyyy-MM-dd hh:mm:ss")) <= new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"))) {
                            layer.msg("免费到指定时间必须大于当前时间", { icon: 0, time: 1500 });
                            return;
                        }
                    }
                   

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.getJSON("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {
                        param.ParkDiscountSet_No = paramNo;
                        $.getJSON("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            },
        };
    </script>
</body>
</html>
