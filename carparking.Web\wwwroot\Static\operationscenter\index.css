﻿@media screen and (min-width:1px) and (max-width: 10000px) {
    .work dl.first dd { font-size: 3rem !important; font-weight: 700 !important; }
    .nowTime { font-size: 2rem !important; }
    .item .item-btns button { font-size: 2.5rem !important; min-width: 40px !important; }
    .space ul.total li div text { font-size: 4rem !important; }

    body { font: 2rem PingFang SC,Helvetica Neue, Helvetica, Tahoma, Arial, sans-serif !important; }
    .header .menus ul li span { position: relative; }
}

@media screen and (max-width: 992px) {
    .layui-col-md6 { width: 50%; }
}

html, body { user-select: none; }
.record { user-select: text !important; }
.monitor .layui-select-title .layui-edge { display: none; }
.layui-form-select dl { max-height: 15rem; }
.layui-table td, .layui-table th { padding: 0 0.8rem; height: 2rem; overflow: hidden; text-overflow: ellipsis; }

.layui-btn + .layui-btn { margin-left: 0; border-radius: 5px; min-width: 40px; }
td button { padding: 0 .3rem !important; height: 1.8rem !important; line-height: 1.8rem !important; }
.layui-btn-primary { border-radius: 5px; }
.layui-btn-xs { border-radius: 5px; min-width: 40px; }
.layui-layer-setwin .layui-layer-close2 { right: -10px !important; top: -10px !important; }
::-webkit-scrollbar { width: 4px; height: 4px; }
::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 5px; }
::-webkit-scrollbar-thumb { border-radius: 5px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }
.header { background-color: #5868e0; height: 5rem; display: flex; background-image: url('../img/bsheader.png'); -moz-background-size: 100% 100%; background-size: 100% 100%; }
.header .title { flex: 1; padding-left: 15px; color: #fff; overflow: hidden; word-break: break-all; }
.header .menus ul li span img { width: 1.6rem; }
.header .menus { }
.header .menus ul { }
.header .menus ul li { float: right; color: #fff; height: 5rem; line-height: 5rem; position: relative; margin-right: .5rem; padding: 2px; font-size: 1.7rem; }
.header .menus ul li:nth-child(n+3) { margin-right: 1.1rem; }
.header .menus ul li:hover { cursor: pointer; background: linear-gradient(135deg, rgb(65 73 137 / 15%) 0%, rgba(255, 255, 255, 0.08) 100%); border-radius: 3px; border: 1px solid rgb(41 22 187 / 12%); box-shadow: 0 4px 15px rgba(88, 104, 224, 0.2), 0 2px 8px rgb(2 2 18 / 15%), inset 0 1px 0 rgb(40 136 165 / 10%); user-select: none; }
.header .menus ul li:active { }
.header .menus ul li span { width: 100%; text-align: center; line-height: 4rem; }
.header .menus ul li dl { position: absolute; top: 5.05rem; background-color: #5868e0; z-index: 103; text-align: center; border-radius: 10px; padding-bottom: 1rem; padding-top: 1rem; }
.menus-bar dd { width: auto; min-width: 12rem; }
.header .menus ul li dl dd { line-height: 4vh; padding: .5rem 1rem; white-space: nowrap; }
.header .menus ul li dl dd:hover { background-color: #0094ff52; font-weight: bold; }
.content { background-color: #dfe4e6; position: absolute; top: 5rem; left: 0; right: 0; bottom: 0; }
.content .left { position: absolute; top: 0.3rem; bottom: 0.5rem; left: 0.5rem; right: 22vw; }
.content .right { position: absolute; top: 0.3rem; bottom: 0.5rem; right: 0.5rem; width: 22vw; padding: .3rem 0; border-radius: 5px; }
.item .trends { width: 100%; height: 100%; overflow: hidden; min-width: 300px; }
.item .trends-1 { height: 50%; position: relative; overflow-y: auto; }
.item .trends-pic { width: 49%; height: 100%; text-align: center; background-color: rgba(100,200,200,0.1); float: left; display: inline-block; }
.item .trends-pic img { width: 100%; height: 100%; }
.item .trends-1 .info { color: #5868e0; }
.item .trends-1 .info:first-child { padding-top: 1rem; }
.record { position: absolute; top: 50%; bottom: 0; left: 0; right: 0; padding: .3rem; min-width: 300px; }
.record .item { width: 50%; min-width: 150px; }
.right .pannel { background-color: #fafbf6; padding: 1rem; overflow: auto; user-select: none; }
.right .notice { height: 2.1vh; line-height: 2.1vh; position: relative; overflow: inherit; border-radius: 5px; margin-bottom: 0.5rem; background-color: #f7f7f7; }
.right .work { height: calc(50% - 5.75rem); border-radius: 5px; padding-top: 0; }
.right .space { margin-top: .5rem; height: calc(50% - 7.7rem); border-radius: 5px; }
.right .space .area { margin: 0.3rem; }
.right .state { margin-top: .5rem; border-radius: 5px; background-color: #f7f8fb; padding: .5rem; }
.notice .labelim { padding-top: 1px; color: #5868e0; overflow: hidden; height: 2.1vh; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; text-align: left; }
.notice .labelim:hover { cursor: pointer; text-decoration: underline; }
.notice .labelim:active { text-decoration: none; }
.work dl { clear: both; width: 100%; color: #666; line-height: 1.5rem; font-size: 1rem; }
.work dl dd { float: left; margin-bottom: 6px; color: #69717a }
.work dl.first dd { box-shadow: 0px 1px 0px #ebecef; width: calc(100% - 1rem); height: 4vh; line-height: 4vh; font-size: 2.5rem; color: #f45454; font-weight: bold; text-align: center; position: relative; border-bottom: 1px solid #f5f5f5; margin-top: 2px; margin-bottom: 4px; padding-top: 6px; padding-bottom: 1px; }
.work .moneytip { display: inline-block; position: absolute; left: 0px; font-size: 1vw; }
.work .moneytip .second { font-size: 1vw; }
.work .moneytip .second #work_0 { font-size: 1.7vw; }
.space ul.total { margin-bottom: .1rem; padding-bottom: .2rem; border-bottom: 1px solid #f5f5f5; box-shadow: 0px 1px 0px #ebecef; }
.space ul.total li { display: flex; }
.space ul.total li div { flex: 1; text-align: center; color: #69717a; }
.space ul.total li div text { float: left; width: 100%; font-size: 2rem; font-weight: bold; color: #2ea8f7; margin-top: 3px; }
.space ul.area li { display: flex; color: #666; margin-top: .2rem; padding: .3rem 0; }
.space ul.area li:hover { background-color: #f5f5f5; cursor: default; }
.space ul.area li ndiv { flex: 2; }
.space ul.area li vdiv { flex: 1; text-align: right; color: #5868e0; }
.state { display: flex; }
.state dl { flex: 1; }
.state dl dd { float: left; width: 100%; text-align: center; }
.state dl dd { height: 3rem; line-height: 3rem; font-size: 3rem; }
.state dl dd:first-child { font-size: 3rem; }
.state dl dd:last-child { font-size: 1.1rem; height: 1rem; line-height: 1rem; bottom: 0; }
.state dl.close { color: red; opacity: 1; }
.state dl.connet { color: #079dfd; font-weight: bold; }
.state dl.loading { color: #999999; }
.floatBottom { position: absolute; bottom: 0; top: 53%; background-color: #fff; left: 0; right: 0; background-color: rgba(0,0,0,0); z-index: 999; }
.floatBottom .box { height: 100%; background-color: rgb(6 101 156 / 0.5); box-shadow: 0px -6px 8px #888888; }
.way { height: 100%; width: 15.9vw; background-color: #043857; float: right; overflow: auto; border-left: 1px solid #dfe4e6; }
.way ul { width: 100%; background-color: rgba(255,255,255,.8); }
.way ul li { min-height: 4.5vh; line-height: 4.5vh; border-bottom: 1px solid rgba(0,0,0,0.1); cursor: pointer; user-select: none; white-space: normal; }
.way ul li.active { color: #fff; font-weight: bold; background-color: #1E9FFF; }
.ops { z-index: 111; height: 100%; width: calc(100% - 15.93vw - 2px); border-left: 1px solid rgba(0,0,0,0); background-color: #043857; float: left; color: #5868e0; font-size: 1rem; }
.ops ul, .ops ul li { width: 100%; height: 100%; position: relative; }
.ops ul li { position: absolute; }
.ops ul li.active { display: block; }
.ops ul li div.lt, .ops ul li div.rt { float: left; width: 50%; height: 100%; }
.ops ul li div.rt .row label { float: left; width: 5rem; line-height: 1.5rem; }
.ops ul li div.rt .row value { float: left; width: calc(100% - 5.3rem); line-height: 1.5rem; word-break: keep-all; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; }
.ops ul li div.rt .row ss { float: left; width: calc(100% - 5.3rem); }
.ops ul li div.rt .row { clear: both; margin-bottom: .1rem; padding: 0.1rem 0; }
.ops ul li div.rt .row.input label { line-height: 2.4rem; }
.ops ul li div.rt .row.input input { height: 2.4rem; }
.ops ul li div.rt .row.label { line-height: 1.5rem; }
.ops ul li .lt { display: flex; }
.ops ul li .lt dl { flex: 1; padding: .5rem; }
.ops ul li .lt dl dd.img { width: 100%; height: 100%; background-color: #043857; position: relative; }
.ops ul li .lt dl dd.img img { width: 100%; height: 100%; border-radius: 3px; }
.ops ul li .lt dl dd.img img.imgsmall { position: absolute; top: 0; right: 0; width: 8rem; height: auto; }
.ops ul li.out .lt dl dd.img { height: 33vh; box-shadow: 0 0 0px #aaa inset; position: relative; }
.ops ul li.out .lt dl dd.vlabel { overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 1; color: #fff; }
.ops .outcarno input { width: 100%; }
.ops .outcarno button { height: 38px; line-height: 1.3rem; width: 100%; text-align: center; padding: 8px 12px !important; background-color: #657de5; border-color: #999; color: #fff; border-bottom-left-radius: 0; border-top-left-radius: 0; }
.ops input.enter_carno { text-transform: uppercase; }
.ops input.outcar_carno { text-transform: uppercase; }
.monitor { position: absolute; top: 0; bottom: 50%; left: 0; right: 0; padding: .3rem; z-index: 102; }
.item { position: relative; float: left; padding: 0 1rem 0 0; height: 100%; border-radius: 4px; }
.item .item-box { background-color: #fff; padding: .5rem; overflow: auto; height: calc(100% - 1rem); border-radius: 5px; overflow-y: hidden; }
.item .item-main { width: 99.9%; height: calc(100%); overflow: auto; position: absolute; float: left; }
.item .item-header { height: 2.5rem; width: 100%; line-height: 2.5rem; float: left; background: radial-gradient(circle, #5a6be1, #7b8fff); background-color: #6673d9; position: relative; border-top-right-radius: 2px; border-top-left-radius: 2px; }
.item .passway_select { float: right; right: 2rem; position: absolute; z-index: 9999; bottom: 0px; width: 15vw; }
.item .passway_select i { }
.item .passway_select select { }
.item .passway_select:hover { background-color: rgb(255 255 255 / 60%); }
.item .passway_select input { border: 0; height: 3.5rem; line-height: 3.5rem; color: #00c71f !important; background-color: rgb(255 255 255 / 0%) !important; text-align: right; font-size: 2.2rem; padding-right: 15px; }
.item .passway_state { position: absolute; right: 0; z-index: 9999; right: 2rem; top: 5px; }
.item .passway_state span { min-width: 2.5rem; color: #fff; float: left; text-align: center; background-color: rgb(3 30 230 / 45%); margin-left: 0.1rem; border-radius: 2px; height: 2.7rem; }
.item .passway_state img { width: auto; height: auto; max-height: 3rem; max-width: 3rem; }
.item .passway_state span:hover { background-color: rgb(67 111 249 / 92%); }
.item .item-body { float: left; width: 100%; height: calc(100% - 0.8rem); overflow: hidden; }
.item .item-body .videobox { height: 100%; width: calc(100% - 2rem); text-align: center; line-height: 17rem; }
.item2 .item-body .videobox { width: calc(100% - .1rem) !important; }
.item .item-body .videobox img { width: 100%; height: 100%; }
.item .item-btns { width: 99.9%; position: absolute; bottom: .5rem; }
.item .item-btns button { margin-top: .5rem; min-width: 60px; border-radius: 5px; background-color: #5868e0; font-family: serif; font-weight: 600; }
.item .item-btns button:last-child { margin-right: 0.3rem; min-width: 5.5rem !important; }
.item .item-btns button[data-key="0"] { }
.item .item-btns button[data-key="3"] { background-color: #f45454; }
.item .item-btns button[data-key="4"] { background-color: #4fb05e; min-width: 6.5rem !important; }
.item-btns .item-btns-other { position: absolute; right: 2.5rem; bottom: .01rem; }
.item-btns .item-btns-other2 { position: absolute; z-index: 9999; max-width: 10rem; bottom: 35px; }

.outPasswayChosen { background-color: rgba(0,0,0,0.5); height: 100%; width: calc(100% - 1rem); position: absolute; top: 0; left: 0; border-radius: 2px; z-index: 999; }
.outPasswayChosen .box { position: absolute; height: 22rem; top: 5rem; left: 10%; right: 15%; background-color: #043857; border-radius: .2rem; min-width: 280px; color: #fff; }
.outPasswayChosen .box .chheader { user-select: none; cursor: default; height: 4rem; line-height: 4rem; font-size: 2rem; text-align: center; border-bottom: 1px solid #f0f0f0; background: radial-gradient(circle, #5a6be1, #7b8fff); color: #fff; min-width: 280px; }
.outPasswayChosen .box .chcontent { height: 100%; overflow: auto; background-color: #043857 }
.outPasswayChosen .box .chcontent li { padding: .5rem 0; text-align: center; line-height: 3rem; cursor: pointer; user-select: none; border-bottom: 1px solid #0094ff23; }
.outPasswayChosen .box .chcontent li { transition: box-shadow 0.2s ease; }
.outPasswayChosen .box .chcontent li:hover { background-color: #6673d9; color: #fff; box-shadow: 0 0 15px #2ea8f7; font-weight: bolder; }
.outPasswayChosen .box .chcontent li.active { background-color: #5868e0; }
.outPasswayChosen .box .chbottom { height: 2rem; }
#msgBox { position: absolute; top: 8rem; bottom: 50%; right: .5rem; width: 50rem; background-color: #fff; box-shadow: 0 0 0.3rem #5868e0; border-radius: 5px; border-top-right-radius: 0px; z-index: 9999; }
#msgBox div.close { opacity: 1; position: absolute; line-height: 1.5rem; height: 1.5rem; width: 2rem; text-align: center; top: -1.65rem; right: -.03rem; cursor: pointer; background-color: #5868e0; border: 1px solid #f5f5f5; border-bottom: 0; box-shadow: 0 0 0.3rem #5868e0; color: #fff; border-top-left-radius: 5px; border-top-right-radius: 5px; }
#msgBox div.close:hover { background-color: #202b83; }
#msgContent li { margin-bottom: 1rem; color: #5868e0; }
#msgContent li .tiptime { color: rgb(0 0 0 / 72%); }
#msgContent li:last-child { border: 0; }
.layui-checkbox-disbaled[lay-skin=primary] span { color: red; }
.enterlabel { padding: 7px 0 0; }
.entervalue { padding: 7px 0 0; }
.enterinput { width: calc(100% - 10rem); min-width: 8rem; max-width: 25rem; display: inline-block; position: absolute !important; }
.enterinput .input-group { position: absolute !important; display: table; border-collapse: separate; }
.in .mtop, .out .mtop { margin-top: .3rem; }
.in .mtop { margin-bottom: 1rem; }

.item2 { height: 50%; padding: 0 0.1rem 0 0; margin-bottom: 0.2rem; }
.item2 .item-box { padding: 0.1rem; height: calc(100% - 0.1rem); }
.item2 .item-main { height: calc(100%); }
.item2 .item-body { height: calc(100%); }
.item2 .item-body .videobox img { object-fit: fill; }
.item2 .item-body .videobox { line-height: normal; }
.item2 .item-btns { width: 85%; position: absolute; z-index: 999; bottom: 0.2rem; padding: 0.2rem; }
.item2 .item-btns button { margin-top: .5rem; margin-left: .1rem; }
.item2 .videoTitle { position: absolute; bottom: .1rem; left: .1rem; background-color: rgba(12,11,11, .5); width: 98.6%; }
.item2 .videoTitle p { text-align: justify; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; line-height: 20px; color: #fff; font-size: 12px; }

.item2 .item-btns button:last-child { float: none; margin-right: 0; }
.item2 .item-btns button[data-key="3"] { float: none; }
.item2 .item-btns button[data-key="4"] { float: none; }

.item3 { height: 100%; }
.item4 { width: 12.5% !important; }
.monitor2 { margin-right: 0.9rem; }
.monitor3 { padding-bottom: 3.5rem; padding-right: 1rem; }

.fpage { position: absolute; bottom: 0; text-align: center; left: 0; right: 0; height: 3.2rem; line-height: 3rem; }
.layui-laypage { margin: 0; background-color: #fff; padding: 0 2rem; border-radius: 20px; line-height: 3vh; height: 3vh; }
.layui-laypage a, .layui-laypage span { margin: 0; }
.layui-laypage .layui-laypage-curr .layui-laypage-em { background-color: #5868e0; }

.outWin .selcarno { width: 7rem; background-color: #043857; z-index: 999999; box-shadow: none; border: 1px solid #ffffff; padding: .5rem; min-height: 100px; min-width: 120px; margin-top: 0.1rem; position: fixed !important; left: auto; top: auto; font-size: 1.6rem; }
.outWin .selcarno2 { left: 0px; z-index: 999999; }
.outWin .selcarno dl { line-height: 3rem; }
.outWin .selcarno dl:hover { cursor: pointer; }
.outWin .selcarno a { color: #fff; height: 3rem; }
.outWin .selcarno a:hover { color: #657de5 !important; cursor: pointer !important; font-weight: 700; }
.carlist a { width: calc(100% - 1rem) !important; display: block; }
.outWin .iptcarno { }
.outWin .mtop { margin-bottom: .5rem; }
.layui-laypage a, .layui-laypage span { margin: 0; }
.carnoys { color: #5868e0; }
.iptcarno { display: inline-block; width: 80%; left: 0; }
.btncarno { display: inline-block; width: 20%; height: 38px; line-height: 1.3; position: absolute; right: 0; }
#eventMenu .total { cursor: pointer; position: absolute; background: none; border-radius: 10px; height: 1.6rem; width: 1.6rem; background: #fb0707; color: #fff; font-size: 1.3rem; line-height: 1.6rem; overflow: hidden; }
.eventpng-large { width: 2rem !important; }
.videoloading { position: absolute; width: 100%; height: 90%; text-align: center; color: rgb(255, 255, 255); display: block; background-image: url(data:image/gif;base64,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); background-repeat: no-repeat; background-position: center center; background-size: 40px 40px; }
.aDisabled { pointer-events: none; cursor: default; opacity: 0.5; }

#rlt_outimgsmall > img, #rlt_enterimgsmall > img { border-bottom-left-radius: 6px !important; }

.watermark-fu { position: relative; text-align: right; font-size: 1rem; color: #fff; font-family: 'Roboto', serif; }
.watermark-fu::after { content: "辅"; -webkit-text-stroke: 0; position: absolute; top: 1rem; right: 1px; transform: translate(-50%, -50%); }


.rigthDidplay { right: .1rem !important; }
#inparkorder-search { height: 5vh; line-height: 5vh; float: left; border-radius: 5px; margin-left: 2px; font-size: 2vh; }
div.layui-table-cell { height: 4vh !important; line-height: 4vh !important; padding: 0 5px; transition: box-shadow 0.2s ease; }
/*div.layui-table-cell a { color: #009688; background-color: #e9f1f1; }
div.layui-table-cell a:hover { background-color: #5868e0; border-color: #fff; color: #fff; }*/
div.layui-table-cell span { font-weight: bolder !important; }
input#inParkOrder_CarNo { text-transform: uppercase; transition: box-shadow 0.2s ease; border-color: #5868e0 !important; }

.color_0 { color: #5664df !important; }
.color_1 { color: #009688 !important; }
.color_2 { color: #0b57d0 !important; }
.color_3 { color: #079dfd !important; }
.color_4 { color: #d98be1 !important; }
.color_5 { color: #83b32b !important; }
.color_6 { color: #2bd569 !important; }
.color_999 { color: #f45454 !important; font-weight: 800; }

.incartitle { color: #e35151; font-weight: 900; }

.item-body .passway_select { line-height: 2rem; border-right: 1px solid rgba(255,255,255,0.1); border-bottom: solid 0px #c2c2c2; border-radius: 0.2rem; }
.layui-table td, .layui-table th { font-size: 1.8rem !important; }
.layui-btn-xs { font-size: 1.5rem; }
.enterlabel, .entervalue, .enterinput { font-size: 1.8rem; height: 3.5vh; color: #fff; }
.outlabel { margin-bottom: .5rem; }

.layui-form-checked[lay-skin=primary] i { border-color: #74b2e4cf !important; background-color: #7a95dec7; color: #d93e57; font-size: 2rem; font-weight: 700; }

.docfb-popup__btn { width: 15.93vw; height: 40px; position: absolute; background: #043857; color: #6091d7; border-radius: 5px 5px 0 0; cursor: pointer; user-select: none; text-align: center; top: -40px; z-index: 9999; right: 0; border: 0; -webkit-box-shadow: 0px -6px 8px #888888; box-shadow: 0px -6px 8px #888888; }
.docfb-popup__btn:after { display: block; position: absolute; left: 50%; top: 50%; margin-left: -8px; margin-top: -8px; width: 16px; height: 16px; }
.docfb-popup__btn i { color: #fff; }

.layui-row pannel .state { padding: 0; line-height: 5rem; }
.picright { float: right !important; }

.layui-form-select .layui-anim { top: auto; bottom: 100%; margin-bottom: 4px; color: #000; }
.layui-btn.out { font-weight: bolder; margin-right: 10px; }
.layui-btn.out.layui-border-blue { background: 0 0; }
.layui-btn.in { font-weight: bolder; margin-right: 10px; }
.layui-btn.int.layui-border-blue { background: 0 0; }


.layui-select-disabled .layui-select-title input.layui-disabled { color: #5868e0 !important; border: none; }
.layui-select-disabled .layui-select-title i.layui-edge { border: none; }

.layui-form .layui-table-page { height: 4vh; line-height: 4vh; padding: 0; }
.layui-table-page .layui-laypage a, .layui-table-page .layui-laypage span { height: 3vh !important; line-height: 3vh !important; margin-bottom: 0px; border: none; background: 0 0; }
.layui-card-body .layui-form.layui-border-box.layui-table-view { height: 100%; }
.layui-card-body .layui-form.layui-border-box.layui-table-view .layui-table-box { height: calc(100% - 4vh); }
.layui-card-body .layui-form.layui-border-box.layui-table-view .layui-table-box .layui-table-header { height: 18%; }
.layui-card-body .layui-form.layui-border-box.layui-table-view .layui-table-box .layui-table-header table { height: 100%; }
.layui-card-body .layui-form.layui-border-box.layui-table-view .layui-table-box .layui-table-body { height: 82% !important; }
.layui-card-body .layui-form.layui-border-box.layui-table-view .layui-table-box .layui-table-body table { height: 100%; }
.layui-card-body .layui-form.layui-border-box.layui-table-view .layui-table-box .layui-table-fixed.layui-table-fixed-r { height: calc(100% - 4px); }
.layui-table-tips-main { font-size: 2vh; height: 3vh; line-height: 3vh; }



.outcar_free, #selFree .layui-input { border-radius: 0 !important; border-style: none !important; }
.landdd { text-align: left; }

.carnoys:hover { color: #233cf5; cursor: pointer; }

xm-select * { font-size: 1vw !important; }
.time-tips > .layui-layer-content { white-space: nowrap; font-size: 1.3vw !important; }
.custom-tips .layui-layer-tips i.layui-layer-TipsB, .layui-layer-tips i.layui-layer-TipsT { left: 5vw !important; }
.layui-form-checkbox[lay-skin=primary] span { color: #f45454 !important; height: 2.1vh; line-height: 2.1vh; }
.layui-form-checkbox[lay-skin=primary]:hover i { border-color: #f45454; }
.layui-table-body .layui-none { position: absolute; top: 0; left: 40%; }

input#inParkOrder_CarNo:hover { background-color: #f0f8ff; border-color: #007bff; }
input#inParkOrder_CarNo::placeholder { color: #888; }

.layui-col-md-5 { width: 20% !important; }
.layui-col-md-7 { width: 14.285% !important; }

/* 面板式车道监控下拉菜单样式 */
.barList { position: absolute; top: 100%; left: -5.48vw; max-height: 420px; overflow-y: auto; background: linear-gradient(101deg, #5868e0 0%, #4750c1 25%, #4454c5 50%, #5868e0 75%, #29337d 100%); border-radius: 16px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 0 8px 32px rgba(83, 52, 131, 0.2), inset 0 0px 0 rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px) saturate(180%); border: 1px solid rgba(255, 255, 255, 0.08); z-index: 1000; padding: 16px; margin-top: 9.1px; min-width: 320px; position: relative; }
.barList::before { content: ''; position: absolute; top: -13px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 10px solid #0f0f23; filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.3)); }

.barList::after { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%); pointer-events: none; border-radius: 16px; }
.barList dd { margin: 0; padding: 14px 18px; border-radius: 12px; margin-bottom: 8px; background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%); color: #e8eaed; cursor: pointer; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); font-size: 1.6rem; font-weight: 500; display: flex; align-items: center; justify-content: space-between; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; position: relative; backdrop-filter: blur(10px); }
.barList dd:hover { background: linear-gradient(135deg, rgba(120, 119, 198, 0.15) 0%, rgba(255, 119, 198, 0.08) 100%); border-color: rgba(120, 119, 198, 0.3); transform: translateY(-2px) scale(1.02); box-shadow: 0 8px 25px rgba(120, 119, 198, 0.2), 0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1); }
.barList dd.checkAll { background: linear-gradient(135deg, #00d4aa 0%, #00b894 50%, #00a085 100%); border-color: #00d4aa; font-weight: 600; margin-bottom: 16px; box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
.barList dd.checkAll:hover { background: linear-gradient(135deg, #00e6c3 0%, #00d4aa 50%, #00b894 100%); transform: translateY(-2px) scale(1.02); box-shadow: 0 8px 25px rgba(0, 212, 170, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3); }
.barList dd.checkNotAll { background: linear-gradient(135deg, #dba558 0%, #ec971f 50%, #d18920 100%); border-color: #dfa758; box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
.barList dd.checkNotAll:hover { background: linear-gradient(135deg, #fbc06b 0%, #ffaf40 50%, #ff9d0e 100%); transform: translateY(-2px) scale(1.02); box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4), 0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3); }
.barList dd.land { position: relative; }
.barList dd.land .landi { color: #00d4aa; font-size: 16px; font-weight: bold; margin-right: 8px; text-shadow: 0 0 8px rgba(0, 212, 170, 0.6); filter: drop-shadow(0 2px 4px rgba(0, 212, 170, 0.3)); }
.barList dd.land .landi:not(.layui-hide) { display: inline-block; }
.barList dd.land .landi.layui-hide { display: none !important; }
.barList dd:last-child { margin-bottom: 0; }

/* 车道监控菜单项样式优化 */
#LanMenu { position: relative; }

#LanMenu:hover .barList { display: block !important; }

.barList t { left: 0; }
/* 车道名称和状态样式 */
.barList .lane-name { flex: 1; font-weight: 500; color: #e8eaed; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); }
.barList .lane-status { align-items: center; gap: 10px; position: absolute; right: 5px; }
.barList .lane-type { font-size: 11px; padding: 4px 10px; border-radius: 16px; background: linear-gradient(135deg, rgb(1 1 18 / 20%) 0%, rgb(75 10 47 / 15%) 100%); color: #ffffff; font-weight: 600; border: 1px solid rgba(120, 119, 198, 0.3); text-transform: uppercase; letter-spacing: 0.5px; box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1); }
/* 滚动条样式 */
.barList::-webkit-scrollbar { width: 5px; }
.barList::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.05); border-radius: 16px; }
.barList::-webkit-scrollbar-thumb { background: linear-gradient(135deg, rgba(120, 119, 198, 0.4) 0%, rgba(255, 119, 198, 0.3) 100%); border-radius: 16px; border: 1px solid rgba(255, 255, 255, 0.1); }
.barList::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, rgba(120, 119, 198, 0.6) 0%, rgba(255, 119, 198, 0.5) 100%); }

/* 车道项选中状态 */
.barList dd.land.selected { background: linear-gradient(135deg, rgba(0, 212, 170, 0.2) 0%, rgba(0, 184, 148, 0.15) 100%); border-color: #00d4aa; box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1); }
.barList dd.land.selected:hover { background: linear-gradient(135deg, rgba(0, 212, 170, 0.25) 0%, rgba(0, 184, 148, 0.2) 100%); transform: translateY(-2px) scale(1.02); box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3), 0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
/* 空状态样式 */
.barList dd.empty-state { text-align: center; color: rgba(255, 255, 255, 0.5); font-style: italic; background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%); border: 2px dashed rgba(255, 255, 255, 0.1); border-radius: 12px; padding: 20px 18px; font-size: 13px; }
.barList dd.empty-state:hover { background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%); border-color: rgba(255, 255, 255, 0.15); color: rgba(255, 255, 255, 0.7); }

/* 微妙的光效和质感 */
.barList dd::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%); border-radius: 12px 12px 0 0; }

.barList dd.checkAll::before,
.barList dd.checkNotAll::before { background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%); }

/* 增强按钮图标样式 */
.barList dd.checkAll .lane-status i,
.barList dd.checkNotAll .lane-status i { font-size: 16px; font-weight: bold; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); }
.barList dd.checkAll .lane-status i { color: #ffffff; filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3)); }
.barList dd.checkNotAll .lane-status i { color: #ffffff; filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3)); }

/* 菜单下拉面板样式 - moreMenu */
#moreMenu .menus-bar { position: absolute; top: 100%; right: -1.95vw !important; overflow-y: auto; background: linear-gradient(101deg, #5868e0 0%, #4750c1 25%, #4454c5 50%, #5868e0 75%, #29337d 100%); border-radius: 16px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5), 0 8px 32px rgba(83, 52, 131, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.12); backdrop-filter: blur(20px) saturate(180%); border: 1px solid rgba(255, 255, 255, 0.1); z-index: 1000; padding: 16px; margin-top: 5.5px; min-width: 180px; width: 180px; overflow: hidden; }
#moreMenu .menus-bar::before { content: ''; position: absolute; top: 0px; right: -1.95vw !important; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 10px solid #1a1a2e; filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.4)); }
#moreMenu .menus-bar::after { content: ''; position: absolute; top: 0; right: -1.95vw !important; bottom: 0; background: radial-gradient(circle at 30% 70%, rgba(255, 119, 198, 0.08) 0%, transparent 50%), radial-gradient(circle at 70% 30%, rgba(120, 119, 255, 0.06) 0%, transparent 50%), radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.03) 0%, transparent 50%); pointer-events: none; border-radius: 16px; }
/* moreMenu 菜单项样式 */
#moreMenu .menus-bar dd { margin: 0; border-radius: 12px; margin-bottom: 8px; background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.03) 100%); color: #f0f2f5; cursor: pointer; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); font-size: 1.65rem; font-weight: 500; display: flex; align-items: center; justify-content: center; white-space: nowrap; position: relative; backdrop-filter: blur(10px); text-align: center; }
#moreMenu .menus-bar dd:hover { background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 119, 198, 0.1) 100%); border-color: rgba(255, 215, 0, 0.3); transform: translateY(-2px) scale(1.02); box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2), 0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15); color: #ffffff; }
#moreMenu .menus-bar dd:last-child { margin-bottom: 0; }
/* moreMenu 菜单项顶部高光效果 */
#moreMenu .menus-bar dd::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.12) 50%, transparent 100%); border-radius: 12px 12px 0 0; }
/* moreMenu 菜单项居中显示和统一风格 */
#moreMenu { position: relative; }
#moreMenu:hover .menus-bar { display: block !important; }

/* 特殊菜单项样式 */
#moreMenu .menus-bar dd[data-key="0"] { background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 184, 148, 0.08) 100%); border-color: rgba(0, 212, 170, 0.2); }
#moreMenu .menus-bar dd[data-key="0"]:hover { background: linear-gradient(135deg, rgba(0, 212, 170, 0.2) 0%, rgba(0, 184, 148, 0.15) 100%); border-color: rgba(0, 212, 170, 0.4); box-shadow: 0 8px 25px rgba(0, 212, 170, 0.2), 0 4px 12px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15); }
.barList .lane-status span.outtype { background: linear-gradient(135deg, rgb(51 225 186 / 24%) 0%, rgb(62 205 80 / 18%) 100%); }

.custom-tip-box { position: relative; top: 0; background: #4fb05e; color: white; padding: 8px 16px; font-size: 18px; border-radius: 4px; display: inline-block; }
.custom-tip-box-nothing { position: relative; top: 0; background: #FF5722; color: white; padding: 8px 16px; font-size: 18px; border-radius: 4px; display: inline-block; }

@keyframes rotateIcon {
    0% { transform: rotate(0deg); }
    20% { transform: rotate(-30deg); }
    50% { transform: rotate(30deg); }
    80% { transform: rotate(-20deg); }
    100% { transform: rotate(0deg); }
}

.ttubiao.spin-once { display: inline-block; animation: rotateIcon 1s ease-in-out 1; }
