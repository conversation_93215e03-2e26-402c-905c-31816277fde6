﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑车辆信息</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">车主姓名</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-null  v-nameSpecial" id="Owner_Name" name="Owner_Name" maxlength="50" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-sm2 edit-label ">车主住址</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Owner_Address" name="Owner_Address" maxlength="200" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">身份证号</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Owner_IDCard" name="Owner_IDCard" maxlength="50" />
                </div>
                <div class="layui-col-sm2 edit-label ">驾驶证号</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Owner_License" name="Owner_License" maxlength="50" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">手机号码</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-phone v-null" id="Owner_Phone" name="Owner_Phone" maxlength="20" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-sm2 edit-label ">电子邮箱</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-email" id="Owner_Email" name="Owner_Email" maxlength="80" />
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-sm4 edit-label">&nbsp;</div>
                <div class="layui-col-sm6 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-card-body">
        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

        <script type="text/html" id="toolbar_btns">
            <div class="layui-btn-container">
                <span class="layui-btn layui-btn-sm" style="color: black; background-color: #ffffff !important; font-size: 14px; font-weight: 600; margin-left: -15px;">绑定车牌</span>
            </div>
        </script>

    </div>


    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>

    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script type="text/html" id="TmplOnLine">
        {{# if(d.Car_OnLine==1){ }}
        <span class="layui-badge layui-bg-blue ">启用</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">禁用</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplStatus">
        {{# if(d.Car_Status==1){ }}
        <span class="layui-badge layui-bg-blue ">正常</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">停用</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplCategory">
        {{# if(d.CarCardType_Category==1){ }}
        <span class="layui-badge layui-bg-blue ">{{d.CarCardType_Name}}</span>
        {{# }else if(d.CarCardType_Category==2){ }}
        <span class="layui-badge layui-bg-orange ">{{d.CarCardType_Name}}</span>
        {{# }else if(d.CarCardType_Category==3){ }}
        <span class="layui-badge layui-bg-red ">{{d.CarCardType_Name}}</span>
        {{# }else if(d.CarCardType_Category==4){ }}
        <span class="layui-badge layui-bg-green ">{{d.CarCardType_Name}}</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-black ">{{d.CarCardType_Name}}</span>
        {{# } }}
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramOwnerNo = $.getUrlParam("Owner_No");

        myVerify.init();

        var comtable = null;
        var layuiForm = null;
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'table', 'jquery', 'form'], function () {


            var admin = layui.admin, table = layui.table, layuiForm = layui.form;
            layuiForm.render("select")
            var conditionParam = { Car_OwnerNo: paramOwnerNo };

            var cols = [[
                { type: 'radio', hide: true }
                , { field: 'Car_CarNo', width: 140, title: '车牌号' }
                , { field: 'CarCardType_Name', title: '车牌类型', toolbar: "#TmplCategory" }
                , { field: 'CarType_Name', title: '车牌颜色' }
                , { field: 'Car_Balance', title: '账户余额' }
                , { field: 'Car_EndTime', title: '有效期' }
                , { field: 'Car_Status', title: '车牌状态', toolbar: "#TmplStatus" }
                , { field: 'Car_OnLine', title: '在线充值', toolbar: "#TmplOnLine" }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Owner/GetCarExtList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });


            pager.init()
        });
    </script>
    <script>

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("/Owner/GetOwner", { Owner_No: paramOwnerNo }, function (json) {
                        if (json.Success) {
                            json.Data["Owner_Pwd"] = "      ";//后台不会返回密码,所以显示个假密码用于文本框js校验
                            $("#verifyCheck").fillForm(json.Data, function (data) { });
                        }
                    });
                } else if (paramAct == "Add") {

                }
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });

                    $("#Save").attr("disabled", true);

                    if (paramAct == "Add") {
                        $.getJSON("/Owner/AddOwner", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {

                        param.Owner_No = paramOwnerNo;
                        $.getJSON("/Owner/UpdateOwner", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            },
        };


    </script>
</body>
</html>
