﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>自助缴费明细</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input{width:182px;}
        .layui-tab-title { background-color: #5868e0 !important; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>    
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var paramOrderNo = $.getUrlParam("orderNo");
        var comtable = null;
        layui.use(['table', 'form'], function () {
            var table = layui.table;

            //var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            var conditionParam = { Paymethod_OrderNo: paramOrderNo  };

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Paymethod_No', title: '记录编号', hide: true  }
                , { field: 'Paymethod_OrderNo', title: '停车订单编号', hide: true  }
                , { field: 'Paymethod_CarNo', title: '车牌号' }
                , {
                    field: 'Paymethod_Mode', title: '缴费方式', hide: true, templet: function (d) {
                        if (d.Paymethod_Mode == 1) return tempBar(1, "现金支付");
                        else if (d.Paymethod_Mode == 2) return tempBar(2, "电子支付");
                        else return tempBar(4, "未知");
                    }
                }
                , { field: 'Paymethod_Money', title: '缴费金额' }
                , { field: 'Paymethod_YsMoney', title: '应收金额' }
                , { field: 'Paymethod_ZlMoney', title: '找零金额' }
                , { field: 'Paymethod_DeviceNo', title: '缴费机编号', hide: true  }
                , { field: 'Paymethod_DeviceName', title: '缴费机名称', hide: true  }
                , { field: 'Paymethod_Time', title: '缴费时间' }
                , {
                    field: 'Paymethod_Status', title: '状态', templet: function (d) {
                        if (d.Paymethod_Status == 0) return tempBar(2, "已预缴");
                        else if (d.Paymethod_Status == 1) return tempBar(1, "已结算");
                        else if (d.Paymethod_Status == 2) return tempBar(3, "已退回");
                        else if (d.Paymethod_Status == 3) return tempBar(0, "已失败");
                        else return tempBar(4, "未知");
                    }
                }
                , { field: 'Paymethod_Remark', title: '备注' }
                , { field: 'Paymethod_Account', title: '操作员', hide: true }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetCashDetailList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function () {

                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                
            });

            tb_row_checkbox();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {

            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index) {
                layer.closeAll();
               
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
