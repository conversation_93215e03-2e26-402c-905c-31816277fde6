﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装向导</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <style>
        @@media screen and (max-width: 400px) {
            main { width: 80% !important; margin-left: 10%; }

            main form { right: auto !important; width: 100%; }

            .title { margin: 2rem !important; font-size: 1.5rem !important; }

            .footer { display: none !important; }
        }

        html, body { margin: 0; padding: 0; height: 100%; width: 100%; min-width: 1024px !important; overflow: hidden; font-size: 13px; }

        body { font-family: 'Microsoft YaHei'; background-image: url(../Static/img/login-bg.png); background-size: 100% 100%; }

        .footer { position: absolute; line-height: 50px; bottom: 0; width: 100%; text-align: center; color: #fff; background-color: rgba(0,0,0,0.1); }

        .title { margin: 50px 0 0 50px; font-size: 3rem; color: #fff; font-family: sans-serif,'Microsoft YaHei'; }

        .title .logo { float: left; }

        .title .logo img { height: 60px; }

        .title text { margin: 0 15px; line-height: 60px; }

        ::-webkit-input-placeholder { color: #ddd; }

        ::-moz-placeholder { color: #ddd; }

        :-ms-input-placeholder { color: #ddd; }

        input:-webkit-autofill,
        textarea:-webkit-autofill,
        select:-webkit-autofill { -webkit-text-fill-color: #fff !important; -webkit-box-shadow: 0 0 0px 1000px transparent inset !important; background-color: transparent; background-image: none; transition: background-color 50000s ease-in-out 0s; }

        input { background-color: transparent; }

        .dldisplay { display: block !important; }
    </style>

    <link href="~/Static/js/ui.roboto/style.css" rel="stylesheet" />
    <style>
        .form { position: absolute !important; left: calc(50% - 180px); top: calc(50% - 203px); width: 360px; height: 354px; }

        .table { width: 400px; height: 350px; margin: 80px auto; }

        .table form { width: 100%; }

        .table .name { width: 280px; margin: 20px auto 30px auto; display: block; height: 30px; border-radius: 20px; border: none; background: rgba(0,0,0,0.2); text-indent: 0.5em; }

        .table .btn { width: 100px; height: 30px; background: rgba(0,0,0,0.1); border-radius: 8px; border: none; color: white; margin: 0 auto; display: block; }

        .styled-button .styled-button__text { color: #fff; }

        .form__content { width: 277px; }
        #PcIpLsit dd:hover { background-color: #5FB878; color: #fff; }

        .dawd { margin-top: -9px !important; -webkit-transform: rotate( 180deg ); transform: rotate( 180deg ); }
    </style>
</head>
<body id="loginBody">
    <div class="title">
        <span class="logo"><img src="~/Static/img/icon/icon_logo.svg" /></span>
        <text id="PlatformName">安装向导</text>
    </div>
    <div class="form">
        <div class="form__cover"></div>
        <div class="layui-form form__content PreviousStep">
            <h1>站点服务</h1>
            <div class="styled-input">
                <div class="layui-form-select">
                    <div class="layui-select-title">
                        <input type="text" placeholder="请选择" autocomplete="off" id="HostService_PcIp" name="HostService_PcIp" value="@ViewBag.WebIP" class="layui-input"><i class="layui-edge" id="ipdisplay"></i>
                    </div>
                    <dl class="layui-anim layui-anim-upbit" id="PcIpLsit">
                    </dl>
                    <script type="text/x-jquery-tmpl" id="Tmpl_PcIpLsit">
                        <dd lay-value="${ip}">${ip}</dd>
                    </script>
                </div>
            </div>
            <div class="styled-input">
                <input type="text" class="styled-input__input" autocomplete="off" id="HostService_PcPort" value="7701" placeholder="本主机站点服务端口(比如：7701等)">
            </div>
            <button type="button" class="styled-button" id="btnNextStep">
                <span class="styled-button__text">下一步</span>
            </button>
        </div>
        <div class="layui-form form__content NextStep layui-hide">
            <h1>选择岗亭</h1>
            <div class="styled-input">
                <select data-placeholder="岗亭名称" class="form-control chosen-select" id="SentryHost_No" name="SentryHost_No" lay-search>
                </select>
                <script type="text/x-jquery-tmpl" id="Tmpl_SentryHostLsit">
                    <option value="${SentryHost_No}" data-ip="${SentryHost_IP}" data-port="${SentryHost_BSPort}">${SentryHost_Name}</option>
                </script>
            </div>
            <button type="button" class="styled-button" id="btnPreviousStep">
                <span class="styled-button__text">上一步</span>
            </button>
            <button type="button" class="styled-button" id="btnSevre">
                <span class="styled-button__text">下一步</span>
            </button>
        </div>
    </div>
    <div class="footer">
        <span style="float:right;margin-right:20px;" data-lan="lg_foot_tip">为了更好的体验效果 推荐使用谷歌浏览器</span>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script src="~/Static/js/ui.roboto/index.js" asp-append-version="true"></script>
    <script>

        layui.use(['laydate', 'form'], function () {
            laydate = layui.laydate;
            layform = layui.form;
        });

        $("#btnNextStep").click(function () {
            var pcip = $("#HostService_PcIp").val();
            var pcport = $("#HostService_PcPort").val();
            var index = layer.msg("初始化中...", { icon: 16, time: 0 });
            $.getJSON("/Login/GetSentryHostList", { ip: pcip, striport: pcport }, function (json) {
                if (json.Success) {
                    $("#SentryHost_No").html($("#Tmpl_SentryHostLsit").tmpl(json.Data));
                    layui.form.render('select');
                    $(".NextStep").removeClass("layui-hide");
                    $(".PreviousStep").addClass("layui-hide");
                    layer.close(index)
                } else {
                    if (json.Data) {
                        window.open(json.Data);
                    }
                    layer.msg(json.Message)
                }
            });
        })

        $("#btnPreviousStep").click(function () {
            $(".NextStep").addClass("layui-hide");
            $(".PreviousStep").removeClass("layui-hide");
        })

        $("#btnSevre").click(function () {
            var ip = $("#SentryHost_No").find("option:selected").attr("data-ip");
            var no = $("#SentryHost_No").find("option:selected").val();
            var port = $("#SentryHost_No").find("option:selected").attr("data-port");
            var webip = $("#HostService_PcIp").val();
            var webport = $("#HostService_PcPort").val();
            layer.msg("初始化中...", { icon: 16, time: 0 });
            $.post("/Login/SetSentryHost", { webIp: webip, webPort: webport, SentryHost_IP: ip, SentryHost_No: no, SentryHost_BSPort: port },
                function (json) {
                    if (json.Success) {
                        layer.msg(json.Message, { icon: 1, time: 3000 }, function () {
                            var host = window.location.host;
                            if (host.indexOf(":") != -1) {
                                host = ip + ":" + port;
                            }
                            location.href = "http://" + host;
                        });
                    } else {
                        layer.msg(json.Message)
                    }
                }, "json");
        })

        $(function () {
            GetPcIpList();
        })


        $("#HostService_PcIp").focus(function () {
            $("#PcIpLsit").addClass("dldisplay");
            $("#ipdisplay").addClass("dawd");
        })

        $(document).on("mouseover", "#PcIpLsit dd", function () {
            $("#HostService_PcIp").val($(this).html());
        });


        $("#HostService_PcIp").blur(function () {
            $("#PcIpLsit").removeClass("dldisplay");
            $("#ipdisplay").removeClass("dawd");
        })






        function GetPcIpList() {
            $.getJSON("/Login/GetAllIp4", {}, function (json) {
                if (json.Success) {
                    $("#PcIpLsit").html($("#Tmpl_PcIpLsit").tmpl(json.Data));
                    layui.form.render('select');
                } else {
                    if (json.Data) {
                        window.open(json.Data);
                    }
                    layer.msg(json.Message)
                }
            });
        }

    </script>
</body>
</html>
