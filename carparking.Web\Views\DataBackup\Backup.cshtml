﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        #SysFileInfo_Remark { white-space: pre-wrap; height: 260px !important; user-select: text; overflow: auto; }
        .red-text { color: red; }
        .parkmsg { margin-bottom: 10px; }
        .parkrow { padding-left: 60px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form layui-form-pane">
            <div class="layui-row form-group"></div>
            <div class="layui-form-item">
                <label class="layui-form-label">备份类型</label>
                <div class="layui-input-block">
                    <div class="btnCombox" id="SysFileInfo_Type">
                        <ul class="flex" style="height: 38px;">
                            <li data-value="1004">全库备份</li>
                            <li data-value="1005">基础资料</li>
                            <li data-value="1006" class="select">车辆信息</li>
                            <li data-value="1007">停车订单</li>
                        </ul>
                    </div>
                </div>
                @*<div class="layui-form-mid layui-word-aux" style="padding:0 !important;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请务必填写正确的文件路径，例如：D:\parkingT30\DBBackup\20230413</div>*@
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">数据备份执行结果</label>
                <div class="layui-input-block">
                    <div placeholder="数据备份执行结果" contenteditable="true" class="layui-textarea" rows="12" id="SysFileInfo_Remark"></div>
                </div>
            </div>
            <div class="layui-form-item" style="text-align: center;">
                <button class="btn btn-primary" id="Save" style="padding: 9px 20px;"> <t>立即执行</t></button>
                <button class="btn btn-warning" id="Cancel" style="padding: 9px 20px;"> <t>取消</t></button>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1" asp-append-version="true"></script>
    <script>
        myVerify.init();
        layui.use('form', function () {
            pager.init();
        });

        var config = {
            SysFileInfo_Type: "1006",
        };
        var onEventCombox = function (idName) {
            if (idName == "SysFileInfo_Type") {

            }
        }
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var isMsgShown = false;
        var pager = {
            loading: "",
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {

                    LAYER_OPEN_TYPE_0("备份数据将会占用系统资源，可能会导致系统卡顿甚至服务重启，确定吗?", res => {
                        layer.msg("处理中", { icon: 16, time: 0 });
                        $("#Save").attr("disabled", true);
                        $("#Cancel").attr("disabled", true);
                        $.getJSON("/DataBackup/HandBackup", { typecode: config.SysFileInfo_Type }, function (json) {
                            if (json.success) {
                                if (json.data == 2) {
                                    layer.msg("已有文件正在执行数据操作，请稍候再运行备份数据", { icon: 1, time: 1500 }, function () {
                                    });
                                } else {
                                    layer.msg("开始执行", { icon: 1, time: 1000 }, function () {
                                    });
                                }
                                pager.GetRunResult();
                            } else {
                                layer.msg(json.msg, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                                $("#Cancel").removeAttr("disabled");
                            }
                        });
                    }, res => {
                        $("#Save").removeAttr("disabled");
                        $("#Cancel").removeAttr("disabled");
                        layer.closeAll();
                    })
                });

                $(".btnCombox ul li").click(function () {
                    if ($(this).hasClass("select")) return;
                    var idName = $(this).parent().parent().attr("id");
                    //if (!onDisabledCom(idName)) { return; }
                    $(this).siblings().removeClass("select");
                    $(this).addClass("select");
                    config[idName] = $(this).attr("data-value");

                    onEventCombox(idName);
                });
            },
            GetRunResult: function () {
                var timer = setInterval(function () {
                    $.post("/DataBackup/GetRunResult", {}, function (json) {
                        if (json != null && json.success) {
                            const myString = json.data;

                            // 将字符串以 # 分割为一个数组
                            const myArray = myString.split('#');
                            myArray.reverse();

                            // 获取到 textbox 元素
                            const myTextBox = document.getElementById('SysFileInfo_Remark');
                            if (pager.loading.length > 36) pager.loading = ".";
                            else pager.loading += ".";
                            myTextBox.innerHTML = pager.loading;

                            // 循环遍历数组，逐行写入到 textbox 中
                            myArray.forEach(item => {
                                if (item.includes("Fail*") || item.includes("Exit*")) item = '<span style="color:red;" >' + item + ' </span>';
                                myTextBox.innerHTML += item + '\n\n';
                            });

                            if (myString.includes("Success*")) {
                                clearInterval(timer);
                                layer.msg("数据备份成功", { icon: 1, time: 1500 }, function () {
                                    $("#Save").removeAttr("disabled");
                                    $("#Cancel").removeAttr("disabled");
                                    $.post("/DataBackup/DelRunResult", {}, function (json) { });
                                    parent.pager.bindData(1, false);
                                });
                            }

                            if (myString.includes("Exit*")) {
                                clearInterval(timer);
                                layer.msg("数据备份失败", { icon: 2, time: 1500 }, function () {
                                    $("#Save").removeAttr("disabled");
                                    $("#Cancel").removeAttr("disabled");
                                    $.post("/DataBackup/DelRunResult", {}, function (json) { });
                                });
                            }

                        } else {
                            clearInterval(timer);
                            if (!isMsgShown) {
                                isMsgShown = true;
                                layer.msg("请求错误,重新发起请求", { icon: 0, btn: ['确定'], time: 0, end: function () { isMsgShown = false; pager.GetRunResult(); } });
                            }
                        }
                    }).fail(function (error) {
                        clearInterval(timer);
                        if (!isMsgShown) {
                            isMsgShown = true;
                            layer.msg("请求失败,重新发起请求", { icon: 0, btn: ['确定'], time: 0, end: function () {isMsgShown = false; pager.GetRunResult(); } });
                        }
                    });
                }, 1000);
            }

        };
    </script>
</body>
</html>
