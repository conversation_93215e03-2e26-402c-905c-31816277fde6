﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增车辆</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?1" rel="stylesheet" />
    <style>
        .layui-card { box-shadow: none; }
        .layui-col-sm2 { overflow: hidden; }
        .layui-card-body > .layui-row { margin-top:10px; }
        .layui-input.after { width:calc(100% - 100px);float:left; }
        .layui-btn.after { width: 50px; padding: 0px; margin: 0 !important; float: left; border: 0; border-radius: 0; }
        .layui-btn.after:last-child { border-left: 1px solid #fff !important; }

    </style>
</head>
<body>
    <div class="layui-content layui-form" id="verifyCheck">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>车牌号</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-submit v-carno" maxlength="8" id="Car_CarNo" name="Car_CarNo" autocomplete="off"  value='@ViewBag.CarPrefix'/>
                    </div>
                    <div class="layui-col-sm1 red-mark">*</div>
                    <div class="layui-col-sm2 edit-label"><label>下发白名单</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <div class="btnCombox" id="Car_EnableOffline">
                            <ul>
                                <li data-value="1" class="select">启用</li>
                                <li data-value="0">禁用</li>
                            </ul>
                        </div>
                        <div class="divInLine" style="position:relative;"><i class="help-btn" data-key="Car_EnableOffline">?</i></div>
                    </div>

                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>系统车位号</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input after v-null v-numen v-submit" maxlength="8" id="Owner_Space" name="Owner_Space" autocomplete="off" />
                        <button class="layui-btn after" id="CreateSpace">生成</button>
                        <button class="layui-btn after" id="SelectSpace">选择</button>
                    </div>
                    <div class="layui-col-sm1 red-mark">*</div>
                    <div class="layui-col-xs2 edit-label ">车场车位号</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_ParkSpace" name="Owner_ParkSpace" maxlength="32" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>车牌颜色</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <select class="layui-select v-null" id="Car_VehicleTypeNo" name="Car_VehicleTypeNo" lay-search>
                            <option value="">车牌颜色</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-sm2 edit-label"><label>车牌类型</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <select class="layui-select v-null" id="Car_TypeNo" name="Car_TypeNo" lay-search lay-filter="Car_TypeNo">
                            <option value="">车牌类型</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>车辆颜色</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Car_Colour" name="Car_Colour" autocomplete="off" maxlength="20" />
                    </div>
                    <div class="layui-col-sm2 edit-label"><label>车辆品牌</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Car_Model" name="Car_Model" autocomplete="off" maxlength="30" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>行驶证号</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Car_License" name="Car_License" autocomplete="off" maxlength="30" />
                    </div>
                    <div class="layui-col-sm2 edit-label"><label>卡号</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Car_CardNo" name="Car_CardNo" autocomplete="off" maxlength="30" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>备注</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Car_Remark" name="Car_Remark" autocomplete="off" maxlength="250" />
                    </div>
                </div>
                <div class="layui-row">
                </div>
            </div>

            <div class="layui-card-body">
                <!--月租车/储值车-->
                <div class="layui-row layui-hide" id="moneyMode">
                    <div class="layui-col-sm2 edit-label"><label>充值规则</label></div>
                    <div class="layui-col-sm8 edit-ipt-ban">
                        <select class="layui-select" lay-search id="MonthRule_No" name="MonthRule_No" lay-filter="MonthRule_No">
                            <option value="">不使用充值规则</option>
                        </select>
                    </div>
                </div>
                <!--月租车-->
                <div class="layui-row layui-hide" id="monthMode">
                    <div class="layui-col-sm2 edit-label"><label>开始日期</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-date v-null v-submit" autocomplete="off" id="Car_BeginTime0" name="Car_BeginTime0" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-sm2 edit-label"><label>结束日期</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-date v-null v-submit" autocomplete="off" id="Car_EndTime0" name="Car_EndTime0" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <!--月租车/储值车-->
                <div class="layui-row layui-hide" id="storeMode">
                    <div id="storeMoney">
                        <div class="layui-col-sm2 edit-label"><label>充值金额</label></div>
                        <div class="layui-col-sm3 edit-ipt-ban">
                            <input type="text" class="layui-input v-null v-float" id="Car_Balance" name="Car_Balance" autocomplete="off" maxlength="8" value="0" />
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                    </div>
                    <div class="layui-col-sm2 edit-label"><label>支付金额</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-float" id="Car_PayMoney" name="Car_PayMoney" autocomplete="off" maxlength="8" value="0" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <!--临时车、免费车、访客车-->
                <div class="layui-row layui-hide" id="timeMode">
                    <div class="layui-col-sm2 edit-label"><label>开始日期</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-datetime v-null v-submit" autocomplete="off" id="Car_BeginTime1" name="Car_BeginTime1" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-sm2 edit-label"><label>结束日期</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-datetime v-null v-submit" autocomplete="off" id="Car_EndTime1" name="Car_EndTime1" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
            </div>

            <div class="layui-card-body" id="ownerBox">
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>车主姓名</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_Name" name="Owner_Name" autocomplete="off" maxlength="20" />
                    </div>
                    <div class="layui-col-sm2 edit-label">手机号码</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-phone" id="Owner_Phone" name="Owner_Phone" maxlength="11" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">身份证号</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-idcard" id="Owner_IDCard" name="Owner_IDCard" maxlength="18" autocomplete="off" />
                    </div>
                    <div class="layui-col-sm2 edit-label">驾驶证号</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_License" name="Owner_License" maxlength="32" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">电子邮箱</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-email" id="Owner_Email" name="Owner_Email" maxlength="50" autocomplete="off" />
                    </div>
                    <div class="layui-col-sm2 edit-label">车主住址</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_Address" name="Owner_Address" maxlength="255" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">车主性别</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <select class="layui-select" lay-search id="Owner_Sex" name="Owner_Sex">
                            <option value="">请选择</option>
                            <option value="0">保密</option>
                            <option value="1" selected>男</option>
                            <option value="2">女</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>&nbsp;</label></div>
                    <div class="layui-col-sm8">
                        <button class="btn btn-primary layui-hide" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                        <button class="btn btn-primary layui-hide" id="SaveNext"><i class="fa fa-check"></i> <t>保存并继续添加</t></button>
                        <button class="btn btn-warning layui-hide" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?2" asp-append-version="true"></script>
    <script>
        myVerify.init();
        s_carno_picker.init("Car_CarNo", function (text, carno) {
            $("#Car_CarNo").val(carno.join(''))
        }, "web").bindkeyup();

        layui.use(["form", "laydate"], function () {

            pager.init();
        });

        var pager = {
            curCardCategory: 3652,
            carParam: null,
            ownerNo: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var selected = '';
                            if (index == 0) selected = 'selected';
                            var option = '<option value="' + item.CarType_No + '" ' + selected + '>' + item.CarType_Name + '</option>';
                            $("#Car_VehicleTypeNo").append(option);
                        });
                    }
                }, "json");

                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var selected = '';
                            if (index == 0) selected = 'selected';
                            //排除商家车&访客车
                            if ("5,6".indexOf(item.CarCardType_Type) < 0) {
                                var option = '<option value="' + item.CarCardType_No + '" ' + selected
                                    + ' data-category="' + item.CarCardType_Category + '">'
                                    + item.CarCardType_Name + '</option>';
                                $("#Car_TypeNo").append(option);
                            }
                        });
                    }
                }, "json");

                $.post("GetMonthRuleList", {}, function (json) {
                    if (json.Success) {
                        RuleObj.data = json.Data;
                    }
                }, "json");

                layui.form.render("select");

                this.onDefault();
                _DATE.bind(layui.laydate, ["Car_BeginTime1", "Car_EndTime1"], { type: "datetime", range: true });
                _DATE.bind(layui.laydate, ["Car_BeginTime0", "Car_EndTime0"], { type: "date", range: true });

                RuleObj.init();
            },
            bindData: function () {

            },
            bindEvent: function () {
                layui.form.on("select(Car_TypeNo)", function (data) {
                    console.log(data);
                    RuleObj.init();
                })

                $("#Cancel").click(function () { parent.layer.closeAll(); })

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Car_Colour = $("#Car_Colour").val();
                        data.Car_Model = $("#Car_Model").val();
                        data.Car_License = $("#Car_License").val();
                        data.Car_Remark = $("#Car_Remark").val();
                        data.Owner_Name = $("#Owner_Name").val();
                        data.Owner_Phone = $("#Owner_Phone").val();
                        data.Owner_IDCard = $("#Owner_IDCard").val();
                        data.Owner_License = $("#Owner_License").val();
                        data.Owner_Email = $("#Owner_Email").val();
                        data.Owner_Address = $("#Owner_Address").val();
                        data.Owner_ParkSpace = $("#Owner_ParkSpace").val();
                        return data;
                    });
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);
                    pager.carParam = param;
                    pager.carParam.Owner_No = pager.ownerNo;

                    pager.OnSave(pager.carParam);
                });

                $("#CreateSpace").click(function () {
                    $.getJSON("/Car/CreateOwnerSpace", {}, function (json) {
                        if (json.success) {
                            $("#Owner_Space").val(json.data);
                            if (pager.ownerNo != null) {
                                pager.ownerNo = null;
                                $("#Owner_Name").val("");
                                $("#Owner_IDCard").val("");
                                $("#Owner_License").val("");
                                $("#Owner_Phone").val("");
                                $("#Owner_Email").val("");
                                $("#Owner_Sex").val(1);
                                $("#Owner_Address").val("");
                            }
                            layui.form.render("select");
                            layer.msg("生成车位号成功", { time: 1000 });
                        }
                    });
                });

                $("#SelectSpace").click(function () {
                    layer.open({
                        title: "选择车辆所属车主",
                        type: 2, id: 1,
                        area: getIframeArea(['750px', '690px']),
                        maxmin: true,
                        content: '/Car/OwnerList?r=' + Math.random(),
                        end: function () {

                        }
                    });
                });

                $("#SaveNext").click(function () {
                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Car_Colour = $("#Car_Colour").val();
                        data.Car_Model = $("#Car_Model").val();
                        data.Car_License = $("#Car_License").val();
                        data.Car_Remark = $("#Car_Remark").val();
                        data.Owner_Name = $("#Owner_Name").val();
                        data.Owner_Phone = $("#Owner_Phone").val();
                        data.Owner_IDCard = $("#Owner_IDCard").val();
                        data.Owner_License = $("#Owner_License").val();
                        data.Owner_Email = $("#Owner_Email").val();
                        data.Owner_Address = $("#Owner_Address").val();
                        data.Owner_ParkSpace = $("#Owner_ParkSpace").val();
                        return data;
                    });
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#SaveNext").attr("disabled", true);
                    pager.carParam = param;
                    pager.carParam.Owner_No = pager.ownerNo;

                    pager.saveNext = true;
                    pager.OnSave(pager.carParam);
                });

                $("#CreateSpace").click();
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    //console.log(pagePower)
                    $("#CreateSpace").removeClass("layui-hide");
                    $("#SelectSpace").removeClass("layui-hide");
                });
            },
            //设置界面文本默认值
            onDefault: function () {
                var date = new Date();
                var dateMonth = _DATE.getSpan(date, { month: 1 }, "d");
                $("#Car_BeginTime1").val(date.Format("yyyy-MM-dd 00:00:00"));
                $("#Car_EndTime1").val(date.Format("yyyy-MM-dd 23:59:59"));

                $("#Car_BeginTime0").val(date.Format("yyyy-MM-dd"));
                $("#Car_EndTime0").val(dateMonth);
            },
            SetOwner: function (Owner_No, Owner_Name, Owner_IDCard, Owner_License, Owner_Phone, Owner_Email, Owner_Sex, Owner_Address, Owner_Space, data) {
                pager.ownerNo = Owner_No;
                $("#Owner_Name").val(Owner_Name);
                $("#Owner_IDCard").val(Owner_IDCard);
                $("#Owner_License").val(Owner_License);
                $("#Owner_Phone").val(Owner_Phone);
                $("#Owner_Email").val(Owner_Email);
                $("#Owner_Sex").val(Owner_Sex);
                $("#Owner_Address").val(Owner_Address);
                $("#Owner_Space").val(Owner_Space);
                layui.form.render("select");
            },
            Continue: function (ParkOrderNo) {
                console.log("支付成功，继续执行登记车牌");

                layer.closeAll();
                layer.msg("支付成功,正在保存车辆信息...", { icon: 16, time: 0 });
                pager.OnSave(pager.carParam);
            },
            OnSave: function (param) {
                param.Car_EnableOffline = config.Car_EnableOffline;
                //查询车辆是否在场内产生费用: success = true：不在场内产生费用，success= false：车辆已在场内且产生费用
                $.post("AddCar", { jsonModel: JSON.stringify(param) }, function (json) {
                    if (json.success) {
                        layer.msg("保存成功", { icon: 1, time: 2000 }, function () {
                            if (!pager.saveNext)
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            else {
                                window.parent.pager.bindData(window.parent.pager.pageIndex, true);
                                pager.onClear();
                            }
                        });
                    } else {
                        if (json.data && json.data != null && json.data != "") {
                            layer.open({
                                type: 0,
                                title: "消息提示",
                                btn: ["去清缴费用", "取消"],
                                shade: 0,
                                content: json.msg,
                                yes: function (res) {
                                    layer.open({
                                        title: "停车支付",
                                        type: 2, id: 1,
                                        area: getIframeArea(['95%', '95%']),
                                        maxmin: true,
                                        content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(json.data) + "&callBack=1",
                                        end: function () { }
                                    });
                                }
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功")>-1) window.parent.pager.bindData(window.parent.pager.pageIndex); } });
                        }
                        $("#Save").removeAttr("disabled");
                        $("#SaveNext").removeAttr("disabled");
                    }
                }, "json");
            },
            onClear: function () {
                //$("#Car_CarNo").val("");
                //$("#Owner_Space").val("");
                //$("#Owner_ParkSpace").val("");
                //$("#Owner_ParkSpace").val("");
                //$("#ownerBox input").val("");
                //pager.ownerNo = null;
                //pager.carParam = null;

                //pager.saveNext = false;
                //$("#SaveNext").removeAttr("disabled");
                window.location.reload();
            }
        }

        //充值\延期
        var RuleObj = {
            data: [],
            temparr: ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'],//临时车类型
            montharr: ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664','3656'],//月租车类型
            freearr: [],//免费车类型
            prepaidarr: ['3657'],//储值车类型
            visitorarr: ['3658'],//免费车类型
            //显示充值\延期
            init: function () {
                var cardNo = $("#Car_TypeNo").val();
                var cardCategory = $("#Car_TypeNo").find("option:selected").attr("data-category");
                if (this.temparr.indexOf(cardCategory) > -1 || this.freearr.indexOf(cardCategory) > -1 || this.visitorarr.indexOf(cardCategory) > -1) {
                    $("#timeMode").removeClass("layui-hide");
                    $("#moneyMode,#monthMode,#storeMode").removeClass("layui-hide").addClass("layui-hide");
                } else if (this.montharr.indexOf(cardCategory) > -1) {
                    $("#monthMode,#storeMode").removeClass("layui-hide");
                    $("#timeMode,#moneyMode,#storeMoney").removeClass("layui-hide").addClass("layui-hide");
                    //this.bindSelect(cardNo, 1);
                } else if (this.prepaidarr.indexOf(cardCategory) > -1) {
                    $("#moneyMode,#monthMode,#storeMode,#storeMoney").removeClass("layui-hide");
                    $("#timeMode").removeClass("layui-hide").addClass("layui-hide");
                    this.bindSelect(cardNo, 2);
                }

                this.bindEvent();
            },
            //绑定充值规则 cardNo=车牌类型编号，code=1 月租车,2 储值车
            bindSelect: function (cardNo, code) {
                var options = '<option value="">不使用充值规则</option>';
                this.data.forEach(function (item, index) {
                    if (item.MonthRule_CarCardTypeNo == cardNo) {
                        var ShowText = item.MonthRule_Name;
                        if (code == 1)
                            ShowText += " 充值：" + item.MonthRule_Cycle + Convert.dateUnit(item.MonthRule_Unit);
                        if (code == 2)
                            ShowText += " 充值：" + item.MonthRule_EMoney + "元";
                        ShowText += " 支付：" + item.MonthRule_Money + "元";
                        if (item.MonthRule_AddEnable == 1)
                            ShowText += " 赠送：" + item.MonthRule_AddCycle + Convert.dateUnit(item.MonthRule_AddUnit);
                        if (code == 1)
                            ShowText += " 开始类型：" + Convert.beginType(item.MonthRule_Type);
                        options += '<option value="' + item.MonthRule_No + '" data-code="' + code + '">' + ShowText + '</option>';
                    }
                });

                $("#MonthRule_No").html(options);
                layui.form.render("select");
            },
            bindEvent: function () {
                layui.form.on("select(MonthRule_No)", function (data) {
                    var code = $("#MonthRule_No").find("option:selected").attr("data-code");
                    if (data.value != "" && data.value != null) {
                        if (code == 2) {
                            RuleObj.data.forEach(function (item, index) {
                                if (item.MonthRule_No == data.value) {
                                    $("#Car_Balance").val(item.MonthRule_EMoney);
                                    $("#Car_PayMoney").val(item.MonthRule_Money);
                                }
                            })
                        }
                    }
                });
            }
        }

        var Convert = {
            dateUnit: function (v) {
                if (v == 1) return "天";
                if (v == 2) return "月";
                if (v == 3) return "年";
                return "";
            },
            beginType: function (v) {
                if (v == 1) return "本月1日";
                if (v == 2) return "当前时间";
                if (v == 3) return "过期时间";
                return "";
            }
        }

        _tip_help.setData("Car_EnableOffline", "启用后：可以通过岗亭端使用 [黑白名单同步] 发送到相机白名单。<br/>禁用后：可以通过岗亭端使用 [黑白名单同步] 从相机白名单删除。");
        _tip_help.init();

    </script>
    <script>
        var config = {
            Car_EnableOffline: 1
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                //if (!onDisabledCom(idName)) { return; }
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName);
            });
        });

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            
        }
    </script>
</body>
</html>
