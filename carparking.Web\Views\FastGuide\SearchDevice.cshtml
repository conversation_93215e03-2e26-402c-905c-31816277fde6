﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索设备</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet">
    <style>
        .layui-form-checked span { background-color: #1ab394; }

        .layui-form-checked:hover span { background-color: #48C2A9; }

        .layui-btn-xs { height: 25px; line-height: 25px; padding: 0 12px; font-size: 14px }

        .layui-card-header { border-bottom: none; }

        /* .layui-card-body { padding: 0px 15px; padding-bottom: 15px; } */

        /* div#searchForm { height: 38px; } */

        .layui-btn .layui-icon { padding: 0 2px; vertical-align: middle; }

        /* 更新禁用选择按钮的样式 */
        .layui-btn-select-disabled { background-color: #FF5722 !important; color: #fff !important; cursor: not-allowed !important; border: none !important; }

        /* 确保鼠标悬停时样式不变 */
        .layui-btn-select-disabled:hover { background-color: #FF5722 !important; color: #fff !important; }

        /* 其他新样式保持不变 */
        .layui-btn-bound { background-color: #FFB800; color: #fff; cursor: not-allowed; }

        .layui-form-checkbox-bound { border-color: #FF5722 !important; cursor: not-allowed; }

        .layui-form-checkbox-bound span { background-color: #FF5722 !important; color: #fff !important; }

        .layui-form-checkbox-bound:hover span { background-color: #FF5722 !important; }

        .layui-form-checkbox-bound i { color: #FF5722 !important; }

        /* 自定义提示框样式 */
        .custom-tooltip { display: none; position: absolute; background-color: rgba(26, 179, 148, 0.9); color: #fff; padding: 10px; font-size: 14px; max-width: 300px; z-index: 1000; border: 1px solid rgba(26, 179, 148, 0.8); box-shadow: 0 4px 8px rgba(20, 139, 116, 0.8); }

        th { font-weight: 700 !important; }

        .layui-table-body .layui-none { color: #2b2727; text-align: left; background-color: #eaeba0; font-size: 1rem; line-height: 35px; padding: 40px 0; }
        .layui-table-box{height:225px;}
        .layui-table-body.layui-table-main { height: 187px; width: 466px; overflow-y: scroll; scrollbar-width: thin; scrollbar-color: #1ab394 #fff; }
            .layui-table-body.layui-table-main::-webkit-scrollbar { width: 6px; }
            .layui-table-body.layui-table-main::-webkit-scrollbar-track { background-color: #fff; }
            .layui-table-body.layui-table-main::-webkit-scrollbar-thumb { background-color: #1ab394; }
    </style>
</head>

<body>
    <div class="ibox-content">
        <div class="layui-card">
            <div class="layui-card-body">
                <table class="layui-hide" id="deviceTable" lay-filter="deviceTable"></table>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script>
        //保存全部记录按钮事件
        function SaveAll() {
            var params = filterTableData();
            if (params.length > 0) {
                $.ajax({
                    url: '/Device/SaveAllDeviceRecords',
                    type: 'POST',
                    data: { jsonModel: JSON.stringify(params) },
                    success: function (res) {
                        if (res.success === true) {
                            tableData.forEach(function (item) {
                                params.find(function (item2) {
                                    if (item.DeviceRecord_IP === item2.DeviceRecord_IP) {
                                        item.DeviceRecord_Statues = 1;
                                    }
                                })
                            });
                            reloadTablePreserveScroll(false)
                            layer.msg('保存成功', { icon: 1 });
                        } else {
                            layer.msg('保存失败', { icon: 0 });
                        }
                    },
                    error: function (err) {
                        layer.msg('保存失败', { icon: 0 });
                    }
                });
            }
            else {
                layer.msg('当前表格无记录，如已进行条件筛选，你可以取消条件筛选或执行查找网络设备！', { icon: 0 });
            }
        }
        // 刷新数据按钮事件
        function Reload() {
            layer.load(2);
            $.ajax({
                url: '/Device/GetDeviceRecord',
                type: 'GET',
                data: { Category: deviceCategory },
                success: function (res) {
                    layer.closeAll('loading');
                    if (res.success === true && Array.isArray(res.data)) {
                        tableData = res.data;
                        reloadTablePreserveScroll(false);
                        parent.SetDeviceCount(res.data.length);
                        layer.msg('数据刷新成功', { icon: 1 });
                    } else {
                        layer.msg('获取数据失败：' + (res.msg || '未知错误'), { icon: 2 });
                    }
                },
                error: function () {
                    layer.closeAll('loading');
                    layer.msg('获取数据请求失败，请检查网络连接', { icon: 2 });
                }
            });
        }
        //查找网络设备按钮事件
        function SearchDevice() {
            if (isSearching) {
                cancelSearch();
            } else {
                startSearch();
            }
        }
    </script>
    <script>
        var tableData = []; // 用于存储表格的初始数据
        var currentFilter = {
            ip: '',
            status: ''
        };
        var isSearching = false;
        var searchInterval;
        var searchTimeout;
        var countdownInterval;
        var remainingTime;
        var table = null,
            util = null,
            form = null;
        var fromTable = null;

        var deviceCategory = $.getUrlParam("Category");
        layui.use(['table', 'form'], function () {
            table = layui.table;
            util = layui.util;
            form = layui.form;

            // 初始化表格
            fromTable = table.render({
                elem: '#deviceTable',
                url: '/Device/GetDeviceRecord?Category=' + deviceCategory,
                page: false,
                totalRow: false,
                text: { none: '1、点击【搜索】，搜索局域网内设备；<br/>2、点击【设备名称】自定义编辑，默认按车道生成；<br/>3、配置设备参数，请前往【设备管理】模块；' },
                cols: [[
                    {
                        field: 'DeviceRecord_IP', title: '设备IP', width: '200', sort: true, templet: function (d) {
                            return '<button class="layui-btn layui-btn-sm layui-btn-xs" lay-event="selectDevice">选择</button>   <span>' + d.DeviceRecord_IP + '</span>'
                        }
                    },
                    { field: 'DeviceRecord_Remark', title: '设备名称', width: '150', edit: 'text' },
                    { field: 'DeviceRecord_Type', title: '设备型号', width: '100' },
                    // { field: 'DeviceRecord_Type', title: '设备型号', width: '120', sort: true },
                    // {
                    //     title: '记录状态',
                    //     width: '130',
                    //     templet: function (d) {
                    //         if (d.DeviceRecord_Statues === 2) {
                    //             return '<div class="layui-form-checkbox layui-form-checkbox-bound" lay-skin="tag"><span>使用中</span><i class="layui-icon layui-icon-ok"></i></div>';
                    //         } else {
                    //             return `<input type="checkbox" name="IsEnable" lay-filter="deviceStatusChange" value="${d.DeviceRecord_IP}" title="${d.DeviceRecord_Statues === 1 ? '永久' : '暂存'}" lay-skin="tag" ${d.DeviceRecord_Statues === 1 ? 'checked' : ''} />`;
                    //         }
                    //     }
                    // },
                  
                ]],
                done: function (res) {
                    tableData = res.data; // 缓存初始数据
                    form.render();
                    parent.SetDeviceCount(res.data.length);
                }
            });

            // 绑定IP输入框事件
            $('#inputDeviceRecord_IP').on('input keyup', function (e) {
                if (e.type === 'input' || (e.type === 'keyup' && (e.key === 'Enter' || e.keyCode === 13))) {
                    currentFilter.ip = this.value;
                    reloadTablePreserveScroll(true);
                }
            });

            // 绑定设备状态下拉框事件
            form.on('select(selectDeviceStatus)', function (data) {
                currentFilter.status = data.value;
                reloadTablePreserveScroll(true);
            });

            table.on('tool(deviceTable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'selectDevice') {
                    // 选择设备并返回数据到父窗口
                    parent.handleDeviceSelection(data); // 调用父窗口的处理函数
                }
                else if (obj.event === 'loginDevice') {
                    if (deviceCategory == '1') {
                        window.open('http://' + data.DeviceRecord_IP, '_blank');
                    }
                    else if (deviceCategory == '4') {
                        window.open('http://' + data.DeviceRecord_IP + ':8099', '_blank');
                    }
                }
            });

            table.on('edit(deviceTable)', function (obj) {
                var value = obj.value, data = obj.data, field = obj.field;
                // 更新缓存中的数据
                var index = tableData.findIndex(item => item.DeviceRecord_IP === data.DeviceRecord_IP);
                if (index !== -1) {
                    tableData[index][field] = value;
                    var param = tableData[index];
                    $.post("/Device/SaveSearchResults", { jsonModel: JSON.stringify(param), act: param.DeviceRecord_Statues }, function (json) {
                        if (json.success) {
                            layer.msg("更新成功", { icon: 1, time: 800 })
                        } else {
                            layer.msg(json.msg)
                        }
                    }, "json");
                }
            });

            form.on('checkbox(deviceStatusChange)', function (obj) {
                var $this = $(this);
                var ip = $this.val();
                var index = tableData.findIndex(item => item.DeviceRecord_IP === ip);
                if (index !== -1) {
                    var checked = $this.prop('checked');
                    var txt = checked ? '永久' : '暂存';
                    // 更新复选框的标题
                    $this.attr('title', txt);
                    tableData[index].DeviceRecord_Statues = checked ? 1 : 0;
                    // 如果当前正在按状态筛选，则需要重新加载表格
                    if (currentFilter.status !== '') {
                        reloadTablePreserveScroll(true);
                    }
                    // 重新渲染表单，确保更改生效
                    form.render('checkbox');

                    var param = tableData[index];
                    $.post("/Device/SaveSearchResults", { jsonModel: JSON.stringify(param), act: param.DeviceRecord_Statues }, function (json) {
                        if (json.success) {
                            layer.msg("记录状态更新成功！", { icon: 1, time: 800 });
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            });


        });

        // 过滤函数
        function filterTableData() {
            if (!Array.isArray(tableData)) {
                return [];
            }
            return tableData.filter(function (item) {
                if (!item) return false;
                var ipMatch = !currentFilter.ip || item.DeviceRecord_IP.toLowerCase().indexOf(currentFilter.ip.toLowerCase()) !== -1;
                var statusMatch = currentFilter.status === '' || item.DeviceRecord_Statues.toString() === currentFilter.status;
                return ipMatch && statusMatch;
            });
        }

        // 修改 reloadTablePreserveScroll 函数
        function reloadTablePreserveScroll(isLength) {
            if (!Array.isArray(tableData)) {
                tableData = [];
            }

            if (isLength == true && tableData.length === 0) {
                return;
            }

            var textTip = tableData.length === 0 ? '1、暂无设备数据，请点击【搜索设备】，对局域网内的设备进行搜索；<br/>2、搜索完毕，可对设备名称进行自定义编辑，若不填将按车道默认生成设备名称；<br/>3、搜索出来的数据只保存6分钟，可点击【保存记录】写入数据库，永久记录；' : '筛选数据无结果';

            fromTable.reload();
            // var filteredData = filterTableData();

            // // 保存滚动位置
            // var scrollTop = 0;
            // var layuiTable = $('.layui-table-main');
            // if (layuiTable != null && layuiTable.length > 0) {
            //     scrollTop = layuiTable[0].scrollTop;
            // }

            // table.reload('deviceTable', {
            //     data: filteredData,
            //     url: '',
            //     text: { none: textTip },
            //     done: function (res) {
            //         if (layuiTable != null && layuiTable.length > 0) {
            //             $('.layui-table-main').scrollTop(scrollTop);
            //         }
            //     }
            // });
        }

        // 修改 addOrUpdateDevice 函数
        function addOrUpdateDevice(device) {
            if (!device || typeof device !== 'object' || !device.DeviceRecord_IP) {
                return;
            }

            if (!Array.isArray(tableData)) {
                tableData = [];
            }

            var existingIndex = tableData.findIndex(function (item) {
                return item && item.DeviceRecord_IP === device.DeviceRecord_IP;
            });

            if (existingIndex !== -1) {
                device.DeviceRecord_Statues = tableData[existingIndex].DeviceRecord_Statues;
                tableData[existingIndex] = { ...tableData[existingIndex], ...device };
            } else {
                tableData.push(device);
            }
        }

        // 修改 startSearch 函数
        function startSearch() {
            isSearching = true;
            remainingTime = 15;
            updateSearchButton();
            layer.msg('正在搜索...', { icon: 1 });

            $.ajax({
                url: '/Device/StartNetworkDeviceSearch',
                type: 'GET',
                data: { Category: deviceCategory },
                success: function (res) {
                    if (res.success) {
                        searchInterval = setInterval(pollSearchResults, 2500);
                        countdownInterval = setInterval(updateCountdown, 1000);
                        searchTimeout = setTimeout(function () {
                            cancelSearch();
                        }, remainingTime * 1000);
                    } else {
                        layer.msg('启动搜索失败: ' + res.message, { icon: 2 });
                        cancelSearch();
                    }
                },
                error: function () {
                    layer.msg('请求失败，请检查网络连接', { icon: 2 });
                    cancelSearch();
                }
            });
        }

        // 取消搜索
        function cancelSearch() {
            isSearching = false;
            clearInterval(searchInterval);
            clearInterval(countdownInterval);
            clearTimeout(searchTimeout);
            updateSearchButton();

            $.ajax({
                url: '/Device/CancelNetworkDeviceSearch',
                type: 'POST',
                success: function (res) {
                    layer.msg('搜索结束', { icon: 1 });
                },
                error: function () {
                    layer.msg('取消请求失败，请检查网络连接', { icon: 2 });
                }
            });
        }

        // 更新搜索倒计时
        function updateCountdown() {
            remainingTime--;
            updateSearchButton();
            if (remainingTime <= 0) {
                cancelSearch();
            }
        }

        // 更新搜索按钮文本
        function updateSearchButton() {
            var buttonText = isSearching
                ? `<i class="layui-icon layui-icon-close inbtn"></i><t> 取消查找 (${remainingTime}s)</t>`
                : '<i class="layui-icon layui-icon-search inbtn"></i><t> 查找网络设备</t>';
            $('#searchNetworkDevices').html(buttonText);
        }

        // 修改 pollSearchResults 函数
        function pollSearchResults() {
            $.ajax({
                url: '/Device/GetSearchResults',
                type: 'GET',
                data: { Category: deviceCategory },
                success: function (res) {
                    if (res.success) {
                        if (res.data.isCompleted) {
                            layer.msg('搜索完成', { icon: 1 });
                            isSearching = false;
                            clearInterval(searchInterval);
                            clearInterval(countdownInterval);
                            clearTimeout(searchTimeout);
                            updateSearchButton();
                        }
                        res.data.devices.forEach(addOrUpdateDevice);
                        reloadTablePreserveScroll(false);
                        parent.SetDeviceCount(res.data.devices.length);
                    } else {
                        layer.msg('搜索失败: ' + res.message, { icon: 2 });
                    }
                },
                error: function () {
                    layer.msg('请求失败，请检查网络连接', { icon: 2 });
                }
            });
        }
    </script>
</body>

</html>