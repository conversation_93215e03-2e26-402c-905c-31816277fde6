﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>&nbsp;</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min.css" rel="stylesheet" />
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/plugins/bootstrap-table/bootstrap-table.min.css?v=12" rel="stylesheet" />
    <style>
        html, body { width: auto; height: auto; margin: 0; padding: 0; }
        @@page { size: landscape; }
        td { padding: 0px; margin: 0px; margin-bottom: 0mm; margin-top: 0mm; }
        body { max-width: 1200px; font-size: 13px; font-family: 'Microsoft YaHei'; }
        th, td { text-align: center; height: 25px; }
        td { width: 75px; font-family: FangSong; font-size: 13px; }
        td.sumt { font-family: 'Microsoft YaHei'; text-align: left; text-indent: 12px; }
    </style>
</head>
<body>
    <table width="1200" border="1" id="table">
        <tr>
            <th colspan="17">@ViewBag.Start — @ViewBag.End 月统计报表</th>
        </tr>
        <tr>
            <th colspan="2">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</th>
            <th colspan="11"></th>
            <th colspan="5">@ViewBag.ParkName</th>
        </tr>
        <tr>
            <th rowspan="2">日期</th>
            <th colspan="2">月租车</th>
            <th colspan="2">免费车</th>
            <th colspan="2">储值车</th>
            <th colspan="2">临时车</th>
            <th colspan="1">免费放行</th>
            <th colspan="2">人工开闸</th>
            <th colspan="4">线下现金合计(不含储值车收费金额)</th>
        </tr>
        <tr>
            <th>出场车辆</th>
            <th>金额</th>
            <th>出场车辆</th>
            <th>金额</th>
            <th>出场车辆</th>
            <th>金额</th>
            <th>出场车辆</th>
            <th>金额</th>
            <th>出场车辆</th>
            <th>出场</th>
            <th>入场</th>
            <th>总车数</th>
            <th>总应收</th>
            <th>减免金额</th>
            <th>总实收</th>
            <th>电子支付</th>
        </tr>
        <tbody id="CarContent">
        </tbody>
        <tbody>
            <tr>
                <td colspan="16"></td>
            </tr>
        </tbody>
        <tbody id="payHtml">
        </tbody>
        <tbody>
            <tr>
                <th colspan="1">电子支付</th>
                <td colspan="1">总应收</td>
                <td colspan="1" data-key="OnlineYingShou"></td>
                <td colspan="1">总减免</td>
                <td colspan="1" data-key="OnlineJianMian"></td>
                <td colspan="1">总实收</td>
                <td colspan="1" data-key="TotalMoney"></td>
                <th colspan="1">平台现金</th>
                <td colspan="1">总应收</td>
                <td colspan="1" data-key="CashYingShou"></td>
                <td colspan="1">减免总金额</td>
                <td colspan="1" data-key="CashJianMian"></td>
                <td colspan="1">总实收</td>
                <td colspan="1" data-key="CashOnline"></td>
                <td colspan="3"></td>
            </tr>
            <tr>
                <th colspan="13"></th>
                <th colspan="2">收费金额合计</th>
                <td colspan="2" data-key="AllTotalMoney"></td>
            </tr>
        </tbody>
    </table>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1.28" asp-append-version="true"></script>
    <script>
        var sdt = decodeURIComponent($.getUrlParam("sdt"));
        var edt = decodeURIComponent($.getUrlParam("edt"));
        var exp = $.getUrlParam("export");
        var datatype = $.getUrlParam("datatype");

        var pager = {
            init: function () {
                this.bindData();
            },
            bindData: function () {
                $.post("/RptMonth/RptData", { sdt: sdt, edt: edt, checkhis: true, dataType: datatype, isPrint: true }, function (json) {
                    if (json.success) {
                        localStorage.setItem("rptdaydata", encodeURIComponent(JSON.stringify(json.data)));
                        pager.bindShow(json.data);
                    } else {
                        layer.msg(json.msg, { icon: 0, time: 2000 });
                    }
                }, "json");
            },
            bindShow: function (data) {
                $("#CarContent").html('');
                data.RptPrintList.forEach((item, index) => {
                    var trHtml = '<tr>'
                        + '<td>' + (item.Account == null ? "平台" : item.Account) + '</td>'
                        + '<td>' + item.MonthCount + '</td>'
                        + '<td>' + item.MonthMoney + '</td>'
                        + '<td>' + item.FreeCount + '</td>'
                        + '<td>' + item.FreeMoney + '</td>'
                        + '<td>' + item.StoreCount + '</td>'
                        + '<td>' + item.StoreMoney + '</td>'
                        + '<td>' + item.TempCount + '</td>'
                        + '<td>' + item.TempMoney + '</td>'
                        + '<td>' + item.FreeOutCount + '</td>'
                        + '<td>' + item.OpenGateCount + '</td>'
                        + '<td>' + item.OpenGateInCount + '</td>'
                        + '<td>' + item.TotalCount + '</td>'
                        + '<td>' + item.TotalYsMoney + '</td>'
                        + '<td>' + item.TotalJmMoney + '</td>'
                        + '<td>' + item.TotalSsMoney + '</td>'
                        + '<td>' + item.onlineMoney + '</td>'
                        + '</tr>';
                    $("#CarContent").append(trHtml);
                });

                var trHtml = '<tr>'
                    + '<th>合计</th>'
                    + '<td>' + data.RptPrintListTotal.MonthCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.MonthMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.FreeCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.FreeMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.StoreCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.StoreMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.TempCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.TempMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.FreeOutCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.OpenGateCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.OpenGateInCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalCount + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalYsMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalJmMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.TotalSsMoney + '</td>'
                    + '<td>' + data.RptPrintListTotal.onlineMoney + '</td>'
                    + '</tr>';
                $("#CarContent").append(trHtml);

                $("#payHtml").html(data.payHtml);
                $("td").each(function () {
                    var key = $(this).attr("data-key");

                    if (key != null && data.onlineData[key] != null)
                        $(this).text(data.onlineData[key]);

                    if (key == "AllTotalMoney")
                        $(this).text((data.onlineData["TotalMoney"] + data.onlineData["CashOnline"] + data.RptPrintListTotal["TotalSsMoney"]).toFixed(2));
                });


                if (!exp)
                    window.print();
                else {
                    var htmltable = document.getElementById('table');
                    var html = htmltable.outerHTML;
                    var isWin = '@ViewBag.IsWindows';
                    var filename = "";
                    if (isWin == "1") {
                        filename = '月报表_' + '@ViewBag.Start' + '_' + '@ViewBag.End';
                    } else {
                        filename = 'MonthReport_' + formatDateToyyyyMMdd(new Date('@ViewBag.Start')) + '_' + formatDateToyyyyMMdd(new Date('@ViewBag.End')) + '.xlsx';
                    }
                    win_downlaod('data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,' + encodeURIComponent(html), filename);
                    window.close();
                }
            }
        }

        $(function () {
            pager.init();
        });

        var beforePrint = function () {
            console.log('beforePrint');
        };

        var afterPrint = function () {
            console.log('afterPrint');
            setTimeout(() => {
                window.close();
            }, 5000);

            var s = 4;
            setInterval(() => {
                layer.msg(s + "秒后自动关闭");
                s--;
            }, 1000);
        };

        if (!exp) {
            if (window.matchMedia) {   //返回一个新的 MediaQueryList 对象，表示指定的媒体查询字符串解析后的结果。
                var mediaQueryList = window.matchMedia('print');
                mediaQueryList.addListener(function (mql) {
                    if (mql.matches) {
                        beforePrint();
                    } else {
                        afterPrint();
                    }
                });
            }

            window.onbeforeprint = beforePrint;
            window.onafterprint = afterPrint;
        }

        function formatDateToyyyyMMdd(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            //const hours = String(date.getHours()).padStart(2, '0');
            //const minutes = String(date.getMinutes()).padStart(2, '0');
            //const seconds = String(date.getSeconds()).padStart(2, '0');
            //${hours}${minutes}${seconds}

            return `${year}${month}${day}`;
        }
    </script>
</body>
</html>
