﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>账号管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input{width:182px;}
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>账号管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input "  name="Admins_Name" id="Admins_Name" autocomplete="off" placeholder="昵称" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input "  name="Admins_Account" id="Admins_Account" autocomplete="off" placeholder="账号" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input "  name="Admins_Phone" id="Admins_Phone" autocomplete="off" placeholder="手机号" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="启用状态" class="form-control layui-select" id="Admins_Enable" name="Admins_Enable">
                                    <option value="">启用状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.Admins.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.Admins.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.Admins.Enable){}}<button class="layui-btn layui-btn-sm" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>启用</t></button>{{# } }}
                                {{# if(Power.Admins.Disable){}}<button class="layui-btn layui-btn-sm" lay-event="Disable"><i class="fa fa-ban"></i><t>禁用</t></button>{{# } }}
                                {{# if(Power.Admins.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                                {{# if(Power.Admins.Bind){}}<button class="layui-btn layui-btn-sm" lay-event="Bind"><i class="fa fa-gg"></i><t>绑定车道</t></button>{{# } }}
                                {{# if(Power.Admins.Bind){}}<button class="layui-btn layui-btn-sm" lay-event="BindBatch"><i class="fa fa-gg-circle"></i><t>批量绑定车道</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TmplEnable">
        {{# if(d.Admins_Enable==1){ }}
        <span class="layui-badge layui-bg-blue ">启用</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">禁用</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Admins_Account', width: 140, title: '账号' }
                , { field: 'Admins_Name', title: '昵称' }
                , { field: 'PowerGroup_Name', title: '权限组' }
                , { field: 'Admins_Phone', title: '联系电话' }
                , { field: 'Admins_Email', title: '邮箱地址' }
                , { field: 'Admins_Address', title: '所在地址' }
                , { field: 'Admins_Enable', width: 100, title: '状态', toolbar: '#TmplEnable' }
                , { field: 'BindPasswayName', title: '绑定车道' }
                , { field: 'Admins_AddTime', title: '注册时间' }
                , { field: 'Admins_Remark', title: '备注', hide: true }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Admins/GetAdminsList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageindex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增账号",
                            content: '/Admins/Edit?Act=Add',
                            area: getIframeArea(['850px', '580px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑账号",
                            content: '/Admins/Edit?Act=Update&Admins_ID=' + data[0].Admins_ID,
                            area: getIframeArea(['850px', '580px']),
                            maxmin: true
                        });
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.msg("处理中", { icon: 16, time: 0 });
                        $.getJSON("/Admins/EnableAdmins", { Admins_ID: data[0].Admins_ID }, function (json) {
                            if (json.Success)
                                layer.msg("启用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                            else
                                layer.msg(json.Message, { icon: 0, time: 1500 });
                        });
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.msg("处理中", { icon: 16, time: 0 });
                        $.getJSON("/Admins/DisableAdmins", { Admins_ID: data[0].Admins_ID }, function (json) {
                            if (json.Success)
                                layer.msg("禁用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                            else
                                layer.msg(json.Message, { icon: 0, time: 1500 });
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/Admins/DeleteAdmins", { Admins_ID: data[0].Admins_ID }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pageindex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Bind':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.open({
                            type: 2,
                            title: "车道列表",
                            content: "/Admins/Passway?Admins_ID=" + data[0].Admins_ID,
                            area: getIframeArea(["900px", "650px"]),
                            maxmin: false
                        })
                        break;
                    case 'BindBatch':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var jsonNo = [];
                        data.forEach((item, index) => { jsonNo.push(item.Admins_ID); });
                        layer.open({
                            type: 2,
                            title: "批量绑定车道",
                            content: "/Admins/PasswayBind?Admins_IDs=" + jsonNo.join(','),
                            area: getIframeArea(["900px", "650px"]),
                            maxmin: false
                        })
                        break;
                };
            });

            tb_row_checkbox();
        });

        //绑定查询事件
        $(function () {
            $("#BtnSearch").click(function () { pager.bindData(1); });
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Admins/GetAdminsList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            }
        }
    </script>
    <script>
        $(function () {
            $.ajaxSettings.async = false;
            pager.bindSelect();
            $.ajaxSettings.async = true;
        });
    </script>


</body>
</html>
