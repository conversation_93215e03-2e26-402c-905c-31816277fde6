﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>车牌识别日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }

        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>

<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>车牌识别记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <select name="CarRecog_PasswayNo" id="CarRecog_PasswayNo" lay-search>
                                    <option value="">所有车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CarRecog_CarNo" id="CarRecog_CarNo" autocomplete="off"
                                       placeholder="车牌号" maxlength="8" />
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input " name="CarRecog_Time0" id="CarRecog_Time0" autocomplete="off"
                                       placeholder="识别时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CarRecog_Time1" id="CarRecog_Time1" autocomplete="off"
                                       placeholder="识别时间止" />
                            </div>


                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search">
                                    <i class="layui-icon layui-icon-search inbtn"></i>
                                    <t>搜索</t>
                                </button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select "
                                        id="CarRecog_CarType" name="CarRecog_CarType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            @*     <div class="layui-inline">
                            <input class="layui-input " name="CarRecog_RecogContent" id="CarRecog_RecogContent" autocomplete="off" placeholder="相机识别结果" />
                            </div>*@
                            <div class="layui-inline">
                                <select data-placeholder="通行结果" class="form-control chosen-select " id="CarRecog_IsOpen"
                                        name="CarRecog_IsOpen" lay-search>
                                    <option value="">通行结果</option>
                                    <option value="0">禁止通行</option>
                                    <option value="1">允许通行</option>
                                    <option value="2">弹框确认</option>
                                    <option value="3">排队等候</option>
                                    <option value="4">最低缴费</option>
                                    <option value="10">黑名单车辆</option>
                                    <option value="11">忽略处理</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input " name="CarRecog_Remark" id="CarRecog_Remark"
                                       autocomplete="off" placeholder="备注" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="识别方式" class="form-control chosen-select " id="CarRecog_Mode"
                                        name="CarRecog_Mode" lay-search>
                                    <option value="">识别方式</option>
                                    <option value="1">相机识别</option>
                                    <option value="2">扫码</option>
                                    <option value="3">ETC</option>
                                    <option value="4">刷卡</option>
                                    <option value="6">输入车牌</option>
                                    <option value="7">平台导入</option>
                                    <option value="8">弹窗修改</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="form-control chosen-select "
                                        id="CarRecog_PlateColor" name="CarRecog_PlateColor" lay-search>
                                    <option value="">车牌颜色</option>
                                    <option value="黄">黄牌</option>
                                    <option value="绿">绿牌</option>
                                    <option value="蓝">蓝牌</option>
                                    <option value="白">白牌</option>
                                    <option value="黑">黑牌</option>
                                    <option value="黄绿">黄绿牌</option>
                                    <option value="0牌车">0牌车</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <select data-placeholder="默认数据" class="form-control chosen-select "
                                        id="CarRecog_CarYear" name="CarRecog_CarYear" lay-search>
                                    <option value="0">默认数据</option>
                                    <option value="1">历史数据</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <select data-placeholder="开闸状态" class="form-control chosen-select" 
                                        id="CarRecog_OpenStatus" name="CarRecog_OpenStatus" lay-search>
                                    <option value="">开闸状态</option>
                                    <option value="1">已开闸</option>
                                    <option value="0">未开闸</option>
                                    <option value="-1">开闸失败</option>
                                </select>
                            </div>

                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplimg">
        {{# if(d.CarRecog_Img!=null && d.CarRecog_Img!=""){ }}
        <a href="{{PathCheck(decodeURIComponent(d.CarRecog_Img))}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看">
            <i class="layui-icon layui-icon-picture"></i>预览
        </a>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">无图片</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplsmallimg">
        {{# if(d.CarRecog_SmallImg!=null && d.CarRecog_SmallImg!=""){ }}
        <a href="{{PathCheck(decodeURIComponent(d.CarRecog_SmallImg))}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看">
            <i class="layui-icon layui-icon-picture"></i>预览
        </a>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">无图片</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplmode">
        {{# if(d.CarRecog_Mode==1){}}
        <span class="layui-badge layui-bg-blue">相机识别</span>
        {{# }else if(d.CarRecog_Mode==2){ }}
        <span class="layui-badge layui-bg-green">扫码</span>
        {{# }else if(d.CarRecog_Mode==3){ }}
        <span class="layui-badge layui-bg-blue">ETC</span>
        {{# }else if(d.CarRecog_Mode==4){ }}
        <span class="layui-badge layui-bg-green">刷卡</span>
        {{# }else if(d.CarRecog_Mode==6){ }}
        <span class="layui-badge layui-bg-red">输入车牌</span>
        {{# }else if(d.CarRecog_Mode==7){ }}
        <span class="layui-badge layui-bg-red">平台导入</span>
        {{# }else if(d.CarRecog_Mode==8){ }}
        <span class="layui-badge layui-bg-red">弹窗修改</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplreal">
        {{# if(d.CarRecog_IsRealPlate==1){}}
        <span class="layui-badge layui-bg-blue">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange">否</span>
        {{# } }}
    </script>

    <script type="text/x-jquery-tmpl" id="tmplplatecolor">
        {{# if(d.CarRecog_PlateColor=="黄牌车"){}}
        <span class="layui-badge layui-bg-yellow">黄牌</span>
        {{# }else if(d.CarRecog_PlateColor=="绿牌车"){ }}
        <span class="layui-badge layui-bg-green">绿牌</span>
        {{# }else if(d.CarRecog_PlateColor=="蓝牌车"){ }}
        <span class="layui-badge layui-bg-blue">蓝牌</span>
        {{# }else if(d.CarRecog_PlateColor=="白牌车"){ }}
        <span class="layui-badge layui-bg-white" style="color:black;border:solid 1px #000">白牌</span>
        {{# }else if(d.CarRecog_PlateColor=="黑牌车"){ }}
        <span class="layui-badge layui-bg-black" style="color:white">黑牌</span>
        {{# }else if(d.CarRecog_PlateColor=="黄绿牌"){ }}
        <span class="layui-badge" style="color:white;background:#9ACD32">黄绿牌</span>
        {{# }
        else { }}
        {{d.CarRecog_PlateColor==null?"":d.CarRecog_PlateColor}}
        {{# } }}
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        var isFrpUrl = IsFrpURLOpenWeb('@Html.Raw(ViewBag.ParkKey)');
        try {
            topBar.init();
        } catch (e) {
            console.log("初始化菜单栏异常：" + e.message);
        }

        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            try {
                pager.init();
            } catch (e) {
                console.log(e);
            }
            layuiForm.render("select");

            try {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'CarRecog_ID', title: 'ID', hide: true }
                , { field: 'CarRecog_No', title: '编号', hide: true }
                , { field: 'CarRecog_CarNo', title: '车牌号', width: 110 }
                , { field: 'CarRecog_CarType', title: '车牌类型' }
                , { field: 'CarRecog_Time', title: '识别时间', width: 170 }
                , { field: 'CarRecog_ParkNo', title: '车场编码', hide: true }
                , { field: 'CarRecog_CameraNo', title: '相机编码', hide: true }
                , { field: 'CarRecog_CameraName', title: '相机名称' }
                , { field: 'CarRecog_PasswayNo', title: '通道编码', hide: true }
                , { field: 'CarRecog_PasswayName', title: '通道名称' }
                , {
                    field: 'CarRecog_Img', title: '抓拍大图', templet: function (d) {
                        if (d.CarRecog_Img != null && d.CarRecog_Img != "") {
                            var img = PathCheck(decodeURIComponent(d.CarRecog_Img));
                            if (isFrpUrl) {
                                img = replaceFirstPathSegment(img);
                            }
                            return '<a href="' + img + '" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title = "点击查看" ><i class="layui-icon layui-icon-picture" > </i>预览</a>';
                        } else {
                            return '<span class= "layui-badge layui-bg-gray" > 无图片 </span>';
                        }
                    }
                }
                , {
                    field: 'CarRecog_SmallImg', title: '抓拍小图', templet: function (d) {
                        if (d.CarRecog_SmallImg != null && d.CarRecog_SmallImg != "") {
                            var img = PathCheck(decodeURIComponent(d.CarRecog_SmallImg));
                            if (isFrpUrl) {
                                img = replaceFirstPathSegment(img);
                            }
                            return '<a href="' + img + '" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title = "点击查看" ><i class="layui-icon layui-icon-picture" > </i>预览</a>';
                        } else {
                            return '<span class= "layui-badge layui-bg-gray" > 无图片 </span>';
                        }
                    }
                }
                , { field: 'CarRecog_Mode', title: '识别方式', toolbar: "#tmplmode" }
                , { field: 'CarRecog_CarLogo', title: '车标', hide: true }
                , { field: 'CarRecog_CarYear', title: '车辆年份', hide: true }
                , { field: 'CarRecog_PlateColor', title: '车牌颜色', toolbar: "#tmplplatecolor" }
                , { field: 'CarRecog_Credibility', title: '置信值' }
                , { field: 'CarRecog_IsRealPlate', title: '是否真车牌', toolbar: "#tmplreal", hide: true }
                , { field: 'CarRecog_LicensePoint', title: '图像坐标点', hide: true }
                , { field: 'CarRecog_OpenStatus', title: '开闸状态', templet: function (d) {
                    if (d.CarRecog_OpenStatus == 1)
                        return '<span class="layui-badge layui-bg-green">已开闸</span>';
                    else if (d.CarRecog_OpenStatus == -1)
                        return '<span class="layui-badge layui-bg-orange">开闸失败</span>';
                    else 
                        return '<span class="layui-badge layui-bg-black">未开闸</span>';
                }}
                //, { field: 'CarRecog_Recogresult', title: '识别结果json', hide: true }
                //, { field: 'CarRecog_RecogContent', title: '相机识别结果' }
                , {
                    field: 'CarRecog_IsOpen', title: '通行结果', templet: function (d) {
                        if (d.CarRecog_IsOpen == 0)
                            return '<span class="layui-badge layui-bg-red">禁止通行</span>';
                        else if (d.CarRecog_IsOpen == 1)
                            return '<span class="layui-badge layui-bg-green">允许通行</span>';
                        else if (d.CarRecog_IsOpen == 2)
                            return '<span class="layui-badge layui-bg-blue">弹框确认</span>';
                        else if (d.CarRecog_IsOpen == 3)
                            return '<span class="layui-badge layui-bg-orange">排队等候</span>';
                        else if (d.CarRecog_IsOpen == 4)
                            return '<span class="layui-badge layui-bg-cyan">最低缴费</span>';
                        else if (d.CarRecog_IsOpen == 10)
                            return '<span class="layui-badge layui-bg-cyan">黑名单车辆</span>';
                        else if (d.CarRecog_IsOpen == 11)
                            return '<span class="layui-badge layui-bg-black">忽略处理</span>';
                        else
                            return '';
                    }
                }
                , { field: 'CarRecog_Remark', title: '备注' }

                ]];

                cols = tb_page_cols(cols);

                comtable = table.render({
                    elem: '#com-table-base'
                    , url: '/CarRecog/GetCarRecogList'
                    , method: 'post'
                    , toolbar: '#toolbar_btns'
                    , defaultToolbar: ["filter"]
                    , cellMinWidth: 90
                    , cols: cols
                    , page: {
                        layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                        groups: 3,
                    }
                    , totalRow: false
                    , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                    , where: { conditionParam: JSON.stringify(conditionParam) }
                    , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                    , done: function (d) {
                        pager.dataCount = d.count;
                        tb_page_set(d);
                        pager.bindPower();
                    }
                });

                //头工具栏事件
                table.on('toolbar(com-table-base)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                    var data = checkStatus.data;  //获取选中行数据
                    pager.pageIndex = $(".layui-laypage-curr").text();
                    switch (obj.event) {
                        case 'Export':
                            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                            if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                                if (conditionParam.CarRecog_Time0 == null || conditionParam.CarRecog_Time1 == null) { layer.msg("请选择识别起止时间", { icon: 0 }); return; }
                                if (_DATE.diffDay(new Date(conditionParam.CarRecog_Time0), new Date(conditionParam.CarRecog_Time1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                            }
                            var field = pager.sortField == null ? "" : pager.sortField;
                            var order = pager.orderField == null ? "" : pager.orderField;

                            pager.dataField = [];
                            obj.config.cols[0].forEach((item) => {
                                if (item.title)
                                    pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                            });
                            layer.open({
                                id: "x_edit_iframe",
                                type: 2,
                                title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                                content: '/ExportExcel/Index?Act=Update&Owner_No=',
                                area: getIframeArea(['1100px', '400px']),
                                maxmin: false,
                                end: function () {
                                    if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                        var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                            return data;
                                        });
                                        conditionParam.SearchType = topBar.config.SearchType;

                                        //实现Ajax下载文件
                                        $.fileDownload('/CarRecog/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&chkfield=" + JSON.stringify(pager.dataField), {
                                            httpMethod: 'GET',
                                            headers: {},
                                            data: null,
                                            prepareCallback: function (url) {
                                                $("#Export").attr("disabled", true);
                                                layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                            },
                                            successCallback: function (url) {
                                                $("#Export").attr("disabled", false);
                                                layer.msg('导出成功');
                                            },
                                            failCallback: function (html, url) {
                                                $("#Export").attr("disabled", false);
                                                layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                            }
                                        });
                                    }
                                }
                            });
                            break;
                    };
                });

                tb_row_checkbox()
            } catch (e) {
                console.log(e);
            }
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            conditionParam: null,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["CarRecog_Time0", "CarRecog_Time1"], { type: 'datetime', range: true });
                $.post("SltCarCardTypeList", {}, function (json) {
                    try {
                        if (json.success) {
                            json.data.forEach(function (d) {
                                var option = '<option value="' + d.CarCardType_Name + '">' + d.CarCardType_Name + '</option>';
                                $("#CarRecog_CarType").append(option);
                            });
                        } else {
                            console.error("SltCarCardTypeList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltCarCardTypeList 错误:", e);
                    }
                }, "json");
                $.post("SltPasswayList", {}, function (json) {
                    try {
                        if (json.success) {
                            json.data.forEach(function (item) {
                                var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                                $("#CarRecog_PasswayNo").append(option);
                            });
                            layui.form.render("select"); // 重新渲染layui表单
                        } else {
                            console.error("SltPasswayList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltPasswayList 错误:", e);
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                comtable.reload({
                    url: '/CarRecog/GetCarRecogList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("CarRecog_CarNo", function (text, carno) { }, "web").bindkeyup();
            }
        }
    </script>
</body>

</html>
