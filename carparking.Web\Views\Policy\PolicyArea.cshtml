﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>功能策略设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/plugins/carnopicker/carnopicker.css" rel="stylesheet" />
    <script src="~/Static/plugins/carnopicker/carnopicker.js?1" asp-append-version="true"></script>
    <style>
        html, body { background-color: #fff !important; }
        .layui-tab-title { padding-left: 2rem; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .6rem; left: .8rem; top: .6rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_p_sen.svg'); }
        .layui-tab-title li.type3::before { background-image: url('../../Static/img/icon/icon_p_card.svg'); }
        .layui-tab-title li.type4::before { background-image: url('../../Static/img/icon/icon_p_park.svg'); }
        .layui-tab-title li.type5::before { background-image: url('../../Static/img/icon/icon_p_pass.svg'); }

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_p_sen1.svg'); }
        .layui-tab-title li.layui-this.type3::before { background-image: url('../../Static/img/icon/icon_p_card1.svg'); }
        .layui-tab-title li.layui-this.type4::before { background-image: url('../../Static/img/icon/icon_p_park1.svg'); }
        .layui-tab-title li.layui-this.type5::before { background-image: url('../../Static/img/icon/icon_p_pass1.svg'); }

        .layui-tab-content { padding: 2rem; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab { margin: 0; background: #fff; padding-top: 15px; }

        .layui-select-title input { color: #0094ff; }
        .layui-disabled { background-color: #eee; opacity: 1; }
        .layui-form-select dl { box-shadow: 0 0 6px; }

        input[value='自动放行'] { color: #1ab394 !important; }
        input[value='禁止通行'] { color: red !important; }
    </style>
    <style>
        /* 下拉多选样式 需要引用*/
        select[multiple] + .layui-form-select > .layui-select-title > input.layui-input { border-bottom: 0 }
        select[multiple] + .layui-form-select dd { padding: 0; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] { margin: 0 !important; display: block; line-height: 36px !important; position: relative; padding-left: 26px; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] span { line-height: 36px !important; padding-left: 10px; float: none; }
        select[multiple] + .layui-form-select .layui-form-checkbox[lay-skin=primary] i { position: absolute; left: 10px; top: 0; margin-top: 9px; }
        .multiSelect { line-height: normal; height: auto; padding: 4px 10px; overflow: hidden; min-height: 38px; margin-top: -38px; left: 0; z-index: 99; position: relative; background: none; }
        .multiSelect a { padding: 2px 5px; background: #0094ff; border-radius: 2px; color: #fff; display: block; line-height: 20px; height: 20px; margin: 2px 5px 2px 0; float: left; }
        .multiSelect a span { float: left; }
        .multiSelect a i { float: left; display: block; margin: 2px 0 0 2px; border-radius: 2px; width: 8px; height: 8px; padding: 4px; position: relative; -webkit-transition: all .3s; transition: all .3s }
        .multiSelect a i:before, .multiSelect a i:after { position: absolute; left: 8px; top: 2px; content: ''; height: 12px; width: 1px; background-color: #fff }
        .multiSelect a i:before { -webkit-transform: rotate(45deg); transform: rotate(45deg) }
        .multiSelect a i:after { -webkit-transform: rotate(-45deg); transform: rotate(-45deg) }
        .multiSelect a i:hover { background-color: #545556; }
        .multiOption { display: inline-block; padding: 0 5px; cursor: pointer; color: #999; }
        .multiOption:hover { color: #5FB878 }
        .simplehide { display: none; }
        .moresetting { display: none; }
        .headmoresetting { cursor: pointer; color: #1e9fff; }
        .headmoresetting:hover { font-weight: 600; }
        .otherdesc { display: none; }
        .descicon { cursor: pointer; font-size: 1.1rem; }
        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }
        .help-btn { position: absolute; width: 20px; margin-left: 7px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .bottomButton { position: fixed; bottom: 0; left: 0; width: 100%; background: #fff; padding: 10px; z-index: 9999; }
        .layui-table { margin-bottom: 50px; }
        tr td:first-child { min-width: 100px; }
        tr td:nth-child(2) { min-width: 200px; }
    </style>
</head>
<body>
    <div class="layui-form" style="background-color:#fff !important;">
        <!--区域设置-->
        <div class="layui-row" style="background-color:#fff;">
            <div class="layui-inline">
                <select class="layui-select" lay-search id="PolicyArea_ParkAreaNo" name="PolicyArea_ParkAreaNo">
                    <option value="">区域名称</option>
                </select>
            </div>
        </div>
        <table class="layui-table">
            <thead>
                <tr>
                    <th >功能类目</th>
                    <th >功能方案</th>
                    <th ></th>
                    <th>注释</th>
                </tr>
            </thead>
            <tbody data-key="area" id="areapanl">
                <tr>
                    <td>一位多车/多车位多车</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_MoreCar" name="PolicyArea_MoreCar">
                            <option value="1">车牌类型场内智能升降级</option>
                            <option value="2">以入场时车牌计费类型为准</option>
                            <option value="3">车主车位已满后其余车辆禁止入场</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置车主一个车位下有一辆或多辆车时，车辆没车位时的指定区域的处理方式。<i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            【车牌类型场内智能升降级】：车主购买了1个指定可停区域的车位，但有2辆车（A和B），当A车先入场，B车后入场，此时B车无车位需要收费，<br />当A车出场后释放车位，B车自动获取车位并暂停收费，出场时仅收取和A车同时在场时间的费用。<br />
                            【以入场时车牌计费类型为准】：A车先入场，B车后入场，此时B车无车位需要收费并且将收取整段停车费用。<br />
                            【车主车位已满后其余车辆禁止入场】：A车入场后，车位被占用，B车不允许入场。
                        </span>
                    </td>

                </tr>
                <tr class="opengate">
                    <td>车主车位已满后其余车辆</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_MoreCarOpen" name="PolicyArea_MoreCarOpen">
                            <option value="3">不设置</option>
                            <option value="0">禁止通行</option>
                            <option value="2">弹框确认</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置车主一个车位下有一辆或多辆车时，没车位车辆入场时的处理方式。<i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            【不设置】：表示该功能无效，按"一位多车/多车位多车"的方式处理；<br />
                            【弹框确认】：没车位车辆入场时，弹出确认放行框，由人工判断是否放行；<br />
                            【禁止通行】：没车位车辆入场时，不允许通行。
                        </span>
                    </td>
                </tr>
                <tr class="simple">
                    <td>免费车是否占用车位数</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_FreeCarUseSpace" name="PolicyArea_FreeCarUseSpace">
                            <option value="0" class="select">占用车位数</option>
                            <option value="1">不占用车位数</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于设置免费车入场后，是否占用车场的余位数。</td>
                </tr>
                <tr>
                    <td colspan="4">
                        <div class="layui-row headmoresetting"><t class="content">更多设置</t>&nbsp;<i class="layui-icon layui-icon-down"></i></div>
                    </td>
                </tr>
                <tr class=" moresetting">
                    <td>访客车是否预留车位数</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_ReserveUseSpace" name="PolicyArea_ReserveUseSpace">
                            <option value="0" class="select">预留车位数</option>
                            <option value="1">不留车位数</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        用于设置访客车预约后是否占用车位数。<br />
                        <t style="color:red;">注意：内场区域设置无效，若设置预留车位，超过预约时间车辆仍未入场，系统自动释放。</t>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>车位统计方式</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_SpaceMode" name="PolicyArea_SpaceMode">
                            <option value="0" class="select">不启用</option>
                            <option value="1">临时车车位统计</option>
                            <option value="2">固定车车位统计</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>
                        <span class="headdesc">用于设置车辆车场的余位统计方式。<i class="help-btn">?</i><br /></span>
                        <span class="otherdesc">
                            【不启用】：按区域统计所有车牌类型的车位。<br />
                            【临时车车位统计】：只统计临时车车牌类型的车位，非临时车入出场车位不变化。<br />
                            【固定车车位统计】：只统计固定车车牌类型的车位，非固定车入出场车位不变化。
                        </span>
                    </td>
                </tr>
                <tr class="simple moresetting">
                    <td>车位统计方式总车位数</td>
                    <td>
                        <input type="text" class="layui-input v-number" id="PolicyArea_SpaceNumber" name="PolicyArea_SpaceNumber" value="0" maxlength="5" />
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于搭配"车位统计方式"配置项使用，可设置临时车或固定车的车位统计数量。</td>
                </tr>

                <tr class="simple moresetting">
                    <td>是否启用防疫设置</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPEnable" name="PolicyArea_EPEnable">
                            <option value="1">启用</option>
                            <option value="0" selected>禁用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>启用后，非重点地区车辆通行需要出示健康码，扫码后符合条件即可放行</td>
                </tr>
                <tr class="fymodal simple moresetting">
                    <td>是否启用身份证读取健康码</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPIDCard" name="PolicyArea_EPIDCard">
                            <option value="1">启用</option>
                            <option value="0" selected>禁用</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>启用时，白名单车辆优先使用登记的身份证号获取健康码数据，获取成功则无需车主扫码.若身份证未填写或获取健康码失败,则继续提示车主扫码.</td>
                </tr>
                <tr class="fymodal simple moresetting">
                    <td>健康码&核酸检测</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPHeSuan" name="PolicyArea_EPHeSuan">
                            <option value="0" selected>不启用</option>
                            <option value="1">绿码</option>
                            <option value="24">绿码+24小时核酸阴性</option>
                            <option value="48">绿码+48小时核酸阴性</option>
                            <option value="72">绿码+72小时核酸阴性</option>
                            <option value="168">绿码+7天内核酸阴性</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>健康码或核酸检测不满足条件则禁止通行</td>
                </tr>
                <tr class="fymodal simple moresetting">
                    <td>抗原检测</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPKangYuan" name="PolicyArea_EPKangYuan">
                            <option value="0" selected>不启用</option>
                            <option value="1">阴性</option>
                            <option value="24">24小时阴性</option>
                            <option value="48">48小时阴性</option>
                            <option value="72">72小时阴性</option>
                            <option value="168">7天内阴性</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>抗原检测不满足条件则禁止通行</td>
                </tr>
                <tr class="fymodal simple moresetting">
                    <td>疫苗针数</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPYmCount" name="PolicyArea_EPYmCount">
                            <option value="0" selected>不启用</option>
                            <option value="1">1针及以上</option>
                            <option value="2">2针及以上</option>
                            <option value="3">3针及以上</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>疫苗针数不满足条件则禁止通行</td>
                </tr>
                <tr class="simple moresetting">
                    <td>重点地区</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPAddress" name="PolicyArea_EPAddress" lay-filter="epp_multiple" multiple lay-search lay-tools>
                            <option value="">重点地区</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>用于选择要控制的省市，根据车牌前缀简称判断(如：粤B表示深圳市)。</td>
                </tr>
                <tr class="simple moresetting">
                    <td>重点地区车辆开闸方式</td>
                    <td>
                        <select class="layui-select" id="PolicyArea_EPPassMode" name="PolicyArea_EPPassMode" lay-search>
                            <option value="3">不设置</option>
                            <option value="0">禁止通行</option>
                            <option value="2">弹框确认</option>
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-sm save">保存</button></td>
                    <td>来自重点地区的车辆，使用重点地区车辆开闸方式。</td>
                </tr>
            </tbody>
        </table>

        <div class="layui-row savebtn">
            <button class="layui-btn layui-btn-sm saveAll" data-id="area">保存全部</button>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}" data-type="${CarCardType_Type}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplrule">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">以 ${CarCardType_Name} 计费规则计费</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1.9" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        if ('@carparking.Config.PubVar.iParkingType' == "1") {
            $("#PolicyPark_MaxDiscount").attr("disabled", true);
        }

        myVerify.init();
        var cp = new CarnoPicker("#PolicyPark_CarPrefix", { ischar: false });
        cp.init();
        layui.use(['element', 'form', 'laydate'], function () {
            pager.init();
        })


        var temparr = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var montharr = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var freearr = ['3656'];//免费车类型
        var prepaidarr = ['3657'];//储值车类型
        var visitorarr = ['3658'];//访客车类型

        var pager = {
            parkareas: null,
            passways: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            drives: null,       //设备型号列表
            links: null,        //通道关联区域列表
            Province: [],
            City: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PolicyCarCard_DelayMaxDate"], { type: 'date' });

                $.post("GetAllPasswayAndCarCardType", {}, function (json) {
                    if (json.success) {
                        pager.passways = json.data.passways;
                        pager.carCardTypes = json.data.carCardTypes;
                        pager.carTypes = json.data.carTypes;
                        pager.drives = json.data.drives;
                        pager.links = json.data.links;

                        $("#PolicyPass_PasswayNo").html($("#tmplpassway").tmpl(json.data.passways));
                        $("#PolicyPass_CarCardTypeNo").html($("#tmplcarcardtype").tmpl(json.data.carCardTypes));
                        $("#PolicyPassway_PasswayNo").html($("#tmplpassway").tmpl(json.data.passways));
                        //$("#PolicyPark_YellowCarType").html($("#tmplcartype").tmpl(json.data.carTypes));
                        //$("#PolicyPark_DefaultDevice").html($("#tmpldrive").tmpl(json.data.drives));
                        $("#PolicyCarCard_ExpireHandle").append($("#tmplrule").tmpl(json.data.carCardTypes));

                        var data = [], tempcar = [];
                        json.data.carCardTypes.forEach(function (item, index) {
                            //data[data.length] = item;
                            if (item.CarCardType_Type != 5) { data[data.length] = item; }//过滤访客车
                            if (item.CarCardType_Type == 1) { tempcar[tempcar.length] = item; }
                        });
                        $("#PolicyCarCard_CarCardTypeNo").html($("#tmplcarcardtype").tmpl(data));
                        $("#PolicyPassway_DefaultCarCardType").append($("#tmplcarcardtype").tmpl(tempcar));
                        $("#PolicyPassway_DefaultCarType").append($("#tmplcartype").tmpl(json.data.carTypes));
                        $("#PolicyPark_DefaultNoneCarType").append($("#tmplcartype").tmpl(json.data.carTypes));
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        pager.parkareas = json.data;
                        var options = '';
                        json.data.forEach(function (item, index) {
                            options += '<option value="' + item.ParkArea_No + '">' + item.ParkArea_Name + '</option>';
                        });
                        $("#PolicyArea_ParkAreaNo").html(options);
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("SltProvinceList", {}, function (json) {
                    if (json.success) {
                        pager.Province = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("SltCityList", {}, function (json) {
                    if (json.success) {
                        pager.City = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                pager.Province.forEach((p, i1) => {
                    if (p.Province_ShortName != null && p.Province_ShortName != '') {
                        var options = '';
                        pager.City.forEach((c, i2) => {
                            if (c.Province_No == p.Province_No)
                                options += '<option value="' + (p.Province_ShortName + c.City_ShortName) + '" >' + c.City_Name + '</option>';
                        });
                        $("#PolicyArea_EPAddress").append(options);
                    }
                });

                layui.form.render();

                $("td").hover(function () {
                    //判断td里有headdesc样式
                    if ($(this).find("span.headdesc").length > 0) {
                        var $td = $(this).find("span.headdesc").siblings(".otherdesc");
                        var $div = $('<div>').append($td.contents().clone());
                        layer.tips($div.html(), this, {
                            tips: [1, '#090a0c'],
                            time: 0,
                            area: '50wh'  // 设置宽度为300px
                        });
                    }
                }, function () {
                    layer.closeAll('tips');
                });
            },
            bindData: function () {
                policy.area.onload();
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    //console.log(data.elem); //得到select原始DOM对象
                    //console.log(data.elem.id); //得到select原始DOM对象
                    //console.log(data.value); //得到被选中的值
                    //console.log(data.othis); //得到美化后的DOM对象

                    var val = data.value;
                    //开闸方式切换通道
                    if (data.elem.id == "PolicyPass_PasswayNo") {
                        policy.pass.onload();
                    }
                    //开闸方式切换车牌类型
                    else if (data.elem.id == "PolicyPass_CarCardTypeNo") {
                        policy.pass.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyPassway_PasswayNo") {
                        policy.passway.onload();
                    }
                    //车牌策略切换类型
                    else if (data.elem.id == "PolicyCarCard_CarCardTypeNo") {
                        policy.card.onload();
                    }
                    //车道设置切换通道
                    else if (data.elem.id == "PolicyArea_ParkAreaNo") {
                        policy.area.onload();
                    }
                    //自定义语音设置
                    else if (data.elem.id == "PolicyPassway_ShowOption") {
                        policy.voicediy(data.elem.id, val);
                    }
                    //开闸方式-未找到入场记录最低收费标准
                    else if (data.elem.id == "PolicyPass_NoFundEnter") {
                        policy.minpayed(data.elem.id, val);
                    }
                    //发布内容
                    else if (data.elem.id == "PolicyPassway_Broadpushinfo") {
                        policy.pushinfo(data.elem.id, val);
                    }
                    //防疫设置
                    else if (data.elem.id == "PolicyArea_EPEnable") {
                        policy.fymodal(data.elem.id, val);
                    }
                    //车主车位已满后其余车辆
                    else if (data.elem.id == "PolicyArea_MoreCar") {
                        policy.opengate(data.elem.id, val);
                    }
                })

                $("button.save").click(function () {

                    if (!myVerify.check()) return;

                    var param = {};
                    $(this).parent().siblings().find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            var v = $(this).val();
                            if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                if (v == null || v == '') v = [];
                                v = JSON.stringify(v);
                            }

                            param[$(this).attr('id')] = v;
                        }
                    });

                    var datakey = $(this).parent().parent().parent().attr("data-key");
                    pager.onSave(datakey, param);
                });

                $("button.saveAll").click(function () {
                    if (!myVerify.check()) return;

                    var param = {};
                    var datakey = $(this).attr("data-id");
                    $("tbody[data-key='" + datakey + "']").find("input,select").each(function () {
                        if (!$(this).hasClass('layui-unselect')) {
                            if (!$(this).closest("tr").hasClass("layui-hide")) {
                                var v = $(this).val();
                                if ($(this).attr('id') == 'PolicyArea_EPAddress') {
                                    if (v == null || v == '') v = [];
                                    v = JSON.stringify(v);
                                }

                                param[$(this).attr('id')] = v;
                            }
                        }
                    });
                    pager.onSave(datakey, param);
                });

                $("#BatchSetPassway").unbind("click").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/BatchSetPassWay",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $("#EditBatch").click(function () {
                    layer.open({
                        type: 2,
                        title: "批量设置",
                        content: "/Policy/EditPassBatch",
                        area: getIframeArea(["800px", "650px"]),
                    })
                });

                $(".headmoresetting").unbind("click").click(function () {
                    var table = $(this).parent().parent().parent().find(".moresetting");
                    var versionType1 = localStorage.getItem("versionType");
                    if ($(table).last().is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                        $(this).find("t").text("隐藏更多设置");
                        $(".savebtn").addClass("bottomButton");
                        if (versionType1 == "simple") {
                            $(".simple").removeClass("hide").removeClass("versionHide").addClass("hide").addClass("versionHide");
                        } else {
                            $(".simple").removeClass("layui-hide").removeClass("versionHide");
                            if ($("#PolicyArea_EPEnable").val() == "1")
                                $(".fymodal").removeClass("layui-hide");
                            else
                                $(".fymodal").removeClass("layui-hide").addClass("layui-hide");

                        }
                    } else {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                        $(this).find("t").text("更多设置");
                        $(".savebtn").removeClass("bottomButton");
                    }
                    $(table).toggle("fast");
                });
            },
            bindPower: function () {
                //iframe跳转过来的怎么改
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                        $("button.saveAll").removeClass("layui-hide");
                    }

                    if (pagePower['EditBatch'] == 'true') {
                        $("#BatchSetPassway").removeClass("layui-hide");
                    }
                });
            },
            onSave: function (datakey, param) {
                //开闸方式保存
                if (datakey == 'pass') {
                    var PolicyPass_PasswayNo = $("#PolicyPass_PasswayNo").val();
                    var PolicyPass_CarCardTypeNo = $("#PolicyPass_CarCardTypeNo").val();
                    var obj = { PolicyPass_PasswayNo: PolicyPass_PasswayNo, PolicyPass_CarCardTypeNo: PolicyPass_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyPass", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车道设置保存
                else if (datakey == 'passway') {
                    var PolicyPassway_PasswayNo = $("#PolicyPassway_PasswayNo").val();
                    var obj = { PolicyPassway_PasswayNo: PolicyPassway_PasswayNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    // 处理PolicyPassway_DoubleGateColorCodes的值，过滤掉空值
                    var selectedColors = $("#PolicyPassway_DoubleGateColorCodes").val();
                    if (selectedColors && selectedColors.length > 0) {
                        selectedColors = selectedColors.filter(function(color) {
                            return color !== "" && color !== null && color !== undefined;
                        });
                    } else {
                        selectedColors = [];
                    }
                    obj.PolicyPassway_DoubleGateColorCodes = encodeURIComponent(JSON.stringify(selectedColors));
                    console.log(obj)
                    $.post("SavePolicyPassway", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车牌类型策略保存
                else if (datakey == 'card') {
                    var PolicyCarCard_CarCardTypeNo = $("#PolicyCarCard_CarCardTypeNo").val();
                    var obj = { PolicyCarCard_CarCardTypeNo: PolicyCarCard_CarCardTypeNo };
                    //obj[key] = val;
                    Object.assign(obj, param);

                    $.post("SavePolicyCarCard", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //车场策略保存
                else if (datakey == 'park') {
                    var obj = {};
                    //obj[key] = val;
                    Object.assign(obj, param);
                    $.post("SavePolicyPark", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
                //区域策略保存
                else if (datakey == 'area') {
                    var obj = {};
                    //obj[key] = val;
                    var obj = { PolicyArea_ParkAreaNo: $("#PolicyArea_ParkAreaNo").val() };
                    Object.assign(obj, param);
                    console.log(obj)
                    $.post("SavePolicyArea", { jsonModel: JSON.stringify(obj) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功");
                        } else {
                            layer.open({
                                icon: 0,
                                content: json.msg,
                                area: ["180px", "auto"],
                                btn: ["我知道了"],
                                closeBtn: false,
                            });
                            //layer.msg(json.msg);
                        }
                    }, "json");
                }
            }
        }

        var policy = {
            area: {
                onload: function () {
                    var parkareano = $("#PolicyArea_ParkAreaNo").val();
                    $.post("GetPolicyArea", { ParkArea_No: parkareano }, function (json) {

                        if (json.data.PolicyArea_EPAddress == null || json.data.PolicyArea_EPAddress == '')
                            json.data.PolicyArea_EPAddress = '[]';
                        var eppData = JSON.parse(json.data.PolicyArea_EPAddress);
                        $("select[name='PolicyArea_EPAddress']").val(eppData);

                        if (json.success) {
                            $("#areapanl").fillForm(json.data, function (data) { });
                            layui.form.render()
                            if (json.data != null) {
                                policy.fymodal("", json.data.PolicyArea_EPEnable);
                                policy.opengate("", json.data.PolicyArea_MoreCar);
                            }
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            },
            voicediy: function (id, val) {
                if (val == 5)
                    $("#PolicyPassway_Show").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Show").removeClass("layui-hide").addClass("layui-hide");
            },
            pushinfo: function (id, val) {
                if (val == 1)
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide");
                else
                    $("#PolicyPassway_Broadpushtext").removeClass("layui-hide").addClass("layui-hide");
            },
            minpayed: function (id, val) {
                if (val == 4)
                    $("#PolicyPass_MinAmount").removeClass("layui-hide");
                else
                    $("#PolicyPass_MinAmount").removeClass("layui-hide").addClass("layui-hide");
            },
            fymodal: function (id, val) {
                if (!$(".fymodal").hasClass("versionHide")) {
                    if (val == 1)
                        $(".fymodal").removeClass("layui-hide");
                    else
                        $(".fymodal").removeClass("layui-hide").addClass("layui-hide");
                }
            },
            opengate: function (id, val) {
                if (!$(".opengate").hasClass("versionHide")) {
                    if (val != 3)
                        $(".opengate").removeClass("layui-hide");
                    else
                        $(".opengate").removeClass("layui-hide").addClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
