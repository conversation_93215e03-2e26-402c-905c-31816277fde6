﻿@using carparking.BLL.Cache
@using carparking.SentryBox
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <meta name="referrer" content="no-referrer">
    <title>@ViewBag.SysConfig_DIYName</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <link href="~/Static/css/plugins/notification/snackbar/snackbar.min.css" rel="stylesheet" />
    <script src="~/Static/css/plugins/notification/snackbar/snackbar.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/blockui/jquery.blockUI.min.js" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/baseInfo.js?3" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/base64.js" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/sha1.js" asp-append-version="true"></script>
    <link href="~/Static/operationscenter/index.css?t=@DateTime.Now.Ticks" rel="stylesheet" />
    <script>
        var sysconfig_playerType = '@ViewBag.sysconfig_playerType';
        if (sysconfig_playerType == undefined || sysconfig_playerType == "" || sysconfig_playerType == "null") sysconfig_playerType = 0;
        else sysconfig_playerType = parseInt(sysconfig_playerType, 10);

        // 获取本地存储的 playerType，并转换为数字
        var playerType = sysconfig_playerType == 0
            ? parseInt(localStorage.getItem("playerType") || "2", 10)
            : sysconfig_playerType;

        // 需要加载的脚本文件
        var scriptsToLoad = [
            playerType === 1
                ? "/Static/flveee/flveee.js?2"
                : "/Static/operationscenter/js/flveee.js?1",
            playerType === 1
                ? "/Static/operationscenter/player_new.js"
                : "/Static/operationscenter/player_old.js"
        ];

        // **通用的动态加载脚本函数（带重试）**
        function loadScript(src, retry = 1, delay = 500) {
            return new Promise((resolve, reject) => {
                function attemptLoad(remainingAttempts) {
                    var script = document.createElement("script");
                    script.src = src;
                    script.async = true;
                    script.onload = () => {
                        console.log("Script loaded:", src);
                        resolve();
                    };
                    script.onerror = () => {
                        console.error("Failed to load script " + remainingAttempts + ":", src);
                        console.log("Retrying:", src);
                        document.head.removeChild(script);  // 移除当前的 script 标签
                        setTimeout(() => attemptLoad(remainingAttempts + 1), delay); // 延时后重试
                    };
                    document.head.appendChild(script);
                }

                attemptLoad(retry);
            });
        }

        // **并行加载所有脚本**
        async function loadScriptsAndInit() {
            try {
                await Promise.all(scriptsToLoad.map(src => loadScript(src))); // 失败后重试
                console.log("All scripts loaded successfully!");
            } catch {
                console.error("Some scripts failed to load after retrying.");
            }
        }

        (async function () {
            await loadScriptsAndInit();  // 等待所有脚本加载完毕
            console.log("All scripts are ready. Now you can initialize your app.");
        })();
    </script>

    <script>
        var uiVersion = "standard-font";
        window.gotoChangeUI = function (first) {
            try {
                // 读取缓存中的 UI 版本
                var cacheUiVersion = localStorage.getItem("b30uiversion");

                // 第一次加载时，使用缓存版本，如果没有缓存则默认 standard-font
                if (first) {
                    uiVersion = cacheUiVersion ?? "standard-font";
                } else {
                    if (uiVersion == "standard-font") uiVersion = "large-font"; else uiVersion = "standard-font";
                    // 更新 localStorage 存储的 UI 版本
                    localStorage.setItem("b30uiversion", uiVersion);
                }

                // 刷新页面以确保新 UI 版本生效
                if (!first) {
                    window.location.reload();
                }

                return uiVersion;
            } catch (error) {
                console.error("发生错误：", error);
                alert("版本文件加载失败");
                return "standard-font"; // 出错时默认返回 standard-font
            }
        };

        $(document).ready(function () {
            uiVersion = gotoChangeUI(true)

            // 根据版本选择CSS文件
            var cssFile = uiVersion === "large-font"
                ? "/Static/operationscenter/css/largefont.css"
                : "/Static/operationscenter/css/standardfont.css";

            // 添加时间戳
            var timestamp = new Date().getTime();
            var cssUrl = cssFile + "?t=" + timestamp;

            // 动态创建<link>标签
            $('<link>')
                .attr({
                    rel: 'stylesheet',
                    type: 'text/css',
                    href: cssUrl
                })
                .appendTo('head');
        });
    </script>
    <style>
        .customClass { font-size: 20px !important; }

        .layui-table,
        .layui-table-view { margin: 0; }

        .nowTime { margin-top: 2rem; text-align: center; color: #5868e0 !important; font-size: 1.3rem; text-overflow: ellipsis; white-space: nowrap; text-align: left; }

        .nowTime dd { color: #5868e0 !important; }

        span.question { width: 1.4vw; right: 1px; top: 1px; height: 3vh; cursor: pointer; line-height: 3vh; text-align: center; background-color: #043857; color: #00a1ff; border-top-right-radius: 2px; border-bottom-right-radius: 2px; font-size: 1.4vw; }

        .vUnpaid { display: inline; color: red; border-bottom: 1px solid #1E9FFF; width: 8rem; border-top: 0; border-right: 0; border-left: 0; height: 2rem; font-size: 2rem !important; }

        .layui-form-select dl { box-shadow: 0 2px 4px rgb(143 142 255 / 85%); border: 0px !important; padding: 0px 0; }

        .outcar_money[readonly] { background-color: #ebebeb; }

        .layui-form-checkbox[lay-skin=primary] span { color: #f45454 !important; font-size: 1.2vw; height: 2.1vh; line-height: 2.1vh; }

        .layui-form-checkbox[lay-skin=primary]:hover i { border-color: #f45454; }
        .reminderinfo { position: absolute; z-index: 1; font-size: 2vw; font-weight: 600; color: #f11b1b; padding-top: .3vh; line-height: 3vh; }
        .layui-icon.layui-icon-speaker,
        .layui-icon-more { font-size: 3.5rem; color: #1E9FFF; font-weight: bold; vertical-align: middle; }

        .edit-icon { margin-left: 5px; font-size: 1.5vw; padding: 0; padding-left: 3px !important; line-height: 2vw !important; height: 2vw; width: 2vw; cursor: pointer; color: #009688; z-index: 999999; background-color: #e7efef; border-radius: 5px; opacity: .9; }

        .edit-icon:hover { color: #fff; background-color: #009688; }

        .action-popup { position: absolute; border-radius: 4px; margin-right: 5px; padding: 2px; width: 20vw; height: 5.7vh; line-height: 5.7vh; text-align: left; }

        .action-popup button,
        .inWinBtn { background: #043857; border-color: #1E9FFF !important; color: #fff !important; font-weight: bolder; margin-right: 5px; margin-bottom: 4px; font-size: 1.8vw; height: 5vh; line-height: 5vh; border-radius: 5px; }
        .monitor .layui-laypage-limits { margin-left: 0 !important; }
        .monitor .layui-laypage-limits select { height: 1.9vh !important; border-color: #fff !important; font-size: 1.5vh; }
        .monitor .layui-laypage a, .layui-laypage span { height: 3vh !important; line-height: 3vh !important; border: 0 !important; font-size: 1.5vh; }
    </style>
</head>

<body oncontextmenu="return false">
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="header">
        <div class="title" id="BSPlatformName">@ViewBag.SysConfig_DIYName</div>
        <div class="menus">
            <ul>
                <li onclick="btns.onBarClick(this)" data-key="0" title="点击刷新">
                    <span> <img src="~/Static/img/icon/icon_bar_refash.png" style="width: 2.4rem;" /></span>
                </li>
                <li onclick="btns.onBarClick(this)" data-key="2" id="moreMenu">
                    <span><img src="~/Static/img/icon/icon_bar_set.png" /></span>
                    <span> 菜单</span>
                    <dl class="menus-bar layui-hide">
                        @{
                            if (ViewBag.WinFormMonitor != null && ViewBag.WinFormMonitor["OpenWeb"] != null &&
                            ViewBag.WinFormMonitor["OpenWeb"] == "true")
                            {
                                <dd data-key="0" onclick="btns.onMenuBarClick(this)">管理后台</dd>
                            }
                        }
                        <dd data-key="1" onclick="btns.onMenuBarClick(this)">播放设置</dd>
                        <dd data-key="8" onclick="btns.onMenuBarClick(this)">界面设置</dd>

                        @{
                            if (ViewBag.WinFormMonitor != null && ViewBag.WinFormMonitor["PrintReceipt"] != null &&
                            ViewBag.WinFormMonitor["PrintReceipt"] == "true")
                            {
                                <dd data-key="66" onclick="btns.onMenuBarClick(this)">打印小票</dd>
                            }
                        }

                        @*    <dd data-key="6" onclick="btns.onMenuBarClick(this)">界面设置</dd> *@
                        @if (carparking.Config.AppSettingConfig.SentryMode != "2")
                        {
                            <dd data-key="3" onclick="btns.onMenuBarClick(this)">黑白名单</dd>
                        }
                        @{
                            if (ViewBag.WinFormMonitor != null && ViewBag.WinFormMonitor["ChangeShifts"] != null &&
                            ViewBag.WinFormMonitor["ChangeShifts"] == "true")
                            {
                                <dd data-key="5" onclick="btns.onMenuBarClick(this)">换班登录</dd>
                            }
                        }

                        @{
                            if (ViewBag.WinFormMonitor != null && ViewBag.WinFormMonitor["DebugTool"] != null &&
                            ViewBag.WinFormMonitor["DebugTool"] == "true")
                            {
                                <dd data-key="100" onclick="btns.onBarClick(this)">调试工具</dd>
                            }
                        }

                        <dd data-key="4" onclick="btns.onMenuBarClick(this)">关于软件</dd>
                        <dd data-key="2" onclick="btns.onMenuBarClick(this)">退出登录</dd>
                    </dl>
                </li>
                @{
                    if (ViewBag.WinFormMonitor != null && ViewBag.WinFormMonitor["HandleEvent"] != null &&
                    ViewBag.WinFormMonitor["HandleEvent"] == "true")
                    {
                        if (carparking.Config.AppSettingConfig.SentryMode != "2")
                        {
                            <li onclick="btns.onBarClick(this)" data-key="1999" id="eventMenu">
                                <span class="total layui-hide"> 0</span>
                                <span><img src="~/Static/img/icon/lingdang.png" class="eventpng" /> 事件提醒</span>
                            </li>
                        }
                    }
                }
                <!--<li onclick="btns.onBarClick(this)" data-key="1"><span><img src="~/Static/img/icon/icon_bar_secreen.png" /></span><span> 全屏显示</span></li>-->
                @if (carparking.Config.AppSettingConfig.SentryMode != "2")
                {
                    <li onclick="btns.onBarClick(this)" data-key="3">
                        <span> <img src="~/Static/img/icon/icon_bar_recod.png" /> 记录查询</span>
                    </li>
                    @* <li onclick="btns.onBarClick(this)" data-key="8">
                <span> <img src="~/Static/img/icon/icon_bar_yincar.png" /> 预入场</span>
                </li>
                <li onclick="btns.onBarClick(this)" data-key="9">
                <span> <img src="~/Static/img/icon/icon_bar_youtcar.png" /> 预出场</span>
                </li> *@
                    <li onclick="btns.onBarClick(this)" data-key="4">
                        <span> <img src="~/Static/img/icon/icon_bar_car.png" /> 固定车查询</span>
                    </li>
                }
                <li onclick="btns.onBarClick(this)" data-key="6" id="LanMenu">
                    <span> <img src="~/Static/img/icon/CameraStatusIcon_NoTip.png" /> 车道监控</span>
                    <dl class="menus-bar layui-hide barList">
                        <dd data-key="0" class="land">
                            <span>暂无车道数据</span>
                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
                        </dd>
                    </dl>
                </li>

            </ul>
        </div>
    </div>
    <div class="content layui-form">
        <div class="left">
            <div class="monitor">
                <!--在线监控-->
            </div>

            <div class="record">
                <div class="item layui-col-md6">
                    <div class="item-box">
                        <div class="trends">
                            <div class="trends-1">
                                <div class="trends-pic" id="rlt_enterimg">
                                    <a href="#" data-fancybox="images" data-caption="">
                                        <img src="~/Static/img/img-nothing.png"
                                             onerror="this.src='../Static/img/img-nothing.png'" />
                                    </a>
                                    <div id="rlt_enterimgsmall" style="position: absolute; top: 0; width: 13rem;">
                                    </div>
                                </div>
                                <div class="trends-pic picright" id="rlt_outimg">
                                    <a href="#" data-fancybox="images" data-caption="">
                                        <img src="~/Static/img/img-nothing.png"
                                             onerror="this.src='../Static/img/img-nothing.png'" />
                                    </a>
                                    <div id="rlt_outimgsmall" style="position:absolute;top:0;right:0;width:13rem;">
                                    </div>
                                </div>
                            </div>
                            <div class="trends-1">
                                <div class="layui-row info">
                                    <div class="layui-col-xs6">
                                        <label>车牌：</label>
                                        <value id="rlt_carno"></value>
                                    </div>
                                    <div class="layui-col-xs6">
                                        <label class="rightlabel">类型：</label>
                                        <value id="rlt_card"></value>
                                    </div>
                                </div>
                                @*<div class="layui-row info"><label>车型：</label><value
                                id="rlt_cartype">等待车辆通行后更新</value></div>*@
                                <div class="layui-row info">
                                    <div class="layui-col-xs6">
                                        <label>车道：</label>
                                        <value id="rlt_passway"></value>
                                    </div>
                                    <div class="layui-col-xs6">
                                        <label class="rightlabel">区域：</label>
                                        <value id="rlt_area"></value>
                                    </div>
                                </div>
                                <div class="layui-row info">
                                    <div class="layui-col-xs6">
                                        <label>时间：</label>
                                        <value id="rlt_time"></value>
                                    </div>
                                    <div class="layui-col-xs6">
                                        <label class="rightlabel">时长：</label>
                                        <value id="rlt_calctime"></value>
                                    </div>
                                </div>

                                <div class="layui-row info">
                                    <div class="layui-col-xs6">
                                        <label>车主：</label>
                                        <value id="rlt_owner"></value>
                                    </div>
                                    <div class="layui-col-xs6">
                                        <label class="rightlabel">备注：</label>
                                        <value id="rlt_remark"></value>
                                    </div>
                                </div>
                                <div class="layui-row info">
                                    <div class="layui-col-xs6">
                                        <label>实收：</label>
                                        <value id="rlt_calcmoney"></value>
                                    </div>
                                    <div class="layui-col-xs6">
                                        <label class="rightlabel">提示：</label>
                                        <value id="rlt_errmsg"></value>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="item layui-col-md6">
                    <div class="item-box">
                        <div class="layui-card" style="box-shadow:none;height:100%;">
                            <div class="layui-card-header" style="height:6vh;line-height:6vh;padding:0 5px;">
                                <div class="inparkrecordtxt" style="float:left;color: #5868e0; ">场内记录</div>
                                <input class="layui-input" placeholder="输入任意字符查询"
                                       style="float:left;"
                                       id="inParkOrder_CarNo" autocomplete="off" />
                                <button class="layui-btn layui-btn-sm layui-icon layui-icon-search"
                                        id="inparkorder-search"
                                        style="float:left;">
                                    查询
                                </button>
                            </div>
                            <div class="layui-card-body" style="padding:0;height:calc(100% - 6vh)">
                                <table class="layui-table" lay-filter="inparkorder-table" id="inparkorder-table"
                                       style="margin:0; ">
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="outPasswayChosen layui-anim layui-anim-fadein layui-hide"
                         onclick="$(this).addClass('layui-hide');">
                        <div class="box">
                            <div class="chheader">请选择出口</div>
                            <div class="chcontent">
                                <ul id="chosenOutGate">
                                    <li>1</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="layui-row pannel notice" id="notice">
                <div class="layui-icon layui-icon-notice labelim"> 提示信息</div>
            </div>
            <div class="layui-row pannel work">
                <dl class="first">
                    <dd>
                        <div class="layui-icon layui-icon-rmb moneytip">
                            <span class="second">
                                &nbsp;当班累计现金：<t id="work_0">0.00</t>
                            </span>
                        </div>
                    </dd>
                </dl>
                <dl>
                    <dd>应收金额：</dd>
                    <dd id="work_1">0.00</dd>
                </dl>
                <dl>
                    <dd>减免金额：</dd>
                    <dd id="work_2">0.00</dd>
                </dl>
                <dl>
                    <dd>实收金额：</dd>
                    <dd id="work_3">0.00</dd>
                </dl>
                <dl>
                    <dd>人工放行：</dd>
                    <dd id="work_4">0.00</dd>
                </dl>
                <dl>
                    <dd>特殊车辆：</dd>
                    <dd id="work_5">0.00</dd>
                </dl>
                <dl>
                    <dd>上班时间：</dd>
                    <dd id="work_6">0.00</dd>
                </dl>
                <dl>
                    <dd>值班人员：</dd>
                    <dd id="work_7">0.00</dd>
                </dl>
                <dl>
                    <dd>电子支付金额：</dd>
                    <dd id="work_8">0.00</dd>
                </dl>
                <dl class="nowTime">
                    <dd>当前时间：</dd>
                    <dd>
                        <div id="time"></div>
                    </dd>
                </dl>
            </div>
            <div class="layui-row pannel space">
                <div style="position: relative;">
                    <ul class="total">
                        <li>
                            @{
                                if (ViewBag.WinFormMonitor != null && ViewBag.WinFormMonitor["ModifySpace"] != null &&
                                ViewBag.WinFormMonitor["ModifySpace"] == "true")
                                {
                                    <div style="position: absolute; left: 1px; top: 1px; z-index: 110;" title="点击修改余位">
                                        <i class="layui-icon layui-icon-util" style="cursor: pointer; font-size: 2.3rem; color: #7c7e81; border-radius: 10%;" onclick="openModifyParkSpace()"></i>
                                    </div>
                                }
                            }

                            <div style="margin-left: 10px;"><span>总车位</span><text id="space_0">0</text></div>
                            <div style="margin-left: 10px;"><span>总余位</span><text id="space_1">0</text></div>
                        </li>
                    </ul>
                    <ul class="area" id="space_area">
                    </ul>
                </div>
            </div>
            <div class="layui-row pannel state">
                <dl class="connet" id="cloudOnline">
                    <dd class="layui-icon layui-icon-upload-drag"></dd>
                    <dd class="ctip">正在连接</dd>
                </dl>
                <dl class="connet" id="hostOnline">
                    <dd class="layui-icon layui-icon-website"></dd>
                    <dd class="ctip">主机连接正常</dd>
                </dl>
            </div>
        </div>
    </div>
    <!--过期登录-->
    <div>
        <!--功能扩展-自动弹出登录，由全局ajax控制-->
        <div id="modal-form" class="modal fade in" aria-hidden="true" data-backdrop="static" style="margin-top: 12%;">
            <div class="modal-dialog" style="width: 300px;">
                <div class="modal-content" style="height: 230px;">
                    <div class="modal-body">
                        <button type="button" class="close hide" data-dismiss="modal">
                            <span aria-hidden="true">×</span><span class="sr-only">Close</span>
                        </button>
                        <div class="row">
                            <div style="width: 100%; height: 100%;">
                                <h5 class="m-t-none m-b text-center" style="color: #1ab394;padding:10px 0;">
                                    登录超时,请重新登录！
                                </h5>
                                @* <div class="layui-row" style="padding:5px 20px;">
                                <input type="text" id="username" placeholder=" 请输入用户名"
                                class="layui-input text-input" autocomplete="off" disabled />
                                </div> *@
                                <div class="layui-row" style="padding:5px 20px;">
                                    <input type="password" id="password" placeholder=" 请输入密码"
                                           class="layui-input text-input" autocomplete="off" />
                                </div>
                                <div class="layui-row">
                                    <button type="button" id="btn_Login"
                                            style="width: 80%; margin:20px 10% 0;color:#fff !important;"
                                            data-loading-text="<div style='text-align:center; margin:0 auto;'><div style='display:inline-block;width:45px;'>登录中</div><div class='sk-spinner sk-spinner-three-bounce' style='display:inline-block;width:45px;'><div class='sk-bounce1'></div><div class='sk-bounce2'></div><div class='sk-bounce3'></div></div></div>"
                                            class="layui-btn">
                                        登&nbsp;&nbsp;录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--提示消息列表-->
    <div class="layui-anim layui-anim-downbit layui-hide" id="msgBox">
        <div class="close layui-icon layui-icon-close"></div>
        <div style="padding: 1rem; font-size: 2rem; height: calc(100% - 2rem); overflow: auto;color:#0094ff;">
            <ul id="msgContent">
            </ul>
        </div>
    </div>
    <div class="floatBottom layui-row layui-form layui-hide" id="winDraggable">
        <button class="docfb-popup__btn" id="toggleButton"><i class="layui-icon layui-icon-down"></i></button>

        <div class="box" id="box">
            <div class="way">
                <ul>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                    <li class="hide"></li>
                </ul>
            </div>
            <div class="ops">
                <ul class="outwin_ul">
                    @* 出口弹窗 *@
                    <li class="out layui-hide" data-key="0" data-orderno="">

                        <div class="lt" style="width:45%">
                            <dl>
                                <dd class="img"><img class="entercar_img" src="" data-src="" onerror="this.src='../Static/img/img-nothing.png'" /></dd>
                                <dd class="vlabel outlabel"><label>入口：</label><value class="enter_passway"></value></dd>
                                <dd class="vlabel outlabel"><label>区域：</label><value class="enter_area"></value></dd>
                                <dd class="vlabel"><label>时间：</label><value class="enter_time"></value></dd>
                            </dl>
                            <dl>
                                <dd class="img">
                                    <img class="outcar_img" src="" data-src="" onerror="this.src='../Static/img/img-nothing.png'" />
                                    <img class="imgsmall" src="" data-src="" onerror="this.style='display:none'" />
                                </dd>
                                <dd class="vlabel outlabel"><label>出口：</label><value class="out_passway"></value></dd>
                                <dd class="vlabel outlabel"><label>区域：</label><value class="out_area"></value></dd>
                                <dd class="vlabel"><label>时间：</label><value class="out_time"></value></dd>
                            </dl>
                        </div>
                        <div class="rt outWin" style="width:55%">

                            <div class="layui-card" style="box-shadow:none;padding:0 !important;background-color:#043857;">
                                <div class="layui-card-body">
                                    <div class="layui-row" style="margin-bottom: .5rem;">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">车牌：</label>
                                            <value class="enterinput outcarno input-group" style="float: none !important;color:#fff;background-color: rgba(0,0,0,0) !important;">
                                                <div class="input-group">
                                                    <div>
                                                        <input onkeyup="GetInCarList(this)" class="layui-input outcar_carno iptcarno" style="float: none !important;color:#fff;background-color: transparent;" id="outcar_carno0" maxlength="10" autocomplete="off" onkeyup="this.value = this.value.toUpperCase()" />
                                                    </div>
                                                    <span class="input-group-btn" style="vertical-align: top;">
                                                        <button class="layui-btn layui-btn-xs layui-btn-primary btnsearch" data-passwayno="" onclick="btns.onGetOrderPay(this)" title="点击查询车辆停车状态"><i class="layui-icon layui-icon-search"></i></button>
                                                    </span>
                                                </div>
                                            </value>
                                            <div class="selcarno hide">
                                                <dd class="carlist">
                                                </dd>
                                            </div>
                                        </div>
                                        <div class="layui-col-md5" style="padding-top:0.45rem;text-align: left;padding-left: 10px;">
                                            <input type="checkbox" class="outcar_noincar" title="无入场记录出场" lay-skin="primary" data-passwayno="0" />
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">颜色：</label>
                                            <value class="enterinput">
                                                <select class="layui-selet outcar_cartype" lay-search data-passwayno="0">
                                                </select>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">应收金额：</label>
                                            <value class="entervalue orderamount" data-id="orderamount">
                                            </value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="entervalue">车型：</label>
                                            <value class="enterinput">
                                                <select class="layui-selet out_carcardtype" lay-search data-passwayno="0">
                                                </select>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">优惠金额：</label>
                                            <value class="entervalue out_couponamount" data-id="couponamount"></value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">折扣：</label>
                                            <value class="enterinput">
                                                <div class="div_out_coupon" data-passwayno="" id="div_out_coupon"></div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">已收金额：</label>
                                            <value class="entervalue out_chuzhiamount" data-id="chuzhiamount"></value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">免费：</label>
                                            <value class="enterinput">
                                                <div>
                                                    <input class="layui-input outcar_free" style="width: calc(100% - 42px); float: left;" value="" maxlength="50" />
                                                    <div style="width: 38px; float: left;">
                                                        <select class="layui-selet outremark" lay-filter="outremark" id="selFree">
                                                        </select>
                                                    </div>
                                                </div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5" style="white-space: nowrap;">
                                            <label class="entervalue">计费时长：</label>
                                            <value class="entervalue out_parktimemin" data-id="parktimemin"></value>
                                        </div>
                                    </div>

                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">实收：</label>
                                            <value class="enterinput">
                                                <div class="input-group">
                                                    <div>
                                                        <input class="layui-input v-null v-floatLimit outcar_money" readonly style="width: 100%; float: left;" data-id="payedamount" data-key="5" maxlength="8" placeholder="按回车键保存" value="" />
                                                    </div>
                                                    <span class="input-group-btn" style="vertical-align: top;">
                                                        <button type="button" class="btn btn-primary modify" style="padding: 8px 12px !important;background-color: #657de5;border-color: #999;"><i class="fa pwd-edit fa-rotate-left"></i></button>
                                                    </span>
                                                </div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5" style="white-space: nowrap;">
                                            <label class="entervalue">计费详情：</label>
                                            <value class="entervalue">
                                                <span class="out_isnonecar hide" title="多位多车">多位多车</span>
                                                <span class="out_isovertime hide" title="超时缴费">超时缴费</span>
                                                <span class="out_isoverdue hide" title="过期缴费">过期缴费</span>
                                                <span class="out_payedmag hide"></span>
                                                <span class="question fa fa-table" title="点击查看计费详情"></span>
                                            </value>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-card-body">
                                    <button class="layui-btn out out_enterpass" data-key="4" onclick="">确认放行<t style="font-size:1vw;">(F1)<t></button>
                                    <button class="layui-btn out out_enterfreepass" data-key="3" onclick="">免费放行</button>
                                    <button class="layui-btn out layui-border-blue out_cancel" data-key="0" onclick="">取消</button>
                                    <a class="out_video" href="javascript:;" data-key="1" title="点击播报" onclick=""><i class="layui-icon layui-icon-speaker"></i></a>
                                    <div class="more-actions" style="position: relative;display: inline-block;">
                                        <a href="javascript:;" data-key="99" title="点击更多" onclick="showMoreActions(this)" class="moreicon"><i class="layui-icon layui-icon-more"></i></a>
                                        <div class="action-popup layui-hide">
                                            <button class="layui-btn layui-btn-sm out_detail" onclick="" data-key="101">订单详情</button>
                                            <button class="layui-btn layui-btn-sm out_morespace" onclick="" data-key="102">多车多位</button>
                                            <button class="layui-btn layui-btn-sm out_awaysvoice" onclick="" data-passwayno="">常用语音</button>
                                            <button class="layui-btn  layui-btn-sm out_print" onclick="" data-key="100">打印小票</button>
                                        </div>
                                    </div>
                                    <div class="reminderinfo"></div>
                                </div>
                            </div>
                        </div>
                    </li>
                    @* 入口弹窗 *@
                    <li class="in layui-hide" data-key="0" data-orderno="">
                        <div class="lt" style="width:40%">
                            <dl>
                                <dd class="img">
                                    <img class="enter_img" onerror="this.src='../Static/img/img-nothing.png'" />
                                    <img class="imgsmall" onerror="this.style='display:none'" />
                                </dd>
                            </dl>
                        </div>
                        <div class="rt" style="width:60%">
                            <div class="layui-card" style="box-shadow:none;background-color:#043857;">
                                <div class="layui-card-body">
                                    <div class="layui-row">
                                        <div class="layui-col-md7 mtop">
                                            <label class="enterlabel">输入车牌：</label>
                                            <value class="enterinput">
                                                <input class="layui-input enter_carno iptcarno" id="enter_carno_0" maxlength="10" autocomplete="off" onkeyup="this.value = this.value.toUpperCase()" style="float: none !important;color:#fff;background-color:rgba(0,0,0,0) !important;" />
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">通行区域：</label><value class="entervalue enter_area"></value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">车牌颜色：</label>
                                            <value class="enterinput">
                                                <select class="layui-selet enter_cartype" lay-search>
                                                </select>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">放行车道：</label><value class="entervalue passwayname"></value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">车牌类型：</label>
                                            <value class="enterinput">
                                                <div>
                                                    <select class="layui-selet enter_carcardtype" lay-search data-passwayno="0">
                                                    </select>
                                                </div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">通行时间：</label><value class="entervalue enter_time"></value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">入场备注：</label>
                                            <value class="enterinput">
                                                <div>
                                                    <input class="layui-input enter_remark" style="width: calc(100% - 42px); float: left;" />
                                                    <div style="width: 38px; float: left;">
                                                        <select class="layui-selet enterremark" lay-filter="enterremark">
                                                        </select>
                                                    </div>
                                                </div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                        </div>
                                    </div>
                                    <div class="layui-row mtop layui-hide">
                                        <div class="layui-col-md7">
                                            <label class="entervalue">补缴金额：</label>
                                            <input class="layui-input vUnpaid" data-id="unpaidresultamount" readonly />
                                            <span class="question fa fa-table" title="点击查看计费详情"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-card-body">
                                    <button class="layui-btn in in_enterpass" data-key="2">确认放行<t style="font-size:1vw;">(F2)<t></button>
                                    <button class="layui-btn in inWinBtn layui-btn-sm enter_morespace" onclick="" data-key="102">多车多位</button>
                                    <div class="layui-inline">
                                        <button class="layui-btn in inWinBtn layui-btn-sm enter_awaysvoice" onclick="" data-passwayno="">常用语音</button>
                                    </div>
                                    <button class="layui-btn in inWinBtn layui-btn-sm in_print" data-key="99" onclick="">打印小票</button>
                                    <button class="layui-btn in in_cancel layui-btn-primary layui-border-blue" data-key="0">取消</button>
                                    <div class="reminderinfo"></div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!--在线监控-->
    <script type="text/x-jquery-tmpl" id="tmplvideo">
        <div class="item layui-col-md6">
            {{if isload}}
            <div class="item-box" id="${id}" data-passwayno="${model.Passway_No}">
                <div class="item-main">

                    <div class="item-body">
        @* <div class="passway_select">
                            <select class="video"  lay-search data-key="${id}" data-modelno="${model.Passway_No}">
                                {{each data}}
                                <option value="${$value.Passway_No}" {{if $value.Passway_No==model.Passway_No}} selected {{/if}}>${$value.Passway_Name}</option>
                                {{/each}}
                            </select>
                        </div> *@
                        <div class="passway_state">
                                <span class="layui-icon lancar"><img src="../Static/img/icon/NoCar.png" title="未知" /></span>
                                <span class="layui-icon chedao"><img src="../Static/img/icon/LaneStatusIcon_Tip.png" title="未知" /></span>
                                <span class="layui-icon xiangji"><img src="../Static/img/icon/CameraStatusIcon_Tip.png" title="未连接" /></span>
                        </div>
                        <div class="layui-row videobox" id="${id}_play" onclick="videoobj.clickVideoForWin(this,'${model.Passway_No}')">
                            <img src="../Static/img/snapvideo2.png" onerror="this.src='../Static/img/img-nothing.png'" />
                        </div>
                    </div>
                </div>
                <div class="item-btns">
                    <button data-key="888" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-danger layui-icon layui-icon-up" ></button>

                    {{if formPower.GateSwitch}}
                            <button data-key="4" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-normal">开闸</button>
                    {{/if}}

                    {{if formPower.PassCarNo}}
                        <button data-key="2" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm">手动放行</button>
                    {{/if}}

                    <div class="item-btns-other2 layui-hide">
                        <button data-key="7" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm">播报</button>
                        <button data-key="0" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm">识别</button>
                        {{if formPower.GateCancel}}
                        <button data-key="6" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-normal" data-long="${model.IsLongOpen}">{{if model.IsLongOpen=="1"}}取消常开{{else}}常开{{/if}}</button>
                        {{/if}}
                        {{if formPower.GateSwitch}}
                        <button data-key="3" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-danger">关闸</button>
                        {{/if}}
                    </div>

                    <div class="passway_select">
                        <select class="video"  lay-search data-key="${id}" data-modelno="${model.Passway_No}">
                            {{each data}}
                            <option value="${$value.Passway_No}" {{if $value.Passway_No==model.Passway_No}} selected {{/if}}>${$value.Passway_Name}</option>
                            {{/each}}
                        </select>
                    </div>

                </div>
            </div>
            {{else}}
            <div class="item-box" id="${id}">

            </div>
            {{/if}}
        </div>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplvideo2">
        <div class="item layui-col-md3 item2">
            {{if isload}}
            <div class="item-box item-box2" id="${id}" data-passwayno="${model.Passway_No}">
                <div class="item-main">
                    <div class="item-body">
                        <div class="layui-row videobox videobox2" onclick="videoobj.clickVideoForWin(this,'${model.Passway_No}')" id="${id}_play">
                            <img src="../Static/img/snapvideo2.png" onerror="this.src='../Static/img/img-nothing.png'" />
                        </div>
                    </div>
                </div>

                <div class="item-btns videoBtn" style="display:none;z-index:999">
                     <button data-key="888" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-danger layui-icon layui-icon-up" ></button>

                    {{if formPower.GateSwitch}}
                            <button data-key="4" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-normal">开闸</button>
                    {{/if}}

                    {{if formPower.PassCarNo}}
                        <button data-key="2" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm">手动放行</button>
                    {{/if}}

                     <div class="item-btns-other2 layui-hide">
                        <button data-key="7" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm">播报</button>
                        <button data-key="0" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm">识别</button>
                        {{if formPower.GateCancel}}
                        <button data-key="6" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-normal" data-long="${model.IsLongOpen}">{{if model.IsLongOpen=="1"}}取消常开{{else}}常开{{/if}}</button>
                        {{/if}}
                        {{if formPower.GateSwitch}}
                        <button data-key="3" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-danger">关闸</button>
                        {{/if}}
                     </div>

                </div>

            </div>
            <div class="videoTitle"><p>${model.Passway_Name}</p></div>
            {{else}}
            <div class="item-box" id="${model.Passway_ID}">

            </div>
            {{/if}}


        </div>
    </script>
    <!--入口弹窗-->
    <script type="text/x-jquery-tmpl" id="opsliIn">
        <ul>
            <li class="in layui-hide" data-key="${id}" data-orderno="${orderno}">
                <div class="lt" style="width:40%">
                    <dl>
                        <dd class="img">
                            <img class="enter_img" src="${enter_img}" data-src="${enter_dataimg}" onerror="this.src='../Static/img/img-nothing.png'" />
                            <img class="imgsmall" src="${imgsmall}" data-src="${dataimgsmall}" onerror="this.style='display:none'" />
                        </dd>
                    </dl>
                </div>
                <div class="rt"  style="width:60%">
                    <div class="layui-card" style="box-shadow:none;background-color:#043857;">
                        <div class="layui-card-body">
                            <div class="layui-row">
                                <div class="layui-col-md7 mtop">
                                    <label class="enterlabel">输入车牌：</label>
                                    <value class="enterinput">
                                        <input class="layui-input enter_carno" id="enter_carno${id}" value="${carno}" maxlength="10" autocomplete="off" onkeyup="this.value = this.value.toUpperCase()"  style="float: none !important;color:#fff;background-color:rgba(0,0,0,0) !important;" />
                                    </value>
                                </div>
                                <div class="layui-col-md5">
                                    <label class="entervalue">通行区域：</label><value class="entervalue">${enter_area}</value>
                                </div>
                            </div>
                            <div class="layui-row mtop">
                                <div class="layui-col-md7">
                                    <label class="enterlabel">车牌颜色：</label>
                                    <value class="enterinput">
                                       {{if formPower.ModifyTypeColor}}
                                        <select class="layui-selet enter_cartype" lay-search>
                                            {{each pager.cartypes}}
                                            <option value="${$value.CarType_No}" {{if $value.CarType_Name==cartype}} selected {{/if}}>${$value.CarType_Name}</option>
                                            {{/each}}
                                        </select>
                                       {{else}}
                                         <select class="layui-selet enter_cartype" lay-search disabled>
                                            {{each pager.cartypes}}
                                            <option value="${$value.CarType_No}" {{if $value.CarType_Name==cartype}} selected {{/if}}>${$value.CarType_Name}</option>
                                            {{/each}}
                                        </select>
                                       {{/if}}
                                    </value>
                                </div>
                                <div class="layui-col-md5">
                                    <label class="entervalue">放行车道：</label><value class="entervalue">${passwayname}</value>
                                </div>
                            </div>
                            <div class="layui-row mtop">
                                <div class="layui-col-md7">
                                    <label class="enterlabel">车牌类型：</label>
                                    <value class="enterinput">
                                        <div>
                                            {{if formPower.ModifyType}}
                                                    <select class="layui-selet enter_carcardtype" lay-search data-passwayno="${id}">
                                                        {{each pager.cards}}
                                                        <option value="${$value.CarCardType_No}" {{if $value.CarCardType_Name==enter_cardname}} selected {{/if}}>${$value.CarCardType_Name}</option>
                                                        {{/each}}
                                                    </select>
                                            {{else}}
                                                    <select class="layui-selet enter_carcardtype" lay-search data-passwayno="${id}" disabled>
                                                        {{each pager.cards}}
                                                        <option value="${$value.CarCardType_No}" {{if $value.CarCardType_Name==enter_cardname}} selected {{/if}}>${$value.CarCardType_Name}</option>
                                                        {{/each}}
                                                    </select>
                                            {{/if}}
                                        </div>
                                    </value>
                                </div>
                                <div class="layui-col-md5">
                                    <label class="entervalue">通行时间：</label><value class="entervalue">${enter_time}</value>
                                </div>
                            </div>


                            <div class="layui-row mtop">
                                <div class="layui-col-md7">
                                    <label class="enterlabel">入场备注：</label>
                                    <value class="enterinput">
                                        <div>
                                            <input class="layui-input enter_remark" style="width: calc(100% - 42px); float: left;" value="${enter_remark}" />
                                            <div style="width: 38px; float: left;">
                                                <select class="layui-selet" lay-filter="enterremark">
                                                    {{each pager.enterremarks}}
                                                    <option value="${$value}">${$value}</option>
                                                    {{/each}}
                                                </select>
                                            </div>
                                        </div>
                                    </value>
                                </div>
                                <div class="layui-col-md5">

                                </div>
                            </div>
                            {{if enter_unpaidresultamount && enter_unpaidresultamount!=null && enter_unpaidresultamount>0}}
                            <div class="layui-row mtop">
                                <div class="layui-col-md7">
                                        <label class="entervalue">补缴金额：</label>
                                        <input class="layui-input vUnpaid" data-id="unpaidresultamount" value="${enter_unpaidresultamount.toFixed(2)}" readonly />
                                        <span class="question fa fa-table" title="点击查看计费详情"></span>
                                    </div>
                            </div>
                            {{/if}}
                        </div>
                        <div class="layui-card-body">
                            <button class="layui-btn in" data-key="2" onclick="btns.onOpsClick('${id}', this)">确认放行<t style="font-size:1vw;">(F2)<t></button>
                            <button class="layui-btn in layui-btn-primary layui-border-blue" data-key="0" onclick="btns.onOpsClick('${id}', this)">取消</button>
                            {{if orderno}}
                            <button class="layui-btn in inWinBtn layui-btn-sm" onclick="btns.onOpsClick('${orderno}', this)" data-key="102">多车多位</button>
                            <div class="layui-inline">
                                <button class="layui-btn in inWinBtn layui-btn-sm" id="btnVoice_${id}" onclick="btns.showVoiceList(this, '${id}')" data-passwayno="${passwayno}">常用语音</button>
                            </div>
                            {{/if}}
                            <div class="reminderinfo"></div>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </script>
    <!--出口弹窗-->
    <script type="text/x-jquery-tmpl" id="opsliOut">
                <ul>
                    <li class="out layui-hide" data-key="${id}" data-orderno="${orderno}">

                        <div class="lt"  style="width:45%">
                            <dl>
                                <dd class="img"><img class="entercar_img" src="${enter_img}" data-src="${enter_dataimg}" onerror="this.src='../Static/img/img-nothing.png'" /></dd>
                                <dd class="vlabel outlabel"><label>入口：</label><value>${enter_passway}</value></dd>
                                <dd class="vlabel outlabel"><label>区域：</label><value>${enter_area}</value></dd>
                                <dd class="vlabel"><label>时间：</label><value>${enter_time}</value></dd>
                            </dl>
                            <dl>
                                <dd class="img">
                                    <img class="outcar_img" src="${out_img}" data-src="${out_dataimg}" onerror="this.src='../Static/img/img-nothing.png'" />
                                    <img class="imgsmall" src="${imgsmall}" data-src="${dataimgsmall}" onerror="this.style='display:none'" />
                                </dd>
                                <dd class="vlabel outlabel"><label>出口：</label><value>${out_passway}</value></dd>
                                <dd class="vlabel outlabel"><label>区域：</label><value>${out_area}</value></dd>
                                <dd class="vlabel"><label>时间：</label><value>${out_time}</value></dd>
                            </dl>
                        </div>
                        <div class="rt outWin"  style="width:55%">

                            <div class="layui-card" style="box-shadow:none;padding:0 !important;background-color:#043857;">
                                <div class="layui-card-body">
                                    <div class="layui-row" style="margin-bottom: .5rem;">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">车牌：</label>
                                            <value class="enterinput outcarno input-group" style="float: none !important;color:#fff;background-color: rgba(0,0,0,0) !important;">
                                                <div class="input-group" >
                                                    <div>
                                                        {{if out_noincar==1}}
                                                            <input onkeyup="GetInCarList(this)" class="layui-input outcar_carno" style="float: none !important;color:#fff;background-color: transparent;" id="outcar_carno${id}" value="${carno}" maxlength="10" autocomplete="off" onkeyup="this.value = this.value.toUpperCase()" />
                                                            {{else}}
                                                            <input onkeyup="GetInCarList(this)" class="layui-input outcar_carno" style="float: none !important;color:#fff;background-color: transparent;" id="outcar_carno${id}" value="${carno}" maxlength="10" autocomplete="off" onkeyup="this.value = this.value.toUpperCase()" />
                                                            {{/if}}
                                                    </div>
                                                    <span class="input-group-btn" style="vertical-align: top;">
                                                            <button class="layui-btn layui-btn-xs layui-btn-primary" data-passwayno="${id}" onclick="btns.onGetOrderPay(this)" title="点击查询车辆停车状态"><i class="layui-icon layui-icon-search"></i></button>
                                                    </span>
                                                </div>
                                            </value>
                                            <div class="selcarno hide">
                                                    <dd class="carlist">
                                                    </dd>
                                            </div>
                                        </div>
                                        <div class="layui-col-md5" style="padding-top:0.45rem;text-align: left;padding-left: 10px;">
                                            {{if out_noincar==1}}
                                            <input type="checkbox" class="outcar_noincar" title="无入场记录出场" lay-skin="primary" data-passwayno="${id}" checked disabled />
                                            {{else}}
                                            <input type="checkbox" class="outcar_noincar" title="无入场记录出场" lay-skin="primary" data-passwayno="${id}" />
                                            {{/if}}
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">颜色：</label>
                                            <value class="enterinput">
                                               {{if formPower.ModifyTypeColor}}
                                                <select class="layui-selet outcar_cartype" lay-search data-passwayno="${id}">
                                                    {{each pager.cartypes}}
                                                    <option value="${$value.CarType_No}" {{if $value.CarType_Name==cartype}} selected {{/if}}>${$value.CarType_Name}</option>
                                                    {{/each}}
                                                </select>
                                               {{else}}
                                                 <select class="layui-selet outcar_cartype" lay-search data-passwayno="${id}" disabled>
                                                    {{each pager.cartypes}}
                                                    <option value="${$value.CarType_No}" {{if $value.CarType_Name==cartype}} selected {{/if}}>${$value.CarType_Name}</option>
                                                    {{/each}}
                                                </select>
                                               {{/if}}
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">应收金额：</label>
                                            <value class="entervalue" data-id="orderamount">
                                                ${out_orderamount.toFixed(2)}
                                                {{if out_unpaidamount>0 }}
                                                    (包含追缴金额${out_unpaidamount.toFixed(2)}元)
                                                {{/if}}
                                            </value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">免费：</label>
                                            <value class="enterinput">
                                                <div>
                                                    <input class="layui-input outcar_free" style="width: calc(100% - 42px); float: left;" value="${out_freeremark}" maxlength="50" />
                                                    <div style="width: 38px; float: left;">
                                                        <select class="layui-selet" lay-filter="outremark" id="selFree">
                                                            {{each pager.outremarks}}
                                                            <option value="${$value}">${$value}</option>
                                                            {{/each}}
                                                        </select>
                                                    </div>
                                                </div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">优惠金额：</label>
                                            <value class="entervalue" data-id="couponamount">${out_couponamount.toFixed(2)}</value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">折扣：</label>
                                            <value class="enterinput">
        @*  <select class="layui-select outcar_coupon" data-passwayno="${id}">
                                                    <option value="">无优惠</option>
                                                    {{if out_noincar==0}}
                                                    {{each out_coupon}}
                                                    <option value="${$value.CouponRecord_No}" {{if ($value.CouponRecord_No==out_couponno)}} selected {{/if}}>${$value.CouponRecord_Name}</option>
                                                    {{/each}}
                                                    {{each pager.parkdiscountset}}
                                                    <option value="${$value.CouponRecord_No}">${$value.CouponRecord_Name}</option>
                                                    {{/each}}
                                                    {{/if}}
                                                </select> *@
                                                <div class="div_out_coupon" data-passwayno="${id}" id="div_out_coupon_${id}"></div>
                                            </value>
                                        </div>
                                        <div class="layui-col-md5">
                                            <label class="entervalue">已收金额：</label>
                                            <value class="entervalue" data-id="chuzhiamount">${out_chuzhiamount.toFixed(2)}</value>
                                        </div>
                                    </div>
                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">车型：</label>
        @*<value class="entervalue" data-id="carcardtype">${out_cardname}</value>*@
                                            <value class="enterinput">
                                                    {{if formPower.ModifyType}}
                                                    <select class="layui-selet outcar_carcardtype" lay-search data-passwayno="${id}">
                                                        {{each pager.cards}}
                                                        <option value="${$value.CarCardType_No}" {{if $value.CarCardType_Name==out_cardname}} selected {{/if}}>${$value.CarCardType_Name}</option>
                                                        {{/each}}
                                                    </select>
                                                    {{else}}
                                                    <select class="layui-selet outcar_carcardtype" lay-search data-passwayno="${id}" disabled>
                                                        {{each pager.cards}}
                                                        <option value="${$value.CarCardType_No}" {{if $value.CarCardType_Name==out_cardname}} selected {{/if}}>${$value.CarCardType_Name}</option>
                                                        {{/each}}
                                                    </select>
                                                    {{/if}}
                                            </value>
                                        </div>
                                        <div class="layui-col-md5" style="white-space: nowrap;">
                                            <label class="entervalue">计费时长：</label>
                                            <value class="entervalue" data-id="parktimemin" style="font-size:1.3vw;">${out_parktimemin}</value>
                                        </div>
                                    </div>

                                    <div class="layui-row mtop">
                                        <div class="layui-col-md7">
                                            <label class="enterlabel">实收：</label>
                                            <value class="enterinput">
                                                {{if formPower.ModifyMoney && out_unpaidamount<=0}}
                                                    <div class="input-group">
                                                        <div>
                                                            <input class="layui-input v-null v-floatLimit outcar_money"  style="width: 100%; float: left;" data-id="payedamount" data-key="5" data-no="${id}" onkeypress="getkey('${id}', this)" maxlength="8" placeholder="按回车键保存" value="${out_payedamount.toFixed(2)}" />
                                                        </div>
                                                        <span class="input-group-btn" style="vertical-align: top;">
                                                            <button type="button" class="btn btn-primary modify" style="padding: 8px 12px !important;background-color: #657de5;border-color: #999;"><i class="fa pwd-edit fa-rotate-left"></i></button>
                                                        </span>
                                                    </div>
                                                {{else}}
                                                    <input class="layui-input v-null v-floatLimit outcar_money" data-id="payedamount" disabled style="background-color:#d8d7d7;" maxlength="8" value="${out_payedamount.toFixed(2)}" />
                                                {{/if}}
                                            </value>
                                        </div>
                                        <div class="layui-col-md5" style="white-space: nowrap;">
                                            <label class="entervalue">计费详情：</label>
                                            <value class="entervalue">
                                                <span class="out_isnonecar hide" title="多位多车">多位多车</span>
                                                <span class="out_isovertime hide" title="超时缴费">超时缴费</span>
                                                <span class="out_isoverdue hide" title="过期缴费">过期缴费</span>
                                                <span class="out_payedmag hide"></span>
                                                <span class="question hide fa fa-table" title="点击查看计费详情"></span></value>
                                        </div>
                                    </div>
                                </div>
                                {{if passwaydutymode==1}}
                                <div class="layui-card-body" style="padding: 10px 1px;">
                                    <button class="layui-btn out" data-key="4" onclick="btns.onOpsClick('${id}', this)">确认放行<t style="font-size:1vw;">(F1)<t></button>
                                    {{if formPower.FreeOpen}}
                                        <button class="layui-btn out" data-key="3" onclick="btns.onOpsClick('${id}', this)">免费放行</button>
                                    {{/if}}
                                    {{if formPower.CancelCharge}}
                                        <button class="layui-btn out layui-border-blue" data-key="0" onclick="btns.onOpsClick('${id}', this)">取消</button>
                                    {{/if}}
                                    <a href="javascript:;" data-key="1" title="点击播报" onclick="btns.onOpsClick('${id}', this)"><i class="layui-icon layui-icon-speaker"></i></a>
                                    <div class="more-actions" style="position: relative;display: inline-block;">
                                        <a href="javascript:;" data-key="99" title="点击更多" onclick="showMoreActions(this)"><i class="layui-icon layui-icon-more"></i></a>
                                        <div class="action-popup layui-hide">
                                            <button class="layui-btn layui-btn-sm" onclick="btns.onOpsClick('${orderno}', this)" data-key="101">订单详情</button>
                                            <button class="layui-btn layui-btn-sm" onclick="btns.onOpsClick('${orderno}', this)" data-key="102">多车多位</button>
                                            <button class="layui-btn layui-btn-sm" onclick="btns.showVoiceList(this, '${id}')" data-passwayno="${passwayno}">常用语音</button>
                                        </div>
                                    </div>
                                    <div class="reminderinfo"></div>
                                </div>
                                {{else}}
                                <div class="layui-card-body">
                                    当前车道为无人值守车道
                                </div>
                                {{/if}}
                            </div>
                        </div>
                    </li>
                </ul>
    </script>
    <!--车道列表-->
    <script type="text/x-jquery-tmpl" id="tmplLanList">
        <dd data-key="${Passway_No}" class="land landdd">
            <span class="lane-name">${Passway_Name}</span>
            <div class="lane-status">
                <i class="fa fa-check layui-hide landi"></i>
                <span class="lane-type ${(Passway_GateType == 1 || Passway_GateType == 2) ? 'intype' : 'outtype'}">${(Passway_GateType == 1 || Passway_GateType == 2) ? '入口' : '出口'}</span>
            </div>
        </dd>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplCarList">
        <dl><a>${carno}</a></dl>
    </script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/index.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools2.min.js?v20230803141" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/jsencrypt.min.js" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/talking.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        function getTime() {
            $("#time").html("" + (new Date()).Format("yyyy-MM-dd hh:mm:ss"));
        }
        window.setInterval("getTime()", 1000);
        modeConfig.loadModule();

        myVerify.init();
    </script>
    <script>
        var rDays = '@DogModel.RemainingDay'
        var Parking_Key = '@ViewBag.Parking_Key';
        var OpenCameraVideo = '@ViewBag.OpenCameraVideo';
        var policyCount = @Html.Raw(AppBasicCache.GetPolicyPark?.PolicyPark_MaxDiscount ?? 1);
        var isWin = '@AppBasicCache.IsWindows';
        var isFrpUrl = IsFrpURLOpenWeb(Parking_Key);
        var LanInterval = null;
        var ParkOrderInterval = null;
        var ParkingStateInterval = null;
        var frmPrintIndex = null;
        var frmSpaceIndex = null;
        var selData = { GetPasswayData: false, GetCarCardType: false, GetCarType: false, GetPasswayLinkExt: false, GetSysConfig: false, GetPolicyPark: false, SltParkDiscountSet: false };

        var laypage = null;
        var xmSelect = null;

        // 等待 videoobj 定义的异步函数
        async function waitForVideoObj() {
            return new Promise((resolve, reject) => {
                console.log("Checking if videoobj is defined...");
                let checkInterval = setInterval(() => {
                    if (typeof videoobj !== "undefined" && videoobj !== null) {
                        clearInterval(checkInterval);  // 清除轮询
                        console.log("✅ videoobj 已定义，可以使用！");
                        resolve();  // videoobj 已定义，继续执行后续操作
                    } else {
                        console.log("❌ videoobj 未定义，继续等待...");
                    }
                }, 200); // 每 200 毫秒检查一次
            });
        }

        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['table', 'element', 'form', 'xmSelect', 'laydate', "laypage"], function () {

            (async function () {
                laypage = layui.laypage;
                xmSelect = layui.xmSelect;

                await waitForVideoObj();

                pager.init();
                var form = layui.form, laydate = layui.laydate;

                layui.form.on('select(outremark)', function (data) {
                    $(".outcar_free").val(data.value);
                });

                layui.form.on('select(enterremark)', function (data) {
                    $(".enter_remark").val(data.value);
                });

                var currentDate = new Date();
                var lastPopupDate = localStorage.getItem('lastPopupDate');
                var oneDayInMillis = 24 * 60 * 60 * 1000;
                if (!lastPopupDate || (currentDate - new Date(lastPopupDate) > oneDayInMillis)) {
                    if (rDays > 0) {
                        layer.open({
                            title: false,
                            type: 1,
                            offset: 'rb', // 右下角
                            content: '<div class="softendDateTip" style="padding: 20px 40px 0 40px;color:red;font-weight:bold;line-height:4vh;">温馨提示：<br/>软件授权还有' + rDays + '天到期，为了不影响使用，请及时处理！</div>',
                            btn: ['知道了'],
                            btnAlign: 'c',
                            shade: 0,
                            time: 10000 * 60,
                            area: ['35vw', '20vh'],
                            success: function (layero, index) {
                                $(layero).css({
                                    'background-color': '#f2f2f2',
                                    'border-radius': '10px',
                                    'box-shadow': '0 0 10px rgba(0, 0, 0, 0.5)',
                                });
                            }
                        });
                        // 更新 localStorage 中的 lastPopupDate
                        localStorage.setItem('lastPopupDate', currentDate);
                    }
                }
            })();
        });

        let debounceTimer;
        var sentryHostNo = '@AppBasicCache.SentryHostInfo?.SentryHost_No';
        var socketAccount = decodeURI('@ViewBag.Account');
        var formPower = @Html.Raw(Json.Serialize(ViewBag.WinFormMonitor));
        console.log(socketAccount);
        var TalkingInterval = null;
        var ws = null;
        var wsUrl = '@ViewBag.WebSocketUrl/' + socketAccount + "?key=" + '@ViewBag.WebSocketKey';
        var lockReconnect = false;//重连

        var b30pageLimit = localStorage.getItem("b30vedioLimit");
        if (b30pageLimit == null || b30pageLimit == undefined || b30pageLimit == "") { b30pageLimit = 2; }

        var picker = new SCarnoPicker();
        var picker2 = new SCarnoPicker();

        var pager = {
            pageCount: 16,
            pageIndex: 1,
            pageLimit: b30pageLimit,
            selData: [],
            passwaydata: [],    //当前岗亭车道数据
            arealinkpassway: [],//区域关联车道信息
            cards: [],          //车牌类型
            cartypes: [],       //车牌颜色
            sysconfig: [],      //配置数据
            policypark: [],      //车场策略
            enterremarks: [],   //入场备注
            outremarks: [],     //出场备注
            parkdiscountset: [], //优惠设置列表
            calcDetail: {},//计费详情
            selPasswayNoes: [],
            payedmsg: null,
            init: function () {
                setTimeout(function () {
                    $(".menus ul li[data-key='0']").css("display", "block");
                }, 5000);

                var carnotoolid = "outcar_carno0";
                var carobj = $('#' + carnotoolid);
                picker.init(carnotoolid, function (text, carno) {
                    if (picker.eleid == "outcar_carno0") {
                        $("#outcar_carno0").val(carno.join(''));
                    };
                    GetInCarList(carobj, carno);
                }, "web").bindkeyup();

                var carnotoolid2 = "enter_carno_0";
                var carobj2 = $('#' + carnotoolid2);
                picker2.init(carnotoolid2, function (text, carno) {
                    if (picker2.eleid == "enter_carno_0") {
                        $("#enter_carno_0").val(carno.join(''));
                    };
                }, "web").bindkeyup();

                $.ajaxSettings.async = false;
                localCache.init();
                this.bindSelect();
                this.bindPower();
                this.bindEvent();
                this.bindData();
                layui.form.render();
                myVerify.init();
                console.log("pager init");
                $.ajaxSettings.async = true;

                if (TalkingInterval != null) {
                    clearInterval(TalkingInterval);
                    TalkingInterval = null;
                }
                TalkingInterval = setInterval(() => {
                    try {
                        console.log("Talking check");
                        if (selData.GetPasswayData && selData.GetCarCardType && selData.GetCarType && selData.GetPasswayLinkExt && selData.GetSysConfig && selData.GetPolicyPark && selData.SltParkDiscountSet) {
                            clearInterval(TalkingInterval);
                            TalkingInterval = null;
                            console.log("Talking init");
                            Talking.init();
                        }
                    } catch (e) { console.log("Talking.init异常：" + e.message); }
                }, 100);
            },
            bindSelect: function () {
                pager.bindVideo();
                $.post("GetCarCardType", {}, function (json) {
                    selData.GetCarCardType = true;
                    console.log("2GetCarCardType");
                    if (json.success) {
                        pager.cards = json.data;
                        var options = '';
                        json.data.forEach((item, index) => {
                            options += '<option value="' + item.CarCardType_No + '">' + item.CarCardType_Name + '</option>';
                        });
                        $(".enter_carcardtype").append(options);
                        $(".out_carcardtype").append(options);
                    } else {
                        console.log("读取车牌类型失败：" + json.msg);
                    }
                }, "json");
                $.post("GetCarType", {}, function (json) {
                    selData.GetCarType = true;
                    console.log("3GetCarType");
                    if (json.success) {
                        pager.cartypes = json.data;

                        var options = '';
                        json.data.forEach((item, index) => {
                            options += '<option value="' + item.CarType_No + '">' + item.CarType_Name + '</option>';
                        });
                        $(".outcar_cartype").append(options);
                        $(".enter_cartype").append(options);
                        layui.form.render('select');
                        bindSelectChange();

                    } else {
                        console.log("读取车牌色失败：" + json.msg);
                    }
                }, "json");
                $.post("GetPasswayLinkExt", {}, function (json) {
                    selData.GetPasswayLinkExt = true;
                    console.log("4GetPasswayLinkExt");
                    if (json.success) {
                        var nos = [];
                        pager.passwaydata.forEach((item, index) => { nos[nos.length] = item.Passway_No; });
                        json.data.forEach((item, index) => {
                            if (nos.indexOf(item.PasswayLink_PasswayNo) > -1) {
                                pager.arealinkpassway[pager.arealinkpassway.length] = item;
                            }
                        });
                        //pager.arealinkpassway = json.data;
                    } else {
                        console.log("获取车道关联区域信息失败：" + json.msg);
                    }
                }, "json");
                $.post("GetSysConfig", {}, function (json) {
                    selData.GetSysConfig = true;
                    console.log("5GetSysConfig");
                    if (json.success) {
                        pager.sysconfig = json.data.config;
                        var content = json.data.content;
                        if (content.SysConfig_EnterRemarks) { pager.enterremarks = content.SysConfig_EnterRemarks.split(';'); }
                        if (content.SysConfig_FreeReasons) { pager.outremarks = content.SysConfig_FreeReasons.split(';'); }

                        if (pager.outremarks && pager.outremarks.length > 0) {
                            var options = '';
                            pager.outremarks.forEach((item, index) => {
                                options += '<option value="' + item + '">' + item + '</option>';
                            });
                            $(".outremark").append(options);
                            // $(".outcar_free").val($("#selFree").find("option").first().val());
                        }

                        if (pager.enterremarks && pager.enterremarks.length > 0) {
                            var options = '';
                            pager.enterremarks.forEach((item, index) => {
                                options += '<option value="' + item + '">' + item + '</option>';
                            });
                            $(".enterremark").append(options);
                        }
                    } else {
                        console.log("获取系统配置失败：" + json.msg);
                    }
                }, "json");
                $.post("GetPolicyPark", {}, function (json) {
                    selData.GetPolicyPark = true;
                    console.log("6GetPolicyPark");
                    if (json.success) {
                        pager.policypark = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");
                $.post("SltParkDiscountSet", {}, function (json) {
                    selData.SltParkDiscountSet = true;
                    if (json.success) {
                        pager.parkdiscountset = json.data;
                    } else {
                        console.log("获取停车场设置的优惠列表失败：" + json.msg);
                    }
                }, "json");
            },
            bindData: function () {

                setTimeout(function () {
                    inParkOrder.onLoad();
                    GetCurrentWorkShift();
                    GetCurrentCarSpace();
                }, 0);

                //2、场内记录
                if (ParkOrderInterval != null) {
                    clearInterval(ParkOrderInterval);
                    ParkOrderInterval = null;
                }
                ParkOrderInterval = setInterval(() => {
                    try {
                        inParkOrder.onSearch();
                        policyCount = @Html.Raw(AppBasicCache.GetPolicyPark?.PolicyPark_MaxDiscount ?? 1);
                    } catch (e) { console.log("定时出入场记录异常：" + e.message); }
                }, 300000);
                //3、设备状态
                if (LanInterval != null) {
                    clearInterval(LanInterval);
                    LanInterval = null;
                }
                LanInterval = setInterval(() => {
                    try {
                        pager.GetDeviceStatus();
                    } catch (e) { console.log("定时设备状态异常：" + e.message); }
                }, 5000);
            },
            bindEvent: function () {
                $("#inparkorder-search").click(function () {
                    inParkOrder.onSearch();
                });

                // 监听输入框的输入事件
                $('#inParkOrder_CarNo').on('input', function () {
                    // 清除上一个定时器
                    clearTimeout(debounceTimer);

                    // 设置一个新的定时器，1秒后触发查询
                    debounceTimer = setTimeout(() => {
                        inParkOrder.onSearch();
                    }, 1000);
                })

                // 优化 moreMenu 和 eventMenu 的显示逻辑
                $("#moreMenu,#eventMenu").hover(
                    function () {
                        // 鼠标进入时显示面板
                        var $panel = $(this).find("dl.menus-bar");
                        $panel.removeClass("layui-hide");
                        $panel.css({
                            'display': 'block',
                            'opacity': '0',
                            'transform': 'translateY(-10px)'
                        }).animate({
                            'opacity': '1',
                            'transform': 'translateY(0)'
                        }, 200);
                    },
                    function () {
                        // 鼠标离开时隐藏面板
                        var $panel = $(this).find("dl.menus-bar");
                        $panel.animate({
                            'opacity': '0',
                            'transform': 'translateY(-10px)'
                        }, 150, function () {
                            $panel.addClass("layui-hide").css('display', 'none');
                        });
                    }
                );

                $("#eventMenu").click(function () {
                    if ($("span.total").is(':visible')) {
                        alerts.success("即将进入管理后台");
                        $.post("GetLoginAuthCode", { page: 1 }, (json) => {
                            if (json.success) {
                                win_open(json.data);
                            } else {
                                alerts.error(json.msg);
                            }
                        }, "json").fail(function () {
                            alerts.error("跳转异常");
                        });
                    } else {
                        alerts.success("无事件处理");
                    }
                });

                // 优化车道监控菜单的显示逻辑
                $("#LanMenu").hover(
                    function () {
                        // 鼠标进入时显示面板
                        var $panel = $(this).find("dl.barList");
                        $panel.removeClass("layui-hide");
                        $panel.css({
                            'display': 'block',
                            'opacity': '0',
                            'transform': 'translateY(-10px)'
                        }).animate({
                            'opacity': '1',
                            'transform': 'translateY(0)'
                        }, 200);
                    },
                    function () {
                        // 鼠标离开时隐藏面板
                        var $panel = $(this).find("dl.barList");
                        $panel.animate({
                            'opacity': '0',
                            'transform': 'translateY(-10px)'
                        }, 150, function () {
                            $panel.addClass("layui-hide").css('display', 'none');
                        });
                    }
                );

                $("#notice").click(function () {
                    if ($("#msgBox").hasClass("layui-hide")) {
                        $("#msgBox").removeClass("layui-hide");
                    } else {
                        $("#msgBox").addClass("layui-hide");
                    }
                });
                $("#msgBox .close").click(function () { $("#msgBox").removeClass("layui-hide").addClass("layui-hide"); });

                layui.form.on("checkbox", function (data) {
                    chkNothingRecord(data);
                });

                $(".way ul").on('click', 'li', function () {
                    opsObj.clickWayUlLi(this);
                });

                $(".input-group-btn .modify").click(function () {
                    var ipt = $(this).parents("div.input-group").find("input");
                    var btn = $(ipt).parents("div.input-group").find("i");
                    var no = $(ipt).attr("data-no");

                    if ($(".pwd-edit").hasClass("fa-pencil")) {
                        $("input.outcar_money").attr("readonly", false);
                        $(".pwd-edit").addClass("fa-rotate-left").removeClass("fa-pencil");
                        $(ipt).val("").focus();
                    } else {
                        var newMoney = $(ipt).val();
                        if (newMoney == "") {
                            var data = HtmlParameter.getConfirmPassOut(no);
                            if (data != null && data.orderamount != undefined && data.orderamount != null) {
                                $(ipt).val(data.orderamount);
                                $(ipt).attr("readonly", true);
                                $(btn).removeClass("fa-rotate-left").removeClass("fa-pencil").addClass("fa-pencil");
                            } else {
                                $(ipt).focus().select();
                                alerts.error("请输入正确的实收金额，金额不能为空");
                            }
                        } else {
                            if (myVerify.rule.float.test($(ipt).val())) {
                                btns.onOpsClick(no, ipt, function () {
                                    $(ipt).attr("readonly", true);
                                    $(ipt).parents("div.input-group").find(".pwd-edit").removeClass("fa-rotate-left").addClass("fa-pencil");
                                })
                            } else {
                                $(ipt).focus().select();
                                alerts.error("请输入正确的实收金额");
                            }
                        }
                    }
                });
            },
            bindPower: function () {
                if (!formPower.FreeOpen) {
                    $("button.out_enterfreepass").addClass("hide");
                }
                if (!formPower.CancelCharge) {
                    $("button.out_cancel").addClass("hide");
                }
            },
            bindVideo: function () {
                $.post("GetPasswayData", {}, function (json) {
                    selData.GetPasswayData = true;
                    console.log("1GetPasswayData");
                    if (json.success) {
                        pager.passwaydata = json.data;
                        if (json.data && json.data != null && json.data.length > 0) vedioControl.setFrpInterval();

                        // <dd data-key="${Passway_No}" class="land"><i class="fa fa-check layui-hide landi">&nbsp;</i>${Passway_Name}</dd>
                        $(".barList").html($('#tmplLanList').tmpl(pager.passwaydata));
                        $(".barList").find("dd").first().before('<dd data-key="" class="land checkAll"><span class="lane-name">全选</span><div class="lane-status"><i class="layui-icon layui-icon-ok"></i></div></dd>');
                        pager.bindPasswayEvent();

                        var selPassway = localStorage.getItem("selPasswayNoes");
                        if (selPassway && selPassway != null) {
                            var selPassArray = JSON.parse(selPassway);
                            if (selPassArray.length > 0) {
                                var allCheck = true;
                                var deviceCount = 0;
                                pager.selData = [];
                                selPassArray.forEach((no) => {
                                    var passway = pager.passwaydata.find((passway, i) => { return no == passway.Passway_No });
                                    if (passway != null) {
                                        pager.selData.push(passway);
                                        deviceCount = deviceCount + passway.Passway_DeviceCount;
                                    }
                                });

                                if (pager.selData.length != pager.passwaydata.length) {
                                    allCheck = false;
                                }


                                if (pager.selData.length > 0) {
                                    if (!allCheck) deviceCount = 0;
                                    pager.CheckSelPassway(deviceCount, allCheck, true);
                                } else {
                                    if (playerType == 1) {
                                        videoobj.init(pager.passwaydata).then(() => {
                                            videoobj.event();
                                            localCache.setCamera(videoobj.data);
                                        });
                                    } else {
                                        videoobj.init(pager.passwaydata);
                                        videoobj.event();
                                        localCache.setCamera(videoobj.data);
                                    }
                                }
                            } else {
                                if (playerType == 1) {
                                    videoobj.init(pager.passwaydata).then(() => {
                                        videoobj.event();
                                        localCache.setCamera(videoobj.data);
                                    });
                                } else {
                                    videoobj.init(pager.passwaydata);
                                    videoobj.event();
                                    localCache.setCamera(videoobj.data);
                                }
                            }
                        } else {
                            if (playerType == 1) {
                                videoobj.init(pager.passwaydata).then(() => {
                                    videoobj.event();
                                    localCache.setCamera(videoobj.data);
                                });
                            } else {
                                videoobj.init(pager.passwaydata);
                                videoobj.event();
                                localCache.setCamera(videoobj.data);
                            }
                        }
                    } else {
                        layer.msg("读取车道失败", { icon: 0, time: 2000 });
                    }
                }, "json");
            },
            bindPasswayEvent: function () {
                $(".land").off("click").on("click", function () {
                    if ($("div.item2").length == 0) { $(".monitor").html(''); }
                    pager.pageCount = $(".item2").length;



                    //全选 / 全不选
                    if ($(this).hasClass("checkAll") || $(this).hasClass("checkNotAll")) {
                        pager.pageLimit = 2;
                        localStorage.removeItem("b30vedioLimit");
                        if ($(this).hasClass("checkAll")) {
                            console.log('点击全选按钮');
                            // 先更新按钮状态为"全不选"
                            $(this).removeClass("checkAll").addClass("checkNotAll").html('<span class="lane-name">全不选</span><div class="lane-status"><i class="layui-icon layui-icon-close"></i></div>');

                            pager.selData = [];
                            var deviceCount = 0;
                            pager.passwaydata.forEach((passway, i) => {
                                pager.selData.push(passway);
                                deviceCount = deviceCount + passway.Passway_DeviceCount;
                            });

                            console.log('全选 - 选中的车道数量:', pager.selData.length);
                            console.log('全选 - 调用CheckSelPassway');
                            pager.CheckSelPassway(deviceCount, true, false);

                        } else {
                            localStorage.removeItem("selPasswayNoes");
                            pager.selData = [];
                            $(".landi").addClass("layui-hide");
                            $(".barList").find("dd").first().addClass("checkAll").removeClass("checkNotAll").html('<span class="lane-name">全选</span><div class="lane-status"><i class="layui-icon layui-icon-ok"></i></div>');
                            $(".fpage").remove();
                            $(".monitor").removeClass("monitor3");
                        }
                    } else {

                        var tt = $(this).find("i").first();
                        var passwayNo = $(this).attr("data-key");
                        if (!$(tt).is(":visible")) {
                            $(tt).removeClass("layui-hide");

                            pager.passwaydata.forEach((passway, i) => {
                                if (passway.Passway_No == passwayNo && pager.selData.find((item, index) => { return item.Passway_No == passwayNo }) == null) {
                                    pager.selData.push(passway);
                                }
                            });

                        } else {
                            $(tt).addClass("layui-hide");
                            $(".barList").find("dd").first().addClass("checkAll").removeClass("checkNotAll").html('<span class="lane-name">全选</span><div class="lane-status"><i class="layui-icon layui-icon-ok"></i></div>');
                            pager.passwaydata.forEach((passway, i) => {
                                if (passway.Passway_No == passwayNo) {
                                    pager.selData.forEach((item, index) => { if (item.Passway_No == passwayNo) { pager.selData.splice(index, 1); return false; } });
                                }
                            });
                        }

                        if (pager.selData.length == 0) {
                            pager.pageLimit = 2;
                            localStorage.removeItem("b30vedioLimit");
                        }

                        deviceCount = 0;
                        pager.selData.forEach((passway, i) => {
                            deviceCount = deviceCount + passway.Passway_DeviceCount;
                        });

                        pager.CheckSelPassway(deviceCount, false);
                    }

                    $(".item2 >.item-box").hover(function () {
                        $(this).find(".videoBtn").first().show();
                        $(this).parent().find(".videoTitle").first().hide();
                    }, function () {
                        $(this).find(".videoBtn").first().hide();
                        $(this).parent().find(".videoTitle").first().show();
                    });

                    if (pager.selData.length == 0) {
                        if (playerType == 1) {
                            videoobj.init(pager.passwaydata).then(() => {
                                videoobj.event();
                                localCache.setCamera(videoobj.data);
                            });
                        } else {
                            videoobj.init(pager.passwaydata);
                            videoobj.event();
                            localCache.setCamera(videoobj.data);
                        }
                        $(".monitor").removeClass("monitor2");
                    } else {
                        $(".monitor").addClass("monitor2");
                    }
                })
            },
            bindPage: function (count, pagecount) {
                laypage.render({
                    elem: 'fpage'
                    , count: count
                    , limit: pagecount
                    , limits: [2, 4, 6, 8, 10, 12, 14, 16]
                    , layout: ['count', 'page', 'prev', 'next', 'limit']
                    , jump: function (obj, first) {
                        if (!first) {
                            try {
                                pager.pageIndex = obj.curr;
                                pager.pageLimit = obj.limit;
                                localStorage.setItem("b30vedioLimit", pager.pageLimit)

                                deviceCount = 0;
                                pager.selData.forEach((passway, i) => {
                                    deviceCount = deviceCount + passway.Passway_DeviceCount;
                                });

                                if (playerType != 1) {
                                    videoobj.init(pager.selData, true, pager.pageLimit, pager.pageIndex);
                                    $(".item2 >.item-box").hover(function () {
                                        $(this).find(".videoBtn").first().show();
                                        $(this).parent().find(".videoTitle").first().hide();
                                    }, function () {
                                        $(this).find(".videoBtn").first().hide();
                                        $(this).parent().find(".videoTitle").first().show();
                                    });
                                } else {
                                    videoobj.moreInit(deviceCount, pager.pageLimit, pager.pageIndex).then(() => {
                                        $(".item2 >.item-box").hover(function () {
                                            $(this).find(".videoBtn").first().show();
                                            $(this).parent().find(".videoTitle").first().hide();
                                        }, function () {
                                            $(this).find(".videoBtn").first().hide();
                                            $(this).parent().find(".videoTitle").first().show();
                                        });
                                    })
                                }
                            } catch (error) {
                                console.log('视频分页异常:', error);
                            }
                        }
                    }
                });
            },
            GetDeviceStatus: function () {
                if (localCache && localCache.videodata && localCache.videodata.length > 0) {
                    var no1 = localCache.videodata[0].passwayno;
                    var no2 = "";
                    if (localCache.videodata.length >= 1) no2 = localCache.videodata[1].passwayno;
                    if (no1 != "" || no2 != "") {
                        $.post("GetDeviceStatus", { authID: socketAccount, passwayno1: no1, passwayno2: no2 }, function (json) {
                            if (json.success) {
                                console.log("请求设备状态成功");
                                if (LanInterval != null) {
                                    clearInterval(LanInterval);
                                    LanInterval = null;
                                }
                            } else {
                                console.log("获取设备状态失败：" + json.msg);
                            }
                        }, "json");
                    }
                }
            },
            CheckSelPassway: function (deviceCount, isAllCheck, isLoad) {

                if (isAllCheck) {
                    // 只有在不是从点击事件调用时才更新按钮状态（避免重复设置）
                    if (isLoad !== false) {
                        $(".barList").find("dd").first().removeClass("checkAll").addClass("checkNotAll").html('<span class="lane-name">全不选</span><div class="lane-status"><i class="layui-icon layui-icon-close"></i></div>');
                    }

                    // 先隐藏所有勾选图标
                    $(".landi").addClass("layui-hide");

                    pager.selPasswayNoes = [];
                    pager.selData.forEach((passway, i) => {
                        pager.selPasswayNoes.push(passway.Passway_No);
                    });

                    // 显示所有选中车道的勾选图标
                    console.log('全选 - 开始显示勾选图标，选中车道数量:', pager.selData.length);
                    console.log('全选 - 页面中的.landi元素数量:', $(".landi").length);

                    pager.selData.forEach((passway, i) => {
                        console.log('全选 - 处理车道:', passway.Passway_Name, '编号:', passway.Passway_No);
                        $.each($(".landi"), function (item, index) {
                            // 查找包含data-key属性的父级dd元素
                            var $ddElement = $(this).closest('dd[data-key]');
                            var dataKey = $ddElement.attr("data-key");
                            console.log('全选 - 检查勾选图标，data-key:', dataKey, '目标:', passway.Passway_No);
                            if (dataKey == passway.Passway_No) {
                                console.log('全选 - 找到匹配的勾选图标，显示它');
                                $(this).removeClass("layui-hide");
                                return false;
                            }
                        });
                    });
                    localStorage.setItem("selPasswayNoes", JSON.stringify(pager.selPasswayNoes));

                    if (modeConfig.getConfig().subsVideo != "1") { deviceCount = pager.selData.length; }
                    if (deviceCount > 0) $(".monitor").addClass("monitor3"); else $(".monitor").removeClass("monitor3");
                    if (deviceCount > 0) {
                        if (playerType == 1) {
                            videoobj.moreInit(deviceCount, pager.pageLimit, 1).then(() => { });
                        } else {
                            videoobj.init(pager.selData, true, pager.pageLimit, 1);
                        }
                    }
                    if (deviceCount > 2) {
                        $(".fpage").remove();
                        $(".monitor").append('<div id="fpage" class="fpage"></div>').addClass("monitor3");
                        pager.bindPage(deviceCount, pager.pageLimit)
                    } else {
                        $(".fpage").remove();
                    }

                } else {
                    // 处理部分选择或全不选的情况
                    deviceCount = 0;
                    pager.selPasswayNoes = [];
                    pager.selData.forEach((passway, i) => {
                        pager.selPasswayNoes.push(passway.Passway_No);
                        deviceCount = deviceCount + passway.Passway_DeviceCount;
                        if (isLoad) {
                            $.each($(".landi"), function (item, index) {
                                // 查找包含data-key属性的父级dd元素
                                var $ddElement = $(this).closest('dd[data-key]');
                                if ($ddElement.attr("data-key") == passway.Passway_No) {
                                    $(this).removeClass("layui-hide");
                                    return false;
                                }
                            });
                        }
                    });
                    localStorage.setItem("selPasswayNoes", JSON.stringify(pager.selPasswayNoes));

                    // 根据选择的数量更新按钮状态
                    var totalPassways = pager.passwaydata ? pager.passwaydata.length : 0;
                    var selectedCount = pager.selData.length;

                    if (selectedCount === 0) {
                        // 没有选择任何车道，显示"全选"
                        $(".barList").find("dd").first().addClass("checkAll").removeClass("checkNotAll").html('<span class="lane-name">全选</span><div class="lane-status"><i class="layui-icon layui-icon-ok"></i></div>');
                    } else if (selectedCount === totalPassways) {
                        // 选择了所有车道，显示"全不选"
                        $(".barList").find("dd").first().removeClass("checkAll").addClass("checkNotAll").html('<span class="lane-name">全不选</span><div class="lane-status"><i class="layui-icon layui-icon-close"></i></div>');
                    } else {
                        // 部分选择，显示"全选"（允许用户选择剩余的）
                        $(".barList").find("dd").first().addClass("checkAll").removeClass("checkNotAll").html('<span class="lane-name">全选</span><div class="lane-status"><i class="layui-icon layui-icon-ok"></i></div>');
                    }

                    if (modeConfig.getConfig().subsVideo != "1") { deviceCount = pager.selData.length; }
                    if (deviceCount > 2) $(".monitor").addClass("monitor3"); else $(".monitor").removeClass("monitor3");
                    //if (deviceCount > 0) videoobj.init(pager.selData, true, pager.pageLimit, 1);
                    if (deviceCount > 0) {
                        if (playerType == 1) {
                            videoobj.moreInit(deviceCount, pager.pageLimit, 1).then(() => { });
                        } else {
                            videoobj.init(pager.selData, true, pager.pageLimit, 1);
                        }
                    }
                    if (deviceCount > 2) {
                        $(".fpage").remove();
                        $(".monitor").append('<div id="fpage" class="fpage"></div>');
                        pager.bindPage(deviceCount, pager.pageLimit);
                    } else {
                        $(".fpage").remove();
                    }

                }

                if (isLoad) {
                    $(".item2 >.item-box").hover(function () {
                        $(this).find(".videoBtn").first().show();
                        $(this).parent().find(".videoTitle").first().hide();
                    }, function () {
                        $(this).find(".videoBtn").first().hide();
                        $(this).parent().find(".videoTitle").first().show();
                    });
                }
            },
            dispalyPart: function (cssName) {
                $("." + cssName).addClass("layui-hide");
                modeConfig.value.showModule[cssName] = false;
                if (cssName == "right") $('.content .left').addClass("rigthDidplay");
                if (cssName == "record") $('.content .monitor').css('bottom', '0%');
                if (cssName == "monitor") $('.content .record').css('top', '0%');
            },
            showPart: function (cssName) {
                $("." + cssName).removeClass("layui-hide");
                modeConfig.value.showModule[cssName] = true;
                if (cssName == "right") $('.content .left').removeClass("rigthDidplay");
                if (cssName == "record") $('.content .monitor').css('bottom', '50%');
                if (cssName == "monitor") $('.content .record').css('top', '50%');
            },
            getPart: function () {
                return modeConfig.getConfig();
            }
        }

        var vedioControl = {
            frpInterval: null,
            setFrpInterval: function () {
                if (Parking_Key && Parking_Key != "" && window.top.location.href.indexOf(Parking_Key) > -1) {
                    if (vedioControl.frpInterval != null) {
                        clearInterval(vedioControl.frpInterval);
                        vedioControl.frpInterval = null;
                    }
                    vedioControl.frpInterval = setInterval(() => {
                        try {
                            alerts.success("远程视频监控5分钟时长已到");
                            console.log(Parking_Key + "中止视频播放");
                            isLoadVideo = false;
                            modeConfig.value.isLoadVideo = false;
                            modeConfig.setConfig(modeConfig.value);
                            clearInterval(vedioControl.frpInterval);

                            if (playerType == 1) {
                                videoobj.init(pager.passwaydata).then(() => {
                                    videoobj.event();
                                    localCache.setCamera(videoobj.data);
                                });
                            } else {
                                videoobj.init(pager.passwaydata);
                                videoobj.event();
                                localCache.setCamera(videoobj.data);
                            }
                            if (players && players != null) {
                                players.forEach(function (item, index) {
                                    try {
                                        if (item && item.id) {
                                            console.log("视频释放：" + item.id);
                                            if (players[item.id]["timer"]) window.clearTimeout([item.id]["timer"]);
                                            if (players[item.id]["player"]) players[id]["player"].destroy();
                                        }
                                    } catch (e) { console.log("视频释放异常：" + e.message); }
                                });
                            }
                            setTimeout(function () { modeConfig.value.isLoadVideo = true; modeConfig.setConfig(modeConfig.value); }, 2000);
                        } catch (e) { console.log("视频检测异常：" + e.message); }
                    }, 300000);
                }
            }
        }

        function CloseIndex(index) {
            layer.close(index);
        }


        function openModifyParkSpace() {
            frmSpaceIndex = layer.open({
                type: 2,
                title: "修改余位",
                content: "ModifyParkSpace?r=" + Math.random(),
                area: getIframeArea(["500px", "500px"]),
                maxmin: false
            })
        }
    </script>
    <!--登录超时弹出登录框-->
    <script>

        function pxToVh(px) {
            const viewportHeight = window.innerHeight;
            return (px / viewportHeight) * 100;
        }

        function showMoreActions(anchor) {

            $(anchor).addClass('layui-hide');

            // 获取 action-popup 元素
            const popup = $(anchor).closest('.more-actions').find('.action-popup');

            // 切换显示状态
            popup.toggleClass('layui-hide');

            // 如果显示了popup，则计算位置
            if (!popup.hasClass('layui-hide')) {
                // 获取anchor元素的位置和尺寸
                const anchorRect = anchor.getBoundingClientRect();

                // 设置popup的位置为anchor的下方
                popup.css({
                    position: 'absolute',
                    left: '0',
                    top: pxToVh(anchorRect.height - 30) + 'vh',
                    zIndex: 1000
                });
            }
        }

        var modal = {
            init: function () {
                $('#modal-form').modal({ show: false });
            }
        }
        var timeOutLogin = function (XMLHttpRequest) {
            alerts.error("登录已失效,请重新登录");
            layer.msg("登录已失效,请重新登录", { icon: 0, time: 3000 }, function () {
                location.href = "/Gt/Index";
            });
        }
        //登录
        $("#btn_Login").click(function () {
            var btn = $(this);
            var username = decodeURIComponent(localData.get("Sentry_Admins_Account"));
            if (username == null) { layer.tips("您的缓存被清除，请刷新页面登录", '#password', { tips: [3] }); return; }
            var password = $("#password").val().trim();

            $('#btn_Login').button('loading');
            $.post("/Gt/ToLogin", { Admins_Account: username, Admins_Pwd: password }, function (data) {
                if (data.Success) {
                    $(".close").click();
                    $("#username").val("");
                    $("#password").val("");
                    btn.button('reset');

                    //判有跳转打开页面
                    var bta = $("#LoginForm");
                    var re = $(bta).attr("data-reload");
                    var name = $(bta).attr("data-iframe");
                    if (re === "true" && name !== "") {
                        var iframe = $(window.document).find("iframe[name='" + name + "']");
                        $(iframe).attr('src', $(iframe).attr("src")); //刷新该框架，重新加载页面
                    } else {
                        layer.msg("登录成功", { icon: 1, time: 1000 }, function () {
                            window.location.reload();
                        });
                    }

                    $('#modal-form').modal("hide");
                } else {
                    setTimeout(function () {
                        $('#btn_Login').button('reset');
                    }, 500);
                    layer.tips("提示：" + data.Message, '#btn_Login', { tips: [3] });
                }
            }, "json");
        });

        //回车登录
        $("#username").keydown(function (event) {
            if (event.keyCode == 13) { $("#btn_Login").click(); }
        });

        $("#password").keydown(function (event) {
            if (event.keyCode == 13) { $("#btn_Login").click(); }
        });

        $(".outcar_money").keydown(function (event) {
            if (event.keyCode == 13) { $(".broadcast").click(); }
        });

        function getkey(no, e) {
            if (event.keyCode == 13) {
                btns.onOpsClick(no, e,
                    function () {
                        $("input.outcar_money").attr("readonly", true);
                        $(".pwd-edit").removeClass("fa-rotate-left").addClass("fa-pencil");
                    })
            }
        }


        $(function () {
            $("input:text").focus();//Linux输入法触发
        })

        document.addEventListener('DOMContentLoaded', function () {
            var ctrlPressed = sessionStorage.getItem('ctrlPressed') === 'true' ? true : false;
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Control') {
                    ctrlPressed = true;
                    sessionStorage.setItem('ctrlPressed', 'true');
                }
            });

            document.addEventListener('keyup', function (event) {
                if (event.key === 'Control') {
                    ctrlPressed = false;
                    sessionStorage.setItem('ctrlPressed', 'false');
                }
            });

            document.getElementById('cloudOnline').addEventListener('dblclick', function () {
                ctrlPressed = sessionStorage.getItem('ctrlPressed') === 'true' ? true : false;
                if (ctrlPressed) {
                    // 当双击时执行的操作
                    layer.open({
                        title: "<div style='color:red;font-size:16px;'>仅限测试使用</div>",
                        type: 2, id: 123654789,
                        area: ['30%', '20%'],
                        fix: false, //不固定
                        maxmin: false,
                        content: '/Monitoring/Emergency'
                    });
                }
            });
        });
    </script>
</body>

</html>