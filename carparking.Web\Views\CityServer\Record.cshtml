﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>上报记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        .turn-back { background-color: transparent; border: 1px solid #009688; color: #009688; vertical-align: middle; }
        .turn-back:hover{color:#009688;}
        .turn-back i{vertical-align:middle !important;}
        #csname{line-height: 50px; font-weight: bold; } 
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>城市服务</cite></a>
                <a><cite>上报记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="layui-row"><h2 id="csname"></h2></div>
                        <div class="test-table-reload-btn layui-form" id="searchForm"> 
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="CityRecord_Interface" id="CityRecord_Interface" autocomplete="off" placeholder="接口" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="上报状态" class="form-control layui-select" id="CityRecord_UploadStatus" name="CityRecord_UploadStatus">
                                    <option value="">上报状态</option>
                                    <option value="1000">成功</option>
                                    <option value="0">失败</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="startDate" id="startDate" autocomplete="off" placeholder="开始日期" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="endDate" id="endDate" autocomplete="off" placeholder="截止日期" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i>查询</button>
                                <a class="layui-btn turn-back" href="../CityServer/Index"><i class="layui-icon layui-icon-return inbtn"></i>返回</a>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.CityServer.Open){}}<button class="layui-btn" lay-event="Open"><t>上报平台</t></button>{{# } }}
                                {{# if(Power.CityServer.Close){}}<button class="layui-btn" lay-event="Close" id="Close" style="display:none;"><t>关闭上报</t></button>{{# } }}
                                {{# if(Power.CityServer.Reset){}}<button class="layui-btn" lay-event="Reset" id="Reset" ><t>重新上传</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <script type="text/html" id="TmplStatus">
        {{# if(d.CityRecord_UploadStatus==1000){ }}
        <span class="layui-badge layui-bg-blue ">成功</span>
        {{# }else { }}
        <span class="layui-badge layui-bg-orange ">失败[{{ d.CityRecord_UploadStatus }}]</span>
        {{# } }}
    </script>
    <script type="text/html" id="barCols">
        <a class="layui-btn layui-btn-sm" id="View" lay-event="View">查看</a>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var CityServer_No = $.getUrlParam("CityServer_No");

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            
            $("#startDate").val(new Date().Format("yyyy-MM-dd"));
            $("#endDate").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["startDate", "endDate"], { type: 'date', range: true });

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { data.CityServer_No = CityServer_No; data.startDate = $("#startDate").val(); data.endDate = $("#endDate").val(); return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'CityRecord_ID', title: '序号', width: 100 }
                , { field: 'Parking_No', title: '车场编号', hide: true }
                , { field: 'Parking_Name', title: '车场名称' }
                , { field: 'CityServer_No', title: '城市服务编号', hide: true }
                , { field: 'CityServer_Name', title: '城市服务名称' }
                , { field: 'CityRecord_Interface', title: '接口' }
                , { field: 'CityRecord_Desc', title: '描述' }
                , { field: 'CityRecord_UploadStatus', width: 100, title: '上报状态', toolbar: '#TmplStatus' } 
                , { field: 'CityRecord_UploadTime', title: '上报时间' }
                //, { fixed: 'right', title: '操作', width: 160, minWidth: 160, toolbar: '#barCols' }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CityServer/GetCityRecord'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    $.getJSON("/CityServer/GetCityParking", { CityServer_No: CityServer_No }, function (json) {
                        if (json.Success) {
                            if (json.Data.CityParking_Status == 1) {
                                $("#Close").show();
                            }
                        }
                    });
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {       
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据          
                switch (obj.event) {
                    case 'Open':
                        layer.open({
                            type: 2,
                            title: "上报停车场",
                            content: '/CityServer/Parking?CityServer_No=' + CityServer_No,
                            area: getIframeArea(['80%', '80%']),
                            maxmin: true
                        });
                        break;
                    case 'Close':
                        layer.confirm('确定关闭城市平台上报？', {
                            btn: ['确定', '取消']
                        },function(){
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/CityServer/CloseParking", { CityServer_No: CityServer_No }, function (json) {
                                if (json.Success) {  
                                    layer.msg("关闭成功", { icon: 1, time: 1500 }, function () { });
                                    $("#Close").hide();
                                }
                                else{
                                    layer.msg("关闭失败:" + json.Message, { icon: 0, time: 1500 });
                                }
                            });
                        });
                        break;
                    case 'Reset':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.confirm('确定重新上报？', {
                            btn: ['确定', '取消']
                        }, function () {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/CityServer/Reset", { jsonModel: JSON.stringify(data) }, function (json) {
                                if (json.Success) {
                                    layer.msg("重新上报成功", { icon: 1, time: 1500 }, function () { });
                                    $("#Close").hide();
                                }
                                else {
                                    layer.msg("重新上报失败:" + json.Message, { icon: 0, time: 1500 });
                                }
                            });
                        });
                        break;
                };
            });

            //监听单元格工具条
            table.on('tool(com-table-base)', function (obj) {
                //var data = obj.data;
                //var pkNo = data.Parking_No;
                //var csNo = data.CityServer_No;
                //pager.pageIndex = $(".layui-laypage-curr").text();
                //if (obj.event === 'View') {
                //    layer.open({
                //        type: 2,
                //        title: "上报停车场(" + CityServer_Name + ")",
                //        content: '/CityServer/Parking?ctrl=edit&Parking_No=' + pkNo + '&CityServer_No=' + csNo,
                //        area: getIframeArea(['80%', '80%']),
                //        maxmin: true
                //    });
                //} 
            });

        });

        //绑定查询事件
        $(function () {
            $("#BtnSearch").click(function () { pager.bindData(1); });
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            //重新加载数据
            bindSelect: function () {
                $.getJSON("/CityServer/GetCityServer", { CityServer_No: CityServer_No }, function (json) {
                    if (json.Success) { 
                        $("#csname").html(json.Data.CityServer_Name); 
                    }
                }); 
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { data.CityServer_No = CityServer_No; data.startDate = $("#startDate").val(); data.endDate = $("#endDate").val(); return data; });
                comtable.reload({
                    url: '/CityServer/GetCityRecord'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            }
        }
         
    </script>
    <script>
        $(function () {
            $.ajaxSettings.async = false;
            pager.bindSelect();
            $.ajaxSettings.async = true;
        });
    </script>


</body>
</html>
