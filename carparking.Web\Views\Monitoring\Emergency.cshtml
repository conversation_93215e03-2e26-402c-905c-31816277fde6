﻿
<!DOCTYPE html>

<html style="width:100%;height:100%;background-color: #3e499d;">
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/plugins/layui/css/modules/layer/default/layer.css" rel="stylesheet" />
    <style>
        label { padding: 10px 10px 0 0; float: right; }
        .layui-col-xs6 span { line-height: 40px; }
        .edit-label { text-align: inherit; padding-right: 0px; padding-left: 18px; font-size: 15px; margin-top: 6px; }
        .tip a { color: #cfbdbd; }
        .tiptitle { color: #fff; font-size: 16px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="layui-form" id="verify-form" style="width:100%;height:100%;">
        <div class="layui-col-xs12 edit-label" style="color: #fff !important;">应急模式：<span class="spanEmergency">@Html.Raw(ViewBag.Emergency)</span></div>

        <div class="layui-col-xs12 edit-label">
            <button class="btn btn-warning" id="Save"><i class="fa fa-check"></i> <t>启用</t></button>
            <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>关闭</t></button>

            <div class="tip">
                <t class="tiptitle">❊<a>温馨提示：</a></t> &nbsp;&nbsp;<a>当前工具仅用于调试岗亭应急模式，平台重连或者重启岗亭即失效</a>
            </div>
        </div>
     
    </div>


    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script>

        $("#Cancel").click(function () { parent.layer.closeAll(); });

        $("#Save").click(function () {
            $("#Save").attr("disabled", true)
            $.post("/Monitoring/IsEmergency", {}, function (json) {
                if (json.success) {
                    layer.msg("启用成功", { icon: 1, time: 1500 }, function () { $(".spanEmergency").text("已启用") });
                } else {
                    layer.alert(json.msg, { icon: 0 });
                }
                $("#Save").removeAttr("disabled");
            }, "json").error(function () {
                $("#Save").removeAttr("disabled");
            });
        })

    </script>
</body>
</html>
