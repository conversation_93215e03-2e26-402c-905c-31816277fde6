﻿using carparking.BLL.Cache;
using carparking.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace carparking.BLL
{
    public class BusinessCar : BaseBLL
    {
        static DAL.BusinessCar dal = new DAL.BusinessCar();

        public static List<Model.BusinessCar> GetAllEntity(string showFileds, string selectWhere)
        {
            return _GetAllEntity(new Model.BusinessCar(), showFileds, selectWhere);
        }

        /// <summary>
        /// 创建商家车辆记录
        /// </summary>
        /// <param name="param"></param>
        /// <param name="car"></param>
        /// <param name="owner"></param>
        /// <param name="reserve"></param>
        /// <param name="errmsg"></param>
        /// <returns></returns>
        public static bool NewCarOwnerReserve(Model.API.BusinessCarParam param, out Model.BusinessCar data, out string errmsg)
        {
            data = null;
            errmsg = string.Empty;


            DateTime.TryParse(param.beginTime, out DateTime bTime);//开始时间
            DateTime.TryParse(param.endTime, out DateTime eTime);//结束时间

            //查询车牌类型 
            List<Model.CarCardType> cctList = BLL.CarCardType.GetAllEntity("*", $"CarCardType_Type=6");
            var card = cctList?.Find(x => x.CarCardType_IsDefault == 1) ?? cctList?.FirstOrDefault();
            if (card == null) { errmsg = "未找到计费卡类信息"; return false; }

            string parkno = card.CarCardType_ParkNo;

            Model.CarType ctype = BLL.CarType.GetDefault();
            if (ctype == null) { errmsg = "未找到车牌颜色信息"; return false; }

            var model = BLL.BusinessCar.GetAllEntity("*", $"BusinessCar_Status=0 AND BusinessCar_CarNo='{param.carNo}' AND BusinessCar_StartTime='{param.beginTime}'")?.FirstOrDefault();

            if (model == null)
            {
                data = new Model.BusinessCar()
                {
                    BusinessCar_No = string.IsNullOrEmpty(param.no) ? Utils.CreateNumber : param.no,
                    BusinessCar_ParkNo = parkno,
                    BusinessCar_CarTypeNo = ctype.CarType_No,
                    BusinessCar_CarTypeName = ctype.CarType_Name,
                    BusinessCar_CardNo = card.CarCardType_No,
                    BusinessCar_CardName = card.CarCardType_Name,
                    BusinessCar_CardType = Model.EnumCarType.Free,
                    BusinessCar_CarNo = param.carNo,
                    BusinessCar_Mode = 1,
                    BusinessCar_OrderNo = "",
                    BusinessCar_Phone = "",
                    BusinessCar_Name = param.businessName ?? "",
                    BusinessCar_StartTime = bTime,
                    BusinessCar_EndTime = eTime,
                    BusinessCar_Remark = "",
                    BusinessCar_Status = 0,
                    BusinessCar_UseSpace = 0,
                    BusinessCar_AddTime = DateTime.Now,
                    BusinessCar_PersonNumber = 1
                };
            }
            else
            {
                model.BusinessCar_Mode = 1;
                model.BusinessCar_Name = param.businessName ?? "";
                model.BusinessCar_EndTime = eTime;
                model.BusinessCar_Status = 0;

                data = model;
            }

            return true;
        }

        /// <summary>
        /// 获取时间范围内重叠的商家车
        /// </summary>
        /// <param name="carno"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <returns></returns>
        public static List<Model.BusinessCar> GetEntityByRangeTime(string carno, DateTime start, DateTime end)
        {
            if (AppBasicCache.ReadWriteCache && AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache == 1)
            {
                var d = AppBasicCache.GetBusinessCar.Values.Where(x =>
                    x.BusinessCar_CarNo == carno && x.BusinessCar_Status == 0
                    && (
                            (x.BusinessCar_StartTime >= start && x.BusinessCar_StartTime <= end)
                         || (x.BusinessCar_EndTime >= start && x.BusinessCar_EndTime <= end)
                         || (start >= x.BusinessCar_StartTime && start <= x.BusinessCar_EndTime)
                         || (end >= x.BusinessCar_StartTime && end <= x.BusinessCar_EndTime)
                       )
                ).LastOrDefault();
                return d == null ? null : new List<Model.BusinessCar> { d };
            }
            else
            {
                return dal.GetEntityByRangeTime(carno, start, end);
            }
        }

        /// <summary>
        /// 车牌号匹配商家车辆
        /// </summary>
        /// <param name="carno">车牌号</param>
        /// <param name="model">商家车辆记录</param>
        /// <param name="car">车辆信息</param>
        /// <param name="carCardType">车牌类型</param>
        /// <param name="carType">车牌颜色</param>
        public static void MacthBusinessCar(
            string carno
            , DateTime intime
            , DateTime outtime
            , out Model.BusinessCar model
            , ref Model.Car car
            , ref Model.CarCardType carCardType
            , ref Model.CarType carType
            , List<Model.CarCardType> cctList = null
            , List<Model.CarType> ctList = null)
        {
            model = null;

            var data = BLL.BusinessCar.GetEntityByRangeTime(carno, intime, outtime);
            if (data != null && data.Count > 0)
            {
                model = data.FirstOrDefault();
                if (model != null)
                {
                    car = new Model.Car()
                    {
                        Car_CarNo = model.BusinessCar_CarNo,
                        Car_VehicleTypeNo = model.BusinessCar_CarTypeNo,
                        Car_TypeNo = model.BusinessCar_CardNo,
                        Car_Status = 1,
                        Car_ParkingNo = model.BusinessCar_ParkNo,
                        Car_BeginTime = model.BusinessCar_StartTime,
                        Car_EndTime = model.BusinessCar_EndTime,
                        Car_OnLine = 1,
                        Car_IsMoreCar = 0,
                        Car_Category = Model.EnumCarType.Free.ToString()
                    };

                    var cardno = model.BusinessCar_CardNo;
                    carCardType = cctList?.Find(x => x.CarCardType_No == cardno);
                    if (carCardType == null) carCardType = BLL.CarCardType.GetEntity(model.BusinessCar_CardNo);

                    var no = model.BusinessCar_CarTypeNo;
                    carType = ctList?.Find(x => x.CarType_No == no);
                    if (carType == null) carType = BLL.CarType.GetEntity(model.BusinessCar_CarTypeNo);
                }
            }
        }

    }
}
