﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>导入停车订单</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-card { box-shadow: none; }
        a:hover { text-decoration: none; }
        .layui-row { margin-bottom: 10px; }
        .edit-label { padding-left: 10px; }
    </style>
</head>
<body>
    <div class="layui-card layui-form">
        <div class="layui-card-header">
        </div>
        <div class="layui-card-body" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">车牌号</label></div>
                <div class="layui-col-sm3">
                    <input class="layui-input v-null v-carno v-submit" id="ParkOrder_CarNo" name="ParkOrder_CarNo" maxlength="8" />
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
                <div class="layui-col-sm2"><label class="edit-label">入场时间</label></div>
                <div class="layui-col-sm3">
                    <input class="layui-input v-null v-datetime v-submit" id="ParkOrder_EnterTime" name="ParkOrder_EnterTime" />
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">停车区域</label></div>
                <div class="layui-col-sm3">
                    <select class="layui-select v-null" id="ParkOrder_ParkAreaNo" name="ParkOrder_ParkAreaNo" lay-filter="parkarea" lay-search>
                        <option value="">停车区域</option>
                    </select>
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
                <div class="layui-col-sm2"><label class="edit-label">入场车道</label></div>
                <div class="layui-col-sm3">
                    <select class="layui-select v-null" id="ParkOrder_EnterPasswayNo" name="ParkOrder_EnterPasswayNo" lay-search>
                        <option value="">入场车道</option>
                    </select>
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">车牌类型</label></div>
                <div class="layui-col-sm3">
                    <select class="layui-select v-null" id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                        <option value="">车牌类型</option>
                    </select>
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
                <div class="layui-col-sm2"><label class="edit-label">车牌颜色</label></div>
                <div class="layui-col-sm3">
                    <select class="layui-select v-null" id="ParkOrder_CarType" name="ParkOrder_CarType" lay-search>
                        <option value="">车牌颜色</option>
                    </select>
                </div>
                <div class="layui-col-sm1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2"><label class="edit-label">备注</label></div>
                <div class="layui-col-sm8">
                    <input class="layui-input" id="ParkOrder_Remark" name="ParkOrder_Remark" />
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-sm2"><label>&nbsp;</label></div>
                <div class="layui-col-sm8">
                    <a class="layui-btn fa fa-check" id="Save"> 保存</a>
                    <a class="layui-btn fa fa-close layui-bg-orange" id="Cancel"> 取消</a>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.visible = true;
        myVerify.init();

        s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
            $("#ParkOrder_CarNo").val(carno.join(''))
        }, "web").bindkeyup();

        layui.use(['form', 'element'], function () {
            pager.init()
        });

        var pager = {
            passwayList: [],
            passwayLinkList: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var selected = '';
                            //if (index == 0) selected = 'selected';
                            var option = '<option value="' + item.ParkArea_No + '" ' + selected + '>' + item.ParkArea_Name + '</option>';
                            $("#ParkOrder_ParkAreaNo").append(option);
                        });
                    }
                }, "json");

                $.post("SltGatePasswayList", {}, function (json) {
                    if (json.success) {
                        pager.passwayList = json.data;
                    }
                }, "json");
                $.post("SltPasswayLinkList", {}, function (json) {
                    if (json.success) {
                        pager.passwayLinkList = json.data;
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var selected = '';
                            if (index == 0) selected = 'selected';
                            var option = '<option value="' + item.CarType_No + '" ' + selected + '>' + item.CarType_Name + '</option>';
                            $("#ParkOrder_CarType").append(option);
                        });
                    }
                }, "json");

                $.post("SltCarCardTypeList2", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var selected = '';
                            if (index == 0) selected = 'selected';
                            var option = '<option value="' + item.CarCardType_No + '" ' + selected
                                + ' data-category="' + item.CarCardType_Category + '">'
                                + item.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select");

                $("#ParkOrder_EnterTime").val(new Date().Format("yyyy-MM-dd hh:mm:ss"));
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime"], { type: "datetime" });
            },
            bindEvent: function () {
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.ParkOrder_Remark = $("#ParkOrder_Remark").val();
                        return data;
                    });
                    if (new Date() < new Date(param.ParkOrder_EnterTime)) { layer.msg("入场时间不能大于当前时间", { icon: 0 }); return; }

                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);

                    $.post("/InParkRecord/AddParkOrder", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { time: 1500, icon: 1 }, function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            });
                        } else {
                            layer.msg(json.msg, { time: 1500, icon: 0 });
                        }
                    }, "json");
                });

                $("#Cancel").click(function () { parent.layer.closeAll(); });

                layui.form.on("select(parkarea)", function (data) {
                    var passways = [];
                    if (data.value) {
                        pager.passwayList.forEach(function (item, index) {
                            var link = pager.passwayLinkList.find(x => x.PasswayLink_ParkAreaNo == data.value && x.PasswayLink_PasswayNo == item.Passway_No);
                            if (link != null && link.PasswayLink_GateType == 1 && item.Passway_Area.indexOf(data.value) > -1 && item.Passway_GateType == '1') {
                                console.log(item.Passway_Name + "," + item.Passway_GateType)
                                passways[passways.length] = item;
                            }
                        });
                    }

                    onBindPasswaySelect(passways);
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        };

        var onBindPasswaySelect = function (data) {
            var option = '<option value="">入场车道</option>';
            data.forEach(function (item, index) {
                if (item.Passway_GateType == 1 || item.Passway_GateType == 2) {
                    var selected = '';
                    //if (index == 0) selected = 'selected';
                    option += '<option value="' + item.Passway_No + '" ' + selected + '>' + item.Passway_Name + '</option>';
                }
            });
            $("#ParkOrder_EnterPasswayNo").html(option);

            layui.form.render("select");
        }
    </script>
</body>
</html>
