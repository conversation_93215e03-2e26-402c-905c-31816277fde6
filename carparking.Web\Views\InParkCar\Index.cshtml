﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>停车记录查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?1" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .fa {
            margin: 10px 4px 0 0;
            float: left;
            font-size: 16px;
        }

        .layui-form-select .layui-input {
            width: 182px;
        }

        .itemcol {
            padding: 0 5px;
            margin: 5px 0;
        }

        .itembd {
            box-shadow: 0 0 2px #aaa;
        }

        .itemhd {
            overflow: auto;
        }

        .carno_span {
            float: left;
            line-height: 20px;
            font-weight: bold;
            font-size: 1rem;
        }

        .entertime_span {
            float: right;
            line-height: 20px;
            color: #999;
        }

        .btns {
            display: flex;
            padding-top: 15px;
            border-top: 1px solid #f2f2f2;
            user-select: none;
            cursor: pointer;
            color: #999;
        }

        .del {
            flex: 1;
            text-align: center;
            border-right: 1px solid #ddd;
        }

            .del:hover {
                color: red;
            }

        .edit {
            flex: 1;
            text-align: center;
        }

            .edit:hover {
                color: #0094ff;
            }

        .orderdesc {
            max-height: 100%;
            overflow: auto;
            background-color: rgba(255,255,255,.8);
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
            z-index: 3;
        }

            .orderdesc dl {
                display: flex;
            }

                .orderdesc dl dt {
                    flex: 1;
                }

                .orderdesc dl dd {
                    flex: 2;
                    font-weight: bold;
                    word-break: break-all;
                    text-align: justify;
                }

        .areaname {
            position: absolute;
            bottom: 0;
            z-index: 2;
            background-color: rgba(255,255,255,.8);
        }

        .HrefBigImg {
            color: #0094ff;
            cursor: pointer;
            text-decoration: underline;
        }

        .layui-btn .layui-icon.layui-icon-export {
            float: left;
            margin: 0 3px 0 0 !important;
            line-height: 38px !important;
        }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>场内记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="fa fa-search"></i><t>搜索</t></button>
                                <button class="layui-btn" id="Expired"><i class="fa fa-warning"></i>逾期车牌处理</button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_ParkAreaNo" name="ParkOrder_ParkAreaNo" lay-search>
                                    <option value="">区域名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="layui-select" id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="锁车状态" class="layui-select" id="ParkOrder_Lock" name="ParkOrder_Lock" lay-search>
                                    <option value="">锁车状态</option>
                                    <option value="0">未锁车</option>
                                    <option value="1">已锁车</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body" id="datalist" style="overflow:auto;">
                    </div>
                    <div class="layui-card-body layui-hide" id="datapage" style="overflow:auto;">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmpldata">
        <div class="layui-col-xs3 itemcol">
            <div class="layui-card itembd" onmouseover="onShowDesc(this)" onmouseout="onHideDesc(this)">
                <div class="layui-card-header itemhd">
                    <div class="carno_span">${ParkOrder_CarNo}</div>
                    <div class="entertime_span">${ParkOrder_EnterTime}</div>
                </div>
                <div class="layui-card-body" style="padding:0 15px;position:relative;" >
                    <img src="${getProcessedImagePath(ParkOrder_EnterImgPath)}" style="width:100%;height:15vw;" onerror="src='../Static/img/nophoto5x3.jpg'" />
                    <div class="orderdesc layui-hide layui-anim layui-anim-upbit">
                        <dl><dt>车牌颜色</dt><dd>${ParkOrder_CarTypeName}</dd></dl>
                        <dl><dt>入口通道</dt><dd>${ParkOrder_EnterPasswayName}</dd></dl>
                        <dl><dt>入口操作员</dt><dd>${ParkOrder_EnterAdminName}</dd></dl>
                        <dl><dt>车牌类型</dt><dd>${ParkOrder_CarCardTypeName}</dd></dl>
                        <dl><dt>锁车状态</dt><dd>${ParkOrder_Lock==1?"已锁车":"否"}</dd></dl>
                        {{if ParkOrder_EnterImgPath!=null&&ParkOrder_EnterImgPath!=''}}
                        <dl><dt>&nbsp;</dt><dd><a href="${getProcessedImagePath(ParkOrder_EnterImgPath)}" target="_blank" class="HrefBigImg">查看大图</a></dd></dl>
                        {{/if}}
                    </div>
                    <div class="areaname">${ParkOrder_ParkAreaName}</div>
                </div>
                <div class="layui-card-header" style="padding-top: 5px !important;">
                    <div class="btns">
                        <span class="del" data-orderno="${ParkOrder_No}" onclick="OnDel(this)">删除</span>
                        <span class="edit" data-orderno="${ParkOrder_No}" onclick="OnEdit(this)">修改车牌</span>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        var isFrpUrl = IsFrpURLOpenWeb('@Html.Raw(ViewBag.ParkKey)');
        topBar.init();

        layui.use(['table', 'form', 'laydate', 'laypage'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
        });

        var onShowDesc = function (e) {
            $(e).find('.orderdesc').removeClass("layui-hide");
        }

        var onHideDesc = function (e) {
            $(e).find('.orderdesc').removeClass("layui-hide").addClass("layui-hide");
        }
        
        // 处理图片路径
        function getProcessedImagePath(rawImgPath) {
            if (!rawImgPath) {
                return ''; 
            }
            try {
                var decodedPath = decodeURIComponent(rawImgPath);
                var checkedPath = PathCheck(decodedPath); 
                if (isFrpUrl) { 
                    return replaceFirstPathSegment(checkedPath); 
                } else {
                    return checkedPath;
                }
            } catch (e) {
                console.error("Error processing image path:", rawImgPath, e);
                return ''; // 处理错误，让 onerror 处理
            }
        }
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            pageSize: 12,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindData(1);
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option)
                        });
                    }
                }, "json");

                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                            $("#ParkOrder_ParkAreaNo").append(option)
                        });
                    }
                }, "json");

                layuiForm.render("select");
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: "datetime", range: true });
            },
            bindData: function (pageIndex) {
                pager.pageIndex = pageIndex;
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                LAYER_LOADING("加载中...");
                $.post("/InParkCar/GetParkOrderList", { pageIndex: pageIndex, pageSize: pager.pageSize, conditionParam: JSON.stringify(conditionParam) }, function (json) {
                    layer.closeAll();
                    if (json.code == 0) {
                        if (json.data.length == 0) {
                            $("#datapage").removeClass("layui-hide").addClass("layui-hide");
                            $("#datalist").html('<span class="norecord">无在场车辆记录</span>');
                        } else {
                            $("#datapage").removeClass("layui-hide");
                            $("#datalist").html($("#tmpldata").tmpl(json.data));
                        }
                        setPage(json.count);
                    } else {

                    }
                }, "json");
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
                $("#Expired").click(function () {

                    layer.open({
                        type: 2,
                        title: "逾期车牌处理",
                        content: "Expired",
                        area: getIframeArea(["1000px", "800px"]),
                        maxmin: false
                    })
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });

                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                        $("#ParkOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }

        var isSetPage = false;
        var setPage = function (count) {
            if (isSetPage) return;
            layui.laypage.render({
                elem: 'datapage'
                , curr: pager.pageIndex
                , count: count
                , limit: pager.pageSize
                , jump: function (obj, first) {
                    if (!first) {
                        pager.bindData(obj.curr)
                    }
                }
            });
            //isSetPage = true;
        }

        var OnDel = function (e) {
            var orderno = $(e).attr("data-orderno");
            LAYER_OPEN_TYPE_0("确定删除?", res => {
                LAYER_LOADING("处理中...");
                $.post("DelOrder", { ParkOrder_No: orderno }, function (json) {
                    if (json.success) {
                        LAYER_MSG("删除成功", res => {
                            pager.bindData(pager.pageIndex);
                        }, { time: 1000 });
                    } else {
                        LAYER_MSG(json.msg, null, { icon: 7, btn: ['确定'], time: 0 });
                    }
                }, "json");
            }, res => {

            })
        }

        var OnEdit = function (e) {
            var orderno = $(e).attr("data-orderno");
            layer.open({
                title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 修改车牌",
                type: 2, id: 1,
                area: getIframeArea(['780px', '520px']),
                fix: false, //不固定
                maxmin: false,
                content: 'Edit?ParkOrder_No=' + encodeURIComponent(orderno)
            });
        }
    </script>
</body>
</html>
