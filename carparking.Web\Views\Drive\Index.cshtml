﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>设备型号</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>设备型号</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="Drive_No" id="Drive_No" autocomplete="off" placeholder="型号编码" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="Drive_Name" id="Drive_Name" autocomplete="off" placeholder="型号名称" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="Drive_Fileds" id="Drive_Fileds" autocomplete="off" placeholder="型号别称" />
                            </div>
                            <div class="layui-inline form-group">
                                <select id="Drive_Category" name="Drive_Category" class="layui-select" lay-search>
                                    <option value="">型号分类</option>
                                    <option value="1">相机</option>
                                    <option value="2">道闸</option>
                                    <option value="3">显示屏</option>
                                    <option value="4">自助设备</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                                <button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
   
    <script type="text/html" id="TmplCategory">
        {{# if(d.Drive_Category==1){ }}
        <span class="layui-badge layui-bg-blue">相机</span>
        {{# }else if(d.Drive_Category==2){ }}
        <span class="layui-badge layui-bg-green">道闸</span>
        {{# }else if(d.Drive_Category==3){ }}
        <span class="layui-badge layui-bg-orange">显示屏</span>
        {{# }else if(d.Drive_Category==4){ }}
        <span class="layui-badge layui-bg-cyan ">自助设备</span>
        {{# }else if(d.Drive_Category==5){ }}
        <span class="layui-badge layui-bg-orange ">电子缴费机</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>
        var comtable = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'Drive_No', title: '型号编码', hide: true }
                , { field: 'Drive_Code', title: '型号代码' }
                , { field: 'Drive_Name', title: '型号名称' }
                , { field: 'Drive_Fileds', title: '型号别称' }
                , { field: 'Drive_Ip', title: 'IP' }
                , { field: 'Drive_Port', title: '端口' }
                , { field: 'Drive_Account', title: '账号' }
                , { field: 'Drive_Pwd', title: '密码' }
                , { field: 'Drive_Category', title: '型号分类', toolbar:"#TmplCategory"}
                , { field: 'Drive_Addtime', title: '创建时间' }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Drive/GetDriveList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增设备型号",
                            content: '/Drive/Edit?Act=Add',
                            area: getIframeArea(['660px', '620px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑设备型号",
                            content: '/Drive/Edit?Act=Update&Drive_No=' + data[0].Drive_No,
                            area: getIframeArea(['660px', '620px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data[0].Drive_IsDefault == 1) { layer.msg("系统默认数据不可删除"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/Drive/Delete", { Drive_No: data[0].Drive_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Drive/GetDriveList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
