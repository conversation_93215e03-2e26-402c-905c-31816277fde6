﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>储值车处理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v1.0" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }

        td { padding: 0px; margin: 0px; margin-bottom: 0mm; margin-top: 0mm; }
        th, td { text-align: center; }
        td { font-family: FangSong; }
        th.sumt { font-family: 'Microsoft YaHei'; text-align: left; text-indent: 15px; }

        .tdInput { border: 0px solid #fff; width: 100%; height: 100%; text-align: center; }
        .tdremark { width: 150px !important; }
        .tdOutTime, .tdInTime { width: 135px !important; }
        .iptAccount, .iptWeather { width: 100px; border: 1px solid #ccc; line-height: 25px; margin-bottom: 3px; font-weight: 100; }
        .del { margin-left: 2px; margin-right: 2px; }
        .layui-fluid { padding: 0px; }
        td[disabled] { background-color: #ccc !important; }
        input:hover { cursor: pointer !important; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card-body">
            <table border="1" id="table">
                <thead>
                    <tr style="border:0 solid #fff;">
                        <th colspan="12" style="font-size:22px;padding:5px;">修改账号余额</th>
                    </tr>
                    <tr>
                    </tr>
                    <tr>
                        <th style="width:80px;">系统车位号</th>
                        <th style="width:80px;">车主名称</th>
                        <th style="width:120px;">车主账号余额</th>
                        <th style="width:80px;">车牌号</th>
                        <th style="width:120px;">车辆账号余额</th>
                    </tr>
                </thead>
                <tbody id="CarContent">
                </tbody>
            </table>
            <span class="total"></span>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script src="~/Static/js/jquery.tmpl.min.js"></script>
    <script src="~/Static/js/jquery.common.js?2301021026"></script>
    <script src="~/Static/js/jquery.verify.js"></script>
    <script src="~/Static/js/pm.utils.js?v3.5"></script>

    <script>
        myVerify.init();

        // var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"));



        layui.use(['table', 'element', 'form'], function () {
            var table = layui.table, layuiForm = layui.form;

            pager.bindData();

        });

        var pager = {
            sortField: null,
            orderField: null,
            carCarNoList: [],
            carNoList: [],
            carParam: {},
            pageIndex: 1,
            //重新加载数据
            bindSelect: function () {
                $.ajaxSettings.async = false;

                $.ajaxSettings.async = true;
                layui.form.render();
            },
            bindData: function (index) {
                $.post("/Tools/GetStore", {}, function (json) {
                    layer.closeAll();

                    if (json.success) {
                        pager.bindShow(json.data);
                    } else {
                        layer.msg("系统异常", { icon: 2 });
                    }
                }, "json");
            },
            bindShow: function (data) {
                $("#CarContent").html('');
                window.parent.Tools.modifiedData = [];
                data.forEach((item, index) => {
                    var trHtml = '<tr>'
                        + '<td disabled><input class="tdInput" type="text" value="' + (item.Owner_Space ?? "") + '" disabled /></td>'
                        + '<td disabled><input class="tdInput" type="text" value="' + (item.Owner_Name ?? "") + '" disabled /></td>'
                        + '<td><input class="tdInput" type="text" value="' + (item.Owner_Balance ?? "") + '" /></td>'
                        + '<td disabled><input class="tdInput" type="text" value="' + (item.Car_CarNo ?? "") + '" disabled /></td>'
                        + '<td><input class="tdInput" type="text" value="' + (item.Car_Balance ?? "") + '" /></td>'
                        + '</tr>';
                    $("#CarContent").append(trHtml);

                    window.parent.Tools.modifiedData.push({
                        Owner_Space: item.Owner_Space ?? "",
                        Owner_Balance: item.Owner_Balance ?? "",
                        Car_CarNo: item.Car_CarNo ?? "",
                        Car_Balance: item.Car_Balance ?? ""
                    })
                });

                $(".total").text("当前共" + data.length + "条数据")

                // 监听所有 tdInput 类的 input 元素的 change 事件
                $(document).on('change', '.tdInput', function () {
                    // 获取当前 input 所在的行
                    var $tr = $(this).closest('tr');

                    // 收集这一行的所有数据
                    var rowData = {
                        Owner_Space: $tr.find('td:eq(0) input').val(),
                        Owner_Balance: $tr.find('td:eq(2) input').val(),
                        Car_CarNo: $tr.find('td:eq(3) input').val(),
                        Car_Balance: $tr.find('td:eq(4) input').val()
                    };

                    // 查找 modifiedData 数组中是否已经存在这一行的数据
                    var existingIndex = window.parent.Tools.modifiedData.findIndex(item =>
                        item.Car_CarNo === rowData.Car_CarNo
                    );

                    if (existingIndex > -1) {
                        // 更新已存在的数据
                        window.parent.Tools.modifiedData[existingIndex] = rowData;
                    } else {
                        // 添加新的数据
                        window.parent.Tools.modifiedData.push(rowData);
                    }

                    console.log(window.parent.Tools.modifiedData);
                });
            }


        }

    </script>




</body>
</html>
