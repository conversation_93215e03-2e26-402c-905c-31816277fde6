﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>充值规则</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>充值规则</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="MonthRule_Name" id="MonthRule_Name" autocomplete="off" placeholder="规则名称" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="MonthRule_CarCardTypeNo" name="MonthRule_CarCardTypeNo">
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="MonthRule_BeginTime0" id="MonthRule_BeginTime0" autocomplete="off" placeholder="开始时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="MonthRule_BeginTime1" id="MonthRule_BeginTime1" autocomplete="off" placeholder="开始时间止" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="MonthRule_EndTime0" id="MonthRule_EndTime0" autocomplete="off" placeholder="结束时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="MonthRule_EndTime1" id="MonthRule_EndTime1" autocomplete="off" placeholder="结束时间止" />
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>

                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.BillingRule.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.BillingRule.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.BillingRule.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplCycle">
        {{d.MonthRule_Cycle}}
        {{# if(d.MonthRule_Unit==1){ }}
        <span class="layui-badge">天</span>
        {{# }else if(d.MonthRule_Unit==2){ }}
        <span class="layui-badge">月</span>
        {{# }else if(d.MonthRule_Unit==3){ }}
        <span class="layui-badge">年</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplAddCycle">

        {{# if(d.MonthRule_AddEnable==1){ }}

        {{d.MonthRule_AddCycle}}
        {{# if(d.MonthRule_AddUnit==1){ }}
        <span class="layui-badge">天</span>
        {{# }else if(d.MonthRule_AddUnit==2){ }}
        <span class="layui-badge">月</span>
        {{# }else if(d.MonthRule_AddUnit==3){ }}
        <span class="layui-badge">年</span>
        {{# } }}

        {{# }else{ }}
        <span class="layui-badge-rim">无</span>
        {{# } }}


    </script>
    <script type="text/x-jquery-tmpl" id="tmplTime">
        {{d.MonthRule_BeginTime}} -  {{d.MonthRule_EndTime}}
        {{# if(getStatus(d.MonthRule_BeginTime,d.MonthRule_EndTime)==1){}}
        <span class="layui-badge layui-bg-green">未生效</span>
        {{# }else if( getStatus(d.MonthRule_BeginTime,d.MonthRule_EndTime)==2){}}
        <span class="layui-badge layui-bg-blue">已生效</span>
        {{# }else if( getStatus(d.MonthRule_BeginTime,d.MonthRule_EndTime)==3){}}
        <span class="layui-badge layui-bg-cyan">已过期</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplUnit">
        {{# if(d.MonthRule_Unit==1){ }}
        <span class="layui-badge">天</span>
        {{# }else if(d.MonthRule_Unit==2){ }}
        <span class="layui-badge">月</span>
        {{# }else if(d.MonthRule_Unit==3){ }}
        <span class="layui-badge">年</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t2" asp-append-version="true"></script>
    <script>
        var Power = window.parent.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            layuiForm = layui.form;

            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = CraeteCols();
            cols = [cols];
            cols = tb_page_cols(cols);
            comtable = table.render({
                elem: '#com-table-base'
                , url: 'GetMonthRuleList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var cardno = $("#MonthRule_CarCardTypeNo").val();
                var carno = $("#MonthRule_CarTypeNo").val();
                var category = $("#MonthRule_CarCardTypeNo").find("option:selected").attr("data-category");

                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增充值规则",
                            content: 'MonthChargeEdit?Act=Add&cardno=' + cardno + '&carno=' + carno + '&category=' + category,
                            area: getIframeArea(['700px', '610px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑充值规则",
                            content: 'MonthChargeEdit?Act=Update&MonthRule_No=' + data[0].MonthRule_No + '&category=' + category,
                            area: getIframeArea(['700px', '610px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("DelMonthRule", { MonthRule_No: data[0].MonthRule_No }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                            pager.bindData(pager.pageIndex);
                                        });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

        });
    </script>
    <script>
        var pager = {
            category: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

                _DATE.bind(layui.laydate, ["MonthRule_BeginTime0", "MonthRule_BeginTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["MonthRule_EndTime0", "MonthRule_EndTime1"], { type: 'datetime', range: true });

                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.carCardTypes = [];
                        json.data.carCardTypes.forEach(function (item, index) {
                            if (",3657,3652,3653,3654,3655,3661,3662,3663,3664,3656,".indexOf(item.CarCardType_Category) >= 0) {
                                pager.carCardTypes[pager.carCardTypes.length] = item;
                            }
                        });
                        pager.carTypes = json.data.carTypes;
                        pager.parkAreas = json.data.parkAreas;

                        $("#MonthRule_CarCardTypeNo").html('<option value="">车牌类型</option>');
                        $("#MonthRule_CarCardTypeNo").append($("#tmplcarcardtype").tmpl(pager.carCardTypes));

                        layuiForm.render("select")
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

                var cols = CraeteCols();
                comtable.reload({
                    url: 'GetMonthRuleList'
                    , cols: [cols]
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                layuiForm.on("select", function (data) {
                    pager.bindData(1);
                });
                $("#Search").click(function () { pager.bindData(1); });
            }
        }

        var CraeteCols = function () {

            var category = $("#MonthRule_CarCardTypeNo").find("option:selected").attr("data-category") || "3";
            var cols = [];
            cols[cols.length] = { type: 'radio' };
            cols[cols.length] = { field: 'MonthRule_No', title: '规则编码', width: 160, hide: true };
            cols[cols.length] = { field: 'MonthRule_Name', title: '规则名称' };
            cols[cols.length] = { field: 'MonthRule_BeginTime', title: '规则有效期', toolbar: "#tmplTime", width: 250 };
            cols[cols.length] = { field: 'MonthRule_CarCardTypeName', title: '车牌类型' };
            cols[cols.length] = { field: 'MonthRule_Money', title: '收费金额(元)' };
            return cols;
        }

        var now = $.ajax({ async: false }).getResponseHeader("Date");
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd 00:00:00"));
        function getStatus(BeginTime, EndTime) {
            if (BeginTime && EndTime) {
                if (new Date(EndTime.replace(/-/g, "/")) >= nowDate) {
                    if (new Date(BeginTime.replace(/-/g, "/")) > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }


        //绑定查询事件
        $(function () {
            $("#BtnSearch").click(function () { pager.bindData(1); });
        });
    </script>
</body>
</html>
