﻿@using TcpConnPools.Camera
@using carparking.DirectCloudMQTT
<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>快速配置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v31" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/operationscenter/driver/driver.min.css" rel="stylesheet" />
    <style>
        html,
        body { width: 100%; height: 100%; overflow: auto; margin: 0; padding: 0; min-width: 583px !important; background-color: #ecf0f5; font-family: 'Microsoft YaHei'; font-size: 1rem; }
        .layui-tab { margin: 0px !important; }
        .layui-tab-title { user-select: none; }
        .box-table,
        .box-bottom { margin: 0 15px; overflow: auto; }
        .box-bottom { text-align: left; padding: 20px 0; }
        .layui-table,
        .layui-table-view { margin: 0 !important; }
        .layui-tab-content { margin: 0; padding: 0 !important; }
        .layui-table-cell { overflow: visible !important; }
        .tdSelect { width: 100%; max-width: 200px; height: 30px; border: 1px solid #d9d9d9; border-radius: 3px; color: #0094ff; font-size: 13px; }
        .layui-elem-quote { border-left: 0 !important; }
        .layui-nav-item t { color: #000 !important; padding: 0 20px; user-select: none; }
        .layui-this t { color: #5FB878 !important; }

        .divTop { margin: 0; padding: 0; width: 99%; height: 5%; /* border-bottom: 1px solid #ccc; */ text-align: left; line-height: 2.2rem; background-color: #04345fdb; color: #fff; }
        .divContent { margin: 0; padding: 0; width: 100%; height: 100%; display: flex; border: 0; }
        .divContentItem { height: 100%; }

        .divLeft { width: 18%; display: flex; flex-direction: column; min-width: 120px; }
        .leftItem { margin: 5px 10px; }
        .leftItem1 { background-color: #fff; flex: 4; margin: 10px 5px 0px 12px; border-top-right-radius: 10px; border-top-left-radius: 10px; border: 1px solid #ccc; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);overflow:auto; }
        .leftItem2 { background-color: #fff; flex: 3; display: flex; flex-direction: column; align-items: center; justify-content: center; border: 1px solid #ccc; border-top: 1px solid #009688; margin: -1px 5px 0px 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }
        .leftItem2 .divBtn { width: 100%; text-align: center; margin-top: 10px; }
        .leftItem2 .divBtn button { width: 80%; height: 35px; line-height: 35px; border-radius: 15px; font-weight: bold; }
        .leftItem2 .divBtn.tx button { color: #1ab394; border-color: #1ab394 !important; background-color: transparent !important; border-radius: 15px; }
        .leftItem3 { background-color: #fff; flex: 3; display: flex; flex-direction: column; align-items: center; justify-content: center; border: 1px solid #ccc; border-top: 1px solid #009688; margin: -1px 5px 10px 12px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); padding: 0 12px; }
        .leftItem3 b { color: #009688; font-size: 1.5rem; }
        .leftItem3 pre { color: #009688; margin: 1px; margin-top: 0.8rem; width: 100%; }
        .line { border-top: 1px solid #ccc; width: 100%; }

        .divRight { width: 82%; display: flex; flex-direction: column; }
        .rightContent { width: 100%; height: 100%; border-left: 1px solid #ccc; display: flex; flex-direction: column; }
        .rightItem { overflow-y: auto; display: flex; flex: 5; flex-direction: column; }
        .rightItem1 { border: 1px solid #ccc; overflow-y: auto; margin: 10px; border-radius: 10px; background-color: #fff; }
        .rightItem2 { border: 1px solid #ccc; overflow-y: auto; margin: 10px; border-radius: 10px; background-color: #fff; }
        .rightItem3 { border: 1px solid #ccc; overflow-y: auto; margin: 0 10px 10px 10px; border-radius: 10px; background-color: #fff; flex: 0.5; min-height: 60px; text-align: center; line-height: 60px; }

        table > thead > tr { font-weight: bolder !important; background-color: #edf1f1 !important; border: 1px solid #dbd3c9 !important; }
        table > thead > tr > td { background-color: #009688 !important; font-size: 1.2rem !important; color: #fff; height: 3rem; }
        td > div input { border-color: #bee1da !important; }
        input#Car_CarNo { border-color: #01aaed !important }
        .layui-table td, .layui-table th { padding: 2px 5px; }
        .layui-input[readonly] { background-color: #fdfdfd !important; }

        .divSelectPop { width: 12rem; height: 10rem; overflow: auto; background-color: #fff; min-width: 200px; }
        .divSelect { padding: 0 10px; line-height: 2rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; cursor: pointer; }
        .divSelect.selected { background-color: #5FB878; color: #fff; }
        .divSelect:hover { background-color: #218cbf; color: #e9f3ec; }

        .layui-tree-entry { height: 24px !important; line-height:24px !important; font-size:17px;}
        .layui-tree-entry:hover { background-color: #f9f9f9 !important; color: #1E9FFF !important; }
        .layui-tree-entry:hover * { color: #1E9FFF !important; }


        .layui-input, .layui-select, .layui-textarea { line-height: 2.3rem !important; height: 2.3rem !important; font-size:16px; }
        .layui-table td, .layui-table th { padding: 1px 2px !important; min-height: 1.7rem !important; line-height: 1.7rem !important; }
        table .layui-btn:not(.input-container button) { height: 0px !important; line-height: 0px !important; border: 0 !important; padding: 0 !important; background-color: transparent; }

        /* 修改图标的大小 */
        .layui-tree .layui-icon-edit { font-size: 1.5rem !important; border: 1px solid #ccc; background: #ffffff !important; z-index: 9999; }
        .layui-tree .layui-icon-add { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: #ffffff !important; z-index: 9999; }
        .layui-tree .layui-icon-add-1 { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: #ffffff !important; z-index: 9999; }
        .layui-tree .layui-icon-delete { font-size: 1.5rem !important; border: 1px solid #ccc; background: #ffffff !important; z-index: 9999; }
        .layui-tree .layui-tree-editInput { border: 1px solid #0f9ee9; color: #000; background-color: #fff; height: 1.6rem; line-height: 1.6rem; }
        /*         .layui-tree .layui-icon-edit { font-size: 1.5rem !important; border: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
                                                                                                                                                        .layui-tree .layui-icon-add { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
                                                                                                                                                        .layui-tree .layui-icon-add-1 { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
                                                                                                                                                        .layui-tree .layui-icon-delete { font-size: 1.5rem !important; border: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; } */
        .layui-btn-group.layui-tree-btnGroup { display: flex; justify-content: left; position: absolute; bottom: -1.4rem; width: 100%; margin-left: 3rem; }
        .layui-btn-group.layui-tree-btnGroup i { cursor: pointer; }
        /* 温馨提示特效 */
        .btnCombox ul li { float: left; padding: 0px; min-width: 60px; background-color: #f0f0f0; line-height: 1.5rem; text-align: center; cursor: pointer; border-right: 1px solid #fff; }
        @@keyframes rotate {
            0% { transform: rotate(0); }
            20% { transform: rotate(-5deg); }
            60% { transform: rotate(0); }
            80% { transform: rotate(5deg); }
            100% { transform: rotate(0); }
        }
        .codeX { animation: rotate 2s ease infinite; }

        .parkname { line-height: 2.5rem; }

        .layui-btn + .layui-btn { margin-left: 0px !important; }

        table { width: 100%; border-collapse: collapse; }
        thead { display: table; width: 100%; text-align: center; color: rgb(74, 74, 74) }
        thead td { position: sticky; /* 使用sticky定位 */ top: 0; /* 距离顶部0 */ z-index: 1; /* 使表头在前面 */ }
        tbody { display: block; overflow-y: auto; max-height: 15rem; /* 可根据需要调整 */ }
        thead td { position: -webkit-sticky; /* 适用于 Safari */ position: sticky; top: 0; z-index: 1; }

        .layui-icon-file::before { content: '\e715'; font-size: 18px; color: #1ab394; }

        .layui-icon-subtraction:before { }

        .btn { /* min-width: 6rem; */ cursor: pointer; }
        .btn img { margin-right: .2rem; }
        .btn img:hover { border: 1px solid #FF5722; }
        .layui-table img { border-radius: 6px; }

        .headDiv { display: inline-block; }
        .refreshPark, .refreshLink { margin-left: 5px; }
        .refreshPark > img, .changemode > img { cursor: pointer !important; border: 1px solid #fff; border-radius: 5px; }
        .refreshPark > img:hover, .changemode > img:hover { background-color: #2ba4ed; border-radius: 30px; line-height: 1.2rem; }

        .refreshLink { cursor: pointer !important; border: 1px solid #fff; border-radius: 5px; height: 1.2rem; line-height: 1.2rem; position: relative; top: .16rem; }
        .refreshLink:hover { background-color: #2ba4ed; border-radius: 30px; line-height: 1.2rem; }

        .yunset { border: 1px solid #fff; border-radius: 5px; line-height: 1.1rem; height: 1.1rem; padding: 1px 5px; cursor: pointer; margin-top: .5rem; font-size: 12px; }
        .yunset:hover { background-color: #fff; color: #04345fdb; font-weight: 600; border: 1px solid #555; }

        .dbdata { right: 18px; position: absolute; height: 1.2rem; line-height: 1.2rem; top: .9rem; }
        /* 顶级节点悬停时隐藏编辑和删除图标 */
        .top-level-node:hover .layui-icon-edit,
        .top-level-node:hover .layui-icon-delete { display: none !important; }
        .layui-layer-title { font-weight: 600; }
        .clear-icon { position: absolute; top: 3px; right: 3px; font-size: 16px; cursor: pointer; color: #FF5722; z-index: 9999; height: 20px; line-height: 20px; background-color: #e1e1e1; border-radius: 5px; padding: 5px; }

        .input-container { position: relative; width: 100%; }
        .input-container button { position: absolute; right: 0px; top: 50%; transform: translateY(-50%); background-color: #0F9EE9; z-index: 1; }

        .layui-tree-entry:hover { box-shadow: 1px 1px 1px rgb(193 184 184 / 60%); background-color: #ededed !important; }

        #successMessage { height: 100%; }
        .successp { font-size: 1.8rem; font-weight: 600; color: #fff; text-align: center; background-color: #37cf1e; padding: 1rem; }
        .device-list { max-height: 180px; overflow-y: auto; }
        .device-item { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #e2e2e2; }
        .device-status { font-weight: bold; }
        .success { color: green; }
        .fail { color: red; }

        .layui-layer-title { font-weight: 600; }

        .resend-btn { position: absolute; bottom: 0; background: #f2f2f2; text-align: right; width: 100%; padding: 10px 0px; }
        .resend-btn button { background-color: #1ab394; color: white; border: none; padding: 10px 20px; cursor: pointer; border: 0; margin-right: 15px; border-radius: 4px; }
        .resend-btn button:hover { background-color: #1ab394; }
        .resend-btn button:disabled { background-color: #bbb5b3; }
        #Cancel2, #Cancel3 { background-color: #FFB800 !important; }
        .devicenum { float: left; margin: 6px 12px; font-size: 1.1rem; }

        .help-btn { width: 20px !important; height: 20px !important; text-align: center; line-height: 20px; margin-left: 0px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }

        .add-btn { z-index: 999; width: 26px !important; height: 25px !important; line-height: 25px; margin-left: 5px; cursor: pointer; background-color: #1e9fff; border-radius: 15px; }
        .add-btn:after { font-weight: bold; }
        .add-btn:hover { font-weight: bold; }
        .add-btn img { width: 26px; height: 26px; }

        #passwayset img { width: 100%; object-fit: cover; object-position: center; }
        .saveText { color: #1080d4; }
        #start-tour { margin-top: 1rem; width: 100%; text-align: center; color: #1200ff; text-decoration: underline; cursor:point;}
        #Clear { margin-right: 25px; font-size: 1vw; font-weight: bold; background-color: #FF5722; }
        #Save { font-size: 1vw; font-weight: bold; }
    </style>
</head>

<body class="animated fadeIn layui-layout layui-layout-admin">

    <!-- 内容 -->
    <div class="divContent">
        <div class="divContentItem divLeft">
            <!-- 区域 -->
            <div id="step1" class="leftItem leftItem1">
                <div id="parkareaList"></div>
            </div>
            <!-- 快速按钮 -->
            <div class="leftItem leftItem2">
                <div class="divBtn tx"><button class="layui-btn" id="TwoLaneConfig" lay-event="TwoLaneConfig"><span class="tx">二车道快速配置</span></button></div>
                <div class="divBtn tx"><button class="layui-btn" id="FourLaneConfig" lay-event="FourLaneConfig"><span class="tx">四车道快速配置</span></button></div>
                <div class="divBtn tx"><button class="layui-btn" id="EightLaneConfig" lay-event="EightLaneConfig"><t>八车道快速配置</t></button></div>
                <div class="divBtn tx"><button class="layui-btn" id="ConstomConfig" lay-event="ConstomConfig"><t>自定义快速配置</t></button></div>
            </div>
            <!-- 温馨提示 -->
            <div class="leftItem leftItem3">
                <b class="codeX">温馨提示</b>

                <pre>1、【清空配置】：清空所有车道和设备！</pre>
                <pre>2、【*快速配置】：则在原配置新增车道和设备！</pre>
                <pre>3、必须点击【<t style="color:#FF5722;">保存</t>】才生效！！！</pre>
                 
                <a id="start-tour" href="javascript:;" onclick="startDriver()"> 操作向导 →</a>
            </div>
        </div>
        <!-- 车道、设备 -->
        <div class="divContentItem divRight">
            <!-- 车道 -->
            <div id="step2" class="rightItem rightItem1">
                <div class="layui-row">
                    <div class="layui-col-x11 table-container" style="overflow:hidden;">
                        <table class="layui-table table_passway" style="overflow:hidden;">
                            <thead>
                                <tr>
                                    <td style="width:15%;">
                                        <div class="layui-col-xs12 head"> 车道编号</div>
                                    </td>
                                    <td style="width:20%;;">
                                        <div class="layui-col-xs12 head"> 车道名称</div>
                                    </td>
                                    <td style="width:20%;">
                                        <div class="layui-col-xs12 head"> 所属区域</div>
                                    </td>
                                    <td style="width:10%;">
                                        <div class="layui-col-xs12 head"> 出入口</div>
                                    </td>
                                    <td style="width:25%;">
                                        <div class="layui-col-xs12 head"> <div style="width:100%;text-align: center;">关联区域(选填)<i class="help-btn">?</i> </div></div>
                                    </td>
                                    <td style="width:10%;">
                                        <div class="head">添加<i class="add-btn"><img class="btnadd" src="../Static/img/add.svg" title="点击添加" alt="添加"></i></div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody class="tbody">
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
            <!-- 设备 -->
            <div id="step3" class="rightItem rightItem2">
                <div class="layui-row" style="overflow:hidden !important;">
                    <div class="layui-col-xs12 table-container" style="overflow:hidden !important;">
                        <table class="layui-table table_device" style="overflow:hidden !important;">
                            <thead>
                                <tr>
                                    <td style="width:23%;">
                                        <div class="layui-col-xs12 head"> 设备IP</div>
                                    </td>
                                    <td style="width:20%;">
                                        <div class="layui-col-xs12 head"> 设备名称</div>
                                    </td>
                                    <td style="width:18%;">
                                        <div class="layui-col-xs12 head"> 所属车道</div>
                                    </td>
                                    <td style="width:17%;">
                                        <div class="layui-col-xs12 head"> 设备型号</div>
                                    </td>
                                    <td style="width:12%;">
                                        <div class="layui-col-xs12 head"> 设备类型</div>
                                    </td>
                                    <td style="width:10%;">
                                        <div class="head">添加<i class="add-btn"><img class="btnadd" src="../Static/img/add.svg" title="点击添加" alt="添加"></i></div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody class="tbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- 操作按钮 -->
            <div class="rightItem rightItem3">
                <div class="layui-row">
                    <div class="layui-col-xs4">&nbsp;</div>
                    <div class="layui-col-xs4">
                        <button class="layui-btn layui-btn-warm" id="Clear" lay-event="Clear"><i class="fa fa-close"></i> <t>清空配置</t></button>
                        <button class="layui-btn" id="Save" lay-event="Save"><i class="fa fa-save"></i> <t>保存</t></button>
                    </div>
                    <div class="layui-col-xs4">&nbsp;</div>
                </div>
            </div>

        </div>
    </div>

    <!-- 底部内容 -->
    <div class="divBottom">
    </div>

    <div id="passwayset" style="display: none;">
        <img src="~/Static/img/desc/passwayset.jpg" />
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/driver/driver.min.js"></script>

    <script type="text/x-jquery-tmpl" id="tmpl_passway">
        <tr class="firsttime-tr">
            <td style="width:15%;">
                <div class="layui-col-xs12">
                    <input type="text" class="layui-input" maxlength="50" value="${Passway_No}" data-key="Passway_No" placeholder="车道编号" />
                </div>
            </td>
            <td style="width:20%;">
                <div class="layui-col-xs14">
                    <input type="text" class="layui-input" maxlength="50" value="${Passway_Name}" data-key="Passway_Name" placeholder="车道名称" />
                </div>
            </td>
            <td style="width:20%;">
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${PasswayLink_ParkAreaName}" data-value="${PasswayLink_ParkAreaNo}" data-key="PasswayLink_ParkAreaNo" placeholder="所属区域" onclick="SetSelectDiv(this,1)" />
                </div>
            </td>
            <td style="width:10%;">
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="20" value="${PasswayLink_GateTypeName}" data-value="${PasswayLink_GateType}" data-key="PasswayLink_GateType" placeholder="出入口"  onclick="SetSelectDiv(this,2)"/>
                </div>
            </td>
              <td style="width:25%;">
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${PasswayLink_SubParkAreaName}" data-value="${PasswayLink_SubParkAreaNo}" data-key="PasswayLink_SubParkAreaNo" placeholder="内场的入口外场的出口" onclick="SetSelectDiv(this,1)" />
                </div>
            </td>
            <td style="max-width:10%;min-width:75px;text-align:center;">
                  <div class="layui-col-xs12 btn">
                     <img class="btndel" src="../Static/img/delete.svg" alt="删除" title="删除" style="width: 26px; height: 26px;margin-left:8px;">
                  </div>
            </td>
        </tr>
    </script>

    <script type="text/x-jquery-tmpl" id="tmpl_device">
        <tr class="firsttime-tr">
             <td style="width:23%;">
                <div class="layui-col-xs12">
                    <div class="input-container">
                        <input type="text" class="layui-input" maxlength="30" value="${Device_IP}" data-value="${Device_IP}" data-key="Device_IP" placeholder="点击搜索" onclick="ShowDeviceIPDiv(this)"/>
                    </div>
                </div>
            </td>
            <td style="width:20%;">
                <div class="layui-col-xs12">
                    <input type="text" class="layui-input" maxlength="50" value="${Device_Name}" data-value="${Device_No}"  data-key="Device_Name" placeholder="设备名称" />
                </div>
            </td>
            <td style="width:18%;">
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="50" value="${Passway_Name}" data-value="${Device_PasswayNo}" data-key="Device_PasswayNo" placeholder="关联车道"  onclick="SetSelectDiv(this,4)"/>
                </div>
            </td>
            <td style="width:17%;">
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${Drive_Name}" data-value="${Device_DriveNo}" data-key="Device_DriveNo" placeholder="设备型号"  onclick="SetSelectDiv(this,3)"/>
                </div>
            </td>
             <td style="width:12%;">
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${Device_IO == 1 ? '主相机' : '辅相机'}" data-value="${Device_IO}" data-key="Device_IO" placeholder="设备类型"  onclick="SetSelectDiv(this,5)"/>
                </div>
            </td>
            <td style="max-width:10%;min-width:75px;text-align:center;">
                 <div class="layui-col-xs12 btn">
                     <img class="btndel" src="../Static/img/delete.svg" alt="删除" title="删除" style="width: 26px; height: 26px;margin-left:8px;">
                  </div>
            </td>
        </tr>
    </script>
    @*界面操作*@
    <script>
        var layuiIndex = 0;

        var tree = null;
        var layuiForm = null;

        var inoutData = [{
            title: '入口',
            id: 1,
            spread: true,
        }, {
            title: '出口',
            id: 0,
            spread: true,
        }];
        var deviceIOData = [{
            title: '主相机',
            id: 1,
            spread: true,
        }, {
            title: '辅相机',
            id: 0,
            spread: true,
        }];

        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'tree', 'layer'], function () {
            tree = layui.tree, layer = layui.layer
            layuiForm = layui.form;

            pager.init()

        });

        var parkmode = '@ViewBag.SentryMode';
        var sentryMode = @(MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0);
        var emergencyMode = @(MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0);
        var parkIndex = null;

        var pager = {
            deviceSend: [],
            passways: null,
            parkareas: null,
            devices: null,
            drives: null,
            editDeviceRow: null,
            inoutdata: inoutData,
            deviceiodata: deviceIOData,
            model: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
                // pager.getParkStatus();
            },
            bindSelect: function () {
                layuiForm.render("select");
            },
            //数据绑定
            bindData: function () {
                $.getJSON("/FastGuide/GetAllFastMsg", {}, function (json) {
                    if (json.success) {
                        layui.form.render("select")

                        pager.passways = json.data.passways;
                        pager.parkareas = json.data.parkareas;
                        pager.devices = json.data.devices;
                        pager.drives = json.data.drives;

                        pager.bindAreas();
                        Passway_Table.binddata(".table_passway", pager.passways);
                        Device_Table.binddata(".table_device", pager.devices);
                    }
                });
            },
            bindAreas: function () {
                $("#parkareaList").html("");
                // 初始化树
                var inst1 = tree.render({
                    elem: '#parkareaList',
                    data: pager.parkareas,
                    id: 'treeDemo',
                    edit: ['update', 'del', 'add'], // 启用编辑功能
                    click: function (obj) {
                        console.log(obj); // 点击事件
                        // pager.onNode(obj);
                    },
                    operate: function (obj) {
                        var type = obj.type; //得到操作类型：add、edit、del
                        var nodeData = obj.data; //得到当前节点的数据
                        var elem = obj.elem; //得到当前节点元素

                        if (type != 'add' && nodeData.id == "0") {
                            layer.msg('当前不可编辑');
                            pager.bindAreas();
                            return;
                        }

                        var tip = nodeData.id == "0" ? "请输入最外场的区域名称" : "请输入下一级的区域名称";

                        // 根据操作类型执行相应的操作
                        if (type === 'add') {
                            console.log('添加节点', nodeData);
                            layer.open({
                                type: 1,
                                title: tip,
                                content: '<textarea id="newNodeName" placeholder="区域名称最大长度50字符" class="large-textarea" maxlength="50" style="width: 90%; border: 1px solid #ccc;  height: 77%;padding:3px;  margin-left: 12px;    margin-top: 10px;"></textarea>',
                                btn: ['确认', '取消'],
                                area: ['300px', '220px'], // 调整弹出层的宽高
                                success: function (layero, index) {
                                    $('#newNodeName').focus();
                                },
                                yes: function (index, layero) {
                                    var value = $('#newNodeName').val();
                                    if (value) {
                                        // 添加新节点
                                        var newNode = { title: value, id: createNumber(3, pager.parkareas), spread: true };
                                        // 更新原始数据
                                        pager.updateDataWithNewNode(pager.parkareas, nodeData.id, newNode);
                                        // 重新加载树以显示新节点
                                        pager.reload(inst1, { data: pager.parkareas });
                                        // 关闭提示框
                                        layer.close(index);
                                    } else {
                                        layer.msg('区域名称不能为空');
                                    }
                                }, btn2: function (index, layero) {
                                    // 关闭提示框
                                    layer.close(index);
                                    // 重新加载树以显示新节点
                                    pager.reload(inst1, { data: inst1.config.data });
                                    return false;
                                }, end: function (index) {
                                    // 重新加载树以显示新节点
                                    pager.reload(inst1, { data: inst1.config.data });
                                    // 关闭提示框
                                    layer.close(index);
                                    return false;
                                }
                            });
                            // setTimeout(function () {
                            //     // 展开父节点
                            //     var parentElem = elem.closest('.layui-tree-pack').find('.layui-tree-entry');
                            //     parentElem.find('.layui-tree-iconClick').click();
                            // },50);

                        } else if (type === 'update') {
                            console.log('更新节点', nodeData);
                            pager.updateDataWithNewNode2(pager.parkareas, nodeData.id, nodeData);
                            pager.reload(inst1, { data: pager.parkareas });
                            // console.log('源数据：', pager.parkareas);

                        } else if (type === 'del') {
                            console.log('删除节点', nodeData);

                        }
                    },
                });

                document.querySelectorAll('.layui-tree >.layui-tree-set> .layui-tree-entry:first-child').forEach(function (node) {
                    node.classList.add('top-level-node');
                });

                // 绑定自定义删除按钮事件
                document.querySelectorAll('.layui-tree .layui-icon-delete').forEach(function (delBtn) {
                    delBtn.onclick = function (event) {
                        event.stopPropagation(); // 阻止事件冒泡
                        var elem = event.target.closest('.layui-tree-set');
                        var nodeId = elem.getAttribute('data-id');
                        var textName = event.target.closest('.layui-tree-entry').innerText;

                        // 自定义删除提示框
                        layer.confirm('确定删除 ' + textName + ' 区域吗？', { icon: 3, title: '提示' }, function (index) {
                            pager.removeNodeFromData(pager.parkareas, nodeId);
                            pager.reload(inst1, { data: pager.parkareas });
                            // 确定删除
                            layer.close(index);
                        });
                    };
                });
            },
            reload: function (inst1, data) {

                inst1.reload(data);

                document.querySelectorAll('.layui-tree >.layui-tree-set> .layui-tree-entry:first-child').forEach(function (node) {
                    node.classList.add('top-level-node');
                });

                // 绑定自定义删除按钮事件
                document.querySelectorAll('.layui-tree .layui-icon-delete').forEach(function (delBtn) {
                    delBtn.onclick = function (event) {
                        event.stopPropagation(); // 阻止事件冒泡
                        var elem = event.target.closest('.layui-tree-set');
                        var nodeId = elem.getAttribute('data-id');
                        var textName = event.target.closest('.layui-tree-entry').innerText;

                        // 自定义删除提示框
                        layer.confirm('确定删除 ' + textName + ' 区域吗？', { icon: 3, title: '提示' }, function (index) {
                            pager.removeNodeFromData(pager.parkareas, nodeId);
                            pager.reload(inst1, { data: pager.parkareas });
                            // 确定删除
                            layer.close(index);
                        });
                    };
                });
            },
            // 更新数据中添加新节点
            updateDataWithNewNode2: function (data, parentId, newNode) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].id === parentId) {
                        data[i] = newNode;
                        return true;
                    }
                    if (data[i].children) {
                        var result = pager.updateDataWithNewNode2(data[i].children, parentId, newNode);
                        if (result) {
                            return true;
                        }
                    }

                }
                return false;
            },
            // 更新数据中添加新节点
            updateDataWithNewNode: function (data, parentId, newNode) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].id === parentId) {
                        if (!data[i].children) {
                            data[i].children = [];
                        }
                        data[i].children.push(newNode);
                        return true;
                    }
                    if (data[i].children) {
                        var result = pager.updateDataWithNewNode(data[i].children, parentId, newNode);
                        if (result) {
                            return true;
                        }
                    }

                }
                return false;
            },
            // 从数据中移除节点
            removeNodeFromData: function (data, id) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].id === id) {
                        Passway_Table.delPasswayByArea(id);
                        if (data[i] && data[i].children && data[i].children != null) {
                            for (var j = 0; j < data[i].children.length; j++) {
                                Passway_Table.delPasswayByArea(data[i].children[j].id);

                                var result = pager.removeNodeFromData(data[i].children, data[i].children[j].id);
                                if (result) {
                                    // 如果子节点数组为空，则删除children属性
                                    if (data[i].children.length === 0) {
                                        delete data[i].children;
                                        break;
                                    }
                                }
                            }
                        }
                        data.splice(i, 1);
                        return true;
                    }
                    if (data[i].children) {
                        var result = pager.removeNodeFromData(data[i].children, id);
                        if (result) {
                            // 如果子节点数组为空，则删除children属性
                            if (data[i].children.length === 0) {
                                delete data[i].children;
                            }
                            return true;
                        }
                    }
                }
                return false;
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                //二车道快速配置
                $("#TwoLaneConfig").click(function () {
                    layer.open({
                        title: false,
                        content: '将在最外层区域，生成1进1出二车道配置，确定吗？',
                        btn: ['确定', '取消'], // 自定义按钮
                        yes: function (index, layero) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#TwoLaneConfig").attr("disabled", true);

                            pager.passways = Passway_Table.getValues(".table_passway");
                            pager.devices = Device_Table.getValues(".table_device");
                            var param = { areas: pager.parkareas, passway: pager.passways, devices: pager.devices };

                            $.post("TwoLaneConfig", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    $("#TwoLaneConfig").removeAttr("disabled");
                                    layer.msg("已配置，请查看保存", { icon: 1, time: 1500 }, function () {
                                        pager.passways = json.data.passways;
                                        pager.parkareas = json.data.parkareas;
                                        pager.devices = json.data.devices;

                                        pager.bindAreas();
                                        Passway_Table.binddata(".table_passway", pager.passways);
                                        Device_Table.binddata(".table_device", pager.devices);

                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0 });
                                    $("#TwoLaneConfig").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function (index, layero) {
                            // 取消按钮的回调
                            // 默认关闭弹出层，返回 true 可以阻止关闭
                            // return true;
                        }
                    });
                });
                //四车道快速配置
                $("#FourLaneConfig").click(function () {
                    layer.open({
                        title: false,
                        content: '将在最外层区域，生成2进2出四车道配置，确定吗？',
                        btn: ['确定', '取消'], // 自定义按钮
                        yes: function (index, layero) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#FourLaneConfig").attr("disabled", true);

                            pager.passways = Passway_Table.getValues(".table_passway");
                            pager.devices = Device_Table.getValues(".table_device");
                            var param = { areas: pager.parkareas, passway: pager.passways, devices: pager.devices };

                            $.post("FourLaneConfig", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    $("#FourLaneConfig").removeAttr("disabled");
                                    layer.msg("已配置，请查看保存", { icon: 1, time: 1500 }, function () {
                                        pager.passways = json.data.passways;
                                        pager.parkareas = json.data.parkareas;
                                        pager.devices = json.data.devices;

                                        pager.bindAreas();
                                        Passway_Table.binddata(".table_passway", pager.passways);
                                        Device_Table.binddata(".table_device", pager.devices);

                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0 });
                                    $("#FourLaneConfig").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function (index, layero) {
                            // 取消按钮的回调
                            // 默关闭弹出层返回 true 可以阻止关闭
                            // return true;
                        }
                    });
                });
                //八车道快速配置
                $("#EightLaneConfig").click(function () {
                    layer.open({
                        title: false,
                        content: '将在最外层区域，生成4进4出八车道配置，确定吗？',
                        btn: ['确定', '取消'], // 自定义按钮
                        yes: function (index, layero) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#EightLaneConfig").attr("disabled", true);

                            pager.passways = Passway_Table.getValues(".table_passway");
                            pager.devices = Device_Table.getValues(".table_device");
                            var param = { areas: pager.parkareas, passway: pager.passways, devices: pager.devices };

                            $.post("EightLaneConfig", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    $("#EightLaneConfig").removeAttr("disabled");
                                    layer.msg("已配置，请查看保存", { icon: 1, time: 1500 }, function () {
                                        pager.passways = json.data.passways;
                                        pager.parkareas = json.data.parkareas;
                                        pager.devices = json.data.devices;

                                        pager.bindAreas();
                                        Passway_Table.binddata(".table_passway", pager.passways);
                                        Device_Table.binddata(".table_device", pager.devices);

                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0 });
                                    $("#EightLaneConfig").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function (index, layero) {
                            // 取消按钮的回调
                            // 默认关闭弹出层，返回 true 可以阻止关闭
                            // return true;
                        }
                    });
                });
                //自定义快速配置
                $("#ConstomConfig").click(function () {
                    layuiIndex = layer.open({
                        type: 2, id: 1,
                        title: false,
                        content: "ConstomConfig",
                        area: getIframeArea(["900px", "500px"]),
                        maxmin: false
                    });
                });
                //一键 保存
                $("#Save").click(function () {
                    layer.open({
                        title: false,
                        content: '确定保存配置吗？',
                        btn: ['确定', '取消'], // 自定义按钮
                        yes: function (index, layero) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Save").attr("disabled", true);
                            pager.passways = Passway_Table.getValues(".table_passway");
                            pager.devices = Device_Table.getValues(".table_device");

                            var isError = false;
                            if (pager.passways.length > 0) {
                                pager.passways.forEach(function (item) {
                                    if (item.Passway_Name.trim() === '') {
                                        isError = true; layer.msg("车道编号 [" + item.Passway_No + "] 的车道名称不能为空，请输入车道名称", { icon: 0, btn: ['确定'], time: 0 }); return;
                                    }
                                    if (item.PasswayLink_ParkAreaName.trim() === '') {
                                        isError = true; layer.msg("车道编号 [" + item.Passway_No + "] 的所属区域不能为空，请选择区域", { icon: 0, btn: ['确定'], time: 0 }); return;
                                    }
                                });
                                if (isError) { $("#Save").removeAttr("disabled"); return; }
                            }

                            if (pager.devices.length > 0) {
                                let isError = false;

                                pager.devices.forEach((item, index) => {
                                    const deviceIndex = index + 1; // 当前设备的索引，从1开始
                                    let errorMessage = '';

                                    if (item.Device_IP.trim() === '') {
                                        if (item.Device_Name.trim() !== '') {
                                            errorMessage = `设备名称 [${item.Device_Name}] 的设备IP不能为空，请输入设备IP`;
                                        } else {
                                            errorMessage = `第${deviceIndex}行设备IP不能为空，请输入设备IP`;
                                        }
                                    } else if (item.Device_Name.trim() === '') {
                                        errorMessage = `设备IP [${item.Device_IP}] 的设备名称不能为空，请输入设备名称`;
                                    } else if (item.Passway_Name.trim() === '') {
                                        errorMessage = `设备IP [${item.Device_IP}] 的所属车道不能为空，请选择车道`;
                                    } else if (item.Drive_Name.trim() === '') {
                                        errorMessage = `设备IP [${item.Device_IP}] 的设备型号不能为空，请选择设备型号`;
                                    }

                                    if (errorMessage) {
                                        isError = true;
                                        layer.msg(errorMessage, { icon: 0, btn: ['确定'], time: 0 });
                                        return false; // 退出循环
                                    }
                                });

                                if (isError) {
                                    $("#Save").removeAttr("disabled"); return;
                                }
                            }

                            var param = { areas: pager.parkareas, passway: pager.passways, devices: pager.devices };
                            $.post("SaveAll", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    $("#Save").removeAttr("disabled");
                                    layer.msg("保存配置成功，详情可至 <t class='saveText'>区域管理</t>、<t class='saveText'>车道管理</t>、<t class='saveText'>设备管理</t> 查看", { icon: 1, time: 0,btn:['确定'] }, function () {
                                        pager.bindData();
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0 });
                                    $("#Save").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function (index, layero) {
                            // 取消按钮的回调
                            // 默认关闭弹出层，返回 true 可以阻止关闭
                            // return true;
                        }
                    });
                });
                //一键 清除
                $("#Clear").click(function () {
                    layer.open({
                        title: false,
                        anim: 6,
                        content: '<div>确定清除当前界面配置吗？<br/><span style="color:red;font-weight:bolder;">若设备正在识别车辆通行，删除后会导致识别失败、缴费后不开闸等等异常错误！</span>确定一键清空？</div>',
                        btn: ['确定', '取消'], // 自定义按钮
                        yes: function (index, layero) {
                            // layer.msg("处理中", { icon: 16, time: 0 });
                            // $("#Clear").attr("disabled", true);
                            pager.passways = [];
                            pager.parkareas = [{ title: "区域信息", id: "0", spread: true, children: null }];
                            pager.devices = [];
                            pager.bindAreas();
                            Passway_Table.binddata(".table_passway", pager.passways);
                            Device_Table.binddata(".table_device", pager.devices);
                            layer.msg("已清除", { icon: 1, time: 1000 }, function () {
                            });
                            // $.post("ClearAll", {}, function (json) {
                            //     if (json.success) {
                            //         $("#Clear").removeAttr("disabled");
                            //         layer.msg("清空成功", { icon: 1, time: 1500 }, function () {
                            //             pager.bindData();
                            //         });
                            //     } else {
                            //         layer.msg(json.msg, { icon: 0 });
                            //         $("#Clear").removeAttr("disabled");
                            //     }
                            // }, "json");
                        },
                        btn2: function (index, layero) {
                            // 取消按钮的回调
                            // 默认关闭弹出层，返回 true 可以阻止关闭
                            // return true;
                        }
                    });
                });

                $(".help-btn").click(function () {
                    layer.open({
                        type: 1,
                        title: '外场内场车道设置举例',
                        content: $('#passwayset'),
                        area: getIframeArea(['1050px', '300px']),
                    });
                });
            },
            constomConfig: function (data) {
                pager.passways = Passway_Table.getValues(".table_passway");
                pager.devices = Device_Table.getValues(".table_device");
                var param = { areas: pager.parkareas, passway: pager.passways, devices: pager.devices, constom: data };
                layer.msg("处理中", { icon: 16, time: 0 });
                $.post("GetConstomConfig", { jsonModel: JSON.stringify(param) }, function (json) {
                    if (json.success) {
                        layer.msg("已配置，请查看保存", { icon: 1, time: 1500 }, function () {
                            pager.passways = json.data.passways;
                            pager.parkareas = json.data.parkareas;
                            pager.devices = json.data.devices;

                            pager.bindAreas();
                            Passway_Table.binddata(".table_passway", pager.passways);
                            Device_Table.binddata(".table_device", pager.devices);

                        });
                    } else {
                        layer.msg(json.msg, { icon: 0 });
                    }
                }, "json");
            },

            closeParkWin: function () {
                layer.close(parkIndex);
            },
            queryDeviceStatus: async function (device) {
                // 查询设备状态的方法
                try {
                    const response = await $.post("QueryDeviceStatus", { deviceNo: device.Device_No });
                    if (response.success) {
                        if (response.data == 2) {
                            var index = pager.deviceSend.indexOf(device);
                            if (index < 0) {
                                pager.deviceSend.push(device);
                            }
                            $(".device-status[data-value=" + device.Device_No + "]").text('消息发送失败').removeClass("success").addClass('fail');
                        } else {
                            var index = pager.deviceSend.indexOf(device);
                            if (index > -1) {
                                pager.deviceSend.splice(index, 1); // 删除指定的元素
                            }
                            $(".device-status[data-value=" + device.Device_No + "]").text('消息发送成功').removeClass("fail").addClass('success');
                        }
                        return true;  // 查询成功，返回 true
                    } else {

                        return false; // 查询失败，返回 false
                    }
                } catch (error) {
                    return false; // 请求出错，返回 false
                }
            },
            checkAllStatus: async function (devices) {
                let success = false;
                var newArray = devices.slice(); // 创建一个 devices 数组的副本

                while (newArray.length > 0 && !success) {
                    for (let i = 0; i < newArray.length; i++) {
                        let device = newArray[i];
                        let ret = await pager.queryDeviceStatus(device);

                        if (ret) {
                            newArray.splice(i, 1); // 删除当前设备
                            i--; // 调整索引，因为数组长度变短
                        }

                        await new Promise(resolve => setTimeout(resolve, 500));
                    }

                    // 如果所有设备都查询成功，可以将 success 设为 true 来退出 while 循环
                    if (newArray.length === 0) {
                        success = true;
                    }
                }
            }
        };


        var createNumber = function (len, data) {
            var d = '';
            for (var i = 0; i < len; i++) {
                d = d + Math.floor(Math.random() * 10);
            }

            var s = (new Date().getTime()) + d;
            if (data != null && data.length > 0) {
                var str = JSON.stringify(data);
                if (str.indexOf(s) > -1) {
                    return createNumber(len, data);
                }
            }
            return s;
        }

        function startDriver() {

            var driver = new Driver({
                animate: true,
                opacity: 0.75,
                allowClose: false,
                allowClick: false,
                closeBtnText: '关闭',
                doneBtnText: '完成',
                nextBtnText: '下一步',
                prevBtnText: '上一步',
                overlayClickNext: false,
                onReset: function () {
                    driver.start();
                },
                onDone: function () {
                    driver.reset();
                }
            });
            driver.defineSteps([
                {
                    element: '#step1',
                    popover: {
                        title: '第一步：添加车场区域',
                        description: '鼠标悬停列表上，默认显示编辑、删除、添加。',
                        position: 'right'
                    }
                },
                {
                    element: '#step2',
                    popover: {
                        title: '第二步：添加车道',
                        description: '编辑车道信息，选择关联所属区域。',
                        position: 'bottom'
                    }
                },
                {
                    element: '#step3',
                    popover: {
                        title: '第三步：添加设备',
                        description: '编辑设备信息，点击【设备IP】搜索局域网设备，选择关联车道。',
                        position: 'top'
                    }
                },
                {
                    element: '#Save',
                    popover: {
                        title: '保存',
                        description: '编辑完成，必须点击保存配置！',
                        position: 'top'
                    }
                },
                // {
                //     element: '#start-tour',
                //     popover: {
                //         title: '【操作向导】',
                //         position: 'top'
                //     }
                // }
            ]);
            driver.start();
            localStorage.setItem('driver', '1');
        }

        $(document).ready(function () {
            function adjustTbodyMaxHeight() {
                var containerHeight = $('.rightItem').height();
                var maxHeight = containerHeight - parseFloat($('html').css('font-size')) * 4; // 减去2rem
                $('.table-container tbody').css('max-height', maxHeight + 'px');
            }

            adjustTbodyMaxHeight();

            $(window).resize(function () {
                adjustTbodyMaxHeight();
            });

            if (localStorage.getItem('driver') != 1) {
                startDriver();
            }
        });

    </script>
    @*车道信息*@
    <script>

        //车道信息
        var Passway_Table = {
            binddata: function (table, data) {
                if (!data) {
                    data = [];
                }

                // if (data.length == 0) {
                //     var no = createPasswayNumber(2, pager.passways);
                //     data = [{ Passway_No: no, Passway_Name: '', PasswayLink_ParkAreaNo: '', PasswayLink_SubParkAreaNo: '', PasswayLink_GateType: '1', PasswayLink_GateTypeName: '入口' }];
                // }

                $(table).find("tbody").html($("#tmpl_passway").tmpl(data))
                $(table).find("tbody tr").each(function () {
                    $(this).find('td').eq(0).find('input').prop("readonly", true);
                });
                // $(table).find("tbody").find('tr:first').find('td').eq(5).find('button').removeClass('btndel').addClass('btnadd').text('添加');
                // var $rows = $(table).find('tbody').find('tr').not(':first');
                // $rows.each(function () {
                //     $(this).find('td').eq(5).find('button.btnadd').remove();
                // });
                Passway_Table.bindEvent(table);
            },
            additem: function (table) {
                var no = createPasswayNumber(2, pager.passways);
                var item = [{ Passway_No: no, Passway_Name: '', PasswayLink_ParkAreaNo: '', PasswayLink_SubParkAreaNo: '', PasswayLink_GateType: '1', PasswayLink_GateTypeName: '入口' }];

                $(table).find("tbody").append($("#tmpl_passway").tmpl(item));
                layer.msg('已添加车道，请填写车道信息', { icon: 1 });

                // if ($(table).find("tbody").find('tr').length > 1) {
                //     $(table).find("tbody").find('tr').each(function (index, item) {
                //         // $(item).find('td').eq(5).find('button').removeClass('btnadd').addClass('btndel').text('删除');
                //         // if (index > 0) $(item).find('td').eq(5).find('button.btnadd').remove()
                //     })
                //     // $(table).find("tbody").find('tr:first').find('td').eq(5).find('button').removeClass('btndel').addClass('btnadd').text('添加');
                // }
                // var $rows = $(table).find('tbody').find('tr').not(':first');
                // $rows.each(function () {
                //     $(this).find('td').eq(5).find('button.btnadd').remove();
                // });
                $(table).find("tbody tr").each(function () {
                    $(this).find('td').eq(0).find('input').prop("readonly", true);
                });

                Passway_Table.bindEvent(table);
            },
            delitem: function (table, obj) {
                Device_Table.delDeviceByPasswayNo($(obj).closest(".firsttime-tr").find('td').eq(0).find('input').val())
                $(obj).closest(".firsttime-tr").remove();
                // if ($(table).find("tbody").find('tr').length == 1) {
                //     // $(table).find("tbody").find('tr').find('td').eq(5).find('button').removeClass('btndel').addClass('btnadd').text('添加');
                // }
                // if ($(table).find("tbody").find('tr:first').find('td').eq(5).find('button.btnadd').length == 0) {
                //     var passno = $(table).find("tbody").find('tr:first').find('td').eq(0).find('input').val();
                //     // $(table).find("tbody").find('tr:first').find('td').eq(5).prepend('<button class="layui-btn btnadd" data-key="Passway_No" data-value="' + passno + '"><t> 添加</t></button>');
                // }
                // var $rows = $(table).find('tbody').find('tr').not(':first');
                // $rows.each(function () {
                //     $(this).find('td').eq(5).find('button.btnadd').remove();
                // });
                pager.passways = Passway_Table.getValues(".table_passway");
                Passway_Table.bindEvent(table);
            },
            bindEvent: function (table) {
                $(table).find(".btnadd").unbind("click").click(function () { Passway_Table.additem(table); });
                $(table).find(".btndel").unbind("click").click(function () { Passway_Table.delitem(table, this); });
                pager.passways = Passway_Table.getValues(".table_passway");
                Passway_Table.bindInput(table);
            },
            getValues: function (table) {
                var data = [];
                $(table).find("tbody").find('tr').each(function (index, item) {

                    var Passway_No = $(item).find('td').eq(0).find('input').val();
                    var Passway_Name = $(item).find('td').eq(1).find('input').val();
                    var PasswayLink_ParkAreaNo = $(item).find('td').eq(2).find('input').attr("data-value")
                    var PasswayLink_ParkAreaName = $(item).find('td').eq(2).find('input').val()
                    var PasswayLink_GateType = $(item).find('td').eq(3).find('input').attr("data-value")
                    var PasswayLink_GateTypeName = $(item).find('td').eq(3).find('input').val()
                    var PasswayLink_SubParkAreaNo = $(item).find('td').eq(4).find('input').attr("data-value")
                    var PasswayLink_SubParkAreaName = $(item).find('td').eq(4).find('input').val()
                    var n = {
                        Passway_No: Passway_No,
                        Passway_Name: Passway_Name,
                        PasswayLink_ParkAreaNo: PasswayLink_ParkAreaNo,
                        PasswayLink_ParkAreaName: PasswayLink_ParkAreaName,
                        PasswayLink_GateType: PasswayLink_GateType,
                        PasswayLink_GateTypeName: PasswayLink_GateTypeName,
                        PasswayLink_SubParkAreaNo: PasswayLink_SubParkAreaNo,
                        PasswayLink_SubParkAreaName: PasswayLink_SubParkAreaName,
                    };
                    data.push(n);
                });
                // console.log(pager.passways)
                return data;

            },
            bindInput: function (table) {
                $(table).off('input');
                $(table).on('input', function () {
                    pager.passways = Passway_Table.getValues(".table_passway");
                    // console.log(pager.passways)
                });
            },
            delPasswayByArea: function (areano) {
                for (let i = pager.passways.length - 1; i >= 0; i--) {
                    var del = false;
                    if (pager.passways[i].PasswayLink_ParkAreaNo == areano && pager.passways[i].PasswayLink_GateType == "1") {
                        del = true;
                    }
                    else if (pager.passways[i].PasswayLink_SubParkAreaNo == areano && pager.passways[i].PasswayLink_GateType == "0") {
                        del = true;
                    } else if (pager.passways[i].PasswayLink_ParkAreaNo == areano && pager.passways[i].PasswayLink_GateType == "0" && pager.passways[i].PasswayLink_SubParkAreaNo == "") {
                        del = true;
                    }

                    if (del) {
                        $('.table_passway').find("tbody").find('tr').each(function (index, item) {
                            if ($(item).find('td').eq(2).find('input').attr("data-value") == areano) {
                                $(item).remove();
                            }
                        })
                        Device_Table.delDeviceByPasswayNo(pager.passways[i].Passway_No)
                        pager.passways.splice(i, 1);
                    }
                }
            }
        };


        function createPasswayNumber(len, data) {
            var beginNo = '';
            if (data != null && data.length > 0) {
                for (let item of data) {
                    if (!isNaN(item.Passway_No) && (item.Passway_No.length == 1 || item.Passway_No.length == 2)) {
                        beginNo = item.Passway_No;
                        break;
                    }
                }
            }

            if (beginNo == '') {
                var d = '1';
                // for (var i = 0; i < len; i++) {
                //     d = d + Math.floor(Math.random() * 10);
                // }

                if (data != null && data.length > 0) {

                    var isexit = false;
                    for (let item of data) {
                        if (item.Passway_No == d) {
                            isexit = true;
                            break;
                        }
                    }

                    if (isexit) {
                        d = createPasswayNumber(len, data);
                    }
                }

                return d;
            } else {
                beginNo = parseInt(beginNo) + 1;
                while (isExitPasswayNo(beginNo, data)) {
                    beginNo = parseInt(beginNo) + 1;
                }
            }

            return beginNo;
        }

        function isExitPasswayNo(beginNo, data) {
            for (let item of data) {
                if (!isNaN(item.Passway_No) && item.Passway_No == beginNo) {
                    return true;
                    break;
                }
            }
            return false;
        }

    </script>
    @*设备信息*@
    <script>

        //设备信息
        var Device_Table = {
            binddata: function (table, data) {
                if (!data) {
                    data = [];
                }

                // if (data.length == 0) data = [{ Device_No: '', Device_Name: '', Device_PasswayNo: '', Device_DriveNo: '', Device_IP: '', Device_IO: '1' }];

                $(table).find("tbody").html($("#tmpl_device").tmpl(data))
                // $(table).find("tbody").find('tr:gt(0)').find('td').eq(5).find('button.btnadd').remove();
                // var $rows = $(table).find('tbody').find('tr').not(':first');
                // $rows.each(function () {
                //     $(this).find('td').eq(5).find('button.btnadd').remove();
                // });
                Device_Table.bindEvent(table);
            },
            additem: function (table) {

                var item = [{ Device_No: '', Device_Name: '', Device_PasswayNo: '', Device_DriveNo: '', Device_IP: '', Device_IO: '1' }];

                $(table).find("tbody").append($("#tmpl_device").tmpl(item));
                layer.msg('已添加设备，请填写设备信息', { icon: 1 });

                // if ($(table).find("tbody").find('tr').length > 1) {
                //     $(table).find("tbody").find('tr').each(function (index, item) {
                //         // $(item).find('td').eq(5).find('button').removeClass('btnadd').addClass('btndel').text('删除');
                //         // if (index > 0) $(item).find('td').eq(5).find('button.btnadd').remove()
                //     })
                //     // $(table).find("tbody").find('tr:first').find('td').eq(5).find('button').removeClass('btndel').addClass('btnadd').text('添加');
                // }
                Device_Table.bindEvent(table);

                var $rows = $(table).find('tbody').find('tr').not(':first');
                $rows.each(function () {
                    $(this).find('td').eq(5).find('button.btnadd').remove();
                });
            },
            delitem: function (table, obj) {
                $(obj).closest(".firsttime-tr").remove();
                // if ($(table).find("tbody").find('tr').length == 1) {
                //     // $(table).find("tbody").find('tr').find('td').eq(5).find('button').removeClass('btndel').addClass('btnadd').text('添加');
                // }

                // if ($(table).find("tbody").find('tr:first').find('td').eq(5).find('button.btnadd').length == 0) {
                //     var passno = $(table).find("tbody").find('tr:first').find('td').eq(0).find('input').attr("data-value")
                //     // $(table).find("tbody").find('tr:first').find('td').eq(5).prepend('<button class="layui-btn btnadd" data-key="Passway_No" data-value="' + passno + '"><t> 添加</t></button>');
                // }
                // var $rows = $(table).find('tbody').find('tr').not(':first');
                // $rows.each(function () {
                //     $(this).find('td').eq(5).find('button.btnadd').remove();
                // });
                Device_Table.bindEvent(table);
            },
            bindEvent: function (table) {
                $(table).find(".btnadd").unbind("click").click(function () { Device_Table.additem(table); });
                $(table).find(".btndel").unbind("click").click(function () { Device_Table.delitem(table, this); });

                // $(table).find(".btnSelectDevice").unbind("click").click(function () {
                //     //当前编辑的行
                //     pager.editDeviceRow = $(this).closest(".firsttime-tr");
                //     layer.open({
                //         type: 2,
                //         title: "<i class='fa fa-edit'></i> 选择设备",
                //         shadeClose: true,
                //         shade: 0.8,
                //         closeBtn: 1, // 显示关闭按钮
                //         area: ['100%', '95%'],
                //         content: '/Device/SearchDevice?Category=1',
                //     });
                // });
            },
            getValues: function (table) {
                var data = [];
                $(table).find("tbody").find('tr').each(function (index, item) {

                    var Device_No = $(item).find('td').eq(1).find('input').attr("data-value")
                    var Device_Name = $(item).find('td').eq(1).find('input').val();
                    var Device_PasswayNo = $(item).find('td').eq(2).find('input').attr("data-value")
                    var Passway_Name = $(item).find('td').eq(2).find('input').val();
                    var Device_DriveNo = $(item).find('td').eq(3).find('input').attr("data-value")
                    var Drive_Name = $(item).find('td').eq(3).find('input').val();
                    var Device_IP = $(item).find('td').eq(0).find('input').val();
                    var Device_IO = $(item).find('td').eq(4).find('input').attr("data-value")

                    var n = {
                        Device_No: Device_No,
                        Device_Name: Device_Name,
                        Passway_Name: Passway_Name,
                        Drive_Name: Drive_Name,
                        Device_PasswayNo: Device_PasswayNo,
                        Device_DriveNo: Device_DriveNo,
                        Device_IP: Device_IP,
                        Device_IO: Device_IO,
                    };
                    data.push(n);
                });
                return data;

            },
            delDeviceByPasswayNo: function (passwayno) {
                $('.table_device').find("tbody").find('tr').each(function (index, item) {
                    if ($(item).find('td').eq(2).find('input').attr("data-value") == passwayno) {
                        $(item).remove();
                    }
                })

                for (let i = pager.devices.length - 1; i >= 0; i--) {
                    if (pager.devices[i].Device_PasswayNo == passwayno) {
                        pager.devices.splice(i, 1);
                    }
                }
            }
        };

        //给子窗口调用的选择设备后的处理函数
        function handleDeviceSelection(deviceData) {
            debugger
            var editRow = pager.editDeviceRow;
            if (editRow && deviceData) {
                debugger
                var drive = pager.drives?.find(x => x.title == deviceData.DeviceRecord_Type);
                if (drive != null) {
                    $(editRow).find("input[data-key='Device_DriveNo']").val(drive.title);
                    $(editRow).find("input[data-key='Device_DriveNo']").attr('data-value', drive.id);
                }

                var deviceName = $(editRow).find("input[data-key='Device_Name']").val();
                if (deviceData.DeviceRecord_Remark != null && deviceData.DeviceRecord_Remark != "") {
                    $(editRow).find("input[data-key='Device_Name']").val(deviceData.DeviceRecord_Remark);
                }

                $(editRow).find("input[data-key='Device_IP']").val(deviceData.DeviceRecord_IP);
            }
            $("#test2").remove();
        }
    </script>

    @*下拉列表*@
    <script>

        //移除弹出的DIV
        var myClickHandler = function (k) {
            event.preventDefault();
            event.stopPropagation();
            setTimeout(function () {
                if (!k.target.classList.contains('layui-icon') && !k.target.classList.contains('layui-tree-icon')) { 
                    $(".tdselect-div").remove(); 
                    $(".clear-icon").remove();
                }

                // $(k.target).find('.clear-icon').remove();
            }, 0);
            return false;
        }
        //绑定window点击事件
        $(window).off('click', myClickHandler).on('click', myClickHandler);

        function SetSelectDiv(e, datatype) {
            event.preventDefault();
            event.stopPropagation();

            if ($("#test1").length > 0) $("#test1").remove();

            //获取当前行的索引
            var dom = $(e).get(0);
            var position = getPosition(dom);
            var top = position.top;
            var left = position.left;
            var width = dom.clientWidth;

            // 获取最近的有滚动条的父级容器
            var parentScrollTop = 0;
            var $scrollableParent = $(dom).closest(".tbody");
            if ($scrollableParent.length > 0) {
                parentScrollTop = $scrollableParent.scrollTop();
                console.log("parentScrollTop、" + parentScrollTop)
            }

            //检查是否超出屏幕底部
            var divHeight = 194; // 高
            var screenBottom = top - parentScrollTop;
            var screentHeight = screenBottom + divHeight;
            if (screentHeight > window.innerHeight) {
                top = screenBottom - divHeight - 10;
                console.log("1、" + top)
            } else {
                if (parentScrollTop > 0) {
                    top = top - parentScrollTop + 38;
                    console.log("2、" + top)
                } else {
                    top = top + 38;
                    console.log("3、" + top)
                }
            }

            //设置样式
            var css = '.tdselect-div {color: #3d3d3d;font-size:13px !important; position: absolute;z-index: 900003; top:' + top + 'px; left:' + left + 'px; bottom:auto;width:14rem; height:' + divHeight + 'px;overflow: auto;background-color: #fff;cursor: pointer; border: 1px solid #dcdee3;}';
            //添加样式
            var style = document.createElement("style");
            style.id = "tdselect-div-float";
            style.innerText = css;
            document.head.append(style);

            //创建DIV
            var div = document.createElement("div");
            div.className = "tdselect-div";
            div.id = "test1";             
            document.body.appendChild(div);

            var udata = [];
            if (datatype == 1) {
                var newdata = pager.parkareas[0].children;
                udata = newdata;
            } else if (datatype == 2) {
                udata = pager.inoutdata;
            } else if (datatype == 3) {
                udata = pager.drives;
            } else if (datatype == 4) {
                var convertedPassways = $.map(pager.passways, function (item) {
                    if (item.Passway_No && item.Passway_No != "") {
                        return {
                            title: item.Passway_Name,
                            id: item.Passway_No,
                            spread: true,
                            children: null
                        };
                    }
                });
                udata = convertedPassways;
            } else if (datatype == 5) {
                udata = pager.deviceiodata;
            }

            if (udata == undefined || udata == null) udata = [];

            var inst1 = tree.render({
                elem: '#test1'  //绑定元素
                , data: udata
                , click: function (obj) {
                    $(e).val(obj.data.title);
                    $(e).attr("data-value", obj.data.id);
                    $(".tdselect-div").remove();
                    $(".clear-icon").remove();
                    pager.passways = Passway_Table.getValues(".table_passway");
                    pager.devices = Device_Table.getValues(".table_device");
                },
            });

            // 在 tree.render 完成后重新插入图标
            var clearIcon = document.createElement("i");
            clearIcon.className = "layui-icon layui-icon-delete clear-icon";
            clearIcon.title = "清空";  // 提示信息
            clearIcon.innerText = "清空";  // 提示信息
            clearIcon.onclick = function () {
                $(e).val('')
                $(e).attr("data-value", '');
                $(".tdselect-div").remove();
                $(".clear-icon").remove();
                pager.passways = Passway_Table.getValues(".table_passway");
                pager.devices = Device_Table.getValues(".table_device");
                $(this).remove();
            };

            //把clearIcon添加到e的父元素，先移除再添加
            $('.clear-icon').remove();
            $(e).parent().append(clearIcon);

            //为每个树节点添加悬浮效果和点击事件
            $('#test1 .layui-tree-entry').each(function () {
                var $entry = $(this);
                $entry.off('click').on('click', function (event) {
                    $(this).find('.layui-tree-txt').click();
                });
            });

            //设置滚动条
            var selectedDiv = $("div.divSelect.selected");
            if (selectedDiv && selectedDiv.length > 0) {
                var divSelectPop = selectedDiv.closest(".divSelectPop");
                divSelectPop.scrollTop(selectedDiv.position().top - 20);
                selectedDiv.focus();
            }
            e.click();
        }

        function ShowDeviceIPDiv(e) {
            event.preventDefault();
            event.stopPropagation();

            if ($("#test2").length > 0) $("#test2").remove();

            pager.editDeviceRow = $(e).closest(".firsttime-tr");

            //获取当前行的索引
            var dom = $(e).get(0);
            var position = getPosition(dom);
            var top = position.top;

            var elementWidth = dom.offsetWidth;
            var left = position.left;
            var width = dom.clientWidth;

            // 获取最近的有滚动条的父级容器
            var parentScrollTop = 0;
            var $scrollableParent = $(dom).closest(".tbody");
            if ($scrollableParent.length > 0) {
                parentScrollTop = $scrollableParent.scrollTop();
            }

            //检查是否超出屏幕底部
            var divHeight = 300; // 高
            var divWidth = 500; // 宽
            var screenBottom = top - parentScrollTop;
            var screentHeight = screenBottom + divHeight + 38;
            if (screentHeight > window.innerHeight) {
                top = screenBottom - divHeight - 5;
            } else {
                if (parentScrollTop > 0) {
                    top = top - parentScrollTop + 38;
                } else {
                    top = top + 38;
                }
            }

            //设置样式
            var css = '.deviceip-div {color: #3d3d3d;font-size:13px !important; position: absolute;z-index: 2147483647; top:' + top + 'px; left:' + left + 'px; bottom:auto;width:' + divWidth + 'px; height:' + divHeight + 'px;overflow: auto;background-color: #fff;cursor: pointer; border: 1px solid #dcdee3;box-shadow: 0 0 12px #bbb;border-radius:10px;}';
            //添加样式
            var style = document.createElement("style");
            style.id = "deviceip-div-float";
            style.innerText = css;
            document.head.append(style);

            //创建DIV
            var div = document.createElement("div");
            div.className = "deviceip-div";
            div.id = "test2";

            //添加到body
            document.body.appendChild(div);
            $("#test2").html('<iframe style="border: 0;" id="myDeviceIframe" src="/FastGuide/SearchDevice?Category=1" width="' + divWidth + '" height="' + (divHeight - 52) + '"></iframe>');
            $("#test2").append('<div class="resend-btn"><span class="devicenum"></span><button id="searchNetworkDevices">搜索</button><button id="Cancel3">关闭</button></div>');
            var iframe = document.getElementById('myDeviceIframe');

            // 等待 iframe 加载完成
            iframe.onload = function () {

                $('#searchNetworkDevices').on('click', function () {
                    if (iframe.contentWindow && typeof iframe.contentWindow.SearchDevice === 'function') {
                        iframe.contentWindow.SearchDevice();
                    } else {
                        console.error('SearchDevice function is not available in the iframe.');
                    }
                });

                $("#Cancel3").click(function () { $("#test2").remove(); });
            };
            //设置滚动条
            var selectedDiv = $("div.divSelect.selected");
            if (selectedDiv && selectedDiv.length > 0) {
                var divSelectPop = selectedDiv.closest(".divSelectPop");
                divSelectPop.scrollTop(selectedDiv.position().top - 20);
                selectedDiv.focus();
            }
            e.click();
        }

        //查找元素的绝对位置
        function getPosition(element) {
            var actualLeft = element.offsetLeft,
                actualTop = element.offsetTop,
                current = element.offsetParent; // 取得元素的offsetParent
            // 一直循环直到根元素
            while (current !== null) {
                actualLeft += current.offsetLeft;
                actualTop += current.offsetTop;
                current = current.offsetParent;
            }
            // 返回包含left、top坐标的对象
            return {
                left: actualLeft,
                top: actualTop
            };
        }

        function SetDeviceCount(num) {
            $(".devicenum").text('当前设备数: ' + num)
        }

    </script>

</body>

</html>
