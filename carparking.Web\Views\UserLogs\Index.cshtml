﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>操作日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa {
            margin: 6px 4px;
            float: left;
        }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>操作日志</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input" name="UserLogs_ModuleName" id="UserLogs_ModuleName" autocomplete="off" placeholder="模块" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="startDate" id="startDate" autocomplete="off" placeholder="开始日期" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="endDate" id="endDate" autocomplete="off" placeholder="截止日期" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                            <div class="layui-row search-more layui-hide">
                                <div class="layui-inline">
                                    <select data-placeholder="数据类型" class="form-control chosen-select " id="UserLogs_Type" name="UserLogs_Type" lay-search>
                                        <option value="" selected>数据类型</option>
                                        <option value="0">后台数据</option>
                                        <option value="1">岗亭数据</option>
                                        <option value="2">中间件数据</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="UserLogs_Title" id="UserLogs_Title" autocomplete="off" placeholder="操作" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="UserLogs_AddAccount" id="UserLogs_AddAccount" autocomplete="off" placeholder="操作员账号" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="UserLogs_Body" id="UserLogs_Body" autocomplete="off" placeholder="日志内容" value="" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        topBar.init();

        layui.use(['table', 'form', 'laydate'], function () {
            pager.init();

            var table = layui.table;

            $("#startDate").val(new Date().Format("yyyy-MM-dd"));
            $("#endDate").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["startDate", "endDate"], { type: 'date', range: true });

            var cols = [[
                { type: 'radio' }
                , {
                    field: 'UserLogs_Type', title: '数据类型', width: 150, hide: true, templet: function (d) {
                        return d.UserLogs_Type == 1 ? "岗亭数据" : d.UserLogs_Type == 2 ? "中间件数据" : "后台数据"
                    }
                }
                , { field: 'UserLogs_ModuleName', title: '模块', width: 150 }
                , { field: 'UserLogs_Title', title: '操作', width: 100 }
                , { field: 'UserLogs_Body', title: '日志内容' }
                , { field: 'UserLogs_AddAccount', title: '操作员账号', width: 140 }
                , { field: 'UserLogs_AddName', title: '操作员', width: 140 }
                , { field: 'UserLogs_AddTime', title: '日志时间', width: 160 }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/UserLogs/GetLogsList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data);
                }
            });

            tb_row_radio(table);
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layui.form.render("select");
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/UserLogs/GetLogsList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }

        $(function () { pager.bindSelect(); });
    </script>
</body>
</html>
