﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>停车详情</title>
	<meta name="keywords" content="">
	<meta name="description" content="">
	<link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
	<link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
	<link href="~/Static/css/animate.min.css" rel="stylesheet">
	<link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
	<link href="~/Static/css/myApp.css" rel="stylesheet" />
	<link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
	<link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
	<link href="~/Static/css/fishBone.css?v1.0" rel="stylesheet" />
	<link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
	<style>
		h3, span { color: black; font-weight: normal !important; }
		.ibox-content { padding-top: 0px !important; }
		body { margin-top: 0; }
		.fishBone { border: 1px solid #f5f5f5; }
		.form-group { margin-bottom: 10px; }
		.gray-bg { background-color: #fdf8f8; margin-top: 5px; }
		h3 { padding-top: 5px; }
	</style>
</head>
<body>
	<div class="ibox-content">
		<div class="form-horizontal">
			<div class="form-group">
				<div class="col-sm-12 gray-bg">
					<h3>停车概要信息</h3>
				</div>
			</div>
			<div class="form-group center-block">
				<div class="row col-sm-12">
					<div class="col-sm-3">
						<label class="control-label">车牌号码：<span id="ParkOrder_CarNo"></span></label>
					</div>
					<div class="col-sm-3">
						<label class="control-label">停车订单：<span id="ParkOrder_OrderNo"></span></label>
					</div>
					<div class="col-sm-3">
						<label class="control-label">订单状态：<span id="ParkOrderStatus_Name"></span></label>
					</div>
				</div>
				<div class="row col-sm-12">
					<div class="col-sm-3">
						<label class="control-label">计费类型：<span id="ParkOrder_CarCardTypeName"></span></label>
					</div>
					<div class="col-sm-3">
						<label class="control-label">车牌颜色：<span id="ParkOrder_CarType"></span></label>
					</div>
					<div class="col-sm-3">
						<label class="control-label">免费原因：<span id="ParkOrder_FreeReason"></span></label>
					</div>
				</div>
			</div>


			<div class="form-group">
				<div class="col-sm-12 gray-bg">
					<h3>优惠券列表</h3>
				</div>
			</div>
			<div class="form-group-sm center-block">
				<table class="table table-striped  table-hover">
					<thead>
						<tr>
							<th>优惠券编号</th>
							<th>优惠方式</th>
							<th>优惠额度</th>
							<th>抵扣金额</th>
							<th>消费时间</th>
						</tr>
					</thead>
					<tbody id="data-view-coupon"></tbody>
					<script id="data-tmpl-coupon" type="text/x-jquery-tmpl">
						<tr>
							<td width=180>${CouponRecord_No}</td>
							<td>
								{{if CouponRecord_CouponCode == "101"}}
								优惠金额
								{{else CouponRecord_CouponCode == "102"}}
								优惠时长
								{{else CouponRecord_CouponCode == "103"}}
								优惠比例
								{{else CouponRecord_CouponCode == "104"}}
								优惠到指定时间
								{{/if}}
							</td>
							<td>
								{{if CouponRecord_CouponCode == "101"}}
								减免${CouponRecord_Value}元
								{{else CouponRecord_CouponCode == "102"}}
								减免${CouponRecord_Value}分钟
								{{else CouponRecord_CouponCode == "103"}}
								打${CouponRecord_Value}折
								{{else CouponRecord_CouponCode == "104"}}
								免费到${CouponRecord_EndTime}
								{{else CouponRecord_CouponCode == "105"}}
								${CouponRecord_EndTime}
								{{/if}}
							</td>
							<td>
								${CouponRecord_Paid}
							</td>
							<td>${CouponRecord_UsedTime}</td>
						</tr>
					</script>
					<tr>
						<td style="color: #ff0000; font-weight: bold;">合计:</td>
						<td></td>
						<td></td>
						<td style="color: #ff0000; font-weight: bold;" class="SumedMoney-Coupon">0</td>
						<td></td>
					</tr>
				</table>
			</div>

			<div class="form-group ">
				<div class="col-sm-12 gray-bg">
					<h3 class="dbclick">支付订单详情列表</h3>
				</div>
			</div>
			<div class="form-group-sm center-block">
				<table class="table table-striped table-hover">
					<thead>
						<tr>
							<th>支付订单号</th>
							<th>应收金额</th>
							<th>订单创建时间</th>
							<th>订单状态</th>
							<th>支付方式</th>
							<th>实收金额</th>
							<th>抵扣金额</th>
							<th>支付时间</th>
						</tr>
					</thead>
					<tbody id="data-view"></tbody>

					<script id="data-tmpl" type="text/x-jquery-tmpl">
						<tr>
							<td width=180>${PayOrder_No }</td>
							<td>${PayOrder_Money }</td>
							<td>${PayOrder_Time }</td>
							<td>
								{{if PayOrder_Status == 0}}
								未支付
								{{if PayOrder_PayTypeCode == 79003 || PayOrder_PayTypeCode == 79007}}
								<!-- <button type="button" data-payorder_no="${PayOrder_No }" data-paytype_code="${PayOrder_PayTypeCode }" class="btn btn-outline btn-info btn-xs" name="toPay" ><i class="fa fa-refresh"></i></button>-->
								{{/if}}
								{{/if}}
								{{if PayOrder_Status == 1}}
								<small class="text-navy">支付成功</small>
								{{/if}}
								{{if PayOrder_Status == 2}}
								支付失败
								{{/if}}
								{{if PayOrder_Status == 3}}
								取消支付
								{{/if}}
							</td>
							<td>
								{{if PayOrder_PayTypeCode==79001}}
								<span class="layui-badge layui-bg-red">线下现金</span>
								{{else PayOrder_PayTypeCode==79002}}
								<span class="layui-badge layui-bg-red">平台现金支付</span>
								{{else PayOrder_PayTypeCode==79003 }}
								<span class="layui-badge layui-bg-wxgreen">微信支付</span>
								{{else PayOrder_PayTypeCode==79010 }}
								<span class="layui-badge layui-bg-wxgreen">线下微信</span>
								{{else PayOrder_PayTypeCode==79013}}
								<span class="layui-badge layui-bg-wxgreen">微信无感支付</span>
								{{else PayOrder_PayTypeCode==79007 }}
								<span class="layui-badge layui-bg-alipayblue">支付宝支付</span>
								{{else PayOrder_PayTypeCode==79011 }}
								<span class="layui-badge layui-bg-alipayblue">线下支付宝</span>
								{{else PayOrder_PayTypeCode==79012 }}
								<span class="layui-badge layui-bg-alipayblue">支付宝无感支付</span>
								{{else PayOrder_PayTypeCode==79004 }}
								<span class="layui-badge layui-bg-cyan">Android端支付</span>
								{{else PayOrder_PayTypeCode==79006 }}
								<span class="layui-badge layui-bg-cyan">终端设备支付</span>
								{{else PayOrder_PayTypeCode==79009 }}
								<span class="layui-badge layui-bg-cyan">第三方支付</span>
								{{else PayOrder_PayTypeCode==79014 }}
								<span class="layui-badge layui-bg-ylblue">建行支付</span>
								{{else PayOrder_PayTypeCode==79015 }}
								<span class="layui-badge layui-bg-ylblue">招行一网通支付</span>
								{{else PayOrder_PayTypeCode==79016 }}
								<span class="layui-badge layui-bg-ylblue">银联无感支付</span>
								{{else PayOrder_PayTypeCode==79017 }}
								<span class="layui-badge layui-bg-ylblue">建行无感支付</span>
								{{else PayOrder_PayTypeCode==79018 }}
								<span class="layui-badge layui-bg-ylblue">威富通聚合支付</span>
								{{else PayOrder_PayTypeCode==79019 }}
								<span class="layui-badge layui-bg-ylblue">招行无感支付</span>
								{{else PayOrder_PayTypeCode==79020 }}
								<span class="layui-badge layui-bg-ylblue">工行无感支付</span>
								{{else PayOrder_PayTypeCode==79021 }}
								<span class="layui-badge layui-bg-ylblue">工行支付</span>
								{{else PayOrder_PayTypeCode==79022 }}
								<span class="layui-badge layui-bg-ylblue">农行无感支付</span>
								{{else PayOrder_PayTypeCode==79023 }}
								<span class="layui-badge layui-bg-ylblue">农行支付</span>
								{{else PayOrder_PayTypeCode==79024 }}
								<span class="layui-badge layui-bg-ylblue">ETC支付</span>
								{{else PayOrder_PayTypeCode==79025 }}
								<span class="layui-badge layui-bg-ylblue">中行支付</span>
								{{else PayOrder_PayTypeCode==79026 }}
								<span class="layui-badge layui-bg-ylblue">中行无感支付</span>
								{{else PayOrder_PayTypeCode==79027 }}
								<span class="layui-badge layui-bg-ylblue">乐聚合支付</span>
								{{else PayOrder_PayTypeCode==79039 }}
								<span class="layui-badge layui-bg-ylblue">随行付</span>
								{{else PayOrder_PayTypeCode==79028 }}
								<span class="layui-badge layui-bg-ylblue">银联商务</span>
								  {{else PayOrder_PayTypeCode==79029 }}
								<span class="layui-badge layui-bg-ylblue">充电抵扣</span>
								{{else}}
								{{/if}}
							</td>
							<td>${PayOrder_PayedMoney }</td>
							<td>${PayOrder_DiscountMoney}</td>
							<td>${PayOrder_PayedTime }</td>
						</tr>
					</script>

					<tr>
						<td style="color: #ff0000; font-weight: bold;">合计:</td>
						<td style="color: #ff0000; font-weight: bold;" class="SumMoney">0</td>
						<td></td>
						<td></td>
						<td></td>
						<td style="color: #ff0000; font-weight: bold;" class="SumedMoney">0</td>
						<td style="color: #ff0000; font-weight: bold;" class="SumedCouponMoney">0</td>
						<td></td>
					</tr>
				</table>
			</div>


			<div class="form-group ">
				<div class="col-sm-12 gray-bg">
					<h3 class="dbclick">停车明细轨迹图</h3>
				</div>
			</div>
			<div class="form-group-sm center-block">
				<div><div class="fishBone" /></div>
			</div>

			<div class="form-group calcdetail hide">
				<div class="col-sm-12 gray-bg">
					<h3>计费详情</h3>
				</div>
			</div>

			<div class="form-group-sm center-block calcdetail hide">
				<table class="table table-striped table-hover table-calcdetail">
					<thead>
						<tr>
							<th>区域</th>
							<th>开始时间</th>
							<th>结束时间</th>
							<th>计费时长</th>
							<th>计费金额</th>
							<th>优惠金额</th>
							<th>免费分钟</th>
							<th>计费描述</th>
							<th>超时缴费</th>
							<th>过期缴费</th>
							<th>上个周期结束时间</th>
							<th>上个周期累积金额</th>
							<th>上个周期累计免费分钟</th>
							@*  <th>当前周期结束时间</th>
							<th>当前周期累积金额</th>
							<th>当前周期累计免费分钟</th>*@
						</tr>
					</thead>
					<tbody id="data-view-calcdetail"></tbody>
				</table>
				<script id="data-tmpl-calcdetail" type="text/x-jquery-tmpl">
					<tr>
							   <td><span class="fa fa-chevron-right"></span> ${CalcDetail_AreaName}</td>
							   <td>${CalcDetail_StartTime}</td>
							   <td>${CalcDetail_EndTime}</td>
							   <td>${getParkMin(CalcDetail_StartTime,CalcDetail_EndTime)}</td>
							   <td>${CalcDetail_PayedAmount}</td>
							   <td>${CalcDetail_CouponAmount}</td>
							   <td>${CalcDetail_UseMin}</td>
							   <td>${CalcDetail_PayedMsg}</td>
								<td>
								{{if CalcDetail_IsOverTime==1}}
								 <span style="color: #ff0000;">是</span>
								{{else}}
								 否
								{{/if}}
								</td>
								  <td>
								{{if CalcDetail_IsCarExpire==1}}
								 <span class="layui-badge layui-bg-red">是</span>
								{{else}}
								 否
								{{/if}}
								</td>

							   <td>${CalcDetail_PreCycleTime}</td>
							   <td>${CalcDetail_PreCycleMoney}</td>
							   <td>${CalcDetail_PreCycleMin}</td>
					@*   <td>${nextcycletime}</td>
							   <td>${NextCyclePaidFees}</td>
							   <td>${nextcyclefreemin}</td>*@

					</tr>
					  <tr style="display: none;"> <td colspan="13">
					{{if CalcDetail_CalcContent!=null && CalcDetail_CalcContent!=""}}
							<div>
								 <p>${CalcDetail_CalcContent}</p>
							</div>
					{{/if}}
					</td></tr>
				</script>
			</div>


			<div class="form-group penaltylist hide">
				<div class="col-sm-12 gray-bg">
					<h3>充电滞留详情</h3>
				</div>
			</div>

			<div class="form-group-sm center-block penaltylist hide">
				<table class="table table-striped table-hover">
					<thead>
						<tr>
							<th>充电编号</th>
							<th>充电开始时间</th>
							<th>充电结束时间</th>
							<th>滞留时长(分钟)</th>
							<th>滞留金额(元)</th>
						</tr>
					</thead>
					<tbody id="data-view-penaltylist"></tbody>
				</table>
				<script id="data-tmpl-penaltylist" type="text/x-jquery-tmpl">
					<tr>
							   <td>${DetentionPenalty_No}</td>
							   <td>${DetentionPenalty_InTime}</td>
							   <td>${DetentionPenalty_OutTime}</td>
							   <td>${DetentionPenalty_StayTime}</td>
							   <td>${DetentionPenalty_Money}</td>
					</tr>
				</script>
			</div>
		</div>
	</div>
	<script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
	<script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
	<script src="~/Static/js/content.min.js?v=1.0.0" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
	<script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
	<script src="~/Static/js/fishBone.js?v1.0.0" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.SuperSlide.2.1.1.js" asp-append-version="true"></script>
	<script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
	<script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
	<script>
		var data3 = null;
		var paramNo = decodeURIComponent($.getUrlParam("ParkOrder_No"));
		var pager = {
			init: function () {
				this.bindData();
			},
			bindEvent: function () {
				$("#data-view button").bind("click", function () {
					layer.msg('加载中...', { icon: 16, time: 0 });
					var btni = $(this).find("i");
					$(btni).addClass("fa-spin");
					var PayOrder_No = $(this).attr("data-payorder_no");
					var PayOrder_PayTypeCode = $(this).attr("data-paytype_code");
					$.ajax({
						type: 'post',
						url: '/InParkRecord/QueryPayOrderStatus',
						dataType: 'json',
						data: { PayOrder_No: PayOrder_No, PayOrder_PayTypeCode: PayOrder_PayTypeCode },
						success: function (json) {
							$(btni).removeClass("fa-spin");
							if (json.Success) {
								layer.closeAll();
							} else {
								layer.msg("提示：" + json.Message, { icon: 5 });
							}
						},
						error: function () {
							layer.msg('系统错误', { icon: 2 });
							$(btni).removeClass("fa-spin");
						}
					});
				});

				$(".dbclick").dblclick(function () {
					$('#data-view').html("")
					$('#data-tmpl').tmpl(data3).appendTo('#data-view');
				});

			},

			//数据绑定
			bindData: function () {
				layer.msg('加载中...', { icon: 16, time: 0 });

				if (ParkOrder_OrderNo != null) {
					$.ajax({

						type: 'post',
						url: '/Monitoring/GetOrderDetailByNo',
						dataType: 'json',
						data: { ParkOrder_No: paramNo },
						success: function (json) {
							if (json.success) {
								//订单信息
								if (json.data != null && json.data.model != null) {
									$("#ParkOrder_OrderNo").text(json.data.model.ParkOrder_No);
									$("#ParkOrder_CarNo").text(json.data.model.ParkOrder_CarNo);
									$("#ParkOrder_CarCardTypeName").text(json.data.model.ParkOrder_CarCardTypeName);
									$("#ParkOrder_CarType").text(json.data.model.ParkOrder_CarTypeName);
									$("#ParkOrder_EnterTime").text(json.data.model.ParkOrder_EnterTime);
									$("#ParkOrder_OutTime").text(json.data.model.ParkOrder_OutTime);
									$("#ParkOrder_FreeReason").text(json.data.model.ParkOrder_FreeReason);
									if (json.data.model.ParkOrder_StatusNo) {
										if (json.data.model.ParkOrder_StatusNo == 199) $("#ParkOrderStatus_Name").text("预入场");
										else if (json.data.model.ParkOrder_StatusNo == 200 && json.data.model.ParkOrder_OutType == 1) $("#ParkOrderStatus_Name").text("预出场");
										else if (json.data.model.ParkOrder_StatusNo == 200) $("#ParkOrderStatus_Name").text("已入场");
										else if (json.data.model.ParkOrder_StatusNo == 201) $("#ParkOrderStatus_Name").text("已出场");
										else if (json.data.model.ParkOrder_StatusNo == 202) $("#ParkOrderStatus_Name").text("自动关闭");
										else if (json.data.model.ParkOrder_StatusNo == 203) $("#ParkOrderStatus_Name").text("场内关闭");
										else if (json.data.model.ParkOrder_StatusNo == 204) $("#ParkOrderStatus_Name").text("欠费出场");
									}

									//$("#ParkOrder_TotalAmount").html(json.data.model.ParkOrder_TotalAmount !== null ? json.data.model.ParkOrder_TotalAmount + " 元" : "");
									//$("#ParkOrderStatus_Name").html(json.data.model.ParkOrderStatus_Name);
									var WXUser_Nickname = json.data.model.WXUser_Nickname != null ? json.data.model.WXUser_Nickname : json.data.model.WXUser_Nickname2;
									$("#WXUser_Nickname").html(WXUser_Nickname);
								}
								//订单的停车场信息
								if (json.data != null && json.data.parking != null) {
									$("#Parking_Name").html(json.data.parking.Parking_Name);
									$("#Parking_Tel").html(json.data.parking.Parking_Tel);
									$("#Parking_Linkman").html(json.data.parking.Parking_Linkman);
									$("#Parking_Add").html(json.data.parking.Parking_Add);
								}
								//订单使用的优惠券列表
								$("#data-view-coupon").html("");
								if (json.data.coupon && json.data.coupon != null)
									$('#data-tmpl-coupon').tmpl(json.data.coupon).appendTo('#data-view-coupon');
								var sumedMoney_Coupon = 0;
								$(json.data.coupon).each(function () {
									sumedMoney_Coupon += parseFloat(this.CouponRecord_Paid == null ? 0 : this.CouponRecord_Paid);
								});
								$(".SumedMoney-Coupon").html(sumedMoney_Coupon.toFixed(2));

								//订单支付信息列表
								$("#data-view").html("");
								var sumMoney = 0, sumedMoney = 0, sumedCouponMoney = 0;

								var newData3 = [];//重组数组，只保留已支付记录

								$('#data-tmpl').tmpl(json.data.payOrders).appendTo('#data-view');
								data3 = json.data.payOrders; //记录原数据

								$(json.data.payOrders).each(function () {
									if (this.PayOrder_Status == 1) {
										newData3[newData3.length] = this;
										sumMoney += parseFloat(this.PayOrder_Money == null ? 0 : this.PayOrder_Money);
										sumedMoney += parseFloat(this.PayOrder_PayedMoney == null ? 0 : this.PayOrder_PayedMoney);
										sumedCouponMoney += parseFloat(this.PayOrder_DiscountMoney == null ? 0 : this.PayOrder_DiscountMoney);
									}
								});

								//$('#data-tmpl').tmpl(newData3).appendTo('#data-view');
								$('#data-view').html($('#data-tmpl').tmpl(newData3));

								$(".SumMoney").html(sumMoney.toFixed(2));
								$(".SumedMoney").html(sumedMoney.toFixed(2));
								$(".SumedCouponMoney").html(sumedCouponMoney.toFixed(2));

								var gjData = [];
								var sumMin = 0;
								//轨迹图
								if (json.data.detail) {
									$.each(json.data.detail, function (k, v) {

										var photo = v.OrderDetail_EnterImgPath ? (' <a href="' + PathCheck(decodeURIComponent(v.OrderDetail_EnterImgPath)) + "" + '" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs">预览</a>') : '<span class="layui-badge layui-bg-gray">无图片</span>';
										var enterTime = "无"; if (v.OrderDetail_EnterTime) enterTime = v.OrderDetail_EnterTime;
										console.log("Orderdetail_Remark:"+JSON.stringify(v));
										//入场
										var item = { '出入口': v.OrderDetail_EnterPasswayName ? "[进场]" + v.OrderDetail_EnterPasswayName : "", '入场操作员': v.OrderDetail_EnterAdminName, '区域名称': v.OrderDetail_ParkAreaName + ((v.OrderDetail_Remark != undefined && v.OrderDetail_Remark != "" && v.OrderDetail_Remark != null) ? "(" + v.OrderDetail_Remark + ")" : ""), '入场图片': photo, '入场时间': enterTime };
										gjData.push(item);

										//出场
										var outTime = ""; if (v.OrderDetail_OutTime) outTime = v.OrderDetail_OutTime;
										var outphoto = v.OrderDetail_OutImgPath ? (' <a href="' + PathCheck(decodeURIComponent(v.OrderDetail_OutImgPath)) + "" + '" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs">预览</a>') : '<span class="layui-badge layui-bg-gray">无图片</span>';
										var remark = v.OrderDetail_Remark != null && v.OrderDetail_Remark != "" ? "(" + v.OrderDetail_Remark + ")" : "";
										if (!v.OrderDetail_OutPasswayName && v.OrderDetail_StatusNo == "202") v.OrderDetail_OutPasswayName = "自动关闭" + remark;
										if (v.OrderDetail_StatusNo < 201) { outphoto = ""; }


										var item = { '出入口': v.OrderDetail_OutPasswayName ? "[出场]" + v.OrderDetail_OutPasswayName : (v.OrderDetail_StatusNo > 200 ? "无" : "未出场"), '出场时间': outTime, '出场图片': outphoto };
										if (v.OrderDetail_IsSettle == 1) {
											item["停车计费"] = v.OrderDetail_TotalAmount + " 元";
										} else {
											item["停车计费"] = "无";
											if (v.OrderDetail_StatusNo < 201) { item["停车计费"] = ""; }
										}

										if (v.OrderDetail_EnterTime && v.OrderDetail_OutTime) {
											var d1 = new Date(v.OrderDetail_EnterTime);
											var d2 = new Date(v.OrderDetail_OutTime);
											var min = Math.floor(parseInt(d2 - d1) / 1000 / 60);
											sumMin += min;
											item["停车时长"] = _DATE.getZhTimesbyMin(Math.ceil(min));
										} else {
											item["停车时长"] = "无";
											if (v.OrderDetail_StatusNo < 201) { item["停车时长"] = ""; }
										}

										gjData.push(item);

									});
								}

								if (json.data.model.ParkOrder_StatusNo > 200) {
									var item = { '出入口': "出场", '停车时长': _DATE.getZhTimesbyMin(Math.ceil(sumMin)), "应收金额": sumMoney.toFixed(2) + " 元", "优惠金额": sumedCouponMoney.toFixed(2) + " 元", '实收金额': sumedMoney.toFixed(2) + " 元" };
									gjData.push(item);
								}

								$(".fishBone").html("");
								$(".fishBone").fishBone(gjData);

								//计费详情
								if (json.data.calclist && json.data.calclist.length > 0) {
									$(".calcdetail").removeClass("hide");
									$('#data-view-calcdetail').html($('#data-tmpl-calcdetail').tmpl(json.data.calclist));
									//绑定点击事件|设置隔两行变色
									$('.table-calcdetail').each(function () {
										var table = $(this);
										table.children('tbody').children('tr').filter(':odd').hide();
										table.children('tbody').children('tr').filter(':even').click(function () {
											$(this).next().find("img").each(function () {
												if ($(this).attr("data-img") != undefined)
													$(this).attr("src", $(this).attr("data-img"))
											})
											var element = $(this);
											element.next('tr').toggle();
											element.find(".fa-chevron-right").toggleClass("fa-chevron-down");
										});

										$.each(table.children('tbody').children('tr'), function (index, obj) {
											var j = parseInt(index / 2);
											if (j % 2 === 0) {
												obj.bgColor = "#f9f9f9";
											}
										});
									});
								} else {
									$(".calcdetail").removeClass("hide").addClass("hide");
								}

								//充电滞留详情
								if (json.data.penaltylist && json.data.penaltylist.length > 0) {
									$(".penaltylist").removeClass("hide");
									$('#data-view-penaltylist').html($('#data-tmpl-penaltylist').tmpl(json.data.penaltylist));
								} else {
									$(".penaltylist").removeClass("hide").addClass("hide");
								}

								pager.bindEvent();

								layer.closeAll();
							} else {
								layer.msg('加载失败：' + json.msg, { icon: 5 });
							}
						},
						error: function () {
							layer.msg('系统错误', { icon: 2 });
						}
					});
				} else {
					layer.msg('订单参数无效', { icon: 0 });
				}
			}
		};

		$(function () { pager.init() });

		function getParkMin(enterTime, outTime) {
			var d1 = new Date(enterTime);
			var d2 = new Date(outTime);
			var min = Math.floor(parseInt(d2 - d1) / 1000 / 60);
			return _DATE.getZhTimesbyMin(Math.ceil(min));
		}


		//获得年月日时分秒
		//传入日期//例：2020-10-27T14:36:23
		var timeFormatSeconds = function (time) {
			if (time == null || time == "") return "";
			var d = time ? new Date(time) : new Date();
			var year = d.getFullYear();
			var month = d.getMonth() + 1;
			var day = d.getDate();
			var hours = d.getHours();
			var min = d.getMinutes();
			var seconds = d.getSeconds();

			if (month < 10) month = '0' + month;
			if (day < 10) day = '0' + day;
			if (hours < 0) hours = '0' + hours;
			if (min < 10) min = '0' + min;
			if (seconds < 10) seconds = '0' + seconds;

			return (year + '-' + month + '-' + day + ' ' + hours + ':' + min + ':' + seconds);
		}
	</script>
</body>
</html>
