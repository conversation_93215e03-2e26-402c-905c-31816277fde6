<link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">

<div class="layui-form" lay-filter="programForm">
    <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title">
            <li class="layui-this">空闲状态</li>
            <li>非空闲状态</li>
        </ul>
        <div class="layui-tab-content">
            <!-- 空闲状态配置 -->
            <div class="layui-tab-item layui-show">
                <div class="layui-tab layui-tab-brief state-sub-tab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">节目内容</li>
                        <li>字体属性</li>
                        <li>显示属性</li>
                    </ul>
                    <div class="layui-tab-content">
                        <!-- 空闲状态 - 节目内容设置 -->
                        <div class="layui-tab-item layui-show">
                            <div class="preset-vars">
                                <div class="vars-group">
                                    <div class="vars-title">
                                        <span>预设内容</span>
                                        <span style="font-size: 12px; color: #999; margin-left: 8px;">(双击标签可编辑)</span>
                                        <div class="preset-actions">
                                            <div class="btn-group">
                                                <button type="button"
                                                    class="layui-btn layui-btn-xs layui-btn-primary preview-preset">
                                                    <i class="layui-icon layui-icon-search"></i>
                                                    <span>预览</span>
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary clear-preset">
                                                    <i class="layui-icon layui-icon-delete"></i>
                                                    <span>清空</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="vars-content" id="presetContentIdle">
                                        <!-- 这里会动态添加选中的预设变量标签 -->
                                    </div>
                                </div>
                                <div class="vars-group">
                                    <div class="vars-title">基础信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{TotalSpaces}}">车场总车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AvailableSpaces}}">车场剩余车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AreaName}}">区域名称</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AreaTotalSpaces}}">区域总车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AreaAvailableSpaces}}">区域剩余车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{LaneName}}">关联车道</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{CustomInfo}}">自定义信息</button>
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">时间信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{CurrentTime}}">当前时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{EntryTime}}">入场时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{ExitTime}}">出场时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{ExpireTime}}">过期时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{ParkingDuration}}">停车时长</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{RemainingDays}}">剩余天数</button>
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">车辆信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-orange"
                                            data-var="{{PlateNumber}}">车牌号</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-orange"
                                            data-var="{{PlateType}}">车牌类型</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-orange"
                                            data-var="{{SpaceNumber}}">车场车位号</button>
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">费用信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-purple"
                                            data-var="{{Fee}}">费用</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-purple"
                                            data-var="{{Balance}}">余额</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 空闲状态 - 字体属性设置 -->
                        <div class="layui-tab-item">
                            <div class="font-config-container">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">字体大小</label>
                                    <div class="layui-input-inline">
                                        <select name="ProgramAreaAction_FontSize">
                                            <option value="16" selected>16</option>
                                            <option value="24">24</option>
                                            <option value="32">32</option>
                                            <option value="48">48</option>
                                            <option value="64">64</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">字体颜色</label>
                                    <div class="layui-input-inline">
                                        <select name="ProgramAreaAction_FontColor">
                                            <option value="255" selected>红色</option>
                                            <option value="65280">绿色</option>
                                            <option value="65535">黄色</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">特殊情况</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" name="ProgramAreaAction_UseRedForNotEnoughSpace" title="剩余车位不足字体使用红色" lay-skin="primary">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 空闲状态 - 显示属性设置 -->
                        <div class="layui-tab-item">
                            <div class="display-config-container">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">显示特效</label>
                                    <div class="layui-input-inline">
                                        <select name="ProgramAreaAction_Effect" lay-filter="displayEffect" lay-search>
                                            <option value="1" selected>立即显示</option>
                                            <option value="2">向左移动</option>
                                            <option value="3">向上移动</option>
                                            <option value="4">向右移动</option>
                                            <option value="5">向下移动</option>
                                            <option value="6">闪烁</option>
                                            <option value="94">向上连移</option>
                                            <option value="95">向下连移</option>
                                            <option value="96">向右连移</option>
                                            <option value="97">向左连移</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">特效速度</label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="ProgramAreaAction_EffectSpeed" lay-verify="required|number"
                                            value="20" min="1" max="64" class="layui-input"
                                            onblur="if(value>64)value=64;if(value<1)value=1">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">数值越大速度越慢(1-64)</div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">显示时间</label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="ProgramAreaAction_DisplayTime" lay-verify="required|number"
                                            value="60" min="1" max="65535" class="layui-input"
                                            onblur="if(value>65535)value=65535;if(value<1)value=1">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">页面留停时间(1-65535)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 非空闲状态配置 -->
            <div class="layui-tab-item">
                <div class="layui-tab layui-tab-brief state-sub-tab">
                    <ul class="layui-tab-title">
                        <li class="layui-this">节目内容</li>
                        <li>字体属性</li>
                        <li>显示属性</li>
                    </ul>
                    <div class="layui-tab-content">
                        <!-- 非空闲状态 - 节目内容设置 -->
                        <div class="layui-tab-item layui-show">
                            <div class="preset-vars">
                                <div class="vars-group">
                                    <div class="vars-title">
                                        <span>预设内容</span>
                                        <span style="font-size: 12px; color: #999; margin-left: 8px;">(双击标签可编辑)</span>
                                        <div class="preset-actions">
                                            <div class="btn-group">
                                                <button type="button"
                                                    class="layui-btn layui-btn-xs layui-btn-primary preview-preset">
                                                    <i class="layui-icon layui-icon-search"></i>
                                                    <span>预览</span>
                                                </button>
                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary clear-preset">
                                                    <i class="layui-icon layui-icon-delete"></i>
                                                    <span>清空</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="vars-content" id="presetContentBusy">
                                        <!-- 这里会动态添加选中的预设变量标签 -->
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">基础信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{TotalSpaces}}">车场总车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AvailableSpaces}}">车场剩余车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AreaName}}">区域名称</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AreaTotalSpaces}}">区域总车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{AreaAvailableSpaces}}">区域剩余车位</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{LaneName}}">关联车道</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-blue"
                                            data-var="{{CustomInfo}}">自定义信息</button>
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">时间信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{CurrentTime}}">当前时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{EntryTime}}">入场时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{ExitTime}}">出场时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{ExpireTime}}">过期时间</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{ParkingDuration}}">停车时长</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-green"
                                            data-var="{{RemainingDays}}">剩余天数</button>
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">车辆信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-orange"
                                            data-var="{{PlateNumber}}">车牌号</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-orange"
                                            data-var="{{PlateType}}">车牌类型</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-orange"
                                            data-var="{{SpaceNumber}}">车场车位号</button>
                                    </div>
                                </div>

                                <div class="vars-group">
                                    <div class="vars-title">费用信息</div>
                                    <div class="vars-content">
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-purple"
                                            data-var="{{Fee}}">费用</button>
                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm bg-purple"
                                            data-var="{{Balance}}">余额</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 非空闲状态 - 字体属性设置 -->
                        <div class="layui-tab-item">
                            <div class="font-config-container">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">字体大小</label>
                                    <div class="layui-input-inline">
                                        <select name="ProgramAreaAction_NonIdleFontSize">
                                            <option value="16" selected>16</option>
                                            <option value="24">24</option>
                                            <option value="32">32</option>
                                            <option value="48">48</option>
                                            <option value="64">64</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">字体颜色</label>
                                    <div class="layui-input-inline">
                                        <select name="ProgramAreaAction_NonIdleFontColor">
                                            <option value="255" selected>红色</option>
                                            <option value="65280">绿色</option>
                                            <option value="65535">黄色</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 非空闲状态 - 显示属性设置 -->
                        <div class="layui-tab-item">
                            <div class="display-config-container">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">显示特效</label>
                                    <div class="layui-input-inline">
                                        <select name="ProgramAreaAction_NonIdleEffect" lay-filter="nonIdleDisplayEffect" lay-search>
                                            <option value="1" selected>立即显示</option>
                                            <option value="2">向左移动</option>
                                            <option value="3">向上移动</option>
                                            <option value="4">向右移动</option>
                                            <option value="5">向下移动</option>
                                            <option value="6">闪烁</option>
                                            <option value="94">向上连移</option>
                                            <option value="95">向下连移</option>
                                            <option value="96">向右连移</option>
                                            <option value="97">向左连移</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">特效速度</label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="ProgramAreaAction_NonIdleEffectSpeed" lay-verify="required|number"
                                            value="20" min="1" max="64" class="layui-input"
                                            onblur="if(value>64)value=64;if(value<1)value=1">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">数值越大速度越慢(1-64)</div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">显示时间</label>
                                    <div class="layui-input-inline">
                                        <input type="number" name="ProgramAreaAction_NonIdleDisplayTime" lay-verify="required|number"
                                            value="60" min="1" max="65535" class="layui-input"
                                            onblur="if(value>65535)value=65535;if(value<1)value=1">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux">页面留停时间(1-65535)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="footer-bar">
    <button type="button" class="footer-btn save-btn" id="saveConfig">
        <i class="layui-icon layui-icon-ok"></i>
        <span>保存</span>
    </button>
    <button type="button" class="footer-btn cancel-btn" id="cancelConfig">
        <i class="layui-icon layui-icon-close"></i>
        <span>取消</span>
    </button>
</div>

<style>
    html,
    body {
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    .layui-form {
        background: #fff;
        border-radius: 2px;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
        padding: 0;
        margin: 0;
        border: none !important;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .layui-form-label {
        width: 100px;
        padding: 9px 15px;
        font-size: 13px;
        color: #666;
    }

    .layui-input-inline {
        width: 250px !important;
        margin-right: 10px;
    }

    .layui-input-block {
        margin-left: 130px;
        line-height: 38px;
        height: 38px;
        padding: 0;
    }

    .layui-tab {
        margin: 0;
        border: none !important;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .layui-tab-content {
        padding: 0;
        min-height: auto;
        border: none !important;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .layui-tab-item {
        min-height: auto;
        border: none !important;
        height: 100%;
        overflow-y: auto;
        padding: 10px 0 80px; /* 调整顶部内边距 */
    }

    .layui-form-item {
        margin-bottom: 15px;
        margin-right: 0;
    }

    .layui-form-checkbox[lay-skin=primary] {
        margin: 0;
        padding: 0;
        height: 38px;
        line-height: 38px;
        display: inline-block;
        vertical-align: middle;
    }

    .layui-form-checkbox[lay-skin=primary] span {
        padding: 0 25px;
        font-size: 13px;
        color: #666;
    }

    .layui-tab-brief {
        height: 100%;
    }

    .layui-tab-title {
        background: #fff;
        border-bottom: 1px solid #f6f6f6;
    }

    .layui-disabled .layui-input {
        background-color: #f5f5f5 !important;
        cursor: not-allowed !important;
    }

    .preset-vars {
        padding: 0 15px;
        margin-top: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 7px; /* 再次调整各组间距 */
    }

    .vars-title {
        font-size: 13px;
        color: #333;
        margin-bottom: 3px; /* 调整标题下边距 */
        padding-left: 5px; /* 进一步减小标题左内边距 */
        border-left: 3px solid transparent;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 0;
    }

    .vars-content {
        display: flex;
        flex-wrap: wrap;
        gap: 2px; /* 进一步减小按钮间距 */
        padding: 2px; /* 进一步减小按钮容器内边距 */
    }

    .vars-content .layui-btn {
        margin: 0;
        padding: 0 6px; /* 减小按钮水平内边距 */
        font-size: 12px;
        height: 22px; /* 减小按钮高度 */
        line-height: 20px; /* 减小按钮行高 */
        flex-shrink: 0;
    }

    .bg-blue {
        background-color: rgba(30, 159, 255, 0.1) !important;
        border-color: rgba(30, 159, 255, 0.2) !important;
        color: #1e9fff !important;
    }

    .bg-green {
        background-color: rgba(22, 183, 119, 0.1) !important;
        border-color: rgba(22, 183, 119, 0.2) !important;
        color: #16b777 !important;
    }

    .bg-orange {
        background-color: rgba(255, 87, 34, 0.1) !important;
        border-color: rgba(255, 87, 34, 0.2) !important;
        color: #ff5722 !important;
    }

    .bg-purple {
        background-color: rgba(22, 186, 170, 0.1) !important;
        border-color: rgba(22, 186, 170, 0.2) !important;
        color: #16baaa !important;
    }

    .vars-content .layui-btn:hover {
        opacity: 0.8;
    }


    .vars-group:last-child {
        margin-bottom: 0;
    }

/* Custom Scrollbar */
    .state-sub-tab .layui-tab-content::-webkit-scrollbar,
    .content-config-grid > .card > .card-body::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .state-sub-tab .layui-tab-content::-webkit-scrollbar-track,
    .content-config-grid > .card > .card-body::-webkit-scrollbar-track {
        background: transparent;
    }

    .state-sub-tab .layui-tab-content::-webkit-scrollbar-thumb,
    .content-config-grid > .card > .card-body::-webkit-scrollbar-thumb {
        background-color: #e0e0e0;
        border-radius: 3px;
    }

    .state-sub-tab .layui-tab-content::-webkit-scrollbar-thumb:hover,
    .content-config-grid > .card > .card-body::-webkit-scrollbar-thumb:hover {
        background-color: #c7c7c7;
    }
    .vars-group:nth-child(1) .vars-title {
        border-left-color: #16b777;
    }

    .vars-group:nth-child(2) .vars-title {
        border-left-color: #1e9fff;
    }

    .vars-group:nth-child(3) .vars-title {
        border-left-color: #16b777;
    }

    .vars-group:nth-child(4) .vars-title {
        border-left-color: #ff5722;
    }

    .vars-group:nth-child(5) .vars-title {
        border-left-color: #16baaa;
    }

    .vars-content .layui-btn {
        cursor: pointer;
    }

    .vars-group:nth-child(1) {
        margin-bottom: 0;
        background: #fff;
        border: 1px dashed #e6e6e6;
        padding: 5px 8px; /* 进一步减小预设内容区外部容器内边距 */
    }

    .vars-group:nth-child(1) .vars-content {
        min-height: 60px; /* 恢复预设内容区高度 */
        height: 60px;    /* 恢复预设内容区高度 */
        padding: 5px;     /* 调整预设内容区内边距 */
        background: #fafafa;
        border-radius: 2px;
        overflow-y: auto;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        transform: translateZ(0);
        -webkit-font-smoothing: subpixel-antialiased;
        will-change: transform;
    }

    .vars-group:nth-child(1) .vars-content::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .vars-group:nth-child(1) .vars-content::-webkit-scrollbar-thumb {
        background: #ddd;
        border-radius: 3px;
    }

    .vars-group:nth-child(1) .vars-content::-webkit-scrollbar-track {
        background: #f5f5f5;
        border-radius: 3px;
    }

    .vars-group:not(:first-child) {
        margin-bottom: 0;
        background: #f8f8f8;
        border-radius: 4px;
        padding: 5px 8px; /* 调整其他按钮组容器内边距 */
    }

    .preset-tag {
        height: 22px;
        display: inline-flex;
        align-items: center;
        padding: 0 6px;
        border-radius: 2px;
        font-size: 12px;
        line-height: 22px;
        user-select: none;
        margin: 1px;
        transition: all .2s;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        transform: translateZ(0);
        backface-visibility: hidden;
        font-size: 12px;
        line-height: 22px;
        user-select: none;
        margin: 1px;
        transition: all .2s;
        cursor: move;
        touch-action: none;
    }

    .preset-tag:hover {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .tag-blue {
        background-color: rgba(30, 159, 255, 0.08);
        border: 1px solid rgba(30, 159, 255, 0.2);
        color: #1e9fff;
    }

    .tag-green {
        background-color: rgba(22, 183, 119, 0.08);
        border: 1px solid rgba(22, 183, 119, 0.2);
        color: #16b777;
    }

    .tag-orange {
        background-color: rgba(255, 87, 34, 0.08);
        border: 1px solid rgba(255, 87, 34, 0.2);
        color: #ff5722;
    }

    .tag-purple {
        background-color: rgba(22, 186, 170, 0.08);
        border: 1px solid rgba(22, 186, 170, 0.2);
        color: #16baaa;
    }

    .preset-tag .tag-close {
        margin-left: 6px;
        width: 16px;
        height: 16px;
        line-height: 16px;
        text-align: center;
        border-radius: 50%;
        cursor: pointer;
        font-size: 12px;
        opacity: 0.6;
        transition: all .2s;
    }

    .preset-tag .tag-close:hover {
        opacity: 1;
        background: rgba(0, 0, 0, 0.08);
    }

    .preset-tag.dragging {
        opacity: 0.5;
        background: #f8f8f8;
    }

    .clear-preset {
        float: none;
        margin: 0;
        height: 20px;
        line-height: 18px;
        padding: 0 6px;
        font-size: 12px;
        background-color: rgba(0, 0, 0, 0.04);
        border: 1px solid #e6e6e6;
        border-radius: 2px;
        color: #666;
        transition: all .2s;
        display: inline-flex;
        align-items: center;
    }

    .clear-preset .layui-icon {
        font-size: 12px;
        margin-right: 3px;
    }

    .clear-preset:hover {
        background-color: #f8f8f8;
        color: #ff5722;
        border-color: #ff5722;
    }

    .preset-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .preset-tabs {
        display: flex;
        gap: 1px;
        background: #e6e6e6;
        padding: 1px;
        border-radius: 2px;
    }

    .preset-tab {
        font-size: 12px;
        padding: 2px 8px;
        cursor: pointer;
        background: #f5f5f5;
        color: #666;
        transition: all .2s;
        user-select: none;
    }

    .preset-tab:first-child {
        border-radius: 2px 0 0 2px;
    }

    .preset-tab:last-child {
        border-radius: 0 2px 2px 0;
    }

    .preset-tab.active {
        background: #fff;
        color: #16b777;
    }

    .preset-tab:hover:not(.active) {
        color: #16b777;
    }

    .preview-preset {
        float: none;
        margin: 0;
        height: 20px;
        line-height: 18px;
        padding: 0 6px;
        font-size: 12px;
        background-color: rgba(22, 183, 119, 0.1);
        border: 1px solid #16b777;
        border-radius: 2px;
        color: #16b777;
        transition: all .2s;
        display: inline-flex;
        align-items: center;
    }

    .preview-preset .layui-icon {
        font-size: 12px;
        margin-right: 3px;
    }

    .preview-preset:hover {
        background-color: rgba(22, 183, 119, 0.2);
        color: #16b777;
    }

    /* 添加按钮组相关样式 */
    .btn-group {
        display: inline-flex;
        border-radius: 2px;
        overflow: hidden;
        border: 1px solid #e6e6e6;
    }

    .btn-group .layui-btn {
        margin: 0;
        height: 20px;
        line-height: 18px;
        padding: 0 6px;
        font-size: 12px;
        border: none;
        border-radius: 0;
        background: #fff;
    }

    .btn-group .layui-btn:not(:last-child) {
        border-right: 1px solid #e6e6e6;
    }

    .btn-group .preview-preset {
        color: #16b777;
    }

    .btn-group .preview-preset:hover {
        background-color: rgba(22, 183, 119, 0.1);
    }

    .btn-group .clear-preset {
        color: #ff5722;
        background-color: rgba(255, 87, 34, 0.08);
    }

    .btn-group .clear-preset:hover {
        background-color: rgba(255, 87, 34, 0.15);
        color: #ff5722;
    }

    .btn-group .layui-btn .layui-icon {
        font-size: 12px;
        margin-right: 3px;
    }

    /* 删除之前的立按钮样式 */
    .preview-preset,
    .clear-preset {
        float: none;
        margin: 0;
        height: 20px;
        line-height: 18px;
        padding: 0 6px;
        font-size: 12px;
        display: inline-flex;
        align-items: center;
    }

    .layui-table {
        margin: 0;
    }

    .layui-table tr[data-id] {
        cursor: pointer;
    }

    .layui-table tr[data-id]:hover {
        background-color: #f2f2f2;
    }

    .layui-layer-content {
        overflow: auto !important;
    }

    .footer-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 12px 15px;
        text-align: center;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: center;
        background: #fff;
        z-index: 999;
        border-radius: 0 0 8px 8px;
    }

    .footer-btn {
        height: 32px;
        line-height: 32px;
        padding: 0 16px;
        border-radius: 6px;
        margin: 0 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        transition: all 0.2s;
    }

    .save-btn {
        background: #1890ff;
        color: white;
        border: none;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
    }

    .save-btn:hover {
        background: #40a9ff;
        box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
        transform: translateY(-1px);
    }

    .cancel-btn {
        background: #ff5722;
        color: white;
        border: none;
        box-shadow: 0 2px 4px rgba(255, 87, 34, 0.2);
    }

    .cancel-btn:hover {
        background: #ff7043;
        box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
        transform: translateY(-1px);
    }

    .layui-btn.layui-btn-disabled {
        cursor: not-allowed !important;
        border-color: #e6e6e6 !important;
        position: relative;
    }

    /* 添加禁用状态的视觉提示 */
    .layui-btn.layui-btn-disabled::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(45deg,
                rgba(0, 0, 0, 0.07),
                rgba(0, 0, 0, 0.07) 5px,
                transparent 5px,
                transparent 10px);
        pointer-events: none;
    }

    /* 修改禁用状态下的文字颜色 */
    .layui-btn.layui-btn-disabled.bg-blue {
        color: rgba(30, 159, 255, 0.8) !important;
    }

    .layui-btn.layui-btn-disabled.bg-green {
        color: rgba(22, 183, 119, 0.8) !important;
    }

    .layui-btn.layui-btn-disabled.bg-orange {
        color: rgba(255, 87, 34, 0.8) !important;
    }

    .layui-btn.layui-btn-disabled.bg-purple {
        color: rgba(22, 186, 170, 0.8) !important;
    }

    /* 禁用状态下的悬停效果 */
    .layui-btn.layui-btn-disabled:hover {
        opacity: 0.65 !important;
        cursor: not-allowed !important;
        box-shadow: none !important;
    }

    /* 添加已禁用行的样式 */
    .disabled-row {
        background-color: #f5f5f5 !important;
        opacity: 0.7;
        cursor: not-allowed !important;
    }

    .disabled-row:hover {
        background-color: #f5f5f5 !important;
    }

    /* 添加已使用标签的样式 */
    .used-tag {
        display: inline-block;
        padding: 2px 6px;
        background: rgba(255, 87, 34, 0.1);
        color: #ff5722;
        border: 1px solid rgba(255, 87, 34, 0.2);
        border-radius: 2px;
        font-size: 12px;
        line-height: 1.5;
    }

    /* 修改表格样式 */
    .layui-table td,
    .layui-table th {
        padding: 9px 15px;
        min-height: 20px;
        line-height: 20px;
        border: 1px solid #e6e6e6;
        font-size: 13px;
    }

    /* 添加新的标签内容样式 */
    .tag-content {
        display: flex;
        align-items: center;
        gap: 2px;
    }

    .tag-prefix {
        color: rgba(0, 0, 0, 0.45);
        font-style: italic;
        padding-right: 3px;
        border-right: 1px dashed rgba(0, 0, 0, 0.15);
    }

    .tag-main {
        font-weight: 500;
    }

    .tag-suffix {
        color: rgba(0, 0, 0, 0.45);
        font-style: italic;
        padding-left: 3px;
        border-left: 1px dashed rgba(0, 0, 0, 0.15);
    }

    /* 针对不同类型标签的特殊样式 */
    .tag-blue .tag-prefix,
    .tag-blue .tag-suffix {
        color: rgba(30, 159, 255, 0.8);
    }

    .tag-green .tag-prefix,
    .tag-green .tag-suffix {
        color: rgba(22, 183, 119, 0.8);
    }

    .tag-orange .tag-prefix,
    .tag-orange .tag-suffix {
        color: rgba(255, 87, 34, 0.8);
    }

    .tag-purple .tag-prefix,
    .tag-purple .tag-suffix {
        color: rgba(22, 186, 170, 0.8);
    }

    .sortable-ghost {
        opacity: 0.4;
        background: #f0f0f0 !important;
        border: 2px dashed #ccc !important;
    }

    .sortable-chosen {
        background: rgba(24, 144, 255, 0.1) !important;
    }

    .sortable-drag {
        opacity: 0.8;
        background: #ffffff !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        cursor: move !important;
    }

    #presetContent {
        position: relative;
        min-height: 60px;
        padding: 6px;
        background: #fafafa;
        border-radius: 2px;
    }

    /* 字体配置容器样式 */
    .font-config-container {
        padding: 15px 0;
    }

    .font-state-tabs {
        display: flex;
        gap: 1px;
        background: #e6e6e6;
        padding: 1px;
        border-radius: 4px;
        margin-bottom: 20px;
        width: fit-content;
    }

    .font-state-tab {
        font-size: 13px;
        padding: 8px 16px;
        cursor: pointer;
        background: #f5f5f5;
        color: #666;
        transition: all .2s;
        user-select: none;
        border-radius: 3px;
    }

    .font-state-tab:first-child {
        border-radius: 3px 0 0 3px;
    }

    .font-state-tab:last-child {
        border-radius: 0 3px 3px 0;
    }

    .font-state-tab.active {
        background: #1e9fff;
        color: white;
        box-shadow: 0 2px 4px rgba(30, 159, 255, 0.3);
    }

    .font-state-tab:hover:not(.active) {
        background: #e8e8e8;
        color: #1e9fff;
    }

    .font-config-panel {
        transition: all .3s ease;
    }

    /* 显示配置容器样式 */
    .display-config-container {
        padding: 15px 0;
    }

    .display-state-tabs {
        display: flex;
        gap: 1px;
        background: #e6e6e6;
        padding: 1px;
        border-radius: 4px;
        margin-bottom: 20px;
        width: fit-content;
    }

    .display-state-tab {
        font-size: 13px;
        padding: 8px 16px;
        cursor: pointer;
        background: #f5f5f5;
        color: #666;
        transition: all .2s;
        user-select: none;
        border-radius: 3px;
    }

    .display-state-tab:first-child {
        border-radius: 3px 0 0 3px;
    }

    .display-state-tab:last-child {
        border-radius: 0 3px 3px 0;
    }

    .display-state-tab.active {
        background: #16b777;
        color: white;
        box-shadow: 0 2px 4px rgba(22, 183, 119, 0.3);
    }

    .display-state-tab:hover:not(.active) {
        background: #e8e8e8;
        color: #16b777;
    }

    .display-config-panel {
        transition: all .3s ease;
    }

    /* 子tab样式 */
    .state-sub-tab {
        height: 100%;
        margin: 0;
        border: none !important;
    }

    .state-sub-tab .layui-tab-title {
        background: #f8f8f8;
        border-bottom: 1px solid #e6e6e6;
        margin: 0;
        padding: 0 10px; /* 调整水平内边距 */
    }

    .state-sub-tab .layui-tab-title li {
        font-size: 13px;
        color: #666;
        padding: 0 15px; /* 调整水平内边距 */
        line-height: 40px;
        height: 40px;
    }

    .state-sub-tab .layui-tab-title li.layui-this {
        background: #fff;
        color: #1e9fff;
        border-bottom: 2px solid #1e9fff;
    }

    .state-sub-tab .layui-tab-content {
        padding: 0;
        border: none !important;
        height: calc(100% - 40px);
        overflow: hidden;
    }

    .state-sub-tab .layui-tab-item {
        height: 100%;
        overflow-y: auto;
        padding: 10px 0 80px; /* 调整顶部内边距 */
    }
</style>

<script src="~/Static/admin/layui/layui.js"></script>
<script src="~/Static/js/Sortable.min.js"></script>
<script>
    layui.use(['form', 'layer'], function () {
        let form = layui.form;
        let layer = layui.layer;
        let $ = layui.jquery;

        // 存储当前编辑的动作配置
        let tempProgramAreaAction = {};
        // 存储预设内容
        let presetContents = {
            idle: [],
            busy: []
        };
        // 当前状态类型（空闲/非空闲）
        let currentStateType = 'idle';

        // 添加一个函数来获取已使用的ID
        function getUsedIds(type) {
            let usedIds = new Set();
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            $(containerId + ' .preset-tag').each(function () {
                let value = $(this).data('value');
                if (value.includes(type)) {
                    let parts = value.split(':');
                    if (parts.length < 2) {
                        console.error('Invalid value format');
                        return;
                    }
                    let id = parts[1].replace('}}', '');
                    usedIds.add(id);
                }
            });
            return usedIds;
        }

        // 初始化数据
        function initializeData() {
            const urlParams = new URLSearchParams(window.location.search);
            const programAreaId = urlParams.get('programAreaId');
            const displayTemplateId = parent.pager.displayTemplate.DisplayTemplate_Id;
            const displayTemplateNo = parent.pager.displayTemplate.DisplayTemplate_No;

            if (programAreaId) {
                const action = parent.pager.programAreaActions.find(action =>
                    action.ProgramAreaAction_ProgramAreaId === parseInt(programAreaId) &&
                    action.ProgramAreaAction_DisplayTemplateId === displayTemplateId
                );
                if (action) {
                    tempProgramAreaAction = action;

                    // 设置表单数据（空闲状态使用原有字段，非空闲状态使用新字段）
                    form.val('programForm', {
                        // 空闲状态字体属性（使用原有字段）
                        'ProgramAreaAction_FontSize': action.ProgramAreaAction_FontSize || 16,
                        'ProgramAreaAction_FontColor': action.ProgramAreaAction_FontColor || 0xff,
                        'ProgramAreaAction_UseRedForNotEnoughSpace': action.ProgramAreaAction_UseRedForNotEnoughSpace === 1,
                        // 空闲状态显示属性（使用原有字段）
                        'ProgramAreaAction_Effect': action.ProgramAreaAction_Effect || 0,
                        'ProgramAreaAction_EffectSpeed': action.ProgramAreaAction_EffectSpeed || 20,
                        'ProgramAreaAction_DisplayTime': action.ProgramAreaAction_DisplayTime || 60,
                        // 非空闲状态字体属性（使用新字段，如果没有则使用原有字段作为默认值）
                        'ProgramAreaAction_NonIdleFontSize': action.ProgramAreaAction_NonIdleFontSize || action.ProgramAreaAction_FontSize || 16,
                        'ProgramAreaAction_NonIdleFontColor': action.ProgramAreaAction_NonIdleFontColor || action.ProgramAreaAction_FontColor || 0xff,
                        // 非空闲状态显示属性（使用新字段，如果没有则使用原有字段作为默认值）
                        'ProgramAreaAction_NonIdleEffect': action.ProgramAreaAction_NonIdleEffect || action.ProgramAreaAction_Effect || 0,
                        'ProgramAreaAction_NonIdleEffectSpeed': action.ProgramAreaAction_NonIdleEffectSpeed || action.ProgramAreaAction_EffectSpeed || 20,
                        'ProgramAreaAction_NonIdleDisplayTime': action.ProgramAreaAction_NonIdleDisplayTime || action.ProgramAreaAction_DisplayTime || 60
                    });

                    // 加载预设内容
                    try {
                        if (action.ProgramAreaAction_IdleContent) {
                            presetContents.idle = JSON.parse(action.ProgramAreaAction_IdleContent);
                        }
                        if (action.ProgramAreaAction_NonIdleContent) {
                            presetContents.busy = JSON.parse(action.ProgramAreaAction_NonIdleContent);
                        }
                    } catch (e) {
                        console.error('解析预设内容失败:', e);
                    }
                    // 如果是连续移动特效，禁用显示时间
                    if (['6', '7', '8', '9'].includes(action.ProgramAreaAction_Effect.toString())) {
                        $('input[name="ProgramAreaAction_DisplayTime"]').attr('disabled', true);
                        $('input[name="ProgramAreaAction_DisplayTime"]').closest('.layui-form-item').addClass('layui-disabled');
                    }
                    // 如果非空闲状态是连续移动特效，禁用其显示时间
                    if (['6', '7', '8', '9'].includes(action.ProgramAreaAction_NonIdleEffect?.toString())) {
                        $('input[name="ProgramAreaAction_NonIdleDisplayTime"]').attr('disabled', true);
                        $('input[name="ProgramAreaAction_NonIdleDisplayTime"]').closest('.layui-form-item').addClass('layui-disabled');
                    }
                } else {
                    // 不存在配置则使用默认值
                    tempProgramAreaAction = {
                        ProgramAreaAction_ProgramAreaId: parseInt(programAreaId),
                        ProgramAreaAction_DisplayTemplateId: displayTemplateId,
                        ProgramAreaAction_DisplayTemplateNo: displayTemplateNo,
                        // 空闲状态默认值（使用原有字段）
                        ProgramAreaAction_FontSize: 16,
                        ProgramAreaAction_FontColor: 0xff,
                        ProgramAreaAction_Effect: 0,
                        ProgramAreaAction_EffectSpeed: 20,
                        ProgramAreaAction_DisplayTime: 60,
                        ProgramAreaAction_UseRedForNotEnoughSpace: 0,
                        // 非空闲状态默认值（使用新字段）
                        ProgramAreaAction_NonIdleFontSize: 16,
                        ProgramAreaAction_NonIdleFontColor: 0xff,
                        ProgramAreaAction_NonIdleEffect: 0,
                        ProgramAreaAction_NonIdleEffectSpeed: 20,
                        ProgramAreaAction_NonIdleDisplayTime: 60
                    };
                }
            }
        }

        // 修改保存当前预设内容的函数
        function saveCurrentPresetContent() {
            const currentContent = [];
            let sortIndex = 0; // 添加排序索引
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';

            $(containerId + ' .preset-tag').each(function () {
                const fullValue = $(this).data('value');
                const text = $(this).find('.tag-main').text();
                const prefix = $(this).data('prefix') || '';
                const suffix = $(this).data('suffix') || '';
                let tagClass = 1;
                let link = 0;

                // 根据tag的class设置对应的数字
                if ($(this).hasClass('tag-green')) tagClass = 2;
                else if ($(this).hasClass('tag-orange')) tagClass = 3;
                else if ($(this).hasClass('tag-purple')) tagClass = 4;
    
                // 解析变量和值
                let key, value;
                const colonIndex = fullValue.indexOf(':');
    
                if (colonIndex > -1 && fullValue.length > colonIndex + 2) { //确保冒号存在且后面有内容才分割
                    // 找到第一个:的位置
                    // 分割字符串
                    const varPart = fullValue.substring(2, colonIndex); // 去掉{{
                    const valuePart = fullValue.substring(colonIndex + 1, fullValue.length - 2); // 去掉}}
    
                    key = `{{${varPart}}}`;
                    value = valuePart;
    
                    // 设置关联类型
                    if (['AreaName', 'AreaTotalSpaces', 'AreaAvailableSpaces'].includes(varPart)) {
                        link = 1; // 关联区域
                    } else if (varPart === 'LaneName') {
                        link = 2; // 关联车道
                    }
                } else {
                    key = fullValue;
                    value = '';
                    link = 0; // 无特定关联
                }
    
                currentContent.push({
                    key: key,
                    value: value,
                    text: text,
                    class: tagClass,
                    link: link,
                    sort: sortIndex++,
                    prefix: prefix,
                    suffix: suffix
                });
            });
    
            presetContents[currentStateType] = currentContent;
        }

        // 修改显示预设内容的函数
        function showPresetContent(type) {
            const containerId = type === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            $(containerId).empty();
            const content = presetContents[type] || [];

            // 根据sort字段排序
            content.sort((a, b) => a.sort - b.sort);

            // 根据sort字段排序
            content.forEach(tag => {
                let cssClass = 'tag-blue';
                switch (tag.class) {
                    case 2: cssClass = 'tag-green'; break;
                    case 3: cssClass = 'tag-orange'; break;
                    case 4: cssClass = 'tag-purple'; break;
                }

                // 构建完整的变量值
                const fullValue = tag.value ?
                    tag.key.replace('}}', `:${tag.value}}}`) :
                    tag.key;

                // 构建标签内容
                const tagContent = `
                    <div class="tag-content">
                        ${tag.prefix ? `<span class="tag-prefix">${tag.prefix}</span>` : ''}
                        <span class="tag-main">${tag.text}</span>
                        ${tag.suffix ? `<span class="tag-suffix">${tag.suffix}</span>` : ''}
                    </div>
                `;

                let $tag = $(`
                    <div class="preset-tag ${cssClass}"
                        data-value="${fullValue}"
                        data-link="${tag.link}"
                        data-sort="${tag.sort}"
                        data-prefix="${tag.prefix || ''}"
                        data-suffix="${tag.suffix || ''}">
                        ${tagContent}
                        <span class="tag-close">×</span>
                    </div>
                `);
                $(containerId).append($tag);
            });
        }

        // 修改 Sortable 配置
        function initSortable() {
            // 初始化空闲状态的Sortable
            const presetContentIdle = document.getElementById('presetContentIdle');
            if (presetContentIdle) {
                const oldInstanceIdle = Sortable.get(presetContentIdle);
                if (oldInstanceIdle) {
                    oldInstanceIdle.destroy();
                }

                new Sortable(presetContentIdle, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    handle: '.preset-tag',
                    filter: '.tag-close',
                    preventOnFilter: true,
                    draggable: '.preset-tag',
                    forceFallback: false,
                    fallbackTolerance: 3,
                    touchStartThreshold: 3,

                    onStart: function (evt) {
                        evt.item.classList.add('dragging');
                        currentStateType = 'idle';
                    },

                    onEnd: function (evt) {
                        evt.item.classList.remove('dragging');
                        currentStateType = 'idle';
                        updateSortOrder();
                    }
                });
            }

            // 初始化非空闲状态的Sortable
            const presetContentBusy = document.getElementById('presetContentBusy');
            if (presetContentBusy) {
                const oldInstanceBusy = Sortable.get(presetContentBusy);
                if (oldInstanceBusy) {
                    oldInstanceBusy.destroy();
                }

                new Sortable(presetContentBusy, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    handle: '.preset-tag',
                    filter: '.tag-close',
                    preventOnFilter: true,
                    draggable: '.preset-tag',
                    forceFallback: false,
                    fallbackTolerance: 3,
                    touchStartThreshold: 3,

                    onStart: function (evt) {
                        evt.item.classList.add('dragging');
                        currentStateType = 'busy';
                    },

                    onEnd: function (evt) {
                        evt.item.classList.remove('dragging');
                        currentStateType = 'busy';
                        updateSortOrder();
                    }
                });
            }
        }

        // 更新排序的函数
        function updateSortOrder() {
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            let sortIndex = 0;
            $(containerId + ' .preset-tag').each(function () {
                $(this).attr('data-sort', sortIndex++);
            });
            saveCurrentPresetContent();
        }

        // 监听主tab切换事件，更新当前状态类型
        $('.layui-form > .layui-tab > .layui-tab-title li').on('click', function () {
            // 先保存当前预设内容
            saveCurrentPresetContent();

            const index = $(this).index();
            currentStateType = index === 0 ? 'idle' : 'busy';
            // 当切换主 tab 时，显示对应状态的内容并更新按钮状态
            showPresetContent(currentStateType);
            updateButtonStates();
        });

        // 监听子tab切换事件，保存当前预设内容
        $('.state-sub-tab .layui-tab-title li').on('click', function () {
            // 在子标签页切换时保存当前预设内容
            saveCurrentPresetContent();
        });

        // 修改保存配置按钮事件
        $('#saveConfig').on('click', function () {
            // 保存当前预设内容
            saveCurrentPresetContent();

            // 检查空闲时和非空闲时的预设内容
            if (presetContents.idle.length === 0 && presetContents.busy.length === 0) {
                layer.msg('请至少设置一种状态（空闲时/非空闲时）的预设内容', {
                    icon: 2,
                    time: 2000
                });
                return;
            }

            // 获取表单数据
            let formData = form.val('programForm');

            //获取关联区域编号和车道编号（从所有预设内容中提取）
            let relatedAreaIds = [], relatedLaneIds = [];

            // 从空闲和非空闲预设内容中提取关联信息
            function extractRelatedIds(contents) {
                if (!contents || contents.length === 0) return;

                contents.forEach(function(content) {
                    if (content.link === 1 && content.value) {
                        // 关联区域
                        if (!relatedAreaIds.includes(content.value)) {
                            relatedAreaIds.push(content.value);
                        }
                    } else if (content.link === 2 && content.value) {
                        // 关联车道
                        if (!relatedLaneIds.includes(content.value)) {
                            relatedLaneIds.push(content.value);
                        }
                    }
                });
            }

            // 提取空闲时的关联信息
            extractRelatedIds(presetContents.idle);
            // 提取非空闲时的关联信息
            extractRelatedIds(presetContents.busy);

            // 更新tempProgramAreaAction
            Object.assign(tempProgramAreaAction, {
                // 空闲状态属性（使用原有字段）
                ProgramAreaAction_FontSize: parseInt(formData.ProgramAreaAction_FontSize),
                ProgramAreaAction_FontColor: parseInt(formData.ProgramAreaAction_FontColor),
                ProgramAreaAction_Effect: parseInt(formData.ProgramAreaAction_Effect),
                ProgramAreaAction_EffectSpeed: parseInt(formData.ProgramAreaAction_EffectSpeed),
                ProgramAreaAction_DisplayTime: parseInt(formData.ProgramAreaAction_DisplayTime),
                // 非空闲状态属性（使用新字段）
                ProgramAreaAction_NonIdleFontSize: parseInt(formData.ProgramAreaAction_NonIdleFontSize),
                ProgramAreaAction_NonIdleFontColor: parseInt(formData.ProgramAreaAction_NonIdleFontColor),
                ProgramAreaAction_NonIdleEffect: parseInt(formData.ProgramAreaAction_NonIdleEffect),
                ProgramAreaAction_NonIdleEffectSpeed: parseInt(formData.ProgramAreaAction_NonIdleEffectSpeed),
                ProgramAreaAction_NonIdleDisplayTime: parseInt(formData.ProgramAreaAction_NonIdleDisplayTime),
                ProgramAreaAction_UseRedForNotEnoughSpace: formData.ProgramAreaAction_UseRedForNotEnoughSpace ? 1 : 0,
                // 预设内容
                ProgramAreaAction_IdleContent: JSON.stringify(presetContents.idle),
                ProgramAreaAction_NonIdleContent: JSON.stringify(presetContents.busy),
                ProgramAreaAction_Status: 0,
                ProgramAreaAction_CreateTime: new Date().toISOString(),
                ProgramAreaAction_UpdateTime: new Date().toISOString(),
                ProgramAreaAction_AreaIds: relatedAreaIds.join(','),
                ProgramAreaAction_LaneIds: relatedLaneIds.join(',')
            });

            // 调用后台保存接口
            $.ajax({
                url: '/DisplayTemplate/SaveProgramAreaAction',
                type: 'POST',
                data: { jsonModel: JSON.stringify(tempProgramAreaAction) },
                success: function (res) {
                    if (res.success) {
                        let programAreaAction = res.data[0];
                        // 更新父页面数据
                        const index = parent.pager.programAreaActions.findIndex(action =>
                            action.ProgramAreaAction_ProgramAreaId === programAreaAction.ProgramAreaAction_ProgramAreaId &&
                            action.ProgramAreaAction_DisplayTemplateId === programAreaAction.ProgramAreaAction_DisplayTemplateId
                        );

                        if (index > -1) {
                            parent.pager.programAreaActions[index] = programAreaAction;
                        } else {
                            parent.pager.programAreaActions.push(programAreaAction);
                        }

                        // 调用父页面的方法更新区域样式
                        parent.updateProgramAreaStyle(programAreaAction.ProgramAreaAction_ProgramAreaId);

                        layer.msg('配置已保存', { icon: 1, time: 1000 }, function () {
                            // 关闭弹窗
                            parent.$('.dialog-mask').remove();
                            parent.$('.custom-dialog').remove();
                        });

                    } else {
                        layer.msg(res.msg || '保存失败', { icon: 2 });
                    }
                },
                error: function (xhr, status, error) {
                    layer.msg('保存失败: ' + error, { icon: 2 });
                }
            });
        });

        // 修改添加新标签的函数
        function addNewTag($tag) {
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            const currentSort = $(containerId + ' .preset-tag').length;
            const prefix = $tag.data('prefix') || '';
            const suffix = $tag.data('suffix') || '';
            const originalText = $tag.find('.tag-main').text();

            // 重构标签内容
            const tagContent = `
                <div class="tag-content">
                    ${prefix ? `<span class="tag-prefix">${prefix}</span>` : ''}
                    <span class="tag-main">${originalText}</span>
                    ${suffix ? `<span class="tag-suffix">${suffix}</span>` : ''}
                </div>
            `;

            $tag.attr('data-sort', currentSort)
                .empty()
                .append(tagContent)
                .append('<span class="tag-close">×</span>');

            $(containerId).append($tag);
            // 添加标签后立即保存当前预设内容
            saveCurrentPresetContent();
            updateButtonStates();
        }

        // 变量按钮点击事件中的标签创建部分
        $('.vars-group:not(:first-child) .layui-btn').on('click', function () {
            if ($(this).hasClass('layui-btn-disabled')) {
                return;
            }

            // 检查当前预设标签的总数
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            let totalTags = $(containerId + ' .preset-tag').length;
            if (totalTags >= 32) {
                layer.msg('预设变量数量已达到上限（32个）', { icon: 2 });
                return;
            }

            let value = $(this).data('var');
            let text = $(this).text();
            let colorClass = '';
            if ($(this).hasClass('bg-blue')) colorClass = 'tag-blue';
            else if ($(this).hasClass('bg-green')) colorClass = 'tag-green';
            else if ($(this).hasClass('bg-orange')) colorClass = 'tag-orange';
            else if ($(this).hasClass('bg-purple')) colorClass = 'tag-purple';

            // 处理车道名称变量
            if (value === '{{LaneName}}') {
                handleLaneSelectionDialog(colorClass, text, false);
                return;
            }

            // 处理区域相关变量
            if (['{{AreaName}}', '{{AreaTotalSpaces}}', '{{AreaAvailableSpaces}}'].includes(value)) {
                handleAreaSelectionDialog(value, colorClass, text, false);
                return;
            }

            // 处理自定义信息
            if (value === '{{CustomInfo}}') {
                handleCustomInfoDialog(colorClass, '', false);
                return;
            }

            // 处理普通变量
            let $tag = $(`
                <div class="preset-tag ${colorClass}" data-value="${value}">
                    <div class="tag-content">
                        <span class="tag-prefix"></span>
                        <span class="tag-main">${text}</span>
                        <span class="tag-suffix"></span>
                    </div>
                    <span class="tag-close">×</span>
                </div>
            `);

            addNewTag($tag);
            updateButtonStates();
        });

        // 添加标签删除事件
        $('#presetContentIdle, #presetContentBusy').on('click', '.tag-close', function (e) {
            $(this).closest('.preset-tag').remove();
            // 删除标签后立即保存当前预设内容
            saveCurrentPresetContent();
            updateButtonStates();
        });

        // 修改双击事件处理，删除原有的双击事件处理代码，替换为：
        $('#presetContentIdle, #presetContentBusy').on('dblclick', '.preset-tag', function (e) {
            const $tag = $(this);
            const value = $tag.data('value');
            let colorClass = '';
            if ($tag.hasClass('tag-blue')) colorClass = 'tag-blue';
            else if ($tag.hasClass('tag-green')) colorClass = 'tag-green';
            else if ($tag.hasClass('tag-orange')) colorClass = 'tag-orange';
            else if ($tag.hasClass('tag-purple')) colorClass = 'tag-purple';

            // 处理自定义信息
            if (value.includes('CustomInfo:')) {
                const currentText = value.replace('{{CustomInfo:', '').replace('}}', '');
                handleCustomInfoDialog(colorClass, currentText, true, $tag);
            }
            // 处理车道选择
            else if (value.includes('LaneName:')) {
                handleLaneSelectionDialog(colorClass, '', true, $tag);
            }
            // 处理区域相关变量
            else if (value.includes('AreaName:') || value.includes('AreaTotalSpaces:') || value.includes('AreaAvailableSpaces:')) {
                const varType = value.substring(2, value.indexOf(':'));
                handleAreaSelectionDialog(`{{${varType}}}`, colorClass, '', true, $tag);
            }
            // 处理扩展属性变量
            else if (extendedVars.includes(value)) {
                handleExtendedPropertiesDialog($tag);
            }
        });

        // 修改预览按钮事件处理代码
        $('.preview-preset').on('click', function (e) {
            e.preventDefault();
            let presetContent = [];
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            $(containerId + ' .preset-tag').each(function () {
                let value = $(this).data('value');
                let text = $(this).find('.tag-main').text();
                let link = $(this).data('link');
                let previewText = getPreviewText(value);

                presetContent.push({
                    value: value,
                    text: text,
                    previewText: previewText,
                    link: link
                });
            });

            if (presetContent.length === 0) {
                layer.msg('当前没有可预览的内容');
                return;
            }

            let formData = form.val('programForm');
            let previewFontSize, previewFontColor;

            if (currentStateType === 'idle') {
                previewFontSize = formData.ProgramAreaAction_FontSize;
                previewFontColor = formData.ProgramAreaAction_FontColor;
            } else { // busy
                previewFontSize = formData.ProgramAreaAction_NonIdleFontSize;
                previewFontColor = formData.ProgramAreaAction_NonIdleFontColor;
            }

            // 构建预览HTML
            let previewHtml = `
                <div class="led-preview-container">
                    <div class="led-screen">
                        <div class="led-display" style="
                                color: ${previewFontColor === '65280' ? '#16b777' :
                                    previewFontColor === '65535' ? '#ffeb3b' : '#ff5722'};
                            font-size: ${Number(previewFontSize) + 2}px;
                        ">
                            ${presetContent.map(item => item.previewText).join('')}
                        </div>
                    </div>
                    <div class="led-info">
                        <div class="led-info-header">
                            <span>变量说明</span>
                            <small>注：以下为模拟数据，预览界面仅供参考</small>
                        </div>
                        <div class="led-info-content">
                            ${presetContent.map(item => `
                                <div class="led-info-item">
                                    <div class="led-info-label">${item.text}</div>
                                    <div class="led-info-value ${item.link === 2 ? 'led-info-note' : ''}">
                                        ${item.link === 2 ?
                            '<i class="layui-icon layui-icon-link"></i> 关联车道不提供预览' :
                            item.previewText || '--'}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            // 修改预览样式
            const previewStyles = `
                <style>
                    .led-preview-container {
                        height: 400px;
                        display: flex;
                        flex-direction: column;
                        background: #fff;
                        border-radius: 2px;
                        overflow: hidden;
                    }


                    .led-screen {
                        padding: 12px 15px;
                        background: linear-gradient(45deg, #000000, #141414);
                        position: relative;
                        box-shadow: inset 0 0 15px rgba(0,0,0,0.6);
                        margin: 15px 15px 0 15px;
                        border-radius: 4px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        justify-content: flex-start; /* 改为左对齐 */
                        overflow: hidden;
                        border: 1px solid #2a2a2a;
                    }

                    .led-display {
                        position: relative;
                        z-index: 1;
                        text-shadow: 0 0 8px currentColor;
                        word-break: keep-all; /* 防止文字换行 */
                        line-height: 1.4;
                        font-family: "Microsoft YaHei";
                        white-space: nowrap;
                        letter-spacing: 1px;
                        display: flex;
                        align-items: center;
                        height: 100%;
                        padding: 0 2px;
                        animation: ledScroll 15s linear infinite; /* 添加滚动动画 */
                    }

                    /* 当文字超出容器宽度时才应用动画 */
                    @@keyframes ledScroll {
                        0% {
                            transform: translateX(100%);
                        }
                        100% {
                            transform: translateX(-100%);
                        }
                    }

                    /* 添加hover时暂停动画的效果 */
                    .led-screen:hover .led-display {
                        animation-play-state: paused;
                    }

                    /* 添加一个渐变遮罩，增强边缘过渡效果 */
                    .led-screen::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background:
                            linear-gradient(90deg,
                                rgba(0,0,0,0.8) 0%,
                                transparent 5%,
                                transparent 95%,
                                rgba(0,0,0,0.8) 100%),
                            linear-gradient(90deg,
                                rgba(255,255,255,0.07) 1px,
                                transparent 1px) 0 0 / 2px 100%,
                            linear-gradient(0deg,
                                rgba(255,255,255,0.07) 1px,
                                transparent 1px) 0 0 / 100% 2px;
                        pointer-events: none;
                        z-index: 2;
                    }

                    .led-screen::after {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(
                            rgba(255,255,255,0.1),
                            transparent 3px,
                            transparent 97%,
                            rgba(255,255,255,0.1)
                        );
                        pointer-events: none;
                    }

                    .led-display {
                        position: relative;
                        z-index: 1;
                        text-shadow: 0 0 8px currentColor;
                        word-break: keep-all; /* 防止文字换行 */
                        line-height: 1.4;
                        font-family: "Microsoft YaHei";
                        white-space: nowrap;
                        letter-spacing: 1px;
                        display: flex;
                        align-items: center;
                        height: 100%;
                        padding: 0 2px;
                        animation: ledScroll 15s linear infinite; /* 添加滚动动画 */
                    }

                    /* 当文字超出容器宽度时才应用动画 */
                    @@keyframes ledScroll {
                        0% {
                            transform: translateX(100%);
                        }
                        100% {
                            transform: translateX(-100%);
                        }
                    }

                    /* 添加hover时暂停动画的效果 */
                    .led-screen:hover .led-display {
                        animation-play-state: paused;
                    }

                    .led-info {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        margin: 12px 15px 15px 15px;
                        border: 1px solid #e8e8e8;
                        border-radius: 4px;
                        background: #fafafa;
                        height: calc(100% - 95px);
                        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                    }

                    .led-info-header {
                        padding: 10px 15px;
                        border-bottom: 1px solid #e8e8e8;
                        background: #f5f5f5;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        flex-shrink: 0;
                    }

                    .led-info-header span {
                        font-size: 13px;
                        font-weight: 500;
                        color: #333;
                        position: relative;
                        padding-left: 10px;
                    }

                    .led-info-header span::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 3px;
                        height: 13px;
                        background: #1e9fff;
                        border-radius: 1px;
                    }

                    .led-info-header small {
                        font-size: 12px;
                        color: #999;
                        display: flex;
                        align-items: center;
                    }

                    .led-info-header small::before {
                        content: '\\e702';
                        font-family: layui-icon !important;
                        font-size: 14px;
                        margin-right: 4px;
                        color: #ff9800;
                    }

                    .led-info-content {
                        flex: 1;
                        overflow-y: auto;
                        padding: 2px 0;
                    }

                    .led-info-item {
                        display: flex;
                        padding: 8px 15px;
                        border-bottom: 1px solid #f0f0f0;
                        min-height: 36px;
                        align-items: center;
                        transition: background-color 0.2s;
                    }

                    .led-info-item:hover {
                        background-color: #fafafa;
                    }

                    .led-info-item:last-child {
                        border-bottom: none;
                    }

                    .led-info-label {
                        width: 45%;
                        color: #666;
                        font-size: 13px;
                        padding-right: 15px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .led-info-value {
                        flex: 1;
                        color: #333;
                        font-size: 13px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-family: Consolas, Monaco, monospace;
                    }

                    .led-info-note {
                        color: #999;
                        font-style: italic;
                        font-family: "Microsoft YaHei";
                    }

                    .led-info-note .layui-icon {
                        font-size: 14px;
                        margin-right: 4px;
                        color: #1e9fff;
                    }

                    /* 自定义滚动条样式 */
                    .led-info-content::-webkit-scrollbar {
                        width: 4px;
                        height: 4px;
                    }

                    .led-info-content::-webkit-scrollbar-thumb {
                        background: #ddd;
                        border-radius: 2px;
                    }

                    .led-info-content::-webkit-scrollbar-track {
                        background: transparent;
                    }

                    .led-info-content::-webkit-scrollbar-thumb:hover {
                        background: #ccc;
                    }

                    .layui-layer-content {
                        overflow: hidden !important;
                    }
                </style>
            `;

            // 修改弹窗配置
            layer.open({
                type: 1,
                title: false,
                area: ['460px', '400px'], // 稍微调整宽度
                content: previewStyles + previewHtml,
                shadeClose: true,
                skin: 'layui-layer-nobg',
                success: function (layero) {
                    $(layero).find('.layui-layer-content').css('overflow', 'hidden');
                }
            });
        });

        // 修改预览按钮事件中的文本显示逻辑
        function getPreviewText(value) {
            // 获取当前时间格式化字符串
            function getCurrentTimeStr() {
                let now = new Date();
                return now.getFullYear() + '-' +
                    String(now.getMonth() + 1).padStart(2, '0') + '-' +
                    String(now.getDate()).padStart(2, '0') + ' ' +
                    String(now.getHours()).padStart(2, '0') + ':' +
                    String(now.getMinutes()).padStart(2, '0')
            }

            // 基础变量映射
            const basicVars = {
                '{{TotalSpaces}}': '121',
                '{{AvailableSpaces}}': '085',
                '{{CurrentTime}}': getCurrentTimeStr(),
                '{{EntryTime}}': getCurrentTimeStr(),
                '{{ExitTime}}': getCurrentTimeStr(),
                '{{ExpireTime}}': getCurrentTimeStr(),
                '{{ParkingDuration}}': '1天5小时24分',
                '{{RemainingDays}}': '160',
                '{{PlateNumber}}': '临B12345',
                '{{PlateType}}': '临时车A',
                '{{SpaceNumber}}': 'P511',
                '{{Fee}}': '68',
                '{{Balance}}': '109'
            };

            // 获取标签的前缀后缀
            const $tag = $(`.preset-tag[data-value="${value}"]`);
            const prefix = $tag.data('prefix') || '';
            const suffix = $tag.data('suffix') || '';

            // 处理基础变量
            if (basicVars.hasOwnProperty(value)) {
                return prefix + basicVars[value] + suffix;
            }

            // 处理关联变量
            if (value.includes('AreaName:')) {
                const areaId = value.replace('{{AreaName:', '').replace('}}', '');
                const area = parent.pager.parkAreas.find(a => a.ParkArea_No.toString() === areaId);
                return suffix;
            }

            if (value.includes('AreaTotalSpaces:')) {
                return '59';
            }

            if (value.includes('AreaAvailableSpaces:')) {
                return '50';
            }

            if (value.includes('LaneName:')) {
                return ''; // 关联车道预览不显示信息
            }

            if (value.includes('CustomInfo:')) {
                return suffix;
            }

            return prefix + value + suffix;
        }

        // 添加清空按钮事件
        $('.clear-preset').on('click', function (e) {
            e.preventDefault();
            layer.confirm('确定要清空当前预设内容吗？', {
                btn: ['确定', '取消'],
                title: '提示'
            }, function (index) {
                const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
                $(containerId).empty();
                presetContents[currentStateType] = [];
                // 清空后立即保存当前预设内容（空数组）
                saveCurrentPresetContent();
                updateButtonStates();
                layer.close(index);
            });
        });

        // 添加取消按钮事件
        $('#cancelConfig').on('click', function () {
            layer.confirm('确定要取消配置吗？未保存的改动将丢失', {
                btn: ['确定', '取消']
            }, function () {
                parent.$('.dialog-mask').remove();
                parent.$('.custom-dialog').remove();
            });
        });

        // 在初始化数据后添加一个函数来更新按钮状态
        function updateButtonStates() {
            // 获取当前预设标签的总数
            const containerId = currentStateType === 'idle' ? '#presetContentIdle' : '#presetContentBusy';
            let totalTags = $(containerId + ' .preset-tag').length;

            // 定义空闲时需要禁用的变量
            const idleDisabledVars = [
                '{{EntryTime}}',
                '{{ExitTime}}',
                '{{ExpireTime}}',
                '{{ParkingDuration}}',
                '{{RemainingDays}}',
                '{{PlateNumber}}',
                '{{SpaceNumber}}',
                '{{Fee}}',
                '{{Balance}}',
                '{{PlateType}}'
            ];

            // 如果总数已经达到32个，禁用所有未禁用的按钮
            if (totalTags >= 32) {
                $('.vars-group:not(:first-child) .layui-btn').each(function () {
                    if (!$(this).hasClass('layui-btn-disabled')) {
                        $(this).addClass('layui-btn-disabled').prop('disabled', true);
                    }
                });
                return; // 直接返回，不需要继续检查其他条件
            }

            // 获取当前所有预设标签的值
            let usedVars = new Set();
            $(containerId + ' .preset-tag').each(function () {
                let value = $(this).data('value');
                // 对于可以重复的变量，不需要加入检查集合
                if (!value.includes('AreaName:') &&
                    !value.includes('AreaTotalSpaces:') &&
                    !value.includes('AreaAvailableSpaces:') &&
                    !value.includes('CustomInfo:') &&
                    !value.includes('LaneName:')) {
                    // 对于其他变量，使用基础变量名（不包含具体值）
                    usedVars.add(value);
                }
            });

            // 更新所有变量按钮的状态
            $('.vars-group:not(:first-child) .layui-btn').each(function () {
                let varValue = $(this).data('var');
                // 检查是否是可重复的变量
                let isRepeatable = varValue === '{{AreaName}}' ||
                    varValue === '{{AreaTotalSpaces}}' ||
                    varValue === '{{AreaAvailableSpaces}}' ||
                    varValue === '{{CustomInfo}}' ||
                    varValue === '{{LaneName}}';

                // 检查是否应该禁用（基于使用状态和当前模式）
                let shouldDisable = false;

                // 在空闲模式下检查是否是需要禁用的变量
                if (currentStateType === 'idle' && idleDisabledVars.includes(varValue)) {
                    shouldDisable = true;
                } else if (!isRepeatable && usedVars.has(varValue)) {
                    shouldDisable = true;
                }

                // 应用禁用/启用状态
                if (shouldDisable) {
                    $(this).addClass('layui-btn-disabled').prop('disabled', true);
                } else {
                    $(this).removeClass('layui-btn-disabled').prop('disabled', false);
                }
            });

            // 初始化排序
            initSortable();
        }

        // 在初始化数据完成后调用
        initializeData();
        // 确保显示空闲状态的预设内容
        showPresetContent('idle');
        updateButtonStates();

        // 添加显示特效的监听事件
        form.on('select(displayEffect)', function (data) {
            // 获取选中的值
            let effectValue = data.value;

            // 如果是连续移动特效(6-9),则禁用显示时间
            if (['6', '7', '8', '9'].includes(effectValue)) {
                $('input[name="ProgramAreaAction_DisplayTime"]').attr('disabled', true);
                $('input[name="ProgramAreaAction_DisplayTime"]').closest('.layui-form-item').addClass('layui-disabled');
            } else {
                $('input[name="ProgramAreaAction_DisplayTime"]').attr('disabled', false);
                $('input[name="ProgramAreaAction_DisplayTime"]').closest('.layui-form-item').removeClass('layui-disabled');
            }

            // 重新渲染表单
            form.render();
        });

        // 添加非空闲状态显示特效的监听事件
        form.on('select(nonIdleDisplayEffect)', function (data) {
            // 获取选中的值
            let effectValue = data.value;
            const $displayTimeInput = $('input[name="ProgramAreaAction_NonIdleDisplayTime"]');
            const $formItem = $displayTimeInput.closest('.layui-form-item');

            // 如果是连续移动特效(6-9),则禁用显示时间
            if (['6', '7', '8', '9'].includes(effectValue)) {
                $displayTimeInput.attr('disabled', true);
                $formItem.addClass('layui-disabled');
            } else {
                $displayTimeInput.attr('disabled', false);
                $formItem.removeClass('layui-disabled');
            }
            // 重新渲染表单以应用更改
            form.render();
        });

        // 修改自定义信息对话框处理方法
        function handleCustomInfoDialog(colorClass, currentText = '', isEdit = false, $existingTag = null) {
            layer.open({
                type: 1,
                title: isEdit ? '编辑自定义信息' : '添加自定义信息',
                area: ['460px', '260px'],
                resize: false,
                content: `
                    <div class="tag-editor">
                        <div class="form-section">
                            <div class="input-group">
                                <label class="input-label">
                                    <span class="label-text">自定义内容</span>
                                    <div class="input-wrapper">
                                        <input type="text" id="customInfoInput"
                                               value="${currentText}"
                                               placeholder="请输入自定义内容"
                                               maxlength="32"
                                               autocomplete="off">
                                        <span class="input-counter">0/32</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    <style>
                        .tag-editor {
                            padding: 32px;
                        }

                        .form-section {
                            display: flex;
                            flex-direction: column;
                            gap: 24px;
                        }

                        .input-group {
                            position: relative;
                        }

                        .input-label {
                            display: block;
                        }

                        .label-text {
                            display: block;
                            font-size: 13px;
                            font-weight: normal;
                            color: #374151;
                            margin-bottom: 8px;
                        }

                        .input-wrapper {
                            position: relative;
                        }

                        .input-wrapper input {
                            width: 100%;
                            height: 36px;
                            padding: 0 12px;
                            font-size: 13px;
                            border: 1px solid #d1d5db;
                            border-radius: 6px;
                            transition: all 0.2s ease;
                        }

                        .input-wrapper input:hover {
                            border-color: #9ca3af;
                        }

                        .input-wrapper input:focus {
                            border-color: #3b82f6;
                            box-shadow: 0 0 0 3px rgba(59,130,246,0.1);
                            outline: none;
                        }

                        .input-counter {
                            position: absolute;
                            right: 12px;
                            top: 50%;
                            transform: translateY(-50%);
                            font-size: 12px;
                            color: #6b7280;
                            background: #fff;
                            padding: 0 4px;
                        }

                        .layui-layer-btn {
                            padding: 16px 32px !important;
                            border-top: 1px solid #e5e7eb;
                            display: flex;
                            justify-content: flex-end;
                            gap: 12px;
                        }

                        .layui-layer-btn a {
                            height: 32px !important;
                            line-height: 32px !important;
                            padding: 0 24px !important;
                            border-radius: 6px !important;
                            transition: all 0.2s ease !important;
                        }
                    </style>
                `,
                btn: ['确定', '取消'],
                btnAlign: 'r',
                success: function (layero) {
                    const $input = layero.find('#customInfoInput');

                    // 更新计数器函数
                    function updateCounter() {
                        const len = $input.val().length;
                        $input.siblings('.input-counter')
                            .text(`${len}/32`)
                            .css('color', len >= 28 ? '#ef4444' : '#6b7280');
                    }

                    // 初始化计数器
                    updateCounter();

                    // 监听输入事件
                    $input.on('input', updateCounter);

                    // 设置初始焦点
                    setTimeout(() => $input.focus(), 100);
                },
                yes: function (index, layero) {
                    const value = layero.find('#customInfoInput').val();

                    if (!value) {
                        layer.msg('请输入自定义信息', { icon: 2 });
                        return;
                    }

                    if (isEdit && $existingTag) {
                        // 编辑模式：更新现有标签
                        $existingTag.data('value', `{{CustomInfo:${value}}}`);
                        $existingTag.empty().append(`
                            <div class="tag-content">
                                <span class="tag-prefix"></span>
                                <span class="tag-main">自定义信息</span>
                                <span class="tag-suffix">${value}</span>
                            </div>
                            <span class="tag-close">×</span>
                        `);
                        // 编辑后立即保存当前预设内容
                        saveCurrentPresetContent();
                    } else {
                        // 新增模式：创建新标签
                        let $tag = $(`
                            <div class="preset-tag ${colorClass}" data-value="{{CustomInfo:${value}}}" data-suffix="${value}">
                                <div class="tag-content">
                                    <span class="tag-prefix"></span>
                                    <span class="tag-main">自定义信息</span>
                                    <span class="tag-suffix">${value}</span>
                                </div>
                                <span class="tag-close">×</span>
                            </div>
                        `);
                        addNewTag($tag);
                    }

                    updateButtonStates();
                    layer.close(index);
                }
            });
        }

        // 修改车道选择对话框处理方法
        function handleLaneSelectionDialog(colorClass, text, isEdit = false, $existingTag = null) {
            if (!parent.pager.passways?.length) {
                layer.msg('未获取到车道数据');
                return;
            }

            const currentLaneId = isEdit ? $existingTag.data('value').split(':')[1].replace('}}', '') : null;

            // 对车道数据进行去重处理，基于 Passway_No 进行去重
            const uniqueLanes = parent.pager.passways.filter((lane, index, self) =>
                index === self.findIndex(l => l.Passway_No === lane.Passway_No)
            );

            const content = `
                <div style="padding: 15px;">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>车道名称</th>
                                <th>车道类型</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${uniqueLanes.map(lane => {
                const isUsed = getUsedIds('LaneName').has(lane.Passway_No.toString());
                // 编辑时，当前选中的车道不应该显示为已使用
                const isCurrentLane = currentLaneId && lane.Passway_No.toString() === currentLaneId;
                return `
                                    <tr data-id="${lane.Passway_No}"
                                        data-name="${lane.Passway_Name}"
                                        class="${(!isCurrentLane && isUsed) ? 'disabled-row' : ''}">
                                    <td>${lane.Passway_Name || '未命名'}</td>
                                    <td>${(() => {
                                        // 查找该车道关联的区域信息
                                        let areaInfo = [];
                                        if (parent.pager.arealinkpassway && parent.pager.arealinkpassway.length > 0) {
                                            const links = parent.pager.arealinkpassway.filter(link =>
                                                link.PasswayLink_PasswayNo === lane.Passway_No
                                            );

                                            links.forEach(link => {
                                                const area = parent.pager.parkAreas.find(a =>
                                                    a.ParkArea_No === link.PasswayLink_ParkAreaNo
                                                );
                                                if (area) {
                                                    const gateType = link.PasswayLink_GateType == 0 ?
                                                        '<span style="color:green;">[出]</span>' :
                                                        '<span style="color:blue;">[入]</span>';
                                                    areaInfo.push(gateType + area.ParkArea_Name);
                                                }
                                            });
                                        }

                                        return areaInfo.length > 0 ? areaInfo.join('、') : (lane.PasswayLink_GateType === 1 ? '入口' : '出口');
                                    })()}</td>
                                    <td>${(!isCurrentLane && isUsed) ? '<span class="used-tag">已添加</span>' :
                        (isCurrentLane ? '<span class="used-tag" style="color:#1e9fff">当前选中</span>' : '')}</td>
                                </tr>
                            `;
            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            layer.open({
                type: 1,
                title: isEdit ? '修改关联车道' : '选择关联车道',
                area: ['500px', '400px'],
                content: content,
                success: function (layero) {
                    $(layero).find('tr[data-id]').on('click', function () {
                        if ($(this).hasClass('disabled-row')) {
                            layer.msg('该车道已添加，请选择其他车道');
                            return;
                        }

                        const laneId = $(this).data('id');
                        const laneName = $(this).data('name');
                        const laneType = $(this).find('td:eq(1)').text(); // 获取车道类型

                        if (isEdit && $existingTag) {
                            $existingTag.data('value', `{{LaneName:${laneId}}}`);
                            const tagContent = `
                                <div class="tag-content">
                                    <span class="tag-prefix"></span>
                                    <span class="tag-main">车道名称</span>
                                    <span class="tag-suffix">${laneName}</span>
                                </div>
                            `;
                            $existingTag.find('.tag-content').remove();
                            $existingTag.prepend(tagContent);
                            // 编辑后立即保存当前预设内容
                            saveCurrentPresetContent();
                        } else {
                            let $tag = $(`
                                <div class="preset-tag ${colorClass}" data-value="{{LaneName:${laneId}}}" data-link="2" data-suffix="${laneName}">
                                    <div class="tag-content">
                                        <span class="tag-prefix"></span>
                                        <span class="tag-main">车道名称</span>
                                        <span class="tag-suffix">${laneName}</span>
                                    </div>
                                    <span class="tag-close">×</span>
                                </div>
                            `);
                            addNewTag($tag);
                        }
                        updateButtonStates();
                        layer.closeAll();
                    });
                }
            });
        }

        // 修改区域选择对话框处理方法
        function handleAreaSelectionDialog(value, colorClass, text, isEdit = false, $existingTag = null) {
            if (!parent.pager.parkAreas?.length) {
                layer.msg('未获取到区域数据');
                return;
            }

            const varType = value.replace('{{', '').replace('}}', '');
            const currentAreaId = isEdit ? $existingTag.data('value').split(':')[1].replace('}}', '') : null;

            const content = `
                <div style="padding: 15px;">
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>区域名称</th>
                                <th>车位数量</th>
                                <th>区域类型</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${parent.pager.parkAreas.map(area => {
                const isUsed = getUsedIds(varType).has(area.ParkArea_No.toString());
                const isCurrentArea = currentAreaId && area.ParkArea_No.toString() === currentAreaId;
                return `
                                    <tr data-id="${area.ParkArea_No}"
                                        data-name="${area.ParkArea_Name}"
                                        data-spaces="${area.ParkArea_SpaceNum}"
                                        class="${(!isCurrentArea && isUsed) ? 'disabled-row' : ''}">
                                    <td>${area.ParkArea_Name || '未命名'}</td>
                                    <td>${area.ParkArea_SpaceNum || 0}</td>
                                    <td>${area.ParkArea_Type === 1 ? '内场' : '外场'}</td>
                                    <td>${(!isCurrentArea && isUsed) ? '<span class="used-tag">已添加</span>' :
                        (isCurrentArea ? '<span class="used-tag" style="color:#1e9fff">当前选中</span>' : '')}</td>
                                </tr>
                            `;
            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            layer.open({
                type: 1,
                title: isEdit ? '修改区域' : '选择区域',
                area: ['500px', '400px'],
                content: content,
                success: function (layero) {
                    $(layero).find('tr[data-id]').on('click', function () {
                        if ($(this).hasClass('disabled-row')) {
                            layer.msg('该区域已添加，请选择其他区域');
                            return;
                        }

                        const areaId = $(this).data('id');
                        const areaName = $(this).data('name');
                        const varType = value.replace('{{', '').replace('}}', '');

                        // 根据变量类型设置前缀和后缀
                        let displayText = '';
                        switch (varType) {
                            case 'AreaName':
                                displayText = '区域名称';
                                break;
                            case 'AreaTotalSpaces':
                                displayText = '区域总车位';
                                break;
                            case 'AreaAvailableSpaces':
                                displayText = '区域剩余车位';
                                break;
                        }

                        let tagValue = `{{${varType}:${areaId}}}`;

                        if (isEdit && $existingTag) {
                            $existingTag.data('value', tagValue);
                            const tagContent = `
                                <div class="tag-content">
                                    <span class="tag-prefix"></span>
                                    <span class="tag-main">${displayText}</span>
                                    <span class="tag-suffix">${areaName}</span>
                                </div>
                            `;
                            $existingTag.find('.tag-content').remove();
                            $existingTag.prepend(tagContent);
                            // 编辑后立即保存当前预设内容
                            saveCurrentPresetContent();
                        } else {
                            let $tag = $(`
                                <div class="preset-tag ${colorClass}" data-value="${tagValue}" data-link="1" data-suffix="${areaName}">
                                    <div class="tag-content">
                                        <span class="tag-prefix"></span>
                                        <span class="tag-main">${displayText}</span>
                                        <span class="tag-suffix">${areaName}</span>
                                    </div>
                                    <span class="tag-close">×</span>
                                </div>
                            `);
                            addNewTag($tag);
                        }
                        updateButtonStates();
                        layer.closeAll();
                    });
                }
            });
        }

        // 修改扩展属性对话框处理方法
        function handleExtendedPropertiesDialog($tag) {
            const prefix = $tag.data('prefix') || '';
            const suffix = $tag.data('suffix') || '';
            const tagText = $tag.find('.tag-main').text();
            layer.open({
                type: 1,
                title: '编辑标签属性',
                area: ['460px', '350px'],
                content: `
                    <div class="tag-editor">
                        <div class="form-section">
                            <div class="input-group">
                                <label class="input-label">
                                    <span class="label-text">前缀内容</span>
                                    <div class="input-wrapper">
                                        <input type="text" id="prefixInput" value="${prefix}"
                                               placeholder="请输入前缀内容" maxlength="16"
                                               autocomplete="off">
                                        <span class="input-counter">0/16</span>
                                    </div>
                                </label>
                            </div>

                            <div class="input-group">
                                <label class="input-label">
                                    <span class="label-text">后缀内容</span>
                                    <div class="input-wrapper">
                                        <input type="text" id="suffixInput" value="${suffix}"
                                               placeholder="请输入后缀内容" maxlength="16"
                                               autocomplete="off">
                                        <span class="input-counter">0/16</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    <style>
                        .tag-editor {
                            padding: 32px;
                        }

                        .form-section {
                            display: flex;
                            flex-direction: column;
                            gap: 24px;
                        }

                        .input-group {
                            position: relative;
                        }

                        .input-label {
                            display: block;
                        }

                        .label-text {
                            display: block;
                            font-size: 13px;
                            font-weight: normal;
                            color: #374151;
                            margin-bottom: 8px;
                        }

                        .input-wrapper {
                            position: relative;
                        }

                        .input-wrapper input {
                            width: 100%;
                            height: 36px;
                            padding: 0 12px;
                            font-size: 13px;
                            border: 1px solid #d1d5db;
                            border-radius: 6px;
                            transition: all 0.2s ease;
                        }

                        .input-wrapper input:hover {
                            border-color: #9ca3af;
                        }

                        .input-wrapper input:focus {
                            border-color: #3b82f6;
                            box-shadow: 0 0 0 3px rgba(59,130,246,0.1);
                            outline: none;
                        }

                        .input-counter {
                            position: absolute;
                            right: 12px;
                            top: 50%;
                            transform: translateY(-50%);
                            font-size: 12px;
                            color: #6b7280;
                            background: #fff;
                            padding: 0 4px;
                        }

                        .layui-layer-btn {
                            padding: 16px 32px !important;
                            border-top: 1px solid #e5e7eb;
                            display: flex;
                            justify-content: flex-end;
                            gap: 12px;
                        }

                        .layui-layer-btn a {
                            height: 32px !important;
                            line-height: 32px !important;
                            padding: 0 24px !important;
                            border-radius: 6px !important;
                            transition: all 0.2s ease !important;
                        }
                    </style>
                `,
                btn: ['确定', '取消'],
                btnAlign: 'r',
                success: function (layero) {
                    const $prefixInput = layero.find('#prefixInput');
                    const $suffixInput = layero.find('#suffixInput');

                    function updateCounter($input) {
                        const len = $input.val().length;
                        $input.siblings('.input-counter')
                            .text(`${len}/16`)
                            .css('color', len >= 14 ? '#ef4444' : '#6b7280');
                    }

                    [$prefixInput, $suffixInput].forEach($input => {
                        updateCounter($input);
                        $input.on('input', function () {
                            updateCounter($(this));
                        });
                    });

                    setTimeout(() => $prefixInput.focus(), 100);
                },
                yes: function (index, layero) {
                    const newPrefix = layero.find('#prefixInput').val();
                    const newSuffix = layero.find('#suffixInput').val();

                    $tag.data('prefix', newPrefix);
                    $tag.data('suffix', newSuffix);

                    // 更新标签内容结构
                    const originalText = $tag.find('.tag-main').text()

                    const tagContent = `
                        <div class="tag-content">
                            ${newPrefix ? `<span class="tag-prefix">${newPrefix}</span>` : ''}
                            <span class="tag-main">${originalText}</span>
                            ${newSuffix ? `<span class="tag-suffix">${newSuffix}</span>` : ''}
                        </div>
                    `;

                    $tag.find('.tag-content').remove();
                    $tag.prepend(tagContent);

                    // 编辑扩展属性后立即保存当前预设内容
                    saveCurrentPresetContent();
                    layer.close(index);
                }
            });
        }

        // 定义需要显示扩展属性的变量列表
        const extendedVars = [
            '{{TotalSpaces}}',
            '{{AvailableSpaces}}',
            '{{CurrentTime}}',
            '{{EntryTime}}',
            '{{ExitTime}}',
            '{{ExpireTime}}',
            '{{ParkingDuration}}',
            '{{RemainingDays}}',
            '{{PlateNumber}}',
            '{{PlateType}}',
            '{{SpaceNumber}}',
            '{{Fee}}',
            '{{Balance}}'
        ];
    });
</script>
