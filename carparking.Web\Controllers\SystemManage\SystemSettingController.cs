﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Common;
using carparking.Library;
using carparking.Config;
using carparking.Model;
using carparking.Model.API;
using carparking.SentryBox.Util;
using carparking.Web.OpenApiV2;
using carparking.Web.OpenApiV2.CallbackTask;
using carparking.Web.OpenApiV2.Other;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using TcpConnPools;
using SysConfig = carparking.Model.SysConfig;
using SystemLogs = carparking.BLL.SystemLogs;
using UserLogs = carparking.BLL.UserLogs;

namespace carparking.Web.Controllers.Sys;

public class SystemSettingController : BaseController
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Microsoft.Extensions.Hosting.IHostEnvironment _hostingEnvironment;

    public SystemSettingController(Microsoft.Extensions.Hosting.IHostEnvironment hostingEnvironment)

    {
        _hostingEnvironment = hostingEnvironment;
    }

    //public SystemSettingController(IServiceProvider serviceProvider)
    //{
    //    _serviceProvider = serviceProvider;
    //}

    public IActionResult Index()
    {
        if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.View.ToString(), false, true, lgAdmins))
            return new EmptyResult();

        ViewBag.SentryPath = ViewBag.Path = ImageTools.LocalFilePath;
        //try
        //{
        //    var model = BaseBLL._GetEntityByWhere(new SysConfig(), "*", $"SysConfig_ParkNo='{parking.Parking_No}'");
        //    if (model != null)
        //    {
        //        var content = SysConfigContent.GetIntance(model);
        //        //if (content != null && content.SysConfig_EnableImgPath == 1 && !string.IsNullOrEmpty(content.SysConfig_SentryImagePath))
        //        //{
        //        //    ViewBag.SentryPath = HttpUtility.UrlDecode(content.SysConfig_SentryImagePath);
        //        //}
        //    }
        //}
        //catch
        //{
        //    // ignored
        //}

        var path1 = AppDomain.CurrentDomain.BaseDirectory;
        if (path1 != null && path1.Contains("Web"))
        {
            path1 = path1.Substring(0, path1.LastIndexOf("Web", StringComparison.Ordinal)) + "DBBackUp";
        }
        else
        {
            path1 = path1 + "DBBackUp";
        }

        if (!AppBasicCache.IsWindows) path1 = "/mnt/sda2/b30/backup";

        ViewBag.InitPath = path1;

        ViewBag.IsWindows = AppBasicCache.IsWindows;

        return View();
    }

    /// <summary>
    /// 第三方相机授权
    /// </summary>
    /// <returns>判断是否有权限访问该页面</returns>
    public IActionResult DeviceAuth()
    {
        if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.View.ToString(), false, true, lgAdmins))
            return new EmptyResult();

        return View();
    }

    /// <summary>
    /// 读取授权列表
    /// </summary>
    /// <returns></returns>
    public IActionResult GetDeviceAuthList()
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.View.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");


            var auths = BLL.BaseBLL._GetAllEntity(new Model.DeviceAuth(), "*", "");

            //var drives = BLL.Drive._GetAllEntity(new Model.Drive(), "*", "Drive_Category=1");

            List<string> drives = new() { "S1215", "S1211", "S1213", "S12YS", "S12HK", "S12DH" };

            return ResOk(true, "读取列表", new { auths = auths, drives = drives });
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(lgAdmins, "[设备授权]读取授权列表", ex.ToString());
            return ResOk(false, ex.Message);
        }
    }

    public IActionResult EnterPwd(string Admins_Pwd)
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Save.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");

            Admins_Pwd = Utils.MD5Encrypt(Admins_Pwd + Utils.passwordMD5String, Encoding.UTF8);
            string account = lgAdmins.Admins_Account;
            Model.Admins admin = BLL.Admins.GetEntity("*", account, Admins_Pwd);
            if (admin == null)
            {
                return ResOk(false, "校验失败");
            }

            return ResOk(true, "校验成功");
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "[EnterPwd]检验密码异常：" + ex.ToString());
            return ResOk(false, "检验密码异常:" + ex.Message);
        }
    }

    public async void Upload()
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Save.ToString(), false, true, lgAdmins))
            { MiniResponse.ResponseResult("无权限", false); return; }

            //解析导入的Excel文件
            var postForm = Request.Form;

            IFormFileCollection formFiles = Request.Form.Files;
            if (!Utils.IsOnlyTextFiles(formFiles))
            {
                MiniResponse.ResponseResult($"导入失败，只允许上传文本文件 。", false, null, "-1"); return;
            }
            long size = formFiles.Sum(f => f.Length);

            string webRootPath = _hostingEnvironment.ContentRootPath;
            string contentRootPath = _hostingEnvironment.ContentRootPath;
            StringBuilder result = new StringBuilder();

            using (var stream = new MemoryStream())
            {
                // 将文件流复制到内存流
                await formFiles[0].CopyToAsync(stream);

                // 重置流的位置到开头
                stream.Position = 0;

                using (var reader = new StreamReader(stream))
                {
                    // 读取流并将内容添加到结果中
                    string content = await reader.ReadToEndAsync();
                    result.AppendLine(content); // 每个文件内容换行
                }
            }

            if (result.ToString().Length == 0)
            {
                MiniResponse.ResponseResult($"请选择有效文件，内容不能为空！", false);
                return;
            }

            var resultStr = AESHelper.AesDecrypt(result.ToString(), "jAKSMXWzPPfwZFj1", "QmgU18iFkPQCMia6");
            if (!string.IsNullOrEmpty(resultStr))
            {
                var authList = TyziTools.Json.ToObject<List<Model.DeviceAuth>>(resultStr);
                if (authList == null || authList.Count == 0)
                {
                    MiniResponse.ResponseResult($"解密错误，授权信息导入失败！", false);
                    return;
                }

                var res = BaseBLL._Insert(authList);
                if (res >= 0)
                {
                    AppBasicCache.ClearElement(DevicePool.Instance.DeviceAuths);
                    authList.ForEach(x =>
                    {
                        AppBasicCache.AddOrUpdateElement(DevicePool.Instance.DeviceAuths, x.DeviceAuth_Key, x.DeviceAuth_Type + "," + x.DeviceAuth_Secret);

                    });

                    MiniResponse.ResponseResult($"授权信息导入成功！", true, null, "0");
                    return;
                }
                else
                {
                    MiniResponse.ResponseResult($"数据处理失败，请重试！", false);
                    return;
                }
            }
            else
            {
                MiniResponse.ResponseResult($"解密错误，授权信息导入失败！", false);
                return;
            }
        }
        catch (Exception ex)
        {
            BLL.SystemLogs.AddLog(lgAdmins, "授权信息导入", $"授权信息导入异常:{ex.ToString()}");
            MiniResponse.ResponseResult("授权信息导入异常", false);
            return;
        }
    }

    public IActionResult SaveDeviceAuth(string jsonModel)
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Save.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");

            var lst = TyziTools.Json.ToObject<List<Model.DeviceAuth>>(jsonModel);

            if (lst == null || lst.Count == 0)
            {
                var clearRet = BaseBLL._ExceteBySql("TRUNCATE TABLE deviceauth;");
                if (clearRet >= 0)
                {
                    return ResOk(true, "保存成功");
                }
            }

            List<string> sqlList = new List<string>();
            sqlList.Add("TRUNCATE TABLE deviceauth");
            lst.ForEach(x =>
            {

                sqlList.Add(BaseBLL._GetAddSql(x));
            });

            var res = BaseBLL._ExecuteTrans(sqlList);
            if (res >= 0)
            {
                AppBasicCache.ClearElement(DevicePool.Instance.DeviceAuths);
                lst.ForEach(x =>
                {
                    AppBasicCache.AddOrUpdateElement(DevicePool.Instance.DeviceAuths, x.DeviceAuth_Key, x.DeviceAuth_Type + "," + x.DeviceAuth_Secret);
                });

                return ResOk(true, "保存成功");
            }

            return ResOk(false, "保存失败");
        }
        catch (Exception ex)
        {
            return ResOk(false, ex.Message);
        }
    }

    /// <summary>
    /// 编辑回调服务信息
    /// </summary>
    /// <returns>判断是否有权限访问该页面</returns>
    public IActionResult EditCallBack()
    {
        if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.View.ToString(), false, true, lgAdmins))
            return new EmptyResult();

        return View();
    }

    public IActionResult GetSysConfig()
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Search.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");

            var model = BaseBLL._GetEntityByWhere(new SysConfig(), "*", $"SysConfig_ParkNo='{parking.Parking_No}'");

            if (model != null)
            {
                model.SysConfig_Content = BLL.SysConfig.GetUrlDecode(model.SysConfig_Content);
                return ResOk(true, "读取配置成功", model);
            }

            return ResOk(false, "读取配置失败");
        }
        catch (Exception ex)
        {
            return ResOk(false, ex.Message);
        }
    }

    /// <summary>
    /// 上传图片
    /// </summary>
    [HttpPost]
    public IActionResult UploadImage()
    {
        if (!Powermanage.AdminCheck(lgAdmins))
        {
            return ResOk(false, "无权限");
        }


        if (HttpContext.Request.Form.Files.Count > 0)
        {
            try
            {
                var files = HttpContext.Request.Form.Files;
                if (!Utils.IsOnlyImageFiles(files))
                {
                    return ResOk(false, "只允许上传图片");
                }
                //得到客户端上传的文件
                var file = files[0];
                var fileName = file.FileName;

                if (file.Length == 0)
                {
                    return ResOk(false, "图片上传保存失败：上传的文件长度为0");
                }

                //文件标识
                var FileId = Convert.ToString(HttpContext.Request.Form["FileId"]);
                if (string.IsNullOrEmpty(FileId))
                {
                    return ResOk(false, "图片上传保存失败：未能获取到文件标识");
                }

                var sysModel = BLL.SysConfig.GetEntity(parking.Parking_No);
                if (sysModel == null)
                {
                    return ResOk(false, "图片上传保存失败：未能查询到停车场配置");
                }

                var content = SysConfigContent.GetIntance(sysModel);
                if (content == null)
                {
                    return ResOk(false, "图片上传保存失败：未能查询到停车场配置");
                }

                var filePath = Path.Combine(AppBasicCache.strImgpath, DateTime.Now.ToString("yyyyMM"), DateTime.Now.ToString("dd"), fileName);
                if (!Directory.Exists(Path.GetDirectoryName(filePath)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(filePath));
                }

                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                }

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                string ymdPath = $"{DateTimeHelper.GetNowTime().ToString("yyyyMM")}/{DateTimeHelper.GetNowTime().ToString("dd")}";
                var path = $"http://{AppBasicCache.Ip}:{AppSettingConfig.SiteDomain_WebPort}/CameraCaptures/{ymdPath}/{fileName}";

                //var base64 = Convert.ToBase64String(System.IO.File.ReadAllBytes(filePath));
                //var url = PassHelperBiz.ImageSaveByBase64(sysModel.SysConfig_ParkNo, base64, AppBasicCache.strImgpath, "", fileName);
                return ResOk(true, "图片上传保存成功", path);
            }
            catch (Exception ex)
            {
                return ResOk(false, "图片上传保存失败：" + ex.Message);
            }
        }

        return ResOk(false, "图片上传保存失败：未能获取到上传的文件");
    }

    public IActionResult SaveSysConfig(string jsonModel)
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Save.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");

            var model = TyziTools.Json.ToObject<SysConfig>(jsonModel);

            if (model == null)
            {
                return ResOk(false, "保存失败：未能获取到停车场配置");
            }

            model.SysConfig_LastUpdateTime = DateTimeHelper.GetNowTime();

            var old = AppBasicCache.CurrentSysConfig;
            if (old == null)
            {
                return ResOk(false, "保存失败：未能查询到停车场配置");
            }

            var oldContent = AppBasicCache.CurrentSysConfigContent;
            if (oldContent == null)
            {
                return ResOk(false, "保存失败：未能查询到停车场配置");
            }

            var content = TyziTools.Json.ToObject<SysConfigContent>(model.SysConfig_Content);

            var isUpdateImgPath = false;
            if (content != null)
            {
                if (!string.IsNullOrEmpty(content.SysConfig_DIYLogo) && !content.SysConfig_DIYLogo.Contains("http://") && !content.SysConfig_DIYLogo.Contains("https://"))
                {
                    content.SysConfig_DIYLogo = LPRTools.SaveSystemPicture(content.SysConfig_DIYLogo, AppBasicCache.strImgpath);
                }

                if (!string.IsNullOrEmpty(content.SysConfig_DIYBackImage) && !content.SysConfig_DIYBackImage.Contains("http://") && !content.SysConfig_DIYBackImage.Contains("https://"))
                {
                    content.SysConfig_DIYBackImage = LPRTools.SaveSystemPicture(content.SysConfig_DIYBackImage, AppBasicCache.strImgpath);
                }

                var rgx = @"^[a-zA-Z]:(((\\(?! )[^/:*?<>\""|\\]+)+\\?)|(\\)?)\s*$";
                if (!AppBasicCache.IsWindows)
                {
                    rgx = @"^\/(\w+\/?)+$";
                }

                if (content.SysConfig_EnableImgPath == 1 && string.IsNullOrWhiteSpace(content.SysConfig_ImagePath))
                {
                    content.SysConfig_ImagePath = ImageTools.LocalFilePath;
                }
                if ((content.SysConfig_DBBack == 1 || content.SysConfig_DBBack == 2) && string.IsNullOrWhiteSpace(content.SysConfig_DBBackPath))
                {
                    var path1 = AppDomain.CurrentDomain.BaseDirectory;
                    if (path1 != null && path1.Contains("Web"))
                    {
                        path1 = path1.Substring(0, path1.LastIndexOf("Web", StringComparison.Ordinal)) + "DBBackUp";
                    }
                    else
                    {
                        path1 = path1 + "DBBackUp";
                    }

                    if (!AppBasicCache.IsWindows) path1 = "/mnt/sda2/b30/backup";
                    content.SysConfig_DBBackPath = path1;
                }


                if (!string.IsNullOrEmpty(content.SysConfig_ImagePath))
                {
                    if (Regex.Match(content.SysConfig_ImagePath, rgx).Success)
                    {
                        if (!CreateDirectory(content.SysConfig_ImagePath))
                        {
                            return ResOk(false, "自定义停车场图片存储地址创建失败，请检查");
                        }

                        #region 目录安全检查
                        string fullPath = Path.GetFullPath(content.SysConfig_ImagePath).TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar); // 获取完整路径
                        string rootPath = Path.GetFullPath(AppDomain.CurrentDomain.BaseDirectory).TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar); // 获取系统安装目录
                        string sysPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..")).TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);

                        // 确保路径不等于根目录，并且不是根目录的父级目录
                        if (string.Equals(fullPath, sysPath, StringComparison.OrdinalIgnoreCase))
                        {
                            return ResOk(false, "自定义停车场图片存储地址不能是系统安装根目录");
                        }
                        if (string.Equals(fullPath, rootPath, StringComparison.OrdinalIgnoreCase))
                        {
                            return ResOk(false, "自定义停车场图片存储地址不能是系统安装根目录 Web 或其子目录");
                        }
                        if (fullPath.StartsWith(rootPath, StringComparison.OrdinalIgnoreCase))
                        {
                            return ResOk(false, "自定义停车场图片存储地址不能是系统安装根目录 Web 或其子目录");
                        }
                        // 不允许的目录列表
                        string[] forbiddenDirs = { "WorkerService", "Sdk", "ManageCenter", "Local", "Config", "CityService", "Tools" };
                        // 检查是否是受限目录或其子目录
                        foreach (var dir in forbiddenDirs)
                        {
                            string forbiddenPath = Path.GetFullPath(Path.Combine(rootPath, "..", dir)); // 构造完整路径
                            if (fullPath.StartsWith(forbiddenPath, StringComparison.OrdinalIgnoreCase))
                            {
                                return ResOk(false, $"自定义停车场图片存储地址不能设置在 {dir} 目录或其子目录");
                            }
                        }
                        #endregion

                        content.SysConfig_ImagePath = HttpUtility.UrlEncode(content.SysConfig_ImagePath);
                        isUpdateImgPath = content.SysConfig_ImagePath != oldContent.SysConfig_ImagePath;
                    }
                    else
                    {
                        return ResOk(false, "自定义停车场图片存储地址格式错误");
                    }
                }

                if (!string.IsNullOrEmpty(content.SysConfig_DBBackPath))
                {
                    if (Regex.Match(content.SysConfig_DBBackPath, rgx).Success)
                    {

                        #region 目录安全检查
                        string fullPath = Path.GetFullPath(content.SysConfig_DBBackPath).TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar); // 获取完整路径
                        string rootPath = Path.GetFullPath(AppDomain.CurrentDomain.BaseDirectory).TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar); // 获取系统安装目录
                        string sysPath = Path.GetFullPath(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..")).TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);

                        // 确保路径不等于根目录，并且不是根目录的父级目录
                        if (string.Equals(fullPath, sysPath, StringComparison.OrdinalIgnoreCase))
                        {
                            return ResOk(false, "备份数据库文件存储路径不能是系统安装根目录");
                        }

                        if (string.Equals(fullPath, rootPath, StringComparison.OrdinalIgnoreCase))
                        {
                            return ResOk(false, "备份数据库文件存储路径不能是系统安装根目录 Web ");
                        }
                        if (fullPath.StartsWith(rootPath, StringComparison.OrdinalIgnoreCase))
                        {
                            return ResOk(false, "备份数据库文件存储路径不能是系统安装根目录 Web 或其子目录");
                        }
                        // 不允许的目录列表
                        string[] forbiddenDirs = { "WorkerService", "Sdk", "ManageCenter", "Local", "Config", "CityService", "Tools" };
                        // 检查是否是受限目录或其子目录
                        foreach (var dir in forbiddenDirs)
                        {
                            string forbiddenPath = Path.GetFullPath(Path.Combine(rootPath, "..", dir)); // 构造完整路径
                            if (fullPath.StartsWith(forbiddenPath, StringComparison.OrdinalIgnoreCase))
                            {
                                return ResOk(false, $"备份数据库文件存储路径不能设置在 {dir} 目录或其子目录");
                            }
                        }
                        #endregion

                        content.SysConfig_DBBackPath = HttpUtility.UrlEncode(content.SysConfig_DBBackPath);
                    }
                    else
                    {
                        return ResOk(false, "备份数据库文件存储路径格式错误");
                    }
                }


                if (content.SysConfig_ThreeApiEnable == 1)
                {
                    //if (string.IsNullOrWhiteSpace(content.SysConfig_ThreeApiUrl))
                    //{
                    //    return ResOk(false, "已启用第三方服务，第三方服务地址不能为空");
                    //}

                    //验证第三方服务地址格式是否正确
                    if (!string.IsNullOrWhiteSpace(content.SysConfig_ThreeApiUrl) && !content.SysConfig_ThreeApiUrl.Contains("http://") && !content.SysConfig_ThreeApiUrl.Contains("https://"))
                    {
                        return ResOk(false, "第三方服务地址格式错误");
                    }

                    //第三方服务AppID 是否正确
                    if (string.IsNullOrWhiteSpace(content.SysConfig_ThreeApiAppID))
                    {
                        return ResOk(false, "第三方服务AppID不能为空，请重置参数！");
                    }

                    //第三方服务AppSecret
                    if (string.IsNullOrWhiteSpace(content.SysConfig_ThreeApiAppSecret))
                    {
                        return ResOk(false, "第三方服务AppSecret不能为空，请重置参数！");
                    }

                    ////第三方服务AppID和旧的参数不同时，判断格式 由小写字母或数字组成，长度8位
                    //if (content.SysConfig_ThreeApiAppID != oldContent.SysConfig_ThreeApiAppID)
                    //{
                    //    if (!Regex.IsMatch(content.SysConfig_ThreeApiAppID, @"^[a-z0-9]{8}$"))
                    //    {
                    //        return ResOk(false, "第三方服务AppID格式错误，请重置参数！");
                    //    }
                    //} 

                    ////第三方服务AppSecret和旧的参数不同时，判断格式 由小写字母或数字组成，长度16位
                    //if (content.SysConfig_ThreeApiAppSecret != oldContent.SysConfig_ThreeApiAppSecret)
                    //{
                    //    if (!Regex.IsMatch(content.SysConfig_ThreeApiAppSecret, @"^[a-z0-9]{16}$"))
                    //    {
                    //        return ResOk(false, "第三方服务AppSecret格式错误，请重置参数！");
                    //    }
                    //}
                }

                LogManagementMap.SetOverDays = content.SysConfig_LogSaveDay ?? 20;

                if (content.SysConfig_LogOpen != null && content.SysConfig_LogOpen != oldContent.SysConfig_LogOpen)
                {
                    LogManagementMap.SetLogOpen = content.SysConfig_LogOpen.Value;
                }

                if (!string.IsNullOrWhiteSpace(content.SysConfig_ABCJHPFXBase64))
                {
                    if (!content.SysConfig_ABCJHPFXBase64.Contains("data:application/x-pkcs12;base64,"))
                    {
                        return ResOk(false, "农行聚合支付PFX证书填写错误！");
                    }
                }

                if (!string.IsNullOrWhiteSpace(content.SysConfig_ABCJHCERBase64))
                {
                    if (!content.SysConfig_ABCJHCERBase64.Contains("data:application/x-x509-ca-cert;base64,"))
                    {
                        return ResOk(false, "农行聚合支付CER证书填写错误！");
                    }
                }

                var newContent = Utils.Returnobj(oldContent, content);
                model.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(newContent));
            }

            var res = BaseBLL._Insert(model);
            if (res > 0)
            {
                AppBasicCache.CurrentSysConfig = model;
                CloudLink.AppCache.GetSysConfigContent = AppBasicCache.CurrentSysConfigContent = TyziTools.Json.ToObject<SysConfigContent>(BLL.SysConfig.GetUrlDecode(model.SysConfig_Content));

                if (!string.IsNullOrEmpty(content.SysConfig_ImagePath))
                {
                    AppBasicCache.strImgpath = ImageTools.LocalFilePath = HttpUtility.UrlDecode(content.SysConfig_ImagePath);
                }

                Push(PushAction.Edit, model, dataType: DataTypeEnum.SystemSetting, Desc: $"更新");

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"{LogHelper.GetChanges(oldContent, content)}", SecondIndex.SystemSetting);

                if (oldContent.SysConfig_ClearData != content.SysConfig_ClearData)
                {
                    if (content.SysConfig_ClearData == 1)
                    {
                        ParkTimer.BeginClearTask();
                    }
                    else
                    {
                        ParkTimer.StopClearTask();
                    }
                }

                //更新第三方服务配置
                if (content.SysConfig_ThreeApiEnable == 1)
                {
                    if (OpenApiV2.OpenApiService.Instance == null)
                    {
                        OpenApiServiceCollectionExtensions.AddOpenApiService();
                    }
                    OpenApiV2.OpenApiService.Instance.UpdateConfig(content);
                }
                else
                {
                    OpenApiServiceCollectionExtensions.StopOpenApiService();
                }

                return ResOk(true, "保存成功", isUpdateImgPath ? 1 : 0);
            }

            return ResOk(false, "保存失败");
        }
        catch (Exception ex)
        {
            return ResOk(false, ex.Message);
        }
    }

    /// <summary>
    /// 查询所有的回调类目
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public IActionResult GetCallBackList()
    {
        if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Search.ToString(), false, true, lgAdmins))
            return ResOk(false, "无权限");
        //根据枚举获取所有的回调类目
        var type = typeof(CallBackType);
        var list = Enum.GetValues(type);
        var result = new List<ThirdPartyCallback>();
        var model = BaseBLL._GetEntityByWhere(new SysConfig(), "*", $"SysConfig_ParkNo='{parking.Parking_No}'");
        var sc = SysConfigContent.GetIntance(model);
        foreach (var item in list)
        {
            var index = (int)item;
            var isEnable = false;
            var url = string.Empty;
            var t = (CallBackType)item;
            var name = t.GetRemark();
            var iInterval = 1000;
            if (sc.SysConfig_ThreeCallBacks != null)
            {
                var f = sc.SysConfig_ThreeCallBacks.FirstOrDefault(m => m.Index == (int)t);
                if (f != null)
                {
                    isEnable = f.IsEnable;
                    url = f.ServiceUrl;
                    iInterval = f.Interval;
                }
            }

            result.Add(new ThirdPartyCallback
            {
                Index = index,
                IsEnable = isEnable,
                ServiceName = name,
                ServiceUrl = url,
                Interval = iInterval
            });
        }

        return ResOk(true, "查询成功", result);
    }

    /// <summary>
    /// 保存回调类目
    /// </summary>
    /// <param name="jsonModel"></param>
    /// <returns>是否保存成功</returns>
    [HttpPost]
    public IActionResult SaveCallBack(string jsonModel)
    {
        if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Save.ToString(), false, true, lgAdmins))
            return ResOk(false, "无权限");

        var model = TyziTools.Json.ToObject<List<ThirdPartyCallback>>(jsonModel);
        if (model == null || model.Count == 0)
        {
            return ResOk(false, "保存失败：未能获取到回调类目");
        }

        var sysModel = AppBasicCache.CurrentSysConfig;
        if (sysModel == null)
        {
            return ResOk(false, "保存失败：未能查询到停车场配置");
        }

        var content = AppBasicCache.CurrentSysConfigContent;
        if (content == null)
        {
            return ResOk(false, "保存失败：未能查询到停车场配置");
        }


        content.SysConfig_ThreeCallBacks = model.ToArray();
        sysModel.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(content));
        var irsult = BaseBLL._UpdateByModelByID(sysModel);
        if (irsult > 0)
        {
            if (OpenApiV2.OpenApiService.Instance != null)
            {
                OpenApiV2.OpenApiService.Instance.UpdateConfig(content);
            }
            AppBasicCache.CurrentSysConfig = sysModel;
            AppBasicCache.CurrentSysConfigContent = TyziTools.Json.ToObject<SysConfigContent>(BLL.SysConfig.GetUrlDecode(sysModel.SysConfig_Content));

            Push(PushAction.Edit, sysModel, dataType: DataTypeEnum.SystemSetting, Desc: $"更新");


            //更新第三方服务配置
            if (content.SysConfig_ThreeApiEnable == 1)
            {
                if (OpenApiV2.OpenApiService.Instance == null)
                {
                    OpenApiServiceCollectionExtensions.AddOpenApiService();
                }
                OpenApiV2.OpenApiService.Instance.UpdateConfig(content);
            }
            else
            {
                OpenApiServiceCollectionExtensions.StopOpenApiService();
            }


            UserLogs.AddLog(lgAdmins, "修改系统设置", "修改回调类目");

            return ResOk(true, "保存成功");
        }

        return ResOk(false, "保存失败");
    }

    /// <summary>
    /// 立即重启web服务
    /// </summary>
    /// <returns></returns>
    public IActionResult StopWebService()
    {
        try
        {
            if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Search.ToString(), false, true, lgAdmins))
                return ResOk(false, "无权限");

            SystemLogs.AddLog(lgAdmins, "变更图片存储路径或本地日志全量存储", "变更图片存储路径后正在重启软件服务.");

            if (AppBasicCache.IsWindows)
            {
                TyziTools.WinService.ReStart("carparking_bs");
                TyziTools.WinService.ReStart("carparking_cloudclient");
                TyziTools.WinService.ReStart("carparking_web");
            }
            else
            {
                var r1 = LinuxApi.SendServiceReboot(ServicesEnum.Sentry);
                var r2 = LinuxApi.SendServiceReboot(ServicesEnum.Middle);
                var r3 = LinuxApi.SendServiceReboot(ServicesEnum.Web);
                if (!r1)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, "重启岗亭服务失败");
                    return ResOk(false, "重启岗亭服务失败");
                }

                if (!r2)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, "重启中间件服务失败");
                    return ResOk(false, "重启中间件服务失败");
                }

                if (!r3)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, "重启后台服务失败");
                    return ResOk(false, "重启后台服务失败");
                }
            }

            return ResOk(true, "重启软件服务成功");
        }
        catch (Exception ex)
        {
            LogManagementMap.WriteToFile(LoggerEnum.WebOperationLog, $"重启服务异常：{ex}");
            return ResOk(false, "重启软件服务失败：" + ex.Message);
        }
    }

    /// <summary>
    /// 文件夹不存在则创建
    /// </summary>
    /// <param name="path">文件夹路径</param>
    /// <returns></returns>
    private static bool CreateDirectory(string path)
    {
        var exist = Directory.Exists(path);
        if (!exist)
        {
            try
            {
                Directory.CreateDirectory(path);
                exist = true;
            }
            catch (Exception)
            {
                exist = false;
            }
        }

        return exist;
    }

    /// <summary>
    /// 软件授权
    /// </summary>
    /// <returns></returns>
    public void SoftAuth()
    {
        if (!Powermanage.PowerCheck("SystemSetting", PowerEnum.Save.ToString(), false))
        {
            MiniResponse.ResponseResult("无权限", false, null, "1");
            return;
        }

        var files = Request.Form.Files;
        if (files == null || files.Count == 0)
        {
            MiniResponse.ResponseResult("未能获取到授权文件", false, null, "1");
            return;
        }

        if (!Utils.IsOnlyTextFiles(files))
        {
            MiniResponse.ResponseResult("只允许上传文本文件", false, null, "1");
            return;
        }

        var authContent = string.Empty;
        //读取txt文件的内容
        using (var stream = files[0].OpenReadStream())
        {
            using (var reader = new StreamReader(stream))
            {
                authContent = reader.ReadToEnd();
            }
        }

        if (string.IsNullOrEmpty(authContent))
        {
            MiniResponse.ResponseResult("授权文件内容为空", false, null, "1");
            return;
        }

        //var resultStr = AESHelper.AesDecrypt(authContent, "jAKSMXWzPPfwZFj1", "QmgU18iFkPQCMia6");
        if (!string.IsNullOrEmpty(authContent))
        {
            AppBasicCache.CurrentSysConfigContent.SysConfig_SoftAuth = authContent;
            AppBasicCache.CurrentSysConfig.SysConfig_Content = WebUtility.UrlEncode(TyziTools.Json.ToString(AppBasicCache.CurrentSysConfigContent));
            var res = BaseBLL._Insert(AppBasicCache.CurrentSysConfig);
            if (res >= 0)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"更新软件授权信息", SecondIndex.SystemSetting);
                MiniResponse.ResponseResult("授权信息导入成功！", true, authContent, "1");
            }
            else
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"更新软件授权信息失败", SecondIndex.SystemSetting);
                MiniResponse.ResponseResult("数据处理失败，请重试！", false, null, "1");
            }
        }
        else
        {
            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"解密错误，授权信息导入失败", SecondIndex.SystemSetting);
            MiniResponse.ResponseResult("解密错误，授权信息导入失败！", false, null, "1");
        }
    }

}