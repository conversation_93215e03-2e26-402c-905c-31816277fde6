﻿using carparking.Common;
using carparking.Config;
using carparking.SentryBox;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using carparking.BLL.Cache;
using TcpConnPools.Camera;

namespace carparking.Web.Controllers.API
{
    /// <summary>
    /// 用于工控机消息交互
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class MonitoringAPIController : SentryBaseAPIController
    {
        #region 基本数据查询

        /// <summary>
        /// 获取车道列表
        /// </summary>
        [HttpPost("GetPasswayData")]
        public async Task<IActionResult> GetPasswayData()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            try
            {
                Model.Parking parking = BLL.Parking._GetAllEntity(new Model.Parking(), "*", $"1=1").FirstOrDefault() ?? new Model.Parking();
                string sql = "";
                List<string> lanes = new List<string>();
                if (!string.IsNullOrWhiteSpace(lgAdmin.Admins_PasswayNo))
                {
                    lanes = JsonConvert.DeserializeObject<List<string>>(lgAdmin.Admins_PasswayNo);
                    if (lanes != null && lanes.Count > 0)
                    {
                        sql = $" and Passway_No in ('{string.Join("','", lanes)}')";
                    }
                }

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("*", $"Passway_ParkNo='{parking.Parking_No}' and Passway_SentryHostNo='{AppBasicCache.SentryHostInfo.SentryHost_No}' {sql} ");
                List<Model.PasswayLinkExt> areas = BLL.PasswayLink.GetAllEntityExt("*", $"PasswayLink_ParkNo='{parking.Parking_No}'");

                List<Model.PasswayAndParkArea> data = TyziTools.Json.ToObject<List<Model.PasswayAndParkArea>>(TyziTools.Json.ToString(passways, true));

                foreach (var item in data)
                {
                    var policypassway = BLL.PolicyPassway.GetEntityByPasswayNo(item.Passway_No);
                    if (policypassway != null)
                    {
                        item.PolicyPassway_WaitTimeOut = policypassway.PolicyPassway_WaitTimeOut ?? -1;
                    }

                    item.Passway_Links = areas.FindAll(x => x.PasswayLink_PasswayNo == item.Passway_No)?.OrderBy(x => x.PasswayLink_GateType).ToList();
                    if (item.Passway_Links != null)
                    {
                        if (item.Passway_Links.Count == 1)
                        {
                            item.Passway_GateType = item.Passway_Links.First().PasswayLink_GateType;
                            item.Passway_AreaCallName = $"{(item.Passway_Links.First().PasswayLink_GateType == 0 ? "[出]" : "[入]")}{item.Passway_Links.First().ParkArea_Name}";
                        }
                        else if (item.Passway_Links.Count == 2)
                        {
                            item.Passway_GateType = 2;
                            List<string> names = new List<string>();
                            item.Passway_Links.ForEach(x => { names.Add($"{(x.PasswayLink_GateType == 0 ? "[出]" : "[入]")}{x.ParkArea_Name}"); });
                            item.Passway_AreaCallName = string.Join('/', names);

                            var o = item.Passway_Links.Find(x => x.PasswayLink_GateType == 0);
                            var i = item.Passway_Links.Find(x => x.PasswayLink_GateType == 1);

                            var oA = areas.Find(x => x.PasswayLink_ParkAreaNo == o.PasswayLink_ParkAreaNo);
                            var iA = areas.Find(x => x.PasswayLink_ParkAreaNo == i.PasswayLink_ParkAreaNo);

                            //出场区域的层级大于入场区域的层级，则表示从内场离开进入外场
                            if (oA.ParkArea_Level > iA.ParkArea_Level)
                            {
                                item.Passway_GateType = 3;
                            }
                        }
                        else
                            item.Passway_GateType = -1;

                        item.IsLongOpen = AppSettingConfig.GetLongOpen(item.Passway_No);
                        if (string.IsNullOrWhiteSpace(item.IsLongOpen))
                        {
                            item.IsLongOpen = "0";
                        }
                    }
                }

                return ResOk(true, "读取成功", data);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "获取车道列表异常");
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 获取视频信息
        /// </summary>
        [HttpPost("GetVideo")]
        public async Task<IActionResult> GetVideo([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwayno = Convert.ToString(jo["passwayno"]);
            try
            {
                Model.Device device = BLL.Device.GetEntityByPasswayNo(passwayno);
                if (device == null) return ResOk(false, "无车牌识别相机");

                List<object> list = new List<object>();

                Model.Drive drive = BLL.BaseBLL._GetEntityByWhere(new Model.Drive(), "*", $"Drive_Category=1 and  Drive_No='{device.Device_DriveNo}'");
                if (drive != null)
                {
                    var data = CreateData(device, drive);
                    if (data != null)
                    {
                        list.Add(data);
                    }

                    if (list.Count > 0)
                    {
                        //辅助相机
                        List<Model.DeviceExt> deviceList = BLL.Device.GetSubEntityByPasswayNo(passwayno);
                        deviceList?.ForEach(device =>
                        {
                            var drive2 = BLL.BaseBLL._GetEntityByWhere(new Model.Drive(), "*", $"Drive_Category=1 and  Drive_No='{device.Device_DriveNo}'");
                            if (drive2 != null)
                            {
                                var data = CreateData(device, drive2);
                                if (data != null)
                                {
                                    list.Add(data);
                                }
                            }
                        });
                    }
                }

                if (list.Count > 0)
                {
                    return ResOk(true, "获取视频成功", list);
                }

                return ResOk(false, "不支持的设备型号");
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetVideo]获取视频异常.");
                return ResOk(false, "获取视频异常：" + ex.Message);
            }
        }


        /// <summary>
        /// 创建一个新的数据对象，该对象包含设备和驱动的相关信息。
        /// </summary>
        /// <param name="device">设备对象，包含设备的相关信息。</param>
        /// <param name="drive">驱动对象，包含驱动的相关信息。</param>
        /// <returns>返回一个包含设备和驱动信息的匿名对象，如果不满足创建条件则返回null。</returns>
        private object CreateData(Model.Device device, Model.Drive drive)
        {
            object CreateCameraObject(Model.Device innerdevice, int mode, string camera)
            {
                return new
                {
                    ip = innerdevice.Device_IP,//相机IP地址
                    username = innerdevice.Device_Account,//相机用户名
                    password = innerdevice.Device_Pwd,//相机密码
                    driveno = innerdevice.Device_No,//相机编号
                    type = 2,
                    mode,
                    camera
                };
            }

            var is06Camera = CameraDeviceType.driveNameList06.Contains(drive.Drive_Name);
            var is11Camera = CameraDeviceType.driveNameList11.Contains(drive.Drive_Name);

            //sfm 自产06相机
            if (is06Camera)
            {
                return CreateCameraObject(device, device.Device_VideoMode ?? 0, "06");
            }
            //芊熠11相机
            else if (is11Camera)
            {
                return CreateCameraObject(device, 1, "11");
            }
            //臻识15相机
            else if (drive.Drive_Code == Model.DriveCode.ZS101)
            {
                return CreateCameraObject(device, 2, "15");
            }
            //华夏16相机
            else if (drive.Drive_Code == Model.DriveCode.ZS113)
            {
                return CreateCameraObject(device, 1, "13");
            }
            //宇视14相机
            else if (drive.Drive_Code == Model.DriveCode.ZS114)
            {
                return CreateCameraObject(device, 1, "14");
            }
            else
            {
                return CreateCameraObject(device, 1, drive.Drive_Code);
            }
        }


        /// <summary>
        /// 获取车牌类型【临时车优先】
        /// </summary>
        [HttpPost("GetCarCardType")]
        public async Task<IActionResult> GetCarCardType()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetCarCardType(lgAdmin.token);
        }

        /// <summary>
        /// 获取车牌颜色
        /// </summary>
        [HttpPost("GetCarType")]
        public async Task<IActionResult> GetCarType()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetCarType(lgAdmin.token);
        }

        /// <summary>
        /// 获取车道关联区域信息
        /// </summary>
        [HttpPost("GetPasswayLinkExt")]
        public async Task<IActionResult> GetPasswayLinkExt()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetPasswayLinkExt(lgAdmin.token);
        }

        /// <summary>
        /// 获取系统配置(主要取得入场备注项，免费原因项)
        /// </summary>
        [HttpPost("GetSysConfig")]
        public async Task<IActionResult> GetSysConfig()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetSysConfig(lgAdmin.token);
        }

        /// <summary>
        /// 获取停车场策略参数
        /// </summary>
        [HttpPost("GetPolicyPark")]
        public async Task<IActionResult> GetPolicyPark()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetPolicyPark(lgAdmin.token);
        }

        /// <summary>
        /// 获取停车场设置的优惠列表
        /// </summary>
        [HttpPost("SltParkDiscountSet")]
        public async Task<IActionResult> SltParkDiscountSet()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.SltParkDiscountSet(lgAdmin.token);
        }


        /// <summary>
        /// 场内记录查询
        /// </summary>
        [HttpPost("GetInParkCarList")]
        public async Task<IActionResult> GetInParkCarList([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            int pageIndex = Convert.ToInt32(jo["pageIndex"]);
            int pageSize = Convert.ToInt32(jo["pageSize"]);
            string conditionParam = Convert.ToString(jo["conditionParam"]);
            return await monitoring.GetInParkCarList(pageIndex, pageSize, conditionParam, lgAdmin.token);
        }

        /// <summary>
        /// 进出场记录查询
        /// </summary>
        [HttpPost("GetParkOrderList")]
        public async Task<IActionResult> GetParkOrderList([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            int pageIndex = Convert.ToInt32(jo["pageIndex"]);
            int pageSize = Convert.ToInt32(jo["pageSize"]);
            string conditionParam = Convert.ToString(jo["conditionParam"]);
            return await monitoring.GetParkOrderList(pageIndex, pageSize, conditionParam, "", "", lgAdmin.token);
        }

        /// <summary>
        /// 获取当班收费
        /// </summary>
        [HttpPost("GetCurrentWorkShift")]
        public async Task<IActionResult> GetCurrentWorkShift()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetCurrentWorkShift(lgAdmin.token);
        }

        /// <summary>
        /// 获取当前车位数据
        /// </summary>
        [HttpPost("GetCurrentCarSpace")]
        public async Task<IActionResult> GetCurrentCarSpace()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetCurrentCarSpace(lgAdmin.token);
        }

        /// <summary>
        /// 获取车场连接状态
        /// </summary>
        [HttpPost("GetHostState")]
        public async Task<IActionResult> GetHostState()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return monitoring.GetHostState();
        }

        /// <summary>
        /// 获取云平台连接状态
        /// </summary>
        [HttpPost("GetParkingState")]
        public async Task<IActionResult> GetParkingState()
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetParkingState(lgAdmin.token);
        }

        /// <summary>
        /// 获取车道相机状态
        /// </summary>
        [HttpPost("GetDeviceStatus")]
        public async Task<IActionResult> GetDeviceStatus([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwaynoes = Convert.ToString(jo["noes"]);
            if (string.IsNullOrEmpty(passwaynoes)) return ResOk(false, "车道编码不能为空");

            List<string> noList = passwaynoes.Split(",").ToList();
            List<object> data = new List<object>();

            if (AppBasicCache.GetSentryPasswayLinkDic != null && AppBasicCache.GetSentryPasswayLinkDic.Count > 0 && AppBasicCache.GetSentryDeviceLinking != null && AppBasicCache.GetSentryDeviceLinking.Count > 0)
            {
                List<Model.DeviceExt> deviceList = new List<Model.DeviceExt>();

                noList.ForEach(passwayno =>
                {
                    var dev = string.IsNullOrEmpty(passwayno) ? null : AppBasicCache.GetSentryDeviceLinking.Where(x => x.Value.Device_PasswayNo == passwayno);

                    if (dev != null)
                    {
                        Model.DeviceExt device = BLL.Device.GetEntityByPasswayNo(passwayno);
                        if (device != null)
                        {
                            deviceList.Add(device);
                        }
                        else
                        {
                            data.Add(new { no = passwayno, online = 0, opengate = 0, hascar = 0, code = 0 });
                        }
                    }
                    else
                    {
                        data.Add(new { no = passwayno, online = 0, opengate = 0, hascar = 0, code = 0 });
                    }
                });

                foreach (var device in deviceList)
                {
                    try
                    {
                        await Task.Delay(50);
                        if (!string.IsNullOrWhiteSpace(device.Device_IP))
                        {
                            switch (device.Device_Category)
                            {
                                //相机
                                case 1:
                                    {
                                        int iOline = 0;
                                        int iOpenGate = 0;
                                        int ihascar = 0;
                                        var camera = CameraController.GetCamera(device.Device_IP);
                                        if (camera != null && (camera.Model?.IsConnected ?? false))
                                        {
                                            iOline = camera?.Model?.IsConnected == true ? 1 : 0;
                                            iOpenGate = await camera.ReadIoOutStatusAsync(device.Device_InIO ?? 0);
                                            ihascar = await camera.ReadGpioInStatusAsync(0);
                                        }

                                        data.Add(new { no = device.Device_PasswayNo, online = iOline, opengate = iOpenGate, hascar = ihascar, code = 1 });
                                    }

                                    break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        //读取设备状态失败不处理
                        LogManagementMap.WriteToFileException(null, $"读取设备状态失败：[{passwaynoes}]，{ex}");
                    }
                }
            }

            return ResOk(true, "获取成功", data);
            ;
        }

        /// <summary>
        /// 车道相机常开状态
        /// </summary>
        [HttpPost("GetGateStatus")]
        public async Task<IActionResult> GetGateStatus([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwaynoes = Convert.ToString(jo["noes"]);
            if (string.IsNullOrEmpty(passwaynoes)) return ResOk(false, "车道编码不能为空");

            List<string> noList = passwaynoes.Split(",").ToList();
            List<object> data = new List<object>();


            noList.ForEach(passwayno =>
            {
                if (!string.IsNullOrEmpty(passwayno))
                {
                    var acionRes = GetDevice(passwayno, out Model.DeviceExt mainDevice, $"获取车道相机设备", lgAdmin.token);
                    if (acionRes != null)
                    {
                        data.Add(new { no = passwayno, longOpen = false, code = 0 });
                    }
                    else
                    {
                        var camera = CameraController.GetCamera(mainDevice.Device_IP);
                        if (camera == null)
                        {
                            data.Add(new { no = passwayno, longOpen = false, code = 0 });
                        }
                        else
                        {
                            data.Add(new { no = passwayno, longOpen = ((CameraModel)camera.Model).GateLongOpen, code = 1 });
                        }
                    }
                }
            });

            return ResOk(true, "获取成功", data);
        }

        private IActionResult GetDevice(string passwayno, out Model.DeviceExt mainDevice, string logHeader = "确认通行不成功", string code = "")
        {
            mainDevice = null;
            //if (!Powermanage.BSAdminCheck("", false) && DataCache.Admin.Get(code) == null) { return ResOk(false, "无权限"); }

            AppBasicCache.GetSentryPasswayDic.TryGetValue(passwayno, out var passway);
            if (passway == null)
            {
                LogManagementMap.WriteToFileException(null, $"{logHeader},未找到车道缓存：[{passwayno}][{JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}]");
                return ResOk(false, "获取停车数据失败，未找到车道缓存");
            }

            if (passway.Passway_Type != 3)
            {
                mainDevice = BLL.Device.GetEntityByPasswayNo(passwayno);
                if (mainDevice == null)
                {
                    LogManagementMap.WriteToFileException(null, $"{logHeader},未配置主相机：[{passwayno}][{JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}]");
                    return ResOk(false, "获取停车数据失败，未配置主相机");
                }
            }
            else
            {
                mainDevice = BLL.Device.GetMotoDeviceByPasswayNo(passwayno);
                if (mainDevice == null)
                {
                    LogManagementMap.WriteToFileException(null, $"{logHeader},未配置车道控制器：[{passwayno}][{JsonConvert.SerializeObject(AppBasicCache.GetSentryDeviceLinking)}]");
                    return ResOk(false, "获取停车数据失败，未配置车道控制器");
                }
            }

            return null;
        }

        #endregion

        #region 监控视频操作

        /// <summary>
        /// 识别车牌
        /// </summary>
        [HttpPost("PlateRecognition")]
        public async Task<IActionResult> PlateRecognition([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwayno = Convert.ToString(jo["passwayno"]);
            return await monitoring.PlateRecognition(passwayno, lgAdmin.token);
        }

        /// <summary>
        /// 抓拍图片
        /// </summary>
        [HttpPost("SnapShootToJpeg")]
        public async Task<IActionResult> SnapShootToJpeg([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwayno = Convert.ToString(jo["passwayno"]);
            return await monitoring.SnapShootToJpeg(passwayno, true, lgAdmin.token);
        }

        /// <summary>
        /// 道闸开关
        /// </summary>
        [HttpPost("OpenCloseGate")]
        public async Task<IActionResult> OpenCloseGate([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwayno = Convert.ToString(jo["passwayno"]);
            string code = Convert.ToString(jo["code"]);
            return await monitoring.OpenCloseGate(passwayno, Utils.StrToInt(code, 0), lgAdmin.token);
        }

        #endregion

        #region 弹窗进出场操作

        /// <summary>
        /// 获取车辆预出场信息
        /// </summary>
        [HttpPost("GetParkOrder")]
        public async Task<IActionResult> GetParkOrder([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);

            return await monitoring.GetParkOrder(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                CouponList = CouponList
            }), lgAdmin.token);
        }

        /// <summary>
        /// 确定放行
        /// </summary>
        [HttpPost("ConfirmRelease")]
        public async Task<IActionResult> ConfirmRelease([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);

            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string PayedMoney = Convert.ToString(jo["PayedMoney"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);

            return await monitoring.ConfirmRelease(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                CouponList = CouponList,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                PayedMoney = PayedMoney,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }

        /// <summary>
        /// 语音播报
        /// </summary>
        [HttpPost("BroadVoice")]
        public async Task<IActionResult> BroadVoice([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);
            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string PayedMoney = Convert.ToString(jo["PayedMoney"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);

            return await monitoring.BroadVoice(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                CouponList = CouponList,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                PayedMoney = PayedMoney,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }

        /// <summary>
        /// 免费放行
        /// </summary>
        [HttpPost("FreeRelease")]
        public async Task<IActionResult> FreeRelease([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);

            return await monitoring.FreeRelease(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }

        /// <summary>
        /// 取消放行
        /// </summary>
        [HttpPost("CancelPass")]
        public async Task<IActionResult> CancelPass([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string passwayno = Convert.ToString(jo["passwayno"]);

            return await monitoring.CancelPass(passwayno, lgAdmin.token);
        }

        /// <summary>
        /// 计费改价
        /// </summary>
        [HttpPost("ModifyMoney")]
        public async Task<IActionResult> ModifyMoney([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);
            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string PayedMoney = Convert.ToString(jo["PayedMoney"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);

            return await monitoring.ModifyMoney(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                CouponList = CouponList,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                PayedMoney = PayedMoney,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }

        /// <summary>
        /// 无入场记录出场
        /// </summary>
        [HttpPost("EnableNoEnterRecord")]
        public async Task<IActionResult> EnableNoEnterRecord([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);
            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string PayedMoney = Convert.ToString(jo["PayedMoney"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);

            return await monitoring.EnableNoEnterRecord(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                CouponList = CouponList,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                PayedMoney = PayedMoney,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }

        /// <summary>
        /// 无入场记录确定放行
        /// </summary>
        [HttpPost("ConfirmReleaseNothingRecord")]
        public async Task<IActionResult> ConfirmReleaseNothingRecord([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);

            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string PayedMoney = Convert.ToString(jo["PayedMoney"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);
            return await monitoring.ConfirmReleaseNothingRecord(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                CouponList = CouponList,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                PayedMoney = PayedMoney,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }

        /// <summary>
        /// 无入场记录取消放行
        /// </summary>
        [HttpPost("CancelNoEnterRecord")]
        public async Task<IActionResult> CancelNoEnterRecord([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string CouponList = Convert.ToString(jo["CouponList"]);

            string ParkOrder_FreeReason = Convert.ToString(jo["ParkOrder_FreeReason"]);
            string PayedMoney = Convert.ToString(jo["PayedMoney"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);
            return await monitoring.CancelNoEnterRecord(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_OutPasswayNo = ParkOrder_OutPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                CouponList = CouponList,
                ParkOrder_FreeReason = ParkOrder_FreeReason,
                PayedMoney = PayedMoney,
                ParkOrder_OutImgPath = ParkOrder_OutImgPath,
                IsApi = true
            }), lgAdmin.token);
        }


        /// <summary>
        /// 确定放行(入场)
        /// </summary>
        [HttpPost("ManualEntrance")]
        public async Task<IActionResult> ManualEntrance([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string mode = Convert.ToString(jo["mode"]);
            string ParkOrder_OutPasswayNo = Convert.ToString(jo["ParkOrder_OutPasswayNo"]);
            string ParkOrder_EnterPasswayNo = Convert.ToString(jo["ParkOrder_EnterPasswayNo"]);
            string ParkOrder_CarNo = Convert.ToString(jo["ParkOrder_CarNo"]);
            string ParkOrder_CarCardType = Convert.ToString(jo["ParkOrder_CarCardType"]);
            string ParkOrder_CarType = Convert.ToString(jo["ParkOrder_CarType"]);
            string ParkOrder_EnterReamrk = Convert.ToString(jo["ParkOrder_EnterReamrk"]);
            string ParkOrder_OutImgPath = Convert.ToString(jo["ParkOrder_OutImgPath"]);
            string ParkOrder_EnterImgPath = Convert.ToString(jo["ParkOrder_EnterImgPath"]);

            return await monitoring.ManualEntrance(TyziTools.Json.ToString(new
            {
                mode = mode,
                ParkOrder_EnterPasswayNo = ParkOrder_EnterPasswayNo,
                ParkOrder_CarNo = ParkOrder_CarNo,
                ParkOrder_CarCardType = ParkOrder_CarCardType,
                ParkOrder_CarType = ParkOrder_CarType,
                ParkOrder_EnterImgPath = ParkOrder_EnterImgPath,
                ParkOrder_EnterReamrk = ParkOrder_EnterReamrk,
                IsApi = true
            }), lgAdmin.token);
        }

        #endregion

        #region 菜单工具栏

        /// <summary>
        /// 管理后台免密登录
        /// </summary>
        [HttpPost("GetLoginAuthCode")]
        public async Task<IActionResult> GetLoginAuthCode([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string page = Convert.ToString(jo["page"]);


            return await monitoring.GetLoginAuthCode(Utils.StrToInt(page, 0), lgAdmin.token);
        }

        /// <summary>
        /// 退出登录
        /// </summary>
        [HttpPost("LogOut")]
        public async Task<IActionResult> LogOut([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            //string code = Convert.ToString(jo["code"]);
            //DataCache.Admin.Del(code);
            return await monitoring.LogOut(lgAdmin.token);
        }

        /// <summary>
        /// 获取交班数据
        /// </summary>
        [HttpPost("GetWorkShiftAnData")]
        public async Task<IActionResult> GetWorkShiftAnData([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetWorkShiftAnData(lgAdmin.token);
        }

        /// <summary>
        /// 获取可换班的账号
        /// </summary>
        [HttpPost("GetAdminsSelect")]
        public async Task<IActionResult> GetAdminsSelect([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            return await monitoring.GetAdminsSelect(lgAdmin.token);
        }

        /// <summary>
        /// 确定交班
        /// </summary>
        [HttpPost("ChangeWorkShift")]
        public async Task<IActionResult> ChangeWorkShift([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string start = Convert.ToString(jo["start"]);
            string end = Convert.ToString(jo["end"]);
            string onaccount = Convert.ToString(jo["onaccount"]);
            string onpwd = Convert.ToString(jo["onpwd"]);

            string Admins_Pwd = Utils.MD5Encrypt(onpwd + Utils.passwordMD5String, Encoding.UTF8);
            Model.Admins lgAdmins = BLL.Admins.GetEntity("*", $"Admins_Account='{onaccount}' AND Admins_Pwd='{Admins_Pwd}'");
            if (lgAdmins == null)
            {
                return ResOk(false, "账号或密码错误");
            }

            if (lgAdmins.Admins_Enable != 1)
            {
                return ResOk(false, "账号未启用");
            }

            Model.PowerGroup powergroup = BLL.PowerGroup.GetEntity(lgAdmins.Admins_PowerNo);
            if (powergroup == null)
            {
                return ResOk(false, "权限组不存在");
            }

            if (powergroup.PowerGroup_Enable != 1)
            {
                return ResOk(false, "权限已被禁用");
            }

            string loginIp = HttpHelper.GetLoginIp(HttpContext, AppBasicCache.Ip);
            LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{loginIp}]Monitoring/ChangeWorkShift交班登录");

            #region 将登录参数写入缓存

            Model.AdminSession session = JsonConvert.DeserializeObject<Model.AdminSession>(JsonConvert.SerializeObject(lgAdmins));
            session.Admins_IPAddress = loginIp;
            session.token = Guid.NewGuid().ToString("N");
            session.PowerGroup_ID = powergroup.PowerGroup_ID;
            session.PowerGroup_Name = powergroup.PowerGroup_Name;
            session.PowerGroup_Value = powergroup.PowerGroup_Value;
            session.Admins_LoginTime = DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");

            DataCache.Admin.Set(session.token, session);

            #endregion

            #region 将登陆账号的车道权限写入到WS缓存

            List<string> lanes = new List<string>();
            if (!string.IsNullOrWhiteSpace(lgAdmins.Admins_PasswayNo))
                lanes = JsonConvert.DeserializeObject<List<string>>(lgAdmins.Admins_PasswayNo);
            WebSocketHandler.AcccountLaneCache.TryRemove(lgAdmin.Admins_Account, out var del);
            WebSocketHandler.AcccountLaneCache.TryRemove(lgAdmins.Admins_Account, out var del1);
            WebSocketHandler.AcccountLaneCache.TryAdd(lgAdmins.Admins_Account, lanes);

            #endregion

            JObject objControl = JObject.Parse(powergroup.PowerGroup_Value);
            var powerValueStr = objControl["WinFormMonitor"]; //获取到当前控制器下的所有权限

            return await monitoring.ChangeWorkShift(TyziTools.Json.ToString(new
            {
                start = start,
                end = end,
                onaccount = onaccount,
                onpwd = onpwd,
                token = lgAdmin.token,
                login = new
                {
                    token = session.token,
                    power = TyziTools.Json.ToString(powerValueStr),
                    passwayno = lgAdmins.Admins_PasswayNo
                }
            }), lgAdmin.token);
        }

        #endregion

        #region 其它

        /// <summary>
        /// 获取场内车辆信息列表
        /// </summary>
        [HttpPost("GetInCarList")]
        public async Task<IActionResult> GetInCarList([FromBody] JObject jo)
        {
            if (lgAdmin == null)
            {
                return ResOk(false, "请登录");
            }

            string carno = Convert.ToString(jo["carno"]);

            return await monitoring.GetInCarList(carno, lgAdmin.token);
        }

        /// <summary>
        /// 查检权限
        /// </summary>
        [HttpPost("CheckPowerGroup")]
        public async Task<IActionResult> CheckPowerGroup([FromBody] JObject jo)
        {
            try
            {
                if (lgAdmin == null)
                {
                    return ResOk(false, "请登录");
                }

                var powergroup = BLL.PowerGroup.GetEntity(lgAdmin.Admins_PowerNo);
                if (powergroup == null)
                {
                    DataCache.Admin.Del(lgAdmin.token);
                    DataCache.LoginAuthCode.Del(lgAdmin.token);
                    return ResOk(false, "权限组不存在");
                }

                if (powergroup.PowerGroup_Enable != 1)
                {
                    DataCache.Admin.Del(lgAdmin.token);
                    DataCache.LoginAuthCode.Del(lgAdmin.token);
                    return ResOk(false, "权限已被禁用");
                }

                JObject objControl = JObject.Parse(powergroup.PowerGroup_Value);
                var powerValueStr = objControl["WinFormMonitor"]; //获取到当前控制器下的所有权限

                JObject objControl2 = JObject.Parse(lgAdmin.PowerGroup_Value);
                var powerValueStr2 = objControl2["WinFormMonitor"]; //获取到当前控制器下的所有权限

                if (powerValueStr.ToString() != powerValueStr2.ToString())
                {
                    return ResOk(false, "权限组已变更");
                }

                return ResOk(true, "权限组无变更");
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        #endregion
    }
}