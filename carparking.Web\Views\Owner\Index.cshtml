﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>车主车位</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }

        .layui-inline .layui-form-select .layui-input { width: 182px; }

        .layui-input.timerange { width: 300px; }

        .layui-layer-btn-l { margin-left: 8.33%; }

        .input-group-btn { background-color: #337ab7 !important; border-top-right-radius: 5px !important; border-bottom-right-radius: 5px !important; }

        .btnUnit { color: #fff !important; }

        .operabar-if { display: inline-flex !important; align-items: center; padding: 0 15px; cursor: pointer; height: 30px; background: #fff; border-radius: 2px; color: #666; transition: all .3s; }

        .operabar-if:hover { color: #1E9FFF; }

        .operabar-if i { margin-right: 5px; }

        .more-operations { margin: 1px; margin-bottom: 10px; padding: 1px; padding-top: 15px;  padding-bottom: 7px; padding-left: 10px; display: none; background-color:#f5f5f5; }

        .moreOps::before,
        .moreOps::after { content: none !important; }

        .operabar-if .layui-icon-down { transition: transform 0.3s; }

        .operabar-if.active .layui-icon-down { transform: rotate(180deg); }
        .layui-layer-TipsG { border-bottom-color: rgba(0,0,0,.2) !important; display:none !important; }
        .layui-layer-content { background-color: rgb(255 255 255) !important; color: #000 !important; }

    </style>
</head>

<body class="index">

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车辆管理</cite></a>
                <a><cite>车辆登记</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div id="searchForm" class="layui-form">
                        <div class="layui-card-header topbar">
                            <div class="layui-col-md6">
                                <div class="fastsearch">
                                    <ul>
                                        <li data-key="0" class="select">
                                            全部 <t>(0)</t>
                                        </li>
                                        <li data-key="1">
                                            未过期 <t>(0)</t>
                                        </li>
                                        <li data-key="2">
                                            将过期 <t>(0)</t>
                                        </li>
                                        <li data-key="3">
                                            已过期 <t>(0)</t>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="operabar searchForm">
                                    <div class="layui-inline">
                                        <input class="layui-input " name="Owner_CarCardNo"
                                               id="Owner_CarCardNo" autocomplete="off" placeholder="车牌号" maxlength="10" />
                                    </div>
                                    <div class="layui-inline">
                                        <div class="operabar-if">更多条件</div>
                                    </div>
                                    <div class="layui-inline">
                                        <button class="layui-btn" id="Search">
                                            <i class="layui-icon layui-icon-search inbtn"></i>
                                            <t>搜索</t>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="test-table-reload-btn layui-form search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="Owner_Space" id="Owner_Space"
                                       autocomplete="off" placeholder="系统车位号" maxlength="10" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Owner_ParkSpace"
                                       id="Owner_ParkSpace" autocomplete="off" placeholder="车场车位号" maxlength="50" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-input" id="Owner_CardTypeNo" name="Owner_CardTypeNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Owner_Name" id="Owner_Name"
                                       autocomplete="off" placeholder="车主姓名" maxlength="20" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Owner_PhoneLastFour"
                                       id="Owner_PhoneLastFour" autocomplete="off" placeholder="手机号后四位" maxlength="4" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Owner_StartTime0" id="Owner_StartTime0"
                                       autocomplete="off" placeholder="有效期起开始" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Owner_StartTime1" id="Owner_StartTime1"
                                       autocomplete="off" placeholder="有效期起结束" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Owner_EndTime0" id="Owner_EndTime0" autocomplete="off"
                                       placeholder="有效期止开始" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Owner_EndTime1" id="Owner_EndTime1" autocomplete="off"
                                       placeholder="有效期止结束" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Owner_AddTime0" id="Owner_AddTime0" autocomplete="off"
                                       placeholder="登记时间开始" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Owner_AddTime1" id="Owner_AddTime1" autocomplete="off"
                                       placeholder="登记时间结束" value="" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="一位多车" class="form-control chosen-select " id="Owner_IsMoreCar"
                                        name="Owner_IsMoreCar" lay-search>
                                    <option value="">一位多车</option>
                                    <option value="1">是</option>
                                    <option value="0">否</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Owner_Address"
                                       id="Owner_Address" autocomplete="off" placeholder="车主地址" maxlength="200" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Owner_Remark" id="Owner_Remark"
                                       autocomplete="off" placeholder="车主备注" maxlength="200" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Car_Remark" id="Car_Remark"
                                       autocomplete="off" placeholder="车辆备注" maxlength="200" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="Car_CardNo" id="Car_CardNo"
                                       autocomplete="off" placeholder="车辆卡号" maxlength="200" />
                            </div>
                            <div class="layui-inline">
                                <div class="layui-inline">
                                    <select data-placeholder="储值余额查询条件" class="form-control chosen-select "
                                            id="Owner_BalanceType" name="Owner_BalanceType" lay-search>
                                        <option value="">储值余额查询条件</option>
                                        <option value="0">大于</option>
                                        <option value="1">大于等于</option>
                                        <option value="2">小于</option>
                                        <option value="3">小于等于</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input v-float" name="Owner_BalanceValue" maxlength="5" id="Owner_BalanceValue" autocomplete="off" placeholder="储值金额条件值" value="" />
                                </div>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <div class="layui-row">
                                  <div class="layui-inline">
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Add" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Update" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Retweet" lay-event="Retweet"><i class="fa fa-rmb"></i><t>延期/充值</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="BathPayDate" lay-event="BathPayDate"><i class="fa fa-rmb"></i><t>批量延期</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Import" lay-event="Import"><i class="fa fa-upload"></i><t>导入</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="SearchPassRecord" lay-event="SearchPassRecord"><i class="fa fa-file-powerpoint-o"></i><t>场内记录</t></button>
                                  </div>
                                  <div class="layui-inline">
                                    <button class="layui-btn layui-btn-sm operabar-if moreOps" id="moreOps">
                                        更多操作
                                        <i class="layui-icon layui-icon-down" style="margin-left: 5px;"></i>
                                    </button>
                                  </div>
                             </div>

                                <div class="more-operations" style="display: none;">
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Vaild" lay-event="Vaild"><i class="fa fa-calendar-check-o"></i><t>设置有效期</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Enable" lay-event="Enable"><i class="fa fa-check-circle-o"></i><t>白名单启用</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Disable" lay-event="Disable"><i class="fa fa-ban"></i><t>白名单禁用</t></button>

                                    <button class="layui-btn layui-btn-sm layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="BathVaild"  lay-event="BathVaild"><i class="fa fa-calendar-check-o"></i><t>批量设置有效期</t></button>
                                    @*<button class="layui-btn layui-btn-sm layui-hide" id="Charge" lay-event="Charge"><i class="fa fa-rmb"></i><t>储值车充值</t></button>*@
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Refund" lay-event="Refund"><i class="fa fa-rmb"></i><t>储值车退费</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Bind" lay-event="Bind"><i class="fa fa-calendar-check-o"></i><t>批量授权</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="DownLoadCarList" lay-event="DownLoadCarList"><i class="fa fa-send"></i><t>相机白名单</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="Pulldown" lay-event="Pulldown"><i class="fa fa-arrow-circle-up"></i><t>推送到线上</t></button>
                                    <button class="layui-btn layui-btn-sm layui-hide" id="SetCarCardType" lay-event="SetCarCardType"><i class="fa fa-edit"></i><t>修改车牌类型</t></button>

                                </div>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?*******" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script>
        myVerify.init();

        topBar.config.SearchType = 0;//0-全部,1-未过期,2-将过期,3-已过期
        topBar.init(function (v) {
            pager.bindData(1);
        });

        var Power = window.parent.global.formPower;
        var comtable = null;
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd 00:00:00"));

        s_carno_picker.init("Owner_CarCardNo", function (text, carno) {
            if (s_carno_picker.eleid == "Owner_CarCardNo") {
                $("#Owner_CarCardNo").val(carno.join(''));
            }
        }, "web");

        // 复制内容到剪贴板
        function copyToClipboard(text) {
            var textarea = document.createElement("textarea");
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand("copy");
            document.body.removeChild(textarea);
        }

        // 判断是否有选中的文字
        function hasSelectedText() {
            var selection = window.getSelection();
            return selection && selection.toString().trim().length > 0;
        }

        layui.use(['table', 'jquery', 'form'], function () {
            pager.init();

            // 监听表格的左键点击事件
            document.addEventListener('click', function (e) {
                // 如果有选中的文字，则不执行复制逻辑
                if (hasSelectedText()) return;

                var td = e.target.closest('td'); // 找到最近的 td
                if (td && td.innerText) {
                    copyToClipboard(td.innerText);
                    layer.tips('已复制：' + td.innerText.trim(), td, {
                        tips: [4, '#20222A'],
                        time: 3000
                    });
                }
            });

            searchFile.bindData(3);

            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            conditionParam.SearchType = topBar.config.SearchType;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Owner_No', title: '车主编码', hide: true }
                , { field: 'Owner_Space', title: '系统车位号', width: 120, sort: true }
                , { field: 'Owner_ParkSpace', title: '车场车位号', width: 120, sort: true }
                , { field: 'Owner_CarCardNo', title: '车牌号', width: 150 }
                , {
                    field: 'Owner_CardType', title: '车牌类型', templet: function (d) {
                        if (d.Owner_CardType == 1) return tempBar(3, d.Owner_CardName);
                        else if (d.Owner_CardType == 2) return tempBar(1, d.Owner_CardName);
                        else if (d.Owner_CardType == 3) return tempBar(2, d.Owner_CardName);
                        else if (d.Owner_CardType == 4) return tempBar(4, d.Owner_CardName);
                        else if (d.Owner_CardType == 5) return tempBar(3, d.Owner_CardName);
                        else if (d.Owner_CardType == 6) return tempBar(6, d.Owner_CardName);
                        else return tempBar(0, d.Owner_CardName);
                    }
                }
                , { field: 'Owner_SpaceNum', title: '车位数', sort: true }
                , {
                    field: 'Owner_StartTime', title: '有效期起', width: 160, sort: true, templet: function (d) {
                        if (d.Owner_CardType == 2)
                            return '-';
                        else if (d.Owner_CardType == 3)
                            return new Date(d.Owner_StartTime).Format("yyyy-MM-dd 00:00:00");
                        else
                            return d.Owner_StartTime;
                    }
                }
                , {
                    field: 'Owner_EndTime', title: '有效期止', width: 160, sort: true, templet: function (d) {
                        if (d.Owner_CardType == 2) {
                            return '-';
                        } else {
                            var s = '';
                            if (new Date(d.Owner_EndTime) < new Date()) s = 'color:red;';
                            return '<span style="' + s + '">' + d.Owner_EndTime + '</span>';
                        }
                    }
                }
                , {
                    field: 'Owner_OnlyDay', title: '剩余天数', templet: function (d) {
                        if (d.Owner_CardType == 2) {
                            return '-';
                        } else {
                            return _DATE.getZhDays(getMaxTime(nowDate, new Date(new Date(d.Owner_StartTime).Format("yyyy-MM-dd 00:00:00"))), new Date(new Date(d.Owner_EndTime).Format("yyyy-MM-dd 23:59:59")));
                        }
                    }
                }
                , {
                    field: 'Owner_Balance', title: '储值余额', width: 102, sort: true, templet: function (d) {
                        if (d.Owner_CardType == 2) return d.Owner_Balance || 0;
                        else return "-";
                    }
                }
                , {
                    field: 'Owner_EnableOffline', title: '白名单', hide: true, templet: function (d) {
                        if (d.Owner_EnableOffline == 1) return tempBar(1, "已启用");
                        else return tempBar(3, "已禁用");
                    }
                }
                , {
                    field: 'Owner_IsMoreCar', title: '一位多车', templet: function (d) {
                        if (d.Owner_IsMoreCar == 1) return tempBar(1, "是");
                        else return tempBar(3, "否");
                    }
                }
                , {
                    field: 'Owner_IsInPark', title: '在场内', templet: function (d) {
                        if (d.Owner_IsInPark == 1) return tempBar(1, "是");
                        else return tempBar(3, "否");
                    }
                }
                , { field: 'Owner_Name', title: '车主姓名' }
                , { field: 'Owner_Phone', width: 120, title: '手机号码', templet: function (d) { if (d.Owner_IsSecretPhone == 1) { return dw_text_omit.bind(d.Owner_Phone); } else { return d.Owner_Phone; } } }
                , { field: 'Owner_ParkNo', title: '车辆卡号', hide: true }
                , { field: 'Owner_StopAreaName', title: '可停区域', hide: true }
                , { field: 'Owner_IDCard', title: '身份证号', hide: true }
                , { field: 'Owner_License', title: '驾驶证号', hide: true, templet: function (d) { return dw_text_omit.bind(d.Owner_License); } }
                , { field: 'Owner_Email', title: '电子邮件', hide: true, templet: function (d) { return dw_text_omit.bind(d.Owner_Email); } }
                , { field: 'Owner_Address', title: '车主地址', hide: true }
                , { field: 'Owner_Remark', title: '备注', hide: true }
                , { field: 'Owner_AddTime', title: '登记时间', width: 160, sort: true, hide: true }
                , { field: 'Owner_AddName', title: '操作员', hide: true }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Owner/GetOwnerList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100, 1000]
                , done: function (data) {
                    tb_page_set(data);
                    pager.bindPower();
                    topBar.set(data.count);

                    // 恢复更多操作的展开状态
                    if (pager.moreOpsExpanded) {
                        $('#moreOps').addClass('active');
                        $('.more-operations').removeClass('layui-hide').css('display', 'block');
                    } else {
                        $('#moreOps').removeClass('active');
                        $('.more-operations').addClass('layui-hide').css('display', 'none');
                    }

                     $('#moreOps').off('click').on('click', function () {
                        if ($(this).hasClass('active')) {
                            $('.more-operations').addClass('layui-hide').css('display', 'none');
                        }else{
                            $('.more-operations').removeClass('layui-hide').css('display', 'block');
                        }
                        $(this).toggleClass('active');
                    });

                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                localStorage.removeItem("ownerarea");
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "新增车辆",
                            content: '/Owner/Add?Act=Add',
                            area: getIframeArea(['1000px', '95%']),
                            maxmin: false
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "编辑车辆",
                            content: '/Owner/Add?Act=Update&Owner_No=' + data[0].Owner_No,
                            area: getIframeArea(['1000px', '95%']),
                            maxmin: false
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1000) { layer.msg("请选择1000条以内", { icon: 0, time: 2000 }); return; }
                        var ownerNoList = [];
                        $.each(data, function (k, v) {
                            ownerNoList.push(v.Owner_No);
                        })

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            area: ["300px"],
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                setTimeout(function () {
                                    var json = pager.delowner(ownerNoList, true);
                                    if (json.Success) {
                                        if (json.Data == 1) {
                                            layer.open({
                                                type: 0,
                                                title: "消息提示",
                                                btn: ["确定", "取消"],
                                                content: json.Message,
                                                area: ["300px"],
                                                yes: function (res) {
                                                    layer.msg("处理中", { icon: 16, time: 0 });
                                                    setTimeout(function () {
                                                        json = pager.delowner(ownerNoList, false);
                                                        if (json.Success) {
                                                            layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                                        } else
                                                            layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageindex); } });

                                                    }, 10);
                                                },
                                                btn2: function () { }
                                            })
                                        } else
                                            layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    } else
                                        layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageindex); } });

                                }, 10);
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Retweet':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        if (data[0].Owner_CardType == 1) { layer.msg("临时车请使用设置有效期"); return; }
                        if (data[0].Owner_CardType == 4) { layer.msg("免费车请使用设置有效期"); return; }

                        if (data[0].Owner_CardType == 2) {
                            layer.open({
                                type: 2, id: 1,
                                title: "延期/充值",
                                content: '/Owner/Retweet?Owner_No=' + data[0].Owner_No,
                                area: getIframeArea(['920px', '610px']),
                                maxmin: true
                            });
                            return;
                        }

                        pager.paysuccess(data[0].Owner_No);

                        break;
                    case 'BathPayDate':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }

                        var ownerNoList = [];
                        var spanceNoList = [];
                        for (var i = 0; i < data.length; i++) {
                            var item = data[i];
                            if (cardtype == null) cardtype = item.Owner_CardType;
                            else if (cardtype != item.Owner_CardType) {
                                layer.msg("请选择相同车牌类型", { time: 1000 });
                                return;
                            }
                            ownerNoList.push(item.Owner_No);
                            spanceNoList.push(item.Owner_Space);
                        }

                        localStorage.setItem("BathPayDate_OwnerNoList", JSON.stringify(ownerNoList));
                        localStorage.setItem("BathPayDate_SpaceNoList", JSON.stringify(spanceNoList));

                        layer.open({
                            type: 2, id: 1,
                            title: "批量延期",
                            content: '/Owner/BathPayDate?cardtype=' + cardtype,
                            area: getIframeArea(['600px', '530px']),
                            maxmin: true
                        });

                        break;
                    case 'Vaild':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var cardtype = null;
                        var ownerNoList = [];
                        for (var i = 0; i < data.length; i++) {
                            var item = data[i];
                            if (cardtype == null) cardtype = item.Owner_CardType;
                            else if (cardtype != item.Owner_CardType) {
                                layer.msg("请选择相同车牌类型", { time: 1000 });
                                return;
                            }
                            ownerNoList.push(item.Owner_No);
                        }
                        localStorage.setItem("Vaild_OwnerNoList", JSON.stringify(ownerNoList));
                        layer.open({
                            type: 2, id: 1,
                            title: "设置有效期",
                            content: '/Owner/Vaild?cardtype=' + cardtype,
                            area: getIframeArea(['600px', '530px']),
                            maxmin: true
                        });
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 1000) { layer.msg("请选择1000条以内", { icon: 0, time: 2000 }); return; }

                        var NoList = [];
                        $.each(data, function (k, v) { NoList.push(v.Owner_No); });

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定启用选中车辆白名单功能?<br/>白名单启用成功后才可以在岗亭软件中下载到相机.",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/Owner/EnableWhiteList", { NoList: JSON.stringify(NoList) }, function (json) {
                                    if (json.success)
                                        layer.msg("启用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageindex); } });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 1000) { layer.msg("请选择1000条以内", { icon: 0, time: 2000 }); return; }

                        var NoList = [];
                        $.each(data, function (k, v) { NoList.push(v.Owner_No); });

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定禁用选中车辆白名单功能?<br/>白名单禁用成功后会从相机白名单中删除.",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/Owner/DisableWhiteList", { NoList: JSON.stringify(NoList) }, function (json) {
                                    if (json.success)
                                        layer.msg("禁用成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageindex); } });
                                }, "json");
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Export':

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/Owner/ExportExcel?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                    case 'Import':
                        layer.open({
                            type: 2, id: 1,
                            title: "导入车辆信息",
                            content: '/Owner/Import?r=' + Math.random(),
                            area: getIframeArea(['500px', '665px']),
                            maxmin: true
                        });
                        break;
                    case 'BathVaild':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }

                        var spaceNoList = [];
                        var ownerNoList = [];
                        $.each(data, function (k, v) {
                            spaceNoList.push(v.Owner_Space);
                            ownerNoList.push(v.Owner_No);
                        })
                        pager.spaceNoList = spaceNoList.join(",");
                        pager.ownerNoList = ownerNoList.join(",");
                        layer.open({
                            type: 2, id: 1,
                            title: "批量设置有效期",
                            content: '/Owner/BathVaild',
                            area: getIframeArea(['650px', '510px']),
                            maxmin: true
                        });
                        break;
                    case 'Bind':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }

                        var spaceNoList = [];
                        var ownerNoList = [];
                        $.each(data, function (k, v) {
                            spaceNoList.push(v.Owner_Space);
                            ownerNoList.push(v.Owner_No);
                        })
                        pager.spaceNoList = spaceNoList.join(",");
                        pager.ownerNoList = ownerNoList.join(",");
                        layer.open({
                            type: 2, id: 1,
                            title: "批量授权",
                            content: '/Owner/BathEditStopSpace',
                            area: getIframeArea(['650px', '510px']),
                            maxmin: true
                        });
                        break;
                    case 'Charge':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.msg("处理中", { icon: 16, time: 0 });
                        $.getJSON("/Owner/CheckSpace", { Owner_No: data[0].Owner_No }, function (json) {
                            if (json.success) {
                                layer.closeAll();
                                layer.open({
                                    type: 2, id: 1,
                                    title: "储值车充值",
                                    content: '/Owner/Charge?Owner_No=' + data[0].Owner_No,
                                    area: getIframeArea(['600px', '540px']),
                                    maxmin: true
                                });
                            } else
                                layer.msg(json.msg, { icon: 0, time: 1500 });
                        });
                        break;
                    case 'Refund':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        layer.closeAll();
                        layer.msg("处理中", { icon: 16, time: 0 });
                        $.getJSON("/Owner/RefundSpace", { Owner_No: data[0].Owner_No }, function (json) {
                            if (json.success) {
                                layer.closeAll();
                                layer.open({
                                    type: 2, id: 1,
                                    title: "储值车退费",
                                    content: '/Owner/Refund?Owner_No=' + data[0].Owner_No,
                                    area: getIframeArea(['680px', '510px']),
                                    maxmin: true
                                });
                            } else
                                layer.msg(json.msg, { icon: 0, time: 1500 });
                        });
                        break;
                    case 'DownLoadCarList':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        pager.carnoes = [];
                        $.each(data, function (k, v) {
                            pager.carnoes.push(v.Owner_CarCardNo);
                        })

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "相机白名单",
                            content: '/Owner/WhiteRecord?Act=Update&t=' + Math.random(),
                            area: getIframeArea(['1100px', '800px']),
                            maxmin: false
                        });
                        break;
                    case 'Pulldown':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1000) { layer.msg("请选择1000条以内", { icon: 0, time: 2000 }); return; }
                        var ownerNoList = [];
                        $.each(data, function (k, v) {
                            ownerNoList.push(v.Owner_No);
                        })

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定添加推送任务，重推固定车到线上?",
                            area: ["300px"],
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                setTimeout(function () {
                                    var json = pager.pushowner(ownerNoList);
                                    if (json.Success) {
                                        if (json.Data > 0) {
                                            var frmIndex = layer.open({
                                                id: 'Store',
                                                type: 0,
                                                title: "提示",
                                                content: "已成功添加 " + json.Data + " 个固定车推送任务，可到云车场记录查看",
                                                area: ["300px"],
                                                maxmin: false,
                                                shade: 0,
                                                closeBtn: 0,
                                                btn: ["云车场记录", "知道了"],
                                                yes: function (d, i) {
                                                    window.parent.global.gotoPage("CloudRecord/Index");
                                                    layer.close(frmIndex);
                                                },
                                                btn2: function () {
                                                    layer.close(frmIndex);
                                                },
                                                btn3: function () {
                                                    layer.close(frmIndex);
                                                }
                                            });
                                        } else
                                            layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                                    } else
                                        layer.msg(json.Message, { icon: 0, btn: ['确定'], time: 0, end: function () { pager.bindData(pager.pageindex); } });

                                }, 10);
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'SearchPassRecord':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("仅支持单选"); return; }
                        console.log(data[0].Owner_No)
                        layer.open({
                            type: 2,
                            id: "x_edit_iframe_inparkcar",
                            title: "场内记录",
                            content: '/Owner/OwnerCarInPark?Owner_No=' + data[0].Owner_No + "&r=" + Math.random(),
                            area: getIframeArea(['950px', '80%']),
                            maxmin: false
                        });
                        break;

                    case 'SetCarCardType':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var ownerNoList = [];
                        $.each(data, function (k, v) {
                            ownerNoList.push(v.Owner_No);
                        })
                        pager.ownerNoList = ownerNoList.join(",");

                        layer.open({
                            type: 2, id: 1,
                            title: "修改车牌类型",
                            content: '/Owner/SetCarCardType?t=' + Math.random(),
                            area: getIframeArea(['650px', '510px']),
                            maxmin: true
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox(table);
        });

        //两个时间，判断哪个时间大，则返回哪个时间
        function getMaxTime(time1, time2) {
            return time1 > time2 ? time1 : time2;
        }
    </script>
    <script>
        var pager = {
            dataField: null,
            sortField: null,
            orderField: null,
            pageIndex: 1,
            carnoes: null,
            moreOpsExpanded: false,
            init: function () {
                $.ajaxSettings.async = false;
                localStorage.removeItem("ownerarea");
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["Owner_StartTime0", "Owner_StartTime1"], { type: "datetime", range: true });
                _DATE.bind(layui.laydate, ["Owner_EndTime0", "Owner_EndTime1"], { type: "datetime", range: true });
                _DATE.bind(layui.laydate, ["Owner_AddTime0", "Owner_AddTime1"], { type: "datetime", range: true });

                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        var options = '';
                        json.data.forEach((item, index) => {
                            if (item.CarCardType_Type != 5 && item.CarCardType_Type != 6 && item.CarCardType_Status != 0)
                                options += '<option value="' + item.CarCardType_No + '">' + item.CarCardType_Name + '</option>';
                        });
                        $("#Owner_CardTypeNo").append(options);
                    }
                }, "json");

                layui.form.render();
                $(".input-clear-icon").not("#Owner_CarCardNo + .input-clear-icon").attr("style", "top: 50% !important;");
            },
            bindData: function (index, refrash) {
                if (!refrash) {
                    layer.closeAll();
                    localStorage.removeItem("ownerarea");
                }

                // 记录当前更多操作的展开状态
                this.moreOpsExpanded = $('#moreOps').hasClass('active');

                now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
                nowDate = new Date(new Date(now).Format("yyyy-MM-dd 00:00:00"));
                var conditionParam = $("#searchForm").formToJSON(true, function(data) {
                    return data;
                });
                conditionParam.SearchType = topBar.config.SearchType;

                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/Owner/GetOwnerList'
                    , where: {
                        conditionParam: JSON.stringify(conditionParam),
                        field: field,
                        order: order
                    }
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { if (!myVerify.check()) return; localStorage.removeItem("ownerarea"); pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            },
            paysuccess: function (Owner_No, callBack) {
                layer.closeAll();
                layer.msg("处理中", { icon: 16, time: 0 });
                $.post("/Owner/CheckNeedPayee", { Owner_No: Owner_No }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        layer.open({
                            type: 2, id: 10000,
                            title: "延期/充值",
                            content: '/Owner/Retweet?Owner_No=' + Owner_No,
                            area: getIframeArea(['920px', '610px']),
                            maxmin: true
                        });
                    } else {
                        if (json.data && json.data != null && json.data != "") {
                            if (callBack && callBack == 5) {
                                layer.open({
                                    title: "停车支付",
                                    type: 2, id: 1,
                                    area: getIframeArea(['95%', '95%']),
                                    maxmin: true,
                                    content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(json.data) + "&callBack=5&Owner_No=" + Owner_No,
                                    end: function () { }
                                });
                            }
                            else {
                                layer.open({
                                    type: 0,
                                    title: "消息提示",
                                    btn: ["去清缴费用", "取消"],
                                    shade: 0,
                                    content: json.msg,
                                    yes: function (res) {
                                        layer.open({
                                            title: "停车支付",
                                            type: 2, id: 1,
                                            area: getIframeArea(['95%', '95%']),
                                            maxmin: true,
                                            content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(json.data) + "&callBack=5&Owner_No=" + Owner_No,
                                            end: function () { }
                                        });
                                    }
                                });
                            }
                        } else {
                            layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                        }
                    }
                }, "json");
            },
            delowner: function (ownerNoList, inParkCar) {
                if (inParkCar == undefined || inParkCar == null) inParkCar = false;
                layer.msg("处理中", { icon: 16, time: 0 });
                $.ajaxSettings.async = false;
                var ret = {};
                $.ajax({
                    type: 'post',
                    url: '/Owner/DeleteOwner?r=' + Math.random(),
                    dataType: 'json',
                    data: { ownerNoArray: ownerNoList.join(','), inParkCar: inParkCar },
                    success: function (json) {
                        ret = json;
                    },
                    complete: function () {
                        $.ajaxSettings.async = true;
                    },
                    error: function () {
                        layer.msg("系统异常", { icon: 2 });
                    }
                });
                return ret;
            },
            pushowner: function (ownerNoList) {
                layer.msg("处理中", { icon: 16, time: 0 });
                $.ajaxSettings.async = false;
                var ret = {};
                $.ajax({
                    type: 'post',
                    url: '/Owner/PushOnlineOwner?r=' + Math.random(),
                    dataType: 'json',
                    data: { ownerNoArray: ownerNoList.join(',') },
                    success: function (json) {
                        ret = json;
                    },
                    complete: function () {
                        $.ajaxSettings.async = true;
                    },
                    error: function () {
                        layer.msg("系统异常", { icon: 2 });
                    }
                });
                return ret;
            }
            , openParkOrderDetail: function (ParkOrder_No) {
                layer.open({
                    type: 2,
                    title: "停车详情",
                    content: "/Owner/InParkCarDetail?ParkOrder_No=" + ParkOrder_No,
                    area: ["90%", "90%"],
                    maxmin: false
                });
            }
        }

        function getStatus(BeginTime, EndTime) {
            if (BeginTime && EndTime) {
                if (new Date(EndTime.replace(/-/g, "/")) >= nowDate) {
                    if (new Date(BeginTime.replace(/-/g, "/")) > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }
    </script>
</body>

</html>
