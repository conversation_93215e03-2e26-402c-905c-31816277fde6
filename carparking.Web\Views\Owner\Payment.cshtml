﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车支付</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        span { color: black; line-height: 33px; }

        label { line-height: 15px; }

        .ibox-content { padding: 15px; overflow: auto; }

        .input-group { position: relative; display: table; border-collapse: separate; }

        .btnUnit { border-color: #f2eeee !important; border-top-right-radius: 5px; border-bottom-right-radius: 5px; color: #5db587 !important; margin-left: 0 !important; background-color: transparent !important; }

        xm-select > .xm-tips { padding: 0 7px !important; }

        .xtime { color: #de0c3d; }
        span.question { right: 1px; top: 1px; cursor: pointer; line-height: 32px; text-align: center; color: #ff6a00; border-top-right-radius: 2px; border-bottom-right-radius: 2px; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="layui-row layui-form">
                <div class="layui-col-xs5 ">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">订单号：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_No" style="white-space:nowrap;"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">车牌号：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_CarNo" style="white-space:nowrap;"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">车牌类型：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_CarType"></span></div>
                    </div>
                    @*<div class="layui-row">
                    <div class="layui-col-xs3"><span class="control-label">停车场：</span></div>
                    <div class="layui-col-xs7"><span id="ParkOrder_ParkName"></span></div>
                    </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">入场时间：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_EnterTime"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">停车时长：</span></div>
                        <div class="layui-col-xs8"><span id="ParkOrder_SumTime"></span>&nbsp;<span class="spantime hide">（<t class="xtime">60</t>秒后刷新）</span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">应付金额：</span></div>
                        <div class="layui-col-xs7" id="errorLoadPrice">
                            <span id="ParkOrder_TotalAmount">0</span>元
                        </div>
                    </div>
                     <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠金额：</span></div>
                        <div class="layui-col-xs7"><span id="CouponSolution_MoneyValue">0</span>元</div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">储值抵扣：</span></div>
                        <div class="layui-col-xs7"><span id="chuzhiamount">0</span>元</div>
                    </div>
                    <div class="layui-row penaltyamount layui-hide">
                        <div class="layui-col-xs3"><span class="control-label">充电滞留：</span></div>
                        <div class="layui-col-xs7"><span id="penaltyamount">0</span>元</div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">计费详情：</span></div>
                        <div class="layui-col-xs7"><span class="question fa fa-table" title="点击查看计费详情"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label" style="color:red; font-weight:800;">现金实收：</span></div>
                        <div class="layui-col-xs7" id="verifyCheck" style="height:43px;">
                            <div class="input-group" style="margin-left: -8px;">
                                <input type="text" class="layui-input v-null v-float" maxlength="8" min="0" max="********" value="0" data-min="0.01" data-max="********" id="ParkOrder_PayedAccount" name="ParkOrder_PayedAccount" autocomplete="off" placeholder="请填写收费金额（元）" style="padding-left: 8px !important;">
                                <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                            </div>
                            <label class="focus valid"></label>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">备注：</span></div>
                        <div class="layui-col-xs7" style="margin-left: -8px;">
                            <input type="text" class="layui-input" maxlength="50" id="ParkOrder_Remark" name="ParkOrder_Remark" autocomplete="off">
                        </div>
                    </div>
                  
                </div>
                <div class="layui-col-xs7">
                    <div class="alert alert-info layui-col-xs12">
                        提示：点击图片查看大图
                    </div>
                    <div>
                        <a id="linkEnterImgPath" href="javascript:;" target="_blank"><img src="../../Static/img/nophoto.jpg" style="width: 100%; max-height: 380px;" /></a>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-xs5" style="margin-top: 15px;">
                <div class="layui-col-xs12 layui-col-xs-offset3">
                    <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> 支付</button>&nbsp;&nbsp;
                    <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplstatusno">
        {{if ParkOrder_StatusNo==199}}
        <span>预入场</span>
        {{else ParkOrder_StatusNo==200 && ParkOrder_OutType==0}}
        <span>已入场</span>
        {{else ParkOrder_StatusNo==200 && ParkOrder_OutType==1}}
        <span>预出场</span>
        {{else ParkOrder_StatusNo==201}}
        <span>已出场</span>
        {{else ParkOrder_StatusNo==202}}
        <span>自动关闭</span>
        {{else ParkOrder_StatusNo==203}}
        <span>场内关闭</span>
        {{else ParkOrder_StatusNo==204}}
        <span>欠费出场</span>
        {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();
        var selCoupon = null;
        var xmSelect = null;
        var layuiForm = null;
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['element', 'form', 'xmSelect'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            pager.init();
        });
    </script>
    <script>
        //var index = parent.layer.getFrameIndex(window.name);
        var Owner_No = decodeURIComponent($.getUrlParam("Owner_No"));
        var ParkOrder_No = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var callBack = decodeURIComponent($.getUrlParam("callBack"));
        var interval = null;
        var timeInterval = null;
        var time = 60;

        var pager = {
            couponIDes: "",
            couponIDes2: "",
            uselist: null,
            chuzhiamount: 0,
            couponList: null,
            parktimemin: null,
            accMoney: 0,
            calcDetail: null,
            init: function () {
                this.bindData();
                this.bindEvent();
                this.bindPower();
                this.loadPrice();
            },

            //数据绑定
            bindData: function (index) {
                layer.msg('计费中...', { icon: 16, time: 0 });
                $(".spantime").addClass("hide");
                pager.countTime();
                if (Owner_No != null) {
                    $.ajax({
                        type: 'post',
                        url: '/Owner/CheckNeedPayee',
                        dataType: 'json',
                        data: { Owner_No: Owner_No },
                        success: function (json) {
                            if (json.success) {
                                $(".spantime").removeClass("hide");
                                var sumMoney = json.data.summoney;
                                if (sumMoney && sumMoney > 0) {
                                    var parkorder = json.data.parkorder;
                                    var owner = json.data.owner;
                                    var carno = json.data.carno;
                                    $("#ParkOrder_No").html(json.data.ordernoes);
                                    $("#ParkOrder_CarNo").html(carno);
                                    $("#ParkOrder_CarType").html(owner.Owner_CardName);
                                    $("#ParkOrder_ParkName").html(json.data.parkname);
                                    $("#ParkOrder_EnterTime").html(parkorder.ParkOrder_EnterTime);
                                    if (parkorder.ParkOrder_EnterImgPath != null && parkorder.ParkOrder_EnterImgPath != "") {
                                        $("#linkEnterImgPath").attr("href", PathCheck(decodeURIComponent(parkorder.ParkOrder_EnterImgPath)))[0].children[0].src = PathCheck(decodeURIComponent(parkorder.ParkOrder_EnterImgPath));
                                    }
                                    var endtime = new Date($.ajax({ async: false }).getResponseHeader("Date"));
                                    var zhTimes = _DATE.getZhTimes(new Date(parkorder.ParkOrder_EnterTime), endtime);
                                    $("#ParkOrder_SumTime").html(zhTimes);

                                    $("#errorLoadPrice").html('<span id="ParkOrder_TotalAmount">' + sumMoney + '</span>元'); //总共需支付金额
                                    pager.accMoney = sumMoney;
                                    pager.calcDetail = json.data.calcdetailList;
                                    $("#CouponSolution_MoneyValue").html(json.data.couponmoney);
                                    $("#penaltyamount").html(json.data.penaltyamount);
                                    if (json.data.penaltyamount > 0) {
                                        $(".penaltyamount").removeClass("layui-hide");
                                    } else {
                                        $(".penaltyamount").removeClass("layui-hide").addClass("layui-hide");
                                    }
                                    $("#ParkOrder_PayedAccount").val(json.data.payedmoney);
                                    layui.form.render("select");
                                    $("#ParkOrder_PayedAccount").focus();
                                    layer.closeAll();
                                } else {
                                    $("#ParkOrder_PayedAccount").val(0);
                                    layui.form.render("select");
                                    $("#ParkOrder_PayedAccount").focus();
                                    layer.msg('无需缴费，请关闭重试');
                                }
                            }else{
                                layer.msg('计费失败：' + json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('订单ID无效', { icon: 0 });
                }
            },
            bindEvent: function () {
                $('#ParkOrder_PayedAccount').on("inttext", function () {
                    var value = $(this).val();
                    if (value == 0) {
                        $(this).val(0);
                        $(this).removeClass("border-red");
                    }
                    else {
                        var max = parseFloat($(this).attr("data-max"));
                        var min = parseFloat($(this).attr("data-min"));
                        if (value > max) value = max;
                        if (value < min) value = min;
                        var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
                        if (!reg.test(value)) {
                            value = parseFloat(value).toFixed(2);
                        }
                        $(this).val(value);
                        $(this).removeClass("border-red");
                    }
                });

                $('#ParkOrder_PayedAccount').on("blur", function () {
                    var value = $(this).val();
                    if (value == 0) {
                        $(this).val(0);
                        $(this).removeClass("border-red");
                    }
                    else {
                        var max = parseFloat($(this).attr("data-max"));
                        var min = parseFloat($(this).attr("data-min"));
                        if (value > max) value = max;
                        if (value < min) value = min;
                        var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
                        if (!reg.test(value)) {
                            value = parseFloat(value).toFixed(2);
                        }
                        $(this).val(value);
                        $(this).removeClass("border-red");
                    }
                });

                $(".question").click(function () {
                    layer.open({
                        id: 1,
                        title: "计费详情",
                        type: 2,
                        area: getIframeArea(['800px', '620px']),
                        fix: false, //不固定
                        maxmin: false,
                        content: 'PaymentDetail'
                    });
                })

                $("#Cancel").click(function () { parent.layer.closeAll(); });
                $("#Save").click(function () {
                    //验证表单
                    if (!myVerify.check()) return;
                    var payedMonty = $("#ParkOrder_PayedAccount").val();
                    if (payedMonty < 0) { layer.msg("请输入正确金额"); return; }
                    if (payedMonty > pager.accMoney) { layer.tips("支付金额不能大于应付金额", $("#ParkOrder_PayedAccount"), { time: 2000 }); return; }

                    //询问框
                    layer.confirm('确定要缴费？', {
                        title: "提示",
                        icon: "3",
                        btn: ['确定', '取消'] //按钮
                    }, function () {

                        pager.paying();

                    }, function () { });

                });

                //$('#PayOrder_Code').bind('keypress', function(event) {
                //    if (event.keyCode == "13") {
                //        event.preventDefault();
                //        //回车执行支付
                //        pager.paying();
                //    }
                //});

            },
            bindPower: function () {
                var global = window.parent.parent.global || window.parent.parent.parent.global;
                global.getBtnPower(window, function (pagePower) {
                    $(".btnUnit").removeClass("layui-hide")
                });
            },
            loadPrice: function () {
                if (interval != null) {
                    clearInterval(interval);
                    interval = null;
                }
                interval = setInterval(function () { pager.bindData(); }, 60 * 1000);
            },
            countTime: function () {
                time = 60;
                if (timeInterval != null) {
                    clearInterval(timeInterval);
                    timeInterval = null;
                }
                timeInterval = setInterval(function () {
                    if (time > 0) time--;
                    else { time = 60; }
                    $(".xtime").html(time);
                }, 1000);
            },
            paying: function () {
                $("#Save").attr("disabled", true);
                //if( $("#PayOrder_Code").val()!="") $("#PayOrder_Code").attr("disabled", true).blur();
                layer.msg('支付中...', { icon: 16, time: 0 });
                $.ajax({ //线上缴费下发通知（注销使用的优惠券）
                    type: 'post',
                    url: '/Owner/SendNoticeForPayed',
                    dataType: 'json',
                    data: {
                        Owner_No: Owner_No, 
                        ParkOrder_No: $("#ParkOrder_No").text(),
                        receAmount: $("#ParkOrder_TotalAmount").text(), 
                        remark: $("#ParkOrder_Remark").val(), 
                        paidAmount: $("#ParkOrder_PayedAccount").val(),
                        chuzhiamount: pager.chuzhiamount
                    },
                    success: function (json) {
                        if (json.success) {
                            layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                layer.closeAll();
                                parent.pager.openChargeFrm(Owner_No);
                            });
                        }
                        else {
                            $("#Save").removeAttr("disabled");
                            layer.msg('支付失败：' + json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                        }
                    },
                    error: function () {
                        $("#Save").removeAttr("disabled");
                        //if( $("#PayOrder_Code").val()!="") $("#PayOrder_Code").removeAttr("disabled").focus();
                        layer.msg('系统错误', { icon: 2 });
                    }
                });
            }
        };
    </script>
</body>
</html>
