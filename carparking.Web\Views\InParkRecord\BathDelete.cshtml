﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量关闭订单</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/
        .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #eee; opacity: 1; color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        .red { color: #de163b; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row chuzhi">
                <div class="layui-col-xs3 edit-label ">入场时间始</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-submit" maxlength="80" id="ParkOrder_EnterTime" name="ParkOrder_EnterTime" value="@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">入场时间止</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-submit" maxlength="10" id="ParkOrder_OutTime" name="ParkOrder_OutTime" />
                </div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>确定</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1214" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function() {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");
            pager.init()
        });


    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramCarNo = $.getUrlParam("Car_No");
        var paramType = $.getUrlParam("type");
        var PayFreeType = 0;
        var dt = new Date().Format("yyyy-MM-dd hh:mm:ss");
        var dt2 = new Date().Format("yyyy-MM-dd hh:mm:ss");
        var index = parent.layer.getFrameIndex(window.name);
        var isOpen = true;

        var pager = {
            init: function() {
                $.ajaxSettings.async = false; //同步执行
                this.bindData();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function() {
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime", "ParkOrder_OutTime"], { type: "datetime", range: true});
            },
            //数据绑定
            bindData: function() {

            },
            bindEvent: function() {
                $("#Cancel").click(function() { parent.layer.close(index); });
                $("#Save").click(function() {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function(data) {
                        return data;
                    });
                    param.ParkOrder_CarType = 0;

                    var msg = "执行该操作，会影响岗亭车辆进出场，请谨慎操作！即将关闭停车订单，时间段为：<t class='red'>" + $("#ParkOrder_EnterTime").val() + "</t>～<t class='red'>" + $("#ParkOrder_OutTime").val() + "</t>"
                    layer.open({
                        type: 0,
                        title: "关闭停车订单提示",
                        btn: ["确定", "取消"],
                        content: msg + "，确定吗?",
                        yes: function(res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Save").attr("disabled", true);
                            $.getJSON("/InParkRecord/GetBathDelete", { jsonModel: JSON.stringify(param) }, function(json) {
                                if (json.success) {
                                    if (json.data <= 0) {
                                        layer.msg("未找到该时间段的停车数据", { icon: 1, time: 1500 }, function() { $("#Save").removeAttr("disabled");});
                                        return;
                                    }

                                    layer.open({
                                        type: 0,
                                        title: "关闭停车订单提示",
                                        btn: ["确定", "取消"],
                                        content: "即将删除<t class='red'>" + json.data + "</t>条停车订单，关闭后<t class='red'>停车订单无法恢复</t>！并且<t class='red'></t>，请谨慎操作！确定继续关闭吗?",
                                        yes: function(res) {
                                            layer.msg("处理中", { icon: 16, time: 0 });
                                            $("#Save").attr("disabled", true);
                                            param.ParkOrder_CarType = 1;
                                            $.getJSON("/InParkRecord/GetBathDelete", { jsonModel: JSON.stringify(param) }, function(d) {
                                                if (d.success) {
                                                    layer.msg("关闭成功", { icon: 1, time: 1500 }, function () {
                                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                                    });
                                                } else {
                                                    layer.msg(d.msg, { icon: 0 });
                                                    $("#Save").removeAttr("disabled");
                                                }
                                            });
                                        },
                                        btn2: function() { $("#Save").removeAttr("disabled"); },
                                        cancel: function () { $("#Save").removeAttr("disabled"); }
                                    })
                                } else {
                                    layer.msg(json.msg, { icon: 0 });
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        },
                        btn2: function() { $("#Save").removeAttr("disabled"); }
                    })
                });

            }
        };


    </script>
</body>
</html>
