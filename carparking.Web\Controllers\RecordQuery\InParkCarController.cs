﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using carparking.Common;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Linq;
using System.Text;
using System.Data;
using System.IO;
using carparking.BLL.Cache;
using carparking.BLL;

namespace carparking.Web.Controllers
{
    public class InParkCarController : BaseController
    {

        private readonly Microsoft.Extensions.Hosting.IHostEnvironment _hostingEnvironment;
        public InParkCarController(Microsoft.Extensions.Hosting.IHostEnvironment hostingEnvironment)
        {
            _hostingEnvironment = hostingEnvironment;
        }

        public IActionResult Index()
        {
            if (!Powermanage.PowerCheck("InParkCar", PowerEnum.View.ToString(), false, true, lgAdmins))
                return new EmptyResult();
            ViewBag.ParkKey = AppBasicCache.GetParking?.Parking_Key ?? "";
            return View();
        }

        public IActionResult Edit()
        {
            if (!Powermanage.PowerCheck("InParkCar", PowerEnum.Update.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult Expired()
        {
            if (!Powermanage.PowerCheck("InParkCar", PowerEnum.Delete.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            ViewBag.ExpireDate = DateTimeHelper.GetNowTime().AddDays(-1).ToString("yyyy-MM-dd 23:59:59");
            //var policy = BLL.PolicyPark.GetEntity(parking.Parking_No);

            //if (policy != null && policy.PolicyPark_OccupyDay > 0)
            //{
            //    ViewBag.ExpireDate = DateTimeHelper.GetNowTime().AddDays(-policy.PolicyPark_OccupyDay.Value).ToString("yyyy-MM-dd 23:59:59");
            //}
            return View();
        }

        /// <summary>
        /// 查询停车订单列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public IActionResult GetParkOrderList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkCar", PowerEnum.Search.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                if (pageSize > 1000)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                if (model == null)
                {
                    return Ok(new Model.PageResult(-1, "参数错误", 0, null));
                }

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and ParkOrder_StatusNo=200 ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");
                if (model.ParkOrder_Lock != null)
                    sqlwhere.Append($" and ParkOrder_Lock = @ParkOrder_Lock ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarCardType))
                    sqlwhere.Append($" and ParkOrder_CarCardType = @ParkOrder_CarCardType ");
                if (!string.IsNullOrEmpty(model.ParkOrder_ParkAreaNo))
                    sqlwhere.Append($" and ParkOrder_ParkAreaNo = @ParkOrder_ParkAreaNo ");

                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime between @ParkOrder_EnterTime0 AND @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime <= @ParkOrder_EnterTime1 ");

                object parameters = new
                {
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? "%" + model.ParkOrder_CarNo + "%" : null,
                    ParkOrder_Lock = model.ParkOrder_Lock,
                    ParkOrder_CarCardType = !string.IsNullOrEmpty(model.ParkOrder_CarCardType) ? model.ParkOrder_CarCardType : null,
                    ParkOrder_ParkAreaNo = !string.IsNullOrEmpty(model.ParkOrder_ParkAreaNo) ? model.ParkOrder_ParkAreaNo : null,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0 != null ? model.ParkOrder_EnterTime0.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1 != null ? model.ParkOrder_EnterTime1.Value.ToString("yyyy-MM-dd HH:mm:ss") : null
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder._GetList<Model.ParkOrder>("*", sqlwhere.ToString(), pageIndex, pageSize, "ParkOrder_EnterTime", 0, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询停车订单列表", "查询停车订单列表发生异常:" + ex.ToString());

                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return Ok(oModel);
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        public IActionResult GetParkOrderByNo(string ParkOrder_No)
        {
            try
            {
                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                if (model == null)
                {
                    return ResOk(false, "订单不存在");
                }

                List<Model.PayOrder> payOrders = BLL.PayOrder.GetAllEntity("*", $"PayOrder_ParkOrderNo='{ParkOrder_No}'");

                return ResOk(true, "", new { model = model, payOrders = payOrders, parking = parking });
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"失败:{ex.Message}", SecondIndex.InParkCar);
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 删除场内车辆
        /// </summary>
        public IActionResult DelOrder(string ParkOrder_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkCar", PowerEnum.Delete.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrder model = BLL.ParkOrder.GetEntity(ParkOrder_No);
                if (model == null || model.ParkOrder_StatusNo != Model.EnumParkOrderStatus.In) { return ResOk(false, "场内车辆订单不存在"); }

                var confirmOrders = BLL.ConfirmRelease.Results.Values.Where(x => x.passres?.parkorderno == ParkOrder_No).ToList();
                if (confirmOrders.Count > 0)
                {
                    var carnoes = confirmOrders.Select(x => x.passres.carno).ToList();
                    return ResOk(false, $"删除失败，请先处理正在出场的车辆：{string.Join(",", carnoes)}");
                }

                model.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity(model.ParkOrder_No);
                string areaNo = string.Empty;
                details.ForEach(x =>
                {
                    if (x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In)
                    {
                        x.OrderDetail_StatusNo = Model.EnumParkOrderStatus.InClose;
                        areaNo = x.OrderDetail_ParkAreaNo;
                    }
                });

                //一位多车处理
                List<Model.ParkOrder> poModelLst = null;

                BLL.ParkOrder.OutParkForMultiCar(model, null, areaNo, (pLst, dLst) =>
                {
                    poModelLst = BLL.ParkOrder.ChangeOrderLst(model, pLst);
                    details = BLL.ParkOrder.ChangeDetailLst(details, dLst);
                });

                var res = BLL.ParkOrder.CarInComplete(poModelLst, details);

                if (res > 0)
                {
                    BLL.PushEvent.CloseCar(parking.Parking_Key, model, "删除场内车辆记录");
                    BLL.PushEvent.SendParkSpace(parking.Parking_No);

                    Push(Model.API.PushAction.Edit, new Model.API.PushResultParse.UpdateParkOrder() { Item1 = poModelLst, Item2 = details }, new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.InParkRecord, Desc: $"删除场内车{model.ParkOrder_CarNo}");

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"成功:{TyziTools.Json.ToString(model)}", SecondIndex.InParkCar);
                    return ResOk(true, "删除成功");
                }
                else
                {
                    return ResOk(false, "删除失败");
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "删除场内车辆", "删除场内车辆发生异常:" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 修改车牌
        /// </summary>
        public IActionResult UpdateCarNo(string jsonModel)
        {
            try
            {
                Model.ParkOrder obj = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (obj == null)
                {
                    return ResOk(false, "参数错误");
                }
                if (string.IsNullOrEmpty(obj.ParkOrder_No)) { return ResOk(false, "订单号错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_CarNo)) { return ResOk(false, "车牌号错误"); }
                if (string.IsNullOrEmpty(obj.ParkOrder_CarCardType)) { return ResOk(false, "车牌类型错误"); }
                if (obj.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间错误"); }
                if (obj.ParkOrder_EnterTime > DateTimeHelper.GetNowTime()) { return ResOk(false, "入场时间不能大于当前时间"); }

                string ParkOrder_No = obj.ParkOrder_No;
                string NewCarNo = obj.ParkOrder_CarNo;
                if (string.IsNullOrEmpty(NewCarNo) || NewCarNo.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                if (!Utils.IsZhNumEn(NewCarNo.Trim()))
                {
                    return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                }
                NewCarNo = NewCarNo.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", "");//字母O替换数字0
                NewCarNo = NewCarNo.ToUpper();
                string NewCarCardType = obj.ParkOrder_CarCardType;
                DateTime? NewEnterTime = obj.ParkOrder_EnterTime;

                Model.ParkOrder model = BLL.ParkOrder._GetEntityByNo(new Model.ParkOrder(), ParkOrder_No);
                if (model == null) { return ResOk(false, "停车订单不存在"); }

                var res = BLL.ParkOrder.UpdateOrder(model, NewCarNo, NewCarCardType, NewEnterTime, obj.ParkOrder_StatusNo, obj.ParkOrder_Remark, true, CheckSpaceNumByEnterTime: false);
                if (res.code == 1)
                {
                    var confirmOrder = ConfirmRelease.Results.Where(kv => kv.Value.passres?.parkorderno == ParkOrder_No).FirstOrDefault();
                    if (confirmOrder.Value != null)
                    {
                        if (confirmOrder.Value.passres != null)
                        {
                            confirmOrder.Value.passres.carno = NewCarNo;
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(NewCarNo, ParkOrder_No, CloseNoInPark: false, mode: 6);
                        }
                    }

                    DataCache.ParkOrder.Set(model.ParkOrder_No, model);
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"成功:{ParkOrder_No},{NewCarNo},{NewCarCardType},{jsonModel}", SecondIndex.InParkCar);
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"失败:{ParkOrder_No},{NewCarNo},{NewCarCardType},{jsonModel}", SecondIndex.InParkCar);
                    return ResOk(false, res.msg);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "修改车牌", "修改车牌发生异常:" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 查询逾期的车辆场内记录
        /// </summary>
        /// <returns></returns>
        public IActionResult GetExpiredList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkCar", PowerEnum.Delete.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                JObject obj = Utils.ClearModelRiskSQL(conditionParam);
                if (obj == null)
                {
                    return Ok(oModel);
                }

                string enterTime = obj["date"]?.ToString() ?? "";
                string chkExp = obj["chkExp"]?.ToString() ?? "";
                string carnos = obj["carnos"]?.ToString() ?? "";
                if (string.IsNullOrEmpty(enterTime) || !DateTime.TryParse(enterTime, out var d)) { oModel.code = 404; oModel.msg = "日期格式错误"; return Ok(oModel); }

                string[] carnoArray = carnos.Split(',');

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and ParkOrder_StatusNo=200 ");
                sqlwhere.Append($" and ParkOrder_EnterTime<='{enterTime}' ");
                if (!chkExp.ToLower().Equals("true") && carnoArray.Length > 0)
                {
                    sqlwhere.Append($" and ParkOrder_CarNo not in ('{string.Join("','", carnoArray)}')");
                }

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder.GetList(200, "*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "查询停车订单列表", "查询停车订单列表发生异常:" + ex.ToString());

                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return Ok(oModel);
        }

        /// <summary>
        /// 读取模板中的车牌号
        /// </summary>
        /// <returns></returns>
        public IActionResult GetExcelCarNos()
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return ResOk(false, "无权限"); }

                //解析导入的Excel文件
                var postForm = Request.Form;
                IFormFileCollection formFiles = Request.Form.Files;
                if (formFiles == null || formFiles.Count == 0) { return ResOk(false, "未选择模板文件"); }

                long size = formFiles.Sum(f => f.Length);
                var rb = new ResultBase();
                ExcelToDataTable(formFiles, out var carnos, ref rb);
                //文件不合符导入条件
                if (rb.Code == "1") { return ResOk(false, rb.Message, null, "1"); }
                if (carnos == null || carnos.Count == 0) { return ResOk(false, "模板中无车牌号"); }

                return ResOk(true, "", carnos);
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "逾期车牌处理", "读取模板失败：" + ex.ToString());
                return ResOk(false, ex.Message);
            }
        }

        private DataTable ExcelToDataTable(IFormFileCollection hfc, out List<string> carnos, ref ResultBase rb)
        {
            carnos = new List<string>();

            string webRootPath = _hostingEnvironment.ContentRootPath;
            string contentRootPath = _hostingEnvironment.ContentRootPath;

            //string imgPath = "\\wwwroot\\Data\\" + DateTimeHelper.GetNowTime().ToString("yyyyMMddhhmmssf") + hfc[0].FileName;
            //string physicalPath = webRootPath + imgPath;

            if (!AppBasicCache.IsWindows) { Common.LocalFile.CreateDirectory("/mnt/sda1/b30/import"); }
            string imgPath = (AppBasicCache.IsWindows ? "\\wwwroot\\Data\\" : "/mnt/sda1/b30/import/") + DateTimeHelper.GetNowTime().ToString("yyyyMMddhhmmssf") + hfc[0].FileName;
            string physicalPath = AppBasicCache.IsWindows ? webRootPath + imgPath : imgPath;

            using (var stream = new FileStream(physicalPath, FileMode.Create))
            {
                hfc[0].CopyTo(stream);
            }
            //读取excel到Table
            NPOIExcelHelper npoi = new NPOIExcelHelper(physicalPath);
            DataTable dtExcel = npoi.ImportToDataTableHasDate("", true, 0);
            if (dtExcel == null) { BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"失败:读取Excel数据失败", SecondIndex.InParkCar); rb = new ResultBase() { Code = "1", Message = "读取Excel数据失败", Success = false }; return null; }

            //限制每次导入最大2000条
            if (dtExcel.Rows.Count > 10000) { BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"失败:每次最多导入10000条", SecondIndex.InParkCar); rb = new ResultBase() { Code = "1", Message = "每次最多导入10000条", Success = false }; return null; }

            int curIndex = 0;
            StringBuilder errMsg = new StringBuilder();    //错误记录
            List<string> orderNoList = Utils.GetRandomLst(dtExcel.Rows.Count * 2);
            try
            {
                foreach (DataRow dr in dtExcel.Rows)
                {
                    curIndex = dtExcel.Rows.IndexOf(dr) + 1; //当前数据行
                    if (dr[0] != null && !string.IsNullOrEmpty(dr[0].ToString()))
                        carnos.Add(dr[0].ToString());
                }

                Utils.DelFile(physicalPath); //删除Excel
                return dtExcel;
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Import, $"失败:第{curIndex}行,存在数据格式错误！{ex.Message}", SecondIndex.InParkCar);
                rb = new ResultBase()
                {
                    Code = "1",
                    Message = $"第{curIndex}行,存在数据格式错误！{ex.Message}",
                    Success = false
                };
                Utils.DelFile(physicalPath);
                return null;
            }
        }

        /// <summary>
        /// 导出停车记录
        /// </summary>
        public FileContentResult Export()
        {
            try
            {
                if (!Powermanage.AdminCheck(lgAdmins)) { return null; }

                string ordernos = Request.Form.ContainsKey("ordernos") ? Request.Form["ordernos"].ToString() : "";

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" ParkOrder_StatusNo=200 ");
                sqlwhere.Append($" and ParkOrder_No in ('{ordernos}')");

                var data = BLL.ParkOrder.GetAllEntity("*", sqlwhere.ToString());
                if (data == null || data.Count == 0)
                {
                    HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true", new Microsoft.AspNetCore.Http.CookieOptions { Expires = DateTimeOffset.Now.AddSeconds(10), IsEssential = true });
                    return File(new byte[0], "application/vnd.ms-excel", $"逾期车辆停车记录_{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd")}.xlsx");
                }
                else
                {
                    var cardList = BLL.CarCardType.GetAllEntity("*", $"CarCardType_ParkNo='{parking.Parking_No}'");
                    var carTypeList = BLL.CarType.GetAllEntity("*", $"CarType_ParkNo='{parking.Parking_No}'");
                    var carList = BLL.Car.GetAllEntity("Car_CarNo,Car_OwnerNo,Car_OwnerName", $"Car_ParkingNo='{parking.Parking_No}'");
                    //重组数据
                    foreach (var item in data)
                    {
                        //导出显示名称
                        item.ParkOrder_ParkNo = parking.Parking_Name;
                        item.ParkOrder_Remark = Utils.TypeConvert.ParkOrderStatus(item.ParkOrder_StatusNo.Value.ToString());
                        item.ParkOrder_UserNo = carList?.Find(x => x.Car_CarNo == item.ParkOrder_CarNo)?.Car_OwnerName;
                    }
                }

                #region 创建DataTable
                DataTable dataTable = Utils.ListToDataTable(data);
                Dictionary<string, string> dic = new Dictionary<string, string>();
                dic.Add("ParkOrder_CarNo", "车牌号");
                dic.Add("ParkOrder_UserNo", "车主名称");
                dic.Add("ParkOrder_CarCardTypeName", "车牌类型");
                dic.Add("ParkOrder_CarTypeName", "车牌颜色");
                dic.Add("ParkOrder_EnterTime", "入场时间");
                dic.Add("ParkOrder_ParkAreaName", "停车区域");
                dic.Add("ParkOrder_EnterPasswayName", "入口名称");
                dic.Add("ParkOrder_EnterAdminName", "入口操作员");
                dic.Add("ParkOrder_EnterRemark", "入场备注");
                dic.Add("ParkOrder_Remark", "订单状态");

                for (int i = 0; i < dic.Keys.ToList().Count; i++)
                {
                    string key = dic.Keys.ToList()[i];
                    dataTable.Columns[key].SetOrdinal(i);
                }

                var columnWidths = new int[] { 12, 12, 12, 12, 20, 12, 15, 15, 15, 15 };
                #endregion

                #region 填充EXCEL数据

                foreach (DataColumn dc in dataTable.Clone().Columns)
                {
                    if (!dic.ContainsKey(dc.ColumnName)) //删除不显示列
                    {
                        dataTable.Columns.Remove(dc.ColumnName);
                        dataTable.AcceptChanges();
                    }
                    else //修改列名
                    {
                        dataTable.Columns[dc.ColumnName].ColumnName = dic[dc.ColumnName];
                    }
                }

                byte[] bt = null;

                if (dataTable.Rows.Count > 5000)
                    bt = NPOIExcelHelper.ExportToExcel2(dataTable);
                else
                    bt = NPOIExcelHelper.ToExport(dataTable, (index, sheet) => { sheet.SetColumnWidth(index, columnWidths[index] * 256); });

                HttpHelper.HttpContext.Response.Cookies.Append("fileDownload", "true",
                   new Microsoft.AspNetCore.Http.CookieOptions
                   {
                       Expires = DateTimeOffset.Now.AddSeconds(10),
                       IsEssential = true
                   });

                #endregion 

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Export, $"成功:{ordernos}", SecondIndex.InParkCar);

                return File(bt, "application/vnd.ms-excel", $"逾期车辆停车记录_{DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd")}.xlsx");
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Export, $"导出停车记录失败:{ex.ToString()}", SecondIndex.InParkCar);
                return null;
            }
        }

        /// <summary>
        /// 删除逾期车牌停车记录
        /// </summary>
        /// <param name="ParkOrder_NoArray"></param>
        /// <returns></returns>
        public IActionResult Delete(string ParkOrder_NoArray)
        {
            try
            {
                if (!Powermanage.PowerCheck("InParkCar", PowerEnum.Delete.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                List<string> ParkOrder_NoList = TyziTools.Json.ToObject<List<string>>(ParkOrder_NoArray);
                if (ParkOrder_NoList == null) return ResOk(false, "请选择");

                List<Model.ParkOrder> parkOrders = BLL.ParkOrder.GetAllEntity("*", $"ParkOrder_ParkNo='{parking.Parking_No}' AND ParkOrder_No in @ParkOrder_NoList", parameters: new { ParkOrder_NoList = ParkOrder_NoList });
                parkOrders?.RemoveAll(x => x.ParkOrder_StatusNo != Model.EnumParkOrderStatus.In);//过滤不是在车场内的记录
                if (parkOrders?.Count == 0) { return ResOk(false, "没有需要删除的记录"); }

                List<Model.OrderDetail> details = BLL.OrderDetail.GetAllEntity("*", $"OrderDetail_ParkNo='{parking.Parking_No}' AND OrderDetail_ParkOrderNo in ('{string.Join("','", parkOrders.Select(x => x.ParkOrder_No))}')");

                parkOrders?.ForEach(item =>
                {
                    item.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                    item.ParkOrder_Remark = "逾期关闭场内记录";
                });

                details?.ForEach(item =>
                {
                    if (item.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In || item.OrderDetail_StatusNo == Model.EnumParkOrderStatus.Est)
                    {
                        item.OrderDetail_StatusNo = Model.EnumParkOrderStatus.InClose;
                        item.OrderDetail_Remark = "逾期关闭场内记录";
                    }
                });

                var res = BLL.ParkOrder.CarInComplete(parkOrders, details);
                if (res > 0)
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"成功:{string.Join(",", parkOrders.Select(x => x.ParkOrder_CarNo))}", SecondIndex.InParkCar);
                    var datas = BLL.ParkOrder.sendPush(parkOrders, details, 50);
                    datas?.ForEach(item =>
                    {
                        Push(Model.API.PushAction.Edit, item, new List<Model.SentryHost>(), "carin", dataType: DataTypeEnum.InParkRecord, Desc: $"删除逾期车牌{string.Join(",", parkOrders.Select(x => x.ParkOrder_CarNo))}");
                    });

                    parkOrders.ForEach(item =>
                    {
                        item.ParkOrder_StatusNo = Model.EnumParkOrderStatus.InClose;
                        BLL.PushEvent.CloseCar(parking.Parking_Key, item, "场内关闭");
                    });
                    BLL.PushEvent.SendParkSpace(parking.Parking_No);

                    return ResOk(true, "删除成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"失败:{string.Join(",", parkOrders.Select(x => x.ParkOrder_CarNo))}", SecondIndex.InParkCar);
                    return ResOk(false, "删除失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"删除停车记录异常：{ex.ToString()}", SecondIndex.InParkCar);
                return ResOk(false, "删除失败");
            }
        }
    }
}