﻿using Newtonsoft.Json;
using System;
using System.Net;

namespace carparking.Model
{
    public class SysConfig
    {
        public int? SysConfig_ID { get; set; }

        public string SysConfig_No { get; set; }

        public string SysConfig_ParkNo { get; set; }

        public string SysConfig_Content { get; set; }

        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? SysConfig_LastUpdateTime { get; set; }
    }

    public class SysConfigContent
    {
        public SysConfigContent()
        {
        }

        public static SysConfigContent GetIntance(Model.SysConfig config)
        {
            if (config == null) return null;
            if (string.IsNullOrEmpty(config.SysConfig_Content)) return null;

            try
            {
                Model.SysConfigContent model = null;
                if (config.SysConfig_Content.StartsWith("{") && config.SysConfig_Content.EndsWith("}"))
                {
                    model = JsonConvert.DeserializeObject<Model.SysConfigContent>(config.SysConfig_Content);
                }
                else
                {
                    model = JsonConvert.DeserializeObject<Model.SysConfigContent>(WebUtility.UrlDecode(config.SysConfig_Content));
                }

                return model;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        #region 基础配置

        /// <summary>
        /// 优惠券使用顺序,1-优先使用减免金额优惠,2-优先使用折扣比例优惠
        /// </summary>
        public int? SysConfig_CouponSort { get; set; }

        /// <summary>
        /// 默认相机设备系列
        /// </summary>
        public string SysConfig_CameraDrive { get; set; }

        /// <summary>
        /// 默认打开相机视频：1-默认打开，2-默认关闭，其它-不设置默认
        /// </summary>
        public string SysConfig_OpenCameraVideo { get; set; }

        /// <summary>
        /// 默认显示屏设备系列
        /// </summary>
        public string SysConfig_ScreenDrive { get; set; }

        /// <summary>
        /// 默认自助停车设备系列
        /// </summary>
        public string SysConfig_SelfDrive { get; set; }

        /// <summary>
        /// 自定义版权信息
        /// </summary>
        public string SysConfig_CopyRight { get; set; }

        /// <summary>
        /// 免费原因组
        /// </summary>
        public string SysConfig_FreeReasons { get; set; }

        /// <summary>
        /// 入场备注|入场原因组
        /// </summary>
        public string SysConfig_EnterRemarks { get; set; }

        #endregion

        #region 服务器

        /// <summary>
        /// 服务器历史记录保存天数
        /// </summary>
        public int? SysConfig_SerRecordDay { get; set; }

        /// <summary>
        /// 服务器历史图片保存天数
        /// </summary>
        public int? SysConfig_SerImgDay { get; set; }

        /// <summary>
        /// 岗亭电脑历史记录保存天数
        /// </summary>
        public int? SysConfig_ClientRecordDay { get; set; }

        /// <summary>
        /// 岗亭电脑历史图片保存天数
        /// </summary>
        public int? SysConfig_ClientImgDay { get; set; }

        /// <summary>
        /// 服务器同步数据到岗亭、云平台的历史记录保存时长（单位：月）。小于等于0则表示永久保留。主要用于清理数据库数据占用的空间
        /// </summary>
        public int? SysConfig_SerSyncData { get; set; }

        /// <summary>
        /// 日志文件保留天数
        /// </summary>
        public int? SysConfig_LogSaveDay { get; set; }

        /// <summary>
        /// 全量保存本地日志，1-启用，0-禁用
        /// </summary>
        public int? SysConfig_LogOpen { get; set; } = 1;

        /// <summary>
        /// 自定义图片存储，1-启用，0-禁用
        /// </summary>
        public int? SysConfig_EnableImgPath { get; set; } = 0;

        /// <summary>
        /// 自定义停车场后台图片存储地址
        /// </summary>
        public string SysConfig_ImagePath { get; set; }

        /// <summary>
        /// 自定义停车场岗亭图片存储地址
        /// </summary>
        public string SysConfig_SentryImagePath { get; set; }

        /// <summary>
        /// 自动备份数据库,1-定时备份，0-禁用,2-定时备份+车流量判断
        /// </summary>
        public int? SysConfig_DBBack { get; set; } = 1;

        /// <summary>
        /// 数据库备份路径
        /// </summary>
        public string SysConfig_DBBackPath { get; set; }

        /// <summary>
        /// 备份数据库的周期，1-每天，2-每周，3-间隔
        /// </summary>
        public int? SysConfig_DBBackPeriod { get; set; }

        /// <summary>
        /// 备份数据库的每周几
        /// </summary>
        public int? SysConfig_DBBackWeekDay { get; set; }

        /// <summary>
        /// 备份数据库的间隔天数
        /// </summary>
        public int? SysConfig_DBBackInterval { get; set; }

        /// <summary>
        /// 备份数据库的时间节点，00:00:00
        /// </summary>
        public string SysConfig_DBBackTime { get; set; }

        /// <summary>
        /// 数据库备份文件保留天数，超过天数的备份文件自动删除(计算时默认加一天,即设置1天时,今天不会删除昨天的备份文件)
        /// </summary>
        public int? SysConfig_DBBackRetent { get; set; }

        /// <summary>
        /// 磁盘空间剩余，百分比 0-100。磁盘剩余空间低于设定值，将发送报警。设置0表示不发送警报
        /// </summary>
        public int? SysConfig_WarmDisk { get; set; }

        /// <summary>
        /// 电脑内存剩余，百分比 0-100。电脑内存剩余低于设定值，将发送报警。设置0表示不发送警报
        /// </summary>
        public int? SysConfig_WarmMemory { get; set; }

        /// <summary>
        /// CPU使用率，百分比 0-100。CPU使用率超出设定值，将发送报警。设置0表示不发送警报
        /// </summary>
        public int? SysConfig_WarmCPUUse { get; set; }

        /// <summary>
        /// 网络状态，1-启用，0-禁用，启用后，网络状态断开连接时，将发送报警。
        /// </summary>
        public int? SysConfig_WarmNet { get; set; }

        /// <summary>
        /// CPU温度，℃。CPU温度超出设定值，将发送报警。设置0表示不发送警报
        /// </summary>
        public int? SysConfig_WarmCPUTemp { get; set; }

        /// <summary>
        /// 邮箱服务器地址
        /// </summary>
        public string SysConfig_SenderServerHost { get; set; }

        /// <summary>
        /// 邮箱服务端口号
        /// </summary>
        public int? SysConfig_Senderport { get; set; }

        /// <summary>
        /// 邮箱服务账号
        /// </summary>
        public string SysConfig_FromMail { get; set; }

        /// <summary>
        /// 邮箱服务密码
        /// </summary>
        public string SysConfig_SenderPassword { get; set; }

        /// <summary>
        /// 邮箱服务用户名
        /// </summary>
        public string SysConfig_SenderUsername { get; set; }

        /// <summary>
        /// 视频播放分辨率使能，1-启用，0-禁用
        /// </summary>
        public int SysConfig_VideoResolutionEnable { get; set; } = 0;

        /// <summary>
        /// 视频播放分辨率，1-流畅（默认），2-标清，3-高清
        /// </summary>
        public int SysConfig_VideoResolution { get; set; } = 1;

        /// <summary>
        /// 设置相机抓拍图片质量，1-流畅（默认），2-标清，3-高清
        /// </summary>
        public int SysConfig_CameraImageQuality { get; set; } = 1;

        /// <summary>
        /// C08显示屏自动关闭非空闲内容的超时时间（秒），默认15秒
        /// </summary>
        public int? SysConfig_DisplayAutoCloseTimeout { get; set; } = 15;

        #endregion

        #region 个性化设置

        /// <summary>
        /// 启用自定义设置，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_DIYEnable { get; set; }

        /// <summary>
        /// 自定义管理后台名称
        /// </summary>
        public string SysConfig_DIYName { get; set; }

        /// <summary>
        /// 自定义岗亭端名称
        /// </summary>
        public string SysConfig_DIYSoftName { get; set; }

        /// <summary>
        /// 自定义LOGO
        /// </summary>
        public string SysConfig_DIYLogo { get; set; }

        /// <summary>
        /// 自定义登录页背景图
        /// </summary>
        public string SysConfig_DIYBackImage { get; set; }

        /// <summary>
        /// 自定义菜单名称
        /// </summary>
        public string SysConfig_DIYMenu { get; set; }

        #endregion

        #region 支付设置

        /// <summary>
        /// 微信收款方式，0-停用收款，2-子商户收款
        /// </summary>
        public int? SysConfig_PayWeiXin { get; set; }

        /// <summary>
        /// 子商户帐户号
        /// </summary>
        public string SysConfig_WXSubMchId { get; set; }

        /// <summary>
        /// 微信支付的APPID
        /// </summary>
        public string SysConfig_WXAppID { set; get; }

        /// <summary>
        /// 微信支付商户密匙
        /// </summary>
        public string SysConfig_WXServerMchKey { set; get; }

        /// <summary>
        /// 微信支付的商户号
        /// </summary>
        public string SysConfig_WXMCHID { set; get; }

        /// <summary>
        /// 支付宝收款方式，0-停用收款，1-启用收款
        /// </summary>
        public int? SysConfig_PayAlipay { get; set; }

        /// <summary>
        /// 签约商户账号
        /// </summary>
        public string SysConfig_AliPayAccountNo { get; set; }

        /// <summary>
        /// 签约商户PID
        /// </summary>
        public string SysConfig_AliPid { get; set; }

        /// <summary>
        /// 签约应用AppId
        /// </summary>
        public string SysConfig_AliAppid { get; set; }

        /// <summary>
        /// 支付宝公钥(服务窗)
        /// </summary>
        public string SysConfig_AliPublicKey { get; set; }

        /// <summary>
        /// 应用授权Token
        /// </summary>
        public string SysConfig_AliAppAuthToken { get; set; }

        /// <summary>
        /// 启用城市服务，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_AliPayEcoStatus { get; set; }

        /// <summary>
        /// 高德POIID
        /// </summary>
        public string SysConfig_CityCode { get; set; }

        /// <summary>
        /// 停车场性质
        /// </summary>
        public string SysConfig_AliPayLotType { get; set; }

        /// <summary>
        /// 启用渠道服务，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_ChannelServices { get; set; }

        /// <summary>
        /// 渠道POIID
        /// </summary>
        public string SysConfig_AliChannelPOIID { get; set; }

        /// <summary>
        /// 渠道PID
        /// </summary>
        public string SysConfig_AliChannelPID { get; set; }

        /// <summary>
        /// 渠道车场ID
        /// </summary>
        public string SysConfig_AliChannelParkingID { get; set; }

        /// <summary>
        ///  支付宝支付公钥
        /// </summary>
        public string SysConfig_AliPayPublicKey { set; get; }

        /// <summary>
        ///  支付宝支付私钥
        /// </summary>
        public string SysConfig_AliPayPrivateKey { set; get; }

        /// <summary>
        /// 建行聚合支付，0-停用收款，1-启用收款
        /// </summary>
        public int? SysConfig_PayCCB { get; set; }

        /// <summary>
        /// 建行聚合支付分行代码
        /// </summary>
        public string SysConfig_CCBMashupBranchID { get; set; }

        /// <summary>
        /// 建行聚合支付柜台代码
        /// </summary>
        public string SysConfig_CCBMashupPosID { get; set; }

        /// <summary>
        /// 建行聚合支付商户号
        /// </summary>
        public string SysConfig_CCBMashupMerchantID { get; set; }

        /// <summary>
        /// 建行聚合支付商户密码
        /// </summary>
        public string SysConfig_CCBMerchantPwd { get; set; }

        /// <summary>
        /// 建行聚合支付建行公钥
        /// </summary>
        public string SysConfig_CCBMashupSecret { get; set; }

        /// <summary>
        /// 工行聚合支付，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_ICBCMashupMode { get; set; }

        /// <summary>
        ///  工行聚合支付APPID
        /// </summary>
        public string SysConfig_ICBCMMAppID { get; set; }

        /// <summary>
        ///  工行聚合支付商户ID
        /// </summary>
        public string SysConfig_ICBCMMMerchatID { get; set; }

        /// <summary>
        ///  工行聚合支付入账账号
        /// </summary>
        public string SysConfig_ICBCMMMerchantAcct { get; set; }

        /// <summary>
        ///  工行聚合支付网关公钥
        /// </summary>
        public string SysConfig_ICBCMMApigwPublicKey { get; set; }

        /// <summary>
        ///  工行聚合支付支付私钥
        /// </summary>
        public string SysConfig_ICBCMMMyPrivateKey { get; set; }

        /// <summary>
        /// 威富通无感支付，1-启用，0-不启用
        /// </summary>
        public int SysConfig_SwiftPassMode { get; set; }

        /// <summary>
        /// 威富通无感支付商户ID
        /// </summary>    
        public string SysConfig_SwiftPassMchID { get; set; }

        /// <summary>
        /// 威富通无感支付商户秘钥
        /// </summary>     
        public string SysConfig_SwiftPassMchKey { get; set; }

        /// <summary>
        /// 威富通无感支付公钥
        /// </summary>   
        public string SysConfig_SwiftPassPublicKey { get; set; }

        /// <summary>
        /// 威富通无感支付请求服务地址
        /// </summary>
        public string SysConfig_SwiftPassApiUrl { get; set; }

        /// <summary>
        /// 是否启用ETC支付方式，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_ETCPayMode { get; set; }

        /// <summary>
        /// ETC接入商户号(接入ID)
        /// </summary>
        public string SysConfig_ETCPayID { get; set; }

        /// <summary>
        /// ETC商户证书密码
        /// </summary>
        public string SysConfig_ETCPayPwd { get; set; }

        /// <summary>
        /// ETC停车场标识
        /// </summary>
        public string SysConfig_ETCPayParkID { get; set; }

        /// <summary>
        /// 乐聚合支付，0-停用收款，1-启用收款
        /// </summary>
        public int? SysConfig_PayLepos { get; set; }

        /// <summary>
        /// 商户编号
        /// </summary>
        public string SysConfig_LeposMerchantId { get; set; }

        /// <summary>
        /// 随行付开关，1-启用，0-禁用
        /// </summary>
        public int? SysConfig_SxPayMode { get; set; }

        /// <summary>
        /// 随行付商户编号
        /// </summary>
        public string SysConfig_SxPayMerId { get; set; }

        /// <summary>
        /// 渠道商户 (简易支付) 0-禁用，1-启用
        /// </summary>
        public int? SysConfig_LeposMerType { get; set; }

        #endregion

        #region 电子发票

        /// <summary>
        /// 是否启用电子发票，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_Fpenable { get; set; }

        /// <summary>
        /// 开票税率
        /// </summary>
        public decimal? SysConfig_FpRate { get; set; }

        /// <summary>
        /// 分类编码（税目编码）
        /// </summary>
        public string SysConfig_FpTaxCode { get; set; }

        /// <summary>
        /// 中税电子发票，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_ZSEnable { get; set; }

        public string SysConfig_ZSFpAppId { get; set; }
        public string SysConfig_ZSFpAppSecret { get; set; }

        /// <summary>
        /// 航信电子发票，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_HXEnable { get; set; }

        /// <summary>
        /// 身份认证(identity)
        /// </summary>
        public string SysConfig_HxIdentity { get; set; }

        /// <summary>
        /// 公司电话(salephone)
        /// </summary>
        public string SysConfig_HxSalePhone { get; set; }

        /// <summary>
        /// 公司地址(saleaddress)
        /// </summary>
        public string SysConfig_HxSaleAddress { get; set; }

        /// <summary>
        /// 公司税号(saletaxnum)
        /// </summary>
        public string SysConfig_HxSaleTaxnum { get; set; }

        /// <summary>
        /// 开票员(clerk)
        /// </summary>
        public string SysConfig_HxClerk { get; set; }

        /// <summary>
        /// 银行账号(saleaccount)
        /// </summary>
        public string SysConfig_HxSaleAccount { get; set; }

        /// <summary>
        /// 高灯电子发票，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_GDEnable { get; set; }

        /// <summary>
        /// 纳税人识别号
        /// </summary>
        public string SysConfig_TaxPayerNum { get; set; }

        #endregion

        #region 云车场服务

        /// <summary>
        /// 第三发数据转发地址
        /// </summary>
        public string SysConfig_ApiUrlThree { get; set; }

        /// <summary>
        /// 连接平台类型，1-智慧停车开放平台，2-智慧停车管理平台
        /// </summary>
        public string SysConfig_ApiType { get; set; }

        /// <summary>
        /// 智慧停车数据转发地址
        /// </summary>
        public string SysConfig_ApiUrl { get; set; }

        /// <summary>
        /// 数据转发应用标识
        /// </summary>
        public string SysConfig_ApiAppID { get; set; }

        /// <summary>
        /// 数据转发应用密钥,用于生成数据签名
        /// </summary>
        public string SysConfig_ApiAppSecret { get; set; }

        /// <summary>
        /// 数据包AES加密向量
        /// </summary>
        public string SysConfig_AESIV { get; set; }

        /// <summary>
        /// 数据包AES加密密钥
        /// </summary>
        public string SysConfig_AESKey { get; set; }

        /// <summary>
        /// 微信扫码域名
        /// </summary>
        public string SiteDomain_Weixin { set; get; }

        #endregion

        #region 第三方服务

        /// <summary>
        /// 第三方服务是否启用，1-启用，0-禁用
        /// </summary>
        public int? SysConfig_ThreeApiEnable { get; set; }

        /// <summary>
        /// 第三方服务名称
        /// </summary>
        public string SysConfig_ThreeApiName { get; set; }

        /// <summary>
        /// 第三方服务地址
        /// </summary>
        public string SysConfig_ThreeApiUrl { get; set; }

        /// <summary>
        /// 第三方服务方法
        /// </summary>
        public string SysConfig_Method { get; set; }

        /// <summary>
        /// 数据转发应用标识
        /// </summary>
        /// 
        public string SysConfig_ThreeApiAppID { get; set; }

        /// <summary>
        /// 数据转发应用密钥
        /// </summary>
        public string SysConfig_ThreeApiAppSecret { get; set; }

        /// <summary>
        /// 第三方服务回调已启用集合
        /// </summary>
        public ThirdPartyCallback[] SysConfig_ThreeCallBacks { get; set; }

        /// <summary>
        /// 第三方mqtt服务是否启用，1-启用，0-禁用
        /// </summary>
        public int? SysConfig_ThreeMqttEnable { get; set; } = 0;

        /// <summary>
        /// 开放接口版本，0-V1，1-V2
        /// </summary>
        public int? SysConfig_ThreeApiVersion { get; set; }

        /// <summary>
        /// 第三方mqtt服务地址
        /// </summary>
        public string SysConfig_ThreeMqttServerUrl { get; set; }

        /// <summary>
        /// 第三方mqtt服务端口
        /// </summary>
        public int? SysConfig_ThreeMqttServerPort { get; set; } = 1883;

        /// <summary>
        /// 第三方mqtt服务用户名
        /// </summary>
        public string SysConfig_ThreeMqttUserName { get; set; }

        /// <summary>
        /// 第三方mqtt服务密码
        /// </summary>
        public string SysConfig_ThreeMqttPassword { get; set; }

        /// <summary>
        /// 第三方mqtt服务客户端ID
        /// </summary>
        public string SysConfig_ThreeMqttClientId { get; set; }

        /// <summary>
        /// 第三方mqtt服务订阅主题
        /// </summary>
        public string SysConfig_ThreeMqttTopic { get; set; }

        /// <summary>
        /// 第三方mqtt服务发布主题
        /// </summary>
        public string SysConfig_ThreeMqttPublishTopic { get; set; }

        /// <summary>
        /// 第三方Mqtt服务客户端断线重连间隔时间，单位：秒
        /// </summary>
        public int? SysConfig_ThreeMqttReconnectInterval { get; set; } = 10;

        /// <summary>
        /// 第三方Mqtt服务协议版本，3-V310,4-V311,5-V500
        /// </summary>
        public int? SysConfig_ThreeMqttVersion { get; set; } = 5;

        #endregion

        #region 无感支付设置

        #region 微信

        /// <summary>
        /// 是否启用微信无感支付
        /// </summary>
        public int? SysConfig_WXPayNoPwd { set; get; }

        /// <summary>
        /// 微信无感支付入账子商户号
        /// </summary>
        public string SysConfig_WXPayNoPwdNo { set; get; }

        /// <summary>
        /// 启用支付中可开通无感支付，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_WXPayNoPwdInPaying { get; set; }

        #endregion

        #region 支付宝

        /// <summary>
        /// 是否启用支付宝无感支付，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_AliPayNoPwd { set; get; }

        /// <summary>
        /// 支付宝无感支付签约账户
        /// </summary>
        public string SysConfig_AliPayNoPwdNo { set; get; }

        /// <summary>
        ///  支付宝无感支付PID
        /// </summary>
        public string SysConfig_AliPayNoPwdAppID { set; get; }

        #endregion

        #region 建行

        /// <summary>
        /// 建行无感支付，0-停用收款，1-启用收款
        /// </summary>
        public int? SysConfig_PayNoCCB { get; set; }

        /// <summary>
        /// 建行无感支付分行代码
        /// </summary>
        public string SysConfig_CCBNoMashupBranchID { get; set; }

        /// <summary>
        /// 建行无感支付柜台代码
        /// </summary>
        public string SysConfig_CCBNoMashupPosID { get; set; }

        /// <summary>
        /// 建行无感支付商户号
        /// </summary>
        public string SysConfig_CCBNoMashupMerchantID { get; set; }

        /// <summary>
        /// 建行无感建行公钥
        /// </summary>
        public string SysConfig_CCBNoMashupSecret { get; set; }

        /// <summary>
        /// 下发的建行无感支付是否仅用于无感支付，1-公用，0-仅用
        /// </summary>
        public int SysConfig_CCBOne { set; get; }

        #endregion

        #region 银联

        /// <summary>
        /// 是否启用银联无感支付，0-停用，1-启用
        /// </summary>
        public int? SysConfig_UnionPayNoPwd { get; set; }

        /// <summary>
        /// 银联无感支付业务代码
        /// </summary>
        public string SysConfig_UnionBusinessCode { get; set; }

        /// <summary>
        /// 银联无感支付密钥
        /// </summary>
        public string SysConfig_UnionSecret { get; set; }

        #endregion

        #region 招行

        /// <summary>
        /// 是否启用招行无感支付，1-启用，0-不启用
        /// </summary>   
        public int? SysConfig_CMBPayNoPwd { get; set; }

        /// <summary>
        /// 招行无感支付车场编码
        /// </summary>
        public string SysConfig_CMBParkCode { get; set; }

        /// <summary>
        /// 招行无感支付应用APPID
        public string SysConfig_CMBAppId { get; set; }

        /// <summary>
        /// 招行无感支付AppSecret
        /// </summary>    
        public string SysConfig_CMBAppSecret { get; set; }

        /// <summary>
        /// 招行无感支付发起IP地址
        /// </summary>
        public string SysConfig_CMBPayApiUrl { get; set; }

        #endregion

        #region 工行

        /// <summary>
        /// 是否启用工行无感支付，1-启用，0-不启用
        /// </summary> 
        public int? SysConfig_ICBCPayNoPwd { get; set; }

        /// <summary>
        /// 工行无感支付 APPID
        /// </summary>
        public string SysConfig_ICBCAppID { get; set; }

        /// <summary>
        /// 工行无感支付商户ID
        /// </summary>
        public string SysConfig_ICBCMerchatID { get; set; }

        /// <summary>
        /// 工行无感支付入账账号
        /// </summary>
        public string SysConfig_ICBCMerchantAcct { get; set; }

        /// <summary>
        /// 工行无感支付网关公钥
        /// </summary>
        public string SysConfig_ICBCApigwPublicKey { get; set; }

        /// <summary>
        /// 工行无感支付私钥
        /// </summary>
        public string SysConfig_ICBCMyPrivateKey { get; set; }

        #endregion

        #region 农行

        /// <summary>
        /// 是否启用农行无感支付，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_ABCPayNoPwd { get; set; }

        /// <summary>
        /// 农行无感支付商户号
        /// </summary>
        public string SysConfig_ABCMerchantID { get; set; }

        /// <summary>
        /// 农行无感支付入账账号
        /// </summary>                                                   
        public string SysConfig_ABCMerchantAcct { get; set; }

        /// <summary>
        /// 农行无感支付证书密码
        /// </summary>
        public string SysConfig_ABCCertificatePwd { get; set; }

        #endregion

        #region 中行

        /// <summary>
        /// 是否启用中行无感支付，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_BOCPayNoPwd { get; set; }

        /// <summary>
        /// 中行无感支付商户代码
        /// </summary>
        public string SysConfig_BOCPayNoMerchantID { get; set; }

        /// <summary>
        /// 中行无感支付经营类别
        /// </summary>
        public string SysConfig_BOCPayNoCategory { get; set; }

        /// <summary>
        /// 中行无感签名密钥
        /// </summary>
        public string SysConfig_BOCPayNoZkey { get; set; }

        /// <summary>
        /// 中行加密密钥
        /// </summary>     
        public string SysConfig_BOCPayNoDkey { get; set; }

        #endregion

        #region 第三方无感

        /// <summary>
        /// 第三方无感支付，0-禁用，1-启用
        /// </summary>
        public int? SysConfig_ThirdNoPwdEnable { get; set; }

        /// <summary>
        /// 第三方无感支付地址
        /// </summary>
        public string SysConfig_ThirdNoPwdAddress { get; set; }

        #endregion

        #endregion

        #region 阿里云存储

        /// <summary>
        /// 阿里云存储节点
        /// </summary>
        public string SysConfig_AliyunEndpoint { get; set; }

        /// <summary>
        /// 云存储调用Key
        /// </summary>
        public string SysConfig_AliyunAccessKeyId { get; set; }

        /// <summary>
        /// 云存储调用密钥
        /// </summary>
        public string SysConfig_AliyunAccessKeySecret { get; set; }

        /// <summary>
        /// 图片存储空间名称
        /// </summary>
        public string SysConfig_AliyunImgBucket { get; set; }

        /// <summary>
        /// 直连云识别是否上传使能
        /// </summary>
        public bool SysConfig_CloudBoxLprEnable { get; set; }

        /// <summary>
        /// 直连云是否启用应急模式
        /// </summary>
        public bool SysConfig_CloudBoxLprEmergency { get; set; }

        /// <summary>
        /// 车场连接模式：0-开放模式 1-协同模式
        /// </summary>
        public int? SysConfig_ConnMode { get; set; } = 0;

        /// <summary>
        /// 车场模式：0-智慧停车 1-车主小程序
        /// </summary>
        public int? SysConfig_Parkmode { get; set; }

        /// <summary>
        /// 车道码域名地址
        /// </summary>
        public string SysConfig_Laneurl { get; set; }

        /// <summary>
        /// 服务产品阿里云存储天数
        /// </summary>
        public int? SysConfig_AccessTime { get; set; }

        #endregion

        #region 黔通ETC

        /// <summary>
        /// 是否启用黔通ETC，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_QianTong_Enable { set; get; }

        /// <summary>
        /// 黔通ETC中间件服务地址
        /// </summary>
        public string SysConfig_QianTong_Address { set; get; }

        /// <summary>
        ///  黔通ETC中间件服务端口
        /// </summary>
        public int SysConfig_QianTong_Port { set; get; }

        /// <summary>
        /// 交易流水上传地址
        /// </summary>
        public string SysConfig_QianTongTransactionFlowAddress { set; get; }

        /// <summary>
        /// 黔通ETC商户信息
        /// </summary>
        public string SysConfig_QingTongMerchantNo { set; get; }

        /// <summary>
        /// 黔通ETC对接停车场编号
        /// </summary>
        public string SysConfig_QingTongParkNo { set; get; }

        /// <summary>
        /// 黔通ETC最大扣费金额
        /// </summary>
        public decimal SysConfig_QingTongMaxMoney { set; get; }

        /// <summary>
        /// 黔通ETC接入码
        /// </summary>
        public string SysConfig_QingTongAccessCode { set; get; }

        /// <summary>
        /// 黔通ETC签名码
        /// </summary>
        public string SysConfig_QingTongSignCode { set; get; }

        #endregion

        #region 山东信联ETC

        /// <summary>
        /// 是否启用信联ETC，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_XinLianEnable { set; get; }

        /// <summary>
        /// 信联http上传地址
        /// </summary>
        public string SysConfig_XinLianUrl { get; set; }

        /// <summary>
        /// 信联httpappid
        /// </summary>
        public string SysConfig_XinLianAppid { get; set; }

        /// <summary>
        /// 信联http商户编号
        /// </summary>
        public string SysConfig_XinLianMerChantNo { get; set; }

        /// <summary>
        /// 信联http秘钥
        /// </summary>
        public string SysConfig_XinLianPriKey { get; set; }

        /// <summary>
        /// 信联http公钥
        /// </summary>
        public string SysConfig_XinLianPubKey { get; set; }

        #endregion

        #region 山东信联ETC（云端扣费）

        /// <summary>
        /// 是否启用信联ETC（云端扣费），1-启用，0-不启用
        /// </summary>
        public int? SysConfig_XinLianCloudEnable { set; get; }

        /// <summary>
        /// 信联ETC（云端扣费）请求网关地址
        /// </summary>
        public string SysConfig_XinLianCloudUrl { set; get; } = "https://xlapi.etcsd.cn:8093/xl-api/common/gateway";

        /// <summary>
        /// 信联ETC（云端扣费）应用APPID
        /// </summary>
        public string SysConfig_XinLianCloudAppID { set; get; }

        /// <summary>
        /// 信联ETC（云端扣费）签名私钥
        /// </summary>
        public string SysConfig_XinLianCloudPrivateKey { set; get; }

        #endregion

        #region 农行聚合支付

        /// <summary>
        /// 农行聚合支付是否启用，1-启用，0-不启用
        /// </summary>
        public int? SysConfig_ABCJHEnable { set; get; }

        /// <summary>
        /// 农行聚合支付请求地址
        /// </summary>
        public string SysConfig_ABCJHUrl { get; set; }

        /// <summary>
        /// 农行聚合支付商户号
        /// </summary>
        public string SysConfig_ABCJHMerchantNo { get; set; }

        /// <summary>
        /// 农行聚合支付入账商户号
        /// </summary>
        public string SysConfig_ABCJHInMerchantNo { get; set; }

        /// <summary>
        /// 农行聚合支付PFX路径
        /// </summary>
        public string SysConfig_ABCJHPFXPath { get; set; }

        /// <summary>
        /// 农行聚合支付PFXBase64数据
        /// </summary>
        public string SysConfig_ABCJHPFXBase64 { get; set; }

        /// <summary>
        /// 农行聚合支付CER路径
        /// </summary>
        public string SysConfig_ABCJHCERPath { get; set; }

        /// <summary>
        /// 农行聚合支付CER Base64数据
        /// </summary>
        public string SysConfig_ABCJHCERBase64 { get; set; }

        /// <summary>
        /// 农行聚合支付证书密码
        /// </summary>
        public string SysConfig_ABCJHPwd { get; set; }

        #endregion

        #region 云托管

        /// <summary>
        /// 云托管地址
        /// </summary>
        public string ParkServiceUrl { set; get; }


        /// <summary>
        /// 置信值，低于这个值的都要上传修正车牌
        /// </summary>
        public float freliability { set; get; }

        /// <summary>
        /// 云托管是否启用车牌智能修正，1-启用，0-不启用
        /// </summary>
        public int OpenCloudCorrection { set; get; }

        /// <summary>
        /// 省略小图存储阿里库地址
        /// </summary>
        public string smallImgBucket { set; get; }

        #endregion

        #region 直连云MQTT连接信息

        /// <summary>
        /// 直连云MQTT连接地址【正式环境】                
        /// </summary>
        public string Parking_MqttUrl { set; get; }

        /// <summary>
        /// 直连云MQTT连接端口【正式环境】               
        /// </summary>
        public int Parking_MqttPort { set; get; }

        /// <summary>
        /// 直连云MQTT连接地址【预发布环境】                
        /// </summary>
        public string Parking_MqttUrlPreRelease { set; get; }

        /// <summary>
        /// 直连云MQTT连接端口【预发布环境】     
        /// </summary>
        public int Parking_MqttPortPreRelease { set; get; }

        /// <summary>
        /// 直连云MQTT连接地址【测试环境】                
        /// </summary>
        public string Parking_MqttUrlDebug { set; get; }

        /// <summary>
        /// 直连云MQTT连接端口【测试环境】     
        /// </summary>
        public int Parking_MqttPortDebug { set; get; }


        /// <summary>
        /// 直连云MQTT连接用户名
        /// </summary>
        public string Parking_MqttUserName { set; get; }

        /// <summary>
        /// 直连云MQTT连接密码
        /// </summary>
        public string Parking_MqttPassWord { set; get; }

        #endregion

        /// <summary>
        /// 微信小程序域名地址
        /// </summary>
        public string SiteDomain_WeixinSmall { set; get; }

        #region 清理数据

        /// <summary>
        /// 清理数据,0-停用，1-启用     
        /// </summary>
        public int? SysConfig_ClearData { set; get; }

        /// <summary>
        /// 清理入场记录,,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryRecord { set; get; }

        /// <summary>
        /// 清理出场记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryExitRecord { set; get; }

        /// <summary>
        /// 清理缴费记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryCarCharge { set; get; }

        /// <summary>
        /// 清理识别记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryRecognitionRecord { set; get; }

        /// <summary>
        /// 清理优惠券记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryCouponRecord { set; get; }

        /// <summary>
        /// 清理开闸记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryOpenGateRecord { set; get; }

        /// <summary>
        /// 清理交班记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录
        /// </summary>
        public int? SysConfig_EntryShiftRecord { set; get; }

        /// <summary>
        /// 清理倒车记录,0-停用，1-一个月前的记录，2-三个月前的记录，3-半年前的记录，4-一年前的记录 
        /// </summary>
        public int? SysConfig_EntryReverseRecord { set; get; }


        /// <summary>
        /// 车流量判断分钟
        /// </summary>
        public int? SysConfig_DBBackFlowMin { set; get; } = 3;
        /// <summary>
        /// 车流量判断车辆数
        /// </summary>
        public int? SysConfig_DBBackFlowCar { set; get; } = 1;

        #endregion

        /// <summary>
        /// 软件授权
        /// </summary>
        public string SysConfig_SoftAuth { set; get; }

        /// <summary>
        /// 时段全面优惠券有效期
        /// </summary>
        public DateTime? SysConfig_TimeCouponValidity { set; get; }

        /// <summary>
        /// 增强型视频播放，0-不设置，1-启用，2-禁用
        /// </summary>
        public int SysConfig_PlayerType { get; set; } = 2;

        /// <summary>
        /// 锁单分钟数  0-不设置
        /// </summary>
        public int SysConfig_LockOrderMin { get; set; }

        /// <summary>
        /// 强密码  1-启用，0-禁用
        /// </summary>
        public int? SysConfig_PwdType { get; set; }
    }

    public class DIYMenu
    {
        public string key { get; set; }
        public string text { get; set; }
    }

    /// <summary>
    /// 第三方服务回调订阅通知保存实体类
    /// </summary>
    public class ThirdPartyCallback
    {
        /// <summary>
        /// 回调服务序号
        /// </summary>
        public int Index { set; get; }

        /// <summary>
        /// 回调服务名称
        /// </summary>
        public string ServiceName { set; get; }

        /// <summary>
        /// 回调服务地址
        /// </summary>
        public string ServiceUrl { set; get; }

        /// <summary>
        /// 处理回调数据间隔 单位：毫秒
        /// </summary>
        public int Interval { set; get; } = 1000;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable { set; get; }
    }
}