﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>储值车退费</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .payOne { width: 100%; display: block; }
        .payTwo { width: 100%; display: none; }
        /*  .dropdown-menu { display: none; }*/
        .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #eee; opacity: 1; color: rgba(0,0,0,.85) !important; }
        .dropdown-toggle > .dropdown-caret { color: #888; display: inline-block; width: 0; height: 0; margin: 0 3px; border-style: solid; border-width: 6px 4px 0 4px; border-left-color: transparent; border-right-color: transparent; border-bottom-color: transparent; vertical-align: baseline; }
        .red { color: #de163b; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车位号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="10" id="Owner_Space" name="Owner_Space" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车主姓名</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="10" id="Owner_Name" name="Owner_Name" disabled />
                </div>
            </div>
          @*  <div class="layui-row chuzhi">
                <div class="layui-col-xs3 edit-label ">当前有效期始</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="80" id="Owner_StartTime" name="Owner_StartTime" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">当前有效期止</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input" maxlength="80" id="Owner_EndTime" name="Owner_EndTime" disabled />
                </div>
            </div>*@
            <div class="layui-row chuzhi">
                <div class="layui-col-xs3 edit-label ">账户余额</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-floatLimit" maxlength="80" id="Owner_Balance" name="Owner_Balance" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">退费金额</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-floatLimit" maxlength="10" id="Car_PayMoney" name="Car_PayMoney" placeholder="请输入退费金额（元）" />
                </div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>确定</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1214" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var index = parent.layer.getFrameIndex(window.name);
        var laydate = null;
        var layform = null;
        layui.use(['laydate', 'form'], function () {
            laydate = layui.laydate;
            layform = layui.form;

            layform.render("select");
            pager.init()
        });

        var carCategoryObj = {
            temparr: ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'],//临时车类型
            montharr: ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'],//月租车类型
            freearr: ['3656'],//免费车类型
            prepaidarr: ['3657'],//储值车类型
            visitorarr: ['3658'],//免费车类型
        }
    </script>
    <script>
        var paramOwnerNo = $.getUrlParam("Owner_No");
        var paramCarNo = $.getUrlParam("Car_No");
        var paramCarCarNo = $.getUrlParam("Car_CarNo");
        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindData();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
               
            },
            //数据绑定
            bindData: function () {
                $.getJSON("/Owner/GetOwnerByNo", { Owner_No: paramOwnerNo }, function (json) {
                    if (json.Success) {
                        $("#verifyCheck").fillForm(json.Data, function (data) { });
                    }
                });

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Owner_No = paramOwnerNo;
                        return data;
                    });

                    var msg = "当前车位号【<t class='red'>" + $("#Owner_Space").val() + "</t>】，<br/>";
                        msg += "&nbsp;&nbsp;&nbsp;车主姓名：<t class='red'>" + $("#Owner_Name").val() + "</t>，<br/>";
                        msg += "&nbsp;&nbsp;&nbsp;账户余额：<t class='red'>" + $("#Owner_Balance").val() + "</t> 元，<br/>";
                        msg += "&nbsp;&nbsp;&nbsp;退费金额：<t class='red'>" + $("#Car_PayMoney").val() + "</t> 元，<br/>&nbsp;&nbsp;&nbsp;";
                    layer.open({
                        type: 0,
                        title: "退费提示",
                        btn: ["确定", "取消"],
                        content: msg + "确定退费吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Save").attr("disabled", true);
                            $.getJSON("/Owner/RefundFee", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    layer.msg("退费成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            });
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); }
                    })
                });

            }
        };


    </script>
</body>
</html>
