﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索设备</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet">
    <style>
        .layui-form-checked span {
            background-color: #1ab394;
        }

        .layui-form-checked:hover span {
            background-color: #48C2A9;
        }

        .layui-btn-xs {
            height: 27px;
            line-height: 27px;
            padding: 0 12px;
            font-size: 14px
        }

        .layui-card-header {
            border-bottom: none;
        }

        .layui-card-body {
            padding: 0px 15px;
            padding-bottom: 15px;
        }

        /* div#searchForm { height: 38px; } */

        .layui-btn .layui-icon {
            padding: 0 2px;
            vertical-align: middle;
        }

        /* 更新禁用选择按钮的样式 */
        .layui-btn-select-disabled {
            background-color: #FF5722 !important;
            color: #fff !important;
            cursor: not-allowed !important;
            border: none !important;
        }

            /* 确保鼠标悬停时样式不变 */
            .layui-btn-select-disabled:hover {
                background-color: #FF5722 !important;
                color: #fff !important;
            }

        /* 其他新样式保持不变 */
        .layui-btn-bound {
            background-color: #FFB800;
            color: #fff;
            cursor: not-allowed;
        }

        .layui-form-checkbox-bound {
            border-color: #FF5722 !important;
            cursor: not-allowed;
        }

            .layui-form-checkbox-bound span {
                background-color: #FF5722 !important;
                color: #fff !important;
            }

            .layui-form-checkbox-bound:hover span {
                background-color: #FF5722 !important;
            }

            .layui-form-checkbox-bound i {
                color: #FF5722 !important;
            }

        /* 自定义提示框样式 */
        .custom-tooltip {
            display: none;
            position: absolute;
            background-color: rgba(26, 179, 148, 0.9);
            color: #fff;
            padding: 10px;
            font-size: 14px;
            max-width: 300px;
            z-index: 1000;
            border: 1px solid rgba(26, 179, 148, 0.8);
            box-shadow: 0 4px 8px rgba(20, 139, 116, 0.8);
        }
    </style>
</head>

<body>
    <div class="ibox-content">
        <div class="layui-card">
            <div class="layui-card-header  layui-form">
                <div id="searchForm">
                    <div class="layui-inline">
                        <input class="layui-input" name="inputDeviceRecord_IP" id="inputDeviceRecord_IP"
                               autocomplete="off" placeholder="IP地址" value="" />
                    </div>
                    <div class="layui-inline">
                        <select data-placeholder="设备状态" class="form-control chosen-select " id="selectDeviceStatus"
                                name="selectDeviceStatus" lay-filter="selectDeviceStatus" lay-search>
                            <option value="">设备状态</option>
                            <option value="0">未记录</option>
                            <option value="1">已记录</option>
                            <option value="2">已绑定</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="Search">
                            <i class="layui-icon layui-icon-refresh inbtn"></i>
                            <t> 刷新数据</t>
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="searchNetworkDevices">
                            <i class="layui-icon layui-icon-search inbtn"></i>
                            <t> 查找网络设备</t>
                        </button>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="SaveAll">
                            <i class="layui-icon layui-icon-star inbtn"></i>
                            <t> 保存全部记录</t>
                        </button>
                    </div>
                </div>
            </div>

            <div class="layui-card-body">
                <table class="layui-hide" id="deviceTable" lay-filter="deviceTable"></table>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>

    <script type="text/html" id="tools">
        <div class="layui-btn-container">
            {{# if(d.DeviceRecord_Statues !== 2){ }}
                <button class="layui-btn layui-btn-sm layui-btn-xs" lay-event="selectDevice">选择</button>
            {{# } else { }}
                <button class="layui-btn layui-btn-sm layui-btn-xs layui-btn-select-disabled" disabled>选择</button>
            {{# } }}
            @* <button class="layui-btn layui-btn-sm layui-btn-xs" lay-event="editDevice">修改</button> *@
            {{# if(d.DeviceRecord_Category==1||d.DeviceRecord_Category==4){ }}
            <button class="layui-btn layui-btn-sm layui-btn-xs" lay-event="loginDevice">登录设备</button>
            {{# } else { }}
                <button class="layui-btn layui-btn-sm layui-btn-xs layui-btn-select-disabled" lay-event="loginDevice">登录设备</button>
            {{# } }}
        </div>
    </script>

    <script>
        var deviceCategory = $.getUrlParam("Category");
        layui.use(['table', 'form'], function () {
            var table = layui.table,
                util = layui.util,
                form = layui.form;

            var tableData = []; // 用于存储表格的初始数据
            var currentFilter = {
                ip: '',
                status: ''
            };

            var isSearching = false;
            var searchInterval;
            var searchTimeout;
            var countdownInterval;
            var remainingTime;

            // 初始化表格
            table.render({
                elem: '#deviceTable',
                url: '/Device/GetDeviceRecord?Category=' + deviceCategory,
                page: false,
                totalRow: false,
                limit: 128,
                height: 'full-100',
                width: 'full-15',
                text: { none: '暂无设备数据，请先查找下网络设备吧！' },
                cols: [[
                    { field: 'DeviceRecord_ID', title: '序号', width: '50', hide: true },
                    { field: 'DeviceRecord_IP', title: 'IP地址', width: '140', sort: true },
                    { field: 'DeviceRecord_Port', title: '端口号', width: '80', hide: true },
                    { field: 'DeviceRecord_User', title: '设备登录账号', width: '140', hide: true },
                    { field: 'DeviceRecord_Pwd', title: '设备登录密码', width: '140', hide: true },
                    { field: 'DeviceRecord_Category', title: '设备类别', width: '120', hide: true },
                    { field: 'DeviceRecord_Gateway', title: '网关', width: '120', hide: true },
                    { field: 'DeviceRecord_Mac', title: 'MAC地址', width: '120', hide: true },
                    { field: 'DeviceRecord_Mask', title: '子网掩码', width: '120', hide: true },
                    { field: 'DeviceRecord_Dns', title: 'DNS', width: '120', hide: true },
                    { field: 'DeviceRecord_Type', title: '设备型号', width: '120', sort: true },
                    {
                        title: '设备状态<i class="layui-icon layui-icon-tips layui-font-14 status-tip" data-tooltip="记录设备将保存到数据库，不记录设备则在6分钟后缓存失效时不再显示，已绑定设备不可更改状态。" style="margin-left: 5px;"></i>',
                        width: '122',
                        templet: function (d) {
                            if (d.DeviceRecord_Statues === 2) {
                                return '<div class="layui-form-checkbox layui-form-checkbox-bound" lay-skin="tag"><span>已绑定</span><i class="layui-icon layui-icon-ok"></i></div>';
                            } else {
                                return `<input type="checkbox" name="IsEnable" lay-filter="deviceStatusChange" value="${d.DeviceRecord_IP}" title="${d.DeviceRecord_Statues === 1 ? '已记录' : '未记录'}" lay-skin="tag" ${d.DeviceRecord_Statues === 1 ? 'checked' : ''} />`;
                            }
                        }
                    },
                    { field: 'DeviceRecord_Remark', title: '设备名称', width: '225', edit: 'text' },
                    { field: 'operation', title: '操作', width: '242', toolbar: '#tools' }
                ]],
                done: function (res) {
                    tableData = res.data; // 缓存初始数据
                    form.render();

                    // 创建自定义提示框
                    $('body').append('<div class="custom-tooltip"></div>');
                    var $tooltip = $('.custom-tooltip');

                    // 为带有 data-tooltip 属性的元素添加鼠标事件
                    $(document).on('mouseenter', '[data-tooltip]', function (e) {
                        var tooltipText = $(this).attr('data-tooltip');
                        $tooltip.text(tooltipText).fadeIn(200);
                    }).on('mousemove', '[data-tooltip]', function (e) {
                        $tooltip.css({
                            top: e.pageY + 10,
                            left: e.pageX + 10
                        });
                    }).on('mouseleave', '[data-tooltip]', function () {
                        $tooltip.fadeOut(200);
                    });
                }
            });

            // 绑定IP输入框事件
            $('#inputDeviceRecord_IP').on('input keyup', function (e) {
                if (e.type === 'input' || (e.type === 'keyup' && (e.key === 'Enter' || e.keyCode === 13))) {
                    currentFilter.ip = this.value;
                    reloadTablePreserveScroll(true);
                }
            });

            //保存全部记录按钮事件
            $('#SaveAll').on('click', function () {
                var params = filterTableData();
                debugger;
                if (params.length > 0) {
                    $.ajax({
                        url: '/Device/SaveAllDeviceRecords',
                        type: 'POST',
                        data: { jsonModel: JSON.stringify(params) },
                        success: function (res) {
                            if (res.success === true) {
                                tableData.forEach(function (item) {
                                    params.find(function (item2) {
                                        if (item.DeviceRecord_IP === item2.DeviceRecord_IP) {
                                            item.DeviceRecord_Statues = 1;
                                        }
                                    })
                                });
                                reloadTablePreserveScroll(false)
                                layer.msg('保存成功', { icon: 1 });
                            } else {
                                layer.msg('保存失败', { icon: 0 });
                            }
                        },
                        error: function (err) {
                            layer.msg('保存失败', { icon: 0 });
                        }
                    });
                }
                else {
                    layer.msg('当前表格无记录，如已进行条件筛选，你可以取消条件筛选或执行查找网络设备！', { icon: 0 });
                }
            });

            // 刷新数据按钮事件
            $('#Search').on('click', function () {
                layer.load(2);
                $.ajax({
                    url: '/Device/GetDeviceRecord',
                    type: 'GET',
                    data: { Category: deviceCategory },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.success === true && Array.isArray(res.data)) {
                            tableData = res.data;
                            reloadTablePreserveScroll(false);
                            layer.msg('数据刷新成功', { icon: 1 });
                        } else {
                            layer.msg('获取数据失败：' + (res.msg || '未知错误'), { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.closeAll('loading');
                        layer.msg('获取数据请求失败，请检查网络连接', { icon: 2 });
                    }
                });
            });

            //查找网络设备按钮事件
            $('#searchNetworkDevices').on('click', function () {
                if (isSearching) {
                    cancelSearch();
                } else {
                    startSearch();
                }
            });

            // 绑定设备状态下拉框事件
            form.on('select(selectDeviceStatus)', function (data) {
                currentFilter.status = data.value;
                reloadTablePreserveScroll(true);
            });

            table.on('tool(deviceTable)', function (obj) {
                var data = obj.data;
                if (obj.event === 'selectDevice') {
                    if (data.DeviceRecord_Statues !== 2) {
                        // 选择设备并返回数据到父窗口
                        var index = parent.layer.getFrameIndex(window.name); // 获取窗口索引
                        parent.handleDeviceSelection(data); // 调用父窗口的处理函数
                        parent.layer.close(index); // 关闭当前窗口
                    }
                }
                else if (obj.event === 'loginDevice') {
                    if (deviceCategory == '1') {
                        window.open('http://' + data.DeviceRecord_IP, '_blank');
                    }
                    else if (deviceCategory == '4') {
                        window.open('http://' + data.DeviceRecord_IP + ':8099', '_blank');
                    }
                }
            });

            table.on('edit(deviceTable)', function (obj) {
                var value = obj.value, data = obj.data, field = obj.field;
                // 更新缓存中的数据
                var index = tableData.findIndex(item => item.DeviceRecord_ID === data.DeviceRecord_ID);
                if (index !== -1) {
                    tableData[index][field] = value;
                    var param = tableData[index];
                    $.post("SaveSearchResults", { jsonModel: JSON.stringify(param), act: param.DeviceRecord_Statues }, function (json) {
                        if (json.success) {
                            layer.msg("设备名称更新成功！", { icon: 1 })
                        } else {
                            layer.msg(json.msg)
                        }
                    }, "json");
                }
            });

            form.on('checkbox(deviceStatusChange)', function (obj) {
                var $this = $(this);
                var ip = $this.val();
                var index = tableData.findIndex(item => item.DeviceRecord_IP === ip);
                if (index !== -1) {
                    var checked = $this.prop('checked');
                    var txt = checked ? '已记录' : '未记录';
                    // 更新复选框的标题
                    $this.attr('title', txt);
                    tableData[index].DeviceRecord_Statues = checked ? 1 : 0;
                    // 如果当前正在按状态筛选，则需要重新加载表格
                    if (currentFilter.status !== '') {
                        reloadTablePreserveScroll(true);
                    }
                    // 重新渲染表单，确保更改生效
                    form.render('checkbox');

                    var param = tableData[index];
                    $.post("SaveSearchResults", { jsonModel: JSON.stringify(param), act: param.DeviceRecord_Statues }, function (json) {
                        if (json.success) {
                            layer.msg("设备状态更新成功！", { icon: 1 });
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                }
            });

            // 过滤函数
            function filterTableData() {
                if (!Array.isArray(tableData)) {
                    return [];
                }
                return tableData.filter(function (item) {
                    if (!item) return false;
                    var ipMatch = !currentFilter.ip || item.DeviceRecord_IP.toLowerCase().indexOf(currentFilter.ip.toLowerCase()) !== -1;
                    var statusMatch = currentFilter.status === '' || item.DeviceRecord_Statues.toString() === currentFilter.status;
                    return ipMatch && statusMatch;
                });
            }

            // 修改 reloadTablePreserveScroll 函数
            function reloadTablePreserveScroll(isLength) {
                if (!Array.isArray(tableData)) {
                    tableData = [];
                }

                if (isLength == true && tableData.length === 0) {
                    return;
                }

                var textTip = tableData.length === 0 ? '暂无设备数据，请先查找下网络设备吧！' : '筛选数据无结果';

                var filteredData = filterTableData();

                // 保存滚动位置
                var scrollTop = 0;
                var layuiTable = $('.layui-table-main');
                if (layuiTable != null && layuiTable.length > 0) {
                    scrollTop = layuiTable[0].scrollTop;
                }

                table.reload('deviceTable', {
                    data: filteredData,
                    url: '',
                    text: { none: textTip },
                    done: function (res) {
                        if (layuiTable != null && layuiTable.length > 0) {
                            $('.layui-table-main').scrollTop(scrollTop);
                        }
                    }
                });
            }

            // 修改 addOrUpdateDevice 函数
            function addOrUpdateDevice(device) {
                if (!device || typeof device !== 'object' || !device.DeviceRecord_IP) {
                    return;
                }

                if (!Array.isArray(tableData)) {
                    tableData = [];
                }

                var existingIndex = tableData.findIndex(function (item) {
                    return item && item.DeviceRecord_IP === device.DeviceRecord_IP;
                });

                if (existingIndex !== -1) {
                    device.DeviceRecord_Statues = tableData[existingIndex].DeviceRecord_Statues;
                    tableData[existingIndex] = { ...tableData[existingIndex], ...device };
                } else {
                    tableData.push(device);
                }
            }

            // 修改 startSearch 函数
            function startSearch() {
                isSearching = true;
                remainingTime = 15;
                updateSearchButton();
                layer.msg('开始搜索网络设备', { icon: 1 });

                $.ajax({
                    url: '/Device/StartNetworkDeviceSearch',
                    type: 'GET',
                    data: { Category: deviceCategory },
                    success: function (res) {
                        if (res.success) {
                            searchInterval = setInterval(pollSearchResults, 2500);
                            countdownInterval = setInterval(updateCountdown, 1000);
                            searchTimeout = setTimeout(function () {
                                cancelSearch();
                            }, remainingTime * 1000);
                        } else {
                            layer.msg('启动搜索失败: ' + res.message, { icon: 2 });
                            cancelSearch();
                        }
                    },
                    error: function () {
                        layer.msg('请求失败，请检查网络连接', { icon: 2 });
                        cancelSearch();
                    }
                });
            }

            // 取消搜索
            function cancelSearch() {
                isSearching = false;
                clearInterval(searchInterval);
                clearInterval(countdownInterval);
                clearTimeout(searchTimeout);
                updateSearchButton();

                $.ajax({
                    url: '/Device/CancelNetworkDeviceSearch',
                    type: 'POST',
                    success: function (res) {
                        layer.msg('设备搜索任务结束', { icon: 1 });
                    },
                    error: function () {
                        layer.msg('取消请求失败，请检查网络连接', { icon: 2 });
                    }
                });
            }

            // 更新搜索倒计时
            function updateCountdown() {
                remainingTime--;
                updateSearchButton();
                if (remainingTime <= 0) {
                    cancelSearch();
                }
            }

            // 更新搜索按钮文本
            function updateSearchButton() {
                var buttonText = isSearching
                    ? `<i class="layui-icon layui-icon-close inbtn"></i><t> 取消查找 (${remainingTime}s)</t>`
                    : '<i class="layui-icon layui-icon-search inbtn"></i><t> 查找网络设备</t>';
                $('#searchNetworkDevices').html(buttonText);
            }

            // 修改 pollSearchResults 函数
            function pollSearchResults() {
                $.ajax({
                    url: '/Device/GetSearchResults',
                    type: 'GET',
                    data: { Category: deviceCategory },
                    success: function (res) {
                        if (res.success) {
                            if (res.data.isCompleted) {
                                layer.msg('设备搜索完成', { icon: 1 });
                                isSearching = false;
                                clearInterval(searchInterval);
                                clearInterval(countdownInterval);
                                clearTimeout(searchTimeout);
                                updateSearchButton();
                            }
                            res.data.devices.forEach(addOrUpdateDevice);
                            reloadTablePreserveScroll(false);
                        } else {
                            layer.msg('获取搜索结果失败: ' + res.message, { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('请求失败，请检查网络连接', { icon: 2 });
                    }
                });
            }
        });
    </script>
</body>

</html>