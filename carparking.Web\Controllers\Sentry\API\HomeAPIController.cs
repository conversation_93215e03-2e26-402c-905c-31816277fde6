﻿using carparking.Common;
using carparking.Config;
using carparking.PassTool;
using carparking.SentryBox;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using carparking.BLL.Cache;
using carparking.AutoSentryBox;
using NPOI.POIFS.Crypt;

namespace carparking.Web.Controllers.API
{
    /// <summary>
    /// 用于工控机消息交互
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class HomeAPIController : ControllerBase
    {
        /// <summary>
        /// 获取授权信息
        /// </summary>
        [HttpPost("GetAuth")]
        public void GetAuth()
        {
            try
            {
                var ret = GoHelper.GetHttpDeviceAuth(AppBasicCache.SentryHostInfo?.SentryHost_No, out var content, AppBasicCache.SentryHostInfo?.SentryHost_IP, false);
                LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[GetAuth]API获取授权信息：{content}");
                MiniResponse.ResponseResult("获取成功", ret, content);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetAuth]获取授权信息异常");
                MiniResponse.ResponseResult("异常错误", false);
            }
        }

        /// <summary>
        /// 账号列表
        /// </summary>
        [HttpPost("GetAccounts")]
        public void GetAccounts()
        {
            try
            {
                var models = BLL.Admins.GetAllEntity("Admins_Account", $"Admins_Enable=1");
                MiniResponse.ResponseResult("获取成功", true, models);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[HomeAPI]账号列表异常");
                MiniResponse.ResponseResult("异常错误", false);
            }
        }

        /// <summary>
        /// 登录
        /// </summary>
        [HttpPost("Login")]
        public void Login([FromBody] JObject jo)
        {
            try
            {
                //JObject jo = TyziTools.Json.ToObject<JObject>(body);

                if (jo == null || !jo.ContainsKey("data"))
                {
                    MiniResponse.ResponseResult($"账号密码信息错误，请重试", false, null);
                    return;
                }

                string encrypted = jo["data"].ToString();
                if (string.IsNullOrWhiteSpace(encrypted))
                {
                    MiniResponse.ResponseResult($"账号密码信息错误，请重试", false);
                    return;
                }

                var logMsg = AESHelper.AesDecrypt(encrypted, "aB8d5F2g7Hj3k9p0", "rT4e7W9q3sZ6x2c1");
                if (string.IsNullOrWhiteSpace(logMsg))
                {
                    MiniResponse.ResponseResult($"账号密码信息错误，请重试", false);
                    return;
                }

                var jo2 = TyziTools.Json.ToObject<JObject>(logMsg);
                if (jo2 == null || !jo2.ContainsKey("Admins_Account") || !jo2.ContainsKey("Admins_Pwd"))
                {
                    MiniResponse.ResponseResult($"账号密码信息错误，请重试", false, null);
                    return;
                }

                string Admins_Account = Convert.ToString(jo2["Admins_Account"]);
                string Admins_Pwd = Convert.ToString(jo2["Admins_Pwd"]);

                Model.SentryHost sentryHost = null;
                if (AppCache.IsWindows)
                {
                    sentryHost = BLL.SentryHost.GetEntity(ConfigurationMap.GetModel.SentryHostNo);
                    if (sentryHost == null)
                    {
                        MiniResponse.ResponseResult($"[{ConfigurationMap.GetModel.SentryHostNo}]岗亭初始化失败,请检查[{ConfigurationMap.Path}]配置文件或重新向导来进行修复", false, null);
                        return;
                    }
                }
                else
                {
                    sentryHost = AppBasicCache.GetSentryHost();
                    if (sentryHost == null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[Login]API岗亭初始化失败！");
                        MiniResponse.ResponseResult($"岗亭初始化失败", false, null);
                        return;
                    }
                }

                Model.PowerGroup powergroup = new Model.PowerGroup();
                Admins_Pwd = Utils.MD5Encrypt(Admins_Pwd + Utils.passwordMD5String, Encoding.UTF8);
                Model.Admins lgAdmins = BLL.Admins.GetEntity("*", Admins_Account, Admins_Pwd);

                #region 校验登录参数

                if (lgAdmins == null)
                {
                    MiniResponse.ResponseResult("账号或密码错误", false, null);
                    return;
                }

                if (lgAdmins.Admins_Enable != 1)
                {
                    MiniResponse.ResponseResult("账号未启用", false, null);
                    return;
                }

                powergroup = BLL.PowerGroup.GetEntity(lgAdmins.Admins_PowerNo);
                if (powergroup == null)
                {
                    MiniResponse.ResponseResult("权限组不存在", false, null);
                    return;
                }

                if (powergroup.PowerGroup_Enable != 1)
                {
                    MiniResponse.ResponseResult("权限已被禁用", false, null);
                    return;
                }

                if (!Powermanage.PowerOnlyCheck(powergroup.PowerGroup_Value, "WinFormMonitor", PowerEnum.Login.ToString()))
                {
                    MiniResponse.ResponseResult("当前账号没有登录岗亭的权限", false, null);
                    return;
                }

                #endregion

                string loginIp = HttpHelper.GetLoginIp(HttpContext, AppBasicCache.Ip);
                LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{loginIp}]HomeAPI/Login登录");

                DogUtil.InitDevice(loginIp, sentryHost, lgAdmins?.Admins_Account);
                if (!DogModel.DogExist)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[DogExist]API检测到加密狗不存在,为了不影响软件正常使用，请插入加密狗或者联系管理员！");
                    MiniResponse.ResponseResult("检测到加密狗不存在,为了不影响软件正常使用，请插入加密狗或者联系管理员！", false, null);
                    return;
                }

                if (DogModel.DogRealExist)
                {
                    var parking = BLL.Parking.GetEntity(null);
                    if (parking != null && parking.Parking_iXieYi == null && parking.Parking_iType == null && parking.Parking_iSonType == null)
                    {
                        if (DogCommon.bAuthorization)
                        {
                            parking.Parking_iXieYi = DogCommon.iXieYi;
                            parking.Parking_iType = DogCommon.iType;
                            parking.Parking_iSonType = DogCommon.iSonType;
                            BLL.Parking.UpdateByModel(parking);
                            _ = CustomThreadPool.SyncTaskPool?.QueueTask(null, () =>
                            {
                                //尝试推送到别的岗亭，不成功也不影响，数据已经保存到本地
                                BLL.ParkApi.UpDogInfor(AppCache.GetParking.Parking_No, new Tuple<int, int, int>(DogCommon.iType, DogCommon.iSonType, DogCommon.iXieYi));
                                return Task.CompletedTask;
                            });
                        }
                    }
                    else if (parking == null || parking.Parking_iXieYi != DogCommon.iXieYi || (parking.Parking_iType != DogCommon.iType && !DogCommon.iDogTypeList.Contains(DogCommon.iType)) || (parking.Parking_iSonType != DogCommon.iSonType && !DogCommon.iDogSongTypeList.Contains(DogCommon.iSonType)))
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[DogExist]API数据库加密信息与当前软件狗信息不匹配请联系管理员！");
                        DogUtil.DogMsg = "数据库加密信息与当前软件狗信息不匹配请联系管理员！";
                        MiniResponse.ResponseResult("数据库加密信息与当前软件狗信息不匹配请联系管理员", false, null);
                        return;
                    }
                }

                var old = BLL.BaseBLL._GetEntityByWhere(new Model.WorkShift(), "*", $"WorkShift_Status=1 and WorkShift_OffAccount='{Admins_Account}'");
                if (old == null)
                {
                    var ret = BLL.WorkShift.Add(new Model.WorkShift()
                    {
                        WorkShift_No = Utils.CreateNumber,
                        WorkShift_OnTime = DateTimeHelper.GetNowTime(),
                        WorkShift_Status = 1,
                        WorkShift_OffAccount = Admins_Account,
                        WorkShift_OffName = lgAdmins.Admins_Name
                    });
                    if (ret != 1)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[{lgAdmins.Admins_Account}]写入登录记录失败，从而影响换班时间");
                    }
                }

                #region 将登录参数写入缓存

                Model.AdminSession session = JsonConvert.DeserializeObject<Model.AdminSession>(JsonConvert.SerializeObject(lgAdmins));
                session.Admins_IPAddress = loginIp;
                session.token = Guid.NewGuid().ToString("N");
                session.PowerGroup_ID = powergroup.PowerGroup_ID;
                session.PowerGroup_Name = powergroup.PowerGroup_Name;
                session.PowerGroup_Value = powergroup.PowerGroup_Value;
                session.Admins_LoginTime = old?.WorkShift_OnTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");

                var oldCode = DataCache.LoginAuthCode.Get(session.Admins_Account);
                if (!string.IsNullOrEmpty(oldCode))
                {
                    var oldAdmin = DataCache.Admin.Get(oldCode);
                    if (oldAdmin != null)
                    {
                        LogManagementMap.WriteToFile(LoggerEnum.LoginSentry, $"[APILogin][{session.Admins_Account}]登录IP：{session.Admins_IPAddress}，上一次登录IP：{oldAdmin.Admins_IPAddress}，登录时间：{oldAdmin.Admins_LoginTime}");
                    }

                    DataCache.Admin.Del(oldCode);
                }

                DataCache.Admin.Set(session.token, session);
                DataCache.LoginAuthCode.Set(session.Admins_Account, session.token);

                #endregion

                #region 将登陆账号的车道权限写入到WS缓存

                List<string> lanes = new List<string>();
                if (!string.IsNullOrWhiteSpace(lgAdmins.Admins_PasswayNo))
                    lanes = JsonConvert.DeserializeObject<List<string>>(lgAdmins.Admins_PasswayNo);
                WebSocketHandler.AcccountLaneCache.TryRemove(lgAdmins.Admins_Account, out var del);
                WebSocketHandler.AcccountLaneCache.TryAdd(lgAdmins.Admins_Account, lanes);

                #endregion

                JObject objControl = JObject.Parse(powergroup.PowerGroup_Value);
                var powerValueStr = objControl["WinFormMonitor"]; //获取到当前控制器下的所有权限

                LogManagementMap.WriteToFile(LoggerEnum.Login, $"[Login]API登录成功：{Admins_Account}");
                BLL.UserLogs.AddLog(session, LogEnum.Backend, "用户登录", $"[Login API][{sentryHost?.SentryHost_IP}]{Admins_Account}登录岗亭管理");
                MiniResponse.ResponseResult("登录成功", true, new { token = session.token, power = TyziTools.Json.ToString(powerValueStr), passwayno = lgAdmins.Admins_PasswayNo, time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[HomeAPI]登录异常");
                MiniResponse.ResponseResult("异常错误", false);
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        [HttpPost("StopLicensePlate")]
        public IActionResult StopLicensePlate([FromBody] JObject jo)
        {
            try
            {
                //获取一分钟内有没有来车
                Model.InCar incar = BLL.BaseBLL._GetEntityByWhere<Model.InCar>(new Model.InCar(), "InCar_ID", $"InCar_EnterTime>'{DateTime.Now.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss")}' and InCar_Status<201");
                // 获取车牌识别队列的数量
                int count = DeviceServerHandle.LicensePlateQueue.Values.Sum(q => q.Count);

                if (incar == null && count < 1)
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "已停止识别车牌，请立刻停止服务");
                    DeviceServerHandle.IsLicensePlateQueue = false;
                    return Ok(new { success = true, msg = "已停止识别车牌，请立刻停止服务", data = default(object), code = 1 });
                }
                else
                {
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, "来车频繁，请勿停止服务");
                    return Ok(new { success = true, msg = "来车频繁，请勿停止服务", data = default(object), code = 0 });
                }
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[HomeAPI]停止服务异常");
                return Ok(new { success = false, msg = "异常错误", data = default(object), code = 0 });
            }
        }

        /// <summary>
        /// 获取配置参数
        /// </summary>
        [HttpPost("GetConfig")]
        public IActionResult GetConfig([FromBody] JObject jo)
        {
            try
            {
                Dictionary<string, string> dic = new Dictionary<string, string>();
                dic.Add("webport", AppSettingConfig.SiteDomain_WebPort);
                dic.Add("sentryport", AppSettingConfig.SiteDomain_BsPort);
                dic.Add("websocketport", AppSettingConfig.WebSocketPort);

                #region 阿里云参数

                var sysconfig = AppBasicCache.CurrentSysConfig ?? BLL.SysConfig.GetEntity();
                if (sysconfig != null)
                {
                    var sysconfigContent = Common.TyziTools.Json.ToObject<Model.SysConfigContent>(BLL.SysConfig.GetUrlDecode(sysconfig.SysConfig_Content));
                    if (sysconfigContent != null)
                    {
                        dic.Add("endpoint", sysconfigContent.SysConfig_AliyunEndpoint ?? "oss-cn-shenzhen.aliyuncs.com");
                        dic.Add("accesskeyid", sysconfigContent.SysConfig_AliyunAccessKeyId ?? "LTAIKypdJuYEjfhe");
                        dic.Add("accesskeysecret", sysconfigContent.SysConfig_AliyunAccessKeySecret ?? "B9FkPfjoAXQLqQExskDwaXo3IrvjYj");
                        dic.Add("imgbucket", sysconfigContent.SysConfig_AliyunImgBucket ?? "sfm-pdf-test");
                    }
                }

                #endregion

                return Ok(new { success = true, msg = "获取成功", data = dic, code = 0 });
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[HomeAPI]获取配置参数");
                return Ok(new { success = false, msg = "异常错误", data = default(object), code = 0 });
            }
        }

        /// <summary>
        /// Frp能否断开
        /// </summary>
        [HttpPost("GetFrpResult")]
        public void GetFrpResult()
        {
            try
            {
                int result = 1;
                var rlt = AppBasicCache.RTSPVideoPlay.Where(m => Math.Abs(DateTimeHelper.GetNowTime().Subtract(m.Value.Item3).TotalMinutes) < 10).ToList();
                if (rlt.Count == 0)
                {
                    result = 1;
                }
                else
                {
                    result = 0;
                }
                MiniResponse.ResponseResult("获取成功", true, result);
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFileException(ex, "[GetFrpResult]获取Frp能否断开信息异常");
                MiniResponse.ResponseResult("异常错误", false, 1);
            }
        }
    }
}