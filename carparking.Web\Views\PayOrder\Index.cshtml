﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>缴费记录查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 14px; font: normal normal normal 14px/1 FontAwesome; }
        .layui-form-select .layui-input { width: 182px; }
        /*  .layui-btn { line-height: normal !important; padding: 0 12px; }*/
        .layui-bg-wxgreen { background-color: #04BE02 !important; }
        .layui-bg-alipayblue { background-color: #1678ff !important; }
        .layui-bg-ylblue { background-color: #1678ff !important; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>缴费记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">

                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_CarNo" id="PayOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="订单类型" class="layui-select" id="PayOrder_OrderTypeNo" name="PayOrder_OrderTypeNo" lay-search>
                                    <option value="">订单类型</option>
                                    <option value="5901">临停车缴费</option>
                                    <option value="5902">月租车充值</option>
                                    <option value="5919">月租车缴费</option>
                                    <option value="5903">储值车充值</option>
                                    <option value="5905">储值车扣费</option>
                                    <!--<option value="5904">商家自助充值</option>
                                    <option value="5905">储值车扣费</option>-->
                                    <option value="5910">车位续期</option>
                                    <option value="0">其他</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_PayedTime0" id="PayOrder_PayedTime0" autocomplete="off" placeholder="支付时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")' />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_PayedTime1" id="PayOrder_PayedTime1" autocomplete="off" placeholder="支付时间止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                                @{
                                    if (carparking.BLL.Cache.AppBasicCache.IsWindows)
                                    {
                                        <button class="layui-btn" id="Search2"><i class="layui-icon layui-icon-search inbtn"> 同订单多笔缴费搜索</i></button>
                                    }
                                }
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_No" id="PayOrder_No" autocomplete="off" placeholder="支付订单号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_ParkOrderNo" id="PayOrder_ParkOrderNo" autocomplete="off" placeholder="停车订单号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_OwnerSpace" id="PayOrder_OwnerSpace" autocomplete="off" placeholder="系统车位号" maxlength="20" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付状态" class="layui-select" id="PayOrder_Status" name="PayOrder_Status" lay-search>
                                    <option value="">支付状态</option>
                                    <option value="0">未支付</option>
                                    <option value="1">支付成功</option>
                                    <option value="2">支付失败</option>
                                    <option value="3">用户取消</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付方式" class="layui-select" id="PayOrder_PayTypeCode" name="PayOrder_PayTypeCode" lay-search>
                                    <option value="">支付方式</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="layui-select" id="PayOrder_CarCardTypeNo" name="PayOrder_CarCardTypeNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="layui-select" id="PayOrder_CarTypeNo" name="PayOrder_CarTypeNo" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="操作员" class="layui-input" id="PayOrder_OperatorName2" name="PayOrder_OperatorName2" lay-search>
                                    <option value="">操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_OperatorName" id="PayOrder_OperatorName" autocomplete="off" placeholder="操作员" />
                            </div>

                            <div class="layui-inline">
                                <select data-placeholder="默认数据" class="form-control chosen-select " id="dataType" name="dataType" lay-search>
                                    <option value="0">默认数据</option>
                                    <option value="1">历史数据</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.PayOrder.Detail){}}<button class="layui-btn layui-btn-sm" lay-event="Detail"><i class="fa fa-list-alt"> 支付详情</i></button>{{# } }}
                                {{# if(Power.PayOrder.ParkDetail){}}<button class="layui-btn layui-btn-sm" lay-event="ParkDetail"><i class="fa fa-list-alt"> 停车详情</i></button>{{# } }}
                                {{# if(Power.PayOrder.Export){}}<button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>{{# } }}
                                {{# if(Power.PayOrder.Print){}}<button class="layui-btn layui-btn-sm" id="Print" lay-event="Print"><i class="fa fa-download"></i><t>打印</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplimg">
        <a href="{{d.CarRecog_Img}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看"><i class="layui-icon layui-icon-picture"></i>预览</a>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.PayOrder_Status==0){}}
        <span class="layui-badge layui-bg-orange">未支付</span>
        {{# }else if(d.PayOrder_Status==1){ }}
        <span class="layui-badge layui-bg-green">支付成功</span>
        {{# }else if(d.PayOrder_Status==2){ }}
        <span class="layui-badge layui-bg-red">支付失败</span>
        {{# }else if(d.PayOrder_Status==3){ }}
        <span class="layui-badge layui-bg-cyan">用户取消</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplordertype">
        {{# if(d.PayOrder_OrderTypeNo==5902){}}
        <span class="layui-badge layui-bg-blue">月租车充值</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5901){ }}
        <span class="layui-badge layui-bg-green">临停车缴费</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5903){ }}
        <span class="layui-badge layui-bg-cyan">储值车充值</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5904){ }}
        <span class="layui-badge layui-bg-orange">商家自助充值</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5905){ }}
        <span class="layui-badge layui-bg-black">储值车扣费</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5910){ }}
        <span class="layui-badge layui-bg-green">车位续期</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5919){ }}
        <span class="layui-badge layui-bg-blue">月租车缴费</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">其他</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpaytypecode">
        {{# if(d.PayOrder_PayTypeCode==79001){}}
        <span class="layui-badge layui-bg-red">线下现金支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79002){ }}
        <span class="layui-badge layui-bg-red">平台现金支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79003){ }}
        <span class="layui-badge layui-bg-wxgreen">微信支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79010){ }}
        <span class="layui-badge layui-bg-wxgreen">线下微信</span>
        {{# }else if(d.PayOrder_PayTypeCode==79013){ }}
        <span class="layui-badge layui-bg-wxgreen">微信无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79007){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79011){ }}
        <span class="layui-badge layui-bg-alipayblue">线下支付宝</span>
        {{# }else if(d.PayOrder_PayTypeCode==79012){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79004){ }}
        <span class="layui-badge layui-bg-cyan">Android端支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79006){ }}
        <span class="layui-badge layui-bg-cyan">终端设备支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79009){ }}
        <span class="layui-badge layui-bg-cyan">第三方支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79014){ }}
        <span class="layui-badge layui-bg-ylblue">建行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79015){ }}
        <span class="layui-badge layui-bg-ylblue">招行一网通支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79016){ }}
        <span class="layui-badge layui-bg-ylblue">银联无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79017){ }}
        <span class="layui-badge layui-bg-ylblue">建行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79018){ }}
        <span class="layui-badge layui-bg-ylblue">威富通聚合支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79019){ }}
        <span class="layui-badge layui-bg-ylblue">招行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79020){ }}
        <span class="layui-badge layui-bg-ylblue">工行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79021){ }}
        <span class="layui-badge layui-bg-ylblue">工行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79022){ }}
        <span class="layui-badge layui-bg-ylblue">农行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79023){ }}
        <span class="layui-badge layui-bg-ylblue">农行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79024){ }}
        <span class="layui-badge layui-bg-ylblue">ETC支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79025){ }}
        <span class="layui-badge layui-bg-ylblue">中行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79026){ }}
        <span class="layui-badge layui-bg-ylblue">中行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79027){ }}
        <span class="layui-badge layui-bg-ylblue">乐聚合支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79039){ }}
        <span class="layui-badge layui-bg-ylblue">随行付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79028){ }}
        <span class="layui-badge layui-bg-ylblue">银联商务</span>
           {{# }else if(d.PayOrder_PayTypeCode==79029){ }}
        <span class="layui-badge layui-bg-ylblue">充电抵扣</span>
        {{# }else if(d.PayOrder_PayTypeCode==80002){ }}
        <span class="layui-badge layui-bg-ylblue">自助缴费</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplname">
        {{# if (d.PayOrder_OrderTypeNo==5910){}}
        <span>{{d.PayOrder_OwnerName}}</span>
        {{# }else{ }}
        <span>{{d.PayOrder_CarNo}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplDiscountMoney">
        {{# if (d.PayOrder_DiscountMoney==null){}}0{{# }else{ }}
        {{d.PayOrder_DiscountMoney}}
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        var frmPrintIndex = null;
        topBar.init();

        layui.use(['table', 'form', 'laydate', 'element'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");

            searchFile.bindData(1);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            pager.conditionParam = conditionParam;
            pager.dataType = $("#dataType").val();

            var cols = [[
                { type: 'radio' }
                , { field: 'PayOrder_ID', title: 'ID', hide: true }
                , { field: 'PayOrder_No', title: '支付订单号', hide: true }
                , { field: 'PayOrder_ParkOrderNo', title: '停车订单号', hide: true }
                , { field: 'PayOrder_CarNo', title: '车牌号', width: 110 }
                , { field: 'PayOrder_OrderTypeNo', title: '订单类型', toolbar: "#tmplordertype" }
                , { field: 'PayOrder_OwnerSpace', title: '系统车位号' }
                , { field: 'PayOrder_CarCardTypeNo', title: '车牌类型' }
                , { field: 'PayOrder_CarTypeNo', title: '车牌颜色' }
                , { field: 'PayOrder_MonthBeginTime', title: '充值开始时间', hide: true }
                , { field: 'PayOrder_MonthEndTime', title: '充值结束时间', hide: true }
                , { field: 'PayOrder_MonthEffTime', title: '充值前结束时间', hide: true }
                , { field: 'PayOrder_OwnerNo', title: '车主编号', hide: true }
                , { field: 'PayOrder_OwnerName', title: '车主姓名' }
                , { field: 'PayOrder_Money', title: '应收金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_Money); }, sort: true }
                , { field: 'PayOrder_PayedMoney', title: '实收金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_PayedMoney); }, sort: true }
                , { field: 'PayOrder_DiscountMoney', title: '优惠金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_DiscountMoney); }, sort: true }
                , { field: 'PayOrder_StoredMoney', title: '储值金额抵扣', width: 130, totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_StoredMoney); }, sort: true }
                , { field: 'PayOrder_SelfMoney', title: '自助缴费金额', hide: true, totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_SelfMoney); }, sort: true }
                , { field: 'PayOrder_OutReduceMoney', title: '找零金额', hide: true, totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_OutReduceMoney); } }
                , { field: 'PayOrder_PayTypeCode', title: '支付方式', toolbar: "#tmplpaytypecode" }
                , { field: 'PayOrder_Status', title: '支付状态', toolbar: "#tmplstatus" }
                , { field: 'PayOrder_EnterTime', title: '入场时间', sort: true }
                , { field: 'PayOrder_PayedTime', title: '支付时间', sort: true }
                , { field: 'PayOrder_TempTimeCount', title: '停车时长', hide: true, templet: function (d) { return _DATE.getZhTimesbyMin(Math.ceil(d.PayOrder_TempTimeCount || 0)); } }
                //, { field: 'PayOrder_TimeCountDesc', title: '时长描述', hide: true }
                , { field: 'PayOrder_Desc', title: '支付描述', hide: true }
                , { field: 'PayOrder_OperatorName', title: '操作员' }
                , {
                    field: '明细', title: '', hide: true, templet: function (d) {
                        if (d.PayOrder_PayTypeCode == '80002' || d.PayOrder_SelfMoney > 0) {
                            var btn = '<div class="layui-btn layui-btn-xs btnCashDetail" style="padding:3px;" data-no="' + d.PayOrder_ParkOrderNo + '">查看明细</div>';
                            return btn;
                        } else {
                            return "";
                        }
                    }
                }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/PayOrder/GetPayOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    pager.dataCount = data.count;
                    tb_page_set(data);
                    if (data.code == 0) {
                        //追加总计行，需要查询时在msg中返回总计数据
                        var total = JSON.parse(data.msg);
                        if (total) {
                            var tr = $(".layui-table-total table tbody").find("tr").first();
                            tr.find('td[data-key="1-0-0"] div').text("合计");
                            $(".layui-table-total table tbody").append('<tr>' + tr.html() + '</tr>');
                            tr = $(".layui-table-total table tbody").find("tr").last();
                            tr.find('td[data-key="1-0-0"] div').text("总计").css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_Money"] div').text(parseFloat(total.PayOrder_Money).toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_PayedMoney"] div').text(parseFloat(total.PayOrder_PayedMoney).toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_DiscountMoney"] div').text(parseFloat(total.PayOrder_DiscountMoney).toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_StoredMoney"] div').text(parseFloat(total.PayOrder_StoredMoney).toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_SelfMoney"] div').text(parseFloat(total.PayOrder_SelfMoney).toFixed(2)).css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_OutReduceMoney"] div').text(parseFloat(total.PayOrder_OutReduceMoney).toFixed(2)).css({ "color": "red" });
                        }
                    }

                    //$("th[data-key='1-0-14']").prop("onclick",null).on("click",function(){ alert("fff")})
                    //$("th[data-key='1-0-16']").off("click").unbind("click").prop("onclick",null).on("click",function(){ alert("fff")})

                    $(".btnCashDetail").unbind("click").click(function () {
                        var orderNo = $(this).attr("data-no");
                        layer.open({
                            type: 2,
                            title: "自助缴费明细 - " + orderNo,
                            content: "/PayOrder/CashDetail?orderNo=" + orderNo,
                            area: getIframeArea(["800px", "650px"]),
                            maxmin: false
                        })
                    });
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var orderno = data[0].PayOrder_No;
                        pager.dataTime = data[0].PayOrder_Time;
                        pager.dataType = $("#dataType").val();
                        layer.open({
                            title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 支付详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'Detail?PayOrder_No=' + encodeURIComponent(orderno)
                        });
                        break;

                    case 'ParkDetail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var parkorderno = data[0].PayOrder_ParkOrderNo;
                        pager.dataTime = data[0].PayOrder_Time;
                        pager.dataType = $("#dataType").val();
                        if (parkorderno == null || parkorderno == '') { layer.msg("停车订单号为空"); return; }
                        layer.open({
                            title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 停车详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'ParkDetail?ParkOrder_No=' + encodeURIComponent(parkorderno)
                        });
                        break;
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                        if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                            if (conditionParam.PayOrder_PayedTime0 == null) { layer.msg("请选择支付时间起", { icon: 0 }); return; }
                            if (conditionParam.PayOrder_PayedTime1 == null) { layer.msg("请选择支付时间止", { icon: 0 }); return; }
                            if (_DATE.diffDay(new Date(conditionParam.PayOrder_PayedTime0), new Date(conditionParam.PayOrder_PayedTime1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                        }
                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/PayOrder/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&more=" + pager.more + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                    case 'Print':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var payorderno = data[0].PayOrder_No;
                        if (payorderno == null || payorderno == '') { layer.msg("支付订单号为空"); return; }
                        var time = data[0].PayOrder_Time;
                        var loadingIndex = layer.msg('正在处理，请稍候...', { icon: 16, time: 0 });
                        $.ajax({
                            type: 'post',
                            url: '/PayOrder/GetPrintData',
                            dataType: 'json',
                            data: { payorder_no: payorderno, time: time, dataType: pager.dataType },
                            success: function (json) {
                                if (json.success) {

                                    var data = json.data;

                                    var action = "PrintTempReceipt?";
                                    var param = "CarNo=" + data.CarNo + "&CarNo=" + data.CarNo + "&enterTime=" + data.enterTime + "&outTime=" + data.outTime + "&payedMoney=" + data.payedMoney +
                                        "&passwayNo=" + data.passwayNo + "&type=out&carCardType=" + data.carCardType + "&CarSpace=" + data.CarSpace;

                                    if (data.Type == "1") {
                                        action = "PrintMonthReceipt?";
                                    }
                                    var content = action + param;

                                    layer.close(loadingIndex);
                                    frmPrintIndex = layer.open({
                                        type: 2,
                                        title: "打印小票",
                                        content: content,
                                        area: getIframeArea(["380px", "600px"]),
                                        maxmin: false
                                    })

                                } else {
                                    layer.msg('获取订单数据失败：' + json.msg, { icon: 5 });
                                }
                            },
                            error: function () {
                                layer.msg('系统错误', { icon: 2 });
                            }
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_radio(table)

        });
    </script>
    <script>
        var pager = {
            more: "0",
            sortField: null,
            orderField: null,
            dataField: null,
            pageIndex: 1,
            dataType: 0,
            dataTime: null,
            conditionParam: null,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PayOrder_PayedTime0", "PayOrder_PayedTime1"], { type: "datetime", range: true });

                $.post("SltCarCardTypeList2", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#PayOrder_CarCardTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#PayOrder_CarTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltPayTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            if (item.PayType_Enable == 1) {
                                var option = '<option value="' + item.PayType_No + '">' + item.PayType_Name + '</option>';
                                $("#PayOrder_PayTypeCode").append(option);
                            }
                        });
                    }
                }, "json");

                $.post("SltAdminList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Admins_Name + '">' + d.Admins_Name + '</option>';
                            $("#PayOrder_OperatorName2").append(option);
                        });
                        var option2 = '<option value="场内支付">场内支付</option>';
                        $("#PayOrder_OperatorName2").append(option2);
                    }
                }, "json");

                layui.form.render("select");
            },
            bindData: function (index, more) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                pager.dataType = $("#dataType").val();
                if (more == "1") {
                    pager.more = "1";
                } else {
                    pager.more = "0";
                }
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/PayOrder/GetPayOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order, more: more } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1, "0"); });
                $("#Search2").click(function () { pager.bindData(1, "1"); });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "dataType" && data.value == "1") {
                        var layerDataType = layer.open({
                            id: 2,
                            type: 0,
                            title: "查询数据须知",
                            btn: ["知道了"],
                            content: "默认查询范围：当您未指定时间条件时，系统将自动查询当前年份的全部数据。<br/>跨年查询限制：若选择历史数据，查询时间范围需在同一年内。<br/>例如，若开始时间设定为2024年，则仅能查询2024年的相关数据，无法跨年查询2023年或2025年的数据。",
                            yes: function (res) {
                                layer.close(layerDataType)
                            },
                            btn2: function () { }
                        })
                    }
                })
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower.Search) {
                        $("#Search2").removeClass("layui-hide");
                    }
                });

                s_carno_picker.init("PayOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "PayOrder_CarNo") {
                        $("#PayOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }

        function CloseIndex(index) {
            layer.close(index);
        }
    </script>
</body>
</html>
