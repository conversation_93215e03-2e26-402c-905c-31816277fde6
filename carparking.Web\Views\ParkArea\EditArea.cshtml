﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html,body{width:100%;height:100%;padding:0;margin:0;}
        .layui-row { margin-bottom: 15px; }
        .m-label { padding:9px 0 0 10px; font-weight:bold;}
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .layui-card { box-shadow: none; }
        .layui-select-title input { color: #0094ff !important; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-card">
        <div class="layui-card-body layui-form" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">停车场名称</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <input type="text" class="layui-input" id="ParkArea_ParkName" name="ParkArea_ParkName" readonly />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">区域类型</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <div class="btnCombox falsemodify" id="ParkArea_Type">
                        <ul class="flex">
                            <li data-value="0">外场</li>
                            <li data-value="1">嵌套内场</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-row panl-areafid">
                <div class="layui-col-xs3 m-label">父区域</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <select class="layui-select" id="ParkArea_FNo" name="ParkArea_FNo" lay-filter="parkAreaFid" lay-search>
                        <option value="0">请选择</option>
                    </select>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">区域名称</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" maxlength="32" id="ParkArea_Name" name="ParkArea_Name" value="" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 m-label">车位数</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <input type="text" class="layui-input v-number v-null" id="ParkArea_SpaceNum" maxlength="4" name="ParkArea_SpaceNum" value="0" autocomplete="off" />
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <button class="btn btn-primary layui-hide" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-primary layui-bg-orange layui-hide" id="Cancel"><i class="fa fa-close"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layform = null;
        layui.use(['form'], function () {
            layform = layui.form;
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("ParkArea_No");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //数据绑定
            bindData: function () {
                $.post("GetParkAreaByNo", { ParkArea_No: paramNo }, function (json) {
                    if (json.success) {
                        var park = json.data.park;
                        var model = json.data.model;
                        var areaList = json.data.areaList;
                        $("#ParkArea_ParkName").val(park.Parking_Name)
                        pager.bindSelect(areaList, model);
                        if (paramAct == "Update") {
                            $("#verifyCheck").fillForm(model, function (data) { });
                            $("#ParkArea_FNo").attr("disabled", true);
                            layform.render('select');
                            LoadDeviceConfig(model)
                        } else {
                            LoadDeviceConfig(config)
                        }

                        if (paramAct == "Update") {
                            $(".falsemodify").each(function () {
                                var val = config[$(this).attr("id")];
                                $(this).find("ul li").each(function () {
                                    if ($(this).attr("data-value") != val)
                                        $(this).removeClass("layui-hide").addClass("layui-hide")
                                });
                            });
                        }
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json")
            },
            bindEvent: function () {

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.ParkArea_FNo = '';
                        data.ParkArea_FNo = $("#ParkArea_FNo").val();
                        return data;
                    });

                    param.ParkArea_Type = config.ParkArea_Type;
                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        LAYER_OPEN_TYPE_0("确定新增停车区域?", res => {
                            LAYER_LOADING("处理中...");
                            $.post("AddParkArea", { jsonModel: JSON.stringify(param) }, function (json) {
                                $("#Save").removeAttr("disabled")
                                if (json.success) {
                                    window.parent.location.reload();
                                    layer.msg("保存成功", { time: 1000 })
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    } else {
                        LAYER_OPEN_TYPE_0("确定修改停车区域?", res => {
                            LAYER_LOADING("处理中...");
                            param.ParkArea_No = paramNo;
                            $.post("UpdateParkArea", { jsonModel: JSON.stringify(param) }, function (json) {
                                $("#Save").removeAttr("disabled")
                                if (json.success) {
                                    window.parent.location.reload();
                                    layer.msg("保存成功", { time: 1000 })
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    }
                });

                $("#Cancel").click(function () {
                    parent.layer.closeAll();
                })
            },
            bindSelect: function (data, model) {
                var htm = "";
                var fid = model != null ? model.ParkArea_FNo : null;
                data.forEach(function (item, index, data) {
                    //过滤当前区域&当前区域的子区域
                    if (item.ParkArea_No != paramNo) {
                        htm += '<option value="' + item.ParkArea_No + '" ' + (item.ParkArea_FNo == fid? "selected" : "") + '>' + item.ParkArea_Name + '</option>';
                    }
                });
                $("#ParkArea_FNo").html(htm)
                layform.render('select');
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        };

    </script>

    <script>
        //设备参数配置[仅选项按钮]默认值
        var config = {
            ParkArea_Type: 0
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                //if (!onDisabledCom(idName)) { return; }
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                if (idName == 'ParkArea_Type') {
                    if (config[idName] == 0) { $(".panl-areafid").removeClass("layui-hide").addClass("layui-hide"); }
                    else { $(".panl-areafid").removeClass("layui-hide"); }
                }
            });
        });

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                if (idName == 'ParkArea_Type') {
                    if (config[idName] == 0) { $(".panl-areafid").removeClass("layui-hide").addClass("layui-hide"); }
                    else { $(".panl-areafid").removeClass("layui-hide"); }
                }
            });
        }

        var onDisabledCom = function (idName) {
            if (paramAct == "Update" && idName == "ParkArea_Type") {
                return false;
            }
            return true;
        }


        
    </script>
</body>
</html>
