﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车位有效期</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet" />
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-btn-warm { background-color: #ec971f; border-color: #d58512; }
        .layui-row { margin-bottom: 15px; }
        .layadmin-warning {line-height: 24px;font-size: 12px;text-align: justify;word-break: break-all;color: #888;background-color: lemonchiffon;float: left;padding: 3px 5px;letter-spacing: 0.6px; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div>
            <div>&nbsp;</div>
            <div style="overflow:hidden;height:0;">
                <!--防止浏览器保存密码后自动填充-->
                <input type="password" />
                <input type="text" />
                <input type="text" name="email" />
            </div>
        </div>
        <div class="ibox-content" style="padding: 1px;">
            <div id="verifyCheck" class="layui-form">
                <div class="layui-row form-group"></div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">有效期开始</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-date v-submit" id="start" name="start" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">有效期结束</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-date v-submit" id="end" name="end" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <input class="layui-form-checkbox" type="checkbox" id="clearfee" name="clearfee" title="已入场车辆不计算过期费用" lay-skin="primary">
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-form-label">&nbsp;</div>
                    <div class="layui-col-xs9 layadmin-warning">
                        <b>温馨提示：</b><br />
                        1、请谨慎设置，若车辆已入场，开始时间比车辆入场时间大，计费时判定这一段时间车辆过期。<br />
                        2、可批量设置车位有效期;此处设置有效期不会生成支付记录;有效期开始时间与结束时间都会被改变<br />
                        3、<b>勾选【已入场车辆不计算过期费用】可以不计算开始时间之前的过期费用</b>。<br />
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-footer">
            <button class="layui-btn layui-btn-md" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
            <button class="layui-btn layui-btn-md layui-btn-warm" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();
        layui.use(['laydate', 'form'], function () {
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = localStorage.getItem("Vaild_OwnerNoList");
        var cardtype = $.getUrlParam("cardtype");
        var dt = new Date().Format("yyyy-MM-dd");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var isOpen = true;
        var nowDate = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var pager = {
            model: null,
            rules: [],
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["start", "end"], { type: "date", range: true });
            },
            //数据绑定
            bindData: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;

                    var clearfee = $("#clearfee")[0].checked
                    var param = {
                        Owner_No: paramNo,
                        Owner_StartTime: $("#start").val().trim(),
                        Owner_EndTime: $("#end").val().trim(),
                        clearfee: (clearfee ? 1 : 0)
                    };

                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);
                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定修改选中车位的有效期?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $(this).attr("disabled", true);
                            $.post("OnUpdateVaild", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); }
                    })
                });
            }
        };

    </script>
</body>
</html>
