﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>通道设备设置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px; float: left; font-size: 16px; }
        html, body { width: 100%; height: 100%; overflow-x: hidden; }
        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }
        .leftmenu { user-select: none; padding: 1rem; }
        .leftmenu, .rightbody { height: 100%; overflow: auto; }
        @@media screen and (max-width: 400px) {
            .leftmenu, .rightbody { height: auto !important; }
        }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .content-panel { height: 100%; overflow: auto; }
        .min100 { min-height: 100%; }

        .menu-list { width: 100%; overflow: auto; position: relative; }
        .menu-title { line-height: 1.5rem; padding: 10px 0; border-bottom: 1px dashed #ddd; position: relative; cursor: pointer; }
        .menu-items { }
        .menu-item { padding-left: 0rem; }
        .menu-text { padding: .5rem 4rem .5rem 1.1rem; line-height: 1.5rem; position: relative; }
        .menu-text:hover { background-color: #ecf0f5; cursor: pointer; text-decoration: underline; }
        .menu-text::before { content: ""; padding: .5rem; background-size: 100% 100%; position: absolute; top: .75rem; }
        .menu-title::before { content: ""; padding: .5rem; background-size: 100% 100%; position: absolute; top: .85rem; }
        .menu-title.type1::before { background-image: url('../../Static/img/icon/icon_passway.svg'); left: 0rem; }
        .menu-text.type2::before { background-image: url('../../Static/img/icon/icon_camera.svg'); left: 1rem; }
        .menu-text.type3::before { background-image: url('../../Static/img/icon/icon_screen.svg'); left: 2rem; }
        .menu-text.level2.type3::before { left: 1rem; }
        .menu-title.level1 { padding-left: 1rem; }
        .menu-text.level2 { padding-left: 2rem; }
        .menu-text.level3 { padding-left: 3rem; }
        .menu-list div.m-active { font-weight: bold; }
        ::-webkit-scrollbar { width: 5px; background-color: rgba(0,0,0,.1); }
        ::-webkit-scrollbar-thumb { background-color: #bbb; border-radius: 5px; }
        .m-active-1 { color: #f6800f; font-weight: bold; }
        #framePanl { padding: 1rem; }
        #framePanl iframe { border: 0; width: 100%; height: calc(100% - 4px); }


        .layui-card-header { height: auto !important; }
        .a-title { padding: 15px 0; font-size: 1.5rem; }
        a.a-name { color: #0094ff; }
        a.a-name:hover { color: red; text-decoration: underline; }
        .layui-form-select .layui-input { width: 165px; }
        #View { float: right; margin-top: 6px; }
        .layui-badge{padding:0 4px;}
        .framList, .framDetail { height: 100%; overflow: auto; }
    </style>
</head>
<body class="animated fadeInRight">
    <div class="layui-col-sm3 leftmenu">
        <div class="layui-card min100">
            <div class="layui-card-header padding-15" id="addpanl">
                <button class="layui-btn layui-btn-fluid layui-bg-blue layui-hide" onclick="onOpenFrame()" id="Add">新增设备</button>
            </div>
            <div class="layui-card-body">
                <div class="menu-list">

                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-sm9 rightbody" id="framePanl">
        @*<iframe src="List" frameborder="0"></iframe>*@
        <div class="framDetail layui-hide">

        </div>
        <div class="framList layui-hide">
            <div class="layui-card min100">
                <div class="layui-card-header">
                    <div class="a-title">设备列表</div>
                    <div class="test-table-reload-btn layui-form form-group" id="searchForm">
                        <div class="layui-inline">
                            <select class="layui-input" id="Device_Category" name="Device_Category" lay-search>
                                <option value="">设备类型</option>
                                <option value="1">车牌识别相机</option>
                                <option value="4">自助停车设备</option>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <select data-placeholder="设备型号" class="layui-input" id="Device_DriveNo" name="Device_DriveNo" lay-search>
                                <option value="">设备型号</option>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <select data-placeholder="所属通道" class="layui-input" id="Device_PasswayNo" name="Device_PasswayNo" lay-search>
                                <option value="">所属通道</option>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <input class="layui-input " name="Device_Name" id="Device_Name" autocomplete="off" placeholder="设备名称" />
                        </div>
                        <div class="layui-inline">
                            <input class="layui-input " name="Device_IP" id="Device_IP" autocomplete="off" placeholder="设备IP" />
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                        </div>
                    </div>
                </div>

                <div class="layui-card-body">
                    <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                    <script type="text/html" id="toolbar_btns">
                        <div class="layui-btn-container">

                        </div>
                    </script>

                </div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmpldata">
        <div class="content-panel">
            <div class="layui-card min100">
                <div class="layui-card-header padding-15 pan-title">
                    <text>设备详情</text>
                    <button class="layui-btn layui-btn-sm" id="View">返回列表</button>
                </div>
                <div class="layui-card-body layui-detail">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">设备类型</label></div>
                        <div class="layui-col-xs6">
                            {{if Device_Category==1 }}
                            <label class="layui-badge layui-bg-blue">车牌识别相机</label>
                            {{else Device_Category==2}}
                            <label class="layui-badge layui-bg-green">道闸</label>
                            {{else Device_Category==3}}
                            <label class="layui-badge layui-bg-cyan">显示屏</label>
                            {{else Device_Category==4}}
                            <label class="layui-badge layui-bg-orange">自助停车设备</label>
                            {{/if}}
                        </div>
                    </div>
                    {{if Device_Category==1||Device_Category==3||Device_Category==4}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">所属通道</label></div>
                        <div class="layui-col-xs6"><label class="value">${Passway_Name}</label></div>
                    </div>
                    {{/if}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">设备编码</label></div>
                        <div class="layui-col-xs6"><label class="value">${Device_No}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">设备名称</label></div>
                        <div class="layui-col-xs6"><label class="value">${Device_Name}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">设备型号</label></div>
                        <div class="layui-col-xs6"><label class="value">${Drive_Name}</label></div>
                    </div>
                    {{if Device_Category==1||Device_Category==4||(Device_Category==3 && Device_FNo==0)}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">设备IP</label></div>
                        <div class="layui-col-xs6"><label class="value">${Device_IP}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">设备端口号</label></div>
                        <div class="layui-col-xs6"><label class="value">${Device_Port}</label></div>
                    </div>
                    {{/if}}
                    {{if Device_Category==1}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">登录账号</label></div>
                        <div class="layui-col-xs6"><label class="value">${Device_Account}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">登录密码</label></div>
                        <div class="layui-col-xs6"><label class="value">${(Device_Pwd!=null&&Device_Pwd!='')?"***":""}</label></div>
                    </div>
                    {{/if}}
                    {{if Device_Category==3}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">是否启用红绿灯</label></div>
                        <div class="layui-col-xs6">
                            {{if Device_IsRedLight==0}}
                            <label class="layui-badge">禁用</label>
                            {{else Device_IsRedLight==1}}
                            <label class="layui-badge">启用</label>
                            {{/if}}
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">是否显示二维码</label></div>
                        <div class="layui-col-xs6">
                            {{if Device_IsRedLight==0}}
                            <label class="layui-badge">不显示</label>
                            {{else Device_IsRedLight==1}}
                            <label class="layui-badge">显示</label>
                            {{/if}}
                        </div>
                    </div>
                    {{/if}}

                    <hr />
                    <div class="layui-row">
                        <div class="layui-col-xs3">&nbsp;</div>
                        <div class="layui-col-xs8">
                            <button class="layui-btn layui-btn-sm layui-hide" id="Update" data-no="${Device_No}"><i class="fa fa-edit"></i>编辑</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red layui-hide" id="Delete" data-no="${Device_No}"><i class="fa fa-trash-o"></i>删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <script type="text/html" id="Category">
        {{# if(d.Device_Category==1){ }}
        <span class="layui-badge layui-bg-blue ">车牌识别相机</span>
        {{# }else if(d.Device_Category==2){ }}
        <span class="layui-badge layui-bg-blue ">道闸</span>
        {{# }else if(d.Device_Category==3){ }}
        <span class="layui-badge layui-bg-cyan ">显示屏</span>
        {{# }else if(d.Device_Category==4){ }}
        <span class="layui-badge layui-bg-orange ">自助停车设备</span>
        {{# }else if(d.Device_Category==5){ }}
        <span class="layui-badge layui-bg-green ">电子缴费机</span>
        {{# } }}
    </script>
    <script type="text/html" id="Online">
        {{# if(d.Device_Online==1){ }}
        <span class="layui-badge layui-bg-green ">在线</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-red ">离线</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="Btns">
        <a href="javascript:onOpenDetailFrame('{{d.Device_No}}')" class="a-name" title="{{d.Device_Name}}">查看</a>
    </script>
    <script type="text/x-jquery-tmpl" id="IP">
        <a href="http://{{d.Device_IP}}:{{d.Device_Port}}" target="_blank" class="a-name" title="{{d.Device_IP}}">{{d.Device_IP}}</a>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();
        var comtable = null;
        var layuiForm = null;
        layui.use(['table', 'form'], function () {

            var admin = layui.admin;
            var table = layui.table;
            layuiForm = layui.form;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                , { field: 'Device_No', title: '设备编码', hide: true }
                , { field: 'Device_Name', title: '设备名称' }
                , { field: 'Device_Category', title: '设备类型', toolbar: "#Category" }
                , { field: 'Device_IP', title: '设备IP' }
                , { field: 'Device_DriveNo', title: '型号编码', hide: true }
                , { field: 'Drive_Name', title: '设备型号' }
                , { field: 'Device_Online', title: '设备状态', toolbar: "#Online" }
                , { field: 'Device_PasswayNo', title: '通道编码', hide: true }
                , { field: 'Passway_Name', title: '所属通道' }
                , { field: 'Device_ScreenNum', title: '视频排序' }
                , { field: 'Btns', title: '操作', toolbar: "#Btns" }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Device/GetDeviceList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data);
                    onGetOnlineStatus(data);
                }
            });

            pager.init();
        });

        var onGetOnlineStatus = function (data) {
            var DeviceNoList = [];
            data.data.forEach(function (item, index) { DeviceNoList[DeviceNoList.length] = item.Device_No });
            $.post("GetDeviceOnlineStatus", { DeviceNoList: JSON.stringify(DeviceNoList) }, function (json) {
                if (json.success && json.data != null) {
                    try {
                        var $table = $(".layui-table-body table");
                        var $trs = $table.find("tr");
                        var onlines = json.data;
                        for (var i = 0; i < $trs.length; i++) {
                            var deviceNo = $($trs[i]).find('[data-field="Device_No"] div').text();
                            var divOnline = $($trs[i]).find('[data-field="Device_Online"] div');
                            var online = 0;
                            onlines.forEach(function (d, e) { if (d.deviceno == deviceNo && d.online == 1) { online = 1; } });

                            if (online == 1) $(divOnline).html('<span class="layui-badge layui-bg-green ">在线</span>');
                            else $(divOnline).html('<span class="layui-badge layui-bg-red ">离线</span>');
                        }
                    } catch (e) {
                        console.log(e)
                    }
                }
            }, "json");
        }

        //右侧新增按钮
        var onOpenFrame = function () {
            layer.open({
                type: 2, id: 1,
                title: "新增设备",
                content: "Edit?Act=Add",
                area: getIframeArea(["900px", "95%"]),
                maxmin: false
            });
            parent.top.setScrollTop(document.body, 0);
        }

        var onOpenDetailFrame = function (itemno) {
            var item = null;

            pager.devices.forEach(function (d, i) {
                if (d.Device_No == itemno) {
                    item = d;
                }
            });
            var data = [item];

            $("#framePanl .framDetail").html($("#tmpldata").tmpl(data));

            $("#framePanl .framList").removeClass("layui-hide").addClass("layui-hide");
            $("#framePanl .framDetail").removeClass("layui-hide");

            pager.bindPower();
            pager.bindEvent();
        }

        var onOpenListFrame = function () {
            $("#Search").click();
            $("#framePanl .framDetail").removeClass("layui-hide").addClass("layui-hide");
            $("#framePanl .framList").removeClass("layui-hide");
        }

        var getFName = function (Device_FNo) {
            var fname = "不关联主设备";
            pager.devices.forEach(function (d, i) {
                if (d.Device_No == Device_FNo) {
                    fname = d.Device_Name;
                }
            });
            return fname;
        }

        var pager = {
            passways: null, //数据已解析为前段展示格式
            devices: null, //数据已解析为前段展示格式
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("GetAllPasswayList", {}, function (json) {
                    if (json.success) {
                        $("#Device_PasswayNo").append($("#tmplpassway").tmpl(json.data))
                        layuiForm.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("GetAllDriveList", {}, function (json) {
                    if (json.success) {
                        var sdata = [];
                        json.data.forEach(function (d, i) { if ("1,4".indexOf(d.Drive_Category) >= 0) sdata[sdata.length] = d; });
                        $("#Device_DriveNo").append($("#tmpldrive").tmpl(sdata))
                        layuiForm.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");
            },
            //passwayNo 右侧显示的通道编码，为null则显示第一个通道
            bindData: function (deviceNo) {
                layer.closeAll();
                var that = this;
                $.post("GetAllDevices", {}, function (json) {
                    if (json.success) {
                        that.passways = json.data.passways;
                        that.devices = json.data.devices;

                        area.init(deviceNo);
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindDataList: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Device/GetDeviceList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower["Add"]) {
                        $("#addpanl").removeClass("layui-hide")
                    } else {
                        $("#addpanl").removeClass("layui-hide").addClass("layui-hide")
                    }
                });
            },
            bindEvent: function () {
                $("#Search").unbind("click").click(function () {
                    pager.bindDataList(1);
                });

                $("#View").unbind("click").click(function () {
                    onOpenListFrame();
                });

                $("#Update").unbind("click").click(function () {
                    var paramNo = $(this).attr("data-no");
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑设备",
                        content: "Edit?Act=Update&Device_No=" + paramNo,
                        area: getIframeArea(["900px", "95%"]),
                        maxmin: false
                    });
                    parent.top.setScrollTop(document.body, 0);
                })

                $("#Delete").unbind("click").click(function () {
                    var paramNo = $(this).attr("data-no");
                    $("#Update,#Delete").attr("disabled", true);
                    LAYER_OPEN_TYPE_0("若设备正在识别车辆通行，删除后会导致<br />识别失败、缴费后不开闸等等异常错误！<br />确定删除设备?", res => {
                        LAYER_LOADING("处理中...");
                        $.getJSON("DelDevice", { Device_No: paramNo }, function (json) {
                            if (json.success) {
                                layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                    window.pager.bindData();
                                });
                            }
                            else {
                                $("#Update,#Delete").removeAttr("disabled")
                                layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                            }
                        });
                    }, res => {
                        $("#Update,#Delete").removeAttr("disabled")
                    })
                    parent.top.setScrollTop(document.body, 0);
                })
            }
        }

        var area = {
            action: 0,//当前操作：0-编辑，1-新增
            cur: null,//当前显示的停车区域信息
            init: function (deviceNo) {
                area.onload(deviceNo)
            },
            //加载左侧车场名称
            onpark: function (parkData) {
                $(".park-name").text(parkData.Parking_Name);
            },
            //加载左侧列表数据
            onload: function (deviceNo) {
                var catecsstype = "";
                var htm = '';
                pager.passways.forEach(function (item, index) {
                    var num = 0;
                    var items = '';
                    console.log(pager.devices)
                    pager.devices.forEach(function (camera, idx) {
                        if (camera.Device_PasswayNo == item.Passway_No) {
                            var ioHtml = "";
                            catecsstype = camera.Device_Category == 1 ? "type2" : "type3";
                            if (camera.Device_Category == 1) {
                                if (camera.Device_IO == 1) ioHtml = " <span class='layui-badge layui-bg-green'>主</span>";
                                else ioHtml = " <span class='layui-badge layui-bg-orange'>辅</span>";
                            }
                            items += '<ul class="menu-item"><li>';
                            items += '<div class="menu-text level2 ' + catecsstype + '" onclick="area.openAreaItem(this)" data-no="' + camera.Device_No + '"> ' + camera.Device_Name + ioHtml + '</div>';
                            items += '</li></ul>';
                            num++;
                        }
                    });
                    htm += '<div class="menu-title m-active level1 type1">' + item.Passway_Name + (num > 0 ? (' (' + num + ')') : '') + '</div>';
                    htm += '<div class="menu-items">';
                    htm += items;
                    htm += '</div>';
                });

                $(".menu-list").html(htm);

                var menus = $(".menu-list").find(".menu-text")
                if (menus != null && menus.length > 0) {
                    if (deviceNo == null) {
                        onOpenListFrame();
                    } else {
                        for (var i = 0; i < menus.length; i++) {
                            if ($(menus[i]).attr("data-no") == deviceNo) {
                                area.openAreaItem($(menus[i]));
                                break;
                            }
                        }
                    }
                } else {
                    onOpenListFrame();
                }

                $(".menu-title").click(function () {
                    var next = $(this).next();
                    if (next.hasClass("layui-hide"))
                        next.removeClass("layui-hide");
                    else
                        next.addClass("layui-hide");
                });
            },
            //点击左侧列表打开详情窗口，并加载数据
            openAreaItem: function (e) {
                console.log(e)
                var itemno = $(e).attr("data-no");
                $(".menu-text").removeClass("m-active-1")
                $(e).removeClass("m-active-1").addClass("m-active-1")

                onOpenDetailFrame(itemno);
            }
        }
    </script>
</body>
</html>
