﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>新增/编辑访客车辆</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-card { box-shadow: none; }
        a:hover { text-decoration:none; }
        .layui-row{margin-bottom:10px;}
        .edit-label{padding-left:10px;}
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="layui-card layui-form">
        <div class="layui-card-body" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">预约入场时间</label></div>
                <div class="layui-col-xs7">
                    <input class="layui-input v-null v-datetime v-submit" id="Reserve_StartTime" name="Reserve_StartTime" value="@Html.Raw(DateTime.Now.AddMinutes(30).ToString("yyyy-MM-dd HH:mm:00"))" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">预约出场时间</label></div>
                <div class="layui-col-xs7">
                    <input class="layui-input v-null v-datetime v-submit" id="Reserve_EndTime" name="Reserve_EndTime" value="@Html.Raw(DateTime.Now.AddHours(1.5).ToString("yyyy-MM-dd HH:mm:00"))" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">车牌号</label></div>
                <div class="layui-col-xs7">
                    <input class="layui-input v-null v-carno v-submit" id="Reserve_CarNo" name="Reserve_CarNo" maxlength="8" autocomplete="off" value="@ViewBag.CarPrefix" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">手机号</label></div>
                <div class="layui-col-xs7">
                    <input class="layui-input v-phone" id="Reserve_Phone" name="Reserve_Phone" value="" maxlength="11" autocomplete="off" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3"><label class="edit-label">备注</label></div>
                <div class="layui-col-xs7">
                    <input class="layui-input" id="Reserve_Remark" name="Reserve_Remark" value="" maxlength="255" />
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <div class="layui-row">
                <div class="layui-col-xs3"><label>&nbsp;</label></div>
                <div class="layui-col-xs7">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
        <div class="layui-card-body">
            <blockquote class="layui-col-xs10 layui-elem-quote" style="font-size:13px;">
                1、访客车在预约时间内入场按访客车正常收费（需要添加访客车计费规则，未添加计费规则就不会计费）；<br />
                2、访客车在预约时间外入场按车道设置的默认类型收费。
            </blockquote>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.visible = true;
        myVerify.init();

        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("No");

        s_carno_picker.init("Reserve_CarNo", function (text, carno) {
            $("#Reserve_CarNo").val(carno.join(''))
        }, "web").bindkeyup();

        layui.use(['form', 'element'], function () {
            pager.init()
        });


        var pager = {
            model: null,
            passwayList: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {               
                _DATE.bind(layui.laydate, ["Reserve_StartTime", "Reserve_EndTime"], { type: "datetime", range: true });
            },
            bindData: function () {
                if (paramAct == "Update") {
                    layer.msg("正在读取数据...", { icon: 16, time: 0 });
                    $.post("/Reserve/GetReserve", { Reserve_No: paramNo }, function (json) {
                        layer.closeAll();
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data, function (data) { });
                        }
                    }, "json");
                }
            },
            bindEvent: function () {
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Reserve_Phone = $("#Reserve_Phone").val();
                        data.Reserve_Remark = $("#Reserve_Remark").val();
                        return data;
                    });
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);

                    if (paramAct == "Add") {
                        $.post("/Reserve/AddReserve", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                var msg = json.msg;
                                var isReg = json.data;
                                layer.msg(msg, { time: isReg ? 1500 : 2500, icon: 1 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.msg, { time: 1500, icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        }, "json");
                    } else if (paramAct == "Update") {
                        param.Reserve_No = paramNo;
                        $.post("/Reserve/UpdateReserve", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                var msg = json.msg;
                                var isReg = json.data;
                                layer.msg(msg, { time: isReg ? 1500 : 2500, icon: 1 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.msg, { time: 1500, icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        }, "json");
                    }
                });

                $("#Cancel").click(function () { parent.layer.closeAll(); });

            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });
            }
        };
    </script>
</body>
</html>
