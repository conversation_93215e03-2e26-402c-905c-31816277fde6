﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>数据比对</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }

        .carmsg, .ownermsg, .msg { right: 100px; position: fixed; }
        cite { color: #c71313 !important; }

        .layui-table tr { height: 45px !important; line-height: 45px !important; padding: 0 10px; transition: box-shadow 0.2s ease; }
        .layui-table tr:hover { box-shadow: 0 5px 15px #d8cccc; font-weight: 900 !important; }
        thead > tr { background-color: #01aaed !important; color: #fff !important; }
        .layui-this { border: 1px solid #cad5cb; background-color: #01aaed; color: #fff !important; }
        .businessCache { position: fixed; right: 15px; top: 60px; padding: 2px 10px; background-image: linear-gradient(to right, #7dc7e3, #c8cbe5); z-index: 9999; color: red; /* border-radius: 5px 0px 0px 5px; */ box-shadow: 0 5px 15px #d8cccc; }
        .layui-table-header { width: 100%; }
        .layui-table-header table,
        .layui-table-header thead,
        .layui-table-header tr { width: 100% !important; }
        th > .layui-table-cell { width: 100% !important; }

        html { background-color: #e9f2f3; color: #666; height: 100%; }
        body{background-color: cadetblue; }
        li { color: #fff; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>若无输入条件，查询缓存与数据库存在差异的数据；若输入查询条件，则直接查询缓存和数据库的数据</cite></a>
                <div class="businessCache">@(ViewBag.BusinessCache == 1 ? "岗亭已启用业务缓存" : "岗亭已禁用业务缓存")</div>
            </div>
        </div>

        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="type1 layui-this">车辆信息</li>
                <li class="type3 simple">车主信息</li>
                <li class="type3 simple">访客车信息</li>
                <li class="type3 simple">商家车信息</li>
                <li class="type3 simple">黑名单信息</li>
            </ul>
            <div class="layui-tab-content layui-form">
                @* //车辆信息 *@
                <div class="layui-tab-item layui-show">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header  layui-form">
                                    <div class="test-table-reload-btn" id="searchForm">
                                        <div class="layui-inline">
                                            <input class="layui-input" name="Car_CarNo" id="Car_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="ClearCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除车辆信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="LoadCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载车辆信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <div class="carmsg"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-card-body">
                                    <table class="layui-hide" id="com-table-car" lay-filter="com-table-car"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @* //车主信息 *@
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header  layui-form">
                                    <div class="test-table-reload-btn" id="searchForm_owner">
                                        <div class="layui-inline">
                                            <input class="layui-input" name="Owner_Space" id="Owner_Space" autocomplete="off" placeholder="系统车位号" value="" />
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="Search_Owner"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="ClearOwner"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除车主信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="LoadOwner"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载车主信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <div class="ownermsg"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-card-body">
                                    <table class="layui-hide" id="com-table-owner" lay-filter="com-table-owner"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @* //访客车信息 *@
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header  layui-form">
                                    <div class="test-table-reload-btn" id="searchFormReserve">
                                        <div class="layui-inline">
                                            <input class="layui-input" name="Reserve_CarNo" id="Reserve_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="SearchReserve"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="ClearReserve"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除访客车信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="LoadReserve"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载访客车信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <div class="msg reservemsg"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-card-body">
                                    <table class="layui-hide" id="com-table-reserve" lay-filter="com-table-reserve"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @* //商家车信息 *@
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header  layui-form">
                                    <div class="test-table-reload-btn" id="searchFormBusinessCar">
                                        <div class="layui-inline">
                                            <input class="layui-input" name="BusinessCar_CarNo" id="BusinessCar_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="SearchBusinessCar"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="ClearBusinessCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除商家车信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="LoadBusinessCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载商家车信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <div class="msg businessCarmsg"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-card-body">
                                    <table class="layui-hide" id="com-table-businessCar" lay-filter="com-table-businessCar"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @* //黑名单信息 *@
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header  layui-form">
                                    <div class="test-table-reload-btn" id="searchFormBlackList">
                                        <div class="layui-inline">
                                            <input class="layui-input" name="BlackList_CarNo" id="BlackList_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="SearchBlackList"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="ClearBlackList"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除黑名单信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <button class="layui-btn" id="LoadBlackList"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载黑名单信息缓存</t></button>
                                        </div>
                                        <div class="layui-inline">
                                            <div class="msg blackListmsg"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="layui-card-body">
                                    <table class="layui-hide" id="com-table-blackList" lay-filter="com-table-blackList"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?20220830" asp-append-version="true"></script>

    <script>
        var Power = [];
        var comtable = null;
        var comtableOwner = null;
        var comtableReserve = null;
        var comtableBusinessCar = null;
        var comtableBlackList = null;

        layui.use(['table', 'element', 'form', 'laydate'], function () {

            var table = layui.table;

            //车辆信息
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            var cols = [[
                , { field: 'OldName', title: '缓存车牌号码' }
                , { field: 'OldBeginTime', title: '缓存开始时间' }
                , { field: 'OldEndTime', title: '缓存结束时间' }
                , { field: 'OldJson', title: '缓存Json' }
                , { field: 'NewName', title: '数据库车牌号码' }
                , { field: 'NewBeginTime', title: '数据库开始时间' }
                , { field: 'NewEndTime', title: '数据库结束时间' }
                , { field: 'NewJson', title: '数据库Json' }
                , {
                    field: '', title: '差异字段', templet: function (d) {
                        if (d.OldJson && d.NewJson && d.NewJson != "" && d.OldJson != "" && d.NewJson != "null" && d.OldJson != "null") {
                            var oldJson = JSON.parse(d.OldJson);
                            var newJson = JSON.parse(d.NewJson);
                            var oldKeys = Object.keys(oldJson);
                            var newKeys = Object.keys(newJson);
                            var diff = new Set();
                            for (var i = 0; i < oldKeys.length; i++) {
                                if (oldJson[oldKeys[i]] != newJson[oldKeys[i]]) {
                                    diff.add(oldKeys[i]);
                                }
                            }
                            for (var i = 0; i < newKeys.length; i++) {
                                if (oldJson[newKeys[i]] != newJson[newKeys[i]]) {
                                    diff.add(newKeys[i]);
                                }
                            }
                            return Array.from(diff).join(",");
                        } else {
                            return "";
                        }
                    }

                }

            ]];
            comtable = table.render({
                elem: '#com-table-car'
                , url: '/Cache/GetCarList'
                , method: 'post'
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".carmsg").html(d.msg);
                }
            });

            //车主信息
            var conditionParamOwner = { "Owner_Space": $("#Owner_Space").val() };
            var cols2 = [[
                , { field: 'OldName', title: '缓存车位号' }
                , { field: 'OldBeginTime', title: '缓存开始时间' }
                , { field: 'OldEndTime', title: '缓存结束时间' }
                , { field: 'OldJson', title: '缓存Json' }
                , { field: 'NewName', title: '数据库车位号' }
                , { field: 'NewBeginTime', title: '数据库开始时间' }
                , { field: 'NewEndTime', title: '数据库结束时间' }
                , { field: 'NewJson', title: '数据库Json' }
                , {
                    field: '', title: '差异字段', templet: function (d) {
                        if (d.OldJson && d.NewJson && d.NewJson != "" && d.OldJson != "" && d.NewJson != "null" && d.OldJson != "null") {
                            var oldJson = JSON.parse(d.OldJson);
                            var newJson = JSON.parse(d.NewJson);
                            var oldKeys = Object.keys(oldJson);
                            var newKeys = Object.keys(newJson);
                            var diff = new Set();
                            for (var i = 0; i < oldKeys.length; i++) {
                                if (oldJson[oldKeys[i]] != newJson[oldKeys[i]]) {
                                    diff.add(oldKeys[i]);
                                }
                            }
                            for (var i = 0; i < newKeys.length; i++) {
                                if (oldJson[newKeys[i]] != newJson[newKeys[i]]) {
                                    diff.add(newKeys[i]);
                                }
                            }
                            return Array.from(diff).join(",");
                        } else {
                            return "";
                        }
                    }
                }
            ]];
            comtableOwner = table.render({
                elem: '#com-table-owner'
                , method: 'post'
                , cols: cols2
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamOwner) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".ownermsg").html(d.msg);
                }
            });


            var cols3 = [[
                , { field: 'OldName', title: '缓存编码' }
                , { field: 'OldBeginTime', title: '缓存开始时间' }
                , { field: 'OldEndTime', title: '缓存结束时间' }
                , { field: 'OldJson', title: '缓存Json' }
                , { field: 'NewName', title: '数据库编码' }
                , { field: 'NewBeginTime', title: '数据库开始时间' }
                , { field: 'NewEndTime', title: '数据库结束时间' }
                , { field: 'NewJson', title: '数据库Json' }
                , {
                    field: '', title: '差异字段', templet: function (d) {
                        if (d.OldJson && d.NewJson && d.NewJson != "" && d.OldJson != "" && d.NewJson != "null" && d.OldJson != "null") {
                            var oldJson = JSON.parse(d.OldJson);
                            var newJson = JSON.parse(d.NewJson);
                            var oldKeys = Object.keys(oldJson);
                            var newKeys = Object.keys(newJson);
                            var diff = new Set();
                            for (var i = 0; i < oldKeys.length; i++) {
                                if (oldJson[oldKeys[i]] != newJson[oldKeys[i]]) {
                                    diff.add(oldKeys[i]);
                                }
                            }
                            for (var i = 0; i < newKeys.length; i++) {
                                if (oldJson[newKeys[i]] != newJson[newKeys[i]]) {
                                    diff.add(newKeys[i]);
                                }
                            }
                            return Array.from(diff).join(",");
                        } else {
                            return "";
                        }
                    }

                }

            ]];

            //访客车信息
            var conditionParamReserve = { "Reserve_CarNo": $("#Reserve_CarNo").val() };

            comtableReserve = table.render({
                elem: '#com-table-reserve'
                , method: 'post'
                , cols: cols3
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamReserve) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".reservemsg").html(d.msg);
                }
            });

            //商家车信息
            var conditionParamBusinessCar = { "BusinessCar_CarNo": $("#BusinessCar_CarNo").val() };

            comtableBusinessCar = table.render({
                elem: '#com-table-businessCar'
                , method: 'post'
                , cols: cols3
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamBusinessCar) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".businessCarmsg").html(d.msg);
                }
            });

            //黑名单信息
            var conditionParamBlackList = { "BlackList_CarNo": $("#BlackList_CarNo").val() };

            comtableBlackList = table.render({
                elem: '#com-table-blackList'
                , method: 'post'
                , cols: cols3
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamBlackList) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".blackListmsg").html(d.msg);
                }
            });

            pager.init();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layui.form.render();
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Cache/GetCarList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            //重新加载数据
            bindDataOwner: function (index) {
                layer.closeAll();
                comtableOwner.reload({
                    url: '/Cache/GetOwnerList'
                    , where: { conditionParam: JSON.stringify({ "Owner_Space": $("#Owner_Space").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindDataReserve: function (index) {
                layer.closeAll();
                comtableReserve.reload({
                    url: '/Cache/GetReserveList'
                    , where: { conditionParam: JSON.stringify({ "Reserve_CarNo": $("#Reserve_CarNo").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindDataBusinessCar: function (index) {
                layer.closeAll();
                comtableBusinessCar.reload({
                    url: '/Cache/GetBusinessCarList'
                    , where: { conditionParam: JSON.stringify({ "BusinessCar_CarNo": $("#BusinessCar_CarNo").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindDataBlackList: function (index) {
                layer.closeAll();
                comtableBlackList.reload({
                    url: '/Cache/GetBlackListList'
                    , where: { conditionParam: JSON.stringify({ "BlackList_CarNo": $("#BlackList_CarNo").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
                $("#Search_Owner").click(function () { pager.bindDataOwner(1); });
                $("#SearchReserve").click(function () { pager.bindDataReserve(1); });
                $("#SearchBusinessCar").click(function () { pager.bindDataBusinessCar(1); });
                $("#SearchBlackList").click(function () { pager.bindDataBlackList(1); });

                $("#ClearCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有车辆信息的缓存吗?（清理车辆缓存后，请手动清理车主缓存）",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearCar", {}, function (json) {
                                if (json.success) {

                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearOwner").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有车主信息的缓存吗?（清理车主缓存后，请手动清理车辆缓存）",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearOwner", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataOwner(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadOwner").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有车主信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadOwnerCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataOwner(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有车辆信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadCarCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearReserve").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有访客车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearReserve", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataReserve(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadReserve").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有访客车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadReserveCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataReserve(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearBusinessCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有商家车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearBusinessCar", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBusinessCar(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadBusinessCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有商家车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadBusinessCarCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBusinessCar(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearBlackList").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有黑名单信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearBlackList", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBlackList(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadBlackList").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有黑名单信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadBlackListCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBlackList(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
            },
            bindPower: function () {

            }
        }
    </script>
</body>
</html>
