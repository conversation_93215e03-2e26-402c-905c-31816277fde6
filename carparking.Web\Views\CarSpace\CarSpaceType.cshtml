﻿
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车位类型管理</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="CarSpaceType_CarCardTypeNo" name="CarSpaceType_CarCardTypeNo">
                                    <option value="">所有车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="CarSpaceType_CarTypeNo" name="CarSpaceType_CarTypeNo">
                                    <option value="">所有车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.CarSpace.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.CarSpace.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.CarSpace.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script>      
        var Power = window.parent.parent.global.formPower;
        var comtable = null;
    
        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'CarSpaceType_No', title: '车位类型名编码' }
                , { field: 'CarSpaceType_Name', title: '车位类型名' }
                , { field: 'CarCardType_Name', title: '车牌类型' }
                , { field: 'CarType_Name', title: '车牌颜色' }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CarSpace/GetCarSpaceTypeList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function () {

                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
               
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增车位类型",
                            content: 'CarSpaceTypeEdit?Act=Add',
                            area: getIframeArea(['600px', '400px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑车位类型",
                            content: 'CarSpaceTypeEdit?Act=Update&CarSpaceType_No=' + data[0].CarSpaceType_No,
                            area: getIframeArea(['600px', '400px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { CarSpaceType_No: data[0].CarSpaceType_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

                $.getJSON("/DropList/GetCarCardType", {}, function (json) {
                    if (json.Success) {
                        pager.carCardTypes = json.data;
                        $("#CarSpaceType_CarCardTypeNo").append($("#tmplcarcardtype").tmpl(json.Data))
                        layui.form.render("select");
                    }
                });
                $.getJSON("/DropList/GetCarType", {}, function (json) {
                    if (json.Success) {
                        pager.carTypes = json.data;
                        $("#CarSpaceType_CarTypeNo").append($("#tmplcartype").tmpl(json.Data))
                        layui.form.render("select");
                    }
                });           
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/CarSpace/GetCarSpaceTypeList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    pager.bindData(1);
                });

                $("#Search").click(function () {
                    pager.bindData(1);
                });
            }
        }
    </script>
</body>
</html>
