﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>自定义语音播报</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet">
    <style>
        .layui-form { margin: 20px; }
        .layui-form-label { width: 120px; padding: 9px 0; float: left; }
        .layui-input-block { margin-left: 120px; width: 300px; }
        .layui-form-item { margin-bottom: 20px; }
        .btn-container { margin-left: 120px; margin-top: 30px; }
        .layui-btn { font-weight: bolder; margin-right: 10px; font-size: 3.5vw; height: 15vh; line-height: 15vh; width: 17vw; border-radius: 5px; padding: 0 18px; background-color: #009688; color: #fff; white-space: nowrap; text-align: center; }
    </style>
</head>
<body>
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">播报内容&nbsp;</label>
            <div class="layui-input-block">
                <select name="broadContent" lay-verify="required" lay-filter="broadContent">
                    <option value="">请选择播报内容</option>
                </select>
            </div>
        </div>

        <div class="btn-container">
            <button type="button" class="layui-btn" id="btnBroad">播报</button>
            <button type="button" class="layui-btn layui-btn-primary" id="btnClose">关闭</button>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>
    <script>

        var paramPasswayno = decodeURIComponent($.getUrlParam("Passway_No"));

        layui.use(['form', 'layer'], function () {
            var form = layui.form;
            var layer = layui.layer;

            // 初始化加载播报内容
            $.ajax({
                url: '/Monitoring/GetBroadContentList',
                type: 'GET',
                success: function (res) {
                    if (res.success && res.data) {
                        var select = $('select[name=broadContent]');
                        res.data.forEach(function (item) {
                            select.append(new Option(item, item));
                        });
                        form.render('select');
                    }
                }
            });

            // 播报按钮点击事件
            $('#btnBroad').on('click', function () {
                var content = $('select[name=broadContent]').val();
                if (!content) {
                    layer.msg('请选择要播报的内容', { icon: 2 });
                    return;
                }

                //请求后端播报
                $.ajax({
                    url: '/Monitoring/BroadCustomVoice',
                    type: 'POST',
                    data: { content: content, passwayno: paramPasswayno },
                    success: function (res) {
                        if (res.success) {
                            layer.msg('播报成功', { icon: 1, time: 1000 });
                        } else {
                            if (res.msg) {
                                layer.msg(res.msg, { icon: 2 });
                            } else {
                                layer.msg("播报失败", { icon: 2 });
                            }
                        }
                    }
                });
            });

            // 关闭按钮点击事件
            $('#btnClose').on('click', function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
        });
    </script>
</body>
</html>
