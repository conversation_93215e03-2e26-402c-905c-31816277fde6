﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>停车支付</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .layui-anim-upbit { max-height: 200px !important; }
        span { color: black; line-height: 33px; }
        label { line-height: 15px; }
        .file { margin-right: 0px; }
        .alert { margin-bottom: 0px; }
        .layui-col-xs4 { padding-right: 0px; }
        .ibox-content { padding: 15px; }
        .layui-row { padding-bottom: 15px; }
        input.rmb { padding-right: 2rem; position: relative; }
        span.rmb { position: absolute; width: 2rem; right: 1px; top: 1px; height: calc(100% - 2px); cursor: pointer; line-height: 38px; text-align: center; background-color: #fff; color: #ff6a00; border-top-right-radius: 2px; border-bottom-right-radius: 2px; }
        span.rmb:hover { background-color: #d0d0d0; }
        span.rmb:active { background-color: #aaaaaa; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal layui-form">
            <div class="layui-col-xs5" id="verifyCheck">
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">入场时间：</span></div>
                    <div class="layui-col-xs7">
                        <input class="layui-input rmb v-null" id="ParkOrder_EnterTime" name="ParkOrder_EnterTime" readonly disabled />
                        <span class="rmb fa fa-edit" onclick="IsWriteEnterTime()"></span>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">车牌号码：</span></div>
                    <div class="layui-col-xs7">
                        <input type="text" placeholder="车牌号" class="layui-input v-null" id="ParkOrder_CarNo" name="ParkOrder_CarNo" maxlength="8">
                    </div>
                </div>
             
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">车牌类型：</span></div>
                    <div class="layui-col-xs7">
                        <select data-placeholder="车牌颜色" class="layui-input v-null" id="ParkOrder_CarCardType" name="ParkOrder_CarCardType">
                        </select>
                    </div>
                </div>
               
                <div class="layui-row" style="color:red;text-align:justify;background-color:#f2f2f2;padding:5px 10px;margin-bottom:15px;">
                    <p>1、若修改后的车牌号在场内已存在停车记录，将会提示【车牌号】已在场内。</p>
                    <p>2、若修改入场时间、车牌类型，将不会自动上传，请在【出入场记录】点击【上传云平台】按钮进行上传。</p>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs6 layui-col-xs-offset4">
                        <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> 保存</button>&nbsp;
                        <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> 取消</button>
                    </div>
                </div>
            </div>

            <div class="layui-col-xs7">
                <div class="file">
                    <a target="_blank">
                        <img id="ParkOrder_EnterImgPath" src="../../Static/img/nophoto.jpg" onerror="src='../../Static/img/nophoto.jpg'" style="width: 100%; max-height: 280px;" />
                    </a>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js//jquery.verify.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var now = new Date();
        layui.use(['form'], function () {
            pager.init();
        });

        var index = parent.layer.getFrameIndex(window.name);
        var ParkOrder_No = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var pager = {
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            if (d.CarCardType_Type != 5 && d.CarCardType_Type != 6) {
                                var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                                $("#ParkOrder_CarCardType").append(option);
                            }
                            layui.form.render("select");
                        });
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime"], { type: "datetime", max: now.Format("yyyy-MM-dd hh:mm:ss") })
            },
            //数据绑定
            bindData: function () {
                layer.msg('加载中...', { icon: 16, time: 0 });
                if (ParkOrder_No != null) {
                    $.ajax({
                        type: 'post',
                        url: 'GetParkOrderByNo',
                        dataType: 'json',
                        data: { ParkOrder_No: ParkOrder_No },
                        success: function (json) {
                            layer.closeAll();
                            if (json.success) {
                                var model = json.data.model;
                                $("#verifyCheck").fillForm(model, function (data) { });
                                layui.form.render("select");
                                if (model.ParkOrder_EnterImgPath != null && model.ParkOrder_EnterImgPath != "") {
                                    var imgsrc = PathCheck(decodeURIComponent(model.ParkOrder_EnterImgPath));
                                    $("#ParkOrder_EnterImgPath").attr("src", imgsrc);
                                    $("#ParkOrder_EnterImgPath").parent().attr("href", imgsrc);
                                }
                                $("#ParkOrder_EnterTime").html(model.ParkOrder_EnterTime);
                            } else {
                                layer.msg('加载失败：' + json.msg, { icon: 5 });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('订单ID无效', { icon: 0 });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.closeAll(); });
                $("#Save").click(function () {
                    //验证表单
                    if (!myVerify.check()) return;
                    if (new Date().getTime() - new Date($("#ParkOrder_EnterTime").val()).getTime() < 0)
                    {
                        layer.msg('入场时间不能大于当前时间');
                        return;
                    }
                    $("#Save").attr("disabled", true);
                    layer.msg('保存中...', { icon: 16, time: 0 });

                    var param = {
                        ParkOrder_No: ParkOrder_No
                        , ParkOrder_CarNo: $("#ParkOrder_CarNo").val()
                        , ParkOrder_CarCardType: $("#ParkOrder_CarCardType").val()
                        , ParkOrder_EnterTime: $("#ParkOrder_EnterTime").val()
                    };
                    $.post("UpdateCarNo", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { time: 1500 }, function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                                parent.layer.closeAll();
                            });
                        } else {
                            layer.msg(json.msg);
                            $("#Save").removeAttr("disabled");
                        }
                    }, "json");
                });
            },
            bindPower: function () {
                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                        $("#ParkOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        };

        var IsWriteEnterTime = function () {
            $("#ParkOrder_EnterTime").removeAttr("disabled");
            $("#ParkOrder_EnterTime").click();
        }
    </script>
</body>
</html>
