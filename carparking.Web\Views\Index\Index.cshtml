﻿@using carparking.BLL.Cache
<!DOCTYPE html>
<html>

<head>
    <script>
        var model = '@Html.Raw(ViewBag.model)';
        localStorage.setItem('sysconfig', model);
    </script>
    <meta charset="utf-8">
    <title>@ViewBag.SysConfig_DIYName</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="~/Static/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/Static/admin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="~/Static/admin/style/admin.css" media="all">
    <link href="~/Static/plugins/notification/snackbar/snackbar.min.css" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }

        .topHengfu { text-align: center; color: red; font-weight: bold; font-size: 1.7rem; }

        /*.layui-layout-admin .layui-logo { background-image: url(../../Static/img/index_top_bg2.gif); background-size: 100%; }*/
        .layui-layout-admin .layui-logo { color: #fff !important; font-weight: bold; letter-spacing: 2px; }

        .layui-side-menu .layui-nav .layui-nav-item a { user-select: none; }

        /*.layui-side-scroll { background-image: url(../../Static/img/index_top_bg2.gif); background-size: 100%; background-position:-20px bottom;background-repeat:no-repeat;}*/
        .layui-layout-admin .layui-header { background-color: #000 !important; }

        .layui-layout-admin .layui-header .layui-nav-item .layui-icon { color: #fff; }

        .layui-layout-admin .layui-header a,
        .layui-layout-admin .layui-header a cite { color: #fff; }

        .text-input { width: 80%; margin: 20px 10% 0; text-align: left; padding-left: 0; text-indent: 1rem; }

        .layui-side-menu,
        .layui-layout-admin .layui-header { background-color: #20222A !important; }


        .gradient-border { text-align: justify; cursor: pointer; margin-top: 13px; margin-right: 2rem; width: 60px; line-height: 25px; height: 25px; border: solid 1px transparent; border-radius: 4px; background-origin: border-box; background-clip: content-box, border-box; }

        .standardtxt { background-image: linear-gradient(to right, #7569d9, #5c69c7); }

        .simpletxt { background-image: linear-gradient(to right, #4dc397, #5cc7b5); }

        .warn { width: 25px; height: 25px; margin-right: 1rem; cursor: pointer; }

        .warn-dropdown { position: absolute; padding: 0; background-color: #20222A; display: none; width: 120px; left: -27px; font-size: 12px; box-shadow: 0 0 4px 0 #6973b3; border-radius: 6px 6px 6px 6px; z-index: 999; }

        .warn-dropdown dd { float: none !important; margin: 0 !important; height: 38px; cursor: pointer; }

        .warn-dropdown dd a { display: flex; height: 38px; align-items: center; padding-left: 20px; padding-right: 12px; box-sizing: border-box; color: rgba(255, 255, 255, .8) !important; white-space: nowrap; text-align: center; width: 100%; }

        .warn-dropdown dd a:hover { color: #20222A !important; }

        .icon-car-count { cursor: pointer; margin-top: 16px; box-sizing: border-box; text-align: center; position: absolute; left: 8px; top: -10px; background: red; color: #fff; border-radius: 999px; padding: 0 0.5em; min-width: 30px; height: 30px; line-height: 30px; font-size: 16px; transform: scale(.7); font-family: tahoma !important; }

        .icon-car-count:before { content: " "; position: absolute; left: 10px; top: 8px; opacity: .75; width: 0; height: 0; background-color: red; border-radius: 50%; box-shadow: 0 0 10px rgb(0 0 0 / 30%) inset; z-index: -1; }

        .icon-item-count { cursor: pointer; margin-top: 10px; box-sizing: border-box; text-align: center; position: absolute; right: 1px; top: -10px; background: #5c69c7; color: #fff; border-radius: 999px; padding: 0 1rem; min-width: 30px; height: 30px; line-height: 30px; font-size: 16px; transform: scale(.7); font-family: tahoma !important; }

        .layui-this .item-title { color: #fff !important; }

        .footer { background-color: #04345fdb; color: #fff; font-size: 1rem; }

        .fastmode { display: inline-block; font-size: 1.5rem; white-space: nowrap; }

        .fastmode ul { display: flex; padding: 0; margin: 0; list-style: none; }

        .fastmode li { flex: 1; margin: 0 5px; width: auto; height: 30px; line-height: 30px; }

        .fastmode li:hover { background-color: rgba(0, 0, 0, 0.05); color: #000; }

        .fastmode li a { display: inline-block; }

        .fastmode li a img { height: 1.4rem; }

        .fastmode li cite { height: 1.5rem; line-height: 1.5rem; text-align: left; display: inline-block; color: #fff; font-size: 1.5rem; }

        .fastmode li cite:hover { color: #0f9ee9; }

        .layui-side-menu .layui-nav .layui-nav-item a { height: 50px !important; line-height: 50px !important; padding-left: 45px; padding-right: 30px; font-size: 1.5rem !important; }

        .layui-side-menu .layui-nav .layui-nav-item a cite { font-size: 1.5rem !important; }

        .layui-side-menu .layui-nav .layui-nav-item .layui-icon { margin-top: -25px !important; }

        .headSearch { position: relative; width: 130px; background-color: rgb(213 205 205 / 10%); color: #ffffff; line-height: 38px; padding: 0 10px; font-size: 16px; border-radius: 4px; word-break: break-all; white-space: nowrap; word-wrap: normal; text-overflow: ellipsis; overflow: hidden; }

        .headSearch:focus { width: 200px; box-shadow: 0px 1px 0px rgb(193 184 184 / 60%); }

        .searchBox { position: absolute; top: 50px; /*  left: 220px; */ width: 500px; height: auto; background-color: #fff; z-index: 10000; box-shadow: 0 0 6px #bbb; padding: 20px; font-size: 1.6rem; }

        .searchBox .selectmenu { display: block; border: 1px solid #eee; border-radius: 4px; min-height: 200px; max-height: 400px; overflow: auto; width: 100%; height: 100%; }

        .searchBox .selectmenu ul { display: block; overflow: hidden; user-select: none; cursor: pointer; }

        .searchBox .selectmenu ul li { display: block; padding: 10px; color: #666; list-style: none; }

        .searchBox .history li { float: left; background-color: #f2f5f7; font-size: 14px; color: #666; border-radius: 4px; margin-right: 5px; }

        .searchBox .history { display: block; height: 22px; line-height: 22px; }

        .yunset { text-align: justify; cursor: pointer; margin-top: 13px; margin-right: 2rem; width: 80px; line-height: 25px; height: 25px; border-radius: 4px; background-origin: border-box; padding-left: 6px; color: #fff; background-color: #0F9EE9; }

        .yunset:hover { background-color: #fff; color: #0F9EE9; font-weight: 600; }

        .standardtxt:hover { background-color: #fff !important; background-image: none; color: #7569d9; font-weight: 600; }

        .simpletxt:hover { background-color: #fff !important; background-image: none; color: #4dc397; font-weight: 600; }

        #selectmenu li:hover { box-shadow: 1px 1px 1px rgb(193 184 184 / 60%); background-color: #ededed !important; }

        #selectmenu li:hover { box-shadow: 1px 1px 1px rgb(193 184 184 / 60%); background-color: #ededed !important; }

        .searchTip { width: 100%; height: 34px; font-size: 13px; display: block; line-height: 34px; color: #8d8d8d; }

        .side-button { position: fixed; right: 0; top: 50%; transform: translateY(-50%); width: 35px; height: 100px; background-color: #009688; color: white; text-align: center; cursor: pointer; z-index: 9999; writing-mode: vertical-rl; border-top-left-radius: 10px; border-bottom-left-radius: 10px; font-size: 16px; font-weight: bold; letter-spacing: 2px; line-height: 2.2; }

        .side-button:hover { background-color: #00796b; }

        #sys-direct { height: 100%; }

        #sys-direct img { width: 40px; height: 40px; }

        #sys-direct a img { width: 90px; height: 90px; }

        #sys-direct cite { height: 43px; line-height: 43px; text-align: center; display: block; color: #666; width: 100%; font-size: 18px; }

        .tooltip1 { position: relative; display: inline-block; }
        .tooltip1 .tooltiptext { visibility: hidden; background-color: #04345fdb; color: #fff; text-align: center; padding: 5px 10px; border-radius: 5px; bottom: -5px; left: -60px; z-index: 99999; position: absolute; transform: translateX(-50%); white-space: nowrap; opacity: 0; transition: opacity 0.3s; }
        .tooltip1:hover .tooltiptext { visibility: visible; opacity: 1; }
    </style>
</head>

<body class="layui-layout-body">
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="text" />
        <input type="password" />
        <input type="text" />
        <input type="password" />
    </div>
    <div class="side-button" id="sideButton">系 统 导 航</div>
    <div id="LAY_app">
        <div class="layui-layout layui-layout-admin">
            <div class="layui-header">
                <!-- 头部区域 -->
                <ul class="layui-nav layui-layout-left">
                    <li class="layui-nav-item layadmin-flexible" lay-unselect>
                        <a href="javascript:;" layadmin-event="flexible" title="隐藏/显示菜单栏">
                            <i class="layui-icon layui-icon-shrink-right" id="pro_frame_flexible"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;" layadmin-event="refresh" title="刷新">
                            <i class="layui-icon layui-icon-refresh-3"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <input id="iptSearch" type="text" placeholder="搜索菜单..." autocomplete="off"
                               class="layui-input-search headSearch" onclick="SearchBox.ShowSelectDiv(this)">
                    </li>

                </ul>
                <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right" id="access_info">
                    <li class="layui-nav-item">
                        <div id="UpdatePark" class="headDiv yunset layui-hide">云平台配置</div>
                    </li>
                    <li class="layui-nav-item">
                        <div class="gradient-border standardtxt"> <span class="versiontext">&nbsp;标 准 版</span> </div>
                        <dl class="layui-nav-child warn-dropdown">
                            <dd><a class="chkstandard">旗舰版</a></dd>
                            <dd><a class="chksimple">标准版</a></dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item">
                        <div>
                            <img class="warn" src="~/Static/img/lingdang.png" />
                            <div class="icon-car-count layui-hide"><span class="total">0</span></div>
                        </div>
                        <dl class="layui-nav-child warn-dropdown warnitem">
                            <dd>
                                <a class="warna">
                                    <span class="item-title">警报消息</span><span class="icon-item-count">
                                        <span class="one">0</span>
                                    </span>
                                </a>
                            </dd>
                            <dd>
                                <a class="warna">
                                    <span class="item-title">事件信息</span><span class="icon-item-count">
                                        <span class="two">0</span>
                                    </span>
                                </a>
                            </dd>
                        </dl>
                    </li>

                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;">
                            <cite>@Html.Raw(ViewBag.lgAdmins != null ? ViewBag.lgAdmins.Admins_Account : "")</cite>
                        </a>
                        <dl class="layui-nav-child warn-dropdown">
                            <dd><a class="changePwd">修改密码</a></dd>
                            <dd id="logout"><a>退出登录</a></dd>
                        </dl>
                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class="layui-side layui-side-menu">
                <div class="layui-side-scroll">
                    <div class="layui-logo" lay-href="/Index/HomeN">
                        <span>@ViewBag.SysConfig_DIYName</span>
                    </div>
                    <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="sideMenu">
                        <li data-name="system" class="layui-nav-item layui-nav-itemed">
                            <a href="javascript:;" lay-tips="车场配置" lay-direction="2">
                                <i class="layui-icon layui-icon-util"></i>
                                <cite>车场配置</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a data-search="kspz" lay-href="FastGuide/Index">快速配置</a></dd>
                                <dd><a data-search="gtgl" lay-href="SentryHost/Index">岗亭管理</a></dd>
                                <dd><a data-search="qygl" lay-href="ParkArea/Index">区域管理</a></dd>
                                <dd><a data-search="cdgl" lay-href="Passway/Index">车道管理</a></dd>
                                <dd><a data-search="sbgl" lay-href="Device/Index">设备管理</a></dd>
                                @*<dd><a lay-href="DeviceTalk/Index">值班中心</a></dd>*@
                            </dl>
                        </li>
                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="车场管理" lay-direction="2">
                                <i class="layui-icon layui-icon-app"></i>
                                <cite>车场管理</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a data-search="ccsz" lay-href="Policy/Index">车场设置</a></dd>
                                <dd><a data-search="cdjk" lay-href="LaneMonitor/Index">车道监控</a></dd>
                                <dd><a data-search="jfgz" lay-href="BillingRule/Index">计费规则</a></dd>
                                <dd><a data-search="czgz" lay-href="MonthRule/MonthCharge">充值规则</a></dd>
                                <dd><a data-search="txkz" lay-href="AccessAuth/Index">通行控制</a></dd>
                                <dd><a data-search="cplx" lay-href="CarCardType/Index">车牌类型</a></dd>
                                <dd><a data-search="cpys" lay-href="CarType/Index">车牌颜色</a></dd>
                                <dd><a data-search="whxx" lay-href="EndNumAuth/Index">尾号限行</a></dd>
                                @*<dd><a lay-href="BoardAuth/Index">控制板授权</a></dd>*@
                                <dd><a data-search="tscp" lay-href="SpecialCar/Index">特殊车辆</a></dd>
                                <dd><a data-search="rqsz" lay-href="DateSet/Index">日期设置</a></dd>
                                <dd><a data-search="cptccl" lay-href="SpecialPlate/Index">车牌特殊处理</a></dd>
                                <dd><a data-search="pxmb" lay-href="DisplayTemplate/Index">屏显模板</a></dd>
                                <dd><a data-search="csptsb" lay-href="CityServer/Index">城市平台上报</a></dd>
                            </dl>
                        </li>
                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="车辆管理" lay-direction="2">
                                <i class="layui-icon layui-icon-component"></i>
                                <cite>车辆管理</cite>
                            </a>
                            <dl class="layui-nav-child">
                                @* <dd><a lay-href="Car/Index">车辆登记</a></dd>*@
                                <dd><a data-search="cldj" lay-href="Owner/Index">车辆登记</a></dd>
                                <dd><a data-search="hmdgl" lay-href="BlackList/Index">黑名单管理</a></dd>
                                <dd><a data-search="fkcl" lay-href="Reserve/Index">访客车辆</a></dd>
                                <dd><a data-search="sjcl" lay-href="BusinessCar/Index">商家车辆</a></dd>
                            </dl>
                        </li>
                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="商家优惠" lay-direction="2">
                                <i class="layui-icon layui-icon-rmb"></i>
                                <cite>商家优惠</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a data-search="yhsz" lay-href="ParkDiscountSet/Index">优惠设置</a></dd>
                                <dd><a data-search="cpyh" lay-href="CarCoupon/Index">车牌优惠</a></dd>
                            </dl>
                        </li>
                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="记录查询" lay-direction="2">
                                <i class="layui-icon layui-icon-form"></i>
                                <cite>记录查询</cite>
                            </a>
                            <dl class="layui-nav-child">
                                @*<dd><a lay-href="ParkOrder/Index">停车收费</a></dd>*@
                                <dd><a data-search="crcjl" lay-href="InParkRecord/Index">出入场记录</a></dd>
                                <dd><a data-search="jfjl" lay-href="PayOrder/Index">缴费记录</a></dd>
                                <dd><a data-search="jfmx" lay-href="PayPart/Index">缴费明细</a></dd>
                                <dd><a data-search="szjl" lay-href="Ledger/Index">储值余额明细</a></dd>
                                @{
                                    if (carparking.Config.AppSettingConfig.SentryMode ==
                                    carparking.Common.VersionEnum.WindowsStandard)
                                    {
                                        <dd><a data-search="jfjl" lay-href="UnpaidRecord/Index">识别未支付</a></dd>
                                    }
                                }
                                <dd><a data-search="yhqjl" lay-href="CouponRecord/Index">优惠券记录</a></dd>
                                <dd><a data-search="chjl" lay-href="InParkCar/Index">场内记录</a></dd>
                                <dd><a data-search="cpsbjl" lay-href="CarRecog/Index">车牌识别记录</a></dd>
                                @*<dd><a lay-href="StoreRecord/Index">储值车扣费记录</a></dd>*@
                                <dd><a data-search="rgkzjl" lay-href="OpenGateRecord/Index">人工开闸记录</a></dd>
                                <dd><a data-search="kzfxjl" lay-href="AbnorOrder/Index">开闸放行记录</a></dd>
                                <dd><a data-search="jbjl" lay-href="WorkShift/Index">交班记录</a></dd>
                                <dd><a data-search="sjgl" lay-href="ControlEvent/Index">事件管理</a></dd>
                                <dd><a data-search="dcjl" lay-href="BackCar/Index">倒车记录</a></dd>
                                <dd><a data-search="jkmjl" lay-href="HealthCodeResult/Index">健康码记录</a></dd>
                                <dd><a data-search="clzxjl" lay-href="CarUnbound/Index">车辆注销记录</a></dd>
                                <dd><a data-search="bmdjl" lay-href="WhiteRecord/Index">黑白名单</a></dd>
                              
                            </dl>
                        </li>
                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="汇总报表" lay-direction="2">
                                <i class="layui-icon layui-icon-chart"></i>
                                <cite>汇总报表</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a data-search="ccrbb" lay-href="RptDay/Index">车场日报表</a></dd>
                                <dd><a data-search="ccybb" lay-href="RptMonth/Index">车场月报表</a></dd>
                                <dd><a data-search="clltj" lay-href="RptTraffic/Index">车流量统计</a></dd>
                                <dd><a data-search="lscsftj" lay-href="RptTempPayment/Index">临时车收费统计</a></dd>
                                <dd><a data-search="yzccztj" lay-href="RptCarMonth/Index">月租车充值统计</a></dd>
                                <dd><a data-search="czccctj" lay-href="RptCarStore/Index">储值车充值统计</a></dd>
                                <dd><a data-search="cwxqtj" lay-href="RptSpace/Index">车位续期统计</a></dd>
                                @*<dd><a lay-href="RptYear/Index">车场年报表</a></dd>*@
                            </dl>
                        </li>

                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="系统管理" lay-direction="2">
                                <i class="layui-icon layui-icon-set"></i>
                                <cite>系统管理</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a data-search="zhgl" lay-href="Admins/Index">账号管理</a></dd>
                                <dd><a data-search="qxgl" lay-href="PowerGroup/Index">权限管理</a></dd>
                                <dd><a data-search="czrz" lay-href="UserLogs/Index">操作日志</a></dd>
                                <dd><a data-search="xtrz" lay-href="SystemLogs/Index">系统日志</a></dd>
                                <dd><a data-search="kfjkrz" lay-href="OpenApiV2Logs/Index">开放接口日志</a></dd>
                                <dd><a data-search="xtsz" lay-href="SystemSetting/Index">系统设置</a></dd>
                                @if (carparking.Config.AppSettingConfig.SentryMode ==
                                carparking.Common.VersionEnum.CloudServer)
                                {
                                    <dd><a data-search="sjff" lay-href="SendHandle/Index">数据分发</a></dd>
                                    <dd><a data-search="sjjs" lay-href="ReceiveHandle/Index">数据接收</a></dd>
                                }
                                <dd><a data-search="yccjl" lay-href="CloudRecord/Index">云车场记录</a></dd>
                                <dd><a data-search="zffs" lay-href="PayType/Index">支付方式</a></dd>
                                <dd><a data-search="xtbj" lay-href="Syswarn/Index">系统报警</a></dd>
                                <!--无感支付日志-->
                                <dd><a data-search="wgzfrz" lay-href="NoSensePayLogs/Index">无感支付日志</a></dd>
                                <dd><a data-search="rwgl" lay-href="Schedule/Index">任务管理</a></dd>

                            </dl>
                        </li>

                        <li data-name="system" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="维护管理" lay-direction="2">
                                <i class="layui-icon layui-icon-set"></i>
                                <cite>维护管理</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a data-search="sjsj" lay-href="Tools/Index">数据升级</a></dd>
                                @if (!AppBasicCache.IsWindows)
                                {
                                    <dd><a data-search="sjbf" lay-href="DataBackup/Index">数据备份</a></dd>
                                }
                                <dd><a data-search="sbss" lay-href="DeviceSearch/Index">设备搜索</a></dd>
                            </dl>
                        </li>

                    </ul>
                </div>
                <div style="width: 100%;height: 30px;position: absolute;bottom: 0;" id="toolDiv"></div>
                @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.WindowsStandard)
                {
                    <div style="width: 100%;height: 20px;position: absolute;bottom: 0;opacity: .5;">
                        <span style="float:left;margin-left:20px;color:#fff;" id="time"></span>
                    </div>
                }
            </div>

            <!-- 页面标签 -->
            <div class="layadmin-pagetabs" id="pro_frame_tabs">
                <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-down">
                    <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
                        <li class="layui-nav-item" lay-unselect>
                            <a href="javascript:;"></a>
                            <dl class="layui-nav-child layui-anim-fadein">
                                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
                    <ul class="layui-tab-title" id="pro_frame_tabsheader">
                        <li lay-id="/Index/HomeN" lay-attr="/Index/HomeN" class="layui-this">
                            <i class="layui-icon layui-icon-home"></i>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- BODY -->
            <div class="layui-body" id="pro_frame_body">
                <div class="layadmin-tabsbody-item layui-show">
                    <iframe src="/Index/HomeN" name="/Index/HomeN" frameborder="0" class="layadmin-iframe"></iframe>
                </div>
            </div>

            <!--Footer-->
            <div class="footer">
                <div style="width:69%;overflow: hidden;white-space: nowrap;position: absolute;left: 0;">
                    <div class="fastmode">
                        <ul>
                            <li class="layui-col-md2">
                                <span class="layui-badge layui-bg-blue"
                                      title="@ViewBag.DBIP - @ViewBag.DBName">数据库：@ViewBag.DBIP - @ViewBag.DBName</span>
                            </li>
                            <li class="layui-col-md2">
                                @if (carparking.Config.AppSettingConfig.SentryMode != "2")
                                {
                                    <div class="headDiv parkstatus"></div>
                                }
                                else
                                {
                                    <div class="headDiv cloudboxlpremergency">
                                        <span>应急模式：</span><span id="SysConfig_CloudBoxLprEmergency">
                                            <span class="layui-orange">未知</span>
                                        </span>
                                        <div class="headDiv changemode">
                                            <img src="/Static/img/icon/icon_bar_refash.png"
                                                 style="width: 1.2rem;">
                                        </div>
                                    </div>
                                    <div class="headDiv" style="width:.5rem;"></div>
                                    <div class="headDiv connMode">
                                        <span>车场模式：</span><span id="SysConfig_ConnMode">
                                            <span class="layui-orange">未知</span>
                                        </span>
                                    </div>
                                    <div class="headDiv" style="width:.5rem;"></div>
                                }
                            </li>

                            @if (carparking.Config.AppSettingConfig.SentryMode != "2")
                            {
                                @if (carparking.BLL.Cache.AppBasicCache.IsWindows)
                                {
                                    <li class="layui-col-md2">
                                        <div class="headDiv dogstatus"></div>
                                    </li>
                                }
                            }

                            <li class="layui-col-md2 hide">
                                <div class="topHengfu">测试平台</div>
                            </li>
                        </ul>
                    </div>
                </div>

                <div style="width:300px;overflow: hidden;white-space: nowrap;position: absolute;right: 0;text-align: right;">
                    <text class="version" id="versionText">
                        &copy; @Html.Raw("V" +
                        ViewBag.ApiVersion)@Html.Raw(ViewBag.ApiVersion_FB != "" ? "." + ViewBag.ApiVersion_FB :
                        "")
                    </text>
                </div>
            </div>
            <div class="layadmin-body-shade" layadmin-event="shade"></div>
        </div>
    </div>
    <div>
        <!--功能扩展-自动弹出登录，由全局ajax控制-->
        <div id="modal-form" class="modal fade in" aria-hidden="true" data-backdrop="static" style="margin-top: 12%;">
            <div class="modal-dialog" style="width: 300px;">
                <div class="modal-content" style="height: 230px;">
                    <div class="modal-body">
                        <button type="button" class="close hide" data-dismiss="modal">
                            <span aria-hidden="true">×</span><span class="sr-only">Close</span>
                        </button>
                        <div class="row">
                            <div style="width: 100%; height: 100%;">
                                <h5 class="m-t-none m-b text-center" style="color: #1ab394">登录超时,请重新登录！</h5>
                                <div class="layui-row">
                                    <input type="text" id="username" placeholder=" 请输入用户名"
                                           class="layui-input text-input" disabled />
                                </div>
                                <div class="layui-row">
                                    <input type="password" id="password" placeholder=" 请输入密码"
                                           class="layui-input text-input" />
                                </div>
                                <div class="layui-row">
                                    <button type="button" id="btn_Login"
                                            style="width: 80%; margin:20px 10% 0;color:#fff !important;"
                                            data-loading-text="<div style='text-align:center; margin:0 auto;'><div style='display:inline-block;width:45px;'>登录中</div><div class='sk-spinner sk-spinner-three-bounce' style='display:inline-block;width:45px;'><div class='sk-bounce1'></div><div class='sk-bounce2'></div><div class='sk-bounce3'></div></div></div>"
                                            class="layui-btn layui-btn-primary">
                                        登&nbsp;&nbsp;录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <a id="LoginForm" data-toggle="modal" class="btn btn-primary hide" href="#modal-form" data-iframe="/Index/Home"
           data-reload=""></a>
    </div>

    <div class="searchBox tdselect-div" style="display: none;">
        <div class="history">
            <label style="font-size: 12px;float: left;">历史搜索：</label>
            <ul id="history">
                <li>
                    <a lay-href="FastGuide/Index">
                        <cite>
                            <t>快速配置</t>
                        </cite>
                    </a>
                </li>
            </ul>
        </div>
        <div class="selectmenu">
            <ul id="selectmenu">
                <li>
                    <a lay-href="FastGuide/Index">
                        <cite>
                            <t>快速配置</t>
                        </cite>
                    </a>
                </li>
            </ul>
        </div>
        <div class="searchTip"><label>提示：仅支持一级菜单项搜索</label></div>
        <div style="padding:10px 0; text-align:center;">
            <button class="layui-btn layui-btn-md" style="background-color: #26817f;" id="closeparkHistory">关闭</button>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="park_online">
        {{if online==1 }}
        <span class="layui-badge layui-bg-blue" title="已连接云平台">云平台：已连接</span>
        {{else online==2 }}
        <span class="layui-badge layui-bg-gray" title="未启用云平台">云平台：未启用</span>
        {{else online==-1 }}
        <span class="layui-badge layui-bg-gray" title="未知">云平台：未知</span>
        {{else}}
        <span class="layui-badge layui-bg-orange" title="未连接云平台">云平台：未连接</span>
        {{/if}}
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/notification/snackbar/snackbar.min.js"></script>
    <script src="~/Static/js/jsencrypt.min.js"></script>
    <script>
        var parkmode = '@ViewBag.SentryMode';
        var versionMode = '@Html.Raw(carparking.Config.AppSettingConfig.SentryMode)';
        var parkIndex = '';
        // 添加实时日期和时间
        function updateTime() {
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            var hours = now.getHours();
            var minutes = now.getMinutes();
            var seconds = now.getSeconds();
            var dateString = year + '-' + month + '-' + day;
            var timeString = hours + ':' + minutes + ':' + seconds;
            document.getElementById('time').innerHTML = dateString + " " + timeString;
        }
        if (versionMode != "0") {
            setInterval(updateTime, 1000); // 每秒钟更新一次时间
        }

        var u = '@Html.Raw(ViewBag.lgAdmins != null ? ViewBag.lgAdmins.Admins_Account : "")';
        localData.set("Admins_Account", encodeURIComponent(u));
        $("#username").val(u);

        $(function () {
            if (location.href.indexOf(".szymzh.com") > 0 || location.href.indexOf("localhost") > 0)
                $(".topHengfu").removeClass("hide");
            else
                $(".topHengfu").addClass("hide");

            var ltype = $.getUrlParam("ltype");
            if (ltype == "setlogin") { $("#access_info").hide(); }

            $('#iptSearch').on('input', function () {
                var inputValue = $(this).val();
                SearchBox.EnterChar(inputValue);
            });

            $("#UpdatePark").click(function () {
                var page = "/Index/EditPark";
                if (versionMode == "2") page = "/Index/EditCloudPark";
                parkIndex = layer.open({
                    type: 2, id: 1,
                    title: false,
                    content: page,
                    area: getIframeArea(["600px", "450px"]),
                    maxmin: false,
                    style: "border-radius:10px"
                });
            });

        });

        function closeParkWin() {
            layer.close(parkIndex);
        }

        var setScrollTop = function (e, h) {
            if ((h + "").indexOf("px") < 0) h += "px";
            e.scrollTop = h;
        }
        var hideMenuList = ["CarType/Index", "EndNumAuth/Index", "SpecialCar/Index", "DateSet/Index", "InParkCar/Index",
            "BackCar/Index", "HealthCodeResult/Index", "CloudRecord/Index", "PayType/Index", "Syswarn/Index", "LaneMonitor/Index", "MonthRule/MonthCharge", "AccessAuth/Index",
            "CarCardType/Index", "EndNumAuth/Index", "SpecialCar/Index", "DateSet/Index", "CityServer/Index", "Reserve/Index", "BusinessCar/Index", "ParkDiscountSet/Index", "CarCoupon/Index",
            "PayPart/Index", "CouponRecord/Index", "InParkCar/Index", "CarRecog/Index", "OpenGateRecord/Index", "AbnorOrder/Index", "WorkShift/Index", "ControlEvent/Index", "BackCar/Index",
            "HealthCodeResult/Index", "CarUnbound/Index", "RptTraffic/Index", "RptTempPayment/Index", "RptCarMonth/Index", "RptCarStore/Index", "RptSpace/Index", "UserLogs/Index",
            "SystemLogs/Index", "SystemSetting/Index", "PushResult/Index", "CloudRecord/Index", "PayType/Index", "Syswarn/Index", "DataBackup/Index", "SQLExecutor/Index"];
        var versionType = localStorage.getItem("versionType");
        var dbVersionType = '@ViewBag.VersionType';
        if (versionType == undefined || versionType == null) { if (dbVersionType == "1") { versionType = "standard"; } else { versionType = "simple"; } localStorage.setItem("versionType", versionType); }

        if (versionType == "standard") {
            $(".gradient-border").removeClass("simpletxt").addClass("standardtxt");
            $(".versiontext").html("&nbsp;旗 舰 版");
        } else {
            $(".gradient-border").removeClass("standardtxt").addClass("simpletxt");
            $(".versiontext").html("&nbsp;标 准 版");
        }

        var myClickHandler = function (k) {
            try {
                event.preventDefault();
                event.stopPropagation();
                setTimeout(function () {
                    if (!k.target.classList.contains('layui-icon') && !k.target.classList.contains('layui-tree-icon')) $(".tdselect-div").hide();
                }, 0);
                return false;
            } catch { return false; }
        }
        //绑定window点击事件
        $(window).off('click', myClickHandler).on('click', myClickHandler);

        //搜索菜单
        SearchBox = {
            EnterChar: function (inputValue) {
                var menuList = SearchBox.SearchMenu(inputValue);
                $("#selectmenu").html('');
                if (menuList.length > 0) {
                    menuList.forEach((menu, index) => {
                        var item = menu.split('#');
                        $("#selectmenu").append('<li onclick="SearchBox.LiClick(this)"> <a lay-href="' + item[0] + '"> <cite><t>' + item[1] + '</t></cite> </a> </li>');
                    })
                } else {
                    $("#selectmenu").html('<div style="color: #b3aeae; text-align: center; margin: 50px; font-size: 2rem;">无数据<div>');
                }
            },
            LiClick: function (e) {
                event.preventDefault();
                event.stopPropagation();

                var href = $(e).find('a').attr("lay-href");
                var menuname = $(e).find('a').text();

                var localMenuList = localStorage.getItem('menuList');
                if (localMenuList) localMenuList = JSON.parse(localMenuList);
                else localMenuList = [];
                if (!localMenuList.includes(href + '#' + menuname)) localMenuList.unshift(href + '#' + menuname)
                // 如果数组长度超过5个，移除多余的元素
                if (localMenuList.length > 6) {
                    localMenuList.splice(6);
                }
                localStorage.setItem('menuList', JSON.stringify(localMenuList));

                // var aTag = $(e).find('a');
                // if (aTag.length > 0) {
                //     aTag[0].click();
                // }
                global.gotoPage(href);
                return false;
            },
            ShowSelectDiv: function (e) {
                event.preventDefault();
                event.stopPropagation();

                //获取当前行的索引
                var dom = $(e).get(0);
                var position = SearchBox.getPosition(dom);
                var top = position.top + dom.offsetHeight + 23;
                var left = position.left - 140;
                var width = dom.clientWidth;

                //设置样式
                var css = '.tdselect-postion { left:' + left + 'px !important; bottom:auto;cursor: pointer;}';
                //添加样式
                var style = document.createElement("style");
                style.id = "tdselect-div-float";
                style.innerText = css;
                document.head.append(style);
                $(".tdselect-div").removeClass("tdselect-postion").addClass("tdselect-postion");
                $("#history").html('');
                $("#selectmenu").html('');

                var localMenuList = localStorage.getItem('menuList');
                if (localMenuList) {
                    // 解析JSON字符串为数组
                    localMenuList = JSON.parse(localMenuList);
                    if (Array.isArray(localMenuList) && localMenuList.length > 0) {
                        localMenuList.forEach((menu, index) => {
                            var item = menu.split('#');
                            if (item.length === 2) {
                                $("#history").append('<li> <a lay-href="' + item[0] + '"> <cite><t>' + item[1] + '</t></cite> </a> </li>');
                            }
                        });
                    }
                }

                SearchBox.EnterChar($('#iptSearch').val());

                $(".tdselect-div").show();
                e.click();
            },
            SearchMenu: function (content) {
                var menuList = [];
                var lin = $("#sideMenu dl dd");

                for (var i = 0; i < lin.length; i++) {
                    var href = $(lin[i]).find("a").attr("lay-href");
                    var pingyin = $(lin[i]).find("a").attr("data-search");
                    if (!pingyin) pingyin = "";
                    var menuname = $(lin[i]).find("a").text();
                    if (content.length > 0) {
                        if (SearchBox.containsAny(pingyin, content) || SearchBox.containsAny(menuname, content)) {
                            menuList.push(href + '#' + menuname);
                        }
                    } else {
                        menuList.push(href + '#' + menuname);
                    }
                }

                if (menuList.length > 0) {
                    const charArray = content.split('');
                    menuList = menuList.sort((a, b) => SearchBox.countContainedChars(b, charArray) - SearchBox.countContainedChars(a, charArray));
                }

                return menuList;
            },
            containsAny: function (str, chars) {
                // 将字符列表转换为数组
                const charArray = chars.split('');

                // 获取字符数组的长度
                const length = charArray.length;

                // 创建一个字符计数器
                const charCount = {};
                charArray.forEach(char => {
                    charCount[char] = (charCount[char] || 0) + 1;
                });

                // 遍历目标字符串并检查字符出现次数
                const strCount = {};
                for (let i = 0; i < str.length; i++) {
                    const char = str[i];
                    if (charCount[char] !== undefined) {
                        strCount[char] = (strCount[char] || 0) + 1;
                    }
                }

                // 检查目标字符串是否包含输入字符中每个字符的足够次数
                for (const char in charCount) {
                    if (strCount[char] === undefined || strCount[char] < charCount[char]) {
                        return false;
                    }
                }

                return true;
            },
            countContainedChars: function (str, charArray) {
                // 统计字符串中包含的字符数量
                return charArray.reduce((count, char) => count + (str.includes(char) ? 1 : 0), 0);
            },
            getPosition: function (element) {
                var actualLeft = element.offsetLeft,
                    actualTop = element.offsetTop,
                    current = element.offsetParent; // 取得元素的offsetParent
                // 一直循环直到根元素
                while (current !== null) {
                    actualLeft += current.offsetLeft;
                    actualTop += current.offsetTop;
                    current = current.offsetParent;
                }
                // 返回包含left、top坐标的对象
                return {
                    left: actualLeft,
                    top: actualTop
                };
            },
        }
    </script>
    <script>
        layui.config({ base: '../Static/admin/', theme: { color: [{ main: '#000000', selected: '#000fff', alias: 'purple-red' }], initColorIndex: 0 } }).extend({ index: 'lib/index' }).use('index', function () {
            try {
                var currentDate = new Date();
                var lastPopupDate = localStorage.getItem('weblastPopupDate');
                var oneDayInMillis = 24 * 60 * 60 * 1000;
                if (!lastPopupDate || (Math.abs(currentDate - new Date(lastPopupDate)) > oneDayInMillis)) {
                    var webIsStrongPassword = localStorage.getItem('webIsStrongPassword');
                    if (webIsStrongPassword != null && webIsStrongPassword == 'false') {
                        var pwdIframe = layer.open({
                            title: false,
                            type: 1,
                            offset: 'auto', // 改为居中显示
                            content: '<div style="padding: 20px 80px;"><t style="color:red;font-size:1.5rem;font-weight:700;">密码风险提醒</t><br/><br/>您的密码为弱密码，存在安全隐患，请及时修改</div>',
                            btn: ['立即修改', '知道了'],
                            btnAlign: 'c',
                            shade: 0.5, // 设置遮罩，0.3 表示 30% 透明度
                            time: 0,
                            closeBtn: 0, // 去掉右上角的关闭按钮
                            yes: function (index, layero) {
                                layer.close(pwdIframe);
                                layer.open({
                                    title: "<i class='fa fa-unlock-alt'></i> 修改密码",
                                    type: 2, id: 1,
                                    area: ['450px', '390px'],
                                    fix: false, //不固定
                                    maxmin: false,
                                    content: '/Index/ChangePassword'
                                });
                            }
                        });
                        localStorage.setItem('weblastPopupDate', currentDate);
                    }
                }
            } catch (e) {
                console.log(e)
            }
        });
        $("#modal-form").on('show.bs.modal', function () { });
        $("#modal-form").on('hide.bs.modal', function () {
            var pathname = $(this).attr("data-iframe");
            if (pathname && pathname != 'undefined' && pathname != '') {
                pathname = pathname.split('/').filter(d => d).join('/');
                var iframe = $(window.document).find("iframe[name='" + pathname + "']");
                $(iframe).attr('src', $(iframe).attr("src")); //刷新该框架，重新加载页面
                $(this).attr("data-iframe", "");
            }
        });
    </script>
    <script>
        var topLayui = true;
        var global = {
            parkStatusRetryInterval: 2000,// 初始间隔 2 秒
            maxInterval: 60000 * 10,// 最大间隔 10 分钟
            controller: new AbortController(),
            timeoutId: null,// 存储定时器 ID
            isWindow: '@Html.Raw(ViewBag.IsWindows)'.toLowerCase() === 'true',
            warnCount: 0,
            formPower: null,
            powerType: null,
            showMenu: function () {
                var lin = $("#sideMenu dl dd");  //显示子菜单
                for (var i = 0; i < lin.length; i++) {
                    var href = $(lin[i]).find("a").attr("lay-href");
                    var controlName = href.split('/')[0];
                    if (this.formPower[controlName] == undefined) {
                        $(lin[i]).addClass("layui-hide");
                        continue;
                    }
                    if (versionType == "simple" && $.inArray(href, hideMenuList) != -1) {
                        $(lin[i]).addClass("layui-hide");
                    } else {
                        if (this.formPower[controlName]["View"] == "true") {
                            $(lin[i]).addClass("layui-show");
                        } else {
                            $(lin[i]).addClass("layui-hide");
                        }
                    }
                }

                //$(".loadingMenu").remove();
                var li = $("#sideMenu li");   //显示父菜单
                for (var j = 0; j < li.length; j++) {
                    if ($(li[j]).find("dl dd[class='layui-show']").length == 0) {
                        $(li[j]).addClass("layui-hide");
                    }
                }

                $("li[data-name=Tools]").each(function () { $(this).removeClass("layui-hide"); $(this).find("dd").removeClass("layui-hide"); })
            },

            //页面的按钮权限控制；win=页面窗口对象,callBack返回参数pagePower=页面对象的所有权限
            //此方法仅处理的对象页面中的button元素，如需要处理其他，则在callBack中自行处理，
            getBtnPower: function (win, callBack) {
                var pathname = win.location.pathname;
                var controlName = pathname.trim('/').split('/')[0];
                var btns = win.document.getElementsByTagName("button");
                var pagePower = this.formPower[controlName]
                for (var i = 0; i < btns.length; i++) {
                    if (!$(btns[i]).hasClass("layui-laypage-btn")) {
                        var id = btns[i].id
                        if (pagePower[id]) {
                            $(btns[i]).removeClass("layui-hide")
                        } else {
                            if (controlName.indexOf("Tools") == -1) $(btns[i]).removeClass("layui-hide").addClass("layui-hide")
                        }
                    }
                }

                if (callBack) callBack(pagePower);
            },

            bindPower: function () {
                var pagePower = global.formPower['HeadPage'];
                if (pagePower["UpdatePark"]) {
                    $("#UpdatePark").removeClass("layui-hide");
                }
            },

            TimeMinMax: function (s, e, ds, de, page) {
                $(e).focus(function () { if ($(s).val() == "") { de.min = "1900-01-01 00:00:00"; page.laydate(de); } });
                $(s).focus(function () { if ($(e).val() == "") { ds.max = "2099-12-31 00:00:00"; page.laydate(ds); } });
            },

            gotoPage: function (pageRote) {
                console.log("跳转：" + pageRote)
                $('#sideMenu a[lay-href="' + pageRote + '"]').click();
                $('#sideMenu li').removeClass('layui-nav-itemed');
                $('#sideMenu a[lay-href="' + pageRote + '"]').closest('li').addClass('layui-nav-itemed');
                if ($("iframe[name='" + pageRote + "']")) {
                    $("iframe[name='" + pageRote + "']").attr("src", pageRote);
                }
            },
            getFormPower: function () {
                $.getJSON("/PowerGroup/GetFormPower?_r=" + Math.random(), {},  //获取到当前用户所有权限
                    function (json) {
                        if (json.Success) {
                            global.formPower = json.Data;
                            global.showMenu();             //处理菜单
                            global.bindPower();            //处理页面按钮权限
                        } else {
                            layer.msg('系统错误', function () {
                                window.top.location.href = '../../';
                            });
                        }
                    });
            },
            getSyswarnRecord: function () {
                $.post("Index/SyswarnRecord", {}, function (json) {
                    if (json.success) {
                        if (json.data != null && json.data.total != undefined) {
                            $(".total").parent().removeClass("layui-hide");
                            $(".warnitem").removeClass("layui-hide");

                            if (json.data.total == null) json.data.total = 0;
                            if (json.data.eventcount == null) json.data.eventcount = 0;
                            if (json.data.total + json.data.eventcount <= 0) {
                                $(".total").parent().removeClass("layui-hide").addClass("layui-hide");
                            }

                            $(".total").html(json.data.total + json.data.eventcount);
                            $(".one").html(json.data.total);
                            $(".two").html(json.data.eventcount);
                        } else {
                            $(".total").parent().removeClass("layui-hide").addClass("layui-hide");
                            $(".warnitem").removeClass("layui-hide").addClass("layui-hide");
                        }
                    } else {
                        $(".total").parent().removeClass("layui-hide").addClass("layui-hide");
                        $(".warnitem").removeClass("layui-hide").addClass("layui-hide");
                    }
                }, "json");
            },
            updatePower: function () {
                $.post("/PowerGroup/UpdatePower", {}, function (json) {
                    if (json.success) {
                        if (json.data == "1") {
                            Snackbar.show({
                                text: "将于3秒后刷新登录权限",
                                pos: 'top-center',
                                showAction: false,
                                duration: 5000,
                                actionTextColor: '#fff',
                                backgroundColor: '#8dbf42',
                                customClass: 'customClass'
                            });
                            setTimeout(function () { location.href = window.location.href; }, 3000); // 刷新当前页面
                        }
                    } else {
                        Snackbar.show({
                            text: json.msg,
                            pos: 'top-center',
                            showAction: false,
                            duration: 5000,
                            actionTextColor: '#fff',
                            backgroundColor: '#e7515a',
                            customClass: 'customClass'
                        });
                        setTimeout(function () {
                            Snackbar.show({
                                text: "将于3秒后刷新登录权限",
                                pos: 'top-center',
                                showAction: false,
                                duration: 5000,
                                actionTextColor: '#fff',
                                backgroundColor: '#e7515a',
                                customClass: 'customClass'
                            });
                            setTimeout(function () { location.href = window.location.href; }, 3000)
                        }, 2000);
                    }
                }, "json").fail(function () {
                    Snackbar.show({
                        text: "刷新登录权限异常",
                        pos: 'top-center',
                        showAction: false,
                        duration: 5000,
                        actionTextColor: '#fff',
                        backgroundColor: '#e7515a',
                        customClass: 'customClass'
                    });
                });
            },
            getParkStatus: function () {
                try {
                    // 取消前一个请求（如果存在）
                    if (global.controller && !global.controller.signal.aborted) {
                        global.controller.abort();
                    }

                    // 重新创建新的 AbortController
                    global.controller = new AbortController();

                    // 指数递增间隔，避免频繁请求
                    global.parkStatusRetryInterval = Math.min(global.parkStatusRetryInterval * 2, global.maxInterval);

                    fetch("/Index/GetParkingState?" + Math.random(), { signal: global.controller.signal })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                let parkStatusHtml = "";
                                let dogStatusHtml = "";

                                if (parkmode !== "2") {
                                    const { parkstatus, dogstatus, dogMsg } = json.data;
                                    parkStatusHtml = $("#park_online").tmpl([{ online: parkstatus }]);

                                    const dogStatusText = dogstatus === 1 ? "已授权" : (dogstatus === 2 ? "试用期" : "未授权");
                                    const dogStatusColor = dogstatus === 1 ? "blue" : (dogstatus === 2 ? "green" : "orange");
                                    const dogMessage = dogstatus > 0 ? dogMsg : "软件未授权";

                                dogStatusHtml = ` <span class="tooltip1 layui-badge layui-bg-${dogStatusColor}">
                                                     软件授权：${dogStatusText}
                                                     <span class="tooltiptext">${dogMessage}</span>
                                                 </span>`;
                            } else {
                                const { parkstatus, isEmergency, ConnMode } = json.data;
                                parkStatusHtml = $("#park_online").tmpl([{ online: parkstatus }]);

                                    const emergencyHtml = isEmergency === 1
                                        ? '<label class="layui-badge layui-bg-blue">开启</label>'
                                        : '<label class="layui-badge layui-bg-cyan">关闭</label>';

                                    const connModeHtml = ConnMode === "1"
                                        ? '<label class="layui-badge layui-bg-green">协同模式</label>'
                                        : '<label class="layui-badge layui-bg-blue">开放模式</label>';

                                    $("#SysConfig_CloudBoxLprEmergency").html(emergencyHtml);
                                    $("#SysConfig_ConnMode").html(connModeHtml);
                                }

                                $(".parkstatus").html(parkStatusHtml);
                                $(".dogstatus").html(dogStatusHtml);
                            } else {
                                $(".parkstatus").html($("#park_online").tmpl([{ online: -1 }]));
                            }
                        })
                        .catch(err => {
                            if (err.name !== "AbortError") console.error("请求失败", err);
                        })
                        .finally(() => {
                            // 确保定时请求不会被意外中断
                            if (!global.controller.signal.aborted) {
                                console.log("ParkStatus");
                                clearTimeout(global.timeoutId);
                                global.timeoutId = setTimeout(global.getParkStatus, global.parkStatusRetryInterval);
                            }
                        });

                } catch (error) {
                    console.error("getParkStatus 发生错误:", error);

                    // 确保定时请求不会中断
                    clearTimeout(global.timeoutId);
                    global.timeoutId = setTimeout(global.getParkStatus, global.parkStatusRetryInterval);
                }
            },
            openTestTools: function () {
                layer.open({
                    type: 2,
                    title: "测试配置",
                    content: "/TestTools/Index",
                    area: ["90%", "90%"],
                    shade: 0,
                    maxmin: true,
                });
            }
        }

        $(function () {
            global.getFormPower();
            global.getSyswarnRecord();
            global.getParkStatus();

            //每隔5分钟刷新一次警告记录
            setInterval(() => { global.getSyswarnRecord(); }, 300000);

            var ToUrl = $.getUrlParam("ToUrl");
            if (ToUrl && ToUrl != '') {
                setTimeout(function () {
                    global.gotoPage(ToUrl + "/Index");

                }, 1000);
            }
        });
    </script>

    <script>
        $(".changePwd").click(function (event) {
            layer.open({
                title: "<i class='fa fa-unlock-alt'></i> 修改密码",
                type: 2, id: 1,
                area: ['450px', '390px'],
                fix: false, //不固定
                maxmin: false,
                content: '/Index/ChangePassword'
            });
        });
        $(".chkstandard").click(function (event) {
            localStorage.setItem("versionType", "standard");
            versionType = "standard";
            $.post("/Index/SetVersionType", { versionType: versionType }, function (data) { }, "json")
            window.location.reload();
        });
        $(".chksimple").click(function (event) {
            localStorage.setItem("versionType", "simple");
            versionType = "simple";
            $.post("/Index/SetVersionType", { versionType: versionType }, function (data) { }, "json")
            window.location.reload();
        });
        $(".warna").click(function () {
            var one = parseInt($(".one").text());
            var two = parseInt($(".two").text());

            global.warnCount = one + two;
            if ($(this).find("span.one").length > 0) {
                $('a[lay-href="Syswarn/Index"]').click();
                if ($("iframe[name='系统报警']"))
                    $("iframe[name='系统报警']").attr("src", "Syswarn/Index");
                setTimeout(function () { global.warnCount = two; }, 2000);
            } else {
                $('a[lay-href="ControlEvent/Index"]').click();
                if ($("iframe[name='事件管理']"))
                    $("iframe[name='事件管理']").attr("src", "ControlEvent/Index");
            }
        })

        $("#toolDiv").dblclick(function () {
            $("li[data-name=Tools]").removeClass("layui-hide");
        })

        $(".changemode").click(function () {
            var msg = "";
            if (emergencyMode == 1) {
                msg = "当前已启用应急模式，确定关闭吗？";
            } else {
                msg = "当前未启用应急模式，确定启用吗？";
            }
            layer.open({
                type: 0,
                title: "消息提示",
                btn: ["确定", "取消"],
                content: msg,
                area: ["300px"],
                yes: function (res) {
                    layer.msg("切换中", { icon: 16, time: 0 });
                    setTimeout(function () {
                        $.post("/FastGuide/SwitchParkingMode", {}, function (json) {
                            if (json.success) {
                                if (sentryMode == 1) sentryMode = 0; else sentryMode = 1;
                                $("#SysConfig_CloudBoxLprEmergency").html(sentryMode == 1 ? '<label class="layui-badge layui-bg-blue">开启</label>' : '<label class="layui-badge layui-bg-cyan">关闭</label>');
                                if (json.data && json.data != null && json.data.length > 0) {

                                    pager.deviceSend = [];

                                    var msg = (sentryMode == 1 ? "已开启" : "已关闭") + "车场应急模式";
                                    $(".successp").html(msg);
                                    // 显示操作成功提示
                                    layer.open({
                                        type: 1,
                                        title: '为了不影响使用，请等待设备消息发送成功后再操作',
                                        content: $('#successMessage'),
                                        area: ['450px', '350px']
                                    });

                                    var devices = json.data;
                                    var $deviceList = $('.device-list');
                                    $deviceList.empty(); // 清空列表

                                    // 动态生成设备列表项
                                    devices.forEach(function (device) {
                                        var $item = $('<div class="device-item"></div>');
                                        $item.append('<span class="device-name">' + device.Device_IP + '</span>');
                                        $item.append('<span class="device-name">' + device.Device_Name + '</span>');
                                        var $status = $('<span data-value="' + device.Device_No + '" class="device-status">正在发送消息...</span>');
                                        $item.append($status);
                                        $deviceList.append($item);
                                    });

                                    setTimeout(async function () {
                                        $.ajaxSettings.async = false;
                                        const deviceNosString = devices.map(device => device.Device_No).join(',');
                                        $.post("/FastGuide/SendDeviceEnableClouds", { deviceNos: deviceNosString, parkingMode: sentryMode }, function (json) {
                                            if (json.success) {
                                                _ = pager.checkAllStatus(devices)
                                            } else {
                                                pager.deviceSend = devices;
                                                $(".device-status").text('消息发送失败').removeClass("success").addClass('fail');
                                            }
                                        });

                                    }, Math.random() * 2000 + 1000); // 随机时间1-3秒

                                } else {
                                    layer.msg("切换成功", { icon: 1, time: 1500 }, function () { });
                                }
                            } else
                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                        });
                    }, 10);
                },
                btn2: function () { }
            })
        });
        $('.changemode').hover(function () {
            // 显示提示
            layer.tips('点击切换车场模式', this, {
                tips: [1, '#3595CC'], // 方向和颜色
                time: 2000
            });
        });
    </script>

    <script>
        //登录
        $("#btn_Login").click(function () {

            var btn = $(this);

            if (localData.get("Admins_Account") == null) {
                layer.tips("您的缓存被清除，请刷新页面登录", '#password', { tips: [3] });
                return;
            }

            var username = decodeURIComponent(localData.get("Admins_Account"));
            var password = $("#password").val().trim();


            var data = { Admins_Account: username, Admins_Pwd: password };
            var encrypt = new JSEncrypt();
            encrypt.setPublicKey("@Html.Raw(carparking.Common.RSAConfig.RsaPublicKey)");
            var encrypted = encrypt.encrypt(JSON.stringify(data));

            $('#btnLogin').button('loading');
                    $.post("/Login/ToLogin", { encrypted: encrypted },
                        function (data) {
                            if (data.Success) {
                                $(".close").click();
                                //$("#username").val("");
                                $("#password").val("");
                                btn.button('reset');

                                //判断有跳转打开页面
                                var bta = $("#LoginForm");
                                var re = $(bta).attr("data-reload");
                                var name = $(bta).attr("data-iframe");
                                if (re === "true" && name !== "") {
                                    var iframe = $(window.document).find("iframe[name='" + name + "']");
                                    $(iframe).attr('src', $(iframe).attr("src")); //刷新该框架，重新加载页面
                                } else {
                                    layer.msg("登录成功", { icon: 1, time: 1000 });
                                }
                            } else {
                                setTimeout(function () {
                                    $('#btn_Login').button('reset');
                                }, 500);
                                layer.tips("提示：" + data.Message, '#btn_Login', { tips: [3] });
                            }
                        }, "json");
        });

        $("#password").keydown(function (event) {
            if (event.keyCode == 13) {
                $("#btn_Login").click();
            }
        });

        $("#logout").click(function () {
            $.post("/Index/ExitLogin", {}, function (data) {
                if (data.Success) {
                    window.location.href = "/Login/Index";
                } else {

                }
            }, "json");
        });

        $(function () {
            $("input:text").focus();//Linux输入法触发
        })


        document.addEventListener('DOMContentLoaded', function () {
            var ctrlPressed = sessionStorage.getItem('ctrlPressed') === 'true' ? true : false;
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Control') {
                    ctrlPressed = true;
                    sessionStorage.setItem('ctrlPressed', 'true');
                }
            });

            document.addEventListener('keyup', function (event) {
                if (event.key === 'Control') {
                    ctrlPressed = false;
                    sessionStorage.setItem('ctrlPressed', 'false');
                }
            });
        });
    </script>

    <!--系统导航-->
    <script>
        layui.use(['layer', 'jquery'], function () {
            var layer = layui.layer;
            var $ = layui.jquery;

            // 使按钮可拖动
            var isDragging = false;
            var startY, startTop;
            var isClick = true;

            $('#sideButton').on('mousedown', function (e) {
                isDragging = true;
                isClick = true;
                startY = e.clientY;
                startTop = e.clientY;
                $(document).on('mousemove', onMouseMove);
                $(document).on('mouseup', onMouseUp);
                console.log('鼠标按下' + e.clientY + ',' + e.clientX + '：' + isDragging + ',' + isClick);
            });

            function onMouseMove(e) {
                if (isDragging) {
                    var distc = Math.abs(e.clientY - startY);
                    if (distc > 5) {
                        isClick = false;
                    }
                    var newTop = startTop + (e.clientY - startY);
                    $('#sideButton').css('top', newTop + 'px');
                }
                console.log('鼠标移动' + isDragging + ',' + isClick + "," + e.clientY);
            }

            function onMouseUp() {
                if (isDragging) {
                    isDragging = false;
                    $(document).off('mousemove', onMouseMove);
                    $(document).off('mouseup', onMouseUp);
                }
                console.log('鼠标抬起' + isDragging + ',' + isClick);
            }

            // 点击按钮弹出页面
            $('#sideButton').on('click', function () {
                if (isClick) {
                    layer.open({
                        type: 1,
                        shadeClose: true,
                        title: '系统导航',
                        content: '<div id="sys-direct" style="display:flex;justify-content: center;align-items: center;padding:0 50px 0 10px;">'
                            + '<div style="flex:2;text-align:center;"><a class="closeLink" lay-href="FastGuide/Index"><img src="/Static/img/home/<USER>"><cite><t>快速配置 </t></cite></a></div>'
                            + '<div style="flex:0.5;text-align:center;"><img src="/Static/img/home/<USER>"></div>'
                            + '<div style="flex:2;text-align:center;"><a class="closeLink" lay-href="Device/Index"><img src="/Static/img/home/<USER>"><cite><t>设备管理 </t></cite></a></div>'
                            + '<div style="flex:1;text-align:center;"><img src="/Static/img/home/<USER>"></div>'
                            + '<div style="flex:2;text-align:center;border: 1px dotted #696969;border-radius: 15px;padding: 10px 0;">'
                            + '<div style="margin-bottom:80px;"><a class="closeLink" lay-href="Policy/Index"><img src="/Static/img/home/<USER>"><cite><t>车场设置 </t></cite></a></div>'
                            + '<div><a class="closeLink" lay-href="BillingRule/Index"><img src="/Static/img/home/<USER>" ><cite><t>计费规则 </t></cite></a></div>'
                            + '</div>'
                            + '<div style="flex:1;text-align:center;"><img src="/Static/img/home/<USER>"></div>'
                            + '<div style="flex:2;text-align:center;border: 1px dotted #696969;border-radius: 15px;padding: 10px 0;">'
                            + '<div style="margin-bottom:80px;"><a class="closeLink" lay-href="Owner/Index"><img src="/Static/img/home/<USER>" ><cite><t>车辆登记 </t></cite></a></div> '
                            + '<div><a class="closeLink" href="#" id="updatePark"><img src="/Static/img/home/<USER>"><cite><t>云平台配置 </t></cite></a></div>'
                            + '</div>'
                            + '</div>',
                        area: ['60%', '60%'],
                        success: function (layero, index) {
                            var closeLinks = document.querySelectorAll('.closeLink');
                            closeLinks.forEach(function (link) {
                                link.onclick = function (e) {
                                    layer.close(index);
                                    if (e.currentTarget.id === 'updatePark') {
                                        $("#UpdatePark").click();
                                    }
                                };
                            });
                        }
                    });
                }
                console.log('鼠标点击按钮' + isClick);
            });
            // 监测鼠标离开按钮范围
            $('#sideButton').on('mouseleave', function () {
                console.log('鼠标离开按钮范围');
                isDragging = false;
                isClick = false;
                $(document).off('mousemove', onMouseMove);
                $(document).off('mouseup', onMouseUp);
            });

        });

    </script>
</body>

</html>