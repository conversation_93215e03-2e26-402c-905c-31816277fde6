﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>优惠券记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                           
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcouponcode">
        {{# if(d.CouponRecord_CouponCode==0||d.CouponRecord_CouponCode==null){}}
        <span class="layui-badge layui-bg-gray">未知</span>
        {{# }else if(d.CouponRecord_CouponCode==101){}}
        <span class="layui-badge layui-bg-blue">减免金额</span>
        {{# }else if(d.CouponRecord_CouponCode==102){}}
        <span class="layui-badge layui-bg-cyan">优惠时间</span>
        {{# }else if(d.CouponRecord_CouponCode==103){}}
        <span class="layui-badge layui-bg-orange">打折优惠</span>
        {{# }else if(d.CouponRecord_CouponCode==104){}}
        <span class="layui-badge layui-bg-green">免费时间</span>
        {{# }else if(d.CouponRecord_CouponCode==105){}}
        <span class="layui-badge layui-bg-green">时段全免</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplvalue">
        {{# if(d.CouponRecord_CouponCode==null||d.CouponRecord_CouponCode==0){}}
        <span class="layui-badge layui-bg-gray"></span>
        {{# }else if(d.CouponRecord_CouponCode==101){}}
        <span>{{d.CouponRecord_Value}}元</span>
        {{# }else if(d.CouponRecord_CouponCode==102){}}
        <span>{{d.CouponRecord_Value}}分钟</span>
        {{# }else if(d.CouponRecord_CouponCode==103){}}
        <span>{{d.CouponRecord_Value}}折</span>
        {{# }else if(d.CouponRecord_CouponCode==104){}}
        <span>{{d.CouponRecord_EndTime}}</span>
        {{# }else if(d.CouponRecord_CouponCode==105){}}
        <span>{{d.CouponRecord_EndTime}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.CouponRecord_Status ==0){}}
        <span class="layui-badge layui-bg-green">未使用</span>
        {{# }else if(d.CouponRecord_Status ==1){}}
        <span class="layui-badge layui-bg-blue">已使用</span>
        {{# }else if(d.CouponRecord_Status ==2){}}
        <span class="layui-badge layui-bg-cyan">已注销</span>
        {{# } else if( d.CouponRecord_Status == 3){}}
        <span class="layui-badge layui-bg-black">已过期</span>
        {{# } else {}}
        <span class="layui-badge layui-bg-gray">已失效</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script>
        var paramParkOrderNo = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");
            var cols = [[
                { type: 'radio' }
                , { field: 'CouponRecord_No', title: '优惠券编号' }
                , { field: 'CouponRecord_ParkNo', title: '车场编码', hide: true }
                , { field: 'CouponRecord_CouponCode', title: '优惠方式', toolbar: "#tmplcouponcode" }
                , { field: 'CouponRecord_Value', title: '优惠额度', toolbar: "#tmplvalue" }
                , { field: 'CouponRecord_IssueCarNo', title: '车牌号' }
                , { field: 'CouponRecord_AddTime', title: '发放时间' }
                , { field: 'CouponRecord_UsedTime', title: '使用时间' }
                , { field: 'CouponRecord_Status', title: '状态', toolbar: "#tmplstatus" }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CarCoupon/GetCouponRecordList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { CouponRecord_ParkOrderNo: paramParkOrderNo  }
                , limits: [10, 20, 50, 100]
                , done: function () {

                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
             
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/CarCoupon/GetCouponRecordList'
                    , where: { CouponRecord_ParkOrderNo: paramParkOrderNo } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
               
            }
        }

        var nowDate = new Date(); 
        function getCouponRecordStatus(CouponRecord_EndTime, CouponRecord_Status) {
            if (CouponRecord_EndTime && CouponRecord_EndTime != "") {
                if (Date.parse(CouponRecord_EndTime.replace(/-/g, "/")) >= nowDate && CouponRecord_Status == -1) { //未领取
                    return -1;
                }
                else if (Date.parse(CouponRecord_EndTime.replace(/-/g, "/")) >= nowDate && CouponRecord_Status == 0) { //未使用
                    return 0;
                }
                else if (CouponRecord_Status == 1) { //已使用
                    return 1;
                }
                else if (CouponRecord_Status == 2) { //已注销
                    return 2;
                }
                else //已过期
                {
                    return 3;
                }
            } else {
                return null;
            }
        }
    </script>
</body>
</html>
