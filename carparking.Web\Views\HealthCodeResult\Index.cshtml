﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>健康码记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v1.0" rel="stylesheet" />
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }

        .help-btn { position: absolute; width: 20px; height: 20px; right: 5px; top: 2px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }
        a.goto { text-decoration: underline; cursor: pointer; color:#1ab394 !important;}
        a.goto:hover { color: orangered !important; font-weight: bold; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>健康码记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-row">
                                <div class="layui-inline">
                                    <input class="layui-input " name="HealthCodeResult_ParkOrderNo" id="HealthCodeResult_ParkOrderNo" autocomplete="off" placeholder="停车订单号" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="HealthCodeResult_CarNo" id="HealthCodeResult_CarNo" autocomplete="off" placeholder="车牌号" />
                                </div>
                                <div class="layui-inline">
                                    <select id="HealthCodeResult_Color" name="HealthCodeResult_Color" lay-search>
                                        <option value="">健康码颜色</option>
                                        <option value="0">黑色</option>
                                        <option value="1">绿色</option>
                                        <option value="2">黄色</option>
                                        <option value="3">红色</option>
                                    </select>
                                </div>
                                <div class="layui-inline form-group">
                                    <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t56" asp-append-version="true"></script>

    <script>
        myVerify.init();

        var Power = window.parent.global.formPower;
        var comtable = null;
        s_carno_picker.init("HealthCodeResult_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "HealthCodeResult_CarNo") {
                $("#HealthCodeResult_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['table', 'element', 'form'], function () {
            var table = layui.table;

            pager.init();
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'HealthCodeResult_ID', title: 'ID', hide: true  }
                , { field: 'HealthCodeResult_No', title: '唯一编号', hide: true }
                , { field: 'HealthCodeResult_ParkOrderNo', title: '停车订单号', hide: true }
                , { field: 'HealthCodeResult_CarNo', title: '车牌号' }
                , { field: 'HealthCodeResult_Name', title: '姓名' }
                , { field: 'HealthCodeResult_IDCard', title: '身份证号' }
                , {
                    field: 'HealthCodeResult_Color', title: '健康码颜色', templet: function (d) {
                        if (d.HealthCodeResult_Color == 0) return tempBar(-1, "黑色");
                        else if (d.HealthCodeResult_Color == 1) return tempBar(2, "绿色");
                        else if (d.HealthCodeResult_Color == 2) return tempBar(7, "黄色");
                        else if (d.HealthCodeResult_Color == 3) return tempBar(0, "红色");
                        else return tempBar(5, "-");
                    }
                }
                , { field: 'HealthCodeResult_HsDate', title: '核酸检测日期', hide: true }
                , {
                    field: 'HealthCodeResult_HsRes', title: '核酸检测结果', templet: function (d) {
                        if (d.HealthCodeResult_HsRes == 0) return tempBar(2, "阴性");
                        else if (d.HealthCodeResult_HsRes == 1) return tempBar(0, "阳性");
                        else if (d.HealthCodeResult_HsRes == 2) return tempBar(3, "待复核");
                        else if (d.HealthCodeResult_HsRes == 3) return tempBar(-1, "未知");
                        else return tempBar(5, "-");
                    }
                }
                , {
                    field: 'HealthCodeResult_HsTime', title: '核酸检测时效', templet: function (d) {
                        if (d.HealthCodeResult_HsTime == 24) return tempBar(1, "24小时");
                        else if (d.HealthCodeResult_HsTime == 48) return tempBar(6, "48小时");
                        else if (d.HealthCodeResult_HsTime == 72) return tempBar(2, "72小时");
                        else return tempBar(5, "-");
                    }
                }
                , { field: 'HealthCodeResult_YmCount', title: '疫苗针数' }
                , { field: 'HealthCodeResult_KyDate', title: '抗原检测日期', hide: true }
                , {
                    field: 'HealthCodeResult_KyRes', title: '抗原检测结果', templet: function (d) {
                        if (d.HealthCodeResult_KyRes == 0) return tempBar(2, "阴性");
                        else if (d.HealthCodeResult_KyRes == 1) return tempBar(0, "阳性");
                        else if (d.HealthCodeResult_KyRes == 2) return tempBar(3, "待复核");
                        else if (d.HealthCodeResult_KyRes == 3) return tempBar(-1, "未知");
                        else return tempBar(5, "-");
                    }
                }
                , {
                    field: 'HealthCodeResult_KyTime', title: '抗原检测时效', templet: function (d) {
                        if (d.HealthCodeResult_KyTime == 24) return tempBar(1, "24小时");
                        else if (d.HealthCodeResult_KyTime == 48) return tempBar(6, "48小时");
                        else if (d.HealthCodeResult_KyTime == 72) return tempBar(2, "72小时");
                        else return tempBar(5, "-");
                    }
                }
                , {
                    field: 'HealthCodeResult_Pass', title: '车辆通行结果', templet: function (d) {
                        if (d.HealthCodeResult_Pass == 0) return tempBar(0, "禁止通行");
                        else if (d.HealthCodeResult_Pass == 1) return tempBar(2, "自动放行");
                        else if (d.HealthCodeResult_Pass == 2) return tempBar(1, "弹框确认");
                        else if (d.HealthCodeResult_Pass == 3) return tempBar(7, "排队等候");
                        else if (d.HealthCodeResult_Pass == 4) return tempBar(3, "最低收费");
                        else return tempBar(5, "-");
                    }
                }
                , { field: 'HealthCodeResult_AddTime', title: '创建时间' }
                , {
                    field: 'btns', title: '操作', templet: function (d) {
                        var t = '<a class="goto" onclick="gotoOrder(this,\'' + d.HealthCodeResult_ParkOrderNo + '\')">查看订单</a>';
                        return t;
                    }
                }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/HealthCodeResult/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageindex = $(".layui-laypage-curr").text();
                pager.pageIndex = pageindex;
                switch (obj.event) {
                    
                };
            });

              //排序
            table.on('sort(com-table-base)', function(obj){
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox();
        });

        var pager = {
            sortField:null,
            orderField:null,
            carCarNoList: [],
            carNoList: [],
            carParam: {},
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

            },
            bindData: function (index) {
                layer.closeAll();

                pager.GetData(index);
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            },
            GetData: function (index) {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/HealthCodeResult/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) ,field: field,order: order} //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
        }

        var gotoOrder = function (e, orderno) {
            var gotod = { ParkOrder_No: orderno };
            localStorage.setItem("gotoInParkRecord", JSON.stringify(gotod));
            window.parent.global.gotoPage('InParkRecord/Index');
        }
    </script>
</body>
</html>
