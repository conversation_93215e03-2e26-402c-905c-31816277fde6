﻿<!DOCTYPE html>
<html>
<head>
    <script>
        var top = window.location.href.indexOf("top=false");
        if (window.top !== window.self && top < 0) {
            window.top.location = window.location;
        }

        var model = '@Html.Raw(ViewBag.model)';
        localStorage.setItem('sysconfig', model);
    </script>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>@ViewBag.SysConfig_DIYName</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <style>
        @@media screen and (max-width: 400px) {
            .form { width: 80% !important; margin-left: 10%; }
            .form { right: 10% !important; }
            .title { margin: 2rem !important; font-size: 1.5rem !important; }
            .footer { display: none !important; }
        }
        html, body { margin: 0; padding: 0; height: 100%; width: 100%; min-width: 1024px !important; overflow: hidden; font-size: 13px; }
        body { font-family: 'Microsoft YaHei'; background-image: url(../Static/img/login-bg.png); background-size: 100% 100%; }
        .footer { position: absolute; line-height: 50px; bottom: 0; width: 100%; text-align: center; color: #fff; background-color: rgba(0,0,0,0.1); }

        .title { margin: 50px 0 0 50px; font-size: 3rem; color: #fff; font-family: sans-serif,'Microsoft YaHei'; }
        .title .logo { float: left; }
        .title .logo img { height: 60px; }
        .title text { margin: 0 15px; line-height: 60px; }

        ::-webkit-input-placeholder { color: #ddd; }
        ::-moz-placeholder { color: #ddd; }
        :-ms-input-placeholder { color: #ddd; }

        input:-webkit-autofill,
        textarea:-webkit-autofill,
        select:-webkit-autofill { -webkit-text-fill-color: #fff !important; -webkit-box-shadow: 0 0 0px 1000px transparent inset !important; background-color: transparent; background-image: none; transition: background-color 50000s ease-in-out 0s; }
        input { background-color: transparent; }
    </style>

    <link href="~/Static/js/ui.roboto/style.css" rel="stylesheet" />
    <style>
        .form { position: absolute !important; right: 10rem; top: calc(50% - 180px); width: 360px; height: 360px; background-color: rgb(45 106 205 / 95%); }
        .table { width: 400px; height: 350px; margin: 80px auto; }
        .table form { width: 100%; }
        .table .name { width: 280px; margin: 20px auto 30px auto; display: block; height: 30px; border-radius: 20px; border: none; background: rgba(0,0,0,0.2); text-indent: 0.5em; }
        .table .btn { width: 100px; height: 30px; background: rgba(0,0,0,0.1); border-radius: 8px; border: none; color: white; margin: 0 auto; display: block; }
        .styled-button .styled-button__text { color: #fff; }
        .styled-input__input { color: #fff; }

        .layui-input { background-color: rgba(0,0,0,0); border: 1px solid rgba(0,0,0,0.1); box-shadow: none; color: #fff; }
        .layui-form-select dl dd { text-align: left; }
        .styled-button2 { background: rgb(0 0 0 / 49%) !important; }
        .bg { /*background-image: url(../../Static/img/pnlMain.BackgroundImage.png);*/ background-repeat: no-repeat; background-size: inherit; position: absolute !important; right: 4.5rem; top: calc(50% - 223px); width: 500px; height: 450px; opacity: 0.7; }
        .loginborder { border: 1px #e0dfdf solid; }
        .loginls { background-color: #1bc61e !important; }
        .loginzs { background-color: #773fd3 !important; }
        .loginszs { background-color: #4c10b0 !important; }
        .loginhs { background-color: #f22323 !important; }
        .logintqs { background-color: #08a589 !important; }
        .loginsqs { background-color: #098f77 !important; }
    </style>
</head>
<body id="loginBody" style='@ViewBag.SysConfig_DIYBackImage'>
    <div class="title">
        <span class="logo">
            @if (ViewBag.SysConfig_DIYLogo == null || ViewBag.SysConfig_DIYLogo == "")
            {
                <img src="~/Static/img/icon/icon_logo.svg" />
            }
            else
            {
                <img src="@ViewBag.SysConfig_DIYLogo" />
            }
        </span>
        <text id="BSPlatformName">@ViewBag.SysConfig_DIYName</text>
    </div>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    @if (ViewBag.SysConfig_DIYBackImage != null && ViewBag.SysConfig_DIYBackImage != null)
    {
        <div class="bg"></div>
    }
    <div class="form layui-form">
        @if (ViewBag.BsLoginTheme == 0)//天蓝色
        {
            <div class="form__cover"></div>
        }
        else if (ViewBag.BsLoginTheme == 1)//深紫色
        {
            <div class="form__cover loginszs"></div>
        }
        else if (ViewBag.BsLoginTheme == 2)//绿色
        {
            <div class="form__cover loginls"></div>
        }
        else if (ViewBag.BsLoginTheme == 3)//紫色
        {
            <div class="form__cover loginzs"></div>
        }
        else if (ViewBag.BsLoginTheme == 4)//红色
        {
            <div class="form__cover loginhs"></div>
        }
        else if (ViewBag.BsLoginTheme == 5)//天青色
        {
            <div class="form__cover logintqs"></div>
        }
        else if (ViewBag.BsLoginTheme == 6)//深青色
        {
            <div class="form__cover loginsqs"></div>
        }
        else  //天蓝色
        {
            <div class="form__cover"></div>
        }
        <div class="form__content">
            <h1>账号登录</h1>
            <div class="styled-input">
                <select class="layui-input v-null v-minlen" data-minlen="2" lay-search id="Admins_Account" name="Admins_Account">
                    <option value="">请输入账号</option>
                </select>
            </div>
            <div class="styled-input">
                <input type="password" class="layui-input" autocomplete="off" id="Admins_Pwd" placeholder="请输入密码">
            </div>
            <button type="button" class="styled-button" id="btnLogin">
                <span class="styled-button__text">登录</span>
            </button>
        </div>
    </div>
    <div class="footer">
        @*<span style="float:left;margin-left:20px;"><a href="http://www.beian.miit.gov.cn" target="_blank" style="color:#0094ff;">粤ICP备********号-1</a></span>*@
        <span style="float:right;margin-right:20px;" data-lan="lg_foot_tip">为了更好的体验效果 推荐使用谷歌浏览器</span>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script src="~/Static/js/jsencrypt.min.js"></script>
    <script>
        var dog = '@ViewBag.Dog';
        myVerify.visible = true;
        myVerify.init();
        var diy = '@ViewBag.SysConfig_DIYEnable';
        var bg = '@ViewBag.SysConfig_DIYBackImage';

        layui.use(["form"], function () {
            if (dog != "") {
                layer.open({
                    type: 1
                    , title: "系统提示"
                    , closeBtn: false
                    , area: '300px;'
                    , shade: 0.3
                    , id: 'LAY_layuipro1'
                    , resize: false
                    , btn: ['确定']
                    , btnAlign: 'c'
                    , moveType: 0
                    , content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">' + dog + '</div>'
                    , success: function (layero) {
                    }
                });
            }
            GetAccounts();
            loginFrm.bindEvent();
            if (diy == "1" && bg && bg != "") {
                //$(".styled-button").addClass("styled-button2");
                $(".layui-form").addClass("loginborder");
            }
        });

        var user_code = $.getUrlParam("user_code");
        if (user_code) console.log("user_code：" + user_code);

        var loginFrm = {
            bindEvent: function () {

                $('#btnLogin').click(function () { ToLogin(); });
                //回车登录
                $("#Admins_Account").keydown(function (event) { if (event.keyCode == 13) { ToLogin(); } });

                $("#Admins_Pwd").keydown(function (event) { if (event.keyCode == 13) { ToLogin(); } });

                function ToLogin() {
                    if (!myVerify.check()) return;

                    var account = $("#Admins_Account").val().trim();
                    var pwd = $("#Admins_Pwd").val().trim();

                    var data = { Admins_Account: account, Admins_Pwd: pwd };
                    var encrypt = new JSEncrypt();
                    encrypt.setPublicKey("@Html.Raw(carparking.Common.RSAConfig.RsaPublicKey)");
                    var encrypted = encrypt.encrypt(JSON.stringify(data));
                    $('#btnLogin').button('loading');
                            $.post("/Gt/ToLogin", { encrypted: encrypted },
                                function (data) {
                                    if (data.Success) {
                                        document.location.href = "../Monitoring/Index";
                                        localData.set("Sentry_Admins_Account", encodeURIComponent(account));
                                        var pwdStrength = CheckPwdStrength(pwd);
                                        localData.set('Sentry_pwdStrength', pwdStrength);
                                    } else {
                                        setTimeout(function () {
                                            $('#btnLogin').button('reset');
                                        }, 500);
                                        layer.tips(data.Message, '#btnLogin', { tips: [3] });
                                    }
                                }, "json");

              
                    return false; //防止submit按钮自动刷新一次页面
                }

            }
        }

        var GetAccounts = function () {
            $.ajaxSettings.async = false;
            $.post("/Gt/GetAccounts", {}, function (json) {
                if (json.success) {
                    json.data.forEach((item, index) => {
                        var selected = '';
                        var account = decodeURIComponent(localData.get('Sentry_Admins_Account') || "");
                        if (account != null && account != '' && account == item.Admins_Account) selected = 'selected';
                        else if (index == 0) selected = 'selected';

                        var option = '<option value="' + item.Admins_Account + '" ' + selected + '>' + item.Admins_Account + '</option>'
                        $("#Admins_Account").append(option);
                    });
                } else {
                    console.log(json.msg);
                }
            }, "json");
            layui.form.render();
            $.ajaxSettings.async = true;
            $("#Admins_Account").focus();
        }

        function CheckPwdStrength(val) {
            var lv = 0;
            //字母+数字
            if (val.match(/^(?!\d+$)(?![a-zA-Z]+$)[a-zA-Z\d]+$/)) { lv = 2; }
            return lv;
        };
        //var theme = { "theme": { "color": { "main": "#000fff", "logo": "#AAAA00", "selected": "#AA3130", "alias": "fashion-red", "index": 7 } } };

        $(function () {
            $("input:text").focus();//Linux输入法触发
        })
    </script>
</body>
</html>
