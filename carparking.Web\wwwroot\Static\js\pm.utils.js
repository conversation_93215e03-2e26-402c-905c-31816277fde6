﻿//公共方法

//引入js前，须先引入jquery/layui(高于2.4.0版本)/UILanguage（多语言js包）
var mlayer = null;
layui.use(["layer"], function () {
    mlayer = layui.layer;
});

/**
 * 询问框（包含确定、取消按钮）
 * @msg 提示文本
 * @yesCallback 确定按钮回调
 * @cancelCallback 取消按钮回调
 */
var LAYER_OPEN_TYPE_0 = function (msg, yesCallback, cancelCallback) {
    mlayer.open({
        type: 0,
        title: "消息提示",
        btn: ["确定", "取消"],
        shade: 0,
        content: msg,
        yes: function (res) {
            yesCallback(res);
        },
        btn2: function (res) {
            if (cancelCallback != null && cancelCallback != undefined)
                cancelCallback(res);
        },
        cancel: function (res) {
            if (cancelCallback != null && cancelCallback != undefined)
                cancelCallback(res);
        }
    });
}

/**
 *消息提示 
 */
var LAYER_MSG = function (msg, Callback, option) {
    var options = option || { icon: 1, time: 1500 };
    mlayer.msg(msg, options, function () {
        if (Callback != null && Callback != undefined)
            Callback()
    });
}

/**
 *加载框
 */
var LAYER_LOADING = function (msg) {
    var content = msg || "处理中";
    mlayer.msg(content, { icon: 16, time: 0 });
}

/**
 *日期管理 
 */
var _DATE = {
    //获取当前月第一天，返回Date类型
    getMonthStartDate: function () {
        var now = new Date(); //当前日期 
        var nowMonth = now.getMonth(); //当前月 
        var nowYear = now.getFullYear(); //当前年 
        var date = new Date(nowYear, nowMonth, 1);
        return date;
    },
    //获取当前月最后一天，返回Date类型
    getMonthEndDate: function () {
        var now = new Date(); //当前日期 
        var nowMonth = now.getMonth(); //当前月 
        var nowYear = now.getFullYear(); //当前年 
        var monthEndDate = new Date(nowYear, nowMonth + 1, 0);
        return monthEndDate;
    },
    //时间格式转换，格式yyyy-MM-dd hh:mm:ss
    getDateTime: function (date, format) {
        date = date || "1970-01-01";
        var d = new Date(date);
        format = format || "yyyy-MM-dd hh:mm:ss";
        return d.Format(format);
    },
    //获取当前时间，格式yyyy-MM-dd hh:mm:ss
    getCurDatetime: function (type) {
        var d = new Date();
        var year = d.getFullYear();
        var month = _DATE.zero(d.getMonth() + 1);
        var date = _DATE.zero(d.getDate());
        var hour = _DATE.zero(d.getHours());
        var min = _DATE.zero(d.getMinutes());
        var sec = _DATE.zero(d.getSeconds());

        var retd = [year, month, date].join('-');
        var rets = [hour, min, sec].join(':');
        if (type == 'd')
            return retd;

        return retd + ' ' + rets;
    },
    //时间增减，
    //obj：增减的时间，{year,month,date,hour,min,sec}
    //type: [d => 返回yyyy - MM - dd格式，其他 => 返回yyyy - MM - dd hh: mm: ss格式]
    getSpan: function (curDate, obj, type) {
        var year = obj.year || 0;
        var month = obj.month || 0;
        var date = obj.date || 0;
        var hour = obj.hour || 0;
        var min = obj.min || 0;
        var sec = obj.sec || 0;

        var d = new Date(curDate);
        if (year && year != 0)
            d = new Date(d.setFullYear(d.getFullYear() + year));
        if (month && month != 0) {
            //d = new Date(d.setMonth(d.getMonth() + month));
            var t = new Date(d.getFullYear(), d.getMonth(), d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds(), d.getMilliseconds());
            t.setMonth(d.getMonth() + month);
            if (t.getDate() != d.getDate()) {
                t.setDate(0);
            }
            d = t;
        }
        if (date && date != 0)
            d = new Date(d.setDate(d.getDate() + date));
        if (hour && hour != 0)
            d = new Date(d.setHours(d.getHours() + hour));
        if (min && min != 0)
            d = new Date(d.setMinutes(d.getMinutes() + min));
        if (sec && sec != 0)
            d = new Date(d.setSeconds(d.getSeconds() + sec));

        d = new Date(d);
        if (type == 'd')
            return d.Format("yyyy-MM-dd");
        return d.Format("yyyy-MM-dd hh:mm:ss");
    },
    addMonths: function (date, monthsToAdd, format) {
        format = format || "yyyy-MM-dd";
        var nowDate = date;
        for (let i = 0; i < monthsToAdd; i++) {
            var dateString = _DATE.addOneMonth(nowDate, format);
            nowDate = new Date(dateString);
        }
        return nowDate.Format(format);
    },
    addOneMonth: function (date, format) {
        format = format || "yyyy-MM-dd";
        // 获取当前日期的年份、月份和日期
        var year = date.getFullYear();
        var month = date.getMonth();
        var day = date.getDate();
        var currentDate = new Date(year, month, day);

        // 计算下一个月的年份和月份
        var nextMonthYear = year;
        var nextMonth = month + 1;

        if (nextMonth === 12) {
            // 如果是12月，年份增加1，月份重置为0（1月）
            nextMonthYear++;
            nextMonth = 0;
        }

        // 获取下一个月的最后一天
        var lastDayOfMonth = getLastDayOfMonth(nextMonthYear, nextMonth);

        currentDate.setDate(currentDate.getDate() + 1);
        if (currentDate.Format("yyyy-MM-dd") == new Date(nextMonthYear, nextMonth, 1).Format("yyyy-MM-dd")) {
            const newDate = new Date(nextMonthYear, nextMonth, lastDayOfMonth);
            return newDate.Format(format);
        }

        // 如果当前日期的天数大于下一个月的最后一天，将其设置为下一个月的最后一天
        day = Math.min(date.getDate(), lastDayOfMonth);

        // 创建新日期对象
        var newDate = new Date(nextMonthYear, nextMonth, day);

        format = format || "yyyy-MM-dd";
        return newDate.Format(format);
    },
    //小于10则前面补0
    zero: function (s) {
        s = s < 10 ? ('0' + s) : s;
        return s;
    },
    getZhDays: function (date1, date2) {
        if (!date1 || !date2) return "";

        var diff = date2.getTime() - date1.getTime();
        var day = (24 * 60 * 60 * 1000);
        var hour = (60 * 60 * 1000);
        var min = (60 * 1000);

        var mark = "";
        if (diff < 0) { mark = "-"; diff = -diff; }

        //计算出相差天数
        var days = Math.floor(diff / day);
        //计算出小时数
        var hours = (diff % day) / hour;
        //计算相差分钟数
        var minutes = ((diff % day) % hour) / min;

        if (days >= 0) {
            if (hours != 0 || minutes != 0) { days++; }
            return mark + days + " 天 ";
        } else
            return " 0 天";
    },
    getZhTimes: function (date1, date2) {
        if (!date1 || !date2) return "";

        var diff = date2.getTime() - date1.getTime();
        var day = (24 * 60 * 60 * 1000);
        var hour = (60 * 60 * 1000);
        var min = (60 * 1000);

        var mark = "";
        if (diff < 0) { mark = "-"; diff = -diff; }

        //计算出相差天数
        var days = Math.floor(diff / day);
        //计算出小时数    
        var hours = Math.floor((diff % day) / hour);
        //计算相差分钟数
        var minutes = Math.floor(((diff % day) % hour) / min);

        if (isNaN(hours) || hours == undefined || isNaN(minutes) || minutes == undefined) return "";

        if (days > 0)
            return mark + days + " 天 " + mark + hours + " 小时 " + mark + minutes + " 分钟";
        else
            return mark + hours + " 小时 " + mark + minutes + " 分钟";
    },
    getZhTimesbyMin: function (min) {
        var d1 = new Date();
        var d2 = new Date(new Date().setMinutes(new Date().getMinutes() + min));
        return this.getZhTimes(d1, d2);
    },
    //返回星期几
    GetWeekDay: function (date) {
        var weeks = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
        var today = new Date(date);
        var num = today.getDay();
        return { num: num, value: weeks[num] };
    },
    //返回时间差
    diffDay: function (t1, t2) {
        if (!t1) return 0;
        if (!t2) return 0;

        var dateDiff = t2.getTime() - t1.getTime();//时间差的毫秒数
        var day = Math.floor(dateDiff / (24 * 3600 * 1000));//计算出相差天数
        return day;
    },

    //文本框添加日期时间选择 laydate
    bind: function (laydate, elearr, config) {

        if (laydate == null) return;
        if (elearr == null) return;
        if (elearr.length == 0) return;
        if (config == null) { config = { type: 'date', range: false }; }//['clear', 'now', 'confirm']
        var cur = new Date();

        if (elearr.length == 2) {
            elearr.forEach(function (item, index) {
                if (config.range) {
                    var data0 = null;
                    var data1 = null;
                    var option0 = { elem: "#" + elearr[0], type: config.type, trigger: 'click', theme: 'molv', btns: ['confirm'] };
                    if (config.value0 != null) option0.value = config.value0;
                    var option1 = { elem: "#" + elearr[1], type: config.type, trigger: 'click', theme: 'molv', btns: ['confirm'] };
                    if (config.value1 != null) option1.value = config.value1;
                    if (config.format) { option0.format = option1.format = config.format; if (option.format == "HH:mm") { option0.ready = option1.ready = formatminutes; } }
                    option0.done = function (value, date) {
                        if (date && date.month && date.month != "NaN") {
                            date.month = date.month - 1;
                            data1.config.min = date;
                        } else {
                            var now = new Date();
                            date.year = now.getFullYear() - 1;
                            date.month = now.getMonth();
                            date.date = now.getDate();
                            date.hours = now.getHours();
                            date.minutes = now.getMinutes();
                            date.seconds = now.getSeconds();
                            data1.config.min = date;
                        }
                    };
                    option1.done = function (value, date) {
                        if (value != "" && value < data0.config.elem[0].value) {
                            var dtvalue = value;
                            if (config.type == "datetime")
                                dtvalue = new Date(dtvalue).Format("yyyy-MM-dd 23:59:59");
                            $(data0.config.elem[0]).val(dtvalue);
                            //data0.config.value = dtvalue;
                        }

                        if (date && date.month && date.month != "NaN") {
                            date.month = date.month - 1;
                            data0.config.max = date;

                        } else {
                            var now = new Date();
                            date.year = now.getFullYear() + 1;
                            date.month = now.getMonth();
                            date.date = now.getDate();
                            date.hours = now.getHours();
                            date.minutes = now.getMinutes();
                            date.seconds = now.getSeconds();
                            data0.config.max = date;
                        }
                    };

                    data0 = laydate.render(option0);
                    data1 = laydate.render(option1);
                } else {
                    var option = { elem: "#" + item, type: config.type, trigger: 'click' };
                    if (config.format) { option.format = config.format; if (option.format == "HH:mm") { option.ready = formatminutes; } }
                    if (config.min) { option.min = config.min; }
                    if (config.max) { option.max = config.max; }
                    laydate.render(option);
                }
            });
        } else {
            var option = { elem: "#" + elearr[0], type: config.type, trigger: 'click' };
            if (config.format) { option.format = config.format; if (option.format == "HH:mm") { option.ready = formatminutes; } }
            if (config.min) { option.min = config.min; }
            if (config.max) { option.max = config.max; }
            laydate.render(option);
        }
    }
}

function formatminutes(date) {
    $($(".laydate-time-list li ol")[2]).closest("li").remove(); //清空秒
}

/**
 *下拉框设置
 */
var dw_com_select = {
    //url: 获取数据
    //eleid: 下拉标签的id
    //data: 获取数据的条件参数
    //tmplid: 追加选项的jq模板id
    init: function (url, eleid, data, tmplid, callBack) {
        $.getJSON(url, data, function (json) {
            if (json.Success) {
                $("#" + eleid).append($("#" + tmplid).tmpl(json.Data));
                $("#" + eleid).trigger("chosen:updated");
                $("#" + eleid).change();
                if (callBack) callBack();
            }
        });
    }
}


/**
 * 输入框检索 
 * 输入框input必须包含：className:ipt-df-search, attr:df-href =>[数据源请求地址]
 * 数据源data数据格式 [{val,name}],val不能为空,name可以为空
 * attr: df-name-key =>[name关联的文本框id，非必需]
 * 实例：<input type="text" class="ipt-df-search" df-href="/Controller/Action" df-name-key="inputID" />
 */
var dw_ipt_search = {
    init: function () {
        this.addcss()
        $("input.ipt-df-search").each(function () {
            var ipte = $(this);
            ipte.bind("keyup", function () {
                var text = ipte.val()
                dw_ipt_search.close()
                if (text == '') return;

                var href = ipte.attr("df-href")
                $.post(href, { text: ipte.val() }, function (json) {
                    if (json.Success) {
                        var data = json.Data;
                        if (data.length > 0) {
                            var html = dw_ipt_search.htm(data)
                            ipte.parent().append(html);

                            dw_ipt_search.onclick()
                        }
                    }
                }, 'json');
            });
        })
    },
    addcss: function () {
        var csst = '.df-search { position: absolute; width: 100%; z-index: 100; background-color: #fff; box-shadow: 0 0 .5rem #bbb; max-height: 10rem; overflow: auto; }'
        csst += '.df-search li { display: flex; line-height: 1rem; padding: 0.5rem; border-bottom:1px solid #dfdfdf;}'
        csst += '.df-search li:last-child{border:0;}'
        csst += '.df-search li:active { background-color: #d2d2d2; color: red; }'
        csst += '.df-search li t { flex: 1; }'

        var style = document.createElement('style')
        style.innerText = csst
        $(document.head).append(style)
    },
    htm: function (data) {
        var h = '<div class="df-search">';
        h += '<ul>';
        for (var i = 0; i < data.length; i++) {
            if (data[i].name != null)
                h += '<li><t>' + data[i].val + '</t><t>' + data[i].name + '</t></li>';
            else
                h += '<li><t>' + data[i].val + '</t></li>';
        }
        h += '</ul>';
        h += '</div>';
        return h;
    },
    onclick: function () {
        $(".df-search li").unbind('click').click(function () {
            var ipte = $(this).parent().parent().parent().find('input.ipt-df-search')
            var text = $($(this).find('t')[0]).text()
            $(ipte).val(text)

            var namekey = $(ipte).attr('df-name-key')
            if (namekey != null && namekey != '') {
                if ($(this).find('t').length > 1) {
                    var name = $($(this).find('t')[1]).text()
                    $("#" + namekey).val(name)
                }
            }
            dw_ipt_search.close()
        });
    },
    close: function () {
        var dfsearch = $('.df-search')
        if (dfsearch != null)
            dfsearch.remove();
    }
}

/**
 * 字符串显示时，用*隐藏部分
 */
var dw_text_omit = {
    bind: function (text) {
        if (text == null || text == '') return "";
        var wb = null;
        if (text.indexOf('@') > 0) {
            wb = text.split('@')[1];
            text = text.split('@')[0];
        }

        if (text.length < 5) text = text;
        else if (text.length < 10)
            text = text.substr(0, 3) + "****" + text.substr(text.length - 2, 2);
        else {
            text = text.substr(0, 3) + "****" + text.substr(text.length - 4, 4);
        }

        if (wb != null)
            return text + '@' + wb;
        return text;
    }
}

function tempBar(state, text) {
    var bgClass = "";
    switch (state) {
        case 0:
            bgClass = "layui-bg-red";
            break;
        case 1:
            bgClass = "layui-bg-blue";
            break;
        case 2:
            bgClass = "layui-bg-green";
            break;
        case 3:
            bgClass = "layui-bg-orange";
            break;
        case 4:
            bgClass = "layui-bg-cyan";
            break;
        case 5:
            bgClass = "layui-bg-gray";
            break;
        case 6:
            bgClass = "layui-bg-purple";
            break;
        case 7:
            bgClass = "layui-bg-yellow";
            break;
        case 8:
            bgClass = "layui-bg-white";
            break;
        default:
            bgClass = "layui-bg-black";
            break;
    }

    return '<span class="layui-badge ' + bgClass + '">' + text + '</span>';
}
//根据状态返回对应的颜色标签
function tempBar1(state, text) {
    var color = "";
    //使用一些比较鲜艳的颜色用作状态标签背景色，以便于区分 状态值1-20,每个颜色不重复，每个颜色肉眼可分辨  case 1: color = "颜色值"; break;
    switch (state) {
        case 1:
            color = "#ff5722";
            break;
        case 2:
            color = "#1e9fff";
            break;
        case 3:
            color = "#16baaa";
            break;
        case 4:
            color = "#a233c6";
            break;
        case 5:
            color = "#16b777";
            break;
        case 6:
            color = "#31bdec";
            break;
        case 7:
            color = "#FF69B4"; // 热情粉红
            break;
        case 8:
            color = "#BA55D3"; // 深紫罗兰色
            break;
        case 9:
            color = "#FF00FF"; // 紫红色
            break;
        case 10:
            color = "#FF1493"; // 深粉红色
            break;
        case 11:
            color = "#7FFFD4"; // 浅蓝色
            break;
        case 12:
            color = "#D2691E"; // 巧克力色
            break;
        case 13:
            color = "#FF7F50"; // 珊瑚色
            break;
        case 14:
            color = "#6B8E23"; // 墨绿色
            break;
        case 15:
            color = "#ffb800";
            break;
        case 16:
            color = "#FFA07A"; // 浅珊瑚色
            break;
        case 17:
            color = "#FF6347"; // 鲜艳的番茄红
            break;
        case 18:
            color = "#FFA500"; // 橙色
            break;
        case 19:
            color = "#32CD32"; // 草绿色
            break;
        case 20:
            color = "#40E0D0"; // 天蓝色
            break;
        default:
            color = "#2f363c";
            break;
    }
    return '<span class="layui-badge" style="background-color:' + color + '">' + text + '</span>';
}

function tempImg(imgsrc) {
    var loadImg = imgsrc;
    if (imgsrc != null && imgsrc != '' && imgsrc != undefined && imgsrc.indexOf(".aliyuncs.com") < 0) {
        loadImg = PathCheck(decodeURIComponent(imgsrc));
    }
    var aHtml = '<a href="' + loadImg + '" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs">预览</a>';
    var nHtml = '<span class="layui-badge layui-bg-gray">无图片</span>';
    if (imgsrc != null && imgsrc != '' && imgsrc != undefined)
        return aHtml;
    return nHtml;
}
//检测并替换正确访问路径
function PathCheck(path) {
    if (path && path != '#') {
        var host = window.location.host;
        if (host.indexOf(":") == -1) {
            var idx = host.indexOf(".");
            var pre = host.substring(0, idx);
            pre = pre.substring(pre.length - 8);
            var main = host.substring(idx);
            var newhost = pre + main;
            var imghost = path.substring(path.indexOf("//") + 2);
            imghost = imghost.substring(0, imghost.indexOf("/"));
            path = path.replace(imghost, newhost);
        }
    } else path = '#';
    return path;
}

var win_open = function (url) {
    var a = document.getElementById("_openwindow");
    if (a == null) {
        a = window.document.createElement("a");
        a.id = "_openwindow";
        a.target = "_blank";
        a.style.display = "none";
    }
    a.href = url;
    window.document.body.appendChild(a);
    a.click();
}
var win_downlaod = function (url, name) {
    var a = document.getElementById("_openwindow");
    if (a == null) {
        a = window.document.createElement("a");
        a.id = "_openwindow";
        a.target = "_blank";
        a.style.display = "none";
    }
    a.href = url;
    a.download = name;
    window.document.body.appendChild(a);
    a.click();
}

var _tip_help = {
    data: [],
    setData: function (key, desc) {
        var item = { key: key, Description: desc };
        this.data[this.data.length] = item;
    },
    getHelpContent: function (key) {
        var data = {};
        for (var i = 0; i < this.data.length; i++) {
            if (key == this.data[i].key) {
                data = this.data[i];
                break;
            }
        }
        if (data.key == null) return null;
        return data;
    },
    init: function () {
        $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
            var key = $(this).attr("data-key");
            var data = _tip_help.getHelpContent(key);
            if (data) {
                layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
            }
        }, function () {
            layer.closeAll();
        });

        $(".help-btn-tip").unbind("click").click(function () {
            var key = $(this).attr("data-key");
            var data = _tip_help.getHelpContent(key);
            var htm = "<div style='margin:10px;'>" + data.Description[0] + "</div>";
            if (data) {
                layer.open({
                    type: 1,
                    title: "简要说明",
                    content: htm,
                    area: ["60%", "60%"],
                    shadeClose: true
                });
            }
        });
    }
}

/*
 *金额数值保留两位小数 
 */
var ToFixed2 = function (money) {
    if (money == null) return "0.00";
    if (isNaN(money)) return "0.00";
    //if (money == 0) return "";
    return parseFloat(money + "").toFixed(2);
}

//预览图片
var onPreviewImage = {
    //数据包格式：data-src=名称|路径,名称|路径  例：data-src='入场图片|http://xxx.33.jpg,出场图片|http://xxx.34.jpg'
    init: function () {
        $(".pm_img_preview").unbind("click").click(function () {
            try {
                var srcstr = $(this).attr("data-src");
                var arr = srcstr.split(',');
                var data = [];
                for (var i = 0; i < arr.length; i++) {
                    data[data.length] = {
                        Name: arr[i].split('|')[0],
                        Src: arr[i].split('|')[1]
                    }
                }
                var html = onPreviewImage.createHtml(data);
                var div = document.createElement("div");
                div.innerHTML = html;
                window.document.body.appendChild(div);

                $(".img_show_box .closeBtn").unbind("click").click(function () {
                    $(div).remove();
                });
                $(".img_show_box").unbind("click").click(function () {
                    $(div).remove();
                });

                onPreviewImage.createCss();
            } catch (e) {
                console.log("预览图片失败：" + e);
            }
        });
    },
    createHtml: function (imgSrcArrays) {
        var html = '<div class="img_show_box layui-anim layui-anim-downbit" title="点击关闭">'
        html += '<div class="closeBtn layui-icon layui-icon-close"></div>'
        html += '<div class="img_show_body">'
        for (var i = 0; i < imgSrcArrays.length; i++) {
            html += '<div class="img_show_item">'
                + '<img src="' + imgSrcArrays[i].Src + '" onerror="onPreviewImage.onError(this)" />'
                + '<text>' + imgSrcArrays[i].Name + '</text>'
                + '</div>';
        }
        html += '</div>'
        html += '</div>';
        return html;
    },
    createCss: function () {
        if ($("style#img_show_box") && $("style#img_show_box").length > 0) return;

        var css = '.img_show_box { position: fixed; top: 0; right: 0; bottom: 0; left: 0; background-color: rgba(0,0,0,0.7); z-index:999;padding:80px;}'
            + '.img_show_box .closeBtn { position: absolute; right: 0; top: 0; width: 40px; height: 40px; background-color: rgba(0,0,0,0.5); color: #fff; line-height: 40px; text-align: center; font-size: 16px; cursor: pointer; }'
            + '.img_show_body { display: flex; margin:0 auto; width: 100%; height: 100%; max-width: 1366px; max-height: 768px; }'
            + '.img_show_body .img_show_item { flex: 1; position: relative; text-align: center; }'
            + '.img_show_body .img_show_item img { position: relative; width: 100%; height: 100%; display: inline-block; background: rgba(0,0,0,0.9); }'
            + '.img_show_body .img_show_item text { height: 30px; line-height: 30px; width: 100%; float: left; clear: both; position: absolute; top: 0; z-index: 2; right: 10px; text-align: right; color: #fff; }';

        var style = document.createElement("style");
        style.id = "img_show_box";
        style.innerText = css;
        window.document.head.appendChild(style);
    },
    onError: function (e) {
        e.src = '../../Static/img/nophoto5x3.jpg';
        //layer.msg("加载失败：图片路径访问失败", { icon: 0 });
        //$(".img_show_box .closeBtn").click();
    }
}

//检测并替换正确访问路径
function PathCheck(path) {
    if (path && path != '#') {
        if (path.indexOf("..") > -1) {
            return path;
        }
        var host = window.location.host;
        if (host.indexOf(":") == -1) {
            var idx = host.indexOf(".");
            var pre = host.substring(0, idx);
            pre = pre.substring(pre.length - 8);
            var main = host.substring(idx);
            var newhost = pre + main;
            var imghost = path.substring(path.indexOf("//") + 2);
            imghost = imghost.substring(0, imghost.indexOf("/"));
            path = path.replace(imghost, newhost);
        }
    }
    else path = '';
    return path;
}

var img_reader = {
    getBase64: function (imgUrl, callBack) {
        window.URL = window.URL || window.webkitURL;
        const xhr = new XMLHttpRequest();
        xhr.open("get", imgUrl, true);
        xhr.responseType = "blob";
        xhr.onload = () => {
            if (xhr.status == 200) {
                const blob = xhr.response;
                const reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = e => {
                    const base64 = e.target.result;
                    callBack(base64);
                };
            }
        };
        xhr.send();
    }
}

function imgtool() {
    // 图片列表，避免全局污染
    var imgList = {};

    // 检测图片链接是否有效
    this.isImageValid = function (url, callback) {
        try {
            if (url) {
                var img = new Image();
                img.onload = function () {
                    callback(true);
                };
                img.onerror = function () {
                    callback(false);
                };
                img.src = url;
            } else {
                callback(false);
            }
        } catch (e) {
            callback(false);
        }
    };

    // 检测图片链接是否有效, 若无效则延迟重新检测，最多检测20次
    this.imageLoad = function (no, url, index, img, call) {
        // 防止重复添加
        if (imgList[no]) {
            clearTimeout(imgList[no].timeout);  // 清理之前的定时器
        }

        imgList[no] = { url: url, call: call, img: img, retries: index };

        var that = this;
        if (imgList[no] && imgList[no].url) {
            this.isImageValid(imgList[no].url, function (isValid) {
                if (isValid) {
                    imgList[no].call();
                    delete imgList[no];  // 加载成功后清除该项
                } else {
                    if (imgList[no].retries >= 3) { // 超过最大重试次数，延迟长一点
                        setTimeout(function () {
                            imgList[no].call(); // 调用回调，确保最终的逻辑
                            delete imgList[no]; // 清除 imgList 项
                        }, 3000);
                    } else {
                        imgList[no].retries += 1;
                        imgList[no].timeout = setTimeout(function () {
                            that.imageLoad(no, imgList[no].url, imgList[no].retries, imgList[no].img, imgList[no].call);
                        }, 500);
                    }
                }
            });
        }
    };

    // 检查图片是否正常显示
    this.isImageDisplayed = function (imgElement) {
        return imgElement.complete && imgElement.naturalWidth !== 0;
    };

    // 清理方法：清除所有相关的状态和定时器
    this.clear = function () {
        // 清除所有的定时器
        for (var key in imgList) {
            if (imgList.hasOwnProperty(key)) {
                clearTimeout(imgList[key].timeout); // 清除定时器
                delete imgList[key]; // 清除 imgList 中的条目
            }
        }
    };
}


//查询条件优化
var topBar = {
    config: {
        SearchType: 0 //左侧快捷导航选中值
    },
    //初始化, callBack-点击快捷导航触发事件回调,包含参数key-选中值
    init: function (callBack) {
        var style = '<style>.layui-card-header.topbar { overflow: auto; padding: 5px 15px 0 !important; user-select: none; }'
            + '.fastsearch { overflow: auto; display: flex; user-select: none; }'
            + '.fastsearch ul:nth-child(n+2) { margin-left:10px;}'
            + '.fastsearch ul li { line-height: 42px; margin: 0 8px; float: left; cursor: pointer; position:relative;}'
            + '.fastsearch ul li { color: #000; }'
            + '.fastsearch ul li:hover { color: #55a1ff; }'
            + '.fastsearch ul li.select { color: #55a1ff; border-bottom: 2px solid #55a1ff;  }'
            + '.fastsearch ul li t { display: none; float:right;}'
            + '.fastsearch ul li.select t { display: block; }'
            + '.operabar { float: right; }'
            + '.operabar-if { padding: 0 10px; cursor: pointer; }'
            + '.operabar-if::after { content: "▲"; color:#999; }'
            + '.operabar-if.on::after { content: "▲"; color: #999; display: inline-block; transform: rotate(180deg); -webkit-transform: rotate(180deg); -moz-transform: rotate(180deg); -o-transform: rotate(180deg); -ms-transform: rotate(180deg); }'
            + '#searchForm .layui-select-disabled .layui-disabled { color:#000 !important; }'
            + '.search-more { padding: 15px 10px !important;background: #f2f3f3; }</style>';
        document.head.innerHTML += style;

        $(".fastsearch li").click(function () {
            var that = $(this);
            var key = that.attr("data-key");

            that.siblings().removeClass("select");
            if (!that.hasClass("select")) {
                that.addClass("select");
                topBar.config.SearchType = key;

                if (callBack)
                    callBack(key);
            }
        });

        $(".operabar-if").click(function () {
            var that = $(this);
            if (that.hasClass("on")) {
                that.removeClass("on");
                $(".search-more").removeClass("layui-hide").addClass("layui-hide");
            } else {
                that.addClass("on");
                $(".search-more").removeClass("layui-hide");
            }
        });
    },
    //设置查询结果总数量
    set: function (count) {
        $(".fastsearch li[data-key='" + topBar.config.SearchType + "'] t").text("(" + count + ")");
    },
    onselect: function (searchType, disabled, elemid) {
        var that = $(".fastsearch li[data-key='" + (searchType || "") + "']");
        that.siblings().removeClass("select");
        if (!that.hasClass("select")) {
            that.addClass("select");
            topBar.config.SearchType = searchType;
        }

        if (disabled && elemid) {
            $("#" + elemid).val(searchType);
            if (searchType != "") $("#" + elemid).attr("disabled", true);
            else $("#" + elemid).removeAttr("disabled");
            layui.form.render();
        }
    }
}
function getLastDayOfMonth(year, month) {
    // 构造下个月的第一天，然后减去一天，即为本月的最后一天
    const nextMonthDate = new Date(year, month + 1, 1);
    const lastDay = new Date(nextMonthDate - 1);
    return lastDay.getDate();
}

function isValidDate(dateString) {
    //判断时间不能是 0001-01-01 00:00:00
    if (dateString === "0001-01-01 00:00:00") {
        return false;
    }

    // 尝试解析日期
    var date = new Date(dateString);

    // 使用isNaN检查日期是否无效
    // 如果日期有效并且解析成功，则不是NaN
    return !isNaN(date.getTime()) && date.getFullYear() != 1; // 有效
}
//设备型号
var DeviceTypeName = {
    device11List: ["S1211", "S1211E", "ZNYKT11"],
    device06List: ["S1206", "S1206TD", "S1206A", "S1206MD", "ZNYKT06", "S1206FD", "Z36FD", "Z56FD", "S6", "Z6"],
    device15List: ["S1215", "1215HX", "S1215HM", "S1205HC", "S1215HR", "ZNYKT15", "Z35HR", "Z55R", "Z15HR", "ZS-R3", "ZS-R5", "S5", "Z5"],
}
