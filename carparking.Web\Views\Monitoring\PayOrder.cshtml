﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>缴费记录查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 0px 4px 0 0; float: left; }
        .layui-form-select .layui-input { width: 182px; font-size: 1rem; }
        .layui-btn { line-height: normal !important; padding: 0 12px; font-size: 1rem !important; }
        .layui-bg-wxgreen { background-color: #04BE02 !important; }
        .layui-bg-alipayblue { background-color: #1678ff !important; }
        .layui-bg-ylblue { background-color: #1678ff !important; }
        .layui-fluid { padding: 0; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; }
        ::-webkit-scrollbar-thumb { border-radius: 10px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }
        .layui-tab-title { background-color: #5868e0 !important; }
        .layui-input i { border-color: #ddd !important; font-size: .9rem; }
        .layui-table-cell { font-size: .9rem; }
        .total-fd { white-space: normal; word-break: break-all; word-wrap: break-word; overflow-wrap: break-word; height: auto; line-height: normal; }

        .layui-table-click { background-color: #f2f3f3; color: #2F4056; font-weight: bold; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">

                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_CarNo" id="PayOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>

                            <div class="layui-inline">
                                <select data-placeholder="订单类型" class="layui-select" id="PayOrder_OrderTypeNo" name="PayOrder_OrderTypeNo" lay-search>
                                    <option value="">订单类型</option>
                                    <option value="5901">临停车缴费</option>
                                    <option value="5902">月租车充值</option>
                                    <option value="5919">月租车缴费</option>
                                    <option value="5903">储值车充值</option>
                                    @*<option value="5904">商家自助充值</option>*@
                                    <option value="5905">储值车扣费</option>
                                    <option value="5910">车位续期</option>
                                    <option value="0">其他</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_PayedTime0" id="PayOrder_PayedTime0" autocomplete="off" placeholder="支付时间起" value="@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_PayedTime1" id="PayOrder_PayedTime1" autocomplete="off" placeholder="支付时间止" />
                            </div>


                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_No" id="PayOrder_No" autocomplete="off" placeholder="支付订单号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_ParkOrderNo" id="PayOrder_ParkOrderNo" autocomplete="off" placeholder="停车订单号" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付状态" class="layui-select" id="PayOrder_Status" name="PayOrder_Status" lay-search>
                                    <option value="">支付状态</option>
                                    <option value="0">未支付</option>
                                    <option value="1">支付成功</option>
                                    <option value="2">支付失败</option>
                                    <option value="3">用户取消</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="支付方式" class="layui-select" id="PayOrder_PayTypeCode" name="PayOrder_PayTypeCode" lay-search>
                                    <option value="">支付方式</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="layui-select" id="PayOrder_CarCardTypeNo" name="PayOrder_CarCardTypeNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="layui-select" id="PayOrder_CarTypeNo" name="PayOrder_CarTypeNo" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="操作员" class="layui-input" id="PayOrder_Account" name="PayOrder_Account" lay-search>
                                    <option value="">操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="PayOrder_OperatorName" id="PayOrder_OperatorName" autocomplete="off" placeholder="操作员" />
                            </div>

                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" lay-event="Detail"><i class="fa fa-list-alt"> 详情</i></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplimg">
        <a href="{{d.CarRecog_Img}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs" title="点击查看"><i class="layui-icon layui-icon-picture"></i>预览</a>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.PayOrder_Status==0){}}
        <span class="layui-badge layui-bg-orange">未支付</span>
        {{# }else if(d.PayOrder_Status==1){ }}
        <span class="layui-badge layui-bg-green">支付成功</span>
        {{# }else if(d.PayOrder_Status==2){ }}
        <span class="layui-badge layui-bg-red">支付失败</span>
        {{# }else if(d.PayOrder_Status==3){ }}
        <span class="layui-badge layui-bg-cyan">用户取消</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplordertype">
        {{# if(d.PayOrder_OrderTypeNo==5902){}}
        <span class="layui-badge layui-bg-blue">月租车充值</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5901){ }}
        <span class="layui-badge layui-bg-green">临停车缴费</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5903){ }}
        <span class="layui-badge layui-bg-cyan">储值车充值</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5904){ }}
        <span class="layui-badge layui-bg-orange">商家自助充值</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5905){ }}
        <span class="layui-badge layui-bg-black">储值车扣费</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5910){ }}
        <span class="layui-badge layui-bg-green">车位续期</span>
        {{# }else if(d.PayOrder_OrderTypeNo==5919){ }}
        <span class="layui-badge layui-bg-blue">月租车缴费</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-gray">其他</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpaytypecode">
        {{# if(d.PayOrder_PayTypeCode==79001){}}
        <span class="layui-badge layui-bg-red">线下现金支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79002){ }}
        <span class="layui-badge layui-bg-red">平台现金支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79003){ }}
        <span class="layui-badge layui-bg-wxgreen">微信支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79010){ }}
        <span class="layui-badge layui-bg-wxgreen">线下微信</span>
        {{# }else if(d.PayOrder_PayTypeCode==79013){ }}
        <span class="layui-badge layui-bg-wxgreen">微信无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79007){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79011){ }}
        <span class="layui-badge layui-bg-alipayblue">线下支付宝</span>
        {{# }else if(d.PayOrder_PayTypeCode==79012){ }}
        <span class="layui-badge layui-bg-alipayblue">支付宝无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79004){ }}
        <span class="layui-badge layui-bg-cyan">Android端支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79006){ }}
        <span class="layui-badge layui-bg-cyan">终端设备支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79009){ }}
        <span class="layui-badge layui-bg-cyan">第三方支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79014){ }}
        <span class="layui-badge layui-bg-ylblue">建行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79015){ }}
        <span class="layui-badge layui-bg-ylblue">招行一网通支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79016){ }}
        <span class="layui-badge layui-bg-ylblue">银联无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79017){ }}
        <span class="layui-badge layui-bg-ylblue">建行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79018){ }}
        <span class="layui-badge layui-bg-ylblue">威富通聚合支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79019){ }}
        <span class="layui-badge layui-bg-ylblue">招行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79020){ }}
        <span class="layui-badge layui-bg-ylblue">工行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79021){ }}
        <span class="layui-badge layui-bg-ylblue">工行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79022){ }}
        <span class="layui-badge layui-bg-ylblue">农行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79023){ }}
        <span class="layui-badge layui-bg-ylblue">农行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79024){ }}
        <span class="layui-badge layui-bg-ylblue">ETC支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79025){ }}
        <span class="layui-badge layui-bg-ylblue">中行支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79026){ }}
        <span class="layui-badge layui-bg-ylblue">中行无感支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79027){ }}
        <span class="layui-badge layui-bg-ylblue">乐聚合支付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79039){ }}
        <span class="layui-badge layui-bg-ylblue">随行付</span>
        {{# }else if(d.PayOrder_PayTypeCode==79028){ }}
        <span class="layui-badge layui-bg-ylblue">银联商务</span>
        {{# }else if(d.PayOrder_PayTypeCode==79029){ }}
        <span class="layui-badge layui-bg-ylblue">充电抵扣</span>
        {{# }else if(d.PayOrder_PayTypeCode==80002){ }}
        <span class="layui-badge layui-bg-ylblue">自助缴费</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?v202312191750" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?4.0" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js"></script>
    <script>
        topBar.init();

        /*
         *金额数值保留两位小数
         */
        var ToFixed2 = function (money) {
            if (money == null) return "";
            if (isNaN(money)) return "";
            //if (money == 0) return "";
            return parseFloat(money + "").toFixed(2);
        }

        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        layui.use(['table', 'form', 'laydate', 'element'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            pager.init();
            layuiForm.render("select");

            searchFile.bindData(1);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'PayOrder_ID', title: 'ID', hide: true }
                , { field: 'PayOrder_No', title: '支付订单号', hide: true, minWidth: 100 }
                , { field: 'PayOrder_ParkOrderNo', title: '停车订单号', hide: true, minWidth: 100 }
                , { field: 'PayOrder_OrderTypeNo', title: '订单类型', toolbar: "#tmplordertype", width: 105 }
                , { field: 'PayOrder_CarNo', title: '车牌号', minWidth: 120 }
                , { field: 'PayOrder_OwnerSpace', title: '系统车位号', hide: true, minWidth: 100 }
                , { field: 'PayOrder_CarCardTypeNo', title: '车牌类型', minWidth: 100 }
                , { field: 'PayOrder_CarTypeNo', title: '车牌颜色', minWidth: 100 }
                , { field: 'PayOrder_OwnerNo', title: '车主编号', hide: true, minWidth: 100 }
                , { field: 'PayOrder_OwnerName', title: '车主姓名', hide: true, minWidth: 100 }
                , { field: 'PayOrder_Money', title: '应收金额', totalRow: true, minWidth: 120, templet: function (d) { return ToFixed2(d.PayOrder_Money); }, sort: true }
                , { field: 'PayOrder_PayedMoney', title: '实收金额', totalRow: true, minWidth: 120, templet: function (d) { return ToFixed2(d.PayOrder_PayedMoney); }, sort: true }
                , { field: 'PayOrder_DiscountMoney', title: '优惠金额', totalRow: true, minWidth: 120, templet: function (d) { return ToFixed2(d.PayOrder_DiscountMoney); }, sort: true }
                , { field: 'PayOrder_StoredMoney', title: '储值金额抵扣', totalRow: true, minWidth: 120, templet: function (d) { return ToFixed2(d.PayOrder_StoredMoney); } }
                , {
                    field: 'PayOrder_SelfMoney', title: '自助缴费金额', hide: true, totalRow: true, minWidth: 150, templet: function (d) {
                        if (d.PayOrder_PayTypeCode == 80002 || d.PayOrder_SelfMoney > 0) {
                            var btn = ToFixed2(d.PayOrder_SelfMoney) + '<div class="layui-btn layui-btn-xs btnCashDetail" style="padding:3px;margin-left:10px;" data-no="' + d.PayOrder_ParkOrderNo + '">查看明细</div>';
                            return btn;
                        } else {
                            return ToFixed2(d.PayOrder_SelfMoney);
                        }
                    }
                }
                //, { field: 'PayOrder_SelfMoney', title: '自助缴费金额', totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_SelfMoney); } }
                , { field: 'PayOrder_OutReduceMoney', hide: true, title: '找零金额', minWidth: 100, totalRow: true, templet: function (d) { return ToFixed2(d.PayOrder_OutReduceMoney); } }
                , { field: 'PayOrder_Status', title: '支付状态', toolbar: "#tmplstatus", minWidth: 100 }
                , { field: 'PayOrder_EnterTime', title: '入场时间', sort: true, hide: true, minWidth: 185 }
                , { field: 'PayOrder_PayedTime', title: '支付时间', sort: true, minWidth: 185 }
                , { field: 'PayOrder_PayTypeCode', title: '支付方式', toolbar: "#tmplpaytypecode", minWidth: 120 }
                , { field: 'PayOrder_TempTimeCount', title: '停车时长', minWidth: 160, templet: function (d) { return _DATE.getZhTimesbyMin(Math.ceil(d.PayOrder_TempTimeCount || 0)); } }
                , { field: 'PayOrder_TimeCountDesc', title: '时长描述', hide: true, minWidth: 160 }
                , { field: 'PayOrder_Desc', title: '支付描述', minWidth: 120 }
                , { field: 'PayOrder_OperatorName', title: '操作员', minWidth: 100 }

            ]];
            cols = tb_page_cols(cols, "PayOrder");

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetPayOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data, "PayOrder");
                    if (data.code == 0) {
                        //追加总计行，需要查询时在msg中返回总计数据
                        var total = JSON.parse(data.msg);
                        if (total) {
                            var tr = $(".layui-table-total table tbody").find("tr").first();
                            tr.find('td[data-key="1-0-0"] div').text("合计");
                            $(".layui-table-total table tbody").append('<tr>' + tr.html() + '</tr>');
                            tr = $(".layui-table-total table tbody").find("tr").last();
                            var hjTr = tr.prev();
                            hjTr.find('td[data-field="PayOrder_SelfMoney"] div').find('div').remove();
                            tr.find('td[data-key="1-0-0"] div').text("总计").css({ "color": "red" });
                            tr.find('td[data-field="PayOrder_Money"] div').text(parseFloat(total.PayOrder_Money).toFixed(2)).css({ "color": "red" }).addClass("total-fd");
                            tr.find('td[data-field="PayOrder_PayedMoney"] div').text(parseFloat(total.PayOrder_PayedMoney).toFixed(2)).css({ "color": "red" }).addClass("total-fd");
                            tr.find('td[data-field="PayOrder_DiscountMoney"] div').text(parseFloat(total.PayOrder_DiscountMoney).toFixed(2)).css({ "color": "red" }).addClass("total-fd");
                            tr.find('td[data-field="PayOrder_StoredMoney"] div').text(parseFloat(total.PayOrder_StoredMoney).toFixed(2)).css({ "color": "red" }).addClass("total-fd");
                            tr.find('td[data-field="PayOrder_SelfMoney"] div').text(parseFloat(total.PayOrder_SelfMoney).toFixed(2)).css({ "color": "red" }).addClass("total-fd");
                            tr.find('td[data-field="PayOrder_OutReduceMoney"] div').text(parseFloat(total.PayOrder_OutReduceMoney).toFixed(2)).css({ "color": "red" }).addClass("total-fd");
                        }
                    }
                    $(".btnCashDetail").unbind("click").click(function () {
                        var orderNo = $(this).attr("data-no");
                        layer.open({
                            type: 2,
                            title: "自助缴费明细 - " + orderNo,
                            content: "CashDetail?orderNo=" + orderNo,
                            area: ['60%', '60%'],
                            fix: true, //不固定
                            maxmin: false
                        })
                    });
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var orderno = data[0].PayOrder_No;
                        layer.open({
                            title: "<i class='fa fa-list-alt' style='margin-top: 17px;'></i> 支付详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'PayOrderDetail?PayOrder_No=' + encodeURIComponent(orderno)
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });
            // 监听行点击事件
            table.on('row(com-table-base)', function (obj) {
                // 移除所有行的选中样式
                obj.tr.siblings().removeClass('layui-table-click');
                // 添加当前行的选中样式
                obj.tr.addClass('layui-table-click');
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["PayOrder_PayedTime0", "PayOrder_PayedTime1"], { type: "datetime", range: true });

                $.post("SltCarCardTypeList2", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#PayOrder_CarCardTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#PayOrder_CarTypeNo").append(option)
                        });
                    }
                }, "json");

                $.post("SltPayTypeList", {}, function (json) {
                    if (json.success) {
                        $("#PayOrder_PayTypeCode").html(' <option value="">支付方式</option>');
                        json.data.forEach((item, index) => {
                            if (item.PayType_Enable == 1) {
                                var option = '<option value="' + item.PayType_No + '">' + item.PayType_Name + '</option>';
                                $("#PayOrder_PayTypeCode").append(option);
                            }
                        });
                    }
                }, "json");

                $.post("SltAdminList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Admins_Account + '">' + d.Admins_Name + '</option>';
                            $("#PayOrder_Account").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/Monitoring/GetPayOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                s_carno_picker.init("PayOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "PayOrder_CarNo") {
                        $("#PayOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }
    </script>
</body>
</html>
