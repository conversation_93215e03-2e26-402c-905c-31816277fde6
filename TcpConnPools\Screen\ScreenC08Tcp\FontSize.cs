namespace TcpConnPools.Screen.ScreenC08Tcp;

/// <summary>
/// 字体大小枚举
/// 对应协议文档中的文字大小定义
/// </summary>
public enum FontSize : byte
{
    /// <summary>
    /// 16点阵 (GB2312字模: 16X16, ASCII字模: 16X8)
    /// </summary>
    Size16 = 16,

    /// <summary>
    /// 24点阵 (GB2312字模: 24X24, ASCII字模: 24X16)
    /// </summary>
    Size24 = 24,

    /// <summary>
    /// 32点阵 (GB2312字模: 32X32, ASCII字模: 32X16)
    /// </summary>
    Size32 = 32,

    /// <summary>
    /// 48点阵 (GB2312字模: 48X48, ASCII字模: 48X24)
    /// </summary>
    Size48 = 48,

    /// <summary>
    /// 64点阵 (GB2312字模: 64X64, ASCII字模: 64X32)
    /// </summary>
    Size64 = 64,
}
