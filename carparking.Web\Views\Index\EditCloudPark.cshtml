﻿@using TcpConnPools.Camera
@using carparking.DirectCloudMQTT

<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title></title>
	<meta name="keywords" content="">
	<meta name="description" content="">
	<link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
	<link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
	<link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
	<link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
	<link href="~/Static/css/com.ui.css" rel="stylesheet" />
	<style>
		html, body { width: 100%; height: 100%; padding: 0; margin: 0; }
		.layui-row { margin-bottom: 15px; }
		.m-label { padding: 1px 0 0 1px; font-weight: bold; }
		.padding-15 { padding: 1rem; }
		.pan-title { font-size: 1.5rem; }
		.layui-card { box-shadow: none; }
		.parkmsg { margin-bottom: 10px; }
		.parkrow { padding-left: 60px; }
		/* .layui-layer-dialog .layui-layer-content { padding: 1px !important; } */

		.headDiv { display: inline-block; }
		.parkstatus .status { height: 35px !important; line-height: 35px !important; width: 112px !important; font-size: 14px !important; }
		.status button { background: #1E9FFF !important; border-radius: 4px; color: #fff; padding: 0px 8px 1px; height: 35px !important; line-height: 35px !important; width: 112px !important; }

		#successMessage { height: 100%; }
		.successp { font-size: 1.8rem; font-weight: 600; color: #fff; text-align: center; background-color: #37cf1e; padding: 1rem; }
		.device-list { max-height: 180px; overflow-y: auto; }
		.device-item { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #e2e2e2; }
		.device-status { font-weight: bold; }
		.success { color: green; }
		.fail { color: red; }

		.layui-layer-title { font-weight: 600; }

		.resend-btn { position: absolute; bottom: 0; background: #f2f2f2; text-align: center; width: 100%; padding: 10px 0px; }
		.resend-btn button { background-color: #ff5722; color: white; border: none; padding: 10px 20px; cursor: pointer; border: 0; margin-right: 10px; border-radius: 4px; }
		.resend-btn button:hover { background-color: #e64a19; }
		.resend-btn button:disabled { background-color: #bbb5b3; }
		#Cancel2 { background-color: #FFB800 !important; }

	</style>
</head>
<body>
	<div style="overflow:hidden;height:0;">
		<!--防止浏览器保存密码后自动填充-->
		<input type="password" />
		<input type="text" />
		<input type="text" name="email" />
	</div>
	<div class="layui-card">
		<div class="layui-card-body layui-form" id="verifyCheck">
			<div class="layui-row">
				<div class="layui-col-xs3 m-label">停车场名称</div>
				<div class="layui-col-xs8 edit-ipt-ban">
					<input type="text" class="layui-input v-null" maxlength="14" id="Parking_Name" name="Parking_Name" autocomplete="off" />
				</div>
			</div>
			<div class="layui-row">
				<div class="layui-col-xs3 m-label">云平台</div>
				<div class="layui-col-xs8 edit-ipt-ban">
					<div class="btnCombox falsemodify" id="Parking_EnableNet">
						<ul class="flex">
							<li data-value="1">启用</li>
							<li data-value="0" class="select">禁用</li>
						</ul>
					</div>
				</div>
			</div>
			<div class="enablenet layui-hide">
				<div class="layui-row">
					<div class="layui-col-xs3 m-label">设备SN</div>
					<div class="layui-col-xs8 edit-ipt-ban">
						<input type="text" class="layui-input v-numen" autocomplete="off" value="@ViewBag.DeviceSN" readonly />
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-xs3 m-label">平台模式</div>
					<div class="layui-col-xs8 edit-ipt-ban">
						<div class="btnCombox falsemodify" id="Parking_Mode">
							<ul class="flex">
								<li data-value="1">测试模式</li>
								<li data-value="3">预发布模式</li>
								<li data-value="2" class="select">正式模式</li>
							</ul>
						</div>
					</div>
				</div>
				<div class="layui-row cloudboxlpremergency">
					<div class="layui-col-xs3 m-label">应急模式</div>
					<div class="layui-col-xs8 m-label">
						<div class="headDiv parkstatus" id="SysConfig_CloudBoxLprEmergency">未知</div>
						<div class="headDiv status"> <button class=" changemode">切换模式</button></div>
					</div>
				</div>
			</div>
			<div class="layui-row connMode">
				<div class="layui-col-xs3 m-label">车场模式</div>
				<div class="layui-col-xs8 m-label">
					<div class="headDiv parkstatus" id="SysConfig_ConnMode">未启用</div>
				</div>
			</div>

			<div class="hr-line-dashed"></div>
			<div class="layui-row">
				<div class="layui-col-xs3 edit-label">&nbsp;</div>
				<div class="layui-col-xs8 edit-ipt-ban">
					<button class="btn btn-primary" id="SavePark"><i class="fa fa-check"></i> <t>保存</t></button>
					<button class="btn btn-warning" id="Cancel"><i class="fa fa-close"></i> <t>取消</t></button>
				</div>
			</div>
			
		</div>
	</div>

	<div id="successMessage" style="display: none;">
		<p class="successp">
			操作成功！
		</p>

		<div class="device-list">
			<!-- 设备列表将动态插入这里 -->
		</div>
		<div class="resend-btn">
			<button id="resendFailed">失败重发</button>
			<button id="Cancel2">关闭</button>
		</div>
	</div>
	<script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
	<script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
	<script src="~/Static/js/pm.utils.js?1" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
	<script type="text/x-jquery-tmpl" id="park_online">
		{{if online==1 }}
		<label class="layui-badge layui-bg-blue status">已连接</label>
		{{else online==2 }}
		<label class="layui-badge status">未启用</label>
		{{else online==-1 }}
		<label class="layui-badge status">未知</label>
		{{else}}
		<label class="layui-badge status">未连接</label>
		{{/if}}
	</script>
	<script>
		myVerify.init();
		layui.use(['table', 'jquery', 'form'], function () {
			pager.init()
		});

		var parking = @Html.Raw(ViewBag.Park);
	</script>
	<script>
		var paramAct = $.getUrlParam("Act");
		var paramNo = $.getUrlParam("Parking_No");
		var parkmode = '@ViewBag.SentryMode';
		var sentryMode = @(MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0);
		var emergencyMode = @(MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0);
		var parkIndex = null;

		var index = parent.layer.getFrameIndex(window.name);
		//parent.layer.iframeAuto(index); //弹层自适应大小

		var pager = {
			deviceSend:[],
			init: function () {
				$.ajaxSettings.async = false; //同步执行
				this.bindPower();
				this.bindData();
				this.bindEvent();
				$.ajaxSettings.async = true;
				pager.getParkStatus();
			},
			//数据绑定
			bindData: function () {
				$("#verifyCheck").fillForm(parking, function (data) { });
				LoadDeviceConfig(parking)
			},
			bindEvent: function () {
				$("#Cancel").click(function () {
					window.parent.layer.closeAll();
				})

				$("#SavePark").click(function () {
					if (!myVerify.check()) return;
					layer.msg('<div style="padding: 20px; line-height: 22px; background-color: #fff; padding-left:52px;">正在处理...</div>', { icon: 16, time: 0 });

					var param = $("#verifyCheck").formToJSON(true, function (data) {
						return data;
					});

					param.Parking_EnableNet = config.Parking_EnableNet;
					param.Parking_Mode = config.Parking_Mode;

					$("#SavePark").attr("disabled", true);
					param.Parking_No = paramNo;
					if (param.Parking_EnableNet == 1) {
						LAYER_OPEN_TYPE_0('<div style="padding: 20px; line-height: 22px; background-color: #fff; padding-left:52px;">确定启用云平台吗？</div>', res => {
							LAYER_LOADING('<div style="padding: 20px; line-height: 22px; background-color: #fff; padding-left:52px;">正在保存...</div>');
							$.post("/Index/SaveParking", { jsonModel: JSON.stringify(param) }, function (json) {
								$("#SavePark").removeAttr("disabled")
								if (json.success) {
									layer.msg('<div style="padding: 20px; line-height: 22px;">保存成功</div>', { time: 1000 }, function () { window.parent.closeParkWin(); });
								} else {
									$("#SavePark").removeAttr("disabled")
									layer.msg(json.msg);
								}
							}, "json");
						}, res => {
							$("#SavePark").removeAttr("disabled");
							layer.closeAll();
						})
					} else {
						layer.closeAll();
						var content = '<div style="padding: 30px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">确定禁用车场云平台吗?</div>';
						LAYER_OPEN_TYPE_0(content, res => {
							LAYER_LOADING('<div style="padding: 20px; line-height: 22px; background-color: #fff; padding-left:52px;">正在处理...</div>');
							$.post("/Index/SaveParking", { jsonModel: JSON.stringify(param) }, function (json) {
								$("#SavePark").removeAttr("disabled")
								if (json.success) {
									layer.msg('<div style="padding: 20px; line-height: 22px;">保存成功</div>', { time: 1500 });

									window.parent.location.reload();
								} else {
									$("#SavePark").removeAttr("disabled")
									layer.msg(json.msg);
								}
							}, "json");
						}, res => {
							layer.closeAll();
							$("#SavePark").removeAttr("disabled")
						})
					}
				});

				$(".changemode").click(function () {
					var msg = "";
					if (emergencyMode == 1) {
						msg = "当前已启用应急模式，确定关闭吗？";
					} else {
						msg = "当前未启用应急模式，确定启用吗？";
					}
					layer.open({
						type: 0,
						title: "消息提示",
						btn: ["确定", "取消"],
						content: msg,
						area: ["300px"],
						yes: function (res) {
							layer.msg("切换中", { icon: 16, time: 0 });
							setTimeout(function () {
								$.post("SwitchParkingMode", {}, function (json) {
									if (json.success) {
										if (sentryMode == 1) sentryMode = 0; else sentryMode = 1;
										$("#SysConfig_CloudBoxLprEmergency").html(sentryMode == 1 ? '<label class="layui-badge layui-bg-blue status">已开启</label>' : '<label class="layui-badge layui-bg-cyan status">已关闭</label>');
										if (json.data && json.data != null && json.data.length > 0) {

											pager.deviceSend = [];

											var msg = (sentryMode == 1 ? "已开启" : "已关闭") + "车场应急模式";
											$(".successp").html(msg);
											// 显示操作成功提示
											layer.open({
												type: 1,
												title: '为了不影响使用，请等待设备消息发送成功后再操作',
												content: $('#successMessage'),
												area: ['450px', '350px']
											});

											var devices = json.data;
											var $deviceList = $('.device-list');
											$deviceList.empty(); // 清空列表

											// 动态生成设备列表项
											devices.forEach(function (device) {
												var $item = $('<div class="device-item"></div>');
												$item.append('<span class="device-name">' + device.Device_IP + '</span>');
												$item.append('<span class="device-name">' + device.Device_Name + '</span>');
												var $status = $('<span data-value="' + device.Device_No + '" class="device-status">正在发送消息...</span>');
												$item.append($status);
												$deviceList.append($item);
											});

											setTimeout(async function () {
												$.ajaxSettings.async = false;
												const deviceNosString = devices.map(device => device.Device_No).join(',');
												$.post("SendDeviceEnableClouds", { deviceNos: deviceNosString, parkingMode: sentryMode }, function (json) {
													if (json.success) {
														_ = pager.checkAllStatus(devices)
													} else {
														pager.deviceSend = devices;
														$(".device-status").text('消息发送失败').removeClass("success").addClass('fail');
													}
												});

											}, Math.random() * 2000 + 1000); // 随机时间1-3秒

										} else {
											layer.msg("切换成功", { icon: 1, time: 1500 }, function () { });
										}
									} else
										layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
								});
							}, 10);
						},
						btn2: function () { }
					})
				});

				$('#Cancel2').on('click', function () {
					layer.closeAll();
				});

				$('#resendFailed').on('click', function () {
					$('#resendFailed').attr("disabled", true);
					setTimeout(async function () {
						$.ajaxSettings.async = false;

						pager.deviceSend.forEach(function (device) {
							$(".device-status[data-value=" + device.Device_No + "]").text('正在发送消息...').removeClass("success").removeClass('fail');
						});

						const deviceNosString = pager.deviceSend.map(device => device.Device_No).join(',');
						$.post("SendDeviceEnableClouds", { deviceNos: deviceNosString, parkingMode: sentryMode }, function (json) {
							if (json.success) {
								_ = pager.checkAllStatus(pager.deviceSend)
							} else {
								pager.deviceSend = pager.deviceSend;
								pager.deviceSend.forEach(function (device) {
									$(".device-status[data-value=" + device.Device_No + "]").text('消息发送失败').removeClass("success").addClass('fail');
								})
							}
						});
					}, Math.random() * 2000 + 1000); // 随机时间1-3秒
					setTimeout(function () { $('#resendFailed').removeAttr("disabled"); }, 2000);
				});

			},
			bindPower: function () {
				// window.parent.global.getBtnPower(window, function (pagePower) {

				// });
			},
			queryDeviceStatus: async function (device) {
				// 查询设备状态的方法
				try {
					const response = await $.post("QueryDeviceStatus", { deviceNo: device.Device_No });
					if (response.success) {
						if (response.data == 2) {
							var index = pager.deviceSend.indexOf(device);
							if (index < 0) {
								pager.deviceSend.push(device);
							}
							$(".device-status[data-value=" + device.Device_No + "]").text('消息发送失败').removeClass("success").addClass('fail');
						} else {
							var index = pager.deviceSend.indexOf(device);
							if (index > -1) {
								pager.deviceSend.splice(index, 1); // 删除指定的元素
							}
							$(".device-status[data-value=" + device.Device_No + "]").text('消息发送成功').removeClass("fail").addClass('success');
						}
						return true;  // 查询成功，返回 true
					} else {

						return false; // 查询失败，返回 false
					}
				} catch (error) {
					return false; // 请求出错，返回 false
				}
			},
			checkAllStatus: async function (devices) {
				let success = false;
				var newArray = devices.slice(); // 创建一个 devices 数组的副本

				while (newArray.length > 0 && !success) {
					for (let i = 0; i < newArray.length; i++) {
						let device = newArray[i];
						let ret = await pager.queryDeviceStatus(device);

						if (ret) {
							newArray.splice(i, 1); // 删除当前设备
							i--; // 调整索引，因为数组长度变短
						}

						await new Promise(resolve => setTimeout(resolve, 500));
					}

					// 如果所有设备都查询成功，可以将 success 设为 true 来退出 while 循环
					if (newArray.length === 0) {
						success = true;
					}
				}
			},
			getParkStatus: function () {
				$.post("GetParkingState", {}, function (json) {
					if (json.success) {
						if (parkmode != "2") {
							$(".parkstatus").html($("#park_online").tmpl([{ online: json.data }]));
						} else {
							$(".parkstatus").html($("#park_online").tmpl([{ online: 1 }]));
							if (json.data != null) {

								$(".parkstatus").html($("#park_online").tmpl([{ online: json.data.parkstatus }]));
								var htmlstring = json.data.isEmergency == 1 ? '<label class="layui-badge layui-bg-blue status">已开启</label>' : '<label class="layui-badge layui-bg-cyan status">已关闭</label>';
								$("#SysConfig_CloudBoxLprEmergency").html(htmlstring);
								$("#SysConfig_ConnMode").html(json.data.ConnMode == "1" ? '<label class="layui-badge layui-bg-green status">协同模式</label>' : '<label class="layui-badge layui-bg-blue status">开放模式</label>');
							}
						}

					} else {
						$(".parkstatus").html($("#park_online").tmpl([{ online: -1 }]));
					}
				});
			},
			closeParkWin: function () {
				layer.close(parkIndex);
			},
		};
	</script>

	<script>
		//设备参数配置[仅选项按钮]默认值
		var config = {
			Parking_EnableNet: 0,
			Parking_Mode: 1,
			Parking_Platform: 2,
			Parking_IsTrusteeship: 0
		};
		$(function () {
			$(".btnCombox ul li").click(function () {
				if ($(this).hasClass("select")) return;
				var idName = $(this).parent().parent().attr("id");
				$(this).siblings().removeClass("select");
				$(this).addClass("select");
				config[idName] = $(this).attr("data-value");

				onEventCombox(idName);
			});
		});

		var LoadDeviceConfig = function (data) {
			$(".btnCombox").each(function () {
				var idName = $(this).attr("id");
				$(this).find("li").removeClass("select");
				$(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
				config[idName] = data[idName];

				onEventCombox(idName);
			});
		}

		var onEventCombox = function (idName) {
			if (idName == "Parking_EnableNet") {
				if (config[idName] == 1) {
					$(".enablenet").removeClass("layui-hide");
				} else {
					$(".enablenet").removeClass("layui-hide").addClass("layui-hide");
				}
			}

			onSetUrl();
		}

		var onSetUrl = function () {
			var apiurl = "";
			if (config.Parking_Mode == 1) {
				if (config.Parking_Platform == 1) {
					apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_DevelopApiDebug";
				} else {
					apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_ParkApiDebug";
				}
			} else {
				if (config.Parking_Platform == 1) {
					apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_DevelopApi";
				} else {
					apiurl = "@carparking.Config.AppSettingConfig.SiteDomain_ParkApi";
				}
			}
			$("#Parking_ApiUrl").val(apiurl);
		}
	</script>
</body>
</html>
