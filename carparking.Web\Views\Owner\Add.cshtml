﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑车辆信息</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet" />
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-table-cell { overflow: visible !important; }
        .tdSelect { width: 100%; max-width: 200px; height: 30px; border: 1px solid #d9d9d9; border-radius: 3px; color: #0094ff; font-size: 13px; }

        .layui-table-view .layui-table td { overflow: hidden; }

        .layui-layout-admin { margin-bottom: 60px; }
        .layui-row { margin-bottom: 8px; }
        .layui-table-body { overflow: hidden !important; }
        .layui-btn-warm { background-color: #ec971f; border-color: #d58512; }

        .areaBtn { background-color: #fff; border-color: #1ab394; color: #1ab394; height: 30px; line-height: 30px; padding: 0 10px; }
        .areaBtn:hover { background-color: #1ab394; border-color: #1ab394; color: #fff; }

        .divSelectPop { width: 110px; height: 250px; overflow: auto; background-color: #fff; }
        .divSelect { padding: 0 10px; line-height: 36px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; cursor: pointer; }
        .divSelect.selected { background-color: #5FB878; color: #fff; }
        .divSelect:hover { background-color: #218cbf; color: #e9f3ec; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div style="overflow:hidden;height:0;">
            <!--防止浏览器保存密码后自动填充-->
            <input type="password" />
            <input type="text" />
            <input type="text" name="email" />
        </div>
        <div class="ibox-content" style="padding: 1px;">
            <div id="verifyCheck" class="layui-form layui-card-body">
                <div class="layui-card-header">
                    <strong>基本信息</strong>
                </div>
                <div class="layui-row form-group"></div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>系统车位号</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <div class="input-group">
                            <input type="text" class="layui-input after v-null v-submit v-numen" maxlength="8" id="Owner_Space" name="Owner_Space" autocomplete="off" />
                            <span class="input-group-btn"><button class="layui-btn layui-btn-outline after layui-hide" id="CreateSpace"><i class="fa fa-rotate-left"></i> 生成</button></span>
                        </div>
                    </div>
                    <div class="layui-col-sm2  edit-label">车场车位号</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_ParkSpace" name="Owner_ParkSpace" maxlength="32" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label"><label>车牌类型</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <select class="layui-input" id="Owner_CardTypeNo" name="Owner_CardTypeNo" lay-search>
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="layui-col-sm2  edit-label">车主姓名</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_Name" name="Owner_Name" maxlength="20" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row layui-hide isedit month free temp">
                    <div class="layui-col-sm2 edit-label"><label>有效期起</label></div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-submit" id="Owner_StartTime" name="Owner_StartTime" autocomplete="off" />
                    </div>
                    <div class="layui-col-sm2 edit-label">有效期止</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-submit" id="Owner_EndTime" name="Owner_EndTime" autocomplete="off" />
                    </div>
                </div>
                <div class="layui-row layui-hide isedit">
                    <div class="layui-col-sm2  edit-label layui-hide store temp month free">应付金额</div>
                    <div class="layui-col-sm3 edit-ipt-ban layui-hide store temp month free">
                        <input type="text" class="layui-input v-float" id="Owner_Money" name="Owner_Money" autocomplete="off" value="0.00" />
                    </div>
                    <div class="layui-col-sm2  edit-label layui-hide store temp month free">支付金额</div>
                    <div class="layui-col-sm3 edit-ipt-ban layui-hide store temp month free">
                        <input type="text" class="layui-input v-float" id="Owner_PayedMoney" name="Owner_PayedMoney" autocomplete="off" value="0.00" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">&nbsp;</div>
                    <div class="layui-col-xs8 carnotable">
                        <table class="layui-table" lay-filter="table_carno" id="table_carno" style="">
                        </table>
                        <script type="text/html" id="toolbarcarnos">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-outline layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i> <t>新增车辆</t></button>
                            </div>
                        </script>
                    </div>
                </div>
                <div class="layui-row chuzhi">
                    <div class="layui-col-sm2 edit-label">&nbsp;</div>
                    <div class="layui-col-xs8">
                        <script type="text/html" id="toolbarareas">
                            <div class="layui-btn-container">
                                    <button class="layui-btn layui-btn-outline layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i> <t>新增可停区域</t></button>
                                </div>
                        </script>
                        <table class="layui-table" lay-filter="table_area" id="table_area"> </table>

                        <div class="label-desc" style="clear:both;color:#1ab394">可停区域至少设置一个。车位数大于0则表示当前有免费车位，无车位则设置0。修改车位数量，若车辆在场内将会重新生成入场订单。</div>
                    </div>
                </div>

                <div class="layui-card-header">
                    <strong>车主信息</strong>
                </div>
                <div class="layui-row form-group"></div>
                <div class="layui-row">
                    <div class="layui-col-sm2  edit-label">身份证号</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-idcard" id="Owner_IDCard" name="Owner_IDCard" maxlength="18" />
                    </div>
                    <div class="layui-col-sm2  edit-label">手机号码</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-phone" id="Owner_Phone" name="Owner_Phone" maxlength="11" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2  edit-label">电子邮箱</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input v-email" id="Owner_Email" name="Owner_Email" maxlength="50" />
                    </div>
                    <div class="layui-col-sm2  edit-label">驾驶证号</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_License" name="Owner_License" maxlength="32" />
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">车主住址</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_Address" name="Owner_Address" maxlength="200" />
                    </div>
                    <div class="layui-col-sm2 edit-label">备注</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_Remark" name="Owner_Remark" maxlength="255" />
                    </div>
                </div>


            </div>

            <div class="layui-card-footer">
                <div class="layui-footer" style="left: 0px;">
                    <button class="layui-btn layui-btn-md" id="SaveNext"><i class="fa fa-check"></i> <t>保存并继续</t></button>
                    <button class="layui-btn layui-btn-md" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="layui-btn layui-btn-md layui-btn-warm" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>

    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?123123123" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>

        myVerify.init();
        var layuiForm = null;
        layui.use(['table', 'element', 'form'], function () {
            layuiForm = layui.form;
            pager.init()
        });

        var createNumber = function (len, data) {
            var d = '';
            for (var i = 0; i < len; i++) {
                d = d + Math.floor(Math.random() * 10);
            }

            var s = (new Date().getTime()) + d;
            if (data != null && data.length > 0) {
                var str = JSON.stringify(data);
                if (str.indexOf(s) > -1) {
                    return createNumber(len, data);
                }
            }
            return s;
        }

        var createId = function (len, data) {
            var d = '';
            for (var i = 0; i < len; i++) {
                d = d + Math.floor(Math.random() * 10);
            }

            var s = d;
            if (data != null && data.length > 0) {
                var str = JSON.stringify(data);
                if (str.indexOf(s) > -1) {
                    return createNumber(len, data);
                }
            }
            return s;
        }
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramOwnerNo = $.getUrlParam("Owner_No");
        var updateCarSpaceNo = '@ViewBag.Create';

        var index = parent.layer.getFrameIndex(window.name);
        var pager = {
            next: false,
            ownerModel: null,
            areaList: [],
            carNoList: [],
            cards: [],
            cartypes: [],
            areas: [],
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {

                $("#Owner_StartTime").val('@Html.Raw(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"))');
                $("#Owner_EndTime").val('@Html.Raw(DateTime.Now.AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd 23:59:59"))');
                _DATE.bind(layui.laydate, ["Owner_StartTime", "Owner_EndTime"], { type: "datetime", range: true });

                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        pager.cards = json.data;
                        var options = '';
                        json.data.forEach((item, index) => {
                            if (item.CarCardType_Type != 5 && item.CarCardType_Type != 6 && item.CarCardType_Status != 0) {
                                options += '<option value="' + item.CarCardType_No + '" data-type="' + item.CarCardType_Type + '">' + item.CarCardType_Name + '</option>';
                            }
                        });
                        $("#Owner_CardTypeNo").html(options);
                    }
                }, "json");
                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        pager.cartypes = json.data;
                    }
                }, "json");
                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        pager.areas = json.data;
                        pager.areas.unshift({ ParkArea_No: "0", ParkArea_Name: "全部区域" });
                    }
                }, "json");

                layui.form.on("select", function (e) {
                    if (e.elem.id == "Owner_CardTypeNo") {
                        on_card_change();
                    }
                })

                layui.form.render();

                pagerCarno.init();

                var areaData = localStorage.getItem("ownerarea");
                if (areaData && areaData != null) {
                    pager.areaList = JSON.parse(areaData);
                    if (pager.areaList && pager.areaList.length > 0) {
                        $.each(pager.areaList, function (k, v) {
                            v.StopSpace_No = createNumber(3, pager.areaList);
                        });
                        console.log(JSON.stringify(pager.areaList));
                    }
                }
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("/Owner/GetOwner", { Owner_No: paramOwnerNo }, function (json) {
                        if (json.Success) {
                            var model = json.Data.model;
                            var cars = json.Data.cars;
                            var spaces = json.Data.spaces;

                            $("#verifyCheck").fillForm(model, function (data) { });
                            layui.form.render("select")

                            pager.ownerModel = model;
                            pager.carNoList = cars;
                            pager.areaList = spaces;

                            // if (updateCarSpaceNo != "1") {
                            //     $("#Owner_Space").attr("disabled", true);
                            // }
                            $("#Owner_Space").attr("disabled", true);

                            if (model.Owner_CardType == 2) { $(".chuzhi").addClass("layui-hide"); } else { $(".chuzhi").removeClass("layui-hide"); }

                            pagerCarno.bindData();
                            areaTable.onload();
                        }
                    });
                } else if (paramAct == "Add") {
                    areaTable.onload(pager.areaList, true);
                }
                on_card_change();
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    pager.next = false;
                    pager.OnSave(false);
                });

                $("#SaveNext").click(function () {
                    pager.next = true;
                    pager.OnSave(true);
                });

                $("#CreateSpace").off('click').click(function () {
                    $.getJSON("CreateOwnerSpace", {}, function (json) {
                        if (json.success) {
                            layer.tips("系统车位号：<t style='font-weight:900;'>" + json.data + "</t>", '#Owner_Space', { tips: [1, '#1ab394'] })
                            $("#Owner_Space").val(json.data);
                        } else {
                            layer.msg("车位号生成失败," + json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                        }
                    });
                });

                if (paramAct == "Add") {
                    $("#CreateSpace").click();
                }

                layui.element.on('tab', function (data) {
                    if (data.index == 0) {
                        carnoTable.onload();
                    } else {
                        areaTable.onload();
                    }
                });
            },
            OnSave: function (next) {
                if (!myVerify.check()) return;
                var param = $("#verifyCheck").formToJSON(true, function (data) {
                    data.Owner_Name = $("#Owner_Name").val();
                    data.Owner_Phone = $("#Owner_Phone").val();
                    data.Owner_Address = $("#Owner_Address").val();
                    data.Owner_IDCard = $("#Owner_IDCard").val();
                    data.Owner_License = $("#Owner_License").val();
                    data.Owner_Email = $("#Owner_Email").val();
                    data.Owner_Remark = $("#Owner_Remark").val();
                    data.Owner_Space = $("#Owner_Space").val();
                    data.Owner_ParkSpace = $("#Owner_ParkSpace").val();
                    return data;
                });

                $("#Save").attr("disabled", true);
                pager.saveData(param, next);
                //if (pagerCarno.tableData && pagerCarno.tableData.length > 0) {
                //    layer.open({
                //        type: 0,
                //        title: "提示",
                //        btn: ["确定", "取消"],
                //        content: '若车辆已在场内，在车辆下次进场时才生效',
                //        yes: function (res) {
                //            pager.saveData(param, next);
                //        },
                //        btn2: function () { $("#Save").removeAttr("disabled"); },
                //        end: function () {
                //            $("#Save").removeAttr("disabled");
                //        }
                //    })
                //} else {
                //    pager.saveData(param, next);
                //}
            },
            saveData: function (param, next) {
                layer.msg("处理中", { icon: 16, time: 0 });
                localStorage.setItem("ownerarea", JSON.stringify(pager.areaList));
                if (paramAct == "Add") {
                    //判断发卡卡号不而且已存在不足10位时，自动前置补0
                    
                    var jsonModel = JSON.stringify(param);
                    var carJson = JSON.stringify(pagerCarno.tableData);
                    var spaceJson = JSON.stringify(pager.areaList);
                    $.post("/Owner/AddOwner", { jsonModel: jsonModel, carJson: carJson, spaceJson: spaceJson }, function (json) {
                        if (json.success) {
                            var isRegVisitor = json.data;
                            if (next) {
                                if (isRegVisitor) {
                                    layer.msg(json.msg, { icon: 1, time: 2500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex, true);
                                        window.location.reload();
                                    });
                                } else {
                                   window.parent.pager.bindData(window.parent.pager.pageIndex, true);
                                   window.location.reload();
                                }
                            } else {
                                layer.msg(json.msg, { icon: 1, time: isRegVisitor ? 2500 : 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            }
                        } else {
                            if (json.data && json.data != null && json.data != "") {
                                layer.open({
                                    type: 0,
                                    title: "消息提示",
                                    btn: ["去清缴费用", "取消"],
                                    shade: 0,
                                    content: json.msg,
                                    yes: function (res) {
                                        layer.open({
                                            title: "停车支付",
                                            type: 2, id: 1,
                                            area: getIframeArea(['95%', '95%']),
                                            maxmin: true,
                                            content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(json.data) + "&callBack=1",
                                            end: function () { }
                                        });
                                    }
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                            }
                            $("#Save").removeAttr("disabled");
                            $("#SaveNext").removeAttr("disabled");
                        }
                    }, "json");
                }
                else if (paramAct == "Update") {

                    param.Owner_No = paramOwnerNo;
                    var jsonModel = JSON.stringify(param);
                    var carJson = JSON.stringify(pagerCarno.tableData);
                    var spaceJson = JSON.stringify(pager.areaList);
                    $.post("/Owner/UpdateOwner", { jsonModel: jsonModel, carJson: carJson, spaceJson: spaceJson }, function (json) {
                        if (json.success) {
                            var isRegVisitor = json.data;
                            if (next) {
                                if (isRegVisitor) {
                                    layer.msg(json.msg, { icon: 1, time: 2500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex, true);
                                        window.location.reload();
                                    });
                                } else {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex, true);
                                    window.location.reload();
                                }
                            } else {
                                layer.msg(json.msg, { icon: 1, time: isRegVisitor ? 2500 : 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            }
                        } else {
                            if (json.data && json.data != null && json.data != "") {
                                layer.open({
                                    type: 0,
                                    title: "消息提示",
                                    btn: ["去清缴费用", "取消"],
                                    shade: 0,
                                    content: json.msg,
                                    yes: function (res) {
                                        layer.open({
                                            title: "停车支付",
                                            type: 2, id: 1,
                                            area: getIframeArea(['95%', '95%']),
                                            maxmin: true,
                                            content: '/InParkRecord/Payment?r=' + Math.random() + "&ParkOrder_No=" + encodeURIComponent(json.data) + "&callBack=1",
                                            end: function () { }
                                        });
                                    }
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                            }
                            $("#Save").removeAttr("disabled");
                            $("#SaveNext").removeAttr("disabled");
                        }
                    }, "json");
                }
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (paramAct == "Add") {
                        $("#CreateSpace").removeClass("layui-hide");
                    }
                    // else {
                    //     if (updateCarSpaceNo == "1") {
                    //         $("#CreateSpace").removeClass("layui-hide");
                    //     }else{
                    //         $("#Owner_Space").removeClass("after");
                    //     }
                    // }
                });
            },
            Continue: function () {
                console.log("支付成功，继续执行登记车牌");

                layer.closeAll();
                layer.msg("支付成功,正在保存车辆信息...", { icon: 16, time: 0 });
                pager.OnSave(pager.next);
            },
        };

        var on_card_change = function () {
            var v = $("#Owner_CardTypeNo").val();
            var card = pager.cards.find((item, index) => { return item.CarCardType_No == v; });
            if (card != null && card.CarCardType_Type == 2) { $(".chuzhi").addClass("layui-hide"); } else { $(".chuzhi").removeClass("layui-hide"); }

            if (paramAct == "Update") return;
            $(".isedit").removeClass("layui-hide");

            if (card != null) {
                $(".month,.store,.temp,.free").removeClass("layui-hide").addClass("layui-hide");
                if (card.CarCardType_Type == 3) {
                    $(".month").removeClass("layui-hide");
                } else if (card.CarCardType_Type == 2) {
                    $(".store").removeClass("layui-hide");
                } else if (card.CarCardType_Type == 1) {
                    $(".temp").removeClass("layui-hide");
                } else if (card.CarCardType_Type == 4) {
                    $(".free").removeClass("layui-hide");
                }
            }
        }
    </script>

    <!--车辆信息表格-->
    <script type="text/x-jquery-tmpl" id="tempCarTypeNo">
        <select class="tdSelect" data-no="{{d.Car_ID}}" data-key="Car_VehicleTypeNo">
            {{# layui.each(pager.cartypes, function(index,item){ }}
            <option {{# if (d.Car_VehicleTypeNo==item.CarType_No){}} selected {{# } }} value="{{ item.CarType_No }}">{{ item.CarType_Name }}</option>
            {{# });}}
        </select>
    </script>
    <script type="text/html" id="toolbarareas">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-outline layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i> <t>新增车辆</t></button>
        </div>
    </script>
    <script>
        var pagerCarno = {
            tableData: [],
            cartypeData: [],
            pageIndex: 1,
            init: function () {
                pagerCarno.cartypeData = pager.cartypes;
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                var n = this.createNewRow();
                this.tableData.unshift(n);
            },
            bindData: function (index) {
                pagerCarno.tableData = pager.carNoList;
                pagerCarno.Refresh(index);
            },
            bindEvent: function () {

            },
            Refresh: function (index) {
                curcartb.reload({
                    data: pagerCarno.tableData
                });
            },
            removeItem: function (no, refresh) {
                pagerCarno.tableData.forEach((item, index) => {
                    if (item.Car_ID == no) {
                        pagerCarno.tableData.splice(index, 1);
                        if (refresh) {
                            pagerCarno.Refresh(pagerCarno.pageIndex);
                        }
                    }
                });
            },
            createNewRow: function () {
                var id = createId(8, pagerCarno.tableData);
                var no = createNumber(3, pagerCarno.tableData);
                var defcartype = pagerCarno.cartypeData.length > 0 ? pagerCarno.cartypeData[0] : {};
                var n = {
                    Car_ID: id,
                    Car_No: no,
                    Car_CarNo: '@Html.Raw(ViewBag.CarPrefix)',
                    Car_VehicleTypeNo: defcartype.CarType_No,
                    Car_Remark: '',
                    Car_License: '',
                    isNew: true
                };
                return n;
            },
            Delete: function (no) {
                var item = pagerCarno.tableData.find((d, i) => { return d.Car_ID == no; });
                if (item == null) return;

                if (item.isNew) {
                    pagerCarno.removeItem(no, true);
                } else {
                    pagerCarno.removeItem(no, true);
                    layer.msg("删除车辆,保存后生效.", { time: 1000 });
                }
            },
            onSelected: function () {
                $("div[lay-id='table_carno'] .tdSelect").unbind("change").change(function () {

                    var no = $(this).attr("data-no");
                    var key = $(this).attr("data-key");
                    var val = $(this).val();
                    var text = $(this).find('option:selected').text();
                    if (key == "Car_VehicleTypeNo") {
                        var isfresh = false;
                        pagerCarno.tableData.forEach((item, index) => {
                            if (item.Car_ID == no) {
                                item[key] = val;
                            }
                        });
                        if (isfresh) pagerCarno.Refresh(pagerCarno.pageIndex);
                    }
                });


                layui.$(document).off("focus", ".layui-table-edit").on("focus", ".layui-table-edit", function (obj) {
                    //console.log(obj)
                    var e = layui.$(this);
                    var parent = e.parent();
                    //选择车牌类型
                    if (parent.attr("data-field") == "Car_VehicleTypeNo") {
                        event.preventDefault();
                        event.stopPropagation();
                        //设置只读
                        $(this).attr("readonly", true);
                        //获取当前行
                        var val = $(this).val();
                        //获取当前行的索引
                        var table = e.parent().parent();
                        var rowIndex = table.attr("data-index");
                        //获取当前行的数据
                        var dom = $(this).get(0);
                        var position = getPosition(dom);
                        var top = position.top + dom.offsetParent.clientHeight;// - 32;
                        var left = position.left;
                        var width = dom.clientWidth;
                        //设置样式
                        var css = '.tdselect-div {position: absolute;z-index: 999; top:' + top + 'px; left:' + left + 'px; bottom:auto; box-shadow: 0 0 10px #ccc; heigth:300px;}';
                        //添加样式
                        var style = document.createElement("style");
                        style.id = "tdselect-div-float";
                        style.innerText = css;
                        document.head.append(style)
                        //移除弹出的DIV
                        var myClickHandler = function () {
                            event.preventDefault();
                            event.stopPropagation();
                            setTimeout(function () {
                                $(".tdselect-div").remove();
                            }, 0);
                            return false;
                        }
                        //绑定window点击事件
                        $(window).off('click', myClickHandler).on('click', myClickHandler);
                        //创建DIV
                        var div = document.createElement("div");
                        div.className = "tdselect-div";
                        var htm = '<div class="divSelectPop">';
                        var cssSelect = '';
                        //循环添加数据
                        layui.each(pager.cartypes, function (index, item) {
                            if (item.CarType_No == val) cssSelect = 'selected'; else cssSelect = '';
                            htm += (' <div class="divSelect ' + cssSelect + '"  data-value="' + item.CarType_No + '" data-index="' + rowIndex + '">' + item.CarType_Name + '</div>');
                        })
                        htm += '</div>';
                        div.innerHTML = htm;
                        //添加到body
                        document.body.appendChild(div);
                        //设置滚动条
                        var selectedDiv = $("div.divSelect.selected");
                        var divSelectPop = selectedDiv.closest(".divSelectPop");
                        divSelectPop.scrollTop(selectedDiv.position().top - 20);
                        selectedDiv.focus();
                        //绑定下拉列表绑定点击事件
                        $(".divSelectPop div").unbind("click").click(function () {
                            //获取当前点击的值
                            var no = $(this).attr("data-value");
                            var name = $(this).text();
                            var index = $(this).attr("data-index");

                            console.log(no)
                            console.log(name)
                            console.log(index)

                            //设置当前行的文本
                            parent.find("div").text(name)
                            //设置当前行数据的值
                            pagerCarno.tableData[index].Car_VehicleTypeNo = no;
                            pagerCarno.Refresh(pagerCarno.pageIndex)
                            //移除弹出的DIV
                            $(".tdselect-div").remove();
                        });
                        e.click();
                        //选择车牌号
                    } else if (parent.attr("data-field") == "Car_CarNo") {
                        var table = e.parent().parent();
                        var index = table.attr("data-index");
                        var id = createId(5);
                        e.attr("id", id)
                        s_carno_picker.init(id, (text, carno) => {
                            var carnoText = carno.join('')
                            parent.find("div").text(carnoText)
                            pagerCarno.tableData[index].Car_CarNo = carnoText;
                            console.log(pagerCarno.tableData[index])
                            pagerCarno.Refresh(pagerCarno.pageIndex)
                        }, "web");
                        $(".sui-carw-clear").addClass("layui-hide");
                        e.click();
                    }
                })
            }
        }
        var curcartb = null;
        layui.use(['table', 'form'], function () {
            var table = layui.table;
            var form = layui.form;

            var cols = [[
                { type: 'checkbox' }
                // , { field: 'Car_VehicleTypeNo', edit: 'text', title: '车牌颜色' }
                , {
                    field: 'Car_VehicleTypeNo', edit: 'select', title: '车牌颜色', templet: function (d) {
                        var CarType_Name = '';
                        for (var i = 0; i < pager.cartypes.length; i++) {
                            var cartype = pager.cartypes[i];
                            if (cartype.CarType_No === d.Car_VehicleTypeNo) {
                                CarType_Name = cartype.CarType_Name;
                                break;
                            }
                        }

                        if (CarType_Name == '') {
                            CarType_Name = pager.cartypes[0].CarType_Name;
                        }
                        return CarType_Name;
                    }
                }
                , { field: 'Car_CarNo', edit: 'text', title: '车牌号' }
                , { field: 'Car_CardNo', edit: 'text', title: '车辆卡号' }
                , { field: 'Car_Remark', edit: 'text', title: '备注' }
                , {
                    field: 'btns', title: '操作', width: 140, templet: function (d) {
                        var h = '<button class="layui-btn layui-btn-outline layui-btn-sm layui-btn-warning" onclick="pagerCarno.Delete(\'' + d.Car_ID + '\')"><i class="fa fa-trash-o"></i> <t>删除</t></button>';
                        return h;
                    }
                }
            ]];

            curcartb = table.render({
                elem: '#table_carno'
                , cols: cols
                , data: []
                , toolbar: '#toolbarcarnos'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: false
                , limit: Number.MAX_VALUE
                , where: {}
                , done: function (d) {
                    tb_page_set(d);
                    pagerCarno.onSelected();
                }
            });

            //头工具栏事件
            table.on('toolbar(table_carno)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pagerCarno.pageIndex = $("#table_carno .layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        var len = $(".carnotable .layui-table-main>table").find("tbody").find("tr").length;
                        if (len >= 2000) {
                            layer.msg("最多只能添加2000辆车", { icon: 0, time: 1000 });
                            return;
                        }
                        var n = pagerCarno.createNewRow();
                        pagerCarno.tableData.unshift(n);
                        pagerCarno.Refresh(pagerCarno.pageIndex);
                        // layuiForm.render("select");
                        break;
                }
            });

            table.on('edit(table_carno)', function (obj) {
                //console.log(obj.value); //得到修改后的值
                //console.log(obj.field); //当前编辑的字段名
                //console.log(obj.data); //所在行的所有相关数据
                pagerCarno.tableData.forEach((item, index) => {
                    if (item.Car_ID == obj.data.Car_ID) {
                        item[obj.field] = obj.value;
                    }
                });
            });

            pagerCarno.Refresh(1);
        });
    </script>

    <!--可停区域表格-->
    <script type="text/x-jquery-tmpl" id="tempParkAreaNo">
        <select class="tdSelect" data-no="{{d.StopSpace_ID}}" data-key="StopSpace_AreaNo" lay-ignore>
            {{# layui.each(pager.areas, function(index,item){ }}
             <option {{# if (d.StopSpace_AreaNo==item.ParkArea_No){}} selected {{# } }}  value="{{ item.ParkArea_No }}">{{ item.ParkArea_Name }}</option>
            {{# });}}
        </select>
    </script>
    <script type="text/x-jquery-tmpl" id="areaName">
        {{# if(d.StopSpace_Type==0){ }}
        <span>全部区域</span>
        {{# }else{ }}
        <span title="{{d.StopSpace_AreaName}}">{{d.StopSpace_AreaName}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="areabtns">
        <vbutton class="layui-btn layui-btn-xs editarea areaBtn" data-no="{{d.StopSpace_No}}">编辑</vbutton>
        <vbutton class="layui-btn layui-btn-xs layui-btn-danger delarea areaBtn" data-no="{{d.StopSpace_No}}">删除</vbutton>
    </script>
    <script>
        var areaTable = {
            onload: function (data, isfirst) {
                data = data || pager.areaList;
                if (!data || data == null || data.length == 0) {
                    data = pager.areaList = [{
                        LAY_TABLE_INDEX: 0
                        , StopSpace_AreaName: "全部区域"
                        , StopSpace_AreaNo: '["0"]'
                        , StopSpace_Content: ""
                        , StopSpace_No: createNumber(3, data)
                        , StopSpace_Number: isfirst ? "1" : "0"
                        , StopSpace_Type: 0
                    }];

                    console.log(JSON.stringify(pager.areaList));
                    layer.msg("当前车位数量：" + (isfirst ? "1" : "0"), { time: 1000 });
                } else {
                    var spaceCount = 0;
                    $.each(data, function (k, v) { spaceCount += parseInt(v.StopSpace_Number) });
                    layer.msg("当前车位数量：" + spaceCount, { time: 1000 });
                }

                //转换静态表格
                var cols = [[
                    { type: 'numbers' }
                    , { field: 'StopSpace_AreaName', title: '可停区域', toolbar: "#areaName" }
                    , { field: 'StopSpace_Number', title: '车位数量' }
                    , { field: 'StopSpace_Content', title: '车位信息' }
                    , { field: 'StopSpace_Btns', title: '操作', toolbar: "#areabtns" }
                ]];

                layui.table.render({
                    elem: '#table_area',
                    cols: cols,
                    toolbar: '#toolbarareas',
                    defaultToolbar: ["filter"],
                    data: data,
                    done: function (data) {
                        tb_page_set(data);
                        $(".editarea").unbind("click").click(function () {
                            var no = $(this).attr("data-no");
                            areaTable.openAreaEdit(true, no);
                        })

                        $(".delarea").unbind("click").click(function () {
                            layer.closeAll();
                            var no = $(this).attr("data-no");
                            pager.areaList.forEach(function (item, i) {
                                if (item.StopSpace_No == no) {
                                    pager.areaList.splice(i, 1);
                                    return;
                                }
                            });
                            areaTable.onload();
                            layer.msg("删除区域,保存后生效.", { time: 1000 });
                        })
                    }
                });

                //头工具栏事件
                layui.table.on('toolbar(table_area)', function (obj) {
                    var checkStatus = layui.table.checkStatus(obj.config.id); //获取选中行状态
                    var data = checkStatus.data;  //获取选中行数据
                    areaTable.pageIndex = $("#table_area .layui-laypage-curr").text();
                    switch (obj.event) {
                        case 'Add':
                            areaTable.openAreaEdit(false);
                            break;
                    }
                });
            },
            addOrUpdate: function (item) {
                if (pager.areaList != null && pager.areaList.length > 0) {
                    var isedit = false;
                    pager.areaList.forEach(function (d, i) {
                        if (d.StopSpace_No == item.StopSpace_No) {
                            pager.areaList[i] = item;
                            isedit = true;
                        }
                    });
                    if (!isedit)
                        pager.areaList[pager.areaList.length] = item;
                } else {
                    pager.areaList = [item];
                }

                this.onload();
                layer.closeAll();
            },
            openAreaEdit: function (isedit, no) {
                if (isedit) {
                    layer.open({
                        type: 2,
                        title: "编辑车位",
                        content: "EditStopSpace?Act=Update&no=" + no,
                        area: getIframeArea(["600px", "420px"]),
                        maxmin: false
                    });
                } else {
                    layer.open({
                        type: 2,
                        title: "新增车位",
                        content: "EditStopSpace?Act=Add",
                        area: getIframeArea(["600px", "420px"]),
                        maxmin: false
                    });
                }
            }
        }


        //查找元素的绝对位置
        function getPosition(element) {
            var actualLeft = element.offsetLeft,
                actualTop = element.offsetTop,
                current = element.offsetParent; // 取得元素的offsetParent
            // 一直循环直到根元素
            while (current !== null) {
                actualLeft += current.offsetLeft;
                actualTop += current.offsetTop;
                current = current.offsetParent;
            }
            // 返回包含left、top坐标的对象
            return {
                left: actualLeft,
                top: actualTop
            };
        }
    </script>
</body>
</html>
