﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html { background-color: #f9f9f9; }
        /* html, body { width: 100%; height: 100%; padding: 0; margin: 0; }*/
        .layui-row { margin-bottom: 10px; }
        .m-label { padding: 9px 0 0 10px; font-weight: bold; }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .layui-card { box-shadow: none; }

        .layui-form .layui-badge { padding: 3px 5px; margin-top: 9px; }
        .gray-bg { background-color: #ffffff; margin-bottom: 15px; border-bottom: 1px solid #f8f1f1 }
        h3 { padding: 5px; font-size: 18px; }

        .layui-card-header { padding: 0 1px !important; }

        .layui-table-cell { padding: 0 5px !important; }
        .layui-table-view .layui-form-radio > i { margin: -12px !important; }
        .layui-form-radio > .layui-icon { right: 26px !important; }
        .layui-collapse { border-top: 0; }
        .layui-table-page > .layui-icon { right: inherit; }
        .layui-card-header .layui-icon { right: auto; }
        .layui-table-page .layui-laypage span { margin-left: 10px !important; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>商家优惠</cite></a>
                <a><cite>车牌优惠</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body layui-form" id="verifyCheck">
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">停车场</div>
                            <div class="layui-col-xs7">
                                <input type="text" class="layui-input v-null" id="Parking_Name" name="Parking_Name" maxlength="32" value="@ViewBag.parkname" disabled />
                            </div>
                            <div class="layui-col-xs1 red-mark">*</div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">优惠方式</div>
                            <div class="layui-col-xs7">
                                <div class="btnCombox" id="CouponRecord_CouponCode">
                                    <ul>
                                        <li data-value="101" class="select">优惠金额</li>
                                        <li data-value="102">优惠时长</li>
                                        <li data-value="103">优惠比例</li>
                                        <li data-value="104">免费到指定时间</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label ">优惠设置</div>
                            <div class="layui-col-xs7 edit-ipt-ban">
                                <select data-placeholder="优惠设置" class="layui-input v-null" id="CouponRecord_ParkDiscountSetNo" name="CouponRecord_ParkDiscountSetNo" lay-search>
                                </select>
                            </div>
                            <div class="layui-col-xs1 red-mark">*</div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">场内车辆</div>
                            <div class="layui-col-xs7" style="min-height:430px;">
                                <div class="layui-card">
                                    <div class="layui-card-header">
                                        <div class="layui-collapse" lay-filter="test">
                                            <div>
                                                @*<h2 class="layui-colla-title">请选择场内车辆</h2>*@
                                                <div class="layui-colla-content layui-show">
                                                    <div class="layui-form" id="searchForm">
                                                        <div class="layui-inline">
                                                            <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                                                        </div>
                                                        <div class="layui-inline">
                                                            <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" />
                                                        </div>
                                                        <div class="layui-inline">
                                                            <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                                                        </div>
                                                        <div class="layui-inline">
                                                            <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                                        </div>
                                                    </div>
                                                    <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>


                        @*<div class="layui-bg-gray" style="padding: 5px;">
                                <div class="layui-row layui-col-space11">
                                    <div class="layui-col-md10">

                                    </div>
                                    <div class="layui-col-md6">
                                            <div class="layui-card">
                                                <div class="layui-card-header">场内订单</div>
                                                <div class="layui-card-body">
                                                    结合 layui 的栅格系统<br>
                                                    轻松实现响应式布局
                                                </div>
                                            </div>
                                        </div>
                                </div>
                            </div>*@

                        <div class="hr-line-dashed"></div>
                        <div class="layui-row">
                            <div class="layui-col-xs4 layui-col-xs-offset1 edit-label">&nbsp;</div>
                            <div class="layui-col-xs7">
                                <button class="btn btn-primary " id="Save"><i class="fa fa-save"></i> <t>保存</t></button>
                                <button class="btn btn-primary layui-bg-red" id="Cancel"><i class="fa fa-close"></i> <t>取消</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-row layui-hide" id="nodata">
                        <div class="padding-15">还没有添加过岗亭主机，您可以先点击左侧[新增岗亭主机]进行添加</div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplParkDiscountSetNo">
        {{if ParkDiscountSet_Type == 101 }}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--优惠${ParkDiscountSet_Amount}元</option>
        {{else ParkDiscountSet_Type == 102 }}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--免费${ParkDiscountSet_Duration}分钟</option>
        {{else ParkDiscountSet_Type == 103 }}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--优惠比例${ParkDiscountSet_Ratio}%</option>
        {{else ParkDiscountSet_Type == 104}}
        <option value="${ParkDiscountSet_No}">${ParkDiscountSet_Name}--免费到指定时间:${ParkDiscountSet_AppointHour}</option>
        {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v1.0.01" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layform = null;
        var laytable = null;
        var laydate = null;
        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            layform = layui.form;
            laydate = layui.laydate;
            var table = layui.table;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'ParkOrder_ID', title: '订单ID', hide: true }
                , { field: 'ParkOrder_No', title: '订单号', hide: true }
                , { field: 'ParkOrder_CarNo', title: '车牌号', width: 80 }
                , { field: 'ParkOrder_EnterTime', title: '入场时间', width: 150 }
                , { field: 'ParkOrder_EnterPasswayName', title: '入口车道名称' }
                , { field: 'ParkOrder_EnterImgPath', title: '入场图片', toolbar: "#tmplenterimg" }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型' }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色' }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ParkOrder/GetParkOrderList'
                , method: 'post'
                , toolbar: ''
                , defaultToolbar: []
                , cols: cols
                , limit: 7
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: false
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [7, 20, 30]

                , done: function (d) {
                    tb_page_set(d);
                },
            });

            //头工具栏事件
            table.on('radio(com-table-base)', function (obj) {
                //console.log(obj.data);
                var checkStatus = table.checkStatus('com-table-base');
                //console.log(checkStatus.data)
                pager.checkStaffData = checkStatus.data;

                pager.pageIndex = $(".layui-laypage-curr").text();
            });

            tb_row_radio(table)

        });

        var dt = new Date().Format("yyyy-MM-dd HH:mm:ss");

        //_DATE.bind(layui.laydate, ["CouponRecord_StartTime", "CouponRecord_EndTime"], { type: 'datetime', range: true });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("SentryHost_No");

        //var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            checkStaffData:[],
            ParkDiscountSet: null,
            passways: null,
            alllink: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;

            },
            bindSelect: function () {
                pager.bindParkDiscountSet();
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["ParkOrder_OutTime0", "ParkOrder_OutTime1"], { type: 'datetime', range: true });
            },
            //数据绑定
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/ParkOrder/GetParkOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    if ($("#CouponRecord_ParkDiscountSetNo").val() == "") { layer.msg("请选择优惠方式"); return; }
                    if (pager.checkStaffData.length == 0) { layer.msg("请选择场内车辆"); return; }

                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.CouponRecord_ParkDiscountSetNo = $("#CouponRecord_ParkDiscountSetNo").val();
                        data.CouponRecord_CouponCode = config.CouponRecord_CouponCode;
                        data.CouponRecord_StartTime = $("#CouponRecord_StartTime").val();
                        data.CouponRecord_EndTime = $("#CouponRecord_EndTime").val();
                        data.CouponRecord_IssueCarNo = pager.checkStaffData[0].ParkOrder_CarNo;
                        data.CouponRecord_ParkOrderNo = pager.checkStaffData[0].ParkOrder_No;
                        return data;
                    });
                    $("#Save").attr("disabled", true);
                    LAYER_OPEN_TYPE_0("确定该车牌优惠下发吗?", res => {
                        LAYER_LOADING("处理中...");
                        $.post("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            $("#Save").removeAttr("disabled")
                            if (json.success) {
                                $("#CouponRecord_StartTime").val("");
                                $("#CouponRecord_EndTime").val("");
                                $("#CouponRecord_IssueCarNo").val("");
                                layer.msg("保存成功", { time: 1000 })
                            } else {
                                layer.msg(json.msg);
                            }
                        }, "json");
                    }, res => {
                        $("#Save").removeAttr("disabled")
                    })
                });

                $("#Cancel").click(function () {
                    parent.layui.admin.events.closeThisTabs();
                })

                $(".btnCombox ul li").click(function () {
                    if ($(this).hasClass("select")) return;
                    var idName = $(this).parent().parent().attr("id");
                    $(this).siblings().removeClass("select");
                    $(this).addClass("select");
                    config[idName] = $(this).attr("data-value");

                    onEventCombox(idName);
                });

                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == 'ParkOrder_CarNo') {
                        $("#ParkOrder_CarNo").val(carno.join(""));
                    }
                }, "web").bindkeyup();
            },
            bindParkDiscountSet: function () {
                var list = [];
                $("#CouponRecord_ParkDiscountSetNo").html('<option value="">请选择优惠设置</option>')
                if (pager.ParkDiscountSet == null) {
                    $.post("/CarCoupon/GetAllParkDiscountSet", {}, function (json) {
                        if (json.Success) {
                            pager.ParkDiscountSet = json.Data;
                            $.each(pager.ParkDiscountSet, function (x, y) {
                                if (y.ParkDiscountSet_Type == config.CouponRecord_CouponCode)
                                    list.push(y);
                            })
                            $("#CouponRecord_ParkDiscountSetNo").append($("#tmplParkDiscountSetNo").tmpl(list))
                            layform.render("select");
                        }
                    }, 'json');
                } else {
                    $.each(pager.ParkDiscountSet, function (x, y) {
                        if (y.ParkDiscountSet_Type == config.CouponRecord_CouponCode)
                            list.push(y);
                    })
                    $("#CouponRecord_ParkDiscountSetNo").append($("#tmplParkDiscountSetNo").tmpl(list))
                    layform.render("select");
                }

            }
        };
    </script>
    <script type="text/javascript">
        //设备参数配置[仅选项按钮]默认值
        var config = {
            CouponRecord_CouponCode: 101,
            SentryHost_Category: 1,
            SentryHost_Net: 1,
        };

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == "CouponRecord_CouponCode") {
                pager.bindParkDiscountSet();
                if (config.CouponRecord_CouponCode == 104) {
                    $(".Time").addClass("layui-hide");
                } else {
                    $(".Time").removeClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
