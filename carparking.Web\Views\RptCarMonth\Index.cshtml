﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>月租车充值统计</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/plugins/bootstrap-table/bootstrap-table.min.css?v=12" rel="stylesheet" />
    <link href="~/Static/css/report.css?v=1.1" rel="stylesheet" />
    <style>
        .fa {
            margin: 6px 4px;
            float: left;
            font-size: 16px;
        }

        .layui-form-select .layui-input {
            width: 182px;
        }
        .desc { font-size: 14px; font-weight: bolder; color: #f18042; text-align: left; width: 850px; position: absolute; }
        .searchdesc { text-align: right; position: absolute; top: -20px; width: 98%; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>汇总报表</cite></a>
                <a><cite>月租车充值统计</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="start" id="start" autocomplete="off" placeholder="开始时间" value="@DateTime.Now.AddDays(-16).ToString("yyyy-MM-dd")" />
                            </div>
                            <div class="layui-inline form-group">
                                <input class="layui-input " name="end" id="end" autocomplete="off" placeholder="截止时间" value="@DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd")" />
                            </div>
                            <div class="layui-inline form-group">
                                <select data-placeholder="默认数据" class="form-control chosen-select " id="dataType" name="dataType" lay-search>
                                    <option value="0">默认数据</option>
                                    <option value="1">历史数据</option>
                                </select>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Export"><i class="layui-icon layui-icon-export inbtn"></i><t>导出</t></button>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="Delete" style="line-height:normal !important;"><i class="layui-icon layui-icon-refresh inbtn"></i><t>清除历史统计</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body" style="padding: 5px 15px!important;">
                        <div class="searchdesc"></div>
                        <div class="tabs-container">
                            <div class="tabs-left">
                                <ul class="nav nav-tabs">
                                    <li class="active" style="text-align: center;">
                                        <a data-toggle="tab" href="#tab-1"> 图形</a>
                                    </li>
                                    <li class="" style="text-align: center;">
                                        <a data-toggle="tab" href="#tab-2"> 数据</a>
                                    </li>
                                </ul>
                                <div class="tab-content ">
                                    <div id="tab-1" class="tab-pane active">
                                        <div class="panel-body">
                                            <div class="table-responsive">
                                                <span id="spanLegend" style="display: none; white-space: nowrap;"></span>
                                                <div id="rate_echart" style="width:100%;height:600px;margin-top:10px;"></div>
                                            </div>

                                        </div>
                                    </div>
                                    <div id="tab-2" class="tab-pane">
                                        <div class="panel-body" style="height: 600px;">
                                            <div>
                                                <table id="tableFromData" data-toggle="table" data-show-footer="true"></table>
                                            </div>
                                            <div class=" p-xs"></div>
                                            <div class="form-group">
                                                <div class="col-sm-11 text-right">
                                                    <button id="Print" class="btn btn-primary" type="button" onclick="printRpt();"><i class="fa fa-print"></i> 打印报表</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="desc">温馨提示：系统在凌晨会自动执行统计前一天报表的数据（00：00 ~ 23：59），若对停车信息手动变更，则需手动清除历史统计数据</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/echarts.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/bootstrap-table/bootstrap-table.min.js" asp-append-version="true"></script>
    <script src="/Static/js/jquery.download.js?t=637806249325147464" asp-append-version="true"></script>
    <script>

        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.use(['form'], function () {
            pager.init();
        });

    </script>
    <script>
        var mychart;
        var pager = {
            dataType: 0,
            dataTime: "",
            pageIndex: 1,
            chartHead: [],//图形报表头
            chartOption: {}, //图形报表数据源
            tbHead: '', //数据报表头
            tbArray: [], //数据报表数据源
            tbArrayAll: [],//数据报表数据源(包含总计)
            tbFooter: [], //数据报表脚
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                pager.bindData();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                // $("#start").val(_DATE.getSpan(new Date().Format("yyyy-MM-dd"), { date: -15 }, "d"));
                // $("#end").val(new Date().Format("yyyy-MM-dd"));
                _DATE.bind(layui.laydate, ["start", "end"], { type: 'date', range: true });
            },
            bindData: function () {
                pager.getdata(true);
            },
            getdata: function (checkhis) {
                var param = { start: $("#start").val(), end: $("#end").val(), checkhis: checkhis, dataType: $("#dataType").val() };
                pager.dataType = $("#dataType").val();
                pager.dataTime = $("#start").val();
                layer.msg("查询中...", { icon: 16, time: 0 });
                $.post("GetMonthRptAnalysis", { jsonModel: JSON.stringify(param) }, function (json) {
                    layer.closeAll();
                    if (json.success) {
                        if (json.msg.length > 15) $(".searchdesc").html(json.msg); else $(".searchdesc").html("");
                        if (json.data != null && json.data.length > 0) {
                            pager.tbArray = [];
                            pager.tbFooter = [];
                            var xdata = [];
                            var seriesData = []
                            //表脚
                            var ft1 = {};
                            var ft2 = {};
                            var ft3 = {};
                            var legends = [];
                            ft1['footerFormatter'] = 'totalTextFormatter';
                            ft2['footerFormatter'] = '';
                            ft3['footerFormatter'] = 'sumFormatter';
                            pager.tbFooter.push(ft1, ft2);
                            pager.tbHead = '<thead><tr><th data-field="编号">编号</th><th data-field="日期">日期</th>';

                            var arrfoot = {};
                            arrfoot['编号'] = '总计';
                            arrfoot['日期'] = '';
                            for (var i = 0; i < json.data.length; i++) {
                                pager.tbHead += '<th data-field="' + json.data[i].typeName + '">' + json.data[i].typeName + '</th>';
                                pager.tbFooter.push(ft3);
                                arrfoot[json.data[i].typeName] = json.data[i].total;
                                legends.push(json.data[i].typeName);

                                var plineArr = json.data[i].rpts.map(obj => { return obj.num });
                                var series = {
                                    name: json.data[i].typeName, type: "line", smooth: true,
                                    label: { show: true, position: 'top' },
                                    barMaxWidth: 30,
                                    barMinWidth: 20,
                                    data: plineArr
                                };
                                seriesData.push(series);
                            }

                            //表数据
                            var lenDate = json.data[0].rpts;
                            for (var i = 0; i < lenDate.length; i++) {
                                var arr = {};
                                arr['编号'] = i + 1;
                                arr['日期'] = lenDate[i].date;
                                for (var n = 0; n < json.data.length; n++) {
                                    arr[json.data[n].typeName] = json.data[n].rpts[i].num;
                                }
                                pager.tbArray.push(arr);
                                xdata.push(lenDate[i].date);
                            }
                            //总计
                            pager.tbArrayAll = pager.tbArray.slice(0);
                            pager.tbArrayAll.push(arrfoot);
                            var dataZoomEnd = parseFloat(100 * 20 / lenDate).toFixed(2);


                            pager.chartOption = {
                                title: { text: "月租车充值统计金额(元)", left: '6%' },
                                legend: { show: true, data: legends, top: 40 },
                                tooltip: { trigger: "axis" },
                                grid: { show: false, top: 100 },
                                toolbox: {
                                    show: true,
                                    orient: 'vertical',
                                    y: 'center',
                                    feature: {
                                        magicType: { show: true, type: ['bar'] },
                                        restore: { show: true },
                                        saveAsImage: { show: true }
                                    }
                                },
                                xAxis: [{
                                    type: "category", boundaryGap: false, splitLine: { show: false }, boundaryGap: ['50', '50'],
                                    data: xdata
                                }],
                                yAxis: [{ type: "value", splitLine: { show: true, lineStyle: { color: "#f2f2f2" } } }],
                                series: seriesData

                            };

                        }
                        else {
                            pager.chartOption = {
                                title: { text: "暂无数据", left: '6%' },
                                legend: { show: true, data: [], top: 40 },
                                tooltip: { trigger: "axis" },
                                grid: { show: false, top: 100 },
                                toolbox: {
                                    show: true,
                                    orient: 'vertical',
                                    y: 'center',
                                    feature: {
                                        magicType: { show: true, type: ['bar'] },
                                        restore: { show: true },
                                        saveAsImage: { show: true }
                                    }
                                },
                                xAxis: [{
                                    type: "category", boundaryGap: false, splitLine: { show: false }, boundaryGap: ['50', '50'],
                                    data: []
                                }],
                                yAxis: [{ type: "value", splitLine: { show: true, lineStyle: { color: "#f2f2f2" } } }],
                                series: []
                            }
                            pager.tbArray = [];
                            pager.tbHead = ''; //数据报表头
                            pager.tbArrayAll = [];//数据报表数据源(包含总计)
                            pager.tbFooter = []; //数据报表脚
                        }
                        pager.bindShow($('li[class = "active"] > a[data-toggle="tab"]')[0].hash);

                        layer.closeAll();
                    } else {
                        if (json.data == "0") {
                            layer.confirm('统计需要耗费较长时间，是否继续？', function (index) {
                                pager.getdata(false);
                                layer.close(index);
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0, time: 2000 });
                        }
                    }
                }, "json");
            },
            bindEvent: function () {
                $("#Search").click(function () {
                    pager.bindData();
                });
                $("#Export").click(function () {
                    var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                    if (conditionParam.start == null) { layer.msg("请选择开始时间", { icon: 0 }); return; }
                    if (conditionParam.end == null) { layer.msg("请选择结束时间", { icon: 0 }); return; }
                    if (_DATE.diffDay(new Date(conditionParam.start), new Date(conditionParam.end)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }

                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定导出月租车充值统计吗?",
                        yes: function (res) {
                            //实现Ajax下载文件
                            $.fileDownload('/RptCarMonth/Export?' + "conditionParam=" + JSON.stringify(conditionParam), {
                                httpMethod: 'GET',
                                data: null,
                                prepareCallback: function (url) {
                                    $("#Export").attr("disabled", true);
                                    layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                },
                                successCallback: function (url) {
                                    $("#Export").attr("disabled", false);
                                    layer.msg('导出成功');
                                },
                                failCallback: function (html, url) {
                                    $("#Export").attr("disabled", false);
                                    layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                }
                            });
                        },
                        btn2: function () { }
                    })

                });
                //Tab选中
                $('a[data-toggle="tab"]').on('click', function (e) {
                    $(this).tab('show');
                    var activeTab = $(e.target)[0].hash; //获取已激活的标签页的ID
                    pager.bindShow(activeTab, pager.chartHead, 1);
                });

                $("#Delete").click(function () {
                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除当前报表的所有历史统计吗?",
                        area: ["300px"],
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.post("DeleteCache?sdt=" + $("#start").val() + "&edt=" + $("#end").val() + "&export=1", {}, function (json) {
                                if (json.success)
                                    layer.msg("清除成功", { icon: 1, time: 1500 }, function () { pager.bindData(); });
                                else
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                            }, "json");
                        },
                        btn2: function () { }
                    })
                });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "dataType" && data.value == "1") {
                        var layerDataType = layer.open({
                            id: 2,
                            type: 0,
                            title: "查询数据须知",
                            btn: ["知道了"],
                            content: "默认查询范围：当您未指定时间条件时，系统将自动查询当前年份的全部数据。<br/>跨年查询限制：若选择历史数据，查询时间范围需在同一年内。<br/>例如，若开始时间设定为2024年，则仅能查询2024年的相关数据，无法跨年查询2023年或2025年的数据。",
                            yes: function (res) {
                                layer.close(layerDataType)
                            },
                            btn2: function () { }
                        })
                    }
                })
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            },
            bindShow: function (tid, pname, type) {
                if (tid == '#tab-1') {
                    /*处理图表有数据切换为无数据再切换回有数据时图表内容不加载问题*/
                    if (type != 1) {
                        if (mychart != null && mychart != "" && mychart != undefined) {
                            mychart.dispose();
                        }
                        document.getElementById("rate_echart").style.height = (window.innerHeight - 280) + "px";
                        mychart = echarts.init(document.getElementById('rate_echart'));
                        mychart.setOption(pager.chartOption, true);
                        window.onresize = function () {
                            document.getElementById("rate_echart").style.height = (window.innerHeight - 280) + "px";
                            mychart.resize();
                        };
                    }
                }
                else if (tid == '#tab-2') { //绑定表格数据
                    $("#tableFromData").bootstrapTable("destroy");//重置table
                    $("#tableFromData").html(pager.tbHead); //动态设置head
                    $("#tableFromData").bootstrapTable({ //绑定table数据
                        data: pager.tbArray,
                        columns: pager.tbFooter,
                        height: "480"
                    });
                }
            }
        }
        //打印（页面跳转）
        function printRpt() {
            sessionStorage.statTime = $("#end").val();//缓存数据
            sessionStorage.tbHead = pager.tbHead;
            sessionStorage.tbArrayAll = JSON.stringify(pager.tbArrayAll);
            window.open('../RptCarMonth/print', '_blank');
        }
    </script>
</body>
</html>
