﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑管理员</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row {
            margin-bottom: 15px;
        }
    </style>
</head>

<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">车牌号码</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-carno v-submit" maxlength="8" id="BlackList_CarNo"
                        name="BlackList_CarNo" autocomplete="off" value="@ViewBag.CarPrefix" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">开始时间</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-datetime v-submit" id="BlackList_BeginTime"
                        name="BlackList_BeginTime" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-sm2 edit-label ">结束时间</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-datetime v-submit" id="BlackList_EndTime"
                        name="BlackList_EndTime" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">联系人</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input" id="BlackList_Name" name="BlackList_Name" maxlength="50" />
                </div>
                <div class="layui-col-sm2 edit-label ">手机号码</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-phone" id="BlackList_Phone" name="BlackList_Phone"
                        maxlength="20" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label">通行控制</div>
                <div class="layui-col-sm8 edit-ipt-ban">
                    <select class="layui-input" id="BlackList_AccessControl" name="BlackList_AccessControl">
                        <option value="0">不可入不可出</option>
                        <option value="1">可入但不可出</option>
                        <option value="2">不可入但可出</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">备注</div>
                <div class="layui-col-sm8 edit-ipt-ban">
                    <textarea style="height:60px;" class="layui-input v-null" id="BlackList_Remark"
                        name="BlackList_Remark" maxlength="200" autocomplete="off"></textarea>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-sm4 edit-label">&nbsp;</div>
                <div class="layui-col-sm6 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i>
                        <t>保存</t>
                    </button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i>
                        <t>取消</t>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/carnopicker/carnopicker.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>

    <script>
        var paramAct = $.getUrlParam("Act");
        var paramBlackListNo = $.getUrlParam("BlackList_No");
        var dt = new Date().Format("yyyy-MM-dd 00:00:00");
        myVerify.init();

        var comtable = null;
        var laydate = null;
        var layuiForm = null;
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'table', 'jquery', 'form', 'laydate'], function () {
            var admin = layui.admin, table = layui.table, layuiForm = layui.form;
            s_carno_picker.init("BlackList_CarNo", function (text, carno) {
                if (s_carno_picker.eleid == "BlackList_CarNo") {
                    $("#BlackList_CarNo").val(carno.join(''));
                }
            }, "web").bindkeyup();
            layuiForm.render("select")
            laydate = layui.laydate;
            pager.init()
        });
    </script>
    <script>

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                //this.ShowDate("#BlackList_BeginTime", "#BlackList_EndTime");
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {
                _DATE.bind(layui.laydate, ["BlackList_BeginTime", "BlackList_EndTime"], { value0: dt, type: "datetime", range: true });

            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("/BlackList/GetBlackList", { BlackList_No: paramBlackListNo }, function (json) {
                        if (json.Success) {
                            $("#verifyCheck").fillForm(json.Data, function (data) { });
                            // 添加以下代码来设置 BlackList_AccessControl 的值
                            if (json.Data.BlackList_AccessControl !== undefined) {
                                $("#BlackList_AccessControl").val(json.Data.BlackList_AccessControl);
                                layui.form.render("select");
                            }
                        }
                    });
                } else if (paramAct == "Add") {
                    // 如果需要，可以在这里设置默认值
                    $("#BlackList_AccessControl").val("0"); // 设置默认值为 "不可入不可出"
                    layui.form.render("select");
                }
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.BlackList_Name = $("#BlackList_Name").val();
                        data.BlackList_Phone = $("#BlackList_Phone").val();
                        return data;
                    });

                    $("#Save").attr("disabled", true);

                    if (paramAct == "Add") {
                        $.getJSON("/BlackList/AddBlackList", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {

                        param.BlackList_No = paramBlackListNo;
                        $.getJSON("/BlackList/UpdateBlackList", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            }
        };


    </script>
</body>

</html>
