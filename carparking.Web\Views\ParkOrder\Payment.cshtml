﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车支付</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        span { color: black; line-height: 33px; }
        label { line-height: 15px; }
        .ibox-content { padding: 15px; overflow: auto; }
        .input-group { position: relative; display: table; border-collapse: separate; }
        .btnUnit { border-color: #f2eeee !important; border-top-right-radius: 5px; border-bottom-right-radius: 5px; color: #5db587 !important; margin-left: 0 !important; background-color: transparent !important; }

        xm-select > .xm-tips { padding: 0 7px !important; }
        .xtime { color: #de0c3d; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="layui-row layui-form">
                <div class="layui-col-xs5 ">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">订单号：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_No"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">车牌号：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_CarNo"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">订单状态：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_StatusNo"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">车牌颜色：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_CarType"></span></div>
                    </div>
                    @*<div class="layui-row">
                            <div class="layui-col-xs3"><span class="control-label">停车场：</span></div>
                            <div class="layui-col-xs7"><span id="ParkOrder_ParkName"></span></div>
                        </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">入场时间：</span></div>
                        <div class="layui-col-xs7"><span id="ParkOrder_EnterTime"></span></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">停车时长：</span></div>
                        <div class="layui-col-xs8"><span id="ParkOrder_SumTime"></span>&nbsp;<span class="spantime hide">（<t class="xtime">60</t>秒后刷新）</span></div>
                    </div>
                    @* <div class="layui-row">
                            <div class="layui-col-xs3"><span class="control-label">计费时长：</span></div>
                            <div class="layui-col-xs7"><span id="ParkOrder_ChargeTime"></span></div>
                        </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠券：</span></div>
                        <div class="layui-col-xs7" style="margin-left: -8px;">
                            <div id="CouponRecord" class="v-null"></div>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠限制：</span></div>
                        <div class="layui-col-xs7"><span id="Coupon_Count">0</span>张</div>
                    </div>
                    @*<div class="layui-row">
                            <div class="layui-col-xs3"><span class="control-label">优惠额度：</span></div>
                            <div class="layui-col-xs7"><span id="CouponSolution_Value">0</span>元</div>
                        </div>*@
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">应付金额：</span></div>
                        <div class="layui-col-xs7" id="errorLoadPrice">
                            <span id="ParkOrder_TotalAmount">0</span>元
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">优惠金额：</span></div>
                        <div class="layui-col-xs7"><span id="CouponSolution_MoneyValue">0</span>元</div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">储值抵扣：</span></div>
                        <div class="layui-col-xs7"><span id="chuzhiamount">0</span>元</div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label" style="color:red; font-weight:800;">现金实收：</span></div>
                        <div class="layui-col-xs7" id="verifyCheck" style="height:43px;">
                            <div class="input-group" style="margin-left: -8px;">
                                <input type="text" class="layui-input v-null v-float" maxlength="8" min="0" max="********" value="0" data-min="0.01" data-max="********" id="ParkOrder_PayedAccount" name="ParkOrder_PayedAccount" autocomplete="off" placeholder="请填写收费金额（元）" style="padding-left: 8px !important;">
                                <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                            </div>
                            <label class="focus valid"></label>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><span class="control-label">备注：</span></div>
                        <div class="layui-col-xs7" style="margin-left: -8px;">
                            <input type="text" class="layui-input" maxlength="50" id="ParkOrder_Remark" name="ParkOrder_Remark" autocomplete="off">
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs7">
                    <div class="alert alert-info layui-col-xs12">
                        提示：点击图片查看大图
                    </div>
                    <div>
                        <a id="linkEnterImgPath" href="javascript:;" target="_blank"><img src="../../Static/img/nophoto.jpg" style="width: 100%; max-height: 380px;" /></a>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-xs5" style="margin-top: 15px;">
                <div class="layui-col-xs12 layui-col-xs-offset3">
                    <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> 支付</button>&nbsp;&nbsp;
                    <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplstatusno">
        {{if ParkOrder_StatusNo==199}}
        <span>预入场</span>
        {{else ParkOrder_StatusNo==200 && ParkOrder_OutType==0}}
        <span>已入场</span>
        {{else ParkOrder_StatusNo==200 && ParkOrder_OutType==1}}
        <span>预出场</span>
        {{else ParkOrder_StatusNo==201}}
        <span>已出场</span>
        {{else ParkOrder_StatusNo==202}}
        <span>自动关闭</span>
        {{else ParkOrder_StatusNo==203}}
        <span>场内关闭</span>
        {{else ParkOrder_StatusNo==204}}
        <span>欠费出场</span>
        {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();
        var selCoupon = null;
        var xmSelect = null;
        var layuiForm = null;
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['element', 'form', 'xmSelect'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            pager.init();
        });
    </script>
    <script>
        //var index = parent.layer.getFrameIndex(window.name);
        var ParkOrder_No = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var callBack = decodeURIComponent($.getUrlParam("callBack"));
        var interval = null;
        var timeInterval = null;
        var time = 60;

        var pager = {
            couponIDes: "",
            couponIDes2: "",
            uselist: null,
            chuzhiamount: 0,
            couponList: null,
            parktimemin: null,
            accMoney: 0,
            init: function () {
                this.bindData();
                this.bindEvent();
                this.bindPower();
                this.loadPrice();
            },

            //数据绑定
            bindData: function (index) {
                layer.msg('计费中...', { icon: 16, time: 0 });
                $(".spantime").addClass("hide");
                pager.countTime();
                if (ParkOrder_No != null) {
                    $.ajax({
                        type: 'post',
                        url: '/ParkOrder/GetOrderPrice',
                        dataType: 'json',
                        data: { ParkOrder_No: ParkOrder_No, couponIDes: pager.couponIDes },
                        success: function (json) {
                            if (json.success) {
                                $(".spantime").removeClass("hide");
                                var data = json.data.order;
                                if (data) {
                                    $("#ParkOrder_No").html(data.ParkOrder_No);
                                    $("#ParkOrder_CarNo").html(data.ParkOrder_CarNo);
                                    $("#ParkOrder_StatusNo").html($("#tmplstatusno").tmpl(data));
                                    $("#ParkOrder_CarType").html(data.ParkOrder_CarTypeName);
                                    $("#ParkOrder_ParkName").html(data.ParkOrder_ParkName);
                                    $("#ParkOrder_EnterTime").html(data.ParkOrder_EnterTime);

                                    if (data.ParkOrder_EnterImgPath != null && data.ParkOrder_EnterImgPath != "") {
                                        $("#linkEnterImgPath").attr("href", PathCheck(decodeURIComponent(data.ParkOrder_EnterImgPath)))[0].children[0].src = PathCheck(decodeURIComponent(data.ParkOrder_EnterImgPath));
                                    }

                                    if (data.ParkOrder_TotalAmount == null) {
                                        $("#Save").attr("disabled", true);
                                        $("#errorLoadPrice").html("获取价格失败,请联系管理员!");
                                        $("#errorLoadPrice").wrapInner("<span><font color='red'/></span>");
                                    } else {
                                        $("#errorLoadPrice").html('<span id="ParkOrder_TotalAmount">' + data.ParkOrder_TotalAmount + '</span>元'); //总共需支付金额
                                        pager.accMoney = data.ParkOrder_TotalAmount;

                                        var policyCount = json.data.policy;
                                        if (policyCount == null) policyCount = 0;
                                        var couponmoney = 0;
                                        var coupon = json.data.coupon;
                                        if (coupon && coupon.money && coupon.money > 0) {
                                            pager.uselist = coupon.uselist;
                                            couponmoney = coupon.money;
                                            $("#CouponSolution_MoneyValue").html(coupon.money);
                                        } else {
                                            pager.uselist = null;
                                            $("#CouponSolution_MoneyValue").html(0);
                                        }

                                        if (json.data.chuzhiamount != null) pager.chuzhiamount = json.data.chuzhiamount; else pager.chuzhiamount = 0;
                                        $("#chuzhiamount").html(pager.chuzhiamount);

                                        $("#ParkOrder_PayedAccount").val(json.data.payedamount);

                                        if (index != "1") {
                                            $("#Coupon_Count").html(policyCount);
                                            var CouponInfoList = json.data.couponinfolist;
                                            //绑定优惠方式
                                            if (CouponInfoList && CouponInfoList != null && CouponInfoList.length > 0) {
                                                pager.couponList = CouponInfoList;
                                                if (pager.couponList && pager.couponList.length > 0) {
                                                    var newlist = [];
                                                    for (var i = 0; i < pager.couponList.length; i++) {
                                                        newlist[i] = {
                                                            "name": pager.couponList[i].CouponRecord_Name,
                                                            "value": pager.couponList[i].CouponRecord_ID
                                                        };
                                                    }
                                                    selCoupon = xmSelect.render({
                                                        el: '#CouponRecord',
                                                        name: 'CouponRecord',
                                                        layVerify: 'required',
                                                        layVerType: 'msg',
                                                        filterable: true,
                                                        height: '130px',
                                                        max: policyCount,
                                                        data: newlist,
                                                        on: function (data) {
                                                            $("#CouponSolution_MoneyValue").val(0);
                                                            clearInterval(interval);
                                                            interval = null;
                                                            setTimeout(function () {
                                                                var arr = data.arr;
                                                                pager.couponIDes = "";
                                                                var index = 0;
                                                                $.each(arr, function (n, v) {
                                                                    pager.couponIDes += v.value + ",";
                                                                    index++;
                                                                })
                                                                if (index > policyCount) {
                                                                    $("#Save").attr("disabled", true);
                                                                    layer.msg("优惠券一次允许使用" + policyCount + "张，勿超过策略使用限制!", { icon: 0, btn: ['确定'], time: 0 });
                                                                    return;
                                                                }
                                                                pager.couponIDes = pager.couponIDes.substring(0, pager.couponIDes.length - 1);
                                                                pager.couponIDes2 = pager.couponIDes;
                                                                pager.bindData("1");
                                                                setTimeout(function () { pager.couponIDes = ""; pager.loadPrice(); }, 2000);
                                                            }, 100)
                                                        },
                                                    })
                                                }
                                            } else {
                                                pager.couponList = "";
                                                selCoupon = xmSelect.render({
                                                    el: '#CouponRecord',
                                                    name: 'CouponRecord',
                                                    layVerify: 'required',
                                                    layVerType: 'msg',
                                                    autoRow: true,
                                                    data: []
                                                })
                                            }
                                        } else {
                                            pager.couponList = "";
                                        }

                                        layui.form.render("select");

                                        //根据入场时间计算停车时长
                                        var endtime = new Date($.ajax({ async: false }).getResponseHeader("Date"));
                                        if (data.ParkOrder_StatusNo == 204) endtime = new Date(data.ParkOrder_OutTime);
                                        var zhTimes = _DATE.getZhTimes(new Date(data.ParkOrder_EnterTime), endtime);
                                        $("#ParkOrder_SumTime").html(zhTimes);
                                        if (json.data.parktimemin != null) {
                                            pager.parktimemin = json.data.parktimemin;
                                            console.log(Math.floor(pager.parktimemin / 60) + "小时" + Math.floor(pager.parktimemin % 60) + "分钟 收：" + data.ParkOrder_TotalAmount);
                                            var endDate = new Date(new Date(data.ParkOrder_EnterTime).setMinutes(new Date(data.ParkOrder_EnterTime).getMinutes() + pager.parktimemin));
                                            $("#ParkOrder_ChargeTime").html(_DATE.getZhTimes(new Date(data.ParkOrder_EnterTime), endDate));
                                        }
                                    }
                                }
                                $("#ParkOrder_PayedAccount").focus();
                                layer.closeAll();
                            } else {
                                layer.msg('操作失败：' + json.msg, { icon: 5 });
                            }

                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('订单ID无效', { icon: 0 });
                }
            },
            bindEvent: function () {
                $('#ParkOrder_PayedAccount').on("inttext", function () {
                    var value = $(this).val();
                    if (value == 0) {
                        $(this).val(0);
                        $(this).removeClass("border-red");
                    }
                    else {
                        var max = parseFloat($(this).attr("data-max"));
                        var min = parseFloat($(this).attr("data-min"));
                        if (value > max) value = max;
                        if (value < min) value = min;
                        var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
                        if (!reg.test(value)) {
                            value = parseFloat(value).toFixed(2);
                        }
                        $(this).val(value);
                        $(this).removeClass("border-red");
                    }
                });

                $('#ParkOrder_PayedAccount').on("blur", function () {
                    var value = $(this).val();
                    if (value == 0) {
                        $(this).val(0);
                        $(this).removeClass("border-red");
                    }
                    else {
                        var max = parseFloat($(this).attr("data-max"));
                        var min = parseFloat($(this).attr("data-min"));
                        if (value > max) value = max;
                        if (value < min) value = min;
                        var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
                        if (!reg.test(value)) {
                            value = parseFloat(value).toFixed(2);
                        }
                        $(this).val(value);
                        $(this).removeClass("border-red");
                    }
                });
                $("#Cancel").click(function () { parent.layer.closeAll(); });
                $("#Save").click(function () {
                    //验证表单
                    if (!myVerify.check()) return;
                    var payedMonty = $("#ParkOrder_PayedAccount").val();
                    if (payedMonty < 0) { layer.msg("请输入正确金额"); return; }
                    if (payedMonty > pager.accMoney) { layer.tips("支付金额不能大于应付金额", $("#ParkOrder_PayedAccount"), { time: 2000 }); return; }

                    //询问框
                    layer.confirm('确定要缴费？', {
                        title: "提示",
                        icon: "3",
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        $("#Save").attr("disabled", true);
                        layer.msg('保存中...', { icon: 16, time: 0 });
                        $.ajax({ //线上缴费下发通知（注销使用的优惠券）
                            type: 'post',
                            url: '/ParkOrder/SendNoticeForPayed',
                            dataType: 'json',
                            data: {
                                ParkOrder_No: ParkOrder_No, receAmount: $("#ParkOrder_TotalAmount").text(), remark: $("#ParkOrder_Remark").val(), CouponRecord_ID: JSON.stringify(selCoupon.getValue('value')), paidAmount: $("#ParkOrder_PayedAccount").val(), parktimemin: pager.parktimemin, uselist: pager.uselist, chuzhiamount: pager.chuzhiamount
                            },
                            success: function (json) {
                                if (json.success) {
                                    if (callBack == 1)
                                        parent.pager.Continue(json.data.ParkOrderNo);
                                    else {
                                        layer.msg(json.msg, { icon: 1, time: 2000 }, function () {
                                            window.parent.pager.bindData(window.parent.pager.pageIndex);
                                        });
                                    }
                                }
                                else {
                                    layer.msg('保存失败：' + json.msg, { icon: 0 });
                                }
                            },
                            error: function () {
                                layer.msg('系统错误', { icon: 2 });
                            }
                        });
                    }, function () { });

                });
            },
            bindPower: function () {
                var global = window.parent.parent.global || window.parent.parent.parent.global;
                global.getBtnPower(window, function (pagePower) {
                    $(".btnUnit").removeClass("layui-hide")
                });
            },
            loadPrice: function () {
                if (interval != null) {
                    clearInterval(interval);
                    interval = null;
                }
                interval = setInterval(function () { pager.bindData(); }, 60 * 1000);
            },
            countTime: function () {
                time = 60;
                if (timeInterval != null) {
                    clearInterval(timeInterval);
                    timeInterval = null;
                }
                timeInterval = setInterval(function () {
                    if (time > 0) time--;
                    else { time = 60; }
                    $(".xtime").html(time);
                }, 1000);
            }
        };
    </script>
</body>
</html>
