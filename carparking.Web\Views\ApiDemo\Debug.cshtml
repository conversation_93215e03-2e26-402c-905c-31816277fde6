﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>调试</title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <style>
        html, body { width: 100%; height: 100%; background-color: #f9f9f9; overflow: hidden; }
        .left { width: 50%; height: 100%; float: left; overflow: auto; }
        .right { width: 50%; height: 100%; float: left; overflow: auto; }
        .layui-form-item { clear: none; }
        .layui-btn { margin-top: 5px; }
        .layui-card-body{overflow:auto;}
     /*   .layui-card { width: 50%; min-height: 100%; float: left; background-color: #f9f9f9; overflow: auto; }
        .card-res { width: 50%; float: right; }
        .card-res::before { content: ""; border: 1px solid #000; position: absolute; width: 0px; height: 100%; }
        .layui-form-label { width: 120px; font-weight: bold; }
        .layui-input-block { margin-left: 150px; }
        .layui-input { color: #300bee; }
        .layui-input[readonly] { background-color: #f5f5f5; }
        .layui-btn[disabled] { background-color: #666 !important; }*/
    </style>
</head>
<body>
    <div class="left">
        <div class="layui-card">
            <div class="layui-card-header">[@ViewBag.Park.Parking_Name] 车辆进出场模拟</div>
            <div class="layui-card-body layui-form" id="form1">
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">parkno</label>
                    <div class="layui-input-block">
                        <input type="text" id="parkno" name="parkno" placeholder="请输入停车场编码" class="layui-input" value="@ViewBag.Park.Parking_No">
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">camerano</label>
                    <div class="layui-input-block">
                        <select class="layui-select" lay-search id="camerano" name="camerano">
                            <option value="">选择相机</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">carno</label>
                    <div class="layui-input-block">
                        <input type="text" id="carno" name="carno" placeholder="请输入车牌号" class="layui-input" value="粤A12345">
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">cartype</label>
                    <div class="layui-input-block">
                        <select class="layui-select" lay-search id="cartype" name="cartype">
                            <option value="">车牌颜色</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">mode</label>
                    <div class="layui-input-block">
                        <select class="layui-select" id="mode" name="mode" lay-search>
                            <option value="">识别模式</option>
                            <option value="1" selected>相机识别</option>
                            <option value="2">扫码</option>
                            <option value="3">ETC</option>
                            <option value="4">刷卡</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">time</label>
                    <div class="layui-input-block">
                        <input type="text" id="time" name="time" placeholder="时间" class="layui-input" value="@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")">
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs12">
                    <label class="layui-form-label">img</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" id="img" name="img" placeholder="出入场抓拍图"></textarea>
                    </div>
                </div>
                <div class="layui-form-item  layui-col-xs12">
                    <label class="layui-form-label">&nbsp;</label>
                    <div class="layui-input-block">
                        <button class="layui-btn layui-btn-sm" id="OnSend"><i class="layui-icon layui-icon-camera"></i><text>相机识别</text></button>
                    </div>
                </div>

                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">orderno</label>
                    <div class="layui-input-block">
                        <input type="text" id="orderno" name="orderno" placeholder="停车订单号" class="layui-input" value="">
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">orderdetailno</label>
                    <div class="layui-input-block">
                        <input type="text" id="orderdetailno" name="orderdetailno" placeholder="停车明细订单号" class="layui-input" value="">
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">account</label>
                    <div class="layui-input-block">
                        <input type="text" id="account" name="account" placeholder="操作员账号" class="layui-input" value="admin">
                    </div>
                </div>
                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">name</label>
                    <div class="layui-input-block">
                        <input type="text" id="name" name="name" placeholder="操作员名称" class="layui-input" value="admin">
                    </div>
                </div>

                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">ordermoney</label>
                    <div class="layui-input-block">
                        <input type="text" id="ordermoney" name="ordermoney" placeholder="订单金额" class="layui-input" value="">
                    </div>
                </div>

                <div class="layui-form-item layui-col-xs6">
                    <label class="layui-form-label">cashmoney</label>
                    <div class="layui-input-block">
                        <input type="text" id="cashmoney" name="cashmoney" placeholder="请输入缴费金额" class="layui-input" value="">
                    </div>
                </div>

                <div class="layui-row layui-col-xs-offset2">
                    <div class="layui-inline"><button class="layui-btn layui-btn-sm" id="OnPass"><i class="layui-icon layui-icon-ok"></i><text>入口开闸放行</text></button></div>
                    <div class="layui-inline"><button class="layui-btn layui-btn-sm" id="OnPay"><i class="layui-icon layui-icon-right"></i><text>出口支付放行（camerano+carno+time）</text></button></div>
                    <div class="layui-inline"><button class="layui-btn layui-btn-sm" id="OnPass2"><i class="layui-icon layui-icon-right"></i><text>出口开闸放行</text></button></div>
                    <div class="layui-inline"><button class="layui-btn layui-btn-sm" id="OnBackCar"><i class="layui-icon layui-icon-left"></i><text>倒车</text></button></div>
                    <div class="layui-inline"><button class="layui-btn layui-btn-sm" id="OnCarFollow"><i class="layui-icon layui-icon-right"></i><text>跟车</text></button></div>
                    <div class="layui-inline"><button class="layui-btn layui-btn-sm" id="OnPayedCash"><i class="layui-icon layui-icon-rmb"></i><text>模拟自助机缴费</text></button></div>
                </div>
            </div>
        </div>
    </div>
    <div class="right">
        <div class="card-res">
            <div class="layui-card-body">
                <div class="layui-form-item">
                    <label class="layui-form-label">未加密参数</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" style="height:50px;" id="uparam"></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">返回结果</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" style="height:600px;" id="result"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    

    <script type="text/javascript">
        layui.use(['form'], function () {

            pager.init();
        });
    </script>
    <script type="text/javascript">
        var pager = {
            payData: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetDeviceList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.Device_No + '">' + item.Device_Name + '</option>';
                            $("#camerano").append(option);
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.CarType_No + '">' + item.CarType_Name + '</option>';
                            $("#cartype").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select");
            },
            bindData: function () {

            },
            bindEvent: function () {
                $("#OnSend").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });

                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("InOutResult", { jsonModel: jsonModel }, function (json) {
                        layer.closeAll();
                        pager.payData = json.payres;
                        $("#result").val(JSON.stringify(json, undefined, 4));
                        if (json.passres) {
                            $("#orderno").val(json.passres.parkorderno);
                            $("#orderdetailno").val(json.passres.orderdetailno);
                            if (json.payres != null)
                                $("#ordermoney").val(json.payres.payedamount);
                        }
                    }, "json");
                });


                $("#OnPass").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });
                    param.code = 200;
                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("PassResult", { jsonModel: jsonModel }, function (json) {
                        layer.closeAll();
                        $("#result").val(JSON.stringify(json, undefined, 4));
                    }, "json");
                });

                $("#OnPay").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });
                    param.code = 200;
                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("PayCar", { carno: $("#carno").val(), camerano: $("#camerano").val(), time: $("#time").val() }, function (json) {
                        layer.closeAll();

                        $("#result").val(JSON.stringify(json, undefined, 4));
                    }, "json");
                });


                $("#OnPass2").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });
                    param.code = 201;
                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("PassResult", { jsonModel: jsonModel }, function (json) {
                        layer.closeAll();
                        $("#result").val(JSON.stringify(json, undefined, 4));
                    }, "json");
                });

                $("#OnBackCar").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) { return data; });

                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("OnBackCar", { jsonModel: jsonModel }, function (json) {
                        layer.closeAll();
                        $("#result").val(JSON.stringify(json, undefined, 4));
                    }, "json");
                });

                $("#OnCarFollow").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) { return data; });

                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("OnCarFollow", { jsonModel: jsonModel }, function (json) {
                        layer.closeAll();
                        $("#result").val(JSON.stringify(json, undefined, 4));
                    }, "json");
                });

                $("#OnPayedCash").click(function () {
                    //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) { return data; });

                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("OnPayedCash", { jsonModel: jsonModel, ordermoney: $("#ordermoney").val(), cashmoney: $("#cashmoney").val(), }, function (json) {
                        layer.closeAll();
                        $("#result").val(JSON.stringify(json, undefined, 4));
                    }, "json");
                });
            }
        }

    </script>
</body>
</html>