﻿using carparking.Cache.Web;
using carparking.Common;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using carparking.BLL.Cache;
using NPOI.Util;

namespace carparking.BLL
{
    public class Car
    {
        static DAL.Car dal = new DAL.Car();

        #region 模板生成

        /// <summary>
        /// 是否存在该记录,自定义条件
        /// </summary>
        public static bool Exists(string selectWhere)
        {
            return dal.Exists(selectWhere);
        }

        public static int Add(Model.API.PushResultParse.CarOwner data)
        {
            return dal.Add(data);
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public static int UpdateByModel(Model.Car model)
        {
            var ret = dal.UpdateByModel(model);
            if (ret >= 0 && (AppBasicCache.ReadWriteCache))
            {
                AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, model.Car_CarNo, model);
            }

            return ret;
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public static int UpdateByList(List<Model.Car> modelList)
        {
            var ret = dal.UpdateByList(modelList);
            if (ret >= 0 && (AppBasicCache.ReadWriteCache))
            {
                modelList.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }
            return ret;
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public static int UpdateByCameraBody(List<Model.CarExt> modelList)
        {
            var ret = dal.UpdateByCameraBody(modelList);
            if (ret >= 0 && (AppBasicCache.ReadWriteCache))
            {
                modelList.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }
            return ret;
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public static int DeleteByNo(Model.Car car, bool isCarUnbound = true)
        {
            var ret = DeleteByList(new List<Model.Car>() { car }, isCarUnbound);
            if (ret >= 0 && (AppBasicCache.ReadWriteCache))
            {
                AppBasicCache.DeleteElement(AppBasicCache.GetCar, car.Car_CarNo, car);
            }
            return ret;
        }

        public static int DeleteByList(List<Model.Car> carList, bool isCarUnbound = true)
        {
            int res = 0;
            if (carList == null || carList.Count == 0) return res;

            res = dal.DeleteByList(carList, isCarUnbound);
            if (res >= 0 && (AppBasicCache.ReadWriteCache))
            {
                carList.ForEach(x => { AppBasicCache.DeleteElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }

            return res;
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public static Model.Car GetEntity(string Car_No)
        {
            return dal.GetEntity(Car_No);
        }


        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public static Model.Car GetEntityByCarNo(string Car_CarNo)
        {
            if (AppBasicCache.ReadWriteCache && AppBasicCache.GetPolicyPark?.PolicyPark_BusinessCache == 1)
            {
                AppBasicCache.GetCar.TryGetValue(Car_CarNo, out var model);
                return model?.Copy();
            }
            else
            {

                var model = BLL.Car.GetEntity(Car_CarNo);
                return model;
            }
        }

        /// <summary>
        /// 得到一个对象实体(通过卡号)
        /// </summary>
        public static Model.Car GetEntityByCardNo(string Car_CardNo)
        {
            var parameters = new { Car_CardNo = Car_CardNo };
            Model.Car model = BLL.Car.GetEntity("*", $"Car_CardNo=@Car_CardNo", parameters);
            return model;
        }

        /// <summary>
        /// 自定义获取实体
        /// </summary>
        /// <param name="fields"></param>
        /// <param name="selectWhere"></param>
        /// <returns></returns>
        public static Model.Car GetEntity(string fields, string selectWhere, object parameters = null)
        {
            return dal.GetEntity(fields, selectWhere, parameters);
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.Car> GetList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord)
        {
            return dal.GetList(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord);
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.CarExt> GetExt2List(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord, object parameters = null)
        {
            List<Model.CarExt> model = dal.GetExt2List(showFields, selectWhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);
            model = AES.Convert(model, false);
            return model;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.CarExt> GetExt2List(string showFields, string selectWhere, int pageIndex, int pageSize, string sortField, int sortType, out int pageCount, out int totalRecord)
        {
            List<Model.CarExt> model = dal.GetExt2List(showFields, selectWhere, pageIndex, pageSize, sortField, sortType, out pageCount, out totalRecord);
            model = AES.Convert(model, false);
            return model;
        }


        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.CarExt> GetCarExtAllEntity(string showFields, string selectWhere)
        {
            List<Model.CarExt> model = dal.GetCarExtAllEntity(showFields, selectWhere);
            model = AES.Convert(model, false);
            return model;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static int GetCarCount(string selectWhere)
        {
            var count = dal.GetCarCount(selectWhere);
            return count;
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public static List<Model.CarOwnerExt> GetCarOwnerAllEntity(string showFields, string selectWhere)
        {
            List<Model.CarOwnerExt> model = dal.GetCarOwnerAllEntity(showFields, selectWhere);
            model = AES.Convert(model, false);
            return model;
        }

        /// <summary>
        /// 获取少量实体
        /// </summary>
        /// <param name="showFields">字段</param>
        /// <param name="selectWhere">查询条件</param>
        /// <returns></returns>
        public static List<Model.Car> GetAllEntity(string showFields, string selectWhere, object parameters = null)
        {
            return dal.GetAllEntity(showFields, selectWhere, parameters);
        }


        /// <summary>
        /// 获取月租车总数
        /// </summary>
        /// <returns></returns>
        public static int GetCountSum()
        {
            return dal.GetCountSum();
        }

        /// <summary>
        /// 获取注销月租车总数
        /// </summary>
        /// <returns></returns>
        public static int GetUnCountSum()
        {
            return dal.GetUnCountSum();
        }

        #endregion

        #region 其他方法

        public static int AddCarOwner(Model.Car car, Model.Owner owner = null, List<Model.StopSpace> spaces = null, Model.PayColl payColl = null, Model.ParkOrder order = null, List<Model.OrderDetail> details = null, List<Model.Ledger> ledgerList = null)
        {

            if (owner != null && !string.IsNullOrWhiteSpace(owner.Owner_Phone) && owner.Owner_Phone.Length > 4)
                owner.Owner_PhoneLastFour = owner.Owner_Phone.Substring(owner.Owner_Phone.Length - 4);

            owner = AES.Convert(owner);
            var ret = dal.AddCarOwner(car, owner, spaces, payColl, order, details, ledgerList);
            if (ret >= 0 && (AppBasicCache.ReadWriteCache))
            {
                if (car != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, car.Car_CarNo, car);
                if (owner != null) AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, owner.Owner_No, owner);
            }

            return ret;
        }

        public static bool AddCarOwnerList(List<Model.Car> carList, List<Model.Owner> ownerList)
        {
            if (ownerList != null)
            {
                ownerList.ForEach(owner =>
                {
                    if (!string.IsNullOrWhiteSpace(owner.Owner_Phone) && owner.Owner_Phone.Length > 4)
                        owner.Owner_PhoneLastFour = owner.Owner_Phone.Substring(owner.Owner_Phone.Length - 4);
                });

                ownerList = AES.Convert(ownerList);
            }
            var ret = dal.AddCarOwnerList(carList, ownerList);
            if (ret)
            {
                ownerList?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x); });
                carList?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x); });
            }

            return ret;
        }

        public static bool AddCarOwnerList(List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> spaceList, List<Model.ParkOrder> parkOrderList = null,
            List<Model.OrderDetail> orderDetailList = null, List<Model.PayOrder> payorderList = null, List<Model.PayPart> paypartList = null, List<Model.Ledger> ledgerList = null)
        {
            if (ownerList != null)
            {
                ownerList.ForEach(owner =>
                {
                    if (!string.IsNullOrWhiteSpace(owner.Owner_Phone) && owner.Owner_Phone.Length > 4)
                        owner.Owner_PhoneLastFour = owner.Owner_Phone.Substring(owner.Owner_Phone.Length - 4);
                });

                ownerList = AES.Convert(ownerList);
            }

            var ret = dal.AddCarOwnerList(carList, ownerList, spaceList, parkOrderList, orderDetailList, payorderList, paypartList, ledgerList);
            if (ret)
            {
                ownerList?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetOwner, x.Owner_No, x, true); });
                carList?.ForEach(x => { AppBasicCache.AddOrUpdateElement(AppBasicCache.GetCar, x.Car_CarNo, x, true); });
            }
            return ret;
        }

        public static Model.CarExt GetCarExtEntityByCarNo(string Car_CarNo)
        {
            Model.CarExt model = dal.GetCarExtEntityByCarNo(Car_CarNo);
            model = AES.Convert(model, false);
            return model;
        }

        public static Model.CarExt GetCarExtEntityByNo(string Car_No)
        {
            Model.CarExt model = dal.GetCarExtEntityByNo(Car_No);
            model = AES.Convert(model, false);
            return model;
        }

        public static Model.CarExt GetCarExtEntityBy(string selectWhere)
        {
            Model.CarExt model = dal.GetCarExtEntityBy(selectWhere);
            model = AES.Convert(model, false);
            return model;
        }

        /// <summary>
        /// 月租车结束时间增加宽限天数
        /// </summary>
        /// <param name="model"></param>
        public static void CarAddEnDay(ref Model.Car model, Model.PolicyCarCard policy)
        {
            if (model == null) return;
            if (model.Car_EndTime == null) return;
            if (policy == null) policy = BLL.PolicyCarCard.GetEntityByCarCard(model.Car_TypeNo);
            if (policy == null) return;

            model.Car_EndTime = model.Car_EndTime.Value.AddDays(policy.PolicyCarCard_AllowExpireDay.Value);
        }

        /// <summary>
        /// 月租车结束时间增加宽限天数
        /// </summary>
        /// <param name="model"></param>
        public static void OwnerAddEnDay(ref Model.Owner model, Model.PolicyCarCard policy)
        {
            if (model == null) return;
            if (model.Owner_EndTime == null) return;
            if (policy == null) policy = BLL.PolicyCarCard.GetEntityByCarCard(model.Owner_CardTypeNo);
            if (policy == null) return;

            model.Owner_EndTime = model.Owner_EndTime.Value.AddDays(policy.PolicyCarCard_AllowExpireDay.Value);
        }

        /// <summary>
        /// 校验新增车辆参数
        /// </summary>
        /// <param name="jsonModel">参数值</param>
        /// <param name="parking">停车场信息</param>
        /// <param name="car">返回车辆信息</param>
        /// <param name="owner">返回车主信息</param>
        /// <param name="card">返回车牌类型信息</param>
        /// <param name="carType">返回车牌颜色信息</param>
        /// <param name="payOrder">返回支付订单</param>
        /// <param name="errmsg">错误描述</param>
        /// <param name="admin">当前管理员</param>
        /// <param name="isPayed">是否需要创建续费支付订单</param>
        /// <returns></returns>
        public static bool checkAddParam(string jsonModel, Model.Parking parking, Model.CarEdit CarEdit
            , ref Model.Car car
            , ref Model.Owner owner
            , out Model.CarCardType card
            , out Model.CarType carType
            , out Model.PayOrder payOrder
            , out List<Model.StopSpace> spaces
            , out string errmsg
            , Model.AdminSession admin = null
            , bool isPayed = true)
        {
            if (CarEdit == null) CarEdit = JObject.Parse(jsonModel).ToObject<Model.CarEdit>();
            if (car == null) car = JObject.FromObject(CarEdit).ToObject<Model.Car>();
            if (owner == null) owner = JObject.Parse(jsonModel).ToObject<Model.Owner>();

            card = null;
            carType = null;
            payOrder = new Model.PayOrder();
            spaces = new List<Model.StopSpace>();
            errmsg = string.Empty;

            if (string.IsNullOrWhiteSpace(car.Car_CarNo)) { errmsg = "车牌号不能为空"; return false; }
            if (string.IsNullOrWhiteSpace(car.Car_VehicleTypeNo)) { errmsg = "车牌颜色不能为空"; return false; }
            if (string.IsNullOrWhiteSpace(car.Car_TypeNo)) { errmsg = "车牌类型不能为空"; return false; }

            var isExist = BLL.Car.GetEntityByCarNo(car.Car_CarNo);
            if (isExist != null) { errmsg = "车牌号已登记，不能重复登记"; return false; }

            if (!string.IsNullOrWhiteSpace(car.Car_CardNo))
            {
                var isExist2 = BLL.Car.GetEntityByCardNo(car.Car_CardNo);
                if (isExist2 != null) { errmsg = "卡号已登记，不能重复登记"; return false; }
            }

            var black = BLL.BaseBLL._GetEntityByWhere(new Model.BlackList(), "BlackList_CarNo", $"BlackList_CarNo='{car.Car_CarNo}' AND Blacklist_Status=1");
            if (black != null) { errmsg = "已登记为黑名单,请先删除黑名单"; return false; }

            card = BLL.CarCardType.GetEntity(CarEdit.Car_TypeNo);
            if (card == null) { errmsg = "车牌类型错误"; return false; }
            carType = BLL.CarType.GetEntity(CarEdit.Car_VehicleTypeNo);
            if (carType == null) { errmsg = "车牌颜色错误"; return false; }

            var cardCode = 0;//0-无需生成订单，1-月租车充值，2-储值车充值
            if (Model.CarCardTypeEnumList.carTemp.Contains(Utils.StrToInt(card.CarCardType_Category, 0)) ||
                Model.CarCardTypeEnumList.carVisitor.Contains(Utils.StrToInt(card.CarCardType_Category, 0)))
            {
                if (CarEdit.Car_BeginTime1 == null) { errmsg = "开始时间不能为空"; return false; }
                if (CarEdit.Car_EndTime1 == null) { errmsg = "结束时间不能为空"; return false; }
                if (CarEdit.Car_EndTime1 < CarEdit.Car_BeginTime1) { errmsg = "开始时间不能大于结束时间"; return false; }
                car.Car_BeginTime = CarEdit.Car_BeginTime1;
                car.Car_EndTime = CarEdit.Car_EndTime1;
            }
            else if (Model.CarCardTypeEnumList.carMonth.Contains(Utils.StrToInt(card.CarCardType_Category, 0)) ||
                 Model.CarCardTypeEnumList.carFree.Contains(Utils.StrToInt(card.CarCardType_Category, 0)) ||
               Model.CarCardTypeEnumList.carPrepaid.Contains(Utils.StrToInt(card.CarCardType_Category, 0)))
            {
                cardCode = 1;
                if (CarEdit.Car_BeginTime0 == null) { errmsg = "开始时间不能为空"; return false; }
                if (CarEdit.Car_EndTime0 == null) { errmsg = "结束时间不能为空"; return false; }
                if (CarEdit.Car_EndTime0 < CarEdit.Car_BeginTime0) { errmsg = "开始时间不能大于结束时间"; return false; }
                if (CarEdit.Car_PayMoney == null) { errmsg = "支付金额不能为空"; return false; }
                if (CarEdit.Car_PayMoney < 0) { errmsg = "支付金额不能小于0"; return false; }

                if (Model.CarCardTypeEnumList.carPrepaid.Contains(Utils.StrToInt(card.CarCardType_Category, 0)))
                {
                    cardCode = 2;
                    if (CarEdit.Car_Balance == null) { errmsg = "充值金额不能为空"; return false; }
                    if (CarEdit.Car_Balance - CarEdit.Car_PayMoney < 0) { errmsg = "支付金额不能大于充值金额"; return false; }
                }

                car.Car_BeginTime = CarEdit.Car_BeginTime0;
                car.Car_EndTime = CarEdit.Car_EndTime0;
            }

            var isExistOwner = BLL.Owner.GetEntity("*", $"Owner_Space='{owner.Owner_Space}'");
            if (isExistOwner != null)
            {
                if (!string.IsNullOrWhiteSpace(owner.Owner_No) && owner.Owner_No == isExistOwner.Owner_No)
                {
                    owner = Utils.Returnobj(isExistOwner, owner);
                    owner.Owner_PhoneLastFour = owner.Owner_Phone?.Substring((owner.Owner_Phone.Length > 4 ? (owner.Owner_Phone.Length - 4) : 0));
                }
                else
                {
                    errmsg = "车位号已被使用,如需使用相同车位号,请[选择]";
                    return false;
                }

                if (card.CarCardType_IsMoreCar == 1)
                {
                    owner.Owner_Balance = Utils.ObjectToDecimal(owner.Owner_Balance, 0) + Utils.ObjectToDecimal(car.Car_Balance, 0);
                    car.Car_Balance = 0;
                }
            }
            else
            {
                if (card.CarCardType_IsMoreCar == 1)
                {
                    owner.Owner_Balance = car.Car_Balance;
                    car.Car_Balance = 0;
                }
                owner.Owner_No = Utils.CreateNumber;
                owner.Owner_AddID = admin?.Admins_ID;
                owner.Owner_AddTime = DateTimeHelper.GetNowTime();
                owner.Owner_ParkNo = parking.Parking_No;
                owner.Owner_StartTime = card.CarCardType_IsMoreCar == 1 ? car.Car_BeginTime : DateTimeHelper.GetNowTime().AddDays(-1);
                owner.Owner_EndTime = card.CarCardType_IsMoreCar == 1 ? car.Car_EndTime : DateTimeHelper.GetNowTime().AddDays(-1);
                owner.Owner_SpaceNum = 1;
                owner.Owner_PhoneLastFour = owner.Owner_Phone?.Substring((owner.Owner_Phone.Length > 4 ? (owner.Owner_Phone.Length - 4) : 0));
            }

            car.Car_No = Utils.CreateNumber;
            car.Car_AddTime = DateTimeHelper.GetNowTime();
            car.Car_ParkingNo = parking.Parking_No;
            car.Car_Status = 1;
            car.Car_OnLine = 1;
            car.Car_AddID = admin?.Admins_ID;
            car.Car_OwnerNo = owner.Owner_No;
            car.Car_OwnerName = owner.Owner_Name;
            car.Car_Category = card.CarCardType_Category;
            car.Car_IsMoreCar = card.CarCardType_IsMoreCar;
            car.Car_Balance = car.Car_Balance ?? 0;
            car.Car_OrderSpace = string.Empty;
            car.Car_OwnerSpace = owner?.Owner_Space;

            if (isPayed)
            {
                //创建支付订单
                //payOrder = BLL.PayOrder.CreateAddCarOrder(cardCode, parking, car, owner, CarEdit.Car_PayMoney, card, admin);
            }

            if (isExistOwner == null)
            {
                spaces.Add(new Model.StopSpace()
                {
                    StopSpace_No = Utils.CreateNumber,
                    StopSpace_ParkNo = parking.Parking_No,
                    StopSpace_OwnerNo = owner.Owner_No,
                    StopSpace_Type = 0,
                    StopSpace_Number = 1,
                    StopSpace_AddTime = DateTimeHelper.GetNowTime()
                });
            }

            return true;
        }

        /// <summary>
        /// 校验修改车辆参数
        /// </summary>
        /// <param name="jsonModel">参数值</param>
        /// <param name="car">返回车辆信息</param>
        /// <param name="owner">返回车主信息</param>
        /// <param name="isEditType">返回是否修改的车牌类型或车牌颜色(判断是否需要缴费)</param>
        /// <param name="card">返回新的车牌类型</param>
        /// <param name="carType">返回新的车牌颜色</param>
        /// <param name="errmsg">返回错误消息</param>
        /// <param name="admin">当前管理员</param>
        /// <returns></returns>
        public static bool checkUpdateParam(string jsonModel, ref Model.Car car, ref Model.Owner owner, out bool isEditType, out Model.CarCardType card, out Model.CarType carType, out string errmsg, Model.AdminSession admin = null)
        {
            card = null;
            carType = null;
            isEditType = false;
            errmsg = string.Empty;
            Model.CarEdit carEdit = JObject.Parse(jsonModel).ToObject<Model.CarEdit>();
            if (carEdit == null || string.IsNullOrWhiteSpace(carEdit.Car_No)) { errmsg = "参数错误,请重试"; return false; }
            car = BLL.Car.GetEntity(carEdit.Car_No);
            if (car == null) { errmsg = "车辆信息不存在"; return false; }
            if (car.Car_CarNo != carEdit.Car_CarNo) { errmsg = "车牌号不允许修改"; return false; }
            isEditType = (car.Car_VehicleTypeNo != carEdit.Car_VehicleTypeNo || car.Car_TypeNo != carEdit.Car_TypeNo);

            if (!string.IsNullOrWhiteSpace(carEdit.Car_CardNo) && car.Car_CardNo != carEdit.Car_CardNo)
            {
                var isExistCar = BLL.Car.GetEntityByCardNo(carEdit.Car_CardNo);
                if (isExistCar != null) { errmsg = "卡号已存在，不能重复登记"; return false; }
            }

            card = BLL.CarCardType.GetEntity(carEdit.Car_TypeNo);
            if (card == null) { errmsg = "车牌类型错误"; return false; }
            carType = BLL.CarType.GetEntity(carEdit.Car_VehicleTypeNo);
            if (carType == null) { errmsg = "车牌颜色错误"; return false; }

            car.Car_TypeNo = carEdit.Car_TypeNo;
            car.Car_Category = card.CarCardType_Category;
            car.Car_IsMoreCar = card.CarCardType_IsMoreCar;
            car.Car_VehicleTypeNo = carEdit.Car_VehicleTypeNo;
            car.Car_License = carEdit.Car_License ?? "";
            car.Car_Model = carEdit.Car_Model ?? "";
            car.Car_Colour = carEdit.Car_Colour ?? "";
            car.Car_Remark = carEdit.Car_Remark ?? "";
            car.Car_EditID = admin?.Admins_ID;
            car.Car_EditTime = DateTimeHelper.GetNowTime();
            car.Car_EnableOffline = carEdit.Car_EnableOffline ?? 1;
            car.Car_CardNo = carEdit.Car_CardNo ?? "";

            if (!Model.CarCardTypeEnumList.carPrepaid.Contains(Utils.StrToInt(card.CarCardType_Category, 0)))
                car.Car_Balance = 0; //不是储值车,更新余额为0
            else
            {
                car.Car_Balance = car.Car_Balance ?? 0;
                carEdit.Car_Balance = carEdit.Car_Balance ?? 0;
                if (car.Car_Balance != carEdit.Car_Balance)
                {
                    BLL.UserLogs.AddLog(admin, "修改账户余额", $"{car.Car_CarNo}账户余额由{car.Car_Balance}修改为{carEdit.Car_Balance}", SecondIndex.Owner);
                    LogManagementMap.WriteToFile(LoggerEnum.MainInfoLog, $"{car.Car_CarNo}账户余额由{car.Car_Balance}修改为{carEdit.Car_Balance}");
                }
                car.Car_Balance = carEdit.Car_Balance; //储值车,初始余额为0
            }

            owner = BLL.Owner.GetEntity(car.Car_OwnerNo);
            car.Car_OwnerSpace = owner?.Owner_Space;

            return true;
        }

        /// <summary>
        /// 缴费后生成新的停车订单
        /// </summary>
        /// <returns></returns>
        public static Model.ParkOrder NewParkOrder(ref Model.ParkOrder oldOrder, Model.Car car, Model.CarCardType card, Model.CarType carType, DateTime? dateTime)
        {
            dateTime = dateTime ?? DateTimeHelper.GetNowTime();
            var order = JObject.FromObject(oldOrder).ToObject<Model.ParkOrder>();

            oldOrder.ParkOrder_OutTime = dateTime;
            oldOrder.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
            oldOrder.ParkOrder_IsSettle = 1;
            oldOrder.ParkOrder_Remark = "车牌登记自动出场并创建新的停车订单";

            order.ParkOrder_No = $"{Utils.CreateNumberWith()}-{(car.Car_CarNo.Length > 6 ? car.Car_CarNo.Substring(car.Car_CarNo.Length - 6, 6) : "")}";
            order.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
            order.ParkOrder_IsSettle = 0;
            order.ParkOrder_EnterTime = dateTime;
            order.ParkOrder_OutTime = null;
            order.ParkOrder_CarCardType = card.CarCardType_No;
            order.ParkOrder_CarCardTypeName = card.CarCardType_Name;
            order.ParkOrder_CarType = carType.CarType_No;
            order.ParkOrder_CarTypeName = carType.CarType_Name;

            return order;
        }

        /// <summary>
        /// 创新的支付订单
        /// </summary>
        /// <param name="park"></param>
        /// <param name="order">停车订单</param>
        /// <param name="card">车牌类型</param>
        /// <param name="car">车辆信息</param>
        /// <param name="CouponRecordNoStr">优惠券编号组</param>
        /// <param name="OrderMoney">订单金额</param>
        /// <param name="DiscountMoney">优惠金额</param>
        /// <param name="StoreMoney">储值车扣费金额</param>
        /// <param name="PayedMoney">支付金额</param>
        /// <param name="parktimemin">停车时长</param>
        /// <param name="payOrderList">返回支付订单列表(当储值车余额不足时会生成多条支付记录)</param>
        /// <param name="admin"></param>
        /// <returns></returns>
        public static List<Model.PayOrder> NewPayOrder(
            Model.Parking park
            , Model.ParkOrder order
            , Model.CarCardType card
            , Model.Car car
            , string CouponRecordNoStr
            , decimal? OrderMoney
            , decimal? DiscountMoney
            , decimal? StoreMoney
            , decimal? PayedMoney
            , int parktimemin
            , Model.AdminSession admin = null)
        {
            List<string> noList = Utils.GetRandomLst(2);//生成
            Model.PayOrder payModel = new Model.PayOrder();
            payModel.PayOrder_ParkOrderNo = order.ParkOrder_No;
            payModel.PayOrder_Time = DateTimeHelper.GetNowTime();
            payModel.PayOrder_CarNo = order.ParkOrder_CarNo;
            payModel.PayOrder_Status = 0;
            payModel.PayOrder_PayedTime = null;
            payModel.PayOrder_PayTypeCode = Model.EnumPayType.OffLineCash.ToString();
            payModel.PayOrder_PayType = 0;
            payModel.PayOrder_CouponRecordNo = CouponRecordNoStr;
            payModel.PayOrder_AdminID = admin?.Admins_ID;
            payModel.PayOrder_OperatorName = admin?.Admins_Name;
            payModel.PayOrder_UserNo = "";
            payModel.PayOrder_Desc = "平台支付";
            payModel.PayOrder_ParkKey = park.Parking_Key;
            payModel.PayOrder_ParkNo = park.Parking_No;
            payModel.PayOrder_OrderTypeNo = card == null ? Convert.ToString((int)Common.EnumOrderType.Temp) : CarTypeHelper.GetOrderType(card.CarCardType_Category, true);
            payModel.PayOrder_EnterTime = order.ParkOrder_EnterTime;
            payModel.PayOrder_TempTimeCount = Utils.ObjectToInt(parktimemin, 0);
            payModel.PayOrder_TimeCountDesc = Utils.DateDiffStr(payModel.PayOrder_TempTimeCount.Value * 60);
            payModel.PayOrder_CarCardTypeNo = order.ParkOrder_CarCardType;
            payModel.PayOrder_CarTypeNo = order.ParkOrder_CarType;
            payModel.PayOrder_DiscountMoney = DiscountMoney;
            payModel.PayOrder_ParkAreaNo = order.ParkOrder_ParkAreaNo;
            payModel.PayOrder_PayedTime = DateTimeHelper.GetNowTime();
            payModel.PayOrder_Status = 1;//已支付
            payModel.PayOrder_Category = card != null ? card.CarCardType_Category : "";

            var payOrderList = new List<Model.PayOrder>();
            if (StoreMoney > 0)
            {
                payModel.PayOrder_No = "PO" + noList[0] + park.Parking_Key;
                payModel.PayOrder_Money = StoreMoney;
                payModel.PayOrder_PayedMoney = StoreMoney;
                payModel.PayOrder_DiscountMoney = null;
                payModel.PayOrder_Desc = "储值车充值";
                payModel = BLL.PayOrder.CreatePayOrder(true, payModel, park.Parking_Key, null, admin);
                payOrderList.Add(payModel);

                if (car.Car_Balance > 0)
                {
                    car.Car_Balance = car.Car_Balance - StoreMoney;
                    if (car.Car_Balance < 0) car.Car_Balance = 0;
                }
            }

            if (PayedMoney > 0)
            {
                payModel.PayOrder_No = "PO" + noList[1] + park.Parking_Key;
                payModel.PayOrder_Money = OrderMoney - StoreMoney;
                payModel.PayOrder_PayedMoney = PayedMoney;
                payModel.PayOrder_Desc = "车辆登记清缴费用";
                payModel = BLL.PayOrder.CreatePayOrder(true, payModel, park.Parking_Key, null, admin);
                payOrderList.Add(payModel);
            }

            return payOrderList;
        }

        /// <summary>
        /// 新增车辆，关闭场内订单并创建新的入场订单
        /// </summary>
        /// <returns></returns>
        public static bool NewParkOrderAndCloseOldOrder(string ParkOrder_No, Model.Car car, Model.CarCardType card, Model.CarType carType, List<Model.OrderDetail> currDetails, out List<Model.ParkOrder> orders, out List<Model.OrderDetail> details, out string errmsg, Model.AdminSession lgAdmins = null, bool isCharge = true, bool isDelay = false)
        {
            errmsg = string.Empty;
            orders = new List<Model.ParkOrder>();
            details = new List<Model.OrderDetail>();
            var dateTime = isDelay ? (car.Car_BeginTime ?? DateTimeHelper.GetNowTime()) : DateTimeHelper.GetNowTime();
            var oItem = BLL.ParkOrder.GetEntity(ParkOrder_No);
            if (oItem == null) { errmsg = "停车订单不存在"; return false; }

            var oDetails = currDetails ?? BLL.OrderDetail.GetAllEntity(ParkOrder_No);
            if (oItem.ParkOrder_IsNoInRecord != 1 && (oDetails == null || oDetails.Count == 0)) { errmsg = "停车订单明细不存在"; return false; }
            var nItem = TyziTools.Json.ToObject<Model.ParkOrder>(TyziTools.Json.ToString(oItem));
            var curIn = new Model.OrderDetail();
            var curNoList = BLL.OrderDetail.NewOrderNo(car.Car_CarNo, oDetails.Count + 2);
            var oRemark = "固定车登记,自动离场";
            var nRemark = "固定车登记,重新入场";

            oItem.ParkOrder_StatusNo = Model.EnumParkOrderStatus.Out;
            oItem.ParkOrder_OutTime = dateTime;
            oItem.ParkOrder_Remark = oRemark;
            oItem.ParkOrder_IsSettle = 1;
            oItem.ParkOrder_OutAdminAccount = lgAdmins?.Admins_Account;
            oItem.ParkOrder_OutAdminName = lgAdmins?.Admins_Name;
            oItem.ParkOrder_TotalAmount = 0;
            oItem.ParkOrder_TotalPayed = 0;

            //新停车订单
            nItem.ParkOrder_No = curNoList[0];
            curNoList.RemoveAt(0);
            nItem.ParkOrder_EnterTime = dateTime;
            nItem.ParkOrder_OutTime = DateTime.Parse("1900-01-01 00:00:00");
            nItem.ParkOrder_EnterAdminAccount = lgAdmins?.Admins_Account;
            nItem.ParkOrder_EnterAdminName = lgAdmins?.Admins_Name;
            nItem.ParkOrder_OutPasswayNo = "";
            nItem.ParkOrder_OutPasswayName = "";
            nItem.ParkOrder_OutType = oItem.ParkOrder_OutType;
            nItem.ParkOrder_CarCardType = card.CarCardType_No;
            nItem.ParkOrder_CarCardTypeName = card.CarCardType_Name;
            nItem.ParkOrder_CarType = carType.CarType_No;
            nItem.ParkOrder_CarTypeName = carType.CarType_Name;
            nItem.ParkOrder_Remark = nRemark;
            nItem.ParkOrder_TotalAmount = 0;
            nItem.ParkOrder_TotalPayed = 0;
            nItem.ParkOrder_CouponNum = 0;
            nItem.ParkOrder_IsLift = isCharge ? 1 : 0;

            oItem.ParkOrder_OutType = 2;


            if (oItem.ParkOrder_IsNoInRecord != 1)
            {
                var oldIn = oDetails.Find(x => x.OrderDetail_StatusNo == Model.EnumParkOrderStatus.In);
                if (oldIn == null) { oldIn = oDetails.LastOrDefault(); }

                curIn = TyziTools.Json.ToObject<Model.OrderDetail>(TyziTools.Json.ToString(oldIn));
                oldIn.OrderDetail_StatusNo = Model.EnumParkOrderStatus.Out;
                oldIn.OrderDetail_OutTime = dateTime;
                oldIn.OrderDetail_OutType = 2;
                oldIn.OrderDetail_IsSettle = 1;
                oldIn.orderdetail_IsCharge = 1;
                oldIn.OrderDetail_Remark = oRemark;
                oldIn.OrderDetail_OutPasswayName = "固定车登记离场";
                oldIn.OrderDetail_OutAdminAccount = lgAdmins?.Admins_Account;
                oldIn.OrderDetail_OutAdminName = lgAdmins?.Admins_Name;

                //新订单明细
                curIn.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                curIn.OrderDetail_EnterTime = dateTime;
                curIn.OrderDetail_OutTime = DateTime.Parse("1900-01-01 00:00:00");

                curIn.OrderDetail_OutType = 0;
                curIn.OrderDetail_IsSettle = 0;
                curIn.orderdetail_IsCharge = isCharge ? 1 : 0;
                curIn.OrderDetail_Remark = nRemark;
                curIn.OrderDetail_EnterPasswayName = "固定车登记入场";
                curIn.OrderDetail_EnterAdminAccount = lgAdmins?.Admins_Account;
                curIn.OrderDetail_EnterAdminName = lgAdmins?.Admins_Name;


                var nDetails = TyziTools.Json.ToObject<List<Model.OrderDetail>>(TyziTools.Json.ToString(oDetails));
                nDetails.Add(curIn);
                nDetails.ForEach(x =>
                {
                    x.OrderDetail_No = curNoList[0];
                    x.OrderDetail_ParkOrderNo = nItem.ParkOrder_No;
                    x.OrderDetail_CarCardType = card.CarCardType_No;
                    x.OrderDetail_CarCardTypeName = card.CarCardType_Name;
                    x.OrderDetail_CarType = carType.CarType_No;
                    x.OrderDetail_CarTypeName = carType.CarType_Name;

                    curNoList.RemoveAt(0);
                });
            }

            orders.Add(oItem);
            orders.Add(nItem);

            details.AddRange(oDetails);
            //details.AddRange(nDetails);
            details.Add(curIn);

            return true;
        }

        public static bool IsTempVisitorCar(string Car_Category)
        {
            if (Model.CarCardTypeEnumList.carTemp.Contains(Utils.StrToInt(Car_Category)) ||
                Model.CarCardTypeEnumList.carVisitor.Contains(Utils.StrToInt(Car_Category)) ||
                Model.CarCardTypeEnumList.carFree.Contains(Utils.StrToInt(Car_Category)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        #endregion
    }
}
