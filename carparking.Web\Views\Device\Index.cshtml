﻿<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>设备管理</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px; float: left; font-size: 16px; }

        .fa1 { margin: 0; float: none; cursor: pointer; }

        .fa2 { margin: 0; float: none; cursor: pointer; margin-top: 1px; }

        .vedio { background-color: #5036c5 !important; }

        .vediobg { background-color: blue !important; }

        html,
        body { width: 100%; height: 100%; overflow-x: hidden; }

        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }

        .layui-form-select .layui-input { width: 182px; }

        .layui-table-tool-temp { padding-right: 30px; }

        .leftmenu { user-select: none; padding: 1rem; }

        .leftmenu,
        .rightbody { height: 100%; overflow: auto; }

        @@media screen and (max-width: 400px) {

            .leftmenu,
            .rightbody { height: auto !important; }
        }

        .layui-card { margin: 15px; }

        .padding-15 { padding: 1rem; }

        .pan-title { font-size: 1.5rem; }

        .content-panel { height: 100%; overflow: auto; }

        .min100 { min-height: 100%; }

        .menu-list { width: 100%; overflow: auto; position: relative; }

        .menu-title { line-height: 1.5rem; padding: 10px 0; border-bottom: 1px dashed #ddd; position: relative; cursor: pointer; }

        .menu-items { }

        .menu-item { padding-left: 0rem; }

        .menu-text { padding: .5rem 4rem .5rem 1.1rem; line-height: 1.5rem; position: relative; }

        .menu-text:hover { background-color: #ecf0f5; cursor: pointer; text-decoration: underline; }

        .menu-text::before { content: ""; padding: .5rem; background-size: 100% 100%; position: absolute; top: .75rem; }

        .menu-title::before { content: ""; padding: .5rem; background-size: 100% 100%; position: absolute; top: .85rem; }

        .menu-title.type1::before { background-image: url('../../Static/img/icon/icon_passway.svg'); left: 0rem; }

        .menu-text.type2::before { background-image: url('../../Static/img/icon/icon_camera.svg'); left: 1rem; }

        .menu-text.type3::before { background-image: url('../../Static/img/icon/icon_screen.svg'); left: 2rem; }

        .menu-text.level2.type3::before { left: 1rem; }

        .menu-title.level1 { padding-left: 1rem; }

        .menu-text.level2 { padding-left: 2rem; }

        .menu-text.level3 { padding-left: 3rem; }

        .menu-list div.m-active { font-weight: bold; }

        ::-webkit-scrollbar { width: 5px; background-color: rgba(0, 0, 0, .1); }

        ::-webkit-scrollbar-thumb { background-color: #bbb; border-radius: 5px; }

        .m-active-1 { color: #f6800f; font-weight: bold; }

        #framePanl { padding: 1rem; }

        #framePanl iframe { border: 0; width: 100%; height: calc(100% - 4px); }


        .layui-card-header { height: auto !important; }

        .a-title { padding: 15px 0; font-size: 1.5rem; }

        a.a-name { color: #0094ff; }

        a.a-name:hover { color: red; text-decoration: underline; }

        .layui-form-select .layui-input { width: 165px; }

        #View { float: right; margin-top: 6px; }

        .layui-badge { padding: 0 4px; }

        .framList,
        .framDetail { height: 100%; overflow: auto; }

        .layui-bg-set { background-color: #fff !important; color: #337ab7 !important; cursor: pointer; }

        .layui-bg-set:hover { background-color: #0094ff !important; color: #fff !important; font-weight: bold; padding-left: 10px; padding-right: 10px; }

        .sorthover { border: 1px dashed #0094ff; padding-left: 10px; padding-right: 10px; }
    </style>
</head>

<body class="animated fadeInRight">
    <div class="framList">
        <div class="layui-card">
            <div class="layui-card-header">
                <div class="layui-breadcrumb" lay-filter="breadcrumb">
                    <a><cite>车场配置</cite></a>
                    <a><cite>设备管理</cite></a>
                </div>
            </div>
            <div class="layui-card-body">
                <div class="test-table-reload-btn layui-form form-group" id="searchForm">
                    <div class="layui-inline">
                        <select data-placeholder="岗亭名称" class="layui-input" id="Device_SentryHostNo"
                                name="Device_SentryHostNo" lay-search>
                            <option value="">岗亭名称</option>
                        </select>
                    </div>
                    @* raymond edit 20220905 设备类型对不上*@
                    <div class="layui-inline">
                        <select class="layui-input" id="Device_Category" name="Device_Category" lay-search>
                            <option value="">设备类型</option>
                            <option value="1">车牌识别相机</option>
                            <option value="4">自助停车设备</option>
                            <option value="6">智慧道闸</option>
                            <option value="7">场景相机</option>
                            <option value="3">显示屏</option>
                            <option value="9">非机动车控制器</option>
                            <option value="10">二维码设备</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <select data-placeholder="设备型号" class="layui-input" id="Device_DriveNo" name="Device_DriveNo"
                                lay-search>
                            <option value="">设备型号</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <select data-placeholder="所属车道" class="layui-input" id="Device_PasswayNo"
                                name="Device_PasswayNo" lay-search>
                            <option value="">所属车道</option>
                        </select>
                    </div>
                    <div class="layui-inline">
                        <input class="layui-input " name="Device_Name" id="Device_Name" autocomplete="off"
                               placeholder="设备名称" />
                    </div>
                    <div class="layui-inline">
                        <input class="layui-input " name="Device_IP" id="Device_IP" autocomplete="off"
                               placeholder="设备IP" />
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn" id="Search">
                            <i class="layui-icon layui-icon-search inbtn"></i>
                            <t>搜索</t>
                        </button>
                    </div>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                <script type="text/html" id="toolbar_btns">
                    <div class="layui-btn-container">
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Add" id="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Update" id="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Delete" id="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>

                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Up" id="Up"><i class="fa fa-sort-amount-asc"></i><t>上移</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Down" id="Down"><i class="fa fa-sort-amount-desc"></i><t>下移</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Reset" id="Reset"><i class="fa fa-retweet"></i><t>重置</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Pulldown" id="Pulldown"><i class="fa fa-cc-amex"></i><t>显示屏配置</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Send" id="Send"><i class="fa fa-stop"></i><t>余位屏配置</t></button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="VoipSet" id="VoipSet"><i class="fa fa-pause"></i><t>智慧道闸配置</t></button>
                    </div>
                </script>
            </div>
        </div>
    </div>
    <script type="text/html" id="Online">
        <span class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></span>
    </script>
    <script type="text/html" id="GateStatus">
        <span class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></span>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}">${Drive_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplhost">
        <option value="${SentryHost_No}">${SentryHost_Name}</option>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js?1" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true" asp-append-version="true"></script>
    <script type="text/javascript">

        myVerify.init();
        var currentSortNo = null;
        var currentSortMode = null;
        var comtable = null;
        var layuiForm = null;
        layui.use(['table', 'form'], function () {

            var admin = layui.admin;
            var table = layui.table;
            layuiForm = layui.form;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            //raymond edit 20220905 设备类型对不上
            var cols = [[
                { type: 'checkbox' }
                , { field: 'Device_No', title: '设备编码', hide: true }
                , { field: 'Device_Name', title: '设备名称' }
                , {
                    field: 'Device_Category', title: '设备类型', templet: function (d) {
                        if (d.Device_Category == 1) {
                            var ioSpan = d.Device_IO == 1 ? '<span class="layui-badge layui-bg-blue">主</span>' : '<span class="layui-badge layui-bg-orange">辅</span>';
                            return ioSpan + ' <span class="layui-badge layui-bg-blue ">车牌识别相机</span>';
                        }
                        else if (d.Device_Category == 4)
                            return tempBar1(1, "自助停车设备");
                        else if (d.Device_Category == 6)
                            return tempBar1(10, "智慧道闸");
                        else if (d.Device_Category == 7) {
                            if (d.Drive_Code == "10103") {
                                return ' <span class="layui-badge layui-bg-green ">萤石相机</span>&nbsp;<span class="layui-badge layui-bg-blue vedio" data-key="' + d.Device_No + '"><i class="fa fa-youtube-play fa1" title="点击打开视频"></i></span>';
                            }
                            else if (d.Drive_Code == "10123") {
                                return ' <span class="layui-badge layui-bg-purple ">YM01</span>&nbsp;<span class="layui-badge layui-bg-blue vedio-vizcloud" data-key="' + d.Device_No + '"><i class="fa fa-youtube-play fa1" title="点击打开视频"></i></span>';
                            }
                            else
                                return tempBar1(3, "场景相机");
                        } else if (d.Device_Category == 3)
                            return tempBar1(4, "显示屏");
                        else if (d.Device_Category == 9)
                            return tempBar1(5, "非机动车控制器");
                        else if (d.Device_Category == 10)
                            return tempBar1(6, "二维码设备");
                        else
                            return '';
                    }
                }
                , { field: 'Device_IP', title: '设备IP' }
                , { field: 'Device_DriveNo', title: '型号编码', hide: true }
                , { field: 'Drive_Name', title: '设备型号' }
                , {
                    field: 'Device_Online', title: '岗亭设备状态', width: 120, templet: function (d) {
                        if ((d.Device_Category != 3 && d.Drive_Code != "10103" && d.Drive_Code != "10123") || (d.Device_Category == 3 && d.Drive_Code == "10124") || (d.Device_Category == 3 && d.Device_CommunicationMode == 2)) {
                            return '<span class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></span>';
                        }
                        else {
                            return '';
                        }
                    }
                }
                , {
                    field: 'Device_PasswayNo', title: '车道编码', hide: true, templet: function (d) {
                        if (d.Device_Category == 3 && d.Drive_Code == "10124") {
                            return '';
                        } else {
                            return d.Device_PasswayNo;
                        }
                    }
                }
                , {
                    field: 'Passway_Name', title: '所属车道', templet: function (d) {
                        if (d.Device_Category == 3 && d.Drive_Code == "10124") {
                            return '';
                        } else {
                            return d.Passway_Name;
                        }
                    }
                }
                , {
                    field: 'Device_ScreenNum', title: '视频排序', width: 90, templet: function (d) {
                        if (d.Device_Category == 1 && d.Device_ScreenNum != null)
                            return d.Device_ScreenNum;
                        else
                            return "";
                    }
                }
                , {
                    field: 'Device_ScreenNum', title: '操作', width: 180, templet: function (d) {

                        if (d.Device_Category == 1) {
                            var actionHtml = "";
                            if (d.Device_ScreenNum != null && @carparking.Config.AppSettingConfig.SentryMode!= "2") {
                                actionHtml = '<span class="layui-badge layui-bg-set vediosort up" data-key="' + d.Device_No + '"><i class="fa fa2" title="点击上移"></i>上移</span><span class="layui-badge layui-bg-set vediosort down" data-key="' + d.Device_No + '"><i class="fa fa2" title="点击下移"></i>下移</span>'
                            }

                            if (d.Drive_Name) {
                                if (DeviceTypeName.device15List.find((m, i) => { return m == d.Drive_Name; }) != null) {
                                    return actionHtml + '<a href="http://' + d.Device_IP + ':80" target="_blank"><span class="layui-badge layui-bg-set"><i class="fa fa2" title="点击打开配置界面"></i>设备配置</span></a>';
                                } else if (DeviceTypeName.device06List.find((m, i) => { return m == d.Drive_Name; }) != null) {
                                    return actionHtml + '<a href="http://' + d.Device_IP + ':80" target="_blank"><span class="layui-badge layui-bg-set"><i class="fa fa2" title="点击打开配置界面"></i>设备配置</span></a>';
                                } else {
                                    return actionHtml;
                                }
                            }
                        }
                        else if (d.Device_Category == 4) {
                            return '<a href="http://' + d.Device_IP + ':8099" target="_blank"><span class="layui-badge layui-bg-set"><i class="fa fa2" title="点击打开配置界面"></i>设备配置</span></a>';
                        }
                        return "";
                    }
                }
            ]];

            cols = tb_page_cols(cols);

            pager.init();

            console.log("GetDeviceList");
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Device/GetDeviceList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data);
                    $(".vedio").hover(function () {
                        $(this).addClass("vediobg");
                    }, function () {
                        $(this).removeClass("vediobg");
                    });

                    $(".vedio").click(function () {
                        var no = $(this).attr("data-key");
                        layer.open({
                            type: 2,
                            title: false,
                            content: 'Monitor?no=' + no,
                            area: getIframeArea(['778px', '443px']),
                            maxmin: false
                        });
                    });

                    $(".vedio-vizcloud").hover(function () {
                        $(this).addClass("vediobg");
                    }, function () {
                        $(this).removeClass("vediobg");
                    });
                    // YM01视频监控
                    $(".vedio-vizcloud").click(function () {
                        var no = $(this).attr("data-key");
                        layer.open({
                            type: 2,
                            title: false,
                            content: 'MonitorVizCloud?no=' + no,
                            area: getIframeArea(['778px', '443px']),
                            maxmin: false
                        });
                    });

                    $(".vediosort").click(function () {
                        var no = $(this).attr("data-key");
                        LAYER_LOADING("处理中...");
                        var sort = $(this).hasClass("up");
                        currentSortNo = no;
                        currentSortMode = sort;
                        var action = sort ? "UpDevice" : "DownDevice";
                        $.getJSON(action, { Device_No: no }, function (json) {
                            if (json.success) {
                                window.pager.bindData();
                                layer.msg(json.msg, { icon: 1, time: 1500 });
                            }
                            else {
                                layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                            }
                        })
                        parent.top.setScrollTop(document.body, 0);
                    });

                    if (currentSortNo && currentSortNo != null) {
                        var btn;
                        if (currentSortMode) {
                            btn = $("span.up[data-key=" + currentSortNo + "]");
                        } else {
                            btn = $("span.down[data-key=" + currentSortNo + "]");
                        }
                        $(btn).addClass("sorthover");
                        setTimeout(() => { $(btn).removeClass("sorthover"); }, 1000);
                    }

                    setTimeout(() => { onGetOnlineStatus(data); }, 200);

                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增设备",
                            content: "Edit?Act=Add",
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        var paramNo = data[0].Device_No;
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑设备",
                            content: "Edit?Act=Update&Device_No=" + paramNo,
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        var paramNo = data[0].Device_No;
                        LAYER_OPEN_TYPE_0("若设备正在识别车辆通行，删除后会导致<br />识别失败、缴费后不开闸等等异常错误！<br />确定删除设备?", res => {
                            LAYER_LOADING("处理中...");
                            $.getJSON("DelDevice", { Device_No: paramNo }, function (json) {
                                if (json.success) {
                                    layer.msg("删除成功", { icon: 1, time: 1500 }, function () {
                                        window.pager.bindData();
                                    });
                                }
                                else {
                                    layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                                }
                            });
                        }, res => {
                        })
                        parent.top.setScrollTop(document.body, 0);
                        break;

                    case 'Up':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        var paramNo = data[0].Device_No;
                        LAYER_LOADING("处理中...");
                        $.getJSON("UpDevice", { Device_No: paramNo }, function (json) {
                            if (json.success) {
                                layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                    window.pager.bindData();
                                });
                            }
                            else {
                                layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                            }
                        })
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Down':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        var paramNo = data[0].Device_No;
                        LAYER_LOADING("处理中...");
                        $.getJSON("DownDevice", { Device_No: paramNo }, function (json) {
                            if (json.success) {
                                layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                    window.pager.bindData();
                                });
                            }
                            else {
                                layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                            }
                        })
                        parent.top.setScrollTop(document.body, 0);
                        break;

                    case 'Reset':
                        LAYER_OPEN_TYPE_0("将视频排序重置到原始状态（设备添加顺序）！<br />确定重置排序吗?", res => {
                            LAYER_LOADING("处理中...");
                            $.getJSON("ResetDevice", {}, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                        window.pager.bindData();
                                    });
                                }
                                else {
                                    layer.open({ type: 0, title: "提示", content: json.msg, area: ["320px"], icon: 0, time: 0, btn: ["我知道了"] });
                                }
                            });
                        }, res => {
                        })
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Send':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        for (const currentValue of data) {
                            if (currentValue.Device_Category != 1) {
                                layer.msg("[" + currentValue.Device_Name + "]非车牌识别相机"); return;
                            }
                        }
                        var noArray = []; var nameArray = [];
                        for (var i = 0; i < data.length; i++) { noArray.push(data[i].Device_No); nameArray.push(data[i].Device_Name) }

                        pager.SelDeviceNoes = noArray.join(",");
                        pager.SelDeviceNames = nameArray.join(",");

                        layer.open({
                            type: 2, id: 1,
                            title: "余位屏配置",
                            content: "ResidualEdit?Act=Add&t=" + Math.random(),
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Pulldown':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        for (const currentValue of data) {
                            if (currentValue.Device_Category != 1) {
                                layer.msg("[" + currentValue.Device_Name + "]非车牌识别相机"); return;
                            }
                        }
                        var noArray = []; var nameArray = [];
                        for (var i = 0; i < data.length; i++) { noArray.push(data[i].Device_No); nameArray.push(data[i].Device_Name) }

                        pager.SelDeviceNoes = noArray.join(",");
                        pager.SelDeviceNames = nameArray.join(",");

                        layer.open({
                            type: 2, id: 1,
                            title: "显示屏配置",
                            content: "ScreenEdit?Act=Add&t=" + Math.random(),
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'VoipSet':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        // 只能单选
                        if (data.length > 1) { layer.msg("请单选"); return; }

                        // 只允许选择智慧道闸设备
                        if (data[0].Device_Category != 6) {
                            layer.msg("[" + data[0].Device_Name + "]非智慧道闸"); return;
                        }

                        // 判断设备是否已连接
                        if (data[0].Device_Online != 1) {
                            layer.alert(`
                                    <div>
                                        <div style="margin-bottom: 20px;">
                                            <h2 style="margin: 15px 0 0; font-size: 18px; color: #333;">
                                                <span style="color: #FF5722">[${data[0].Device_Name}]</span> 设备未连接
                                            </h2>
                                        </div>
                                        <div style="background: #f8f8f8; border-radius: 4px; padding: 15px;">
                                            <div style="color: #666; margin-bottom: 12px; font-size: 14px;">如果设备一直未连接，请检查：</div>
                                            <ol style="padding-left: 20px; margin: 0; color: #666;">
                                                <li style="line-height: 2;">1、设备关联车道是否启用控制板功能</li>
                                                <li style="line-height: 2;">2、设备网络状态是否正常</li>
                                                <li style="line-height: 2;">3、设备IP地址是否配置正确</li>
                                                <li style="line-height: 2;">4、设备是否正常上电</li>
                                                <li style="line-height: 2;">5、网络线缆是否正常连接</li>
                                            </ol>
                                        </div>
                                    </div>`, {
                                title: false,
                                area: ['450px', 'auto'],
                                closeBtn: 0,
                                btnAlign: 'c',
                                btn: ['<span style="margin-bottom: 8px;">我知道了</span>'],
                                skin: 'layui-layer-custom',
                                success: function (layero, index) {
                                    $(layero).find('.layui-layer-btn').css('margin-bottom', '20px');
                                }
                            });
                            return;
                        }

                        var noArray = [data[0].Device_No];
                        var nameArray = [data[0].Device_Name];

                        pager.SelDeviceNoes = noArray.join(",");
                        pager.SelDeviceNames = nameArray.join(",");

                        layer.open({
                            type: 2, id: 1,
                            title: "智慧道闸配置",
                            content: "LanEdit?Act=Add&t=" + Math.random(),
                            area: getIframeArea(["900px", "95%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                };
            });

            tb_row_checkbox(table);


        });

        var onGetOnlineStatus = function (data) {
            var DeviceNoList = [];
            data.data.forEach(function (item, index) { DeviceNoList[DeviceNoList.length] = item.Device_No });
            $.post("GetDeviceOnlineStatus", { DeviceNoList: JSON.stringify(DeviceNoList) }, function (json) {
                if (json.success && json.data != null) {
                    try {
                        var $table = $(".layui-table-body table");
                        var $trs = $table.find("tr");
                        var onlines = json.data;
                        for (var i = 0; i < $trs.length; i++) {

                            var isExist = $($trs[i]).find('[data-field="Device_Online"] span').length > 0;
                            var deviceNo = $($trs[i]).find('[data-field="Device_No"] div').text();
                            var divOnline = $($trs[i]).find('[data-field="Device_Online"] div');
                            var online = 0;
                            onlines.forEach(function (d, e) { if (d.deviceno == deviceNo) { online = d.online; } });

                            if (online == 1) $(divOnline).html('<span class="layui-badge layui-bg-green ">已连接</span>');
                            else if (isExist) $(divOnline).html('<span class="layui-badge layui-bg-red ">未连接</span>');

                            //设置表格$table中对应的数据值
                            data.data[i].Device_Online = online;

                            //var divGate = $($trs[i]).find('[data-field="Passway_Gate"] div');
                            //var gateStatus = -1;
                            //onlines.forEach(function (d, e) { if (d.deviceno == deviceNo) { gateStatus = d.gatestatus; } });
                            //if (gateStatus == 1) $(divGate).html('<span class="layui-badge layui-bg-red ">开闸</span>');
                            //else if (gateStatus == 0) $(divGate).html('<span class="layui-badge layui-bg-orange ">关闸</span>');
                            //else $(divGate).html('<span class="layui-badge layui-bg-gray ">未知</span>');
                        }
                    } catch (e) {
                        console.log(e)
                    }
                }
            }, "json");
        }

        var pager = {
            SelDeviceNoes: null,
            SelDeviceNames: null,
            init: function () {
                $.ajaxSettings.async = false;
                //this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                console.log("GetAllSentryHost");
                $.post("GetAllSentryHost", {}, function (json) {
                    if (json.success) {
                        var data = json.data.sentryHosts;
                        $("#Device_SentryHostNo").append($("#tmplhost").tmpl(data));
                        layui.form.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");
                console.log("GetAllPasswayList");
                $.post("GetAllPasswayList", {}, function (json) {
                    if (json.success) {
                        $("#Device_PasswayNo").append($("#tmplpassway").tmpl(json.data))
                        layuiForm.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");
                console.log("GetAllDriveList");
                $.post("GetAllDriveList", {}, function (json) {
                    if (json.success) {
                        var sdata = [];
                        json.data.forEach(function (d, i) { if ("1,3,4,6,7,9".indexOf(d.Drive_Category) >= 0) sdata[sdata.length] = d; });
                        $("#Device_DriveNo").append($("#tmpldrive").tmpl(sdata))
                        layuiForm.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");
            },
            bindData: function (index, noclose) {
                if (!noclose) layer.closeAll();
                console.log("GetDeviceList");
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Device/GetDeviceList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    // console.log(pagePower)
                });
            },
            bindEvent: function () {
                $("#Search").unbind("click").click(function () {
                    pager.bindData(pager.pageIndex);
                });
            }
        }

    </script>
</body>

</html>
