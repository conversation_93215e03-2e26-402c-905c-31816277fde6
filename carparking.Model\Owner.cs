﻿//------------------------------------------------------------------------------
// <autogenerated>
//     Implementation of the Class Admin
//     Creater: runrui.wang
//     Date:    2016-08-05 19:11
//     Version: 2.0.0.0
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </autogenerated>
//------------------------------------------------------------------------------
using System;
using Newtonsoft.Json;

namespace carparking.Model
{
    /// <summary>
    ///Owner(车主管理）数据实体
    /// </summary>
    [Serializable]
    public class Owner
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int? Owner_ID { get; set; }

        /// <summary>
        /// 车主编码
        /// </summary>
        public string Owner_No { get; set; }

        public string Owner_ParkNo { get; set; }

        /// <summary>
        /// 车主名称
        /// </summary>
        public string Owner_Name { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string Owner_IDCard { get; set; }

        /// <summary>
        /// 驾驶证号
        /// </summary>
        public string Owner_License { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Owner_Phone { get; set; }

        /// <summary>
        /// 手机号后四位
        /// </summary>
        public string Owner_PhoneLastFour { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Owner_Email { get; set; }

        /// <summary>
        /// 车主性别：0-未知，1-男，2-女
        /// </summary>
        public int? Owner_Sex { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Owner_Remark { get; set; }

        /// <summary>
        /// 添加人
        /// </summary>
        public int? Owner_AddID { get; set; }

        /// <summary>
        /// 添加时间（默认系统当前时间）
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Owner_AddTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public int? Owner_EditID { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Owner_EditTime { get; set; }

        /// <summary>
        /// 车主住址
        /// </summary>
        public string Owner_Address { get; set; }

        /// <summary>
        /// 车位数量
        /// </summary>
        public int? Owner_SpaceNum { get; set; }
        /// <summary>
        /// 车位有效期起
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Owner_StartTime { get; set; }
        /// <summary>
        /// 车位有效期止
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_DateTime))]
        public DateTime? Owner_EndTime { get; set; }

        /// <summary>
        /// 系统车位号(不允许修改的)
        /// </summary>
        public string Owner_Space { get; set; }

        /// <summary>
        /// 停车场车位号
        /// </summary>
        public string Owner_ParkSpace { get; set; }

        /// <summary>
        /// 账户余额
        /// </summary>
        public decimal? Owner_Balance { get; set; }

        /// <summary>
        /// 车牌类型编号
        /// </summary>
        public string Owner_CardTypeNo { get; set; }
        /// <summary>
        /// 车牌类别：1-临停车,2-储值车,3-月租车,4-免费车,5-访客车,6-商家车
        /// </summary>
        public int? Owner_CardType { get; set; }
        /// <summary>
        /// 相机离线自动开闸(白名单)：0-禁用，1-启用
        /// </summary>
        public int? Owner_EnableOffline { get; set; }
    }

    public class OwnerExt : Owner
    {
        /// <summary>
        /// 车牌
        /// </summary>
        public string Owner_CarCardNo { get; set; }
        /// <summary>
        /// 车牌类型名称
        /// </summary>
        public string Owner_CardName { get; set; }

        public string Owner_AddName { get; set; }
        /// <summary>
        /// 是否多车多位：0-否，1-是
        /// </summary>
        public int? Owner_IsMoreCar { get; set; }

        /// <summary>
        /// 是否保密手机号码
        /// </summary>
        public int? Owner_IsSecretPhone { get; set; }

        /// <summary>
        /// 可停区域
        /// </summary>
        public string Owner_StopAreaName { get; set; }
        /// <summary>
        /// 是否场内车：0-否，1-是
        /// </summary>
        public int? Owner_IsInPark { get; set; }
    }

    public class OwnerWhere : Owner
    {
        public DateTime? Owner_StartTime0 { get; set; }
        public DateTime? Owner_StartTime1 { get; set; }
        public DateTime? Owner_EndTime0 { get; set; }
        public DateTime? Owner_EndTime1 { get; set; }

        public DateTime? Owner_AddTime0 { get; set; }
        public DateTime? Owner_AddTime1 { get; set; }
        public string Owner_CarCardNo { get; set; }

        public string Car_CarNo { get; set; }
        public string Car_Remark { get; set; }

        public string Car_CardNo { get; set; }

        public int? Owner_IsMoreCar { get; set; }
        public int? SearchType { get; set; }

        /// <summary>
        /// 储值车余额查询条件 0 大于 1 大于等于 2 小于 3 小于等于
        /// </summary>
        public int? Owner_BalanceType { get; set; }

        /// <summary>
        /// 储值车余额查询条件值
        /// </summary>
        public decimal? Owner_BalanceValue { get; set; }

        public string code { get; set; }
    }



    public class SpaceCharge
    { /// <summary>
      /// 车位唯一编码
      /// </summary>
        public string Owner_No { get; set; }
        /// <summary>
        /// 自定义延期开始日期
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_Date))]
        public DateTime? Owner_StartTimeYQ { get; set; }
        /// <summary>
        /// 自定义延期截止日期
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_Date))]
        public DateTime? Owner_EndTimeYQ { get; set; }
        /// <summary>
        /// 1- 按规则延期/充值  2-自定义延期/充值
        /// </summary>
        public int? PayOrder_PayType { get; set; }
        /// <summary>
        /// 按规则支付金额
        /// </summary>
        public decimal? PayOrder_PayedMoney { get; set; }
        /// <summary>
        /// 自定义充值金额
        /// </summary>
        public decimal? PayOrder_MoneyYQ { get; set; }
        /// <summary>
        /// 自定义支付金额
        /// </summary>
        public decimal? PayOrder_PayedMoneyYQ { get; set; }
        /// <summary>
        /// 延期/充值规则唯一编码
        /// </summary>
        public string MonthRule_No { get; set; }

        /// <summary>
        /// 当前车位的车辆入场时间
        /// </summary>
        [JsonConverter(typeof(JsonConvertDateTimeOverride_Date))]
        public DateTime? InCar_EnterTime { get; set; }
    }

    public class OwnerConvert
    {
        public static int Sex(string sex)
        {
            if (sex == "男") return 1;
            if (sex == "女") return 2;
            return 0;
        }

        public static string Sex(int sex)
        {
            if (sex == 1) return "男";
            if (sex == 2) return "女";

            return "保密";
        }
    }
}
