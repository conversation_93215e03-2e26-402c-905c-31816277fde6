﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车辆查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }

        .help-btn { position: absolute; width: 20px; height: 20px; right: 5px; top: 10px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }

        .layui-fluid { padding: 0; }
        .layadmin-header .layui-breadcrumb { background-color: #5868e0; color: #fff; }
        .layui-breadcrumb a cite { color: #fff; min-height: 1rem; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; }
        ::-webkit-scrollbar-thumb { border-radius: 10px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }
        .layui-breadcrumb, .layui-tab-title { background-color: #5868e0 !important; }

        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-input.timerange { width: 300px; }
        .layui-layer-btn-l { margin-left: 8.33%; }
        .layui-input { border-color: #ddd !important; font-size: .9rem; }
        .layui-table-cell { font-size: .9rem; }
        .layui-tab-title li { font-size: 1rem; }
        .fastsearch ul li { font-size: 1rem; }

        .layui-table-click { background-color: #f2f3f3; color: #2F4056; font-weight: bold; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车辆查询</cite></a>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">

                            <div class="layui-card-header topbar">
                                <div class="layui-col-md6">
                                    <div class="fastsearch">
                                        <ul>
                                            <li data-key="0" class="select">全部 <t>(0)</t></li>
                                            <li data-key="1">未过期 <t>(0)</t></li>
                                            <li data-key="2">将过期 <t>(0)</t></li>
                                            <li data-key="3">已过期 <t>(0)</t></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="operabar searchForm">
                                        <div class="layui-inline"><input class="layui-input " name="Owner_CarCardNo" id="Owner_CarCardNo" autocomplete="off" placeholder="车牌号" maxlength="10" /></div>
                                        <div class="layui-inline"><div class="operabar-if">更多条件</div></div>
                                        <div class="layui-inline"><button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button></div>
                                    </div>
                                </div>
                            </div>

                            <div class="test-table-reload-btn layui-form search-more layui-hide">
                                <div class="layui-inline"><input class="layui-input " name="Owner_Space" id="Owner_Space" autocomplete="off" placeholder="系统车位号" maxlength="10" /></div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_ParkSpace" id="Owner_ParkSpace" autocomplete="off" placeholder="车场车位号" maxlength="10" />
                                </div>
                                <div class="layui-inline">
                                    <select class="layui-input" id="Owner_CardTypeNo" name="Owner_CardTypeNo" lay-search>
                                        <option value="">车牌类型</option>
                                    </select>
                                </div>

                                <div class="layui-inline"><input class="layui-input " name="Owner_Name" id="Owner_Name" autocomplete="off" placeholder="车主姓名" maxlength="20" /></div>
                                <div class="layui-inline"><input class="layui-input " name="Owner_PhoneLastFour" id="Owner_PhoneLastFour" autocomplete="off" placeholder="手机号后四位" maxlength="4" /></div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="Owner_StartTime0" id="Owner_StartTime0" autocomplete="off" placeholder="有效期起开始" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="Owner_StartTime1" id="Owner_StartTime1" autocomplete="off" placeholder="有效期起结束" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="Owner_EndTime0" id="Owner_EndTime0" autocomplete="off" placeholder="有效期止开始" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="Owner_EndTime1" id="Owner_EndTime1" autocomplete="off" placeholder="有效期止结束" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_Address"
                                           id="Owner_Address" autocomplete="off" placeholder="车主地址" maxlength="200" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Owner_Remark" id="Owner_Remark"
                                           autocomplete="off" placeholder="车主备注" maxlength="200" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_Remark" id="Car_Remark"
                                           autocomplete="off" placeholder="车辆备注" maxlength="200" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input " name="Car_CardNo" id="Car_CardNo"
                                           autocomplete="off" placeholder="车辆卡号" maxlength="200" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.WindowsStandard)
    {
        <div style="width: 100%;height: 20px;position: absolute;bottom: 10px; right:0px; opacity: .5;">
            <span style="float:left;margin-left:20px;color:black;" id="time"></span>
        </div>
    }
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?3.5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v20230620" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var code = $.getUrlParam("code");

        var versionMode = '@Html.Raw(carparking.Config.AppSettingConfig.SentryMode)';
        // 添加实时日期和时间
        function updateTime() {
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            var hours = now.getHours();
            var minutes = now.getMinutes();
            var seconds = now.getSeconds();
            var dateString = year + '-' + month + '-' + day;
            var timeString = hours + ':' + minutes + ':' + seconds;
            document.getElementById('time').innerHTML = dateString + " " + timeString;
        }
        if (versionMode != "0") {
            setInterval(updateTime, 1000); // 每秒钟更新一次时间
        }

        topBar.config.SearchType = 0;//0-全部,1-未过期,2-将过期,3-已过期
        topBar.init(function (v) {
            pager.bindData(1);
        });

        var arrtemp = ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'];//临时车类型
        var arrmonth = ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'];//月租车类型
        var arrfree = ['3656'];//免费车类型
        var arrprepaid = ['3657'];//储值车类型
        var arrvisitor = ['3658'];//免费车类型

        var comtable = null;
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var nowDate = new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"));
        s_carno_picker.init("Owner_CarCardNo", function (text, carno) {
            if (s_carno_picker.eleid == "Owner_CarCardNo") {
                $("#Owner_CarCardNo").val(carno.join(''));
            }
        }, "web").bindkeyup();

        layui.use(['table', 'form', 'element', 'laydate'], function () {
            var table = layui.table;

            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            conditionParam.SearchType = topBar.config.SearchType;
            conditionParam.code = code;

			var cols = [[
				{ type: 'checkbox' }
				, { field: 'Owner_No', title: '车主编码', hide: true, minWidth: 100 }
				, { field: 'Owner_Space', title: '系统车位号', sort: true, minWidth: 100 }
				, { field: 'Owner_ParkSpace', title: '车场车位号', sort: true, minWidth: 100 }
				, { field: 'Owner_CarCardNo', title: '车牌号', minWidth: 120 }
				, {
					field: 'Owner_CardType', title: '车牌类型', minWidth: 100, templet: function (d) {
						if (d.Owner_CardType == 1) return tempBar(3, d.Owner_CardName);
						else if (d.Owner_CardType == 2) return tempBar(1, d.Owner_CardName);
						else if (d.Owner_CardType == 3) return tempBar(2, d.Owner_CardName);
						else if (d.Owner_CardType == 4) return tempBar(4, d.Owner_CardName);
						else if (d.Owner_CardType == 5) return tempBar(3, d.Owner_CardName);
						else if (d.Owner_CardType == 6) return tempBar(6, d.Owner_CardName);
					}
				}
				, { field: 'Owner_SpaceNum', title: '车位数量', minWidth: 100 }
				, {
					field: 'Owner_StartTime', title: '有效期起', minWidth: 130, templet: function (d) {
						if (d.Owner_CardType == 2)
							return '-';
						else if (d.Owner_CardType == 3)
							return new Date(d.Owner_StartTime).Format("yyyy-MM-dd 00:00:00");
						else
							return d.Owner_StartTime;
					}
				}
				, {
					field: 'Owner_EndTime', title: '有效期止', minWidth: 130, templet: function (d) {
						if (d.Owner_CardType == 2) {
							return '-';
						} else {
							var s = '';
							if (new Date(d.Owner_EndTime) < new Date()) s = 'color:red;';
							return '<span style="' + s + '">' + d.Owner_EndTime + '</span>';
						}
					}
				}
				, {
					field: 'Owner_Balance', title: '剩余天数', minWidth: 100, templet: function (d) {
						if (d.Owner_CardType == 2) {
							return '-';
						} else {
                            return _DATE.getZhDays(getMaxTime(nowDate, new Date(new Date(d.Owner_StartTime).Format("yyyy-MM-dd 00:00:00"))), new Date(new Date(d.Owner_EndTime).Format("yyyy-MM-dd 23:59:59")));
                            
						}
					}
				}
				, {
					field: 'Owner_Balance', title: '储值余额', minWidth: 100, templet: function (d) {
						if (d.Owner_CardType == 2) return d.Owner_Balance || 0;
						else return "-";
					}
				}
				, {
					field: 'Owner_EnableOffline', title: '白名单', hide: true, minWidth: 100, templet: function (d) {
						if (d.Owner_EnableOffline == 1) return tempBar(1, "启用");
						else return tempBar(3, "禁用");
					}
				}
				, { field: 'Owner_Name', title: '车主姓名', minWidth: 100 }
				, { field: 'Owner_Phone', minWidth: 120, title: '手机号码', minWidth: 100, templet: function (d) { if (d.Owner_IsSecretPhone == 1) { return dw_text_omit.bind(d.Owner_Phone); } else { return d.Owner_Phone; } } }
				, { field: 'Owner_IDCard', title: '身份证号', hide: true, minWidth: 100 }
				, { field: 'Owner_License', title: '驾驶证号', hide: true, minWidth: 100, templet: function (d) { return dw_text_omit.bind(d.Owner_License); } }
				, { field: 'Owner_Email', title: '电子邮件', hide: true, minWidth: 100, templet: function (d) { return dw_text_omit.bind(d.Owner_Email); } }
				, { field: 'Owner_Address', title: '车主地址', hide: true, minWidth: 100 }
				, { field: 'Owner_Remark', title: '车主备注', hide: true, minWidth: 100 }
				, { field: 'Owner_AddTime', title: '登记时间', hide: true, sort: true, minWidth: 130 }
				, { field: 'Owner_AddName', title: '操作员', hide: true, minWidth: 100 }
			]];
			cols = tb_page_cols(cols, "Car");

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetCarList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (data) {
                    tb_page_set(data, "Car");
                    topBar.set(data.count);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageindex = $(".layui-laypage-curr").text();
                pager.pageIndex = pageindex;
                switch (obj.event) {

                };
            });
            // 监听行点击事件
            table.on('row(com-table-base)', function (obj) {
                // 移除所有行的选中样式
                obj.tr.siblings().removeClass('layui-table-click');
                // 添加当前行的选中样式
                obj.tr.addClass('layui-table-click');
            });

            tb_row_checkbox(table);
        });

        //绑定查询事件
        $(function () {
            $("#Search").click(function () { pager.bindData(1); });
        });

        var pager = {
            carCarNoList: [],
            carNoList: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("/Monitoring/SltCarCardTypeList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            //排除商家车&访客车
                            if ("5,6".indexOf(d.CarCardType_Type) < 0) {
                                var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                                $("#Owner_CardTypeNo").append(option)
                            }
                        });
                    }
                }, "json");

                $.post("/Monitoring/SltCarTypeList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#Car_VehicleTypeNo").append(option)
                        });
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["Owner_StartTime0", "Owner_StartTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["Owner_EndTime0", "Owner_EndTime1"], { type: 'datetime', range: true });


                $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
                    var key = $(this).attr("data-key");
                    var data = getHelpContent(key);
                    if (data) {
                        layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
                    }
                }, function () {
                    layer.closeAll();
                });

                layui.form.render();
            },
            bindData: function (index) {
                layer.closeAll();
                pager.GetData(index);
            },
            GetData: function (index) {
                now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
                nowDate = new Date(new Date(now).Format("yyyy-MM-dd hh:mm:ss"));
                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                    if (data.Car_EndDay) {
                        if (isNaN(data.Car_EndDay)) {
                            data.Car_EndDay = null;
                            $("#Car_EndDay").val("");
                        }
                    }
                    return data;
                });
                conditionParam.SearchType = topBar.config.SearchType;
                conditionParam.code = code;
                comtable.reload({
                    url: '/Monitoring/GetCarList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            }
        }


        function getStatus(ChargeRules_BeginTime, ChargeRules_EndTime) {
            if (ChargeRules_BeginTime && ChargeRules_EndTime) {
                ChargeRules_BeginTime = new Date(new Date(ChargeRules_BeginTime).Format("yyyy-MM-dd hh:mm:ss"));
                ChargeRules_EndTime = new Date(new Date(ChargeRules_EndTime).Format("yyyy-MM-dd hh:mm:ss"));
                if (ChargeRules_EndTime >= nowDate) {
                    if (ChargeRules_BeginTime > nowDate) {
                        return 1;
                    } else {
                        return 2;
                    }
                } else {
                    return 3;
                }
            } else {
                return 0;
            }
        }

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return null;
            return data;
        }

        //两个时间，判断哪个时间大，则返回哪个时间
        function getMaxTime(time1, time2) {
            return time1 > time2 ? time1 : time2;
        }

        //提示信息数据
        var HelpData = [
            {
                key: "Car_EndDay",
                Description: ["剩余天数：车辆已过期，并且该车辆的结束时间过期超出*天 或者 车辆即将过期，并且该车辆的结束时间在未来*天即将过期"],
            }
        ];
    </script>




</body>
</html>
