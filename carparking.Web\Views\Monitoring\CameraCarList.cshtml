﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <style>
        label { padding: 10px 10px 0 0; float: right; }
        .layui-btn[disabled]{background-color:#808080;color:#fff;}
        .layui-tab-title { background-color: #5868e0 !important; }
    </style>
</head>
<body>
    <div class="body">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-xs4">
                        <div class="xm-span-hide">白名单：<br />已启用(<t id="whiteEnableCount">0</t>)<br /> 已注销(<t id="whiteLayoutCount">0</t>)</div>
                    </div>
                    <div class="layui-col-xs4">
                        <div class="xm-span-hide">黑名单：<br />已启用(<t id="blackEnableCount">0</t>)<br /> 已删除(<t id="blackLayoutCount">0</t>)</div>
                    </div>
                    <div class="layui-col-xs4">
                        <button class="layui-btn layui-btn-normal layui-btn-sm" id="StartSync">开始同步</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-body" id="syncResult">
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script type="text/x-jquery-tmpl" id="syncResultTempl">
        {{if Status==0}}
        <div class="layui-row" style="color:red;">${msg}</div>
        {{else}}
        <div class="layui-row">${msg}</div>
        {{/if}}
    </script>
    <script>
        layui.use(["form"], function () {
            pager.init();
        });
    </script>
    <script>
        var pager = {
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetCarWhiteList", {}, function (json) {
                    if (json.success) {
                        $("#whiteEnableCount").text(json.data.whiteEnableCount);
                        $("#whiteLayoutCount").text((json.data.whiteLayoutCount + json.data.unCarCount));
                        $("#blackEnableCount").text(json.data.blackEnableCount);
                        $("#blackLayoutCount").text(json.data.blackLayoutCount);
                    }
                }, "json");
            },
            bindData: function () {

            },
            bindEvent: function () {
                $("#StartSync").click(function () {
                    layer.msg("正在进行黑白名单同步,请耐心等待同步完成.", { icon: 16, time: 0 });
                    $("#StartSync").attr("disabled", true);
                    $.post("SyncCarWhiteList", {}, function (json) {
                        if (json.success) {
                            layer.msg(json.msg, { icon: 1 });
                            $("#syncResult").html($("#syncResultTempl").tmpl(json.data));
                        } else {
                            layer.msg(json.msg, { icon: 0 });
                        }

                        $("#StartSync").removeAttr("disabled");
                    }, "json");
                });
            }
        }
    </script>
</body>
</html>
