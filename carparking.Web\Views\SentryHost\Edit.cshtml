﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html,
        body { width: 100%; height: 100%; padding: 0; margin: 0; }

        .layui-row { margin-bottom: 10px; }

        .m-label { padding: 9px 0 0 10px; font-weight: bold; }

        .padding-15 { padding: 1rem; }

        .pan-title { font-size: 1.5rem; }

        .layui-card { box-shadow: none; }

        .layui-form .layui-badge { padding: 3px 5px; margin-top: 9px; }

        .layui-collapse .layui-colla-title { background-color: #f2f2f2; color: #333; font-weight: bold; margin: 10px 55px 10px 55px; }
    </style>
</head>

<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-card">
        <div class="layui-card-body layui-form" id="verifyCheck">
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">岗亭名称</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-null" id="SentryHost_Name" name="SentryHost_Name"
                           maxlength="32" autocomplete="off" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row layui-hide">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">部署类型</div>
                <div class="layui-col-xs7">
                    <div class="btnCombox falsemodify" id="SentryHost_Type">
                        <ul>
                            <li data-value="1" class="layui-hide select">岗亭客户端</li>
                            <li data-value="2" class="layui-hide">岗亭服务器</li>
                            <li data-value="3" class="layui-hide">管理系统服务器</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="layui-row SentryHost_Net">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">岗亭IP</div>
                <div class="layui-col-xs7">
                    <input type="text" class="layui-input v-ip v-null" id="SentryHost_IP" name="SentryHost_IP"
                           value="127.0.0.1" disabled />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <!-- 收缩更多 -->
            <div class="layui-collapse">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title layui-col-xs-offset2 layui-col-xs10">更多</h2>
                    <div class="layui-colla-content">
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">岗亭编号</div>
                            <div class="layui-col-xs7">
                                <input type="text" class="layui-input v-null v-numen" id="SentryHost_No"
                                       name="SentryHost_No" maxlength="32" autocomplete="off" />
                            </div>
                            <div class="layui-col-xs1 red-mark">*</div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">主机类型</div>
                            <div class="layui-col-xs7">
                                <div class="btnCombox" id="SentryHost_Category">
                                    <ul>
                                        @* <li data-value="1" class="select">车场岗亭收费软件</li> *@
                                        <li data-value="2" class="">车场岗亭后台服务</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row SentryHost_Net">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">岗亭TCP端口</div>
                            <div class="layui-col-xs7">
                                <input type="text" class="layui-input v-number v-null" id="SentryHost_Port"
                                       name="SentryHost_Port" maxlength="5" value="33568" />
                            </div>
                            <div class="layui-col-xs1 red-mark">*</div>
                        </div>
                        <div class="layui-row SentryHost_NotBS">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">监控画面数</div>
                            <div class="layui-col-xs7">
                                <div class="btnCombox" id="SentryHost_VideoPlayNum">
                                    <ul>
                                        <li data-value="8" class="select">8个画面</li>
                                        <li data-value="16" class="">16个画面</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row SentryHost_NotBS">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">开机自启动选项</div>
                            <div class="layui-col-xs7">
                                <div class="btnCombox" id="SentryHost_T30AutoStart">
                                    <ul>
                                        <li data-value="0" class="select">不操作</li>
                                        <li data-value="1" class="">启动并进入在线监控(使用最后登录的账号)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="layui-row">
                            <div class="layui-col-xs2 layui-col-xs-offset1 m-label">岗亭经纬度</div>
                            <div class="layui-col-xs7 layui-row">
                                <div class="layui-col-xs6">
                                    <div class="input-group">
                                        <input type="text" class="layui-input" id="SentryHost_Longitude"
                                               name="SentryHost_Longitude" value="0" placeholder="请输入经度" />
                                       @*  <span class="input-group-btn">
                                            <button class="layui-btn layui-btn-outline after btn_map">
                                                <i class="fa fa-map-marker"></i> &nbsp;地图
                                            </button>
                                        </span> *@
                                    </div>
                                </div>
                                <div class="layui-col-xs6">
                                    <div class="input-group">
                                        <input type="text" class="layui-input" id="SentryHost_Latitude"
                                               name="SentryHost_Latitude" value="0" placeholder="请输入纬度" />
                                        @* <span class="input-group-btn">
                                            <button class="layui-btn layui-btn-outline after btn_map">
                                                <i class="fa fa-map-marker"></i> &nbsp;地图
                                            </button>
                                        </span> *@
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 m-label">管理通道</div>
                <div class="layui-col-xs7">
                    <table id="stable" lay-filter="table">
                    </table>
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-xs2 layui-col-xs-offset1 edit-label">&nbsp;</div>
                <div class="layui-col-xs7">
                    <button class="btn btn-primary layui-hide" id="Save">
                        <i class="fa fa-save"></i>
                        <t>保存</t>
                    </button>
                    <button class="btn btn-primary layui-bg-red layui-hide" id="Cancel">
                        <i class="fa fa-close"></i>
                        <t>取消</t>
                    </button>
                </div>
            </div>
        </div>

        <div class="layui-row layui-hide" id="nodata">
            <div class="padding-15">还没有添加过岗亭，您可以先点击左侧[新增岗亭]进行添加</div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layform = null;
        var laytable = null;
        layui.use(['form'], function () {
            layform = layui.form;
            laytable = layui.table;
            pager.init();
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("SentryHost_No");

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            passways: null,
            alllink: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllPasswayAndLink", {}, function (json) {
                    if (json.success) {
                        var passways = json.data.passways;
                        var alllink = json.data.alllink;
                        pager.alllink = alllink;
                        pager.passways = passways;
                        //转换静态表格
                        var cols = [[
                            { type: "checkbox", fixed: 'left' }
                            , { field: 'Passway_Name', title: '通道名称' }
                            , { field: 'linkName', title: '所属区域' }
                        ]];

                        passways.forEach(function (p, index) {
                            var links = [];
                            alllink.forEach(function (l, index) {
                                if (l.PasswayLink_PasswayNo == p.Passway_No) {
                                    links[links.length] = l;
                                }
                            });

                            var linkName = [];
                            links.forEach(function (l, index) {
                                linkName[linkName.length] = l.ParkArea_Name + "[" + (l.PasswayLink_GateType == 0 ? "出口" : "入口") + "]";
                            });
                            p.linkName = linkName.join("、");
                        });
                        laytable.render({
                            elem: '#stable', id: 'stableTable', cols: cols, data: passways, limit: 1000
                        });
                        // $('input[lay-filter="layTableAllChoose"]').parent().html("")
                        //$(".layui-table-header").addClass("layui-hide");
                    } else {
                        layer.msg(json.msg)
                    }
                }, "json");
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $("#SentryHost_Type ul li").removeClass("layui-hide");
                    $.post("GetSentryHost", { SentryHost_No: paramNo }, function (json) {
                        if (json.success) {
                            var model = json.data.model;
                            model.SentryHost_Category = 2;
                            var passways = json.data.passways;
                            var linkExts = json.data.linkExts;
                            if (paramAct == "Update") {
                                $("#verifyCheck").fillForm(model, function (data) { });
                                $("#SentryHost_No").attr("disabled", true);
                                SetCheckedTr(passways)
                                LoadDeviceConfig(model)

                                $(".falsemodify").each(function () {
                                    var val = config[$(this).attr("id")];
                                    $(this).find("ul li").each(function () {
                                        if ($(this).attr("data-value") != val)
                                            $(this).removeClass("layui-hide").addClass("layui-hide")
                                    });
                                });
                            }
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json")
                } else {
                    $("#SentryHost_Type ul li[data-value='1']").removeClass("layui-hide");

                    $("#SentryHost_No").val('@carparking.Common.Utils.CreateNumber');
                }
            },
            bindEvent: function () {

                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });
                    var linkData = getLinkData();
                    param.SentryHost_Type = config.SentryHost_Type;
                    param.SentryHost_Category = config.SentryHost_Category;
                    param.SentryHost_Net = config.SentryHost_Net;
                    param.SentryHost_VideoPlayNum = config.SentryHost_VideoPlayNum;
                    param.SentryHost_T30AutoStart = config.SentryHost_T30AutoStart;//是否自动启动T30。0不自动，1自动。 默认为1。 不传递该参

                    //判断SentryHost_Longitude,SentryHost_Latitude是否为空
                    if (param.SentryHost_Longitude === "" || param.SentryHost_Longitude == null) {
                        param.SentryHost_Longitude = 0;
                    }
                    if (param.SentryHost_Latitude === "" || param.SentryHost_Latitude == null) {
                        param.SentryHost_Latitude = 0;
                    }

                    //判断SentryHost_Longitude长度是否大于10
                    if (param.SentryHost_Longitude.length > 10) {
                        layer.msg("经度长度不能大于10位");
                        return;
                    }

                    //判断SentryHost_Latitude长度是否大于10
                    if (param.SentryHost_Latitude.length > 10) {
                        layer.msg("纬度长度不能大于10位");
                        return;
                    }

                    //判断SentryHost_Longitude,SentryHost_Latitude 输入是否为数字
                    if (isNaN(param.SentryHost_Longitude) || isNaN(param.SentryHost_Latitude)) {
                        layer.msg("经纬度输入必须为数字");
                        return;
                    }

                    //判断SentryHost_Latitude纬度是否在-90到90之间
                    if (param.SentryHost_Latitude < -90 || param.SentryHost_Latitude > 90) {
                        layer.msg("纬度输入必须在-90到90之间");
                        return;
                    }

                    //判断SentryHost_Longitude经度是否在-180到180之间
                    if (param.SentryHost_Longitude < -180 || param.SentryHost_Longitude > 180) {
                        layer.msg("经度输入必须在-180到180之间");
                        return;
                    }


                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        LAYER_OPEN_TYPE_0("确定新增岗亭?", res => {
                            LAYER_LOADING("处理中...");
                            $.post("AddSentryHost", { jsonModel: JSON.stringify(param), linkModel: JSON.stringify(linkData) }, function (json) {
                                $("#Save").removeAttr("disabled")
                                if (json.success) {
                                    var passwayNo = json.data;
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    layer.msg("保存成功", { time: 1000 })
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    } else {
                        LAYER_OPEN_TYPE_0("确定修改岗亭?", res => {
                            LAYER_LOADING("处理中...");
                            param.SentryHost_No = paramNo;
                            $.post("UpdateSentryHost", { jsonModel: JSON.stringify(param), linkModel: JSON.stringify(linkData) }, function (json) {
                                $("#Save").removeAttr("disabled")
                                if (json.success) {
                                    window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    layer.msg("保存成功", { time: 1000 })
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                            $("#Save").removeAttr("disabled")
                        })
                    }
                });

                $("#Cancel").click(function () {
                    parent.layer.closeAll()
                })

                $(".btn_map").click(function () {
                    layer.open({
                        title: "<i class='fa fa-map-marker'></i> 设置岗亭经纬度",
                        type: 2,
                        area: ['80%', '80%'],
                        fix: true, //不固定
                        maxmin: false, //可缩放
                        content: 'Map?y=' + $("#Parking_Y").val() + "&x=" + $("#Parking_X").val()
                    });
                });

            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    $(".btn_map").removeClass("layui-hide");
                });
            }
        };

        //读取当前关联区域列表
        var getLinkData = function () {
            var data = [];
            var $tableView = $("#stable").next(".layui-table-view"); // 找到渲染后的表格
            $tableView.find(".layui-table-fixed tbody tr").each(function (i, row) {
                var $checkbox = $(row).find("div.layui-form-checkbox");
                // 使用 Layui 渲染时的状态检查
                if ($checkbox.hasClass("layui-form-checked")) {
                    var item = pager.passways[i];
                    if (item) {
                        data.push(item.Passway_No);
                    }
                }
            });
            console.log(data);
            return data;
        }

        //表单赋值，编辑时设置管理的通道
        var SetCheckedTr = function (passways) {
            for (var i = 0; i < passways.length; i++) {
                pager.passways.forEach(function (item, index) {
                    if (item.Passway_No == passways[i].Passway_No) {
                        var trcheckbox = $("tr[data-index='" + index + "'] input[type='checkbox']");
                        trcheckbox.attr("checked", true);
                    }
                });
            }
            layform.render()
            syncHeaderCheckboxIfAllSelected("stable");
        }

        function syncHeaderCheckboxIfAllSelected(tableId) {
            var $table = $("#" + tableId).next(".layui-table-view"); // Layui渲染后的容器

            var checkboxes = $table.find(".layui-table-body tbody input[type='checkbox']:not(:disabled)");
            var checked = checkboxes.filter(":checked");

            var isAllChecked = checkboxes.length > 0 && checkboxes.length === checked.length;

            var $headerCheckbox = $table.find(".layui-table-header thead input[type='checkbox']");
            $headerCheckbox.prop("checked", isAllChecked);
            layui.form.render("checkbox");
        }
    </script>
    <script type="text/javascript">
        //设备参数配置[仅选项按钮]默认值
        var config = {
            SentryHost_Type: 1,
            SentryHost_Category: 2,
            SentryHost_Net: 1,
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName);
            });
        });
        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == "SentryHost_Net") {
                if (config[idName] == "1") {
                    $(".SentryHost_Net").removeClass("layui-hide");
                } else {
                    $(".SentryHost_Net").removeClass("layui-hide").addClass("layui-hide");
                }
            }
            if (idName == "SentryHost_Category") {
                if (config[idName] == "2") {
                    $(".SentryHost_Category").removeClass("layui-hide");
                    $(".SentryHost_NotBS").removeClass("layui-hide").addClass("layui-hide");
                } else {
                    $(".SentryHost_Category").removeClass("layui-hide").addClass("layui-hide");
                    $(".SentryHost_NotBS").removeClass("layui-hide");
                }
            }
        }
    </script>
</body>

</html>
