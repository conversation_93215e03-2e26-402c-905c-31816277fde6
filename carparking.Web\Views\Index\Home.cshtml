﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="~/Static/admin/layui/css/layui.css" media="all">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="~/Static/admin/style/admin.css" media="all">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <style>
        .layui-fluid { padding: 5px 15px; }
        .property tr { height: 43px; }
        #CommunityCount { color: #0094ff; font-size: 20px; padding: 0 10px; letter-spacing: 1px; }
        .chosen-container { width: 180px !important; }
        .chosen-container-single .chosen-single { height: 38px; border: 0 !important; box-shadow: none; }
        .chosen-container-single .chosen-single span { height: 38px; line-height: 38px; }
        .leftBorder { text-align: center; }

        .sltOptions { position: absolute; right: 90px; top: 12px; z-index: 999; }
        .sltOptions span { float: left; padding: 5px; background-color: #eee; border-right: 1px solid #fff; cursor: pointer; width: 50px; text-align: center; user-select: none; }
        .sltOptions span.active { background-color: #0094ff; color: #fff; }

        #HomeCardComm p cite { font-family: FangSong; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md8" id="easyBtns">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md6">
                        <div class="layui-card">
                            <div class="layui-card-header ">快捷方式</div>
                            <div class="layui-card-body">
                                <div class="layui-carousel layadmin-carousel layadmin-shortcut">
                                    <div carousel-item>
                                        <ul class="layui-row layui-col-space10">
                                            <li class="layui-col-xs3" id="FastGuide">
                                                <a lay-href="FastGuide/Index">
                                                    <i class="layui-icon layui-icon-engine"></i>
                                                    <cite><t>向导设置</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="ParkArea">
                                                <a lay-href="ParkArea/Index">
                                                    <i class="layui-icon layui-icon-app"></i>
                                                    <cite><t>场区设置</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="SentryHost">
                                                <a lay-href="SentryHost/Index">
                                                    <i class="layui-icon layui-icon-cols"></i>
                                                    <cite><t>岗亭管理</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="Passway">
                                                <a lay-href="Passway/Index">
                                                    <i class="layui-icon layui-icon-slider"></i>
                                                    <cite><t>车道管理</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="Device">
                                                <a lay-href="Device/Index">
                                                    <i class="layui-icon layui-icon-component"></i>
                                                    <cite><t>设备管理</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="Policy">
                                                <a lay-href="Policy/Index">
                                                    <i class="layui-icon layui-icon-util"></i>
                                                    <cite><t>车场设置</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="BillingRule">
                                                <a lay-href="BillingRule/Index">
                                                    <i class="layui-icon layui-icon-senior"></i>
                                                    <cite><t>计费规则</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="Passway">
                                                <a lay-href="LaneMonitor/Index">
                                                    <i class="layui-icon layui-icon-video"></i>
                                                    <cite><t>车道监控</t></cite>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6">

                        <div class="layui-card">
                            <div class="layui-card-header ">快捷方式</div>
                            <div class="layui-card-body">
                                <div class="layui-carousel layadmin-carousel layadmin-shortcut">
                                    <div carousel-item>
                                        <ul class="layui-row layui-col-space10">
                                            <li class="layui-col-xs3">
                                                <a lay-href="ParkOrder/Index">
                                                    <i class="layui-icon layui-icon-layouts"></i>
                                                    <cite><t>停车收费</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3">
                                                <a lay-href="InParkRecord/Index">
                                                    <i class="layui-icon layui-icon-shrink-right"></i>
                                                    <cite><t>出入场记录</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="Device">
                                                <a lay-href="Owner/Index">
                                                    <i class="layui-icon layui-icon-add-circle"></i>
                                                    <cite><t>车辆登记</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="ParkArea">
                                                <a lay-href="WorkShift/Index">
                                                    <i class="layui-icon layui-icon-chart-screen"></i>
                                                    <cite><t>交班记录</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="BillingRule">
                                                <a lay-href="PayOrder/Index">
                                                    <i class="layui-icon layui-icon-list"></i>
                                                    <cite><t>缴费记录</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="Admins">
                                                <a lay-href="Admins/Index">
                                                    <i class="layui-icon layui-icon-username"></i>
                                                    <cite><t>账号管理</t></cite>
                                                </a>
                                            </li>
                                            <li class="layui-col-xs3" id="PowerGroup">
                                                <a lay-href="PowerGroup/Index">
                                                    <i class="layui-icon layui-icon-auz"></i>
                                                    <cite><t>权限管理</t></cite>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="layui-col-md4" id="HomeCardComm">
                <div class="layui-card">
                    <div class="layui-card-header"><t>车位分析</t></div>
                    <div class="layui-card-body">
                        <div class="layui-carousel layadmin-carousel layadmin-backlog">
                            <div carousel-item>
                                <ul class="layui-row layui-col-space10">
                                    <li class="layui-col-xs6" title="不计算超过设置日期的场内订单">
                                        <a lay-text="场内车辆" class="layadmin-backlog-body">
                                            <h3><t>场内车辆</t></h3>
                                            <p><cite id="inParkCount">0</cite></p>
                                        </a>
                                    </li>
                                    <li class="layui-col-xs6">
                                        <a lay-text="重点车辆" class="layadmin-backlog-body">
                                            <h3><t>重点车辆</t></h3>
                                            <p><cite id="parkCarCount">0</cite></p>
                                        </a>
                                    </li>
                                    <li class="layui-col-xs6">
                                        <a lay-text="总车位数" class="layadmin-backlog-body">
                                            <h3><t>总车位数</t></h3>
                                            <p><cite id="spaceNum">0</cite></p>
                                        </a>
                                    </li>
                                    <li class="layui-col-xs6" title="超过设置日期的场内订单不占用车位数">
                                        <a lay-text="剩余车位" class="layadmin-backlog-body">
                                            <h3><t>剩余车位</t></h3>
                                            <p><cite id="haveNum">0</cite></p>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="leftBorder"><t>车流量分析</t></div>
                        <div class="sltOptions" id="ctype">
                            <span data-index="0" class="active">今日</span>
                            <span data-index="7">一周内</span>
                            <span data-index="30">30天内</span>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <div class="layadmin-dataview" id="ch_dataview" data-anim="fade" lay-filter="LAY-index-dataview">

                            <div carousel-item id="LAY-index-dataview">
                                <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=0" asp-append-version="true"></script>
    <script>
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'console'], function () {
            pager.init();
        });
    </script>

    <script>
        var pager = {
            init: function () {
                //$.ajaxSettings.async = false;
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                //$.ajaxSettings.async = true;
            },
            bindSelect: function () {
                onEChartSize();
            },
            bindData: function () {
                $.post("CarSpaceAnalysis", {}, function (json) {
                    if (json.success) {
                        numHand.start(json.data.inParkCount, "inParkCount", 9);
                        numHand.start(json.data.parkCarCount, "parkCarCount", 9);
                        numHand.start(json.data.spaceNum, "spaceNum", 9);
                        numHand.start(json.data.haveNum, "haveNum", 9);

                        if (json.data.parkCarCount > 0) $("#parkCarCount").css({ "color": "orangered" });
                    }
                }, "json");
            },
            bindEvent: function () {
                $("#ctype span").click(function () {
                    $("#ctype span").removeClass("active");
                    $(this).addClass("active");
                });

                window.onresize = function () {
                    onEChartSize();
                }
            }
        }

        var onEChartSize = function () {
            var vh = $(window).height() - $("#easyBtns").height() - 110;
            if (vh < 260) vh = 260;
            if (vh > 520) vh = 520;
            $("#ch_dataview,#ch_dataview #LAY-index-dataview div").height(vh);
        }

        //数字跳动效果
        var numHand = {
            roll: function (total, idname, step) {
                let n = 0;
                return function () {
                    n = (n + step) >= total ? total : (n + step);
                    if (n <= total) {
                        document.getElementById(idname).innerHTML = n;
                    }
                }
            },
            //total 最终显示的数字，idname 标签id，step 跳动的速度，runtime 跳动的总时长
            start: function (total, idname, step, runtime = 1000) {
                let rolling = numHand.roll(total, idname, step)
                runtime = (runtime >= 300) ? runtime : 1000;
                for (let i = 0; i < (total / step); i++) {
                    let timer = setTimeout(rolling, (runtime / total) * i * step)
                }
                //clearTimeout(timer);
            }
        }
    </script>
</body>
</html>


