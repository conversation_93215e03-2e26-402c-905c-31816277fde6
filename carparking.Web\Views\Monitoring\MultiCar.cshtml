﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车辆信息</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 2.2vh !important; font-weight: 600; }
        .cardesc { display: block; font-size: 15px; text-align: left; width: calc(100% - 5px); margin-left: 1px; margin-top: 5px; }
        .layui-btn.close { font-weight: bolder; margin-right: 10px; font-size: 2.5vw; height: 8vh; line-height: 8vh; width: 14vw; border-radius: 5px; padding: 0 18px; background-color: #009688; color: #fff; white-space: nowrap; text-align: center; }
        .cardesc input { border: none; }
        .layui-card-header { padding: 0 !important; padding-left: 10px !important; }
        .cardesc input:focus { border: none !important; }
        div.layui-table-cell { height: 4vh !important; line-height: 4vh !important; padding: 0 5px; transition: box-shadow 0.2s ease; }
        div.layui-table-cell span { font-weight: bolder !important; }
        td:nth-child(3) div.layui-table-cell span { font-weight: normal !important; }
        td:nth-child(2) div.layui-table-cell { color: #5868e0;font-weight: bolder !important; }
    </style>
</head>
<body>

    <div class="ibox-content">
        <div class="layui-card">
            <div class="layui-card-header" style="font-size: 16px;">
                <div class="cardesc">
                    <div style="display: flex; flex-wrap: wrap; width: 100%;">
                        <div style="width: 33.33%; margin-bottom: 10px;">
                            <span>车主姓名：<input type="text" class="layui-input" style="display:inline-block;width:120px" id="ownerName" readonly /></span>
                        </div>
                        <div style="width: 63.33%; margin-bottom: 10px;">
                            <span>系统车位号：<input type="text" class="layui-input" style="display:inline-block;width:100px" id="parkingSpot" readonly /></span>
                        </div>
                        <div style="width: 33.33%;margin-bottom: 10px;">
                            <span>车主车辆数：<input type="number" class="layui-input" style="display:inline-block;width:60px" id="carCount" readonly /></span>
                        </div>
                        <div style="width: 63.33%;margin-bottom: 10px;">
                            <span>车主场内车辆数：<input type="number" class="layui-input" style="display:inline-block;width:60px" id="ownerCarCount" readonly /></span>
                        </div>
                        <div style="width: 99.33%; margin-bottom: 10px;">
                            <span>车辆可停区域车位数：<input type="text" class="layui-input" style="display:inline-block;width:400px" id="parkingArea" readonly /></span>
                        </div>
                         <div style="width: 99.33%; ">
                            <span>可停区域剩余车位数：<input type="text" class="layui-input" style="display:inline-block;width:510px" id="remainspaces" readonly /></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-card-body">
                <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
            </div>
        </div>
        <div class="layui-form-item" style="text-align: center; margin-top: 15px;">
            <button type="button" class="layui-btn layui-btn-primary close" id="btnClose">关闭</button>
        </div>
    </div>

    <script type="text/html" id="TmplIsMoreCar">
        {{# if(d.CarType_IsMoreCar==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>

    <!-- 添加订单号模板 -->
    <script type="text/html" id="orderNoTpl">
        <div style="display: flex; align-items: center;">
            <span>{{d.InCar_ParkOrderNo}}</span>
            <i class="fa fa-file-text-o" style="cursor: pointer; margin-left: 5px; color: #1E9FFF;" data-key="101" lay-event="showOrder" title="查看订单详情"></i>
        </div>
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>

    <script>
        var paramNo = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var carNo = decodeURIComponent($.getUrlParam("carNo"));
        var comtable = null;

        layui.use(['table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;

            var cols = [[
                { type: 'radio' }
                , { field: 'InCar_CarNo', title: '车牌号', width: 120 }
                , { field: 'InCar_ParkOrderNo', title: '停车订单号', width: 250, templet: '#orderNoTpl' }
                , { field: 'InCar_ParkAreaNo', title: '停车区域', width: 100 }
                , { field: 'InCar_EnterTime', title: '入场时间', width: 190 }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetMultiCarList'
                , method: 'post'
                , defaultToolbar: []
                , cols: cols
                , height: 'full-320'
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { orderno: paramNo, carno: carNo }
                , limit: tb_page_limit()
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    if (d.code == 0 && d.msg != null) {
                        //json字符串
                        var msg = JSON.parse(d.msg);
                        $("#ownerName").val(msg.ownerName);
                        $("#parkingSpot").val(msg.parkingSpot);
                        $("#parkingArea").val(msg.parkingArea);
                        $("#carCount").val(msg.carCount);
                        $("#ownerCarCount").val(msg.ownerCarCount);
                        $("#remainspaces").val(msg.remainspaces);
                    } else {
                        layer.msg(d.msg, { icon: 2, time: 2000 });
                    }
                    $(window).resize(function(){
                        comtable.resize();
                    });
                }
            });

            tb_row_radio(table);

            pager.init();

            $('#btnClose').on('click', function () {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });

            table.on('tool(com-table-base)', function (obj) {
                var data = obj.data;
                if (obj.event === 'showOrder') {
                    parent.btns.onOpsClick(data.InCar_ParkOrderNo, $(this), function () {

                    });
                }
            });
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Monitoring/GetMultiCarList'
                    , where: { orderno: paramNo, carno: carNo } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
