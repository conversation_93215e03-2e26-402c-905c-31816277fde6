﻿
<!DOCTYPE html>
<html>
<head>
    <title>场内车</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input " name="InCar_CarNo"
                                       id="InCar_CarNo" autocomplete="off" placeholder="车牌号" maxlength="10" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                          <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                            <script type="text/html" id="toolbar_btns">
                                <div class="layui-btn-container">

                                </div>
                            </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20241109" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v2306201521" asp-append-version="true"></script>

    <!-- 添加订单号模板 -->
    <script type="text/html" id="orderNoTpl">
        {{#  if(d.InCar_ParkOrderNo){ }}
        <div style="display: flex; align-items: center;">
            <span>{{d.InCar_ParkOrderNo}}</span>
            <i class="fa fa-file-text-o" style="cursor: pointer; margin: 0 2px; color: #1E9FFF;" data-key="101" lay-event="showOrder" title="查看订单详情"></i>
        </div>
        {{#  }else { }}
        <span style="color: #999;"></span>
        {{#  } }}
    </script>

    <script>
        var paramOwnerNo = '@ViewBag.Owner_No';
        var comtable = null;
        var table = null;

        s_carno_picker.init("InCar_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "InCar_CarNo") {
                $("#InCar_CarNo").val(carno.join(''));
            }
        }, "web");

        layui.use(['table', 'jquery', 'form'], function () {
            table = layui.table;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                data.Owner_No = paramOwnerNo;
                return data;
            });

            var cols = [[
                { field: 'InCar_ID', title: '序号', width: 60 }
                , { field: 'InCar_CarNo', title: '车牌号码', width: 120 }
                , {
                    field: 'InCar_Status', title: '在场内', width: 100, templet: function (d) {
                        if (d.InCar_Status == 200) return tempBar(1, "是");
                        else
                            return tempBar(5, "否");
                    }
                }
                , { field: 'InCar_EnterTime', title: '入场时间', width: 190 }
                , { field: 'InCar_ParkAreaNo', title: '停车区域', width: 100 }
                , { field: 'InCar_ParkOrderNo', title: '停车订单号', width: 220, templet: '#orderNoTpl' }
                , {
                    field: 'InCar_CarNo', title: '操作', templet: function (d) {
                        if (d.InCar_Status == 200) {
                            return "<a href='javascript:;' title='关闭场内停车订单'  onclick='DeleteInCar(\"" + d.InCar_CarNo + "\")' style='text-decoration: underline;'>关闭订单</a>";
                        } else {
                            return "";
                        }
                    }
                }
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Owner/GetInParkCarList'
                , method: 'post'
                //不显示工具栏
                , defaultToolbar: false
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam), Owner_No: paramOwnerNo }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });
            
            table.on('tool(com-table-base)', function (obj) {
                var data = obj.data;
                if (obj.event === 'showOrder') {
                    console.log(data.InCar_ParkOrderNo);
                    window.parent.pager.openParkOrderDetail(data.InCar_ParkOrderNo);
                }
            });
            //tb_row_radio(table);

            pager.init();
        });

        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                layui.form.render();
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                    data.Owner_No = paramOwnerNo;
                    return data;
                });
                comtable.reload({
                    url: '/Owner/GetInParkCarList'
                    , where: { conditionParam: JSON.stringify(conditionParam), Owner_No: paramOwnerNo } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {

            }
        }

        function DeleteInCar(InCar_CarNo) {
            layer.open({
                title: '关闭场内停车订单',
                content: '确定要关闭 ' + InCar_CarNo + ' 的场内停车订单吗？<br><span style="color: red;">关闭后出场将无入场记录。</span>',
                btn: ['确定', '取消'],
                yes: function () {
                    //请求后台
                    $.post("/Owner/DeleteInCar", { InCar_CarNo: InCar_CarNo }, function (json) {
                        if (json.success) {
                            layer.msg('关闭成功');
                            pager.bindData(1);
                        } else {
                            layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                        }
                    });
                }
            });
        }
    </script>
  
</body>
</html>
