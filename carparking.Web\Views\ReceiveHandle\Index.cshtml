﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>数据接收</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }

        .layui-form-select .layui-input { width: 182px; }

        .content { margin: 7px 19px 0px 19px !important; cursor: pointer; }

        .content:hover { color: #1E9FFF; font-weight: 600; font-size: 20px; }

        span.ss { font-size: 13px; text-align: justify; word-break: break-all; color: #61a8d1; background-color: #f5eeee; float: left; padding: 3px 5px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>数据接收</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input" name="ReceiveHandle_ReceiveDataNo" id="ReceiveHandle_ReceiveDataNo" autocomplete="off" placeholder="数据编码" value="" maxlength="21" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="ReceiveHandle_SentryNo" id="ReceiveHandle_SentryNo" autocomplete="off" placeholder="岗亭编码" value="" maxlength="21" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="数据类型" class="form-control chosen-select " id="ReceiveHandle_DataType" name="ReceiveHandle_DataType" lay-search>
                                    <option value="">数据类型</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <select data-placeholder="数据状态" class="form-control chosen-select " id="ReceiveHandle_Status" name="ReceiveHandle_Status" lay-search>
                                    <option value="">数据状态</option>
                                    <option value="0">未处理</option>
                                    <option value="1">已处理</option>
                                    <option value="2">处理失败</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input" name="ReceiveHandle_Desc" id="ReceiveHandle_Desc" autocomplete="off" placeholder="数据描述" value="" maxlength="40" />
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input" name="startDate" id="startDate" autocomplete="off" placeholder="开始日期" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="endDate" id="endDate" autocomplete="off" placeholder="截止日期" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                             <button class="layui-btn layui-btn-sm layui-hide" id="Detail" lay-event="Detail"><i class="fa fa-list-alt"></i><t>详情</t></button>
                            </div>
                        </script>
                    </div>
                    <div class="layui-col-xs10"><span class="ss"><b>温馨提示：</b>当前界面记录着由其它岗亭分发过来的数据.</span></div>
                </div>
            </div>
        </div>

    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/html" id="TmplSuccess">
         {{#  if(d.ReceiveHandle_Success==-1){ }}
        <span class="layui-badge layui-bg-orange ">未处理</span>
        {{#  } else if(d.ReceiveHandle_Success==1) { }}
        <span class="layui-badge layui-bg-blue ">成功</span>
        {{#  } else if(d.ReceiveHandle_Success==3) { }}
        <span class="layui-badge layui-bg-gray ">取消</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-orange ">失败</span>
        {{#  } }}
    </script>
    <script type="text/html" id="TmplOK">
        {{#  if(d.ReceiveHandle_Ok==0){ }}
        <span class="layui-badge layui-bg-gray ">未处理</span>

        {{#  } else if(d.ReceiveHandle_Ok==1) { }}
        <span class="layui-badge layui-bg-orange ">处理失败</span>
        {{#  } else if(d.ReceiveHandle_Ok==2) { }}
        <span class="layui-badge layui-bg-blue ">处理成功</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-cyan ">未知</span>
        {{#  } }}
    </script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var dataTypedic = { 1: "车辆登记", 2: "黑名单管理", 3: "访客车辆", 4: "商家车辆", 5: "向导设置", 6: "场区设置", 7: "岗亭管理", 8: "车道管理", 9: "设备管理", 10: "车场设置", 11: "车道监控", 12: "计费规则", 13: "充值规则", 14: "通行控制", 15: "车牌类型", 16: "车牌颜色", 17: "尾号限行", 18: "特殊车辆", 19: "日期设置", 20: "城市平台上报", 21: "优惠设置", 22: "车牌优惠", 23: "出入场记录", 24: "缴费记录", 25: "缴费明细", 26: "优惠券记录", 27: "场内记录", 28: "车牌识别记录", 29: "人工开闸记录", 30: "开闸放行记录", 31: "交班记录", 32: "倒车记录", 33: "事件管理", 34: "健康码记录", 35: "车辆注销记录", 36: "白名单记录", 37: "账号管理", 38: "权限管理", 39: "系统设置", 40: "支付方式", 41: "支付费用", 42: "车辆入场", 43: "车辆出场",44: "异常放行",45: "无入场记录出场",46: "自助现金预交记录",48: "预入场",47: "车场信息", 999: "未知" };

        layui.use(['table', 'form'], function () {
            pager.init();
            var table = layui.table;
            var layuiForm = layui.form;
            layuiForm.render("select");

            $("#startDate").val(new Date().Format("yyyy-MM-dd"));
            $("#endDate").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["startDate", "endDate"], { type: 'date', range: true });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'ReceiveHandle_ID', title: '数据ID' }
                , { field: 'ReceiveHandle_ReceiveDataNo', title: '数据编码' }
                , {
                    field: 'ReceiveHandle_DataType', title: '数据类型', templet: function (d) {
                        return dataTypedic[d.ReceiveHandle_DataType];
                    }
                }
                , { field: 'ReceiveHandle_Desc', title: '数据描述' }
                , {
                    field: 'ReceiveHandle_Status', title: '状态', templet: function (d) {
                        if (d.ReceiveHandle_Status == 0) return tempBar(1, "未处理");
                        else if (d.ReceiveHandle_Status == 1) return tempBar(2, "已处理");
                        else if (d.ReceiveHandle_Status == 2) return tempBar(3, "处理失败");
                        else return tempBar(5, "未知");
                    }
                }
                , { field: 'ReceiveHandle_Time', title: '创建时间' }
                , { field: 'ReceiveHandle_SentryNo', title: '岗亭编码' }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ReceiveHandle/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Pulldown':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        var dateTime = data[0].ReceiveHandle_Time;
                        for (var i = 0; i < data.length; i++) { NoArray.push(data[i].ReceiveHandle_No); }
                        break;
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            title: "数据详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'Detail?ReceiveHandle_ID=' + data[0].ReceiveHandle_ID
                        });
                        break;
                };
            });

            tb_row_checkbox();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltDataTypeList", {}, function (json) {
                    if (json.success) {
                        var options = '';
                        json.data.forEach((item, index) => {
                            options += '<option value="' + item.Key + '">' + item.Value + '</option>';
                        });
                        $("#ReceiveHandle_DataType").append(options);
                    }
                }, "json");
                layui.form.render();
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/ReceiveHandle/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
    </script>
</body>
</html>
