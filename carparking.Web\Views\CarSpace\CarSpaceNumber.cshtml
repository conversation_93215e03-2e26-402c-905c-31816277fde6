﻿
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车位类型管理</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="CarSpaceNumber_CarSpaceTypeNo" name="CarSpaceNumber_CarSpaceTypeNo">
                                    <option value="">所有车位类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="CarSpaceNumber_ParkAreaNo" name="CarSpaceNumber_ParkAreaNo">
                                    <option value="">所有场区</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.CarSpace.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcarspacetype">
        <option value="${CarSpaceType_No}">${CarSpaceType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplparkarea">
        <option value="${ParkArea_No}">${ParkArea_Name}</option>
    </script>
    <script type="text/html" id="TmplSave">
        <button class="layui-btn layui-btn-sm save" lay-event="save">保存</button>
    </script>
    <script type="text/html" id="TmplNumber">
        <input type="text" class="layui-input v-null v-number" maxlength="8"  name="CarSpaceNumber_Number" value="{{d.CarSpaceNumber_Number}}"
               style="height:28px;line-height:28px;">
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.parent.global.formPower;
        var comtable = null;

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[              
                 { field: 'CarSpaceType_Name', title: '车位类型' }
                , { field: 'ParkArea_Name', title: '停车场区' }
                , { field: 'CarSpaceNumber_Number', title: '车位数量', toolbar: "#TmplNumber" }
                , { field: '', title: '', toolbar: "#TmplSave" }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CarSpace/GetCarSpaceNumberList'
                , method: 'post'           
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function () {

                }
            });
          
            table.on('tool(com-table-base)', function (obj) {                
                var no = obj.data.CarSpaceNumber_No;    
                var numinput = obj.tr.find('td:eq(2)').find("[name='CarSpaceNumber_Number']");
                var num = numinput.val();
                //var num = obj.tr.find('td:eq(2)').find("[name='CarSpaceNumber_Number']").val();
                //if (num == "") { layer.msg("请填写车位数量", numinput, { time: 1000 }); return; }
                if (num == "") { layer.msg("请填写车位数量", { time: 1000 }); return; }
                var param = {};
                param.CarSpaceNumber_No = no;
                param.CarSpaceNumber_Number = num;
                $.getJSON("UpdateNumber", { jsonModel: JSON.stringify(param) }, function (json) {
                    if (json.Success) {
                        layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                            pager.bindData(1);
                        });
                    } else {
                        layer.msg(json.Message, { icon: 0 });                       
                    }
                });

            })


            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {

                $.getJSON("/DropList/GetCarSpaceType", {}, function (json) {
                    if (json.Success) {
                        $("#CarSpaceNumber_CarSpaceTypeNo").append($("#tmplcarspacetype").tmpl(json.Data))
                        layui.form.render("select");
                    }
                });
                $.getJSON("/DropList/GetParkArea", {}, function (json) {
                    if (json.Success) {
                        $("#CarSpaceNumber_ParkAreaNo").append($("#tmplparkarea").tmpl(json.Data))
                        layui.form.render("select");
                    }
                });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/CarSpace/GetCarSpaceNumberList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                layui.form.on("select", function (data) {
                    pager.bindData(1);
                });

                $("#Search").click(function () {
                    pager.bindData(1);
                });
            }
        }
    </script>
</body>
</html>
