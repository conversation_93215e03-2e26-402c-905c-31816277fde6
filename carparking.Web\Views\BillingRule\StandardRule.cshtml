﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>计费规则</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/css/free.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/chargerules.css?t=@DateTime.Now.Ticks" rel="stylesheet" />
    <style>
        .iptbottom { border: 0px; border-bottom: 1px solid #eee; text-align: center; }
        .iptbtn { height: 30px; line-height: 30px; border: 1px solid transparent; padding: 0 10px; }
         .headTxt {
            font-size: 14px !important;
            color: #0F9EE9 !important;
        }
    </style>
</head>
<body>

    <div class="ibox-content layui-form">
        <span class="light"></span>
        <div id="verifyCheck" @*style="border-bottom: 1px solid #bbb; "*@>
            <div class="layui-row form-group itemtitle basetitle">基本设置 <div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>
            <div class="basesetting">
                <div class="layui-row">
                    <div class="edit-label layui-col-xs2">计费标准</div>
                    <div class="layui-col-xs8">
                        <div class="btnCombox" id="ChargeRules_Type">
                            <ul>
                                <li data-value="0" class="biaozhun layui-hide select">标准版</li>
                                <li data-value="1" class="qijian layui-hide">旗舰版</li>
                                <li data-value="2" class="yinchuan layui-hide">银川收费</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="edit-label  layui-col-xs2">规则名称</div>
                    <div class="edit-ipt-ban layui-col-xs8">
                        <input type="text" class="layui-input v-null" id="ChargeRules_Name" data-minlen="2" maxlength="50" value="" placeholder="请填写规则名称" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label ">车牌类型</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <div id="BillRuleTemp_CarCardType" class="v-null" style="min-width:210px;"></div>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-xs2 edit-label ">车牌颜色</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <div id="BillRuleTemp_CarType" class="v-null" style="min-width:210px;"></div>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="help-btn-div" style="width: 27px;">&nbsp;</div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label ">计费区域</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <div id="BillRuleTemp_ParkArea" class="v-null" style="min-width:210px;"></div>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <div class="edit-label  layui-col-xs2">规则描述</div>
                    <div class="edit-ipt-ban layui-col-xs8">
                        <textarea placeholder="请简述计费规则，此处描述仅供查看，以实际设置规则为准" class="layui-textarea" maxlength="500" id="ChargeRules_Remark"></textarea>
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>
            </div>
            <div class="layui-row form-group itemtitle hourtitle">时段设置<div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>
            <div class="layui-row hoursetting">
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label">免费分钟</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input v-number  v-null" maxlength="8" value="30" id="Logic_FreeMin" data-key="Logic_FreeMin" placeholder="请填写分钟" />
                        <label class="label2 lan-label">例如：30分钟内免费.</label>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="help-btn-div"><i class="help-btn" data-key="Logic_FreeMin">?</i></div>

                    <div class="layui-col-xs2 edit-label">场内支付滞留时间</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input v-number  v-null" maxlength="8" value="5" id="Logic_PayLeaveMin" data-key="Logic_PayLeaveMin" placeholder="请填写分钟" />
                        <label class="label2 lan-label">例如：场内支付后，5分钟内离场不会产生费用.</label>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="help-btn-div"><i class="help-btn" data-key="Logic_PayLeaveMin">?</i></div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label ">免费分钟参与计费（停车时长）</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <select class="layui-select" lay-search id="Logic_IsFreeTimeCharge" name="Logic_IsFreeTimeCharge" lay-filter="Logic_IsFreeTimeCharge" data-key="Logic_IsFreeTimeCharge">
                            <option value="1" selected>参与</option>
                            <option value="0">不参与</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="help-btn-div"><i class="help-btn" data-key="Logic_IsFreeTimeCharge">?</i></div>
                    <div class="layui-col-xs2 edit-label">每日最高收费限额</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input v-floatLimit v-null" maxlength="8" id="Logic_MaxDailyAmount" data-key="Logic_MaxDailyAmount" placeholder="请填写金额(元)" />
                        <label class="label2 lan-label">例如：停车24小时最高收费XX元.</label>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="help-btn-div"><i class="help-btn" data-key="Logic_MaxDailyAmount">?</i></div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label divLogic_IsDiffTime">时段类型</div>
                    <div class="layui-col-xs3 edit-ipt-ban divLogic_IsDiffTime">
                        <select class="layui-select" lay-search id="Logic_IsDiffTime" name="Logic_IsDiffTime" lay-filter="Logic_IsDiffTime" data-key="Logic_IsDiffTime">
                            <option value="0">不分时段</option>
                            <option value="1">分时段</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark divLogic_IsDiffTime">*</div>
                    <div class="help-btn-div divLogic_IsDiffTime"><i class="help-btn" data-key="Logic_IsDiffTime">?</i></div>
                    <div class="layui-col-xs2 edit-label divLogic_IsDiffHoliday">计费类型</div>
                    <div class="layui-col-xs3 edit-ipt-ban divLogic_IsDiffHoliday">
                        <select class="layui-select" lay-search id="Section_ChargeMode" name="Section_ChargeMode" lay-filter="Section_ChargeMode" data-key="Section_ChargeMode">
                            <option value="1">按计时单位收费</option>
                            <option value="2">按时长范围收费</option>
                            <option value="3">按次收费</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark divLogic_IsDiffHoliday">*</div>
                </div>


                <div class="hourmore" style="display:none;">
                    <div class="layui-row">
                        <div class="edit-label  layui-col-xs2">时间优惠券计算免费时间</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <select class="layui-select" lay-search data-key="Logic_IsCouponTime" id="Logic_IsCouponTime" name="Logic_IsCouponTime">
                                <option value="1">不计算</option>
                                <option value="0" selected>计算</option>
                            </select>
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="help-btn-div"><i class="help-btn" data-key="Logic_IsCouponTime">?</i></div>

                        @* <div class="layui-col-xs2 edit-label ">多区域嵌套记录严格匹配</div>
                        <div class="layui-col-xs3 edit-ipt-ban">
                        <select class="layui-select" lay-search id="Logic_NestedRecords" name="Logic_NestedRecords" lay-filter="Logic_NestedRecords" data-key="Logic_NestedRecords">
                        <option value="1">启用</option>
                        <option value="0" selected>禁用</option>
                        </select>
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                        <div class="help-btn-div"><i class="help-btn" data-key="Logic_NestedRecords">?</i></div>*@
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs2 edit-label layui-hide across">跨计时单位拆分</div>
                        <div class="layui-col-xs3 edit-ipt-ban layui-hide across">
                            <select class="layui-select" lay-search id="Logic_Across" name="Logic_Across" lay-filter="Logic_Across" data-key="Logic_Across">
                                <option value="0">禁用</option>
                                <option value="1">启用</option>
                            </select>
                        </div>
                        <div class="layui-col-xs1 red-mark layui-hide across">*</div>
                        <div class="layui-col-xs1 layui-hide across"><i class="help-btn" data-key="Logic_Across">?</i></div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-xs2 edit-label layui-hide IsTopMoney">最高收费金额</div>
                        <div class="layui-col-xs3 edit-ipt-ban layui-hide IsTopMoney">
                            <input type="text" class="layui-input v-floatLimit v-null" data-minlen="6" maxlength="6" placeholder="填写金额（元）" id="Logic_TopMoney" name="Logic_TopMoney" data-key="Logic_TopMoney" />
                        </div>
                        <div class="layui-col-xs1 red-mark layui-hide IsTopMoney">*</div>
                    </div>
                    <!--周期多次累计计费-->
                    <div class="layui-row">
                        <div class="edit-label  layui-col-xs2">周期计费</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <select class="layui-select" lay-search data-key="Cycle_IsCharge" id="Cycle_IsCharge" name="Cycle_IsCharge">
                                <option value="1">启用</option>
                                <option value="0" selected>禁用</option>
                            </select>
                            <label class="label2 lan-label">设置周期内多次进出有最高限额</label>
                        </div>
                    </div>
                    <div class="layui-row  Cycle_IsCharge layui-hide">
                        <div class="edit-label  layui-col-xs2">累计小时数</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <select class="layui-select" lay-search id="Cycle_Unit" name="Cycle_Unit" data-key="Cycle_Unit">
                                <option value="12">12小时</option>
                                <option value="24" selected>24小时</option>
                            </select>
                            <label class="label2 lan-label">例如：12小时内累计最高收费200元.</label>
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                        <div class="edit-label  layui-col-xs2">累计最高收费</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <input type="text" class="layui-input v-floatLimit v-null" data-minlen="2" maxlength="8" value="" data-key="Cycle_UnitMaxCharge" placeholder="请填写收费金额（元）" />
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                    </div>
                    <div class="layui-row  Cycle_IsCharge layui-hide">
                        <div class="edit-label layui-col-xs2">周期开始时间</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <select class="layui-select" lay-search id="Cycle_StartTime" name="Cycle_StartTime" data-key="Cycle_StartTime">
                                <option value="1">入场时间</option>
                                <option value="0">自定义时间</option>
                            </select>
                        </div>
                        <div class="help-btn-div"><i class="help-btn" data-key="Cycle_StartTime">?</i></div>
                        <div class="edit-label  Cycle_StartTime layui-col-xs2 layui-hide">自定义时间</div>
                        <div class="edit-ipt-ban layui-col-xs3 Cycle_StartTime layui-hide">
                            <input type="text" class="layui-input required v-null" autocomplete="off" id="Cycle_CustomStartTime" name="Cycle_CustomStartTime" value="" data-key="Cycle_CustomStartTime" data-error="不能为空" />
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="layui-col-xs1 red-mark  Cycle_StartTime layui-hide">*</div>
                    </div>
                    <div class="layui-row">
                        <div class="edit-label  layui-col-xs2">跨天加收过夜费</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <select class="layui-select" lay-search data-key="Logic_IsNightFee" id="Logic_IsNightFee" name="Logic_IsNightFee">
                                <option value="1">启用</option>
                                <option value="0" selected>禁用</option>
                            </select>
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="help-btn-div"><i class="help-btn" data-key="Logic_IsNightFee">?</i></div>
                    </div>
                    <div class="layui-row Logic_IsNightFee layui-hide">
                        <div class="edit-label layui-col-xs2">开始时间</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <input type="text" class="layui-input required v-null" autocomplete="off" id="Logic_NightTime" name="Logic_NightTime" value="00:00" data-key="Logic_NightTime" data-error="不能为空" />
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                        <div class="edit-label  layui-col-xs2">加收金额</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <input type="text" class="layui-input v-floatLimit v-null" data-minlen="2" maxlength="8" value="" id="Logic_NightFee" data-key="Logic_NightFee" placeholder="请填写加收金额（元）" />
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                    </div>
                </div>
                <div class="layui-row  hourmoresetting"><t class="content">更多设置</t>&nbsp;<i class="layui-icon layui-icon-down"></i></div>

            </div>

            @*<div class="layui-row form-group itemtitle cycletitle">周期设置<div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>

            <div class="layui-row cyclesetting">

            </div>*@
        </div>
        <div>
            <div class="layui-row itemcontent">
                <div class="layui-row form-group itemtitle ruletitle">参数设置<div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>
                <!--规则项-->
                <div class="rulebox-items rulebox rulesetting" id="rulebox">
                    <div class="workday">
                        <div class="workday_work  layui-hide">
                            <div class="workday_header">
                                <span>工作日</span>
                                <div class="divInLine divTopMoneySetItem2">
                                    <select class="layui-select" lay-search id="Workday_IsMaxCharge" name="Workday_IsMaxCharge" lay-filter="Workday_IsMaxCharge" data-key="Workday_IsMaxCharge">
                                        <option value="1">不设一天最高收费</option>
                                        <option value="2">一天最高收费</option>
                                    </select>

                                </div>

                                <div class="divInLine workday_header_value layui-hide divTopMoneySetItem2">
                                    <input type="text" class="layui-input normal  v-floatLimit v-null" value="0" id="Workday_MaxCharge" data-key="Workday_MaxCharge" />
                                </div>
                                <div class="divInLine workday_header_value layui-hide divTopMoneySetItem2">
                                    <label>元</label>
                                </div>
                                <div class="divInLine" style="position:relative;"><i class="help-btn" data-key="Workday_IsMaxCharge" style="margin-top:-16px;">?</i></div>
                            </div>
                            <div class="wcontent"></div>
                        </div>
                        <div class="workday_holiday  layui-hide">
                            <div class="workday_holiday_header">
                                <span>节假日</span>
                                <div class="divInLine divTopMoneySetItem2">
                                    <select class="layui-select" lay-search id="Holiday_IsMaxCharge" name="Holiday_IsMaxCharge" lay-filter="Holiday_IsMaxCharge" data-key="Holiday_IsMaxCharge">
                                        <option value="1">不设一天最高收费</option>
                                        <option value="2">一天最高收费</option>
                                    </select>
                                </div>
                                <div class="divInLine workday_holiday_header_value layui-hide divTopMoneySetItem2">
                                    <input type="text" class="layui-input normal  v-floatLimit v-null" value="0" id="Holiday_MaxCharge" data-key="Holiday_MaxCharge" />
                                </div>
                                <div class="divInLine workday_holiday_header_value layui-hide divTopMoneySetItem2">
                                    <label>元</label>
                                </div>
                                <div class="divInLine" style="position:relative;"><i class="help-btn" data-key="Holiday_IsMaxCharge" style="margin-top:-16px;">?</i></div>
                            </div>
                            <div class="wcontent"></div>
                        </div>

                        <div class="timepart layui-hide">
                            <div class="spacetime  layui-hide"></div>
                            <div class="rangtime layui-hide"></div>
                            <div class="numbertime layui-hide"></div>
                        </div>
                        <div class="notimepart layui-hide">
                            <div class="spacetime  layui-hide"></div>
                            <div class="rangtime layui-hide"></div>
                            <div class="numbertime layui-hide"></div>
                        </div>
                    </div>

                    <div class="noworkday layui-hide">
                        <div class="timepart layui-hide">
                            <div class="spacetime  layui-hide"></div>
                            <div class="rangtime layui-hide"></div>
                            <div class="numbertime layui-hide"></div>
                        </div>
                        <div class="notimepart layui-hide">
                            <div class="spacetime  layui-hide"></div>
                            <div class="rangtime layui-hide"></div>
                            <div class="numbertime layui-hide"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="customer-footer">
            <button class="layui-btn layui-btn-normal" id="Save"><i class="layui-icon layui-icon-ok"></i>保存</button>
            <button class="layui-btn layui-btn-danger " id="Cancel" style="background-color: #f0ad4e !important;"><i class="layui-icon layui-icon-close"></i>取消</button>
        </div>
    </div>

    <div class="tipRequest"></div>

    <!--临停计费规则-->
    <!--<div class="mainbody layui-form">
    <div class="layui-form">
        <div class="layui-row nodata layui-hide">
            还没有为此类型添加过计费规则，您可以点击右上角新增一个。 如果不需要计费则不需要添加
        </div>
    </div>
    <div class="layui-form" id="ruleContent">-->
    <!--下拉选项-->
    <!--<div class="layui-row topheader">

            </div>
            <hr class="hr-line-solid" />

        </div>

    </div>-->
    @*<div class="layui-form">
    <div class="layui-row">
    <span class="tipMsg  layui-hide">温馨提示：计费规则修改后，请一定要点击「保存」</span>
    </div>
    </div>*@
    <script type="text/x-jquery-tmpl" id="tmplcarcardtype">
        <option value="${CarCardType_No}" data-category="${CarCardType_Category}">${CarCardType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${CarType_No}">${CarType_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplarea">
        <option value="${ParkArea_No}" data-type="${ParkArea_Type}">${ParkArea_Name}</option>
    </script>
    @*不分工作节假日-不分时段-按次计费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_numbertime">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_numbertime_item">
            <div class="layui-row">
                <div class="edit-label layui-col-xs2">每次收费</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <input type="text" class="layui-input v-floatLimit v-null" data-minlen="2" maxlength="8" value="${Money}" data-key="Money" placeholder="请填写收费金额（元）" />
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
        </div>
    </script>
    @*不分工作节假日-不分时段-按计时单位-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_spacetime">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_spacetime_item">
             <div class="firstrow">
                <div class="layui-row firsttimeunit">
                    <div class="edit-label layui-col-xs2">首计时收费价格</div>
                    <div class="edit-ipt-ban layui-col-xs3">
                        <div class="input-group">
                            <input type="text" class="layui-input v-floatLimit v-null" maxlength="8" value="${FirstTime_UnitCharge}" data-key="FirstTime_UnitCharge" placeholder="请填写收费金额（元）" />
                            <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                        </div>
                        <label class="label2 lan-label">例如：首60分钟内，收1元.</label>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="edit-label" style="font-size: 1.5rem;">&nbsp; / </div>
                    <div class="edit-ipt-ban layui-col-xs3">
                        <div class="input-group">
                            <input type="text" class="layui-input v-number v-null v-min" min="1" maxlength="8" value="${FirstTime_Unit}" data-key="FirstTime_Unit" placeholder="请填写分钟（大于或等于1）" />
                            <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">分钟</t></button></span>
                        </div>
                        <label class="label2 lan-label"></label>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>

                     <div><button class="layui-btn btnadd" style="margin-left: 10px;"><t> 添加</t></button></div>
                </div>
            </div>
            <div class="layui-row">
                <div class="edit-label layui-col-xs3">计时单位</div>
                <div class="edit-ipt-ban layui-col-xs2">
                    <div class="input-group">
                         <input type="text" class="layui-input v-number v-null v-min iptbottom" min="1" maxlength="8" value="${Unit}" data-key="Unit" placeholder="" />
                        <span class="input-group-btn"><t class="lan-label">分钟</t></span>
                    </div>
                    <label style="float:none;" class="label2 lan-label"></label>
                </div>
                <div class="edit-label">，每计时单位收费 </div>
                <div class="edit-ipt-ban layui-col-xs2">
                    <div class="input-group">
                        <input type="text" class="layui-input v-floatLimit v-null iptbottom" maxlength="8" value="${UnitCharge}" data-key="UnitCharge" placeholder="" />
                        <span class="input-group-btn"><t class="lan-label">元</t></span>
                    </div>
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
        </div>
    </script>
    @*不分工作节假日-不分时段-按时长范围收费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_rangtime">
        <div class="layui-col-xs12 tmplnoworkday_notimepart_rangtime_item">
            <div class="layui-row" style="display:block;">
                <div class="divInLine tmplnoworkday_notimepart_rangtime_rule layui-col-xs12">

                </div>
                <label class="label2 lan-label">温馨提示：停车时长范围最后一条表示周期或时段的最高收费，例如：周期或时段为24小时,最后一条要设置成24小时收费X元.</label>
            </div>
            
        </div>
    </script>
    @*不分工作节假日-不分时段-按时长范围收费->规则-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_notimepart_rangtime_rule">
        <div>
            <div class="edit-label layui-col-xs2">停车时长小于等于</div>
            <div class=" layui-col-xs10 tmplnoworkday_notimepart_rangtime_item_rule">
                <div class="divInLine"><input type="text" class="layui-input normal  v-number v-null" maxlength="8" value="${DurationRule_Hour}" data-key="DurationRule_Hour" /></div>
                <div class="divInLine"><label>小时</label></div>
                <div class="divInLine"><input type="text" class="layui-input normal  v-number v-null" maxlength="8" value="${DurationRule_Min}" data-key="DurationRule_Min" /></div>
                <div class="divInLine"><label>分钟，收费</label></div>
                <div class="divInLine"><input type="text" class="layui-input normal  v-floatLimit v-null" maxlength="8" value="${DurationRule_ChargeMoney}" data-key="DurationRule_ChargeMoney" /></div>
                <div class="divInLine"><label>元。</label></div>
                <div class="divInLine"><button class="layui-btn tmplnoworkday_notimepart_rangtime_item_rule_additem"><t>新增规则</t></button></div>
            </div>
        </div>
    </script>
    @*不分工作节假日-分时段-节点*@
    <script type="text/x-jquery-tmpl" id="tmplnoworkday_timepart">
        <div class="layui-col-xs12 tmplnoworkday_timepart_item">
            <div class="headitem">
                <div class="divInLine"><label>时间段</label></div>
                <div class="divInLine"> <input type="text" class="layui-input required lan-error normal stime" autocomplete="off" id="sstime" name="sstime" value="${Section_StartTime}" data-key="Section_StartTime" data-error="不能为空" />   @*<input type="time" class="layui-input normal" value="${Section_StartTime}" data-key="Section_StartTime" />*@</div>
                <div class="divInLine"><label>-</label></div>
                <div class="divInLine"> <input type="text" class="layui-input required lan-error normal stime" autocomplete="off" id="setime" name="setime" value="${Section_EndTime}" data-key="Section_EndTime" data-error="不能为空" /> @*<input type="time" class="layui-input normal" value="${Section_EndTime}" data-key="Section_EndTime" />*@</div>
                <div class="divInLine layui-hide">
                    <select class="layui-select" lay-search id="Section_ChargeMode" name="Section_ChargeMode" lay-filter="Section_ChargeMode" data-key="Section_ChargeMode_NoWorkTimePart" data-category="noworkday_timepart">
                        <option value="1">按计时单位计费</option>
                    </select>
                </div>
        @*<div class="divInLine divSection_Grade">
            <select class="layui-select" lay-search id="Section_Grade" name="Section_Grade" lay-filter="Section_Grade" data-key="Section_Grade" data-category="noworkday_timepart">
            <option value="0">计费等级：0</option>
            <option value="1">计费等级：1</option>
            <option value="2">计费等级：2</option>
            </select>
            </div>*@
                <div class="divInLine"><button class="layui-btn noworkday_timepart_additem"><t>新增时段</t></button></div>
                <div class="divInLine iconItem"><i class="layui-icon layui-icon-down"></i></div>
            </div>
            <div class="content layui-row">

            </div>
        </div>
    </script>

    @*分工作节假日-工作日-节点${}*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_work">
        <div class="layui-col-xs12 workday_item">
            <div class="headitem">
                <div class="divInLine"><label>时间段</label></div>
                <div class="divInLine"><input type="text" class="layui-input required lan-error normal stime" autocomplete="off" id="sstime" name="sstime" value="${Section_StartTime}" data-key="Section_StartTime" data-error="不能为空" /> </div>
                <div class="divInLine"><label>-</label></div>
                <div class="divInLine"> <input type="text" class="layui-input required lan-error normal stime" autocomplete="off" id="setime" name="setime" value="${Section_EndTime}" data-key="Section_EndTime" data-error="不能为空" /> </div>
                <div class="divInLine">
                    <select class="layui-select" lay-search id="Section_ChargeMode" name="Section_ChargeMode" lay-filter="Section_ChargeMode" data-category="workday" data-key="Section_ChargeMode_Work">
                        <option value="1">按计时单位计费</option>
                        <option value="2">按停车时长范围计费</option>
                        <option value="3">按次计费</option>
                    </select>
                </div>
                <div class="divInLine"><button class="layui-btn workday_additem"><t>新增时段</t></button></div>
                <div class="divInLine iconItem"><i class="layui-icon layui-icon-down"></i></div>
            </div>

            <div class="content" style="display:block;"></div>
        </div>
    </script>
    @*分工作节假日-节假日-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_holiday">
        <div class="layui-col-xs12 workday_holidayItem">
            <div class="headitem">
                <div class="divInLine"><label>时间段</label></div>
                <div class="divInLine"><input type="text" class="layui-input required lan-error normal stime" autocomplete="off" id="sstime" name="sstime" value="${Section_StartTime}" data-key="Section_StartTime" data-error="不能为空" /> </div>
                <div class="divInLine"><label>-</label></div>
                <div class="divInLine"> <input type="text" class="layui-input required lan-error normal stime" autocomplete="off" id="setime" name="setime" value="${Section_EndTime}" data-key="Section_EndTime" data-error="不能为空" /> </div>
                <div class="divInLine">
                    <select class="layui-select" lay-search id="Section_ChargeMode" name="Section_ChargeMode" lay-filter="SelHolidayTimeFree" data-category="holiday" data-key="Section_ChargeMode_Holiday">
                        <option value="1">按计时单位计费</option>
                        <option value="2">按停车时长范围计费</option>
                        <option value="3">按次计费</option>
                    </select>
                </div>
                <div class="divInLine"><button class="layui-btn workday_holidayAdditem"><t>新增时段</t></button></div>
                <div class="divInLine iconItem"><i class="layui-icon layui-icon-down"></i></div>
            </div>
            <div class="content" style="display:block;"></div>
        </div>
    </script>
    @*分工作节假日-按计时单位-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_spacetime">
        <div class="layui-col-xs12 tmplworkday_spacetime_item">

            <div class="firstrow">
                <div class="layui-row firsttimeunit">
                <div class="edit-label layui-col-xs2">首计时收费价格</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <div class="input-group">
                        <input type="text" class="layui-input v-floatLimit v-null" maxlength="8" value="${FirstTime_UnitCharge}" data-key="FirstTime_UnitCharge" placeholder="请填写收费金额（元）" />
                        <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button></span>
                    </div>
                    <label class="label2 lan-label">例如：首60分钟内，收1元.</label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="edit-label" style="font-size: 1.5rem;">&nbsp; / </div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <div class="input-group">
                        <input type="text" class="layui-input v-number v-null v-min" min="1" maxlength="8" value="${FirstTime_Unit}" data-key="FirstTime_Unit" placeholder="请填写分钟（大于或等于1）" />
                        <span class="input-group-btn"><button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">分钟</t></button></span>
                    </div>
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>

               <div><button class="layui-btn btnadd" style="margin-left: 10px;"><t> 添加</t></button></div>
            </div>
            </div>

          <div class="layui-row">
                <div class="edit-label layui-col-xs3">计时单位</div>
                <div class="edit-ipt-ban layui-col-xs2">
                    <div class="input-group">
                         <input type="text" class="layui-input v-number v-null v-min iptbottom" min="1" maxlength="8" value="${Unit}" data-key="Unit" placeholder="" />
                        <span class="input-group-btn"><t class="lan-label">分钟</t></span>
                    </div>
                    <label style="float:none;" class="label2 lan-label"></label>
                </div>
                <div class="edit-label">，每计时单位收费 </div>
                <div class="edit-ipt-ban layui-col-xs2">
                    <div class="input-group">
                        <input type="text" class="layui-input v-floatLimit v-null iptbottom" maxlength="8" value="${UnitCharge}" data-key="UnitCharge" placeholder="" />
                        <span class="input-group-btn"><t class="lan-label">元</t></span>
                    </div>
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="edit-label layui-col-xs2">时段最高收费</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <select class="layui-select" lay-search id="CycleReCalc" name="CycleReCalc" lay-filter="CycleReCalc" data-key="CycleReCalc">
                        <option value="1">启用</option>
                        <option value="2">禁用</option>
                    </select>
                    <label class="label2 lan-label"></label>
                </div>
                <div class="CycleReCalc">
                    <div class="edit-label layui-col-xs2">最高收费</div>
                    <div class="edit-ipt-ban layui-col-xs3">
                        <input type="text" class="layui-input v-floatLimit v-null" data-minlen="2" maxlength="8" value="${CycleReCalc_MaxCharge}" data-key="CycleReCalc_MaxCharge" placeholder="请填写金额" />
                        <label class="label2 lan-label"></label>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
            </div>
        </div>
    </script>
    @*首计时分段*@
    <script type="text/x-jquery-tmpl" id="tmpl_firsttime">
         <tr class="firsttime-tr">
            <td>
                <div class="num"><input type="text" class="layui-input iptnum" value="${FirstTime_Id}" data-key="FirstTime_Id" readOnly="true"/></div>
            </td>
            <td>
                <div class="desc">${desc}</div>
            </td>
             <td>
                <div class="layui-col-xs12">
                    <input type="text" class="layui-input v-number v-null v-min" min="1" maxlength="8"
                    value="${FirstTime_Min}" data-key="FirstTime_Min" placeholder="停留分钟" />
                </div>
            </td>
            <td>
                <div class="layui-col-xs12">
                    <div class="input-group">
                            <input type="text" class="layui-input v-floatLimit v-null" maxlength="8"
                            value="${FirstTime_Money}" data-key="FirstTime_Money" placeholder="收费金额" />
                            <span class="input-group-btn">
                                  <button type="button" class="layui-btn layui-btn-outline layui-btn-primary btnUnit"><t class="lan-label">元</t></button>
                            </span>
                        </div>
                </div>
            </td>
            <td>
                 {{if FirstTime_Id==1}}
                   <button class="layui-btn btnadd"><t> 添加</t></button>
                  {{else}}
                    <button class="layui-btn btndel"><t> 删除</t></button>
                  {{/if}}
            </td>
        </tr>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpl_firsttimerang">
        <div class="layui-row firsttimeunit">
                 <div class="edit-label layui-col-xs3">停车小于等于</div>
                 <div class="edit-ipt-ban layui-col-xs2">
                     <div class="input-group">
                         <input type="text" class="layui-input v-number v-null v-min iptbottom" min="1" maxlength="8" value="${FirstTime_Unit}" data-key="FirstTime_Unit" placeholder="" />
                         <span class="input-group-btn"><t class="lan-label">分钟</t></span>
                     </div>
                     <label class="label2 lan-label"></label>
                 </div>
                 <div class="edit-label">，收费 </div>
                 <div class="edit-ipt-ban layui-col-xs2">
                     <div class="input-group">
                          <input type="text" class="layui-input v-floatLimit v-null iptbottom" maxlength="8" value="${FirstTime_UnitCharge}" data-key="FirstTime_UnitCharge" placeholder="" />
                         <span class="input-group-btn"><t class="lan-label">元</t></span>
                     </div>
                     <label class="label2 lan-label"></label>
                 </div>
                 <div class="layui-col-xs1 red-mark">*</div>

                <div class="layui-col-xs1 red-mark"><button class="layui-btn btndel iptbtn" style="margin-left: 10px;"><t> 删除</t></button></div>
             </div>
    </script>

    @*分工作节假日-按时长范围收费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_rangtime">
        <div class="layui-col-xs12 tmplworkday_rangtime_item">
            <div class="layui-row">
        @*<div class="edit-label layui-col-xs2">跨段时长</div>
            <div class="edit-ipt-ban layui-col-xs3">
            <input type="text" class="layui-input v-number v-null" maxlength="8" value="${SpanMin}" data-key="SpanMin" placeholder="请填写分钟" />
            <label class="label2 lan-label">例如：当停车时长出现跨段(由本段跨入下段),且小于等于5分钟,按照本段计费.</label>
            </div>*@
                <div class="edit-label layui-col-xs2">免费分钟</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <input type="text" class="layui-input v-number  v-null" maxlength="8" value="${FreeMin}" data-key="FreeMin" placeholder="请填写分钟" />
                    <label class="label2 lan-label">例如：5分钟内免费.</label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="edit-label layui-col-xs2">每次都有免费分钟</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <select class="layui-select" lay-search id="IsFreeMin" name="IsFreeMin" lay-filter="IsFreeMin" data-key="IsFreeMin">
                        <option value="1">启用</option>
                        <option value="2" selected>禁用</option>
                    </select>
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="help-btn-div"><i class="help-btn" data-key="IsFreeMin">?</i></div>
            </div>

            <div class="layui-row">
                <div class="edit-label layui-col-xs2">停车时长</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <select class="layui-select" lay-search id="ValueMethod" name="ValueMethod" lay-filter="ValueMethod" data-key="ValueMethod">
                        <option value="1">大于或等于</option>
                        <option value="2">小于或等于</option>
                    </select>
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="edit-label layui-col-xs2"></div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <label class="label2 lan-label"></label>
                </div>
            </div>
            <div class="layui-row" style="display:block;">
                <div class="layui-col-xs12 divInLine tmplworkday_rangtime_rule">
                    <div>
                        <div class="edit-label layui-col-xs2"></div>
                        <div class=" layui-col-xs10 tmplworkday_rangtime_item_rule">
                            <div class="divInLine"><input type="text" class="layui-input normal  v-number v-null" maxlength="8" value="${DurationRule_Hour}" data-key="DurationRule_Hour" /></div>
                            <div class="divInLine"><label>小时</label></div>
                            <div class="divInLine"><input type="text" class="layui-input normal  v-number v-null" maxlength="8" value="${DurationRule_Min}" data-key="DurationRule_Min" /></div>
                            <div class="divInLine"><label>分钟，收费</label></div>
                            <div class="divInLine"><input type="text" class="layui-input normal  v-floatLimit v-null" maxlength="8" value="${DurationRule_ChargeMoney}" data-key="DurationRule_ChargeMoney" /></div>
                            <div class="divInLine"><label>元。</label></div>
                            <div class="divInLine"><button class="layui-btn tmplnoworkday_notimepart_rangtime_item_rule_additem"><t>新增规则</t></button></div>
                        </div>
                    </div>
                </div>
                <div class="layui-row ">
                    <div class="edit-label layui-col-xs2">时段内多次进出累计计费</div>
                    <div class="edit-ipt-ban layui-col-xs3">
                        <select class="layui-select" lay-search id="Section_IsHoursMaxCharge" name="Section_IsHoursMaxCharge" lay-filter="Section_IsHoursMaxCharge" data-key="Section_IsHoursMaxCharge">
                            <option value="1">启用</option>
                            <option value="2">禁用</option>
                        </select>
                        <label class="label2 lan-label"></label>
                    </div>
                    <div class="Section_IsHoursMaxCharge">
                        <div class="edit-label layui-col-xs2">累计最高收费</div>
                        <div class="edit-ipt-ban layui-col-xs3">
                            <input type="text" class="layui-input v-floatLimit v-null" data-minlen="2" maxlength="8" value="${Section_HoursMaxCharge}" data-key="Section_HoursMaxCharge" placeholder="请填写金额" />
                            <label class="label2 lan-label"></label>
                        </div>
                        <div class="layui-col-xs1 red-mark">*</div>
                    </div>
                </div>
                <label class="label2 lan-label">
                    温馨提示：停车时长范围最后一条表示周期或时段的最高收费， 例如：周期或时段为<span class='checkResult'>24</span>小时,连续停车<span class='checkResult'>12</span>个小时以上并且在<span class='checkResult'>24</span>小时以下的收费<span class='checkResult'>50</span>元，
                    那么只要定义规则到<span class='checkResult'>12</span>小时<span class='checkResult'>0</span>分钟收<span class='checkResult'>50</span>元就可以了，后面的规则可以不定义。
                </label>
            </div>
        </div>
    </script>
    @*分工作节假日-按时长范围收费->规则-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_rangtime_rule">
        <div>
            <div class="edit-label layui-col-xs2"></div>
            <div class=" layui-col-xs10 tmplworkday_rangtime_item_rule">
                <div class="divInLine"><input type="text" class="layui-input normal  v-number v-null" maxlength="8" value="${DurationRule_Hour}" data-key="DurationRule_Hour" /></div>
                <div class="divInLine"><label>小时</label></div>
                <div class="divInLine"><input type="text" class="layui-input normal  v-number v-null" maxlength="8" value="${DurationRule_Min}" data-key="DurationRule_Min" /></div>
                <div class="divInLine"><label>分钟，收费</label></div>
                <div class="divInLine"><input type="text" class="layui-input normal  v-floatLimit v-null" maxlength="8" value="${DurationRule_ChargeMoney}" data-key="DurationRule_ChargeMoney" /></div>
                <div class="divInLine"><label>元。</label></div>
                <div class="divInLine"><button class="layui-btn tmplworkday_rangtime_item_rule_additem"><t>新增规则</t></button></div>
            </div>
        </div>
    </script>
    @*分工作节假日-按次计费-节点*@
    <script type="text/x-jquery-tmpl" id="tmplworkday_numbertime">
        <div class="layui-col-xs12 tmplworkday_numbertime_item">
            <div class="layui-row">
                <div class="edit-label layui-col-xs2">每次收费</div>
                <div class="edit-ipt-ban layui-col-xs3">
                    <input type="text" class="layui-input v-floatLimit v-null" data-minlen="2" maxlength="8" value="${Money}" data-key="Money" placeholder="请填写收费金额（元）" />
                    <label class="label2 lan-label"></label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
        </div>
    </script>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?2" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v=1.1" asp-append-version="true"></script>

    <script type="text/javascript">
        var paramChargeRulesNo = $.getUrlParam("ChargeRules_No");
        var layuiForm = null;
        var xmSelect = null;
        var laydate = null;
        var table = null;
        var curstoptb = null;

        var selParkArea = null;
        var selCarType = null;
        var selCarCardType = null;
        var index = parent.layer.getFrameIndex(window.name);
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var vType = localStorage.getItem("versionType");
        $("#ChargeRules_Type ul li").removeClass("layui-hide");
        if (vType == 'simple') {
            $("#ChargeRules_Type ul li[data-value=1]").addClass("layui-hide");
        } else {
            $("#ChargeRules_Type ul li").removeClass("select");
            $("#ChargeRules_Type ul li[data-value=1]").addClass("select");
            $("#ChargeRules_Type ul li[data-value=0]").addClass("layui-hide");
        }

        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['table', 'element', 'form', 'xmSelect', 'laydate'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            laydate = layui.laydate;
            table = layui.table;

            _DATE.bind(laydate, ["Cycle_CustomStartTime"], { type: 'time', range: false, format: 'HH:mm' });
            _DATE.bind(laydate, ["Logic_NightTime"], { type: 'time', range: false, format: 'HH:mm' });

            pager.init();
        })

        var pager = {
            ChargeRules_BeginTime: null,
            ChargeRules_EndTime: null,
            ChargeRules_No: null,
            parkAreas: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            firsttimeList: [],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                bindTip();
                firsttimeRang.bindEvent($(".tmplworkday_spacetime_item"));
                firsttimeRang.bindEvent($(".tmplnoworkday_notimepart_spacetime_item"));
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.carCardTypes = json.data.carCardTypes;
                        pager.carTypes = json.data.carTypes;
                        pager.parkAreas = json.data.parkAreas;

                        if (pager.carTypes && pager.carTypes.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.carTypes.length; i++) {
                                data[i] = {
                                    "name": pager.carTypes[i].CarType_Name,
                                    "value": pager.carTypes[i].CarType_No
                                };
                            }
                            selCarType = xmSelect.render({
                                el: '#BillRuleTemp_CarType',
                                name: 'BillRuleTemp_CarType',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        if (pager.carCardTypes && pager.carCardTypes.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.carCardTypes.length; i++) {
                                data[i] = {
                                    "name": pager.carCardTypes[i].CarCardType_Name,
                                    "value": pager.carCardTypes[i].CarCardType_No
                                };
                            }
                            selCarCardType = xmSelect.render({
                                el: '#BillRuleTemp_CarCardType',
                                name: 'BillRuleTemp_CarCardType',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        if (pager.parkAreas && pager.parkAreas.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.parkAreas.length; i++) {
                                data[i] = {
                                    "name": pager.parkAreas[i].ParkArea_Name,
                                    "value": pager.parkAreas[i].ParkArea_No
                                };
                            }
                            selParkArea = xmSelect.render({
                                el: '#BillRuleTemp_ParkArea',
                                name: 'BillRuleTemp_ParkArea',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        //$("#BillRuleTemp_ParkArea").html($("#tmplarea").tmpl(pager.parkAreas));

                        layuiForm.render("select")
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function () {

                //绑定下拉事件
                _selectChangeBLL.bindChange();

                //获取计费规则数据
                pager.getRulesData();

            },
            bindEvent: function () {
                //新增规则
                $("#Add").click(function () {
                    _InitAll.process();

                    $("#ruleContent,.tipMsg").removeClass("layui-hide");
                    $(".nodata").addClass("layui-hide");
                    $(".divTopMoneySet").addClass("layui-hide");
                    $(".tmplnoworkday_notimepart_spacetime_item").remove();

                    $("#Logic_IsDiffTime").val(0);
                    $("#Section_ChargeMode").val(1);
                    $(".divLogic_IsDiffTime,.divLogic_IsDiffHoliday").removeClass("layui-hide");

                    $("#Cycle_IsCharge").val(0);
                    $(".Cycle_IsCharge").addClass("layui-hide");

                    $("#Logic_IsNightFee").val(0);
                    $(".Logic_IsNightFee").addClass("layui-hide");

                    $("#Logic_MergeSectionTime").val(0);

                    _noworkdayNotimepart_spacetime_BLL.show();
                    _selectChangeBLL.bindChange();
                });
                //保存规则
                $("#Save").click(function () {
                    if ($(".rulesetting").is(":hidden")) { $(".ruletitle").click() }
                    if ($(".hoursetting").is(":hidden")) { $(".hourtitle").click() }
                    if ($(".basesetting").is(":hidden")) { $(".basetitle").click() }
                    if ($(".hourmore").is(":hidden")) { $(".hourmoresetting").click() }

                    $(".iconItem").each(function () {
                        var content = $(this).parent().parent().find(".content");
                        if ($(content).is(":hidden")) {
                            $(this).click();
                        }
                    })

                    var ChargeRules_CarCardTypeNo = selCarCardType.getValue('value');
                    var ChargeRules_CarTypeNo = selCarType.getValue('value');
                    var ChargeRules_ParkAreaNo = selParkArea.getValue('value');
                    if (!ChargeRules_CarCardTypeNo || ChargeRules_CarCardTypeNo.length == 0) {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择车牌类型", "#BillRuleTemp_CarCardType", { time: 2000 });
                        return;
                    }
                    if (!ChargeRules_CarTypeNo || ChargeRules_CarTypeNo.length == 0) {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择车牌颜色", "#BillRuleTemp_CarType", { time: 2000 });
                        return;
                    }
                    if (!ChargeRules_ParkAreaNo || ChargeRules_ParkAreaNo.length == 0) {
                        $("#ChargeRules_Remark").focus();
                        layer.tips("请选择计费区域", "#BillRuleTemp_ParkArea", { time: 2000 });
                        return;
                    }
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    $("#Save").attr("disabled", true);

                    var list = pager.saveRules();
                    setTimeout(function () {
                        pager.showActionSpan();
                        console.log(JSON.stringify(list));
                        var action = paramChargeRulesNo ? "UpdateChargeRules" : "AddChargeRules";
                        $.post(action, { jsonModel: JSON.stringify(list), rule_type: 0 }, function (json) {
                            $("#Save").removeAttr("disabled");
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                var retMsg = json.msg.replace(/#/g, "<br/>");

                                layer.msg(retMsg, { icon: 0, btn: ['确定'], time: 0 });
                            }
                        }, "json");
                    }, 200);

                });

                $("#Cancel").click(function () {
                    layer.confirm('当前设置未保存，是否退出？', {
                        btn: ['确定', '取消'] //按钮
                    }, function () {
                        parent.layer.close(index);
                    }, function () {
                        return;
                    });
                });

                $(".iconItem").unbind("click").click(function () {
                    var content = $(this).parent().parent().find(".content");
                    if ($(content).is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(content).toggle("normal");
                })

                $(".basetitle").unbind("click").click(function () {
                    if ($(".basesetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".basesetting").toggle("normal");
                });
                $(".cycletitle").unbind("click").click(function () {
                    if ($(".cyclesetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".cyclesetting").toggle("normal");
                });
                $(".ruletitle").unbind("click").click(function () {
                    if ($(".rulesetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".rulesetting").toggle("normal");
                });
                $(".hourtitle").unbind("click").click(function () {
                    if ($(".hoursetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".hoursetting").toggle("normal");
                });

                $(".hourmoresetting").unbind("click").click(function () {
                    if ($(".hourmore").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                        $(this).find("t").text("隐藏更多设置");
                    } else {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down");
                        $(this).find("t").text("更多设置");
                    }
                    $(".hourmore").toggle("normal");
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                    }
                });
            },
            //保存计费规则
            saveRules: function () {

                var ChargeRules_CarCardTypeNo = selCarCardType.getValue('value');
                var ChargeRules_CarTypeNo = selCarType.getValue('value');
                var ChargeRules_ParkAreaNo = selParkArea.getValue('value');

                var Logic_IsDiffHoliday = 0;
                var Logic_IsDiffTime = $("#Logic_IsDiffTime").val();
                var Section_ChargeMode = $("#Section_ChargeMode").val();

                var ChargeRules_Name = $("#ChargeRules_Name").val();
                var ChargeRules_Remark = $("#ChargeRules_Remark").val();
                var Logic_Across = $("#Logic_Across").val();
                var Logic_IsFreeTimeCharge = $("#Logic_IsFreeTimeCharge").val();
                var Logic_IsCouponTime = $("#Logic_IsCouponTime").val();
                var Logic_IsOnlyFreeTime = 0;
                var Logic_IsOnlyFirstTime = 0;
                var Logic_IsTopMoney = 0;
                var Logic_TopMoney = $("#Logic_TopMoney").val();
                var Logic_IsFreeTime = $("#Logic_IsFreeTime").val();
                var Logic_FreeTime = $("#Logic_FreeTime").val();
                var Logic_IsFullTimeCharge = 0;
                var ChargeRules_OverTime = 0;

                var Logic_IsNightFee = $("#Logic_IsNightFee").val();
                var Logic_NightTime = $("#Logic_NightTime").val();
                var Logic_NightFee = $("#Logic_NightFee").val();

                var Logic_MergeSectionTime = $("#Logic_MergeSectionTime").val();

                var Logic_FreeMin = $("#Logic_FreeMin").val();
                var Logic_PayLeaveMin = $("#Logic_PayLeaveMin").val();
                var Logic_MaxDailyAmount = $("#Logic_MaxDailyAmount").val();
                var Logic_NestedRecords = $("#Logic_NestedRecords").val();

                var ResultModel = {};
                if (Logic_IsDiffHoliday == 0 || Logic_IsDiffHoliday == 1) {
                    if (Logic_IsDiffHoliday == 1) {//分工作节假日
                        ResultModel = Rules.GetWorkAndHoliDayValue();
                    } else if (Logic_IsDiffHoliday == 0) {//不分工作节假日
                        if (Logic_IsDiffTime == 0 || Logic_IsDiffTime == 1) {
                            if (Logic_IsDiffTime == 1) {//分时段
                                ResultModel = Rules.GetNoWorkDay_PartTimeValue();
                            } else if (Logic_IsDiffTime == 0) {//不分时段
                                if (Section_ChargeMode) {
                                    if (Section_ChargeMode == 1) {//按计时单位收费
                                        ResultModel = Rules.GetNoWorkDay_NoPartTime_SpacetimeValue();
                                    } else if (Section_ChargeMode == 2) {//按时长范围收费
                                        ResultModel = Rules.GetNoWorkDay_NoPartTime_RangtimeValue();
                                    } else if (Section_ChargeMode == 3) {//按次收费
                                        ResultModel = Rules.GetNoWorkDay_NoPartTime_NumbertimeValue();
                                    }
                                }
                            }
                        }
                    }
                }

                ResultModel.Logic_IsDiffHoliday = Logic_IsDiffHoliday;
                ResultModel.Logic_IsDiffTime = Logic_IsDiffTime;
                ResultModel.Logic_Across = Logic_Across;
                ResultModel.Logic_IsFreeTimeCharge = Logic_IsFreeTimeCharge;
                ResultModel.Logic_IsCouponTime = Logic_IsCouponTime;
                ResultModel.Logic_IsOnlyFreeTime = Logic_IsOnlyFreeTime;
                ResultModel.Logic_IsOnlyFirstTime = Logic_IsOnlyFirstTime;
                ResultModel.Logic_IsTopMoney = Logic_IsTopMoney;
                ResultModel.Logic_TopMoney = Logic_TopMoney;
                ResultModel.Logic_IsFreeTime = Logic_IsFreeTime;
                ResultModel.Logic_FreeTime = Logic_FreeTime;
                ResultModel.Logic_IsFullTimeCharge = Logic_IsFullTimeCharge;
                ResultModel.Logic_IsNightFee = Logic_IsNightFee;
                ResultModel.Logic_MergeSectionTime = Logic_MergeSectionTime;
                ResultModel.Logic_NightTime = Logic_NightTime;
                ResultModel.Logic_NightFee = Logic_NightFee;

                ResultModel.Logic_FreeMin = Logic_FreeMin;
                ResultModel.Logic_PayLeaveMin = Logic_PayLeaveMin;
                ResultModel.Logic_MaxDailyAmount = Logic_MaxDailyAmount;
                ResultModel.Logic_NestedRecords = Logic_NestedRecords;

                ResultModel.Section_ChargeMode = Section_ChargeMode;

                return {
                    ChargeRules_No: paramChargeRulesNo,
                    ChargeRules_CarCardTypeNo: ChargeRules_CarCardTypeNo,
                    ChargeRules_CarTypeNo: ChargeRules_CarTypeNo,
                    ChargeRules_ParkAreaNo: ChargeRules_ParkAreaNo,
                    ChargeRules_IsDiffHoliday: Logic_IsDiffHoliday,
                    ChargeRules_IsDiffTime: Logic_IsDiffTime,
                    ChargeRules_IsDiffFree: Section_ChargeMode,
                    ChargeRules_JsonData: ResultModel,
                    ChargeRules_BeginTime: pager.ChargeRules_BeginTime,
                    ChargeRules_EndTime: pager.ChargeRules_EndTime,
                    ChargeRules_Name: ChargeRules_Name,
                    ChargeRules_OverTime: ChargeRules_OverTime,
                    ChargeRules_Remark: ChargeRules_Remark,
                    ChargeRules_Across: Logic_Across,
                    ChargeRules_IsFreeTimeCharge: Logic_IsFreeTimeCharge,
                    ChargeRules_IsFullTimeCharge: Logic_IsFullTimeCharge,
                    ChargeRules_Type: 0
                };
            },
            //获取计费规则数据
            getRulesData: function () {
                _InitAll.process();
                if (!paramChargeRulesNo) {

                    $("#ruleContent,.tipMsg").removeClass("layui-hide");
                    var Logic_IsDiffHoliday = 0;
                    var Logic_IsDiffTime = 0;
                    var Section_ChargeMode = 1;
                    layuiForm.render("select");

                    var d = {};
                    pager.showItemRules(d, Logic_IsDiffHoliday, Logic_IsDiffTime, Section_ChargeMode);
                    return;
                }

                $("#ChargeRules_Type ul li").removeClass("layui-hide");
                $("#ChargeRules_Type ul li[data-value!=0]").addClass("layui-hide");

                $.post("GetChargeRules", { ChargeRules_No: paramChargeRulesNo }, function (json) {
                    if (json.success) {
                        var data = json.data;
                        if (data) {
                            $(".nodata").addClass("layui-hide");
                            $("#ruleContent,.tipMsg").removeClass("layui-hide");
                            var Logic_IsDiffHoliday = data.ChargeRules_IsDiffHoliday;
                            var Logic_IsDiffTime = data.ChargeRules_IsDiffTime;
                            var Section_ChargeMode = data.ChargeRules_IsDiffFree;
                            pager.ChargeRules_BeginTime = data.ChargeRules_BeginTime;
                            pager.ChargeRules_EndTime = data.ChargeRules_EndTime;

                            $("#ChargeRules_Name").val(data.ChargeRules_Name);
                            $("#ChargeRules_Remark").val(data.ChargeRules_Remark);
                            $("#Logic_Across").val(data.ChargeRules_Across);
                            $("#Logic_IsFreeTimeCharge").val(data.ChargeRules_IsFreeTimeCharge);

                            $("#BillRuleTemp_CarCardType").val(data.ChargeRules_CarCardTypeNo);
                            $("#BillRuleTemp_CarType").val(data.ChargeRules_CarTypeNo);
                            $("#BillRuleTemp_ParkArea").val(data.ChargeRules_ParkAreaNo);

                            $("#Logic_IsDiffTime").val(Logic_IsDiffTime);
                            $("#Section_ChargeMode").val(Section_ChargeMode);

                            $("#ChargeRules_Type ul li").removeClass("select");
                            $("#ChargeRules_Type ul li[data-value=" + data.ChargeRules_Type + "]").addClass("select");

                            if (data.ChargeRules_CarCardTypeNo && data.ChargeRules_CarCardTypeNo != "") {
                                if (data.ChargeRules_CarCardTypeNo.indexOf('[') == -1) { data.ChargeRules_CarCardTypeNo = '["' + data.ChargeRules_CarCardTypeNo + '"]'; }
                                selCarCardType.setValue(JSON.parse(data.ChargeRules_CarCardTypeNo))
                            }
                            if (data.ChargeRules_CarTypeNo && data.ChargeRules_CarTypeNo != "") {
                                selCarType.setValue(JSON.parse(data.ChargeRules_CarTypeNo))
                            }
                            if (data.ChargeRules_ParkAreaNo && data.ChargeRules_ParkAreaNo != "") {
                                selParkArea.setValue(JSON.parse(data.ChargeRules_ParkAreaNo))
                            }

                            layuiForm.render("select");

                            var d = data ? $.parseJSON(data.ChargeRules_JsonData) : {};
                            if (d.Logic_IsFullTimeCharge == 1) $("#Logic_IsFullTimeCharge").val(d.Logic_IsFullTimeCharge); else $("#Logic_IsFullTimeCharge").val(0);
                            if (d.Logic_IsFreeTime == 1) {
                                $("#Logic_IsFreeTime").val(d.Logic_IsFreeTime);
                                d.Logic_FreeTime = d.Logic_FreeTime || 0;
                                $("#Logic_FreeTime").val(d.Logic_FreeTime);
                            } else $("#Logic_IsFreeTime").val(0);

                            $("#Logic_IsCouponTime").val(d.Logic_IsCouponTime || 0);
                            d.Logic_IsNightFee = d.Logic_IsNightFee == null ? 0 : d.Logic_IsNightFee;
                            d.Logic_NightTime = d.Logic_NightTime == null ? "00:00" : d.Logic_NightTime;
                            $("#Logic_IsNightFee").val(d.Logic_IsNightFee);
                            $("#Logic_NightTime").val(d.Logic_NightTime);
                            $("#Logic_NightFee").val(d.Logic_NightFee);

                            $("#Logic_FreeMin").val(d.Logic_FreeMin);
                            $("#Logic_PayLeaveMin").val(d.Logic_PayLeaveMin);
                            $("#Logic_MaxDailyAmount").val(d.Logic_MaxDailyAmount);
                            $("#Logic_NestedRecords").val(d.Logic_NestedRecords);

                            if (d.Logic_IsNightFee == 1) {
                                $(".Logic_IsNightFee").removeClass("layui-hide");
                            } else {
                                $(".Logic_IsNightFee").addClass("layui-hide");
                            }

                            d.Logic_MergeSectionTime = d.Logic_MergeSectionTime == null ? 0 : d.Logic_MergeSectionTime;
                            $("#Logic_MergeSectionTime").val(d.Logic_MergeSectionTime);

                            if (d.Logic_IsOnlyFreeTime == 1) $("#Logic_IsOnlyFreeTime").val(d.Logic_IsOnlyFreeTime); else $("#Logic_IsOnlyFreeTime").val(0);
                            if (d.Logic_IsOnlyFirstTime == 1) $("#Logic_IsOnlyFirstTime").val(d.Logic_IsOnlyFirstTime); else $("#Logic_IsOnlyFirstTime").val(0);
                            if (d.Logic_IsTopMoney == 1) { $("#Logic_IsTopMoney").val(d.Logic_IsTopMoney); $("#Logic_TopMoney").val(d.Logic_TopMoney); $(".IsTopMoney").removeClass("layui-hide"); } else { $("#Logic_IsTopMoney").val(0); $(".IsTopMoney").addClass("layui-hide"); }
                            pager.showItemRules(d, Logic_IsDiffHoliday, Logic_IsDiffTime, Section_ChargeMode);

                            if (d.Logic_IsDiffTime == 1 && d.Logic_IsDiffHoliday != 1) { $(".divLogic_IsDiffHoliday2").removeClass("layui-hide") }
                            //if (!$(".hoursetting").is(":hidden")) {
                            //    $(".hoursetting").css("display", "none");
                            //    $(".hourtitle").find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                            //}
                            $(".iconItem").each(function () {
                                var content = $(this).parent().parent().find(".content");
                                if (!$(content).is(":hidden")) {
                                    $(this).click();
                                }
                            })
                        } else {
                            if (json.msg) layer.msg(json.msg);

                            $("#ruleContent,.tipMsg").addClass("layui-hide");
                            $(".nodata").removeClass("layui-hide");
                        }
                        //console.log(JSON.stringify(json.data));
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            //显示动画
            showActionSpan: function () {
                $(".mainbody>.light").remove();
                $(".mainbody").prepend(' <span class="light"></span>');
            },
            //显示规则节点
            showItemRules: function (d, Logic_IsDiffHoliday, Logic_IsDiffTime, Section_ChargeMode) {

                if (d.Logic_IsFreeTime == 1) {
                    $(".Logic_IsFreeTime").removeClass("layui-hide");
                } else {
                    $(".Logic_IsFreeTime").addClass("layui-hide");
                }
                if (d.Logic_IsNightFee && d.Logic_IsNightFee == 1) {
                    $(".Logic_IsNightFee").removeClass("layui-hide");
                } else {
                    $(".Logic_IsNightFee").addClass("layui-hide");
                }
                pager.showCycleTotal(d);
                if (Logic_IsDiffHoliday == 0 || Logic_IsDiffHoliday == 1) {
                    if (Logic_IsDiffHoliday == 1) {//分工作节假日
                        if (d && d != {}) {
                            $("#Login_HolidayCappingMode").val(d.Login_HolidayCappingMode);
                            //if (d.SelTopFree == 1) {
                            //    $(".divTopMoneySetItem").removeClass("layui-hide")
                            //    $(".divTopMoneySetItem2").addClass("layui-hide")
                            //} else {
                            //    $(".divTopMoneySetItem").addClass("layui-hide")
                            //    $(".divTopMoneySetItem2").removeClass("layui-hide")
                            //}
                            $("#HolidayCycle_Hour").val(d.HolidayCycle_Hour);
                            $("#HolidayCycle_MaxCharge").val(d.HolidayCycle_MaxCharge);
                            $("#Workday_IsMaxCharge").val(d.Workday_IsMaxCharge);
                            if (d.Logic_IsDiffHoliday == 1) {
                                $(".divTopMoneySetItem2,.workday_header_value").removeClass("layui-hide")
                                if (d.Workday_IsMaxCharge != 2) {
                                    $(".workday_header_value").addClass("layui-hide");
                                }
                            } else {
                                $(".divTopMoneySetItem2,.workday_header_value").addClass("layui-hide")
                            }
                            $("#Workday_MaxCharge").val(d.Workday_MaxCharge);
                            $("#Holiday_IsMaxCharge").val(d.Holiday_IsMaxCharge);
                            if (d.Holiday_IsMaxCharge != 2) {
                                $(".workday_holiday_header>.workday_holiday_header_value").addClass("layui-hide")
                            }

                            $("#Holiday_MaxCharge").val(d.Holiday_MaxCharge);
                            $(".across").removeClass("layui-hide");
                        }

                        try {
                            //dataConfig._workday_work_data = d.Section.WorkDayList;
                            //dataConfig._workday_holiday_data = d.Section.HoliDayList
                        } catch (e) { }

                        var workData = $.parseJSON(JSON.stringify(d));
                        var holidayData = d;
                        _workday_work_BLL.show(workData);
                        _workday_holiday_BLL.show(holidayData);

                    } else if (Logic_IsDiffHoliday == 0) {//不分工作节假日
                        if (!d.Logic_Sections) {
                            d.Logic_Sections = {};
                        }
                        $(".divTopMoneySet").addClass("layui-hide")
                        if (Logic_IsDiffTime == 0 || Logic_IsDiffTime == 1) {
                            if (Logic_IsDiffTime == 1) {//分时段
                                $(".across").removeClass("layui-hide");
                                try {
                                    //dataConfig._noworkdayTimepart_data = d.Section.HoursList
                                } catch (e) { }
                                _noworkdayTimepartBLL.show(d);
                            } else if (Logic_IsDiffTime == 0) {//不分时段
                                if (Section_ChargeMode) {

                                    if (Section_ChargeMode == 1) {//按计时单位收费
                                        try {
                                            /* dataConfig._noworkdayNotimepart_spacetime_data = [d.Section.Item];*/
                                        } catch (e) { }
                                        _noworkdayNotimepart_spacetime_BLL.show(d);
                                    } else if (Section_ChargeMode == 2) {//按时长范围收费
                                        try {
                                            //dataConfig._noworkdayNotimepart_rangtime_data = [d.Section.Item];
                                        } catch (e) { }
                                        _noworkdayNotimepart_rangtime_BLL.show(d);
                                    } else if (Section_ChargeMode == 3) {//按次收费
                                        try {
                                            //dataConfig._noworkdayNotimepart_numbertime_data = d.Section.Item;
                                        } catch (e) { }
                                        _noworkdayNotimepart_numbertime_BLL.show(d);
                                    }
                                }
                            }
                        }
                    }
                }
                _selectChangeBLL.bindChange();
            },
            //显示周期多次累计计费
            showCycleTotal: function (d) {
                if (d) {
                    if (d.Cycle_IsCharge != 0 && d.Cycle_IsCharge != 1) d.Cycle_IsCharge = 0;
                    if (d.Cycle_HoursMaxAmount != 0 && d.Cycle_HoursMaxAmount != 1) d.Cycle_HoursMaxAmount = 1;
                    $("#Cycle_IsCharge").val(d.Cycle_IsCharge);

                    $("#Cycle_Unit").val(d.Cycle_Unit || 24);
                    $("input[data-key=Cycle_UnitMaxCharge]").val(d.Cycle_UnitMaxCharge);
                    if (d.Cycle_StartTime == "1" || d.Cycle_StartTime == null) {
                        $(".Cycle_StartTime").addClass("layui-hide");
                        $("#Cycle_StartTime").val(1);
                    } else {
                        $("#Cycle_StartTime").val(0);
                        $("#Cycle_CustomStartTime").val(d.Cycle_StartTime);
                        $(".Cycle_StartTime").removeClass("layui-hide");
                    }

                    if (d.Cycle_ReadStart == "1") {
                        $("#Cycle_IsFreeTime").find('option').remove();
                        $("#Cycle_StartTime").find('option').remove();
                        $("#Cycle_IsFreeTime").append('<option value="2"' + (d.Cycle_IsFreeTime == 2 ? " selected" : "") + '>每次停车只用一次免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="1"' + (d.Cycle_IsFreeTime == 1 ? " selected" : "") + '>每个周期都用一次免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="4"' + (d.Cycle_IsFreeTime == 4 ? " selected" : "") + '>每次停车只用一次免费时长(跨周期重置)</option>');
                        $("#Cycle_StartTime").append('<option value="1">入场时间</option>');
                        $(".Cycle_StartTime").addClass("layui-hide");
                    }

                    if (d.Cycle_IsFreeTime != null) $("#Cycle_IsFreeTime").val(d.Cycle_IsFreeTime);
                    if (d.Cycle_IsFreeTimeUseCycle != null) $("#Cycle_IsFreeTimeUseCycle").val(d.Cycle_IsFreeTimeUseCycle);
                    if (d.Cycle_HoursMaxAmount != null) $("#Cycle_HoursMaxAmount").val(d.Cycle_HoursMaxAmount);
                    if (d.Cycle_ReadStart != null) $("#Cycle_ReadStart").val(d.Cycle_ReadStart);
                    if (d.Cycle_IsFirstSection != null) $("#Cycle_IsFirstSection").val(d.Cycle_IsFirstSection);

                    if (d.Cycle_IsCharge == 1) {
                        $(".Cycle_IsCharge").removeClass("layui-hide");
                    } else {
                        $(".Cycle_IsCharge").addClass("layui-hide");
                    }
                }
            }
        }

        //获取规则JSON
        var Rules = {
            GetItemValue: function (obj, className) {
                if (!$(obj).hasClass("layui-hide")) {
                    var Model = {};
                    if (className) {
                        $(obj).find("input:visible,select").each(function (k, v) {
                            if ($(this).closest('.' + className + '').length == 0) {
                                var key = $(v).attr("data-key");
                                if (key && key != "") {
                                    Model[key] = $(v).val();
                                }
                            }
                        });

                        var itemList = [];
                        $(obj).find('.' + className + '').each(function (m, n) {
                            if (!$(this).hasClass("layui-hide")) {
                                var item = {};
                                $(this).find("input:visible,select").each(function (k, v) {
                                    var key = $(v).attr("data-key");
                                    if (key && key != "") {
                                        item[key] = $(v).val();
                                    }
                                })
                                itemList.push(item);
                            }
                        });

                        Model["DurationRules"] = itemList;

                    } else {
                        $(obj).find("input:visible,select").each(function (k, v) {
                            var key = $(v).attr("data-key");
                            if (key && key != "") {
                                Model[key] = $(v).val();
                            }
                        });
                    }
                    return Model;
                } else {
                    return null;
                }
            },
            //分工作节假日
            GetWorkAndHoliDayValue: function () {
                var workdayItemList = $(".workday>.workday_work>.wcontent>.workday_item");
                var holidayItemList = $(".workday>.workday_holiday>.wcontent>.workday_holidayItem");
                var WorkDayModel = {
                    Logic_Sections: [],
                    Logic_Sections2: [],
                    CycleTotal: {}
                };
                /*WorkDayModel.Section_ChargeMode = $("#Login_HolidayCappingMode").val();*/
                WorkDayModel.Login_HolidayCappingMode = $("#Login_HolidayCappingMode").val();

                WorkDayModel.Workday_IsMaxCharge = $("#Workday_IsMaxCharge").val();
                WorkDayModel.Workday_MaxCharge = $("#Workday_MaxCharge").val();

                WorkDayModel.Holiday_IsMaxCharge = $("#Holiday_IsMaxCharge").val();
                WorkDayModel.Holiday_MaxCharge = $("#Holiday_MaxCharge").val();

                WorkDayModel.HolidayCycle_Hour = $("#HolidayCycle_Hour").val();
                WorkDayModel.HolidayCycle_MaxCharge = $("#HolidayCycle_MaxCharge").val();

                if (workdayItemList && workdayItemList.length > 0) {
                    $.each(workdayItemList, function (k, v) {
                        var model = Rules.GetItemValue(this, "tmplworkday_rangtime_item_rule");
                        if (model != null) {
                            WorkDayModel.Logic_Sections.push(Rules.SpecialData(model));
                        }
                    })
                }
                if (holidayItemList && holidayItemList.length > 0) {
                    $.each(holidayItemList, function (k, v) {
                        var model = Rules.GetItemValue(this, "tmplworkday_rangtime_item_rule");
                        if (model != null) {
                            WorkDayModel.Logic_Sections2.push(Rules.SpecialData(model));
                        }
                    })
                }

                WorkDayModel = Rules.GetCycleTotal(WorkDayModel);
                return WorkDayModel;
            },
            //不分工作节假日->分时段
            GetNoWorkDay_PartTimeValue: function () {
                var ItemList = $(".noworkday>.timepart>.tmplnoworkday_timepart_item");
                var ResultModel = {
                    Logic_Sections: [],
                };

                if (ItemList && ItemList.length > 0) {
                    $.each(ItemList, function (k, v) {
                        var model = Rules.GetItemValue(this, "tmplworkday_rangtime_item_rule");
                        if (model != null) {
                            var firsttimeList = [];
                            $(this).find(".firsttimeunit").each(function (k, v) {
                                var m = Rules.GetItemValue(this);
                                if (m != null) {
                                    firsttimeList.push(m);
                                }
                            })
                            if (firsttimeList.length > 0) { model.FirstTimeRang = firsttimeList; }

                            ResultModel.Logic_Sections.push(Rules.SpecialData(model));
                        }
                    })


                }
                ResultModel = Rules.GetCycleTotal(ResultModel);
                return ResultModel;
            },
            //不分工作节假日->不分时段->按计时单位收费
            GetNoWorkDay_NoPartTime_SpacetimeValue: function () {
                var ItemList = $(".noworkday>.notimepart>.spacetime>.tmplnoworkday_notimepart_spacetime_item");
                var ResultModel = {
                    Logic_Sections: [],
                };

                if (ItemList && ItemList.length > 0) {
                    $.each(ItemList, function (k, v) {
                        var model = Rules.GetItemValue(this);
                        if (model != null) {
                            var firsttimeList = [];
                            $(this).find(".firsttimeunit").each(function (k, v) {
                                var m = Rules.GetItemValue(this);
                                if (m != null) {
                                    firsttimeList.push(m);
                                }
                            })
                            if (firsttimeList.length > 0) { model.FirstTimeRang = firsttimeList; }
                            ResultModel.Logic_Sections.push(Rules.SpecialData(model));
                        }
                    })
                }
                ResultModel = Rules.GetCycleTotal(ResultModel);
                return ResultModel;
            },
            //不分工作节假日->不分时段->按时长范围收费
            GetNoWorkDay_NoPartTime_RangtimeValue: function () {
                var ItemList = $(".noworkday>.notimepart>.rangtime>.tmplnoworkday_notimepart_rangtime_item");
                var ResultModel = {
                    Logic_Sections: [],
                };

                if (ItemList && ItemList.length > 0) {
                    $.each(ItemList, function (k, v) {
                        var model = Rules.GetItemValue(this, "tmplnoworkday_notimepart_rangtime_item_rule");
                        if (model != null) {
                            ResultModel.Logic_Sections.push(Rules.SpecialData(model));
                        }
                    })
                }
                ResultModel = Rules.GetCycleTotal(ResultModel);
                return ResultModel;
            },
            //不分工作节假日->不分时段->按次收费
            GetNoWorkDay_NoPartTime_NumbertimeValue: function () {
                var ItemList = $(".noworkday>.notimepart>.numbertime>.tmplnoworkday_notimepart_numbertime_item");
                var ResultModel = {
                    Logic_Sections: [],
                };

                if (ItemList && ItemList.length > 0) {
                    $.each(ItemList, function (k, v) {
                        var model = Rules.GetItemValue(this);
                        if (model != null) {
                            model.CycleReCalc = model.Cycle;
                            ResultModel.Logic_Sections.push(Rules.SpecialData(model));
                        }
                    })
                }

                ResultModel = Rules.GetCycleTotal(ResultModel);
                return ResultModel;
            },
            //周期多次累计计费
            GetCycleTotal: function (data) {
                data.Cycle_IsCharge = $("#Cycle_IsCharge").val();
                data.Cycle_Unit = $("#Cycle_Unit").val();
                data.Cycle_UnitMaxCharge = $("input[data-key=Cycle_UnitMaxCharge]").val();
                data.Cycle_StartTime = $("#Cycle_StartTime").val() == "1" ? $("#Cycle_StartTime").val() : $("#Cycle_CustomStartTime").val();
                data.Cycle_IsFreeTime = $("#Cycle_IsFreeTime").val();
                data.Cycle_IsFreeTimeUseCycle = $("#Cycle_IsFreeTimeUseCycle").val();
                data.Cycle_HoursMaxAmount = $("#Cycle_HoursMaxAmount").val();
                data.Cycle_ReadStart = $("#Cycle_ReadStart").val();
                data.Cycle_IsFirstSection = $("#Cycle_IsFirstSection").val();
                return data;
            },
            SpecialData: function (model) {
                var Logic_Sections = { Section_ChargeMode: $("#Section_ChargeMode").val() };
                if (model.hasOwnProperty("Section_StartTime")) {
                    Logic_Sections.Section_StartTime = model.Section_StartTime;
                    delete model["Section_StartTime"];
                }
                if (model.hasOwnProperty("Section_EndTime")) {
                    Logic_Sections.Section_EndTime = model.Section_EndTime;
                    delete model["Section_EndTime"];
                }
                if (model.hasOwnProperty("Section_ChargeMode")) {
                    Logic_Sections.Section_ChargeMode = model.Section_ChargeMode;
                    delete model["Section_ChargeMode"];
                }
                if (model.hasOwnProperty("Section_ChargeMode_NoWorkTimePart")) {
                    Logic_Sections.Section_ChargeMode = model.Section_ChargeMode_NoWorkTimePart;
                    delete model["Section_ChargeMode_NoWorkTimePart"];
                }
                if (model.hasOwnProperty("Section_ChargeMode_Work")) {
                    Logic_Sections.Section_ChargeMode = model.Section_ChargeMode_Work;
                    delete model["Section_ChargeMode_Work"];
                }
                if (model.hasOwnProperty("Section_ChargeMode_Holiday")) {
                    Logic_Sections.Section_ChargeMode = model.Section_ChargeMode_Holiday;
                    delete model["Section_ChargeMode_Holiday"];
                }
                if (model.hasOwnProperty("Section_ChargeMode_NoWorkTimePart")) {
                    Logic_Sections.Section_ChargeMode = model.Section_ChargeMode_NoWorkTimePart;
                    delete model["Section_ChargeMode_NoWorkTimePart"];
                }
                if (model.hasOwnProperty("Section_IsMaxCharge")) {
                    Logic_Sections.Section_IsMaxCharge = model.Section_IsMaxCharge;
                    delete model["Section_IsMaxCharge"];
                }
                if (model.hasOwnProperty("Section_MaxCharge")) {
                    Logic_Sections.Section_MaxCharge = model.Section_MaxCharge;
                    delete model["Section_MaxCharge"];
                }
                if (model.hasOwnProperty("Section_IsHoursMaxCharge")) {
                    Logic_Sections.Section_IsHoursMaxCharge = model.Section_IsHoursMaxCharge;
                    delete model["Section_IsHoursMaxCharge"];
                }
                if (model.hasOwnProperty("Section_HoursMaxCharge")) {
                    Logic_Sections.Section_HoursMaxCharge = model.Section_HoursMaxCharge;
                    delete model["Section_HoursMaxCharge"];
                }

                if (model.hasOwnProperty("FirstTime_Id")) {
                    delete model["FirstTime_Id"];
                }
                if (model.hasOwnProperty("FirstTime_Min")) {
                    delete model["FirstTime_Min"];
                }
                if (model.hasOwnProperty("FirstTime_Money")) {
                    delete model["FirstTime_Money"];
                }

                //if (model.hasOwnProperty("CycleReCalc_MaxCharge")) {
                //    Logic_Sections.CycleReCalc_MaxCharge = model.CycleReCalc_MaxCharge;
                //    delete model["CycleReCalc_MaxCharge"];
                //}
                //if (model.hasOwnProperty("CycleReCalc")) {
                //    Logic_Sections.CycleReCalc = model.CycleReCalc;
                //    delete model["CycleReCalc"];
                //}

                Logic_Sections.Section_ChargeRules = JSON.stringify(model);
                return Logic_Sections;
            }
        }

        //隐藏移除所有项
        var _InitAll = {
            hide: function () {
                pager.showActionSpan();
                $(".workday,.noworkday,.workday_work,.workday_holiday,.timepart,.notimepart,.spacetime,.rangtime,.numbertime").addClass("layui-hide");
            },
            remove: function () {
                $(".tmplworkday_numbertime_item,.tmplworkday_rangtime_item,.tmplworkday_spacetime_item,.workday_item," +
                    ".tmplnoworkday_timepart_item,.tmplnoworkday_rangtime_item,.tmplnoworkday_spacetime_item," +
                    ".tmplnoworkday_notimepart_rangtime_item,.tmplnoworkday_notimepart_spacetime_item,.tmplnoworkday_notimepart_rangtime_item_rule").remove();
            },
            process: function () {
                _InitAll.hide();
                _InitAll.remove();
            }
        }

        //分工作节假日
        var _workdayBLL = {
            bindChange: function () {
                _selectChangeBLL.bindChange();
            },
        }
        //分工作节假日->按工作日
        var _workday_work_BLL = {
            onload: function (data) {

            },
            show: function (data) {
                if (!data) data = dataConfig._workday_work_data;
                else data = GetWebModel(data, data.Logic_Sections);

                $(".divLogic_IsDiffTime,.divLogic_IsDiffHoliday").addClass("layui-hide");
                $(".divLogic_IsDiffTime2,.divLogic_IsDiffHoliday2").addClass("layui-hide");
                $(".divTopMoneySet").removeClass("layui-hide");
                $(".workday,.workday_work").removeClass("layui-hide");
                $.each(data, function (k, v) {

                    var htm = $("#tmplworkday_work").tmpl([v]);
                    var bid = "Section_StartTime" + "_workday_work" + k + + $(".workday_delitem").length;
                    var eid = "Section_EndTime" + "_workday_work" + k + $(".workday_delitem").length;
                    htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                    if (k > 0) {
                        htm[0].innerHTML = htm[0].innerHTML.replace("workday_additem", "workday_delitem").replace("新增时段", "删除时段")
                        $(".workday>.workday_work>.wcontent").append(htm);
                    } else {
                        $(".workday>.workday_work>.wcontent").html(htm);
                    }
                    if (v.Section_ChargeMode) {
                        $(".workday>.workday_work>.wcontent").find("select[data-key=Section_ChargeMode_Work]").last().val(v.Section_ChargeMode);
                    }
                    layuiForm.render("select");
                    _workday_work_BLL.bindAddItem();
                    _workday_work_BLL.delItem();
                    _DATE.bind(laydate, [bid, eid], { type: 'time', range: false, format: 'HH:mm' });

                    var content = $(".workday_item").last().find(".content");
                    if (v.Section_ChargeMode == null || v.Section_ChargeMode == undefined)
                        _workday_spacetime_BLL.show(content, [v]);
                    else {
                        if (v.Section_ChargeMode == 2) {
                            _workday_rangtime_BLL.show(content, [v]);
                        } else if (v.Section_ChargeMode == 1) {
                            _workday_spacetime_BLL.show(content, [v]);
                        } else {
                            _workday_numbertime_BLL.show(content, [v]);
                        }
                    }
                })

                myVerify.init();

            },
            bindAddItem: function () {
                $(".workday_additem").unbind("click").click(function () {

                    $(".iconItem").parent().parent().find(".content").each(function () {
                        $(this).slideUp();
                    })
                    $(".iconItem").each(function () {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                    })

                    var htm = $("#tmplworkday_work").tmpl([{ index: 0 }]);
                    var bid = "Section_StartTime" + "_workday_delitem" + $(".workday_delitem").length;
                    var eid = "Section_EndTime" + "_workday_delitem" + $(".workday_delitem").length;
                    htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("workday_additem", "workday_delitem").replace("新增时段", "删除时段")
                    $(".workday>.workday_work>.wcontent").append(htm);
                    _DATE.bind(laydate, [bid, eid], { type: 'time', range: false, format: 'HH:mm' });
                    _workday_work_BLL.delItem();

                    var selValue = $(".workday>.workday_work>.wcontent>.workday_item").first().find("select").first().val();
                    $(".workday>.workday_work>.wcontent>.workday_item").last().find("select").first().val(selValue);
                    var content = $(".workday_item").last().find(".content");
                    if (selValue == 1) {
                        _workday_spacetime_BLL.show(content);
                    } else if (selValue == 2) {
                        _workday_rangtime_BLL.show(content);
                    } else {
                        _workday_numbertime_BLL.show(content);
                    }
                    layuiForm.render("select");
                });
            },
            delItem: function () {
                $(".workday_delitem").unbind("click").click(function () {
                    $(this).parent().parent().parent().remove();
                });
                $(".iconItem").unbind("click").click(function () {
                    var content = $(this).parent().parent().find(".content");
                    if ($(content).is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(content).toggle("normal");
                })
            },
        }
        //分工作节假日—>按节假日
        var _workday_holiday_BLL = {
            onload: function (data) {

            },
            show: function (data) {
                if (!data) {
                    data = dataConfig._workday_holiday_data;
                } else {
                    data = GetWebModel(data, data.Logic_Sections2);
                }

                $(".workday,.workday_holiday").removeClass("layui-hide");
                $.each(data, function (k, v) {

                    var htm = $("#tmplworkday_holiday").tmpl([v]);
                    var bid = "Section_StartTime" + "_workday_holiday" + k + $(".workday_holidayDelitem").length;
                    var eid = "Section_EndTime" + "_workday_holiday" + k + $(".workday_holidayDelitem").length;
                    htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                    if (k > 0) {
                        htm[0].innerHTML = htm[0].innerHTML.replace("workday_holidayAdditem", "workday_holidayDelitem").replace("新增时段", "删除时段")
                        $(".workday>.workday_holiday>.wcontent").append(htm);
                    } else {
                        $(".workday>.workday_holiday>.wcontent").html(htm);
                    }

                    if (v.Section_ChargeMode) {
                        $(".workday>.workday_holiday>.wcontent").find("select[data-key=Section_ChargeMode_Holiday]").last().val(v.Section_ChargeMode);
                    }

                    layuiForm.render("select");
                    _workday_holiday_BLL.bindAddItem();
                    _workday_holiday_BLL.delItem();
                    _DATE.bind(laydate, [bid, eid], { type: 'time', range: false, format: 'HH:mm' });

                    var content = $(".workday_holidayItem").last().find(".content");
                    if (v.Section_ChargeMode == null || v.Section_ChargeMode == undefined)
                        _workday_spacetime_BLL.show(content, [v]);
                    else {
                        if (v.Section_ChargeMode == 2) {
                            _workday_rangtime_BLL.show(content, [v]);
                        } else if (v.Section_ChargeMode == 1) {
                            _workday_spacetime_BLL.show(content, [v]);
                        } else {
                            _workday_numbertime_BLL.show(content, [v]);
                        }
                    }
                });
                myVerify.init();
            },
            bindAddItem: function () {
                $(".workday_holidayAdditem").unbind("click").click(function () {
                    $(".iconItem").parent().parent().find(".content").each(function () {
                        $(this).slideUp();
                    })
                    $(".iconItem").each(function () {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                    })

                    var htm = $("#tmplworkday_holiday").tmpl([{ index: 0 }]);
                    var bid = "Section_StartTime" + "_workday_holidayDelitem" + $(".workday_holidayDelitem").length;
                    var eid = "Section_EndTime" + "_workday_holidayDelitem" + $(".workday_holidayDelitem").length;
                    htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("workday_holidayAdditem", "workday_holidayDelitem").replace("新增时段", "删除时段")
                    $(".workday>.workday_holiday>.wcontent").append(htm);
                    _workday_holiday_BLL.delItem();
                    _DATE.bind(laydate, [bid, eid], { type: 'time', range: false, format: 'HH:mm' });

                    var selValue = $(".workday>.workday_holiday>.wcontent>.workday_holidayItem").first().find("select").first().val();
                    $(".workday>.workday_holiday>.wcontent>.workday_holidayItem").last().find("select").first().val(selValue);
                    var content = $(".workday_holidayItem").last().find(".content");

                    if (selValue == 1) {
                        _workday_spacetime_BLL.show(content);
                    } else if (selValue == 2) {
                        _workday_rangtime_BLL.show(content);
                    } else {
                        _workday_numbertime_BLL.show(content);
                    }

                    layuiForm.render("select");
                    _selectChangeBLL.bindChange();
                });
            },
            delItem: function () {
                $(".workday_holidayDelitem").unbind("click").click(function () {
                    $(this).parent().parent().parent().remove();
                });
                $(".iconItem").unbind("click").click(function () {
                    var content = $(this).parent().parent().find(".content");
                    if ($(content).is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(content).toggle("normal");
                })
            },

        }
        //分工作日->按次收费
        var _workday_numbertime_BLL = {
            show: function (id, data) {
                if (!data) {
                    data = dataConfig._workday_numbertime_data;
                }
                //if (JSON.stringify(data) == JSON.stringify([{}])) {
                //    data = itemConfig._numbertime;
                //}
                //if (!Array.isArray(data)) data = [data];

                var htm = $("#tmplworkday_numbertime").tmpl(data);
                htm[0].innerHTML = htm[0].innerHTML.replace("sstime", "Section_StartTime" + "_workday_numbertime_BLL" + 1);
                htm[0].innerHTML = htm[0].innerHTML.replace("setime", "Section_EndTime" + "_workday_numbertime_BLL" + 1);
                $(id).html(htm);
                _DATE.bind(laydate, ["Section_StartTime" + "_workday_numbertime_BLL" + 1, "Section_EndTime" + "_workday_numbertime_BLL" + 1], { type: 'time', range: false, format: 'HH:mm' });

                if (data && data.length > 0 && data[0].Section_IsHoursMaxCharge == 1) {
                    $(id).find("select[data-key=Section_IsHoursMaxCharge]").val(1);
                    $(id).find(".Section_IsHoursMaxCharge").removeClass("layui-hide");
                } else {
                    $(id).find("select[data-key=Section_IsHoursMaxCharge]").val(2);
                    $(id).find(".Section_IsHoursMaxCharge").addClass("layui-hide");
                }

                layuiForm.render("select");
                myVerify.init();
            },
        }
        //分工作日->按计时单位
        var _workday_spacetime_BLL = {
            show: function (id, data) {
                if (!data) {
                    data = dataConfig._workday_spacetime_data;
                }
                //if (JSON.stringify(data) == JSON.stringify([{}])) {
                //    data = itemConfig._spacetime;
                //}

                var htm = $("#tmplworkday_spacetime").tmpl(data);
                $(id).html(htm);

                $.each(data, function (k, v) {
                    firsttimeRang.binddata($(id), v.FirstTimeRang);

                    if (v.FirstTime_Type == 1) {
                        $(id).find(".firsttimerang").removeClass("layui-hide");
                        $(id).find(".firsttimeunit").removeClass("layui-hide").addClass("layui-hide");
                        firsttime_table.binddata($(id).find(".firsttime-table").first(), v.FirstTimeArray);
                    }
                    if (v.Section_IsMaxCharge == 1) {
                        $(id).find("select[data-key=Section_IsMaxCharge]").eq(k).val(1);
                        $(id).find(".Section_IsMaxCharge").eq(k).removeClass("layui-hide");
                    } else {
                        $(id).find("select[data-key=Section_IsMaxCharge]").eq(k).val(2);
                        $(id).find(".Section_IsMaxCharge").eq(k).addClass("layui-hide");
                    }

                    if (v.CycleReCalc == 1) {
                        $(id).find("select[data-key=CycleReCalc]").eq(k).val(1);
                        $(id).find(".CycleReCalc").eq(k).removeClass("layui-hide");
                    } else {
                        $(id).find("select[data-key=CycleReCalc]").eq(k).val(2);
                        $(id).find(".CycleReCalc").eq(k).addClass("layui-hide");
                    }

                    if (v.IsEveryFirstTime == 1) {
                        $(id).find("select[data-key=IsEveryFirstTime]").eq(k).val(1);
                    } else {
                        $(id).find("select[data-key=IsEveryFirstTime]").eq(k).val(0);
                    }

                    if (v.FirstTime_Type == 1) {
                        $(id).find("select[data-key=FirstTime_Type]").eq(k).val(1);
                    } else {
                        $(id).find("select[data-key=FirstTime_Type]").eq(k).val(0);
                    }

                    if (v.Section_IsHoursMaxCharge == 1) {
                        $(id).find("select[data-key=Section_IsHoursMaxCharge]").eq(k).val(1);
                        $(id).find(".Section_IsHoursMaxCharge").eq(k).removeClass("layui-hide");
                    } else {
                        $(id).find("select[data-key=Section_IsHoursMaxCharge]").eq(k).val(2);
                        $(id).find(".Section_IsHoursMaxCharge").eq(k).addClass("layui-hide");
                    }
                })

                layuiForm.render("select");
                myVerify.init();
            }
        }
        //分工作日->按时长范围收费
        var _workday_rangtime_BLL = {
            show: function (id, data) {
                if (!data) {
                    data = dataConfig._workday_rangtime_data;
                }
                //if (JSON.stringify(data) == JSON.stringify([{}])) {
                //    data = itemConfig._rangtime;
                //}

                $.each(data, function (m, n) {
                    if (m > 0)
                        $(id).append($("#tmplworkday_rangtime").tmpl([n]));
                    else
                        $(id).html($("#tmplworkday_rangtime").tmpl([n]));

                    if (n.ValueMethod == 1 || n.ValueMethod == 2) {
                        $(id).find("select[data-key=ValueMethod]").val(n.ValueMethod);
                    }
                    if (n.IsFreeMin == 1 || n.IsFreeMin == 0) {
                        $(id).find("select[data-key=IsFreeMin]").val(n.IsFreeMin);
                    }
                    if (n.IsEveryFirstTime == 1) { $(id).find("select[data-key=IsEveryFirstTime]").val(1); } else { $(id).find("select[data-key=IsEveryFirstTime]").val(0); }
                    if (n.FirstTime_Type == 1) { $(id).find("select[data-key=FirstTime_Type]").val(1); } else { $(id).find("select[data-key=FirstTime_Type]").val(0); }
                    if (n.Section_IsHoursMaxCharge == 1) { $(id).find("select[data-key=Section_IsHoursMaxCharge]").val(1); $(id).find(".Section_IsHoursMaxCharge").removeClass("layui-hide"); } else { $(id).find("select[data-key=Section_IsHoursMaxCharge]").val(2); $(id).find(".Section_IsHoursMaxCharge").addClass("layui-hide"); }

                    layuiForm.render("select");
                    if (n.DurationRules) {
                        $.each(n.DurationRules, function (k, v) {
                            var htm = $("#tmplworkday_rangtime_rule").tmpl([v]);
                            if (k > 0) {
                                htm[0].innerHTML = htm[0].innerHTML.replace("tmplworkday_rangtime_item_rule_additem", "tmplworkday_rangtime_item_rule_delitem").replace("新增规则", "删除规则");
                                $(id).find(".tmplworkday_rangtime_rule").append(htm);
                            } else {
                                $(id).find(".tmplworkday_rangtime_rule").html(htm);
                                _workday_rangtime_BLL.bindAddItem(id);
                            }
                            layuiForm.render("select");
                        });
                    }
                });

                _workday_rangtime_BLL.bindAddItem(id);
                _workday_rangtime_BLL.delItem();
                myVerify.init();
            },
            bindAddItem: function (id) {
                $(id).find("button").unbind("click").click(function () {

                    var topParent = $(this).closest(".layui-col-xs10");
                    $.each($(topParent).find("input[data-key=DurationRule_Hour]"), function (k, v) {

                        console.log(v)
                    })

                    var htm = $("#tmplworkday_rangtime_rule").tmpl([{}]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("tmplworkday_rangtime_item_rule_additem", "tmplworkday_rangtime_item_rule_delitem").replace("新增规则", "删除规则")
                    $(this).closest(".tmplworkday_rangtime_rule").append(htm);
                    layuiForm.render("select");
                    _workday_rangtime_BLL.delItem();
                    myVerify.init();
                });
            },
            delItem: function () {
                $(".tmplworkday_rangtime_item_rule_delitem").unbind("click").click(function () {
                    $(this).parent().parent().parent().remove();
                });
            },
        }

        //不分工作日->不分时段
        var _noworkdayNotimepartBLL = {
            bindChange: function () {

            },
        }
        //不分工作日->不分时段->按次收费
        var _noworkdayNotimepart_numbertime_BLL = {
            show: function (data) {
                $(".noworkday,.notimepart,.numbertime").removeClass("layui-hide");

                if (!data) {
                    data = dataConfig._noworkdayNotimepart_numbertime_data;
                } else {
                    data = GetWebModel(data, data.Logic_Sections);
                }

                //if (JSON.stringify(obj) == JSON.stringify({})) {
                //    obj = itemConfig._numbertime;
                //}
                $(".noworkday>.notimepart>.numbertime").html($("#tmplnoworkday_notimepart_numbertime").tmpl(data));

                if (data && data.length > 0 && data[0].CycleReCalc == 1) {
                    $(".noworkday>.notimepart>.numbertime").find("select[data-key=Cycle]").val(1);
                    $(".noworkday>.notimepart>.numbertime").find(".Cycle").removeClass("layui-hide");
                } else {
                    $(".noworkday>.notimepart>.numbertime").find("select[data-key=Cycle]").val(2);
                    $(".noworkday>.notimepart>.numbertime").find(".Cycle").addClass("layui-hide");
                }

                if (data && data.length > 0 && data[0].Section_IsHoursMaxCharge == 1) {
                    $(".noworkday>.notimepart>.numbertime").find("select[data-key=Section_HoursMaxCharge]").val(1);
                    $(".noworkday>.notimepart>.numbertime").find(".Section_HoursMaxCharge").removeClass("layui-hide");
                } else {
                    $(".noworkday>.notimepart>.numbertime").find("select[data-key=Section_HoursMaxCharge]").val(2);
                    $(".noworkday>.notimepart>.numbertime").find(".Section_HoursMaxCharge").addClass("layui-hide");
                }

                layuiForm.render("select");
                myVerify.init();
            }
        }
        //不分工作日->不分时段->按计时单位收费
        var _noworkdayNotimepart_spacetime_BLL = {
            show: function (data) {
                $(".noworkday,.notimepart,.spacetime").removeClass("layui-hide");
                var obj = dataConfig._noworkdayNotimepart_spacetime_data;
                if (data) {
                    obj = GetWebModel(data, data.Logic_Sections);
                }
                //if (JSON.stringify(obj) == JSON.stringify([{}])) {
                //    obj = itemConfig._spacetime;
                //}
                $(".noworkday>.notimepart>.spacetime").html($("#tmplnoworkday_notimepart_spacetime").tmpl(obj));

                $.each(obj, function (k, v) {
                    firsttimeRang.binddata($(".tmplnoworkday_notimepart_spacetime_item"), v.FirstTimeRang);

                    if (v.FirstTime_Type == 1) {
                        $(".noworkday>.notimepart>.spacetime").find(".firsttimerang").removeClass("layui-hide");
                        $(".noworkday>.notimepart>.spacetime").find(".firsttimeunit").removeClass("layui-hide").addClass("layui-hide");
                        firsttime_table.binddata($(".noworkday>.notimepart>.spacetime").find(".firsttime-table").first(), v.FirstTimeArray);
                    }

                    if (v.CycleReCalc == 1) {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=CycleReCalc]").eq(k).val(1);
                        $(".noworkday>.notimepart>.spacetime").find(".CycleReCalc").eq(k).removeClass("layui-hide");
                    } else {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=CycleReCalc]").eq(k).val(2);
                        $(".noworkday>.notimepart>.spacetime").find(".CycleReCalc").eq(k).addClass("layui-hide");
                    }

                    if (v.IsEveryFirstTime == 1) {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=IsEveryFirstTime]").eq(k).val(1);
                    } else {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=IsEveryFirstTime]").eq(k).val(0);
                    }
                    if (v.FirstTime_Type == 1) {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=FirstTime_Type]").eq(k).val(1);
                    } else {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=FirstTime_Type]").eq(k).val(0);
                    }

                    if (v.Section_IsHoursMaxCharge == 1) {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=Section_IsHoursMaxCharge]").eq(k).val(1);
                        $(".noworkday>.notimepart>.spacetime").find(".Section_IsHoursMaxCharge").eq(k).removeClass("layui-hide");
                    } else {
                        $(".noworkday>.notimepart>.spacetime").find("select[data-key=Section_IsHoursMaxCharge]").eq(k).val(2);
                        $(".noworkday>.notimepart>.spacetime").find(".Section_IsHoursMaxCharge").eq(k).addClass("layui-hide");
                    }
                })

                layuiForm.render("select");
                myVerify.init();
            }
        }
        //不分工作日->不分时段->按时长范围收费
        var _noworkdayNotimepart_rangtime_BLL = {
            show: function (data) {
                $(".noworkday,.notimepart,.rangtime").removeClass("layui-hide");
                if (!data) { data = dataConfig._noworkdayNotimepart_rangtime_data; }
                else {
                    data = GetWebModel(data, data.Logic_Sections);
                }
                //if (JSON.stringify(data) == JSON.stringify([{}])) {
                //    data = itemConfig._rangtime;
                //}

                $.each(data, function (m, n) {
                    if (m > 0)
                        $(".noworkday>.notimepart>.rangtime").append($("#tmplnoworkday_notimepart_rangtime").tmpl([n]));
                    else
                        $(".noworkday>.notimepart>.rangtime").html($("#tmplnoworkday_notimepart_rangtime").tmpl([n]));

                    if (n.OverCycleMode == 2) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=OverCycleMode]").eq(m).val(2);
                        $(".noworkday>.notimepart>.rangtime").find(".OverCycleMode").removeClass("layui-hide");
                    } else {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=OverCycleMode]").eq(m).val(1);
                        $(".noworkday>.notimepart>.rangtime").find(".OverCycleMode").addClass("layui-hide");
                    }

                    if (n.ValueMethod == 1 || n.ValueMethod == 2) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=ValueMethod]").eq(m).val(n.ValueMethod);
                    }
                    if (n.IsFreeMin == 1 || n.IsFreeMin == 2) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=IsFreeMin]").eq(m).val(n.IsFreeMin);
                    }

                    if (n.CycleReCalc == 1) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=CycleReCalc]").eq(m).val(1);
                        if (n.OverCycleMode == 2)
                            $(".noworkday>.notimepart>.rangtime").find(".CycleReCalc").removeClass("layui-hide");
                    } else {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=CycleReCalc]").eq(m).val(2);
                        $(".noworkday>.notimepart>.rangtime").find(".CycleReCalc").addClass("layui-hide");
                    }
                    if (n.IsEveryFirstTime == 1) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=IsEveryFirstTime]").eq(m).val(1);
                    } else {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=IsEveryFirstTime]").eq(m).val(0);
                    }
                    if (n.FirstTime_Type == 1) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=FirstTime_Type]").eq(m).val(1);
                    } else {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=FirstTime_Type]").eq(m).val(0);
                    }

                    if (n.Section_IsHoursMaxCharge == 1) {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=Section_IsHoursMaxCharge]").eq(m).val(1);
                        $(".noworkday>.notimepart>.rangtime").find(".Section_IsHoursMaxCharge").removeClass("layui-hide");
                    } else {
                        $(".noworkday>.notimepart>.rangtime").find("select[data-key=Section_IsHoursMaxCharge]").eq(m).val(2);
                        $(".noworkday>.notimepart>.rangtime").find(".Section_IsHoursMaxCharge").addClass("layui-hide");
                    }

                    if (!n.DurationRules) n.DurationRules = [{}];
                    if (n.DurationRules) {
                        $.each(n.DurationRules, function (k, v) {
                            var htm = $("#tmplnoworkday_notimepart_rangtime_rule").tmpl([v]);
                            if (k > 0) {
                                htm[0].innerHTML = htm[0].innerHTML.replace("tmplnoworkday_notimepart_rangtime_item_rule_additem", "tmplnoworkday_notimepart_rangtime_item_rule_delitem").replace("新增规则", "删除规则");
                                $(".tmplnoworkday_notimepart_rangtime_rule").append(htm);
                            } else {
                                $(".tmplnoworkday_notimepart_rangtime_rule").html(htm);
                                _noworkdayNotimepart_rangtime_BLL.bindAddItem();
                            }
                            
                            layuiForm.render("select");
                        });
                    }
                    layuiForm.render("select");
                });
                _noworkdayNotimepart_rangtime_BLL.delItem();
            },
            bindAddItem: function () {
                $(".tmplnoworkday_notimepart_rangtime_item_rule_additem").unbind("click").click(function () {
                    var htm = $("#tmplnoworkday_notimepart_rangtime_rule").tmpl([{ DurationRule_Hour: 0, DurationRule_Min: 0 }]);
                    htm[0].innerHTML = htm[0].innerHTML.replace("tmplnoworkday_notimepart_rangtime_item_rule_additem", "tmplnoworkday_notimepart_rangtime_item_rule_delitem").replace("新增规则", "删除规则")
                    $(".tmplnoworkday_notimepart_rangtime_rule").append(htm);
                    layuiForm.render("select");
                    _noworkdayNotimepart_rangtime_BLL.delItem();
                    myVerify.init();
                });
            },
            delItem: function () {
                $(".tmplnoworkday_notimepart_rangtime_item_rule_delitem").unbind("click").click(function () {
                    $(this).parent().parent().parent().remove();
                });
            },

        }
        //不分工作日->分时段
        var _noworkdayTimepartBLL = {
            bindChange: function () {

            },
            show: function (data) {
                $(".divTopMoneySet").addClass("layui-hide");
                $(".divLogic_IsDiffHoliday").addClass("layui-hide");
                $(".noworkday,.timepart").removeClass("layui-hide");
                if (!data) data = dataConfig._noworkdayTimepart_data;
                else { data = GetWebModel(data, data.Logic_Sections); }

                $.each(data, function (m, n) {
                    var subdata = [n];

                    var bid = "Section_StartTime" + "_noworkdayTimepartBLL" + m + + $(".noworkday_timepart_delitem").length;
                    var eid = "Section_EndTime" + "_noworkdayTimepartBLL" + m + $(".noworkday_timepart_delitem").length;
                    if (m > 0) {
                        var htm = $("#tmplnoworkday_timepart").tmpl(subdata);
                        htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                        htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                        htm[0].innerHTML = htm[0].innerHTML.replace("noworkday_timepart_additem", "noworkday_timepart_delitem").replace("新增时段", "删除时段")
                        $(".noworkday>.timepart").append(htm);
                    }
                    else {
                        var htm = $("#tmplnoworkday_timepart").tmpl(subdata);
                        htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                        htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                        $(".noworkday>.timepart").html(htm);

                    }

                    if (n.Section_ChargeMode == undefined || n.Section_ChargeMode == null || n.Section_ChargeMode == 0) {
                        n.Section_ChargeMode = 1;
                        subdata = null;
                    }
                    $(".noworkday>.timepart").find("select[data-key=Section_ChargeMode_NoWorkTimePart]").last().val(n.Section_ChargeMode);
                    var content = $(".noworkday>.timepart>.tmplnoworkday_timepart_item").last().find(".content");
                    if (n.Section_ChargeMode == 2) {
                        _workday_rangtime_BLL.show(content, subdata);
                    } else if (n.Section_ChargeMode == 1) {
                        _workday_spacetime_BLL.show(content, subdata);
                    } else {
                        _workday_numbertime_BLL.show(content, subdata);
                    }

                    if (n.Section_IsHoursMaxCharge == 1) {
                        $(".noworkday>.timepart").find("select[data-key=Section_IsHoursMaxCharge]").eq(m).val(1);
                        $(".noworkday>.timepart").find(".Section_IsHoursMaxCharge").eq(m).removeClass("layui-hide");
                    } else {
                        $(".noworkday>.timepart").find("select[data-key=Section_IsHoursMaxCharge]").eq(m).val(2);
                        $(".noworkday>.timepart").find(".Section_IsHoursMaxCharge").eq(m).addClass("layui-hide");
                    }

                    _DATE.bind(laydate, [bid, eid], { type: 'time', range: false, format: 'HH:mm' });
                    layuiForm.render("select");
                });



                _noworkdayTimepartBLL.bindAddItem();
                _noworkdayTimepartBLL.delItem();
                _selectChangeBLL.bindChange();
                myVerify.init();
            },
            bindAddItem: function (data) {
                $(".noworkday_timepart_additem").unbind("click").click(function () {
                    $(".iconItem").parent().parent().find(".content").each(function () {
                        $(this).slideUp();
                    })
                    $(".iconItem").each(function () {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up");
                    })

                    var htm = $("#tmplnoworkday_timepart").tmpl([{}]);
                    var bid = "Section_StartTime" + "_tmplnoworkday_timepart_item" + $(".tmplnoworkday_timepart_item").length;
                    var eid = "Section_EndTime" + "_tmplnoworkday_timepart_item" + $(".tmplnoworkday_timepart_item").length;
                    htm[0].innerHTML = htm[0].innerHTML.replace("sstime", bid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("setime", eid);
                    htm[0].innerHTML = htm[0].innerHTML.replace("noworkday_timepart_additem", "noworkday_timepart_delitem").replace("新增时段", "删除时段");
                    $(".timepart").append(htm);

                    var selValue = $(".noworkday>.timepart>.tmplnoworkday_timepart_item").first().find("select").first().val();
                    $(".noworkday>.timepart>.tmplnoworkday_timepart_item").last().find("select").first().val(selValue);
                    var content = $(".noworkday>.timepart>.tmplnoworkday_timepart_item").last().find(".content");
                    if (selValue == 1) {
                        _workday_spacetime_BLL.show(content);
                    } else if (selValue == 2) {
                        _workday_rangtime_BLL.show(content);
                    } else {
                        _workday_numbertime_BLL.show(content);
                    }
                    _DATE.bind(laydate, [bid, eid], { type: 'time', range: false, format: 'HH:mm' });
                    layuiForm.render("select");
                    _noworkdayTimepartBLL.delItem();
                });
            },
            delItem: function () {
                $(".noworkday_timepart_delitem").unbind("click").click(function () {
                    $(this).parent().parent().parent().remove();
                });
                $(".iconItem").unbind("click").click(function () {
                    var content = $(this).parent().parent().find(".content");
                    if ($(content).is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(content).toggle("normal");
                })
            },

        }

        //下拉控制
        var _selectChangeBLL = {
            timeRangChange: function (val) {
                _InitAll.process();
                $("#Section_ChargeMode").val(1);
                layuiForm.render("select");
                if (val == 1) {
                    $(".divLogic_IsDiffHoliday").addClass("layui-hide");
                    $(".across,.divLogic_DiffTimeCharge").removeClass("layui-hide");
                    if ($("#Logic_IsDiffHoliday").val() != 1) {
                        $(".divLogic_IsDiffHoliday2").removeClass("layui-hide")
                    }
                    _noworkdayTimepartBLL.show();
                } else {
                    $(".across,.divLogic_DiffTimeCharge").addClass("layui-hide");
                    $(".divLogic_IsDiffHoliday").removeClass("layui-hide");
                    $(".divLogic_IsDiffHoliday2").addClass("layui-hide")
                    _noworkdayNotimepart_spacetime_BLL.show();
                }
            },
            bindChange: function () {
                layuiForm.on("select", function (data) {
                    var val = data.value;
                    var id = data.elem.id;
                    var key = $(data.elem).attr("data-key");
                    var category = $(data.elem).attr("data-category");

                    _selectChangeBLL.show(this, key, val, category);
                    bindTip();
                })
            },
            show: function (obj, key, val, category) {
                if (key == "Logic_IsDiffHoliday") {//是否分工作日下拉
                    _InitAll.process();
                    if (val == 1) {//分
                        $(".across").removeClass("layui-hide");
                        $(".divLogic_IsDiffTime,.divLogic_IsDiffHoliday").addClass("layui-hide")
                        $(".divLogic_IsDiffHoliday2").addClass("layui-hide")
                        $(".divTopMoneySet").removeClass("layui-hide")
                        _workday_work_BLL.show();
                        _workday_holiday_BLL.show();
                    } else {
                        $(".across").addClass("layui-hide");
                        $(".divTopMoneySet").addClass("layui-hide")
                        $(".divLogic_IsDiffTime,.divLogic_IsDiffHoliday").removeClass("layui-hide");
                        $(".divLogic_IsDiffTime2").addClass("layui-hide");
                        $("#Section_ChargeMode").val(1);
                        $("#Logic_IsDiffTime").val(0);
                        if ($("#Logic_IsDiffTime").val() == 1) {
                            $(".divLogic_IsDiffHoliday2").removeClass("layui-hide")
                        }
                        layuiForm.render("select");
                        _noworkdayNotimepart_spacetime_BLL.show();
                    }
                }
                else if (key == "Login_HolidayCappingMode") {////周期封顶金额下拉
                    if (val == 1) {
                        $(".divTopMoneySetItem").removeClass("layui-hide")
                        $(".divTopMoneySetItem2").addClass("layui-hide")
                    } else {
                        $(".divTopMoneySetItem").addClass("layui-hide")
                        $(".divTopMoneySetItem2").removeClass("layui-hide")
                    }
                }
                else if (key == "Section_ChargeMode_NoWorkTimePart" || key == "Section_ChargeMode_Work" || key == "Section_ChargeMode_Holiday") {//分工作节假日
                    _InitAll.hide();
                    if (category == "workday") {
                        $(".workday,.workday_work,.workday_holiday").removeClass("layui-hide");
                        var content = $(obj).closest(".workday_item").find(".content");
                        $(content).html("");
                        if (val == 1) {
                            _workday_spacetime_BLL.show(content);
                        } else if (val == 2) {
                            _workday_rangtime_BLL.show(content);
                        } else {
                            _workday_numbertime_BLL.show(content);
                        }
                    } else if (category == "holiday") {
                        $(".workday,.workday_work,.workday_holiday").removeClass("layui-hide");
                        var content = $(obj).closest(".workday_holidayItem").find(".content");
                        if (val == 1) {
                            _workday_spacetime_BLL.show(content);
                        } else if (val == 2) {
                            _workday_rangtime_BLL.show(content);
                        } else {
                            _workday_numbertime_BLL.show(content);
                        }
                    } else if (category == "noworkday_timepart") {
                        $(".noworkday,.timepart").removeClass("layui-hide");
                        if (val == 1) {
                            $(".noworkday>.timepart>.spacetime").removeClass("layui-hide");

                            var content = $(obj).closest(".tmplnoworkday_timepart_item").find(".content");
                            _workday_spacetime_BLL.show(content);
                        } else if (val == 2) {
                            $(".noworkday>.timepart>.rangtime").removeClass("layui-hide");
                            var content = $(obj).closest(".tmplnoworkday_timepart_item").find(".content");
                            _workday_rangtime_BLL.show(content);
                        } else {
                            $(".noworkday>.timepart>.numbertime").removeClass("layui-hide");
                            var content = $(obj).closest(".tmplnoworkday_timepart_item").find(".content");
                            _workday_numbertime_BLL.show(content);
                        }
                    }
                } else if (key == "Logic_IsDiffTime") {//不分工作节假日 ->是否分时段下拉
                    if (val == 1) {
                        var layIndex = layer.open({
                            title: "温馨提示",
                            content: "注意：当存在内外场区域都收费时，暂不支持选择分时段收费类型",
                            area: ["350px", "200px"],
                            btn: ["车场不存在内外场，确定继续", "取消"],
                            yes: function () {
                                layer.close(layIndex);
                                _selectChangeBLL.timeRangChange(val);
                            },
                            btn2: function () {
                                $("#Logic_IsDiffTime").val(0);
                                layuiForm.render("select");
                            },
                            cancel: function () { }
                        })
                    } else {
                        _selectChangeBLL.timeRangChange(val);
                    }
                } else if (key == "Section_ChargeMode") {//不分工作日->不分时段->收费下拉
                    _InitAll.process();
                    if (val == 3) {
                        _noworkdayNotimepart_numbertime_BLL.show();
                    } else if (val == 1) {
                        _noworkdayNotimepart_spacetime_BLL.show();
                    } else {
                        _noworkdayNotimepart_rangtime_BLL.show();
                    }
                } else if (key == "Workday_IsMaxCharge") {//是否设置最高收费
                    if (val == 2) {
                        $(".workday_header_value").removeClass("layui-hide");
                    } else {
                        $(".workday_header_value").addClass("layui-hide");
                    }
                } else if (key == "Holiday_IsMaxCharge") {//是否设置最高收费
                    if (val == 2) {
                        $(".workday_holiday_header_value").removeClass("layui-hide");
                    } else {
                        $(".workday_holiday_header_value").addClass("layui-hide");
                    }
                } else if (key == "BillRuleTemp_CarCardType" || key == "BillRuleTemp_CarType" || key == "BillRuleTemp_ParkArea") {
                    /*  pager.getRulesData();*/
                } else if (key == "Cycle_IsCharge") {
                    if (val == 1) {
                        $(".Cycle_IsCharge").removeClass("layui-hide");
                    } else {
                        $(".Cycle_IsCharge").addClass("layui-hide");
                    }
                } else if (key == "Section_IsMaxCharge") {
                    if (val == 1) {
                        $(obj).closest(".layui-col-xs12").find(".Section_IsMaxCharge").removeClass("layui-hide");
                        $(obj).closest(".layui-col-xs12").find(".Section_IsMaxCharge").removeClass("layui-hide");
                    } else {
                        $(obj).closest(".layui-col-xs12").find(".Section_IsMaxCharge").addClass("layui-hide");
                        $(obj).closest(".layui-col-xs12").find(".Section_IsMaxCharge").addClass("layui-hide");
                    }
                } else if (key == "CycleReCalc") {
                    if (val == 1) {
                        $(obj).closest(".layui-col-xs12").find(".CycleReCalc").removeClass("layui-hide");
                    } else {
                        $(obj).closest(".layui-col-xs12").find(".CycleReCalc").addClass("layui-hide");
                    }
                }
                else if (key == "Section_IsHoursMaxCharge") {
                    if (val == 1) {
                        $(obj).closest(".layui-col-xs12").find(".Section_IsHoursMaxCharge").removeClass("layui-hide");
                    } else {
                        $(obj).closest(".layui-col-xs12").find(".Section_IsHoursMaxCharge").addClass("layui-hide");
                    }
                }
                else if (key == "OverCycleMode") {
                    if (val == 1) {
                        $(obj).closest(".layui-col-xs12").find(".OverCycleMode").addClass("layui-hide");
                        $(obj).closest(".layui-col-xs12").find(".CycleReCalc").addClass("layui-hide");


                    } else {
                        $(obj).closest(".layui-col-xs12").find(".OverCycleMode").removeClass("layui-hide");
                        $(obj).closest(".layui-col-xs12").find("select[data-key=CycleReCalc]").val(1);
                        layuiForm.render("select");
                        $(obj).closest(".layui-col-xs12").find(".CycleReCalc").removeClass("layui-hide");
                    }
                } else if (key == "Cycle") {
                    if (val == 1) {
                        $(obj).closest(".layui-col-xs12").find(".Cycle").removeClass("layui-hide");
                    } else {
                        $(obj).closest(".layui-col-xs12").find(".Cycle").addClass("layui-hide");
                    }
                } else if (key == "Cycle_StartTime") {
                    if (val == 1) {
                        $(".Cycle_StartTime").addClass("layui-hide");
                    } else {
                        $(".Cycle_StartTime").removeClass("layui-hide");
                    }
                } else if (key == "Logic_CycleReCalc") {
                    if (val == 2) {
                        $(".Logic_CycleReCalc").addClass("layui-hide");
                    } else {
                        $(".Logic_CycleReCalc").removeClass("layui-hide");
                    }
                } else if (key == "Logic_IsFreeTime") {
                    if (val == 1) {
                        $(".Logic_IsFreeTime").removeClass("layui-hide");
                    } else {
                        $(".Logic_IsFreeTime").addClass("layui-hide");
                    }
                } else if (key == "Logic_IsTopMoney") {
                    if (val == 1) {
                        $(".IsTopMoney").removeClass("layui-hide");
                    } else {
                        $(".IsTopMoney").addClass("layui-hide");
                    }
                } else if (key == "Logic_IsNightFee") {
                    if (val == 1) {
                        $(".Logic_IsNightFee").removeClass("layui-hide");
                    } else {
                        $(".Logic_IsNightFee").addClass("layui-hide");
                    }
                } else if (key == "Logic_SetNightTime") {
                    if (val == 1) {
                        $(".Logic_SetNightTime").addClass("layui-hide");
                    } else {
                        $(".Logic_SetNightTime").removeClass("layui-hide");
                    }
                } else if (key == "Cycle_ReadStart") {
                    var isFreeTime = $("#Cycle_IsFreeTime").val();
                    $("#Cycle_IsFreeTime").find('option').remove();
                    $("#Cycle_StartTime").find('option').remove();

                    if (val == 1) {//重新计费
                        $("#Cycle_IsFreeTime").append('<option value="2"' + (isFreeTime == 2 ? " selected" : "") + '>每次停车只用一次免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="1"' + (isFreeTime == 1 ? " selected" : "") + '>每个周期都用一次免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="4"' + (isFreeTime == 4 ? " selected" : "") + '>每次停车只用一次免费时长(跨周期重置)</option>');

                        $("#Cycle_StartTime").append('<option value="1">入场时间</option>');
                        layuiForm.render("select");
                        $(".Cycle_StartTime").addClass("layui-hide");
                    } else {
                        $("#Cycle_IsFreeTime").append('<option value="2"' + (isFreeTime == 2 ? " selected" : "") + '>每次停车只用一次免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="1"' + (isFreeTime == 1 ? " selected" : "") + '>每个周期都用一次免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="3"' + (isFreeTime == 3 ? " selected" : "") + '>周期内累计免费时长</option>');
                        $("#Cycle_IsFreeTime").append('<option value="4"' + (isFreeTime == 4 ? " selected" : "") + '>每次停车只用一次免费时长(跨周期重置)</option>');

                        $("#Cycle_StartTime").append('<option value="1">入场时间</option>');
                        $("#Cycle_StartTime").append('<option value="0">自定义时间</option>');
                        layuiForm.render("select");
                        $(".Cycle_StartTime").addClass("layui-hide");
                    }
                } else if (key == "FirstTime_Type") {
                    if (val == 1) {
                        $(obj).closest(".layui-col-xs12").find(".firsttimerang").removeClass("layui-hide");
                        $(obj).closest(".layui-col-xs12").find(".firsttimeunit").addClass("layui-hide");
                        firsttime_table.bindEvent($(obj).closest(".layui-col-xs12").find(".firsttime-table").first());
                    } else {
                        $(obj).closest(".layui-col-xs12").find(".firsttimerang").addClass("layui-hide");
                        $(obj).closest(".layui-col-xs12").find(".firsttimeunit").removeClass("layui-hide");
                    }
                }
            }
        }

        //数据缓存
        var dataConfig = {
            _workday_work_data: [{}],
            _workday_holiday_data: [{}],
            _workday_numbertime_data: [{}],
            _workday_spacetime_data: [{}],
            _workday_rangtime_data: [{}],
            _noworkdayNotimepart_data: [{}],
            _noworkdayNotimepart_numbertime_data: {},
            _noworkdayNotimepart_spacetime_data: [{}],
            _noworkdayNotimepart_rangtime_data: [{}],
            _noworkdayTimepart_data: [{}],
        };
        //默认参数值
        var itemConfig = {
            _numbertime: { "FreeMin": "10", "Money": "20", "Cycle": "2", "FreeMin": "5", "InsideMoney": "20", "SpanMin": "5" },
            _spacetime: [{ "FreeMin": "10", "FirstTime": "30", "FirstTime_UnitCharge": "20", "SepMoney": "10", "SepMin": "30", "CycleReCalc": "2", "Section_IsHoursMaxCharge": "2" }],
            _rangtime: [{ "FreeMin": "5", "SpanMin": "5", "FreeMin": "10", "Hours": "12", "ValueMethod": "1", "IsFreeMin": "2", "OverCycleMode": "1", "CycleReCalc": "2", "Section_IsHoursMaxCharge": "2", "ItemList": [{ "Hours": "1", "FreeMin": "0", "Money": "0" }] }],
        }

        function CopyObj(obj1, obj2) {
            if (obj2) {
                for (var i in obj2) {
                    obj1[i] = obj2[i];
                }
            }
            return obj1;
        }
        function GetWebModel(topObj, obj) {
            var itemArray = [];
            if (obj) {
                if (topObj.hasOwnProperty("Logic_Sections"))
                    delete topObj["Logic_Sections"];
                if (topObj.hasOwnProperty("Logic_Sections2"))
                    delete topObj["Logic_Sections2"];
                if (obj.length > 0 && obj[0].Section_ChargeRules) {
                    $.each(obj, function (k, v) {
                        var obj2 = $.parseJSON(v.Section_ChargeRules);
                        if (!obj2) return [{}];
                        if (v.hasOwnProperty("Section_ChargeRules"))
                            delete v["Section_ChargeRules"];
                        obj2 = CopyObj(obj2, topObj);
                        obj2 = CopyObj(obj2, v);
                        itemArray.push(obj2);
                    })
                } else {
                    obj = CopyObj(obj, topObj);
                    itemArray.push(obj);
                }

                return itemArray;
            }

            return obj;
        }


        var firsttime_table = {
            binddata: function (table, data) {
                if (data) {
                    var max = data.length - 1;
                    $.each(data, function (m, n) {
                        switch (m) {
                            case 0:
                                n.desc = "刚刚开始";
                                break;
                            case max:
                                n.desc = '以后每停';
                                break;
                            default:
                                n.desc = "以后再停";
                                break;
                        }
                    })
                } else {
                    data = [];
                }
                $(table).find("tbody").html($("#tmpl_firsttime").tmpl(data))
                firsttime_table.bindEvent(table);
            },
            additem: function (table) {
                var id = $(table).find("tbody>tr").length + 1;
                $(table).find("tbody>tr").each(function (k, v) {
                    if (k != 0) $(v).find(".desc").html("以后再停");
                });
                var item = [{ FirstTime_Id: id, FirstTime_Money: "", FirstTime_Min: "", desc: '以后每停' }];
                $(table).find("tbody").append($("#tmpl_firsttime").tmpl(item));
                firsttime_table.bindEvent(table);
            },
            delitem: function (table, obj) {
                $(obj).closest(".firsttime-tr").remove();
                var ftr = $(table).find("tbody>tr");
                if (ftr.length != 1) {
                    $.each(ftr, function (k, v) {
                        $(v).find(".iptnum").val(k + 1);
                    })
                    $(ftr).last().find(".desc").html('以后每停');
                }
            },
            bindEvent: function (table) {
                $(table).find(".btnadd").unbind("click").click(function () { firsttime_table.additem(table); });
                $(table).find(".btndel").unbind("click").click(function () { firsttime_table.delitem(table, this); });
            }
        };

        var firsttimeRang = {
            binddata: function (table, data) {
                $(table).find(".firstrow").html($("#tmpl_firsttimerang").tmpl(data))
                $(table).find(".firsttimeunit").first().find(".btndel").first().removeClass("btndel").addClass("btnadd").text("添加")
                firsttimeRang.bindEvent(table);
            },
            additem: function (table, timerang) {
                var item = [{ FirstTime_UnitCharge: "", FirstTime_Unit: "" }];
                $(timerang).append($("#tmpl_firsttimerang").tmpl(item));
                firsttimeRang.bindEvent(table);
            },
            delitem: function (table, obj) {
                $(obj).closest(".firsttimeunit").remove();
            },
            bindEvent: function (table) {
                $(table).find(".btnadd").unbind("click").click(function () { firsttimeRang.additem(table, $(this).closest("div.firstrow").first()); });
                $(table).find(".btndel").unbind("click").click(function () { firsttimeRang.delitem(table, this); });
            }
        };
    </script>
    <script>
      var doc = {
            tip1: ["<div style='margin:10px;max-width:350px'><header class='aliyun-docs-view-header'style='padding: 5px; margin: 5px; box-sizing: inherit; color: #fff;'><div class='Header--title--3Yyt2Vx'style='padding: 0px; margin: 12px 0px 0px; box-sizing: inherit;'><h1 style='padding: 0px; margin: 0px; box-sizing: inherit; font-size: 32px; font-weight: 500; color: #fff; word-break: break-word;'data-spm-anchor-id='a2c4g.11186623.0.i0.2be47873rURhx3'>如何设置时间段</h1></div></header><div id='pc-markdown-container'class='pc-markdown-container'style='padding: 0px; margin: 0px; box-sizing: inherit; color: #181818;'><div class='markdown-body'style='padding: 0px; margin: 20px 0px; box-sizing: inherit; text-size-adjust: 100%; color: #fff; overflow: hidden; line-height: 1.6; overflow-wrap: break-word;'><div class='icms-help-docs-content'lang='zh-CN'style='padding: 0px; box-sizing: border-box; margin: 0px !important 0px 0px !important 0px;'><main style='padding: 0px; margin: 0px; box-sizing: border-box;'role='main'><article style='padding: 0px; margin: 0px; box-sizing: border-box;'role='article'aria-labelledby='title_ikh_zvj_1su'><div id='taskbody-p75-pje-j4h'class='body taskbody'style='padding: 0px; margin: 0px; box-sizing: border-box;'><section id='section-73g-119-c8q'class='section'style='padding: 0px; margin: 30px 0px; box-sizing: border-box;'><ol id='steps-bh1-ohz-92q'class='ol steps'style='padding: 0px 0px 0px 17px; margin: 0px 0px 25px; box-sizing: border-box; list-style: none;'><li id='step-7fv-53h-jlz'class='li step stepexpand'style='padding: 0px; margin: 8px 0px; box-sizing: border-box; list-style: decimal;'><span id='cmd-kpl-ri3-abq'class='ph cmd'style='padding: 0px; margin: 0px; box-sizing: border-box;'><span id='uicontrol-yaw-05e-1o2'class='ph uicontrol'style='padding: 0px; margin: 0px; box-sizing: border-box; font-weight: bold;'>时间段</span><br/>停车分时段计费，可设置各段的计费方式。<br/>时间段拆为开始时间和结束时间。<br/>例如：分白天夜间段计费，早上8点到晚上20点为白天，则填08:00-20:00，晚上20点到次日8点为夜间，则填20:00-08:00。</li></ol></section></div></article></main></div></div></div></div>"]
        }


    </script>
    <script>
        function bindTip() {
            $(".help-btn").off('mouseenter').unbind('mouseleave').hover(function () {
                var key = $(this).attr("data-key");
                var data = getHelpContent(key);
                if (data) {
                    layer.tips(data.Description, this, { time: 0, tips: [3, '#090a0c'] });
                }
            }, function () {
                layer.closeAll();
            });

            $(".help-btn-tip").unbind("click").click(function () {
                var key = $(this).attr("data-key");
                var data = getHelpContent(key);
                var htm = "<div style='margin:10px;'>" + data.Description[0] + "</div>";
                if (data) {
                    layer.open({
                        type: 1,
                        title: "简要说明",
                        content: htm,
                        area: ["60%", "60%"],
                        shadeClose: true
                    });
                }
            });
        }

        function getHelpContent(key) {
            var data = {};
            for (var i = 0; i < HelpData.length; i++) {
                if (key == HelpData[i].key) {
                    data = HelpData[i];
                    break;
                }
            }
            if (data.key == null) return null;
            return data;
        }
        //提示信息数据
        var HelpData = [
            {
                key: "Logic_IsFreeTimeCharge",//免费分钟参与计费（停车时长）
                Description: ["例如：当免费分钟为30分钟，停车35分钟；<br/><t class='headTxt'>启用</t>：参与，35分钟都计算停车费用；<br/><t class='headTxt'>禁用</t>：不参与，只计费免费时间后的5分钟费用；"],
            }, {
                key: "Logic_IsCouponTime",//时间优惠券计算免费时间
                Description: ["例如：停车90分钟，领取时间优惠券60分钟，收费标准免费时间为30分钟。<br/><t class='headTxt'>启用</t>：计算免费时间，此次停车免费出场，<br/><t class='headTxt'>禁用</t>：不计算免费时间，此次停车收取90-60=30分钟的费用"],
            }, {
                key: "ChargeRules_Time",//规则有效期
                Description: ["同一个规则可在不同的时间段定义，有效期不要重叠"],
            }, {
                key: "Logic_Across",//跨计时单位拆分
                Description: ["<t class='headTxt'>启用</t>：超出本时段范围（或超出首开始分钟）的部分计时单位停车时长，则按照下一时段计费规则处理；<br/><t class='headTxt'>禁用</t>：超出本时段范围（或超出首开始分钟）的部分停车时长，依然按照上一时段的计费规则处理。"],
            }, {
                key: "Cycle_StartTime",//周期开始时间
                Description: ["<t class='headTxt'>入场时间</t>：停车时长周期从进场后开始累积计算；<br/><t class='headTxt'>自定义时间</t>：停车时长的周期按照每天定义的时间重置，停车费用重新累积计算。<br/><t class='headTxt' style='font-weight:700 !important;'>注意</t>：若周期内多次停车且跨下一个周期时，周期的开始时间节点将不变"],
            }
            , {
                key: "Cycle_IsFreeTime",//免费时长
                Description: ["<t class='headTxt'>每次停车只用一次免费时长</t>：停车周期内，多次停车设置的免费停车时长都有效；<br/><t class='headTxt'>每个周期都用一次免费时长</t>：停车周期内，多次停车设置的免费停车时长只有第一次有效；<br/><t class='headTxt'>周期内累计免费时长</t>：停车周期内，累计多次停车使用的免费时长，如周期累计的免费时长大于或等于当前时段设置的免费时长，则当前停车时段的免费时长为0；<br/><t class='headTxt'>每次停车只用一次免费时长(跨周期重置)</t>：每次车辆入场停车都有免费时长，而且停车时长跨周期时又有免费时长。"],
            }, {
                key: "Cycle_IsFirstSection",//首段时长
                Description: ["<t class='headTxt'>每次停车只用一次首段时长</t>：停车周期内，多次停车设置的首段停车时长都有效；<br/><t class='headTxt'>每个周期都用一次首段时长</t>：停车周期内，多次停车设置的首段停车时长只有第一次有效；<br/><t class='headTxt'>每次停车只用一次首段时长(跨周期重置)</t>：每次车辆入场停车都有首段时长，而且停车时长跨周期时又有首段时长。"],
            }, {
                key: "Workday_IsMaxCharge",//工作日一天最高收费
                Description: ["<t class='headTxt'>设置启用</t>：当前停车，不同的时段计费总费用不能超过设置的最高收费金额；<br/><t class='headTxt'>不设置</t>：不会限制不同时段的计费总金额。"],
            }, {
                key: "Holiday_IsMaxCharge",//节假日一天最高收费
                Description: ["<t class='headTxt'>启用</t>：当前停车，不同的时段计费总费用不能超过设置的最高收费金额；<br/><t class='headTxt'>不设置</t>：不会限制不同时段的计费总金额。"],
            }, {
                key: "tip1",
                Description: doc.tip1,
                type: 1
            }, {
                key: "Logic_IsDiffHoliday",
                Description: ["可在《<span style='color:red;'> 日期设置 </span>》里自定义工作日或节假日。若某一天没有定义工作日或节假日，则按照默认处理（<span style='color:red;'>默认星期一至星期五为工作日，星期六星期日为节假日</span>）"],
            }, {
                key: "Logic_IsFullTimeCharge",//满计时单位计费
                Description: ["<t class='headTxt'>开启</t>：停车时长<span style='color:red;'>满一个计时单位</span>才计算费用；<br/><t class='headTxt'>禁用</t>：停车时长<span style='color:red;'>只要超出一分钟就会当做一个计时单位</span>来计算费用。<br/>例如：停车按<span style='color:red;'>2元每30分钟</span>计费（这里的一个计时单位为30分钟），若启用，停车59分钟则只按<span style='color:red;'>1个计时单位</span>计费，收费共2元；若禁用则按照<span style='color:red;'>2个计时单位</span>计费，收费共4元。"],
            }, {
                key: "Logic_CycleReCalc",//封顶金额
                Description: ["当前一次停车累计小时最高封顶收费，若停车时长超过设置的累计小时数，会优先对停车时段进行时间段拆分，如果按次或按时长范围收费，会累加多次费用，请谨慎设置。<br/>例如：车辆停车时间段为<span style='color:red;'>2022-02-08 08:00:00 - 2022-02-08 12:00:00</span>，设置累计小时数为2小时，最高收费10元，则将停车时长拆分为两个时间段(<span style='color:red;'>2022-02-08 08:00:00-2022-02-08 10:00:00</span> 和 <span style='color:red;'>2022-02-08 10:00:00-2022-02-08 12:00:00</span>)按照计费规则进行计费，并且这两个时间段的应收最高费用为10元"],
            }, {
                key: "Cycle_IsFreeTimeUseCycle",//未产生费用不计入周期
                Description: ["当前停车出场计费时，未产生费用，周期不会变更。例：停车1个小时内免费，若车辆停车时段为2022-02-08 08:00:00 - 2022-02-08 08:35:00，则不收费，也不会记录周期；若车辆停车时段为2022-02-08 08:00:00 - 2022-02-08 09:35:00，产生费用，则开始记录周期。"],
            }, {
                key: "Logic_FreeMin",//免费分钟
                Description: ["停车时长在免费分钟内的，免费离场"],
            }, {
                key: "IsFreeMin",//每次都有免费分钟
                Description: ["<t class='headTxt'>启用</t>：每次使用该时段计费，都会有当前计费时段设置的免费分钟。例如：当前时段的时间设置为00:00 - 23:59，免费分钟设为10分钟，车辆停车时段为2022-02-08 08:00:00 - 2022-02-09 08:00:00，计费时，该车辆使用了两次该时段的计费规则进行计费分别为：<span class='blue'>2022-02-08 08:00:00</span> - <span class='blue'>2022-02-08 23:59:59</span> 和 <span class='blue'>2022-02-09 00:00:00</span> - <span class='blue'>2022-02-09 08:00:00</span>；<span style='color:red;'>若禁用，只有<span class='blue'>2022-02-08 08:00:00 - 2022-02-08 23:59:59</span>有10分钟的免费时间，若启用则两段都有10分钟的免费时间</span>。"],
            }, {
                key: "Logic_IsOnlyFreeTime",//跨区域仅使用一次免费分钟
                Description: ["<t class='headTxt'>启用</t>：停车跨多个区域统一共用设置的免费分钟，用完即止；<br/><t class='headTxt'>禁用</t>：各个区域设置的免费分钟按区域设置独立使用。"],
            }, {
                key: "Logic_IsTopMoney",//当次停车最高收费
                Description: ["当前一次停车累计收费金额不能超过当前设置的最高收费金额。"],
            }, {
                key: "IsEveryFirstTime",//每次都有首开始分钟
                Description: ["<t class='headTxt'>启用</t>：每次使用该时段计费，都会有当前计费时段设置的首开始分钟。例如：当前计费时段的设置分别为00:00 - 08:00、08:00 - 00:00，首开始分钟均设置为60分钟，车辆停车时段为2022-02-08 08:00:00 - 2022-02-09 08:00:00，计费时，该车辆使用了两次该时段的计费规则进行计费分别为：<span class='blue'>2022-02-08 08:00:00</span> - <span class='blue'>2022-02-08 23:59:59</span> 和 <span class='blue'>2022-02-09 00:00:00</span> - <span class='blue'>2022-02-09 08:00:00</span>；<span style='color:red;'>若禁用，只有<span class='blue'>2022-02-08 08:00:00 - 2022-02-08 23:59:59</span>有60分钟的首开始分钟，若启用则两段都有60分钟的首开始分钟</span>。"],
            }, {
                key: "Logic_IsNightFee",//跨天加收过夜费
                Description: ["<t class='headTxt'>启用</t>：在停车跨过指定时间点时另外加收过夜费；如果车辆在免费时间内跨过指定时间点直接免费出场不加收过夜费；每日封顶金额不会统计加收的过夜费金额；启用了周期收费过夜费会累计到周期限额中"],
            }, {
                key: "Cycle_ReadStart",//周期类型
                Description: ["<span class='blue'>多次进出场累计计费</span>，是指周期内车辆多次进出累计计费金额；<span class='blue'>每次停车重新计费</span>，是指周期内车辆每次进场重新计费。"],
            }, {
                key: "Logic_IsOnlyFirstTime",//跨区域仅使用一次首计时
                Description: ["<t class='headTxt'>启用</t>：停车跨多个区域统一只使用一次首计时；<br/><t class='headTxt'>禁用</t>：各个区域设置的首计时按区域设置多次使用。"],
            }, {
                key: "FirstTime_Type",//首开始分钟计费类型
                Description: ["<t class='headTxt'>按时长范围</t>：则将首开始分钟拆分成多个时段叠加计费；<br/><t class='headTxt'>按计费单位</t>：则以*元/*分钟计费"],
            }, {
                key: "Logic_MergeSectionTime",//跨区域同时间段合并计费
                Description: ["<t class='headTxt'>启用</t>：车辆进出车场区域（大小车场），同一个白天段或黑夜段的停车时间合并到一起计费；<br/><t class='headTxt'>禁用</t>：不合并计费。"],
            }, {
                key: "Cycle_HoursMaxAmount",//按停车时长封顶金额
                Description: ["<t class='headTxt'>启用</t>：停车计费金额不能超出周期最大封顶金额（停车时长除以周期时长乘以周期封顶金额）；<br/><t class='headTxt'>禁用</t>：按照实际停车时长计费和周期累计封顶金额收费，停车时长跨周期而又不满一个周期时长的，会存在收费超出一个周期封顶金额的情况。"],
            }, {
                key: "Logic_NestedRecords",//多区域嵌套记录严格匹配
                Description: ["<t class='headTxt'>启用</t>：嵌套区域有进场没出场记录按当前记录的上级区域计费；<br/><t class='headTxt'>禁用</t>：嵌套区域有进场没出场记录按当前计费区域计费。"],
            }, {
                key: "Logic_PayLeaveMin",//场内支付滞留时间
                Description: ["车主在场内提前缴费后，在场内的免费停留时间。<br/><t class='headTxt'>举例</t>：设置15分钟，则代表缴费后在15分钟内免费出场"],
            }, {
                key: "Logic_MaxDailyAmount",//每日最高收费限额
                Description: ["停车24小时最高收费限额"],
            }, {
                key: "Logic_IsDiffTime",//时段类型
                Description: ["<t class='headTxt'>不分时段</t>：用于不分时段停车时长收费的场景。<br/><t class='headTxt'>分时段</t>：用于停车时长按不同时间点收费并且收费不一样的场景。可设置各段的计费方式。时间段拆为开始时间和结束时间。<br/><t class='headTxt'>例如 </t>：分时段，分白天夜间段计费，早上8点到晚上20点为白天，则填08:00-20:00，晚上20点到次日8点为夜间，则填20:00-08:00。"]
            }
        ];
    </script>

    <script>
        $(".btnCombox ul li").click(function () {
            if ($(this).hasClass("select")) return;
            var idName = $(this).parent().parent().attr("id");
            if (idName == "ChargeRules_Type") {

                if ($(this).attr("data-value") == "0") { layer.msg("跳转中", { icon: 16, time: 0 }); window.location.href = "StandardRule"; }
                if ($(this).attr("data-value") == "1") { layer.msg("跳转中", { icon: 16, time: 0 }); window.location.href = "TempRule"; }
                if ($(this).attr("data-value") == "2") { layer.msg("跳转中", { icon: 16, time: 0 }); window.location.href = "YinChuanRule"; }

            }
        });

    </script>
</body>
</html>
