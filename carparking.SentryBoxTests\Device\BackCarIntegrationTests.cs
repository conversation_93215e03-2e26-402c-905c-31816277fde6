using Microsoft.VisualStudio.TestTools.UnitTesting;
using carparking.SentryBox.BarrierDevice;
using carparking.BLL.Cache;
using carparking.Model;
using carparking.SentryBoxTests.TestHelpers;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Concurrent;
using Newtonsoft.Json;

namespace carparking.SentryBoxTests.Device
{
    /// <summary>
    /// 倒车检测集成测试
    /// </summary>
    [TestClass]
    public class BackCarIntegrationTests
    {
        private const string TestPasswayNo = "P001";
        private const string TestCarNo = "粤A12345";
        private const string TestOrderNo = "123456789-A12345";

        [TestInitialize]
        public void Setup()
        {
            MockHelper.InitializeBasicCache();
            SetupTestPassway();
        }

        [TestCleanup]
        public void Cleanup()
        {
            MockHelper.CleanupCache();
        }

        /// <summary>
        /// 设置测试车道
        /// </summary>
        private void SetupTestPassway()
        {
            var passway = new PasswayExt
            {
                Passway_No = TestPasswayNo,
                Passway_Name = "测试车道",
                Passway_IsBackCar = 1, // 启用倒车检测
                Passway_EnableBoard = 1, // 启用控制板
                Passway_SameInOut = 0, // 非同进同出
                Passway_OutNo = "P002"
            };

            AppBasicCache.GetSentryPasswayDic.TryAdd(TestPasswayNo, passway);
        }

        /// <summary>
        /// 测试完整的倒车缓存流程：存储 -> 读取 -> 处理
        /// </summary>
        [TestMethod]
        public void BackCarFlow_CompleteProcess_ShouldWork()
        {
            // Arrange - 创建测试订单
            var parkOrder = new ParkOrder
            {
                ParkOrder_No = TestOrderNo,
                ParkOrder_CarNo = TestCarNo,
                ParkOrder_EnterTime = DateTime.Now,
                ParkOrder_StatusNo = 200,
                ParkOrder_EnterImgPath = "/test/image.jpg"
            };

            // Step 1: 模拟车辆入场时存储缓存
            var cacheKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, TestCarNo);
            var cacheOptions = new MemoryCacheEntryOptions()
            {
                SlidingExpiration = TimeSpan.FromMinutes(15),
                Priority = CacheItemPriority.High,
                Size = 1
            };

            AppBasicCache.GetMemoryCache.Set(cacheKey, parkOrder, cacheOptions);

            // Step 2: 验证缓存存储成功
            var cacheStored = AppBasicCache.GetMemoryCache.TryGetValue(cacheKey, out var cachedValue);
            Assert.IsTrue(cacheStored, "缓存应该存储成功");
            Assert.IsNotNull(cachedValue, "缓存值不应该为null");

            // Step 3: 模拟倒车事件处理 - 使用车牌号读取缓存
            var retrievedByCarNo = AppBasicCache.GetMemoryCache.TryGetValue(cacheKey, out var temp1);
            ParkOrder innerParkOrder = null;

            if (retrievedByCarNo && temp1 != null && temp1 is ParkOrder temp2)
            {
                innerParkOrder = temp2;
                AppBasicCache.GetMemoryCache.Remove(cacheKey);
            }

            // Assert
            Assert.IsTrue(retrievedByCarNo, "应该能通过车牌号读取到缓存");
            Assert.IsNotNull(innerParkOrder, "应该能获取到订单信息");
            Assert.AreEqual(TestOrderNo, innerParkOrder.ParkOrder_No, "订单号应该匹配");
            Assert.AreEqual(TestCarNo, innerParkOrder.ParkOrder_CarNo, "车牌号应该匹配");

            // 验证缓存已被移除
            var cacheAfterRemoval = AppBasicCache.GetMemoryCache.TryGetValue(cacheKey, out _);
            Assert.IsFalse(cacheAfterRemoval, "缓存应该已被移除");
        }

        /// <summary>
        /// 测试订单号后缀匹配的倒车流程
        /// </summary>
        [TestMethod]
        public void BackCarFlow_OrderNoSuffixMatching_ShouldWork()
        {
            // Arrange
            var parkOrder = new ParkOrder
            {
                ParkOrder_No = TestOrderNo,
                ParkOrder_CarNo = TestCarNo,
                ParkOrder_EnterTime = DateTime.Now,
                ParkOrder_StatusNo = 200
            };

            // 使用车牌号存储缓存
            var cacheKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, TestCarNo);
            var cacheOptions = new MemoryCacheEntryOptions()
            {
                SlidingExpiration = TimeSpan.FromMinutes(15),
                Priority = CacheItemPriority.High,
                Size = 1
            };

            AppBasicCache.GetMemoryCache.Set(cacheKey, parkOrder, cacheOptions);

            // Act - 使用订单号后缀读取缓存（模拟Y312设备上报的倒车事件）
            var orderSuffix = TestOrderNo.Split('-')[1]; // A12345
            var orderBasedKey = $"{TestPasswayNo}{orderSuffix}";

            var retrievedByOrderSuffix = AppBasicCache.GetMemoryCache.TryGetValue(orderBasedKey, out var temp1);

            // Assert
            Assert.IsTrue(retrievedByOrderSuffix, "应该能通过订单号后缀读取到缓存");
            Assert.IsNotNull(temp1, "缓存值不应该为null");
            Assert.IsInstanceOfType(temp1, typeof(ParkOrder), "缓存值应该是ParkOrder类型");

            var retrievedOrder = (ParkOrder)temp1;
            Assert.AreEqual(TestOrderNo, retrievedOrder.ParkOrder_No, "订单号应该匹配");
        }

        /// <summary>
        /// 测试同进同出车道的倒车处理
        /// </summary>
        [TestMethod]
        public void BackCarFlow_SameInOutPassway_ShouldWork()
        {
            // Arrange - 设置同进同出车道
            var inPassway = new PasswayExt
            {
                Passway_No = TestPasswayNo,
                Passway_Name = "测试入车道",
                Passway_IsBackCar = 1,
                Passway_EnableBoard = 1,
                Passway_SameInOut = 1, // 同进同出
                Passway_OutNo = "P002"
            };

            var outPassway = new PasswayExt
            {
                Passway_No = "P002",
                Passway_Name = "测试出车道",
                Passway_IsBackCar = 1,
                Passway_EnableBoard = 1,
                Passway_SameInOut = 1,
                Passway_OutNo = "P002"
            };

            AppBasicCache.GetSentryPasswayDic.TryAdd(TestPasswayNo, inPassway);
            AppBasicCache.GetSentryPasswayDic.TryAdd("P002", outPassway);

            var parkOrder = new ParkOrder
            {
                ParkOrder_No = TestOrderNo,
                ParkOrder_CarNo = TestCarNo,
                ParkOrder_EnterTime = DateTime.Now,
                ParkOrder_StatusNo = 200
            };

            // 在出车道缓存中存储订单
            var outCacheKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey("P002", TestCarNo);
            var cacheOptions = new MemoryCacheEntryOptions()
            {
                SlidingExpiration = TimeSpan.FromMinutes(15),
                Priority = CacheItemPriority.High,
                Size = 1
            };

            AppBasicCache.GetMemoryCache.Set(outCacheKey, parkOrder, cacheOptions);

            // Act - 模拟在入车道查找缓存失败后，尝试在出车道查找
            var inCacheKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, TestCarNo);
            var foundInInPassway = AppBasicCache.GetMemoryCache.TryGetValue(inCacheKey, out _);
            
            ParkOrder innerParkOrder = null;
            if (!foundInInPassway && inPassway.Passway_SameInOut == 1)
            {
                // 尝试在出车道查找
                var foundInOutPassway = AppBasicCache.GetMemoryCache.TryGetValue(outCacheKey, out var temp3);
                if (foundInOutPassway && temp3 != null && temp3 is ParkOrder temp4)
                {
                    innerParkOrder = temp4;
                }
            }

            // Assert
            Assert.IsFalse(foundInInPassway, "入车道缓存中不应该有数据");
            Assert.IsNotNull(innerParkOrder, "应该能从出车道缓存中找到订单");
            Assert.AreEqual(TestOrderNo, innerParkOrder.ParkOrder_No, "订单号应该匹配");
        }

        /// <summary>
        /// 测试缓存键为空的异常情况
        /// </summary>
        [TestMethod]
        public void BackCarFlow_EmptyCacheKey_ShouldHandleGracefully()
        {
            // Arrange
            var emptyCarNo = "";
            var nullCarNo = (string)null;

            // Act & Assert
            var emptyKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, emptyCarNo);
            var nullKey = BarrierDeviceUtilsl.GenerateBackCarCacheKey(TestPasswayNo, nullCarNo);

            Assert.AreEqual(string.Empty, emptyKey, "空车牌号应该返回空缓存键");
            Assert.AreEqual(string.Empty, nullKey, "null车牌号应该返回空缓存键");
        }
    }
}
