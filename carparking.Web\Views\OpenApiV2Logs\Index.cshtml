﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>开放接口日志</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet"/>
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px; float: left; font: normal normal normal 14px/1 FontAwesome; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

<div class="layui-fluid animated fadeInRight">
    <div class="layui-card layadmin-header height0"></div>
    <div class="layui-card layadmin-header">
        <div class="layui-breadcrumb" lay-filter="breadcrumb">
            <a>
                <cite>系统管理</cite>
            </a>
            <a>
                <cite>开放接口日志</cite>
            </a>
        </div>
    </div>
    <div class="layui-row layui-col-space30">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header layui-form" id="searchForm">
                    <div class="layui-row">
                        <div class="layui-inline">
                            <select lay-search id="DataFlow" name="DataFlow">
                                <option value="">数据类型</option>
                                <option value="0">未定义</option>
                                <option value="1">上行数据</option>
                                <option value="2">下行数据</option>
                                <option value="3">MQTT数据</option>
                            </select>
                        </div>
                        <div class="layui-inline">
                            <input class="layui-input " name="RequestId" id="RequestId" autocomplete="off" placeholder="操作标识"/>
                        </div>
                        <div class="layui-inline">
                            <input class="layui-input " name="RequestData" id="RequestData" autocomplete="off" placeholder="请求内容关键字"/>
                        </div>
                        <div class="layui-inline">
                            <input class="layui-input " name="StartTime" id="StartTime" autocomplete="off" placeholder="记录开始时间" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")'/>
                        </div>
                        <div class="layui-inline">
                            <input class="layui-input " name="EndTime" id="EndTime" autocomplete="off" placeholder="记录结束时间" value='@DateTime.Now.ToString("yyyy-MM-dd 23:59:59")'/>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn" id="Search">
                                <i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="layui-card-body">
                    <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                    <script type="text/html" id="toolbar_btns">
                        <div class="layui-btn-container">
                        </div>
                    </script>

                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
<script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
<script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
<script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
<script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
<script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
<script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
<script>
        var comtable = null;
                layui.use(['table', 'form', 'laydate'], function () {
                   
                    pager.init();
                    var table = layui.table;
                    $("#StartTime").val(new Date().Format("yyyy-MM-dd 00:00:00"));
                    $("#EndTime").val(new Date().Format("yyyy-MM-dd 23:59:59"));
                    _DATE.bind(layui.laydate, ["StartTime", "EndTime"], { type: 'datetime', range: true });
                    var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                    pager.conditionParam = conditionParam;
        
                    var cols = [[
                         { field: 'RequestIdStr', width:250, title: '操作标识'}
                        , {
                            field: 'DataFlow', title: '数据类型', width:100, templet: function (d) {
                                //0未定义 1上行数据 2下行数据
                                if (d.DataFlow === 1) return tempBar(1, "上行数据");
                                else if (d.DataFlow === 2) return tempBar(6, "下行数据");
                                else if (d.DataFlow === 3) return tempBar(2, "MQTT数据");
                                else return tempBar(0, "未定义");
                            }
                        }
                        , { field: 'RouteType', title: '路由类型'}
                        , { field: 'RequestData', title: '请求内容' }
                        , { field: 'ResponseData', title: '响应内容' }
                        , { field: 'RequestTime', title: '操作时间'}
                        , {
                            field: 'Status', title: '操作状态', templet: function (d) {
                                //200操作成功 1001签名校验不通过 1002请求参数不正确 1003接口服务处理异常，请联系管理员 1004接口服务未启用 1005预料之中的错误，以输出的实际提示信息为准
                                if (d.Status === 200) return tempBar(2, "操作成功");
                                else if (d.Status === 1001) return tempBar(0, "签名校验不通过");
                                else if (d.Status === 1002) return tempBar(3, "请求参数不正确");
                                else if (d.Status === 1003) return tempBar(6, "接口服务处理异常，请联系管理员");
                                else if (d.Status === 1004) return tempBar(5, "接口服务未启用");
                                else if (d.Status === 1005) return tempBar(6, "预料之中的错误，以输出的实际提示信息为准");
                                else return tempBar(4, "正在处理中(值:"+d.Status+")");
                            }
                        }
                    ]];
                    cols = tb_page_cols(cols);
        
                    comtable = table.render({
                        elem: '#com-table-base'
                        , url: '/OpenApiV2Logs/QueryLogs'
                        , method: 'post'
                        , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                        , cols: cols
                        , page: {
                            layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                            groups: 3,
                        }
                        , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                        , where: { conditionParam: JSON.stringify(conditionParam) }
                        , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                        , done: function (d) {
                            pager.dataCount = d.count;
                            tb_page_set(d);
                        }
                    });
                });
    </script>
<script>
        var pager = {
                    pageIndex: 1,
                    conditionParam: null,
                    dataCount: 0,
                    init: function () {
                        $.ajaxSettings.async = false;
                        pager.bindPower();
                        pager.bindSelect();
                        pager.bindEvent();
                        $.ajaxSettings.async = true;
                    },
                    //重新加载数据
                    bindSelect: function () {
                    },
                    bindData: function (index) {
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                        pager.conditionParam = conditionParam;
                        comtable.reload({
                            url:  '/OpenApiV2Logs/QueryLogs'
                            , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                            , page: { curr: index }
                        });
                    },
                    bindEvent: function () {
                        $("#Search").click(function () { pager.bindData(1); });
                    },
                    bindPower: function () {
                        window.parent.global.getBtnPower(window, function (pagePower) { });
                    }
                }
                $(function () { pager.bindSelect(); });
    </script>
</body>
</html>