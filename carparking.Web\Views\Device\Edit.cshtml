﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row {
            margin-bottom: 10px;
        }

        .layui-select-title input {
            color: #0094ff;
        }

        .layui-collapse {
            border: 0;
            height: auto;
            position: relative;
        }

        .layui-colla-content {
            padding: 0;
            border: 0;
        }

        .btnCombox ul li {
            margin-bottom: 2px;
        }

        .label-desc {
            color: #646161;
            clear: both;
        }

        .edit-ipt-ban .input-container {
            position: relative;
            width: 100%;
        }

            .edit-ipt-ban .input-container input {
                width: 100%;
                padding-right: 100px;
                /* 为按钮留出空间 */
            }

            .edit-ipt-ban .input-container button {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                background-color: #0F9EE9;
                z-index: 1;
                font-size: 14px;
                /* 增大按钮字体大小 */
            }

                .edit-ipt-ban .input-container button i {
                    font-size: 14px;
                    /* 调整图标大小以匹配文字 */
                }

        /* 修复设备编码输入框样式 */
        .edit-ipt-ban .device-code-container {
            position: relative;
            width: 100%;
        }

            .edit-ipt-ban .device-code-container input {
                width: 100%;
                padding-right: 30px;
                /* 为清除按钮留出空间 */
            }

            .edit-ipt-ban .device-code-container .input-clear-icon {
                position: absolute !important;
                right: 10px !important;
                top: 50% !important;
                transform: translateY(-50%) !important;
                cursor: pointer;
                color: #999 !important;
                z-index: 2;
                margin-top: 0 !important;
            }

        /* YM01设备编码格式说明样式优化 */
        .viz-cloud-format {
            margin-top: 5px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-left: 3px solid #0F9EE9;
            border-radius: 3px;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>

<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>

            <div class="layui-row">
                <div class="layui-col-xs2 edit-label">设备类型</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox falsemodify" id="Device_Category">
                        <ul>
                            <li data-value="1" class="select">车牌识别相机</li>
                            <li data-value="4">自助停车设备</li>
                            <li data-value="6">智慧道闸</li>
                            <li data-value="7">场景相机</li>
                            <li data-value="3">显示屏</li>
                            <li data-value="9">非机动车控制器</li>
                            <li data-value="10">二维码设备</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="layui-row xj">
                <div class="layui-col-xs2 edit-label">主辅相机</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_IO">
                        <ul>
                            <li data-value="0">辅助相机</li>
                            <li data-value="1" class="select">主相机</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="layui-row layui-hide xj xsp zztc kzb qjxj ezviz ewm c08">
                <div class="layui-col-xs2 edit-label">所属车道</div>
                <div class="layui-col-xs8 edit-ipt-ban" id="divPassway">
                    <select class="layui-input" id="Device_PasswayNo" name="Device_PasswayNo" lay-search>
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>

            <div class="layui-row layui-hide c08">
                <div class="layui-col-xs2 edit-label"></div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="label-desc" style="float:left;">选择车道仅用作表示当前显示屏关联车道的岗亭</div>
                </div>
            </div>

            <div class="layui-row layui-hide xj xsp zztc kzb qjxj ezviz c08">
                <div class="layui-col-xs2 edit-label">设备型号</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <select class="layui-input" id="Device_DriveNo" name="Device_DriveNo" lay-search>
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs2 edit-label">设备名称</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-nameSpecial" id="Device_Name" name="Device_Name"
                        maxlength="32" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs2 edit-label">设备编码</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="device-code-container">
                        <input type="text" class="layui-input v-null v-no v-minlen" data-minlen="8" maxlength="32"
                               id="Device_No" name="Device_No" placeholder="可输入数字、字母、下划线、横线,8-32位" />
                    </div>
                    <div class="label-desc layui-hide viz-cloud-format">
                        YM01网络录像机设备编码格式：设备编号_视频通道号（如：a1b2-c3d4_1）
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row layui-hide xj zztc kzb qjxj ewm xsp c08">
                <div class="layui-col-xs2 edit-label">设备IP</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="input-container">
                        <input type="text" class="layui-input v-ip" id="Device_IP" name="Device_IP" maxlength="16" />
                        <button type="button" class="layui-btn layui-btn-sm" id="btnSelectDevice"
                            name="btnSelectDevice">
                            <i class="layui-icon layui-icon-edit"></i>选择设备
                        </button>
                    </div>
                </div>
            </div>

            <!-- 非机动车控制器联动相机选项 -->
            <div class="layui-row layui-hide fjdc">
                <div class="layui-col-xs2 edit-label">联动相机</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <select class="layui-input" id="Device_FNo" name="Device_FNo" lay-search>
                        <option value="">请选择</option>
                    </select>
                </div>
            </div>

            <!-- 非机动车控制器相机用途选项 -->
            <div class="layui-row layui-hide fjdc">
                <div class="layui-col-xs2 edit-label">相机用途</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_CameraUsage">
                        <ul>
                            <li data-value="0" class="select">仅抓图</li>
                            <li data-value="1">抓图+播报</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-hide x c08">
                <div class="layui-col-xs2 edit-label">屏显模板</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="input-container" style="display: flex; align-items: center;">
                        <div style="flex: 1;">
                            <select class="layui-input" id="Device_DisplayTemplateId" name="Device_DisplayTemplateId" lay-search>
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <button type="button" class="layui-btn layui-btn-sm layui-hide" id="refreshDisplayTemplate" style="margin-left: 10px;">
                            <i class="layui-icon layui-icon-refresh"></i>
                        </button>
                    </div>
                    <div class="label-desc" style="float:left;padding-top:6px;">
                        需在“车场管理”->“屏显模板”模块下进行创建
                    </div>
                </div>
            </div>

            <div class="layui-row layui-hide c08">
                <div class="layui-col-xs2 edit-label">单模组宽度</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-number" id="Device_C08Width" name="Device_C08Width"
                           maxlength="4" />
                </div>
            </div>

            <div class="layui-row layui-hide c08">
                <div class="layui-col-xs2 edit-label">单模组高度</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input v-number" id="Device_C08Height" name="Device_C08Height"
                           maxlength="4" />
                </div>
            </div>

            <div class="layui-row layui-hide ezviz appezviz ezviz-nvr">
                <div class="layui-col-xs2 edit-label">视频设备类型</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_VideoDevType">
                        <ul>
                            <li data-value="1">IPC</li>
                            <li data-value="0" class="select">NVR</li>
                        </ul>
                    </div>
                    <div class="label-desc" style="float:left;padding-top:6px;">
                        NVR支持查看本地回放，IPC仅支持查看云存储回放（需用户在萤石云购买云存储）
                    </div>
                </div>
            </div>

            <div class="layui-row layui-hide ezviz appezviz ezviz-nvr viz-cloud">
                <div class="layui-col-xs2 edit-label">视频通道号</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <input type="text" class="layui-input  v-null v-number" id="Device_VideoChannelNo"
                        name="Device_VideoChannelNo" maxlength="10" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row layui-hide xj">
                <div class="layui-col-xs2 edit-label">设备开闸IO</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_InIO">
                        <ul>
                            <li data-value="0" class="select">OUT1</li>
                            <li data-value="1">OUT2</li>
                        </ul>
                    </div>
                    <div class="label-desc" style="float:left;padding-top:6px;">(选中的表示接入开闸,未选中的表示接入关闸)</div>
                </div>
            </div>

            <div class="layui-row layui-hide xj">
                <div class="layui-col-xs2 edit-label">485输出</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_Com">
                        <ul>
                            <li data-value="0">A1B1</li>
                            <li data-value="1" class="select">A2B2</li>
                        </ul>
                    </div>
                    <div class="label-desc remark_15" style="float:left;padding-top:6px;">
                        HR相机选择485输出A2B2，HM相机选择485输出A1B1
                    </div>
                </div>
            </div>

            <div class="layui-row layui-hide xsp">
                <div class="layui-col-xs2 edit-label">通讯模式</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_CommunicationMode">
                        <ul>
                            <li data-value="0" class="select">相机485</li>
                            <li data-value="1">网口UDP</li>
                            <li data-value="2">网口TCP</li>
                        </ul>
                    </div>
                    <div class="label-desc " style="float:left;padding-top:6px;">
                        相机485: 显示屏连接相机485端口通讯; 网口UDP:
                        显示屏连接网线通过UDP协议通讯; 网口TCP: 显示屏连接网线通过TCP协议通讯
                    </div>
                </div>
            </div>

            <div class="layui-row layui-hide xj video06">
                <div class="layui-col-xs2 edit-label">视频模式</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox" id="Device_VideoMode">
                        <ul>
                            <li data-value="1">启用</li>
                            <li data-value="0" class="select">禁用</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="layui-collapse">
                <div class="layui-colla-item zztc xj kzb qjxj xsp ezviz ewm c08">
                    <h2 class="layui-colla-title layui-col-xs-offset2 layui-col-xs8">更多</h2>
                    <div class="layui-colla-content">
                        <div class="layui-row layui-hide xj zztc kzb qjxj ewm xsp">
                            <div class="layui-col-xs2 edit-label">设备端口号</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-number" id="Device_Port" name="Device_Port"
                                    maxlength="5" autocomplete="off" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide zztc">
                            <div class="layui-col-xs2 edit-label">设备TCP端口</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-number" id="Device_TcpPort"
                                    name="Device_TcpPort" maxlength="5" autocomplete="off" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide zztc">
                            <div class="layui-col-xs2 edit-label">蓝牙编号</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input" id="Device_BlueToothNo" name="Device_BlueToothNo"
                                    maxlength="32" autocomplete="off" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xj qjxj viz-cloud">
                            <div class="layui-col-xs2 edit-label">连接账号</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-numen" id="Device_Account" name="Device_Account"
                                    maxlength="32" autocomplete="off" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xj qjxj viz-cloud">
                            <div class="layui-col-xs2 edit-label">连接密码</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="password" class="layui-input" id="Device_Pwd" name="Device_Pwd"
                                    maxlength="32" autocomplete="off" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xj">
                            <div class="layui-col-xs2 edit-label">视频排序</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-number v-null" id="Device_ScreenNum"
                                    name="Device_ScreenNum" maxlength="4" value="1" />
                            </div>
                            <div class="layui-col-xs1 red-mark">*</div>
                        </div>

                        <div class="layui-row layui-hide zsxj">
                            <div class="layui-col-xs2 edit-label">视频流监控</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox" id="Device_Style">
                                    <ul>
                                        <li data-value="0" class="select">禁用</li>
                                        <li data-value="1">启用</li>
                                    </ul>
                                </div>
                                <div class="label-desc" style="float:left;padding-top:6px;">(后台服务岗亭)</div>
                            </div>
                        </div>

                        <div class="layui-row layui-hide zsxj">
                            <div class="layui-col-xs2 edit-label">应用AppID</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input" id="Device_AppID" name="Device_AppID"
                                       placeholder="不填写则使用默认的AppID" maxlength="50" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide zsxj ezviz">
                            <div class="layui-col-xs2 edit-label">应用AppKey</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input" id="Device_AppKey" name="Device_AppKey"
                                       placeholder="不填写则使用默认的AppKey" maxlength="50" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">移动速度</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <select class="layui-select" id="Device_Speed" name="Device_Speed" lay-search>
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5" selected>5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">移动方式</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox btn-min" id="Device_MoveMode">
                                    <ul>
                                        <li data-value="1" class="select">连续左移</li>
                                        <li data-value="2">连续右移</li>
                                        <li data-value="3">连续上移</li>
                                        <li data-value="4">连续下移</li>
                                        <li data-value="5">立即打出</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">显示颜色</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox" id="Device_Color">
                                    <ul>
                                        <li data-value="1" class="select">红色</li>
                                        <li data-value="2">绿色</li>
                                        <li data-value="3">黄色</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">上下行显示</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <div class="btnCombox" id="Device_UpDownShow">
                                    <ul>
                                        <li data-value="1">上行显示</li>
                                        <li data-value="2" class="select">下行显示</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">显示时间</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-number v-max v-min" min="60" max="120"
                                    maxlength="3" value="100" id="Device_ShowDuration" name="Device_ShowDuration" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">静止时间</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-number v-max v-min" min="0" max="200"
                                    maxlength="3" value="150" id="Device_StopDuration" name="Device_StopDuration" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">车位数前缀</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input" id="Device_ScreenBody" name="Device_ScreenBody"
                                    maxlength="6" value="空车位:" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide xsp">
                            <div class="layui-col-xs2 edit-label">机号</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input v-number v-max v-min" id="Device_CtrlNo"
                                    name="Device_CtrlNo" min="1" max="30" maxlength="2" value="1" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide ezviz appezviz">
                            <div class="layui-col-xs2 edit-label">Secret</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <input type="text" class="layui-input" id="Device_Secret" name="Device_Secret"
                                       placeholder="不填写则使用默认的Secret" maxlength="32" autocomplete="off" />
                            </div>
                        </div>

                        <div class="layui-row layui-hide c08">
                            <div class="layui-col-xs2 edit-label">控制卡类型</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <select class="layui-input" id="Device_C08Type" name="Device_C08Type" lay-search>
                                    <option value="0">T,A,U,XC,W</option>
                                    <option value="1">E</option>
                                    <option value="2">X</option>
                                    <option value="3">C</option>
                                </select>
                            </div>
                        </div>

                        <div class="layui-row layui-hide c08">
                            <div class="layui-col-xs2 edit-label">屏显颜色</div>
                            <div class="layui-col-xs8 edit-ipt-ban">
                                <select class="layui-input" id="Device_C08Color" name="Device_C08Color" lay-search>
                                    <option value="1">单基色</option>
                                    <option value="2">双基色</option>
                                    <option value="3">三基色</option>
                                </select>
                                <div class="label-desc">
                                    <p>单基色：显示屏支持显示红色。</p>
                                    <p>双基色：显示屏支持显示红色和绿色。</p>
                                    <p>三基色：显示屏支持显示红色、绿色和黄色。</p>
                                    <p>注意：如果选择了显示屏不支持的基色，将导致屏显节目内容不能正常显示。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="hr-line-dashed" style="clear:both;"></div>
        <div class="layui-row">
            <div class="layui-col-xs2 edit-label">&nbsp;</div>
            <div class="layui-col-xs8 edit-ipt-ban">
                <button class="btn btn-primary" id="Save">
                    <i class="fa fa-check"></i>
                    <t>保存</t>
                </button>
                <button class="btn btn-primary" id="SaveNext">
                    <i class="fa fa-check"></i>
                    <t>保存并继续</t>
                </button>
                <button class="btn btn-warning" id="Cancel">
                    <i class="fa fa-times"></i>
                    <t>取消</t>
                </button>
            </div>
        </div>
    </div>

    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}" data-sentryHostNo="${Passway_SentryHostNo}">${Passway_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldrive">
        <option value="${Drive_No}" data-code="${Drive_Code}" data-category="${Drive_Category}">${Drive_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmpldisplaytemplate">
        <option value="${DisplayTemplate_Id}">${DisplayTemplate_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?202209010939" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var laydate = null;
        var layform = null;
        var xmSelect = null;

        var SysConfigContent = decodeURIComponent('@ViewBag.SysConfigContent');
        var SysConfigDefalutDrive = null;
        try {
            SysConfigDefalutDrive = JSON.parse(SysConfigContent);
        } catch (e) { }

        layui.config({ base: '../Static/admin/' }).extend({ xmSelect: '/layui/lay/modules/xm-select' }).use(['laydate', 'xmSelect', 'form', 'element'], function () {
            laydate = layui.laydate;
            layform = layui.form;
            xmSelect = layui.xmSelect;
            pager.init();
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("Device_No");
        var isInint = false;

        var selPassWay = null;
        var pager = {
            passwayList: null, //车道数据
            passwayListByAuto: null, //自动车车道数据
            deviceList: null,  //相机数据
            driveList: null,   //型号数据
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("GetAllPasswayList", {}, function (json) {
                    if (json.success) {
                        pager.passwayList = json.data;
                        $("#Device_PasswayNo").html($("#tmplpassway").tmpl(json.data))
                        layform.render("select")
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("GetDeviceCate2List", {}, function (json) {
                    if (json.success) {
                        pager.deviceList = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                $.post("GetAllDriveList", {}, function (json) {
                    if (json.success) {
                        pager.driveList = json.data;
                    } else {
                        console.log(json.msg);
                    }
                }, "json");

                //屏显模板
                $.post("GetDisplayTemplateList", {}, function (json) {
                    if (json.success) {
                        $("#Device_DisplayTemplateId").html($("#tmpldisplaytemplate").tmpl(json.data))
                        layform.render("select")
                    }
                }, "json");
            },
            bindData: function () {
                if (paramAct == "Update") {
                    layer.msg("加载中...", { icon: 16, time: 0 })
                    $("#Device_No").attr("disabled", true);
                    $.post("GetDeviceDetail", { Device_No: paramNo }, function (json) {
                        layer.closeAll();
                        if (json.success) {
                            config.Device_TcpStatus = json.data.tcp;
                            config.Device_Com = json.data.model.Device_Com;
                            config.Device_InIO = json.data.model.Device_InIO;

                            console.log("json.data.model.Device_Com：" + json.data.model.Device_Com)
                            console.log("json.data.model.Device_InIO：" + json.data.model.Device_InIO)

                            json.data.model.Device_VideoMode = json.data.model.Device_VideoMode == null ? 0 : json.data.model.Device_VideoMode;
                            LoadDeviceConfig(json.data.model);
                            $("#verifyCheck").fillForm(json.data.model, function (data) { });
                            onShowZSXJ();
                            onChangeData();
                            if (json.data.model.Device_Category == 7) {
                                selPassWay.setValue(json.data.model.Device_PasswayNo == null ? "" : json.data.model.Device_PasswayNo.split(','));
                            }

                            $(".falsemodify").each(function () {
                                var val = config[$(this).attr("id")];
                                $(this).find("ul li").each(function () {
                                    if ($(this).attr("data-value") != val)
                                        $(this).removeClass("layui-hide").addClass("layui-hide")
                                });
                            });

                            // 如果是非机动车控制器，设置联动相机
                            if (json.data.model.Device_Category == 9 && json.data.model.Device_FNo) {
                                $("#Device_FNo").val(json.data.model.Device_FNo);
                            }


                            layform.render("select");

                            var dritem = null;
                            pager.driveList.forEach(function (d, i) { if (d.Drive_No == json.data.model.Device_DriveNo) { dritem = d; } });
                            if (dritem != null && DeviceTypeName.device15List.find((m, i) => { return m == dritem.Drive_Name; }) != null && dritem.Drive_Category == 1) { $(".remark_15").removeClass("layui-hide"); } else { $(".remark_15").removeClass("layui-hide").addClass("layui-hide"); }

                            $("#Device_Port").val(json.data.model.Device_Port);

                            //屏显模板
                            $("#Device_DisplayTemplateId").val(json.data.model.Device_DisplayTemplateId);
                        } else {
                            console.log(json.msg);
                        }
                    }, "json");

                    $("#SaveNext").removeClass("layui-hide").addClass("layui-hide");
                } else {
                    pager.onDefault();
                    LoadDeviceConfig(config);
                }
            },
            bindEvent: function () {

                $("#Cancel").click(function () {
                    parent.layer.closeAll();
                });

                $("#Save").click(function () {
                    pager.saveDevice();
                });

                $("#SaveNext").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Device_IP = $("#Device_IP").val();
                        data.Device_Port = $("#Device_Port").val();
                        data.Device_Account = $("#Device_Account").val();
                        data.Device_Pwd = $("#Device_Pwd").val();
                        data.Device_BlueToothNo = $("#Device_BlueToothNo").val();
                        if (config.Device_Category == 7) {
                            var selList = selPassWay.getValue('value');
                            data.Device_PasswayNo = selList.join(",");

                            var passway = pager.passwayList.find((m, i) => { return m.Passway_No == selList[0]; });
                            if (passway != null) data.Device_SentryHostNo = passway.Passway_SentryHostNo;
                        } else if (config.Device_Category == 9) {
                            // 非机动车控制器联动相机
                            data.Device_FNo = $("#Device_FNo").val();
                            var sentry = pager.passwayList.find((m, i) => { return m.Passway_No == data.Device_PasswayNo; });
                            if (sentry != null) data.Device_SentryHostNo = sentry.Passway_SentryHostNo;
                        } else {
                            var sentry = pager.passwayList.find((m, i) => { return m.Passway_No == data.Device_PasswayNo; });
                            if (sentry != null) data.Device_SentryHostNo = sentry.Passway_SentryHostNo;
                        }
                        return data;
                    });
                    Object.assign(param, config);

                    console.log(param)
                    if (paramAct == "Add") {
                        $.post("AddDevice", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(parent.pager.pageIndex, true);
                                    window.location.reload();
                                })
                            } else {
                                if (json.data == 1) {
                                    layer.confirm(json.msg, {
                                        icon: 0,
                                        title: '提示', // 设置标题
                                        btn: ['确定添加', '取消添加'] // 按钮
                                    }, function (index) {
                                        layer.close(index);
                                        $.post("AddDevice", { jsonModel: JSON.stringify(param), ask: "1" }, function (json) {
                                            if (json.success) {
                                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                                    window.parent.pager.bindData(parent.pager.pageIndex, true);
                                                    window.location.reload();
                                                })
                                            } else {
                                                layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] }, function () { });
                                            }
                                        }, "json");
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] }, function () { });
                                }
                            }
                        }, "json");
                    } else {
                        param.Device_No = paramNo;
                        $.post("UpdateDevice", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(parent.pager.pageIndex, true);
                                    window.location.reload();
                                })
                            } else {
                                if (json.data == 1) {
                                    layer.confirm(json.msg, {
                                        icon: 0,
                                        title: '提示', // 设置标题
                                        btn: ['确定更改', '取消更改'] // 按钮
                                    }, function (index) {
                                        layer.close(index);
                                        $.post("UpdateDevice", { jsonModel: JSON.stringify(param), ask: "1" }, function (json) {
                                            if (json.success) {
                                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                                    window.parent.pager.bindData(parent.pager.pageIndex, true);
                                                    window.location.reload();
                                                })
                                            } else {
                                                layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] }, function () { });
                                            }
                                        }, "json");
                                    }, function (index) {
                                        layer.close(index);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] }, function () { });
                                }
                            }
                        }, "json");
                    }
                });

                //选择设备按钮点击事件config.Device_Category
                $("#btnSelectDevice").click(function () {
                    layer.open({
                        type: 2,
                        title: "<i class='fa fa-edit'></i> 选择设备",
                        shadeClose: true,
                        shade: 0.8,
                        closeBtn: 1, // 显示关闭按钮
                        area: ['100%', '95%'],
                        content: 'SearchDevice?Category=' + config.Device_Category,
                    });
                });

                $("#refreshDisplayTemplate").click(function () {
                    layer.msg("正在刷新模板...", { icon: 16, time: 0 });
                    $.post("GetDisplayTemplateList", {}, function (json) {
                        layer.closeAll('loading');
                        if (json.success) {
                            var selectedValue = $("#Device_DisplayTemplateId").val();
                            $("#Device_DisplayTemplateId").html($("#tmpldisplaytemplate").tmpl(json.data));
                            $("#Device_DisplayTemplateId").val(selectedValue);
                            layform.render("select");
                            layer.msg("刷新成功", { icon: 1, time: 1000 });
                        } else {
                            layer.msg("刷新失败", { icon: 2, time: 1000 });
                        }
                    }, "json");
                });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "Device_PasswayNo") {
                        setDeviceName(data.value);
                    } else if (data.elem.id == "Device_DriveNo") {

                        onChangeData(this);
                    }
                });

                $(".layui-btn-token").click(function () {
                    var appKey = $("#Device_AppKey").val();
                    if (appKey == '') {
                        layer.msg("AppKey不允许为空", { icon: 0 });
                        $("#Device_AppKey").focus();
                        return;
                    }
                    var appSecret = $("#Device_Secret").val();
                    if (appSecret == '') {
                        layer.msg("Secret不允许为空", { icon: 0 });
                        $("#Device_Secret").focus();
                        return;
                    }
                    $.ajax({
                        type: 'post',
                        url: 'GetToken?r=' + Math.random(),
                        dataType: 'json',
                        data: { appkey: appKey, secret: appSecret },
                        success: function (json) {
                            if (json.success) {
                                $("#Device_AccessToken").val(json.data.accessToken);
                            } else {
                                layer.msg(json.msg, { icon: 0 });
                            }
                        },
                        complete: function () {
                            $("#Save").removeAttr("disabled");
                            $("#SaveNext").removeAttr("disabled");
                        },
                        error: function () {
                            layer.msg("请求异常", { icon: 2 });
                        }
                    });
                })
            },
            onDefault: function () {
                $("#Device_No").val('@carparking.Common.Utils.CreateNumber');
                setDeviceName($("#Device_PasswayNo").val());
                onEventCombox("Device_Category");
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            },
            //单选车道
            passwaySel: function () {
                $("#divPassway").html('<select class="layui-input" id="Device_PasswayNo" name="Device_PasswayNo" lay-search></select>');
                $("#Device_PasswayNo").html($("#tmplpassway").tmpl(pager.passwayList))
                layform.render("select")
            },
            //多选车道
            passwayMultipleSel: function () {
                $("#divPassway").html('<div id="Device_PasswayNo" class="v-null" style="min-width:210px;"></div>');
                if (pager.passwayList && pager.passwayList.length > 0) {
                    var data = [];
                    for (var i = 0; i < pager.passwayList.length; i++) {
                        data[i] = {
                            "name": pager.passwayList[i].Passway_Name,
                            "value": pager.passwayList[i].Passway_No
                        };
                    }
                    selPassWay = xmSelect.render({
                        el: '#Device_PasswayNo',
                        name: 'Device_PasswayNo',
                        layVerify: 'required',
                        layVerType: 'msg',
                        autoRow: true,
                        toolbar: { show: true },
                        data: data
                    })
                }
            },
            saveDevice: function (ask) {
                if (!myVerify.check()) return;
                layer.msg("处理中", { icon: 16, time: 0 });
                var param = $("#verifyCheck").formToJSON(true, function (data) {
                    data.Device_IP = $("#Device_IP").val();
                    data.Device_Port = $("#Device_Port").val();
                    data.Device_Account = $("#Device_Account").val();
                    data.Device_Pwd = $("#Device_Pwd").val();
                    data.Device_BlueToothNo = $("#Device_BlueToothNo").val();
                    if (config.Device_Category == 7) {
                        var selList = selPassWay.getValue('value');
                        data.Device_PasswayNo = selList.join(",");
                        var passway = pager.passwayList.find((m, i) => { return m.Passway_No == selList[0]; });
                        if (passway != null) data.Device_SentryHostNo = passway.Passway_SentryHostNo;
                    } else if (config.Device_Category == 9) {
                        //为非机动车控制器设置联动相机
                        data.Device_FNo = $("#Device_FNo").val();
                        var sentry = pager.passwayList.find((m, i) => { return m.Passway_No == data.Device_PasswayNo; });
                        if (sentry != null) data.Device_SentryHostNo = sentry.Passway_SentryHostNo;
                    } else {
                        var sentry = pager.passwayList.find((m, i) => { return m.Passway_No == data.Device_PasswayNo; });
                        if (sentry != null) data.Device_SentryHostNo = sentry.Passway_SentryHostNo;
                    }
                    return data;
                });
                Object.assign(param, config);
                param.Device_ScreenBody = $("#Device_ScreenBody").val();

                console.log(param)
                if (paramAct == "Add") {
                    $.post("AddDevice", { jsonModel: JSON.stringify(param), ask: (ask == 1 ? "1" : "0") }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                window.parent.pager.bindData(parent.pager.pageIndex);
                            })
                        } else {
                            if (ask != 1 && json.data == 1) {
                                layer.confirm(json.msg, {
                                    icon: 0,
                                    title: '提示', // 设置标题
                                    btn: ['确定添加', '取消添加'] // 按钮
                                }, function (index) {
                                    layer.close(index);
                                    pager.saveDevice(1)
                                }, function (index) {
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] }, function () { });
                            }
                        }
                    }, "json");
                } else {
                    param.Device_No = paramNo;
                    $.post("UpdateDevice", { jsonModel: JSON.stringify(param), ask: (ask == 1 ? "1" : "0") }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                parent.pager.bindData(parent.pager.pageIndex)
                            })
                        } else {
                            if (ask != 1 && json.data == 1) {
                                layer.confirm(json.msg, {
                                    icon: 0,
                                    title: '提示', // 设置标题
                                    btn: ['确定更改', '取消更改'] // 按钮
                                }, function (index) {
                                    layer.close(index);
                                    pager.saveDevice(1)
                                }, function (index) {
                                    layer.close(index);
                                });
                            } else {
                                layer.msg(json.msg, { icon: 0, time: 0, btn: ['确定'] }, function () { });
                            }
                        }
                    }, "json");
                }
            }
        }

        var onDefaultDrive = function () {
            if (paramAct == "Update") return;
            if (config.Device_Category == 1 && SysConfigDefalutDrive != null && SysConfigDefalutDrive.SysConfig_CameraDrive != '') {
                $("#Device_DriveNo").val(SysConfigDefalutDrive.SysConfig_CameraDrive);
            } else if (config.Device_Category == 4 && SysConfigDefalutDrive != null && SysConfigDefalutDrive.SysConfig_SelfDrive != '') {
                $("#Device_DriveNo").val(SysConfigDefalutDrive.SysConfig_SelfDrive);
            }

            onChangeData();
            layui.form.render("select");
        }

        var onChangeData = function (d) {
            var drno = $("#Device_DriveNo").val();
            var dritem = null;
            if (config.Device_Category != 10 && config.Device_Category != 9) {
                pager.driveList.forEach(function (r, i) { if (r.Drive_No == drno) { dritem = r; } })
                if (dritem == null) return;
            }
            if (dritem != null && DeviceTypeName.device15List.find((m, i) => { return m == dritem.Drive_Name; }) != null && dritem.Drive_Category == 1) {
                $(".remark_15").removeClass("layui-hide");
            }
            else {
                $(".remark_15").removeClass("layui-hide").addClass("layui-hide");
            }

            if (dritem == null) { dritem = {}; }

            if (paramAct == "Add") {
                $("#Device_IP").val(dritem.Drive_Ip);
                if (config.Device_Category == 10) dritem.Drive_Port = 8849;

                $("#Device_TcpPort").val(dritem.Drive_TcpPort);

                //默认C08控制卡类型
                $("#Device_C08Type").val(0);
                //默认C08屏幕颜色
                $("#Device_C08Color").val(1);

                //默认C08单模组宽度
                $("#Device_C08Width").val(32);
                //默认C08单模组高度
                $("#Device_C08Height").val(16);

                $("#Device_Account").val(dritem.Drive_Account);
                $("#Device_Pwd").val(dritem.Drive_Pwd);
                $("#Device_Port").val(dritem.Drive_Port);
            }

            if (config.Device_Category == 9 || config.Device_Category == 10) {
                var port = $("#Device_Port").val();
                var ip = $("#Device_IP").val();
                if (paramAct == "Add") {
                    $("#Device_IP").val("***************");
                    $("#Device_Port").val("1007");
                    if (config.Device_Category == 10) $("#Device_Port").val("8849");
                }
                else {
                    if (ip == "") { $("#Device_IP").val("***************"); }
                    if (port == "") { $("#Device_Port").val("1007"); }
                    if (config.Device_Category == 10) $("#Device_Port").val("8049");
                }
            }

            //如果是添加设备或编辑设备更改类型时，重新获取设备类型初始化配置
            if (isInint || paramAct == "Add") {
                config.Device_InIO = dritem.Drive_InIO;
                config.Device_Com = dritem.Drive_Com;
                config.Device_VideoMode = dritem.Drive_VideoMode;
            }

            $("#Device_InIO").find("li").removeClass("select");
            $("#Device_InIO").find("ul li[data-value=" + config.Device_InIO + "]").addClass("select");

            $("#Device_Com").find("li").removeClass("select");
            $("#Device_Com").find("ul li[data-value=" + config.Device_Com + "]").addClass("select");

            $("#Device_VideoMode").find("li").removeClass("select");
            $("#Device_VideoMode").find("ul li[data-value=" + config.Device_VideoMode + "]").addClass("select");

            if (config.Device_Category == 3) {
                if (dritem.Drive_Code == 10124) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.zsxj,.video06").removeClass("layui-hide").addClass("layui-hide");
                    $(".c08").removeClass("layui-hide");
                    $("#refreshDisplayTemplate").removeClass("layui-hide");
                }
                else {
                    $(".c08").removeClass("layui-hide").addClass("layui-hide");
                    $("#refreshDisplayTemplate").addClass("layui-hide");
                    $(".xsp").removeClass("layui-hide");
                }
            }

            if (dritem.Drive_Code == 10101) {
                $(".appezviz,.video06").removeClass("layui-hide").addClass("layui-hide");
                $(".zsxj").removeClass("layui-hide");
                if (config.Device_Category == 7) {
                    $(".kzb,.qjxj").removeClass("layui-hide");
                }
            }
            else if (dritem.Drive_Code == 10103 || dritem.Drive_Code == 10123) {
                $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.zsxj,.video06").removeClass("layui-hide").addClass("layui-hide");
                $(".ezviz").removeClass("layui-hide");
                if (dritem.Drive_Code == 10123) {
                    $(".ezviz-nvr").removeClass("layui-hide").addClass("layui-hide");
                    $(".viz-cloud").removeClass("layui-hide");
                    // 显示YM01网络录像机设备编码格式说明
                    $(".viz-cloud-format").removeClass("layui-hide");
                }
                else {
                    $(".ezviz-nvr").removeClass("layui-hide");
                    // 隐藏YM01网络录像机设备编码格式说明
                    $(".viz-cloud-format").removeClass("layui-hide").addClass("layui-hide");
                }
            }
            else {
                if (dritem.Drive_Code == 10100) {
                    if (DeviceTypeName.device06List.find((m, i) => { return m == dritem.Drive_Name; }) != null) {
                        $(".video06").removeClass("layui-hide");
                    }
                    else {
                        $(".video06").removeClass("layui-hide").addClass("layui-hide");
                    }
                } else {
                    $(".video06").removeClass("layui-hide").addClass("layui-hide");
                }

                $(".zsxj,.appezviz").removeClass("layui-hide").addClass("layui-hide");
                // 隐藏YM01网络录像机设备编码格式说明
                $(".viz-cloud-format").removeClass("layui-hide").addClass("layui-hide");
                if (config.Device_Category == 7) { $(".kzb,.qjxj").removeClass("layui-hide"); }
            }

            isInint = true;
        }

        var onShowZSXJ = function () {
            var drno = $("#Device_DriveNo").val();
            var dritem = null;
            pager.driveList.forEach(function (d, i) { if (d.Drive_No == drno) { dritem = d; } });
            if (dritem == null) return;

            if (dritem.Drive_Code == 10101) {
                $(".zsxj").removeClass("layui-hide");
            } else {
                $(".zsxj").removeClass("layui-hide").addClass("layui-hide");
            }
        }

        var setDeviceName = function (passwayno) {
            if (paramAct == "Add") {
                var way = null;
                var isSet = false;
                var deviceName = "";
                var category = parseInt(config.Device_Category);
                switch (category) {
                    case 1:
                        deviceName = "识别相机";
                        break;
                    case 2:
                        deviceName = "";
                        break;
                    case 3:
                        deviceName = "显示屏";
                        break;
                    case 4:
                        deviceName = "通道机";
                        break;
                    case 5:
                        deviceName = "";
                        break;
                    case 6:
                        deviceName = "控制板";
                        break;
                    case 7:
                        deviceName = "场景相机";
                        break;
                    case 8:
                        deviceName = "";
                        break;
                    case 9:
                        deviceName = "控制器";
                        break;
                    case 10:
                        deviceName = "二维码设备";
                        break;
                }
                pager.passwayList.forEach(function (item, index) {
                    var defaultName = item.Passway_Name + deviceName;
                    if ($("#Device_Name").val() == '' || defaultName == $("#Device_Name").val()) {
                        isSet = true;
                    }

                    if (item.Passway_No == passwayno) {
                        way = item;
                    }
                });

                if (isSet) {

                    var dname = way != null ? way.Passway_Name + deviceName : deviceName;
                    dname = dname.slice(-32);
                    $("#Device_Name").val(dname);
                }
            }
        }
    </script>
    <script>
        //设备参数配置[仅选项按钮]默认值
        var config = {
            Device_Category: 1,
            Device_IO: 1,
            Device_IsShowPasswayName: 0,
            Device_IsRedLight: 1,
            Device_Voice: 0,
            Device_IsQrCode: 0,
            Device_InIO: 0,
            Device_Com: 1,
            Device_TcpStatus: 0,
            Device_VideoDevType: 0,
            Device_Inverse: 1,
            Device_MoveMode: 1,
            Device_Color: 1,
            Device_UpDownShow: 2,
            Device_VideoMode: 1,
            Device_CommunicationMode: 0,
            Device_Style: 1,
            Device_CameraUsage: 0,
        };

        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName);
            });
        });

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }

        var onEventCombox = function (idName) {
            if (idName == 'Device_Voice') {
                if (config[idName] == 1 && config["Device_Category"] == 4) {
                    $(".zztc_talk").removeClass("layui-hide");
                    if (config["Device_TcpStatus"] == 1) {
                        $("#Device_TcpStatus").text("在线").removeClass("layui-bg-red").addClass("layui-bg-blue");
                    } else {
                        $("#Device_TcpStatus").text("离线").removeClass("layui-bg-blue").addClass("layui-bg-red");
                    }
                } else {
                    $(".zztc_talk").addClass("layui-hide");
                }
            }

            if (idName == "Device_CommunicationMode" && config.Device_Category == 3) {
                if (config[idName] == 2) {
                    $("#Device_Port").val("1007")
                } else {
                    $("#Device_Port").val("1005")
                }
            }

            if (idName == "Device_Category") {
                $("#Device_Name").val('');

                if (config[idName] == 1) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".xj").removeClass("layui-hide");
                    //启用选择设备按钮
                    $("#btnSelectDevice").removeClass("layui-hide");
                } else if (config[idName] == 2) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".dz").removeClass("layui-hide");
                    //禁用选择设备按钮
                    $("#btnSelectDevice").addClass("layui-hide");
                } else if (config[idName] == 3) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".xsp").removeClass("layui-hide");
                    //禁用选择设备按钮
                    $("#btnSelectDevice").addClass("layui-hide");
                    if (config.Device_CommunicationMode == 2)
                        $("#Device_Port").val("1007")
                    else
                        $("#Device_Port").val("1005")
                } else if (config[idName] == 4) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".zztc").removeClass("layui-hide");
                    //启用选择设备按钮
                    $("#btnSelectDevice").removeClass("layui-hide");
                } else if (config[idName] == 6) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".kzb").removeClass("layui-hide");
                    //启用选择设备按钮
                    $("#btnSelectDevice").removeClass("layui-hide");
                }
                if (config[idName] == 7) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".qjxj").removeClass("layui-hide");
                    //禁用选择设备按钮
                    $("#btnSelectDevice").addClass("layui-hide");
                    pager.passwayMultipleSel();
                } else {
                    pager.passwaySel();
                }
                if (config[idName] == 9) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.zsxj,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".ewm,.fjdc").removeClass("layui-hide");
                    //启用选择设备按钮
                    $("#btnSelectDevice").removeClass("layui-hide");

                    pager.passwayListByAuto = [];
                    pager.passwayList.forEach(function (item, index) { if (item.Passway_Type == 3) { pager.passwayListByAuto.push(item) } });
                    $("#Device_PasswayNo").html($("#tmplpassway").tmpl(pager.passwayListByAuto))

                    // 获取车牌识别相机列表
                    $.ajax({
                        url: "/Device/GetCameraList",
                        type: "get",
                        dataType: "json",
                        success: function (res) {
                            if (res.success && res.data) {
                                var html = '<option value="">请选择</option>';
                                $.each(res.data, function (i, item) {
                                    var selected = (item.Device_No == config.Device_FNo) ? "selected" : "";
                                    html += '<option value="' + item.Device_No + '" ' + selected + '>' + item.Device_Name + '</option>';
                                });
                                $("#Device_FNo").html(html);
                                layui.form.render("select");
                            }
                        }
                    });
                }
                else if (config[idName] == 10) {
                    $(".xj,.xsp,.dz,.zztc,.kzb,.qjxj,.zsxj,.fjdc,.c08").removeClass("layui-hide").addClass("layui-hide");
                    $(".ewm").removeClass("layui-hide");
                    //禁用选择设备按钮
                    $("#btnSelectDevice").addClass("layui-hide");
                    pager.passwaySel();
                }
                else {
                    if (pager.passwayListByAuto != null && pager.passwayListByAuto != pager.passwayList) {
                        if (config[idName] == 7) {
                            pager.passwayMultipleSel();
                        } else {
                            pager.passwaySel();
                        }
                    }
                    pager.passwayListByAuto = null;
                }

                var driveData = [];
                pager.driveList.forEach(function (item, index) {
                    if (item.Drive_Category == config[idName]) {
                        driveData[driveData.length] = item;
                    }
                });

                var deviceData = [];
                pager.deviceList.forEach(function (item, index) {
                    if (item.Device_Category == 1) {
                        deviceData[deviceData.length] = item;
                    }
                });

                $("#Device_DriveNo").html($("#tmpldrive").tmpl(driveData));

                layui.form.render("select")
                onDefaultDrive();
                setDeviceName($("#Device_PasswayNo").val());
            }
        }

        // 在父窗口中定义的处理函数（这部分代码应该放在打开弹窗的页面中）
        function handleDeviceSelection(deviceData) {
            //修改设备类型
            pager.driveList.forEach(function (item, index) {
                if (item.Drive_Category == deviceData.DeviceRecord_Category && item.Drive_Name == deviceData.DeviceRecord_Type) {
                    $("#Device_DriveNo").val(item.Drive_No);
                    onChangeData();
                    layui.form.render("select")
                    return true;
                }
            });
            //修改IP输入框的值
            $("#Device_IP").val(deviceData.DeviceRecord_IP);
            //如果deviceData.DeviceRecord_Remark不为空，则修改设备名称
            if (deviceData.DeviceRecord_Remark != null && deviceData.DeviceRecord_Remark != "") {
                $("#Device_Name").val(deviceData.DeviceRecord_Remark);
            }
            //修改账号端口号
            $("#Device_Port").val(deviceData.DeviceRecord_Port);
            //修改账号密码
            $("#Device_Pwd").val(deviceData.DeviceRecord_Pwd);
            //修改连接账号
            $("#Device_Account").val(deviceData.DeviceRecord_User);
        }

        // 修复设备编码输入框样式问题
        $(document).ready(function() {
            // 确保设备编码输入框的清除按钮正确显示
            function fixDeviceCodeInputStyle() {
                var $deviceCodeContainer = $('.device-code-container');
                var $deviceCodeInput = $('#Device_No');

                if ($deviceCodeContainer.length && $deviceCodeInput.length) {
                    // 移除可能存在的重复清除按钮
                    $deviceCodeContainer.find('.input-clear-icon').remove();

                    // 重新添加清除按钮
                    if ($deviceCodeInput.siblings('.input-clear-icon').length === 0) {
                        var clearBtn = $('<i class="layui-icon layui-icon-close-fill input-clear-icon"></i>');
                        $deviceCodeInput.after(clearBtn);

                        // 绑定清除按钮事件
                        clearBtn.click(function() {
                            $deviceCodeInput.val('').focus();
                            $(this).hide();
                        });
                    }

                    // 监听输入框变化
                    $deviceCodeInput.on('input focus', function() {
                        var $clearIcon = $(this).siblings('.input-clear-icon');
                        $clearIcon.toggle(!!$(this).val());
                    });

                    // 初始化清除按钮显示状态
                    var $clearIcon = $deviceCodeInput.siblings('.input-clear-icon');
                    $clearIcon.toggle(!!$deviceCodeInput.val());
                }
            }

            // 页面加载完成后执行修复
            setTimeout(fixDeviceCodeInputStyle, 100);

            // 当设备类型改变时重新执行修复
            $(document).on('click', '.btnCombox ul li', function() {
                setTimeout(fixDeviceCodeInputStyle, 100);
            });
        });
    </script>
</body>

</html>