﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车牌号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input v-null v-carno v-submit" id="Car_CarNo" name="Car_CarNo" maxlength="8" autocomplete="off" onblur="BindCarDetail(this.value)"  value="@ViewBag.CarPrefix"/>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">车牌类型</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select class="layui-select v-null" id="Car_TypeNo" name="Car_TypeNo" lay-search>
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车牌颜色</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select class="layui-input v-null" id="Car_VehicleTypeNo" name="Car_VehicleTypeNo" lay-search>
                        <option value="">请选择</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row rdate">
                <div class="layui-col-xs3 edit-label ">开始时间</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input v-null v-submit" id="Car_BeginTime" name="Car_BeginTime" autocomplete="off" maxlength="50" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row rdate">
                <div class="layui-col-xs3 edit-label ">结束时间</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input  v-null v-submit" id="Car_EndTime" name="Car_EndTime" autocomplete="off" maxlength="50" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">行驶证号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" id="Car_License" name="Car_License" autocomplete="off" maxlength="32" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车辆品牌</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" id="Car_Model" name="Car_Model" autocomplete="off" maxlength="50" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车辆颜色</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" id="Car_Colour" name="Car_Colour" autocomplete="off" maxlength="20" />
                </div>
            </div>
              <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">卡号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" id="Car_CardNo" name="Car_CardNo" autocomplete="off" maxlength="50" />
                </div>
            </div>
            <div class="layui-row layui-hide">
                <div class="layui-col-xs3 edit-label ">状态</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="btnCombox" id="Car_Status">
                        <ul class="flex">
                            <li class="" data-value="3">停用</li>
                            <li class="select" data-value="1">正常</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">备注</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" id="Car_Remark" name="Car_Remark" autocomplete="off" maxlength="255" />
                </div>
            </div>
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.visible = false;
        myVerify.init();
        s_carno_picker.init("Car_CarNo", (text, carno) => {
            if (s_carno_picker.eleid == "Car_CarNo") {
                $("#Car_CarNo").val(carno.join(''));
                BindCarDetail(carno.join(''));
            }
        }, "web").bindkeyup();
        layui.use(['form', 'laydate'], function () {
            pager.init()
        });

        var curDatetime = new Date();
        var endDatetime = new Date();
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("no");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            cardList: [],
            data: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                layui.form.on("select", function (data) {
                    var val = data.value;
                    if (data.elem.id == 'Car_TypeNo') {
                        pager.onHideDate(val);
                    }
                });

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, i) {
                            $("#Car_VehicleTypeNo").append('<option value="' + item.CarType_No + '" ' + (i == 0 ? "selected" : "") + '>' + item.CarType_Name + '</option>');
                        });
                        layui.form.render("select");
                    }
                }, "json");


                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        pager.cardList = json.data;
                        json.data.forEach(function (item, i) {
                            //排除商家车&访客车
                            if ("5,6".indexOf(item.CarCardType_Type) < 0) {
                                $("#Car_TypeNo").append('<option value="' + item.CarCardType_No
                                    + '" data-category="' + item.CarCardType_Category
                                    + '" data-ismorecar="' + item.CarCardType_IsMoreCar
                                    + '">' + item.CarCardType_Name + (item.CarCardType_IsMoreCar == 1 ? " [多位多车]" : "") + '</option>');
                            }
                        });
                        layui.form.render("select")
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["Car_BeginTime", "Car_EndTime"], { type: 'date', range: true });
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    var data = null;
                    parent.pager.carNoList.forEach(function (item, i) {
                        if (item.Car_No == paramNo) {
                            data = item;
                        }
                    });
                    console.log(data)
                    pager.data = data;
                    pager.onShowData(data);
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Car_License = $("#Car_License").val();
                        data.Car_Colour = $("#Car_Colour").val();
                        data.Car_Remark = $("#Car_Remark").val();
                        data.Car_Model = $("#Car_Model").val();
                        data.CarCardType_Name = $("#Car_TypeNo").find("option:selected").text();
                        data.CarType_Name = $("#Car_VehicleTypeNo").find("option:selected").text();
                        data.Car_Category = $("#Car_TypeNo").find("option:selected").attr("data-category");
                        data.Car_CardNo = $("#Car_CardNo").val();
                        return data;
                    });

                    if (param.Car_TypeNo == null || param.Car_TypeNo == "") { layer.msg("请选择车牌类型", { icon: 0, time: 1500 }); return; }
                    if (param.Car_VehicleTypeNo == null || param.Car_VehicleTypeNo == "") { layer.msg("请选择车牌颜色", { icon: 0, time: 1500 }); return; };

                    var begin = $("#Car_BeginTime").val();
                    var end = $("#Car_EndTime").val();
                    if (RuleObj.montharr.indexOf(param.Car_Category) > -1 || RuleObj.prepaidarr.indexOf(param.Car_Category) > -1) {
                        param.Car_BeginTime = new Date(begin).Format("yyyy-MM-dd 00:00:00");
                        param.Car_EndTime = new Date(end).Format("yyyy-MM-dd 23:59:59");
                    }
                    
                    param.Car_Status = config.Car_Status;

                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        param.Car_No = param.Car_No || new Date().getTime();
                    }
                    else if (paramAct == "Update") {
                        param.Car_No = paramNo;
                    }

                    parent.carnoTable.addOrUpdate(param);
                });

            },
            onShowData: function (data) {
                $("#verifyCheck").fillForm(data, function (data) { });
                LoadDeviceConfig(data);
                pager.onHideDate(data.Car_TypeNo);
                layui.form.render("select");
            },
            onHideDate: function (val) {
                var ismorecar = 0;
                var category = 0;
                pager.cardList.forEach((item, index) => { if (item.CarCardType_No == val) { ismorecar = item.CarCardType_IsMoreCar; category = item.CarCardType_Category; } });
                var bDiv = $("#Car_BeginTime").parent();
                var eDiv = $("#Car_EndTime").parent();
                var bHtml = bDiv.html();
                var eHtml = eDiv.html();
                if (ismorecar == 1) {
                    $(".rdate").removeClass("layui-hide").addClass("layui-hide");
                } else {
                    $("#Car_BeginTime").remove();
                    $("#Car_EndTime").remove();
                    bDiv.html(bHtml);
                    eDiv.html(eHtml);
                    $(".rdate").removeClass("layui-hide");
                    var start;
                    var end;
                    if (RuleObj.montharr.indexOf(category) > -1 || RuleObj.prepaidarr.indexOf(category) > -1) {                        
                        if (paramAct != "Add") {
                            start = new Date(pager.data.Car_BeginTime).Format("yyyy-MM-dd");
                            end = new Date(pager.data.Car_EndTime).Format("yyyy-MM-dd");
                        } else {
                            start = curDatetime.Format("yyyy-MM-dd");
                            end = new Date(endDatetime.setMonth(endDatetime.getMonth() + 1)).Format("yyyy-MM-dd");
                        }
                        $("#Car_BeginTime").val(start);
                        $("#Car_EndTime").val(end);
                        _DATE.bind(layui.laydate, ["Car_BeginTime", "Car_EndTime"], { type: 'date', range: true });
                    } else {
                        if (paramAct != "Add") {
                            start = new Date(pager.data.Car_BeginTime).Format("yyyy-MM-dd hh:mm:ss");
                            end = new Date(pager.data.Car_EndTime).Format("yyyy-MM-dd hh:mm:ss");
                        } else {
                            start = curDatetime.Format("yyyy-MM-dd 00:00:00");
                            end = endDatetime.Format("yyyy-MM-dd 23:59:59");
                        }
                        $("#Car_BeginTime").val(start);
                        $("#Car_EndTime").val(end);
                        _DATE.bind(layui.laydate, ["Car_BeginTime", "Car_EndTime"], { type: 'datetime', range: true });
                    }
                }
            }
        };

        var isRead = false;
        var BindCarDetail = function (carno) {
            $.post("GetCarDetail", { Car_CarNo: carno }, function (json) {
                if (json.success) {
                    if (json.data != null) {
                        isRead = true;
                        pager.onShowData(json.data);
                    }
                    //else if (isRead) {
                    //    isRead = false;
                    //    $("#verifyCheck").find("select,input").val("");
                    //    $("#Car_CarNo").val(carno);
                    //    layui.form.render("select");
                    //}
                }
            }, "json");
        }

    </script>
    <script>
        //设备参数配置[仅选项按钮]默认值
        var config = {
            Car_Status: 1
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                onEventCombox(idName)
            });
        });

        var LoadDeviceConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName)
            });
        }

        var onEventCombox = function (idName) {

        }

        var RuleObj = {
            data: [],
            temparr: ['3644', '3645', '3646', '3647', '3648', '3649', '3650', '3651'],//临时车类型
            montharr: ['3652', '3653', '3654', '3655', '3661', '3662', '3663', '3664'],//月租车类型
            freearr: ['3656'],//免费车类型
            prepaidarr: ['3657'],//储值车类型
            visitorarr: ['3658'],//免费车类型          
        }
    </script>
</body>
</html>
