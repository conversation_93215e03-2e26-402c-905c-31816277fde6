﻿//------------------------------------------------------------------------------
// <autogenerated>
//     Implementation of the Class Car
//     Creater: tianji.liu
//     Date:    2019-03-21 15:00
//     Version: 1.0.0.0
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </autogenerated>
//------------------------------------------------------------------------------
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using carparking.Common;

namespace carparking.DAL
{
    public class Car : DbService
    {

        Model.Car CarModel = new Model.Car();
        Model.CarOwnerExt CarOwnerExtModel = new Model.CarOwnerExt();
        Model.CarExt CarExtModel = new Model.CarExt();
        #region 模板生成

        /// <summary>
        /// 是否存在该记录,自定义条件
        /// </summary>
        public bool Exists(string selectWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select count(1) from Car");
            strSql.Append(" where " + selectWhere);

            using (var db = ReadConnection)
            {
                return db.QueryFirstOrDefault<int>(strSql.ToString()) > 0;
            }
        }

        /// <summary>
        /// 增加一条数据,返回数据主键编号
        /// </summary>
        public int Add(Model.Car model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("insert into Car(");
            strSql.Append(@"Car_No,Car_CarNo,Car_Colour,Car_VehicleTypeNo,Car_Model,Car_Space,Car_License,Car_TypeNo,Car_Status,Car_AddTime,Car_AddID,Car_ParkingNo,Car_Balance,Car_EndTime,Car_OwnerNo,Car_OwnerName,Car_OnLine,Car_BeginTime)");
            strSql.Append(" values (");
            strSql.Append(@"?Car_No,?Car_CarNo,?Car_Colour,?Car_VehicleTypeNo,?Car_Model,?Car_Space,?Car_License,?Car_TypeNo,?Car_Status,?Car_AddTime,?Car_AddID,?Car_ParkingNo,?Car_Balance,?Car_EndTime,?Car_OwnerNo,?Car_OwnerName,?Car_OnLine,?Car_BeginTime)");

            strSql.Append(";SELECT LAST_INSERT_ID();");

            using (var db = WriteConnection)
            {
                return int.Parse(db.ExecuteScalar(strSql.ToString(), model).ToString());
            }
        }

        public int Add(Model.API.PushResultParse.CarOwner data)
        {
            List<string> sqlList = new List<string>();
            if (data.Item1 != null)
                sqlList.Add(GetAddOrUpdateSql(data.Item1));

            data.Item2?.ForEach(item =>
            {
                if (item != null)
                    sqlList.Add(GetAddOrUpdateSql(item));
            });

            data.Item3?.ForEach(item =>
            {
                if (item != null)
                    sqlList.Add(GetAddOrUpdateSql(item));
            });

            data.Item4?.ForEach(item =>
            {
                if (item != null)
                    sqlList.Add(GetAddOrUpdateSql(item));
            });

            return ExecuteTrans(sqlList, WriteConnection);
        }

        /// <summary>
        /// 更新一条数据,更新前先查询数据是否存在，在提交新的实体
        /// </summary>
        public int Update(Model.Car model)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Car set ");
            strSql.Append("Car_CarNo=?Car_CarNo,");
            strSql.Append("Car_Colour=?Car_Colour,");
            strSql.Append("Car_VehicleTypeNo=?Car_VehicleTypeNo,");
            strSql.Append("Car_Model=?Car_Model,");
            strSql.Append("Car_Space=?Car_Space,");
            strSql.Append("Car_Space=?Car_Space,");
            strSql.Append("Car_License=?Car_License,");
            strSql.Append("Car_TypeNo=?Car_TypeNo,");
            strSql.Append("Car_Status=?Car_Status,");
            strSql.Append("Car_AddTime=?Car_AddTime,");
            strSql.Append("Car_AddID=?Car_AddID,");
            strSql.Append("Car_EditTime=?Car_EditTime,");
            strSql.Append("Car_EditID=?Car_EditID,");

            strSql.Append("Car_ParkingNo=?Car_ParkingNo,");
            strSql.Append("Car_Balance=?Car_Balance,");
            strSql.Append("Car_EndTime=?Car_EndTime,");
            strSql.Append("Car_OwnerNo=?Car_OwnerNo,");
            strSql.Append("Car_OwnerName=?Car_OwnerName,");
            strSql.Append("Car_OnLine=?Car_OnLine,");
            strSql.Append("Car_BeginTime=?Car_BeginTime,");

            strSql.Remove(strSql.Length - 1, 1);
            strSql.Append(" where Car_No='" + model.Car_No + "'");
            using (var db = WriteConnection)
            {
                return db.Execute(strSql.ToString(), model);
            }
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public int UpdateByModel(Model.Car model)
        {
            Type type = model.GetType();
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update " + type.Name + " set ");
            string commandText;
            MySqlParameter[] paramArray;
            GetUpdateCommandText(model, out commandText, out paramArray);
            strSql.Append(commandText);
            strSql.Append(" where " + type.Name + "_No='" + model.GetType().GetProperty(type.Name + "_No").GetValue(model, null) + "'");

            using (var db = WriteConnection)
            {
                return db.Execute(strSql.ToString(), model);
            }
        }

        /// <summary>
        /// 根据模型生成SQL更新语句
        /// </summary>
        public int UpdateByList(List<Model.Car> modelList)
        {
            List<string> sqlList = new List<string>();

            modelList.ForEach(model =>
            {
                string sql = GetAddOrUpdateSql<Model.Car>(model);
                sqlList.Add(sql);
            });

            return ExecuteTrans(sqlList);
        }

        /// <summary>
        /// 更新黑白名单下发参数标识
        /// </summary>
        public int UpdateByCameraBody(List<Model.CarExt> modelList)
        {
            List<string> sqlList = new List<string>();

            modelList.ForEach(model =>
            {
                sqlList.Add($" UPDATE car SET Car_CameraBody ='{model.Car_CameraBody}' WHERE Car_No ='{model.Car_No}' ");
            });

            return ExecuteTrans(sqlList);
        }


        /// <summary>
        /// 更新一条数据,自定义修改
        /// </summary>
        public int Update(string fields, string selectWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("update Car set ");
            strSql.Append(" " + fields + " ");
            if (!string.IsNullOrEmpty(selectWhere))
                strSql.Append(" where " + selectWhere + " ");
            using (var db = WriteConnection)
            {
                return db.Execute(strSql.ToString());
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public int Delete(int? Car_ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Car ");
            strSql.Append(" where Car_ID=?Car_ID");

            using (var db = WriteConnection)
            {
                return db.Execute(strSql.ToString(), new { Car_ID = Car_ID });
            }
        }

        /// <summary>
        /// 删除一条数据
        /// </summary>
        public int DeleteByNo(string Car_No)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Car ");
            strSql.Append(" where Car_No=?Car_No");

            using (var db = WriteConnection)
            {
                return db.Execute(strSql.ToString(), new { Car_No = Car_No });
            }
        }

        /// <summary>
        /// 删除一条数据，自定义条件删除
        /// </summary>
        public int DeleteByWhere(string selectWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("delete from Car ");
            strSql.Append(" where " + selectWhere);

            using (var db = WriteConnection)
            {
                return db.Execute(strSql.ToString());
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.Car GetEntity(int? Car_ID)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select " + ConditionsToReplace("*", CarModel) + " from Car ");
            strSql.Append(" where Car_ID=?Car_ID");

            using (var db = ReadConnection)
            {
                return db.Query<Model.Car>(strSql.ToString(), new { Car_ID = Car_ID }).FirstOrDefault();
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.Car GetEntity(string Car_No)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append("select " + ConditionsToReplace("*", CarModel) + " from Car ");
            strSql.Append(" where Car_No=@Car_No");

            using (var db = ReadConnection)
            {
                return db.Query<Model.Car>(strSql.ToString(), new { Car_No = Car_No }).FirstOrDefault();
            }
        }

        /// <summary>
        /// 自定义获取实体
        /// </summary>
        /// <param name="fields"></param>
        /// <param name="selectWhere"></param>
        /// <returns></returns>
        public Model.Car GetEntity(string fields, string selectWhere, object parameters = null)
        {
            if (string.IsNullOrEmpty(selectWhere))
            {
                return null;
            }
            StringBuilder strSql = new StringBuilder();
            fields = ConditionsToReplace(fields, CarModel);
            strSql.Append("select " + fields + " from Car ");
            strSql.Append(" where " + selectWhere);
            using (var db = ReadConnection)
            {
                return db.Query<Model.Car>(strSql.ToString(), parameters).FirstOrDefault();
            }
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public List<Model.Car> GetList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord)
        {
            showFields = ConditionsToReplace(showFields, CarModel);
            return GetPageList<Model.Car>("Car", showFields, selectWhere, "Car_ID", 0, "Car_ID", pageSize, pageIndex, out pageCount, out totalRecord).ToList();
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public List<Model.CarOwnerExt> GetExtList(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord)
        {
            showFields = ConditionsToReplace(showFields, CarOwnerExtModel);
            string tableName = string.Format(@"(select " + showFields + @" from Car a left join Owner d on a.Car_OwnerNo=d.Owner_No) tt");
            return GetPageList<Model.CarOwnerExt>(tableName, showFields, selectWhere, "Car_ID", 0, "Car_ID", pageSize, pageIndex, out pageCount, out totalRecord).ToList();
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public List<Model.CarExt> GetExt2List(string showFields, string selectWhere, int pageIndex, int pageSize, out int pageCount, out int totalRecord, object parameters = null)
        {
            showFields = ConditionsToReplace(showFields, CarExtModel);
            string tableName = string.Format(@"(select " + showFields + @" from Car a left join Owner d on a.Car_OwnerNo=d.Owner_No 
                                                left join CarCardType ct on ct.CarCardType_No=a.Car_TypeNo left join CarType cp on cp.CarType_No=a.Car_VehicleTypeNo) tt");
            return GetPageList<Model.CarExt>(tableName, showFields, selectWhere, "Car_ID", 0, "Car_ID", pageSize, pageIndex, out pageCount, out totalRecord, parameters: parameters).ToList();
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public List<Model.CarExt> GetExt2List(string showFields, string selectWhere, int pageIndex, int pageSize, string sortField, int sortType, out int pageCount, out int totalRecord)
        {
            showFields = ConditionsToReplace(showFields, CarExtModel);

            StringBuilder tableName = new StringBuilder();
            tableName.Append($" (select {showFields} from Car a ");
            tableName.Append($" left join Owner d on a.Car_OwnerNo=d.Owner_No ");
            tableName.Append($" left join CarCardType ct on ct.CarCardType_No=a.Car_TypeNo ");
            tableName.Append($" left join CarType cp on cp.CarType_No=a.Car_VehicleTypeNo) tt ");

            return GetPageList<Model.CarExt>(tableName.ToString(), showFields, selectWhere, sortField, sortType, "Car_ID", pageSize, pageIndex, out pageCount, out totalRecord).ToList();
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public List<Model.CarExt> GetCarExtAllEntity(string showFields, string selectWhere)
        {
            showFields = ConditionsToReplace(showFields, CarExtModel);
            string strSql = $"select {showFields} from Car a " +
                $"left join Owner d on a.Car_OwnerNo=d.Owner_No " +
                $"left join CarCardType ct on ct.CarCardType_No=a.Car_TypeNo " +
                $"left join CarType cp on cp.CarType_No=a.Car_VehicleTypeNo ";
            if (!string.IsNullOrEmpty(selectWhere))
                strSql += $" where {selectWhere} ";

            using (var db = ReadConnection)
            {
                return db.Query<Model.CarExt>(strSql.ToString()).ToList();
            }
        }

        /// <summary>
        /// 获取车辆数
        /// </summary>
        public int GetCarCount(string selectWhere)
        {
            string strSql = $"select count(1) from Car ";

            if (!string.IsNullOrEmpty(selectWhere))
                strSql += $" where {selectWhere} ";

            using (var db = ReadConnection)
            {
                return db.QueryFirstOrDefault<int>(strSql.ToString());
            }
        }

        /// <summary>
        /// 自定义条件分页,获得实体列表
        /// </summary>
        public List<Model.CarOwnerExt> GetCarOwnerAllEntity(string showFields, string selectWhere)
        {
            showFields = ConditionsToReplace(showFields, CarOwnerExtModel);
            string strSql = $"select {showFields} from Car a " +
                $"left join Owner d on a.Car_OwnerNo=d.Owner_No ";
            if (!string.IsNullOrEmpty(selectWhere))
                strSql += $" where {selectWhere} ";

            using (var db = ReadConnection)
            {
                return db.Query<Model.CarOwnerExt>(strSql.ToString()).ToList();
            }
        }


        /// <summary>
        /// 获取少量实体
        /// </summary>
        /// <param name="showFields">字段</param>
        /// <param name="selectWhere">查询条件</param>
        /// <returns></returns>
        public List<Model.Car> GetAllEntity(string showFields, string selectWhere, object parameters = null)
        {
            showFields = ConditionsToReplace(showFields, CarModel);
            string sql = "select " + showFields + " from Car ";
            if (!string.IsNullOrEmpty(selectWhere)) sql += " where " + selectWhere;

            using (var db = ReadConnection)
            {
                return db.Query<Model.Car>(sql, parameters).ToList();
            }
        }

        /// <summary>
        /// 获取月租车总数
        /// </summary>
        /// <returns></returns>
        public int GetCountSum()
        {
            string sql = $"SELECT COUNT(*) FROM car where Car_EnableOffline='1'";

            using (var db = ReadConnection)
            {
                int.TryParse(Convert.ToString(db.ExecuteScalar(sql)), out int iSum);
                return iSum;
            }
        }


        /// <summary>
        /// 获取注销月租车总数
        /// </summary>
        /// <returns></returns>
        public int GetUnCountSum()
        {
            string sql = $"SELECT COUNT(*) FROM carunbound";

            using (var db = ReadConnection)
            {
                int.TryParse(Convert.ToString(db.ExecuteScalar(sql)), out int iSum);
                return iSum;
            }
        }

        #endregion

        #region 其他方法

        public int AddCarOwner(Model.Car car, Model.Owner owner, List<Model.StopSpace> spaces, Model.PayColl payColl, Model.ParkOrder order = null, List<Model.OrderDetail> details = null, List<Model.Ledger> ledgerList = null)
        {
            List<string> sqlList = new List<string>();

            if (owner != null)
            {
                string sql = GetAddOrUpdateSql<Model.Owner>(owner);
                sqlList.Add(sql);

                string sql1 = $"update car set Car_OwnerName='{owner.Owner_Name}' where Car_OwnerNo='{owner.Owner_No}'";
                sqlList.Add(sql1);
            }

            if (car != null)
            {
                string sql2 = GetAddOrUpdateSql<Model.Car>(car);
                sqlList.Add(sql2);
            }

            spaces?.ForEach(x =>
            {
                if (x != null) sqlList.Add(GetAddOrUpdateSql(x));
            });

            if (payColl != null)
            {
                payColl.payOrderList?.ForEach(x =>
                {
                    string sql = GetAddOrUpdateSql<Model.PayOrder>(x);
                    sqlList.Add(sql);
                });
                payColl.payPartList?.ForEach(x =>
                {
                    string sql = GetAddOrUpdateSql<Model.PayPart>(x);
                    sqlList.Add(sql);
                });
            }

            if (order != null)
            {
                string sql = GetAddOrUpdateSql(order);
                sqlList.Add(sql);
            }

            if (details != null)
            {
                details.ForEach(x =>
                {
                    string sql = GetAddOrUpdateSql(x);
                    sqlList.Add(sql);
                });
            }

            if (ledgerList != null)
            {
                ledgerList.ForEach(x =>
                {
                    string sql = GetAddSql(x);
                    sqlList.Add(sql);
                });
            }

            return ExecuteTrans(sqlList, WriteConnection);
        }


        public bool AddCarOwnerList(List<Model.Car> carList, List<Model.Owner> ownerList)
        {
            List<string> sqlList = new List<string>();

            if (carList != null)
            {
                carList.ForEach(x =>
                {
                    string sql2 = GetAddOrUpdateSql<Model.Car>(x);
                    sqlList.Add(sql2);
                });

            }

            if (ownerList != null)
            {
                ownerList.ForEach(x =>
                {
                    string sql = GetAddOrUpdateSql<Model.Owner>(x);
                    sqlList.Add(sql);
                });
            }
            return ExecuteTrans(sqlList) > 0;
        }

        public bool AddCarOwnerList(List<Model.Car> carList, List<Model.Owner> ownerList, List<Model.StopSpace> spaceList, List<Model.ParkOrder> parkOrderList = null, 
            List<Model.OrderDetail> orderDetailList = null, List<Model.PayOrder> payorderList = null, List<Model.PayPart> paypartList = null, List<Model.Ledger> ledgerList = null)
        {
            List<string> sqlList = new List<string>();

            carList?.ForEach(x =>
            {
                if (x != null)
                {
                    string sql2 = GetAddOrUpdateSql<Model.Car>(x);
                    sqlList.Add(sql2);
                }
            });

            ownerList?.ForEach(x =>
            {
                if (x != null)
                {
                    string sql = GetAddOrUpdateSql<Model.Owner>(x);
                    sqlList.Add(sql);
                }
            });

            spaceList?.ForEach(item =>
            {
                if (item != null)
                {
                    string sql = GetAddOrUpdateSql(item);
                    sqlList.Add(sql);
                }
            });

            parkOrderList?.ForEach(item =>
            {
                if (item != null)
                {
                    string sql = GetAddOrUpdateSql(item);
                    sqlList.Add(sql);
                }
            });

            orderDetailList?.ForEach(item =>
            {
                if (item != null)
                {
                    string sql = GetAddOrUpdateSql(item);
                    sqlList.Add(sql);
                }
            });

            payorderList?.ForEach(item =>
            {
                if (item != null)
                {
                    string sql = GetAddOrUpdateSql(item);
                    sqlList.Add(sql);
                }
            });
            paypartList?.ForEach(item =>
            {
                if (item != null)
                {
                    string sql = GetAddOrUpdateSql(item);
                    sqlList.Add(sql);
                }
            });

            ledgerList?.ForEach(item =>
            {
                if (item != null)
                {
                    string sql = GetAddOrUpdateSql(item);
                    sqlList.Add(sql);
                }
            });

            return ExecuteTrans(sqlList) >= 0;
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.CarExt GetCarExtEntityByNo(string Car_No)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT c.*,o.Owner_Email,o.Owner_IDCard,o.Owner_License,o.Owner_License,o.Owner_Phone,o.Owner_Sex,ct.CarCardType_Category,ct.CarCardType_Name,ct.CarCardType_Type,o.Owner_SpaceNum,o.Owner_StartTime,o.Owner_EndTime,o.Owner_No,o.Owner_Address,o.Owner_Space from car c LEFT JOIN `owner` o on c.Car_OwnerNo=o.Owner_No " +
                "left join CarCardType ct on ct.CarCardType_No=c.Car_TypeNo");
            strSql.Append(" where Car_No=?Car_No");

            using (var db = ReadConnection)
            {
                return db.Query<Model.CarExt>(strSql.ToString(), new { Car_No = Car_No }).FirstOrDefault();
            }
        }
        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.CarExt GetCarExtEntityByCarNo(string Car_CarNo)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT c.*,o.Owner_Email,o.Owner_IDCard,o.Owner_License,o.Owner_License,o.Owner_Phone,o.Owner_Sex,ct.CarCardType_Category,ct.CarCardType_Name,o.Owner_SpaceNum,o.Owner_StartTime,o.Owner_EndTime,o.Owner_No,o.Owner_Address,o.Owner_Space,o.Owner_Name,o.Owner_Balance from car c LEFT JOIN `owner` o on c.Car_OwnerNo=o.Owner_No " +
                "left join CarCardType ct on ct.CarCardType_No=c.Car_TypeNo");
            strSql.Append(" where Car_CarNo=?Car_CarNo");

            using (var db = ReadConnection)
            {
                return db.Query<Model.CarExt>(strSql.ToString(), new { Car_CarNo = Car_CarNo }).FirstOrDefault();
            }
        }

        /// <summary>
        /// 得到一个对象实体
        /// </summary>
        public Model.CarExt GetCarExtEntityBy(string selectWhere)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.Append(" SELECT c.*,o.Owner_Email,o.Owner_IDCard,o.Owner_License,o.Owner_License,o.Owner_Phone,o.Owner_Sex,ct.CarCardType_Category,ct.CarCardType_Name,cart.CarType_Name,o.Owner_SpaceNum,o.Owner_StartTime,o.Owner_EndTime,o.Owner_No,o.Owner_Name,o.Owner_Space from car c LEFT JOIN `owner` o on c.Car_OwnerNo=o.Owner_No " +
                "left join CarCardType ct on ct.CarCardType_No=c.Car_TypeNo " +
                "left join cartype cart on cart.CarType_No=c.Car_VehicleTypeNo ");
            if (!string.IsNullOrEmpty(selectWhere))
            {
                strSql.Append($" where {selectWhere}");
            }
            else
            {
                strSql.Append($" where 1=1");
            }

            using (var db = ReadConnection)
            {
                return db.Query<Model.CarExt>(strSql.ToString()).FirstOrDefault();
            }
        }


        public int DeleteByList(List<Model.Car> carList, bool isCarUnbound = true)
        {
            if (carList == null || carList.Count == 0) return -1;

            List<string> sqlList = new List<string>();
            sqlList.Add($"delete from car where Car_CarNo in ('{string.Join("','", carList.Select(x => x.Car_CarNo))}')");
            if (isCarUnbound)
            {
                var unboundList = new List<Model.CarUnbound>();
                carList?.ForEach(item =>
                {
                    unboundList.Add(new Model.CarUnbound()
                    {
                        CarUnbound_No = "UC" + item.Car_No,
                        CarUnbound_CarNo = item.Car_CarNo,
                        CarUnbound_OwnerNo = item.Car_OwnerNo,
                        CarUnbound_ParkNo = item.Car_ParkingNo,
                        CarUnbound_AddTime = DateTimeHelper.GetNowTime(),
                        CarUnbound_Content = TyziTools.Json.ToString(item),
                        CarUnbound_OwnerName = item.Car_OwnerName,
                        CarUnbound_OwnerSpace = item.Car_OwnerSpace,
                        CarUnbound_CarCardTypeNo = item.Car_TypeNo,
                        CarUnbound_BeginTime = item?.Car_BeginTime,
                        CarUnbound_EndTime = item?.Car_EndTime,
                        CarUnbound_CarTypeNo = item?.Car_VehicleTypeNo,
                        CarUnbound_IsMoreCar = item?.Car_IsMoreCar,
                        CarUnbound_RegTime = item?.Car_AddTime,
                    });
                });

                unboundList?.ForEach(x =>
                {
                    sqlList.Add(GetAddOrUpdateSql(x));
                });
            }
            return ExecuteTrans(sqlList, WriteConnection);
        }
        #endregion

        /// <summary>
        /// 添加车辆预约
        /// </summary>
        /// <returns></returns>
        public int AddCarReserve(Model.Car car, Model.Owner owner, Model.Reserve reserve)
        {
            List<string> sqlList = new List<string>();
            if (car != null)
                sqlList.Add(GetAddOrUpdateSql(car));
            if (owner != null)
                sqlList.Add(GetAddOrUpdateSql(car));
            if (reserve != null)
                sqlList.Add(GetAddOrUpdateSql(reserve));

            if (sqlList.Count == 0) return -1;

            return ExecuteTrans(sqlList, WriteConnection);
        }

        /// <summary>
        /// 添加车辆预约
        /// </summary>
        /// <returns></returns>
        public int AddCarReserve(Model.API.PushResultParse.ReserveCar data)
        {
            if (data == null) return -1;

            List<string> sqlList = new List<string>();
            data.Item1?.ForEach(car =>
            {
                if (car != null)
                    sqlList.Add(GetAddOrUpdateSql(car));
            });

            data.Item2?.ForEach(owner =>
            {
                if (owner != null)
                    sqlList.Add(GetAddOrUpdateSql(owner));
            });

            data.Item3?.ForEach(reserve =>
            {
                if (reserve != null)
                    sqlList.Add(GetAddOrUpdateSql(reserve));
            });

            if (sqlList.Count == 0) return -1;

            return ExecuteTrans(sqlList, WriteConnection);
        }
    }
}

