﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>出入通道设置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px; float: left; font-size: 16px; }
        html, body { width: 100%; height: 100%; overflow-x: hidden; }
        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }
        .leftmenu { user-select: none; padding: 1rem; }
        .leftmenu, .rightbody { height: 100%; overflow: auto; }
        @@media screen and (max-width: 400px) {
            .leftmenu, .rightbody { height: auto !important; }
        }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .content-panel { height: 100%; overflow: auto; }
        .min100 { min-height: 100%; }

        .menu-list { width: 100%; overflow: auto; position: relative; }
        .menu-title { line-height: 1.5rem; padding: 10px 0; border-bottom: 1px dashed #ddd; cursor: pointer; }
        .menu-items { }
        .menu-item { padding-left: 1rem; }
        .menu-text { padding: .5rem 0; line-height: 1.5rem; position: relative; }
        .park-name:hover, .menu-text:hover { background-color: #ecf0f5; cursor: pointer; text-decoration: underline; }
        .park-name::before { content: " "; background: url('../../Static/img/icon_park.svg'); background-size: 100%; padding: 0 .6rem; }
        .menu-text::before { content: "»"; color: #999; position: absolute; left: -.6rem; line-height: 1.3rem; }
        .menu-title::after, .menu-text::after { color: #ffffff; position: absolute; right: 0; padding: 0 3px; border-radius: 3px; font-size: 10px; }
        .menu-title.type1::after { content: "外场"; background-color: #0094ff; }
        .menu-title.type2::after { content: "内场"; background-color: #00bcd4; }
        .menu-text.state1::after { content: "出口"; color: #f6800f; }
        .menu-text.state2::after { content: "入口"; color: #496ad3; }
        .menu-list div.m-active { font-weight: bold; }
        ::-webkit-scrollbar { width: 5px; background-color: rgba(0,0,0,.1); }
        ::-webkit-scrollbar-thumb { background-color: #bbb; border-radius: 5px; }
        .m-active-1 { color: #f6800f; font-weight: bold; }
        #framePanl { padding: 1rem; }
        #framePanl iframe { border: 0; width: 100%; height: calc(100% - 4px); }
    </style>
</head>
<body class="animated fadeInRight">
    <div class="layui-col-sm3 leftmenu">
        <div class="layui-card min100">
            <div class="layui-card-header padding-15" id="addpanl">
                <button class="layui-btn layui-btn-fluid layui-bg-blue layui-hide" onclick="onOpenFrame()" id="Add">新增车道</button>
            </div>
            <div class="layui-card-body">
                <div class="menu-list">

                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-sm9 rightbody" id="framePanl">
        @*<iframe src="" frameborder="0"></iframe>*@
    </div>
    <script type="text/x-jquery-tmpl" id="tmpldata">
        <div class="menu-title m-active ${(ParkArea_Type==0?'type1':'type2')}">${ParkArea_Name} ${ParkArea_PasswayList.length>0?'('+ParkArea_PasswayList.length+')':''}</div>
        <div class="menu-items">
            <!--左侧菜单列表-->
            <ul class="menu-item">
                {{each ParkArea_PasswayList}}
                <li>
                    <div class="menu-text ${($value.PasswayLink_GateType==0?'state1':'state2')}" onclick="area.openAreaItem(this)" data-no="${$value.Passway_No}">${$value.Passway_Name}</div>
                </li>
                {{/each}}
            </ul>
        </div>
    </script>

    <script type="text/x-jquery-tmpl" id="tmplpark">
        <div class="content-panel">
            <div class="layui-card min100">
                <div class="layui-card-header padding-15 pan-title"><text>车道详情</text></div>
                <div class="layui-card-body layui-detail">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车道名称</label></div>
                        <div class="layui-col-xs6"><label class="value">${Passway_Name}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车道编号</label></div>
                        <div class="layui-col-xs6"><label class="value">${Passway_No}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">车道类型</label></div>
                        <div class="layui-col-xs6">
                            {{if Passway_Type==1 }}
                            <label class="layui-badge layui-bg-blue">汽车通道</label>
                            {{else}}
                            <label class="layui-badge layui-bg-blue">人行通道</label>
                            {{/if}}
                        </div>
                    </div>
                    @*<div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">值守模式</label></div>
                        <div class="layui-col-xs6">
                            {{if Passway_DutyMode==0 }}
                            <label class="layui-badge layui-bg-blue">无人值守</label>
                            {{else}}
                            <label class="layui-badge layui-bg-blue">现场值守</label>
                            {{/if}}
                        </div>
                    </div>*@

                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">相机模式</label></div>
                        <div class="layui-col-xs6">
                            {{if Passway_CameraMode==1 }}
                            <label class="layui-badge layui-bg-blue">单相机模式</label>
                            {{else Passway_CameraMode==2 }}
                            <label class="layui-badge layui-bg-blue">双相机模式</label>
                            {{else Passway_CameraMode==3 }}
                            <label class="layui-badge layui-bg-blue">多相机模式</label>
                            {{/if}}
                        </div>
                    </div>
                    {{if Passway_CameraMode > 1 }}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">相机识别间隔(秒)</label></div>
                        <div class="layui-col-xs6"><label class="value">${Passway_IdInterval}</label></div>
                    </div>
                    {{/if}}
                    <div class="layui-row layui-hide">
                        <div class="layui-col-xs3"><label class="name">是否启用倒车</label></div>
                        <div class="layui-col-xs6">
                            {{if Passway_IsBackCar==1 }}
                            <label class="layui-badge layui-bg-blue">启用</label>
                            {{else}}
                            <label class="layui-badge">禁用</label>
                            {{/if}}
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">无牌车扫码是否判断地感</label></div>
                        <div class="layui-col-xs6">
                            {{if Passway_Sense==1 }}
                            <label class="layui-badge layui-bg-blue">是</label>
                            {{else}}
                            <label class="layui-badge">否</label>
                            {{/if}}
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">是否启用收费</label></div>
                        <div class="layui-col-xs6">
                            {{if Passway_IsCharge==1 }}
                            <label class="layui-badge layui-bg-blue">启用</label>
                            {{else}}
                            <label class="layui-badge">禁用</label>
                            {{/if}}
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">所属岗亭</label></div>
                        <div class="layui-col-xs6">
                            {{if SentryHost_Name==null||SentryHost_Name==""}}
                            <label class="layui-badge">未设置</label>
                            {{else}}
                            <label class="value">${SentryHost_Name}</label>
                            {{/if}}
                        </div>
                    </div>

                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">所在区域</label></div>
                        <div class="layui-col-xs6">
                            <table class="layui-table" lay-filter="table">
                                <tbody>
                                    {{each linkdata}}
                                    <tr>
                                        <td>${$value.ParkArea_Name}</td>
                                        <td>
                                            {{if $value.PasswayLink_GateType==0}}
                                            <span class="layui-badge layui-bg-green">出口</span>
                                            {{else}}
                                            <span class="layui-badge layui-bg-blue">入口</span>
                                            {{/if}}
                                        </td>
                                    </tr>
                                    {{/each}}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <hr />
                    <div class="layui-row">
                        <div class="layui-col-xs3">&nbsp;</div>
                        <div class="layui-col-xs8">
                            <button class="layui-btn layui-btn-sm layui-hide" id="Update" data-no="${Passway_No}"><i class="fa fa-edit"></i>编辑</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red layui-hide" id="Delete" data-no="${Passway_No}"><i class="fa fa-trash-o"></i>删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();

        var laytable = null;
        layui.use(['table'], function () {
            laytable = layui.table;
            pager.init();
        });

        //右侧新增按钮
        var onOpenFrame = function () {
            layer.open({
                type: 2, id: 1,
                title: "新增车道",
                content: "Edit?Act=Add",
                area: getIframeArea(["800px", "95%"]),
                maxmin: false
            });
            parent.top.setScrollTop(document.body, 0);
        }

        //打开详情展示
        var openDetail = function (itemno) {
            var item = null;
            pager.passways.forEach(function (d, i) {
                if (d.Passway_No == itemno) {
                    item = d;
                }
            });
            if (item != null) {
                var linkdata = [];
                pager.links.forEach(function (d, i) {
                    if (d.PasswayLink_PasswayNo == itemno) {
                        linkdata[linkdata.length] = d;
                    }
                });
                item.linkdata = linkdata;
            }
            var data = [item];
            $("#framePanl").html($("#tmplpark").tmpl(data));
            pager.bindPower();
            pager.bindEvent();
        }

        var pager = {
            showData: null, //数据已解析为前段展示格式
            passways: null,
            links: null,
            areas: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //passwayNo 右侧显示的通道编码，为null则显示第一个通道
            bindData: function (passwayNo) {
                layer.closeAll();
                var that = this;
                $.post("GetPasswayList", {}, function (json) {
                    if (json.success) {
                        that.showData = json.data.showData;
                        that.passways = json.data.passways;
                        that.links = json.data.links;
                        that.areas = json.data.areas;

                        area.init(that.showData, passwayNo);
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower["Add"]) {
                        $("#addpanl").removeClass("layui-hide")
                    } else {
                        $("#addpanl").removeClass("layui-hide").addClass("layui-hide")
                    }
                });
            },
            bindEvent: function () {
                $("#Update").unbind("click").click(function () {
                    var paramNo = $(this).attr("data-no");
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑出入通道",
                        content: "Edit?Act=Update&Passway_No=" + paramNo,
                        area: getIframeArea(["800px", "95%"]),
                        maxmin: false
                    });
                    parent.top.setScrollTop(document.body, 0);
                })

                $("#Delete").unbind("click").click(function () {
                    var paramNo = $(this).attr("data-no");
                    $("#Update,#Delete").attr("disabled", true);
                    LAYER_OPEN_TYPE_0("确定删除通道?", res => {
                        LAYER_LOADING("处理中...");
                        $.post("DelPassway", { Passway_No: paramNo }, function (json) {
                            if (json.success) {
                                window.pager.bindData();
                                layer.msg("删除成功", { time: 1000 }, function () {
                                })
                            } else {
                                $("#Update,#Delete").removeAttr("disabled")
                                layer.msg(json.msg);
                            }
                        }, "json");
                    }, res => {
                        $("#Update,#Delete").removeAttr("disabled")
                    })
                    parent.top.setScrollTop(document.body, 0);
                })
            }
        }

        var area = {
            action: 0,//当前操作：0-编辑，1-新增
            cur: null,//当前显示的停车区域信息
            init: function (showData, passwayNo) {
                area.onload(showData, passwayNo)
            },
            //加载左侧车场名称
            onpark: function (parkData) {
                $(".park-name").text(parkData.Parking_Name);
            },
            //加载左侧列表数据
            onload: function (data, passwayNo) {
                $(".menu-list").html($("#tmpldata").tmpl(data))
                var menus = $(".menu-list").find(".menu-text")
                if (menus != null && menus.length > 0) {
                    if (passwayNo == null) {
                        area.openAreaItem(menus[0]);
                    } else {
                        for (var i = 0; i < menus.length; i++) {
                            if ($(menus[i]).attr("data-no") == passwayNo) {
                                area.openAreaItem($(menus[i]));
                                break;
                            }
                        }
                    }
                } else {
                    $("#framePanl iframe").attr("src", "/Passway/Detail?Passway_No=");
                }

                $(".menu-title").click(function () {
                    var next = $(this).next();
                    if (next.hasClass("layui-hide"))
                        next.removeClass("layui-hide");
                    else
                        next.addClass("layui-hide");
                });
            },
            //点击左侧列表打开详情窗口，并加载数据
            openAreaItem: function (e) {
                console.log(e)
                var itemno = $(e).attr("data-no");
                $(".menu-text").removeClass("m-active-1")
                $(e).removeClass("m-active-1").addClass("m-active-1")

                //$("#framePanl iframe").attr("src", "/Passway/Detail?Passway_No=" + itemno);

                openDetail(itemno);
            }
        }
    </script>
</body>
</html>
