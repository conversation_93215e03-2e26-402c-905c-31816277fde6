﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>停车收费</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 3px 4px 0 0; float: left; }
        .layui-form-select .layui-input { width: 182px; }
        .layui-btn { line-height: normal !important; padding: 0 12px; }
        .layui-bg-wxgreen { background-color: #04BE02 !important; }
        .layui-bg-alipayblue { background-color: #1678ff !important; }
        .layui-bg-ylblue { background-color: #1678ff !important; }
    </style>
</head>
<body>

    <div class="layui-fluid ">
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" value="@ViewBag.start" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                        </div>
                        <div class="layui-code">
                            <div class="layui-row label-desc">仅查询入场时间在事件发生之前的订单</div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" lay-event="Save" id="Save"><i class="fa fa-check-circle-o"></i><t>确定关联</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var paramControlEventNo = $.getUrlParam("ControlEvent_No");
        var comtable = null;
        var layuiForm = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'ParkOrder_ID', title: '订单ID', hide: true }
                , { field: 'ParkOrder_No', title: '订单号' }
                , { field: 'ParkOrder_ParkNo', title: '车场编码', hide: true }
                , { field: 'Parking_Name', title: '车场名称', hide: true }
                , { field: 'ParkOrder_CarNo', title: '车牌号' }
                , { field: 'ParkOrder_OwnerName', title: '车主', hide: true }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型', hide: true }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色', hide: true }
                , {
                    field: 'ParkOrder_StatusNo', title: '订单状态', templet: function (d) {
                        if (d.ParkOrder_StatusNo == 199) return tempBar(2, "预入场");
                        else if (d.ParkOrder_StatusNo == 200) {
                            if (d.ParkOrder_OutType == 0) return tempBar(1, "已入场");
                            if (d.ParkOrder_OutType == 1) return tempBar(3, "预出场");
                        }
                        else if (d.ParkOrder_StatusNo == 201) return tempBar(4, "已出场");
                        else if (d.ParkOrder_StatusNo == 202) return tempBar(0, "自动关闭");
                        else if (d.ParkOrder_StatusNo == 203) return tempBar(0, "场内关闭");
                        else if (d.ParkOrder_StatusNo == 204) return tempBar(6, "欠费出场");
                    }
                }
                , { field: 'ParkOrder_EnterTime', title: '入场时间' }
                , { field: 'ParkOrder_EnterPasswayName', title: '入场车道名称', hide: true }
                , { field: 'ParkOrder_EnterImgPath', title: '入场图片', templet: function (d) { return tempImg(d.ParkOrder_EnterImgPath); } }
                , { field: 'ParkOrder_EnterRemark', title: '入场备注', hide: true }
                , { field: 'ParkOrder_OutTime', title: '出场时间' }
                , { field: 'ParkOrder_OutPasswayName', title: '出场车道名称', hide: true }
                , { field: 'ParkOrder_OutImgPath', title: '出场图片', templet: function (d) { return tempImg(d.ParkOrder_OutImgPath); } }
                , { field: 'ParkOrder_ParkAreaName', title: '停车区域' }
                , { field: 'ParkOrder_Remark', title: '订单备注', hide: true }
            ]];

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ControlEvent/GetOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam), ControlEvent_No: paramControlEventNo }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Save':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 1500 }); return; }
                        if (data[0].ParkOrder_StatusNo == 201) { layer.msg("订单已出场", { icon: 0, time: 1500 }); return; }
                        if (data[0].ParkOrder_StatusNo == 204) { layer.msg("订单已被其他事件关联", { icon: 0, time: 1500 }); return; }
                        if (data[0].ParkOrder_EnterTime == 204) { layer.msg("订单已被其他事件关联", { icon: 0, time: 1500 }); return; }

                        LAYER_OPEN_TYPE_0("订单将会变更为欠费出场状态<br/>确定关联此订单?", res => {
                            layer.msg("正在处理...", { icon: 16, time: 0 });
                            $.post("BindOrder", { ControlEvent_No: paramControlEventNo, ParkOrder_No: data[0].ParkOrder_No }, function (json) {
                                if (json.success) {
                                    layer.msg(json.msg, { icon: 1, time: 1500 }, function () {
                                        parent.pager.bindData(parent.pager.pageIndex);
                                    })
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            }, "json");
                        }, res => { })

                        break;
                };
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                if (parent.pager && parent.pager.carno) $("#ParkOrder_CarNo").val(parent.pager.carno);
                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#ParkOrder_EnterPasswayNo").append(option);
                            $("#ParkOrder_OutPasswayNo").append(option);
                        });
                    }
                }, "json");

                layuiForm.render("select");
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: 'datetime', range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/ControlEvent/GetOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam), ControlEvent_No: paramControlEventNo } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                        $("#ParkOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }
    </script>
</body>
</html>
