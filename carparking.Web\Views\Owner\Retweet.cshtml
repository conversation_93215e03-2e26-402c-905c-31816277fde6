﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车位续期</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .layui-col-sm2 edit-label { width: 95px !important; }
        .layui-tab-title .layui-this::after { height: 40px; }
        input.rmb { padding-right: 2.5rem; position: relative; }
        span.rmb { position: absolute; width: 2.5rem; right: 0; top: 0; height: 36px; line-height: 36px; text-align: center; color: green; border: 1px solid #eee; }
        .layui-btn-warm { background-color: #ec971f; border-color: #d58512; }
        .layui-form-select dl { max-height: 250px !important; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div style="overflow:hidden;height:0;">
            <!--防止浏览器保存密码后自动填充-->
            <input type="password" />
            <input type="text" />
            <input type="text" name="email" />
        </div>
        <div class="ibox-content">
            <div id="verifyCheck" class="layui-form layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">车位号</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_Space" name="Owner_Space" readonly />
                    </div>
                    <div class="layui-col-sm2 edit-label">车位数量</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_SpaceNum" name="Owner_SpaceNum" readonly />
                    </div>
                </div>
                <div class="layui-row layui-hide month">
                    <div class="layui-col-sm2 edit-label">当前有效期起</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_StartTime" name="Owner_StartTime" readonly />
                    </div>
                    <div class="layui-col-sm2 edit-label">当前有效期止</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" id="Owner_EndTime" name="Owner_EndTime" readonly />
                    </div>
                </div>
                <div class="layui-row layui-hide store">
                    <div class="layui-col-sm2 edit-label">当前余额</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input rmb v-float" maxlength="11" id="Owner_Balance" name="Owner_Balance" readonly />
                        <span class="rmb">元</span>
                    </div>
                </div>

                <div class="layui-tab">
                    <ul class="layui-tab-title layui-col-xs-offset1 layui-col-xs11">
                        <li class="paytype layui-this" data-paytype='1'><strong>按规则延期/充值</strong></li>
                        <li class="paytype" data-paytype='2'><strong>自定义延期/充值</strong></li>
                    </ul>
                    <div class="layui-tab-content layui-form">
                        <!--按规则延期/充值-->
                        <div class="layui-tab-item layui-show">
                            <div class="layui-row"></div>
                            <div class="layui-row">
                                <div class="layui-col-sm2 edit-label required">续期规则</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <select class="layui-select v-null" id="MonthRule_No" name="MonthRule_No" lay-filter="MonthRule_No" lay-search>
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-row layui-hide month">
                                <div class="layui-col-sm2 edit-label required">续期开始日期</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <input type="text" class="layui-input v-null" id="start" name="start" readonly />
                                </div>
                                <div class="layui-col-sm2 edit-label required">续期截止日期</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <input type="text" class="layui-input v-null" id="end" name="end" readonly />
                                </div>
                            </div>

                            <div class="layui-row">
                                <div class="layui-col-sm2 edit-label layui-hide store">充值金额</div>
                                <div class="layui-col-sm3 edit-ipt-ban layui-hide store">
                                    <input type="text" class="layui-input rmb v-float" maxlength="11" id="PayOrder_Money" name="PayOrder_Money" readonly />
                                    <span class="rmb">元</span>
                                </div>
                                <div class="layui-col-sm2 edit-label layui-hide month">应付金额</div>
                                <div class="layui-col-sm3 edit-ipt-ban layui-hide month">
                                    <input type="text" class="layui-input rmb v-float" maxlength="11" id="PayOrder_MoneyMc" name="PayOrder_MoneyMc" readonly />
                                    <span class="rmb">元</span>
                                </div>
                            </div>

                            <div class="layui-row">
                                <div class="layui-col-sm2 edit-label required">支付金额</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <input type="text" class="layui-input rmb v-null v-float" maxlength="11" id="PayOrder_PayedMoney" name="PayOrder_PayedMoney" />
                                    <span class="rmb">元</span>
                                </div>
                                <div class="layui-col-sm2 edit-label layui-hide store">充值后余额</div>
                                <div class="layui-col-sm3 edit-ipt-ban layui-hide store">
                                    <input type="text" class="layui-input rmb v-float" maxlength="11" id="Owner_Balance1" name="Owner_Balance1" readonly />
                                    <span class="rmb">元</span>
                                </div>
                            </div>
                        </div>
                        <!--自定义延期/充值-->
                        <div class="layui-tab-item">
                            <div class="layui-row"></div>
                            <div class="layui-row layui-hide month">
                                <div class="layui-col-sm2 edit-label required">续期开始日期</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <div class="input-group">
                                        <input type="text" class="layui-input v-null" id="Owner_StartTimeYQ" name="Owner_StartTimeYQ" style="border-color: #1ab394 !important;" readonly />
                                        <span class="input-group-btn"><button class="layui-btn layui-btn-outline after" id="GetInCarTime">查询场内车</button></span>
                                    </div>
                                </div>
                                <div class="layui-col-sm2 edit-label required">续期截止日期</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <input type="text" class="layui-input v-null v-submit" id="Owner_EndTimeYQ" name="Owner_EndTimeYQ" autocomplete="off" />
                                </div>
                            </div>

                            <div class="layui-row">
                                <div class="layui-col-sm2 edit-label layui-hide store">充值金额</div>
                                <div class="layui-col-sm3 edit-ipt-ban layui-hide store">
                                    <input type="text" class="layui-input rmb v-float" maxlength="11" id="PayOrder_MoneyYQ" name="PayOrder_MoneyYQ" value="0.00" />
                                    <span class="rmb">元</span>
                                </div>
                                <div class="layui-col-sm2 edit-label">支付金额</div>
                                <div class="layui-col-sm3 edit-ipt-ban">
                                    <input type="text" class="layui-input rmb v-float" maxlength="11" id="PayOrder_PayedMoneyYQ" name="PayOrder_PayedMoneyYQ" value="0.00" />
                                    <span class="rmb">元</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-footer">
            <button class="layui-btn layui-btn-md" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
            <button class="layui-btn layui-btn-md layui-btn-warm" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?230804" asp-append-version="true" asp-append-version="true"></script>
    <script>

        myVerify.init();
        layui.use(['laydate', 'form'], function () {

            layui.form.render("select");
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("Owner_No");
        var dt = new Date().Format("yyyy-MM-dd");

        var index = parent.layer.getFrameIndex(window.name);

        var isOpen = true;
        var nowDate = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        var pager = {
            isHand: false,
            incartime: "",
            model: null,
            rules: [],
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                $("#GetInCarTime").click(function () {
                    $("#GetInCarTime").attr("disabled", true);
                    layer.msg("查询中", { icon: 16, time: 0 });
                    $.getJSON("GetInCarTime", { Owner_No: paramNo, isHand: pager.isHand }, function (json) {
                        if (json.success) {
                            var layerFrmIndex = layer.confirm(json.msg, {
                                title: "提示",
                                icon: "3",
                                btn: ['确定', '取消'] //按钮
                            }, function () {
                                pager.incartime = json.data;
                                $("#Owner_StartTimeYQ").val(json.data);
                                layer.close(layerFrmIndex);
                            }, function () { });
                        } else {
                            pager.incartime = "";
                            layer.msg(json.msg, { icon: 0, time: 1500, end: function () { } });
                        }
                        $("#GetInCarTime").removeAttr("disabled");
                    });
                    pager.isHand = true;
                });

                $.post("GetOwner", { Owner_No: paramNo }, function (json) {
                    if (json.Success) {
                        var model = json.Data.model;
                        if (model.Owner_StartTime != null) model.Owner_StartTime = new Date(model.Owner_StartTime).Format("yyyy-MM-dd");
                        if (model.Owner_EndTime != null) model.Owner_EndTime = new Date(model.Owner_EndTime).Format("yyyy-MM-dd");
                        $("#verifyCheck").fillForm(model, function (data) {
                            if (data.Owner_StartTime == null) $("#Owner_StartTime").val("未设置");
                            if (data.Owner_EndTime == null) $("#Owner_EndTime").val("未设置");

                            var currentDate = new Date(data.Owner_EndTime);
                            $("#Owner_StartTimeYQ").val(_DATE.getDateTime(currentDate.setDate(currentDate.getDate() + 1), "yyyy-MM-dd"));
                            $("#Owner_Balance").val(data.Owner_Balance || "0.00");
                        });
                        pager.model = model;
                        $(".store,.month").removeClass("layui-hide").addClass("layui-hide");
                        if (model.Owner_CardType == 2)
                            $(".store").removeClass("layui-hide");
                        else if (model.Owner_CardType == 3 || model.Owner_CardType == 1 || model.Owner_CardType == 4)
                            $(".month").removeClass("layui-hide");

                        $("#GetInCarTime").click();
                    } else {
                        layer.msg(json.Message);
                    }
                }, "json");

                $.getJSON("SltMonthRuleList", {}, function (json) {
                    if (json.success) {
                        if (json.data != null) {
                            json.data.forEach(function (item, i) {
                                if (item.MonthRule_CarCardTypeNo == pager.model.Owner_CardTypeNo) {
                                    pager.rules[pager.rules.length] = item;
                                    var text = item.MonthRule_Name + ' - ';

                                    if (item.MonthRule_Category == 3657) {
                                        text += '充值' + item.MonthRule_EMoney + "元" + ' - ';
                                    }
                                    else {
                                        text += '延期' + item.MonthRule_Cycle;
                                        text += (item.MonthRule_Unit == 1 ? "天" : (item.MonthRule_Unit == 2 ? "月" : "年")) + ' - ';
                                    }
                                    text += '收费' + item.MonthRule_Money + '元';
                                    if (item.MonthRule_AddEnable == 1) {
                                        text += ' 赠送' + item.MonthRule_AddCycle;
                                        text += (item.MonthRule_AddUnit == 1 ? "天" : (item.MonthRule_AddUnit == 2 ? "月" : "年"));
                                    }
                                    if (item.MonthRule_CardType == 3) {
                                        if (item.MonthRule_Type == 1) text += ' - 本月1日开始延期';
                                        if (item.MonthRule_Type == 2) text += ' - 当前日期开始延期';
                                        if (item.MonthRule_Type == 3) text += ' - 过期日期开始延期';
                                    }
                                    $("#MonthRule_No").append('<option value="' + item.MonthRule_No + '">' + text + '</option>');
                                }
                            });
                            layui.form.render("select");
                        }
                    }
                });

                _DATE.bind(layui.laydate, ["Owner_EndTimeYQ"], { type: "date" });
            },
            //数据绑定
            bindData: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;

                    var param = {
                        Owner_No: pager.model.Owner_No,
                        PayOrder_PayType: $("li.paytype.layui-this").attr("data-paytype"),
                        InCar_EnterTime: pager.incartime
                    };

                    if (param.PayOrder_PayType == 1) {
                        param.MonthRule_No = $("#MonthRule_No").val();
                        if (!param.MonthRule_No) {
                            layer.msg("请选择续期规则", { icon: 0, time: 1500 });
                            return;
                        }

                        if (pager.model.Owner_CardType == 3) {
                            param.Owner_StartTime = $("#start").val();
                            param.Owner_EndTime = $("#end").val();
                        }
                        param.PayOrder_PayedMoney = $("#PayOrder_PayedMoney").val();
                        param.PayOrder_Money = $("#PayOrder_Money").val();
                    } else {
                        if (pager.model.Owner_CardType == 3) {
                            param.Owner_StartTimeYQ = $("#Owner_StartTimeYQ").val();
                            param.Owner_EndTimeYQ = $("#Owner_EndTimeYQ").val();
                        }

                        param.PayOrder_MoneyYQ = $("#PayOrder_MoneyYQ").val();
                        param.PayOrder_PayedMoneyYQ = $("#PayOrder_PayedMoneyYQ").val();
                    }

                    var canMsg = [];
                    canMsg[canMsg.length] = "确认下列续期信息：<br />";
                    if (pager.model.Owner_CardType == 3) {
                        canMsg[canMsg.length] = "续期起始时间：" + (param.Owner_StartTime || param.Owner_StartTimeYQ) + "<br />";
                        canMsg[canMsg.length] = "续期截止时间：" + (param.Owner_EndTime || param.Owner_EndTimeYQ) + "<br />";
                    } else {
                        canMsg[canMsg.length] = "充值金额：" + (param.PayOrder_Money || param.PayOrder_MoneyYQ) + "元<br />";
                    }
                    canMsg[canMsg.length] = "支付金额：<span style='color: red;'>" + (param.PayOrder_PayedMoney || param.PayOrder_PayedMoneyYQ) + "元</span><br />";
                    canMsg[canMsg.length] = "确定续期吗?<br />";
                    canMsg[canMsg.length] = "【提示：若车位已过期并且车辆已在场内，<br />&nbsp;&nbsp;&nbsp;&nbsp;请点击<span style='color: red;'>查询场内车</span>更改续期开始时间】<br />";

                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Save").attr("disabled", true);
                    layer.open({
                        type: 0,
                        title: "续期提示",
                        btn: ["确定", "取消"],
                        content: canMsg.join(''),
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $(this).attr("disabled", true);
                            $.post("OnSpacePayCharge", { jsonModel: JSON.stringify(param) }, function (json) {
                                if (json.success) {
                                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                        window.parent.pager.bindData(window.parent.pager.pageIndex);
                                    });
                                } else {
                                    layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { if (json.msg.indexOf("成功") > -1) window.parent.pager.bindData(window.parent.pager.pageindex); } });
                                    $("#Save").removeAttr("disabled");
                                }
                            }, "json");
                        },
                        btn2: function () { $("#Save").removeAttr("disabled"); },
                        cancel: function () { $("#Save").removeAttr("disabled"); }
                    })
                });

                layui.form.on("select(MonthRule_No)", function (data) {

                    var rule = null;
                    pager.rules.forEach(function (r, i) { if (r.MonthRule_No == data.value) { rule = r; } });

                    var start = null;
                    var end = null;
                    if (pager.model.Owner_CardType == 3) {
                        if (rule) {
                            if (rule.MonthRule_Type == 1) {//本月1日起
                                start = new Date().Format("yyyy-MM-01");
                            } else if (rule.MonthRule_Type == 2) {//当前时间
                                start = new Date().Format("yyyy-MM-dd");
                            } else if (rule.MonthRule_Type == 3) {//过期时间
                                if (pager.model.Owner_EndTime != null && pager.model.Owner_EndTime != '') {
                                    var currentDate = new Date(pager.model.Owner_EndTime);
                                    start = _DATE.getDateTime(currentDate.setDate(currentDate.getDate() + 1), "yyyy-MM-dd")
                                } else
                                    start = new Date().Format("yyyy-MM-dd");
                            }

                            var start2 = new Date(new Date(start).setDate(new Date(start).getDate() - 1)).Format("yyyy-MM-dd 23:59:59");
                            if (rule.MonthRule_Unit == 1) {//充值天数
                                end = _DATE.getSpan(start2, { date: rule.MonthRule_Cycle }, "d");
                            } else if (rule.MonthRule_Unit == 2) {//充值月数
                                end = _DATE.addMonths(new Date(start2), rule.MonthRule_Cycle);
                            } else if (rule.MonthRule_Unit == 3) {//充值年数
                                end = _DATE.getSpan(start2, { year: rule.MonthRule_Cycle }, "d");
                            }
                    
                            if (rule.MonthRule_AddEnable == 1) {
                                if (rule.MonthRule_AddUnit == 1) {//赠送天数
                                    end = _DATE.getSpan(end, { date: rule.MonthRule_AddCycle }, "d");
                                } else if (rule.MonthRule_AddUnit == 2) {//赠送月数
                                    end = _DATE.addMonths(new Date(end), rule.MonthRule_AddCycle);
                                } else if (rule.MonthRule_AddUnit == 3) {//赠送年数
                                    end = _DATE.getSpan(end, { year: rule.MonthRule_AddCycle }, "d");
                                }
                            }
                        } else {
                            $("#MonthRule_No").val("");
                            start = "";
                            end = "";
                            layui.form.render();
                        }

                        $("#start").val(start);
                        $("#end").val(end);
                        $("#PayOrder_MoneyMc").val((rule.MonthRule_Money * (pager.model.Owner_SpaceNum || 0)).toFixed(2));
                    }



                    $("#PayOrder_PayedMoney").val((rule.MonthRule_Money * (pager.model.Owner_SpaceNum || 0)).toFixed(2));


                    if (pager.model.Owner_CardType == 2) {
                        $("#PayOrder_Money").val(rule.MonthRule_EMoney.toFixed(2));
                        $("#Owner_Balance1").val(((pager.model.Owner_Balance || 0) + rule.MonthRule_EMoney).toFixed(2));
                        $("#PayOrder_PayedMoney").val(rule.MonthRule_Money.toFixed(2));
                    }
                });


            }
        };

    </script>
</body>
</html>
