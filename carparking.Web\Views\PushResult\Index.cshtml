﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>记录管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }

        .layui-form-select .layui-input { width: 182px; }

        .content { margin: 7px 19px 0px 19px !important; cursor: pointer; }

        .content:hover { color: #1E9FFF; font-weight: 600; font-size: 20px; }

        span.ss { font-size: 13px; text-align: justify; word-break: break-all; color: #61a8d1; background-color: #f5eeee; float: left; padding: 3px 5px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>记录管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input" name="PushResult_Hostname" id="PushResult_Hostname" autocomplete="off" placeholder="主机名称" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="PushResult_Hostip" id="PushResult_Hostip" autocomplete="off" placeholder="IP地址" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="PushResult_ReqTname" id="PushResult_ReqTname" autocomplete="off" placeholder="标识" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="PushResult_ReqPush" id="PushResult_ReqPush" autocomplete="off" placeholder="消息内容" value="" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="发送状态" class="form-control chosen-select " id="PushResult_Success" name="PushResult_Success" lay-search>
                                    <option value="">发送状态</option>
                                    <option value="0">失败</option>
                                    <option value="1">成功</option>
                                    <option value="3">取消</option>
                                    <option value="-1">未处理</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="处理结果" class="form-control chosen-select" id="PushResult_Ok" name="PushResult_Ok" lay-search>
                                    <option value="">处理结果</option>
                                    <option value="0">未处理</option>
                                    <option value="1">处理失败</option>
                                    <option value="2">处理成功</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="startDate" id="startDate" autocomplete="off" placeholder="开始日期" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="endDate" id="endDate" autocomplete="off" placeholder="截止日期" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Pulldown" lay-event="Pulldown"><i class="fa fa-cloud-download"></i><t>推送</t></button>
                            </div>
                        </script>
                    </div>
                    <div class="layui-col-xs10"><span class="ss"><b>温馨提示：</b>当前界面记录着由T30服务器向岗亭分发数据的实时状态，若发送状态为【失败】或者处理结果为【未处理】【处理失败】，表示服务器数据与岗亭数据未能及时同步，可以手动点击【推送】,重新向岗亭同步数据.</span></div>
                </div>
            </div>
        </div>

    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/html" id="TmplSuccess">
         {{#  if(d.PushResult_Success==-1){ }}
        <span class="layui-badge layui-bg-orange ">未处理</span>
        {{#  } else if(d.PushResult_Success==1) { }}
        <span class="layui-badge layui-bg-blue ">成功</span>
        {{#  } else if(d.PushResult_Success==3) { }}
        <span class="layui-badge layui-bg-gray ">取消</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-orange ">失败</span>
        {{#  } }}
    </script>
    <script type="text/html" id="TmplOK">
        {{#  if(d.PushResult_Ok==0){ }}
        <span class="layui-badge layui-bg-gray ">未处理</span>

        {{#  } else if(d.PushResult_Ok==1) { }}
        <span class="layui-badge layui-bg-orange ">处理失败</span>
        {{#  } else if(d.PushResult_Ok==2) { }}
        <span class="layui-badge layui-bg-blue ">处理成功</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-cyan ">未知</span>
        {{#  } }}
    </script>
    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var actionList = [{ action: "carcardtype", name: "车牌类型" }, { action: "coupon", name: "优惠券" }, { action: "cartype", name: "车牌颜色" }, { action: "deletecar", name: "车辆注销" }
            , { action: "carin", name: "出入场" }, { action: "carout", name: "车辆出场" }, { action: "specialcar", name: "特殊车辆" }, { action: "car", name: "车主车辆" }, { action: "owner", name: "车主车位" }, { action: "accessauth", name: "出入权限" }
            , { action: "backcar", name: "倒车记录" }, { action: "chargerule", name: "计费规则" }, { action: "blacklist", name: "黑名单" }, { action: "boardauth", name: "控制板授权" }, { action: "businesscar", name: "商家车辆" }, { action: "dateset", name: "日期设置" }
            , { action: "device", name: "设备" }, { action: "drive", name: "设备型号" }, { action: "endnumauth", name: "尾号限行" }, { action: "monthrule", name: "充值规则" }, { action: "parkarea", name: "区域信息" }, { action: "parkdiscountset", name: "优惠设置" }, { action: "parkorder", name: "停车订单" }, { action: "passway", name: "车道管理" }
            , { action: "payorder", name: "缴费记录" }, { action: "paypart", name: "缴费明细" }, { action: "paytype", name: "支付方式" }, { action: "policy", name: "停车场设置" }, { action: "powergroup", name: "权限设置" }, { action: "reserve", name: "访客车辆" }, { action: "sysconfig", name: "系统设置" }, { action: "sentryhost", name: "岗亭管理" }
            , { action: "paysuccess", name: "支付信息" }, { action: "onspacepaycharge", name: "车位有效期变更" }];

        layui.use(['table', 'form'], function () {
            pager.init();
            var table = layui.table;
            var layuiForm = layui.form;
            layuiForm.render("select");

            $("#startDate").val(new Date().Format("yyyy-MM-dd"));
            $("#endDate").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["startDate", "endDate"], { type: 'date', range: true });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'PushResult_No', title: '数据编码', hide: true }
                , { field: 'PushResult_Hostname', title: '主机名称', hide: true }
                , { field: 'PushResult_Hostip', title: 'IP地址' }
                , { field: 'PushResult_Hostport', title: '端口号', hide: true }
                , {
                    field: 'PushResult_ReqTname', title: '名称', templet: function (d) {
                        var action = d.PushResult_ReqTname.toLowerCase();
                        var name = d.PushResult_ReqTname;
                        for (var i = 0; i < actionList.length; i++) {
                            if (action.indexOf(actionList[i].action) != -1) {
                                name = actionList[i].name;
                                break;
                            }
                        }
                        return name;
                    }
                }
                , { field: 'PushResult_ReqTname', title: '标识' }
                , {
                    field: 'PushResult_ReqAct', title: '操作', templet: function (d) {
                        if (d.PushResult_ReqAct == 'Edit') { return tempBar(1, "编辑"); }
                        else if (d.PushResult_ReqAct == 'Add') { return tempBar(2, "新增"); }
                        else if (d.PushResult_ReqAct == 'Delete') { return tempBar(0, "删除"); }
                        else { return tempBar(4, d.PushResult_ReqAct); }
                    }
                }
                , {
                    field: 'PushResult_No', width: 100, title: '消息内容', templet: function (d) {
                        return '<i class="fa fa-search-plus content" title="点击查看" lay-event="showContent"></i>';//formatJson(d.PushResult_ReqPush);
                    }
                }
                , { field: 'PushResult_Success', title: '发送状态', toolbar: '#TmplSuccess' }
                , { field: 'PushResult_Errcode', title: '返回代号', hide: true }
                , { field: 'PushResult_Ok', title: '处理结果', toolbar: '#TmplOK', hide: true }
                , { field: 'PushResult_Time', title: '发送时间' }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/PushResult/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Pulldown':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        var dateTime = data[0].PushResult_Time;
                        for (var i = 0; i < data.length; i++) { NoArray.push(data[i].PushResult_No); }

                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "<font style='color:red;'>您正在推送记录到岗亭端，请谨慎操作。</font><br/>确定推送?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/PushResult/Pulldown", { NoArrayString: JSON.stringify(NoArray), dateTime: dateTime }, function (json) {
                                    if (json.success)
                                        layer.msg(json.msg, { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            table.on("tool(com-table-base)", function (obj) {
                var data = obj.data;
                switch (obj.event) {
                    case 'showContent':
                        layer.msg("处理中...", { icon: 16, time: 0 });
                        $.getJSON("/PushResult/GetResult", { PushResult_No: data.PushResult_No, PushResult_Time: data.PushResult_Time }, function (json) {
                            if (json.success)
                                try {
                                    layer.closeAll();
                                    var subData = JSON.parse(json.data);
                                    layer.open({
                                        type: 1,
                                        title: '消息内容',
                                        area: ['700px', '520px'], //宽高
                                        btn: ['关闭'],
                                        content:
                                            ' <div class="layui-content">' +
                                            '<div class="layui-row"><pre>' +
                                            JSON.stringify(JSON.parse(subData.data), null, 2) +
                                            '</pre></div>' +
                                            '</div>',
                                        success: function () {
                                        }
                                    });
                                } catch {
                                    layer.open({
                                        type: 1,
                                        title: '消息内容',
                                        area: ['700px', '520px'], //宽高
                                        btn: ['关闭'],
                                        content:
                                            ' <div class="layui-content">' +
                                            '<div class="layui-row"><pre>' +
                                            JSON.stringify(data, null, 2) +
                                            '</pre></div>' +
                                            '</div>',
                                        success: function () {
                                        }
                                    });
                                }
                            else
                                layer.msg(json.msg, { icon: 0, time: 1500 });
                        });
                        break;
                }
            });

            tb_row_checkbox();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/PushResult/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
    </script>
</body>
</html>
