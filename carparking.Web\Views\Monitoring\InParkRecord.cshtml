﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>记录查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        .layui-fluid { padding: 0; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; }
        ::-webkit-scrollbar-thumb { border-radius: 10px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }

        .layui-input { border-color: #ddd !important; font-size: .9rem; }
        .layui-btn { font-size: .9rem !important; }
        .layui-table-cell { font-size: .9rem; }

        .layui-table-click { background-color: #f2f3f3; color: #2F4056; font-weight: bold; }
    </style>
    <style data-mark="表格列数量多的时候使用此样式展示列选择">
        .layui-table-tool-panel { width: 500px; }
        .layui-table-tool-panel li { width: 33.33%; float: left; }
        .layui-tab-title { background-color: #5868e0 !important; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">

                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号（精确查询）" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo1" id="ParkOrder_CarNo1" autocomplete="off" placeholder="车牌号(模糊查询)" maxlength="8" />
                            </div>
                            <div class="layui-inline status">
                                <select data-placeholder="订单状态" class="form-control chosen-select " id="ParkOrder_StatusNo" name="ParkOrder_StatusNo" lay-search>
                                    <option value="" selected>订单状态</option>
                                    <option value="200">已入场</option>
                                    <option value="201">已出场</option>
                                    <option value="199">预入场</option>
                                    <option value="202">自动关闭</option>
                                    <option value="203">场内关闭</option>
                                    <option value="204">欠费出场</option>
                                    <option value="0">预出场</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" value="@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">

                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_No" id="ParkOrder_No" autocomplete="off" placeholder="订单编号" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="form-control chosen-select " id="ParkOrder_CarType" name="ParkOrder_CarType" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入场车道" class="form-control chosen-select " id="ParkOrder_EnterPasswayNo" name="ParkOrder_EnterPasswayNo" lay-search>
                                    <option value="">入场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入口操作员" class="layui-input" id="ParkOrder_EnterAdminAccount" name="ParkOrder_EnterAdminAccount" lay-search>
                                    <option value="">入口操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterAdminName" id="ParkOrder_EnterAdminName" autocomplete="off" placeholder="入口操作员" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="锁车状态" class="form-control chosen-select " id="ParkOrder_Lock" name="ParkOrder_Lock" lay-search>
                                    <option value="">锁车状态</option>
                                    <option value="0">未锁车</option>
                                    <option value="1">已锁车</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime0" id="ParkOrder_OutTime0" autocomplete="off" placeholder="出场时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime1" id="ParkOrder_OutTime1" autocomplete="off" placeholder="出场时间止" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="出场车道" class="form-control chosen-select " id="ParkOrder_OutPasswayNo" name="ParkOrder_OutPasswayNo" lay-search>
                                    <option value="">出场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="出口操作员" class="layui-input" id="ParkOrder_OutAdminAccount" name="ParkOrder_OutAdminAccount" lay-search>
                                    <option value="">出口操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutAdminName" id="ParkOrder_OutAdminName" autocomplete="off" placeholder="出口操作员" />
                            </div>

                            <div class="layui-inline">
                                <select data-placeholder="停车区域" class="form-control chosen-select " id="ParkOrder_ParkAreaNo" name="ParkOrder_ParkAreaNo" lay-search>
                                    <option value="">停车区域</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="无入场记录" class="form-control chosen-select " id="ParkOrder_IsNoInRecord" name="ParkOrder_IsNoInRecord" lay-search>
                                    <option value="">无入场记录</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_IsEpCar" name="ParkOrder_IsEpCar" lay-search>
                                    <option value="">重点地区车辆</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarLogo" id="ParkOrder_CarLogo" autocomplete="off" placeholder="车辆车标" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OwnerName" id="ParkOrder_OwnerName" autocomplete="off" placeholder="车主姓名" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OwnerSpace" id="ParkOrder_OwnerSpace" autocomplete="off" placeholder="系统车位号" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Add" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Detail" lay-event="Detail"><i class="fa fa-list-alt"></i><t>详情</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Import" lay-event="Import"><i class="fa fa-file-excel-o"></i><t>导入</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="HandlePreRecord" lay-event="HandlePreRecord"><i class="fa fa-file-text"></i><t>预入出场处理</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>

    </div>
    @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.WindowsStandard)
    {
        <div style="width: 100%;height: 20px;position: absolute;bottom: 10px; right:0px; opacity: .5;">
            <span style="float:left;margin-left:20px;color:black;" id="time"></span>
        </div>
    }
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?4.0" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script>
        var Parking_Key = '@ViewBag.Parking_Key';
        var code = $.getUrlParam("code");
        var isFrpUrl = IsFrpURLOpenWeb(Parking_Key);

        var versionMode = '@Html.Raw(carparking.Config.AppSettingConfig.SentryMode)';
        var parkIndex = '';
        // 添加实时日期和时间
        function updateTime() {
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            var hours = now.getHours();
            var minutes = now.getMinutes();
            var seconds = now.getSeconds();
            var dateString = year + '-' + month + '-' + day;
            var timeString = hours + ':' + minutes + ':' + seconds;
            document.getElementById('time').innerHTML = dateString + " " + timeString;
        }
        if (versionMode != "0") {
            setInterval(updateTime, 1000); // 每秒钟更新一次时间
        }

        topBar.init();

        var paramInOutType = '@ViewBag.InOutType';
        // if (paramInOutType === 1 || paramInOutType === 2) {
        //     $(".status").addClass("layui-hide");
        //     $("#HandlePreRecord").removeClass("layui-hide");
        // } else {
        //     $(".status").removeClass("layui-hide");
        // }

        s_carno_picker.init("ParkOrder_CarNo", function (text, carno) { }, "web").bindkeyup();
        s_carno_picker.init("ParkOrder_CarNo1", function (text, carno) { }, "web").bindkeyup();

        var comtable = null;
        layui.use(['table', 'form', 'laydate', 'element'], function () {
            var table = layui.table;

            searchFile.bindData(0);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                if (paramInOutType == 1) {
                    data.ParkOrder_StatusNo = 199;
                } else if (paramInOutType == 2) {
                    data.ParkOrder_StatusNo = 200;
                    data.ParkOrder_OutType = 1;
                }
                data.PageInOutType = paramInOutType;
                return data;
            });
            conditionParam.code = code;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'ParkOrder_ID', title: '订单ID', hide: true, minWidth: 80 }
                , { field: 'ParkOrder_No', title: '订单号', hide: true, minWidth: 110 }
                , { field: 'ParkOrder_ParkNo', title: '车场编码', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_CarNo', title: '车牌号', minWidth: 120 }
                , { field: 'ParkOrder_CarCardType', title: '车牌类型编码', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型', minWidth: 95 }
                , { field: 'ParkOrder_CarType', title: '车牌颜色编码', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色', minWidth: 95 }

                , {
                    field: 'ParkOrder_StatusNo', title: '订单状态', minWidth: 95, templet: function (d) {
                        if (d.ParkOrder_StatusNo == 199) return tempBar(2, "预入场");
                        else if (d.ParkOrder_StatusNo == 200) {
                            if (d.ParkOrder_OutType == 0) return tempBar(1, "已入场");
                            if (d.ParkOrder_OutType == 1) return tempBar(3, "预出场");
                        }
                        else if (d.ParkOrder_StatusNo == 201) return tempBar(4, "已出场");
                        else if (d.ParkOrder_StatusNo == 202) return tempBar(0, "自动关闭");
                        else if (d.ParkOrder_StatusNo == 203) return tempBar(0, "场内关闭");
                        else if (d.ParkOrder_StatusNo == 204) return tempBar(6, "欠费出场");
                    }
                }
                , { field: 'ParkOrder_EnterTime', title: '入场时间', sort: true, minWidth: 185 }
                , { field: 'ParkOrder_EnterPasswayNo', title: '入口车道编码', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_EnterPasswayName', title: '入口车道', minWidth: 120 }
                , { field: 'ParkOrder_EnterAdminAccount', title: '入口操作员账号', hide: true, minWidth: 130 }
                , { field: 'ParkOrder_EnterAdminName', title: '入口操作员', hide: true, minWidth: 130 }
                , {
                    field: 'ParkOrder_EnterImgPath', title: '入场图片', minWidth: 100, templet: function (d) {
                        var img = d.ParkOrder_EnterImgPath;
                        if (isFrpUrl) {
                            img = replaceFirstPathSegment(img);
                        }
                        return tempImg(img);
                    }
                }
                , { field: 'ParkOrder_EnterRemark', title: '入场备注', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_OutTime', title: '出场时间', sort: true, minWidth: 185 }
                , { field: 'ParkOrder_OutPasswayNo', title: '出口车道编码', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_OutPasswayName', title: '出口车道', minWidth: 110 }
                , { field: 'ParkOrder_OutAdminAccount', title: '出口操作员账号', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_OutAdminName', title: '出口操作员', hide: true, minWidth: 100 }
                , {
                    field: 'ParkOrder_OutImgPath', title: '出场图片', minWidth: 100, templet: function (d) {
                        var img = d.ParkOrder_OutImgPath;
                        if (isFrpUrl) {
                            img = replaceFirstPathSegment(img);
                        }
                        return tempImg(img);
                    }
                }
                , {
                    field: 'ParkOrder_IsFree', title: '免费放行', minWidth: 100, templet: function (d) {
                        if (d.ParkOrder_IsFree == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }
                , { field: 'ParkOrder_FreeReason', title: '免费原因', hide: true, minWidth: 100 }
                , {
                    field: 'ParkOrder_IsLift', title: '智能升降', minWidth: 100, templet: function (d) {
                        if (d.ParkOrder_IsLift != 0 && d.ParkOrder_IsLift != null) return tempBar(1, "是");
                        else return tempBar(3, "否");
                    }
                }
                , {
                    field: 'ParkOrder_Lock', title: '锁车状态', minWidth: 100, hide: true, templet: function (d) {
                        if (d.ParkOrder_Lock == 1) return tempBar(1, "已锁车");
                        else if (d.ParkOrder_Lock == 0) return tempBar(3, "未锁车");
                    }
                }
                , { field: 'ParkOrder_ParkAreaNo', title: '停车区域编码', hide: true, minWidth: 100 }
                , { field: 'ParkOrder_ParkAreaName', title: '停车区域', minWidth: 120 }
                , { field: 'ParkOrder_TotalAmount', title: '应收金额', totalRow: true, sort: true, minWidth: 120 }
                , { field: 'ParkOrder_TotalPayed', title: '实收金额', totalRow: true, sort: true, minWidth: 120 }
                , { field: 'ParkOrder_UserNo', title: '用户', hide: true, minWidth: 100 }
                , {
                    field: 'ParkOrder_PayScene', title: '支付场景', minWidth: 100, hide: true, templet: function (d) {
                        if (d.ParkOrder_PayScene == 1) return tempBar(1, "场内缴费");
                        else if (d.ParkOrder_PayScene == 2) return tempBar(2, "出口缴费");
                        else return "";
                    }
                }
                , { field: 'ParkOrder_Remark', title: '订单备注', minWidth: 100 }
                , {
                    field: 'ParkOrder_IsNoInRecord', title: '无入场记录', minWidth: 100, hide: true, templet: function (d) {
                        if (d.ParkOrder_IsNoInRecord == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }, {
                    field: 'ParkOrder_IsEpCar', title: '重点地区车辆', minWidth: 100, hide: true, templet: function (d) {
                        if (d.ParkOrder_IsEpCar == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                },
                { field: 'ParkOrder_CarLogo', title: '车辆车标', minWidth: 100, hide: true }
            ]];
            cols = tb_page_cols(cols, "InParkRecord");

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/Monitoring/GetParkOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d, "InParkRecord");
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].ParkOrder_No);
                        }

                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "<font style='color:red;'>您正在删除停车记录，请谨慎操作。</font><br/>确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/Monitoring/DeleteParkOrder", { ParkOrder_NoArray: JSON.stringify(NoArray) }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'HandlePreRecord':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].ParkOrder_No);
                        }

                        var orderMsg = "";
                        if (paramInOutType == 1) orderMsg = "入场状态";
                        else if (paramInOutType == 2) orderMsg = "出场状态";

                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "<font style='color:red;'>您正在将停车记录变更" + orderMsg + "，请谨慎操作。</font><br/>（若批量处理订单，请确保车辆没有在其它车道正在出入场或正在登记操作，否则容易造成数据状态异常）确定变更处理?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("/Monitoring/HandlePreRecord", { ParkOrder_NoArray: JSON.stringify(NoArray), InOutType: paramInOutType }, function (json) {
                                    if (json.success)
                                        layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };

            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });
            // 监听行点击事件
            table.on('row(com-table-base)', function (obj) {
                // 移除所有行的选中样式
                obj.tr.siblings().removeClass('layui-table-click');
                // 添加当前行的选中样式
                obj.tr.addClass('layui-table-click');
            });

            tb_row_checkbox();

            pager.init();
        });
    </script>
    <script>
        var pager = {
            sortField: null,
            orderField: null,
            dataField: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#ParkOrder_CarType").append(option)
                        });
                    }
                }, "json");

                $.post("SltPasswayList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#ParkOrder_EnterPasswayNo").append(option);
                            $("#ParkOrder_OutPasswayNo").append(option);
                        });
                    }
                }, "json");

                $.post("SltParkAreaList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                            $("#ParkOrder_ParkAreaNo").append(option);
                        });
                    }
                }, "json");

                $.post("SltAdminList", { code: code }, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Admins_Account + '">' + d.Admins_Name + '</option>';
                            $("#ParkOrder_EnterAdminAccount").append(option);
                            $("#ParkOrder_OutAdminAccount").append(option);
                        });
                    }
                }, "json");

                layui.form.render();

                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: "datetime", range: true });
                _DATE.bind(layui.laydate, ["ParkOrder_OutTime0", "ParkOrder_OutTime1"], { type: "datetime", range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                    if (paramInOutType == 1) {
                        data.ParkOrder_StatusNo = 199;
                    } else if (paramInOutType == 2) {
                        data.ParkOrder_StatusNo = 200;
                        data.ParkOrder_OutType = 1;
                    }
                    data.PageInOutType = paramInOutType;
                    return data;
                });
                conditionParam.code = code;
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/Monitoring/GetParkOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                if (paramInOutType == 1 || paramInOutType == 2) {
                    $("#HandlePreRecord").removeClass("layui-hide");
                    $("#Delete").removeClass("layui-hide");
                }
            }
        }
    </script>
</body>
</html>
