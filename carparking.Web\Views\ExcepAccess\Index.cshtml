﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>异常出入场记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
    <style data-mark="表格列数量多的时候使用此样式展示列选择">
        .layui-table-tool-panel { width: 500px; }
        .layui-table-tool-panel li { width: 33.33%; float: left; }
        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>异常出入场记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号（精确查询）" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo1" id="ParkOrder_CarNo1" autocomplete="off" placeholder="车牌号(模糊查询)" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                           
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" value="@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>
                            
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                            
                        </div>


                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_No" id="ParkOrder_No" autocomplete="off" placeholder="订单编号" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入场车道" class="form-control chosen-select " id="ParkOrder_EnterPasswayNo" name="ParkOrder_EnterPasswayNo" lay-search>
                                    <option value="">入场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入口操作员" class="layui-input" id="ParkOrder_EnterAdminAccount" name="ParkOrder_EnterAdminAccount" lay-search>
                                    <option value="">入口操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterAdminName" id="ParkOrder_EnterAdminName" autocomplete="off" placeholder="入口操作员" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime0" id="ParkOrder_OutTime0" autocomplete="off" placeholder="出场时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime1" id="ParkOrder_OutTime1" autocomplete="off" placeholder="出场时间止" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_OutPasswayNo" name="ParkOrder_OutPasswayNo" lay-search>
                                    <option value="">出场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="出口操作员" class="layui-input" id="ParkOrder_OutAdminAccount" name="ParkOrder_OutAdminAccount" lay-search>
                                    <option value="">出口操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutAdminName" id="ParkOrder_OutAdminName" autocomplete="off" placeholder="出口操作员" />
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Detail" lay-event="Detail"><i class="fa fa-list-alt"></i><t>详情</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script type="text/x-jquery-tmpl" id="tmplouttype">
        {{# if(d.ParkOrder_OutType==0){}}
        <span class="layui-badge layui-bg-gray">未出场</span>
        {{# }else if(d.ParkOrder_OutType==1){}}
        <span class="layui-badge layui-bg-blue">预出场</span>
        {{# }else if(d.ParkOrder_OutType==2){}}
        <span class="layui-badge layui-bg-green">已出场</span>
        {{# } }}
    </script>
    <script>

        var isFrpUrl = IsFrpURLOpenWeb('@Html.Raw(ViewBag.ParkKey)');

        s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
            if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                $("#ParkOrder_CarNo").val(carno.join(''));
            }
        }, "web").bindkeyup();
        s_carno_picker.init("ParkOrder_CarNo1", function (text, carno) {
            if (s_carno_picker.eleid == "ParkOrder_CarNo1") {
                $("#ParkOrder_CarNo1").val(carno.join(''));
            }
        }, "web").bindkeyup();

        topBar.init();

        var gotoData = localStorage.getItem("gotoInParkRecord");
        if (gotoData != null && gotoData != '' && gotoData != "null") {
            try {
                var gotod = JSON.parse(gotoData);
                $("#searchForm select").val('');
                $("#searchForm input").val('');

                $("#ParkOrder_No").val(gotod.ParkOrder_No || "");
                $("#ParkOrder_CarNo").val(gotod.ParkOrder_CarNo || "");
                $("#ParkOrder_CarNo1").val(gotod.ParkOrder_CarNo1 || "");
                $("#ParkOrder_IsEpCar").val(gotod.ParkOrder_IsEpCar || "");
                $("#ParkOrder_StatusNo").val(gotod.ParkOrder_StatusNo || "");
                $("#ParkOrder_EnterTime0").val(gotod.ParkOrder_EnterTime0 || "");
                $("#ParkOrder_EnterTime1").val(gotod.ParkOrder_EnterTime1 || "");

                layui.form.render();
                localStorage.setItem("gotoInParkRecord", null);
            } catch (e) {
                console.log(e);
            }
        }

        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        var element = null;

        layui.use(['table', 'form', 'laydate', 'element'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            element = layui.element;

            searchFile.bindData(0);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            pager.conditionParam = conditionParam;

            var cols = [[
                { type: 'checkbox' }
                , { field: 'ExcepAccess_No', title: '异常出入场记录编号', hide: true }
                , { field: 'ExcepAccess_CarNo', title: '车牌号', width: 120 }
                , { field: 'ExcepAccess_CarRecogNo', title: '识别记录编号' }
                , { field: 'ExcepAccess_ParkOrderNo', title: '停车订单编号' }
                , { field: 'ExcepAccess_CarCardTypeNo', title: '车牌类型编号', hide: true } 
                , { field: 'ExcepAccess_CarCardTypeName', title: '车牌类型' }
                , { field: 'ExcepAccess_InTime', title: '入场时间', width: 160, sort: true }
                , { field: 'ExcepAccess_OutTime', title: '出场时间', width: 160, sort: true }
                , { field: 'ExcepAccess_TotalAmount', title: '应收总金额', totalRow: true, templet: function (d) { if (d.ExcepAccess_TotalAmount != null) return ToFixed2(d.ExcepAccess_TotalAmount); else return ToFixed2(0); }, sort: true }
                , { field: 'ExcepAccess_TotalPayed', title: '实收总金额', totalRow: true, templet: function (d) { if (d.ExcepAccess_TotalPayed != null) return ToFixed2(d.ExcepAccess_TotalPayed); else return ToFixed2(0); }, sort: true }
                , { field: 'ExcepAccess_EnterPasswayNo', title: '入口车道编号' , hide: true}
                , { field: 'ExcepAccess_EnterPasswayName', title: '入口车道名称' }
                , { field: 'ExcepAccess_OutPasswayNo', title: '出口车道编号', hide: true }
                , { field: 'ExcepAccess_OutPasswayName', title: '出口车道名称' }
                , { field: 'ExcepAccess_OutAdminAccount', title: '出口操作员', hide: true }
                , { field: 'ExcepAccess_OutAdminName', title: '出口操作员名称' }
                , { field: 'ExcepAccess_Remark', title: '订单备注' }
            ]];


            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/ExcepAccess/GetExcepAccessList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    pager.dataCount = d.count;
                    tb_page_set(d);
                    pager.bindPower();
                    onPreviewImage.init();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }
                        var orderno = data[0].ParkOrder_No;
                        layer.open({
                            title: "详情",
                            type: 2, id: 1,
                            area: ['95%', '95%'],
                            fix: true, //不固定
                            maxmin: true,
                            content: 'Detail?From=ParkOrder&ParkOrder_No=' + encodeURIComponent(orderno)
                        });
                        break;

                    default: break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox(table);

            pager.init();
        });
    </script>
    <script>
        var pager = {
            sortField: null,
            orderField: null,
            dataField: null,
            conditionParam: null,
            pageIndex: 1,
            dataCount: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option)
                        });
                    }
                }, "json");

                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#ParkOrder_CarType").append(option)
                        });
                    }
                }, "json");

                $.post("SltGatePasswayList", {}, function (json) {
                    if (json.success) {
                        var optionIn = '';
                        var optionOut = '';
                        json.data.forEach(function (d, i) {
                            if (d.Passway_GateType == 0 || d.Passway_GateType == 3)
                                optionOut += '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            else
                                optionIn += '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                        });
                        $("#ParkOrder_EnterPasswayNo").append(optionIn);
                        $("#ParkOrder_OutPasswayNo").append(optionOut);
                    }
                }, "json");
                $.post("SltParkAreaList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                            $("#ParkOrder_ParkAreaNo").append(option);
                        });
                    }
                }, "json");

                $.post("SltAdminList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Admins_Account + '">' + d.Admins_Name + '</option>';
                            $("#ParkOrder_EnterAdminAccount").append(option);
                            $("#ParkOrder_OutAdminAccount").append(option);
                        });
                    }
                }, "json");

                layui.form.render("select");
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: "datetime", range: true });
                _DATE.bind(layui.laydate, ["ParkOrder_OutTime0", "ParkOrder_OutTime1"], { type: "datetime", range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                var field = pager.sortField == null ? "" : pager.sortField;
                var order = pager.orderField == null ? "" : pager.orderField;
                comtable.reload({
                    url: '/ExcepAccess/GetExcepAccessList'
                    , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    console.log(pagePower)
                });
            }
        }
    </script>
</body>
</html>
