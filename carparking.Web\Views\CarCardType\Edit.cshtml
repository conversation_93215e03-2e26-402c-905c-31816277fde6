﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .layui-anim-upbit { max-height: 200px !important; }
        .layui-select-disabled .layui-disabled { color: #333 !important; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车牌类型</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-minlen" data-minlen="2" maxlength="32" id="CarCardType_Name" name="CarCardType_Name" placeholder="长度限制(2~32)" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">车牌分类</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select data-placeholder="车牌分类" class="layui-input" id="CarCardType_Category" name="CarCardType_Category" lay-search lay-filter="CarCardType_Category">
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">用于一位多车</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select class="layui-input" id="CarCardType_IsMoreCar" name="CarCardType_IsMoreCar" lay-search>
                        <option value="0">否</option>
                        <option value="1">是</option>
                    </select>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">下发白名单</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select class="layui-input" id="CarCardType_WhiteEnable" name="CarCardType_WhiteEnable" lay-search>
                        <option value="0">禁用</option>
                        <option value="1">启用</option>
                    </select>
                    <span class="label-desc">用于车辆登记。启用，登记车辆时则默认向相机下发白名单；禁用，登记车辆时则默认不下发</span>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">屏显/播报名称</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <select class="layui-input" id="CarCardType_ShowText" name="CarCardType_ShowText" lay-search>
                        <option value="">默认</option>
                        <option value="0">时租车</option>
                        <option value="1">月租车</option>
                        <option value="2">储值车</option>
                        <option value="3">贵宾车</option>
                        <option value="4">内部车</option>
                        <option value="5">持证车</option>
                        <option value="6">免费车</option>
                        <option value="7">业主车</option>
                        <option value="8">VIP车</option>
                        <option value="9">直通车</option>
                        <option value="10">登记车</option>
                    </select>
                    <div class="label-desc">可以设置当前类型车辆出入场时,电子屏及语音播报的名称。</div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">入口语音</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" maxlength="20" id="CarCardType_InVoice" name="CarCardType_InVoice" autocomplete="off" placeholder="自定义入口语音" />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">出口语音</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input class="layui-input" maxlength="20" id="CarCardType_OutVoice" name="CarCardType_OutVoice" autocomplete="off" placeholder="自定义出口语音" />
                </div>
            </div>
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${VehicleType_No}">${VehicleType_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var layuiForm = null;
        layui.use('form', function () {
            layuiForm = layui.form;
            pager.init()
        });

    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("CarCardType_No");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            model: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltVehicleTypeList", {}, function (json) {
                    if (json.success) {
                        $("#CarCardType_Category").html($("#tmplcartype").tmpl(json.data));
                    }
                    else {
                        layer.msg(json.msg);
                    }
                }, "json");
                layuiForm.render("select");
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("GetDetail", { CarCardType_No: paramNo }, function (json) {
                        if (json.Success) {
                            pager.model = json.Data;
                            $("#verifyCheck").fillForm(json.Data, function (data) { });
                            $("#CarCardType_Category").attr("disabled", "disabled");
                            layuiForm.render("select")
                        }
                    });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.CarCardType_Major = $("#CarCardType_Major").val();
                        data.CarCardType_Remark = $("#CarCardType_Remark").val();
                        data.CarCardType_ShowText = $("#CarCardType_ShowText").val();
                        data.CarCardType_InVoice = $("#CarCardType_InVoice").val();
                        data.CarCardType_OutVoice = $("#CarCardType_OutVoice").val();
                        return data;
                    });

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.post("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        }, "json");
                    }
                    else if (paramAct == "Update") {
                        param.CarCardType_No = paramNo;
                        $.post("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        }, "json");

                        //if (param.CarCardType_IsMoreCar != pager.model.CarCardType_IsMoreCar) {
                        //    var msg = "";
                        //    if (param.CarCardType_IsMoreCar == 1) {
                        //        msg = "<t style='color:red;'>启用一位多车保存时将会修改已登记此车牌类型的车辆为一位多车.若车辆信息已启用白名单,需要管理员重新下载</t>";
                        //    } else {
                        //        msg = "<t style='color:red;'>取消一位多车保存时将会修改已登记此车牌类型的车辆为普通固定车.若车辆信息已启用白名单,需要管理员重新下载</t>";
                        //    }
                        //    layer.open({
                        //        title: "消息提示",
                        //        content: msg,
                        //        btn: ["确定", "取消"],
                        //        area: getIframeArea(["300px","220px"]),
                        //        yes: function () {
                        //            $.post("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                        //                if (json.Success) {
                        //                    layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                        //                        window.parent.pager.bindData(1);
                        //                    });
                        //                } else {
                        //                    layer.msg(json.Message, { icon: 0 });
                        //                    $("#Save").removeAttr("disabled");
                        //                }
                        //            }, "json");
                        //        },
                        //        end: function () {
                        //            $("#Save").removeAttr("disabled");
                        //            layer.closeAll();
                        //        }
                        //    })
                        //} else {
                        //    $.post("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                        //        if (json.Success) {
                        //            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                        //                window.parent.pager.bindData(1);
                        //            });
                        //        } else {
                        //            layer.msg(json.Message, { icon: 0 });
                        //            $("#Save").removeAttr("disabled");
                        //        }
                        //    }, "json");
                        //}
                    }
                });

                layuiForm.on("select", function (data) {
                    var val = data.value;
                    var id = data.elem.id;
                    var key = $(data.elem).attr("data-key");
                    if (id == "CarCardType_IsMoreCar") {
                        if (val == "1") {
                            var layIndex = layer.open({
                                title: "温馨提示",
                                content: "您开启后，该类型已入场的车辆不会生效，下次入场才会生效，确认开启吗？",
                                area: ["350px", "200px"],
                                btn: ["确定", "取消"],
                                yes: function () { layer.close(layIndex) },
                                btn2: function () {
                                    $("#CarCardType_IsMoreCar").val("0");
                                    layuiForm.render("select");
                                },
                                cancel: function () { }
                            })
                        }
                    }
                });
            },
        };
    </script>
</body>
</html>
