﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>生成签名</title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <style>
        html, body { width: 100%; height: 100%; }
        .layui-card { width: 800px; min-height: 100%; margin: 0 auto 0; padding-right: 30px; background-color: #f9f9f9; }
        .layui-form-label { width: 120px; font-weight: bold; }
        .layui-input-block { margin-left: 150px; }
        .layui-input { color: #300bee; }
        .layui-input[readonly] { background-color:#f5f5f5;}
        .layui-btn[disabled] { background-color: #666 !important; }
    </style>
</head>
<body>
    <div class="layui-card">
        <div class="layui-card-header">生成签名</div>
        <div class="layui-card-body layui-form">
       
            <div class="layui-form-item">
                <label class="layui-form-label">parkkey</label>
                <div class="layui-input-block">
                    <input type="text" id="parkkey" placeholder="请输入停车场编码" class="layui-input" value="">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">appid</label>
                <div class="layui-input-block">
                    <input type="text" id="appid" placeholder="请输入appid" class="layui-input" value="">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">timestamp</label>
                <div class="layui-input-block">
                    <input type="text" id="timestamp" placeholder="请输入时间戳" class="layui-input" value="@Html.Raw(DateTime.Now.ToString("yyyyMMddHHmmss"))">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">version</label>
                <div class="layui-input-block">
                    <input type="text" id="version" placeholder="请输入停车场version" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">Json</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:200px;" id="data"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">&nbsp;</label>
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm" id="btnSendApi" onclick="sendApi()"><i class="layui-icon layui-icon-ok-circle"></i><text>提交</text></button>
                </div>
            </div>
         
            <div class="layui-form-item">
                <label class="layui-form-label">返回结果</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" style="height:200px;" id="result"></textarea>
                </div>
            </div>
        </div>
    </div> 
    <script type="text/javascript">
        var sendApi = function () {
            submitBtn.disabled();
            var str = "";
            str  = $("#data").val();
            var item = str.replace(/[\r\n]/g, '')
            var json = {

                appid:$("#appid").val(),
                parkkey: $("#parkkey").val(),
                timestamp: $("#timestamp").val(),
                version: $("#version").val(),
                data:item
            };
            var jsonData = JSON.stringify(json);
            $.post("/GetSign/ResponeSign", {jsonData: jsonData }, function (json) {
                //layer.msg(json.msg);
                submitBtn.enable();          
                $("#result").val(json.sign);
            }, "json")
        };

        var submitBtn = {
            enable: function () { $("#btnSendApi").removeAttr("disabled"); },
            disabled: function () { $("#btnSendApi").attr("disabled", true); }
        }

    </script>
</body>
</html>