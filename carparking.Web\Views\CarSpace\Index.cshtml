﻿
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车位类型设置</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <style>
        html, body { height:100%;width:100%;overflow:auto; }

        .layui-tab { margin: 0;  background: #fff; height:100%;position:relative; }

        .layui-tab-title { padding-left: 2rem; padding-top: 15px; }
        .layui-tab-title li.layui-this { color: #336afc; font-weight: bold; }
        .layui-tab-title li { padding-left: 2rem; }
        .layui-tab-title li::before { content: ""; position: absolute; padding: .5rem; left: .8rem; top: .75rem; background-size: 100% 100%; }
        .layui-tab-title li.type1::before { background-image: url('../../Static/img/icon/icon_number.svg'); }
        .layui-tab-title li.type2::before { background-image: url('../../Static/img/icon/icon_type.svg'); }    

        .layui-tab-title li.layui-this.type1::before { background-image: url('../../Static/img/icon/icon_number1.svg'); }
        .layui-tab-title li.layui-this.type2::before { background-image: url('../../Static/img/icon/icon_type1.svg'); }

        .layui-tab-content { padding: 0; position: absolute; bottom: 0; top: 60px; left: 0; right: 0; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .layui-tab-item.full { width: 100%; height: 100%; }
        .layui-tab-item.full iframe{width:100%;height:100%;border:0;}

        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right:5px;}
    </style>
</head>
<body>

    <div class="layui-tab">
        <ul class="layui-tab-title">
            <li class="type1 layui-this">车位类型定义</li>
            <li class="type2">车位数量定义</li>
        </ul>
        <div class="layui-tab-content layui-form">
            <!--车位类型定义-->
            <div class="layui-tab-item full layui-show">
                <iframe id="CarSpaceType" src="CarSpaceType"></iframe>
            </div>

            <!--车位数量定义-->
            <div class="layui-tab-item full">
                <iframe id="CarSpaceNumber" src="CarSpaceNumber"></iframe>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script type="text/javascript">
        var layuiForm = null;
        layui.use(['element', 'form'], function () {
            layuiForm = layui.form;

            pager.init();
        })

        var pager = {           
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            carspacetype: null, //通道列表
            parkareas: null,    //场区列表
           
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            bindData: function () {
            },
            bindEvent: function () {

            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                    }
                });
            }
        }
    </script>
</body>
</html>
