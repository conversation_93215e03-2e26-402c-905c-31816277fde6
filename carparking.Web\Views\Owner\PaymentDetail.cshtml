﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车详情</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
    <link href="~/Static/css/fishBone.css?v1.0" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        h3, span { color: black; font-weight: normal !important; }
        .ibox-content { padding-top: 0px !important; }
        body { margin-top: 0; }
        .fishBone { border: 1px solid #f5f5f5; }
        .form-group { margin-bottom: 10px; }
        .gray-bg { background-color: #fdf8f8; margin-top: 5px; }
        h3 { padding-top: 5px; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="form-group-sm center-block calcdetail hide">
                <table class="table table-striped table-hover table-calcdetail">
                    <thead>
                        <tr>
                            <th>车牌号</th>
                            <th>区域</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>计费时长</th>
                            <th>计费金额</th>
                            <th>优惠金额</th>
                            <th>免费分钟</th>
                            <th>计费描述</th>
                            <th>超时缴费</th>
                            <th>过期缴费</th>
                            <th>上个周期结束时间</th>
                            <th>上个周期累积金额</th>
                            <th>上个周期累计免费分钟</th>
                        </tr>
                    </thead>
                    <tbody id="data-view-calcdetail"></tbody>
                </table>
                <script id="data-tmpl-calcdetail" type="text/x-jquery-tmpl">
                    <tr>
                               <td>${CalcDetail_CarNo}</td>
                               <td><span class="fa fa-chevron-right"></span> ${CalcDetail_AreaName}</td>
                               <td>${CalcDetail_StartTime}</td>
                               <td>${CalcDetail_EndTime}</td>
                               <td>${getParkMin(CalcDetail_StartTime,CalcDetail_EndTime)}</td>
                               <td>${CalcDetail_PayedAmount}</td>
                               <td>${CalcDetail_CouponAmount}</td>
                               <td>${CalcDetail_UseMin}</td>
                               <td>${CalcDetail_PayedMsg}</td>
                                <td>
                                {{if CalcDetail_IsOverTime==1}}
                                 <span style="color: #ff0000;">是</span>
                                {{else}}
                                 否
                                {{/if}}
                                </td>
                                  <td>
                                {{if CalcDetail_IsCarExpire==1}}
                                 <span class="layui-badge layui-bg-red">是</span>
                                {{else}}
                                 否
                                {{/if}}
                                </td>

                               <td>${CalcDetail_PreCycleTime}</td>
                               <td>${CalcDetail_PreCycleMoney}</td>
                               <td>${CalcDetail_PreCycleMin}</td>
                    </tr>
                      <tr style="display: none;"> <td colspan="13">
                    {{if CalcDetail_CalcContent!=null && CalcDetail_CalcContent!=""}}
                         <div>
                             <p>${CalcDetail_CalcContent}</p>
                         </div>
                    {{/if}}
                    </td></tr>
                </script>
            </div>
        </div>
    </div>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/content.min.js?v=1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/js/fishBone.js?v1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.SuperSlide.2.1.1.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        var data3 = null;
        var pager = {
            init: function () {
                this.bindData();
            },
            bindEvent: function () {
            },
            //数据绑定
            bindData: function () {
                //计费详情
                var data = window.parent.pager.calcDetail;

                if (data && data != null && data.length > 0) {
                    $('#data-view-calcdetail').html("");
                    $(".calcdetail").removeClass("hide");
                    $('#data-view-calcdetail').html($('#data-tmpl-calcdetail').tmpl(data));

                    //绑定点击事件|设置隔两行变色
                    $('.table-calcdetail').each(function () {
                        var table = $(this);
                        table.children('tbody').children('tr').filter(':odd').hide();
                        table.children('tbody').children('tr').filter(':even').click(function () {
                            $(this).next().find("img").each(function () {
                                if ($(this).attr("data-img") != undefined)
                                    $(this).attr("src", $(this).attr("data-img"))
                            })
                            var element = $(this);
                            element.next('tr').toggle();
                            element.find(".fa-chevron-right").toggleClass("fa-chevron-down");
                        });

                        $.each(table.children('tbody').children('tr'), function (index, obj) {
                            var j = parseInt(index / 2);
                            if (j % 2 === 0) {
                                obj.bgColor = "#f9f9f9";
                            }
                        });
                    });
                } else {
                    $(".calcdetail").removeClass("hide").addClass("hide");
                }
            }
        };

        $(function () { pager.init() });

        function getParkMin(enterTime, outTime) {
            var d1 = new Date(enterTime);
            var d2 = new Date(outTime);
            var min = Math.floor(parseInt(d2 - d1) / 1000 / 60);
            return _DATE.getZhTimesbyMin(Math.ceil(min));
        }


        //获得年月日时分秒
        //传入日期//例：2020-10-27T14:36:23
        var timeFormatSeconds = function (time) {
            if (time == null || time == "") return "";
            var d = time ? new Date(time) : new Date();
            var year = d.getFullYear();
            var month = d.getMonth() + 1;
            var day = d.getDate();
            var hours = d.getHours();
            var min = d.getMinutes();
            var seconds = d.getSeconds();

            if (month < 10) month = '0' + month;
            if (day < 10) day = '0' + day;
            if (hours < 0) hours = '0' + hours;
            if (min < 10) min = '0' + min;
            if (seconds < 10) seconds = '0' + seconds;

            return (year + '-' + month + '-' + day + ' ' + hours + ':' + min + ':' + seconds);
        }
    </script>
</body>
</html>
