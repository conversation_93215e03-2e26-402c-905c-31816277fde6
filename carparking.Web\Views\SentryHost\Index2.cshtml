﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>岗亭主机设置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .leftmenu, .rightbody { height: 100%; overflow: auto; }
        @@media screen and (max-width: 400px) {
            .leftmenu, .rightbody { height: auto !important; }
        }
        .fa { margin: 7px 4px; float: left; font-size: 16px; }
        html, body { width: 100%; height: 100%; overflow-x: hidden; }
        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }
        .leftmenu { user-select: none; padding: 1rem; }
        .padding-15 { padding: 1rem; }
        .pan-title { font-size: 1.5rem; }
        .content-panel { height: 100%; overflow: auto; }
        .min100 { min-height: 100%; }

        .menu-list { width: 100%; overflow: auto; position: relative; }
        .menu-items { }
        .menu-item { padding-left: 0rem; }
        .menu-text:hover { background-color: #ecf0f5; cursor: pointer; text-decoration: underline; }
        .menu-text { padding: .5rem 4rem .5rem 1.1rem; line-height: 1.5rem; position: relative; }
        .menu-text::before { content: " "; padding: .5rem; background-size: 100% 100%; position: absolute; top: .75rem; }
        .menu-text::after { color: #ffffff; position: absolute; right: 0; padding: 0 3px; border-radius: 3px; font-size: 10px; }
        .menu-text.type1::after { content: "岗亭客户端"; background-color: #91d5f7; }
        .menu-text.type2::after { content: "岗亭服务器"; background-color: #3a4953; }
        .menu-text.type3::after { content: "系统服务器"; background-color: #91d5f7; }
        .menu-text.type1::before { background-image: url('../../Static/img/icon/icon_host.svg'); left: .5rem; }
        .menu-text.type2::before { background-image: url('../../Static/img/icon/icon_host_2.svg'); left: 0rem; }
        .menu-text.type3::before { background-image: url('../../Static/img/icon/icon_host_ser.svg'); left: .5rem; }
        .menu-text.type1 { padding-left: 1.6rem; }
        .menu-text.type2 { padding-left: 1.1rem; }
        .menu-text.type3 { padding-left: 1.6rem; }
        .menu-list div.m-active { font-weight: bold; }
        ::-webkit-scrollbar { width: 5px; background-color: rgba(0,0,0,.1); }
        ::-webkit-scrollbar-thumb { background-color: #4facfa; border-radius: 5px; }
        .m-active-1 { color: #f6800f; font-weight: bold; }
        #framePanl { padding: 1rem; }
        #framePanl iframe { border: 0; width: 100%; height: calc(100% - 4px); }
        .tounbind { text-decoration: underline; user-select: none; cursor: pointer; }
    </style>
</head>
<body class="animated fadeInRight">
    <div class="layui-col-sm3 leftmenu">
        <div class="layui-card min100">
            <div class="layui-card-header padding-15" id="addpanl">
                <button class="layui-btn layui-btn-fluid layui-bg-blue layui-hide" onclick="onOpenFrame()" id="Add">新增岗亭</button>
            </div>
            <div class="layui-card-body">
                <div class="menu-list">

                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-sm9 rightbody" id="framePanl">
        @*<iframe src="" frameborder="0"></iframe>*@
    </div>
    <script type="text/x-jquery-tmpl" id="tmpldata">
        <div class="menu-items">
            <!--左侧菜单列表-->
            <ul class="menu-item">
                <li>
                    <div class="menu-text type${SentryHost_Type}" onclick="area.openAreaItem(this)" data-no="${SentryHost_No}">${SentryHost_Name}</div>
                </li>
            </ul>
        </div>
    </script>

    <script type="text/x-jquery-tmpl" id="tmpldetail">
        <div class="content-panel">
            <div class="layui-card min100">
                <div class="layui-card-header padding-15 pan-title"><text>岗亭详情</text></div>
                <div class="layui-card-body layui-detail">
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">岗亭名称</label></div>
                        <div class="layui-col-xs6"><label class="value">${SentryHost_Name}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">岗亭编号</label></div>
                        <div class="layui-col-xs6"><label class="value">${SentryHost_No}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">系统部署类型</label></div>
                        <div class="layui-col-xs6">
                            {{if SentryHost_Type==1}}
                            <label class="layui-badge layui-bg-blue">岗亭客户端</label>
                            {{else SentryHost_Type==2 }}
                            <label class="layui-badge layui-bg-blue">岗亭服务器</label>
                            {{else SentryHost_Type==3}}
                            <label class="layui-badge layui-bg-blue">系统管理服务器</label>
                            {{/if}}
                        </div>
                    </div>
                    {{if SentryHost_Net==1}}
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">岗亭IP</label></div>
                        <div class="layui-col-xs6"><label class="value">${SentryHost_IP}</label></div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">岗亭端口</label></div>
                        <div class="layui-col-xs6"><label class="value">${SentryHost_Port}</label></div>
                    </div>
                    {{/if}}

                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">是否在线</label></div>
                        <div class="layui-col-xs6">
                            {{if SentryHost_Online==0}}
                            <label class="layui-badge">未连接</label>
                            {{else}}
                            <label class="layui-badge layui-bg-blue">已连接</label>
                            {{/if}}
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">主机类型</label></div>
                        <div class="layui-col-xs6">
                            {{if SentryHost_Category==1}}
                            <label class="layui-badge layui-bg-cyan">电脑主机</label>
                            {{else SentryHost_Category==2}}
                            <label class="layui-badge layui-bg-cyan">嵌入式主机</label>
                            {{/if}}

                            {{if SentryHost_BingHost != 0}}
                            <label class="layui-badge layui-bg-green">已绑定</label>
                            <a class="tounbind" data-no="${SentryHost_No}">解绑</a>
                            {{/if}}
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs3"><label class="name">管理的通道</label></div>
                        <div class="layui-col-xs6">
                            {{if linkdata!=null&&linkdata.length>0}}
                            <table class="layui-table" lay-filter="table">
                                <thead>
                                    <tr>
                                        <th>通道名称</th>
                                        <th>所属区域</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {{each linkdata}}
                                    <tr>
                                        <td>${$value.Passway_Name}</td>
                                        <td>${$value.Passway_AreaName}</td>
                                    </tr>
                                    {{/each}}
                                </tbody>
                            </table>
                            {{else}}
                            <label class="layui-badge layui-bg-gray">未设置</label>
                            {{/if}}
                        </div>
                    </div>
                    <hr />
                    <div class="layui-row">
                        <div class="layui-col-xs3">&nbsp;</div>
                        <div class="layui-col-xs8">
                            <button class="layui-btn layui-btn-sm layui-hide" id="Update" data-no="${SentryHost_No}"><i class="fa fa-edit"></i>编辑</button>
                            <button class="layui-btn layui-btn-sm layui-bg-red layui-hide" id="Delete" data-no="${SentryHost_No}" data-type="${SentryHost_Type}"><i class="fa fa-trash-o"></i>删除</button>
                            {{if SentryHost_Category==2}}
                            <button class="layui-btn layui-btn-sm layui-bg-blue" id="Desktop" data-no="${SentryHost_No}"><i class="fa fa-desktop"></i>岗亭配置</button>
                            {{/if}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();

        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index'], function () {
            pager.init();
        });

        //右侧新增按钮
        var onOpenFrame = function () {
            layer.open({
                type: 2, id: 1,
                title: "新增岗亭",
                content: "Edit?Act=Add",
                area: getIframeArea(["800px", "80%"]),
                maxmin: false
            });
            parent.top.setScrollTop(document.body, 0);
        }

        //打开详情展示
        var openDetail = function (itemno) {
            var item = null;
            pager.showData.forEach(function (d, i) {
                if (d.SentryHost_No == itemno) {
                    item = d;
                }
            });
            item.linkdata = [];
            if (item != null) {
                var linkdata = [];
                pager.passways.forEach(function (d, i) {
                    if (d.Passway_SentryHostNo == itemno) {
                        var passwayAreaName = [];
                        pager.links.forEach(function (d2, i2) {
                            if (d2.PasswayLink_PasswayNo == d.Passway_No) {
                                passwayAreaName[passwayAreaName.length] = d2.ParkArea_Name + "[" + (d2.PasswayLink_GateType == 1 ? "入口" : "出口") + "]";
                            }
                        });
                        d.Passway_AreaName = passwayAreaName.join("、");
                        linkdata[linkdata.length] = d;
                    }
                });
                item.linkdata = linkdata;
            }
            var data = [item];
            $("#framePanl").html($("#tmpldetail").tmpl(data));
            pager.bindPower();
            pager.bindEvent();
        }

        var pager = {
            showData: null, //数据已解析为前段展示格式
            passways: null,
            links: null,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //showNo 右侧显示的通道编码，为null则显示第一个通道
            bindData: function (showNo) {
                layer.closeAll();
                var that = this;
                $.post("GetSentryHostList", {}, function (json) {
                    if (json.success) {
                        that.showData = json.data.data;
                        that.passways = json.data.passways;
                        that.links = json.data.links;

                        area.init(that.showData, showNo);
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower["Add"]) {
                        $("#addpanl").removeClass("layui-hide")
                    } else {
                        $("#addpanl").removeClass("layui-hide").addClass("layui-hide")
                    }

                    if (pagePower["Delete"]) {
                        var stype = $("#Delete").attr("data-type");
                        if (stype == 2) $("#Delete").removeClass("layui-hide").addClass("layui-hide");
                    }

                    $("#Desktop").removeClass("layui-hide");
                });
            },
            bindEvent: function () {
                $("#Update").unbind("click").click(function () {
                    var paramNo = $(this).attr("data-no");
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑岗亭",
                        content: "Edit?Act=Update&SentryHost_No=" + paramNo,
                        area: getIframeArea(["800px", "80%"]),
                        maxmin: false
                    });
                    parent.top.setScrollTop(document.body, 0);
                })

                $("#Delete").unbind("click").click(function () {
                    var paramNo = $(this).attr("data-no");
                    $("#Update,#Delete").attr("disabled", true);
                    LAYER_OPEN_TYPE_0("确定删除岗亭?", res => {
                        LAYER_LOADING("处理中...");
                        $.post("DelSentryHost", { SentryHost_No: paramNo }, function (json) {
                            if (json.success) {
                                window.pager.bindData();
                                layer.msg("删除成功", { time: 1000 }, function () {

                                })
                            } else {
                                $("#Update,#Delete").removeAttr("disabled");
                                layer.msg(json.msg);
                            }
                        }, "json");
                    }, res => {
                        $("#Update,#Delete").removeAttr("disabled");
                    })
                    parent.top.setScrollTop(document.body, 0);
                })

                $("#Desktop").unbind("click").click(function () {
                    var no = $(this).attr("data-no");
                    $.post("OnDesktop", { SentryHost_No: no }, function (json) {
                        if (json.success) {
                            win_open(json.data);
                        } else {
                            layer.msg(json.msg);
                        }
                    }, "json");
                });

                $(".tounbind").unbind("click").click(function () {
                    var hostno = $(this).attr("data-no");
                    layer.open({
                        title: "确定解绑岗亭主机?",
                        content: "解绑操作不会影响岗亭使用，仅在安装向导中更换主机时需要解绑机器码。",
                        area: ["300px", "200px"],
                        btn: ["确定", "取消"],
                        yes: function () {
                            $.post("UnBindHost", { SentryHost_No: hostno }, function (json) {
                                if (json.success) {
                                    window.pager.bindData(hostno);
                                    layer.msg("解绑成功", { time: 1000 }, function () { })
                                } else {
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                                }
                            });
                        },
                        btn2: function () { },
                        cancel: function () { }
                    })
                });
            }
        }

        var area = {
            action: 0,//当前操作：0-编辑，1-新增
            cur: null,//当前显示的停车区域信息
            init: function (showData, showNo) {
                area.onload(showData, showNo)
            },
            //加载左侧列表数据
            onload: function (data, showNo) {
                $(".menu-list").html($("#tmpldata").tmpl(data))
                var menus = $(".menu-list").find(".menu-text")
                if (menus != null && menus.length > 0) {
                    if (showNo == null) {
                        area.openAreaItem(menus[0]);
                    } else {
                        for (var i = 0; i < menus.length; i++) {
                            if ($(menus[i]).attr("data-no") == showNo) {
                                area.openAreaItem($(menus[i]));
                                break;
                            }
                        }
                    }
                } else {
                    $("#framePanl iframe").attr("src", "Detail?SentryHost_No=");
                }
            },
            //点击左侧列表打开详情窗口，并加载数据
            openAreaItem: function (e) {
                var itemno = $(e).attr("data-no");
                $(".menu-text").removeClass("m-active-1")
                $(e).removeClass("m-active-1").addClass("m-active-1")

                //$("#framePanl iframe").attr("src", "Detail?SentryHost_No=" + itemno);

                openDetail(itemno);
            }
        }
    </script>
</body>
</html>
