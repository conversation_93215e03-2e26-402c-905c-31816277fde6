﻿<!DOCTYPE html>
<html>
<head>
    <script>
        var top = window.location.href.indexOf("top=false");
        if (window.top !== window.self && top < 0) {
            window.top.location = window.location;
        }

        var versionType = '@Html.Raw(carparking.Config.AppSettingConfig.SentryMode)';
        var model = '@Html.Raw(ViewBag.model)';
        localStorage.setItem('sysconfig', model);

        // 添加实时日期和时间
        function updateTime() {
            var now = new Date();
            var year = now.getFullYear();
            var month = now.getMonth() + 1;
            var day = now.getDate();
            var hours = now.getHours();
            var minutes = now.getMinutes();
            var seconds = now.getSeconds();
            var dateString = year + '-' + month + '-' + day;
            var timeString = hours + ':' + minutes + ':' + seconds;
            document.getElementById('time').innerHTML = dateString + " " + timeString;
        }
        if (versionType != "0") {
            setInterval(updateTime, 1000); // 每秒钟更新一次时间
        }
    </script>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>@ViewBag.SysConfig_DIYName</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <style>
        @@media screen and (max-width: 400px) {
            main { width: 80% !important; margin-left: 10%; }
            main form { right: auto !important; width: 100%; }
            .title { margin: 2rem !important; font-size: 1.5rem !important; }
            .footer { display: none !important; }
        }
        html, body { margin: 0; padding: 0; height: 100%; width: 100%; min-width: 1024px !important; overflow: hidden; font-size: 13px; }
        body { font-family: 'Microsoft YaHei'; background-image: url(../Static/img/login-bg.png); background-size: 100% 100%; }
        .footer { position: absolute; line-height: 50px; bottom: 0; width: 100%; text-align: center; color: #fff; background-color: rgba(0,0,0,0.1); }

        .title { margin: 50px 0 0 50px; font-size: 3rem; color: #fff; font-family: sans-serif,'Microsoft YaHei'; }
        .title .logo { float: left; }
        .title .logo img { height: 60px; }
        .title text { margin: 0 15px; line-height: 60px; }

        ::-webkit-input-placeholder { color: #ddd; }
        ::-moz-placeholder { color: #ddd; }
        :-ms-input-placeholder { color: #ddd; }

        input:-webkit-autofill,
        textarea:-webkit-autofill,
        select:-webkit-autofill { -webkit-text-fill-color: #fff !important; -webkit-box-shadow: 0 0 0px 1000px transparent inset !important; background-color: transparent; background-image: none; transition: background-color 50000s ease-in-out 0s; }
        input { background-color: transparent; }
    </style>

    <link href="~/Static/js/ui.roboto/style.css" rel="stylesheet" />
    <style>
        h1 { color: #000; }
        .form { position: absolute !important; left: calc(50% - 350px); top: calc(50% - 220px); width: 700px; height: 400px; background-color: #fff !important; }
        .form__content { width: 450px !important; }
        .table { width: 400px; height: 350px; margin: 80px auto; }
        .table form { width: 100%; }
        .table .name { width: 280px; margin: 20px auto 30px auto; display: block; height: 30px; border-radius: 20px; border: none; background: rgba(0,0,0,0.2); text-indent: 0.5em; }
        .table .btn { width: 100px; height: 30px; background: rgba(0,0,0,0.1); border-radius: 8px; border: none; color: white; margin: 0 auto; display: block; }
        .styled-button2 { background: rgb(0 0 0 / 49%) !important; }
        .styled-button .styled-button__text { color: #fff; }
        .styled-input__input { color: #fff; }
        .styled-button { background: #2175bc; width: 100%; }
        .styled-button:hover { background: #2175bc; }

        .layui-input { background-color: rgba(0,0,0,0); border: 1px solid rgba(0,0,0,0.1); box-shadow: none; color: #fff; }
        .layui-form-select dl dd { text-align: left; }
        .bg { /*background-image: url(../../Static/img/pnlMain.BackgroundImage.png);*/ background-repeat: no-repeat; background-size: inherit; position: absolute !important; left: calc(50% - 250px); top: calc(50% - 220px); width: 500px; height: 450px; opacity: 0.7; }
        .loginls { background-color: #1bc61e !important; }
        .loginzs { background-color: #773fd3 !important; }
        .loginszs { background-color: #4c10b0 !important; }
        .loginhs { background-color: #f22323 !important; }
        .logintqs { background-color: #08a589 !important; }
        .loginsqs { background-color: #098f77 !important; }

        .layui-input { background-color: rgb(251 251 251 / 10%); border: 1px solid rgb(83 83 83 / 30%); box-shadow: none; color: #040404; }
        .layui-input::placeholder { color: #939292; }
        .layui-row { margin-bottom: 1vh; }
        .edit-label { height: 38px; line-height: 38px; }
        input[disabled] { background-color: #e1e1e1 !important; }
    </style>
</head>
<body id="loginBody" style='@ViewBag.SysConfig_DIYBackImage'>
    <div class="title">
        <span class="logo">
            @if (ViewBag.SysConfig_DIYLogo == null || ViewBag.SysConfig_DIYLogo == "")
            {
                <img src="~/Static/img/icon/icon_logo.svg" />
            }
            else
            {
                <img src="@ViewBag.SysConfig_DIYLogo" />
            }
        </span>
        <text id="PlatformName">@ViewBag.SysConfig_DIYName</text>
    </div>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    @if (ViewBag.SysConfig_DIYBackImage != null && ViewBag.SysConfig_DIYBackImage != "")
    {
        <div class="bg"></div>
    }
    <div class="form layui-form">
        @if (ViewBag.WebLoginTheme == 0)//天蓝色
        {
            <div class="form__cover"></div>
        }
        else if (ViewBag.WebLoginTheme == 1)//深紫色
        {
            <div class="form__cover loginszs"></div>
        }
        else if (ViewBag.WebLoginTheme == 2)//绿色
        {
            <div class="form__cover loginls"></div>
        }
        else if (ViewBag.WebLoginTheme == 3)//紫色
        {
            <div class="form__cover loginzs"></div>
        }
        else if (ViewBag.WebLoginTheme == 4)//红色
        {
            <div class="form__cover loginhs"></div>
        }
        else if (ViewBag.WebLoginTheme == 5)//天青色
        {
            <div class="form__cover logintqs"></div>
        }
        else if (ViewBag.WebLoginTheme == 6)//深青色
        {
            <div class="form__cover loginsqs"></div>
        }
        else  //天蓝色
        {
            <div class="form__cover"></div>
        }
        <div style="position: absolute;bottom: 13px;color: #939292;">
            温馨提示： 请设置您的新密码。强密码规则：6-20位，英文字符、数字和特殊符号三种组合
        </div>
        <div class="form__content">
            <h1>首次登录系统，请设置新密码！</h1>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">账号</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="text" class="layui-input v-null" data-minlen="2" maxlength="32" value="admin" disabled />
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">新密码</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="password" class="layui-input" autocomplete="off" id="Admins_Pwd" placeholder="新密码">
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">确认新密码</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <input type="password" class="layui-input" autocomplete="off" id="Admins_Pwd2" placeholder="确认新密码">
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <button type="button" class="styled-button" id="btnLogin">
                        <span class="styled-button__text">登录</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="footer">
        @*<span style="float:left;margin-left:20px;"><a href="http://www.beian.miit.gov.cn" target="_blank" style="color:#0094ff;">粤ICP备18033481号-1</a></span>*@
        @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.WindowsStandard)
        {
            <span style="float:left;margin-left:20px;" id="time"></span>
        }
        <span style="float:right;margin-right:20px;" data-lan="lg_foot_tip">为了更好的体验效果 推荐使用谷歌浏览器</span>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script>
        var dog = '@ViewBag.Dog';
        myVerify.visible = true;
        myVerify.init();
        var diy = '@ViewBag.SysConfig_DIYEnable';
        var bg = '@ViewBag.SysConfig_DIYBackImage';

        layui.use(["form"], function () {
            if (dog != "") {
                layer.open({
                    type: 1
                    , title: "系统提示"
                    , closeBtn: false
                    , area: '300px;'
                    , shade: 0
                    , id: 'LAY_layuipro1'
                    , resize: false
                    , btn: ['确定']
                    , btnAlign: 'c'
                    , moveType: 0
                    , content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300;">' + dog + '</div>'
                    , success: function (layero) {
                    }
                });
            }
            loginFrm.bindEvent();
        });

        var user_code = $.getUrlParam("user_code");
        if (user_code) console.log("user_code：" + user_code);

        var loginFrm = {
            IsStrongPassword: true,
            bindEvent: function () {

                $('#btnLogin').click(function () { ToLogin(); });
                $("#Admins_Pwd,#Admins_Pwd2").keydown(function (event) { if (event.keyCode == 13) { ToLogin(); } });

                function ToLogin() {
                    if (!myVerify.check()) return;

                    var pwd = $("#Admins_Pwd").val().trim();
                    var pwd2 = $("#Admins_Pwd2").val().trim();
                    if (pwd == "") {
                        layer.tips("请输入新密码", '#Admins_Pwd', { tips: [3] }); return;
                    }
                    if (pwd2 == "") {
                        layer.tips("请输入确认密码", '#Admins_Pwd', { tips: [3] }); return;
                    }
                    if (pwd != pwd2) {
                        layer.tips("新密码与确认密码不一致", '#Admins_Pwd2', { tips: [3] }); $("#Admins_Pwd2").val("").focus(); return;
                    }

                    localStorage.setItem('webIsStrongPassword', isStrongPassword(pwd));

                    $('#btnLogin').button('loading');
                    $.post("/Gt/ToSetPwd", { Admins_Pwd: pwd },
                        function (data) {
                            if (data.Success) {
                                document.location.href = "../Monitoring/Index";
                                localData.set("Sentry_Admins_Account", encodeURIComponent("admin"));
                                var pwdStrength = CheckPwdStrength(pwd);
                                localData.set('Sentry_pwdStrength', pwdStrength);
                            } else {
                                $("#Admins_Pwd,#Admins_Pwd2").val("");
                                setTimeout(function () {
                                    $('#btnLogin').button('reset');
                                }, 500);
                                layer.tips(data.Message, '#btnLogin', { tips: [3] });
                            }
                        }, "json");
                    return false; //防止submit按钮自动刷新一次页面
                }


                $("#Admins_Pwd").focus();

            }
        }

        var GetAccounts = function () {
            $.ajaxSettings.async = false;
            layui.form.render();
            $.ajaxSettings.async = true;

        }

        function CheckPwdStrength(val) {
            var lv = 0;
            //字母+数字
            if (val.match(/^(?!\d+$)(?![a-zA-Z]+$)[a-zA-Z\d]+$/)) { lv = 2; }
            return lv;
        };

        $(function () {
            $("input:text").focus();//Linux输入法触发

            setTimeout(function () { $("#Admins_Pwd").focus(); }, 200);

        })
    </script>
</body>
</html>
