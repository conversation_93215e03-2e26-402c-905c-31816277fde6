﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车道监控</title>
    <link href="~/Static/css/bootstrap.min14ed.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/video/snackbar/snackbar.min.css?v125" rel="stylesheet" />
    <script src="~/Static/js/plugins/video/snackbar/snackbar.min.js?v125" asp-append-version="true"></script>
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        .fa { margin: 10px 4px; float: left; font-size: 16px; }
        .layui-select-title input { color: #0094ff; }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { margin-top: 10px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.small { width: 70px; float: left; }
        .select-small { float: left; margin-right: 5px; }

        .card { height: 375px; border: 1px solid #e9eefd !important; padding: 6px; }
        .card-header { /*background: linear-gradient(to right,#e9eefd,#e9eefd,#e9eefd) !important;*/ background-color: #e9eefd !important; padding: 10px !important; font-weight: bold; }
        .edit-label { text-align: left; }
        .right { display: inline-block; text-align: right !important; float: right; margin-top: 0px; }
        .card-content { text-align: left; align-content: center; padding-top: 7px; max-height: 57px; overflow: auto; }
        .card-desc { text-align: justify; align-content: center; margin-top: 7px; height: 75px; overflow-y: auto; word-break: break-all; }
        .btn { padding: 3px 15px; }
        .cards { margin-top: 15px; }
        .layui-btn-sm { height: 38px; line-height: 38px; }
        .layui-none { line-height: 26px; padding: 30px 15px; text-align: center; color: #999; }

        .layui-icon2 { padding: 0 2px; vertical-align: middle\9; vertical-align: bottom; }
        .layui-icon2 { font-family: layui-icon !important; font-size: 16px; font-style: normal; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }

        .boxbtn { border: 0; border-radius: 3px; }
        .btn-danger { background-color: #d9534f; color: #fff; border: 0px solid #fefcfc; }
        .btn-success { background-color: rgb(92, 184, 92); color: #fff /*rgba(0,0,0,0)*/; border: 0px solid #fefcfc; }
        .layui-icon2 { font-size: 18px; }

        .videobox img { height: 16rem; width: 100%; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>车道监控</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <select class="layui-select" lay-search id="SentryHost_No" name="SentryHost_No" lay-filter="selSentry">
                                    <option value="">岗亭列表</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <div id="Passway_No" style="min-width:210px;"></div>
                            </div>
                            <div class="layui-inline form-group">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-none layui-hide">无数据</div>

                        <div class="monitor layui-row">
                            <!--在线监控-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--在线监控-->
    <script type="text/x-jquery-tmpl" id="tmplvideo">
        <div class="layui-col-xs12 layui-col-sm6 layui-col-md4 layui-col-lg3" id="${id}" data-passwayno="${model.Passway_No}" style="margin-bottom: 2vh;">
            <div class="item-main" style="width: 99%; border: 1px solid #ddd;">
                <div class="layui-row videobox" id="${id}_play" style="height:16rem;width:100%;">
                    <img src="../Static/img/snapvideo2.png" />
                </div>
            </div>
            <div class="item-btns" style="width: 99%; overflow: hidden; border:1px solid #ddd;">
                <div style="width:100%;height:3rem;line-height:3rem;text-align:left;padding-left:1rem;">
                    ${model.Passway_Name}
                    <button data-key="3" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-danger" style="position: absolute; right: 20%; top: 16.5rem; width: 13%; padding: 0;">关闸</button>
                    <button data-key="4" data-passwayno="${model.Passway_No}" class="layui-btn layui-btn-sm layui-btn-normal" style="position: absolute; right: 3%; top: 16.5rem; width: 13%; padding: 0;">开闸</button>
                </div>
            </div>
        </div>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplsentry">
        <option value="${SentryHost_No}" data-type="${SentryHost_Type}">${SentryHost_Name}</option>
    </script>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}" data-type="${Passway_Type}">${Passway_Name}</option>
    </script>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
   @*  <script src="~/Static/js/plugins/video/js/baseInfo.js?1" asp-append-version="true"></script> *@
  @*   <script src="~/Static/js/plugins/video/js/flveee.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/video/js/base64.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/video/js/sha1.js" asp-append-version="true"></script> *@
    <script src="~/Static/operationscenter/js/baseInfo.js?3" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/base64.js" asp-append-version="true"></script>
    <script src="~/Static/operationscenter/js/sha1.js" asp-append-version="true"></script>
    <script src="~/Static/flveee/flveee.js?2" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/video/index.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/video/js/jsencrypt.min.js" asp-append-version="true"></script>
    <script>
        var Power = window.parent.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        var xmSelect = null;
        var selPasswayNos = null;

        layui.config({
            base: '../Static/admin/' //静态资源所在路径
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['table', 'jquery', 'xmSelect', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;

            pager.init();
        });
    </script>
    <script>
        var Parking_Key = '@ViewBag.Parking_Key';

        var pager = {
            ChargeRules_No: null,
            parkSentrys: null,  //岗亭列表
            passWays: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            category: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindData();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.parkSentrys = json.data.parkSentrys;
                        $("#SentryHost_No").append($("#tmplsentry").tmpl(pager.parkSentrys));
                        $("#SentryHost_No option:eq(1)").prop("selected", 'selected');
                        layuiForm.render("select");
                        pager.bindPassway($("#SentryHost_No option:eq(1)").val());
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                
                var pwnos = selPasswayNos.getValue('value');
                $.post("GetPasswayData", { SentryHost_No: $("#SentryHost_No").val(), Passway_Nos: pwnos }, function (json) {
                    if (json.success) {
                        
                        if (json.data && json.data != null && json.data.length > 0) vedioControl.setFrpInterval();
                        pager.passwaydata = json.data;
                        videoobj.init(pager.passwaydata).then(() => {
                            videoobj.event();
                        });
                        if (json.data.length > 0) { $(".layui-none").addClass("layui-hide"); }
                        else { $(".layui-none").removeClass("layui-hide"); }
                    } else {
                        layer.msg("读取车道失败", { icon: 0, time: 2000 });
                    }
                }, "json");

            },
            bindEvent: function () {
                layuiForm.on("select(selSentry)", function (data) {

                    if (data.elem.id == "SentryHost_No") {

                        if (data.value != "") {
                            pager.bindPassway(data.value);
                        } else {
                            selPasswayNos = xmSelect.render({
                                el: '#Passway_No',
                                name: 'Passway_No',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: []
                            })
                        }
                    }
                });
                $("#BtnSearch").click(function () { pager.bindData(1); });


            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });
            },
            bindPassway: function (v) {
                $.post("GetPasswayList", { SentryHost_No: v }, function (json) {
                    if (json.success) {
                        pager.passWays = json.data.passways;

                        var data = [];
                        for (var i = 0; i < pager.passWays.length; i++) {
                            data[i] = {
                                "name": pager.passWays[i].Passway_Name,
                                "value": pager.passWays[i].Passway_No
                            };
                        }
                        selPasswayNos = xmSelect.render({
                            el: '#Passway_No',
                            name: 'Passway_No',
                            layVerType: 'msg',
                            autoRow: true,
                            toolbar: { show: true },
                            data: data
                        })

                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            }
        }

        var vedioControl = {
            frpInterval: null,
            setFrpInterval: function () {
                if (Parking_Key && Parking_Key != "" && window.top.location.href.indexOf(Parking_Key) > -1) {
                    if (vedioControl.frpInterval != null) {
                        clearInterval(vedioControl.frpInterval);
                        vedioControl.frpInterval = null;
                    }
                    vedioControl.frpInterval = setInterval(() => {
                        try {
                            isLoadVideo = false;
                            if (snap_list != null && snap_list != undefined) {
                                snap_list.forEach(function (item, index) { window.clearInterval(item); });
                            }
                            clearInterval(vedioControl.frpInterval);
                            videoobj.init(pager.passwaydata).then(() => {
                                videoobj.event();
                            });
                        } catch (e) { console.log("视频检测异常：" + e.message); }
                    }, 300000);
                }
            }
        }

    </script>
</body>
</html>
