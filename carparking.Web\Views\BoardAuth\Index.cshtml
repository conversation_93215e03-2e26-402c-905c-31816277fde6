﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>控制板授权</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>
    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>控制板授权</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">

                            <div class="layui-row">
                                <div class="layui-inline">
                                    <input class="layui-input " name="BoardAuth_Title" id="BoardAuth_Title" autocomplete="off" placeholder="标题" />
                                </div>
                                <div class="layui-inline">
                                    <select class="layui-select" lay-search id="BoardAuth_PasswayNo" name="BoardAuth_PasswayNo">
                                        <option value="">车道名称</option>
                                    </select>
                                </div>
                                <div class="layui-inline form-group">
                                    <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm" id="Add" lay-event="Add"><i class="fa fa-plus"></i> <t>新增</t></button>
                                <button class="layui-btn layui-btn-sm" id="Update" lay-event="Update"><i class="fa fa-edit"></i> <t>编辑</t></button>
                                <button class="layui-btn layui-btn-sm" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i> <t>删除</t></button>
                                <button class="layui-btn layui-btn-sm" id="Enable" lay-event="Enable"><i class="fa fa-check-circle-o"></i> <t>启用</t></button>
                                <button class="layui-btn layui-btn-sm" id="Disable" lay-event="Disable"><i class="fa fa-ban"></i> <t>禁用</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        layui.use(['table', 'element', 'form'], function () {
            pager.init();

            var table = layui.table;

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'BoardAuth_No', width: 140, title: '编号', hide: true }
                , {
                    field: 'BoardAuth_Type', title: '类型', hide: true, templet: function (d) {
                        if (d.BoardAuth_Type == 1) return tempBar(1, "遥控器授权");
                    }
                }
                , { field: 'BoardAuth_Title', title: '标题' }
                , { field: 'BoardAuth_PasswayNo', title: '车道编号', hide: true }
                , {
                    field: 'BoardAuth_PasswayName', title: '车道名称', templet: function (d) {
                        if (d.BoardAuth_PasswayName != null && d.BoardAuth_PasswayName != '')
                            return JSON.parse(d.BoardAuth_PasswayName).join(",");
                    }
                }
                , {
                    field: 'BoardAuth_IsAccess', title: '授权状态', templet: function (d) {
                        if (d.BoardAuth_IsAccess == 1) return tempBar(1, "启用");
                        else return tempBar(0, "禁用");
                    }
                }
                , {
                    field: 'BoardAuth_DateType', title: '周期类型', templet: function (d) {
                        if (d.BoardAuth_DateType == 1) return tempBar(1, "每天");
                        else if (d.BoardAuth_DateType == 2) return tempBar(2, "每周");
                        else if (d.BoardAuth_DateType == 3) return tempBar(3, "定时");
                    }
                }
                , { field: 'BoardAuth_Addtime', title: '添加时间' }
                , { field: 'BoardAuth_Updatetime', title: '修改时间', hide: true }
                , { field: 'BoardAuth_Remark', title: '备注' }
                , { field: 'BoardAuth_AddAccount', title: '操作员' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/BoardAuth/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2,
                            id: 1,
                            title: "新增授权",
                            content: '/BoardAuth/Edit?Act=Add',
                            area: getIframeArea(['680px', '550px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 2000 }); return; }
                        layer.open({
                            type: 2,
                            id: 1,
                            title: "编辑授权",
                            content: '/BoardAuth/Edit?Act=Update&BoardAuth_No=' + data[0].BoardAuth_No,
                            area: getIframeArea(['680px', '550px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var noList = [];
                        $.each(data, function (k, v) {
                            noList.push(v.BoardAuth_No);
                        })
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/BoardAuth/Delete", { BoardAuth_No: noList.join(",") }, function (json) {
                                    if (json.success)
                                        layer.msg(json.msg, { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                }, "json");
                            }
                        })
                        break;
                    case 'Enable':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var noList = [];
                        $.each(data, function (k, v) {
                            noList.push(v.BoardAuth_No);
                        })
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定启用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/BoardAuth/Enable", { BoardAuth_No: noList.join(",") }, function (json) {
                                    if (json.success)
                                        layer.msg(json.msg, { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                }, "json");
                            }
                        })
                        break;
                    case 'Disable':
                        if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 2000 }); return; }
                        if (data.length > 100) { layer.msg("请选择100条以内", { icon: 0, time: 2000 }); return; }
                        var noList = [];
                        $.each(data, function (k, v) {
                            noList.push(v.BoardAuth_No);
                        })
                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "确定禁用?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.post("/BoardAuth/Disable", { BoardAuth_No: noList.join(",") }, function (json) {
                                    if (json.success)
                                        layer.msg(json.msg, { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                }, "json");
                            }
                        })
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox(table);

        });

        var pager = {
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach((item, index) => {
                            var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                            $("#BoardAuth_PasswayNo").append(option);
                        });
                        layui.form.render();
                    }
                }, "json");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/BoardAuth/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }

        var onEvent_BoardAuthDateType = function (data) {

        }
    </script>
</body>
</html>
