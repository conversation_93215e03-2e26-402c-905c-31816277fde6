﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>换班登录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?1" rel="stylesheet" />
    <style>
        html, body { padding: 0 15px; }
        .layui-card-header { border-bottom-color: #0094ff; font-weight: bold; color: #0094ff; font-size: 1rem; font-family: FangSong; }
        .layui-card:last-child { box-shadow: none; }
        ul.dataList li { padding: 0 0 4px 0; font-size: 1rem; }
        ul.dataList li text { padding-left: 4px; color: #0094ff; font-size: 1.2rem; font-family: FangSong; word-break: break-all; word-wrap: break-word; }
        .admin { font-size: 1rem; }
        .admin text { font-size: 1.2rem; color: #0094ff; font-family: FangSong; }
        .layui-tab-title { background-color: #5868e0 !important; }
        label.desc { font-size: 9px; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="layui-card-body">
        <div class="layui-row admin">
            <div class="layui-col-sm6">交班人员：<text id="name">admin</text></div>
            <div class="layui-col-sm6">交班账号：<text id="account">admin</text></div>
        </div>
        <div class="layui-row admin">
            <div class="layui-col-sm6">上班时间：<text id="start">2022-03-11 08:00:00</text></div>
            <div class="layui-col-sm6">交班时间：<text id="end">2022-03-11 18:00:00</text></div>
        </div>
    </div>
    <div class="layui-row" style="border-bottom:1px solid #0094ff;">
        <div class="layui-col-sm3">
            <div class="layui-card">
                <div class="layui-card-header">
                    <text>车辆统计</text>
                </div>
                <div class="layui-card-body" style="border-right:1px solid #0094ff;">
                    <ul class="dataList" id="countHtml">
                    </ul>
                </div>
            </div>
        </div>
        <div class="layui-col-sm9">
            <div class="layui-card">
                <div class="layui-card-header">
                    <text>收费统计</text>
                </div>
                <div class="layui-card-body">
                    <ul class="layui-row dataList" id="payHtml">
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <label class="desc">*温馨提示： 不统计访客车，商家车数量计入临时车</label>
    <div class="layui-form">
        <div class="layui-row" style="padding-top:1rem;">
            <div class="layui-col-sm4 layui-col-sm-offset4" id="divAccount">
                <select class="layui-select" lay-search id="onaccount" name="onaccount">
                    <option value="">请选择换班人员</option>
                </select>
            </div>
        </div>
        <div class="layui-row" style="padding-top:1rem;">
            <div class="layui-col-sm4 layui-col-sm-offset4">
                <input type="password" class="layui-input" id="onpwd" name="onpwd" placeholder="请输入密码" maxlength="20" />
            </div>
        </div>
        <div class="layui-row" style="padding-top:1rem;">
            <div class="layui-col-sm8 layui-col-sm-offset4">
                <button class="layui-btn" id="OK" disabled>确定换班</button>
                @*<button class="layui-btn" id="Print" style="line-height:normal !important;"><t>打印记录</t></button>*@
                <button class="layui-btn" style="background-color: #f0ad4e;" id="Cancel">取消换班</button>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/localData.js" asp-append-version="true"></script>
    <script>
        layui.use(["form", "element"], function () {
            $("#OK").click(function () {
                var json = {
                    start: $("#start").text(),
                    end: $("#end").text(),
                    onaccount: $("#onaccount").val(),
                    onpwd: $("#onpwd").val()
                };
                if (json.onaccount == "") { layer.tips('请选择换班人员', '#divAccount'); return; }

                layer.open({
                    type: 0,
                    title: "消息提示",
                    btn: ["确定", "取消"],
                    content: "确定换班登录?",
                    yes: function (res) {
                        layer.msg("换班中...", { icon: 16, time: 0 });

                        $.getJSON("ChangeWorkShift", { jsonModel: JSON.stringify(json) }, function (json) {
                            if (json.success) {
                                localData.set("Sentry_Admins_Account", encodeURIComponent($("#onaccount").val()));
                                layer.msg("换班成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.location.href = "/Monitoring/Index?" + Math.random()
                                    //window.parent.GetCurrentWorkShift();
                                    //window.parent.layer.closeAll();
                                });
                            } else {
                                layer.msg(json.msg);
                            }
                        })
                    },
                    btn2: function () { }
                })
            });
            $("#Cancel").click(function () {
                window.parent.layer.closeAll();
            });

            $("#Print").click(function () {
                win_open("/Monitoring/OffWorkView?sdt=" + $("#start").val() + "&edt=" + $("#end").val());
            });

            $.post("GetAdminsSelect", {}, function (json) {
                if (json.success) {
                    var htm = "";
                    json.data.forEach((item, index) => {
                        var text = item.Admins_Account;
                        if (item.Admins_Name != null && item.Admins_Name != '')
                            text += '(昵称：' + item.Admins_Name + ')';
                        htm += '<option value="' + item.Admins_Account + '">' + text + '</option>';
                    });
                    $("#onaccount").append(htm);
                    layui.form.render();
                } else {
                    layer.msg(json.msg);
                }
            }, "json")
        })

        $(function () {
            $.getJSON("GetWorkShiftAnData", {}, function (json) {
                if (json.success) {
                    $("#OK").removeAttr("disabled");
                    var model = json.data.model;
                    var OrderCoutLabel = json.data.OrderCoutLabel;
                    var PayModeLabel = json.data.PayModeLabel;

                    $("#name").text(model.WorkShift_OffName);
                    $("#account").text(model.WorkShift_OffAccount);
                    $("#start").text(model.WorkShift_OnTime);
                    $("#end").text(model.WorkShift_OffTime);

                    var countHtml = "";
                    OrderCoutLabel.forEach((item, index) => {
                        countHtml += '<li><label>' + item.Name + '</label><text>' + item.Value + '</text></li>';
                    });
                    $("#countHtml").html(countHtml);

                    var payHtml = "";
                    PayModeLabel.forEach((item, index) => {
                        payHtml += '<li class="layui-col-sm3"><label>' + item.Name + '</label><text>' + item.Value + '</text></li>';
                    });
                    $("#payHtml").html(payHtml);

                } else {
                    layer.msg(json.msg, { icon: 0, time: 2000 }, function () {
                        window.parent.layer.closeAll();
                    });
                }
            });
        });

    </script>
</body>
</html>
