﻿@model Dictionary<string, string>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Print Settings</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }

        .container { max-width: 800px; margin: auto; }

        h1 { text-align: center; }

        .form-group { margin-bottom: 10px; }

        .form-group label { display: block; margin-bottom: 5px; }

        .form-group input,
        .form-group select { width: 100%; padding: 8px; }

        input[type="text"] { width: 95%; padding: 8px; margin-bottom: 10px; }

        button { padding: 10px 20px; background-color: #007bff; color: white; border: none; cursor: pointer; font-weight: 800 !important; }

        button:hover { background-color: #0056b3; }

        .message-success { color: green; }

        .message-error { color: red; }

        #Cancel { margin-left: 10px; border: 1px solid #dedede; background-color: #fff; color: #333; }
    </style>
</head>
<body>
    <div class="container">
        @*         <h1>Print Settings</h1> *@
        <form id="printSettingsForm" action="/Monitoring/SavePrintSettings" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="enable">启用打印:</label>
                <select id="enable" name="enable">
                    <option value="0" @((Model["enable"] == "0" || Model["enable"] == null) ? "selected" : "")>禁用</option>
                    <option value="1" @(Model["enable"] == "1" ? "selected" : "")>启用</option>
                </select>
            </div>
            <div class="form-group">
                <label for="mode">打印模式:</label>
                <select id="mode" name="mode">
                    <option value="0" @(Model["mode"] == "0" ? "selected" : "")>手动</option>
                    <option value="1" @(Model["mode"] == "1" ? "selected" : "")>自动</option>
                </select>
            </div>
            <!-- 小票标题设置 -->
            <div class="form-group">
                <label for="title">标题:</label>
                <input type="text" id="title" name="title" value="@Model["title"]">
            </div>

            <!-- 小票副标题设置 -->
            <div class="form-group">
                <label for="subtitle">副标题:</label>
                <input type="text" id="subtitle" name="subtitle" value="@Model["subtitle"]">
            </div>

            <!-- 小票表尾设置 -->
            <div class="form-group">
                <label for="endcontent">表尾:</label>
                <input type="text" id="endcontent" name="endcontent" value="@Model["endcontent"]">
            </div>

            <div style="float: right;">
                <button type="submit">保存</button>
                <button id="Cancel"><t>取消</t></button>
            </div>
            <div id="messageArea" class="message"></div>
        </form>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>

        $(function () {
            $("#Cancel").click(function (event) {
                event.preventDefault(); // 阻止默认行为（包括提交）
                parent.window.CloseIndex(parent.window.frmPrintIndex);
                return false;
            });
        })

        document.getElementById('printSettingsForm').onsubmit = function (event) {
            event.preventDefault();
            fetch('/Monitoring/SavePrintSettings', {
                method: 'POST',
                body: new FormData(this),
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        layer.msg("保存成功", { time: 1500, icon: 1 }, function () {
                            parent.window.CloseIndex(parent.window.frmPrintIndex)
                        });
                    } else {
                        layer.msg(json.msg, { time: 1500, icon: 0 });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    layer.msg('发生错误，请重试。', { time: 1500, icon: 0 });
                });
        };

        function displayMessage(text, className) {
            var messageArea = document.getElementById('messageArea');
            messageArea.textContent = text;
            messageArea.className = className;
            setTimeout(function () {
                messageArea.textContent = '';//可以设置一个时间后自动清空消息
                messageArea.className = ''; //清除类名
            }, 5000);//5秒后自动清空消息
        }
    </script>
</body>
</html>