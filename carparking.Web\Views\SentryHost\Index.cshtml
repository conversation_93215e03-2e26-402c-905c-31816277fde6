﻿<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>岗亭管理</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { width: 100%; height: 100%; overflow-x: hidden; }
        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }
        .fa { margin: 7px 4px; float: left; font-size: 16px; }
        .layui-card { margin: 15px; }
        .layui-table-tool-temp { padding-right: 30px; }
    </style>
</head>
<body class="animated fadeInRight">
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb" id="sentrymsg">
                <a><cite>车场配置</cite></a>
                <a><cite>岗亭管理</cite></a>
            </div>
        </div>
        <div class="layui-card-body">
            <table class="layui-table" id="com-table-base" lay-filter="com-table-base">
            </table>
            <script type="text/html" id="toolbar_btns">
                <div class="layui-btn-container">
                    @* <button class="layui-btn layui-btn-sm layui-hide" lay-event="Add" id="Add"><i class="fa fa-plus"></i><t>新增</t></button> *@
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Update" id="Update"><i class="fa fa-edit"></i><t>编辑</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Delete" id="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                    <button class="layui-btn layui-btn-sm layui-hide" lay-event="Desktop" id="Desktop"><i class="fa fa-desktop"></i><t>岗亭配置</t></button>
                </div>
            </script>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="type">
        {{# if(d.SentryHost_Type==1){}}
        <span class="layui-badge layui-bg-green">岗亭端</span>
        {{# }else if(d.SentryHost_Type==2){ }}
        <span class="layui-badge layui-bg-red">服务器+岗亭端</span>
        {{# }else if(d.SentryHost_Type==3){ }}
        <span class="layui-badge layui-bg-orange">服务器</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="category">
        {{# if(d.SentryHost_Category==1){}}
        <span class="layui-badge layui-bg-blue">车场岗亭收费软件</span>
        {{# }else if(d.SentryHost_Category==2){ }}
        <span class="layui-badge layui-bg-green">车场岗亭后台服务</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="bsport">
        {{# if(d.SentryHost_Category==2){}}
        <span>{{d.SentryHost_BSPort}}</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="bind">
        {{# if(d.SentryHost_Type==1 && d.SentryHost_BingHost!=null&&d.SentryHost_BingHost!=0){}}
        <button class="layui-btn layui-btn-xs unBind" onclick="unbindhost(this)" data-no="{{d.SentryHost_No}}">解绑</button>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="status">
        {{# if(d.SentryHost_Online==1){}}
        <span class="layui-badge layui-bg-blue">在线</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange">离线</span>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script type="text/javascript">
        myVerify.init();
        var Power = window.parent.global.formPower;

        var comtable = null;
        layui.use(['form'], function () {
            var table = layui.table;

            var cols = [[
                { type: 'radio' }
                , { field: 'SentryHost_No', title: '岗亭编号',hide:true }
                , { field: 'SentryHost_Name', title: '岗亭名称' }
                , { field: 'SentryHost_Type', title: '安装类型', toolbar: "#type" }
                , { field: 'SentryHost_Online', title: '岗亭本地连接状态', toolbar: "#status" }
                , { field: 'SentryHost_Category', title: '主机类型', toolbar: "#category", hide: true }
                , { field: 'SentryHost_IP', title: 'IP地址' }
                , { field: 'SentryHost_Port', title: 'TCP端口' }
                , { field: 'SentryHost_BSPort', title: 'BS端口', toolbar: "#bsport", hide: true }
                , { field: 'SentryHost_Addtime', title: '注册时间' }
                , { field: 'SentryHost_BingHost', title: '绑定状态', toolbar: "#bind" }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = {};
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/SentryHost/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增岗亭",
                            content: "Edit?Act=Add",
                            area: getIframeArea(["800px", "90%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var paramNo = data[0].SentryHost_No;
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑岗亭",
                            content: "Edit?Act=Update&SentryHost_No=" + paramNo,
                            area: getIframeArea(["800px", "90%"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var paramNo = data[0].SentryHost_No;
                        LAYER_OPEN_TYPE_0("确定删除岗亭?", res => {
                            LAYER_LOADING("处理中...");
                            $.post("DelSentryHost", { SentryHost_No: paramNo }, function (json) {
                                if (json.success) {
                                    window.pager.bindData(pager.pageIndex);
                                    layer.msg("删除成功", { time: 1000 }, function () {

                                    });
                                } else {
                                    layer.msg(json.msg);
                                }
                            }, "json");
                        }, res => {
                        })
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Desktop':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data[0].SentryHost_Category != 2 && data[0].SentryHost_Category != 1) { layer.msg("非车场岗亭后台服务主机类型，无法打开岗亭配置!"); return; }
                        var paramNo = data[0].SentryHost_No;
                        var frmIndex = layer.open({
                            id: 'Store',
                            type: 0,
                            title: "提示",
                            content: "<span style='color:red;'>进入岗亭：</span>跳转到岗亭界面操作；<br/> <span style='color:red;'>查看：</span>打开小窗口查看岗亭；",
                            area: ["300px"],
                            maxmin: false,
                            shade: 0,
                            closeBtn: 0,
                            btn: ["进入岗亭", "查看", "取消"],
                            yes: function (d, i) {
                                setTimeout(function () {
                                    $.post("OnDesktop", { SentryHost_No: paramNo }, function (json) {
                                        if (json.success) {
                                            win_open(json.data);
                                        } else {
                                            layer.msg(json.msg);
                                        }
                                    }, "json");
                                }, 10);
                                layer.close(frmIndex);
                            },
                            btn2: function () {
                                setTimeout(function () {
                                    $.post("OnDesktop", { SentryHost_No: paramNo }, function (json) {
                                        if (json.success) {
                                            layer.open({
                                            type: 2,
                                            title: false,
                                            content: '/Monitoring/Index',
                                            area: ["96%", "96%"],
                                            maxmin: false
                                        });

                                layer.close(frmIndex);
                                        } else {
                                            layer.msg(json.msg);
                                        }
                                    }, "json");
                                }, 10);
                            },
                            btn3: function () {
                                layer.close(frmIndex);
                            }
                        });


                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindData(1);
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            //showNo 右侧显示的通道编码，为null则显示第一个通道
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = {};
                comtable.reload({
                    url: '/SentryHost/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });

            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    $('.unBind').removeClass("layui-hide");
                });
            },
            bindEvent: function () {

            }
        }

        var unbindhost = function (e) {
            var hostno = $(e).attr("data-no");
            layer.open({
                title: "确定解绑岗亭主机?",
                content: "解绑操作不会影响岗亭使用，仅在安装向导中更换主机时需要解绑机器码。",
                area: ["300px", "200px"],
                btn: ["确定", "取消"],
                yes: function () {
                    $.post("UnBindHost", { SentryHost_No: hostno }, function (json) {
                        if (json.success) {
                            window.pager.bindData(pager.pageIndex);
                            layer.msg("解绑成功", { time: 1000 }, function () { })
                        } else {
                            layer.msg(json.msg, { icon: 0, time: 1500 });
                        }
                    });
                },
                btn2: function () { },
                cancel: function () { }
            })
        }


        document.addEventListener('DOMContentLoaded', function () {
            var ctrlPressed = sessionStorage.getItem('ctrlPressed') === 'true' ? true : false;
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Control') {
                    ctrlPressed = true;
                    sessionStorage.setItem('ctrlPressed', 'true');
                }
            });

            document.addEventListener('keyup', function (event) {
                if (event.key === 'Control') {
                    ctrlPressed = false;
                    sessionStorage.setItem('ctrlPressed', 'false');
                }
            });

            document.getElementById('sentrymsg').addEventListener('dblclick', function () {
                ctrlPressed = sessionStorage.getItem('ctrlPressed') === 'true' ? true : false;
                if (ctrlPressed) {
                    // 当双击时执行的操作
                    layer.open({
                        title: "<div style='color:red;font-size:16px;'>系统状态</div>",
                        type: 2, id: 123654789,
                        area: ['80%', '80%'],
                        fix: false, //不固定
                        maxmin: false,
                        content: '/ProjectStatus/Index'
                    });
                }
            });
        });


    </script>
</body>
</html>
