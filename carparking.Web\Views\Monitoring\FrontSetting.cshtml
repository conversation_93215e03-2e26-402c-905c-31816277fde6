﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/operationscenter/index.css?t=@DateTime.Now.Ticks" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <style>
        label { padding: 10px 10px 0 0; float: right; }
        .layui-tab-title { background-color: #5868e0 !important; }
        .layui-form { font-size: 1rem; }

        .left { background-color: #fff !important; }
        .right { background-color: #5864e0 !important; }

        .monitor { background-color: #1064e0 !important; bottom: 49%; }
        .record { background-color: #2014e0 !important; top: 52%; min-width: 1px !important; }

        .content { top: .1rem; min-height: 150px; font-size: 1rem !important; margin-top: 10px; }
        .content:hover { cursor: pointer; }
        .content .right { width: 17.5vw !important; }
        .content .left { right: 20vw !important; }
        .hideDiv { opacity: .3; }
        .watermark-text { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-weight: 500 !important; color: #fff; pointer-events: none; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="layui-form" id="verify-form">
            <div class="layui-row" style="margin-top:1rem;">
                <div class="layui-col-xs3 edit-label">字体设置</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="btnCombox falsemodify" id="sysfont">
                        <ul>
                            <li data-value="0" class="select">标准字体</li>
                            <li data-value="1">大字体</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="layui-row" style="margin-top:1rem;">
                <div class="layui-col-xs3 edit-label">界面展示</div>
                <div class="layui-col-xs8 edit-ipt-ban">
                    <div class="ibox-content content">
                        <div class="left">
                            <div class="monitor">
                                <p class="watermark-text">视频监控</p>
                            </div>
                            <div class="record">
                                <p class="watermark-text">场内记录</p>
                            </div>
                        </div>
                        <div class="right">
                            <p class="watermark-text">侧边栏</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        var config = {
            sysfont: 0,
        };

        layui.use(["form"], function () {
            config.sysfont = parent.uiVersion == "large-font" ? 1 : 0;
            LoadConfig(config);

            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");
                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");
                onEventCombox(idName);
            });

            var con = parent.pager.getPart();
            if (con && con.hasOwnProperty("showModule")) {
                if (!con.showModule.right) {
                    $('.content .right').addClass("hideDiv");
                }
                if (!con.showModule.monitor) {
                    $('.content .monitor').addClass("hideDiv");
                }
                if (!con.showModule.record) {
                    $('.content .record').addClass("hideDiv");
                }
            }

            $(".content").find("div").click(function () {
                event.stopPropagation();
                if ($(this).hasClass("hideDiv")) {
                    $(this).removeClass("hideDiv");
                    parent.pager.showPart($(this).attr('class').split(' ')[0]);
                } else {
                    $(this).addClass("hideDiv");
                    parent.pager.dispalyPart($(this).attr('class').split(' ')[0]);
                }
            });
            layui.form.render();
        });

        var onEventCombox = function (idName) {
            if (idName == 'sysfont') {

            }
        }
        var LoadConfig = function (data) {
            $(".btnCombox").each(function () {
                var idName = $(this).attr("id");
                $(this).find("li").removeClass("select");
                $(this).find("ul li[data-value=" + data[idName] + "]").addClass("select");
                config[idName] = data[idName];

                onEventCombox(idName);
            });
        }
    </script>

</body>
</html>
