﻿@using carparking.BLL.Cache
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑管理员</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/plugins/iCheck/custom.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
    <style>
        table { width: 100%; }
        table tr { border-bottom: 1px dashed #ccc; height: 50px; }
        table tr:first-child { border-top: 1px dashed #ccc; }
        .td-1 { padding: 0; text-align: center; width: 15%; min-width: 100px; border-right: 1px dashed #ccc; }
        .td-2 { border-left: 1px dashed #ccc; text-align: right; padding-right: 30px; width: 12%; min-width: 160px; }
        .td-3 { overflow-wrap: break-word; padding: 10px; }
        .td-3 label { padding: 5px 0px; }
        .td-3 label.hide { display: none; }
        .form-horizontal .form-group { margin-right: 0; margin-left: 0; }
        .text-navy { color: #1ab394; }
        .font-bold { font-weight: 600; }
        .i-checks { padding-left: 0; }
        label { margin: 0; font-weight: normal; }

        .checkbox-inline, .checkbox-inline + .checkbox-inline, .radio-inline, .radio-inline + .radio-inline { margin: 0 15px 0 0; }

        .layui-collapse { margin: 0 8.3% 0 16.67%; }
        .layui-row { margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label ">名称</div>
                <div class="layui-col-xs3 " id="verifyCheck">
                    <input type="text" id="PowerGroup_Name" class="layui-input v-null" maxlength="20">
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-xs2 edit-label ">备注</div>
                <div class="layui-col-xs4">
                    <input type="text" id="PowerGroup_Remark" class="layui-input" maxlength="200">
                </div>
            </div>
        </div>
        <div class="layui-collapse">
            <!--<div class="layui-colla-item">
            <h2 class="layui-colla-title">首页</h2>
            <div class="layui-colla-content layui-show">
            <table>-->
            <!--平台主页Start-->
            <!--<tbody>
            <tr type="platform">
                <td class="td-1"><label class="text-navy i-checks border font-bold"><t>平台</t><input type="checkbox" value="platform" class="groupItem" /></label></td>
                <td class="td-2"><label class="text-navy i-checks border  "><t>所有页面</t><input type="checkbox" class="all-check" id="Index" value="Index"></label></td>
                <td class="td-3">
                    <label class="checkbox-inline i-checks"><input type="checkbox" value="View"><t>主界面</t></label>
                    <label class="checkbox-inline i-checks"><input type="checkbox" value="Home"><t>首页</t></label>
                    <label class="checkbox-inline i-checks"><input type="checkbox" value="ChangePwd"><t>修改密码</t></label>
                </td>
            </tr>
            </tbody>-->
            <!--平台主页End-->
            <!--</table>
            </div>
            </div>-->
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">首页</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="headpage">
                                <td rowspan="1" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>首页</t><input type="checkbox" value="headpage" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>首页</t><input type="checkbox" class="all-check" id="HeadPage" value="HeadPage">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="OnSiteVehicles" class="cloud"><t>场内车辆</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="TodayCharge" class="cloud"><t>今日收费</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="FixedNumberVehicles"><t>固定车数量</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="OnlineDevices" class="cloud"><t>在线设备数量</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="ChargeData" class="cloud"><t>收费数据</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="TodayCars" class="cloud"><t>今日车流量</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="UpdatePark"><t>查看云平台配置</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="SavePark"><t>编辑云平台配置</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="ReStartCloud" class="cloud"><t>重连云平台</t>
                                    </label>
                                    @if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.CloudServer && carparking.BLL.Cache.AppBasicCache.CurrentSysConfigContent?.SysConfig_ConnMode == 1)
                                    {
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Pulldown"><t>切换车场模式</t>
                                        </label>
                                    }

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">车场配置</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="base">
                                <td rowspan="6" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>车场配置</t><input type="checkbox" value="base" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>快速配置</t><input type="checkbox" class="all-check" id="FastGuide" value="FastGuide">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>

                                </td>
                            </tr>

                            <tr type="base">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>岗亭管理</t><input type="checkbox" class="all-check" id="SentryHost" value="SentryHost">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    @*   <label class="checkbox-inline i-checks cloud">
                                    <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label> *@
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Desktop"><t>岗亭配置</t>
                                    </label>

                                </td>
                            </tr>
                            <tr type="base">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>区域管理</t><input type="checkbox" class="all-check" id="ParkArea" value="ParkArea">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增区域</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑区域</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除区域</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="base">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车道管理</t><input type="checkbox" class="all-check" id="Passway" value="Passway">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Open"><t>开闸</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Close"><t>关闸</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="GateSwitch"><t>道闸常开</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="SaveNext" class="cloud"><t>保存并继续</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Synchronous"><t>同步白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="UnBind"><t>清空黑白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="SyncInOut"><t>同步进出口及小数点位数</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="base">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>设备管理</t><input type="checkbox" class="all-check" id="Device" value="Device">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="SaveNext" class="cloud"><t>保存并继续</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Up"><t>上移</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Down"><t>下移</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Reset"><t>重置</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Pulldown" class="cloud"><t>显示屏配置</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Send" class="cloud"><t>余位屏配置</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="VoipSet" class="cloud"><t>智慧道闸配置</t>
                                    </label>
                                </td>
                            </tr>

                            @*<tr type="base">
                            <td class="td-2"><label class="text-navy i-checks border"><t>值班中心</t><input type="checkbox" class="all-check" id="DeviceTalk" value="DeviceTalk"></label></td>
                            <td class="td-3">
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="View"><t>查看</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Search"><t>查询</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Add"><t>新增</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Update"><t>编辑</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Delete"><t>删除</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Save"><t>保存</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Cancel"><t>取消</t></label>
                            </td>
                            </tr>*@
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">车场管理</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="park">
                                <td rowspan="14" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>车场管理</t><input type="checkbox" value="busi" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车牌类型</t><input type="checkbox" class="all-check" id="CarCardType" value="CarCardType">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车道监控</t><input type="checkbox" class="all-check" id="LaneMonitor" value="LaneMonitor">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>计费规则</t><input type="checkbox" class="all-check" id="BillingRule" value="BillingRule">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Copy" class="cloud"><t>复制</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Test"><t>测试</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>充值规则</t><input type="checkbox" class="all-check" id="MonthRule" value="MonthRule">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车牌颜色</t><input type="checkbox" class="all-check" id="CarType" value="CarType">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>通行控制</t><input type="checkbox" class="all-check" id="AccessAuth" value="AccessAuth">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Add" class="cloud"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Update" class="cloud"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Delete" class="cloud"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Save" class="cloud"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>尾号限行</t><input type="checkbox" class="all-check" id="EndNumAuth" value="EndNumAuth">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Enable"><t>启用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Disable"><t>禁用</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>特殊车辆</t><input type="checkbox" class="all-check" id="SpecialCar" value="SpecialCar">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车场设置</t><input type="checkbox" class="all-check" id="Policy" value="Policy">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="EditBatch"><t>批量设置</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>日期设置</t><input type="checkbox" class="all-check" id="DateSet" value="DateSet">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>设置</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>设置周期</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>城市平台上报</t><input type="checkbox" class="all-check" id="CityServer" value="CityServer">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Open"><t>上报停车场</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Close"><t>关闭上报</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Reset"><t>重新上报</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车牌特殊处理</t><input type="checkbox" class="all-check" id="SpecialPlate" value="SpecialPlate">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Enable"><t>启用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Disable"><t>禁用</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="park">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>屏显模板</t><input type="checkbox" class="all-check" id="DisplayTemplate" value="DisplayTemplate">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">车辆管理</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            @* <tr type="carui">
                            <td class="td-2"><label class="text-navy i-checks border"><t>车辆登记</t><input type="checkbox" class="all-check" id="Car" value="Car"></label></td>
                            <td class="td-3">
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="View"><t>查看</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Search"><t>查询</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Add"><t>新增</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Update"><t>编辑</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Delete"><t>注销</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Import"><t>导入</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Export"><t>导出</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Save"><t>保存</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="SaveNext"><t>保存并继续</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Cancel"><t>取消</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Retweet"><t>续费延期</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Vaild"><t>修改有效期</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Import"><t>导入</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Export"><t>导出</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="BathVaild"><t>批量修改有效期</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Enable"><t>白名单启用</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Disable"><t>白名单禁用</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Charge"><t>储值车退费</t></label>
                            </td>
                            </tr>*@
                            <tr type="carui">
                                <td rowspan="4" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>车辆管理</t><input type="checkbox" value="carui" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车辆登记</t><input type="checkbox" class="all-check" id="Owner" value="Owner">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Retweet"><t>延期/充值</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="BathPayDate"><t>批量延期</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Vaild"><t>设置有效期</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Import"><t>导入</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="SaveNext"><t>保存并继续</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Enable"><t>启用白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Disable"><t>禁用白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="BathVaild"><t>批量设置有效期</t>
                                    </label>
                                    @*<label class="checkbox-inline i-checks"><input type="checkbox" value="Charge"><t>储值车充值</t></label>*@
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Refund"><t>储值车退费</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Bind"><t>批量授权</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="DownLoadCarList"><t>相机白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Pulldown"><t>推送到线上</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="SearchPassRecord"><t>场内记录</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="SetCarCardType"><t>设置车牌类型</t>
                                    </label>
                                    @* <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Create"><t>修改车位号</t></label> *@
                                </td>
                            </tr>
                            <tr type="carui">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>黑名单管理</t><input type="checkbox" class="all-check" id="BlackList" value="BlackList">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="DownLoadCarList"><t>相机黑名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Import"><t>导入</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="carui">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>访客车辆</t><input type="checkbox" class="all-check" id="Reserve" value="Reserve">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>取消预约</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="carui">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>商家车辆</t><input type="checkbox" class="all-check" id="BusinessCar" value="BusinessCar">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item cloud">
                <h2 class="layui-colla-title">商家优惠</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="busi">
                                <td rowspan="2" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>商家优惠</t><input type="checkbox" value="disc" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>优惠设置</t><input type="checkbox" class="all-check" id="ParkDiscountSet" value="ParkDiscountSet">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="busi">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车牌优惠</t><input type="checkbox" class="all-check" id="CarCoupon" value="CarCoupon">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">记录查询</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="record" class="preferential">
                                <td rowspan="16" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>记录查询</t><input type="checkbox" value="rptpay" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>出入场记录</t><input type="checkbox" class="all-check" id="InParkRecord" value="InParkRecord">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>修改</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Detail"><t>详情</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Payment"><t>支付</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Import"><t>导入</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="DelBind"><t>批量删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Send"><t>上传云平台</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>缴费记录</t><input type="checkbox" class="all-check" id="PayOrder" value="PayOrder">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Detail"><t>支付详情</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="ParkDetail"><t>停车详情</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Print"><t>打印</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>缴费明细</t><input type="checkbox" class="all-check" id="PayPart" value="PayPart">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>储值余额明细</t><input type="checkbox" class="all-check" id="Ledger" value="Ledger">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            @{
                                if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.WindowsStandard)
                                {
                                    <tr type="record" class="cloud">
                                        <td class="td-2">
                                            <label class="text-navy i-checks border">
                                                <t>识别未支付</t><input type="checkbox" class="all-check" id="UnpaidRecord" value="UnpaidRecord">
                                            </label>
                                        </td>
                                        <td class="td-3">
                                            <label class="checkbox-inline i-checks cloud">
                                                <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                            </label>
                                            <label class="checkbox-inline i-checks cloud">
                                                <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                            </label>
                                            <label class="checkbox-inline i-checks cloud">
                                                <input type="checkbox" value="Export"><t>导出</t>
                                            </label>
                                            <label class="checkbox-inline i-checks cloud">
                                                <input type="checkbox" value="Detail"><t>详情</t>
                                            </label>
                                        </td>
                                    </tr>
                                }
                            }
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>优惠券记录</t><input type="checkbox" class="all-check" id="CouponRecord" value="CouponRecord">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>注销</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="preferential cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>场内记录</t><input type="checkbox" class="all-check" id="InParkCar" value="InParkCar">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Expired"><t>逾期车牌处理</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="preferential">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车牌识别记录</t><input type="checkbox" class="all-check" id="CarRecog" value="CarRecog">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            @*<tr type="record">
                            <td class="td-2"><label class="text-navy i-checks border"><t>储值车扣费记录</t><input type="checkbox" class="all-check" id="StoreRecord" value="StoreRecord"></label></td>
                            <td class="td-3">
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="View"><t>查看</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Search"><t>查询</t></label>
                            </td>
                            </tr>*@
                            @*  <tr type="record" class="cloud">
                            <td class="td-2"><label class="text-navy i-checks border"><t>特殊车辆放行记录</t><input type="checkbox" class="all-check" id="SpecialCarNoPass" value="SpecialCarNoPass"></label></td>
                            <td class="td-3">
                            <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="View"><t>查看</t></label>
                            <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Search"><t>查询</t></label>
                            </td>
                            </tr> *@
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>人工开闸记录</t><input type="checkbox" class="all-check" id="OpenGateRecord" value="OpenGateRecord">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>开闸放行记录</t><input type="checkbox" class="all-check" id="AbnorOrder" value="AbnorOrder">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>交班记录</t><input type="checkbox" class="all-check" id="WorkShift" value="WorkShift">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>

                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>倒车记录</t><input type="checkbox" class="all-check" id="BackCar" value="BackCar">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>事件列表</t><input type="checkbox" class="all-check" id="ControlEvent" value="ControlEvent">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Plus"><t>新增事件</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Add"><t>新增入场</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Update"><t>关联出场</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Audit"><t>确认倒车</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Close"><t>忽略跟车</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Reject"><t>忽略倒车</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>健康码记录</t><input type="checkbox" class="all-check" id="HealthCodeResult" value="HealthCodeResult">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车辆注销记录</t><input type="checkbox" class="all-check" id="CarUnbound" value="CarUnbound">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="record">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>黑白名单</t><input type="checkbox" class="all-check" id="WhiteRecord" value="WhiteRecord">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Send"><t>同步名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Delete"><t>删除记录</t>
                                    </label>

                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Bind"><t>一键同步白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Audit"><t>一键同步黑名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Reject"><t>一键注销黑白名单</t>
                                    </label>

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item cloud">
                <h2 class="layui-colla-title">汇总报表</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="rptpay">
                                <td rowspan="9" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>汇总报表</t><input type="checkbox" value="rptpay" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车流量报表</t><input type="checkbox" class="all-check" id="RptTraffic" value="RptTraffic">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    @*<label class="checkbox-inline i-checks"><input type="checkbox" value="Export" />导出</label>*@
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="rptpay" class="cloud">

                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>临时车收费统计</t><input type="checkbox" class="all-check" id="RptTempPayment" value="RptTempPayment">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Export" />导出</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t></label>
                                </td>
                            </tr>
                            <tr type="rptpay" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>月租车充值统计</t><input type="checkbox" class="all-check" id="RptCarMonth" value="RptCarMonth">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Export" />导出</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t></label>
                                </td>
                            </tr>
                            <tr type="rptpay">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>储值车充值统计</t><input type="checkbox" class="all-check" id="RptCarStore" value="RptCarStore">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Export" />导出</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t></label>
                                </td>
                            </tr>
                            <tr type="rptpay" class="preferential">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车位续期统计</t><input type="checkbox" class="all-check" id="RptSpace" value="RptSpace">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Export" />导出</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t></label>
                                </td>
                            </tr>
                            <tr type="rptpay" class="preferential">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车场日报表</t><input type="checkbox" class="all-check" id="RptDay" value="RptDay">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    @*<label class="checkbox-inline i-checks"><input type="checkbox" value="Export" />导出</label>*@
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t></label>
                                </td>
                            </tr>
                            <tr type="rptpay" class="preferential">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>车场月报表</t><input type="checkbox" class="all-check" id="RptMonth" value="RptMonth">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    @*<label class="checkbox-inline i-checks"><input type="checkbox" value="Export" />导出</label>*@
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Print">打印</label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Export"><t>导出</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud"><input type="checkbox" class="cloud" value="Delete"><t>清除历史统计</t></label>
                                </td>
                            </tr>
                            @*<tr type="rptpay" class="preferential">
                            <td class="td-2"><label class="text-navy i-checks border"><t>车场年报表</t><input type="checkbox" class="all-check" id="RptYear" value="RptYear"></label></td>
                            <td class="td-3">
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="View"><t>查看</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Search"><t>查询</t></label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Export" />导出</label>
                            <label class="checkbox-inline i-checks"><input type="checkbox" value="Print">打印</label>
                            </td>
                            </tr>*@
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-colla-item">
                <h2 class="layui-colla-title">系统管理</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="system">
                                <td rowspan="15" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>系统管理</t><input type="checkbox" value="system" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>账号管理</t><input type="checkbox" class="all-check" id="Admins" value="Admins">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Enable"><t>启用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Disable"><t>禁用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Bind"><t>绑定车道</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>权限管理</t><input type="checkbox" class="all-check" id="PowerGroup" value="PowerGroup">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Enable"><t>启用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Disable"><t>禁用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>操作日志</t><input type="checkbox" class="all-check" id="UserLogs" value="UserLogs">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>系统日志</t><input type="checkbox" class="all-check" id="SystemLogs" value="SystemLogs">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>开放接口日志</t><input type="checkbox" class="all-check" id="OpenApiV2Logs" value="OpenApiV2Logs">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>无感支付日志</t><input type="checkbox" class="all-check" id="NoSensePayLogs" value="NoSensePayLogs">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>任务管理</t><input type="checkbox" class="all-check" id="Schedule" value="Schedule">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>系统设置</t><input type="checkbox" class="all-check" id="SystemSetting" value="SystemSetting">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Add"><t>新增</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Update"><t>编辑</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Delete"><t>删除</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Save"><t>保存</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Cancel"><t>取消</t>
                                    </label>
                                </td>
                            </tr>

                            @if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.CloudServer)
                            {
                                <tr type="system">
                                    <td class="td-2">
                                        <label class="text-navy i-checks border">
                                            <t>数据分发</t><input type="checkbox" class="all-check" id="SendHandle" value="SendHandle">
                                        </label>
                                    </td>
                                    <td class="td-3">
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="View"><t>查看</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Search"><t>查询</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Pulldown"><t>重传</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Detail"><t>详情</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Cancel"><t>取消</t>
                                        </label>
                                    </td>
                                </tr>
                                <tr type="system">
                                    <td class="td-2">
                                        <label class="text-navy i-checks border">
                                            <t>数据接收</t><input type="checkbox" class="all-check" id="ReceiveHandle" value="ReceiveHandle">
                                        </label>
                                    </td>
                                    <td class="td-3">
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="View"><t>查看</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Search"><t>查询</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Detail"><t>详情</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Cancel"><t>取消</t>
                                        </label>
                                    </td>
                                </tr>
                            }
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>云车场记录</t><input type="checkbox" class="all-check" id="CloudRecord" value="CloudRecord">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Upload"><t>重传</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>处理记录</t><input type="checkbox" class="all-check" id="Assist" value="Assist">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>通话记录</t><input type="checkbox" class="all-check" id="CallRecord" value="CallRecord">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system" class="cloud">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>支付方式</t><input type="checkbox" class="all-check" id="PayType" value="PayType">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Enable"><t>启用</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Disable"><t>禁用</t>
                                    </label>
                                </td>
                            </tr>
                            <tr type="system">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>系统报警</t><input type="checkbox" class="all-check" id="Syswarn" value="Syswarn">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Update"><t>已处理</t>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="layui-colla-item">
                <h2 class="layui-colla-title">维护管理</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            @if (carparking.Config.AppSettingConfig.SentryMode != carparking.Common.VersionEnum.CloudServer)
                            {
                                <tr type="maintenance">
                                    <td rowspan="4" class="td-1">
                                        <label class="text-navy i-checks border font-bold">
                                            <t>维护管理</t><input type="checkbox" value="maintenance" class="groupItem" />
                                        </label>
                                    </td>
                                    <td class="td-2">
                                        <label class="text-navy i-checks border">
                                            <t>数据升级</t><input type="checkbox" class="all-check" id="Tools" value="Tools">
                                        </label>
                                    </td>
                                    <td class="td-3">
                                        <label class="checkbox-inline i-checks cloud">
                                            <input type="checkbox" class="cloud" value="View"><t>查看</t>
                                        </label>
                                        <label class="checkbox-inline i-checks cloud">
                                            <input type="checkbox" class="cloud" value="Search"><t>查询</t>
                                        </label>
                                        <label class="checkbox-inline i-checks cloud">
                                            <input type="checkbox" class="cloud" value="Update"><t>升级</t>
                                        </label>
                                    </td>
                                </tr>
                                <tr type="maintenance">
                                    <td class="td-2">
                                        <label class="text-navy i-checks border">
                                            <t>数据备份</t><input type="checkbox" class="all-check" id="DataBackup" value="DataBackup">
                                        </label>
                                    </td>
                                    <td class="td-3">
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="View"><t>查看</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Search"><t>查询</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Save"><t>备份</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Delete"><t>删除</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Bind"><t>数据还原</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Create"><t>手动备份</t>
                                        </label>
                                        @if (AppBasicCache.IsWindows)
                                        {
                                            <label class="checkbox-inline i-checks">
                                                <input type="checkbox" value="UnBind"><t>手动还原</t>
                                            </label>
                                        }
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Download"><t>下载备份文件</t>
                                        </label>
                                    </td>
                                </tr>
                            }
                            else
                            {
                                <tr type="maintenance">
                                    <td rowspan="3" class="td-1">
                                        <label class="text-navy i-checks border font-bold">
                                            <t>维护管理</t><input type="checkbox" value="maintenance" class="groupItem" />
                                        </label>
                                    </td>
                                    <td class="td-2">
                                        <label class="text-navy i-checks border">
                                            <t>数据备份</t><input type="checkbox" class="all-check" id="DataBackup" value="DataBackup">
                                        </label>
                                    </td>
                                    <td class="td-3">
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="View"><t>查看</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Search"><t>查询</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Save"><t>备份</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Delete"><t>删除</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Bind"><t>数据还原</t>
                                        </label>
                                        <label class="checkbox-inline i-checks">
                                            <input type="checkbox" value="Create"><t>手动备份</t>
                                        </label>

                                        @if (AppBasicCache.IsWindows)
                                        {
                                            <label class="checkbox-inline i-checks">
                                                <input type="checkbox" value="UnBind"><t>手动还原</t>
                                            </label>
                                        }

                                    </td>
                                </tr>
                            }
                           @*  <tr type="maintenance">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>数据查询</t><input type="checkbox" class="all-check" id="SQLExecutor" value="SQLExecutor">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Export"><t>导出</t>
                                    </label>
                                </td>
                            </tr> *@
                            <tr type="maintenance">
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>设备搜索</t><input type="checkbox" class="all-check" id="DeviceSearch" value="DeviceSearch">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="View"><t>查看</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Search"><t>查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Open"><t>自动刷新</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="Update"><t>修改IP</t>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>


            <div class="layui-colla-item">
                <h2 class="layui-colla-title">在线监控</h2>
                <div class="layui-colla-content">
                    <table>
                        <tbody>
                            <tr type="monitor">
                                <td rowspan="7" class="td-1">
                                    <label class="text-navy i-checks border font-bold">
                                        <t>在线监控</t><input type="checkbox" value="monitor" class="groupItem" />
                                    </label>
                                </td>
                                <td class="td-2">
                                    <label class="text-navy i-checks border">
                                        <t>在线监控菜单</t><input type="checkbox" class="all-check" id="WinFormMonitor" value="WinFormMonitor">
                                    </label>
                                </td>
                                <td class="td-3">
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="Login"><t>登录</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="ModifyMoney"><t>金额修改</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">

                                        <input type="checkbox" class="cloud" value="ModifyTypeColor"><t>修改车牌颜色</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="ModifyType"><t>修改车辆类型</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="ModifyRecord"><t>修改场内记录</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="ManualTrigger"><t>手动触发</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="QueryMonthlyCard"><t>月卡查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="QueryRecord"><t>记录查询</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="ChangeShifts"><t>交接班</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="ModifyPwd"><t>修改密码</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="MinWindow"><t>岗亭最小化</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="DownLoadCarList"><t>黑白名单</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="PassCarNo"><t>手动放行</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="ConfirmPass"><t>确认放行</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="GateSwitch"><t>道闸开关</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="GateCancel"><t>道闸常开</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="CancelCharge"><t>取消放行</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="FreeOpen"><t>免费放行</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="ShowMoney"><t>显示累计金额</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="OpenWeb"><t>登录后台入口</t>
                                    </label>
                                    <label class="checkbox-inline i-checks">
                                        <input type="checkbox" value="HandleEvent"><t>事件通知处理</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="HandlePreRecord"><t>预入出场处理</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="PrintReceipt"><t>打印小票</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="ModifySpace"><t>修改余位</t>
                                    </label>
                                    <label class="checkbox-inline i-checks cloud">
                                        <input type="checkbox" class="cloud" value="DebugTool"><t>调试工具</t>
                                    </label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="form-horizontal">
            <div class="layui-row form-group"></div>
            <div class=" layui-row">
                <div class="layui-col-sm4 col-sm-offset-2">
                    <button id="Save" class="btn btn-primary" type="button">
                        <i class="fa fa-check"></i> <t>保存</t>
                    </button>&nbsp;
                    <button id="Cancel" class="btn btn-warning" type="button">
                        <i class="fa fa-times"></i> <t>取消</t>
                    </button>
                </div>
            </div>
            <div class="layui-row form-group"></div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/iCheck/icheck.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>

    <script>

        if ('@carparking.Config.AppSettingConfig.SentryMode' == "2") {
            $(".cloud").addClass("layui-hide");
        }
        if ('@carparking.Config.PubVar.iParkingType' == "1") {
            $(".preferential").addClass("layui-hide");
        }
        var paramAct = $.getUrlParam("Act");
        var paramPowerGroupID = $.getUrlParam("PowerGroup_ID");
        myVerify.init();

        var index = parent.layer.getFrameIndex(window.name);

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                this.bindPower();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {
                layer.msg("处理中", { icon: 16, time: 0 });
                if (paramAct == "Update" && paramAct != null) {
                    $.post("/PowerGroup/GetPowerGroup", { PowerGroup_ID: paramPowerGroupID }, function (json) {
                        if (json.Success) {
                            //填充表格
                            $("#PowerGroup_Name").val(json.Data.PowerGroup_Name);
                            $("#PowerGroup_Remark").val(json.Data.PowerGroup_Remark);

                            var pJson = JSON.parse(json.Data.PowerGroup_Value);
                            //得到控制器名称
                            for (var control in pJson) {
                                var powers = eval(pJson[control]);
                                var fgtr = $("#" + control).closest("tr");
                                for (var p in powers) {
                                    var v = powers[p];
                                    var s = $(fgtr).find("input[type='checkbox'][value='" + p + "']");
                                    if (v) {
                                        s.iCheck('check');
                                    }
                                }
                            }
                            layer.closeAll();
                        } else {

                            layer.msg(json.Message, { icon: 0 });
                        }
                    }, 'json');
                } else {
                    layer.closeAll();
                }
            },

            bindPower: function () {
                //加载表单权限控制--3级页面(弹层)处理方式
                //var iframeSrc = document.location.href.split("/")[document.location.href.split("/").length - 1];
                //window.parent.parent.global.formPowerFill(iframeSrc, window.parent.location.pathname);
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    var PowerGroup_Name = $("#PowerGroup_Name").val();
                    var PowerGroup_Remark = $("#PowerGroup_Remark").val();
                    if (!myVerify.check()) return;

                    $("#Save").attr("disabled", true);
                    var jsonItem = '"controlName":{powerValue},';
                    var enumjson = '"Key":"Value",';

                    var trList = $("tr");
                    var PowerGroup_Value = "";

                    for (var i = 0; i < trList.length; i++) {
                        var pelist = "";
                        var trItem = trList[i]; //行
                        var iptList = $(trItem).find("input:checkbox").not(".groupItem"); //行所有checkbox
                        if ('@carparking.Config.AppSettingConfig.SentryMode' == "2") {
                            iptList = iptList.not(".cloud");
                        }
                        var controlName = iptList[0].value; //行第一个checkbox是control名称
                        for (var j = 1; j < iptList.length; j++) {
                            var cbInput = iptList[j];
                            if (cbInput.checked) {
                                pelist += enumjson.replace("Key", cbInput.value).replace("Value", cbInput.checked);
                            }
                        }
                        PowerGroup_Value += jsonItem.replace("controlName", controlName).replace("powerValue", pelist);
                    }
                    PowerGroup_Value = ("{" + PowerGroup_Value + "}").replace(/,}/g, "}");

                    layer.msg("处理中", { icon: 16, time: 0 });

                    var obj = {
                        PowerGroup_Name: PowerGroup_Name,
                        PowerGroup_Value: PowerGroup_Value,
                        PowerGroup_Remark: PowerGroup_Remark
                    };
                    if (paramAct == "Add") {
                        $.ajax({
                            type: 'post',
                            url: '/PowerGroup/AddPowerGroup',
                            dataType: 'json',
                            data: { jsonModel: JSON.stringify(obj) },
                            success: function (json) {
                                layer.closeAll();
                                if (json.Success) {
                                    layer.msg("保存成功", { icon: 1, time: 2000 }, function () {
                                        window.parent.pager.bindData(1);
                                    });
                                } else {
                                    layer.msg(json.Message, { icon: 0 });
                                    $("#Save").attr("disabled", false);
                                }
                            },
                            complete: function () { },
                            error: function () {
                                $("#Save").attr("disabled", false);
                                layer.msg("异常错误", { icon: 2 });
                            }
                        });
                    }
                    else if (paramAct == "Update") {
                        obj.PowerGroup_ID = paramPowerGroupID;
                        $.ajax({
                            type: 'post',
                            url: '/PowerGroup/UpdatePowerGroup',
                            dataType: 'json',
                            data: { jsonModel: JSON.stringify(obj) },
                            success: function (json) {
                                layer.closeAll();
                                if (json.Success) {
                                    layer.msg("保存成功", { icon: 1, time: 2000 }, function () {
                                        window.parent.parent.global.updatePower();
                                        //刷新父页
                                        window.parent.pager.bindData(window.parent.pager.pageIndexCurrent);
                                    });
                                } else {
                                    layer.msg(json.Message, { icon: 0 });
                                    $("#Save").attr("disabled", false);
                                }
                            },
                            complete: function () { },
                            error: function () {
                                $("#Save").attr("disabled", false);
                                layer.msg("异常错误", { icon: 2 });
                            }
                        });
                    }
                });

            }
        };

        $(function () {
            //layui配置
            layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use('index');

            //加载表中checkbox的i-checks样式
            $(".i-checks").iCheck({ checkboxClass: "icheckbox_square-green", radioClass: "iradio_square-green" });
            //功能 全选|全不选
            $(".all-check").each(function () {
                $(this).on('ifChecked', function () {
                    $(this).closest("tr").find("label").not(".font-bold").each(function () {
                        if (!$(this).hasClass("hide"))
                            $(this).find("input").iCheck('check');
                    })
                });

                $(this).on('ifUnchecked', function () {
                    $(this).closest("tr").find("label").not(".font-bold").find("input").iCheck('uncheck');
                });
            });

            //模块 全选|全不选
            $(".groupItem").each(function () {
                $(this).on('ifChecked', function () {
                    $(this).closest("table").find("tr[type='" + $(this).closest("tr").attr("type") + "']").each(function () {
                        $(this).find("label").each(function () {
                            if (!$(this).hasClass("hide"))
                                $(this).find("input").iCheck('check');
                        })
                    });
                });

                $(this).on('ifUnchecked', function () {
                    $(this).closest("table").find("tr[type='" + $(this).closest("tr").attr("type") + "']").each(function () {
                        $(this).find("label").find("input").iCheck('uncheck');
                    });
                });
            });

            pager.init()
        });
    </script>
</body>
</html>