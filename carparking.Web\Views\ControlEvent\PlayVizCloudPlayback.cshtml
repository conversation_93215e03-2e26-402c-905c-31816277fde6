<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>YM01视频回放</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <script src="~/Static/js/jquery.min.js"></script>
    <!-- 使用JessibucaPro播放器 -->
    <script src="~/Static/flveee/decoder-pro.js?v=@(new Random().Next())"></script>
    <script src="~/Static/flveee/flveee.js"></script>
    <style>
        html,
        body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
        }

        .video-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        #player {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div class="video-container">
        <div id="player"></div>
    </div>

    <script type="text/javascript">
        $(function () {
            var player = null;
            var videoUrl = '@Html.Raw(ViewBag.VideoUrl)'; // 从 ViewBag 获取视频 URL，使用 Html.Raw 防止编码问题
            console.log("ViewBag Video URL:", videoUrl); // 打印从 ViewBag 获取的 URL

            function initPlayer(url) {
                if (!url) {
                    console.error("视频 URL 无效");
                    // 可以在这里添加用户提示，例如使用 layer.msg
                    if (parent && parent.layer) {
                        parent.layer.msg('无效的视频地址', { icon: 2 });
                    } else {
                        alert('无效的视频地址');
                    }
                    // 尝试关闭弹窗
                    try {
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    } catch (e) {
                        console.warn("关闭窗口失败", e);
                    }
                    return;
                }

                if (player) {
                    player.destroy();
                    player = null;
                }

                var showOperateBtns = true; // 启用操作按钮的显示
                player = new JessibucaPro({
                    playType: 'playbackTF',
                    container: document.getElementById('player'),
                    decoder: '/Static/flveee/decoder-pro.js', // 让播放器自动寻找 .wasm 文件
                    videoBuffer: 0.2,
                    isResize: false,
                    text: "",
                    loadingText: "加载中",
                    debug: false,
                    debugLevel: 'debug',
                    isMulti: true,
                    useMSE: true,
                    autoWasm: true, // 启用自动 WASM 加载
                    decoderErrorAutoWasm: true, // 启用错误时自动 WASM 加载
                    useSIMD: true,
                    useWCS: true,
                    useMThreading: true,
                    hasAudio: false, // 假设回放通常没有音频，如有需要可改为 true
                    useVideoRender: true,
                    controlAutoHide: true,
                    showBandwidth: false, // 不显示带宽
                    showPerformance: false, // 不显示性能
                    isFlv: true, // 明确是 FLV 格式
                    showTimeline: true, // 显示时间轴/进度条
                    timeout: 15,
                    operateBtns: {
                        fullscreen: showOperateBtns,
                        screenshot: showOperateBtns,
                        play: showOperateBtns,
                        zoom: showOperateBtns,
                        showPerformance: showOperateBtns,
                    },
                    playbackConfig: {
                        controlType: 'simple',
                        duration: 600,
                        showControl: true,
                        isUseFpsRender: true,
                        isCacheBeforeDecodeForFpsRender: true,
                        supportWheel: false,
                        showRateBtn: true,
                        uiUsePlaybackPause: true,
                        isPlaybackPauseClearCache: false,
                        rateConfig: [
                            { label: '正常', value: 1 },
                            { label: '2倍', value: 2 },
                            { label: '4倍', value: 4 },
                            { label: '8倍', value: 8 },
                            { label: '16倍', value: 16 },
                        ]
                    },
                    demuxUseWorker: true,
                    mseDecoderUseWorker: true,
                    websocketOpenTimeout: 5,
                    loadingTimeout: 10,
                    streamErrorReplay: false, // 回放失败不自动重试
                    streamEndReplay: false, // 回放结束不自动重试
                    isNotMute: false,
                    playbackForwardMaxRateDecodeIFrame: false,
                    useWebGPU: false, // 使用WebGPU
                    playbackCheckStreamEnd: true,
                });

                player.on(JessibucaPro.EVENTS.playFailedAndPaused, function (err) {
                    console.error('Player Error:', err);
                    player.showErrorMessageTips('视频加载失败, 请检查地址或网络');
                    // 可以在这里添加用户提示，例如使用 layer.msg
                    if (parent && parent.layer) {
                        parent.layer.msg('视频加载失败', { icon: 2 });
                    }
                });
                player.on('playbackPreRateChange', (rate) => {
                    console.log("playbackPreRateChange:", rate);
                    player.forward(rate);
                })
                player.on('playbackSeek', (data) => {
                    console.log("playbackSeek:", data);
                    player.setPlaybackStartTime(data.ts);
                })
                player.on(JessibucaPro.EVENTS.streamEnd, function () {
                    console.log('视频播放结束');
                    if (parent && parent.layer) {
                        parent.layer.msg('视频播放结束', { icon: 1 });
                    }
                });
                player.on('playbackEnd', () => {
                    console.error('playbackEnd: 播放结束');
                })

                // 立即尝试播放
                setTimeout(function () {
                    console.log("Attempting to play URL:", url); // 打印即将播放的 URL
                    player && player.play(url).then(function () {
                        console.log("Successfully started playing video: " + url);
                    }).catch(function (error) {
                        console.error("Failed to play video:", error);
                        player.showErrorMessageTips('播放视频失败: ' + error); // 显示更详细的错误
                        if (parent && parent.layer) {
                            parent.layer.msg('播放视频失败', { icon: 2 });
                        }
                    });
                }, 0);

                function currentTimeScroll() {
                    if (player) {
                        player.playbackCurrentTimeScroll();
                    }
                }
            }

            // 初始化播放器
            initPlayer(videoUrl);

            // 页面关闭时清理资源
            window.onbeforeunload = function () {
                if (player) {
                    player.destroy();
                }
            };
        });
    </script>
</body>

</html>