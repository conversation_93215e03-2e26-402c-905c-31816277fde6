﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Echo Example</title>
    <script src="~/Static/js/jquery.min.js?v=2.1.4"></script>
    <style>
        html, body, form, input { margin: 0px; padding: 0px; width: 100%; height: 100%; overflow: hidden; }

        input { display: block; text-align: center; font-size: 24px; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body>
    <form id="form">
        <input id="message" autocomplete="off" />
    </form>
    <script>

        var wsUrl = "ws://*************:7681/admin?key=7ee09c0b-dbe7-40d2-b41a-d7fea04559dc";
        var TalkingInterval = null;
        var ws = null;
        var lockReconnect = false;//重连

        //对讲
        var Talking = {
            first: true,
            init: null,
            createWebSocket: null,
            closeTalking: null,
            errorVedioCount: {}
        };

        //初始化对讲
        Talking.init = function () {
            Talking.createWebSocket(wsUrl);
        }

        //创建WebSocket
        Talking.createWebSocket = function (url) {
            try {
                console.log("ws create")
                console.log(url)
                if ('WebSocket' in window) {
                    ws = new WebSocket(url);
                } else {
                    console.error("WebSocket连接没有建立成功！");
                    $("#UserTalkingStatus").html("离线");
                    $("#UserTalkingStatus").css("color", "red");
                    return;
                }
                Talking.initWebSocket();
            } catch (e) {
                Talking.reconnect(url);
                console.error("ws create error");
                console.error(e);
            }
        }

        //初始化WebSocket
        Talking.initWebSocket = function () {
            var heartCheck = {
                timeout: 10000,
                timeoutObj: null,
                serverTimeoutObj: null,
                reset: function () {
                    clearTimeout(this.timeoutObj);
                    clearTimeout(this.serverTimeoutObj);
                    return this;
                },
                start: function () {
                    var self = this;
                    this.timeoutObj = setTimeout(function () {
                        Talking.send("ping");
                        //self.serverTimeoutObj = setTimeout(function () { ws.close(); }, self.timeout);
                    }, this.timeout);
                }
            };
            ws.onclose = function () {
                console.error("ws onclose");
                Talking.reconnect(wsUrl);
                console.log("llws连接关闭!" + new Date().toLocaleString());
                $("#UserTalkingStatus").html("离线");
                $("#UserTalkingStatus").css("color", "red");
            };
            ws.onerror = function (e) {
                console.error("ws onerror:"+e.Message);
                Talking.reconnect(wsUrl);
                console.log("llws连接错误!");
                $("#UserTalkingStatus").html("离线");
                $("#UserTalkingStatus").css("color", "red");
            };
            ws.onopen = function () {
                console.log("ws onopen");
                heartCheck.reset().start();
                $("#UserTalkingStatus").html("在线");
                $("#UserTalkingStatus").css("color", "#0cff00");
            };
            ws.onmessage = function (event) {
                if (event.data == "Scramble") {
                    console.log("Scramble")
                    console.error("登录已失效,即将退出");

                    //return;
                    ws.close();
                    return;
                }
                heartCheck.reset().start();
                if (event.data != "Scramble" && event.data != 'pong' && event.data != '"pong"') {
                    var data = JSON.parse(event.data);
                    //console.log(data);
                    var guid = data["guid"];
                    var sender = data["sender"];
                    var responder = data["responder"];
                    var type = data["type"];
                    var act = data["act"];

                };
            };
        }

        //WebSocket重连
        Talking.reconnect = function () {
            if (lockReconnect)
                return;
            lockReconnect = true;
            //console.log(getLoginStatus());
            //Talking.getLoginStatus();
            console.log("重新连接！");
            setTimeout(function () {     //没连接上会一直重连，设置延迟避免请求过多
                lockReconnect = false;
                Talking.createWebSocket(wsUrl);
            }, 5000);
        }

        //发送消息
        Talking.send = function (message) {
            if (!window.WebSocket) { return; }
            if (ws != null && ws.readyState == WebSocket.OPEN) {
                ws.send(message);
            } else {
                Talking.reconnect(wsUrl);
                console.error("WebSocket 连接没有建立成功！");
            }
        }

        $(function () {

            debugger

            Talking.init();

        })
    </script>
</body>
</html>
