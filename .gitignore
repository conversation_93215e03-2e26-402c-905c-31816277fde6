# ---> <PERSON> Sharp
# Build Folders (you can keep bin if you'd like, to store dlls and pdbs)
[Bb]in/
[Oo]bj/

# mstest test results
TestResults

## Ignore Visual Studio temporary files, build results, and
## files generated by popular Visual Studio add-ons.

# User-specific files
*.suo
*.user
*.sln.docstates

# Build results
[Dd]ebug/
[Rr]elease/
x64/
*_i.c
*_p.c
*.ilk
*.meta
*.obj
*.pch
*.pdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.log
*.vspscc
*.vssscc
.builds

# Visual C++ cache files
ipch/
*.aps
*.ncb
*.opensdf
*.sdf

# Visual Studio profiler
*.psess
*.vsp
*.vspx

# Guidance Automation Toolkit
*.gpState

# ReSharper is a .NET coding add-in
_ReSharper*

# NCrunch
*.ncrunch*
.*crunch*.local.xml

# Installshield output folder
[Ee]xpress

# DocProject is a documentation generator add-in
DocProject/buildhelp/
DocProject/Help/*.HxT
DocProject/Help/*.HxC
DocProject/Help/*.hhc
DocProject/Help/*.hhk
DocProject/Help/*.hhp
DocProject/Help/Html2
DocProject/Help/html

# Click-Once directory
publish

# Publish Web Output
*.Publish.xml

# NuGet Packages Directory
packages

# Windows Azure Build Output
csx
*.build.csdef

# Windows Store app package directory
AppPackages/

# Others
[Bb]in
[Oo]bj
sql
TestResults
[Tt]est[Rr]esult*
*.Cache
ClientBin
[Ss]tyle[Cc]op.*
~$*
*.dbmdl
Generated_Code #added for RIA/Silverlight projects

# Backup & report files from converting an old project file to a newer
# Visual Studio version. Backup files are not needed, because we have git ;-)
_UpgradeReport_Files/
Backup*/
UpgradeLog*.XML

/.idea
/.vs
/carparking.Web/Log
/carparking.Web/Properties/launchSettings.json
/carparking.Web/Properties/PublishProfiles
/carparking.Web/Properties
/carparking.Web/Properties/PublishProfiles/FolderProfile.pubxml
/carparking.Weixin/Properties/PublishProfiles/FolderProfile.pubxml
/miniprogram
/carparking.Web/wwwroot/DOCM
/packages/DShinLib/DSkin.dll
/carparking.Web/Properties/PublishProfiles/FolderProfile.pubxml
/Dskin/DSkin.dll
/Dskin/DSkin.Design.dll
/packages/DShinLib/DSkin.dll
/packages/DShinLib/DSkin.Design.dll
/carparking.Config/appsettings.json
/carparking.OnlineMonitoring/appsettings.json
/carparking.Web/Properties/PublishProfiles/IISProfile.pubxml
/MigrationBackup/3c135b83/carparking.Guide
/carparking.Middleware/Properties/PublishProfiles/FolderProfile.pubxml
/carparking.Middleware/Properties
/carparking.AutoSentryBox/Properties/PublishProfiles/FolderProfile.pubxml
*.pubxml
/InstallPacket/Setup Files/T30车牌识别软件安装包.exe
/carparking.SDK/DbTransfer
/carparking.Guide/Config
/carparking.SDK/carparking.WorkerService/Properties
/InstallPacket/Prerequisites
/InstallPacket/Setup Files
/carparking.AutoSentryBox/Properties/launchSettings.json
/carparking.AutoSentryBox/Properties/PublishProfiles/FolderProfile.pubxml
/carparking.Web/CompressImages/20220223
/carparking.Web/1646319110473740.mp4
/InstallPacket/T30-cache
/InstallPacket
/T30Install/T30Install-SetupFiles
/T30Install/T30Install-cache
/T30Install/Setup Files
/carparking.T30InstallPacket/T30Install-cache
/carparking.T30InstallPacket/Setup Files
/carparking.GuideBS/Properties
/carparking.GuideBS/.vs/carparking.GuideBS/DesignTimeBuild
/carparking.Web/sys_image/202206/14
/carparking.Web/USERIMAGE/202206/14
/carparking.T30InstallPacket/Setup Files
/carparking.T30InstallPacket/T30Install-cache
/.idea/.idea.carparking
/UpgradeLog.htm
/T30批量发布版本命令.bat
/T30批量发布版本命令.bat
/T30批量发布版本命令.bat
/32-4G.bat
/B30批量发布版本命令.bat
/T30批量发布版本命令-web.bat
/B30开4G内存指令.bat
/T30批量发布版本命令64.bat
/T30批量发布版本命令32.bat
*.bat
/T30批量发布版本命令32.bat
/T30批量发布版本命令32.bat
/T30批量发布版本命令32.bat
/T30批量发布版本命令64.bat
/carparking.AutoSentryBox/D_cameraImages/20221213
/UpgradeLog3.htm
/T30批量发布版本命令32.bat
/T30批量发布版本命令64.bat
/B30批量发布版本命令.bat
/B30开4G内存指令.bat
/MigrationBackup/410e8eee/carparking.ManageCenter
/packages_1
/carparking.T30InstallPacket/T30Install.aip
/carparking.T30InstallPacket/T30Install-cache
/carparking.T30InstallPacket/Setup Files
/carparking.sln.DotSettings
/carparking.Installer/Resources
/Installer
.vscode/solution-explorer/template-parameters.js
/carparking.Installer/Resources/SourceHanSerifCN-Regular.otf
/carparking.Installer/Resources/ParkingMainApp.lz4
.vscode/settings.json
.vscode/settings.json
/.vscode/settings.json
.cursorignore
.cursor/mcp.json
.cursor/rules/behavior.mdc
/.roo
/.clinerules
/.tool
/.trae
/.vscode
/.windsurfrules
/.vscode
# 开发报告文档目录
/docs/development-reports/
/carparking.Web/dnscache.json
/.test
/.auto
/.kilocode
CLAUDE.md

# YoYo AI version control directory
.yoyo/
