﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入文件</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 20px; }
        .line-link { color: #0094ff; text-decoration: underline; }
        #Import { min-width: 90px; }
        #download-temp { }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div id="verifyCheck">
            <div class="layui-row form-group">&nbsp;</div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label lan-label" data-lan="Select_Excel">选择文件</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="layui-form-item layui-row margin-bottom0">
                        <div class="layui-col-xs9">
                            <input type="text" placeholder="" id="iptFilePath" class="layui-input" readonly="readonly">
                        </div>
                        <div class="layui-col-xs3">
                            <span class="input-group-btn">
                                <label title="选择" for="inputFile" class="layui-btn">
                                    <input type="file" accept=".xlsx" name="file" id="inputFile" class="hide" onchange="chooseFile(this);"><i class="fa fa-folder-open-o"></i> <t class="lan-label" data-lan="Select">选择</t>
                                </label>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs3">&nbsp;</div>
                <div class="layui-col-xs7">
                    <a href="../../Data/car_template.xlsx" id="download-temp" download="" class="layui-btn lan-label" style="background-color: #FF5722;">下载模板</a>
                    <button class="layui-btn" lay-submit lay-filter="formDemo" id="Import"><t class="lan-label" data-lan="submit">提交</t></button>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3">&nbsp;</div>
                <div class="layui-col-xs7">
                    <label class="label-desc">温馨提示：若车牌号已存在，不会重复写入和更新</label>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/ajaxfileupload2.js" asp-append-version="true"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js" asp-append-version="true"></script>
    <script>
        var paramCommunityNo = $.getUrlParam("communityNo");
        var comElement = null;
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'form', 'element'], function () { comElement = layui.element; });
    </script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        function chooseFile(iptFile) {
            $("#iptFilePath").val(iptFile.value);
        }
        function chooseFile2(iptFile) {
            var Count = iptFile.files.length || 0;
            $("#iptFilePath2").val(Count + " 张");
        }
        function ajaxFileUpload(ctNo) {
            $("#Import").attr("disabled", true);
            layer.msg("耗时较长,请等待..", { icon: 16, time: 0 });
            $.ajaxFileUpload({
                url: '/Car/Upload', //用于文件上传的服务器端请求地址
                type: 'post',
                data: {}, //此参数非常严谨，写错一个引号都不行
                secureuri: false, //一般设置为false
                fileElementId: ['inputFile', 'inputFile2'], //文件上传空间的id属性
                dataType: 'json', //返回值类型 一般设置为json
                success: function (data) //服务器成功响应处理函数
                {
                    if (data.Success) {
                        layer.open({
                            content: "导入成功"
                            , btn: ["我知道了"]
                            , yes: function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            }
                        });                        
                    } else {
                        var retMsg = data.Message.replaceAll('&lt;br/&gt;', "<br/>");
                        layer.open({ content: retMsg });
                    }
                },
                complete: function () {
                    $("#iptFilePath").val("");
                    $("#inputFile").val("")
                    $("#Import").attr("disabled", false);
                },
                error: function (data, status, e) //服务器响应失败处理函数
                {
                    console.log("[" + e.message + "]" + data.responseText)
                    layer.msg(status);
                }
            });
            return false;
        }
        var pager = {
            init: function () {
                this.bindSelect();
                this.bindEvent();
            },
            bindSelect: function () {


            },
            bindEvent: function () {
                $("#Import").click(function () {
                    var ctNo = $("#Community_No").val();
                    if ($("#inputFile").val() == "") { return; }
                    ajaxFileUpload(ctNo);
                });
                $("#Cancel").click(function () {
                    $("#Cancel").click(function () { parent.layer.close(index); });
                });
                $("#download-temp").click(function () {
                    this.href = "../../Data/car_template.xlsx";
                });
            }
        };

        $(function () { pager.init() });
    </script>
</body>
</html>