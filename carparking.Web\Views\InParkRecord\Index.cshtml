﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>出入场记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <link href="~/Static/css/searchfile.css" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }

        .layui-form-select .layui-input { width: 182px; }
    </style>
    <style data-mark="表格列数量多的时候使用此样式展示列选择">
        .layui-table-tool-panel { width: 500px; }

        .layui-table-tool-panel li { width: 33.33%; float: left; }

        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }

        .layui-layer-TipsG { border-bottom-color: rgba(0,0,0,.2) !important; display: none !important; }
        .layui-layer-content { background-color: rgb(255 255 255) !important; color: #000 !important; }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>出入场记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号（精确查询）" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo1" id="ParkOrder_CarNo1" autocomplete="off" placeholder="车牌号(模糊查询)" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="订单状态" class="form-control chosen-select " id="ParkOrder_StatusNo" name="ParkOrder_StatusNo" lay-search>
                                    <option value="" selected>订单状态</option>
                                    <option value="200">已入场</option>
                                    <option value="201">已出场</option>
                                    <option value="199">预入场</option>
                                    <option value="202">自动关闭</option>
                                    <option value="203">场内关闭</option>
                                    <option value="204">欠费出场</option>
                                    <option value="0">预出场</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" value="@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>

                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"> 搜索</i></button>
                            </div>
                            <div class="layui-inline">
                                <ul class="layui-nav searchFile">
                                    <li class="layui-nav-item">
                                        <a href="javascript:;" class="title">搜索方案&nbsp;</a>
                                        <dl class="layui-nav-child searchItem">
                                        </dl>
                                    </li>
                                </ul>
                            </div>

                        </div>


                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_No" id="ParkOrder_No" autocomplete="off" placeholder="订单编号" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌颜色" class="form-control chosen-select " id="ParkOrder_CarType" name="ParkOrder_CarType" lay-search>
                                    <option value="">车牌颜色</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入场车道" class="form-control chosen-select " id="ParkOrder_EnterPasswayNo" name="ParkOrder_EnterPasswayNo" lay-search>
                                    <option value="">入场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="入口操作员" class="layui-input" id="ParkOrder_EnterAdminAccount" name="ParkOrder_EnterAdminAccount" lay-search>
                                    <option value="">入口操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterAdminName" id="ParkOrder_EnterAdminName" autocomplete="off" placeholder="入口操作员" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="锁车状态" class="form-control chosen-select " id="ParkOrder_Lock" name="ParkOrder_Lock" lay-search>
                                    <option value="">锁车状态</option>
                                    <option value="0">未锁车</option>
                                    <option value="1">已锁车</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime0" id="ParkOrder_OutTime0" autocomplete="off" placeholder="出场时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutTime1" id="ParkOrder_OutTime1" autocomplete="off" placeholder="出场时间止" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_OutPasswayNo" name="ParkOrder_OutPasswayNo" lay-search>
                                    <option value="">出场车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="出口操作员" class="layui-input" id="ParkOrder_OutAdminAccount" name="ParkOrder_OutAdminAccount" lay-search>
                                    <option value="">出口操作员</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OutAdminName" id="ParkOrder_OutAdminName" autocomplete="off" placeholder="出口操作员" />
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_ParkAreaNo" name="ParkOrder_ParkAreaNo" lay-search>
                                    <option value="">停车区域</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_IsFree" name="ParkOrder_IsFree" lay-search>
                                    <option value="">免费放行</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_IsNoInRecord" name="ParkOrder_IsNoInRecord" lay-search>
                                    <option value="">无入场记录</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>

                            <div class="layui-inline">
                                <select class="layui-select" id="ParkOrder_IsEpCar" name="ParkOrder_IsEpCar" lay-search>
                                    <option value="">重点地区车辆</option>
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarLogo" id="ParkOrder_CarLogo" autocomplete="off" placeholder="车辆车标" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_FreeReason" id="ParkOrder_FreeReason" autocomplete="off" placeholder="免费原因" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OwnerName" id="ParkOrder_OwnerName" autocomplete="off" placeholder="车主姓名" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_OwnerSpace" id="ParkOrder_OwnerSpace" autocomplete="off" placeholder="系统车位号" />
                            </div>
                            @if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.CloudServer)
                            {
                                <div class="layui-inline">
                                    <select class="layui-select" id="ParkOrder_IsSupplement" name="ParkOrder_IsSupplement" lay-search>
                                        <option value="">订单类型</option>
                                        <option value="1" selected>补单放行</option>
                                    </select>
                                </div>
                            }
                            <div class="layui-inline">
                                <select data-placeholder="默认数据" class="form-control chosen-select " id="dataType" name="dataType" lay-search>
                                    <option value="0">默认数据</option>
                                    <option value="1">历史数据</option>
                                </select>
                            </div>

                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Add" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Update" lay-event="Update"><i class="fa fa-edit"></i><t>修改</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Detail" lay-event="Detail"><i class="fa fa-list-alt"></i><t>详情</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Delete" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="DelBind" lay-event="DelBind"><i class="fa fa-trash-o"></i><t>批量关闭</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Payment" lay-event="Payment"><i class="fa fa-cny"></i><t>支付</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Send" lay-event="Send"><i class="fa fa-cloud-upload"></i><t>上传云平台</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Import" lay-event="Import"><i class="fa fa-file-excel-o"></i><t>导入</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools2.min.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/searchfile.js" asp-append-version="true"></script>
    <script type="text/x-jquery-tmpl" id="tmplouttype">
        {{# if(d.ParkOrder_OutType==0){}}
        <span class="layui-badge layui-bg-gray">未出场</span>
        {{# }else if(d.ParkOrder_OutType==1){}}
        <span class="layui-badge layui-bg-blue">预出场</span>
        {{# }else if(d.ParkOrder_OutType==2){}}
        <span class="layui-badge layui-bg-green">已出场</span>
        {{# } }}
    </script>
    <script>

        var isFrpUrl = IsFrpURLOpenWeb('@Html.Raw(ViewBag.ParkKey)');

        try {
            var picker = new SCarnoPicker();
            var picker2 = new SCarnoPicker();

            picker.init("ParkOrder_CarNo", function (text, carno) {
                if (picker.eleid == "ParkOrder_CarNo") {
                    $("#ParkOrder_CarNo").val(carno.join(''));
                }
            }, "web").bindkeyup();

            picker2.init("ParkOrder_CarNo1", function (text, carno) {
                if (picker2.eleid == "ParkOrder_CarNo1") {
                    $("#ParkOrder_CarNo1").val(carno.join(''));
                }
            }, "web").bindkeyup();

            topBar.init();
        } catch (e) {
            console.log("初始化异常：" + e.message);
        }

        var gotoData = localStorage.getItem("gotoInParkRecord");
        if (gotoData != null && gotoData != '' && gotoData != "null") {
            try {
                var gotod = JSON.parse(gotoData);
                $("#searchForm select").val('');
                $("#searchForm input").val('');

                $("#ParkOrder_No").val(gotod.ParkOrder_No || "");
                $("#ParkOrder_CarNo").val(gotod.ParkOrder_CarNo || "");
                $("#ParkOrder_CarNo1").val(gotod.ParkOrder_CarNo1 || "");
                $("#ParkOrder_IsEpCar").val(gotod.ParkOrder_IsEpCar || "");
                $("#ParkOrder_StatusNo").val(gotod.ParkOrder_StatusNo || "");
                $("#ParkOrder_EnterTime0").val(gotod.ParkOrder_EnterTime0 || "");
                $("#ParkOrder_EnterTime1").val(gotod.ParkOrder_EnterTime1 || "");

                layui.form.render();
                localStorage.setItem("gotoInParkRecord", null);
            } catch (e) {
                console.log(e);
            }
        }

        var comtable = null;
        var layuiForm = null;
        var layuiDate = null;
        var element = null;

        layui.use(['table', 'form', 'laydate', 'element'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            layuiDate = layui.laydate;
            element = layui.element;
            try {
                pager.init();
            } catch (e) {
                console.log(e);
            }

            // 监听表格的左键点击事件
            document.addEventListener('click', function (e) {
                // 如果有选中的文字，则不执行复制逻辑
                if (hasSelectedText()) return;

                var td = e.target.closest('td'); // 找到最近的 td
                if (td && td.innerText) {
                    copyToClipboard(td.innerText);
                    layer.tips('已复制：' + td.innerText.trim(), td, {
                        tips: [4, '#20222A'],
                        time: 3000
                    });
                }
            });

            try {
                searchFile.bindData(0);

                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                pager.conditionParam = conditionParam;
                pager.dataType = $("#dataType").val();

            var cols = [[
                { type: 'checkbox' }
                , { field: 'ParkOrder_ID', title: '订单ID', hide: true }
                , { field: 'ParkOrder_No', title: '订单号', width: 220, hide: true }
                , { field: 'ParkOrder_ParkNo', title: '车场编码', hide: true }
                , { field: 'ParkOrder_CarNo', title: '车牌号', width: 120 }
                , { field: 'ParkOrder_CarCardType', title: '车牌类型编码', hide: true }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型' }
                , { field: 'ParkOrder_CarType', title: '车牌颜色编码', hide: true }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色' }
                , {
                    field: 'ParkOrder_StatusNo', title: '订单状态', templet: function (d) {
                        if (d.ParkOrder_StatusNo == 199) return tempBar(2, "预入场");
                        else if (d.ParkOrder_StatusNo == 200) {
                            if (d.ParkOrder_OutType == 0) return tempBar(1, "已入场");
                            if (d.ParkOrder_OutType == 1) return tempBar(3, "预出场");
                        }
                        else if (d.ParkOrder_StatusNo == 201) return tempBar(4, "已出场");
                        else if (d.ParkOrder_StatusNo == 202) return tempBar(0, "自动关闭");
                        else if (d.ParkOrder_StatusNo == 203) return tempBar(0, "场内关闭");
                        else if (d.ParkOrder_StatusNo == 204) return tempBar(6, "欠费出场");
                    }
                }
                , { field: 'ParkOrder_ParkAreaNo', title: '停车区域编码', hide: true }
                , { field: 'ParkOrder_ParkAreaName', title: '停车区域' }
                , { field: 'ParkOrder_EnterTime', title: '入场时间', width: 160, sort: true }
                , { field: 'ParkOrder_EnterPasswayNo', title: '入口车道编码', hide: true }
                , { field: 'ParkOrder_EnterPasswayName', title: '入口车道' }
                , { field: 'ParkOrder_EnterAdminAccount', title: '入口操作员账号', hide: true }
                , { field: 'ParkOrder_EnterAdminName', title: '入口操作员', hide: true }
                , {
                    field: 'ParkOrder_EnterImgPath', title: '入出场图', templet: function (d) {
                        if (d.ParkOrder_EnterImgPath || d.ParkOrder_OutImgPath) {
                            if (isFrpUrl) {
                                d.ParkOrder_EnterImgPath = replaceFirstPathSegment(d.ParkOrder_EnterImgPath);
                                d.ParkOrder_OutImgPath = replaceFirstPathSegment(d.ParkOrder_OutImgPath);
                            }
                            var src = [];
                            if (d.ParkOrder_EnterImgPath != null && d.ParkOrder_EnterImgPath != '')
                                src[src.length] = "入场图片|" + d.ParkOrder_EnterImgPath;
                            if (d.ParkOrder_OutImgPath != null && d.ParkOrder_OutImgPath != '')
                                src[src.length] = "出场图片|" + d.ParkOrder_OutImgPath;

                            return '<p class="layui-btn layui-btn-xs pm_img_preview" data-src="' + src.join(',') + '">图片</p>';
                        }
                        else
                            return '<p class="layui-badge layui-bg-gray">无图</p>';
                    }
                }
                , { field: 'ParkOrder_EnterRemark', title: '入场备注', hide: true }
                , { field: 'ParkOrder_OutTime', title: '出场时间', width: 160, sort: true }
                , { field: 'ParkOrder_OutPasswayNo', title: '出口车道编码', hide: true }
                , { field: 'ParkOrder_OutPasswayName', title: '出口车道' }
                , { field: 'ParkOrder_OutAdminAccount', title: '出口操作员账号', hide: true }
                , { field: 'ParkOrder_OutAdminName', title: '出口操作员', hide: true }
                //, { field: 'ParkOrder_OutImgPath', title: '出场图片', templet: function (d) { return tempImg(d.ParkOrder_OutImgPath); } }
                , {
                    field: 'ParkOrder_IsFree', title: '免费放行', hide: true, templet: function (d) {
                        if (d.ParkOrder_IsFree == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }
                , {
                    field: 'ParkOrder_CarLogo', title: '车辆车标', hide: true, templet: function (d) {
                        return ((d.ParkOrder_CarLogo == "0" || d.ParkOrder_CarLogo == null) ? "" : d.ParkOrder_CarLogo);
                    }
                }
                , {
                    field: 'ParkOrder_Lock', title: '锁车状态', hide: true, templet: function (d) {
                        if (d.ParkOrder_Lock == 1) return tempBar(1, "已锁车");
                        else if (d.ParkOrder_Lock == 0) return tempBar(3, "未锁车");
                    }
                }
                , { field: 'ParkOrder_TotalAmount', title: '应收金额', totalRow: true, templet: function (d) { if (d.ParkOrder_TotalAmount != null) return ToFixed2(d.ParkOrder_TotalAmount); else return ToFixed2(0); }, sort: true }
                , { field: 'ParkOrder_TotalPayed', title: '实收金额', totalRow: true, templet: function (d) { if (d.ParkOrder_TotalPayed != null) return ToFixed2(d.ParkOrder_TotalPayed); else return ToFixed2(0); }, sort: true }

                , { field: 'ParkOrder_OwnerNo', title: '车主编号', hide: true }
                , { field: 'ParkOrder_OwnerName', title: '车主姓名'}
                , { field: 'ParkOrder_FreeReason', title: '免费原因', hide: true }
                , {
                    field: 'ParkOrder_IsLift', hide: true, title: '智能升降', templet: function (d) {
                        if (d.ParkOrder_IsLift != 0 && d.ParkOrder_IsLift != null) return tempBar(1, "是");
                        else return tempBar(3, "否");
                    }
                }
                , {
                    field: 'ParkOrder_UserNo', title: (@carparking.Config.AppSettingConfig.SentryMode== "2" ? "补单放行" : '用户'), hide: true, templet: function (d) {
                        if (@carparking.Config.AppSettingConfig.SentryMode == @carparking.Common.VersionEnum.CloudServer) {
                            if (d.ParkOrder_UserNo == "1" || d.ParkOrder_UserNo == "2") return tempBar(1, "是");
                            else return tempBar(3, "否");
                        } else {
                            return "";
                        }
                    }
                }
                , {
                    field: 'ParkOrder_PayScene', title: '支付场景', hide: true, templet: function (d) {
                        if (d.ParkOrder_PayScene == 1) return tempBar(1, "场内缴费");
                        else if (d.ParkOrder_PayScene == 2) return tempBar(2, "出口缴费");
                        else return "";
                    }
                }
                , { field: 'ParkOrder_Remark', title: '订单备注', hide: true }
                , {
                    field: 'ParkOrder_IsNoInRecord', title: '无入场记录', hide: true, templet: function (d) {
                        if (d.ParkOrder_IsNoInRecord == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }
                , {
                    field: 'ParkOrder_IsEpCar', title: '重点地区车辆', hide: true, templet: function (d) {
                        if (d.ParkOrder_IsEpCar == 1) return tempBar(0, "是");
                        else return tempBar(3, "否");
                    }
                }
            ]];

                cols = tb_page_cols(cols);

                comtable = table.render({
                    elem: '#com-table-base'
                    , url: '/InParkRecord/GetParkOrderList'
                    , method: 'post'
                    , toolbar: '#toolbar_btns'
                    , defaultToolbar: ["filter"]
                    , cellMinWidth: 90
                    , cols: cols
                    , page: {
                        layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                        groups: 3,
                    }
                    , totalRow: true
                    , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                    , where: { conditionParam: JSON.stringify(conditionParam) }
                    , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                    , done: function (d) {
                        pager.dataCount = d.count;
                        tb_page_set(d);
                        pager.bindPower();
                        onPreviewImage.init();
                    }
                });

                //头工具栏事件
                table.on('toolbar(com-table-base)', function (obj) {
                    var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                    var data = checkStatus.data;  //获取选中行数据
                    pager.pageIndex = $(".layui-laypage-curr").text();
                    switch (obj.event) {
                        case 'Add':
                            layer.open({
                                type: 2,
                                title: "新增停车记录",
                                content: "/InParkRecord/Add",
                                area: getIframeArea(["800px", "550px"]),
                                maxmin: false
                            });
                            break;
                        case 'Update':
                            if (!pager.checkDataType()) { return; }
                            if (data.length == 0) { layer.msg("请选择"); return; }
                            if (data.length > 1) { layer.msg("请单选"); return; }
                            layer.open({
                                id: 1,
                                title: "修改订单",
                                type: 2,
                                area: getIframeArea(['800px', '620px']),
                                fix: false, //不固定
                                maxmin: false,
                                content: 'Edit?ParkOrder_No=' + encodeURIComponent(data[0].ParkOrder_No)
                            });
                            break;
                        case 'Delete':
                            if (!pager.checkDataType()) { return; }
                            if (data.length == 0) { layer.msg("请选择"); return; }
                            var NoArray = [];
                            for (var i = 0; i < data.length; i++) {
                                NoArray.push(data[i].ParkOrder_No);
                            }

                            layer.open({
                                id: 2,
                                type: 0,
                                title: "消息提示",
                                btn: ["确定", "取消"],
                                content: "<font style='color:red;'>您正在删除停车记录，请谨慎操作。</font>确定删除?<br/>(若启用了一位多车智能升降，删除该订单不会对该车主下的其它车辆自动升降处理)",
                                yes: function (res) {
                                    layer.msg("处理中", { icon: 16, time: 0 });
                                    $.getJSON("/InParkRecord/Delete", { ParkOrder_NoArray: JSON.stringify(NoArray) }, function (json) {
                                        if (json.success)
                                            layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                        else
                                            layer.msg(json.msg, { icon: 0, time: 2500 });
                                    });
                                },
                                btn2: function () { }
                            })
                            break;
                        case 'DelBind':
                            if (!pager.checkDataType()) { return; }
                            layer.open({
                                type: 2,
                                title: "批量删除",
                                content: "/InParkRecord/BathDelete",
                                area: getIframeArea(["600px", "500px"]),
                                maxmin: false
                            });
                            break;
                        case 'Import':
                            layer.open({
                                type: 2,
                                title: "导入停车记录",
                                content: "/InParkRecord/Import",
                                area: getIframeArea(["500px", "400px"]),
                                maxmin: false
                            });
                            break;
                        case 'Export':
                            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                            if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                                var inTime = false;
                                var outTime = false;
                                if (conditionParam.ParkOrder_EnterTime0 != null && conditionParam.ParkOrder_EnterTime1 != null) { inTime = true; }
                                if (conditionParam.ParkOrder_OutTime0 != null && conditionParam.ParkOrder_OutTime1 != null) { outTime = true; }
                                if (!inTime && !outTime) { layer.msg("请选择入场或出场的起止时间", { icon: 0 }); return; }

                                if (inTime) {
                                    if (_DATE.diffDay(new Date(conditionParam.ParkOrder_EnterTime0), new Date(conditionParam.ParkOrder_EnterTime1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                                } else if (outTime) {
                                    if (_DATE.diffDay(new Date(conditionParam.ParkOrder_OutTime0), new Date(conditionParam.ParkOrder_OutTime1)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                                }
                            }

                            var field = pager.sortField == null ? "" : pager.sortField;
                            var order = pager.orderField == null ? "" : pager.orderField;

                            pager.dataField = [];
                            obj.config.cols[0].forEach((item) => {
                                if (item.title)
                                    pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                            });
                            layer.open({
                                id: "x_edit_iframe",
                                type: 2,
                                title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                                content: '/ExportExcel/Index?Act=Update&Owner_No=',
                                area: getIframeArea(['1100px', '400px']),
                                maxmin: false,
                                end: function () {
                                    if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                        var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                            return data;
                                        });
                                        var field = pager.sortField == null ? "" : pager.sortField;
                                        var order = pager.orderField == null ? "" : pager.orderField;
                                        conditionParam.SearchType = topBar.config.SearchType;

                                        //实现Ajax下载文件
                                        $.fileDownload('/InParkRecord/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                            httpMethod: 'GET',
                                            headers: {},
                                            data: null,
                                            prepareCallback: function (url) {
                                                $("#Export").attr("disabled", true);
                                                layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                            },
                                            successCallback: function (url) {
                                                $("#Export").attr("disabled", false);
                                                layer.msg('导出成功');
                                            },
                                            failCallback: function (html, url) {
                                                $("#Export").attr("disabled", false);
                                                layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                            }
                                        });
                                    }
                                }
                            });
                            break;
                        case 'Detail':
                            if (data.length == 0) { layer.msg("请选择"); return; }
                            if (data.length > 1) { layer.msg("请单选"); return; }
                            var orderno = data[0].ParkOrder_No;
                            pager.dataTime = data[0].ParkOrder_EnterTime;
                            pager.dataType = $("#dataType").val();
                            layer.open({
                                title: "详情",
                                type: 2, id: 1,
                                area: ['95%', '95%'],
                                fix: true, //不固定
                                maxmin: true,
                                content: 'Detail?From=ParkOrder&ParkOrder_No=' + encodeURIComponent(orderno)
                            });
                            break;
                        case 'Send':
                            if (!pager.checkDataType()) { return; }
                            if (data.length == 0) { layer.msg("请选择"); return; }
                            var NoArray = [];
                            for (var i = 0; i < data.length; i++) {
                                NoArray.push(data[i].ParkOrder_No);
                            }

                            var successMsg = "处理成功. ";
                            if (NoArray.length > 10)
                                successMsg += "<br/>选择数据较多时上传云平台会有一点延迟，请耐心等待。";

                            layer.open({
                                id: 2,
                                type: 0,
                                title: "消息提示",
                                btn: ["确定", "取消"],
                                content: "确定上传选中的停车记录到云平台?",
                                yes: function (res) {
                                    layer.msg("处理中", { icon: 16, time: 0 });
                                    $.getJSON("/InParkRecord/SendCloudPark", { ParkOrder_NoArray: JSON.stringify(NoArray) }, function (json) {
                                        if (json.success)
                                            layer.msg(successMsg, { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                        else
                                            layer.msg(json.msg, { icon: 0, time: 1500 });
                                    });
                                },
                                btn2: function () { }
                            })
                            break;

                        case 'Payment':
                            if (!pager.checkDataType()) { return; }
                            if (data.length == 0) { layer.msg("请选择", { icon: 0, time: 1500 }); return; }
                            if (data.length > 1) { layer.msg("请单选", { icon: 0, time: 1500 }); return; }
                            if (data[0].ParkOrder_StatusNo != 200 && data[0].ParkOrder_StatusNo != 204) { layer.msg("不在场内无需支付", { icon: 0, time: 1500 }); return; }
                            var orderno = data[0].ParkOrder_No;
                            layer.open({
                                title: "<i class='fa fa-rmb' style='margin-top: 17px;'></i> 停车支付",
                                type: 2, id: 1,
                                area: getIframeArea(['960px', '640px']),
                                fix: false, //不固定
                                maxmin: false,
                                content: 'Payment?ParkOrder_No=' + encodeURIComponent(orderno)
                            });
                            break;

                        default: break;
                    };
                });

                //排序
                table.on('sort(com-table-base)', function (obj) {
                    if (obj.type == null) obj.field = null;
                    pager.sortField = obj.field;
                    pager.orderField = obj.type;
                    pager.bindData(1);
                });

                tb_row_checkbox(table);
            } catch (e) {
                console.error("layui数据加载错误:", e);
            }
        });

        // 复制内容到剪贴板
        function copyToClipboard(text) {
            var textarea = document.createElement("textarea");
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand("copy");
            document.body.removeChild(textarea);
        }

        // 判断是否有选中的文字
        function hasSelectedText() {
            var selection = window.getSelection();
            return selection && selection.toString().trim().length > 0;
        }
    </script>
    <script>
        function isWithinThreeMonths(startDate, endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);

            // 计算 start 1 个月后的日期
            const threeMonthsLater = new Date(start);
            threeMonthsLater.setMonth(start.getMonth() + 1);

            // 检查 end 是否在 1 个月范围内
            return end <= threeMonthsLater;
        }

        var pager = {
            sortField: null,
            orderField: null,
            dataField: null,
            conditionParam: null,
            pageIndex: 1,
            dataCount: 0,
            dataType: 0,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                // 获取车辆卡类型
                $.post("SltCarCardTypeList", {}, function (json) {
                    try {
                        if (json.success) {
                            json.data.forEach(function (d) {
                                var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                                $("#ParkOrder_CarCardType").append(option);
                            });
                        } else {
                            console.error("SltCarCardTypeList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltCarCardTypeList 错误:", e);
                    }
                }, "json");

                // 获取车辆类型
                $.post("SltCarTypeList", {}, function (json) {
                    try {
                        if (json.success) {
                            json.data.forEach(function (d) {
                                var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                                $("#ParkOrder_CarType").append(option);
                            });
                        } else {
                            console.error("SltCarTypeList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltCarTypeList 错误:", e);
                    }
                }, "json");

                // 获取通道列表
                $.post("SltGatePasswayList", {}, function (json) {
                    try {
                        if (json.success) {
                            var optionIn = '';
                            var optionOut = '';
                            json.data.forEach(function (d) {
                                if (d.Passway_GateType == 0 || d.Passway_GateType == 3) {
                                    optionOut += '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                                } else {
                                    optionIn += '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                                }
                            });
                            $("#ParkOrder_EnterPasswayNo").append(optionIn);
                            $("#ParkOrder_OutPasswayNo").append(optionOut);
                        } else {
                            console.error("SltGatePasswayList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltGatePasswayList 错误:", e);
                    }
                }, "json");

                // 获取停车区域
                $.post("SltParkAreaList", {}, function (json) {
                    try {
                        if (json.success) {
                            json.data.forEach(function (d) {
                                var option = '<option value="' + d.ParkArea_No + '">' + d.ParkArea_Name + '</option>';
                                $("#ParkOrder_ParkAreaNo").append(option);
                            });
                        } else {
                            console.error("SltParkAreaList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltParkAreaList 错误:", e);
                    }
                }, "json");

                // 获取管理员列表
                $.post("SltAdminList", {}, function (json) {
                    try {
                        if (json.success) {
                            json.data.forEach(function (d) {
                                var option = '<option value="' + d.Admins_Account + '">' + d.Admins_Name + '</option>';
                                $("#ParkOrder_EnterAdminAccount").append(option);
                                $("#ParkOrder_OutAdminAccount").append(option);
                            });
                        } else {
                            console.error("SltAdminList 请求失败:", json.message);
                        }
                    } catch (e) {
                        console.error("SltAdminList 错误:", e);
                    }
                }, "json");

                // 渲染 layui 表单
                layui.form.render("select");

                // 日期时间绑定
                try {
                    _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: "datetime", range: true });
                    _DATE.bind(layui.laydate, ["ParkOrder_OutTime0", "ParkOrder_OutTime1"], { type: "datetime", range: true });
                } catch (e) {
                    console.error("日期时间绑定错误:", e);
                }
            },
            bindData: function (index) {
                try {
                    layer.closeAll();
                    var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                    pager.conditionParam = conditionParam;
                    var field = pager.sortField == null ? "" : pager.sortField;
                    var order = pager.orderField == null ? "" : pager.orderField;
                    pager.dataType = $("#dataType").val();

                    if (pager.dataType == "1") {
                        if (conditionParam.ParkOrder_EnterTime0 == null || conditionParam.ParkOrder_EnterTime1 == null) { layer.msg("请选择入场起止时间", { icon: 0 }); return; }
                        if (conditionParam.ParkOrder_CarNo1 != null && conditionParam.ParkOrder_CarNo1 != "" && !isWithinThreeMonths(conditionParam.ParkOrder_EnterTime0, conditionParam.ParkOrder_EnterTime1)) { layer.msg("车牌模糊查询时间范围不能超过1个月，请使用车牌精确查询", { icon: 0 }); return; }
                    }

                    comtable.reload({
                        url: '/InParkRecord/GetParkOrderList'
                        , where: { conditionParam: JSON.stringify(conditionParam), field: field, order: order } //设定异步数据接口的额外参数
                        , page: { curr: index }
                    });
                } catch (e) {
                    console.error("数据加载错误:", e);
                }
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });

                layui.form.on("select", function (data) {
                    if (data.elem.id == "dataType" && data.value == "1") {
                        var layerDataType = layer.open({
                            id: 2,
                            type: 0,
                            title: "查询数据须知",
                            btn: ["知道了"],
                            content: "默认查询范围：当您未指定时间条件时，系统将自动查询当前年份的全部数据。<br/>跨年查询限制：若选择历史数据，查询时间范围需在同一年内。<br/>例如，若开始时间设定为2024年，则仅能查询2024年的相关数据，无法跨年查询2023年或2025年的数据。",
                            yes: function (res) {
                                layer.close(layerDataType)
                            },
                            btn2: function () { }
                        })
                    }
                })
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    //console.log(pagePower)
                });
            },
            checkDataType: function () {
                if (pager.dataType == 1) {
                    layer.msg("历史数据不能进行修改等操作"); return false;
                }
                return true;
            }
        }
    </script>
</body>
</html>
