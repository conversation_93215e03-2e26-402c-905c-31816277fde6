﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using carparking.BLL.Cache;
using carparking.Common;
using carparking.Config;
using carparking.SentryBox;
using carparking.SentryBox.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.WebUtilities;

namespace carparking.Web.Controllers
{
    public class ControlEventController : BaseController
    {
        public ActionResult Index()
        {
            if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.View.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }

        public IActionResult CheckOrder()
        {
            if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            string ControlEvent_No = Request.Query["ControlEvent_No"];
            var model = BLL.ControlEvent.GetEntity(ControlEvent_No) ?? new Model.ControlEvent();

            ViewBag.start = model.ControlEvent_Time.Value.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.end = model.ControlEvent_Time.Value.AddMinutes(1).ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.passwayno = model.ControlEvent_PasswayNo;
            ViewBag.gate = model.ControlEvent_Gate;
            return View();
        }

        /// <summary>
        /// 视频监控视图
        /// </summary>
        /// <returns></returns>
        public IActionResult Monitor()
        {
            string url = string.Empty;
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                    return new EmptyResult();

                url = Request.Query["url"];
                ViewBag.MonitorUrl = url;
                return View();
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "视频设备监控视图", $"视频设备监控视图加载发生异常:[{url}]" + ex.ToString());
                HttpHelper.HttpContext.Response.Redirect(@"/Static/html/AccessError.html");
                return new EmptyResult();
            }
        }

        /// <summary>
        /// 仅查询外场区域的车道
        /// </summary>
        /// <returns></returns>
        public IActionResult SltOutGatePasswayList()
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");

                List<Model.PasswayLink> links = BLL.PasswayLink.GetAllEntity(parking.Parking_No);

                List<Model.ParkArea> area = BLL.ParkArea.GetAllEntity("*", $"ParkArea_Type=0");
                List<string> sildeAreaNos = area.Select(x => x.ParkArea_No)?.ToList() ?? new List<string>();
                List<Model.PasswayLink> sideLinks = new List<Model.PasswayLink>();

                foreach (var item in links)
                {
                    if (!sildeAreaNos.Contains(item.PasswayLink_ParkAreaNo)) continue;

                    var count = links.FindAll(x => x.PasswayLink_PasswayNo == item.PasswayLink_PasswayNo).Count;
                    if (count == 1)
                    {
                        sideLinks.Add(item);
                    }
                }

                List<string> passwayNos = sideLinks.Select(x => x.PasswayLink_PasswayNo)?.ToList() ?? new List<string>();

                List<Model.Passway> passways = BLL.Passway.GetAllEntity("Passway_ID,Passway_No,Passway_Name,Passway_SameInOut,Passway_OutNo"
                    , $"Passway_ParkNo='{parking.Parking_No}' AND Passway_No in ('{string.Join("','", passwayNos)}')");

                List<Dictionary<string, object>> data = TyziTools.Json.ToObject<List<Dictionary<string, object>>>(TyziTools.Json.ToString(passways));

                foreach (var item in data)
                {
                    var l = links?.FindAll(x => x.PasswayLink_PasswayNo == item["Passway_No"].ToString());

                    if (l == null || l?.Count == 0)
                        item.Add("Passway_GateType", 1);
                    else if (l?.Count == 1)
                        item.Add("Passway_GateType", l.First().PasswayLink_GateType);
                    else
                        item.Add("Passway_GateType", 1);

                    item.Add("Passway_Area", string.Join(',', l?.Select(x => x.PasswayLink_ParkAreaNo)));

                }

                return ResOk(true, "", data);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 查询跟车事件
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="conditionParam"></param>
        public IActionResult GetList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), false, true, lgAdmins))
                    return Ok(oModel);

                if (pageSize > 1000)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                Model.ControlEventWhere model = Utils.ClearModelRiskSQL<Model.ControlEventWhere>(conditionParam);
                if (model == null)
                    return Ok(new Model.PageResult(-1, "", 0, null));

                StringBuilder sqlwhere = new StringBuilder();
                if (!string.IsNullOrEmpty(model.ControlEvent_No))
                    sqlwhere.Append($" and ControlEvent_No = @ControlEvent_No ");
                if (!string.IsNullOrEmpty(model.ControlEvent_CarNo))
                    sqlwhere.Append($" and ControlEvent_CarNo like @ControlEvent_CarNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_ParkAreaNo))
                    sqlwhere.Append($" and ControlEvent_ParkAreaNo = @ControlEvent_ParkAreaNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_PasswayNo))
                    sqlwhere.Append($" and ControlEvent_PasswayNo = @ControlEvent_PasswayNo ");
                if (!string.IsNullOrEmpty(model.ControlEvent_DeviceNo))
                    sqlwhere.Append($" and ControlEvent_DeviceNo = @ControlEvent_DeviceNo ");
                if (model.ControlEvent_Status != null)
                    sqlwhere.Append($" and ControlEvent_Status = @ControlEvent_Status ");
                if (model.ControlEvent_Type != null)
                    sqlwhere.Append($" and ControlEvent_Type = @ControlEvent_Type ");

                if (model.ControlEvent_Time0 != null && model.ControlEvent_Time1 != null)
                    sqlwhere.Append($" and ControlEvent_Time between @ControlEvent_Time0 AND @ControlEvent_Time1 ");
                else if (model.ControlEvent_Time0 != null)
                    sqlwhere.Append($" and ControlEvent_Time >= @ControlEvent_Time0 ");
                else if (model.ControlEvent_Time1 != null)
                    sqlwhere.Append($" and ControlEvent_Time <= @ControlEvent_Time1 ");

                object parameters = new
                {
                    ControlEvent_No = model.ControlEvent_No,
                    ControlEvent_CarNo = !string.IsNullOrEmpty(model.ControlEvent_CarNo) ? $"%{model.ControlEvent_CarNo}%" : null,
                    ControlEvent_ParkAreaNo = model.ControlEvent_ParkAreaNo,
                    ControlEvent_PasswayNo = model.ControlEvent_PasswayNo,
                    ControlEvent_DeviceNo = model.ControlEvent_DeviceNo,
                    ControlEvent_Status = model.ControlEvent_Status,
                    ControlEvent_Type = model.ControlEvent_Type,
                    ControlEvent_Time0 = model.ControlEvent_Time0,
                    ControlEvent_Time1 = model.ControlEvent_Time1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ControlEvent> lst = BLL.BaseBLL._GetList<Model.ControlEvent>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;
                return Ok(oModel);
            }
            catch (Exception)
            {
                oModel.code = 4;
                oModel.msg = "异常错误";
            }
            return Ok(oModel);
        }

        public IActionResult GetControlEvent(string ControlEvent_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                    return ResOk(false, "无权限");


                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);

                return ResOk(true, "", model);
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        public IActionResult Add()
        {
            if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Add.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }
        /// <summary>
        /// 新增跟车事件
        /// </summary>
        /// <returns></returns>
        public IActionResult AddEvent()
        {
            if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Plus.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            return View();
        }
        public IActionResult AddFollow(string jsonModel)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Plus.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ControlEvent model = Utils.ClearModelRiskSQL<Model.ControlEvent>(jsonModel);
                if (model == null) { return ResOk(false, "操作失败请重试"); }

                Model.Passway pass = BLL.Passway.GetEntity(model.ControlEvent_PasswayNo);
                if (pass == null) { return ResOk(false, "车道信息不存在"); }
                var device = BLL.Device.GetEntityByPasswayNo(pass.Passway_No);
                if (device == null) return ResOk(false, "车道未设置主相机设备");
                var area = BLL.ParkArea.GetAllEntity("*", $"ParkArea_Type=0 AND ParkArea_No in (select PasswayLink_ParkAreaNo from PasswayLink where PasswayLink_PasswayNo='{pass.Passway_No}')")?.FirstOrDefault();
                if (area == null) return ResOk(false, "读取车道关联的外场区域失败");

                if (!string.IsNullOrWhiteSpace(model.ControlEvent_CarNo))
                {
                    if (model.ControlEvent_CarNo.Trim().Length < 7) return ResOk(false, "车牌号不允许为空且长度最小为7");
                    if (!Utils.IsZhNumEn(model.ControlEvent_CarNo.Trim()))
                    {
                        return ResOk(false, "车牌号仅支持(中文、字母、数字)组成");
                    }
                    model.ControlEvent_CarNo = model.ControlEvent_CarNo.Replace('o', '0').Replace('O', '0').Trim().Replace(" ", ""); //字母O替换数字0
                    model.ControlEvent_CarNo = model.ControlEvent_CarNo.ToUpper();
                }

                model.ControlEvent_No = Utils.CreateNumber;
                model.ControlEvent_AddTime = DateTime.Now;
                model.ControlEvent_AdminAccount = lgAdmins.Admins_Account;
                model.ControlEvent_AdminName = lgAdmins.Admins_Name;
                model.ControlEvent_PasswayName = pass.Passway_Name;
                model.ControlEvent_PasswayNo = pass.Passway_No;
                model.ControlEvent_Status = 0;
                model.ControlEvent_Type = 1;
                model.ControlEvent_ParkNo = parking.Parking_No;
                model.ControlEvent_ParkName = parking.Parking_Name;
                model.ControlEvent_DeviceNo = device?.Device_No;
                model.ControlEvent_DeviceName = device?.Device_Name;
                model.ControlEvent_ParkAreaName = area?.ParkArea_Name;
                model.ControlEvent_ParkAreaNo = area?.ParkArea_No;

                if (!string.IsNullOrEmpty(model.ControlEvent_BigImg) && !model.ControlEvent_BigImg.Contains("http://") && !model.ControlEvent_BigImg.Contains("https://"))
                {
                    string base64String = model.ControlEvent_BigImg;
                    string photoExtension = null;
                    if (base64String.Contains("base64,")) { photoExtension = base64String.Substring(0, base64String.IndexOf(";")).Substring(base64String.IndexOf("/") + 1); }

                    string filename = $"{Utils.CreateNumber}.{photoExtension ?? "jpg"}";
                    string path = string.Empty;
                    path = $"http://{AppBasicCache.Ip}:{AppSettingConfig.SiteDomain_WebPort}/CameraCaptures/{DateTime.Now.ToString("yyyyMM")}/{DateTime.Now.ToString("dd")}/{filename}";

                    model.ControlEvent_BigImg = LPRTools.OnSaveByDate(base64String, filename, BLL.ImageTools.LocalFilePath, !string.IsNullOrEmpty(BLL.ImageTools.LocalFilePath), device.Device_SentryHostNo);
                }

                var res = BLL.BaseBLL._Insert(model);
                if (res > 0)
                {
                    //内场区域的跟车不发送到智慧停车平台
                    if (area != null && area.ParkArea_Type == 0)
                        BLL.PushEvent.CarFollowAdd(parking.Parking_Key, model);

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"{LogHelper.GetEntityCotent(model)}", SecondIndex.CarFollow);
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"失败:{LogHelper.GetEntityCotent(model)}", SecondIndex.CarFollow);
                    return ResOk(false, "插入数据失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"异常:{ex.ToString()}", SecondIndex.CarFollow);
                return ResOk(false, $"保存失败：{ex.Message}");
            }
        }

        public IActionResult AddParkOrder(string jsonModel, string ControlEvent_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Add.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ControlEvent evt = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (evt == null) { return ResOk(false, "操作失败请重试"); }
                if (evt.ControlEvent_Type != 1) { return ResOk(false, "操作失败请重试"); }
                if (evt.ControlEvent_Gate != 1) { return ResOk(false, "操作失败请重试"); }
                if (evt.ControlEvent_Status == 2 || evt.ControlEvent_Status == 3) { return ResOk(false, "操作失败请重试"); }

                Model.ParkOrder query = Utils.ClearModelRiskSQL<Model.ParkOrder>(jsonModel);
                if (string.IsNullOrEmpty(query?.ParkOrder_CarNo)) { return ResOk(false, "车牌号不能为空"); }
                if (query?.ParkOrder_EnterTime == null) { return ResOk(false, "入场时间不能为空"); }

                var exist = BLL.ParkOrder.GetParkOrder("*", parking.Parking_No, query.ParkOrder_CarNo);
                if (exist != null) { return ResOk(false, "车辆已在场内，不允许重复添加"); }

                var area = BLL.ParkArea.GetEntity(query?.ParkOrder_ParkAreaNo);
                if (area == null) { return ResOk(false, "区域不存在"); }

                var passway = BLL.Passway.GetEntity(query?.ParkOrder_EnterPasswayNo);
                if (passway == null) { return ResOk(false, "车道不存在"); }

                var card = BLL.CarCardType.GetEntity(query?.ParkOrder_CarCardType);
                if (card == null) { return ResOk(false, "车牌类型不存在"); }

                var carType = BLL.CarType.GetEntity(query?.ParkOrder_CarType);
                if (carType == null) { return ResOk(false, "车牌颜色不存在"); }

                var car = BLL.Car.GetEntityByCarNo(query.ParkOrder_CarNo);

                query.ParkOrder_ParkAreaName = area.ParkArea_Name;
                query.ParkOrder_EnterPasswayName = passway.Passway_Name;
                query.ParkOrder_CarCardTypeName = card.CarCardType_Name;
                query.ParkOrder_CarTypeName = carType.CarType_Name;

                Model.ParkOrder model = BLL.ParkOrder.CreateParkOrder(parking.Parking_No, query.ParkOrder_ParkAreaNo, query.ParkOrder_ParkAreaName, query.ParkOrder_CarNo, query.ParkOrder_CarCardType, query.ParkOrder_CarCardTypeName, query.ParkOrder_CarType, query.ParkOrder_CarTypeName, query.ParkOrder_EnterTime.Value, query.ParkOrder_EnterPasswayNo, query.ParkOrder_EnterPasswayName, 0, 0, car?.Car_OwnerNo, car?.Car_OwnerName);
                model.ParkOrder_StatusNo = Model.EnumParkOrderStatus.In;
                model.ParkOrder_EnterAdminAccount = lgAdmins?.Admins_Account;
                model.ParkOrder_EnterAdminName = lgAdmins?.Admins_Name;
                model.ParkOrder_EnterRemark = query.ParkOrder_EnterRemark;
                model.ParkOrder_EnterImgPath = evt.ControlEvent_BigImg;

                Model.OrderDetail detail = BLL.OrderDetail.CreateOrderDetail(model.ParkOrder_No, parking.Parking_No, query.ParkOrder_ParkAreaNo, query.ParkOrder_ParkAreaName, query.ParkOrder_CarNo, query.ParkOrder_CarCardType, query.ParkOrder_CarCardTypeName, query.ParkOrder_CarType, query.ParkOrder_CarTypeName, query.ParkOrder_EnterTime.Value, query.ParkOrder_EnterPasswayNo, query.ParkOrder_EnterPasswayName);
                detail.OrderDetail_StatusNo = Model.EnumParkOrderStatus.In;
                detail.OrderDetail_EnterAdminAccount = lgAdmins?.Admins_Account;
                detail.OrderDetail_EnterAdminName = lgAdmins?.Admins_Name;
                detail.orderdetail_EnterRemark = query.ParkOrder_EnterRemark;

                evt.ControlEvent_Status = 3;
                evt.ControlEvent_AdminAccount = lgAdmins?.Admins_Account;
                evt.ControlEvent_AdminName = lgAdmins?.Admins_Name;
                evt.ControlEvent_OkTime = DateTime.Now;
                evt.ControlEvent_ParkOrderNo = model.ParkOrder_No;
                evt.ControlEvent_CarNo = model.ParkOrder_CarNo;

                //var payData = carparking.Charge.Calc.GetChargeByCar(model, evt.ControlEvent_Time, null);
                //evt.ControlEvent_Money = payData?.orderamount ?? 0;
                BLL.ParkOrder.EpParkOrder(ref model, null);

                var details = new List<Model.OrderDetail> { detail };
                var res = BLL.ControlEvent.Handle(evt, (model, details), out string errmsg);
                if (res > 0)
                {
                    var ds = new Model.API.PushResultParse.ControlEvent()
                    {
                        Item1 = evt,
                        Item2 = model,
                        Item3 = details
                    };
                    Push(Model.API.PushAction.Edit, ds, new List<Model.SentryHost>(), "carfollowhandle", dataType: DataTypeEnum.ControlEvent, Desc: $"新增{model.ParkOrder_CarNo}");
                    BLL.PushEvent.EnterCar(parking.Parking_Key, model);
                    BLL.PushEvent.CarFollowUpdate(
                        parking.Parking_Key
                        , evt.ControlEvent_No
                        , evt.ControlEvent_CarNo
                        , evt.ControlEvent_ParkOrderNo
                        , evt.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , evt.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , BLL.CarFollowUpdate_Status.BindOrder
                        , lgAdmins.Admins_Name
                        , evt.ControlEvent_BigImg ?? ""
                        , evt.ControlEvent_Video ?? ""
                        , evt.ControlEvent_Remark
                        , "0");

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"成功:{TyziTools.Json.ToString((model, detail), true)}", SecondIndex.CarFollow);
                    return ResOk(true, "保存成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"失败:{jsonModel}.{errmsg}", SecondIndex.CarFollow);
                    return ResOk(false, errmsg);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, "新增停车记录", $"新增停车记录异常:{jsonModel}");
                return ResOk(false, $"保存失败：{ex.Message}");
            }
        }

        public IActionResult SelectOrder()
        {
            if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Update.ToString(), false, true, lgAdmins))
                return new EmptyResult();

            string ControlEvent_No = Request.Query["ControlEvent_No"];
            var model = BLL.ControlEvent.GetEntity(ControlEvent_No) ?? new Model.ControlEvent();

            ViewBag.start = model.ControlEvent_Time.Value.AddMinutes(-1).ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.end = model.ControlEvent_Time.Value.AddMinutes(1).ToString("yyyy-MM-dd HH:mm:ss");

            return View();
        }

        public IActionResult GetOrderList(int pageIndex, int pageSize, string conditionParam, string ControlEvent_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Update.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                if (model == null) { return ResOk(false, "参数错误"); }

                Model.ControlEvent conEvt = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (conEvt == null || conEvt.ControlEvent_Time == null) return ResOk(false, "事件不存在");

                StringBuilder sqlwhere = new StringBuilder();
                sqlwhere.Append($" and ParkOrder_EnterTime<='{conEvt.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")}' ");
                sqlwhere.Append($" and ParkOrder_StatusNo NOT IN (199,201,204) ");//过滤 预入场、已出场、欠费出场 的停车订单
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");

                if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                    sqlwhere.Append($" and ParkOrder_EnterPasswayNo = @ParkOrder_EnterPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                    sqlwhere.Append($" and ParkOrder_OutPasswayNo = @ParkOrder_OutPasswayNo ");

                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime between @ParkOrder_EnterTime0 AND @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime <= @ParkOrder_EnterTime1 ");

                var parameters = new
                {
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? $"%{model.ParkOrder_CarNo}%" : null,
                    ParkOrder_EnterPasswayNo = model.ParkOrder_EnterPasswayNo,
                    ParkOrder_OutPasswayNo = model.ParkOrder_OutPasswayNo,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder._GetList<Model.ParkOrder>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;

                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"失败:{ex.Message}", SecondIndex.CarFollow);
                oModel.code = -1;
                oModel.msg = ex.Message;
                return Ok(oModel.ParseJson());
            }
        }

        public IActionResult BindOrder(string ControlEvent_No, string ParkOrder_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Update.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) { return ResOk(false, "事件不存在"); }
                else if (model.ControlEvent_Status == 2) { return ResOk(false, "事件已处理"); }
                else if (model.ControlEvent_Status == 3) { return ResOk(false, "事件已处理"); }
                else if (model.ControlEvent_Status == 4) { return ResOk(false, "事件已处理"); }

                var ret = BLL.ControlEvent.bindOrder(ref model, ParkOrder_No, out var order, out var detail, out var errmsg);
                if (!ret) return ResOk(false, errmsg);

                var po = order.Item1 ?? new Model.ParkOrder();
                var payData = carparking.Charge.Calc.GetChargeByCar(order.Item1, model.ControlEvent_Time, null, null, false, "", "", null, order.Item2, po.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Follow ? true : false);

                #region 合计停车金额
                if (order.Item1 != null)
                {
                    var payOrders = BLL.PayOrder.GetAllEntity("PayOrder_No,PayOrder_Money,PayOrder_PayedMoney", $"PayOrder_ParkOrderNo='{po.ParkOrder_No}' AND PayOrder_Status=1") ?? new List<Model.PayOrder>();
                    po.ParkOrder_TotalAmount = payOrders.Sum(x => x.PayOrder_Money) ?? 0;
                    po.ParkOrder_TotalPayed = payOrders.Sum(x => (x.PayOrder_PayedMoney ?? 0) + (x.PayOrder_StoredMoney ?? 0));
                }
                #endregion

                model.ControlEvent_AdminAccount = lgAdmins?.Admins_Account;
                model.ControlEvent_AdminName = lgAdmins?.Admins_Name;
                model.ControlEvent_Money = payData?.orderamount ?? 0;
                var res = BLL.ControlEvent.Handle(model, order, out errmsg);
                if (res > 0)
                {
                    if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                        //关闭弹窗
                        PasswayConfirmReleaseUtil.RemoveResultByOrderNo(model.ControlEvent_ParkOrderNo);
                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                    }

                    //计费详情
                    BLL.CommonBLL.CreateCalcDetail(payData, order.Item1);

                    var ds = new Model.API.PushResultParse.ControlEvent()
                    {
                        Item1 = model,
                        Item2 = order.Item1,
                        Item3 = order.Item2
                    };
                    //Push(Model.API.PushAction.Edit, ds, new List<Model.SentryHost>(), "carfollowhandle", dataType: DataTypeEnum.ControlEvent, Desc: $"绑定{model.ControlEvent_CarNo}");

                    BLL.PushEvent.CarFollowUpdate(
                        parking.Parking_Key
                        , model.ControlEvent_No
                        , model.ControlEvent_CarNo
                        , model.ControlEvent_ParkOrderNo
                        , model.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , model.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , BLL.CarFollowUpdate_Status.BindOrder
                        , lgAdmins?.Admins_Name
                        , model.ControlEvent_BigImg ?? ""
                        , model.ControlEvent_Video ?? ""
                        , model.ControlEvent_Remark
                        , model.ControlEvent_Money.Value.ToString());

                    return ResOk(true, "关联订单成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"绑定失败:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                    return ResOk(false, errmsg);
                }
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 忽略事件
        /// </summary>
        /// <param name="ControlEvent_No"></param>
        /// <param name="ControlEvent_Remark">备注</param>
        /// <returns></returns>
        public IActionResult IgnoreControlEvent(string ControlEvent_No, string ControlEvent_Remark)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Close.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) { return ResOk(false, "事件不存在"); }
                if (model.ControlEvent_Status == 1) { return ResOk(true, "处理成功"); }
                if (model.ControlEvent_Status == 4) return ResOk(false, "事件已缴费");

                var ret = BLL.ControlEvent.Ingore(ref model, ControlEvent_Remark, out var order, out var errmsg);
                if (!ret) return ResOk(false, errmsg);

                model.ControlEvent_AdminAccount = lgAdmins?.Admins_Account;
                model.ControlEvent_AdminName = lgAdmins?.Admins_Name;

                var res = BLL.ControlEvent.Handle(model, order, out errmsg);
                if (res > 0)
                {
                    if (!string.IsNullOrEmpty(order.Item1?.ParkOrder_No))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(order.Item1.ParkOrder_CarNo, null, CloseNoInPark: false);
                    }

                    var ds = new Model.API.PushResultParse.ControlEvent()
                    {
                        Item1 = model,
                        Item2 = order.Item1,
                        Item3 = order.Item2
                    };

                    //Push(Model.API.PushAction.Edit, ds, new List<Model.SentryHost>(), "carfollowhandle", dataType: DataTypeEnum.ControlEvent, Desc: $"忽略{model.ControlEvent_CarNo}");

                    BLL.PushEvent.CarFollowUpdate(
                        parking.Parking_Key
                        , model.ControlEvent_No
                        , model.ControlEvent_CarNo ?? ""
                        , model.ControlEvent_ParkOrderNo ?? ""
                        , (model.ControlEvent_Time == null ? "" : model.ControlEvent_Time.Value.ToString("yyyy-MM-dd HH:mm:ss"))
                        , model.ControlEvent_OkTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        , BLL.CarFollowUpdate_Status.Ignore
                        , lgAdmins?.Admins_Name
                        , model.ControlEvent_BigImg ?? ""
                        , model.ControlEvent_Video ?? ""
                        , model.ControlEvent_Remark
                        , "0");

                    return ResOk(true, "处理成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"忽略失败:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                    return ResOk(false, errmsg);
                }
            }
            catch (Exception ex)
            {
                return ResOk(false, ex.Message);
            }
        }

        /// <summary>
        /// 处理倒车事件
        /// </summary>
        /// <param name="ControlEvent_No"></param>
        /// <returns></returns>
        public IActionResult CompleteBackEvent(string ControlEvent_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Audit.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) { return ResOk(false, "事件不存在"); }
                if (model.ControlEvent_Status == 5) { return ResOk(true, "处理成功"); }
                if (model.ControlEvent_Status == 4) return ResOk(false, "事件已缴费");

                Model.API.apiBackCar apiModel = TyziTools.Json.ToObject<Model.API.apiBackCar>(System.Web.HttpUtility.UrlDecode(model.ControlEvent_Content));

                var device = BLL.Device.GetEntity(model.ControlEvent_DeviceNo);
                if (device == null) return ResOk(false, "相机信息不存在");
                var passway = BLL.Passway.GetEntity(device.Device_PasswayNo);
                if (passway == null) return ResOk(false, "车道信息不存在");

                List<Model.ParkOrder> orders = new List<Model.ParkOrder>();
                List<Model.OrderDetail> details = new List<Model.OrderDetail>();
                BLL.BackCar.BackHandle(ref apiModel, ref orders, ref details, out var errmsg);

                if (orders != null && orders.Count > 0)
                {
                    var res = BLL.ParkOrder.CarInComplete(orders, details);
                    if (res > 0)
                    {
                        if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                        {
                            CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                            CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                            //关闭弹窗
                            PasswayConfirmReleaseUtil.RemoveResultByOrderNo(model.ControlEvent_ParkOrderNo);
                            //刷新弹窗
                            SentryBox.CommHelper.CheckConfirmResultForCarNo(model.ControlEvent_CarNo, null, CloseNoInPark: false);
                        }

                        var order = orders.First();
                        BLL.BackCar.Add(apiModel, device, order, passway, out var backCar);

                        model.ControlEvent_Status = 5;
                        BLL.ControlEvent.Handle(model, lgAdmins, order);

                        _ = CustomThreadPool.WebTaskPool?.QueueTask(null, () =>
                        {
                            BLL.PushEvent.CarBack(parking.Parking_Key, backCar.BackCar_No, backCar.BackCar_CarNo, backCar.BackCar_OrderNo, backCar.BackCar_GateType == 0 ? "2" : "1", backCar.BackCar_Time.Value.ToString("yyyy-MM-dd HH:mm:ss"), backCar.BackCar_PasswayNo, backCar.BackCar_Remark);

                            if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.Out)
                            {
                                //发送异常出场到智慧停车平台
                                BLL.PushEvent.OutCar(parking.Parking_Key, order, order.ParkOrder_Remark);
                                BLL.PushEvent.SendParkSpace(parking.Parking_No);
                            }
                            else if (order.ParkOrder_StatusNo == Model.EnumParkOrderStatus.In)
                            {
                                BLL.PushEvent.CarOrderStatus(parking.Parking_Key, order.ParkOrder_CarNo, order.ParkOrder_No, "200", apiModel.time, order.ParkOrder_Remark);
                            }

                            //收到线下软件上传的订单后，转发给其他岗亭
                            if (AppBasicCache.IsSendTcp) Library.PushTools.SendToClient(Model.API.PushAction.Edit, null, (orders, details), parking.Parking_Secret, "carin", DataTypeEnum.InParkRecord, $"倒车事件{order?.ParkOrder_CarNo}");
                            return Task.CompletedTask;
                        });

                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"处理成功:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                        return ResOk(true, "倒车处理订单成功", parking.Parking_No);
                    }
                    else
                    {
                        BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"处理失败:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                        return ResOk(false, "倒车处理订单失败", parking.Parking_No);
                    }
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"未生成入场订单，无需处理:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                    return ResOk(true, "未生成入场订单，无需处理", parking.Parking_No);
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(null, "倒车处理订单异常", ex.ToString());
                return ResOk(false, $"倒车处理订单失败：{ex.Message}", "");
            }
        }

        /// <summary>
        /// 忽略倒车事件
        /// </summary>
        /// <param name="ControlEvent_No"></param>
        /// <returns></returns>
        public IActionResult IngoreBackEvent(string ControlEvent_No)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Reject.ToString(), false, true, lgAdmins))
                    return ResOk(false, "无权限");

                var model = BLL.ControlEvent._GetEntityByNo(new Model.ControlEvent(), ControlEvent_No);
                if (model == null) return ResOk(false, "事件不存在");
                if (model.ControlEvent_Type != 3) return ResOk(false, "不是倒车事件");
                if (model.ControlEvent_Status == 1) return ResOk(true, "处理成功");
                if (model.ControlEvent_Status == 5) return ResOk(false, "事件已处理");
                if (model.ControlEvent_Status == 4) return ResOk(false, "事件已缴费");

                model.ControlEvent_Status = 1;
                model.ControlEvent_OkTime = DateTime.Now;
                model.ControlEvent_AdminAccount = lgAdmins?.Admins_Account;
                model.ControlEvent_AdminName = lgAdmins?.Admins_Name;

                var res = BLL.ControlEvent._UpdateByModelByNo(model);
                if (res > 0)
                {
                    if (!string.IsNullOrEmpty(model.ControlEvent_ParkOrderNo))
                    {
                        CalcCache.Del("OrderPrice:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice1:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice2:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice3:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del($"OrderPrice4:" + model.ControlEvent_ParkOrderNo);
                        CalcCache.Del("OrderPriceAutoPay:" + model.ControlEvent_ParkOrderNo);

                        //刷新弹窗
                        SentryBox.CommHelper.CheckConfirmResultForCarNo(null, model.ControlEvent_ParkOrderNo, CloseNoInPark: false);
                    }

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"忽略倒车事件成功:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                    return ResOk(true, "处理成功");
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"忽略倒车事件失败:{TyziTools.Json.ToString(model)}", SecondIndex.CarFollow);
                    return ResOk(false, "处理失败");
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"忽略倒车事件失败:{ex.ToString()}", SecondIndex.CarFollow);
                return ResOk(false, ex.Message);
            }
        }

        public ActionResult UploadVideo()
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                    return Json(new { Success = 0, Msg = "无权限", Src = "" });

                if (HttpContext.Request.Form.Files.Count > 0)
                {
                    var formFiles= HttpContext.Request.Form.Files;
                    IFormFile aFile = formFiles[0];
                    if (!Utils.IsOnlyVideoFiles(formFiles))
                    {
                        return Json(new { Success = 0, Msg = "只允许上传视频文件", Src = "" });
                    }
                    string imgPath = BLL.ImageTools.LocalFilePath;
                    string fileExtension = aFile.FileName.Substring(aFile.FileName.LastIndexOf('.') + 1);

                    string filename = $"{Utils.CreateNumber}.{fileExtension ?? "mp4"}";

                    string src = Common.LocalFile.FileOnSaveByDate(aFile, filename, imgPath, !string.IsNullOrEmpty(imgPath));

                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Upload, $"上传视频成功:{src}", SecondIndex.CarFollow);

                    return Json(new { Success = 1, Msg = "上传成功", Src = src });
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Upload, $"上传视频失败:请选择视频文件", SecondIndex.CarFollow);
                    return Json(new { Success = 0, Msg = "请选择视频文件", Src = "" });
                }
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Upload, $"上传视频失败:{ex.Message}", SecondIndex.CarFollow);
                return Json(new { Success = 0, Msg = ex.Message, Src = "" });
            }
        }

        public IActionResult GetCheckOrderList(int pageIndex, int pageSize, string conditionParam)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Update.ToString(), false, true, lgAdmins))
                    return new EmptyResult();

                Model.ParkOrderWhere model = Utils.ClearModelRiskSQL<Model.ParkOrderWhere>(conditionParam);
                if (model == null)
                {
                    oModel.code = -1;
                    oModel.msg = "参数错误";
                    return Ok(oModel.ParseJson());
                }

                StringBuilder sqlwhere = new StringBuilder();

                if (!string.IsNullOrEmpty(model.ParkOrder_No))
                    sqlwhere.Append($" and ParkOrder_No=@ParkOrder_No ");
                if (!string.IsNullOrEmpty(model.ParkOrder_EnterPasswayNo))
                    sqlwhere.Append($" and ParkOrder_EnterPasswayNo=@ParkOrder_EnterPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_OutPasswayNo))
                    sqlwhere.Append($" and ParkOrder_OutPasswayNo=@ParkOrder_OutPasswayNo ");
                if (!string.IsNullOrEmpty(model.ParkOrder_CarNo))
                    sqlwhere.Append($" and ParkOrder_CarNo like @ParkOrder_CarNo ");

                if (model.ParkOrder_EnterTime0 != null && model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime between @ParkOrder_EnterTime0 AND @ParkOrder_EnterTime1 ");
                else if (model.ParkOrder_EnterTime0 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime >= @ParkOrder_EnterTime0 ");
                else if (model.ParkOrder_EnterTime1 != null)
                    sqlwhere.Append($" and ParkOrder_EnterTime <= @ParkOrder_EnterTime1 ");

                if (model.ParkOrder_OutTime0 != null && model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime between @ParkOrder_OutTime0 AND @ParkOrder_OutTime1 ");
                else if (model.ParkOrder_OutTime0 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime >= @ParkOrder_OutTime0 ");
                else if (model.ParkOrder_OutTime1 != null)
                    sqlwhere.Append($" and ParkOrder_OutTime <= @ParkOrder_OutTime1 ");

                var parameters = new
                {
                    ParkOrder_No = model.ParkOrder_No,
                    ParkOrder_EnterPasswayNo = model.ParkOrder_EnterPasswayNo,
                    ParkOrder_OutPasswayNo = model.ParkOrder_OutPasswayNo,
                    ParkOrder_CarNo = !string.IsNullOrEmpty(model.ParkOrder_CarNo) ? "%" + model.ParkOrder_CarNo + "%" : null,
                    ParkOrder_EnterTime0 = model.ParkOrder_EnterTime0,
                    ParkOrder_EnterTime1 = model.ParkOrder_EnterTime1,
                    ParkOrder_OutTime0 = model.ParkOrder_OutTime0,
                    ParkOrder_OutTime1 = model.ParkOrder_OutTime1
                };

                int pageCount = 0, totalRecord = 0;
                List<Model.ParkOrder> lst = BLL.ParkOrder._GetList<Model.ParkOrder>("*", sqlwhere.ToString(), pageIndex, pageSize, out pageCount, out totalRecord, parameters: parameters);

                oModel.code = 0;
                oModel.data = lst;
                oModel.count = totalRecord;

                return Ok(oModel.ParseJson());
            }
            catch (Exception ex)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"查询异常放行列表异常:{ex.ToString()}", SecondIndex.CarFollow);
                oModel.code = -1;
                oModel.msg = ex.Message;
                return Ok(oModel.ParseJson());
            }
        }

        /// <summary>
        /// 播放YM01视频回放
        /// </summary>
        /// <param name="videoUrl">YM01回放视频URL</param>
        /// <param name="passwayNo">车道编号</param>
        /// <returns></returns>
        public async Task<IActionResult> PlayVizCloudPlayback(string videoUrl, string passwayNo)
        {
            // 添加权限检查
            if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
            {
                return BadRequest("无权限");
            }

            if (string.IsNullOrWhiteSpace(passwayNo))
            {
                return BadRequest("车道编号不能为空");
            }

            //地址格式：https://video3.vzicloud.com:4431/playback.flv?port=13036&channel=2&videotime=1744182014&end_time=1744184180
            if (string.IsNullOrWhiteSpace(videoUrl))
            {
                return BadRequest("视频URL不能为空");
            }

            var isSuccess = false;
            //获取地址上的参数
            // 使用标准库解析查询参数
            var uri = new Uri(videoUrl);
            var queryParams = QueryHelpers.ParseQuery(uri.Query);

            if (!queryParams.TryGetValue("videotime", out var videotimeStr) || !long.TryParse(videotimeStr, out var startTime))
            {
                return BadRequest("无效或缺失的 videotime 参数");
            }
            if (!queryParams.TryGetValue("end_time", out var endTimeStr) || !long.TryParse(endTimeStr, out var endTime))
            {
                return BadRequest("无效或缺失的 end_time 参数");
            }

            var vizCloudDevice = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m => m.Value.Device_PasswayNo.Contains(passwayNo) && m.Value.Drive_Code == "10123");
            if (vizCloudDevice.Value != null)
            {
                using var _vizCloudDevice = new TcpConnPools.SceneVideo.VziCloud.VziCloudService(accessKeyId: vizCloudDevice.Value.Device_AppKey, accessKeySecret: vizCloudDevice.Value.Device_Secret);
                // startTime 和 endTime 已经通过 TryParse 获取
                var quickUrl = await _vizCloudDevice.QuickGetRecordPlaybackUrlAsync(vizCloudDevice.Value.Device_No, startTime, endTime, passwayNo, vizCloudDevice.Value.Device_VideoChannelNo);
                if (!string.IsNullOrEmpty(quickUrl))
                {
                    ViewBag.VideoUrl = quickUrl;
                    isSuccess = true;
                }
            }
            if (!isSuccess)
            {
                ViewBag.VideoUrl = videoUrl;
            }
            return View();
        }

        /// <summary>
        /// 获取YM01视频回放地址（实时调用API）
        /// </summary>
        /// <param name="identifierUrl">YM01回放标识URL</param>
        /// <param name="passwayNo">车道编号</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetVizCloudPlaybackUrl(string identifierUrl, string passwayNo)
        {
            try
            {
                // 记录调试信息
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", $"开始处理请求，标识URL：{identifierUrl}，车道编号：{passwayNo}");

                // 添加权限检查
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", "权限检查失败");
                    return Json(new { success = false, message = "无权限" });
                }

                // 参数验证
                if (string.IsNullOrWhiteSpace(identifierUrl))
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", "标识URL为空");
                    return Json(new { success = false, message = "标识URL不能为空" });
                }

                // 解析标识URL
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", $"开始解析标识URL：{identifierUrl}");
                var parameters = TcpConnPools.SceneVideo.VziCloud.VziCloudService.ParsePlaybackIdentifierUrl(identifierUrl);
                if (parameters == null)
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", "标识URL解析失败");
                    return Json(new { success = false, message = "无效的标识URL格式" });
                }

                // 记录解析结果
                var paramInfo = string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"));
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", $"URL解析成功，参数：{paramInfo}");

                // 提取参数
                var sn = parameters["sn"];
                var channel = parameters["channel"];
                var startTime = long.Parse(parameters["start"]);
                var endTime = long.Parse(parameters["end"]);
                var eventId = parameters["event"];

                // 验证时间范围合理性
                var startDateTime = DateTimeOffset.FromUnixTimeSeconds(startTime);
                var endDateTime = DateTimeOffset.FromUnixTimeSeconds(endTime);
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址",
                    $"时间范围：{startDateTime:yyyy-MM-dd HH:mm:ss} 到 {endDateTime:yyyy-MM-dd HH:mm:ss}");

                // 获取设备配置信息
                var deviceConfig = AppBasicCache.GetSentryDeviceLinking.FirstOrDefault(m =>
                    m.Value.Device_PasswayNo.Contains(passwayNo) && m.Value.Drive_Code == "10123");

                if (deviceConfig.Value == null)
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", "未找到设备配置信息");
                    return Json(new { success = false, message = "未找到设备配置信息" });
                }

                // 从设备配置获取所需信息
                var username = deviceConfig.Value.Device_Account;
                var password = deviceConfig.Value.Device_Pwd;
                var appKey = string.IsNullOrWhiteSpace(deviceConfig.Value.Device_AppKey)
                    ? TcpConnPools.SceneVideo.VziCloud.VziCloudService.GetDefaultAccessKeyId()
                    : deviceConfig.Value.Device_AppKey;
                var secret = string.IsNullOrWhiteSpace(deviceConfig.Value.Device_Secret)
                    ? TcpConnPools.SceneVideo.VziCloud.VziCloudService.GetDefaultAccessKeySecret()
                    : deviceConfig.Value.Device_Secret;

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址",
                    $"从设备配置安全获取API密钥，用户名：{username}");

                // 调用YM01API获取真实播放地址（包含设备绑定检查）
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址",
                    $"开始调用YM01API，设备：{sn}，通道：{channel}，事件ID：{eventId}");

                using var _vizCloudDevice = new TcpConnPools.SceneVideo.VziCloud.VziCloudService(
                    accessKeyId: appKey,
                    accessKeySecret: secret);

                var quickUrl = await _vizCloudDevice.QuickGetRecordPlaybackUrlAsync(sn, startTime, endTime, eventId, channel, username, password);

                if (!string.IsNullOrEmpty(quickUrl))
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", $"API调用成功，获取到播放地址：{quickUrl}");
                    return Json(new { success = true, data = quickUrl });
                }
                else
                {
                    BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址", "API调用返回空地址");
                    return Json(new { success = false, message = "获取视频播放地址失败，可能是时间段内无录像数据" });
                }
            }
            catch (Exception ex)
            {
                // 记录详细错误日志
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, "获取YM01视频回放地址",
                    $"获取失败：{ex.Message}，堆栈：{ex.StackTrace}，标识URL：{identifierUrl}");
                return Json(new { success = false, message = $"获取视频播放地址失败：{ex.Message}" });
            }
        }

        /// <summary>
        /// 测试YM01API调用（调试用）
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> TestVizCloudApi(string sn, string appKey, string secret, string channel, long startTime, long endTime, string eventId, string username = null, string password = null)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                {
                    return Json(new { success = false, message = "无权限" });
                }

                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API",
                    $"参数：sn={sn}, channel={channel}, startTime={startTime}, endTime={endTime}, eventId={eventId}, username={username}");

                using var _vizCloudDevice = new TcpConnPools.SceneVideo.VziCloud.VziCloudService(
                    accessKeyId: appKey, accessKeySecret: secret);

                // 测试设备信息获取
                var deviceInfo = await _vizCloudDevice.GetDeviceInfoAsync(sn);
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API",
                    $"设备信息：{(deviceInfo != null ? "存在" : "不存在")}");

                // 如果设备不存在且提供了绑定凭据，尝试绑定
                if (deviceInfo == null && !string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API", "设备未绑定，尝试自动绑定");
                    var bindResult = await _vizCloudDevice.QuickBindDeviceAsync(sn, $"测试设备_{sn}", username, password);
                    BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API",
                        $"绑定结果：{(bindResult.Item1 ? "成功" : "失败")} - {bindResult.Item2}");
                }

                // 测试通道列表获取
                var channels = await _vizCloudDevice.GetChannelListAsync(sn);
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API",
                    $"通道数量：{channels?.Items?.Count ?? 0}");

                // 测试录像回放地址获取（包含设备绑定检查）
                var playbackUrl = await _vizCloudDevice.QuickGetRecordPlaybackUrlAsync(sn, startTime, endTime, eventId, channel, username, password);
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API",
                    $"回放地址：{playbackUrl ?? "空"}");

                return Json(new
                {
                    success = true,
                    deviceExists = deviceInfo != null,
                    channelCount = channels?.Items?.Count ?? 0,
                    playbackUrl = playbackUrl,
                    channels = channels?.Items?.Select(c => new { c.Id, c.Name })
                });
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试YM01API", $"测试失败：{ex.Message}");
                return Json(new { success = false, error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// 测试标识URL解析（调试用）
        /// </summary>
        [HttpPost]
        public IActionResult TestUrlParsing(string identifierUrl)
        {
            try
            {
                if (!Powermanage.PowerCheck("ControlEvent", PowerEnum.Search.ToString(), adminSession: lgAdmins))
                {
                    return Json(new { success = false, message = "无权限" });
                }

                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试URL解析", $"输入URL：{identifierUrl}");

                var parameters = TcpConnPools.SceneVideo.VziCloud.VziCloudService.ParsePlaybackIdentifierUrl(identifierUrl);

                if (parameters != null)
                {
                    BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试URL解析", "解析成功");
                    return Json(new { success = true, parameters = parameters });
                }
                else
                {
                    BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试URL解析", "解析失败");
                    return Json(new { success = false, message = "URL解析失败" });
                }
            }
            catch (Exception ex)
            {
                BLL.SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "测试URL解析", $"测试失败：{ex.Message}");
                return Json(new { success = false, error = ex.Message });
            }
        }
    }
}