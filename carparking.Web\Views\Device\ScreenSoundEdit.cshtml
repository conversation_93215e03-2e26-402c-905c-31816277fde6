﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>显示屏</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 10px; }
        .divInLine { display: inline-block; }
        .divInLine input { max-height: 30px; }
        .divInLine input.normal { max-width: 100px; max-height: 38px !important; text-align: center; padding-left: 3px; }
        .divInLine button { max-height: 32px !important; line-height: 30px; padding: 0px 10px; margin-bottom: 5px; }
        .divInLine label { height: 15px; padding: 0; }
        .layui-laydate-content > .layui-laydate-list { padding-bottom: 0px; overflow: hidden; }
        .layui-laydate-content > .layui-laydate-list > li { width: 50% }
        .layui-card { margin-bottom: 50px; height: 100%; }
        html, body { height: 100%; }
        .layui-layout-body, .layui-tab-item, .layui-layout, .layui-card, .layui-card-body { height: 90% !important; }
        .layui-layout-admin .layui-footer { width: 100%; left: 10px; }
        .layui-layout-admin .layui-footer { position: relative; }
        .layui-form-mid { width: 100%; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div>
            <div>&nbsp;</div>
            <div style="overflow:hidden;height:0;">
                <!--防止浏览器保存密码后自动填充-->
                <input type="password" />
                <input type="text" />
                <input type="text" name="email" />
            </div>
        </div>
        <div class="layui-card">
            <div id="verifyCheck" class="layui-form layui-card-body">
                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">设备名称</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="text" class="layui-input" maxlength="22" id="DisplayScreen_Name" name="DisplayScreen_Name" placeholder="" />
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">音量时间段内级别</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="number" class="layui-input v-number v-min v-max v-null" min="1" max="7" id="DisplayScreen_VolumeWithin" name="DisplayScreen_VolumeWithin" value="6" placeholder="设置时间段内音量级别" />
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                    <div class="layui-col-sm2 edit-label">音量时间段外级别</div>
                    <div class="layui-col-sm3 edit-ipt-ban">
                        <input type="number" class="layui-input v-number v-min v-max v-null" min="1" max="7" id="DisplayScreen_VolumeOther" name="DisplayScreen_VolumeOther" value="3" placeholder="其它时间段音量级别" />
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-sm2 edit-label">音量使用时段</div>
                    <div class="layui-col-sm3 edit-ipt-ban">

                        <div class="divInLine"><input type="number" class="layui-input required lan-error normal v-number v-min v-max v-null" min="0" max="23" autocomplete="off" id="DisplayScreen_VolumeOpenTime" name="DisplayScreen_VolumeOpenTime" value="7" /></div>
                        <div class="divInLine"><label>-</label></div>
                        <div class="divInLine"><input type="number" class="layui-input required lan-error normal v-number  v-min v-max v-null" min="0" max="23" autocomplete="off" id="DisplayScreen_VolumeCloseTime" name="DisplayScreen_VolumeCloseTime" value="19" /></div>
                    </div>
                    <div class="layui-col-xs1 red-mark"></div>
                </div>

                <div class="hr-line-dashed" style="clear:both;"></div>
                <div class="layui-row">
                    <div class="layui-col-xs3 edit-label">&nbsp;</div>
                    <div class="layui-col-xs7 edit-ipt-ban">
                        <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>下发设备</t></button>
                        <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}" data-sentryHostNo="${Passway_SentryHostNo}">${Passway_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>

        myVerify.init();
        var xmSelect = null;
        var laydate = null;
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['table', 'element', 'form', 'xmSelect', 'laydate'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            laydate = layui.laydate;
            table = layui.table;
            pager.init();
        });

    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramDeviceNo = parent.pager.SelDeviceNoes;  //decodeURIComponent($.getUrlParam("Device_No"));
        var paramDeviceName = parent.pager.SelDeviceNames;// decodeURIComponent($.getUrlParam("Device_Name"));
        var index = parent.index;

        $("#DisplayScreen_Name").val(paramDeviceName);

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {
                layui.form.on("select", function (data) {
                    if (data.elem.id == "DisplayScreen_DisplayType") {
                        var no = data.value;
                        if (no == '1') {
                            $(".DisplayType0").removeClass("layui-hide");
                        }
                        else {
                            $(".DisplayType0").addClass("layui-hide");
                        }
                    }
                })

                layui.form.render("select");
            },
            //数据绑定
            bindData: function () {
                $("#DisplayScreen_Name").attr("disabled", true);
                if (paramAct == "Update") {
                    $("#DisplayScreen_PasswayNo").attr("disabled", true);
                    $.post("GetDeviceDetail", { DisplayScreen_Key: paramDisplayScreenKey }, function (json) {
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data.model, function (data) { });
                            if (json.data.model.DisplayScreen_DisplayType == '1') {
                                $(".DisplayType0").removeClass("layui-hide");
                            }
                            else {
                                $(".DisplayType0").addClass("layui-hide");
                            }
                            layui.form.render("select");
                        } else {
                            console.log(json.msg);
                        }
                    }, "json");

                }
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.DisplayScreen_AdvertText1 = $("#DisplayScreen_AdvertText1").val();
                        data.DisplayScreen_AdvertText2 = $("#DisplayScreen_AdvertText2").val();
                        data.DisplayScreen_PasswayNo = $("#DisplayScreen_PasswayNo").val();
                        return data;
                    });
                    param.DisplayScreen_DeviceNo = paramDeviceNo;
                    $("#Save").attr("disabled", true);
                    $.getJSON("/Device/SetScreenDevice", { jsonModel: JSON.stringify(param), act: "1" }, function (json) {
                        if (json.success) {
                            layer.msg("已下发", { icon: 1, time: 1500 }, function () {
                                //window.parent.parent.pager.bindData(1);
                                $("#Save").removeAttr("disabled");
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });
                });

            },
        };

    </script>
</body>
</html>
