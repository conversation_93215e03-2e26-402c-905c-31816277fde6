﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>开闸放行记录</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>开闸放行记录</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                           
                            <div class="layui-inline">
                                <input class="layui-input " name="AbnorOrder_CarNo" id="AbnorOrder_CarNo" autocomplete="off" placeholder="车牌号" />
                            </div>
                          
                            <div class="layui-inline">
                                <input class="layui-input " name="AbnorOrder_Time0" id="AbnorOrder_Time0" autocomplete="off" placeholder="开闸时间起" value='@DateTime.Now.ToString("yyyy-MM-dd 00:00:00")'/>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="AbnorOrder_Time1" id="AbnorOrder_Time1" autocomplete="off" placeholder="开闸时间止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                        <div class="layui-row search-more layui-hide">
                            <div class="layui-inline">
                                <select class="layui-input" lay-search name="AbnorOrder_PasswayNo" id="AbnorOrder_PasswayNo" placeholder="车道名称">
                                    <option value="">车道名称</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="AbnorOrder_CardNo" name="AbnorOrder_CardNo" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-input" lay-search name="AbnorOrder_Type" id="AbnorOrder_Type" placeholder="开闸类型">
                                    <option value="">开闸类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select class="layui-input" lay-search name="AbnorOrder_OpenReason" id="AbnorOrder_OpenReason" placeholder="开闸原因">
                                    <option value="">开闸原因</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                 {{# if(Power.AbnorOrder.Export){}}<button class="layui-btn layui-btn-sm" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>{{# } }}
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>
        topBar.init();

        s_carno_picker.init("AbnorOrder_CarNo", function (carno, text) {
            $("#AbnorOrder_CarNo").val(text.join(""))
        }, "web");
        var Power = window.parent.global.formPower;
        var comtable = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'AbnorOrder_No', title: '记录编号', hide: true }
                , { field: 'AbnorOrder_Time', title: '开闸时间' }
                , {
                    field: 'AbnorOrder_Type', title: '开闸类型', templet: function (d) {
                        switch (d.AbnorOrder_Type) {
                            case 6100: return tempBar(0, "无入场记录");
                            case 6200: return tempBar(1, "人工放行");
                            case 6300: return tempBar(2, "免费放行");
                            case 6400: return tempBar(3, "改价收费");
                            default: return "";
                        }                        
                    }
                }
                , {
                    field: 'AbnorOrder_OpenReason', title: '开闸原因', templet: function (d) {
                        switch (d.AbnorOrder_OpenReason) {
                            case 6101: return tempBar(5, "临停自动放行");
                            case 6102: return tempBar(5, "临停确认放行");
                            case 6103: return tempBar(5, "月临车自动放行");
                            case 6104: return tempBar(5, "月临车人工放行");
                            case 6201: return tempBar(5, "人工开闸");
                            case 6202: return tempBar(5, "人工关闸");
                            case 6203: return tempBar(5, "平台车道开闸");
                            case 6204: return tempBar(5, "平台车道关闸");
                            case 6205: return tempBar(5, "APP车道开闸");
                            case 6206: return tempBar(5, "APP车道关闸");
                            case 6207: return tempBar(5, "公众号车道开闸");
                            case 6208: return tempBar(5, "公众号车道关闸");
                            case 6209: return tempBar(5, "值班中心开闸");
                            case 6210: return tempBar(5, "值班中心关闸");
                            case 6211: return tempBar(5, "道闸常开");
                            case 6212: return tempBar(5, "云值班开闸");
                            case 6213: return tempBar(5, "云值班关闸");
                            case 6214: return tempBar(5, "物业电话机开闸");
                            case 6215: return tempBar(5, "物业电话机关闸");
                            case 6301: return tempBar(5, "软件减免优惠");
                            case 6302: return tempBar(5, "线下优惠券减免");
                            case 6303: return tempBar(5, "平台优惠券减免");
                            case 6304: return tempBar(5, "充电优惠券减免");
                            case 6305: return tempBar(5, "商家车辆减免");
                            case 6401: return tempBar(5, "值班中心改价");
                            case 6402: return tempBar(5, "软件改价");
                            case 6403: return tempBar(5, "APP改价");
                            case 6404: return tempBar(5, "值班室改价");
                            default: return "";
                        }
                    }
                }
                , {
                    field: 'AbnorOrder_PasswayName', title: '车道名称', templet: function (d) {
                        return (d.AbnorOrder_Gate == 0 ? d.AbnorOrder_OutGateName : d.AbnorOrder_EntGateName) || "";
                    }
                }
                , { field: 'AbnorOrder_Money', title: '应收金额', totalRow: true }
                , { field: 'AbnorOrder_TotalAmount', title: '收费金额', totalRow: true }
                , { field: 'AbnorOrder_CarNo', title: '车牌号' }
                , { field: 'AbnorOrder_OrderNo', title: '订单号' }
                , { field: 'AbnorOrder_CardName', title: '车牌类型' }
                , { field: 'AbnorOrder_PasswayNo', title: '车道编码', hide: true  }
                , { field: 'AbnorOrder_EnterTime', title: '入场时间', hide: true }
                , { field: 'AbnorOrder_EntGateName', title: '入口', hide: true }
                , { field: 'AbnorOrder_EntOperatorName', title: '入口操作员' }
                , { field: 'AbnorOrder_EntImgPath', title: '入场图片', templet: function (d) { return tempImg(d.AbnorOrder_EntImgPath); } }
                , { field: 'AbnorOrder_OutTime', title: '出场时间', hide: true }
                , { field: 'AbnorOrder_OutGateName', title: '出口', hide: true }
                , { field: 'AbnorOrder_OutOperatorName', title: '出口操作员', hide: true }
                , { field: 'AbnorOrder_OutImgPath', title: '出场图片', templet: function (d) { return tempImg(d.AbnorOrder_OutImgPath); } }
                , { field: 'AbnorOrder_Remark', title: '备注' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/AbnorOrder/GetAbnorOrderNList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cellMinWidth: 90
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });

                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    var field = pager.sortField == null ? "" : pager.sortField;
                                    var order = pager.orderField == null ? "" : pager.orderField;
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/AbnorOrder/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&field=" + field + "&order=" + order + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                };
            });

            //排序
            table.on('sort(com-table-base)', function (obj) {
                if (obj.type == null) obj.field = null;
                pager.sortField = obj.field;
                pager.orderField = obj.type;
                pager.bindData(1);
            });

            tb_row_checkbox(table)
        });
    </script>
    <script>
        var pager = {
            sortField: null,
            orderField: null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["AbnorOrder_Time0", "AbnorOrder_Time1"], { type: 'datetime', range: true });
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#AbnorOrder_CardNo").append(option)
                        });
                    }
                }, "json");
                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                            $("#AbnorOrder_PasswayNo").append(option);
                        })
                    }
                }, "json");

                $.post("SltOpenType", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.DictionaryValue + '">' + item.DictionaryText + '</option>';
                            $("#AbnorOrder_Type").append(option);
                        })
                    }
                }, "json");

                $.post("SltOpenReason", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.DictionaryValue + '">' + item.DictionaryText + '</option>';
                            $("#AbnorOrder_OpenReason").append(option);
                        })
                    }
                }, "json");

                layui.form.render('select');
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/AbnorOrder/GetAbnorOrderNList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
    </script>
</body>
</html>
