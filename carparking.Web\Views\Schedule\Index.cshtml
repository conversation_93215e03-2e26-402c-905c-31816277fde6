﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>任务管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
        .desc { display: block; font-size: 14px; text-align: left; width: calc(100% - 1px); margin-left: 1px;margin-top:5px; }
        li { display: inline-block; margin-right: 10px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>系统管理</cite></a>
                <a><cite>任务管理</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <blockquote class="layui-elem-quote">该模块主要用于展示“系统管理”-->“系统设置”-->“数据清理”启用的清理任务，系统将按照用户预设的时间周期（如一个月、三个月、半年、一年）自动进行历史数据的定期清理。每一次清理操作会被系统视为一次“清理任务”，在执行过程中生成相应的任务记录。</blockquote>
                    </div>
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input" name="Schedule_Name" id="Schedule_Name" autocomplete="off" placeholder="任务名称" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="Schedule_Content" id="Schedule_Content" autocomplete="off" placeholder="任务内容" value="" />
                            </div>
                            <div class="layui-inline">  
                                <select data-placeholder="任务状态" class="form-control chosen-select " id="Schedule_Status" name="Schedule_Status" lay-search>
                                    <option value="">任务状态</option>
                                    <option value="0">未开始</option>
                                    <option value="1">已完成</option>
                                    <option value="2">执行中</option>
                                    <option value="3">执行失败</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                            <div class="layui-row search-more layui-hide">
                                <div class="layui-inline">
                                    <select id="Schedule_Type" name="Schedule_Type" lay-search>
                                        <option value="">任务类型</option>
                                        <option value="1">入场记录</option>
                                        <option value="2">出场记录</option>
                                        <option value="3">缴费记录</option>
                                        <option value="4">缴费明细</option>
                                        <option value="5">识别记录</option>
                                        <option value="6">优惠券记录</option>
                                        <option value="7">开闸记录</option>
                                        <option value="8">交班记录</option>
                                        <option value="9">倒车记录</option>
                                        <option value="10">停车详情</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="Schedule_StartTime" id="Schedule_StartTime" autocomplete="off" placeholder="开始时间" value="" />
                                </div>
                                <div class="layui-inline">
                                    <input class="layui-input" name="Schedule_EndTime" id="Schedule_EndTime" autocomplete="off" placeholder="结束时间" value="" />
                                </div>
                            </div>
                        </div>
                        
                    </div>
                 
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?20240517" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?20220830" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        topBar.init();

        layui.use(['table', 'form'], function () {

            var table = layui.table;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'checkbox' }
                , { field: 'Schedule_Name', title: '任务名称', width: 120 }
                , { field: 'Schedule_Content', title: '任务内容', width: 400 }
                , {
                    field: 'Schedule_Status', title: '任务状态', width: 100, templet: function (d) {
                        if (d.Schedule_Status == 0) return tempBar(5, "未开始");
                        else if (d.Schedule_Status == 1) return tempBar(1, "已完成");
                        else if (d.Schedule_Status == 2) return tempBar(2, "执行中");
                        else return tempBar(3, "执行失败");
                    }
                }
                , {
                    field: 'Schedule_Type', title: '任务类型', width: 100, templet: function (d) {
                        if (d.Schedule_Type == 1) return tempBar(1, "入场记录");
                        else if (d.Schedule_Type == 2) return tempBar(1, "出场记录");
                        else if (d.Schedule_Type == 3) return tempBar(1, "缴费记录");
                        else if (d.Schedule_Type == 4) return tempBar(1, "缴费明细");
                        else if (d.Schedule_Type == 5) return tempBar(1, "识别记录");
                        else if (d.Schedule_Type == 6) return tempBar(1, "优惠券记录");
                        else if (d.Schedule_Type == 7) return tempBar(1, "开闸记录");
                        else if (d.Schedule_Type == 8) return tempBar(1, "交班记录");
                        else if (d.Schedule_Type == 9) return tempBar(1, "倒车记录");
                        else if (d.Schedule_Type == 10) return tempBar(1, "停车详情");
                    }
                }
                , { field: 'Schedule_BeginTime', title: '开始时间', width: 170 }
                , { field: 'Schedule_EndTime', title: '结束时间', width: 170 }
                , { field: 'Schedule_ExecNum', title: '清理数目', width: 100 }
                , { field: 'Schedule_AddTime', title: '添加时间' }  
          
            ]];
            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: 'GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100, 1000]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();

                switch (obj.event) {
                    case 'Send':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].Schedule_ID);
                        }
                        layer.open({
                            id: 2,
                            type: 0,
                            title: false,
                            btn: ["确定", "取消"],
                            content: "确定同步该名单到所有连接相机吗?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Send", { Schedule_IDArray: JSON.stringify(NoArray) }, function (json) {
                                    if (json.success) {

                                        layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    } else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var NoArray = [];
                        for (var i = 0; i < data.length; i++) {
                            NoArray.push(data[i].Schedule_ID);
                        }

                        layer.open({
                            id: 2,
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: "删除当前名单状态记录，不会影响相机名单，确定删除?",
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { NoArray: JSON.stringify(NoArray) }, function (json) {
                                    if (json.success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.msg, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                    default: break;
                }

            });

            tb_row_checkbox();

            pager.init();
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                _DATE.bind(layui.laydate, ["Schedule_StartTime", "Schedule_EndTime"], { type: 'datetime', range: true });
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: 'GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            },
        }
    </script>
</body>
</html>
