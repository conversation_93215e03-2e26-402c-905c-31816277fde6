﻿@using carparking.BLL.Cache
@using carparking.Common
@using carparking.Config
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>@(ViewBag.SysConfig_DIYName ?? "停车场管理系统")</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="~/Static/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/Static/admin/layui/css/layui.css" media="all">
    <style>
        /* 防止主题切换闪烁的样式 */
        html.theme-dark-blue { background-color: #0f2b5b; }
        html.theme-dark-gray { background-color: #2c3e50; }</style>
    <script>
        // 在页面渲染前应用主题，防止闪烁
        (function () {
            var savedTheme = localStorage.getItem('theme');
            console.log('页面初始化时保存的主题:', savedTheme);

            // 如果没有保存的主题或者不是已知主题，则使用深蓝色调作为默认主题
            if (!savedTheme || (savedTheme !== 'dark-blue' && savedTheme !== 'dark-gray')) {
                savedTheme = 'dark-blue';
                localStorage.setItem('theme', savedTheme);
                console.log('设置默认主题为深蓝色');
            }

            // 应用主题到HTML元素
            document.documentElement.className = 'theme-' + savedTheme;
            console.log('初始化时应用到HTML:', document.documentElement.className);

            // 在DOMContentLoaded事件中应用到body，因为此时body元素可能还未加载
            document.addEventListener('DOMContentLoaded', function () {
                try {
                    // 确保body元素有正确的主题类
                    document.body.className = document.body.className || '';
                    document.body.className = document.body.className.replace(/theme-dark-blue/g, '').replace(/theme-dark-gray/g, '').trim();

                    if (document.body.className) {
                        document.body.className += ' theme-' + savedTheme;
                    } else {
                        document.body.className = 'theme-' + savedTheme;
                    }

                    console.log('DOMContentLoaded后应用主题:', savedTheme);
                    console.log('HTML类:', document.documentElement.className);
                    console.log('BODY类:', document.body.className);

                    // 强制重新应用样式
                    document.body.style.display = 'none';
                    setTimeout(function () {
                        document.body.style.display = '';
                    }, 5);
                } catch (e) {
                    console.error('应用主题时出错:', e);
                }
            });
        })();
    </script>
    <style>
        /* 基础样式 */
        html, body { height: 100%; margin: 0; padding: 0; /* background: linear-gradient(135deg, #2c3e50 0%, #1a2634 100%); */ font-family: "Microsoft YaHei", sans-serif; overflow: hidden; }

        /* 应用容器 */
        .app-container { height: 100%; display: flex; flex-direction: column; background: rgba(44, 62, 80, 0.9); box-shadow: 0 0 20px rgba(0, 0, 0, 0.15); }

        /* 头部样式 */
        .header { background: rgba(44, 62, 80, 0.95); padding: 10px 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); display: flex; justify-content: space-between; align-items: center; height: 60px; border-bottom: 1px solid rgba(255, 255, 255, 0.15); }

        .header-title { font-size: 20px; color: #fff; font-weight: 600; background: linear-gradient(45deg, #1890ff, #096dd9); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }

        .header-right { display: flex; align-items: center; gap: 20px; }

        /* 主要内容区域 */
        .main-content { flex: 1; padding: 20px; overflow: hidden; background: transparent; display: flex; gap: 20px; height: calc(100vh - 100px); margin-bottom: 20px; /* 添加底部间距 */ }

        /* 左侧菜单区域 */
        .menu-section { flex: 1; overflow-y: auto; overflow-x: hidden; padding-right: 20px; }

        .menu-section::-webkit-scrollbar { width: 6px; }

        .menu-section::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.05); border-radius: 3px; }

        .menu-section::-webkit-scrollbar-thumb { background: rgba(255, 255, 255, 0.2); border-radius: 3px; }

        .menu-section::-webkit-scrollbar-thumb:hover { background: rgba(255, 255, 255, 0.3); }

        /* 右侧信息栏 */
        .info-sidebar { width: 300px; background: rgba(44, 62, 80, 0.85); border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); border: 1px solid rgba(255, 255, 255, 0.15); display: flex; flex-direction: column; gap: 20px; overflow-y: auto; position: sticky; top: 20px; margin-left: auto; }

        /* 自定义滚动条样式 */
        .info-sidebar::-webkit-scrollbar { width: 6px; }

        .info-sidebar::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.05); border-radius: 3px; }

        .info-sidebar::-webkit-scrollbar-thumb { background: rgba(255, 255, 255, 0.2); border-radius: 3px; }

        .info-sidebar::-webkit-scrollbar-thumb:hover { background: rgba(255, 255, 255, 0.3); }

        /* 信息卡片样式 */
        .info-card { background: rgba(255, 255, 255, 0.08); border-radius: 6px; padding: 16px; border: 1px solid rgba(255, 255, 255, 0.15); flex-shrink: 0; /* 防止卡片被压缩 */ }

        .info-card-title { font-size: 16px; color: #fff; margin-bottom: 12px; font-weight: 500; display: flex; align-items: center; gap: 8px; }

        .info-card-title:before { content: ''; display: inline-block; width: 4px; height: 16px; background: #1890ff; border-radius: 2px; }

        .info-item { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid rgba(255, 255, 255, 0.1); }

        .info-item:last-child { border-bottom: none; }

        .info-label { color: rgba(255, 255, 255, 0.7); font-size: 14px; }

        .info-value { color: #fff; font-size: 14px; font-weight: 500; }

        /* 系统状态样式 */
        .system-status { display: flex; flex-direction: column; gap: 12px; }

        .status-item { display: flex; align-items: center; gap: 8px; padding: 8px; background: rgba(255, 255, 255, 0.05); border-radius: 4px; }

        .status-dot { width: 8px; height: 8px; border-radius: 50%; }

        .status-dot.online { background: #52c41a; }

        .status-dot.offline { background: #ff4d4f; }

        .status-text { color: #fff; font-size: 14px; }

        /* 菜单网格布局升级 */
        .menu-grid { display: flex; flex-direction: column; gap: 14px; padding-right: 10px; padding-top: 8px; margin: 0; box-sizing: border-box; overflow-y: auto; }
        /* 菜单网格滚动条样式 */
        .menu-grid::-webkit-scrollbar { width: 6px; }

        .menu-grid::-webkit-scrollbar-track { background: rgba(255, 255, 255, 0.05); border-radius: 3px; }

        .menu-grid::-webkit-scrollbar-thumb { background: rgba(255, 255, 255, 0.2); border-radius: 3px; }

        .menu-grid::-webkit-scrollbar-thumb:hover { background: rgba(255, 255, 255, 0.3); }

        /* 菜单分类卡片升级 */
        .menu-category { background: rgba(44, 62, 80, 0.96); border-radius: 14px; padding: 5px 8px 5px 18px; box-shadow: 0 4px 20px rgba(24,144,255,0.08); border: none; transition: box-shadow 0.3s, background 0.3s; position: relative; }
        .menu-category:hover { box-shadow: 0 8px 28px rgba(24,144,255,0.18); background: rgba(44, 62, 80, 1); }

        /* 分组标题升级 */
        .category-title { font-size: 16px; color: #1890ff; margin-bottom: 12px; padding-bottom: 6px; font-weight: 700; display: flex; align-items: center; letter-spacing: 0.5px; border-bottom: 1px solid #1890ff; background: none; }
        .category-title i { font-size: 18px; margin-right: 8px; color: #40a9ff; }

        /* 菜单项卡片升级 */
        .menu-items { display: flex; flex-wrap: wrap; gap: 12px; width: 100%; }
        .menu-item { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 10px 6px 8px 6px; cursor: pointer; border-radius: 8px; background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%); min-width: 80px; min-height: 75px; box-shadow: 0 2px 6px rgba(24,144,255,0.04); border: none; transition: box-shadow 0.2s, background 0.2s, transform 0.2s; position: relative; }
        .menu-item:hover { background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%); box-shadow: 0 6px 20px rgba(24,144,255,0.18); transform: translateY(-3px) scale(1.04); }
        /* 支持图片图标 */
        .menu-item img { width: 28px; height: 28px; margin-bottom: 6px; transition: transform 0.3s; filter: drop-shadow(0 2px 4px rgba(24,144,255,0.10)); }
        .menu-item:hover img { transform: scale(1.12); }

        /* 支持Layui图标 */
        .menu-item .layui-icon { font-size: 24px; margin-bottom: 6px; transition: transform 0.3s; color: #1890ff; text-shadow: 0 2px 4px rgba(24,144,255,0.15); }
        .menu-item:hover .layui-icon { transform: scale(1.12); color: #40a9ff; }
        .menu-item span { font-size: 12px; color: #222; font-weight: 600; text-align: center; letter-spacing: 0.3px; white-space: nowrap; text-shadow: 0 1px 2px rgba(255,255,255,0.15); }
        .menu-item:hover span { color: #1890ff; }

        /* 底部样式 */
        .footer { background: rgba(44, 62, 80, 0.95); padding: 10px 20px; border-top: 1px solid rgba(255, 255, 255, 0.15); color: rgba(255, 255, 255, 0.7); font-size: 12px; position: relative; /* 确保底部始终在底部 */ z-index: 1; /* 确保底部显示在其他元素之上 */ }

        /* 用户信息样式 */
        .user-info { display: flex; align-items: center; gap: 12px; cursor: pointer; padding: 8px 16px; border-radius: 4px; transition: all 0.3s ease; }

        .user-info:hover { background: rgba(255, 255, 255, 0.12); }

        .user-avatar { width: 32px; height: 32px; border-radius: 50%; background: linear-gradient(45deg, #1890ff, #096dd9); color: #fff; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: 500; }

        /* 状态标签样式 */
        .status-badge { padding: 6px 12px; border-radius: 4px; font-size: 13px; font-weight: 500; color: #fff; display: flex; align-items: center; gap: 6px; background: rgba(255, 255, 255, 0.12); }

        .status-badge:before { content: ''; display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: currentColor; }

        .status-online { background: rgba(82, 196, 26, 0.15); color: #52c41a; }

        .status-offline { background: rgba(255, 77, 79, 0.15); color: #ff4d4f; }

        /* 子菜单弹出层样式 */
        .submenu-layer { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }

        .submenu-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(44, 62, 80, 0.95); padding: 24px; border-radius: 8px; width: 80%; max-width: 1000px; max-height: 80vh; overflow-y: auto; box-shadow: 0 4px 12px rgba(0,0,0,0.2); border: 1px solid rgba(255, 255, 255, 0.15); }

        .submenu-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; padding-bottom: 16px; border-bottom: 1px solid rgba(255, 255, 255, 0.15); }

        .submenu-title { font-size: 20px; color: #fff; font-weight: 500; }

        .submenu-close { cursor: pointer; font-size: 24px; color: rgba(255, 255, 255, 0.7); transition: color 0.3s ease; }

        .submenu-close:hover { color: #fff; }

        .submenu-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 16px; }

        /* 搜索框样式 */
        .search-box { display: flex; align-items: center; background: rgba(255, 255, 255, 0.1); border-radius: 4px; padding: 8px 16px; margin: 0 20px; width: 300px; }

        .search-box input { background: transparent; border: none; color: #fff; width: 100%; outline: none; }

        .search-box input::placeholder { color: rgba(255, 255, 255, 0.5); }

        /* 全功能模态框样式 */
        .all-functions-modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.35); z-index: 2000; }

        .modal-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 700px; max-width: 90vw; background: #f7fafd; border-radius: 18px; box-shadow: 0 8px 32px rgba(24, 144, 255, 0.18); padding: 0 0 32px 0; overflow: hidden; }

        .modal-header { background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%); color: #fff; padding: 24px 32px 16px 32px; display: flex; align-items: center; justify-content: space-between; }

        .modal-header h3 { margin: 0; font-size: 22px; font-weight: 600; letter-spacing: 1px; }

        .modal-close { font-size: 28px; cursor: pointer; color: #fff; transition: color 0.2s; }

        .modal-close:hover { color: #ff7875; }

        .modal-body { padding: 20px 24px 0 24px; display: flex; flex-wrap: wrap; gap: 14px; min-height: 260px; }

        .function-card { background: #fff; border-radius: 8px; box-shadow: 0 2px 6px rgba(24,144,255,0.06); width: 110px; height: 100px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; transition: box-shadow 0.2s, transform 0.2s; text-align: center; padding: 8px; }

        .function-card:hover { box-shadow: 0 4px 14px rgba(24,144,255,0.18); transform: translateY(-3px) scale(1.04); background: #e6f7ff; }

        /* 支持图片图标 */
        .function-card img { width: 32px; height: 32px; margin-bottom: 6px; }

        /* 支持Layui图标 */
        .function-card .layui-icon { font-size: 28px; margin-bottom: 6px; transition: transform 0.3s; color: #1890ff; }
        .function-card:hover .layui-icon { transform: scale(1.12); color: #40a9ff; }

        .function-card span { font-size: 13px; color: #1890ff; font-weight: 500; }

        /* 新增样式 */
        .more-functions-btn { display: flex; align-items: center; justify-content: center; width: 44px; height: 44px; border-radius: 50%; background: linear-gradient(135deg, #1890ff 0%, #0050b3 100%); cursor: pointer; margin-left: 16px; box-shadow: 0 2px 8px rgba(24,144,255,0.10); transition: background 0.2s, box-shadow 0.2s, transform 0.2s; position: relative; }
        .more-functions-btn:hover { background: linear-gradient(135deg, #40a9ff 0%, #096dd9 100%); box-shadow: 0 4px 16px rgba(24,144,255,0.18); transform: scale(1.08); }
        .more-functions-btn .layui-icon { font-size: 26px; color: #fff; }

        /* 主题切换按钮 */
        .theme-toggle-btn { display: flex; align-items: center; justify-content: center; width: 44px; height: 44px; border-radius: 50%; background: linear-gradient(135deg, #1890ff 0%, #0050b3 100%); cursor: pointer; margin-left: 16px; box-shadow: 0 2px 8px rgba(24,144,255,0.10); transition: background 0.2s, box-shadow 0.2s, transform 0.2s; position: relative; }
        .theme-toggle-btn:hover { background: linear-gradient(135deg, #40a9ff 0%, #096dd9 100%); box-shadow: 0 4px 16px rgba(24,144,255,0.18); transform: scale(1.08); }
        .theme-toggle-btn .layui-icon { font-size: 26px; color: #fff; }

        /* 顶部页签导航样式 - 使用layui页签 */
        .top-tabs {
            width: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: none; /* 移除底部边框 */
            display: flex;
            flex-direction: column;
        }
        /* 移除layui页签标题栏的边框 */
        .top-tabs .layui-tab {
            margin: 0;
            border: none;
        }
        .top-tabs .layui-tab-title {
            border: none !important;
        }
        /* 当显示页签内容时设置高度 */
        .top-tabs:not(.showing-home) {
            height: calc(100vh - 60px);
        }
        .top-tabs.active {
            display: flex !important;
        }

        /* 页签标题栏样式 */
        .top-tabs .tab-header {
            height: 40px;
            display: flex;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: none; /* 移除底部边框 */
        }

        /* 首页页签样式 - 更加大气 */
        .top-tabs .tab-home {
            height: 40px;
            line-height: 40px;
            padding: 0 20px;
            color: #fff;
            background: linear-gradient(135deg, #1890ff 0%, #0050b3 100%);
            cursor: pointer;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            white-space: nowrap;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }
        .top-tabs .tab-home:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #096dd9 100%);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
        }
        .top-tabs .tab-home:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
        }
        .top-tabs .tab-home .layui-icon {
            margin-right: 8px;
            font-size: 16px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* 页签列表样式 */
        .top-tabs .tab-list {
            flex: 1;
            display: flex;
            overflow-x: auto;
            overflow-y: hidden;
        }
        .top-tabs .tab-list::-webkit-scrollbar {
            height: 2px;
        }
        .top-tabs .tab-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }
        .top-tabs .tab-list::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 页签项样式 */
        .top-tabs .tab-item {
            height: 40px;
            line-height: 40px;
            padding: 0 15px;
            color: rgba(255, 255, 255, 0.8);
            background-color: transparent;
            cursor: pointer;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            white-space: nowrap;
            position: relative;
            user-select: none;
        }
        .top-tabs .tab-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        .top-tabs .tab-item.active {
            color: #fff;
            background: linear-gradient(135deg, #40a9ff 0%, #096dd9 100%);
        }
        .top-tabs .tab-item .layui-icon {
            margin-right: 5px;
            font-size: 14px;
        }
        .top-tabs .tab-item .tab-close {
            display: inline-block;
            width: 16px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            border-radius: 50%;
            margin-left: 12px;
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
        }
        .top-tabs .tab-item .tab-close:hover {
            background-color: #ff4d4f;
            color: #fff;
        }
        /* layui页签内容区域样式 */
        .top-tabs .layui-tab-content {
            flex: 1;
            position: relative;
            overflow: hidden;
            background-color: #fff;
            padding: 0;
            height: calc(100% - 40px); /* 减去标题栏高度 */
        }
        .top-tabs .layui-tab-content .layui-tab-item {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            height: 100%;
            width: 100%;
            display: none; /* 默认隐藏 */
        }
        .top-tabs .layui-tab-content .layui-tab-item.layui-show {
            display: block !important; /* 强制显示激活的Tab */
        }
        .top-tabs .layui-tab-content iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        /* 关闭按钮样式 */
        .top-tabs .layui-tab-close {
            font-size: 12px;
            height: 16px;
            line-height: 16px;
            width: 16px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: inline-block;
            text-align: center;
            margin-left: 12px;
            color: rgba(255, 255, 255, 0.6);
        }
        .top-tabs .layui-tab-close:hover {
            background-color: #ff4d4f;
            color: #fff;
        }

        /* 首页页签样式 - 更加大气 */
        .top-tabs .layui-tab-title li.layui-tab-home {
            padding: 0 20px;
            color: #fff;
            font-weight: 500;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        .top-tabs .layui-tab-title li.layui-tab-home:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #096dd9 100%);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
        }
        .top-tabs .layui-tab-title li.layui-tab-home:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
        }
        .top-tabs .layui-tab-title li.layui-tab-home .layui-icon {
            margin-right: 8px;
            font-size: 16px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }
        /* 隐藏首页页签的关闭按钮 */
        .top-tabs .layui-tab-title li[lay-id="tab-home"] .layui-tab-close { display: none; }

        /* 页签字体和图标设置为白色，激活时为蓝色 */
        .top-tabs .layui-tab-title li {
            color: #ffffff !important; /* 白色字体 */
            border: none !important; /* 去掉边框 */
        }
        .top-tabs .layui-tab-title li .layui-icon {
            color: #ffffff !important; /* 白色图标 */
        }
        .top-tabs .layui-tab-title li.layui-this {
            color: #1890ff !important; /* 蓝色字体 */
            font-weight: bold; /* 加粗激活的页签 */
            background-color: transparent !important; /* 透明背景 */
            border: none !important; /* 去掉边框 */
        }
        .top-tabs .layui-tab-title li.layui-this:after {
            display: none !important; /* 去掉底部边框线 */
        }
        .top-tabs .layui-tab-title li.layui-this .layui-icon {
            color: #1890ff !important; /* 蓝色图标 */
        }
        /* 首页页签保持白色 */
        .top-tabs .layui-tab-title li.layui-tab-home.layui-this {
            color: #ffffff !important; /* 白色字体 */
        }
        .top-tabs .layui-tab-title li.layui-tab-home.layui-this .layui-icon {
            color: #ffffff !important; /* 白色图标 */
        }

        /* 内容区域样式调整 */
        /* 移除这个样式，使用JavaScript控制显示/隐藏 */

        /* 主题样式集合 */
        /* 1. 深蓝色调主题 */
        html.theme-dark-blue, body.theme-dark-blue { background-color: #0f2b5b; }
        /* 添加背景色防止闪烁 */
        body.theme-dark-blue .app-container { background: linear-gradient(135deg, #0f2b5b 0%, #1a3a6c 100%); }
        body.theme-dark-blue .header { background: rgba(15, 43, 91, 0.95); }
        body.theme-dark-blue .info-sidebar { background: rgba(15, 43, 91, 0.85); }
        body.theme-dark-blue .menu-category { background: rgba(15, 43, 91, 0.96); }
        body.theme-dark-blue .menu-category:hover { background: rgba(15, 43, 91, 1); }
        body.theme-dark-blue .footer { background: rgba(15, 43, 91, 0.95); }
        body.theme-dark-blue .submenu-content { background: rgba(15, 43, 91, 0.95); }
        body.theme-dark-blue .category-title { color: #64b5f6; border-bottom-color: #64b5f6; }
        body.theme-dark-blue .category-title i { color: #64b5f6; }
        body.theme-dark-blue .info-card-title:before { background: #64b5f6; }
        body.theme-dark-blue .menu-item:hover span { color: #64b5f6; }
        body.theme-dark-blue .layui-tab-title .layui-this { color: #64b5f6; }

        /* 2. 暗灰色调主题 */
        html.theme-dark-gray, body.theme-dark-gray { background-color: #2c3e50; }
        /* 添加背景色防止闪烁 */
        body.theme-dark-gray .app-container { background: linear-gradient(135deg, #2c3e50 0%, #1a2634 100%); }
        body.theme-dark-gray .header { background: rgba(44, 62, 80, 0.95); }
        body.theme-dark-gray .info-sidebar { background: rgba(44, 62, 80, 0.85); }
        body.theme-dark-gray .menu-category { background: rgba(44, 62, 80, 0.96); }
        body.theme-dark-gray .menu-category:hover { background: rgba(44, 62, 80, 1); }
        body.theme-dark-gray .footer { background: rgba(44, 62, 80, 0.95); }
        body.theme-dark-gray .submenu-content { background: rgba(44, 62, 80, 0.95); }
        body.theme-dark-gray .category-title { color: #1890ff; border-bottom-color: #1890ff; }
        body.theme-dark-gray .category-title i { color: #40a9ff; }
        body.theme-dark-gray .info-card-title:before { background: #1890ff; }
        body.theme-dark-gray .menu-item:hover span { color: #1890ff; }
        body.theme-dark-gray .layui-tab-title .layui-this { color: #1890ff; }</style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <div class="header-title">@(ViewBag.SysConfig_DIYName ?? "停车场管理系统")</div>
            <div class="search-box">
                <input type="text" id="functionSearch" placeholder="搜索功能...">
                <i class="layui-icon layui-icon-search"></i>
            </div>
            <div class="header-right">
                <div class="status-badge status-online parkstatus"></div>
                <div class="user-info" id="userDropdown">
                    <div class="user-avatar">
                        @(ViewBag.lgAdmins != null ? ViewBag.lgAdmins.Admins_Account.Substring(0, 1).ToUpper() : "U")
                    </div>
                    <span>@Html.Raw(ViewBag.lgAdmins != null ? ViewBag.lgAdmins.Admins_Account : "")</span>
                </div>
                <!-- 美化后的更多功能按钮 -->
                <div class="theme-toggle-btn" onclick="toggleTheme()" title="切换主题">
                    <i class="layui-icon layui-icon-theme"></i>
                </div>
                <div class="more-functions-btn" onclick="openAllFunctions()" title="更多功能">
                    <i class="layui-icon layui-icon-more"></i>
                </div>
            </div>
        </div>

        <!-- 顶部页签导航 - 使用layui页签 -->
        <div class="top-tabs" style="display: none;">
            <div class="layui-tab" lay-filter="top-tabs" lay-allowclose="true">
                <ul class="layui-tab-title">
                    <!-- 首页页签 -->
                    <li class="layui-tab-home" lay-id="tab-home"><i class="layui-icon layui-icon-home"></i> 首页</li>
                    <!-- 其他页签将在这里动态添加 -->
                </ul>
                <div class="layui-tab-content">
                    <!-- iframe将在这里动态添加 -->
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="menu-section">
                <div class="menu-grid">
                    <!-- 汇总报表 -->
                    <div class="menu-category">
                        <div class="category-title">
                            <i class="layui-icon layui-icon-chart"></i>
                            <span>汇总报表</span>
                        </div>
                        <div class="menu-items">
                            <div class="menu-item" data-href="RptDay/Index">
                                <i class="layui-icon layui-icon-date"></i>
                                <span>车场日报表</span>
                            </div>
                            <div class="menu-item" data-href="RptMonth/Index">
                                <i class="layui-icon layui-icon-chart-screen"></i>
                                <span>车场月报表</span>
                            </div>
                            <div class="menu-item" data-href="RptTraffic/Index">
                                <i class="layui-icon layui-icon-chart"></i>
                                <span>车流量统计</span>
                            </div>
                            <div class="menu-item" data-href="RptTempPayment/Index">
                                <i class="layui-icon layui-icon-dollar"></i>
                                <span>临时车收费统计</span>
                            </div>
                            <div class="menu-item" data-href="RptCarMonth/Index">
                                <i class="layui-icon layui-icon-template-1"></i>
                                <span>月租车充值统计</span>
                            </div>
                        </div>
                    </div>

                    <!-- 记录查询 -->
                    <div class="menu-category">
                        <div class="category-title">
                            <i class="layui-icon layui-icon-form"></i>
                            <span>记录查询</span>
                        </div>
                        <div class="menu-items">
                            <div class="menu-item" data-href="InParkRecord/Index">
                                <i class="layui-icon layui-icon-log"></i>
                                <span>出入场记录</span>
                            </div>
                            <div class="menu-item" data-href="PayOrder/Index">
                                <i class="layui-icon layui-icon-rmb"></i>
                                <span>缴费记录</span>
                            </div>
                            <div class="menu-item" data-href="PayPart/Index">
                                <i class="layui-icon layui-icon-file"></i>
                                <span>缴费明细</span>
                            </div>
                            <div class="menu-item" data-href="CouponRecord/Index">
                                <i class="layui-icon layui-icon-gift"></i>
                                <span>优惠券记录</span>
                            </div>
                            <div class="menu-item" data-href="InParkCar/Index">
                                <i class="layui-icon layui-icon-tabs"></i>
                                <span>场内记录</span>
                            </div>
                            <div class="menu-item" data-href="CarRecog/Index">
                                <i class="layui-icon layui-icon-camera"></i>
                                <span>车牌识别记录</span>
                            </div>
                            <div class="menu-item" data-href="ControlEvent/Index">
                                <i class="layui-icon layui-icon-notice"></i>
                                <span>事件管理</span>
                            </div>
                            <div class="menu-item" data-href="CarUnbound/Index">
                                <i class="layui-icon layui-icon-delete"></i>
                                <span>车辆注销记录</span>
                            </div>
                        </div>
                    </div>

                    <!-- 车辆管理 -->
                    <div class="menu-category">
                        <div class="category-title">
                            <i class="layui-icon layui-icon-release"></i>
                            <span>车辆管理</span>
                        </div>
                        <div class="menu-items">
                            <div class="menu-item" data-href="Owner/Index">
                                <i class="layui-icon layui-icon-add-circle"></i>
                                <span>车辆登记</span>
                            </div>
                            <div class="menu-item" data-href="BlackList/Index">
                                <i class="layui-icon layui-icon-close-fill"></i>
                                <span>黑名单管理</span>
                            </div>
                            <div class="menu-item" data-href="Reserve/Index">
                                <i class="layui-icon layui-icon-user"></i>
                                <span>访客车辆</span>
                            </div>
                            <div class="menu-item" data-href="BusinessCar/Index">
                                <i class="layui-icon layui-icon-cart"></i>
                                <span>商家车辆</span>
                            </div>
                            <div class="menu-item" data-href="InParkCar/Index">
                                <i class="layui-icon layui-icon-list"></i>
                                <span>场内记录</span>
                            </div>
                        </div>
                    </div>

                    <!-- 车场管理 -->
                    <div class="menu-category">
                        <div class="category-title">
                            <i class="layui-icon layui-icon-app"></i>
                            <span>车场管理</span>
                        </div>
                        <div class="menu-items">
                            <div class="menu-item" data-href="Policy/Index">
                                <i class="layui-icon layui-icon-set"></i>
                                <span>车场设置</span>
                            </div>
                            <div class="menu-item" data-href="LaneMonitor/Index">
                                <i class="layui-icon layui-icon-video"></i>
                                <span>车道监控</span>
                            </div>
                            <div class="menu-item" data-href="BillingRule/Index">
                                <i class="layui-icon layui-icon-calculator"></i>
                                <span>计费规则</span>
                            </div>
                            <div class="menu-item" data-href="MonthRule/MonthCharge">
                                <i class="layui-icon layui-icon-diamond"></i>
                                <span>充值规则</span>
                            </div>
                            <div class="menu-item" data-href="AccessAuth/Index">
                                <i class="layui-icon layui-icon-vercode"></i>
                                <span>通行控制</span>
                            </div>
                            <div class="menu-item" data-href="SpecialCar/Index">
                                <i class="layui-icon layui-icon-flag"></i>
                                <span>特殊车牌</span>
                            </div>
                        </div>
                    </div>

                    <!-- 车场配置 -->
                    <div class="menu-category">
                        <div class="category-title">
                            <i class="layui-icon layui-icon-util"></i>
                            <span>车场配置</span>
                        </div>
                        <div class="menu-items">
                            <div class="menu-item" data-href="FastGuide/Index">
                                <i class="layui-icon layui-icon-console"></i>
                                <span>快速配置</span>
                            </div>
                            <div class="menu-item" data-href="SentryHost/Index">
                                <i class="layui-icon layui-icon-home"></i>
                                <span>岗亭管理</span>
                            </div>
                            <div class="menu-item" data-href="ParkArea/Index">
                                <i class="layui-icon layui-icon-layouts"></i>
                                <span>区域管理</span>
                            </div>
                            <div class="menu-item" data-href="Passway/Index">
                                <i class="layui-icon layui-icon-transfer"></i>
                                <span>车道管理</span>
                            </div>
                            <div class="menu-item" data-href="Device/Index">
                                <i class="layui-icon layui-icon-component"></i>
                                <span>设备管理</span>
                            </div>
                        </div>
                    </div>

                    <!-- 系统管理 -->
                    <div class="menu-category">
                        <div class="category-title">
                            <i class="layui-icon layui-icon-set"></i>
                            <span>系统管理</span>
                        </div>
                        <div class="menu-items">
                            <div class="menu-item" data-href="Admins/Index">
                                <i class="layui-icon layui-icon-username"></i>
                                <span>账号管理</span>
                            </div>
                            <div class="menu-item" data-href="PowerGroup/Index">
                                <i class="layui-icon layui-icon-auz"></i>
                                <span>权限管理</span>
                            </div>
                            <div class="menu-item" data-href="UserLogs/Index">
                                <i class="layui-icon layui-icon-read"></i>
                                <span>操作日志</span>
                            </div>
                            <div class="menu-item" data-href="SystemLogs/Index">
                                <i class="layui-icon layui-icon-file-b"></i>
                                <span>系统日志</span>
                            </div>
                            <div class="menu-item" data-href="SystemSetting/Index">
                                <i class="layui-icon layui-icon-set-sm"></i>
                                <span>系统设置</span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- 右侧信息栏 -->
            <div class="info-sidebar">
                <!-- 系统信息卡片 -->
                <div class="info-card">
                    <div class="info-card-title">系统信息</div>
                    <div class="info-item">
                        <span class="info-label">系统版本</span>
                        <span class="info-value">@Html.Raw("V" + (ViewBag.ApiVersion ?? "1.0"))@Html.Raw((ViewBag.ApiVersion_FB != null && ViewBag.ApiVersion_FB != "") ? "." + ViewBag.ApiVersion_FB : "")</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">系统名称</span>
                        <span class="info-value">@(ViewBag.SysConfig_DIYName ?? "停车场管理系统")</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">当前用户</span>
                        <span class="info-value">@Html.Raw(ViewBag.lgAdmins != null ? ViewBag.lgAdmins.Admins_Account : "")</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">登录时间</span>
                        <span class="info-value">@DateTime.Now.ToString("yyyy-MM-dd HH:mm")</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">IP地址</span>
                        <span class="info-value">*************</span>
                    </div>
                </div>

                <!-- 系统状态卡片 -->
                <div class="info-card">
                    <div class="info-card-title">系统状态</div>
                    <div class="system-status">
                        <div class="status-item">
                            <div class="status-dot online"></div>
                            <span class="status-text">云平台连接</span>
                        </div>
                        <div class="status-item">
                            <div class="status-dot online"></div>
                            <span class="status-text">数据库连接</span>
                        </div>
                        <div class="status-item">
                            <div class="status-dot online"></div>
                            <span class="status-text">服务运行</span>
                        </div>
                        <div class="status-item">
                            <div class="status-dot online"></div>
                            <span class="status-text">摄像头连接</span>
                        </div>
                        <div class="status-item">
                            <div class="status-dot online"></div>
                            <span class="status-text">道闸控制</span>
                        </div>
                    </div>
                </div>

                <!-- 停车场信息卡片 -->
                <div class="info-card">
                    <div class="info-card-title">停车场信息</div>
                    <div class="info-item">
                        <span class="info-label">总车位</span>
                        <span class="info-value">500</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">已使用</span>
                        <span class="info-value">320</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">剩余车位</span>
                        <span class="info-value">180</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">今日收入</span>
                        <span class="info-value">¥2,580.00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">今日车流量</span>
                        <span class="info-value">1,256</span>
                    </div>
                </div>
                <!-- 系统公告卡片 -->
                <div class="info-card">
                    <div class="info-card-title">系统公告</div>
                    <div class="info-item">
                        <span class="info-label">系统维护</span>
                        <span class="info-value">2024-03-15</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">版本更新</span>
                        <span class="info-value">2024-03-10</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">功能优化</span>
                        <span class="info-value">2024-03-05</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <div style="text-align: center;">
                <span>&copy; @Html.Raw("V" + (ViewBag.ApiVersion ?? "1.0"))@Html.Raw((ViewBag.ApiVersion_FB != null && ViewBag.ApiVersion_FB != "") ? "." + ViewBag.ApiVersion_FB : "")</span>
            </div>
        </div>
    </div>

    <!-- 子菜单弹出层模板 -->
    <div class="submenu-layer" id="submenuLayer">
        <div class="submenu-content">
            <div class="submenu-header">
                <div class="submenu-title"></div>
                <div class="submenu-close">&times;</div>
            </div>
            <div class="submenu-grid">
                <!-- 子菜单项将通过JavaScript动态添加 -->
            </div>
        </div>
    </div>

    <!-- 全部功能弹窗 -->
    <div class="all-functions-modal" id="allFunctionsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>全部功能</h3>
                <span class="modal-close" onclick="closeAllFunctions()">&times;</span>
            </div>
            <div class="modal-body" id="allFunctionsBody">
                <!-- 功能卡片通过JS动态填充 -->
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script src="~/Static/js/jquery.common.js"></script>
    <script>
        // 页面初始化
        $(function () {
            // 初始化Layui模块
            layui.use(['element'], function() {
                var element = layui.element;

                // 监听Tab切换事件
                element.on('tab(top-tabs)', function(data) {
                    // 处理Tab切换事件
                    console.log('切换到Tab:', data.index);

                    // 获取当前点击的Tab ID
                    var tabId = $(this).attr('lay-id') || $('.layui-tab-title .layui-this').attr('lay-id');
                    console.log('点击的Tab ID:', tabId);

                    if (tabId === 'tab-home' || $(data.elem).find('.layui-tab-home').hasClass('layui-this')) {
                        // 如果点击的是首页页签，显示首页内容
                        showHomeContent();
                    } else {
                        // 如果点击的是其他页签，确保显示页签内容
                        if ($('.top-tabs').hasClass('showing-home')) {
                            // 如果当前显示的是首页内容，隐藏首页内容
                            hideHomeContent();
                        }

                        // 强制更新激活状态
                        $('.layui-tab-title li').removeClass('layui-this');
                        $('.layui-tab-title li[lay-id="' + tabId + '"]').addClass('layui-this');

                        // 强制显示对应的Tab内容
                        $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                        $('.layui-tab-content .layui-tab-item').eq(data.index-1).addClass('layui-show').css('display', 'block');

                        // 确保页签内容区域显示
                        $('.layui-tab-content').show();

                        // 检查iframe是否需要重新加载
                        var iframe = $('.layui-tab-content .layui-tab-item').eq(data.index-1).find('iframe');
                        if (iframe.length > 0 && (!iframe.attr('src') || iframe.attr('src') === 'about:blank')) {
                            var href = $('.layui-tab-title li[lay-id="' + tabId + '"]').data('href');
                            if (href) {
                                console.log('重新加载iframe内容:', href);
                                iframe.attr('src', href);
                            }
                        }
                    }
                });

                // 添加自定义Tab切换处理函数，确保layui-show类被正确添加
                $(document).on('click', '.layui-tab-title li', function() {
                    var tabId = $(this).attr('lay-id');
                    var tabIndex = $(this).index();

                    console.log('自定义Tab点击处理 - ID:', tabId, '索引:', tabIndex);

                    if (tabId === 'tab-home') {
                        // 如果点击的是首页页签，显示首页内容
                        showHomeContent();
                    } else {
                        // 如果点击的是其他页签，确保显示页签内容
                        if ($('.top-tabs').hasClass('showing-home')) {
                            // 如果当前显示的是首页内容，隐藏首页内容
                            hideHomeContent();
                        }

                        // 延迟执行，确保在layui处理完Tab切换后执行
                        setTimeout(function() {
                            // 强制更新激活状态
                            $('.layui-tab-title li').removeClass('layui-this');
                            $('.layui-tab-title li[lay-id="' + tabId + '"]').addClass('layui-this');

                            // 强制显示对应的Tab内容
                            $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                            $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');

                            // 确保页签内容区域显示
                            $('.layui-tab-content').show();

                            console.log('强制更新Tab显示状态 - ID:', tabId, '索引:', tabIndex);
                        }, 50);
                    }
                });

                // 强制重新渲染Tab组件
                element.render('tab', 'top-tabs');
            });

            // 初始化用户下拉菜单
            $('#userDropdown').click(function () {
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: '<div style="padding: 20px;">' +
                        '<div class="user-menu-item" onclick="changePassword()">修改密码</div>' +
                        '<div class="user-menu-item" onclick="logout()">退出登录</div>' +
                        '</div>',
                    style: 'width: 200px;'
                });
            });

            // 菜单项点击事件 - 使用layui页签
            $('.menu-item').click(function () {
                var href = $(this).data('href');
                var title = $(this).find('span').text();
                var icon = $(this).find('i').attr('class');

                if (href) {
                    console.log('菜单项点击 - 打开页面:', href, '标题:', title);

                    // 确保href是完整的URL
                    if (href && !href.startsWith('http') && !href.startsWith('/')) {
                        href = '/' + href;
                    }

                    // 生成唯一的Tab ID
                    var tabId = 'tab-' + href.replace(/\//g, '-').replace(/\./g, '-');

                    // 获取顶部Tab容器
                    var topTabs = $('.top-tabs');
                    var element = layui.element;

                    // 如果顶部Tab容器未显示，则显示
                    if (!topTabs.hasClass('active')) {
                        console.log('初始化页签容器');

                        // 显示顶部Tab容器
                        topTabs.removeClass('showing-home');
                        topTabs.addClass('active');

                        // 设置显示样式
                        topTabs.css({
                            'display': 'flex',
                            'height': 'calc(100vh - 60px)'
                        });

                        // 隐藏主内容区域
                        $('.main-content').hide();

                        // 绑定首页页签点击事件
                        $('.layui-tab-title li.layui-tab-home').off('click').on('click', function() {
                            console.log('点击首页页签');
                            showHomeContent();
                        });
                    } else {
                        // 如果当前显示的是首页内容，切换到页签内容
                        if (topTabs.hasClass('showing-home')) {
                            // 隐藏主菜单内容
                            $('.main-content').hide();
                            topTabs.removeClass('showing-home');

                            // 设置页签栏高度
                            topTabs.css('height', 'calc(100vh - 60px)');
                        }
                    }

                    // 检查Tab是否已存在
                    var existTab = $('.layui-tab-title li[lay-id="' + tabId + '"]');
                    if (existTab.length > 0) {
                        console.log('Tab已存在，切换到:', tabId);

                        // 存储href属性，用于后续可能的重新加载
                        existTab.attr('data-href', href);

                        // 切换到该Tab
                        element.tabChange('top-tabs', tabId);

                        // 强制更新激活状态
                        $('.layui-tab-title li').removeClass('layui-this');
                        existTab.addClass('layui-this');

                        // 确保页签内容区域显示
                        $('.layui-tab-content').show();

                        // 获取Tab索引
                        var tabIndex = existTab.index();

                        // 强制显示对应的Tab内容
                        $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                        $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');

                        // 检查iframe是否需要重新加载
                        var iframe = $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).find('iframe');
                        if (iframe.length > 0 && (!iframe.attr('src') || iframe.attr('src') === 'about:blank')) {
                            console.log('重新加载iframe内容:', href);
                            iframe.attr('src', href);
                        }
                    } else {
                        console.log('创建新Tab:', tabId);

                        // 创建Tab标题
                        var tabTitle = '';
                        if (icon) {
                            // 使用图标
                            tabTitle = '<i class="' + icon + '"></i> ' + title;
                        } else {
                            tabTitle = title;
                        }

                        // 添加新Tab
                        element.tabAdd('top-tabs', {
                            title: tabTitle,
                            id: tabId,
                            content: '<iframe src="' + href + '" frameborder="0" scrolling="auto" style="width:100%;height:100%;"></iframe>'
                        });

                        // 存储href属性，用于后续可能的重新加载
                        var newTab = $('.layui-tab-title li[lay-id="' + tabId + '"]');
                        newTab.attr('data-href', href);

                        // 切换到新Tab
                        element.tabChange('top-tabs', tabId);

                        // 延迟执行，确保在layui处理完Tab切换后执行
                        setTimeout(function() {
                            // 强制更新激活状态
                            $('.layui-tab-title li').removeClass('layui-this');
                            $('.layui-tab-title li[lay-id="' + tabId + '"]').addClass('layui-this');

                            // 获取新Tab的索引
                            var tabIndex = $('.layui-tab-title li[lay-id="' + tabId + '"]').index();

                            // 强制显示对应的Tab内容
                            $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                            $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');

                            // 确保页签内容区域显示
                            $('.layui-tab-content').show();

                            console.log('强制更新新Tab显示状态 - ID:', tabId, '索引:', tabIndex);
                        }, 50);
                    }

                    // 强制重新渲染Tab组件
                    setTimeout(function() {
                        element.render('tab', 'top-tabs');
                    }, 10);
                }
            });

            // 关闭子菜单层
            $('.submenu-close').click(function () {
                $('#submenuLayer').hide();
            });

            // 点击遮罩层关闭
            $('#submenuLayer').click(function (e) {
                if ($(e.target).hasClass('submenu-layer')) {
                    $(this).hide();
                }
            });

            // 更新状态信息
            updateParkStatus();
            setInterval(updateParkStatus, 30000); // 每30秒更新一次

            // 初始化主题
            initTheme();
        });

        // 修改密码
        function changePassword() {
            layer.open({
                title: "修改密码",
                type: 2,
                area: ['450px', '390px'],
                content: '/Index/ChangePassword'
            });
        }

        // 退出登录
        function logout() {
            $.post("/Index/ExitLogin", {}, function (data) {
                if (data.Success) {
                    window.location.href = "/Login/Index";
                }
            }, "json");
        }

        // 更新停车场状态
        function updateParkStatus() {
            $.get("/Index/GetParkingState", function (json) {
                if (json.success) {
                    var status = json.data.parkstatus;
                    var statusText = status == 1 ? "已连接" :
                        status == 2 ? "未启用" :
                            status == -1 ? "未知" : "未连接";
                    var statusClass = status == 1 ? "status-online" : "status-offline";

                    $('.parkstatus')
                        .removeClass('status-online status-offline')
                        .addClass(statusClass)
                        .text("云平台：" + statusText);
                }
            });
        }

        // 打开全部功能面板
        function openAllFunctions() {
            // 动态填充功能卡片
            var functions = [
                { name: "岗亭管理", href: "SentryHost/Index", icon: "layui-icon-home" },
                { name: "区域管理", href: "ParkArea/Index", icon: "layui-icon-layouts" },
                { name: "车道管理", href: "Passway/Index", icon: "layui-icon-transfer" },
                { name: "设备管理", href: "Device/Index", icon: "layui-icon-component" },
                { name: "车场设置", href: "Policy/Index", icon: "layui-icon-set" },
                { name: "车道监控", href: "LaneMonitor/Index", icon: "layui-icon-video" },
                { name: "计费规则", href: "BillingRule/Index", icon: "layui-icon-calculator" },
                { name: "充值规则", href: "MonthRule/MonthCharge", icon: "layui-icon-diamond" },
                { name: "通行控制", href: "AccessAuth/Index", icon: "layui-icon-vercode" },
                { name: "特殊车牌", href: "SpecialCar/Index", icon: "layui-icon-flag" },
                { name: "车辆登记", href: "Owner/Index", icon: "layui-icon-add-circle" },
                { name: "黑名单管理", href: "BlackList/Index", icon: "layui-icon-close-fill" },
                { name: "访客车辆", href: "Reserve/Index", icon: "layui-icon-user" },
                { name: "商家车辆", href: "BusinessCar/Index", icon: "layui-icon-cart" }
            ];
            var html = "";
            functions.forEach(function (f) {
                html += '<div class="function-card" onclick="openFunctionInTab(\'' + f.href + '\', \'' + f.name + '\', \'layui-icon ' + f.icon + '\')">' +
                    '<i class="layui-icon ' + f.icon + '"></i>' +
                    '<span>' + f.name + '</span>' +
                    '</div>';
            });
            document.getElementById('allFunctionsBody').innerHTML = html;
            document.getElementById('allFunctionsModal').style.display = 'block';
        }

        // 主题切换功能
        function toggleTheme() {
            var currentTheme = localStorage.getItem('theme') || 'dark-blue';
            console.log('当前主题:', currentTheme);

            // 设置主题顺序：深蓝色 -> 暗灰色
            var themes = ['dark-blue', 'dark-gray'];

            // 获取当前主题在数组中的索引
            var currentIndex = themes.indexOf(currentTheme);
            console.log('当前主题索引:', currentIndex);

            // 如果找不到当前主题，默认使用深蓝色主题
            if (currentIndex === -1) {
                currentIndex = 0;
            }
            // 计算下一个主题的索引（循环切换）
            var nextIndex = (currentIndex + 1) % themes.length;
            var newTheme = themes[nextIndex];
            console.log('新主题:', newTheme);

            // 保存主题设置
            localStorage.setItem('theme', newTheme);

            // 应用主题
            applyTheme(newTheme);

            // 显示提示
            var themeName = newTheme === 'dark-blue' ? '深蓝色' : '暗灰色';
            layer.msg('已切换到' + themeName + '主题');
        }

        // 应用主题
        function applyTheme(theme) {
            console.log('应用主题:', theme);

            try {
                // 先移除所有主题类 - 使用更直接的方式
                var htmlElement = document.documentElement;
                var bodyElement = document.body;

                // 移除所有可能的主题类
                htmlElement.className = htmlElement.className.replace(/theme-dark-blue/g, '').replace(/theme-dark-gray/g, '').trim();
                bodyElement.className = bodyElement.className.replace(/theme-dark-blue/g, '').replace(/theme-dark-gray/g, '').trim();

                console.log('移除主题类后 HTML 类:', htmlElement.className);
                console.log('移除主题类后 BODY 类:', bodyElement.className);

                // 添加新主题类
                if (htmlElement.className) {
                    htmlElement.className += ' theme-' + theme;
                } else {
                    htmlElement.className = 'theme-' + theme;
                }

                if (bodyElement.className) {
                    bodyElement.className += ' theme-' + theme;
                } else {
                    bodyElement.className = 'theme-' + theme;
                }

                console.log('添加主题类后 HTML 类:', htmlElement.className);
                console.log('添加主题类后 BODY 类:', bodyElement.className);

                // 强制重新应用样式
                document.body.style.display = 'none';
                setTimeout(function () {
                    document.body.style.display = '';
                }, 5);
            } catch (e) {
                console.error('应用主题时出错:', e);
            }
        }

        // 初始化主题
        function initTheme() {
            var savedTheme = localStorage.getItem('theme');
            console.log('初始化主题函数中的保存主题:', savedTheme);

            // 如果没有保存的主题或者不是已知主题，则使用深蓝色调作为默认主题
            if (!savedTheme || (savedTheme !== 'dark-blue' && savedTheme !== 'dark-gray')) {
                savedTheme = 'dark-blue';
                localStorage.setItem('theme', savedTheme);
                console.log('设置默认主题为深蓝色');
            }

            // 使用改进的applyTheme函数应用主题
            applyTheme(savedTheme);
        }

        // 关闭全部功能
        function closeAllFunctions() {
            document.getElementById('allFunctionsModal').style.display = 'none';
        }

        // 在Tab中打开功能并关闭全部功能面板
        function openFunctionInTab(href, title, icon) {
            console.log('功能卡片点击 - 打开页面:', href, '标题:', title);

            // 关闭全部功能面板
            closeAllFunctions();

            // 确保href是完整的URL
            if (href && !href.startsWith('http') && !href.startsWith('/')) {
                href = '/' + href;
            }

            // 生成唯一的Tab ID
            var tabId = 'tab-' + href.replace(/\//g, '-').replace(/\./g, '-');

            // 获取顶部Tab容器
            var topTabs = $('.top-tabs');
            var element = layui.element;

            // 如果顶部Tab容器未显示，则显示
            if (!topTabs.hasClass('active')) {
                console.log('初始化页签容器');

                // 显示顶部Tab容器
                topTabs.removeClass('showing-home');
                topTabs.addClass('active');

                // 设置显示样式
                topTabs.css({
                    'display': 'flex',
                    'height': 'calc(100vh - 60px)'
                });

                // 隐藏主内容区域
                $('.main-content').hide();

                // 绑定首页页签点击事件
                $('.layui-tab-title li.layui-tab-home').off('click').on('click', function() {
                    console.log('点击首页页签');
                    showHomeContent();
                });
            } else {
                // 如果当前显示的是首页内容，切换到页签内容
                if (topTabs.hasClass('showing-home')) {
                    // 隐藏主菜单内容
                    $('.main-content').hide();
                    topTabs.removeClass('showing-home');

                    // 设置页签栏高度
                    topTabs.css('height', 'calc(100vh - 60px)');
                }
            }

            // 检查Tab是否已存在
            var existTab = $('.layui-tab-title li[lay-id="' + tabId + '"]');
            if (existTab.length > 0) {
                console.log('Tab已存在，切换到:', tabId);

                // 存储href属性，用于后续可能的重新加载
                existTab.attr('data-href', href);

                // 切换到该Tab
                element.tabChange('top-tabs', tabId);

                // 强制更新激活状态
                $('.layui-tab-title li').removeClass('layui-this');
                existTab.addClass('layui-this');

                // 确保页签内容区域显示
                $('.layui-tab-content').show();

                // 获取Tab索引
                var tabIndex = existTab.index();

                // 强制显示对应的Tab内容
                $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');

                // 检查iframe是否需要重新加载
                var iframe = $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).find('iframe');
                if (iframe.length > 0 && (!iframe.attr('src') || iframe.attr('src') === 'about:blank')) {
                    console.log('重新加载iframe内容:', href);
                    iframe.attr('src', href);
                }
            } else {
                console.log('创建新Tab:', tabId);

                // 创建Tab标题
                var tabTitle = '';
                if (icon) {
                    // 使用图标
                    tabTitle = '<i class="' + icon + '"></i> ' + title;
                } else {
                    tabTitle = title;
                }

                // 添加新Tab
                element.tabAdd('top-tabs', {
                    title: tabTitle,
                    id: tabId,
                    content: '<iframe src="' + href + '" frameborder="0" scrolling="auto" style="width:100%;height:100%;"></iframe>'
                });

                // 存储href属性，用于后续可能的重新加载
                var newTab = $('.layui-tab-title li[lay-id="' + tabId + '"]');
                newTab.attr('data-href', href);

                // 切换到新Tab
                element.tabChange('top-tabs', tabId);

                // 延迟执行，确保在layui处理完Tab切换后执行
                setTimeout(function() {
                    // 强制更新激活状态
                    $('.layui-tab-title li').removeClass('layui-this');
                    $('.layui-tab-title li[lay-id="' + tabId + '"]').addClass('layui-this');

                    // 获取新Tab的索引
                    var tabIndex = $('.layui-tab-title li[lay-id="' + tabId + '"]').index();

                    // 强制显示对应的Tab内容
                    $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                    $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');

                    // 确保页签内容区域显示
                    $('.layui-tab-content').show();

                    console.log('强制更新新Tab显示状态 - ID:', tabId, '索引:', tabIndex);
                }, 50);
            }

            // 强制重新渲染Tab组件
            setTimeout(function() {
                element.render('tab', 'top-tabs');
            }, 10);
        }

        // 点击遮罩关闭
        document.querySelector('.all-functions-modal').onclick = function (e) {
            if (e.target === this) closeAllFunctions();
        };

        // 功能搜索
        $('#functionSearch').on('input', function () {
            var searchText = $(this).val().toLowerCase();
            // 实现实时搜索功能
        });

        // Tab相关函数

        // 在顶部Tab中打开页面
        function openInTopTab(href, title, icon) {
            var topTabs = $('.top-tabs');
            var element = layui.element;
            var tabId = 'tab-' + href.replace(/\//g, '-');

            // 确保href是完整的URL
            if (href && !href.startsWith('http') && !href.startsWith('/')) {
                href = '/' + href;
            }

            console.log('打开页面:', href, '标题:', title, 'TabID:', tabId);

            // 完全重置页签容器状态
            if (!topTabs.hasClass('active')) {
                console.log('重置并初始化页签容器');

                // 清空所有Tab标题和内容
                $('.top-tabs .layui-tab-title').empty();
                $('.top-tabs .custom-tab-content iframe').remove();

                // 显示顶部Tab容器
                topTabs.removeClass('showing-home');
                topTabs.addClass('active');

                // 设置显示样式
                topTabs.css({
                    'display': 'flex',
                    'height': 'calc(100vh - 60px)'
                });
                $('.top-tabs .layui-tab').css('display', 'block');

                // 隐藏主内容区域
                $('.main-content').hide();

                // 显示iframe容器
                $('.top-tabs .custom-tab-content').show();

                // 添加首页页签
                var homeButton = $('<li class="layui-tab-home" lay-id="tab-home" title="返回主菜单"><i class="layui-icon layui-icon-home"></i> 首页</li>');
                $('.top-tabs .layui-tab-title').append(homeButton);

                // 绑定返回主菜单按钮点击事件
                homeButton.on('click', function() {
                    // 切换到首页内容，但不关闭页签
                    showHomeContent();
                });

                // 强制重新渲染Tab组件
                element.render('tab', 'top-tabs');
            }

            // 检查Tab是否已存在
            var existTab = $('.top-tabs .layui-tab-title li[lay-id="' + tabId + '"]');
            if (existTab.length > 0) {
                // 如果Tab已存在，切换到该Tab
                console.log('Tab已存在，切换到:', tabId);
                element.tabChange('top-tabs', tabId);

                // 显示对应的iframe
                $('.top-tabs iframe').removeClass('active');
                $('#iframe-' + tabId).addClass('active');

                return;
            }

            // 创建Tab标题
            var tabTitle = '';
            if (icon) {
                // 使用图标
                tabTitle = '<i class="' + icon + '"></i> ' + title;
            } else {
                tabTitle = title;
            }

            // 添加新Tab标题
            element.tabAdd('top-tabs', {
                title: tabTitle,
                id: tabId
            });

            // 创建并添加iframe
            var iframe = $('<iframe></iframe>');
            iframe.attr({
                'id': 'iframe-' + tabId,
                'frameborder': '0',
                'scrolling': 'auto'
            });

            // 监听iframe加载完成事件
            iframe.on('load', function() {
                console.log('iframe加载完成:', href);
            });

            // 监听iframe加载错误事件
            iframe.on('error', function() {
                console.error('iframe加载失败:', href);
                // 尝试重新加载
                setTimeout(function() {
                    iframe.attr('src', href);
                }, 500);
            });

            // 将iframe添加到自定义容器中
            $('.top-tabs .custom-tab-content').append(iframe);

            // 设置iframe的src属性
            console.log('设置iframe src:', href);
            iframe.attr('src', href);

            // 切换到新Tab并确保激活状态正确
            setTimeout(function() {
                // 切换到新Tab
                element.tabChange('top-tabs', tabId);

                // 强制更新激活状态
                $('.top-tabs .layui-tab-title li').removeClass('layui-this');
                $('.top-tabs .layui-tab-title li[lay-id="' + tabId + '"]').addClass('layui-this');

                // 隐藏所有iframe，显示当前iframe
                $('.top-tabs iframe').removeClass('active');
                iframe.addClass('active');

                // 强制重新渲染Tab
                element.render('tab', 'top-tabs');
            }, 50);
        }

        // 关闭所有Tab并返回菜单
        function closeAllTopTabs() {
            console.log('关闭所有Tab并返回菜单');

            // 获取layui元素对象
            var element = layui.element;

            // 获取所有非首页页签
            var tabs = $('.layui-tab-title li:not(.layui-tab-home)');

            // 移除所有非首页页签
            tabs.each(function() {
                var id = $(this).attr('lay-id');
                element.tabDelete('top-tabs', id);
            });

            // 完全重置顶部Tab容器状态
            $('.top-tabs').removeClass('active');
            $('.top-tabs').removeClass('showing-home');
            $('.top-tabs').removeAttr('style');
            $('.top-tabs').css('display', 'none');

            // 显示主内容区域
            $('.main-content').show();
            $('.main-content').removeClass('with-tabs');
        }

        // 显示首页内容但保留页签
        function showHomeContent() {
            console.log('显示首页内容');

            // 获取layui元素对象
            var element = layui.element;

            // 显示主菜单内容
            $('.main-content').show();

            // 添加一个标记，表示当前显示的是首页内容
            $('.top-tabs').addClass('showing-home');

            // 调整页签栏高度
            $('.top-tabs').css('height', 'auto');

            // 确保页签内容区域隐藏
            $('.layui-tab-content').hide();

            // 激活首页页签
            $('.layui-tab-title li').removeClass('layui-this');
            $('.layui-tab-title li.layui-tab-home').addClass('layui-this');

            console.log('显示首页内容 - 主菜单已显示');
        }

        // 隐藏首页内容，显示页签内容
        function hideHomeContent() {
            if ($('.top-tabs').hasClass('showing-home')) {
                // 获取layui元素对象
                var element = layui.element;

                // 隐藏主菜单内容
                $('.main-content').hide();
                $('.top-tabs').removeClass('showing-home');

                // 设置页签栏高度
                $('.top-tabs').css('height', 'calc(100vh - 60px)');

                // 显示页签内容区域
                $('.layui-tab-content').show();

                // 获取当前激活的Tab
                var activeTab = $('.layui-tab-title li.layui-this:not(.layui-tab-home)');

                // 如果当前已有激活的非首页Tab，则使用它
                if (activeTab.length > 0) {
                    var tabId = activeTab.attr('lay-id');
                    var tabIndex = activeTab.index();
                    console.log('使用当前激活的Tab:', tabId, '索引:', tabIndex);

                    // 强制更新激活状态
                    $('.layui-tab-title li').removeClass('layui-this');
                    activeTab.addClass('layui-this');

                    // 强制显示对应的Tab内容
                    $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                    $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');
                }
                // 否则，激活第一个非首页Tab
                else {
                    var firstTab = $('.layui-tab-title li:not(.layui-tab-home)').first();
                    if (firstTab.length > 0) {
                        var tabId = firstTab.attr('lay-id');
                        var tabIndex = firstTab.index();
                        console.log('切换到第一个页签:', tabId, '索引:', tabIndex);

                        // 切换到该Tab
                        element.tabChange('top-tabs', tabId);

                        // 强制更新激活状态
                        $('.layui-tab-title li').removeClass('layui-this');
                        firstTab.addClass('layui-this');

                        // 强制显示对应的Tab内容
                        $('.layui-tab-content .layui-tab-item').removeClass('layui-show').css('display', 'none');
                        $('.layui-tab-content .layui-tab-item').eq(tabIndex-1).addClass('layui-show').css('display', 'block');
                    }
                }

                // 确保所有iframe都正确加载
                var activeIframe = $('.layui-tab-content .layui-tab-item.layui-show iframe');
                if (activeIframe.length > 0 && (!activeIframe.attr('src') || activeIframe.attr('src') === 'about:blank')) {
                    var activeTabId = $('.layui-tab-title li.layui-this').attr('lay-id');
                    var href = $('.layui-tab-title li[lay-id="' + activeTabId + '"]').data('href');
                    if (href) {
                        console.log('重新加载iframe内容:', href);
                        activeIframe.attr('src', href);
                    }
                }

                console.log('隐藏首页内容，显示页签内容');
            }
        }

        // 关闭指定的Tab
        function closeTab(tabId) {
            console.log('关闭Tab:', tabId);

            // 获取要关闭的Tab项
            var tabItem = $('.top-tabs .tab-item[data-id="' + tabId + '"]');

            // 检查该Tab是否处于激活状态
            var isActive = tabItem.hasClass('active');

            // 获取对应的iframe
            var iframe = $('#iframe-' + tabId);

            // 移除Tab项和iframe
            tabItem.remove();
            iframe.remove();

            // 检查是否还有其他Tab
            var remainingTabs = $('.top-tabs .tab-item');
            if (remainingTabs.length > 0) {
                // 如果关闭的是激活的Tab，则激活其他Tab
                if (isActive) {
                    // 激活第一个Tab
                    var firstTab = remainingTabs.first();
                    firstTab.addClass('active');

                    // 显示对应的iframe
                    var firstTabId = firstTab.data('id');
                    $('#iframe-' + firstTabId).addClass('active');
                }
            } else {
                // 如果没有其他Tab了，关闭所有页签并返回主菜单
                closeAllTopTabs();
            }
        }

        // 页面加载完成后初始化顶部Tab
        $(document).ready(function() {
            layui.use(['element'], function() {
                var element = layui.element;

                // 确保Tab组件正确初始化
                element.render('tab', 'top-tabs');

                // 监听Tab切换事件
                element.on('tab(top-tabs)', function(data) {
                    console.log('切换到Tab:', data.index);

                    // 获取当前点击的Tab ID
                    var tabId = $(this).attr('lay-id');
                    console.log('点击的Tab ID:', tabId);

                    if (tabId === 'tab-home') {
                        // 如果点击的是首页页签，显示首页内容
                        showHomeContent();
                    } else {
                        // 如果点击的是其他页签，确保显示页签内容
                        hideHomeContent();

                        // 强制更新激活状态
                        $('.top-tabs .layui-tab-title li').removeClass('layui-this');
                        $('.top-tabs .layui-tab-title li[lay-id="' + tabId + '"]').addClass('layui-this');

                        // 显示对应的iframe
                        $('.top-tabs iframe').removeClass('active');
                        $('#iframe-' + tabId).addClass('active');

                        console.log('显示iframe:', '#iframe-' + tabId);

                        // 强制重新渲染Tab
                        setTimeout(function() {
                            element.render('tab', 'top-tabs');
                        }, 10);
                    }
                });

                // 监听Tab删除事件
                element.on('tabDelete(top-tabs)', function(data) {
                    console.log('删除Tab:', data.index);

                    // 延迟检查，确保DOM已更新
                    setTimeout(function() {
                        // 检查是否只剩下首页页签
                        var tabs = $('.top-tabs .layui-tab-title li:not([lay-id="tab-home"])');
                        console.log('剩余非首页页签数量:', tabs.length);

                        if (tabs.length === 0) {
                            console.log('只剩下首页页签，关闭所有页签并返回主菜单');
                            // 如果只剩下首页页签，关闭所有页签并返回主菜单
                            closeAllTopTabs();
                        }
                    }, 10);
                });

                // 首页页签现在在openInTopTab函数中添加

                // 修复Tab内容区域的高度
                function adjustTabContentHeight() {
                    var headerHeight = $('.header').outerHeight();
                    var tabTitleHeight = $('.top-tabs .layui-tab-title').outerHeight();
                    var footerHeight = $('.footer').outerHeight();
                    var windowHeight = $(window).height();

                    // 计算Tab内容区域的高度
                    var contentHeight = windowHeight - headerHeight - tabTitleHeight - footerHeight;
                    $('.top-tabs .layui-tab-content').height(contentHeight);
                }

                // 初始调整高度
                adjustTabContentHeight();

                // 窗口大小改变时调整高度
                $(window).resize(function() {
                    adjustTabContentHeight();
                });

                global.getFormPower();
            });
        });


         var global = {
             parkStatusRetryInterval: 2000,// 初始间隔 2 秒
            maxInterval: 60000 * 10,// 最大间隔 10 分钟
            controller: new AbortController(),
            timeoutId: null,// 存储定时器 ID
            isWindow: '@Html.Raw(ViewBag.IsWindows)'.toLowerCase() === 'true',
            warnCount: 0,
            formPower: null,
            powerType: null,
            getFormPower: function () {
                $.getJSON("/PowerGroup/GetFormPower?_r=" + Math.random(), {},  //获取到当前用户所有权限
                    function (json) {
                        if (json.Success) {
                            global.formPower = json.Data;
                        } else {
                            layer.msg('系统错误', function () {
                                window.top.location.href = '../../';
                            });
                        }
                    });
            },
            getBtnPower: function (win, callBack) {
                var pathname = win.location.pathname;
                var controlName = pathname.trim('/').split('/')[0];
                var btns = win.document.getElementsByTagName("button");
                var pagePower = this.formPower[controlName]
                for (var i = 0; i < btns.length; i++) {
                    if (!$(btns[i]).hasClass("layui-laypage-btn")) {
                        var id = btns[i].id
                        if (pagePower[id]) {
                            $(btns[i]).removeClass("layui-hide")
                        } else {
                            if (controlName.indexOf("Tools") == -1) $(btns[i]).removeClass("layui-hide").addClass("layui-hide")
                        }
                    }
                }

                if (callBack) callBack(pagePower);

            }
         };
    </script>
</body>
</html>