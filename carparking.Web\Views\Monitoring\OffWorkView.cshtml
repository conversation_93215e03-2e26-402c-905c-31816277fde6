﻿
<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>&nbsp;</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
	<link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
	<link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
	<link href="~/Static/css/animate.min.css" rel="stylesheet"/>
	<link href="~/Static/css/com.ui.css?1" rel="stylesheet" />
	<style>
		@@page { size: landscape; }
		html, body { padding: 0 15px; }
		.layui-card-header { border-bottom-color: #0094ff; font-weight: bold; color: #0094ff; font-size: 1rem; font-family: FangSong; }
		.layui-card:last-child { box-shadow: none; }
		ul.dataList li { padding: 0 0 4px 0; font-size: 0.9rem; }
		ul.dataList li text { padding-left: 4px; color: #0094ff; font-size: 1.2rem; font-family: FangSong; word-break: break-all; word-wrap: break-word; }
		.admin { font-size: 0.9rem; }
		.admin text { font-size: 1.2rem; color: #0094ff; font-family: FangSong; }
		.layui-tab-title { background-color: #5868e0 !important; }
		body { font-size: 12px; }
	</style>
</head>
<body>
	<div style="width:980px;height:375px;border:1px solid #ccc;" id="divPrint" media="screen">

		<div class="layui-card-body">
			<div class="layui-row admin">
				<div class="layui-col-sm6">交班人员：<text id="name">admin</text></div>
				<div class="layui-col-sm6">交班账号：<text id="account">admin</text></div>
			</div>
			<div class="layui-row admin">
				<div class="layui-col-sm6">上班时间：<text id="start">2022-03-11 08:00:00</text></div>
				<div class="layui-col-sm6">交班时间：<text id="end">2022-03-11 18:00:00</text></div>
			</div>
		</div>
		<div class="layui-row" style="border-bottom:1px solid #0094ff;">
			<div class="layui-col-sm3">
				<div class="layui-card">
					<div class="layui-card-header">
						<text>车辆统计</text>
					</div>
					<div class="layui-card-body" style="border-right:1px solid #0094ff;">
						<ul class="dataList" id="countHtml">
						</ul>
					</div>
				</div>
			</div>
			<div class="layui-col-sm9">
				<div class="layui-card">
					<div class="layui-card-header">
						<text>收费统计</text>
					</div>
					<div class="layui-card-body">
						<ul class="layui-row dataList" id="payHtml">
						</ul>
					</div>
				</div>
			</div>
		</div>

	</div>
	<script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
	<script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
	<script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
	<script src="~/Static/js/pm.utils.js?1.28" asp-append-version="true"></script>
	<script>
		var sdt = decodeURIComponent($.getUrlParam("sdt"));
		var edt = decodeURIComponent($.getUrlParam("edt"));
		var exp = $.getUrlParam("export");

		var pager = {
			init: function () {
				this.bindData();
			},
			bindData: function () {
				$.getJSON("GetWorkShiftAnData", {}, function (json) {
					if (json.success) {
						var model = json.data.model;
					var OrderCoutLabel = json.data.OrderCoutLabel;
					var PayModeLabel = json.data.PayModeLabel;

					$("#name").text(model.WorkShift_OffName);
					$("#account").text(model.WorkShift_OffAccount);
					$("#start").text(model.WorkShift_OnTime);
					$("#end").text(model.WorkShift_OffTime);

					var countHtml = "";
					OrderCoutLabel.forEach((item, index) => {
						countHtml += '<tr><td>' + item.Name + '</td><td>' + item.Value + '</td></tr>';
					});
					$("#countHtml").html(countHtml);

					var payHtml = "";
					PayModeLabel.forEach((item, index) => {
						payHtml += '<tr class="layui-col-sm3"><td>' + item.Name + '</td><td>' + item.Value + '</td></tr>';
					});
					$("#payHtml").html(payHtml);

						setTimeout(function () {
							window.print();
						}, 1000);

					} else {
						layer.msg(json.msg, { icon: 0, time: 2000 }, function () {
							window.parent.layer.closeAll();
						});
					}
				});

			},
			bindShow: function (data) {


			}
		}

		$(function () {
			pager.init();
		});

		var beforePrint = function () {
			console.log('beforePrint');
		};

		var afterPrint = function () {
			console.log('afterPrint');
			setTimeout(() => {
				window.close();
			}, 5000);

			var s = 4;
			setInterval(() => {
				layer.msg(s + "秒后自动关闭");
				s--;
			}, 1000);
		};

		if (window.matchMedia) {   //返回一个新的 MediaQueryList 对象，表示指定的媒体查询字符串解析后的结果。
			var mediaQueryList = window.matchMedia('OffWorkPrint');
			mediaQueryList.addListener(function (mql) {
				if (mql.matches) {
					beforePrint();
				} else {
					afterPrint();
				}
			});
		}

		window.onbeforeprint = beforePrint;
		window.onafterprint = afterPrint;
	</script>
</body>
</html>
