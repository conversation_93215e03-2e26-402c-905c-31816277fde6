﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑管理员</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        .label { color: #999; font-size: 11px; padding-bottom: 2px; clear: both; font-weight: normal; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
        <input type="text" name="email" />
    </div>
    <div class="ibox-content layui-form">
        <div id="verifyCheck">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">权限</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <select data-placeholder="选择权限" class="form-control chosen-input " id="Admins_PowerNo" name="Admins_PowerNo" lay-search>
                        <option value="">选择权限</option>
                    </select>
                    <script type="text/x-jquery-tmpl" id="Tmpl_PowerID">
                        <option value="${PowerGroup_No}" data-id="${PowerGroup_ID}">${PowerGroup_Name}</option>
                    </script>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">账号</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-name v-minlen" data-minlen="2" maxlength="20" id="Admins_Account" name="Admins_Account" />
                    <label class="label lan-label">最少2个字符，最多20个字符</label>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
                <div class="layui-col-sm2 edit-label ">密码</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="password" class="layui-input" maxlength="20" id="Admins_Pwd" name="Admins_Pwd" onkeydown="if (event.keyCode == 32) return false;" />
                    <div class="label-desc" style="color:#5f5b59">强密码规则：6-20位，英文字符、数字和特殊符号三种组合</div>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">昵称</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-minlen" data-minlen="1" maxlength="20" id="Admins_Name" name="Admins_Name" />
                </div>
                <div class="layui-col-sm2 edit-label ">手机</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-phone" id="Admins_Phone" name="Admins_Phone" maxlength="11" />
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">邮箱</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <input type="text" class="layui-input v-email" id="Admins_Email" name="Admins_Email" maxlength="50" />
                </div>
                <div class="layui-col-sm2 edit-label ">接收系统通知</div>
                <div class="layui-col-sm3 edit-ipt-ban">
                    <select class="layui-select" lay-search id="Admins_EnableNotify" name="Admins_EnableNotify">
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">地址</div>
                <div class="layui-col-sm8 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Admins_Address" name="Admins_Address" maxlength="200" />
                </div>
            </div>

            <div class="layui-row">
                <div class="layui-col-sm2 edit-label ">备注</div>
                <div class="layui-col-sm8 edit-ipt-ban">
                    <input type="text" class="layui-input" id="Admins_Remark" name="Admins_Remark" maxlength="200" />
                    <label class="focus valid"></label>
                </div>
            </div>

            <div class="hr-line-dashed"></div>
            <div class="layui-row">
                <div class="layui-col-sm2 edit-label">&nbsp;</div>
                <div class="layui-col-sm8 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?3" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jsencrypt.min.js"></script>
    <script>
        myVerify.init();

        layui.use('form', function () {
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramAdminsID = $.getUrlParam("Admins_ID");
        var pwdType = '@(carparking.BLL.Cache.AppBasicCache.CurrentSysConfigContent?.SysConfig_PwdType ?? 1)';

        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {
                power.getByAllType();
            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("/Admins/GetAdmins", { Admins_ID: paramAdminsID }, function (json) {
                        if (json.Success) {
                            json.Data["Admins_Pwd"] = "******";//后台不会返回密码,所以显示个假密码用于文本框js校验
                            $("#verifyCheck").fillForm(json.Data, function (data) { });

                            $("#Admins_Account").attr("disabled", true);
                            layui.form.render("select");
                        }
                    });
                } else if (paramAct == "Add") {
                    $("#Admins_PowerType").change();
                }
            },

            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var encrypt = new JSEncrypt();
                    encrypt.setPublicKey("@Html.Raw(carparking.Common.RSAConfig.RsaPublicKey)");

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Admins_PowerID = $("#Admins_PowerNo").find("option:selected").attr("data-id");
                        data.Admins_PowerNo = $("#Admins_PowerNo").val();
                        data.Admins_Address = $("#Admins_Address").val();
                        data.Admins_Remark = $("#Admins_Remark").val();
                        data.Admins_Email = $("#Admins_Email").val();
                        data.Admins_Account = $("#Admins_Account").val();
                        data.Admins_Name = $("#Admins_Name").val();
                        data.Admins_Phone = $("#Admins_Phone").val();
                        data.Admins_Pwd = encrypt.encrypt($("#Admins_Pwd").val());
                        return data;
                    });

                    if (param.Admins_Name == '') { param.Admins_Name = param.Admins_Account; }

                    if (pwdType!=1 && !isStrongPassword($("#Admins_Pwd").val())) {
                        var frm = layer.confirm('您的密码为弱密码,请立即修改密码', {
                            title: '<font color="#fbba49">密码风险提醒</font>',
                            btn: ['立即修改', '以后再说'],
                            btnAlign: 'c',
                        }, function () {
                            layer.close(frm);
                            $("#Admins_Pwd").focus();
                        }, function () {
                            layer.close(frm);
                            layer.msg("处理中", { icon: 16, time: 10000 });
                            pager.saveAdmin(param);
                        });
                    } else {
                        pager.saveAdmin(param);
                    }
                });

            },
            saveAdmin: function (param) {
                $("#Save").attr("disabled", true);
                if (paramAct == "Add") {
                    $.getJSON("/Admins/AddAdmins", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.Success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                window.parent.pager.bindData(1);
                            });
                        } else {
                            layer.msg(json.Message, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });
                }
                else if (paramAct == "Update") {
                    param.Admins_ID = paramAdminsID;
                    param.Admins_Account = $("#Admins_Account").val();
                    $.getJSON("/Admins/UpdateAdmins", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.Success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                window.parent.pager.bindData(1);
                            });
                        } else {
                            layer.msg(json.Message, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });
                }
            }
        };

        var power = {
            getByAllType: function () {
                $.getJSON("/Admins/GetPowerGroupList", {}, function (json) {
                    if (json.Success) {
                        $("#Admins_PowerNo").html($("#Tmpl_PowerID").tmpl(json.Data));

                        layui.form.render("select");
                    }
                });
            },
        }
    </script>
</body>
</html>
