﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>余位屏</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 10px; }
        .layui-col-sm2 edit-label { width: 190px !important; }
        .btnCombox ul li { margin-bottom: 2px; }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <div style="overflow:hidden;height:0;">
            <!--防止浏览器保存密码后自动填充-->
            <input type="password" />
            <input type="text" />
            <input type="text" name="email" />
        </div>
        <div class="layui-card">
            <div id="verifyCheck" class="layui-form layui-card-body">
                <div class="layui-card-header">
                    <strong>上行余位屏</strong>
                </div>
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">设备名称</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="text" class="layui-input" maxlength="32" id="Residual_Name" name="Residual_Name" placeholder="" />
                            </div>
                        </div>

                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">机号</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen  v-number v-min v-max" min="1" max="30" value="1" maxlength="1" id="Residual_RdMachineNumber" name="Residual_RdMachineNumber" placeholder="" />
                            </div>
                            <div class="layui-col-sm2 edit-label">模组数量</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="1" max="8" value="2" maxlength="1" id="Residual_RdModuleQuantity" name="Residual_RdModuleQuantity" placeholder="" />
                            </div>

                        </div>

                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">接口类型</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_RdInterfaceType" name="Residual_RdInterfaceType">
                                    <option value="1">T08单色</option>
                                    <option value="2">T08双色</option>
                                    <option value="3">T12单色</option>
                                    <option value="4">T12双色</option>
                                    <option value="5" selected>T08小字库</option>
                                    <option value="6">T08大字库</option>
                                    <option value="7">T08双行小字库</option>
                                    <option value="8">T08双行大字库</option>
                                </select>
                            </div>
                            <div class="layui-col-sm2 edit-label">反向使能</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_RdInverseEnable" name="Residual_RdInverseEnable">
                                    <option value="0" selected>反向</option>
                                    <option value="1">不反向</option>
                                </select>
                            </div>

                        </div>

                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">移动方式</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_RdMobileMode" name="Residual_RdMobileMode">
                                    <option value="1" selected>连续左移</option>
                                    <option value="2">连续右移</option>
                                    <option value="3">连续上移</option>
                                    <option value="4">连续下移</option>
                                    <option value="5">立即打出</option>
                                </select>
                            </div>
                            <div class="layui-col-sm2 edit-label">移动速度</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="0" max="7" value="7" maxlength="1" id="Residual_RdMovingSpeed" name="Residual_RdMovingSpeed" placeholder="" />
                            </div>
                        </div>


                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">默认显示</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_RdDefaultDisplay" name="Residual_RdDefaultDisplay">
                                    <option value="1" selected>广告</option>
                                    <option value="2">空车位</option>
                                </select>
                            </div>
                            <div class="layui-col-sm2 edit-label RdDisplaytype">广告</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="text" class="layui-input v-minlen" maxlength="32" id="Residual_RdPrefix" name="Residual_RdPrefix" placeholder="余位12" value="欢迎光临"/>
                            </div>
                        </div>

                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">显示时间</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="60" max="120" value="100" maxlength="3" id="Residual_RdDisplayTime" name="Residual_RdDisplayTime" placeholder="" />
                            </div>
                            <div class="layui-col-sm2 edit-label">静止时间</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="0" max="200" value="150" maxlength="3" id="Residual_RdRestTime" name="Residual_RdRestTime" placeholder="" />
                            </div>
                        </div>

                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">显示颜色</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_RdShowYa" name="Residual_RdShowYa">
                                    <option value="0" selected>红色</option>
                                    <option value="1">绿色</option>
                                    <option value="2">黄色</option>
                                </select>
                            </div>
                            @*  <div class="layui-col-sm2 edit-label">上下行显示</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                            <div class="btnCombox" id="Residual_RdUpDownShow">
                            <ul class="flex">
                            <li data-value="1" class="select">上行显示</li>
                            <li data-value="2">下行显示</li>
                            </ul>
                            </div>
                            </div>*@
                        </div>
                    </div>
                    <div class="layui-card-header">
                        <strong>下行余位屏</strong>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row">
                            <div class="layui-col-sm2 edit-label">下行余位屏</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <div class="btnCombox" id="Residual_DoubleLineDisplay">
                                    <ul class="flex">
                                        <li data-value="0">禁用</li>
                                        <li data-value="1" class="select">启用</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="layui-col-sm2 edit-label Residual_DoubleLineDisplay">模组数量</div>
                            <div class="layui-col-sm3 edit-ipt-ban Residual_DoubleLineDisplay">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="1" max="30" value="2" maxlength="1" id="Residual_DlModuleQuantity" name="Residual_DlModuleQuantity" placeholder="" />
                            </div>
                        </div>

                        <div class="layui-row Residual_DoubleLineDisplay">
                            <div class="layui-col-sm2 edit-label">接口类型</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_DlInterfaceType" name="Residual_DlInterfaceType">
                                    <option value="1">T08单色</option>
                                    <option value="2">T08双色</option>
                                    <option value="3">T12单色</option>
                                    <option value="4">T12双色</option>
                                    <option value="5" selected>T08小字库</option>
                                    <option value="6">T08大字库</option>
                                    <option value="7">T08双行小字库</option>
                                    <option value="8">T08双行大字库</option>
                                </select>
                            </div>
                            <div class="layui-col-sm2 edit-label">反向使能</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_DlInverseEnable" name="Residual_DlInverseEnable">
                                    <option value="0" selected>反向</option>
                                    <option value="1">不反向</option>
                                </select>
                            </div>

                        </div>

                        <div class="layui-row Residual_DoubleLineDisplay">
                            <div class="layui-col-sm2 edit-label">移动方式</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_DlMobileMode" name="Residual_DlMobileMode">
                                    <option value="1" selected>连续左移</option>
                                    <option value="2">连续右移</option>
                                    <option value="3">连续上移</option>
                                    <option value="4">连续下移</option>
                                    <option value="5">立即打出</option>
                                </select>
                            </div>
                            <div class="layui-col-sm2 edit-label">移动速度</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="0" max="7" value="7" maxlength="1" id="Residual_DlMovingSpeed" name="Residual_DlMovingSpeed" placeholder="" />
                            </div>
                        </div>

                        <div class="layui-row Residual_DoubleLineDisplay">

                            <div class="layui-col-sm2 edit-label">默认显示</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_DlDefaultDisplay" name="Residual_DlDefaultDisplay">
                                    <option value="1">广告</option>
                                    <option value="2" selected>空车位</option>
                                </select>
                            </div>
                            <div class="layui-col-sm2 edit-label DlDisplaytype">车位数前缀</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="text" class="layui-input v-minlen" maxlength="32" id="Residual_DlAdvert" name="Residual_DlAdvert" placeholder="余位12" value="余位12" />
                            </div>
                        </div>

                        <div class="layui-row Residual_DoubleLineDisplay">
                            <div class="layui-col-sm2 edit-label">显示时间</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="60" max="120" value="100" maxlength="3" id="Residual_DlDisplayTime" name="Residual_DlDisplayTime" placeholder="" />
                            </div>
                            <div class="layui-col-sm2 edit-label">静止时间</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <input type="number" class="layui-input v-minlen v-number v-min v-max" min="0" max="200" value="150" maxlength="3" id="Residual_DlRestTime" name="Residual_DlRestTime" placeholder="" />
                            </div>
                        </div>



                        <div class="layui-row Residual_DoubleLineDisplay">
                            <div class="layui-col-sm2 edit-label">显示颜色</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                                <select class="layui-select" lay-search id="Residual_DlShowYa" name="Residual_DlShowYa">
                                    <option value="0" selected>红色</option>
                                    <option value="1">绿色</option>
                                    <option value="2">黄色</option>
                                </select>
                            </div>
                            @*  <div class="layui-col-sm2 edit-label">上下行显示</div>
                            <div class="layui-col-sm3 edit-ipt-ban">
                            <div class="btnCombox" id="Residual_DlUpDownShow">
                            <ul class="flex">
                            <li data-value="1" class="select">上行显示</li>
                            <li data-value="2">下行显示</li>
                            </ul>
                            </div>
                            </div>*@
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs3 edit-label">&nbsp;</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>下发设备</t></button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplpassway">
        <option value="${Passway_No}" data-sentryHostNo="${Passway_SentryHostNo}">${Passway_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?202209010939" asp-append-version="true"></script>

    <script>

        var paramDeviceNo = "";
        var paramDeviceName = "";
        var xmSelect = null;
        var laydate = null;
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['table', 'element', 'form', 'xmSelect', 'laydate'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            laydate = layui.laydate;
            table = layui.table;

            paramDeviceNo = parent.pager.SelDeviceNoes;  //decodeURIComponent($.getUrlParam("Device_No"));
            paramDeviceName = parent.pager.SelDeviceNames;// decodeURIComponent($.getUrlParam("Device_Name"));
            $("#Residual_Name").val(paramDeviceName);
            pager.init();
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var index = parent.layer.getFrameIndex(window.name);

        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },

            bindSelect: function () {
                $.post("GetAllPasswayList", {}, function (json) {
                    if (json.success) {
                        pager.passwayList = json.data;
                        $("#Residual_PasswayNo").html($("#tmplpassway").tmpl(json.data));
                    } else {
                        console.log(json.msg);
                    }
                }, "json");


                layui.form.on("select", function (data) {
                    if (data.elem.id == "Residual_RdDefaultDisplay") {
                        var no = data.value;
                        if (no == '1') {
                            $(".RdDisplaytype").html("广告");
                            $(".DlDisplaytype").html("车位数前缀");
                            $("#Residual_DlDefaultDisplay").val("2");
                        }
                        else {
                            $(".RdDisplaytype").html("车位数前缀");
                            $(".DlDisplaytype").html("广告");
                            $("#Residual_DlDefaultDisplay").val("1");
                        }
                    }
                    if (data.elem.id == "Residual_DlDefaultDisplay") {
                        var no = data.value;
                        if (no == '1') {
                            $(".DlDisplaytype").html("广告");
                            $(".RdDisplaytype").html("车位数前缀");
                            $("#Residual_RdDefaultDisplay").val("2");
                        }
                        else {
                            $(".DlDisplaytype").html("车位数前缀");
                            $(".RdDisplaytype").html("广告");
                            $("#Residual_RdDefaultDisplay").val("1");
                        }
                    }
                    layui.form.render("select");
                })

                layui.form.render("select");
            },
            //数据绑定
            bindData: function () {
                $("#Residual_Name").attr("disabled", true);
                if (paramAct == "Update") {
                    $("#Residual_PasswayNo").attr("disabled", true);
                    $.post("GetDeviceDetail", { Residual_No: paramResidualNo }, function (json) {
                        if (json.success) {
                            $("#verifyCheck").fillForm(json.data.model, function (data) { });



                            if (json.data.model.Residual_RdDefaultDisplay == '1') {
                                $(".RdDisplaytype").html("广告");
                                $(".DlDisplaytype").html("车位数前缀");
                                $("#Residual_DlDefaultDisplay").val("2");
                            }
                            else {
                                $(".RdDisplaytype").html("车位数前缀");
                                $(".DlDisplaytype").html("广告");
                                $("#Residual_DlDefaultDisplay").val("1");
                            }

                            if (json.data.model.Residual_DlDefaultDisplay == '1') {
                                $(".DlDisplaytype").html("广告");
                                $(".RdDisplaytype").html("车位数前缀");
                                $("#Residual_RdDefaultDisplay").val("2");
                            }
                            else {
                                $(".DlDisplaytype").html("车位数前缀");
                                $(".RdDisplaytype").html("广告");
                                $("#Residual_RdDefaultDisplay").val("1");
                            }

                            layui.form.render("select");
                        } else {
                            console.log(json.msg);
                        }
                    }, "json");

                }
            },

            bindEvent: function () {

                $(".btnCombox ul li").click(function () {
                    if ($(this).hasClass("select")) return;
                    var idName = $(this).parent().parent().attr("id");
                    //if (!onDisabledCom(idName)) { return; }
                    $(this).siblings().removeClass("select");
                    $(this).addClass("select");
                    config[idName] = $(this).attr("data-value");

                    onEventCombox(idName);
                });

                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });

                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.Residual_DlAdvert = $("#Residual_DlAdvert").val();
                        data.Residual_RdPrefix = $("#Residual_RdPrefix").val();
                        data.Residual_PasswayNo = $("#Residual_PasswayNo").val();
                        return data;
                    });
                    Object.assign(param, config);
                    param.Residual_DeviceNo = paramDeviceNo;
                    $("#Save").attr("disabled", true);
                    $.getJSON("/Device/SetResidualDevice", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.success) {
                            layer.msg("已下发", { icon: 1, time: 1500 }, function () {
                                //window.parent.pager.bindData(1);
                                $("#Save").removeAttr("disabled");
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });
                });

            },
        };

        var config = {
            Residual_DoubleLineDisplay: 1,
            Residual_RdUpDownShow: 1,
            Residual_DlUpDownShow: 1
        };

        var onEventCombox = function (idName) {
            if (idName == "Residual_DoubleLineDisplay") {
                if (config[idName] == 0) {
                    $("." + idName).removeClass("layui-hide").addClass("layui-hide");
                } else {
                    $("." + idName).removeClass("layui-hide");
                }
            }
        }
    </script>

</body>
</html>
