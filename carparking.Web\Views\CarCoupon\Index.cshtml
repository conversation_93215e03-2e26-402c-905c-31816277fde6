﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车牌优惠</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
    <style data-mark="表格列数量多的时候使用此样式展示列选择">
        .layui-table-tool-panel { width: 500px; }
        .layui-table-tool-panel li { width: 33.33%; float: left; }

        after { float: left; height: 38px; line-height: 38px; padding: 0 10px; background-color: #aaa; color: #fff; border-radius: 3px; cursor: pointer; user-select: none; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>商家优惠</cite></a>
                <a><cite>车牌优惠</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header layui-form" id="searchForm">
                        <div class="layui-row">
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_No" id="ParkOrder_No" autocomplete="off" placeholder="订单编号" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_CarNo" id="ParkOrder_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌类型" class="form-control chosen-select " id="ParkOrder_CarCardType" name="ParkOrder_CarCardType" lay-search>
                                    <option value="">车牌类型</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="优惠券" class="form-control chosen-select " id="ParkOrder_CouponNum" name="ParkOrder_CouponNum" lay-search>
                                    <option value="">优惠券发放</option>
                                    <option value="0">未发放</option>
                                    <option value="1">已发放</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime0" id="ParkOrder_EnterTime0" autocomplete="off" placeholder="入场时间起" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="ParkOrder_EnterTime1" id="ParkOrder_EnterTime1" autocomplete="off" placeholder="入场时间止" />
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Update" lay-event="Update"><i class="fa fa-cny"></i><t>车牌优惠</t></button>
                            </div>
                        </script>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="tmplenterimg">
        {{# if(d.ParkOrder_EnterImgPath!=null&&d.ParkOrder_EnterImgPath!=''){}}
        <a href="{{replaceFirstPathSegment(decodeURIComponent(d.ParkOrder_EnterImgPath))}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs">预览</a>
        {{# }else{}}
        <span class="layui-badge layui-bg-gray">无图片</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmploutimg">
        {{# if(d.ParkOrder_OutImgPath!=null&&d.ParkOrder_OutImgPath!=''){}}
        <a href="{{replaceFirstPathSegment(ddecodeURIComponent(d.ParkOrder_OutImgPath))}}" data-fancybox="images" data-caption="" class="layui-btn layui-btn-xs">预览</a>
        {{# }else{}}
        <span class="layui-badge layui-bg-gray">无图片</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplstatus">
        {{# if(d.ParkOrder_StatusNo==199){}}
        <span class="layui-badge layui-bg-green">预入场</span>
        {{# }else if(d.ParkOrder_StatusNo==200){}}
        <span class="layui-badge layui-bg-blue">已入场</span>
        {{# }else if(d.ParkOrder_StatusNo==201){}}
        <span class="layui-badge layui-bg-cyan">已出场</span>
        {{# }else if(d.ParkOrder_StatusNo==202){}}
        <span class="layui-badge layui-bg-orange">已关闭</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmpllock">
        {{# if(d.ParkOrder_Lock==0){}}
        <span class="layui-badge layui-bg-orange">未锁车</span>
        {{# }else if(d.ParkOrder_Lock==1){}}
        <span class="layui-badge layui-bg-red">已锁车</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplouttype">
        {{# if(d.ParkOrder_OutType==0){}}
        <span class="layui-badge layui-bg-gray">未出场</span>
        {{# }else if(d.ParkOrder_OutType==1){}}
        <span class="layui-badge layui-bg-blue">自助缴费放行</span>
        {{# }else if(d.ParkOrder_OutType==2){}}
        <span class="layui-badge layui-bg-cyan">人工起竿放行</span>
        {{# }else if(d.ParkOrder_OutType==3){}}
        <span class="layui-badge layui-bg-orange">特殊车辆放行</span>
        {{# }else if(d.ParkOrder_OutType==4){}}
        <span class="layui-badge layui-bg-green">免费放行</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplNum">
        {{# if(d.ParkOrder_CouponNum==null||d.ParkOrder_CouponNum==0){}}
        <span class="layui-badge layui-bg-gray">未发放</span>
        {{# }else if(d.ParkOrder_CouponNum>0){}}
        <a href="#" class="layui-btn layui-btn-xs couponnum" data-key="{{d.ParkOrder_No}}" data-carno="{{d.ParkOrder_CarNo}}">{{d.ParkOrder_CouponNum}}&nbsp;&nbsp;张</a>
        {{# } }}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        $("after").click(function () {
            var state = $("#searchForm").attr("state");
            if (state == 1) {
                $("after").text("展开查询");
                $("#searchForm").removeAttr("state");
                $("#searchForm .layui-row").each(function (index) {
                    if (index != 0) { $(this).removeClass("layui-hide").addClass("layui-hide"); }
                });
            } else {
                $("after").text("隐藏查询");
                $("#searchForm").attr("state", 1);
                $("#searchForm .layui-row").each(function (index) {
                    $(this).removeClass("layui-hide");
                });
            }
        });

        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        layui.use(['table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            pager.init();

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'ParkOrder_ID', title: '订单ID', hide: true }
                , { field: 'ParkOrder_No', title: '订单号' , hide: true}
                , { field: 'ParkOrder_ParkNo', title: '车场编码', hide: true }
                , { field: 'ParkOrder_CarNo', title: '车牌号' }
                , { field: 'ParkOrder_StatusNo', title: '订单状态', toolbar: "#tmplstatus" }
                , { field: 'ParkOrder_EnterTime', title: '入场时间',sort:true }
                , { field: 'ParkOrder_EnterPasswayNo', title: '入口车道编码', hide: true }
                , { field: 'ParkOrder_EnterPasswayName', title: '入口车道名称' }
                , { field: 'ParkOrder_EnterAdminAccount', title: '入口操作员账号', hide: true }
                , { field: 'ParkOrder_EnterAdminName', title: '入口操作员', hide: true }
                , { field: 'ParkOrder_EnterImgPath', title: '入场图片', toolbar: "#tmplenterimg" }
                , { field: 'ParkOrder_Lock', title: '锁车状态', toolbar: "#tmpllock" }
                , { field: 'ParkOrder_CarCardType', title: '车牌类型编码', hide: true }
                , { field: 'ParkOrder_CarCardTypeName', title: '车牌类型' }
                , { field: 'ParkOrder_CarType', title: '车牌颜色编码', hide: true }
                , { field: 'ParkOrder_CarTypeName', title: '车牌颜色' }
                , {
                    field: 'ParkOrder_PayScene', title: '支付场景', hide: true, templet: function (d) {
                        if (d.ParkOrder_PayScene == 1) return tempBar(1, "场内缴费");
                        else if (d.ParkOrder_PayScene == 2) return tempBar(2, "出口缴费");
                        else return "";
                    }
                }
                , { field: 'ParkOrder_QrCodeType', title: '入场二维码类型', hide: true }
                , { field: 'ParkOrder_Remark', title: '备注', hide: true }
                , { field: 'ParkOrder_CouponNum', title: '优惠券', toolbar: "#tmplNum" }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CarCoupon/GetParkOrderList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , totalRow: true
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                    $(".couponnum").click(function () {
                        var no = $(this).attr("data-key");
                        var carno = $(this).attr("data-carno");
                        layer.open({
                            title: "<i class='fa fa-rmb' style='margin-top: 17px;'></i> 车牌【" + carno+"】优惠券",
                            type: 2, id: 1,
                            area: getIframeArea(['60%', '60%']),
                            fix: false, //不固定
                            maxmin: false,
                            content: '/CarCoupon/CouponIndex?ParkOrder_No=' + encodeURIComponent(no)
                        });
                    })
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        var orderno = data[0].ParkOrder_No;
                        layer.open({
                            title: "<i class='fa fa-rmb' style='margin-top: 17px;'></i> 车牌优惠",
                            type: 2, id: 1,
                            area: getIframeArea(['780px', '490px']),
                            fix: false, //不固定
                            maxmin: false,
                            content: 'Edit?ParkOrder_No=' + encodeURIComponent(orderno) + "&ParkOrder_CarNo=" + encodeURIComponent(data[0].ParkOrder_CarNo)
                        });
                        break;
                };
            });

              //排序
            table.on('sort(com-table-base)', function(obj){
                  if(obj.type==null) obj.field=null;
                  pager.sortField=obj.field;
                  pager.orderField=obj.type;
                  pager.bindData(1);
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            sortField:null,
            orderField:null,
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindPower();
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option)
                        });
                        layuiForm.render("select");
                    }
                }, "json");

                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#ParkOrder_EnterPasswayNo").append(option);
                            $("#ParkOrder_OutPasswayNo").append(option);
                        });
                        layuiForm.render("select");
                    }
                }, "json");

                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime0", "ParkOrder_EnterTime1"], { type: 'datetime', range: true });
                _DATE.bind(layui.laydate, ["ParkOrder_OutTime0", "ParkOrder_OutTime1"], { type: 'datetime', range: true });
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                 var field=pager.sortField==null?"":pager.sortField;
                var order=pager.orderField==null?"":pager.orderField;
                comtable.reload({
                    url: '/CarCoupon/GetParkOrderList'
                    , where: { conditionParam: JSON.stringify(conditionParam) ,field: field,order: order } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {

                });

                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                        $("#ParkOrder_CarNo").val(carno.join(''));
                    }
                }, "web").bindkeyup();
            }
        }
    </script>
</body>
</html>
