﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入文件</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet" />
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 20px; }
        .line-link { color: #0094ff; text-decoration: underline; }
        #Import, #Cancel { min-width: 90px; }
        #download-temp { margin-right: 10px; }

        .layui-btn-warm { background-color: #ec971f; border-color: #d58512; }
        .layadmin-warning { line-height: 24px; font-size: 12px; text-align: justify; word-break: break-all; color: #888; background-color: lemonchiffon; float: left; padding: 3px 5px; letter-spacing: 0.6px; }
    </style>
</head>
<body>
    <div class="ibox-content" style="padding-top: 5px;">
        <div class="layui-card-header" style="border-bottom:0;"></div>
        <div id="verifyCheck" class="layui-form">
            <div class="layui-row">
                <div class="layui-col-xs2 edit-label lan-label" data-lan="Select_Excel">选择文件</div>
                <div class="layui-col-xs9 edit-ipt-ban">
                    <div class="layui-form-item layui-row margin-bottom0">
                        <div class="layui-col-xs9">
                            <input type="text" placeholder="" id="iptFilePath" class="layui-input" readonly="readonly">
                        </div>
                        <div class="layui-col-xs3" style="left: -1px;">
                            <span class="input-group-btn">
                                <label title="选择" for="inputFile" class="layui-btn" style="border-top-left-radius: 0px;border-bottom-left-radius: 0px;">
                                    <input type="file" accept=".xlsx" name="file" id="inputFile" class="hide" onchange="chooseFile(this);"><i class="fa fa-folder-open-o"></i> <t class="lan-label" data-lan="Select">选择</t>
                                </label>
                            </span>
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs6 edit-ipt-ban">
                            <input class="layui-form-checkbox" type="checkbox" id="replacecar" name="replacecar" checked title="覆盖已登记车辆" lay-skin="primary">
                        </div>
                        <div class="layui-col-xs6 edit-ipt-ban">
                            <input class="layui-form-checkbox" type="checkbox" id="replaceorder" name="replaceorder" checked title="更新场内订单" lay-skin="primary">
                        </div>
                    </div>
                    <div class="layui-row">
                        <div class="layui-col-xs6 edit-ipt-ban">
                            <input class="layui-form-checkbox" type="checkbox" id="createpayorder" name="createpayorder" checked title="生成储值车充值记录" lay-skin="primary">
                        </div>
                        <div class="layui-col-xs6 edit-ipt-ban">
                            &nbsp;
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-row">
                <div class="layui-col-xs2">&nbsp;</div>
                <div class="layui-col-xs9">
                    <a href="../../Data/car_template.xlsx" id="download-temp" download="" class="btn btn-success"><i class=" fa fa-download"></i> 下载模板</a>
                    <button class="btn btn-primary" lay-submit lay-filter="formDemo" id="Import"><i class="fa fa-check"></i> 提交</button>
                    <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> 取消</button>
                </div>
            </div>
            <div class="layui-row" style="margin-top: 45px;margin-bottom：12px;">
                <div class="layui-col-xs1">&nbsp;</div>
                <div class="layui-col-xs11 layadmin-warning" style="padding:3px 10px">
                    <b>温馨提示：</b><br />
                    1、请在导入完成后<b><t style="color:#d9534f;"> 等待 3 分钟 </t></b>，以确保数据同步至岗亭。同步完成前请<b>勿操作删除或更新车辆信息及其对应的岗亭弹窗</b>；<br />
                    2、只允许<b>中文字母数字还有,，.。:：，\-/*+</b>，其它字符都会被替换成空字符串；<br />
                    3、<b>勾选【<t style="color:#d9534f;">覆盖已登记车辆</t>】：</b>导入已存在的车牌时，车位和车辆信息将以最后导入的为准；<b>若不勾选</b>，车牌号或车位已存在时，<b>不会重复写入和更新</b>；<br />
                    4、<b>勾选【<t style="color:#d9534f;">更新场内订单</t>】：</b>若导入车辆已入场，则按最新导入的车牌类型为准，并且车辆的入场时间修改为当前操作时间，请谨慎设置！！！<br />
                    5、<b>勾选【<t style="color:#d9534f;">生成储值车充值记录</t>】：</b>若“余额（储值车）”> 0，生成一条“储值车充值”类型的缴费记录。
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/ajaxfileupload2.js" asp-append-version="true"></script>
    <script>
        var paramCommunityNo = $.getUrlParam("communityNo");
        var comElement = null;
        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'form', 'element'], function () {
            comElement = layui.element;
            pager.init();
        });
    </script>
    <script>
        var index = parent.layer.getFrameIndex(window.name);
        function chooseFile(iptFile) {
            $("#iptFilePath").val(iptFile.value);
        }
        function chooseFile2(iptFile) {
            var Count = iptFile.files.length || 0;
            $("#iptFilePath2").val(Count + " 张");
        }
        function ajaxFileUpload(ctNo, replacecar, replaceorder, createpayorder) {
            $("#Import").attr("disabled", true);
            layer.msg("耗时较长,请等待..", { icon: 16, time: 0 });
            $.ajaxFileUpload({
                url: '/Owner/Upload', //用于文件上传的服务器端请求地址
                type: 'post',
                data: { replacecar: replacecar, replaceorder: replaceorder, createpayorder: createpayorder }, //此参数非常严谨，写错一个引号都不行
                secureuri: false, //一般设置为false
                fileElementId: ['inputFile', 'inputFile2'], //文件上传空间的id属性
                dataType: 'json', //返回值类型 一般设置为json
                success: function (data) //服务器成功响应处理函数
                {

                    if (data.Success) {
                        layer.open({
                            content: data.Message
                            , btn: ["我知道了"]
                            , yes: function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            }
                        });
                    } else {
                        var msg = data.Message;
                        var retMsg = msg.replace(new RegExp("&lt;br/&gt;", "gm"), "<br/>");
                        layer.open({ content: retMsg });
                        return;
                    }
                },
                complete: function () {
                    $("#iptFilePath").val("");
                    $("#inputFile").val("")
                    $("#Import").attr("disabled", false);
                },
                error: function (data, status, e) //服务器响应失败处理函数
                {
                    console.log("[" + e.message + "]" + data.responseText)
                    layer.msg(status);
                }
            });
            return false;
        }

        var pager = {
            init: function () {
                this.bindSelect();
                this.bindEvent();
            },
            bindSelect: function () {

                layui.form.render("select");
            },
            bindEvent: function () {
                $("#Import").click(function () {
                    var ctNo = $("#Community_No").val();
                    if ($("#inputFile").val() == "") { layer.msg("请选择文件", { icon: 0 }); return; }

                    var replacecar = $("#replacecar").is(":checked");
                    var replaceorder = $("#replaceorder").is(":checked");
                    var createpayorder = $("#createpayorder").is(":checked");

                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#Import").attr("disabled", true);

                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "请确认：" + (replacecar ? "已勾选【覆盖已登记车辆】，" : "未勾选【覆盖已登记车辆】，") + (replaceorder ? "已勾选【更新场内订单】，" : "未勾选【更新场内订单】，") + (createpayorder ? "已勾选【生成储值车充值记录】，" : "未勾选【生成储值车充值记录】，") + "确定导入吗？",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $("#Import").attr("disabled", true);

                            ajaxFileUpload(ctNo, replacecar, replaceorder, createpayorder);

                        },
                        btn2: function () { $("#Import").removeAttr("disabled"); },
                        end: function () {
                            $("#Import").removeAttr("disabled");
                        }
                    })


                });
                $("#Cancel").click(function () {
                    parent.layer.close(index);
                });
                $("#download-temp").click(function () {
                    this.href = "../../Data/car_template.xlsx";
                });
            }
        };
    </script>
</body>
</html>