﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row { margin-bottom: 15px; }
        span.ss { font-size: 12px; text-align: justify; word-break: break-all; color: #888; background-color: lemonchiffon; width: calc(100% - 10px); float: left; padding: 3px 5px; }
        .layui-input[readonly] { background-color: initial !important; }

        .model_input { border: none !important; outline: none; width: 50px; padding: 0px 0px; height: 95%; text-align: center; color: #0e0e0e; }
        .model_text { height: 38px; border: 1px solid #e6e6e6; padding: 2px; border-radius: 2px; resize: both; box-sizing: border-box; max-width: 95%; }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck" class="layui-form">

            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">IP地址</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="model_text">
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_IP1" name="UDPDevice_IP1" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_IP2" name="UDPDevice_IP2" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_IP3" name="UDPDevice_IP3" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_IP4" name="UDPDevice_IP4" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>


            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">子网掩码</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="model_text">
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceSubNet1" name="UDPDevice_DeviceSubNet1" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceSubNet2" name="UDPDevice_DeviceSubNet2" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceSubNet3" name="UDPDevice_DeviceSubNet3" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceSubNet4" name="UDPDevice_DeviceSubNet4" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">默认网关</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="model_text">
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceGateway1" name="UDPDevice_DeviceGateway1" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceGateway2" name="UDPDevice_DeviceGateway2" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceGateway3" name="UDPDevice_DeviceGateway3" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceGateway4" name="UDPDevice_DeviceGateway4" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>

            <div class="layui-row">
                <div class="layui-col-xs3 edit-label ">DNS服务器</div>
                <div class="layui-col-xs7 edit-ipt-ban">
                    <div class="model_text">
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceDNS1" name="UDPDevice_DeviceDNS1" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceDNS2" name="UDPDevice_DeviceDNS2" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceDNS3" name="UDPDevice_DeviceDNS3" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                        <div style="display:inline-block;height:100%">
                            .
                        </div>
                        <div style="display:inline-block;height:100%">
                            <input type="text" id="UDPDevice_DeviceDNS4" name="UDPDevice_DeviceDNS4" autocomplete="off" class="layui-input model_input" onkeyup="value=value.replace(/[^\0-9]/g,'')" onblur="checkDate(this,255);" onkeypress="Keypress(this)" maxlength="3">
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs3 edit-label">&nbsp;</div>
            <div class="layui-col-xs7 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        myVerify.init();

        var layuiForm = null;
        layui.use(["form", "laydate"], function () {
            layuiForm = layui.form;
            pager.init()
        });


        function formatIPAddress(input) {
            // 从输入框中获取原始IP地址
            let rawValue = input.value;

            // 删除所有非数字和点的字符
            let formattedValue = rawValue.replace(/[^\d.]/g, '');

            // 将点插入每个输入位置之间
            formattedValue = formattedValue.replace(/(\d{1,3})?(\.(\d{1,3})?)?(\.(\d{1,3})?)?(\.(\d{1,3})?)?/, function (match, p1, p2, p3, p4, p5, p6, p7, p8) {
                let parts = [p1, p3, p5, p7];
                for (let i = 0; i < parts.length; i++) {
                    if (parts[i] == undefined) {
                        parts[i] = '';
                    }
                }
                return parts.join('.');
            });

            // 将格式化后的IP地址写回输入框
            input.value = formattedValue;
        }


    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramData = decodeURIComponent($.getUrlParam("data"));
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            ip: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {
                var model = JSON.parse(paramData);
                var UDPDevice_IP = pager.ip = model.UDPDevice_IP;
                var UDPDevice_DeviceSubNet = model.UDPDevice_DeviceSubNet;
                var UDPDevice_DeviceGateway = model.UDPDevice_DeviceGateway;
                var UDPDevice_DeviceDNS = model.UDPDevice_DeviceDNS;
                pager.setInputValue("UDPDevice_IP", UDPDevice_IP); console.log(UDPDevice_IP)
                pager.setInputValue("UDPDevice_DeviceSubNet", UDPDevice_DeviceSubNet);
                pager.setInputValue("UDPDevice_DeviceGateway", UDPDevice_DeviceGateway);
                pager.setInputValue("UDPDevice_DeviceDNS", UDPDevice_DeviceDNS);
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        return data;
                    });
                    $("#Save").attr("disabled", true);
                    $.getJSON("UpdateDeviceIp", { sourceIp: pager.ip, jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                window.parent.pager.bindData(1);
                                window.parent.pager.GetCalendar();
                            });
                        } else {
                            layer.msg(json.msg, { icon: 0 });
                            $("#Save").removeAttr("disabled");
                        }
                    });
                });

            },
            setInputValue: function (name, d) {
                if (d != "" && d != undefined && d != null) {
                    var codeList = d.split(".");
                    if (codeList.length == 4) {
                        $("#" + name + "1").val(codeList[0]);
                        $("#" + name + "2").val(codeList[1]);
                        $("#" + name + "3").val(codeList[2]);
                        $("#" + name + "4").val(codeList[3]);
                    }
                }
            }
        };

        function checkDate(obj, max_num) {
            if (obj.value > max_num || obj.value < 0) {
                window.event.keyCode = 0;

                obj.value = max_num;
                obj.focus();
                obj.select();
            }//end if
        }
    </script>
</body>
</html>
