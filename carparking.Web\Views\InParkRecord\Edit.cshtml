﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>停车支付</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        span { color: black; line-height: 33px; }
        label { line-height: 15px; }
        .file { margin-right: 0px; }
        .alert { margin-bottom: 0px; }
        .layui-col-xs4 { padding-right: 0px; }
        .ibox-content { padding: 15px; }
        .layui-row { padding-bottom: 15px; }
        input.rmb { padding-right: 2rem; position: relative; }
        span.rmb { position: absolute; width: 2rem; right: 1px; top: 1px; height: calc(100% - 2px); cursor: pointer; line-height: 38px; text-align: center; background-color: #fff; color: #ff6a00; border-top-right-radius: 2px; border-bottom-right-radius: 2px; }
        span.rmb:hover { background-color: #d0d0d0; }
        span.rmb:active { background-color: #aaaaaa; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal layui-form">
            <div class="layui-col-xs5" id="verifyCheck">
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">车场名称：</span></div>
                    <div class="layui-col-xs7">
                        <span id="Parking_Name"></span>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">入场时间：</span></div>
                    <div class="layui-col-xs7">
                        <input class="layui-input rmb v-null" id="ParkOrder_EnterTime" name="ParkOrder_EnterTime" readonly disabled />
                        <span class="rmb fa fa-edit" onclick="IsWriteEnterTime()"></span>
                    </div>
                </div>

                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">车牌类型：</span></div>
                    <div class="layui-col-xs7">
                        <select data-placeholder="车牌颜色" class="layui-input v-null" id="ParkOrder_CarCardType" name="ParkOrder_CarCardType">
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">车牌号码：</span></div>
                    <div class="layui-col-xs7">
                        <input type="text" placeholder="车牌号" class="layui-input v-null" id="ParkOrder_CarNo" name="ParkOrder_CarNo" maxlength="8">
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">订单状态：</span></div>
                    <div class="layui-col-xs7">
                        <select data-placeholder="订单状态" class="form-control chosen-select " id="ParkOrder_StatusNo" name="ParkOrder_StatusNo" lay-search>
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs4"><span class="control-label">订单备注：</span></div>
                    <div class="layui-col-xs7">
                        <input type="text" placeholder="订单备注" class="layui-input" id="ParkOrder_Remark" name="ParkOrder_Remark" maxlength="150">
                    </div>
                </div>
                <div class="layui-row" style="color:red;text-align:justify;background-color:#f2f2f2;padding:5px 10px;margin-bottom:15px;">
                    <p>1、若修改后的车牌号在场内已存在停车记录，将会提示【车牌号】已在场内。</p>
                    <p>2、若修改入场时间、车牌类型，将不会自动上传，请在【出入场记录】点击【上传云平台】按钮进行上传。</p>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs6 layui-col-xs-offset4">
                        <button id="Save" class="btn btn-primary" type="button"><i class="fa fa-check"></i> 保存</button>&nbsp;
                        <button id="Cancel" class="btn btn-warning" type="button"><i class="fa fa-times"></i> 取消</button>
                    </div>
                </div>
            </div>

            <div class="layui-col-xs7">
                <div class="alert alert-info">
                    提示：点击图片查看大图
                </div>
                <div class="file">
                    <a target="_blank">
                        <img id="ParkOrder_EnterImgPath" src="../../Static/img/nophoto.jpg" onerror="src='../../Static/img/nophoto.jpg'" style="width: 100%; max-height: 340px;" />
                    </a>
                </div>
            </div>
        </div>
    </div>

    @*{{# if (item.Visible==1){}} <option  value="{{ item.Name }}" {{# if (item.Sel==1){}} selected {{# } }}>{{ item.Value }}</option>{{# } }}*@
    <script type="text/x-jquery-tmpl" id="tempstatus">
        {{if Visible==1}}
             {{if Sel==1}}
              <option value="${Value}" selected>${Name}</option>
              {{else}}
               <option value="${Value}">${Name}</option>
             {{/if}}
         {{/if}}
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        var now = new Date();
        layui.use(['element', 'form'], function () {
            pager.init();
        });

        var index = parent.layer.getFrameIndex(window.name);
        var ParkOrder_No = decodeURIComponent($.getUrlParam("ParkOrder_No"));
        var pager = {
            ParkStatus: [{ Name: "已入场", Value: "200", Visible: 1, Sel: 0 }, { Name: "已出场", Value: "201", Visible: 1, Sel: 0 }, { Name: "预入场", Value: "199", Visible: 1, Sel: 0 },
            { Name: "自动关闭", Value: "202", Visible: 1, Sel: 0 }, { Name: "场内关闭", Value: "203", Visible: 1, Sel: 0 }, { Name: "欠费出场", Value: "204", Visible: 1, Sel: 0 }, { Name: "预出场", Value: "0", Visible: 1, Sel: 0 }],
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                this.bindData();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            //if (d.CarCardType_Type != 5 && d.CarCardType_Type != 6) {
                            //}
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#ParkOrder_CarCardType").append(option)
                            layui.form.render("select");
                        });
                    }
                }, "json");
                _DATE.bind(layui.laydate, ["ParkOrder_EnterTime"], { type: "datetime", range: true });
            },
            //数据绑定
            bindData: function () {
                layer.msg('加载中...', { icon: 16, time: 0 });
                if (ParkOrder_No != null) {
                    $.ajax({
                        type: 'post',
                        url: 'GetParkOrderByNo',
                        dataType: 'json',
                        data: { ParkOrder_No: ParkOrder_No },
                        success: function (json) {
                            layer.closeAll();
                            if (json.success) {
                                var model = json.data.model;
                                var parking = json.data.parking;
                                $("#verifyCheck").fillForm(model, function (data) { });
                                layui.form.render("select");
                                if (model.ParkOrder_EnterImgPath != null && model.ParkOrder_EnterImgPath != "") {
                                    var imgsrc = decodeURIComponent(model.ParkOrder_EnterImgPath);
                                    $("#ParkOrder_EnterImgPath").attr("src", imgsrc);
                                    $("#ParkOrder_EnterImgPath").parent().attr("href", imgsrc);
                                }
                                $("#ParkOrder_EnterTime").html(model.ParkOrder_EnterTime);
                                $("#Parking_Name").html(parking.Parking_Name);

                                if (model.ParkOrder_StatusNo == 199) {
                                    $.each(pager.ParkStatus, function (k, v) {
                                        if (v.Value == 199 || v.Value == 200) { v.Visible = 1; } else { v.Visible = 0; }
                                    });
                                } else if (model.ParkOrder_StatusNo == 200) {
                                    $.each(pager.ParkStatus, function (k, v) {
                                        if (v.Value == 200 || v.Value == 201) { v.Visible = 1; } else { v.Visible = 0; }
                                    });
                                } else {
                                    $.each(pager.ParkStatus, function (k, v) {
                                        if (v.Value == model.ParkOrder_StatusNo) { v.Visible = 1; } else { v.Visible = 0; }
                                    });
                                }
                                $("#ParkOrder_StatusNo").html($("#tempstatus").tmpl(pager.ParkStatus));
                                layui.form.render("select");
                                //检测车牌是否为月租车

                            } else {
                                layer.msg('加载失败：' + json.msg, { icon: 5 });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('订单ID无效', { icon: 0 });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.closeAll(); });
                $("#Save").click(function () {
                    //验证表单
                    if (!myVerify.check()) return;
                    if (new Date().getTime() - new Date($("#ParkOrder_EnterTime").val()).getTime() < 0) {
                        layer.msg('入场时间不能大于当前时间');
                        return;
                    }
                    $("#Save").attr("disabled", true);
                    layer.msg('保存中...', { icon: 16, time: 0 });

                    var param = {
                        ParkOrder_No: ParkOrder_No
                        , ParkOrder_CarNo: $("#ParkOrder_CarNo").val()
                        , ParkOrder_CarCardType: $("#ParkOrder_CarCardType").val()
                        , ParkOrder_EnterTime: $("#ParkOrder_EnterTime").val()
                        , ParkOrder_StatusNo: $("#ParkOrder_StatusNo").val()
                        , ParkOrder_Remark: $("#ParkOrder_Remark").val()
                    };
                    $.post("UpdateCarNo", { jsonModel: JSON.stringify(param) }, function (json) {
                        if (json.success) {
                            layer.msg("保存成功", { time: 1500 }, function () {
                                window.parent.pager.bindData(window.parent.pager.pageIndex);
                            });
                        } else {
                            layer.msg(json.msg);
                            $("#Save").removeAttr("disabled");
                        }
                    }, "json");
                });

                //检测车牌是否为月租车

            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) { });
                s_carno_picker.init("ParkOrder_CarNo", function (text, carno) {
                    if (s_carno_picker.eleid == "ParkOrder_CarNo") {
                        $("#ParkOrder_CarNo").val(carno.join(''));

                        $.post("GetCarInfo", { carno: carno.join('') }, function (json) {
                            if (json.success && json.data != null) {
                                var carcardtype = $("#ParkOrder_CarCardType").val();
                                if (carcardtype != json.data.Car_TypeNo) {
                                    $("#ParkOrder_CarCardType").val(json.data.Car_TypeNo);
                                    layui.form.render("select");
                                    layer.msg("温馨提示：【" + carno.join('') + "】属于已登记车辆，车牌类型：" + json.data.CarCardType_Name + "!", { icon: 0, btn: ['知道了'], time: 0 });
                                }
                            }
                        }, "json");
                    }
                }, "web").bindkeyup();
            }
        };

        var IsWriteEnterTime = function () {
            $("#ParkOrder_EnterTime").removeAttr("disabled");
            $("#ParkOrder_EnterTime").click();
        }
    </script>
</body>
</html>
