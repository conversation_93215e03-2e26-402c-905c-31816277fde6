﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增与编辑</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        .layui-row {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div style="overflow:hidden;height:0;">
        <!--防止浏览器保存密码后自动填充-->
        <input type="password" />
        <input type="text" />
    </div>
    <div class="ibox-content">
        <div id="verifyCheck">
            <div class="layui-row form-group"></div>
            <div class="layui-row">
                <div class="layui-col-xs4 edit-label ">特殊车辆名</div>
                <div class="layui-col-xs6 edit-ipt-ban">
                    <input type="text" class="layui-input v-null v-minlen" data-minlen="2" maxlength="32" placeholder="长度限制(2~32)" 
                           id="SpecialCar_Name" name="SpecialCar_Name" onkeyup="this.value=this.value.replace(/[ ]/g,'')" />
                </div>
                <div class="layui-col-xs1 red-mark">*</div>
            </div>
        </div>

        <div class="hr-line-dashed"></div>
        <div class="layui-row">
            <div class="layui-col-xs4 edit-label">&nbsp;</div>
            <div class="layui-col-xs8 edit-ipt-ban">
                <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>保存</t></button>
                <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>
            </div>
        </div>
    </div>
   
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/chosen/chosen.jquery.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script>
        myVerify.init();
        layui.use(["form"], function () {
            pager.init()
        });
    </script>
    <script>
        var paramAct = $.getUrlParam("Act");
        var paramNo = $.getUrlParam("SpecialCar_No");
        var index = parent.layer.getFrameIndex(window.name);
        //parent.layer.iframeAuto(index); //弹层自适应大小
        var pager = {
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //数据绑定
            bindData: function () {
                if (paramAct == "Update") {
                    $.getJSON("GetDetail", { SpecialCar_No: paramNo }, function (json) {
                        if (json.Success) {
                            $("#verifyCheck").fillForm(json.Data, function (data) { });                           
                        }
                    });
                }
            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(index); });
                $("#Save").click(function () {
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var param = $("#verifyCheck").formToJSON(true, function (data) {
                        data.SpecialCar_Major = $("#SpecialCar_Major").val();
                        data.SpecialCar_Remark = $("#SpecialCar_Remark").val();
                        return data;
                    });

                    $("#Save").attr("disabled", true);
                    if (paramAct == "Add") {
                        $.getJSON("Add", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                    else if (paramAct == "Update") {
                        param.SpecialCar_No = paramNo;
                        $.getJSON("Update", { jsonModel: JSON.stringify(param) }, function (json) {
                            if (json.Success) {
                                layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                    window.parent.pager.bindData(1);
                                });
                            } else {
                                layer.msg(json.Message, { icon: 0 });
                                $("#Save").removeAttr("disabled");
                            }
                        });
                    }
                });

            },
        };
    </script>
</body>
</html>
