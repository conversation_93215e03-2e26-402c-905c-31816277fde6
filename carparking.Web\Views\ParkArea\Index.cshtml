﻿@using TcpConnPools.Camera
@using carparking.DirectCloudMQTT
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>车场区域</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { width: 100%; height: 100%; overflow-x: hidden; }
        body { background-color: #ecf0f5; font-family: 'Microsoft YaHei'; }
        .fa { margin: 7px 4px; float: left; font-size: 16px; }

        .layui-card { margin: 15px; }
        .padding1rem { border-bottom: 1px solid rgba(0,0,0,0.1); padding: 1rem 0; }
        .padding1rem:last-child { border: 0; }
        .layui-table-cell .layui-btn + .layui-btn { margin-left: 1px; }
        .layui-table-tool-temp { padding-right: 30px; }

        #successMessage { height: 100%; }
        .successp { font-size: 1.8rem; font-weight: 600; color: #fff; text-align: center; background-color: #37cf1e; padding: 1rem; }
        .device-list { max-height: 180px; overflow-y: auto; }
        .device-item { display: flex; justify-content: space-between; padding: 10px; border-bottom: 1px solid #e2e2e2; }
        .device-status { font-weight: bold; }
        .success { color: green; }
        .fail { color: red; }

        .layui-layer-title { font-weight: 600; }

        .resend-btn { position: absolute; bottom: 0; background: #f2f2f2; text-align: center; width: 100%; padding: 10px 0px; }
        .resend-btn button { background-color: #ff5722; color: white; border: none; padding: 10px 20px; cursor: pointer; border: 0; margin-right: 10px; border-radius: 4px; }
        .resend-btn button:hover { background-color: #e64a19; }
        .resend-btn button:disabled { background-color: #bbb5b3; }
        #Cancel { background-color: #FFB800 !important; }
    </style>
</head>
<body class="animated fadeInRight">
    <div class="layui-card">
        <div class="layui-card-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场配置</cite></a>
                <a><cite>区域管理</cite></a>
            </div>
        </div>
        <div class="layui-card-body">
            <table class="layui-table" id="com-table-base" lay-filter="com-table-base">
            </table>
            @if (ViewBag.SentryMode != "2")
            {
                <script type="text/html" id="toolbar_btns">
                    <div class="layui-btn-container">
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Add" id="Add">
                            <i class="fa fa-plus"></i><t>新增</t>
                        </button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Update" id="Update">
                            <i class="fa fa-edit"></i><t>编辑</t>
                        </button>
                        <button class="layui-btn layui-btn-sm layui-hide" lay-event="Delete" id="Delete">
                            <i class="fa fa-trash-o"></i><t>删除</t>
                        </button>
                    </div>
                </script>
            }
        </div>
    </div>

    <div id="successMessage" style="display: none;">
        <p class="successp">
            操作成功！
        </p>

        <div class="device-list">
            <!-- 设备列表将动态插入这里 -->
        </div>
        <div class="resend-btn">
            <button id="resendFailed">失败重发</button>
            <button id="Cancel">关闭</button>
        </div>
    </div>
    <script type="text/x-jquery-tmpl" id="type">
        {{# if(d.ParkArea_Type==0){ }}
        <span class="layui-badge layui-bg-cyan">外场</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-green">内场</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="park_online">
        {{if online==1 }}
        <label class="layui-badge layui-bg-blue">已连接</label>
        {{else}}
        <label class="layui-badge">未连接</label>
        {{/if}}
    </script>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        var parkingMode = @(MqttClient.Instance == null ? 0 : CameraGlobal.IsEmergency ? 1 : 0);
        var mode = '@ViewBag.SentryMode';
        var comtable = null;
        layui.use(['table', 'jquery', 'form'], function () {
            var table = layui.table;
            var cols = [[
                { type: 'radio' }
                , { field: 'ParkArea_Type', title: '区域类型', toolbar: "#type" }
                , { field: 'ParkArea_No', title: '区域编号', hide: true }
                , { field: 'ParkArea_Name', title: '区域名称' }
                , { field: 'ParkArea_SpaceNum', title: '车位数量' }
                , { field: 'ParkArea_FName', title: '上级区域' }
                , { field: 'ParkArea_Addtime', title: '注册时间' }
                , { field: 'ParkArea_RemainSpace', title: '余位调整量', hide: true }
            ]];
            cols = tb_page_cols(cols);

            var TableSetting = {
                elem: '#com-table-base'
                , url: '/ParkArea/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: "{}" }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                    pager.bindPower();
                }
            };
            if (mode == "2") delete TableSetting.toolbar;
            comtable = table.render(TableSetting);

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增区域",
                            content: "EditArea?Act=Add",
                            area: getIframeArea(["600px", "500px"]),
                            maxmin: false
                        });
                        parent.top.setScrollTop(document.body, 0);
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        editArea(data[0].ParkArea_No);
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        delArea(data[0].ParkArea_No);
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

        var pager = {
            deviceSend: [],
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = true;
                this.bindSelect();
                this.bindEvent();
                // this.onlinPark();
                $.ajaxSettings.async = false;
            },
            bindSelect: function () {
            },
            bindData: function (index) {
                layer.closeAll();
                comtable.reload({
                    url: '/ParkArea/GetList'
                    , where: {}
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#UpdatePark").click(function () {
                    var page = "EditPark";
                    if (mode == "2") page = "EditCloudPark";
                    layer.open({
                        type: 2, id: 1,
                        title: "编辑车场",
                        content: page,
                        area: getIframeArea(["600px", "550px"]),
                        maxmin: false
                    });
                    parent.top.setScrollTop(document.body, 0);
                });

                $("#RefreshPark").click(function () {
                    $.ajaxSettings.async = true;
                    LAYER_LOADING("正在查询中...");
                    $.post("GetParkingState", {}, function (json) {
                        if (json.success) {
                            $("#Parking_Online").html($("#park_online").tmpl([{ online: 1 }]));
                        } else {
                            $("#Parking_Online").html($("#park_online").tmpl([{ online: 0 }]));
                        }
                        layer.msg("刷新车场连接状态成功", { time: 1000 }, function () { })
                    });
                    $.ajaxSettings.async = false;
                    parent.top.setScrollTop(document.body, 0);
                });


                $("#ReStartCloud").click(function () {
                    $.ajaxSettings.async = true;
                    layer.msg("正在重启中间件...", { icon: 16, time: 0 });
                    $.post("ReStartCloud", {}, function (json) {
                        if (json.success) {
                            layer.msg(json.msg, { icon: 1, time: 1500 });
                        } else {
                            layer.msg(json.msg, { icon: 0, time: 1500 });
                        }
                    });
                    $.ajaxSettings.async = false;
                });

                $("#Pulldown").click(function () {
                    var msg = "";
                    if (parkingMode == 1) {
                        msg = "当前已启用应急模式，确定关闭吗？";
                    } else {
                        msg = "当前未启用应急模式，确定启用吗？";
                    }
                    layer.open({
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: msg,
                        area: ["300px"],
                        yes: function (res) {
                            layer.msg("切换中", { icon: 16, time: 0 });
                            setTimeout(function () {
                                $.post("SwitchParkingMode", {}, function (json) {
                                    if (json.success) {
                                        if (parkingMode == 1) parkingMode = 0; else parkingMode = 1;
                                        $("#SysConfig_CloudBoxLprEmergency").html(parkingMode == 1 ? '<label class="layui-badge layui-bg-blue">开启</label>' : '<label class="layui-badge layui-bg-cyan">关闭</label>');
                                        if (json.data && json.data != null && json.data.length > 0) {

                                            pager.deviceSend = [];

                                            var msg = (parkingMode == 1 ? "已开启" : "已关闭") + "车场应急模式";
                                            $(".successp").html(msg);
                                            // 显示操作成功提示
                                            layer.open({
                                                type: 1,
                                                title: '为了不影响使用，请等待设备消息发送成功后再操作',
                                                content: $('#successMessage'),
                                                area: ['450px', '350px']
                                            });

                                            var devices = json.data;
                                            var $deviceList = $('.device-list');
                                            $deviceList.empty(); // 清空列表

                                            // 动态生成设备列表项
                                            devices.forEach(function (device) {
                                                var $item = $('<div class="device-item"></div>');
                                                $item.append('<span class="device-name">' + device.Device_IP + '</span>');
                                                $item.append('<span class="device-name">' + device.Device_Name + '</span>');
                                                var $status = $('<span data-value="' + device.Device_No + '" class="device-status">正在发送消息...</span>');
                                                $item.append($status);
                                                $deviceList.append($item);
                                            });

                                            setTimeout(async function () {
                                                $.ajaxSettings.async = false;
                                                const deviceNosString = devices.map(device => device.Device_No).join(',');
                                                $.post("SendDeviceEnableClouds", { deviceNos: deviceNosString, parkingMode: parkingMode }, function (json) {
                                                    if (json.success) {
                                                        _ = pager.checkAllStatus(devices)
                                                    } else {
                                                        pager.deviceSend = devices;
                                                        $(".device-status").text('消息发送失败').removeClass("success").addClass('fail');
                                                    }
                                                });

                                            }, Math.random() * 2000 + 1000); // 随机时间1-3秒

                                        } else {
                                            layer.msg("切换成功", { icon: 1, time: 1500 }, function () { });
                                        }
                                    } else
                                        layer.msg(json.msg, { icon: 0, btn: ['确定'], time: 0, end: function () { } });
                                });
                            }, 10);
                        },
                        btn2: function () { }
                    })
                });

                $('#resendFailed').on('click', function () {
                    $('#resendFailed').attr("disabled", true);
                    setTimeout(async function () {
                        $.ajaxSettings.async = false;

                        pager.deviceSend.forEach(function (device) {
                            $(".device-status[data-value=" + device.Device_No + "]").text('正在发送消息...').removeClass("success").removeClass('fail');
                        });

                        const deviceNosString = pager.deviceSend.map(device => device.Device_No).join(',');
                        $.post("SendDeviceEnableClouds", { deviceNos: deviceNosString, parkingMode: parkingMode }, function (json) {
                            if (json.success) {
                                _ = pager.checkAllStatus(pager.deviceSend)
                            } else {
                                pager.deviceSend = pager.deviceSend;
                                pager.deviceSend.forEach(function (device) {
                                    $(".device-status[data-value=" + device.Device_No + "]").text('消息发送失败').removeClass("success").addClass('fail');
                                })
                            }
                        });
                    }, Math.random() * 2000 + 1000); // 随机时间1-3秒
                    setTimeout(function () { $('#resendFailed').removeAttr("disabled"); }, 2000);
                });
                $('#Cancel').on('click', function () {
                    layer.closeAll();
                });
            },
            onlinPark: function () {
                $.ajaxSettings.async = true;
                LAYER_LOADING("正在查询中...");
                $.post("GetParkingState", {}, function (json) {
                    if (json.success) {
                        $("#Parking_Online").html($("#park_online").tmpl([{ online: 0 }]));
                        if (json.data != null) {
                            $("#Parking_Online").html($("#park_online").tmpl([{ online: json.data.status }]));
                            $(".cloudboxlpremergency").removeClass("layui-hide");
                            $("#SysConfig_CloudBoxLprEmergency").html(json.data.isEmergency == "1" ? '<label class="layui-badge layui-bg-blue">开启</label>' : '<label class="layui-badge layui-bg-cyan">关闭</label>');
                            $("#SysConfig_ConnMode").html(json.data.ConnMode == "1" ? '<label class="layui-badge layui-bg-green">协同模式</label>' : '<label class="layui-badge layui-bg-blue">开放模式</label>');
                        } else {
                            $(".cloudboxlpremergency").addClass("layui-hide");
                            $(".connMode").addClass("layui-hide");
                        }
                    } else {
                        $("#Parking_Online").html($("#park_online").tmpl([{ online: 0 }]));
                        $(".cloudboxlpremergency").addClass("layui-hide");
                        $(".connMode").addClass("layui-hide");
                    }
                    layer.msg("刷新车场连接状态成功", { time: 1000 }, function () { })
                });
                $.ajaxSettings.async = false;
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) {
                    $("#resendFailed,#Cancel").removeClass("layui-hide")
                    console.log(pagePower)
                });
            },
            queryDeviceStatus: async function (device) {
                // 查询设备状态的方法
                try {
                    const response = await $.post("QueryDeviceStatus", { deviceNo: device.Device_No });
                    if (response.success) {
                        if (response.data == 2) {
                            var index = pager.deviceSend.indexOf(device);
                            if (index < 0) {
                                pager.deviceSend.push(device);
                            }
                            $(".device-status[data-value=" + device.Device_No + "]").text('消息发送失败').removeClass("success").addClass('fail');
                        } else {
                            var index = pager.deviceSend.indexOf(device);
                            if (index > -1) {
                                pager.deviceSend.splice(index, 1); // 删除指定的元素
                            }
                            $(".device-status[data-value=" + device.Device_No + "]").text('消息发送成功').removeClass("fail").addClass('success');
                        }
                        return true;  // 查询成功，返回 true
                    } else {

                        return false; // 查询失败，返回 false
                    }
                } catch (error) {
                    return false; // 请求出错，返回 false
                }
            },
            checkAllStatus: async function (devices) {
                let success = false;
                var newArray = devices.slice(); // 创建一个 devices 数组的副本

                while (newArray.length > 0 && !success) {
                    for (let i = 0; i < newArray.length; i++) {
                        let device = newArray[i];
                        let ret = await pager.queryDeviceStatus(device);

                        if (ret) {
                            newArray.splice(i, 1); // 删除当前设备
                            i--; // 调整索引，因为数组长度变短
                        }

                        await new Promise(resolve => setTimeout(resolve, 500));
                    }

                    // 如果所有设备都查询成功，可以将 success 设为 true 来退出 while 循环
                    if (newArray.length === 0) {
                        success = true;
                    }
                }
            }
        }

        var editArea = function (areano) {
            layer.open({
                type: 2, id: 1,
                title: "编辑区域",
                content: "EditArea?Act=Update&ParkArea_No=" + areano,
                area: getIframeArea(["600px", "500px"]),
                maxmin: false
            });
            parent.top.setScrollTop(document.body, 0);
        }

        var delArea = function (areano) {
            LAYER_OPEN_TYPE_0("确定删除停车区域?", res => {
                LAYER_LOADING("处理中...");
                $.post("DelParkArea", { ParkArea_No: areano }, function (json) {
                    if (json.success) {
                        window.pager.bindData(1);
                        layer.msg("删除成功", { time: 1000 }, function () { })
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            }, res => {
            })
            parent.top.setScrollTop(document.body, 0);
        }

    </script>
</body>
</html>