﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>车牌识别</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="keywords" content="">
    <meta name="description" content="">

    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/plugins/chosen/chosen.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/plugins/layui/css/modules/layer/default/layer.css" rel="stylesheet" />
    <style>
        h3, span { color: black; font-weight: normal !important; }
        .ibox-content { padding-top: 0px !important; }
        html { background-color: #8be1ff !important; }
        body { margin-top: 0; height: 99%; }
        .form-group { margin-bottom: 10px; }
        h3 { padding-top: 5px; }
        .layui-card-header { font-size: 18px; }
        .layui-input-block { min-width: 150px; }
        .layui-tab-title { background-color: #5868e0 !important; }
        .layui-form-item .layui-form-checkbox { margin-top: -1px !important; }
        .layui-form-checkbox { height: 39px !important; line-height: 39px !important; }
        .layui-form-checkbox i { position: absolute; right: 0; top: 0; width: 30px; height: 36px; border: 1px solid #d7eaeb; border-left: none; border-radius: 0 2px 2px 0; font-size: 20px; text-align: center; }
        .layui-card-header .layui-icon { top: 30%; margin-top: -12px; height: 37px; right: 0px !important; }
        input#carno { background-color: rgb(249 242 242 / 100%) !important; }
        input#carno:disabled { background-color: rgb(0 0 0) !important; color: #fff; }
        input#time:disabled { background-color: rgb(0 0 0) !important; color: #fff; }
        .testCard { max-width: 700px; min-height: 310px; box-shadow: 1px 1px 4px 1px #1ab394; margin: 8px; }
        .layui-elem-quote { margin-bottom: 0px !important; border-left: 2px solid #1ab394 !important; }
        .ibox-content { background-color: #90d8f1 !important; height: 100%; }

        .layui-table tr { height: 45px !important; line-height: 45px !important; padding: 0 10px; transition: box-shadow 0.2s ease; }
        .layui-table tr:hover { box-shadow: 0 5px 15px #d8cccc; font-weight: 900 !important; }
        thead > tr { background-color: #01aaed !important; color: #fff !important; }
        .layui-this { border: 1px solid #cad5cb; background-color: #01aaed; color: #fff !important; }
        .cardBottom { text-align: center; }
        .layui-tab-title > li { color: #fff; }

        .layui-card-body > .layui-row { margin-top: 20px; }
        .edit-label { color: #100f0f !important; }
        input#time { min-width: 150px; }
        .layui-form-select dl { max-height: 200px; }

        .layui-this { border: 1px solid #cad5cb; background-color: #01aaed; color: #fff !important; }
        .businessCache { position: absolute; right: 15px; top: 60px; padding: 2px 10px; background-image: linear-gradient(to right, #7dc7e3, #c8cbe5); z-index: 9999; color: red; /* border-radius: 5px 0px 0px 5px; */ box-shadow: 0 5px 15px #d8cccc; }
        .layui-table-header { width: 100%; }
        .layui-table-header table,
        .layui-table-header thead,
        .layui-table-header tr { width: 100% !important; }
        th > .layui-table-cell { width: 100% !important; }

        .msg { font-size: 13px; position: fixed; }
        .layui-card { background-color: rgb(255 255 255 / 30%) !important; }
        .tip { margin-top: 20px; }
        .tip a { color: #666; }
        .tiptitle { color: #fff; font-size: 16px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="layui-row form-group"></div>

        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="type1 layui-this">车牌识别</li>
                <li class="type2 simple">道闸控制板</li>
                <li class="type3 simple">缓存校对</li>
            </ul>
            <div class="layui-tab-content layui-form">
                @* //车牌识别 *@
                <div class="layui-tab-item layui-show">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-row">
                                        <div class="layui-col-sm2 edit-label"><label>车牌号码</label></div>
                                        <div class="layui-col-sm3 edit-ipt-ban">
                                            <div class="input-group">
                                                <input type="text" class="layui-input" id="carno" name="carno" maxlength="18" />
                                                <span class="input-group-btn" style="vertical-align: top;">
                                                    <input type="checkbox" name="carnoAuto" id="carnoAuto" title="自动">
                                                </span>
                                            </div>
                                        </div>
                                        <div class="layui-col-sm2 edit-label"><label>识别时间</label></div>
                                        <div class="layui-col-sm3 edit-ipt-ban">
                                            <div class="input-group">
                                                <input type="text" class="layui-input" id="time" name="time" value="@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")" disabled />
                                                <span class="input-group-btn" style="vertical-align: top;">
                                                    <input type="checkbox" name="timeAuto" id="timeAuto" title="自动" checked>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <div class="layui-col-sm2 edit-label"><label>识别车道</label></div>
                                        <div class="layui-col-sm3 edit-ipt-ban">
                                            <select data-placeholder="识别车道" class="form-control chosen-select " id="passwayno" name="passwayno" lay-search style="max-height:300px;">
                                                <option value="">识别车道</option>
                                            </select>
                                        </div>
                                        <div class="layui-col-sm2 edit-label"><label>车牌颜色</label></div>
                                        <div class="layui-col-sm3 edit-ipt-ban">
                                            <select data-placeholder="车牌颜色" class="form-control chosen-select " id="cartype" name="cartype" lay-search style="max-height:300px;">
                                                <option value="">车牌颜色</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-card-header  layui-form cardBottom">
                                    <div class="layui-row">
                                        <div class="layui-col-sm12">
                                            <button class="btn btn-primary" id="BathSave"><i class="fa fa-check"></i> <t>批量识别</t></button>
                                            <button class="btn btn-primary" id="Save"><i class="fa fa-check"></i> <t>单次识别</t></button>
                                            <button class="btn btn-warning" id="Reflash"><i class="fa fa-flash"></i> <t>刷新</t></button>
                                            <button class="btn btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>关闭</t></button>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                    <div class="tip">
                        <t class="tiptitle">❊<a>温馨提示：</a></t><br /> &nbsp;&nbsp;<a>当前工具仅用于调试模拟车牌识别功能，模拟过程中系统正常产生停车订单以及计费信息。 <t style="color:red"> 若手动修改了识别时间进行出场弹窗电子或现金支付，支付时间不会变更，为当前实际服务器时间。</t></a>
                    </div>
                </div>
                @* //道闸控制板 *@
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space30">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <div class="layui-row">
                                        <div class="layui-col-sm2 edit-label"><label>停车订单号</label></div>
                                        <div class="layui-col-sm4 edit-ipt-ban">
                                            <input type="text" class="layui-input" id="parkorderno" name="parkorderno" value="" placeholder="停车订单号" />
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-card-header  layui-form cardBottom">
                                    <div class="layui-row">
                                        <div class="layui-col-sm12">
                                            <button class="btn btn-primary" id="EventAbnormal"><i class="fa fa-check"></i> <t>道闸控制板异常信息上报</t></button>
                                            <button class="btn btn-primary" id="EventFollowing"><i class="fa fa-check"></i> <t>道闸控制板跟车信息上报</t></button>
                                            <button class="btn btn-primary" id="EventAstern"><i class="fa fa-check"></i> <t>道闸控制板倒车信息上报</t></button>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                    <div class="tip">
                        <t class="tiptitle">❊<a>温馨提示：</a></t><br /> &nbsp;&nbsp;<a>当前工具仅用于调试道闸控制板功能，模拟道闸产生的异常信息、倒车或跟车信息的上报过程。</a>
                        @{
                            if (carparking.Config.AppSettingConfig.SentryMode == carparking.Common.VersionEnum.EPSServer && carparking.Config.AppSettingConfig.InstallType == carparking.Common.InstallTypeEnum.LinuxB30BOX)
                            {
                                <br />
                                <t class="tiptitle">❊<a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</a></t>
                                <a>第三方相机授权：@(carparking.BLL.Cache.AppBasicCache.isDeviceAuth ? "已授权" : "未授权")</a>
                            }
                        }
                    </div>
                </div>
                @* //缓存校对 *@
                <div class="layui-tab-item item3">

                    <div class="layui-card layadmin-header">
                        <div class="layui-breadcrumb" lay-filter="breadcrumb">
                            <a><cite>若无输入条件，查询缓存与数据库存在差异的数据；若输入查询条件，则直接查询缓存和数据库的数据</cite></a>
                            <div class="businessCache">@(ViewBag.BusinessCache == 1 ? "岗亭已启用业务缓存" : "岗亭已禁用业务缓存")</div>
                        </div>
                    </div>
                    <div class="layui-tab">
                        <ul class="layui-tab-title">
                            <li class="type1 layui-this">车辆信息</li>
                            <li class="type3 simple">车主信息</li>
                            <li class="type3 simple">访客车信息</li>
                            <li class="type3 simple">商家车信息</li>
                            <li class="type3 simple">黑名单信息</li>
                        </ul>
                        <div class="layui-tab-content layui-form">
                            @* //车辆信息 *@
                            <div class="layui-tab-item layui-show">
                                <div class="layui-row layui-col-space30">
                                    <div class="layui-col-md12">
                                        <div class="layui-card">
                                            <div class="layui-card-header  layui-form">
                                                <div class="test-table-reload-btn" id="searchForm">
                                                    <div class="layui-inline">
                                                        <input class="layui-input" name="Car_CarNo" id="Car_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="SearchCar"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="ClearCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除车辆信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="LoadCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载车辆信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <div class="carmsg msg"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="layui-card-body">
                                                <table class="layui-hide" id="com-table-car" lay-filter="com-table-car"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @* //车主信息 *@
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space30">
                                    <div class="layui-col-md12">
                                        <div class="layui-card">
                                            <div class="layui-card-header  layui-form">
                                                <div class="test-table-reload-btn" id="searchForm_owner">
                                                    <div class="layui-inline">
                                                        <input class="layui-input" name="Owner_Space" id="Owner_Space" autocomplete="off" placeholder="系统车位号" value="" />
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="Search_Owner"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="ClearOwner"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除车主信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="LoadOwner"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载车主信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <div class="ownermsg msg"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="layui-card-body">
                                                <table class="layui-hide" id="com-table-owner" lay-filter="com-table-owner"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @* //访客车信息 *@
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space30">
                                    <div class="layui-col-md12">
                                        <div class="layui-card">
                                            <div class="layui-card-header  layui-form">
                                                <div class="test-table-reload-btn" id="searchFormReserve">
                                                    <div class="layui-inline">
                                                        <input class="layui-input" name="Reserve_CarNo" id="Reserve_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="SearchReserve"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="ClearReserve"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除访客车信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="LoadReserve"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载访客车信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <div class="msg reservemsg"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="layui-card-body">
                                                <table class="layui-hide" id="com-table-reserve" lay-filter="com-table-reserve"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @* //商家车信息 *@
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space30">
                                    <div class="layui-col-md12">
                                        <div class="layui-card">
                                            <div class="layui-card-header  layui-form">
                                                <div class="test-table-reload-btn" id="searchFormBusinessCar">
                                                    <div class="layui-inline">
                                                        <input class="layui-input" name="BusinessCar_CarNo" id="BusinessCar_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="SearchBusinessCar"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="ClearBusinessCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除商家车信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="LoadBusinessCar"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载商家车信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <div class="msg businessCarmsg"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="layui-card-body">
                                                <table class="layui-hide" id="com-table-businessCar" lay-filter="com-table-businessCar"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @* //黑名单信息 *@
                            <div class="layui-tab-item">
                                <div class="layui-row layui-col-space30">
                                    <div class="layui-col-md12">
                                        <div class="layui-card">
                                            <div class="layui-card-header  layui-form">
                                                <div class="test-table-reload-btn" id="searchFormBlackList">
                                                    <div class="layui-inline">
                                                        <input class="layui-input" name="BlackList_CarNo" id="BlackList_CarNo" autocomplete="off" placeholder="车牌号码" value="" />
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="SearchBlackList"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="ClearBlackList"><i class="layui-icon layui-icon-edit inbtn"></i><t>清除黑名单信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <button class="layui-btn" id="LoadBlackList"><i class="layui-icon layui-icon-edit inbtn"></i><t>加载黑名单信息缓存</t></button>
                                                    </div>
                                                    <div class="layui-inline">
                                                        <div class="msg blackListmsg"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="layui-card-body">
                                                <table class="layui-hide" id="com-table-blackList" lay-filter="com-table-blackList"></table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="tip">
                        <t class="tiptitle">❊<a>温馨提示：</a></t><br />&nbsp;&nbsp;<a>当前工具仅用于当前岗亭的缓存业务数据和数据库数据的比对，提供缓存清理以及缓存重载操作。</a>
                    </div>
                </div>



            </div>
        </div>
    </div>

    <!--过期登录-->
    <div>
        <!--功能扩展-自动弹出登录，由全局ajax控制-->
        <div id="modal-form" class="modal fade in" aria-hidden="true" data-backdrop="static" style="margin-top: 12%;">
            <div class="modal-dialog" style="width: 300px;">
                <div class="modal-content" style="height: 230px;">
                    <div class="modal-body">
                        <button type="button" class="close hide" data-dismiss="modal">
                            <span aria-hidden="true">×</span><span class="sr-only">Close</span>
                        </button>
                        <div class="row">
                            <div style="width: 100%; height: 100%;">
                                <h5 class="m-t-none m-b text-center" style="color: #1ab394;padding:10px 0;">
                                    登录超时,请重新登录！
                                </h5>
                                <div class="layui-row" style="padding:5px 20px;">
                                    <input type="text" id="username" placeholder=" 请输入用户名"
                                           class="layui-input text-input" autocomplete="off" />
                                </div>
                                <div class="layui-row" style="padding:5px 20px;">
                                    <input type="password" id="password" placeholder=" 请输入密码"
                                           class="layui-input text-input" autocomplete="off" />
                                </div>
                                <div class="layui-row">
                                    <button type="button" id="btn_Login"
                                            style="width: 80%; margin:20px 10% 0;color:#fff !important;"
                                            data-loading-text="<div style='text-align:center; margin:0 auto;'><div style='display:inline-block;width:45px;'>登录中</div><div class='sk-spinner sk-spinner-three-bounce' style='display:inline-block;width:45px;'><div class='sk-bounce1'></div><div class='sk-bounce2'></div><div class='sk-bounce3'></div></div></div>"
                                            class="layui-btn">
                                        登&nbsp;&nbsp;录
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v20230620" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>
        s_carno_picker.init("carno", function (text, carno) {
            if (s_carno_picker.eleid == "carno") {
                $("#carno").val(carno.join(''));
            }
        }, "web").bindkeyup();

        var carnoAuto = false;
        var timeAuto = true;

        layui.use(['element', 'table', 'form', 'laydate'], function () {
            var table = layui.table;
            layuiForm = layui.form;
            _DATE.bind(layui.laydate, ["time"], { type: "datetime", range: false });


            layuiForm.on('checkbox', function (obj) {
                if (obj.elem.checked == true) {
                    if (obj.elem.id == "carnoAuto") {
                        carnoAuto = true;
                        $("#carno").attr("disabled", true);
                    }
                    if (obj.elem.id == "timeAuto") {
                        timeAuto = true;
                        $("#time").attr("disabled", true);
                    }
                } else {
                    if (obj.elem.id == "carnoAuto") {
                        carnoAuto = false;
                        $("#carno").removeAttr("disabled");
                    }
                    if (obj.elem.id == "timeAuto") {
                        timeAuto = false;
                        $("#time").removeAttr("disabled");
                    }
                }
            });
            pager.init()
        });

        var pager = {
            init: function () {
                this.bindEvent();
                this.bindData();
            },
            bindEvent: function () {

                $("#Cancel").click(function () { parent.layer.closeAll(); });

                $("#Save").click(function () {

                    if (carnoAuto) {
                        $("#carno").val("蒙ZZ" + getend5(5));
                    }

                    if (timeAuto) {
                        $("#time").val(new Date().Format("yyyy-MM-dd hh:mm:ss"));
                    }
                    var cartype = $("#cartype").val();
                    var passwayno = $("#passwayno").val();
                    var carno = $("#carno").val();
                    var time = $("#time").val();
                    $("#Save").attr("disabled", true)
                    $.post("/Monitoring/InCarByPassNo", { passwayno: passwayno, carno: carno, time: time, cartype: cartype, }, function (json) {
                        if (json.success) {
                            layer.msg("识别成功", { icon: 1, time: 1500 }, function () { });
                        } else {
                            layer.alert(json.msg, { icon: 0 });
                        }
                        $("#Save").removeAttr("disabled");
                    }, "json").error(function () {
                        $("#Save").removeAttr("disabled");
                    });
                })

                $("#BathSave").click(function () {
                    layer.prompt({
                        formType: 3,
                        value: '',
                        title: '请输入批量次数（间隔100毫秒执行1次，请注意谨慎使用）',
                        btn: ['确定', '取消'], //按钮，
                        btnAlign: ''
                    }, function (value, index) {
                        // 检查输入是否只包含数字
                        if (/^\d+$/.test(value)) {
                            for (let i = 0; i < value; i++) {
                                setTimeout(function () {
                                    if (carnoAuto) {
                                        $("#carno").val("蒙ZZ" + getend5(5));
                                    }

                                    if (timeAuto) {
                                        $("#time").val(new Date().Format("yyyy-MM-dd hh:mm:ss"));
                                    }

                                    var passwayno = $("#passwayno").val();
                                    var cartype = $("#cartype").val();
                                    var carno = $("#carno").val();
                                    var time = $("#time").val();
                                    $("#Save").attr("disabled", true)
                                    $.post("/Monitoring/InCarByPassNo", { passwayno: passwayno, carno: carno, time: time, cartype: cartype, }, function (json) {
                                        if (json.success) {
                                            layer.msg("识别成功", { icon: 1, time: 1500 }, function () { });
                                        } else {
                                            layer.alert(json.msg, { icon: 0 });
                                        }
                                        $("#Save").removeAttr("disabled");
                                    }, "json").error(function () {
                                        $("#Save").removeAttr("disabled");
                                    });
                                }, 100 * i);
                            }
                            layer.close(index);
                        } else {
                            layer.msg('请输入数字');
                        }


                    });
                })

                $("#EventAbnormal").click(function () {
                    var passwayno = $("#passwayno").val();
                    var carno = $("#carno").val();
                    var time = $("#time").val();
                    var parkorderno = $("#parkorderno").val();
                    $("#EventAbnormal").attr("disabled", true)
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $.post("/Monitoring/EventAbnormal", { passwayno: passwayno, carno: carno, time: time, parkorderno: parkorderno }, function (json) {
                        if (json.success) {
                            layer.msg("发送成功", { icon: 1, time: 1500 }, function () { });
                        } else {
                            layer.alert(json.msg, { icon: 0 });
                        }
                        $("#EventAbnormal").removeAttr("disabled");
                    }, "json").error(function () {
                        $("#EventAbnormal").removeAttr("disabled");
                    });
                })

                $("#EventFollowing").click(function () {
                    var passwayno = $("#passwayno").val();
                    var carno = $("#carno").val();
                    var time = $("#time").val();
                    var parkorderno = $("#parkorderno").val();
                    $("#EventFollowing").attr("disabled", true)
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $.post("/Monitoring/EventFollowing", { passwayno: passwayno, carno: carno, time: time, parkorderno: parkorderno }, function (json) {
                        if (json.success) {
                            layer.msg("发送成功", { icon: 1, time: 1500 }, function () { });
                        } else {
                            layer.alert(json.msg, { icon: 0 });
                        }
                        $("#EventFollowing").removeAttr("disabled");
                    }, "json").error(function () {
                        $("#EventFollowing").removeAttr("disabled");
                    });
                })

                $("#EventAstern").click(function () {
                    var passwayno = $("#passwayno").val();
                    var carno = $("#carno").val();
                    var time = $("#time").val();
                    var parkorderno = $("#parkorderno").val();
                    $("#EventAstern").attr("disabled", true)
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $.post("/Monitoring/EventAstern", { passwayno: passwayno, carno: carno, time: time, parkorderno: parkorderno }, function (json) {
                        if (json.success) {
                            layer.msg("发送成功", { icon: 1, time: 1500 }, function () { });
                        } else {
                            layer.alert(json.msg, { icon: 0 });
                        }
                        $("#EventAstern").removeAttr("disabled");
                    }, "json").error(function () {
                        $("#EventAstern").removeAttr("disabled");
                    });
                })

                $("#Reflash").click(function () {
                    window.location.reload();
                })
            },
            //数据绑定
            bindData: function () {
                $.post("/Monitoring/SltPasswayList3", {}, function (json) {
                    if (json.success && json.data && json.data.length > 0) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.Passway_No + '">' + d.Passway_Name + '</option>';
                            $("#passwayno").append(option);
                        });
                        $("#passwayno").val(json.data[0].Passway_No);
                        layui.form.render("select");
                    } else {
                        layui.form.render("select");
                        // timeOutLogin();
                    }
                }, "json");

                $.post("/Monitoring/SltCarTypeList", {}, function (json) {
                    if (json.success && json.data && json.data.length > 0) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_Name + '">' + d.CarType_Name + '</option>';
                            $("#cartype").append(option);
                        });
                        $("#cartype").val(json.data[0].CarType_Name);
                        layui.form.render("select");
                    } else {
                        layui.form.render("select");
                    }
                }, "json");
            }
        };

    </script>

    <!--登录超时弹出登录框-->
    <script>
        var modal = {
            init: function () {
                $('#modal-form').modal({ show: false });
            }
        }
        var timeOutLogin = function (XMLHttpRequest) {
            layer.closeAll();
            $('#modal-form').modal("show");
        }
        //登录
        $("#btn_Login").click(function () {
            var btn = $(this);
            var username = $("#username").val().trim();
            if (username == null) { layer.tips("您的缓存被清除，请刷新页面登录", '#password', { tips: [3] }); return; }
            var password = $("#password").val().trim();

            $('#btn_Login').button('loading');
            $.post("/Login/ToLogin", { Admins_Account: username, Admins_Pwd: password }, function (data) {
                if (data.Success) {
                    $(".close").click();
                    $("#username").val("");
                    $("#password").val("");
                    btn.button('reset');

                    //判断有跳转打开页面
                    var bta = $("#LoginForm");
                    var re = $(bta).attr("data-reload");
                    var name = $(bta).attr("data-iframe");
                    if (re === "true" && name !== "") {
                        var iframe = $(window.document).find("iframe[name='" + name + "']");
                        $(iframe).attr('src', $(iframe).attr("src")); //刷新该框架，重新加载页面
                    } else {
                        layer.msg("登录成功", { icon: 1, time: 1000 }, function () {
                            window.location.reload();
                        });
                    }

                    $('#modal-form').modal("hide");
                } else {
                    setTimeout(function () {
                        $('#btn_Login').button('reset');
                    }, 500);
                    layer.tips("提示：" + data.Message, '#btn_Login', { tips: [3] });
                }
            }, "json");
        });

        //回车登录
        $("#username").keydown(function (event) {
            if (event.keyCode == 13) { $("#btn_Login").click(); }
        });

        $("#password").keydown(function (event) {
            if (event.keyCode == 13) { $("#btn_Login").click(); }
        });

        $(".outcar_money").keydown(function (event) {
            if (event.keyCode == 13) { $(".broadcast").click(); }
        });

        function getkey(no, e) {
            if (event.keyCode == 13) {
                btns.onOpsClick(no, e,
                    function () {
                        $("input.outcar_money").attr("readonly", true);
                        $(".pwd-edit").removeClass("fa-rotate-left").addClass("fa-pencil");
                    })
            }
        }

        $(function () {
            $("input:text").focus();//Linux输入法触发
        })
    </script>

    <!--车牌生成-->
    <script type="text/javascript">
        var citys = ["京", "津", "晋", "湘", "赣", "沪", "渝", "冀", "辽", "吉", "黑", "苏", "浙", "皖"
            , "闽", "鲁", "豫", "鄂", "青", "粤", "琼", "川", "贵", "云", "陕", "甘", "藏", "桂", "蒙", "宁", "新", "港", "台",
            "澳", "电"
        ];
        //车牌号所在地区
        var area = ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K","M", "N", "P", "Q"]
        //车牌号后5位
        var nums = ["A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "M", "N","P", "Q", "R", "S",
            "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        ]
        //省份生成
        function getcity() {
            var id = Math.floor((Math.random() * 34));
            return citys[id]??"A";
        }
        //地区生成
        function getarea() {
            var id = Math.floor((Math.random() * 17));
            return area[id]??"A";
        }
        //车牌后五位
        function getend5(n) {
            var res = "";
            for (var i = 0; i < n; i++) {
                var id = Math.floor((Math.random() * 36));
                res += (nums[id]??"A")
            }
            return res;
        }

        $("#carno").val("蒙ZZ" + getend5(5));
    </script>

    <!--缓存校对-->
    <script>
        var Power = [];
        var comtable = null;
        var comtableOwner = null;
        var comtableReserve = null;
        var comtableBusinessCar = null;
        var comtableBlackList = null;

        layui.use(['table', 'element', 'form', 'laydate'], function () {

            var table = layui.table;

            //车辆信息
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            var cols = [[
                , { field: 'OldName', title: '缓存车牌号码' }
                , { field: 'OldBeginTime', title: '缓存开始时间', hide: true }
                , { field: 'OldEndTime', title: '缓存结束时间', hide: true }
                , { field: 'OldJson', title: '缓存Json' }
                , { field: 'NewName', title: '数据库车牌号码' }
                , { field: 'NewBeginTime', title: '数据库开始时间', hide: true }
                , { field: 'NewEndTime', title: '数据库结束时间', hide: true }
                , { field: 'NewJson', title: '数据库Json' }
                , {
                    field: '', title: '差异字段', templet: function (d) {
                        if (d.OldJson && d.NewJson && d.NewJson != "" && d.OldJson != "" && d.NewJson != "null" && d.OldJson != "null") {
                            var oldJson = JSON.parse(d.OldJson);
                            var newJson = JSON.parse(d.NewJson);
                            var oldKeys = Object.keys(oldJson);
                            var newKeys = Object.keys(newJson);
                            var diff = new Set();
                            for (var i = 0; i < oldKeys.length; i++) {
                                if (oldJson[oldKeys[i]] != newJson[oldKeys[i]]) {
                                    diff.add(oldKeys[i]);
                                }
                            }
                            for (var i = 0; i < newKeys.length; i++) {
                                if (oldJson[newKeys[i]] != newJson[newKeys[i]]) {
                                    diff.add(newKeys[i]);
                                }
                            }
                            return Array.from(diff).join(",");
                        } else {
                            return "";
                        }
                    }

                }

            ]];
            comtable = table.render({
                elem: '#com-table-car'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".carmsg").html(d.msg);
                }
            });

            //车主信息
            var conditionParamOwner = { "Owner_Space": $("#Owner_Space").val() };
            var cols2 = [[
                , { field: 'OldName', title: '缓存车位号' }
                , { field: 'OldBeginTime', title: '缓存开始时间', hide: true }
                , { field: 'OldEndTime', title: '缓存结束时间', hide: true }
                , { field: 'OldJson', title: '缓存Json' }
                , { field: 'NewName', title: '数据库车位号' }
                , { field: 'NewBeginTime', title: '数据库开始时间', hide: true }
                , { field: 'NewEndTime', title: '数据库结束时间', hide: true }
                , { field: 'NewJson', title: '数据库Json' }
                , {
                    field: '', title: '差异字段', templet: function (d) {
                        if (d.OldJson && d.NewJson && d.NewJson != "" && d.OldJson != "" && d.NewJson != "null" && d.OldJson != "null") {
                            var oldJson = JSON.parse(d.OldJson);
                            var newJson = JSON.parse(d.NewJson);
                            var oldKeys = Object.keys(oldJson);
                            var newKeys = Object.keys(newJson);
                            var diff = new Set();
                            for (var i = 0; i < oldKeys.length; i++) {
                                if (oldJson[oldKeys[i]] != newJson[oldKeys[i]]) {
                                    diff.add(oldKeys[i]);
                                }
                            }
                            for (var i = 0; i < newKeys.length; i++) {
                                if (oldJson[newKeys[i]] != newJson[newKeys[i]]) {
                                    diff.add(newKeys[i]);
                                }
                            }
                            return Array.from(diff).join(",");
                        } else {
                            return "";
                        }
                    }
                }
            ]];
            comtableOwner = table.render({
                elem: '#com-table-owner'
                , method: 'post'
                , cols: cols2
                , toolbar: '#toolbar_btns'
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamOwner) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".ownermsg").html(d.msg);
                }
            });


            var cols3 = [[
                , { field: 'OldName', title: '缓存编码' }
                , { field: 'OldBeginTime', title: '缓存开始时间', hide: true }
                , { field: 'OldEndTime', title: '缓存结束时间', hide: true }
                , { field: 'OldJson', title: '缓存Json' }
                , { field: 'NewName', title: '数据库编码' }
                , { field: 'NewBeginTime', title: '数据库开始时间', hide: true }
                , { field: 'NewEndTime', title: '数据库结束时间', hide: true }
                , { field: 'NewJson', title: '数据库Json' }
                , {
                    field: '', title: '差异字段', templet: function (d) {
                        if (d.OldJson && d.NewJson && d.NewJson != "" && d.OldJson != "" && d.NewJson != "null" && d.OldJson != "null") {
                            var oldJson = JSON.parse(d.OldJson);
                            var newJson = JSON.parse(d.NewJson);
                            var oldKeys = Object.keys(oldJson);
                            var newKeys = Object.keys(newJson);
                            var diff = new Set();
                            for (var i = 0; i < oldKeys.length; i++) {
                                if (oldJson[oldKeys[i]] != newJson[oldKeys[i]]) {
                                    diff.add(oldKeys[i]);
                                }
                            }
                            for (var i = 0; i < newKeys.length; i++) {
                                if (oldJson[newKeys[i]] != newJson[newKeys[i]]) {
                                    diff.add(newKeys[i]);
                                }
                            }
                            return Array.from(diff).join(",");
                        } else {
                            return "";
                        }
                    }

                }

            ]];

            //访客车信息
            var conditionParamReserve = { "Reserve_CarNo": $("#Reserve_CarNo").val() };

            comtableReserve = table.render({
                elem: '#com-table-reserve'
                , method: 'post'
                , cols: cols3
                , toolbar: '#toolbar_btns'
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamReserve) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".reservemsg").html(d.msg);
                }
            });

            //商家车信息
            var conditionParamBusinessCar = { "BusinessCar_CarNo": $("#BusinessCar_CarNo").val() };

            comtableBusinessCar = table.render({
                elem: '#com-table-businessCar'
                , method: 'post'
                , cols: cols3
                , toolbar: '#toolbar_btns'
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamBusinessCar) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".businessCarmsg").html(d.msg);
                }
            });

            //黑名单信息
            var conditionParamBlackList = { "BlackList_CarNo": $("#BlackList_CarNo").val() };

            comtableBlackList = table.render({
                elem: '#com-table-blackList'
                , method: 'post'
                , cols: cols3
                , toolbar: '#toolbar_btns'
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParamBlackList) }
                , limit: 10, limits: [10, 20, 50, 100]
                , done: function (d) {
                    $(".blackListmsg").html(d.msg);
                }
            });

            pager2.init();
        });
    </script>
    <script>
        var pager2 = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layui.form.render();
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/Cache/GetCarList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            //重新加载数据
            bindDataOwner: function (index) {
                layer.closeAll();
                comtableOwner.reload({
                    url: '/Cache/GetOwnerList'
                    , where: { conditionParam: JSON.stringify({ "Owner_Space": $("#Owner_Space").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindDataReserve: function (index) {
                layer.closeAll();
                comtableReserve.reload({
                    url: '/Cache/GetReserveList'
                    , where: { conditionParam: JSON.stringify({ "Reserve_CarNo": $("#Reserve_CarNo").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindDataBusinessCar: function (index) {
                layer.closeAll();
                comtableBusinessCar.reload({
                    url: '/Cache/GetBusinessCarList'
                    , where: { conditionParam: JSON.stringify({ "BusinessCar_CarNo": $("#BusinessCar_CarNo").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindDataBlackList: function (index) {
                layer.closeAll();
                comtableBlackList.reload({
                    url: '/Cache/GetBlackListList'
                    , where: { conditionParam: JSON.stringify({ "BlackList_CarNo": $("#BlackList_CarNo").val() }) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#SearchCar").click(function () { pager2.bindData(1); });
                $("#Search_Owner").click(function () { pager2.bindDataOwner(1); });
                $("#SearchReserve").click(function () { pager2.bindDataReserve(1); });
                $("#SearchBusinessCar").click(function () { pager2.bindDataBusinessCar(1); });
                $("#SearchBlackList").click(function () { pager2.bindDataBlackList(1); });

                $("#ClearCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有车辆信息的缓存吗?（清理车辆缓存后，请手动清理车主缓存）",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearCar", {}, function (json) {
                                if (json.success) {

                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearOwner").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有车主信息的缓存吗?（清理车主缓存后，请手动清理车辆缓存）",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearOwner", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataOwner(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadOwner").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有车主信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadOwnerCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataOwner(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有车辆信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadCarCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindData(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearReserve").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有访客车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearReserve", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataReserve(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadReserve").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有访客车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadReserveCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataReserve(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearBusinessCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有商家车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearBusinessCar", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBusinessCar(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadBusinessCar").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有商家车信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadBusinessCarCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBusinessCar(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#ClearBlackList").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "确定清除所有黑名单信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/ClearBlackList", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBlackList(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
                $("#LoadBlackList").click(function () {
                    layer.open({
                        id: 2,
                        type: 0,
                        title: "消息提示",
                        btn: ["确定", "取消"],
                        content: "重新加载所有黑名单信息的缓存吗?",
                        yes: function (res) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            $.getJSON("/Cache/LoadBlackListCache", {}, function (json) {
                                if (json.success) {
                                    layer.msg("处理成功", { icon: 1, time: 1500 }, function () { pager.bindDataBlackList(1); });
                                } else
                                    layer.msg(json.msg, { icon: 0, time: 1500 });
                            });
                        },
                        btn2: function () { }
                    })
                });
            },
            bindPower: function () {

            }
        }
    </script>

</body>
</html>
