﻿
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>数据模拟</title>
    <link href="~/Static/plugins/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <script src="~/Static/plugins/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <style>
        html, body {
            width: 100%;
            height: 100%;
            background-color: #f9f9f9;
            overflow: hidden;
        }

        .layui-card {
            width: 99%;
            min-height: 100%;
            float: left;
            background-color: #f9f9f9;
            overflow: auto;
        }

        .card-res {
            width: 50%;
            float: right;
            overflow-y: auto;
        }

            .card-res::before {
                content: "";
                border: 1px solid #000;
                position: absolute;
                width: 0px;
                height: 100%;
            }

        .layui-form-label {
            width: 120px;
            font-weight: bold;
        }

        .layui-input-block {
            margin-left: 150px;
        }

        .layui-input {
            color: #300bee;
        }

            .layui-input[readonly] {
                background-color: #f5f5f5;
            }



        p {
            margin: 0 1em;
            font-size: 2em;
            font-weight: 600;
        }

        .landIn {
            display: flex;
            flex-wrap: wrap;
            line-height: 1.8;
            color: #153c7c;
            font-family: Lora, serif;
            white-space: pre;
        }

            .landIn span {
                animation: landIn 0.8s ease-out both;
            }

        @@keyframes landIn {
            from {
                opacity: 0;
                transform: translateY(-20%);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="layui-card">
        <div class="layui-card-header">
            <p class="landIn">计费数据模拟</p>
        </div>
        <div class="layui-card-body layui-form" id="form1">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>三类计费</legend>
            </fieldset>

            <div class="layui-collapse" lay-accordion="">
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">周期计费</h2>
                    <div class="layui-colla-content layui-show">

                        <div class="layui-collapse" lay-accordion="">
                            <div class="layui-colla-item">
                                <h2 class="layui-colla-title">按间隔时长收费</h2>
                                <div class="layui-colla-content layui-show">

                                    <div class="layui-collapse" lay-accordion="">
                                        <div class="layui-colla-item">
                                            <h2 class="layui-colla-title">8888888</h2>
                                            <div class="layui-colla-content layui-show">
                                                1112
                                            </div>
                                        </div>
                                        <div class="layui-colla-item">
                                            <h2 class="layui-colla-title">88888888</h2>
                                            <div class="layui-colla-content">
                                                <p>222222</p>
                                            </div>
                                        </div>
                                        
                                    </div>

                                </div>
                            </div>
                            <div class="layui-colla-item">
                                <h2 class="layui-colla-title">按时长范围收费</h2>
                                <div class="layui-colla-content">
                                    <p>1111111</p>
                                </div>
                            </div>
                            <div class="layui-colla-item">
                                <h2 class="layui-colla-title">按次收费</h2>
                                <div class="layui-colla-content">
                                    <p>333333</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-colla-item">
                    <h2 class="layui-colla-title">非周期计费</h2>
                    <div class="layui-colla-content">
                        <p>4444444</p>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.md5.js" asp-append-version="true"></script>
    <script type="text/javascript">
        function titleShow(){
            var landInTexts = document.querySelectorAll(".landIn");
            landInTexts.forEach(function (landInText) {
            var letters = landInText.textContent.split("");
            landInText.textContent = "";
            letters.forEach(function (letter, i) {
                var span = document.createElement("span");
                span.textContent = letter;
                span.style.animationDelay = i * 0.5 + "s";
                landInText.append(span);
                });
            });
        }

        titleShow();
        var interval = null;
        var timeInterval = null;
        var time = 5;
        if (timeInterval != null) {
            clearInterval(timeInterval);
            timeInterval = null;
        }
        timeInterval = setInterval(function () {
           titleShow();
        }, 5000);

        layui.use(['form','element', 'layer'], function () {
           var element = layui.element;
           var layer = layui.layer;
           //监听折叠
           element.on('collapse(test)', function(data){
                layer.msg('展开状态：'+ data.show);
           });
           pager.init();
           layui.form.render("select");
        });
    </script>
    <script type="text/javascript">

        var pager = {
            order:"",
            payData: null,
            init: function () {
                $.ajaxSettings.async = false;
                //this.bindSelect();
                //this.bindData();
                //this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltCameranoList?r=" + Math.random(), {}, function (json) {
                    if (json.success) {
                        if (json.inList)
                            json.inList.forEach(function (d, i) {
                                var option = '<option value="' + d.Device_No + '">' + d.Device_Name + '</option>';
                                $("#camerano").append(option)
                            });

                        if (json.outList)
                            json.outList.forEach(function (d, i) {
                                var option = '<option value="' + d.Device_No + '">' + d.Device_Name + '</option>';
                                $("#camerano_out").append(option)
                            });
                    } else {
                        layer.msg(json.msg)
                    }
                }, "json");
            },
            bindData: function () {

            },
            bindEvent: function () {
                $("#OnSend").click(function () {
                    if(pager.order==""){
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定','取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value,index){
                            pager.order=$.md5(value);
                            layer.close(index);
                            pager.InOutResult();
                        });
                    }else{
                      pager.InOutResult();
                    }
                });


                $("#OnPass").click(function () {
                    if(pager.order==""){
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定','取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value,index){
                            pager.order=$.md5(value);
                            layer.close(index);
                            pager.PassResult();
                        });
                    }else{
                      pager.PassResult();
                    }
                });

                $("#OnPay").click(function () {
                     if(pager.order==""){
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定','取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value,index){
                            pager.order=$.md5(value);
                            layer.close(index);
                            pager.PayCar();
                        });
                    }else{
                      pager.PayCar();
                    }
                });

                $("#OnCheck").click(function () {
                    if(pager.order==""){
                        layer.prompt({
                            formType: 1,
                            value: '',
                            title: '你的账号无权使用api调试，请输入口令',
                            btn: ['确定','取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value,index){
                            pager.order=$.md5(value);
                            layer.close(index);
                            pager.GetInOutCar();
                        });
                    }else{
                      pager.GetInOutCar();
                    }
                });
            },
            InOutResult:function(){
                   //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });

                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    var order=pager.order;
                    pager.order="";
                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("InOutResult", { jsonModel: jsonModel,order:order }, function (json) {
                        layer.closeAll();
                        pager.payData = json.payres;
                         var  resultTxt=JSON.stringify(json, undefined, 4);
                        $("#result").val(resultTxt);
                        if (json.passres) {
                            $("#orderno").val(json.passres.parkorderno);
                            $("#orderdetailno").val(json.passres.orderdetailno);
                        }
                      
                        if(resultTxt.indexOf("无权")==-1){
                             pager.order=order;
                        }
                    }, "json");
            },
            PassResult:function(){
                 //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });
                    param.code = 200;
                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);

                    var order=pager.order;
                    pager.order="";
                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("PassResult", { jsonModel: jsonModel,order:order  }, function (json) {
                        layer.closeAll();
                        var  resultTxt=JSON.stringify(json, undefined, 4);
                        $("#result").val(resultTxt);
                        if(resultTxt.indexOf("无权")==-1){
                             pager.order=order;
                        }
                    }, "json");
            },
            PayCar:function(){
                  //获取所有表单元素
                    var param = $("#form1").formToJSON(true, function (data) {
                        return data;
                    });
                    param.code = 200;
                    param.img = encodeURIComponent(param.img);
                    var jsonModel = JSON.stringify(param);
                    $("#uparam").val(jsonModel);
                    var order=pager.order;
                    pager.order="";
                    layer.msg("正在处理...", { icon: 16, time: 0 });
                    $.post("PayCar", { carno: $("#carno").val(), camerano: $("#camerano_out").val(), time: $("#time_out").val(), cartype: $("#cartype").val() ,order:order }, function (json) {
                        layer.closeAll();
                        var resultTxt="计费结果：";
                        if(json.success){
                            var d=json.data;
                            var list=d.list;
                            if(list){
                              delete d["list"];
                               $("#data-view-detail").html("");
                                $('#data-tmpl-detail').tmpl(list).appendTo('#data-view-detail');
                              //var detailTxt="";
                              //var spaceStr="                                                                                                                                                                     ";
                              //$.each(list,function(k,v){
                              //   detailTxt+=((k+1)+"、时间段："+v.starttime+spaceStr+"-"+v.endtime+"，区域编号："+v.areano+"，上个周期结束时间："+v.preNextcycletime+spaceStr);
                              //})
                              //$("#detail").val(JSON.stringify(list, undefined, 4));
                            }

                            if(d.payed==0){
                                resultTxt+="无需计费";
                            }else if(d.payed==1){
                                 resultTxt+="计费成功";
                            }else{
                                  resultTxt+="计费失败";
                            }
                            resultTxt+="，订单金额："+d.orderamount;
                            resultTxt+="，优惠金额："+d.couponamount;
                            resultTxt+="，储值金额："+d.chuzhiamount;
                            resultTxt+="，支付金额："+d.payedamount;
                            resultTxt+="                                                                                                                                                ";
                        }
                        resultTxt+=JSON.stringify(json, undefined, 4);
                        $("#result").val(resultTxt);
                        if(resultTxt.indexOf("无权")==-1){
                             pager.order=order;
                        }
                    }, "json");
            },
            GetInOutCar:function(){
                layer.prompt({
                            formType: 0,
                            value: '',
                            title: '请输入车牌号',
                            btn: ['确定','取消'], //按钮，
                            btnAlign: 'c'
                        }, function(value,index){
                            layer.close(index);
                            layer.msg("正在处理...", { icon: 16, time: 0 });
                            var order=pager.order;
                            pager.order="";
                            $("#uparam").val(JSON.stringify({ carno: value }));
                            $.post("GetInOutCar", { carno: value,order:order  }, function (json) {
                                layer.closeAll();
                                var  resultTxt=JSON.stringify(json, undefined, 4);
                                $("#result").val(resultTxt);
                                if(resultTxt.indexOf("无权")==-1){
                                     pager.order=order;
                                }
                                if(json.success){
                                     layer.open({
                                        title: "<i class='fa fa-rmb' style='margin-top: 17px;'></i> 进出场详情",
                                        type: 2, id: 1,
                                        area: getIframeArea(['98%', '98%']),
                                        fix: false, //不固定
                                        maxmin: false,
                                        content: 'Detail?key=' + encodeURIComponent(pager.order)+'&carno='+encodeURIComponent(value)
                                    });
                                }
                            }, "json");
                        });
            },
        }


        pager.order='@ViewBag.order';
    </script>
</body>
</html>