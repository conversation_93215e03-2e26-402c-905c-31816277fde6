﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付详情</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        h3, span { color: black; font-weight: normal !important; }
        .ibox-content { padding-top: 0px !important; }
        body { margin-top: 0; }
        .fishBone { border: 1px solid #f5f5f5; }
        .form-group { margin-bottom: 10px; }
        .gray-bg { background-color: #fdf8f8; margin-top: 5px; }
        h3 { padding-top: 5px; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3); border-radius: 10px; }
        ::-webkit-scrollbar-thumb { border-radius: 10px; background: rgba(0,0,0,0.1); -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5); }
        ::-webkit-scrollbar-thumb:window-inactive { background: rgba(255,0,0,0.4); }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>支付概要信息</h3>
                </div>
            </div>
            <div class="form-group center-block">
                <div class="row col-xs-12">
                    <div class="col-xs-3">
                        <label class="control-label">车牌号码：<span id="PayOrder_CarNo"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">支付订单：<span id="PayOrder_No"></span></label>
                    </div>
                </div>
                <div class="row col-xs-12">

                    <div class="col-xs-3">
                        <label class="control-label">应收金额：<span id="PayOrder_Money"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">实收金额：<span id="PayOrder_PayedMoney"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">优惠金额：<span id="PayOrder_DiscountMoney"></span></label>
                    </div>
                </div>
                <div class="row col-xs-12">
                    <div class="col-xs-3">
                        <label class="control-label">停车订单：<span id="PayOrder_ParkOrderNo"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">用户号：<span id="PayOrder_UserNo"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">操作员：<span id="PayOrder_OperatorName"></span></label>
                    </div>
                </div>

                <div class="row col-xs-12">
                    <div class="col-xs-3">
                        <label class="control-label">支付状态：<span id="PayOrder_Status"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">支付时间：<span id="PayOrder_PayedTime"></span></label>
                    </div>
                    <div class="col-xs-3">
                        <label class="control-label">时长描述：<span id="PayOrder_TimeCountDesc"></span></label>
                    </div>
                </div>
            </div>


            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>优惠券列表</h3>
                </div>
            </div>
            <div class="form-group-sm center-block">
                <table class="table table-striped  table-hover">
                    <thead>
                        <tr>
                            <th>优惠券编号</th>
                            <th>优惠方式</th>
                            <th>优惠额度</th>
                            <th>抵扣金额</th>
                            <th>消费时间</th>
                        </tr>
                    </thead>
                    <tbody id="data-view-coupon"></tbody>
                    <script id="data-tmpl-coupon" type="text/x-jquery-tmpl">
                    <tr>
                        <td width=180>${CouponRecord_No}</td>
                        <td>
                            {{if CouponRecord_CouponCode == "101"}}
                            优惠金额
                            {{else CouponRecord_CouponCode == "102"}}
                            优惠时长
                            {{else CouponRecord_CouponCode == "103"}}
                            优惠比例
                            {{else CouponRecord_CouponCode == "104"}}
                            优惠到指定时间
                            {{/if}}
                        </td>
                        <td>
                            {{if CouponRecord_CouponCode == "101"}}
                            减免${CouponRecord_Value}元
                            {{else CouponRecord_CouponCode == "102"}}
                            减免${CouponRecord_Value}分钟
                            {{else CouponRecord_CouponCode == "103"}}
                            打${CouponRecord_Value/10}折
                            {{else CouponRecord_CouponCode == "104"}}
                            免费到${CouponRecord_EndTime}
                            {{else CouponRecord_CouponCode == "105"}}
                            ${CouponRecord_EndTime}
                            {{/if}}
                        </td>
                        <td>
                            ${CouponRecord_Paid}
                        </td>
                        <td>${CouponRecord_UsedTime}</td>
                    </tr>
                    </script>
                    <tr>
                        <td style="color: #ff0000; font-weight: bold;">合计:</td>
                        <td></td>
                        <td></td>
                        <td style="color: #ff0000; font-weight: bold;" class="SumedMoney-Coupon">0</td>
                        <td></td>
                    </tr>
                </table>
            </div>

            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>收费明细列表</h3>
                </div>
            </div>
            <div class="form-group-sm center-block">
                <table class="table table-striped  table-hover">
                    <thead>
                        <tr>
                            <th>支付明细号</th>
                            <th>区域名称</th>
                            <th>应收金额</th>
                            <th>实收金额</th>
                            <th>优惠金额</th>
                            <th>支付描述</th>
                        </tr>
                    </thead>
                    <tbody id="data-view-orderdetail"></tbody>
                    <script id="data-tmpl-orderdetail" type="text/x-jquery-tmpl">
                    <tr>
                        <td width=180>${PayPart_No}</td>
                        <td>${PayPart_ParkAreaName}</td>
                        <td>${PayPart_OrderMoney}</td>
                        <td>${PayPart_PayedMoney}</td>
                        <td>
                            {{if PayPart_CouponMoney != null}}
                            ${PayPart_CouponMoney}
                            {{else}}
                            0
                            {{/if}}
                        </td>
                        <td>${PayPart_Desc}</td>
                    </tr>
                    </script>
                </table>
            </div>
        </div>
    </div>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?1" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?1" asp-append-version="true"></script>
    <script>
        layui.use(["form"], function () {
            pager.init();
        });

        var data3 = null;
        var paramNo = decodeURIComponent($.getUrlParam("PayOrder_No"));
        var pager = {
            init: function () {
                this.bindData();
            },
            bindEvent: function () {
                $(".dbclick").dblclick(function () {
                    $('#data-view').html("")
                    $('#data-tmpl').tmpl(data3).appendTo('#data-view');
                });
            },

            //数据绑定
            bindData: function () {
                layer.msg('加载中...', { icon: 16, time: 0 });

                if (paramNo != null) {
                    $.ajax({
                        type: 'post',
                        url: '/Monitoring/GetPayOrderByNo',
                        dataType: 'json',
                        data: { PayOrder_No: paramNo },
                        success: function (json) {
                            if (json.success) {
                                //订单信息
                                if (json.data != null && json.data.model != null) {
                                    $("#PayOrder_OrderNo").text(json.data.model.PayOrder_No);
                                    $("#PayOrder_CarNo").text(json.data.model.PayOrder_CarNo);

                                    $("#PayOrder_No").text(json.data.model.PayOrder_No);
                                    $("#PayOrder_OrderTypeNo").text(json.data.model.PayOrder_OrderTypeNo);
                                    $("#PayOrder_Money").text(json.data.model.PayOrder_Money);
                                    $("#PayOrder_PayedMoney").text(json.data.model.PayOrder_PayedMoney);

                                    $("#PayOrder_DiscountMoney").text(json.data.model.PayOrder_DiscountMoney);
                                    $("#PayOrder_ParkOrderNo").text(json.data.model.PayOrder_ParkOrderNo);
                                    $("#PayOrder_UserNo").text(json.data.model.PayOrder_UserNo);
                                    $("#PayOrder_TimeCountDesc").text(json.data.model.PayOrder_TimeCountDesc);
                                    $("#PayOrder_PayedTime").text(json.data.model.PayOrder_PayedTime);
                                    $("#PayOrder_OperatorName").text(json.data.model.PayOrder_OperatorName);

                                    if (json.data.model.PayOrder_Status != null) {
                                        if (json.data.model.PayOrder_Status == 0) $("#PayOrder_Status").text("未支付");
                                        else if (json.data.model.PayOrder_Status == 1) $("#PayOrder_Status").text("支付成功");
                                        else if (json.data.model.PayOrder_Status == 2) $("#PayOrder_Status").text("支付失败");
                                        else if (json.data.model.PayOrder_Status == 3) $("#PayOrder_Status").text("用户取消");
                                    }
                                }

                                //订单使用的优惠券列表
                                $("#data-view-coupon").html("")
                                if (json.data.coupon && json.data.coupon != null) {
                                    $('#data-tmpl-coupon').tmpl(json.data.coupon).appendTo('#data-view-coupon');
                                    var sumedMoney_Coupon = 0;
                                    $(json.data.coupon).each(function () {
                                        sumedMoney_Coupon += parseFloat(this.CouponRecord_Paid == null ? 0 : this.CouponRecord_Paid);
                                    });
                                    $(".SumedMoney-Coupon").html(sumedMoney_Coupon.toFixed(2));
                                }

                                //订单使用的支付明细列表
                                $("#data-view-orderdetail").html("")
                                if (json.data.paypart && json.data.paypart != null) {
                                    $('#data-tmpl-orderdetail').tmpl(json.data.paypart).appendTo('#data-view-orderdetail');
                                }

                                pager.bindEvent();

                                layer.closeAll();
                            } else {
                                layer.msg('加载失败：' + json.msg, { icon: 5 });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('订单参数无效', { icon: 0 });
                }
            }
        };

    </script>
</body>
</html>
