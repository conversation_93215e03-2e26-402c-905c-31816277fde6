﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <!--<meta name="viewport" content="width=device-width, initial-scale=1.0">-->
    <title>编辑接口回调</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
</head>

<body>
    <div class="layui-form">
        <table id="tableCallBack" lay-filter="tableCallBack" style="margin:-6px"></table>

        <div class="edit-ipt-ban" style="text-align: center; padding-top: 8px">
            <button class="btn btn-primary" id="Save">
                <i class="fa fa-check"></i>
                <t>保存</t>
            </button>
            <button class="btn btn-warning" id="Cancel">
                <i class="fa fa-times"></i>
                <t>取消</t>
            </button>

        </div>
    </div>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>

    <script>
        layui.use(['table', 'form'], function () {
            var table = layui.table;
            var form = layui.form;
            //创建列表实例
            table.render({
                elem: '#tableCallBack',
                url: '/SystemSetting/GetCallBackList',
                page: false,
                totalRow: false,
                cols: [[
                    { field: 'ServiceName', title: '回调服务名称', width: 200 },
                    { field: 'ServiceUrl', title: '回调服务地址 <i class="layui-icon layui-icon-tips layui-font-14" lay-event="email-tips" title="点击下面单元格可编辑修改回调地址" style="margin-left: 5px;"></i>', fieldTitle: '回调服务地址', hide: 0, width: 400, expandedMode: 'tips', edit: 'text' },
                    { field: 'Interval', title: '延迟(ms)', fieldTitle: '延迟(ms)', hide: 0, width: 90, expandedMode: 'tips', edit: 'text' },
                    {
                        title: '', width: 108, templet: function (d) {
                            return `<input type="checkbox" name="IsEnable" lay-filter="IsEnable-templet-status" value="${d.Index}" title="启用" lay-skin="tag" ${d.IsEnable === true ? 'checked' : ''} />`;
                        }
                    },
                    //{ field: 'IsEnable', title: '启用', width: 90, templet: '#IsEnable-templet-switch' }
                ]],
                done: function (res, curr, count) {
                    //表格渲染完成后，绑定事件
                    form.render();
                }
            });

            // 监听指定开关
            form.on('checkbox(IsEnable-templet-status)', function (data) {
                var index = parseInt(data.value);
                var isEnable = data.elem.checked
                var orData = layui.table.cache.tableCallBack;
                for (var i = 0; i < orData.length; i++) {
                    //把orData[i].Index转换成相同的类型再比较
                    var index1 = parseInt(orData[i].Index);
                    if (index1 === index) {
                        orData[i].IsEnable = isEnable;
                    }
                }
            });
        });

        //点击保存时
        $('#Save').click(function () {
            var data = layui.table.cache.tableCallBack;
            var callBackList = [];
            for (var i = 0; i < data.length; i++) {
                var item = data[i];
                //判断服务启用时，服务地址和延迟不能为空
                if (item.IsEnable === true) {
                    if (item.ServiceUrl === '' || item.Interval === '') {
                        layer.msg('[' + item.ServiceName + ']服务地址和延迟不能为空', { icon: 2 });
                        return;
                    } else {
                        if (!isValidURL(item.ServiceUrl)) {
                            layer.msg('[' + item.ServiceName + ']请输入正确的服务地址', { icon: 2 });
                            return;
                        }
                    }

                    //判断延迟是否为整数型数字
                    if (!/^\d+$/.test(item.Interval)) {
                        layer.msg('[' + item.ServiceName + ']延迟必须为整数型数字', { icon: 2 });
                        return;
                    }

                    //单位：毫秒 ，间隔必须大于等于200ms，小于等于600000ms(10分钟)
                    if (item.Interval < 200 || item.Interval > 600000) {
                        layer.msg('[' + item.ServiceName + ']间隔必须大于等于200ms，小于等于600000ms(10分钟)', { icon: 2 });
                        return;
                    }
                }
                callBackList.push({
                    Index: item.Index,
                    ServiceName: item.ServiceName,
                    ServiceUrl: item.ServiceUrl,
                    IsEnable: item.IsEnable,
                    Interval: item.Interval
                });
            }
            $.post('/SystemSetting/SaveCallBack', { jsonModel: JSON.stringify(callBackList) }, function (res) {
                if (res.success) {
                    layer.msg('保存成功', { icon: 1, time: 500 }, function () {
                        //关闭当前窗口
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        parent.pager.bindUpdateCallBack(callBackList);
                    });

                } else {
                    layer.msg(res.msg, { icon: 2 });
                }
            });
        });

        //点击取消时
        $('#Cancel').click(function () {
            //关闭当前窗口
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        });

        function isValidURL(url) {
            const pattern = new RegExp('^(https?:\\/\\/)?' + // 协议
                '((([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,})|' + // 域名
                '(localhost)|' + // 本地主机
                '((\\d{1,3}\\.){3}\\d{1,3}))' + // IP地址 (IPv4)
                '(\\:\\d+)?(\\/[-a-zA-Z0-9%_.~+]*)*' + // 端口和路径
                '(\\?[;&a-zA-Z0-9%_.~+=-]*)?' + // 查询字符串
                '(\\#[-a-zA-Z0-9_]*)?$'); // 锚点
            return pattern.test(url);
        }
    </script>
</body>

</html>