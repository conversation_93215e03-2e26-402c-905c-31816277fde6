﻿using System;
using System.Text.RegularExpressions;
using System.Text;
using System.Web;
using System.IO;
using System.Security.Cryptography;
using Microsoft.VisualBasic;
using System.Collections;
using System.Drawing;
using System.Net;
using System.Runtime.InteropServices;
using System.IO.Compression;
using System.Collections.Generic;
using System.Reflection;
using System.Collections.Specialized;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using Microsoft.Win32;
using System.Management;
using System.Diagnostics;
using System.Security.Principal;
using System.ComponentModel;
using Exception = System.Exception;
using System.Threading;
using System.Globalization;
using System.Collections.Concurrent;
using Microsoft.AspNetCore.Http;

namespace carparking.Common
{
    public class Utils
    {
        /// <summary>
        /// 触发无感则对查费进行锁单，优先触发查费，则不执行无感, 1表示优先触发的无感，2表示优先触发的查费
        /// </summary>
        public static ConcurrentDictionary<string, Tuple<int, DateTime>> PairsLockOrder = new ConcurrentDictionary<string, Tuple<int, DateTime>>();

        public const string passwordMD5String = "znykt";

        private static Regex RegexBr = new Regex(@"(\r\n)", RegexOptions.IgnoreCase);

        //public static Regex RegexFont = new Regex(@"<font color=" + "\".*?\"" + @">([\s\S]+?)</font>", Tools.GetRegexOptions());
        /// <summary>
        /// 得到正则编译参数设置
        /// </summary>
        /// <returns>参数设置</returns>
        public static RegexOptions GetRegexOptions()
        {
            return RegexOptions.None;
        }

        public static int ConvertDateTimeInt(System.DateTime time)
        {
            System.DateTime startTime = TimeZoneInfo.ConvertTime(new System.DateTime(1970, 1, 1), TimeZoneInfo.Local); //TimeZone.CurrentTimeZone.ToLocalTime(new System.DateTime(1970, 1, 1));
            return (int)(time - startTime).TotalSeconds;
        }

        #region 处理图片字符串

        /// <summary>
        /// 处理图片字符串
        /// </summary>
        /// <param name="filepath">路径</param>
        /// <param name="ImgStr">图片字符串</param>
        //public static bool saveToImgFile(string filepath, string ImgStr)
        //{
        //    if (ImgStr == null || ImgStr.Length == 0)
        //    {
        //        return false;
        //    }
        //    // byte[] buff = System.Text.Encoding.UTF8.GetBytes(src);
        //    try
        //    {

        //        //  ImgStr = ImgStr.Trim().Replace("%", "").Replace(",", "").Replace(" ", "+");
        //        //if (ImgStr.Length % 4 > 0)
        //        //{
        //        //    ImgStr = ImgStr.PadRight(ImgStr.Length + 4 - ImgStr.Length % 4, '=');
        //        //}


        //        byte[] buff = Convert.FromBase64String(ImgStr);
        //        MemoryStream ms = new MemoryStream(buff);
        //        Image image = Image.FromStream(ms);

        //        image.Save(filepath);
        //        ms.Close();
        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        return false;
        //        //throw ex;
        //    }
        //}

        //public static bool AndroidSaveToImgFile(string filepath, string ImgStr)
        //{
        //    if (ImgStr == null || ImgStr.Length == 0)
        //    {
        //        return false;
        //    }
        //    // byte[] buff = System.Text.Encoding.UTF8.GetBytes(src);
        //    try
        //    {

        //        //  ImgStr = ImgStr.Trim().Replace("%", "").Replace(",", "").Replace(" ", "+");
        //        //if (ImgStr.Length % 4 > 0)
        //        //{
        //        //    ImgStr = ImgStr.PadRight(ImgStr.Length + 4 - ImgStr.Length % 4, '=');
        //        //}


        //        byte[] buff = Convert.FromBase64String(UrlDecode(ImgStr));
        //        MemoryStream ms = new MemoryStream(buff);
        //        Image image = Image.FromStream(ms);

        //        image.Save(filepath);
        //        ms.Close();
        //        return true;
        //    }
        //    catch (Exception ex)
        //    {
        //        return false;
        //        //throw ex;
        //    }
        //}

        #endregion

        #region 随机数

        /// <summary>
        /// 验证码生成的取值范围
        /// </summary>
        private static string[] verifycodeRange =
        {
            "1", "2", "3", "4", "5", "6", "7", "8", "9",
            "a", "b", "c", "d", "e", "f", "g",
            "h", "j", "k", "m", "n",
            "p", "q", "r", "s", "t",
            "u", "v", "w", "x", "y"
        };

        /// <summary>
        /// 验证码生成的取值范围，包含了26个字母大小写和0
        /// </summary>
        private static string[] verifycodeRange_All = { "0", "1", "2", "3", "4", "5", "6", "7", "a", "c", "d", "e", "f", "h", "i", "j", "k", "m", "n", "p", "r", "s", "t", "A", "C", "D", "E", "F", "G", "H", "J", "K", "M", "N", "P", "Q", "R", "S", "U", "V", "W", "X", "Y", "Z" };

        /// <summary>
        /// 生成验证码所使用的随机数发生器
        /// </summary>
        private static Random verifycodeRandom = new Random();

        /// <summary>
        /// 产生验证码
        /// </summary>
        /// <param name="len">长度</param>
        /// <returns>验证码</returns>
        public static string CreateAuthStr(int len)
        {
            int number;
            StringBuilder checkCode = new StringBuilder();
            Random random = new Random(Guid.NewGuid().GetHashCode());

            for (int i = 0; i < len; i++)
            {
                number = random.Next();

                if (number % 2 == 0)
                    checkCode.Append((char)('0' + (char)(number % 10)));
                else
                    checkCode.Append((char)('A' + (char)(number % 26)));
            }

            return checkCode.ToString();
        }

        /// <summary>
        /// 产生验证码
        /// </summary>
        /// <param name="len">长度</param>
        /// <param name="OnlyNum">是否仅为数字</param>
        /// <returns>string</returns>
        public static string CreateAuthStr(int len, bool OnlyNum)
        {
            int number;
            StringBuilder checkCode = new StringBuilder();

            for (int i = 0; i < len; i++)
            {
                if (!OnlyNum)
                    number = verifycodeRandom.Next(0, verifycodeRange.Length);
                else
                    number = verifycodeRandom.Next(1, 9);

                checkCode.Append(verifycodeRange[number]);
            }

            return checkCode.ToString();
        }

        /// <summary>
        /// 产生随机码，包含了26个字母大小写和0
        /// </summary>
        /// <param name="len">长度</param>
        /// <param name="OnlyNum">是否仅为数字</param>
        /// <returns>string</returns>
        public static string CreateAuthStr_All(int len, bool OnlyNum)
        {
            int number;
            StringBuilder checkCode = new StringBuilder();

            for (int i = 0; i < len; i++)
            {
                if (!OnlyNum)
                    number = verifycodeRandom.Next(0, verifycodeRange_All.Length);
                else
                    number = verifycodeRandom.Next(1, 9);

                checkCode.Append(verifycodeRange_All[number]);
            }

            return checkCode.ToString();
        }

        /// <summary>
        /// 批量生成验证码（大写）
        /// </summary>
        /// <param name="count"></param>
        /// <param name="len"></param>
        /// <param name="OnlyNum"></param>
        /// <returns></returns>
        public static List<string> CreateAuthStrList(int count, int len, bool OnlyNum)
        {
            List<string> lst = new List<string>();
            var maxeErrorCount = 100000;
            var j = 0;
            while (j < count)
            {
                string no = Utils.CreateAuthStr_All(len, OnlyNum).ToUpper();
                if (!lst.Contains(no))
                {
                    lst.Add(no);
                    j++;
                }
                else
                {
                    maxeErrorCount--;
                }

                if (maxeErrorCount < 1) break;
            }

            return lst;
        }

        /// <summary>
        /// 生成随机码（16位数字）
        /// </summary>
        public static string CreateNumber
        {
            get
            {
                Random random = new Random(Guid.NewGuid().GetHashCode());
                //return DateTimeHelper.GetNowTime().ToString("yyMMddHHmmssfff") + random.Next(100, 999);//18位

                var ts = (long)(DateTimeHelper.GetNowTime() - TimeZoneInfo.ConvertTime(new DateTime(1970, 1, 1), TimeZoneInfo.Local)).TotalMilliseconds;
                return ts.ToString() + random.Next(100, 999); //16位
            }
        }

        private static readonly object _lock = new object();

        /// <summary>
        /// 【雪花算法】生成随机码（19位数字）
        /// </summary>
        public static string CreateNumber_SnowFlake
        {
            get
            {
                return new Snowflake.Net.Worker().NextId().ToString();
            }
        }

        /// <summary>
        /// 生成随机码,带前缀
        /// </summary>
        public static string CreateNumberWith(string qz = "")
        {
            return qz + CreateNumber;
        }


        /// <summary>
        /// 生成随机码（18位数字）
        /// </summary>
        public static string CreateNumber_18
        {
            get
            {
                Random random = new Random(Guid.NewGuid().GetHashCode());
                return DateTimeHelper.GetNowTime().ToString("yyMMddHHmmssfff") + random.Next(100, 999); //18位
            }
        }

        /// <summary>
        /// 生成访客随机码（"90"+16位数字）
        /// </summary>
        public static string CreateVisitorNumber
        {
            get { return "90" + CreateNumber; }
        }

        /// <summary>
        /// 生成随机数列表
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        public static List<string> GetRandomLst(int num, string qz = "")
        {
            List<string> lst = new List<string>();
            var maxeErrorCount = 100000;
            var j = 0;
            while (j < num)
            {
                string no = qz + Utils.CreateNumber;
                if (!lst.Contains(no))
                {
                    lst.Add(no);
                    j++;
                }
                else
                {
                    maxeErrorCount--;
                }

                if (maxeErrorCount < 1) break;
            }

            return lst;
        }


        /// <summary>
        /// 生成随机数列表
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        public static List<string> GetRandomLstByTime(DateTime dt, int num, string qz = "")
        {
            List<string> lst = new List<string>();
            var maxeErrorCount = 100000;
            var j = 0;
            while (j < num)
            {
                string no = CreateNumberByTime(dt);
                if (!lst.Contains(no))
                {
                    lst.Add(no);
                    j++;
                }
                else
                {
                    dt = dt.AddMilliseconds(10);
                    maxeErrorCount--;
                }

                if (maxeErrorCount < 1) break;
            }

            return lst;
        }

        public static string CreateNumberByTime(DateTime dt)
        {
            Random random = new Random(Guid.NewGuid().GetHashCode());
            var ts = (long)(dt - TimeZoneInfo.ConvertTime(new DateTime(1970, 1, 1), TimeZoneInfo.Local)).TotalMilliseconds;
            return ts.ToString() + random.Next(100, 999); //16位
        }

        /// <summary>
        /// 生成随机码
        /// </summary>
        public static string CreateRandom
        {
            get
            {
                Random random = new Random(Guid.NewGuid().GetHashCode());
                string datetime = DateTimeHelper.GetNowTime().ToString("yyyyMMddHHmmss"); //14位
                return MD5(datetime + random.Next(1001, 9999).ToString()); //+4位随机数
            }
        }

        #endregion

        #region 字符串数组

        /// <summary>
        /// 分割字符串
        /// </summary>
        /// <param name="strContent">需要分割的文字内容</param>
        /// <param name="strSplit">分割字符串</param>
        /// <returns>分割后的数组</returns>
        public static string[] SplitString(string strContent, string strSplit)
        {
            if (!Utils.StrIsNullOrEmpty(strContent))
            {
                if (strContent.IndexOf(strSplit) < 0)
                    return new string[] { strContent };

                return Regex.Split(strContent, Regex.Escape(strSplit), RegexOptions.IgnoreCase);
            }
            else
                return new string[0] { };
        }

        /// <summary>
        /// 将参数数组转换为名值串
        /// </summary>
        /// <param name="a">参数数组</param>
        /// <returns>转换的名值串,名值串之间用逗号分隔</returns>
        public static string ConvertArrayToString(Array a)
        {
            return ConvertArrayToString(a, ",");
        }

        /// <summary>
        /// 将参数数组转换为名值串
        /// </summary>
        /// <param name="a">参数数组</param>
        /// <returns>转换的名值串,名值串之间用逗号分隔</returns>
        public static string ConvertArrayToString(Array a, string strSplit)
        {
            StringBuilder builder = new StringBuilder();

            for (int i = 0; i < a.Length; i++)
            {
                if (i > 0)
                    builder.Append(strSplit);

                builder.Append(a.GetValue(i).ToString());
            }

            return builder.ToString();
        }

        /// <summary>
        /// 判断字符串中数组中是否包含某字符串
        /// </summary>
        /// <param name="str">被包含的字符串</param>
        /// <param name="stringarray">字符串数组</param>
        /// <param name="strsplit">分割字符</param>
        /// <returns>返回true、false</returns>
        public static bool IsCompriseStr(string str, string stringarray, string strsplit)
        {
            if (Utils.StrIsNullOrEmpty(stringarray))
                return false;

            str = str.ToLower();
            string[] stringArray = Utils.SplitString(stringarray.ToLower(), strsplit);
            for (int i = 0; i < stringArray.Length; i++)
            {
                if (str.IndexOf(stringArray[i]) > -1)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 判断指定字符串是否属于指定字符串数组中的一个元素
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="stringarray">内部以逗号分割单词的字符串</param>
        /// <returns>判断结果</returns>
        public static bool InArray(string str, string stringarray)
        {
            return InArray(str, SplitString(stringarray, ","), false);
        }

        /// <summary>
        /// 判断指定字符串是否属于指定字符串数组中的一个元素
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="stringarray">内部以逗号分割单词的字符串</param>
        /// <param name="strsplit">分割字符串</param>
        /// <returns>判断结果</returns>
        public static bool InArray(string str, string stringarray, string strsplit)
        {
            return InArray(str, SplitString(stringarray, strsplit), false);
        }

        /// <summary>
        /// 判断指定字符串是否属于指定字符串数组中的一个元素
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="stringarray">内部以逗号分割单词的字符串</param>
        /// <param name="strsplit">分割字符串</param>
        /// <param name="caseInsensetive">是否不区分大小写, true为不区分, false为区分</param>
        /// <returns>判断结果</returns>
        public static bool InArray(string str, string stringarray, string strsplit, bool caseInsensetive)
        {
            return InArray(str, SplitString(stringarray, strsplit), caseInsensetive);
        }

        /// <summary>
        /// 判断指定字符串是否属于指定字符串数组中的一个元素
        /// </summary>
        /// <param name="str">字符串</param>
        /// <param name="stringarray">字符串数组</param>
        /// <returns>判断结果</returns>
        public static bool InArray(string str, string[] stringarray)
        {
            return InArray(str, stringarray, false);
        }

        /// <summary>
        /// 判断指定字符串是否属于指定字符串数组中的一个元素
        /// </summary>
        /// <param name="strSearch">字符串</param>
        /// <param name="stringArray">字符串数组</param>
        /// <param name="caseInsensetive">是否不区分大小写, true为不区分, false为区分</param>
        /// <returns>判断结果</returns>
        public static bool InArray(string strSearch, string[] stringArray, bool caseInsensetive)
        {
            return GetInArrayID(strSearch, stringArray, caseInsensetive) >= 0;
        }

        /// <summary>
        /// 判断指定字符串在指定字符串数组中的位置
        /// </summary>
        /// <param name="strSearch">字符串</param>
        /// <param name="stringArray">字符串数组</param>
        /// <returns>字符串在指定字符串数组中的位置, 如不存在则返回-1</returns>		
        public static int GetInArrayID(string strSearch, string[] stringArray)
        {
            return GetInArrayID(strSearch, stringArray, true);
        }

        /// <summary>
        /// 判断指定字符串在指定字符串数组中的位置
        /// </summary>
        /// <param name="strSearch">字符串</param>
        /// <param name="stringArray">字符串数组</param>
        /// <param name="caseInsensetive">是否不区分大小写, true为不区分, false为区分</param>
        /// <returns>字符串在指定字符串数组中的位置, 如不存在则返回-1</returns>
        public static int GetInArrayID(string strSearch, string[] stringArray, bool caseInsensetive)
        {
            if (caseInsensetive)
            {
                for (int i = 0; i < stringArray.Length; i++)
                {
                    if (strSearch.ToLower() == stringArray[i].ToLower())
                        return i;
                }
            }
            else
            {
                for (int i = 0; i < stringArray.Length; i++)
                {
                    if (strSearch == stringArray[i])
                        return i;
                }
            }

            return -1;
        }

        /// <summary>
        /// 分割字符串
        /// </summary>
        /// <param name="strContent">被分割的字符串</param>
        /// <param name="strSplit">分割符</param>
        /// <param name="ignoreRepeatItem">忽略重复项</param>
        /// <param name="maxElementLength">单个元素最大长度</param>
        /// <returns></returns>
        public static string[] SplitString(string strContent, string strSplit, bool ignoreRepeatItem, int maxElementLength)
        {
            string[] result = SplitString(strContent, strSplit);

            return ignoreRepeatItem ? DistinctStringArray(result, maxElementLength) : result;
        }

        /// <summary>
        /// 分割字符串
        /// </summary>
        /// <param name="strContent">被分割的字符串</param>
        /// <param name="strSplit"分割符></param>
        /// <param name="ignoreRepeatItem">忽略重复项</param>
        /// <param name="minElementLength">单个元素最小长度</param>
        /// <param name="maxElementLength">单个元素最大长度</param>
        /// <returns></returns>
        public static string[] SplitString(string strContent, string strSplit, bool ignoreRepeatItem, int minElementLength, int maxElementLength)
        {
            string[] result = SplitString(strContent, strSplit);

            if (ignoreRepeatItem)
            {
                result = DistinctStringArray(result);
            }

            return PadStringArray(result, minElementLength, maxElementLength);
        }

        /// <summary>
        /// 分割字符串
        /// </summary>
        /// <param name="strContent">被分割的字符串</param>
        /// <param name="strSplit">分割符</param>
        /// <param name="ignoreRepeatItem">忽略重复项</param>
        /// <returns></returns>
        public static string[] SplitString(string strContent, string strSplit, bool ignoreRepeatItem)
        {
            return SplitString(strContent, strSplit, ignoreRepeatItem, 0);
        }

        /// <summary>
        /// 清除字符串数组中的重复项
        /// </summary>
        /// <param name="strArray">字符串数组</param>
        /// <param name="maxElementLength">字符串数组中单个元素的最大长度</param>
        /// <returns></returns>
        public static string[] DistinctStringArray(string[] strArray, int maxElementLength)
        {
            Hashtable h = new Hashtable();

            foreach (string s in strArray)
            {
                string k = s;
                if (maxElementLength > 0 && k.Length > maxElementLength)
                {
                    k = k.Substring(0, maxElementLength);
                }

                h[k.Trim()] = s;
            }

            string[] result = new string[h.Count];

            h.Keys.CopyTo(result, 0);

            return result;
        }

        /// <summary>
        /// 清除字符串数组中的重复项
        /// </summary>
        /// <param name="strArray">字符串数组</param>
        /// <returns></returns>
        public static string[] DistinctStringArray(string[] strArray)
        {
            return DistinctStringArray(strArray, 0);
        }

        /// <summary>
        /// 过滤字符串数组中每个元素为合适的大小
        /// 当长度小于minLength时，忽略掉,-1为不限制最小长度
        /// 当长度大于maxLength时，取其前maxLength位
        /// 如果数组中有null元素，会被忽略掉
        /// </summary>
        /// <param name="strArray">字符串数组</param>
        /// <param name="minLength">单个元素最小长度</param>
        /// <param name="maxLength">单个元素最大长度</param>
        /// <returns></returns>
        public static string[] PadStringArray(string[] strArray, int minLength, int maxLength)
        {
            if (minLength > maxLength)
            {
                int t = maxLength;
                maxLength = minLength;
                minLength = t;
            }

            int iMiniStringCount = 0;
            for (int i = 0; i < strArray.Length; i++)
            {
                if (minLength > -1 && strArray[i].Length < minLength)
                {
                    strArray[i] = null;
                    continue;
                }

                if (strArray[i].Length > maxLength)
                    strArray[i] = strArray[i].Substring(0, maxLength);

                iMiniStringCount++;
            }

            string[] result = new string[iMiniStringCount];
            for (int i = 0, j = 0; i < strArray.Length && j < result.Length; i++)
            {
                if (strArray[i] != null && strArray[i] != string.Empty)
                {
                    result[j] = strArray[i];
                    j++;
                }
            }

            return result;
        }

        /// <summary>
        /// 字符串数组求和
        /// </summary>
        /// <param name="strArray">字符串数组</param>
        /// <returns></returns>
        public static decimal GetStringArraySum(string[] strArray)
        {
            decimal total = 0;
            for (int i = 0; i < strArray.Length; i++)
            {
                try
                {
                    total += StrToDecimal(strArray[i], 0);
                }
                catch
                {
                    continue;
                }
            }

            return total;
        }

        #endregion

        #region 字符串处理

        /// <summary>
        /// 返回字符串真实长度, 1个汉字长度为2
        /// </summary>
        /// <returns>字符长度</returns>
        public static int GetStringLength(string str)
        {
            return Encoding.Default.GetBytes(str).Length;
        }

        /// <summary>
        /// 字段串是否为Null或为""(空)
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool StrIsNullOrEmpty(string str)
        {
            if (str == null || str.Trim() == string.Empty)
                return true;

            return false;
        }

        public static bool StrIsNullOrEmpty(object str)
        {
            if (str == null || str.ToString().Trim() == string.Empty)
                return true;

            return false;
        }

        /// <summary>
        /// 字段串去除空格后是否为Null或为""(空),
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool StrIsNullOrEmptyNoSpace(string str)
        {
            str = RemoveSpaces(str);
            if (str == null || str.Trim() == string.Empty)
                return true;

            return false;
        }

        /// <summary>
        /// 清除给定字符串中的回车及换行符
        /// </summary>
        /// <param name="str">要清除的字符串</param>
        /// <returns>清除后返回的字符串</returns>
        public static string ClearBR(string str)
        {
            Match m = null;
            for (m = RegexBr.Match(str); m.Success; m = m.NextMatch())
            {
                str = str.Replace(m.Groups[0].ToString(), "");
            }

            return str;
        }

        /// <summary>
        /// 从字符串的指定位置开始截取到字符串结尾的符串
        /// </summary>
        /// <param name="str">原字符串</param>
        /// <param name="startIndex">子字符串的起始位置</param>
        /// <returns>子字符串</returns>
        public static string CutString(string str, int startIndex)
        {
            return CutString(str, startIndex, str.Length);
        }

        /// <summary>
        /// 从字符串的指定位置截取指定长度的子字符串
        /// </summary>
        /// <param name="str">原字符串</param>
        /// <param name="startIndex">子字符串的起始位置</param>
        /// <param name="length">子字符串的长度</param>
        /// <returns>子字符串</returns>
        public static string CutString(string str, int startIndex, int length)
        {
            if (startIndex >= 0)
            {
                if (length < 0)
                {
                    length = length * -1;
                    if (startIndex - length < 0)
                    {
                        length = startIndex;
                        startIndex = 0;
                    }
                    else
                        startIndex = startIndex - length;
                }

                if (startIndex > str.Length)
                    return "";
            }
            else
            {
                if (length < 0)
                    return "";
                else
                {
                    if (length + startIndex > 0)
                    {
                        length = length + startIndex;
                        startIndex = 0;
                    }
                    else
                        return "";
                }
            }

            if (str.Length - startIndex < length)
                length = str.Length - startIndex;

            return str.Substring(startIndex, length);
        }

        /// <summary>
        /// 字符串如果操过指定长度则将超出的部分用指定字符串代替
        /// </summary>
        /// <param name="p_SrcString">要检查的字符串</param>
        /// <param name="p_Length">指定长度</param>
        /// <param name="p_TailString">用于替换的字符串</param>
        /// <returns>截取后的字符串</returns>
        public static string GetSubString(string p_SrcString, int p_Length, string p_TailString)
        {
            return GetSubString(p_SrcString, 0, p_Length, p_TailString);
        }

        /// <summary>
        /// 取指定长度的字符串
        /// </summary>
        /// <param name="p_SrcString">要检查的字符串</param>
        /// <param name="p_StartIndex">起始位置</param>
        /// <param name="p_Length">指定长度</param>
        /// <param name="p_TailString">用于替换的字符串</param>
        /// <returns>截取后的字符串</returns>
        public static string GetSubString(string p_SrcString, int p_StartIndex, int p_Length, string p_TailString)
        {
            StringBuilder buffer = new StringBuilder(p_Length);
            int length = 0;
            int index = 0;
            while (index < p_SrcString.Length)
            {
                char c = p_SrcString[index];
                length += Encoding.Default.GetByteCount(new char[] { c });
                if (length <= p_Length)
                {
                    buffer.Append(c);
                }
                else
                {
                    break;
                }

                index++;
            }

            if (index < p_SrcString.Length && !string.IsNullOrEmpty(p_TailString)) buffer.Append(p_TailString);
            return buffer.ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="str"></param>
        /// <param name="len"></param>
        /// <param name="p_TailString"></param>
        /// <returns></returns>
        public static string GetUnicodeSubString(string str, int len, string p_TailString)
        {
            str = str.TrimEnd();
            string result = string.Empty; // 最终返回的结果
            int byteLen = System.Text.Encoding.Default.GetByteCount(str); // 单字节字符长度
            int charLen = str.Length; // 把字符平等对待时的字符串长度
            int byteCount = 0; // 记录读取进度
            int pos = 0; // 记录截取位置
            if (byteLen > len)
            {
                for (int i = 0; i < charLen; i++)
                {
                    if (Convert.ToInt32(str.ToCharArray()[i]) > 255) // 按中文字符计算加2
                        byteCount += 2;
                    else // 按英文字符计算加1
                        byteCount += 1;
                    if (byteCount > len) // 超出时只记下上一个有效位置
                    {
                        pos = i;
                        break;
                    }
                    else if (byteCount == len) // 记下当前位置
                    {
                        pos = i + 1;
                        break;
                    }
                }

                if (pos >= 0)
                    result = str.Substring(0, pos) + p_TailString;
            }
            else
                result = str;

            return result;
        }

        /// <summary>
        /// 自定义的替换字符串函数
        /// </summary>
        /// <param name="SourceString">源字符串</param>
        /// <param name="SearchString">替换的字符串</param>
        /// <param name="ReplaceString">替换为的字符串</param>
        /// <returns>替换后的字符串</returns>
        public static string ReplaceString(string SourceString, string SearchString, string ReplaceString)
        {
            return Utils.ReplaceString(SourceString, SearchString, ReplaceString, false);
        }

        /// <summary>
        /// 自定义的替换字符串函数
        /// </summary>
        /// <param name="SourceString">源字符串</param>
        /// <param name="SearchString">替换的字符串</param>
        /// <param name="ReplaceString">替换为的字符串</param>
        /// <param name="IsCaseInsensetive">是否区分大小写，true不区分，false区分</param>
        /// <returns>替换后的字符串</returns>
        public static string ReplaceString(string SourceString, string SearchString, string ReplaceString, bool IsCaseInsensetive)
        {
            return Regex.Replace(SourceString, Regex.Escape(SearchString), ReplaceString, IsCaseInsensetive ? RegexOptions.IgnoreCase : RegexOptions.None);
        }

        /// <summary>
        /// 通过自定义正则，自定义的替换字符串函数
        /// </summary>
        /// <param name="SourceString">源字符串</param>
        /// <param name="SearchString">替换的字符串，正则表达式</param>
        /// <param name="ReplaceString">替换为的字符串</param>
        /// <returns>替换后的字符串</returns>
        public static string RegReplaceString(string SourceString, string SearchString, string ReplaceString)
        {
            return Utils.RegReplaceString(SourceString, SearchString, ReplaceString, false);
        }

        /// <summary>
        /// 通过自定义正则，自定义的替换字符串函数
        /// </summary>
        /// <param name="SourceString">源字符串</param>
        /// <param name="SearchString">替换的字符串，正则表达式</param>
        /// <param name="ReplaceString">替换为的字符串</param>
        /// <param name="IsCaseInsensetive">是否区分大小写，true不区分，false区分</param>
        /// <returns>替换后的字符串</returns>
        public static string RegReplaceString(string SourceString, string SearchString, string ReplaceString, bool IsCaseInsensetive)
        {
            return Regex.Replace(SourceString, SearchString, ReplaceString, IsCaseInsensetive ? RegexOptions.IgnoreCase : RegexOptions.None);
        }

        /// <summary>
        /// 替换URL地址中的IP
        /// </summary>
        /// <param name="SourceString"></param>
        /// <param name="IP"></param>
        /// <returns></returns>
        public static string RegReplaceIP(string SourceString, string url)
        {
            if (string.IsNullOrEmpty(SourceString)) return SourceString;
            //Regex r = new Regex("(?<=://).*?(?=:)", RegexOptions.IgnoreCase);
            Regex r = new Regex("(?<=http://).*?(?=/)", RegexOptions.IgnoreCase);
            return Regex.Replace(SourceString, r.Match(SourceString).Value, url, RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 替换URL地址中的IP
        /// </summary>
        /// <param name="r"></param>
        /// <param name="SourceString"></param>
        /// <param name="IP"></param>
        /// <returns></returns>
        public static string RegReplaceIP(Regex r, string SourceString, string IP)
        {
            if (string.IsNullOrEmpty(SourceString)) return SourceString;
            return Regex.Replace(SourceString, r.Match(SourceString).Value, IP, RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 生成指定数量的html空格符号
        /// </summary>
        /// <param name="spacesCount">个数</param>
        /// <returns></returns>
        public static string GetSpacesString(int spacesCount)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < spacesCount; i++)
            {
                sb.Append("&nbsp;&nbsp;");
            }

            return sb.ToString();
        }

        /// <summary>
        /// 过滤空格
        /// </summary>
        /// <param name="str">需要过滤的字符串</param>
        /// <returns></returns>
        public static string RemoveSpaces(string str)
        {
            str = Regex.Replace(str, "[\f\n\r\t\v]", "");
            str = Regex.Replace(str, " {2,}", " ");
            str = Regex.Replace(str, ">[ ]{1}", ">");
            return str;
        }

        ///// <summary>
        ///// 检测是否有危险的可能用于链接的字符串
        ///// </summary>
        ///// <param name="str">要判断字符串</param>
        ///// <returns>判断结果</returns>
        //public static bool IsSafeUserInfoString(string str)
        //{
        //    return !Regex.IsMatch(str, @"^\s*$|^c:\\con\\con$|[%,\*" + "\"" + @"\s\t\<\>\&]|游客|^Guest");
        //}
        /// <summary>
        /// 清理字符串
        /// </summary>
        public static string CleanInput(string strIn)
        {
            return Regex.Replace(strIn.Trim(), @"[^\w\.@-]", "");
        }

        /// <summary>
        /// 返回URL中结尾的文件名
        /// </summary>		
        public static string GetFilename(string url)
        {
            if (url == null)
            {
                return "";
            }

            string[] strs1 = url.Split(new char[] { '/' });
            return strs1[strs1.Length - 1].Split(new char[] { '?' })[0];
        }

        /// <summary>
        /// 根据阿拉伯数字返回月份的名称(可更改为某种语言)
        /// </summary>	
        public static string[] Monthes
        {
            get { return new string[] { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" }; }
        }

        /// <summary>
        /// 替换回车换行符为html换行符
        /// </summary>
        public static string StrFormat(string str)
        {
            string str2;

            if (str == null)
            {
                str2 = "";
            }
            else
            {
                str = str.Replace("\r\n", "<br />");
                str = str.Replace("\n", "<br />");
                str2 = str;
            }

            return str2;
        }

        /// <summary>
        /// 转换为简体中文
        /// </summary>
        //public static string ToSChinese(string str)
        //{
        //    return Strings.AscW(.StrConv(str, VbStrConv.SimplifiedChinese, 0);
        //}
        /// <summary>
        /// 转换为繁体中文
        /// </summary>
        //public static string ToTChinese(string str)
        //{
        //    return Strings.StrConv(str, VbStrConv.TraditionalChinese, 0);
        //}
        /// <summary>
        /// 替换html字符
        /// </summary>
        public static string EncodeHtml(string strHtml)
        {
            if (strHtml != "")
            {
                strHtml = strHtml.Replace(",", "&def");
                strHtml = strHtml.Replace("'", "&dot");
                strHtml = strHtml.Replace(";", "&dec");
                return strHtml;
            }

            return "";
        }

        /// <summary>
        /// 进行指定的替换(脏字过滤)
        /// </summary>
        /// <param name="bantext">需要过滤的词语（a=b）</param>
        /// <param name="str">过滤的字符串</param>
        public static string StrFilter(string str, string bantext)
        {
            string text1 = "", text2 = "";
            string[] textArray1 = SplitString(bantext, "\n");
            for (int num1 = 0; num1 < textArray1.Length; num1++)
            {
                text1 = textArray1[num1].Substring(0, textArray1[num1].IndexOf("="));
                text2 = textArray1[num1].Substring(textArray1[num1].IndexOf("=") + 1);
                str = str.Replace(text1, text2);
            }

            return str;
        }

        /// <summary>
        /// 从HTML中获取文本,保留br,p,img
        /// </summary>
        /// <param name="HTML"></param>
        /// <returns></returns>
        public static string GetTextFromHTML(string HTML)
        {
            Regex regEx = new Regex(@"</?(?!br|/?p|img)[^>]*>", RegexOptions.IgnoreCase);

            return regEx.Replace(HTML, "");
        }

        /// <summary>
        /// 合并字符
        /// </summary>
        /// <param name="source">要合并的源字符串</param>
        /// <param name="target">要被合并到的目的字符串</param>
        /// <param name="mergechar">合并符</param>
        /// <returns>并到字符串</returns>
        public static string MergeString(string source, string target, string mergechar)
        {
            if (Utils.StrIsNullOrEmpty(target))
                target = source;
            else
                target += mergechar + source;

            return target;
        }

        /// <summary>
        /// 清除UBB标签
        /// </summary>
        /// <param name="sDetail">帖子内容</param>
        /// <returns>帖子内容</returns>
        public static string ClearUBB(string sDetail)
        {
            return Regex.Replace(sDetail, @"\[[^\]]*?\]", string.Empty, RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 根据字符串获取枚举值
        /// </summary>
        /// <typeparam name="T">枚举类型</typeparam>
        /// <param name="value">字符串枚举值</param>
        /// <param name="defValue">缺省值</param>
        /// <returns></returns>
        public static T GetEnum<T>(string value, T defValue)
        {
            try
            {
                return (T)Enum.Parse(typeof(T), value, true);
            }
            catch (ArgumentException)
            {
                return defValue;
            }
        }

        /// <summary>
        /// 移除Html标记
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        public static string RemoveHtml(string content)
        {
            return Regex.Replace(content, @"<[^>]*>", string.Empty, RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 过滤HTML中的不安全标签
        /// </summary>
        /// <param name="content"></param>
        /// <returns></returns>
        public static string RemoveUnsafeHtml(string content)
        {
            content = Regex.Replace(content, @"(\<|\s+)o([a-z]+\s?=)", "$1$2", RegexOptions.IgnoreCase);
            content = Regex.Replace(content, @"(script|frame|form|meta|behavior|style)([\s|:|>])+", "$1.$2", RegexOptions.IgnoreCase);
            return content;
        }

        /// <summary>
        /// 提取字符串中的数字
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static int? ExtractNumber(string str)
        {
            if (string.IsNullOrEmpty(str)) return null;

            string res = System.Text.RegularExpressions.Regex.Replace(str.Trim(), @"[^0-9]+", "");

            if (int.TryParse(res, out int d))
                return d;

            return null;
        }

        #endregion

        #region 时间处理

        /// <summary>
        /// 返回标准日期格式string
        /// </summary>
        public static string GetDate()
        {
            return DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 返回指定yyyy-MM-dd日期格式
        /// </summary>
        /// <param name="datetimestr">日期字符串</param>
        /// <param name="replacestr">空日期是替换的字符串</param>
        /// <returns></returns>
        public static string GetDate(string datetimestr, string replacestr)
        {
            if (datetimestr == null)
                return replacestr;

            if (datetimestr.Equals(""))
                return replacestr;

            try
            {
                datetimestr = Convert.ToDateTime(datetimestr).ToString("yyyy-MM-dd").Replace("1900-01-01", replacestr);
            }
            catch
            {
                return replacestr;
            }

            return datetimestr;
        }

        /// <summary>
        /// 返回标准时间格式string
        /// </summary>
        public static string GetTime()
        {
            return DateTimeHelper.GetNowTime().ToString("HH:mm:ss");
        }

        /// <summary>
        /// 返回标准时间格式string
        /// </summary>
        public static string GetDateTime()
        {
            return DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 返回相对于当前时间的相对天数
        /// </summary>
        public static string GetDateTime(int relativeday)
        {
            return DateTimeHelper.GetNowTime().AddDays(relativeday).ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 返回标准时间格式string
        /// </summary>
        public static string GetDateTimeF()
        {
            return DateTimeHelper.GetNowTime().ToString("yyyy-MM-dd HH:mm:ss:fffffff");
        }

        /// <summary>
        /// 返回标准时间 yyyy-MM-dd HH:mm:ss
        /// </sumary>
        public static string GetStandardDateTime(string fDateTime)
        {
            return GetStandardDateTime(fDateTime, "yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 返回标准时间 yyyy-MM-dd
        /// </sumary>
        public static string GetStandardDate(string fDate)
        {
            return GetStandardDateTime(fDate, "yyyy-MM-dd");
        }

        /// <summary>
        /// 返回标准时间 
        /// </sumary>
        public static string GetStandardDateTime(string fDateTime, string formatStr)
        {
            if (fDateTime == "0000-0-0 0:00:00")
                return fDateTime;
            DateTime time = new DateTime(1900, 1, 1, 0, 0, 0, 0);
            if (DateTime.TryParse(fDateTime, out time))
                return time.ToString(formatStr);
            else
                return "N/A";
        }

        public static string AdDeTime(int times)
        {
            return (DateTimeHelper.GetNowTime()).AddMinutes(times).ToString();
        }

        /// <summary>
        /// 时间转UTC时间
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static DateTime TimeToUTC0(DateTime date)
        {
            return date.ToUniversalTime();
        }

        /// <summary>
        /// utc时间转当地时间
        /// </summary>
        /// <param name="date"></param>
        /// <param name="utc">utc时差</param>
        /// <returns></returns>
        public static DateTime UTCToLocation(DateTime date, int utc)
        {
            return date.AddHours(utc);
        }

        /// <summary>
        /// 获取时间戳
        /// </summary>
        /// <returns></returns>
        public static string GetTimeStamp()
        {
            TimeSpan ts = DateTime.Now - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds).ToString();
        }

        /// <summary>
        /// 得到时间差 （x天x小时x分钟）
        /// </summary>
        /// <param name="DateTime1">当前时间</param>
        /// <param name="DateTime2">过往时间</param>
        /// <returns></returns>
        public static string DateDiff(DateTime DateTime1, DateTime DateTime2)
        {
            string dateDiff = null;
            TimeSpan ts1 = new TimeSpan(DateTime1.Ticks);
            TimeSpan ts2 = new
                TimeSpan(DateTime2.Ticks);
            TimeSpan ts = ts1.Subtract(ts2).Duration();
            dateDiff = ts.Days.ToString() + "天" + ts.Hours.ToString() + "小时" + ts.Minutes.ToString() + "分钟"; // +ts.Seconds.ToString() + "秒";

            return dateDiff;
        }


        public static string GetDateDiff(DateTime DateTime1, DateTime DateTime2)
        {
            string dateDiff = null;
            TimeSpan ts1 = new TimeSpan(DateTime1.Ticks);
            TimeSpan ts2 = new
                TimeSpan(DateTime2.Ticks);
            TimeSpan ts = ts1.Subtract(ts2).Duration();
            dateDiff = ts.Days.ToString();

            return dateDiff;
        }


        /// <summary>
        /// 得到时间差（分钟，舍弃小数部分）
        /// </summary>
        /// <param name="DateTime1">当前时间</param>
        /// <param name="DateTime2">过往时间</param>
        public static int MinutesDiff(DateTime DateTime1, DateTime DateTime2)
        {
            TimeSpan ts = DateTime1 - DateTime2;
            //int n = ts.Minutes;//分差
            return (int)ts.TotalMinutes; //总分差，舍弃小数部分
        }

        /// <summary>
        /// 得到时间（x天x小时x分钟）
        /// </summary>
        /// <param name="second">秒</param>
        /// <returns></returns>
        public static string DateDiffStr(int second)
        {
            int day = second / (3600 * 24);
            int hour = second % (3600 * 24) / 3600;
            int min = second % 3600 / 60;
            //int sec = second%60;
            //return string.Format("{0}天{1}小时{2}分钟{3}秒", day, hour, min, sec);
            return string.Format("{0}天{1}小时{2}分钟", day, hour, min);
        }

        #endregion

        #region 验证

        /// <summary>
        /// 检查输入的字符是否有效
        /// 返回 false: 表示charKey为无效字符
        /// </summary>
        /// <param name="charKey">e.KeyChar</param>
        /// <param name="sValidText">有效字符串</param>
        /// <param name="bExclude">如果为true，则表示sValidText里面的是非法字符</param>
        /// <returns>返回 false:表示charKey为无效字符</returns>
        public static bool ValidText(char charKey, string sValidText, bool bExclude = false)
        {
            try
            {
                bool bValid;
                string sKey = charKey.ToString();
                if (sKey == "\b") //Backspace
                {
                    return true;
                }

                if (bExclude == false)
                    bValid = sValidText.Contains(sKey);
                else
                    bValid = !sValidText.Contains(sKey);

                return bValid;
            }
            catch (System.Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 是否全是数字字符串
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsNumeric(string str) //接收一个string类型的参数,保存到str里
        {
            if (string.IsNullOrEmpty(str) || str.Length == 0) //验证这个参数是否为空
                return false; //是，就返回False
            ASCIIEncoding ascii = new ASCIIEncoding(); //new ASCIIEncoding 的实例
            byte[] bytestr = ascii.GetBytes(str); //把string类型的参数保存到数组里
            foreach (byte c in bytestr) //遍历这个数组里的内容
            {
                if (c < 48 || c > 57) //判断是否为数字
                {
                    return false; //不是，就返回False
                }
            }

            return true; //是，就返回True
        }
        /// <summary>
        /// 是否全是数字字符串，包含小数
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsNumericAll(string str)
        {
            if (string.IsNullOrEmpty(str) || str.Length == 0)
                return false;

            ASCIIEncoding ascii = new ASCIIEncoding();
            byte[] bytestr = ascii.GetBytes(str);

            bool hasDecimalPoint = false; // 用于检查小数点是否已经出现过

            foreach (byte c in bytestr)
            {
                if (c >= 48 && c <= 57) // 数字字符
                {
                    continue;
                }
                else if (c == 46) // 小数点 (ASCII 码 46)
                {
                    if (hasDecimalPoint) // 如果已经有一个小数点，则返回 false
                        return false;

                    hasDecimalPoint = true; // 第一次遇到小数点，标记为已存在
                }
                else
                {
                    // 如果既不是数字也不是小数点，返回 false
                    return false;
                }
            }

            // 确保字符串不以小数点开头或结尾
            if (str[0] == '.' || str[str.Length - 1] == '.')
                return false;

            return true;
        }

        public static bool AreNumbersEqual(string value1, string value2)
        {
            // 尝试将两个输入转换为 decimal 类型
            if (decimal.TryParse(value1, out decimal number1) && decimal.TryParse(value2, out decimal number2))
            {
                // 比较两个数是否相等
                return number1 == number2;
            }
            else
            {
                // 如果任意输入不是数字，返回 false
                return false;
            }
        }


        /// <summary>
        /// 车牌号码是否属于单号
        /// </summary>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static bool IsSingleNum(string carno)
        {
            var chkstr = carno;
            for (int i = 0; i < carno.Length; i++)
            {
                var str = chkstr.Last().ToString();
                chkstr = chkstr.Remove(chkstr.Length - 1);
                if (IsNumeric(str))
                {
                    if (ObjectToInt(str, 0) % 2 == 0)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 车牌号码尾号（若匹配不到数字，默认0）
        /// </summary>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static string GetCarEndNum(string carno)
        {
            var chkstr = carno;
            for (int i = 0; i < carno.Length; i++)
            {
                var str = chkstr.Last().ToString();
                chkstr = chkstr.Remove(chkstr.Length - 1);
                if (IsNumeric(str))
                {
                    return str;
                }
            }

            return "0";
        }

        /// <summary>
        /// 是否为数值串列表，各数值间用","间隔
        /// </summary>
        /// <param name="numList"></param>
        /// <returns></returns>
        public static bool IsNumericList(string numList)
        {
            if (StrIsNullOrEmpty(numList))
                return false;

            return IsIntArray(numList.Split(','));
        }

        public static bool IsNumericList(string numList, char splitstr)
        {
            if (StrIsNullOrEmpty(numList))
                return false;

            return IsIntArray(numList.Split(splitstr));
        }

        /// <summary>
        /// 检查颜色值是否为3/6位的合法颜色
        /// </summary>
        /// <param name="color">待检查的颜色</param>
        /// <returns></returns>
        public static bool CheckColorValue(string color)
        {
            if (StrIsNullOrEmpty(color))
                return false;

            color = color.Trim().Trim('#');

            if (color.Length != 3 && color.Length != 6)
                return false;

            //不包含0-9  a-f以外的字符
            if (!Regex.IsMatch(color, "[^0-9a-f]", RegexOptions.IgnoreCase))
                return true;

            return false;
        }

        #region 验证邮箱

        /// <summary>
        /// 验证是否是邮箱
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>是返回true</returns>
        public static bool IsEmail(string source)
        {
            return Regex.IsMatch(source, @"^[A-Za-z0-9](([_\.\-]?[a-zA-Z0-9]+)*)@([A-Za-z0-9]+)(([\.\-]?[a-zA-Z0-9]+)*)\.([A-Za-z]{2,})$", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 验证是否包含邮箱
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>包含返回true</returns>
        public static bool HasEmail(string source)
        {
            return Regex.IsMatch(source, @"[A-Za-z0-9](([_\.\-]?[a-zA-Z0-9]+)*)@([A-Za-z0-9]+)(([\.\-]?[a-zA-Z0-9]+)*)\.([A-Za-z]{2,})", RegexOptions.IgnoreCase);
        }

        #endregion

        #region 验证网址

        /// <summary>
        /// 验证是否是网址
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>是返回true</returns>
        public static bool IsUrl(string source)
        {
            return Regex.IsMatch(source, @"^(((file|gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)://)|(www\.))+(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(/[a-zA-Z0-9\&amp;%_\./-~-]*)?$", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 验证字符串是否包含网址
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>包含返回true</returns>
        public static bool HasUrl(string source)
        {
            return Regex.IsMatch(source, @"(((file|gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)://)|(www\.))+(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(/[a-zA-Z0-9\&amp;%_\./-~-]*)?", RegexOptions.IgnoreCase);
        }

        #endregion

        #region 验证日期

        /// <summary>
        /// 验证是否是日期格式
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>是返回true</returns>
        public static bool IsDateTime(string source)
        {
            try
            {
                DateTime time = Convert.ToDateTime(source);
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 验证时间

        /// <summary>
        /// 判断是否为时间字符串
        /// </summary>
        /// <param name="timeval">验证的字符串</param>
        /// <returns></returns>
        public static bool IsTime(string timeval)
        {
            return Regex.IsMatch(timeval, @"^((([0-1]?[0-9])|(2[0-3])):([0-5]?[0-9])(:[0-5]?[0-9])?)$");
        }

        #endregion

        #region 验证车牌

        /// <summary>
        /// 验证是否是车牌
        /// </summary>
        /// <param name="carno"></param>
        /// <returns></returns>
        public static bool ValidationCarNo(string carno)
        {
            bool result = false;
            if (string.IsNullOrEmpty(carno))
            {
                return result;
            }

            // 7位常规车牌
            string express7 = @"^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼军警民电A-Z0-9]{1}[A-Z0-9航]{1}[A-Z0-9]{4}[A-Z0-9学港澳领使警挂应急]{1}$";

            // 8位常规车牌
            string express8 = @"^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼W]{1}[A-Z]{1}[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼应急A-Z0-9]{6}$";

            // 支持带常规颜色的车牌
            string expressSpecial = @"^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z]{1,2}[0-9]{4,5}(蓝|黄|白|黑|绿|黄绿)$";

            if (carno.Length == 7)
            {
                result = Regex.IsMatch(carno, express7);
            }
            else if (carno.Length == 8)
            {
                result = Regex.IsMatch(carno, express8);
                if (!result)
                {
                    result = carno.StartsWith("民航");
                }
            }

            if (!result)
            {
                result = Regex.IsMatch(carno, expressSpecial);
            }

            return result;
        }

        #endregion

        #region 验证手机号

        /// <summary>
        /// 验证手机号
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>正确返回true</returns>
        public static bool IsPhone(string source)
        {
            return Regex.IsMatch(source, @"^(((13[0-9]{1})|(14[579])|(15[0-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1}))+\d{8})$", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 验证手机号
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <param name="countrycode">国家代码：CN</param>
        /// <returns>正确返回true</returns>
        public static bool IsMobile(string source, string countrycode)
        {
            switch (countrycode.ToUpper())
            {
                case "AU":
                    return Regex.IsMatch(source, @"^(61){1}\-?4\d{8,9}$", RegexOptions.IgnoreCase);
                case "CA":
                    return Regex.IsMatch(source, @"^(1){1}\-?\d{10}$", RegexOptions.IgnoreCase);
                case "CN":
                    return Regex.IsMatch(source, @"^(86){1}\-?(13[0-9]|15[012356789]|18[0-9]|14[57]|17[013678])[0-9]{8}$", RegexOptions.IgnoreCase);
                case "FR":
                    return Regex.IsMatch(source, @"^(33){1}\-?[168](\d{5}|\d{7,8})$", RegexOptions.IgnoreCase);
                case "DE":
                    return Regex.IsMatch(source, @"^(49){1}\-?1(\d{5,6}|\d{9,12})$", RegexOptions.IgnoreCase);
                case "ID":
                    return Regex.IsMatch(source, @"^(62){1}\-?[2-9]\d{7,11}$", RegexOptions.IgnoreCase);
                case "IT":
                    return Regex.IsMatch(source, @"^(39){1}\-?[37]\d{8,11}$", RegexOptions.IgnoreCase);
                case "MY":
                    return Regex.IsMatch(source, @"^(60){1}\-?1\d{8}$", RegexOptions.IgnoreCase);
                case "NZ":
                    return Regex.IsMatch(source, @"^(64){1}\-?[278]\d{7,9}$", RegexOptions.IgnoreCase);
                case "NL":
                    return Regex.IsMatch(source, @"^(31){1}\-?6\d{8}$", RegexOptions.IgnoreCase);
                case "PH":
                    return Regex.IsMatch(source, @"^(63){1}\-?[24579](\d{7,9}|\d{12})$", RegexOptions.IgnoreCase);
                case "RU":
                    return Regex.IsMatch(source, @"^(7){1}\-?[13489]\d{9,11}$", RegexOptions.IgnoreCase);
                case "SG":
                    return Regex.IsMatch(source, @"^(65){1}\-?[13689]\d{6,7}$", RegexOptions.IgnoreCase);
                case "SE":
                    return Regex.IsMatch(source, @"^(46){1}\-?[124-7](\d{8}|\d{10}|\d{12})$", RegexOptions.IgnoreCase);
                case "TH":
                    return Regex.IsMatch(source, @"^(66){1}\-?[13456789]\d{7,8}$", RegexOptions.IgnoreCase);
                case "GB":
                    return Regex.IsMatch(source, @"^(44){1}\-?[347-9](\d{8,9}|\d{11,12})$", RegexOptions.IgnoreCase);
                case "UA":
                    return Regex.IsMatch(source, @"^(380){1}\-?[3-79]\d{8,9}$", RegexOptions.IgnoreCase);
                case "US":
                    return Regex.IsMatch(source, @"^(1){1}\-?\d{10,12}$", RegexOptions.IgnoreCase);
                case "VN":
                    return Regex.IsMatch(source, @"^(84){1}\-?[1-9]\d{6,9}$", RegexOptions.IgnoreCase);
                case "HK":
                    return Regex.IsMatch(source, @"^(852){1}\-?0{0,1}[1,5,6,9](?:\d{7}|\d{8}|\d{12})$", RegexOptions.IgnoreCase);
                case "MO":
                    return Regex.IsMatch(source, @"^(853){1}\-?6\d{7}$", RegexOptions.IgnoreCase);
                case "TW":
                    return Regex.IsMatch(source, @"^(886){1}\-?0{0,1}[6,7,9](?:\d{7}|\d{8}|\d{10})$", RegexOptions.IgnoreCase);
                case "KR":
                    return Regex.IsMatch(source, @"^(82){1}\-?0{0,1}[7,1](?:\d{8}|\d{9})$", RegexOptions.IgnoreCase);
                case "JP":
                    return Regex.IsMatch(source, @"^(81){1}\-?0{0,1}[7,8,9](?:\d{8}|\d{9})$", RegexOptions.IgnoreCase);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 验证是否包含手机号码
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>包含返回true</returns>
        public static bool HasMobile(string source)
        {
            return HasMobile(source, "CN");
        }

        public static bool HasMobile(string source, string countrycode)
        {
            switch (countrycode.ToUpper())
            {
                case "AU":
                    return Regex.IsMatch(source, @"(61){1}\-?4\d{8,9}$", RegexOptions.IgnoreCase);
                case "CA":
                    return Regex.IsMatch(source, @"(1){1}\-?\d{10}$", RegexOptions.IgnoreCase);
                case "CN":
                    return Regex.IsMatch(source, @"(86){1}\-?(13[0-9]|15[012356789]|18[0-9]|14[57]|17[0678])[0-9]{8}$", RegexOptions.IgnoreCase);
                case "FR":
                    return Regex.IsMatch(source, @"(33){1}\-?[168](\d{5}|\d{7,8})$", RegexOptions.IgnoreCase);
                case "DE":
                    return Regex.IsMatch(source, @"(49){1}\-?1(\d{5,6}|\d{9,12})$", RegexOptions.IgnoreCase);
                case "ID":
                    return Regex.IsMatch(source, @"(62){1}\-?[2-9]\d{7,11}$", RegexOptions.IgnoreCase);
                case "IT":
                    return Regex.IsMatch(source, @"(39){1}\-?[37]\d{8,11}$", RegexOptions.IgnoreCase);
                case "MY":
                    return Regex.IsMatch(source, @"(60){1}\-?1\d{8}$", RegexOptions.IgnoreCase);
                case "NZ":
                    return Regex.IsMatch(source, @"(64){1}\-?[278]\d{7,9}$", RegexOptions.IgnoreCase);
                case "NL":
                    return Regex.IsMatch(source, @"(31){1}\-?6\d{8}$", RegexOptions.IgnoreCase);
                case "PH":
                    return Regex.IsMatch(source, @"(63){1}\-?[24579](\d{7,9}|\d{12})$", RegexOptions.IgnoreCase);
                case "RU":
                    return Regex.IsMatch(source, @"(7){1}\-?[13489]\d{9,11}$", RegexOptions.IgnoreCase);
                case "SG":
                    return Regex.IsMatch(source, @"(65){1}\-?[13689]\d{6,7}$", RegexOptions.IgnoreCase);
                case "SE":
                    return Regex.IsMatch(source, @"(46){1}\-?[124-7](\d{8}|\d{10}|\d{12})$", RegexOptions.IgnoreCase);
                case "TH":
                    return Regex.IsMatch(source, @"(66){1}\-?[13456789]\d{7,8}$", RegexOptions.IgnoreCase);
                case "GB":
                    return Regex.IsMatch(source, @"(44){1}\-?[347-9](\d{8,9}|\d{11,12})$", RegexOptions.IgnoreCase);
                case "UA":
                    return Regex.IsMatch(source, @"(380){1}\-?[3-79]\d{8,9}$", RegexOptions.IgnoreCase);
                case "US":
                    return Regex.IsMatch(source, @"(1){1}\-?\d{10,12}$", RegexOptions.IgnoreCase);
                case "VN":
                    return Regex.IsMatch(source, @"(84){1}\-?[1-9]\d{6,9}$", RegexOptions.IgnoreCase);
                case "HK":
                    return Regex.IsMatch(source, @"(852){1}\-?0{0,1}[1,5,6,9](?:\d{7}|\d{8}|\d{12})$", RegexOptions.IgnoreCase);
                case "MO":
                    return Regex.IsMatch(source, @"(853){1}\-?6\d{7}$", RegexOptions.IgnoreCase);
                case "TW":
                    return Regex.IsMatch(source, @"(886){1}\-?0{0,1}[6,7,9](?:\d{7}|\d{8}|\d{10})$", RegexOptions.IgnoreCase);
                case "KR":
                    return Regex.IsMatch(source, @"(82){1}\-?0{0,1}[7,1](?:\d{8}|\d{9})$", RegexOptions.IgnoreCase);
                case "JP":
                    return Regex.IsMatch(source, @"(81){1}\-?0{0,1}[7,8,9](?:\d{8}|\d{9})$", RegexOptions.IgnoreCase);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 过略手机号码的中间四位为****
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public static string MobileToHide(string mobile)
        {
            if (mobile.Length < 4) return mobile;
            int len = (mobile.Length - 4);
            if (len % 2 > 0)
                len = len / 2 + 1;
            else
                len = len / 2;

            return mobile.Substring(0, len - 1) + "****" + mobile.Substring(len + 3);
        }

        /// <summary>
        /// 过略email的中间位为****
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public static string EmailToHide(string email)
        {
            if (!IsEmail(email)) return email;
            string[] arr = SplitString(email, "@");
            int leng = arr[0].Length;
            int clen = 0;
            if (leng < 6)
            {
                if (leng < 3)
                {
                    clen = 1;
                }
                else
                    clen = leng - 2;
            }
            else
            {
                clen = leng - 4;
            }

            string rstr = CutString(email, 0, clen);
            for (int i = 0; i < leng - clen; i++)
                rstr += "*";
            rstr += "@" + arr[1];

            return rstr;
        }

        #endregion

        #region 验证IP

        /// <summary>
        /// 验证IP
        /// </summary>
        /// <param name="source">验证的字符串</param>
        /// <returns>是返回true</returns>
        public static bool IsIP(string source)
        {
            return Regex.IsMatch(source, @"^(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])$", RegexOptions.IgnoreCase);
        }

        public static bool HasIP(string source)
        {
            return Regex.IsMatch(source, @"(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 返回指定IP是否在指定的IP数组所限定的范围内, IP数组内的IP地址可以使用*表示该IP段任意, 例如192.168.1.*
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="iparray"></param>
        /// <returns></returns>
        public static bool InIPArray(string ip, string[] iparray)
        {
            string[] userip = Utils.SplitString(ip, @".");

            for (int ipIndex = 0; ipIndex < iparray.Length; ipIndex++)
            {
                string[] tmpip = Utils.SplitString(iparray[ipIndex], @".");
                int r = 0;
                for (int i = 0; i < tmpip.Length; i++)
                {
                    if (tmpip[i] == "*")
                        return true;

                    if (userip.Length > i)
                    {
                        if (tmpip[i] == userip[i])
                            r++;
                        else
                            break;
                    }
                    else
                        break;
                }

                if (r == 4)
                    return true;
            }

            return false;
        }

        #endregion

        #region 验证身份证是否有效

        /// <summary>
        /// 验证身份证是否有效
        /// </summary>
        /// <param name="Id">验证的字符串</param>
        /// <returns>是返回true</returns>
        public static bool IsIDCard(string Id)
        {
            if (Id.Length == 18)
            {
                bool check = IsIDCard18(Id);
                return check;
            }
            else if (Id.Length == 15)
            {
                bool check = IsIDCard15(Id);
                return check;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 验证身份证是否为18位
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public static bool IsIDCard18(string Id)
        {
            long n = 0;
            if (long.TryParse(Id.Remove(17), out n) == false || n < Math.Pow(10, 16) || long.TryParse(Id.Replace('x', '0').Replace('X', '0'), out n) == false)
            {
                return false; //数字验证
            }

            string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
            if (address.IndexOf(Id.Remove(2)) == -1)
            {
                return false; //省份验证
            }

            string birth = Id.Substring(6, 8).Insert(6, "-").Insert(4, "-");
            DateTime time = new DateTime();
            if (DateTime.TryParse(birth, out time) == false)
            {
                return false; //生日验证
            }

            string[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").Split(',');
            string[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").Split(',');
            char[] Ai = Id.Remove(17).ToCharArray();
            int sum = 0;
            for (int i = 0; i < 17; i++)
            {
                sum += int.Parse(Wi[i]) * int.Parse(Ai[i].ToString());
            }

            int y = -1;
            Math.DivRem(sum, 11, out y);
            if (arrVarifyCode[y] != Id.Substring(17, 1).ToLower())
            {
                return false; //校验码验证
            }

            return true; //符合GB11643-1999标准
        }

        /// <summary>
        /// 验证身份证是否为15位
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public static bool IsIDCard15(string Id)
        {
            long n = 0;
            if (long.TryParse(Id, out n) == false || n < Math.Pow(10, 14))
            {
                return false; //数字验证
            }

            string address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
            if (address.IndexOf(Id.Remove(2)) == -1)
            {
                return false; //省份验证
            }

            string birth = Id.Substring(6, 6).Insert(4, "-").Insert(2, "-");
            DateTime time = new DateTime();
            if (DateTime.TryParse(birth, out time) == false)
            {
                return false; //生日验证
            }

            return true; //符合15位身份证标准
        }

        #endregion

        #region 是不是Int型的

        /// <summary>
        /// 是不是Int型的
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsInt(string source)
        {
            Regex regex = new Regex(@"^(-){0,1}\d+$");
            if (regex.Match(source).Success)
            {
                if ((long.Parse(source) > 0x7fffffffL) || (long.Parse(source) < -2147483648L))
                {
                    return false;
                }

                return true;
            }

            return false;
        }

        #endregion

        #region 验证是否是Double类型

        /// <summary>
        /// 是否为Double类型
        /// </summary>
        /// <param name="expression"></param>
        /// <returns></returns>
        public static bool IsDouble(object expression)
        {
            if (expression != null)
                return Regex.IsMatch(expression.ToString(), @"^([0-9])[0-9]*(\.\w*)?$");

            return false;
        }

        #endregion

        #region 验证是否Int字符串数组

        /// <summary>
        /// 判断给定的字符串数组(strNumber)中的数据是不是都为数值型
        /// </summary>
        /// <param name="strNumber">要确认的字符串数组</param>
        /// <returns>是则返加true 不是则返回 false</returns>
        public static bool IsIntArray(string[] strNumber)
        {
            if (strNumber == null)
                return false;

            if (strNumber.Length < 1)
                return false;

            foreach (string id in strNumber)
            {
                if (!IsInt(id))
                    return false;
            }

            return true;
        }

        #endregion

        #region 看字符串的长度是不是在限定数之间 一个中文为两个字符

        /// <summary>
        /// 看字符串的长度是不是在限定数之间 一个中文为两个字符
        /// </summary>
        /// <param name="source">字符串</param>
        /// <param name="begin">大于等于</param>
        /// <param name="end">小于等于</param>
        /// <returns></returns>
        public static bool IsLengthStr(string source, int begin, int end)
        {
            int length = Regex.Replace(source, @"[^\x00-\xff]", "OK").Length;
            if ((length <= begin) && (length >= end))
            {
                return false;
            }

            return true;
        }

        #endregion

        #region 是不是中国电话，格式010-85849685

        /// <summary>
        /// 是不是中国电话，格式010-85849685
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsTel(string source)
        {
            return Regex.IsMatch(source, @"^\d{3,4}-?\d{6,8}$", RegexOptions.IgnoreCase);
        }

        #endregion

        #region 邮政编码 6个数字

        /// <summary>
        /// 邮政编码 6个数字
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsPostCode(string source)
        {
            return Regex.IsMatch(source, @"^\d{6}$", RegexOptions.IgnoreCase);
        }

        #endregion

        #region 中文

        /// <summary>
        /// 中文
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsChinese(string source)
        {
            return Regex.IsMatch(source, @"^[\u4e00-\u9fa5]+$", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 判断是否包含中文
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool hasChinese(string source)
        {
            return Regex.IsMatch(source, @"[\u4e00-\u9fa5]+", RegexOptions.IgnoreCase);
        }

        #endregion

        #region 验证是不是正常字符 字母，数字，下划线的组合

        /// <summary>
        /// 验证是不是正常字符 字母，数字，下划线的组合
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsNormalChar(string source)
        {
            return Regex.IsMatch(source, @"[\w\d_-]+", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 仅支持(中文、字母、数字)组成
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsZhNumEn(string source)
        {
            return Regex.IsMatch(source, @"^[a-zA-Z0-9\u4e00-\u9fa5]+$", RegexOptions.IgnoreCase);
        }

        /// <summary>
        /// 仅支持(字母、数字)组成
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsNumEn(string source)
        {
            return Regex.IsMatch(source, @"^[a-zA-Z0-9]+$", RegexOptions.IgnoreCase);
        }

        #endregion

        /// <summary>
        /// 验证正实数(最多两位小数)
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static bool IsDecimelTwo(string source)
        {
            return Regex.IsMatch(source, @"^[0-9]+(\.[0-9]{0,2})?$", RegexOptions.IgnoreCase);
        }

        #endregion

        #region 文件操作

        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="name">名称</param>
        /// <returns>创建是否成功</returns>
        [DllImport("dbgHelp", SetLastError = true)]
        private static extern bool MakeSureDirectoryPathExists(string name);

        /// <summary>
        /// 建立文件夹
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static bool CreateDir(string name)
        {
            return Utils.MakeSureDirectoryPathExists(name);
        }

        /// <summary>
        /// 目录不存在时新建
        /// </summary>
        /// <param name="filePath">目录（相对路径）</param>
        //public static void CreateFolder(string filePath)
        //{
        //    string[] PathArr = filePath.Split(new string[] { "/" }, StringSplitOptions.None);
        //    string _path = PathArr[0];
        //    for (int i = 1; i < PathArr.Length; i++)
        //    {
        //        _path = _path + "/" + PathArr[i];
        //        string _filePath = Utils.GetMapPath(_path);
        //        if (!System.IO.Directory.Exists(_filePath))
        //        {
        //            System.IO.Directory.CreateDirectory(_filePath);
        //        }
        //    }
        //}
        /// <summary>
        /// 目录是否存在
        /// </summary>
        /// <param name="folderPath"></param>
        /// <returns></returns>
        public static bool FolderExists(string folderPath)
        {
            return System.IO.Directory.Exists(folderPath);
        }

        private const double KBCount = 1024;
        private const double MBCount = KBCount * 1024;
        private const double GBCount = MBCount * 1024;
        private const double TBCount = GBCount * 1024;

        /// <summary>
        /// 将文件流读取到DataTable数据表中
        /// </summary>
        /// <param name="fileStream">文件流</param>
        /// <param name="sheetName">指定读取excel工作薄sheet的名称</param>
        /// <param name="isFirstRowColumn">第一行是否是DataTable的列名：true=是，false=否</param>
        /// <returns>DataTable数据表</returns>
        public static DataTable ReadStreamToDataTable(Stream fileStream, string sheetName = null, bool isFirstRowColumn = true)
        {
            //定义要返回的datatable对象
            DataTable data = new DataTable();
            //excel工作表
            NPOI.SS.UserModel.ISheet sheet = null;
            //数据开始行(排除标题行)
            int startRow = 0;
            try
            {
                //根据文件流创建excel数据结构,NPOI的工厂类WorkbookFactory会自动识别excel版本，创建出不同的excel数据结构
                NPOI.SS.UserModel.IWorkbook workbook = NPOI.SS.UserModel.WorkbookFactory.Create(fileStream);
                //如果有指定工作表名称
                if (!string.IsNullOrEmpty(sheetName))
                {
                    sheet = workbook.GetSheet(sheetName);
                    //如果没有找到指定的sheetName对应的sheet，则尝试获取第一个sheet
                    if (sheet == null)
                    {
                        sheet = workbook.GetSheetAt(0);
                    }
                }
                else
                {
                    //如果没有指定的sheetName，则尝试获取第一个sheet
                    sheet = workbook.GetSheetAt(0);
                }

                if (sheet != null)
                {
                    NPOI.SS.UserModel.IRow firstRow = sheet.GetRow(0);
                    //一行最后一个cell的编号 即总的列数
                    int cellCount = firstRow.LastCellNum;
                    //如果第一行是标题列名
                    if (isFirstRowColumn)
                    {
                        for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
                        {
                            NPOI.SS.UserModel.ICell cell = firstRow.GetCell(i);
                            if (cell != null)
                            {
                                string cellValue = cell.StringCellValue;
                                if (cellValue != null)
                                {
                                    DataColumn column = new DataColumn(cellValue);
                                    data.Columns.Add(column);
                                }
                            }
                        }

                        startRow = sheet.FirstRowNum + 1;
                    }
                    else
                    {
                        startRow = sheet.FirstRowNum;
                    }

                    //最后一列的标号
                    int rowCount = sheet.LastRowNum;
                    for (int i = startRow; i <= rowCount; ++i)
                    {
                        NPOI.SS.UserModel.IRow row = sheet.GetRow(i);
                        if (row == null || row.FirstCellNum < 0) continue; //没有数据的行默认是null　　　　　　　

                        DataRow dataRow = data.NewRow();
                        for (int j = row.FirstCellNum; j < cellCount; ++j)
                        {
                            //同理，没有数据的单元格都默认是null
                            NPOI.SS.UserModel.ICell cell = row.GetCell(j);
                            if (cell != null)
                            {
                                if (cell.CellType == NPOI.SS.UserModel.CellType.Numeric)
                                {
                                    //判断是否日期类型
                                    if (NPOI.SS.UserModel.DateUtil.IsCellDateFormatted(cell))
                                    {
                                        dataRow[j] = row.GetCell(j).DateCellValue;
                                    }
                                    else
                                    {
                                        dataRow[j] = row.GetCell(j).ToString().Trim();
                                    }
                                }
                                else
                                {
                                    dataRow[j] = row.GetCell(j).ToString().Trim();
                                }
                            }
                        }

                        data.Rows.Add(dataRow);
                    }
                }

                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        /// <summary>
        /// 将excel文件内容读取到DataTable数据表中
        /// </summary>
        /// <param name="fileName">文件完整路径名</param>
        /// <param name="sheetName">指定读取excel工作薄sheet的名称</param>
        /// <param name="isFirstRowColumn">第一行是否是DataTable的列名：true=是，false=否</param>
        /// <returns>DataTable数据表</returns>
        public static DataTable ReadExcelToDataTable(string fileName, string sheetName = null, bool isFirstRowColumn = true)
        {
            //定义要返回的datatable对象
            DataTable data = new DataTable();
            //excel工作表
            NPOI.SS.UserModel.ISheet sheet = null;
            //数据开始行(排除标题行)
            int startRow = 0;
            try
            {
                if (!File.Exists(fileName))
                {
                    return null;
                }

                //根据指定路径读取文件
                FileStream fs = new FileStream(fileName, FileMode.Open, FileAccess.Read);
                //根据文件流创建excel数据结构
                NPOI.SS.UserModel.IWorkbook workbook = NPOI.SS.UserModel.WorkbookFactory.Create(fs);
                //IWorkbook workbook = new HSSFWorkbook(fs);
                //如果有指定工作表名称
                if (!string.IsNullOrEmpty(sheetName))
                {
                    sheet = workbook.GetSheet(sheetName);
                    //如果没有找到指定的sheetName对应的sheet，则尝试获取第一个sheet
                    if (sheet == null)
                    {
                        sheet = workbook.GetSheetAt(0);
                    }
                }
                else
                {
                    //如果没有指定的sheetName，则尝试获取第一个sheet
                    sheet = workbook.GetSheetAt(0);
                }

                if (sheet != null)
                {
                    NPOI.SS.UserModel.IRow firstRow = sheet.GetRow(0);
                    //一行最后一个cell的编号 即总的列数
                    int cellCount = firstRow.LastCellNum;
                    //如果第一行是标题列名
                    if (isFirstRowColumn)
                    {
                        for (int i = firstRow.FirstCellNum; i < cellCount; ++i)
                        {
                            NPOI.SS.UserModel.ICell cell = firstRow.GetCell(i);
                            if (cell != null)
                            {
                                string cellValue = cell.StringCellValue;
                                if (cellValue != null)
                                {
                                    DataColumn column = new DataColumn(cellValue);
                                    data.Columns.Add(column);
                                }
                            }
                        }

                        startRow = sheet.FirstRowNum + 1;
                    }
                    else
                    {
                        startRow = sheet.FirstRowNum;
                    }

                    //最后一列的标号
                    int rowCount = sheet.LastRowNum;
                    for (int i = startRow; i <= rowCount; ++i)
                    {
                        NPOI.SS.UserModel.IRow row = sheet.GetRow(i);
                        if (row == null) continue; //没有数据的行默认是null　　　　　　　

                        DataRow dataRow = data.NewRow();
                        for (int j = row.FirstCellNum; j < cellCount; ++j)
                        {
                            if (row.GetCell(j) != null) //同理，没有数据的单元格都默认是null
                                dataRow[j] = row.GetCell(j).ToString();
                        }

                        data.Rows.Add(dataRow);
                    }
                }

                return data;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion

        #region FileExists/DelAllFile/DelFile/GetAllFile/BackupFile/RestoreFile

        /// <summary>
        /// 删除指定目录下的所有文件
        /// </summary>
        /// <param name="path"></param>
        public static void DelAllFile(string path)
        {
            DirectoryInfo Folder = new DirectoryInfo(path);
            FileInfo[] subFiles = Folder.GetFiles();
            for (int j = 0; j < subFiles.Length; j++)
            {
                subFiles[j].Delete();
            }
        }

        /// <summary>
        /// 删除指定文件
        /// </summary>
        /// <param name="filePath"></param>
        public static void DelFile(string filePath)
        {
            if (FileExists(filePath)) System.IO.File.Delete(filePath);
        }

        /// <summary>
        /// 删除指定文件夹
        /// </summary>
        /// <param name="filePath"></param>
        public static void DelFolder(string filePath)
        {
            if (FolderExists(filePath)) System.IO.Directory.Delete(filePath);
        }

        /// <summary>
        /// 删除指定文件夹及子文件夹
        /// </summary>
        /// <param name="dir"></param>
        public static void DelAllFolder(string dir)
        {
            if (!dir.EndsWith("\\")) dir += "\\";
            if (Directory.Exists(dir))
            {
                DirectoryInfo Folder = new DirectoryInfo(dir);
                DirectoryInfo[] subFiles = Folder.GetDirectories();
                for (int j = 0; j < subFiles.Length; j++)
                {
                    DelAllFolder(dir + subFiles[j].Name);
                }

                DirectoryInfo Files = new DirectoryInfo(dir);
                FileInfo[] subFiles_1 = Files.GetFiles();
                for (int j = 0; j < subFiles_1.Length; j++)
                {
                    System.IO.File.Delete(dir + subFiles_1[j].Name);
                }

                System.IO.Directory.Delete(dir);
            }
        }

        /// <summary>
        /// 取指定目录下的所有文件夹名
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static StringBuilder GetAllFolder(string path)
        {
            StringBuilder fileList = new StringBuilder();
            try
            {
                DirectoryInfo Folder = new DirectoryInfo(path);
                DirectoryInfo[] subFiles = Folder.GetDirectories();
                for (int j = 0; j < subFiles.Length; j++)
                {
                    fileList.Append(subFiles[j].Name + "|");
                }
            }
            catch
            {
            }

            return fileList.Length.Equals(0) ? fileList : fileList.Remove(fileList.Length - 1, 1);
        }

        /// <summary>
        /// 取指定目录下的所有文件名
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static StringBuilder GetAllFile(string path)
        {
            StringBuilder fileList = new StringBuilder();
            try
            {
                DirectoryInfo Folder = new DirectoryInfo(path);
                FileInfo[] subFiles = Folder.GetFiles();
                for (int j = 0; j < subFiles.Length; j++)
                {
                    fileList.Append(subFiles[j].Name + "|");
                }
            }
            catch
            {
            }

            return fileList.Length.Equals(0) ? fileList : fileList.Remove(fileList.Length - 1, 1);
        }

        /// <summary>
        /// 取目录下的所有文件
        /// </summary>
        /// <param name="path"></param>
        /// <param name="fileList"></param>
        /// <param name="delStr"></param>
        public static void GetAllFile(string path, ref StringBuilder fileList, string delStr)
        {
            if (delStr.Equals("")) delStr = path;
            delStr = delStr.Substring(delStr.Length - 1).Equals("\\") ? delStr : delStr + "\\";
            string[] files = System.IO.Directory.GetFiles(path);
            foreach (string s in files) fileList.Append(s + "|").Replace(delStr, "");
            string[] dir = System.IO.Directory.GetDirectories(path);
            foreach (string s in dir) GetAllFile(s, ref fileList, delStr);
        }

        /// <summary>
        /// 获得当前绝对路径
        /// </summary>
        /// <param name="strPath">指定的相对路径</param>
        /// <returns>绝对路径</returns>
        //public static string GetMapPath(string strPath)
        //{
        //    if (HttpContext.Current != null)
        //    {
        //        return HttpContext.Current.Server.MapPath(strPath);
        //    }
        //    else //非web程序引用
        //    {
        //        strPath = strPath.Replace("/", "\\");
        //        if (strPath.StartsWith("\\"))
        //        {
        //            strPath = strPath.TrimStart('\\');
        //        }
        //        return AppDomain.CurrentDomain.SetupInformation.ApplicationBase + strPath;
        //    }
        //}
        /// <summary>
        /// 获取站点根目录URL
        /// </summary>
        /// <returns></returns>
        //public static string GetRootUrl(string forumPath)
        //{
        //    int port = HttpContext.Current.Request.Url.Port;
        //    return string.Format("{0}://{1}{2}{3}",
        //                         HttpContext.Current.Request.Url.Scheme,
        //                         HttpContext.Current.Request.Url.Host.ToString(),
        //                         (port == 80 || port == 0) ? "" : ":" + port,
        //                         forumPath);
        //}
        /// <summary>
        /// 获取指定文件的扩展名
        /// </summary>
        /// <param name="fileName">指定文件名</param>
        /// <returns>扩展名</returns>
        public static string GetFileExtName(string fileName)
        {
            if (Utils.StrIsNullOrEmpty(fileName) || fileName.IndexOf('.') <= 0)
                return "";

            fileName = fileName.ToLower().Trim();

            return fileName.Substring(fileName.LastIndexOf('.') + 1, fileName.Length - fileName.LastIndexOf('.') - 1);
        }

        /// <summary>
        /// 返回文件是否存在
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns>是否存在</returns>
        public static bool FileExists(string filename)
        {
            return System.IO.File.Exists(filename);
        }
        /// <summary>
        /// 以指定的ContentType输出指定文件文件
        /// </summary>
        /// <param name="filepath">文件路径</param>
        /// <param name="filename">输出的文件名</param>
        /// <param name="filetype">将文件输出时设置的ContentType</param>
        //public static void ResponseFile(string filepath, string filename, string filetype)
        //{
        //    Stream iStream = null;
        //    // 缓冲区为10k
        //    byte[] buffer = new Byte[10000];
        //    // 文件长度
        //    int length;
        //    // 需要读的数据长度
        //    long dataToRead;
        //    try
        //    {
        //        // 打开文件
        //        iStream = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);

        //        // 需要读的数据长度
        //        dataToRead = iStream.Length;

        //        HttpContext.Current.Response.ContentType = filetype;
        //        if (HttpContext.Current.Request.ServerVariables["HTTP_USER_AGENT"].IndexOf("MSIE") > -1)
        //            HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + Utils.UrlEncode(filename.Trim()).Replace("+", " "));
        //        else
        //            HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + filename.Trim());

        //        while (dataToRead > 0)
        //        {
        //            // 检查客户端是否还处于连接状态
        //            if (HttpContext.Current.Response.IsClientConnected)
        //            {
        //                length = iStream.Read(buffer, 0, 10000);
        //                HttpContext.Current.Response.OutputStream.Write(buffer, 0, length);
        //                HttpContext.Current.Response.Flush();
        //                buffer = new Byte[10000];
        //                dataToRead = dataToRead - length;
        //            }
        //            else
        //            {
        //                // 如果不再连接则跳出死循环
        //                dataToRead = -1;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        HttpContext.Current.Response.Write("Error : " + ex.Message);
        //    }
        //    finally
        //    {
        //        if (iStream != null)
        //        {
        //            // 关闭文件
        //            iStream.Close();
        //        }
        //    }
        //    HttpContext.Current.Response.End();
        //}
        /// <summary>
        /// 判断文件名是否为浏览器可以直接显示的图片文件名
        /// </summary>
        /// <param name="filename">文件名</param>
        /// <returns>是否可以直接显示</returns>
        public static bool IsImgFilename(string filename)
        {
            filename = filename.Trim();
            if (filename.EndsWith(".") || filename.IndexOf(".") == -1)
                return false;

            string extname = filename.Substring(filename.LastIndexOf(".") + 1).ToLower();
            return (extname == "jpg" || extname == "jpeg" || extname == "png" || extname == "bmp" || extname == "gif");
        }

        /// <summary>
        /// 备份文件
        /// </summary>
        /// <param name="sourceFileName">源文件名</param>
        /// <param name="destFileName">目标文件名</param>
        /// <param name="overwrite">当目标文件存在时是否覆盖</param>
        /// <returns>操作是否成功</returns>
        public static bool BackupFile(string sourceFileName, string destFileName, bool overwrite)
        {
            if (!System.IO.File.Exists(sourceFileName))
                throw new FileNotFoundException(sourceFileName + "文件不存在！");

            if (!overwrite && System.IO.File.Exists(destFileName))
                return false;

            try
            {
                System.IO.File.Copy(sourceFileName, destFileName, true);

                //System.IO.FileStream fs = new FileStream(sourceFileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                //StreamReader sread = new StreamReader(fs);

                //StreamWriter sr = new StreamWriter(fs, System.Text.Encoding.Default);
                ////开始写入
                //sr.Write(destFileName);
                ////清空缓冲区
                //sr.Flush();
                ////关闭流
                //sr.Close();
                //fs.Close();//关闭流

                return true;
            }
            catch (Exception e)
            {
                throw e;
            }
        }


        /// <summary>
        /// 备份文件,当目标文件存在时覆盖
        /// </summary>
        /// <param name="sourceFileName">源文件名</param>
        /// <param name="destFileName">目标文件名</param>
        /// <returns>操作是否成功</returns>
        public static bool BackupFile(string sourceFileName, string destFileName)
        {
            return BackupFile(sourceFileName, destFileName, true);
        }

        /// <summary>
        /// 恢复文件
        /// </summary>
        /// <param name="backupFileName">备份文件名</param>
        /// <param name="targetFileName">要恢复的文件名</param>
        /// <param name="backupTargetFileName">要恢复文件再次备份的名称,如果为null,则不再备份恢复文件</param>
        /// <returns>操作是否成功</returns>
        public static bool RestoreFile(string backupFileName, string targetFileName, string backupTargetFileName)
        {
            try
            {
                if (!System.IO.File.Exists(backupFileName))
                    throw new FileNotFoundException(backupFileName + "文件不存在！");

                if (backupTargetFileName != null)
                {
                    if (!System.IO.File.Exists(targetFileName))
                        throw new FileNotFoundException(targetFileName + "文件不存在！无法备份此文件！");
                    else
                        System.IO.File.Copy(targetFileName, backupTargetFileName, true);
                }

                System.IO.File.Delete(targetFileName);
                System.IO.File.Copy(backupFileName, targetFileName);
            }
            catch (Exception e)
            {
                throw e;
            }

            return true;
        }

        /// <summary>
        /// 恢复文件
        /// </summary>
        /// <param name="backupFileName">备份文件名</param>
        /// <param name="targetFileName">要恢复的文件名</param>
        /// <returns>操作是否成功</returns>
        public static bool RestoreFile(string backupFileName, string targetFileName)
        {
            return RestoreFile(backupFileName, targetFileName, null);
        }

        /// <summary>
        /// 取文件编码
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns></returns>
        public static Encoding GetFileEncoding(string fileName)
        {
            /*byte[] Unicode=new byte[]{0xFF,0xFE};
            byte[] UnicodeBIG=new byte[]{0xFE,0xFF};
            byte[] UTF8=new byte[]{0xEF,0xBB,0xBF};*/

            try
            {
                FileStream fs = new FileStream(fileName, FileMode.Open, FileAccess.Read);
                BinaryReader r = new BinaryReader(fs, System.Text.Encoding.Default);
                byte[] ss = r.ReadBytes(3);
                r.Close();
                fs.Close();
                fs.Dispose();

                if (ss[0] >= 0xEF)
                {
                    if (ss[0] == 0xEF && ss[1] == 0xBB && ss[2] == 0xBF)
                        return System.Text.Encoding.UTF8;
                    else if (ss[0] == 0xFE && ss[1] == 0xFF)
                        return System.Text.Encoding.BigEndianUnicode;
                    else if (ss[0] == 0xFF && ss[1] == 0xFE)
                        return System.Text.Encoding.Unicode;
                    else
                        return System.Text.Encoding.Default;
                }
                else return System.Text.Encoding.UTF8;
            }
            catch
            {
                return System.Text.Encoding.UTF8;
            }
        }

        /// <summary>
        /// GZip压缩文件
        /// </summary>
        /// <param name="inFilename">源文件地址</param>
        /// <param name="outFilename">保存文件地址</param>
        public static void GZipCompress(string inFilename, string outFilename)
        {
            FileStream sourceFile = File.OpenRead(inFilename);
            FileStream destFile = File.Create(outFilename);
            GZipStream compStream = new GZipStream(destFile, CompressionMode.Compress);
            int theByte = sourceFile.ReadByte();

            while (theByte != -1)
            {
                compStream.WriteByte((byte)theByte);
                theByte = sourceFile.ReadByte();
            }

            sourceFile.Close();
            compStream.Close();
            destFile.Close();
        }

        /// <summary>
        /// GZip解压缩文件
        /// </summary>
        /// <param name="inFilename">源文件地址</param>
        /// <param name="outFilename">保存文件地址</param>
        public static void GZipDecompress(string inFileName, string outFileName)
        {
            FileStream sourceFile = File.OpenRead(inFileName);
            FileStream destFile = File.Create(outFileName);
            GZipStream compStream = new GZipStream(sourceFile, CompressionMode.Decompress);
            int theByte = compStream.ReadByte();

            while (theByte != -1)
            {
                destFile.WriteByte((byte)theByte);
                theByte = compStream.ReadByte();
            }

            destFile.Close();
            compStream.Close();
            sourceFile.Close();
        }

        /// <summary>
        /// 文件夹的大小
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static long GetDirectorySize(string path)
        {
            long size = 0;
            string[] files = System.IO.Directory.GetFiles(path);
            foreach (string s in files) size += new FileInfo(s).Length;
            string[] dir = System.IO.Directory.GetDirectories(path);
            foreach (string s in dir) size += GetDirectorySize(s);
            return size;
        }

        /// <summary>
        /// 获取文件的大小
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static long GetFileSize(string path)
        {
            if (!FileExists(path)) return 0;
            return new FileInfo(path).Length;
        }

        /// <summary>
        /// 获取文件的创建时间
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static DateTime GetFileCreateTime(string path)
        {
            if (!FileExists(path)) return Convert.ToDateTime("1900-01-01");
            return new FileInfo(path).CreationTime;
        }

        /// <summary>
        /// 获取文件的修改时间
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static DateTime GetFileWriteTime(string path)
        {
            if (!FileExists(path)) return Convert.ToDateTime("1900-01-01");
            return new FileInfo(path).LastWriteTime;
        }
        /// <summary>
        /// 获取文件内的代码
        /// </summary>
        /// <param name="FilePath">文件地址，相对</param>
        /// <returns></returns>
        //public static string GetFileText(string FilePath)
        //{
        //    FilePath = GetMapPath(FilePath);
        //    if (!FileExists(FilePath))
        //        return "";
        //    StringBuilder strText = new StringBuilder();
        //    //创建 FileStream 的对象,说白了告诉程序,文件在那里,对文件如何处理,对文件内容采取的处理方式
        //    FileStream fs = new FileStream(FilePath, FileMode.Open, FileAccess.Read);
        //    //仅 对文本 进行 读写操作
        //    StreamReader sr = new StreamReader(fs, GetFileEncoding(FilePath));

        //    //定位操作点,begin 是一个参考点
        //    sr.BaseStream.Seek(0, SeekOrigin.Begin);
        //    //读一下，看看文件内有没有内容，为下一步循环 提供判断依据
        //    //sr.ReadLine() 这里是 StreamReader的方法 可不是 console 中的~
        //    string str = sr.ReadLine();                      //如果 文件有内容
        //    while (str != null)
        //    {
        //        //输出字符串，str 在上面已经定义了 读入一行字符
        //        strText.Append(str + "\r\n");
        //        //这里我的理解是 当输出一行后，指针移动到下一行~
        //        //下面这句话就是 判断 指针所指这行是否有内容~
        //        str = sr.ReadLine();
        //    }
        //    //关闭文件，注意顺序，先对文件内部进行关闭，然后才是文件~
        //    sr.Close();
        //    fs.Close();
        //    return strText.ToString();
        //}

        /// <summary>
        /// 获取文件内的代码
        /// </summary>
        /// <param name="FilePath">文件地址，相对</param>
        /// <returns></returns>
        //public static string GetFileText(string FilePath, Encoding encoding)
        //{
        //    FilePath = GetMapPath(FilePath);
        //    if (!FileExists(FilePath))
        //        return "";
        //    StringBuilder strText = new StringBuilder();
        //    //创建 FileStream 的对象,说白了告诉程序,文件在那里,对文件如何处理,对文件内容采取的处理方式
        //    FileStream fs = new FileStream(FilePath, FileMode.Open, FileAccess.Read);
        //    //仅 对文本 进行 读写操作
        //    StreamReader sr = new StreamReader(fs, encoding);

        //    //定位操作点,begin 是一个参考点
        //    sr.BaseStream.Seek(0, SeekOrigin.Begin);
        //    //读一下，看看文件内有没有内容，为下一步循环 提供判断依据
        //    //sr.ReadLine() 这里是 StreamReader的方法 可不是 console 中的~
        //    string str = sr.ReadLine();                      //如果 文件有内容
        //    while (str != null)
        //    {
        //        //输出字符串，str 在上面已经定义了 读入一行字符
        //        strText.Append(str + "\r\n");
        //        //这里我的理解是 当输出一行后，指针移动到下一行~
        //        //下面这句话就是 判断 指针所指这行是否有内容~
        //        str = sr.ReadLine();
        //    }
        //    //关闭文件，注意顺序，先对文件内部进行关闭，然后才是文件~
        //    sr.Close();
        //    fs.Close();
        //    return strText.ToString();
        //}
        /// <summary>
        /// 创建文件
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="text">内容</param>
        /// <returns></returns>
        public static bool CreatFile(string path, string text)
        {
            Encoding encod;
            if (FileExists(path))
                encod = GetFileEncoding(path);
            else
                encod = Encoding.GetEncoding("utf-8");
            return CreatFile(path, text, encod);
        }

        /// <summary>
        /// 创建文件
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="text">内容</param>
        /// <returns></returns>
        public static bool CreatFile(string path, string text, Encoding encoding)
        {
            try
            {
                DelFile(path);
                StreamWriter sr = new StreamWriter(path, true, encoding); // File.CreateText(path);
                sr.WriteLine(text);
                sr.Close();
                sr.Dispose();
                return true;
            }
            catch
            {
                return false;
            }
        }


        private static object locker1 = new object();

        /// <summary>
        /// 追加文件内容
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="text">内容</param>
        /// <returns></returns>
        public static bool CreatFileAppend(string path, string text)
        {
            try
            {
                lock (locker1)
                {
                    StreamWriter sr = File.AppendText(path);
                    sr.WriteLine(text);
                    sr.Close();
                    sr.Dispose();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 追加文件内容 无此文件和路径将进行创建
        /// </summary>
        /// <param name="path">文件夹路径</param>
        /// <param name="fileName">文件名</param>
        /// <param name="text">内容</param>
        /// <returns></returns>
        public static bool CreatFileAppend(string path, string fileName, string text)
        {
            if (!FolderExists(path))
            {
                Directory.CreateDirectory(path);
            }

            if (!FileExists(path + "/" + fileName))
            {
                CreatFile(path + "/" + fileName, null);
            }

            return CreatFileAppend(path + "/" + fileName, text);
        }


        /// <summary>
        /// 获取文件的最后访问时间
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static DateTime GetFileReadTime(string path)
        {
            if (!FileExists(path)) return Convert.ToDateTime("1900-01-01");
            return new FileInfo(path).LastAccessTime;
        }

        /// <summary>
        /// 得到适应的大小
        /// </summary>
        /// <param name="path"></param>
        /// <returns>string</returns>
        public static string GetAutoSizeString(double size, int roundCount)
        {
            if (KBCount > size)
            {
                return Math.Round(size, roundCount) + "B";
            }
            else if (MBCount > size)
            {
                return Math.Round(size / KBCount, roundCount) + "KB";
            }
            else if (GBCount > size)
            {
                return Math.Round(size / MBCount, roundCount) + "MB";
            }
            else if (TBCount > size)
            {
                return Math.Round(size / GBCount, roundCount) + "GB";
            }
            else
            {
                return Math.Round(size / TBCount, roundCount) + "TB";
            }
        }

        #endregion

        #region 字符串编码

        /// <summary>
        /// 返回 HTML 字符串的编码结果
        /// </summary>
        /// <param name="str">字符串</param>
        /// <returns>编码结果</returns>
        public static string HtmlEncode(string str)
        {
            return HttpUtility.HtmlEncode(str);
        }

        /// <summary>
        /// 返回 HTML 字符串的解码结果
        /// </summary>
        /// <param name="str">字符串</param>
        /// <returns>解码结果</returns>
        public static string HtmlDecode(string str)
        {
            return HttpUtility.HtmlDecode(str);
        }

        /// <summary>
        /// 返回 URL 字符串的编码结果
        /// </summary>
        /// <param name="str">字符串</param>
        /// <returns>编码结果</returns>
        public static string UrlEncode(string str)
        {
            return HttpUtility.UrlEncode(str);
        }

        /// <summary>
        /// 返回 URL 字符串的解码结果
        /// </summary>
        /// <param name="str">字符串</param>
        /// <returns>解码结果</returns>
        public static string UrlDecode(string str)
        {
            return HttpUtility.UrlDecode(str);
        }

        /// <summary>
        /// 返回指定目录下的非 UTF8 字符集文件
        /// </summary>
        /// <param name="Path">路径</param>
        /// <returns>文件名的字符串数组</returns>
        public static string[] FindNoUTF8File(string Path)
        {
            StringBuilder filelist = new StringBuilder();
            DirectoryInfo Folder = new DirectoryInfo(Path);
            FileInfo[] subFiles = Folder.GetFiles();

            for (int j = 0; j < subFiles.Length; j++)
            {
                if (subFiles[j].Extension.ToLower().Equals(".htm"))
                {
                    FileStream fs = new FileStream(subFiles[j].FullName, FileMode.Open, FileAccess.Read);
                    bool bUtf8 = IsUTF8(fs);
                    fs.Close();
                    if (!bUtf8)
                    {
                        filelist.Append(subFiles[j].FullName);
                        filelist.Append("\r\n");
                    }
                }
            }

            return Utils.SplitString(filelist.ToString(), "\r\n");
        }

        //0000 0000-0000 007F - 0xxxxxxx  (ascii converts to 1 octet!)
        //0000 0080-0000 07FF - 110xxxxx 10xxxxxx    ( 2 octet format)
        //0000 0800-0000 FFFF - 1110xxxx 10xxxxxx 10xxxxxx (3 octet format)

        /// <summary>
        /// 判断文件流是否为UTF8字符集
        /// </summary>
        /// <param name="sbInputStream">文件流</param>
        /// <returns>判断结果</returns>
        private static bool IsUTF8(FileStream sbInputStream)
        {
            int i;
            byte cOctets; // octets to go in this UTF-8 encoded character 
            byte chr;
            bool bAllAscii = true;
            long iLen = sbInputStream.Length;

            cOctets = 0;
            for (i = 0; i < iLen; i++)
            {
                chr = (byte)sbInputStream.ReadByte();

                if ((chr & 0x80) != 0) bAllAscii = false;

                if (cOctets == 0)
                {
                    if (chr >= 0x80)
                    {
                        do
                        {
                            chr <<= 1;
                            cOctets++;
                        } while ((chr & 0x80) != 0);

                        cOctets--;
                        if (cOctets == 0)
                            return false;
                    }
                }
                else
                {
                    if ((chr & 0xC0) != 0x80)
                        return false;

                    cOctets--;
                }
            }

            if (cOctets > 0)
                return false;

            if (bAllAscii)
                return false;

            return true;
        }

        /// <summary>
        /// 格式化字节数字符串
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        public static string FormatBytesStr(int bytes)
        {
            if (bytes > 1073741824)
                return ((double)(bytes / 1073741824)).ToString("0") + "G";

            if (bytes > 1048576)
                return ((double)(bytes / 1048576)).ToString("0") + "M";

            if (bytes > 1024)
                return ((double)(bytes / 1024)).ToString("0") + "K";

            return bytes.ToString() + "Bytes";
        }

        /// <summary>
        /// 字符串转换为字符串数组
        /// 如12345678,每取两位转换为12 34 56 78的四个数组
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string[] GetStringArray(string str)
        {
            List<String> al = new List<String>();
            for (int i = 0; i < str.Length / 2; i++)
            {
                string strJihao = str.Substring(i * 2, 2);
                al.Add(strJihao);
            }

            string s = string.Join(",", al.ToArray());
            string[] HexStr = s.Split(',');
            return HexStr;
        }

        /// <summary>
        ///字符串转换为Byte数组
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte[] GetByteArray(string str)
        {
            string[] HexStr = GetStringArray(str);
            byte[] Hexbyte = new byte[HexStr.Length];
            for (int j = 0; j < HexStr.Length; j++)
            {
                Hexbyte[j] = Convert.ToByte(HexStr[j], 16);
            }

            return Hexbyte;
        }

        #endregion

        #region 加密处理

        /// <summary>
        /// MD5函数
        /// </summary>
        /// <param name="str">原始字符串</param>
        /// <returns>MD5结果</returns>
        public static string MD5(string str)
        {
            byte[] b = Encoding.UTF8.GetBytes(str);
            b = new MD5CryptoServiceProvider().ComputeHash(b);
            string ret = "";
            for (int i = 0; i < b.Length; i++)
                ret += b[i].ToString("x").PadLeft(2, '0');

            return ret;
        }

        /// <summary>
        /// MD5函数
        /// </summary>
        /// <param name="str">原始字符串</param>
        /// <returns>MD5结果</returns>
        public static string MD5_GBK(string str)
        {
            byte[] b = Encoding.GetEncoding("GBK").GetBytes(str);
            b = new MD5CryptoServiceProvider().ComputeHash(b);
            string ret = "";
            for (int i = 0; i < b.Length; i++)
                ret += b[i].ToString("x").PadLeft(2, '0');

            return ret;
        }

        /// <summary>
        /// SHA256函数
        /// </summary>
        /// /// <param name="str">原始字符串</param>
        /// <returns>SHA256结果</returns>
        public static string SHA256(string str)
        {
            byte[] SHA256Data = Encoding.UTF8.GetBytes(str);
            SHA256Managed Sha256 = new SHA256Managed();
            byte[] Result = Sha256.ComputeHash(SHA256Data);
            return Convert.ToBase64String(Result); //返回长度为44字节的字符串
        }


        /// <summary>
        /// MD5加密
        /// </summary>
        /// <param name="input">需要加密的字符串</param>
        /// <returns></returns>
        public static string MD5Encrypt(string input)
        {
            return MD5Encrypt(input, new UTF8Encoding());
        }

        /// <summary>
        /// md5加密16|32位
        /// </summary>
        /// <param name="input"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string MD5Encrypt(string input, int length)
        {
            string res = MD5Encrypt(input, new UTF8Encoding());
            if (length == 16)
            {
                res = res.Substring(8, 16);
            }

            return res;
        }

        /// <summary>
        /// MD5加密
        /// </summary>
        /// <param name="input">需要加密的字符串</param>
        /// <param name="encode">字符的编码</param>
        /// <returns></returns>
        public static string MD5Encrypt(string input, Encoding encode)
        {
            if (string.IsNullOrEmpty(input))
            {
                return null;
            }

            MD5CryptoServiceProvider md5Hasher = new MD5CryptoServiceProvider();
            byte[] data = md5Hasher.ComputeHash(encode.GetBytes(input));
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("x2"));
            }

            return sBuilder.ToString();
        }

        /// <summary>
        /// DES加密
        /// </summary>
        /// <param name="pToEncrypt">要加密的字符串</param>
        /// <param name="sKey">密钥，且必须为8位</param>
        /// <returns>以Base64格式返回的加密字符串</returns>
        public static string EncryptDES(string pToEncrypt, string sKey)
        {
            if (string.IsNullOrEmpty(pToEncrypt) || string.IsNullOrEmpty(sKey))
            {
                return "";
            }

            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                byte[] inputByteArray = Encoding.UTF8.GetBytes(pToEncrypt);
                des.Key = Encoding.ASCII.GetBytes(sKey);
                des.IV = Encoding.ASCII.GetBytes(sKey);
                MemoryStream ms = new MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, des.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }

                string str = Convert.ToBase64String(ms.ToArray());
                ms.Close();
                return str;
            }
        }

        /// <summary>
        /// DES解密
        /// </summary>
        /// <param name="pToDecrypt">要解密的Base64</param>
        /// <param name="sKey">密钥，且必须为8位</param>
        /// <returns>已解密的字符串</returns>
        public static string DecryptDES(string pToDecrypt, string sKey)
        {
            if (string.IsNullOrEmpty(pToDecrypt) || string.IsNullOrEmpty(sKey))
            {
                return "";
            }

            byte[] inputByteArray = Convert.FromBase64String(pToDecrypt);
            using (DESCryptoServiceProvider des = new DESCryptoServiceProvider())
            {
                des.Key = Encoding.ASCII.GetBytes(sKey);
                des.IV = Encoding.ASCII.GetBytes(sKey);
                MemoryStream ms = new MemoryStream();
                using (CryptoStream cs = new CryptoStream(ms, des.CreateDecryptor(), CryptoStreamMode.Write))
                {
                    cs.Write(inputByteArray, 0, inputByteArray.Length);
                    cs.FlushFinalBlock();
                    cs.Close();
                }

                string str = Encoding.UTF8.GetString(ms.ToArray());
                ms.Close();
                return str;
            }
        }

        #endregion

        #region 类型转换

        /// <summary>
        /// 转换为Int32类型
        /// </summary>
        /// <param name="objNum"></param>
        /// <returns></returns>
        public static int SafeInt32(object objNum)
        {
            if (objNum == null)
                return 0;

            string strNum = objNum.ToString();
            if (IsInt(strNum))
            {
                if (strNum.ToString().Length > 9)
                {
                    if (strNum.StartsWith("-"))
                        return int.MinValue;
                    else
                        return int.MaxValue;
                }

                return Int32.Parse(strNum);
            }
            else
                return 0;
        }

        /// <summary>
        /// string型转换为bool型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的bool类型结果</returns>
        public static bool ObjectToBool(object strValue, bool defValue)
        {
            if (strValue != null)
                return StrToBool(strValue.ToString(), defValue);

            return defValue;
        }

        /// <summary>
        /// string型转换为bool型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的bool类型结果</returns>
        public static bool StrToBool(string strValue, bool defValue)
        {
            if (strValue != null)
            {
                if (string.Compare(strValue, "true", true) == 0 || string.Compare(strValue, "1", true) == 0)
                    return true;
                else if (string.Compare(strValue, "false", true) == 0 || string.Compare(strValue, "0", true) == 0)
                    return false;
            }

            return defValue;
        }

        /// <summary>
        /// 将对象转换为Int32类型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static int ObjectToInt(object strValue)
        {
            return ObjectToInt(strValue, 0);
        }

        /// <summary>
        /// 将对象转换为Int32类型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static int ObjectToInt(object strValue, int defValue)
        {
            if (strValue != null)
                return StrToInt(strValue.ToString(), defValue);

            return defValue;
        }

        /// <summary>
        /// 将对象转换为Int32类型,转换失败返回0
        /// </summary>
        /// <param name="str">要转换的字符串</param>
        /// <returns>转换后的int类型结果</returns>
        public static int StrToInt(string strValue)
        {
            return StrToInt(strValue, 0);
        }

        /// <summary>
        /// 将对象转换为Int32类型
        /// </summary>
        /// <param name="str">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static int StrToInt(string strValue, int defValue)
        {
            return StrToInt_32(strValue, defValue);
        }

        public static int StrToInt_32(string strValue, int defValue)
        {
            if (string.IsNullOrWhiteSpace(strValue))
                return defValue;

            strValue = strValue.Trim();

            // 优先尝试解析为整数
            if (Int32.TryParse(strValue, out int intValue))
                return intValue;

            // 拆出整数部分（兼容小数），忽略负号，只检测整数部分长度
            var dotIndex = strValue.IndexOf('.');
            var intPart = (dotIndex >= 0 ? strValue.Substring(0, dotIndex) : strValue);
            var intPartDigits = intPart.TrimStart('-');

            // 如果整数部分超过 int 范围（最多10位）
            if (intPartDigits.Length > 10)
                return defValue;

            // 最后 fallback：支持 "123.45" => 123（四舍五入）Convert.ToInt32(StrToFloat(strValue, defValue));
            //取整（直接截断小数位）
            return (int)Math.Truncate(StrToFloat(strValue, defValue));
        }

        public static Int64 StrToInt_64(string strValue, Int64 defValue)
        {
            if (string.IsNullOrEmpty(strValue) || strValue.Trim().Length >= 19 || !Regex.IsMatch(strValue.Trim(), @"^([-]|[0-9])[0-9]*(\.\w*)?$"))
                return defValue;

            Int64 rv;
            if (Int64.TryParse(strValue, out rv))
                return rv;

            return Convert.ToInt64(StrToFloat(strValue, defValue));
        }

        public static byte ObjectToByte(object strValue)
        {
            return ObjectToByte(strValue, 0);
        }

        public static byte ObjectToByte(object strValue, byte defValue)
        {
            if (strValue != null)
                return StrToByte(strValue.ToString(), defValue);

            return defValue;
        }

        public static byte StrToByte(string strValue)
        {
            return StrToByte(strValue, 0);
        }

        /// <summary>
        /// 将对象转换为Int32类型
        /// </summary>
        /// <param name="str">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static byte StrToByte(string strValue, byte defValue)
        {
            if (string.IsNullOrEmpty(strValue) || strValue.Trim().Length >= 4 || !Regex.IsMatch(strValue.Trim(), @"^([0-9])[0-9]*(\.\w*)?$"))
                return defValue;

            byte rv;
            if (Byte.TryParse(strValue, out rv))
                return rv;

            return defValue;
        }

        /// <summary>
        /// string型转换为float型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static float StrToFloat(object strValue, float defValue)
        {
            if ((strValue == null))
                return defValue;

            return StrToFloat(strValue.ToString(), defValue);
        }

        /// <summary>
        /// string型转换为float型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static float ObjectToFloat(object strValue, float defValue)
        {
            if ((strValue == null))
                return defValue;

            return StrToFloat(strValue.ToString(), defValue);
        }

        /// <summary>
        /// string型转换为float型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static float ObjectToFloat(object strValue)
        {
            return ObjectToFloat(strValue.ToString(), 0);
        }

        /// <summary>
        /// string型转换为float型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <returns>转换后的int类型结果</returns>
        public static float StrToFloat(string strValue)
        {
            if ((strValue == null))
                return 0;

            return StrToFloat(strValue.ToString(), 0);
        }

        /// <summary>
        /// string型转换为float型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static float StrToFloat(string strValue, float defValue)
        {
            if ((strValue == null))
                return defValue;

            float intValue = defValue;
            if (strValue != null)
            {
                bool IsFloat = Regex.IsMatch(strValue, @"^([-]|[0-9])[0-9]*(\.\w*)?$");
                if (IsFloat)
                    float.TryParse(strValue, out intValue);
            }

            return intValue;
        }

        /// <summary>
        /// 多位数字转成十六进制
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static string ConvertNumberToHex(int number)
        {
            //将数字转换为十六进制字符串，并确保结果是两位数
            return number.ToString("X2");
        }

        /// <summary>
        /// 将对象转换为日期时间类型
        /// </summary>
        /// <param name="str">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static DateTime StrToDateTime(string strValue, DateTime defValue)
        {
            if (!string.IsNullOrEmpty(strValue))
            {
                DateTime dateTime;
                if (DateTime.TryParse(strValue, out dateTime))
                    return dateTime;
            }

            return defValue;
        }

        /// <summary>
        /// 将对象转换为日期时间类型
        /// </summary>
        /// <param name="str">要转换的字符串</param>
        /// <returns>转换后的int类型结果</returns>
        public static DateTime StrToDateTime(string strValue)
        {
            return StrToDateTime(strValue, DateTimeHelper.GetNowTime());
        }

        /// <summary>
        /// 将对象转换为日期时间类型
        /// </summary>
        /// <param name="obj">要转换的对象</param>
        /// <returns>转换后的int类型结果</returns>
        public static DateTime ObjectToDateTime(object obj)
        {
            return StrToDateTime(obj.ToString());
        }

        /// <summary>
        /// 将对象转换为日期时间类型
        /// </summary>
        /// <param name="obj">要转换的对象</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static DateTime ObjectToDateTime(object obj, DateTime defValue)
        {
            return StrToDateTime(obj.ToString(), defValue);
        }

        /// <summary>
        /// 检测时间与当前时间差是否在条件范围内(天)
        /// </summary>
        /// <param name="checkTime">被检测时间（yyyyMMddHHmmssfff）</param>
        /// <param name="dayParam">前后n天内</param>
        /// <returns></returns>
        public static bool CheckTimeRange(string checkTime, double dayParam = 1)
        {
            try
            {
                IFormatProvider ifp = new System.Globalization.CultureInfo("zh-CN", true);
                TimeSpan timeSpan = DateTimeHelper.GetNowTime().Subtract(DateTime.ParseExact(checkTime, "yyyyMMddHHmmssfff", ifp));
                if (Math.Abs(timeSpan.TotalDays) > dayParam)
                {
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// string型转换为decimal型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static decimal StrToDecimal(object strValue, decimal defValue)
        {
            if ((strValue == null))
                return defValue;

            return StrToDecimal(strValue.ToString(), defValue);
        }

        /// <summary>
        /// string型转换为decimal型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static decimal ObjectToDecimal(object strValue, decimal defValue)
        {
            if ((strValue == null))
                return defValue;

            return StrToDecimal(strValue.ToString(), defValue);
        }

        /// <summary>
        /// string型转换为decimal型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static decimal ObjectToDecimal(object strValue)
        {
            return ObjectToDecimal(strValue.ToString(), 0);
        }

        /// <summary>
        /// string型转换为decimal型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <returns>转换后的int类型结果</returns>
        public static decimal StrToDecimal(string strValue)
        {
            if ((strValue == null))
                return 0;

            return StrToDecimal(strValue, 0);
        }

        /// <summary>
        /// string型转换为decimal型
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static decimal StrToDecimal(string strValue, decimal defValue)
        {
            decimal.TryParse(strValue, out defValue);
            defValue = Math.Round(defValue, 2);
            return defValue;
        }


        /// <summary>
        /// string型转换为Double型(取两位小数，不四舍五入)
        /// </summary>
        /// <param name="strValue">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static double ObjectToDouble(object strValue, double defValue)
        {
            if ((strValue == null))
                return defValue;

            var r = ((int)(StrToDecimal(strValue, 0) * 100));
            return r / 100.0;
        }

        /// <summary>
        /// 字符串转成整型数组
        /// </summary>
        /// <param name="idList">要转换的字符串</param>
        /// <returns>转换后的int类型结果</returns>
        public static int[] StringToIntArray(string idList)
        {
            return StringToIntArray(idList, -1);
        }

        /// <summary>
        /// 字符串转成整型数组
        /// </summary>
        /// <param name="idList">要转换的字符串</param>
        /// <param name="defValue">缺省值</param>
        /// <returns>转换后的int类型结果</returns>
        public static int[] StringToIntArray(string idList, int defValue)
        {
            if (string.IsNullOrEmpty(idList))
                return null;
            string[] strArr = Utils.SplitString(idList, ",");
            int[] intArr = new int[strArr.Length];
            for (int i = 0; i < strArr.Length; i++)
                intArr[i] = StrToInt(strArr[i], defValue);

            return intArr;
        }

        /// <summary>
        /// 将全角数字转换为数字
        /// </summary>
        /// <param name="SBCCase"></param>
        /// <returns></returns>
        public static string SBCCaseToNumberic(string SBCCase)
        {
            char[] c = SBCCase.ToCharArray();
            for (int i = 0; i < c.Length; i++)
            {
                byte[] b = System.Text.Encoding.Unicode.GetBytes(c, i, 1);
                if (b.Length == 2)
                {
                    if (b[1] == 255)
                    {
                        b[0] = (byte)(b[0] + 32);
                        b[1] = 0;
                        c[i] = System.Text.Encoding.Unicode.GetChars(b)[0];
                    }
                }
            }

            return new string(c);
        }

        /// <summary>
        /// 将字符串转换为Color
        /// </summary>
        /// <param name="color"></param>
        /// <returns></returns>
        public static Color ToColor(string color)
        {
            int red, green, blue = 0;
            char[] rgb;
            color = color.TrimStart('#');
            color = Regex.Replace(color.ToLower(), "[g-zG-Z]", "");
            switch (color.Length)
            {
                case 3:
                    rgb = color.ToCharArray();
                    red = Convert.ToInt32(rgb[0].ToString() + rgb[0].ToString(), 16);
                    green = Convert.ToInt32(rgb[1].ToString() + rgb[1].ToString(), 16);
                    blue = Convert.ToInt32(rgb[2].ToString() + rgb[2].ToString(), 16);
                    return Color.FromArgb(red, green, blue);
                case 6:
                    rgb = color.ToCharArray();
                    red = Convert.ToInt32(rgb[0].ToString() + rgb[1].ToString(), 16);
                    green = Convert.ToInt32(rgb[2].ToString() + rgb[3].ToString(), 16);
                    blue = Convert.ToInt32(rgb[4].ToString() + rgb[5].ToString(), 16);
                    return Color.FromArgb(red, green, blue);
                default:
                    return Color.FromName(color);
            }
        }

        /// <summary>
        /// 转换长文件名为短文件名
        /// </summary>
        /// <param name="filename"></param>
        /// <param name="repstring"></param>
        /// <param name="leftnum"></param>
        /// <param name="rightnum"></param>
        /// <param name="charnum"></param>
        /// <returns></returns>
        public static string ConvertSimpleFileName(string fullname, string repstring, int leftnum, int rightnum, int charnum)
        {
            string simplefilename = "", leftstring = "", rightstring = "", filename = "";
            string extname = GetFileExtName(fullname);

            if (Utils.StrIsNullOrEmpty(extname))
                return fullname;

            int filelength = 0, dotindex = 0;

            dotindex = fullname.LastIndexOf('.');
            filename = fullname.Substring(0, dotindex);
            filelength = filename.Length;
            if (dotindex > charnum)
            {
                leftstring = filename.Substring(0, leftnum);
                rightstring = filename.Substring(filelength - rightnum, rightnum);
                if (repstring == "" || repstring == null)
                    simplefilename = leftstring + rightstring + "." + extname;
                else
                    simplefilename = leftstring + repstring + rightstring + "." + extname;
            }
            else
                simplefilename = fullname;

            return simplefilename;
        }

        /// <summary>
        /// 将数据表转换成JSON类型串
        /// </summary>
        /// <param name="dt">要转换的数据表</param>
        /// <returns></returns>
        public static StringBuilder DataTableToJSON(System.Data.DataTable dt)
        {
            return DataTableToJson(dt, true);
        }

        /// <summary>
        /// 将数据表转换成JSON类型串
        /// </summary>
        /// <param name="dt">要转换的数据表</param>
        /// <param name="dispose">数据表转换结束后是否dispose掉</param>
        /// <returns></returns>
        public static StringBuilder DataTableToJson(System.Data.DataTable dt, bool dt_dispose)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[\r\n");

            //数据表字段名和类型数组
            string[] dt_field = new string[dt.Columns.Count];
            int i = 0;
            string formatStr = "{{";
            string fieldtype = "";
            foreach (System.Data.DataColumn dc in dt.Columns)
            {
                dt_field[i] = dc.Caption.ToLower().Trim();
                formatStr += "'" + dc.Caption.ToLower().Trim() + "':";
                fieldtype = dc.DataType.ToString().Trim().ToLower();
                if (fieldtype.IndexOf("int") > 0 || fieldtype.IndexOf("deci") > 0 ||
                    fieldtype.IndexOf("floa") > 0 || fieldtype.IndexOf("doub") > 0 ||
                    fieldtype.IndexOf("bool") > 0)
                {
                    formatStr += "{" + i + "}";
                }
                else
                {
                    formatStr += "'{" + i + "}'";
                }

                formatStr += ",";
                i++;
            }

            if (formatStr.EndsWith(","))
                formatStr = formatStr.Substring(0, formatStr.Length - 1); //去掉尾部","号

            formatStr += "}},";

            i = 0;
            object[] objectArray = new object[dt_field.Length];
            foreach (System.Data.DataRow dr in dt.Rows)
            {
                foreach (string fieldname in dt_field)
                {
                    //对 \ , ' 符号进行转换 
                    objectArray[i] = dr[dt_field[i]].ToString().Trim().Replace("\\", "\\\\").Replace("'", "\\'");
                    switch (objectArray[i].ToString())
                    {
                        case "True":
                            {
                                objectArray[i] = "true";
                                break;
                            }
                        case "False":
                            {
                                objectArray[i] = "false";
                                break;
                            }
                        default: break;
                    }

                    i++;
                }

                i = 0;
                stringBuilder.Append(string.Format(formatStr, objectArray));
            }

            if (stringBuilder.ToString().EndsWith(","))
                stringBuilder.Remove(stringBuilder.Length - 1, 1); //去掉尾部","号

            if (dt_dispose)
                dt.Dispose();

            return stringBuilder.Append("\r\n];");
        }

        /// <summary>
        /// Json特符字符过滤，参见http://www.json.org/
        /// </summary>
        /// <param name="sourceStr">要过滤的源字符串</param>
        /// <returns>返回过滤的字符串</returns>
        public static string JsonCharFilter(string sourceStr)
        {
            sourceStr = sourceStr.Replace("\\", "\\\\");
            sourceStr = sourceStr.Replace("\b", "\\\b");
            sourceStr = sourceStr.Replace("\t", "\\\t");
            sourceStr = sourceStr.Replace("\n", "\\\n");
            sourceStr = sourceStr.Replace("\n", "\\\n");
            sourceStr = sourceStr.Replace("\f", "\\\f");
            sourceStr = sourceStr.Replace("\r", "\\\r");
            return sourceStr.Replace("\"", "\\\"");
        }

        /// <summary>
        /// 转换时间为unix时间戳
        /// </summary>
        /// <param name="date">需要传递UTC时间,避免时区误差,例:DataTime.UTCNow</param>
        /// <returns></returns>
        public static double ConvertToUnixTimestamp(DateTime date)
        {
            DateTime origin = new DateTime(1970, 1, 1, 0, 0, 0, 0);
            TimeSpan diff = date - origin;
            return Math.Floor(diff.TotalSeconds);
        }

        /// <summary>
        /// 将8位日期型整型数据转换为日期字符串数据
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static string FormatDate(int date)
        {
            return FormatDate(date, false);
        }

        /// <summary>
        /// 将8位日期型整型数据转换为日期字符串数据
        /// </summary>
        /// <param name="date">整型日期</param>
        /// <param name="chnType">是否以中文年月日输出</param>
        /// <returns></returns>
        public static string FormatDate(int date, bool chnType)
        {
            string dateStr = date.ToString();

            if (date <= 0 || dateStr.Length != 8)
                return dateStr;

            if (chnType)
                return dateStr.Substring(0, 4) + "年" + dateStr.Substring(4, 2) + "月" + dateStr.Substring(6) + "日";
            return dateStr.Substring(0, 4) + "-" + dateStr.Substring(4, 2) + "-" + dateStr.Substring(6);
        }

        #endregion

        #region 网络操作

        /// <summary>
        /// 根据Url获得源文件内容
        /// </summary>
        /// <param name="url">合法的Url地址</param>
        /// <returns></returns>
        public static string GetSourceTextByUrl(string url)
        {
            return GetSourceTextByUrl(url, Encoding.UTF8);
        }

        public static string GetSourceTextByUrl(string url, Encoding encoding)
        {
            return GetSourceTextByUrlSetCookie(url, encoding, "");
        }


        /// <summary>
        /// 根据Url获得源文件内容
        /// </summary>
        /// <param name="url">合法的Url地址</param>
        /// <returns></returns>
        public static string GetSourceTextByUrlSetCookie(string url, string cookie)
        {
            return GetSourceTextByUrlSetCookie(url, Encoding.UTF8, cookie);
        }

        public static string GetSourceTextByUrlSetCookie(string url, Encoding encoding, string cookie)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(url);
                request.Timeout = 10000; //10秒超时 
                request.KeepAlive = false;

                string[] cookies = cookie.Split(";".ToCharArray());
                CookieContainer cc = new CookieContainer();
                foreach (string c in cookies)
                    cc.SetCookies(new Uri(url), c);
                request.CookieContainer = cc;

                string result = string.Empty;
                using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
                {
                    using (Stream resStream = response.GetResponseStream())
                    {
                        using (StreamReader reader = new StreamReader(resStream, Encoding.UTF8))
                        {
                            result = reader.ReadToEnd();
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "HttpWebRequest错误:" + url, LogLevel.Error, ex);
                return "";
            }
        }

        /// <summary>
        /// http POST请求url
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string GetHttpWebResponse(string url)
        {
            return GetHttpWebResponse(url, string.Empty);
        }

        /// <summary>
        /// http POST请求url
        /// </summary>
        /// <param name="url">请求的地址</param>
        /// <param name="postData">参数</param>
        /// <returns></returns>
        public static string GetHttpWebResponse(string url, string postData)
        {
            return GetHttpWebResponse(url, postData, Encoding.UTF8);
        }

        public static string GetHttpWebResponse(string url, string postData, Encoding encoding)
        {
            HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(url);
            request.Method = "POST";
            request.ContentType = "application/x-www-form-urlencoded";
            byte[] byteArray = Encoding.UTF8.GetBytes(postData);
            request.ContentLength = byteArray.Length;
            request.Timeout = 20000;

            //try
            //{ 
            using (Stream reqStream = request.GetRequestStream())
            {
                reqStream.Write(byteArray, 0, byteArray.Length);
            }

            //if (swRequestWriter != null)
            //    swRequestWriter.Close();
            string result = string.Empty;
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {
                using (Stream resStream = response.GetResponseStream())
                {
                    using (StreamReader reader = new StreamReader(resStream, Encoding.UTF8))
                    {
                        result = reader.ReadToEnd();
                    }
                }
            }
            //}
            //catch (Exception ex)
            //{
            //    System.Web.HttpContext.Current.Response.Write(ex.ToString());
            //}
            //finally
            //{

            //}
            return result;
        }

        #endregion

        #region 获取客户端信息

        private static string[] browerNames = { "MSIE", "Firefox", "Opera", "Netscape", "Safari", "Lynx", "Konqueror", "Mozilla" };
        //private const string[] osNames = { "Win", "Mac", "Linux", "FreeBSD", "SunOS", "OS/2", "AIX", "Bot", "Crawl", "Spider" };

        /// <summary>
        /// 获得浏览器信息
        /// </summary>
        /// <returns></returns>
        public static string GetClientBrower()
        {
            //string agent = HttpContext.Current.Request.ServerVariables["HTTP_USER_AGENT"];
            string agent = HttpHelper.HttpContext.Request.Headers["X-Forwarded-For"];
            if (!string.IsNullOrEmpty(agent))
            {
                foreach (string name in browerNames)
                {
                    if (agent.Contains(name))
                        return name;
                }
            }

            return "Other";
        }

        /// <summary>
        /// 获得操作系统信息
        /// </summary>
        /// <returns></returns>
        public static string GetClientOS()
        {
            string os = string.Empty;
            //string agent = System.Web.HttpContext.Current.Request.ServerVariables["HTTP_USER_AGENT"];
            string agent = HttpHelper.HttpContext.Request.Headers["X-Forwarded-For"];
            if (agent == null)
                return "Other";

            if (agent.IndexOf("Win") > -1)
                os = "Windows";
            else if (agent.IndexOf("Mac") > -1)
                os = "Mac";
            else if (agent.IndexOf("Linux") > -1)
                os = "Linux";
            else if (agent.IndexOf("FreeBSD") > -1)
                os = "FreeBSD";
            else if (agent.IndexOf("SunOS") > -1)
                os = "SunOS";
            else if (agent.IndexOf("OS/2") > -1)
                os = "OS/2";
            else if (agent.IndexOf("AIX") > -1)
                os = "AIX";
            else if (Regex.IsMatch(agent, @"(Bot|Crawl|Spider)"))
                os = "Spiders";
            else
                os = "Other";
            return os;
        }

        #endregion


        #region 重启iis

        /// <summary>
        ///  通过更新web.config文件方式来重启IIS进程池（注：iis中web园数量须大于1,且为非虚拟主机用户才可调用该方法）
        /// </summary>
        /// <param name="path">相对当前目录路径 如："~/" 或者 "/" (控制器中应该使用"~/"，global中用"/")</param>
        //public static void RestartIISProcess(string path="/")
        //{
        //    try
        //    {
        //        System.Xml.XmlDocument xmldoc = new System.Xml.XmlDocument();
        //        xmldoc.Load(Utils.GetMapPath(path + "web.config"));
        //        System.Xml.XmlTextWriter writer = new System.Xml.XmlTextWriter(Utils.GetMapPath(path + "web.config"),
        //            null);
        //        writer.Formatting = System.Xml.Formatting.Indented;
        //        xmldoc.WriteTo(writer);
        //        writer.Flush();
        //        writer.Close();
        //    }
        //    catch
        //    {

        //    }
        //}

        #endregion

        #region cookie

        ///// <summary>
        ///// 写cookie值
        ///// </summary>
        ///// <param name="strName">名称</param>
        ///// <param name="strValue">值</param>
        //public static void WriteCookie(string strName, string strValue)
        //{
        //    HttpCookie cookie = HttpContext.Current.Request.Cookies[strName];
        //    if (cookie == null)
        //    {
        //        cookie = new HttpCookie(strName);
        //    }
        //    cookie.Value = strValue;
        //    HttpContext.Current.Response.AppendCookie(cookie);
        //}

        ///// <summary>
        ///// 写cookie值
        ///// </summary>
        ///// <param name="strName">名称</param>
        ///// <param name="strValue">值</param>
        //public static void WriteCookie(string strName, string key, string strValue)
        //{
        //    HttpCookie cookie = HttpContext.Current.Request.Cookies[strName];
        //    if (cookie == null)
        //    {
        //        cookie = new HttpCookie(strName);
        //    }
        //    cookie[key] = strValue;
        //    HttpContext.Current.Response.AppendCookie(cookie);
        //}

        ///// <summary>
        ///// 写cookie值
        ///// </summary>
        ///// <param name="strName">名称</param>
        ///// <param name="strValue">值</param>
        ///// <param name="strValue">过期时间(分钟)</param>
        //public static void WriteCookie(string strName, string strValue, int expires)
        //{
        //    HttpCookie cookie = HttpContext.Current.Request.Cookies[strName];
        //    if (cookie == null)
        //    {
        //        cookie = new HttpCookie(strName);
        //    }
        //    cookie.Value = strValue;
        //    cookie.Expires = DateTimeHelper.GetNowTime().AddMinutes(expires);
        //    HttpContext.Current.Response.AppendCookie(cookie);
        //}

        ///// <summary>
        ///// 读cookie值
        ///// </summary>
        ///// <param name="strName">名称</param>
        ///// <returns>cookie值</returns>
        //public static string GetCookie(string strName)
        //{
        //    if (HttpContext.Current.Request.Cookies != null && HttpContext.Current.Request.Cookies[strName] != null)
        //        return HttpContext.Current.Request.Cookies[strName].Value.ToString();

        //    return "";
        //}

        ///// <summary>
        ///// 读cookie值
        ///// </summary>
        ///// <param name="strName">名称</param>
        ///// <returns>cookie值</returns>
        //public static string GetCookie(string strName, string key)
        //{
        //    if (HttpContext.Current.Request.Cookies != null && HttpContext.Current.Request.Cookies[strName] != null && HttpContext.Current.Request.Cookies[strName][key] != null)
        //        return HttpContext.Current.Request.Cookies[strName][key].ToString();

        //    return "";
        //}

        #endregion

        /// <summary>
        /// 得到论坛的真实路径
        /// </summary>
        /// <returns></returns>
        //public static string GetTrueForumPath()
        //{
        //    string forumPath = HttpContext.Current.Request.Path;
        //    if (forumPath.LastIndexOf("/") != forumPath.IndexOf("/"))
        //        forumPath = forumPath.Substring(forumPath.IndexOf("/"), forumPath.LastIndexOf("/") + 1);
        //    else
        //        forumPath = "/";

        //    return forumPath;
        //}

        /// <summary>
        /// 判断文件类型
        /// </summary>
        /// <param name="hifile"></param>
        /// <returns></returns>
        public static bool IsAllowedExtension(string path, string fileext)
        {
            bool ret = false;

            FileStream fs = new FileStream(path, FileMode.Open, FileAccess.Read);
            BinaryReader r = new BinaryReader(fs);
            string fileclass = "";
            byte buffer;
            try
            {
                buffer = r.ReadByte();
                fileclass = buffer.ToString();
                buffer = r.ReadByte();
                fileclass += buffer.ToString();
            }
            catch
            {
                return false;
            }

            r.Close();
            fs.Close();
            /*文件扩展名说明
             *7173        gif
             *255216      jpg
             *13780       png
             *6677        bmp
             *239187      txt,aspx,asp,sql
             *208207      xls.doc.ppt
             *6063        xml
             *6033        htm,html
             *4742        js
             *8075        xlsx,zip,pptx,mmap,zip
             *8297        rar
             *01          accdb,mdb
             *7790        exe,dll
             *5666        psd
             *255254      rdp
             *10056       bt种子
             *64101       bat
             */
            int fextindex = GetInArrayID(fileext, new string[] { "jpg", "jpeg", "gif", "bmp", "png" });
            if (fextindex > -1 && fextindex < 5)
            {
                String[] fileType = { "7173", "255216", "13780", "6677" };

                for (int i = 0; i < fileType.Length; i++)
                {
                    if (fileclass == fileType[i])
                    {
                        ret = true;
                        break;
                    }
                }
            }

            if (ret)
            {
                StreamReader sr = new StreamReader(path, Encoding.Default);
                string strContent = sr.ReadToEnd();
                sr.Close();
                string str = "request|script|.getfolder|.createfolder|.deletefolder|.createdirectory|.deletedirectory|.saveas|wscript.shell|script.encode|server.|.createobject|execute|activexobject|language=";
                foreach (string s in str.Split('|'))
                    if (strContent.IndexOf(s) != -1)
                    {
                        ret = false;
                    }
            }

            return ret;
        }

        /// <summary>
        /// 判断内容是否包含图片
        /// </summary>
        /// <param name="sHtmlText"></param>
        /// <returns></returns>
        public static bool GetHtmlIsImage(string sHtmlText)
        {
            // 定义正则表达式用来匹配 img 标签
            Regex regImg = new Regex(@"<img\b[^<>]*?\bsrc[\s\t\r\n]*=[\s\t\r\n]*[""']?[\s\t\r\n]*(?<imgUrl>[^\s\t\r\n""'<>]*)[^<>]*?/?[\s\t\r\n]*>", RegexOptions.IgnoreCase);

            // 搜索匹配的字符串
            MatchCollection matches = regImg.Matches(sHtmlText);

            if (matches.Count > 0)
                return true;
            else
                return false;
        }

        /// <summary>
        /// 去除字符串中的图片信息
        /// </summary>
        /// <param name="sHtmlText"></param>
        /// <returns></returns>
        public static string GetHtmlRemoveImage(string sHtmlText)
        {
            // 定义正则表达式用来匹配 img 标签
            Regex regImg = new Regex(@"<img\b[^<>]*?\bsrc[\s\t\r\n]*=[\s\t\r\n]*[""']?[\s\t\r\n]*(?<imgUrl>[^\s\t\r\n""'<>]*)[^<>]*?/?[\s\t\r\n]*>", RegexOptions.IgnoreCase);

            // 搜索匹配的字符串
            MatchCollection matches = regImg.Matches(sHtmlText);

            if (matches.Count > 0)
            {
                foreach (Match match in matches)
                {
                    int i = sHtmlText.IndexOf(match.ToString());
                    sHtmlText = sHtmlText.Remove(i, match.ToString().Length);
                }
            }

            return sHtmlText;
        }


        /// <summary>
        /// 获取内容的所有图片列表
        /// </summary>
        /// <param name="sHtmlText"></param>
        /// <returns></returns>
        public static string[] GetHtmlImageUrlList(string sHtmlText)
        {
            if (Utils.StrIsNullOrEmpty(sHtmlText)) return new string[0] { };
            // 定义正则表达式用来匹配 img 标签
            Regex regImg = new Regex(@"<img\b[^<>]*?\bsrc[\s\t\r\n]*=[\s\t\r\n]*[""']?[\s\t\r\n]*(?<imgUrl>[^\s\t\r\n""'<>]*)[^<>]*?/?[\s\t\r\n]*>", RegexOptions.IgnoreCase);

            // 搜索匹配的字符串
            MatchCollection matches = regImg.Matches(sHtmlText);

            int i = 0;
            string[] sUrlList = new string[matches.Count];

            // 取得匹配项列表
            foreach (Match match in matches)
                sUrlList[i++] = match.Groups["imgUrl"].Value;

            return sUrlList;
        }


        /// <summary>
        /// 金额转为大写金额
        /// </summary>
        /// <param name="LowerMoney"></param>
        /// <returns></returns>
        public static string MoneyToChinese(string LowerMoney)
        {
            string functionReturnValue = null;
            bool IsNegative = false; // 是否是负数
            if (LowerMoney.Trim().Substring(0, 1) == "-")
            {
                // 是负数则先转为正数
                LowerMoney = LowerMoney.Trim().Remove(0, 1);
                IsNegative = true;
            }

            string strLower = null;
            string strUpart = null;
            string strUpper = null;
            int iTemp = 0;
            // 保留两位小数 123.489→123.49　　123.4→123.4
            LowerMoney = Math.Round(double.Parse(LowerMoney), 2).ToString();
            if (LowerMoney.IndexOf(".") > 0)
            {
                if (LowerMoney.IndexOf(".") == LowerMoney.Length - 2)
                {
                    LowerMoney = LowerMoney + "0";
                }
            }
            else
            {
                LowerMoney = LowerMoney + ".00";
            }

            strLower = LowerMoney;
            iTemp = 1;
            strUpper = "";
            while (iTemp <= strLower.Length)
            {
                switch (strLower.Substring(strLower.Length - iTemp, 1))
                {
                    case ".":
                        strUpart = "圆";
                        break;
                    case "0":
                        strUpart = "零";
                        break;
                    case "1":
                        strUpart = "壹";
                        break;
                    case "2":
                        strUpart = "贰";
                        break;
                    case "3":
                        strUpart = "叁";
                        break;
                    case "4":
                        strUpart = "肆";
                        break;
                    case "5":
                        strUpart = "伍";
                        break;
                    case "6":
                        strUpart = "陆";
                        break;
                    case "7":
                        strUpart = "柒";
                        break;
                    case "8":
                        strUpart = "捌";
                        break;
                    case "9":
                        strUpart = "玖";
                        break;
                }

                switch (iTemp)

                {
                    case 1:
                        strUpart = strUpart + "分";
                        break;
                    case 2:
                        strUpart = strUpart + "角";
                        break;
                    case 3:
                        strUpart = strUpart + "";
                        break;
                    case 4:
                        strUpart = strUpart + "";
                        break;

                    case 5:
                        strUpart = strUpart + "拾";
                        break;
                    case 6:
                        strUpart = strUpart + "佰";
                        break;
                    case 7:
                        strUpart = strUpart + "仟";
                        break;
                    case 8:
                        strUpart = strUpart + "万";
                        break;
                    case 9:
                        strUpart = strUpart + "拾";
                        break;
                    case 10:
                        strUpart = strUpart + "佰";
                        break;
                    case 11:
                        strUpart = strUpart + "仟";
                        break;
                    case 12:
                        strUpart = strUpart + "亿";
                        break;
                    case 13:
                        strUpart = strUpart + "拾";
                        break;
                    case 14:
                        strUpart = strUpart + "佰";
                        break;
                    case 15:
                        strUpart = strUpart + "仟";
                        break;
                    case 16:
                        strUpart = strUpart + "万";
                        break;
                    default:
                        strUpart = strUpart + "";
                        break;
                }

                strUpper = strUpart + strUpper;
                iTemp = iTemp + 1;
            }

            strUpper = strUpper.Replace("零拾", "零");
            strUpper = strUpper.Replace("零佰", "零");
            strUpper = strUpper.Replace("零仟", "零");
            strUpper = strUpper.Replace("零零零", "零");
            strUpper = strUpper.Replace("零零", "零");
            strUpper = strUpper.Replace("零角零分", "整");
            strUpper = strUpper.Replace("零分", "整");
            strUpper = strUpper.Replace("零角", "零");
            strUpper = strUpper.Replace("零亿零万零圆", "亿圆");
            strUpper = strUpper.Replace("亿零万零圆", "亿圆");
            strUpper = strUpper.Replace("零亿零万", "亿");
            strUpper = strUpper.Replace("零万零圆", "万圆");
            strUpper = strUpper.Replace("零亿", "亿");
            strUpper = strUpper.Replace("零万", "万");
            strUpper = strUpper.Replace("零圆", "圆");
            strUpper = strUpper.Replace("零零", "零");

            // 对壹圆以下的金额的处理
            if (strUpper.Substring(0, 1) == "圆")
            {
                strUpper = strUpper.Substring(1, strUpper.Length - 1);
            }

            if (strUpper.Substring(0, 1) == "零")
            {
                strUpper = strUpper.Substring(1, strUpper.Length - 1);
            }

            if (strUpper.Substring(0, 1) == "角")
            {
                strUpper = strUpper.Substring(1, strUpper.Length - 1);
            }

            if (strUpper.Substring(0, 1) == "分")
            {
                strUpper = strUpper.Substring(1, strUpper.Length - 1);
            }

            if (strUpper.Substring(0, 1) == "整")
            {
                strUpper = "零圆整";
            }

            functionReturnValue = strUpper;
            if (IsNegative == true)
            {
                return "负" + functionReturnValue;
            }
            else
            {
                return functionReturnValue;
            }
        }

        /// <summary>
        /// 金额精确
        /// </summary>
        /// <param name="Money">金额</param>
        /// <param name="Exact">1-元，2-角，3-分</param>
        /// <returns></returns>
        public static decimal MoneyExact(decimal? num, int? Exact)
        {
            string numToString = num.ToString();
            int index = numToString.IndexOf(".");
            int length = numToString.Length;
            if (index != -1)
            {
                string value = numToString;
                if (Exact == 1)
                {
                    value = numToString.Substring(0, index);
                }
                else if (Exact == 2)
                {
                    value = $"{numToString.Substring(0, index)}.{numToString.Substring(index + 1, Math.Min(length - index - 1, 1))}";
                }
                else if (Exact == 3)
                {
                    value = $"{numToString.Substring(0, index)}.{numToString.Substring(index + 1, Math.Min(length - index - 1, 2))}";
                }

                return decimal.Parse(value);
            }
            else
            {
                return num.Value;
            }
        }

        /// <summary>
        /// 将整数转为大写的中文数字
        /// </summary>
        /// <param name="ni_intInput"></param>
        /// <returns></returns>
        public static string ToCNUpperCase(int ni_intInput)
        {
            string tstrRet = "";
            int tintInput;
            int tintRemainder, tintDigitPosIndex = 0;
            int tintLoopX = 0;
            string[] tastrNumCNChar = new string[] { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
            string[] tastrDigitPosCNChar = new string[] { "", "十", "佰", "仟", "万", "亿" };
            tintInput = ni_intInput;
            tintLoopX = 0;
            while (tintInput / 10 > 0 || tintInput > 0)
            {
                tintRemainder = (tintInput % 10);
                if (tintLoopX == 5) //十万
                    tintDigitPosIndex = 1;
                else if (tintLoopX == 8) //亿
                    tintDigitPosIndex = 5;
                else if (tintLoopX == 9) //十亿
                    tintDigitPosIndex = 1;
                //end if
                if (tintRemainder > 0)
                    tstrRet
                        = tastrNumCNChar[tintRemainder] + tastrDigitPosCNChar[tintDigitPosIndex] + tstrRet;
                else
                    tstrRet
                        = tastrNumCNChar[tintRemainder] + tstrRet;
                ;
                //end if
                tintDigitPosIndex += 1;
                tintLoopX += 1;
                tintInput /= 10;
            } //end while

            tstrRet = System.Text.RegularExpressions.Regex.Replace(tstrRet, "零零*零*", "零");
            return tstrRet;
        }

        /// <summary>
        /// 将整数转为小写的中文数字
        /// </summary>
        /// <param name="ni_intInput"></param>
        /// <returns></returns>
        public static string ToCNLowerCase(int ni_intInput)
        {
            string tstrRet = "";
            int tintInput;
            int tintRemainder, tintDigitPosIndex = 0;
            int tintLoopX = 0;

            string[] tastrNumCNChar = new string[] { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
            string[] tastrDigitPosCNChar = new string[] { "", "十", "百", "千", "万", "亿" };

            tintInput = ni_intInput;

            tintLoopX = 0;
            while (tintInput / 10 > 0 || tintInput > 0)
            {
                tintRemainder = (tintInput % 10);

                if (tintLoopX == 5) //十万
                    tintDigitPosIndex = 1;
                else if (tintLoopX == 8) //亿
                    tintDigitPosIndex = 5;
                else if (tintLoopX == 9) //十亿
                    tintDigitPosIndex = 1;
                //end if

                if (tintRemainder > 0)
                    tstrRet
                        = tastrNumCNChar[tintRemainder] + tastrDigitPosCNChar[tintDigitPosIndex] + tstrRet;
                else
                    tstrRet
                        = tastrNumCNChar[tintRemainder] + tstrRet;
                ;
                //end if

                tintDigitPosIndex += 1;

                tintLoopX += 1;
                tintInput /= 10;
            } //end while

            tstrRet = System.Text.RegularExpressions.Regex.Replace(tstrRet, "零零*零*", "零");
            return tstrRet;
        }

        /// <summary>
        /// 返回小数点后两位
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        public static float GetDecimalPoint(float num)
        {
            return GetDecimalPoint(num, 2);
        }

        /// <summary>
        /// 返回小数点后两位
        /// </summary>
        /// <param name="num"></param>
        /// <returns></returns>
        public static float GetDecimalPoint(float num, int size)
        {
            return ObjectToFloat(Math.Round(num, size));
        }

        internal class ListComparer<TBaseBusinessObject> : IComparer<TBaseBusinessObject>
        {
            private string propertyName;

            public ListComparer(string PropertyName)
            {
                propertyName = PropertyName;
            }


            #region IComparer<TBaseBusinessObject> Members

            public int Compare(TBaseBusinessObject x, TBaseBusinessObject y)
            {
                PropertyInfo property = typeof(TBaseBusinessObject).GetProperty(propertyName);
                if (property.PropertyType == Type.GetType("System.Int16"))
                {
                    int xNumber = 0;
                    int yNumber = 0;
                    if (property.GetValue(x, null) != null)
                    {
                        xNumber = Convert.ToInt16(property.GetValue(x, null).ToString());
                    }

                    if (property.GetValue(y, null) != null)
                    {
                        yNumber = Convert.ToInt16(property.GetValue(y, null).ToString());
                    }

                    return xNumber.CompareTo(yNumber);
                }

                if (property.PropertyType == Type.GetType("System.Int32"))
                {
                    int xNumber = 0;
                    int yNumber = 0;
                    if (property.GetValue(x, null) != null)
                    {
                        xNumber = Convert.ToInt32(property.GetValue(x, null).ToString());
                    }

                    if (property.GetValue(y, null) != null)
                    {
                        yNumber = Convert.ToInt32(property.GetValue(y, null).ToString());
                    }

                    return xNumber.CompareTo(yNumber);
                }

                if (property.PropertyType == Type.GetType("System.Double"))
                {
                    double xNumber = 0;
                    double yNumber = 0;
                    if (property.GetValue(x, null) != null)
                    {
                        xNumber = Convert.ToDouble(property.GetValue(x, null).ToString());
                    }

                    if (property.GetValue(y, null) != null)
                    {
                        yNumber = Convert.ToDouble(property.GetValue(y, null).ToString());
                    }

                    return xNumber.CompareTo(yNumber);
                }

                if (property.PropertyType == Type.GetType("System.DateTime"))
                {
                    DateTime xTime = DateTimeHelper.GetNowTime();
                    DateTime yTime = DateTimeHelper.GetNowTime();
                    if (property.GetValue(x, null) != null)
                    {
                        xTime = Convert.ToDateTime(property.GetValue(x, null).ToString());
                    }

                    if (property.GetValue(y, null) != null)
                    {
                        yTime = Convert.ToDateTime(property.GetValue(y, null).ToString());
                    }

                    return xTime.CompareTo(yTime);
                }

                if ((property.PropertyType == Type.GetType("System.String")) || (property.PropertyType == Type.GetType("System.Boolean")))
                {
                    string xText = string.Empty;
                    string yText = string.Empty;
                    if (property.GetValue(x, null) != null)
                    {
                        xText = property.GetValue(x, null).ToString();
                    }

                    if (property.GetValue(y, null) != null)
                    {
                        yText = property.GetValue(y, null).ToString();
                    }

                    return xText.CompareTo(yText);
                }

                return 0;
            }

            #endregion
        }

        public class BaseBusinessObjectList<TBaseBusinessObject> : List<TBaseBusinessObject>
        {
            public void Sort(string sortfield, bool isAscending)
            {
                //这里实例化了刚才写的IComparer类。
                ListComparer<TBaseBusinessObject> listComparer = new ListComparer<TBaseBusinessObject>(sortfield);
                base.Sort(listComparer);
                if (!isAscending) base.Reverse();
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="StrUrl"></param>
        /// <param name="StrFileName"></param>
        /// <returns></returns>
        public static bool DownloadFile(string StrUrl, string StrFileName)
        {
            try
            {
                Uri u = new Uri(StrUrl);
                string Cookie = String.Empty;
                String refer = StrUrl.Substring(0, StrUrl.LastIndexOf("/") + 1);
                System.Net.HttpWebRequest req = System.Net.HttpWebRequest.Create(StrUrl) as System.Net.HttpWebRequest;
                req.AllowAutoRedirect = false;
                req.Referer = refer;
                req.UserAgent = "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:********) Gecko/20101203 Firefox/3.6.13";
                System.Net.HttpWebResponse res = req.GetResponse() as System.Net.HttpWebResponse;
                System.Net.WebHeaderCollection headers = res.Headers;
                String newUrl = "";
                if ((res.StatusCode == System.Net.HttpStatusCode.Found) ||
                    (res.StatusCode == System.Net.HttpStatusCode.Redirect) ||
                    (res.StatusCode == System.Net.HttpStatusCode.Moved) ||
                    (res.StatusCode == System.Net.HttpStatusCode.MovedPermanently))
                {
                    newUrl = headers["Location"];
                    newUrl = newUrl.Trim();
                }

                if (headers["Set-Cookie"] != null)
                {
                    Cookie = headers["Set-Cookie"];
                }

                NameValueCollection collHeader = new NameValueCollection();
                if (Cookie.Length > 0)
                {
                    collHeader.Add("Cookie", Cookie);
                }

                res.Close();
                req = null;
                if (!Regex.IsMatch(newUrl, @"^((file|gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)://).*$", RegexOptions.IgnoreCase))
                {
                    if (newUrl.StartsWith("/"))
                        newUrl = StrUrl.Substring(0, StrUrl.IndexOf(":")) + "://" + u.Host + (u.IsDefaultPort ? "" : ":" + u.Port) + "/" + newUrl.TrimStart('/');
                    else
                        newUrl = refer + newUrl.TrimStart('/');
                }
                //System.Web.HttpContext.Current.Response.Write(newUrl);
                //System.Web.HttpContext.Current.Response.End();

                req = System.Net.HttpWebRequest.Create(newUrl) as System.Net.HttpWebRequest;
                req.AllowAutoRedirect = true;
                req.Referer = StrUrl;
                req.UserAgent = "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:********) Gecko/20101203 Firefox/3.6.13";
                res = req.GetResponse() as System.Net.HttpWebResponse;

                System.IO.Stream stream = res.GetResponseStream();
                byte[] buffer = new byte[32 * 1024];
                int bytesProcessed = 0;
                System.IO.FileStream fs = System.IO.File.Create(StrFileName);
                int bytesRead;
                do
                {
                    bytesRead = stream.Read(buffer, 0, buffer.Length);
                    fs.Write(buffer, 0, bytesRead);
                    bytesProcessed += bytesRead;
                } while (bytesRead > 0);

                fs.Flush();
                fs.Close();
                res.Close();
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return true;
        }

        /// <summary>
        /// 给字符串前面补零
        /// </summary>
        /// <param name="len">补零后字符串长度</param>
        /// <param name="str">原字符串</param>
        /// <returns></returns>
        public static string Fill0ToStr(string str, int len)
        {
            int strlength = str.Length;
            if (strlength >= len)
                return str;
            for (int i = 0; i < len - strlength; i++)
            {
                str = "0" + str;
            }

            return str;
        }


        public static string GetPropName<T>(Expression<Func<T, object>> expr)
        {
            switch (expr.Body.NodeType)
            {
                case ExpressionType.MemberAccess:
                    return ((MemberExpression)expr.Body).Member.Name;
                case ExpressionType.Convert:
                    return ((MemberExpression)((UnaryExpression)expr.Body).Operand).Member.Name;
                default:
                    return null;
            }
        }


        public static string ModelToSqlWhere(object model)
        {
            //此方法要进行大量的扩展
            string where = "";
            Type t = model.GetType(); //获得该类的Type
            //再用Type.GetProperties获得PropertyInfo[],然后就可以用foreach 遍历了
            foreach (PropertyInfo pi in t.GetProperties())
            {
                object value = pi.GetValue(model, null);
                //用pi.GetValue获得值
                string name = pi.Name; //获得属性的名字,后面就可以根据名字判断来进行些自己想要的操作
                string type = pi.PropertyType.Name;
                if (type == "Int32")
                {
                    where += " and " + name + "='" + value + "' ";
                }

                if (type == "String" && value != null)
                {
                    where += " and " + name + "= " + value + " ";
                }

                if (type == "Boolean")
                {
                    where += " and " + name + "= " + value + " ";
                }
                //进行你想要的操作
            }

            return where;
        }


        //jsonconvert 转换时间格式化
        public class JsonConvertDateTimeOverride_Date : DateTimeConverterBase
        {
            private static IsoDateTimeConverter dtConverter = new IsoDateTimeConverter { DateTimeFormat = "yyyy-MM-dd" };

            public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
            {
                return dtConverter.ReadJson(reader, objectType, existingValue, serializer);
            }

            public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
            {
                dtConverter.WriteJson(writer, value, serializer);
            }
        }

        public class JsonConvertDateTimeOverride_DateTime : DateTimeConverterBase
        {
            private static IsoDateTimeConverter dtConverter = new IsoDateTimeConverter { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" };

            public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
            {
                return dtConverter.ReadJson(reader, objectType, existingValue, serializer);
            }

            public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
            {
                dtConverter.WriteJson(writer, value, serializer);
            }
        }

        public class JsonConvertDateTimeOverride_Time : DateTimeConverterBase
        {
            private static IsoDateTimeConverter dtConverter = new IsoDateTimeConverter { DateTimeFormat = "HH:mm:ss" };

            public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
            {
                return dtConverter.ReadJson(reader, objectType, existingValue, serializer);
            }

            public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
            {
                dtConverter.WriteJson(writer, value, serializer);
            }
        }

        /// <summary>
        /// List转DataTable
        /// </summary>
        /// <typeparam name="T">Model</typeparam>
        /// <param name="items">List数据集</param>
        /// <returns></returns>
        public static DataTable ListToDataTable<T>(List<T> items)
        {
            var tb = new DataTable();

            PropertyInfo[] props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (PropertyInfo prop in props)
            {
                Type t = GetCoreType(prop.PropertyType);
                tb.Columns.Add(prop.Name, t);
            }

            foreach (T item in items)
            {
                var values = new object[props.Length];

                for (int i = 0; i < props.Length; i++)
                {
                    values[i] = props[i].GetValue(item, null);
                }

                tb.Rows.Add(values);
            }

            return tb;
        }

        /// <summary>
        /// List转DataTable
        /// </summary>
        /// <typeparam name="T">Model</typeparam>
        /// <param name="items">List数据集</param>
        /// <returns></returns>
        public static DataTable ListToDataTableString<T>(List<T> items)
        {
            var tb = new DataTable();

            PropertyInfo[] props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (PropertyInfo prop in props)
            {
                //Type t = GetCoreType(prop.PropertyType);
                tb.Columns.Add(prop.Name, typeof(string));
            }

            foreach (T item in items)
            {
                var values = new object[props.Length];

                for (int i = 0; i < props.Length; i++)
                {
                    try
                    {
                        var value = props[i].GetValue(item, null);
                        if (value != null && value is DateTime dateTimeValue)
                        {
                            values[i] = dateTimeValue.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                        }
                        else
                        {
                            values[i] = value?.ToString() ?? string.Empty;
                        }
                    }
                    catch
                    {
                        values[i] = string.Empty; // 赋值为默认空字符串
                    }
                }

                tb.Rows.Add(values);
            }

            return tb;
        }

        public static DataTable ListToDataTableStringByObj(List<object> items)
        {
            var tb = new DataTable();

            PropertyDescriptorCollection props = TypeDescriptor.GetProperties(items[0]);

            foreach (PropertyDescriptor prop in props)
            {
                if (!tb.Columns.Contains(prop.Name)) tb.Columns.Add(prop.Name, typeof(string));
            }

            foreach (object item in items)
            {
                var values = new object[tb.Columns.Count];

                for (int i = 0; i < tb.Columns.Count; i++)
                {
                    values[i] = props[i].GetValue(item);
                }

                tb.Rows.Add(values);
            }

            return tb;
        }

        private static Type GetCoreType(Type t)
        {
            if (t != null && (!t.IsValueType || (t.IsGenericType && t.GetGenericTypeDefinition() == typeof(Nullable<>))))
            {
                if (!t.IsValueType)
                {
                    return t;
                }
                else
                {
                    return Nullable.GetUnderlyingType(t);
                }
            }
            else
            {
                return t;
            }
        }


        #region 百度地图和高德地图坐标互转

        /// <summary>
        /// 百度转高德（百度坐标bd09ll–>火星坐标gcj02ll）
        /// </summary>
        /// <param name="bd_lat"></param>
        /// <param name="bd_lon"></param>
        /// <returns></returns>
        public static double[] BaiduToGaode(double bd_lat, double bd_lon)
        {
            double[] gd_lat_lon = new double[2];
            double PI = 3.14159265358979324 * 3000.0 / 180.0;
            double x = bd_lon - 0.0065, y = bd_lat - 0.006;
            double z = Math.Sqrt(x * x + y * y) - 0.00002 * Math.Sin(y * PI);
            double theta = Math.Atan2(y, x) - 0.000003 * Math.Cos(x * PI);
            gd_lat_lon[0] = z * Math.Cos(theta);
            gd_lat_lon[1] = z * Math.Sin(theta);
            return gd_lat_lon;
        }


        /// <summary>
        /// 高德转百度（火星坐标gcj02ll–>百度坐标bd09ll）
        /// </summary>
        /// <param name="gd_lon"></param>
        /// <param name="gd_lat"></param>
        /// <returns></returns>
        public static double[] GaodeToBaidu(double gd_lon, double gd_lat)
        {
            double[] bd_lat_lon = new double[2];
            double PI = 3.14159265358979324 * 3000.0 / 180.0;
            double x = gd_lon, y = gd_lat;
            double z = Math.Sqrt(x * x + y * y) + 0.00002 * Math.Sin(y * PI);
            double theta = Math.Atan2(y, x) + 0.000003 * Math.Cos(x * PI);
            bd_lat_lon[0] = z * Math.Cos(theta) + 0.0065;
            bd_lat_lon[1] = z * Math.Sin(theta) + 0.006;
            return bd_lat_lon;
        }

        #endregion


        #region URL地址编码

        /// <summary>
        /// c#与java的转换的UrlEncode 不同，采用特殊处理
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string _UrlEncode(string str)
        {
            StringBuilder builder = new StringBuilder();
            foreach (char c in str)
            {
                if (HttpUtility.UrlEncode(c.ToString()).Length > 1)
                {
                    builder.Append(HttpUtility.UrlEncode(c.ToString()).ToUpper());
                }
                else
                {
                    builder.Append(c);
                }
            }

            return builder.ToString();
        }

        #endregion


        #region 获取浏览器类型

        /// <summary>
        /// 获取浏览器类型（返回值对应Model.EnumBrowserType）
        /// </summary>
        /// <returns></returns>
        public static string UserAgent()
        {
            try
            {
                //string userAgent = HttpContext.Current.Request.UserAgent;
                string userAgent = HttpHelper.HttpContext.Response.Headers["User-Agent"];
                if (string.IsNullOrEmpty(userAgent))
                    return "Other";
                else
                    userAgent = userAgent.ToLower();

                if (userAgent.Contains("iphone") || userAgent.Contains("android") ||
                    userAgent.Contains("mobile") || userAgent.Contains("ucbrowser") || userAgent.Contains("mqqbrowser"))
                {
                    //判断是否移动端和微信浏览器
                    if (userAgent.Contains("micromessenger"))
                    {
                        return "MicroMessenger";
                    }
                    else if (userAgent.Contains("alipay"))
                    {
                        return "Alipay";
                    }
                    else
                    {
                        return "Mobile";
                    }
                }

                return "Other";
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, $"{nameof(UserAgent)}异常", LogLevel.Error, ex);
                return "Other";
            }
        }

        #endregion


        /// <summary>
        /// 将新的Model参数赋值旧的Model
        /// </summary>
        /// <param name="p1">需要获取的模型【旧】</param>
        /// <param name="p2">新参数模型</param>
        /// <returns>返回p1</returns>
        public static T Returnobj<T>(T p1, T p2)
        {
            if (p1 == null) return p2;

            //拷贝p1的一个副本，上下文不关联对象
            var pl = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(Newtonsoft.Json.JsonConvert.SerializeObject(p1));
            Type t1 = pl.GetType();
            Type t2 = p2.GetType();
            System.Reflection.PropertyInfo[] pro1 = t1.GetProperties();
            System.Reflection.PropertyInfo[] pro2 = t2.GetProperties();

            foreach (var item2 in pro2)
            {
                string name = item2.Name;
                object value = item2.GetValue(p2, null);

                foreach (var item1 in pro1)
                {
                    if (item1.Name == item2.Name && value != null)
                        item1.SetValue(pl, value, null);
                }
            }

            return pl;
        }

        /// <summary>
        /// 用户编辑操作日志，检测用户修改那些字段
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="oldData">旧数据</param>
        /// <param name="newData">新数据</param>
        /// <returns></returns>
        public static string returnLogUpdateFiled<T>(T oldData, T newData)
        {
            StringBuilder logStr = new StringBuilder();
            Type t1 = oldData.GetType();
            Type t2 = newData.GetType();
            System.Reflection.PropertyInfo[] pro1 = t1.GetProperties();
            System.Reflection.PropertyInfo[] pro2 = t2.GetProperties();

            foreach (var item2 in pro2)
            {
                object value2 = item2.GetValue(newData, null);

                foreach (var item1 in pro1)
                {
                    object value1 = item1.GetValue(oldData, null);

                    if (item1.Name == item2.Name)
                    {
                        if (value1 == null && value2 == null)
                            continue;
                        if (value1 == null && value2 != null)
                            logStr.Append(string.Format("[{0}]{1}->{2}，", item1.Name, value1, value2));
                        //if (value1 != null && value2 == null)
                        //    logStr.Append(string.Format("[{0}]{1}->{2}，", item1.Name, value1, value2));
                        if (value1 != null && value2 != null && value1.ToString() != value2.ToString())
                            logStr.Append(string.Format("[{0}]{1}->{2}，", item1.Name, value1, value2));
                    }
                }
            }

            return logStr.ToString();
        }

        /// <summary>
        /// 照片的base64字符串转成byte数组
        /// </summary>
        /// <param name="base64String">照片的base64字符串</param>
        /// <returns>byte数组，失败返回null</returns>
        public static byte[] ConvertPhotoByte(string base64String)
        {
            byte[] photoByte = null;
            try
            {
                photoByte = Convert.FromBase64String(base64String);
                Bitmap bmp2 = new Bitmap(new System.IO.MemoryStream(photoByte));
            }
            catch (Exception)
            {
                return null;
            }

            return photoByte;
        }

        /// <summary> 
        /// 将 byte[] 转成 Stream 
        /// </summary> 
        public static Stream BytesToStream(byte[] bytes)
        {
            Stream stream = new MemoryStream(bytes);
            return stream;
        }

        /// <summary>
        /// 业务类型转化(编号转字符串描述)
        /// </summary>
        public class TypeConvert
        {
            /// <summary>
            /// 停车状态转化
            /// </summary>
            public static string ParkOrderStatus(string ParkOrderStatus_No)
            {
                switch (ParkOrderStatus_No)
                {
                    case "199":
                        return "预入场";
                    case "200":
                        return "已入场";
                    case "201":
                        return "已出场";
                    case "202":
                        return "自动关闭";
                    case "203":
                        return "场内关闭";
                    case "204":
                        return "欠费出场";
                    default:
                        return "";
                }
            }

            /// <summary>
            /// 停车订单类型转化
            /// </summary>
            public static string CarType(string CarType_No)
            {
                switch (CarType_No)
                {
                    case "3651":
                        return "临时车A";
                    case "3650":
                        return "临时车B";
                    case "3649":
                        return "临时车C";
                    case "3648":
                        return "临时车D";
                    case "3652":
                        return "月租车A";
                    case "3653":
                        return "月租车B";
                    case "3654":
                        return "月租车C";
                    case "3655":
                        return "月租车D";
                    case "3656":
                        return "免费车";
                    case "3661":
                        return "月租车E";
                    case "3662":
                        return "月租车F";
                    case "3663":
                        return "月租车G";
                    case "3664":
                        return "月租车H";
                    default:
                        return "";
                }
            }

            /// <summary>
            /// 优惠券方式转化
            /// </summary>
            public static string CouponTypeName(string CouponType_Code)
            {
                switch (CouponType_Code)
                {
                    case "101":
                        return "减免金额";
                    case "102":
                        return "优惠时间 ";
                    case "103":
                        return "打折优惠";
                    default:
                        return "";
                }
            }


            /// <summary>
            /// 订单类型转化
            /// </summary>
            public static string OrderTypeName(string OrderType_No)
            {
                switch (OrderType_No)
                {
                    case "5901":
                        return "临时车缴费";
                    case "5902":
                        return "月租车充值";
                    case "5903":
                        return "储值车充值";
                    case "5904":
                        return "商家自助充值";
                    case "5905":
                        return "储值车扣费";
                    case "5910":
                        return "车位续期";
                    case "5919":
                        return "月租车缴费";
                    default:
                        return "";
                }
            }

            /// <summary>
            /// 阿里云消息相关错误码
            /// </summary>
            public static string IotMessagebroker(string code)
            {
                switch (code.ToUpper())
                {
                    case "IOT.MESSAGEBROKER.OFFLINE": return "由于设备离线导致失败";
                    case "IOT.MESSAGEBROKER.PUBLISHMESSAGEEXCEPTION": return "发送消息过程出现异常";
                    case "IOT.MESSAGEBROKER.PUBLISHMESSAGEFAILED": return "发送消息失败";
                    case "IOT.MESSAGEBROKER.HALFCONN": return "由于设备为半连接状态导致失败";
                    case "IOT.MESSAGEBROKER.TIMEOUT": return "由于超时导致失败";
                    case "IOT.MESSAGEBROKER.MESSAGECONTENTISNOTBASE64ENCODE": return "消息内容没有经过BASE64编码";
                    case "IOT.MESSAGEBROKER.NULLMESSAGECONTENT": return "消息内容不能为空";
                    case "IOT.MESSAGEBROKER.INVALIDTIMEOUTVALUE": return "超时时间参数设置有误";
                    case "IOT.MESSAGEBROKER.RRPCEXCEPTION": return "RRPC发送消息过程出现异常";
                    case "IOT.MESSAGEBROKER.RRPCFAILED": return "RRPC发送消息失败";
                    case "IOT.MESSAGEBROKER.RATELIMIT": return "由于限流导致失败";
                    case "IOT.DEVICE.ALREADYEXISTEDDEVICENAME": return "设备名称已经存在";
                    case "IOT.DEVICE.NOTEXISTEDDEVICE": return "设备不存在";
                    case "IOT.DEVICE.DELETEDEVICEFAILED": return "删除设备失败";
                    case "IOT.DEVICE.CREATEDEVICEFAILED": return "创建设备失败";
                    case "IOT.MESSAGEBROKER.NONEELEMENTINDESIRE": return "DESIRE信息中没有属性";
                    case "IOT.MESSAGEBROKER.NONEELEMENTINREPORT": return "REPORT信息中没有属性";
                    case "IOT.MESSAGEBROKER.CREATETOPICROUTEFAILED": return "创建TOPIC之间消息路由失败";
                    case "IOT.MESSAGEBROKER.CREATETOPICTEMPLATEEXCEPTION": return "创建TOPIC类过程发生异常";
                    case "IOT.MESSAGEBROKER.CREATETOPICTEMPLATEFAILED": return "创建TOPIC类失败";
                    case "IOT.MESSAGEBROKER.DELETETOPICTEMPLATEEXCEPTION": return "删除TOPIC类过程发生异常";
                    case "IOT.MESSAGEBROKER.DELETETOPICTEMPLATEFAILED": return "删除TOPIC类失败";
                    case "IOT.MESSAGEBROKER.DESTTOPICNAMEARRAYSIZEISLARGE": return "同一消息源TOPIC配置的路由目标TOPIC个数量超过最大限制数";
                    case "IOT.MESSAGEBROKER.DELETETOPICROUTEFAILED": return "删除指定TOPIC间的路由失败";
                    case "IOT.MESSAGEBROKER.DESIREINFOINSHADOWMESSAGEISNOTJSON": return "设备影子中的DESIRE信息不是JSON格式";
                    case "IOT.MESSAGEBROKER.DESIREVALUEISNULLINSHADOWMESSAGE": return "设备影子中的DESIRE信息值为空";
                    case "IOT.MESSAGEBROKER.ELEMENTKEYORVALUEISNULLINDESIRE": return "DESIRE信息包含有空的属性名称或者属性值";
                    case "IOT.MESSAGEBROKER.ELEMENTKEYORVALUEISNULLINREPORT": return "REPORT信息包含有空的属性名称或者属性值";
                    case "IOT.MESSAGEBROKER.REPORTINSHADOWMESSAGEISNOTJSON": return "设备影子中的STATE信息中的REPORT信息不是JSON格式";
                    case "IOT.MESSAGEBROKER.INVALIDFORMATTEDSRCTOPICNAME": return "消息源TOPIC名称格式错误";
                    case "IOT.MESSAGEBROKER.INVALIDFORMATTEDTOPICNAME": return "TOPIC格式错误";
                    case "IOT.MESSAGEBROKER.INVALIDFORMATTEDTOPICTEMPLATEID": return "TOPIC类ID格式错误";
                    case "IOT.MESSAGEBROKER.INVALIDTOPICTEMPLATEOPERATIONVALUE": return "TOPIC类的操作权限值错误";
                    case "IOT.MESSAGEBROKER.INVALIDVERSIONVALUEINSHADOWMESSAGE": return "设备影子中的VERSION值错误";
                    case "IOT.MESSAGEBROKER.METHODVALUEISNOTUPDATE": return "设备影子中的METHOD信息值不是UPDATE";
                    case "IOT.MESSAGEBROKER.NONEELEMENTDESTTOPICNAMEINARRAY": return "目标TOPIC列表中没有元素";
                    case "IOT.MESSAGEBROKER.NOTFOUNDDESIREINSHADOWMESSAGE": return "设备影子的STATE信息中没有DESIRE信息";
                    case "IOT.MESSAGEBROKER.NOTFOUNDMETHODINSHADOWMESSAGE": return "设备影子没有METHOD信息";
                    case "IOT.MESSAGEBROKER.NOTFOUNDREPORTINSHADOWMESSAGE": return "设备影子中没有REPORT信息";
                    case "IOT.MESSAGEBROKER.NOTFOUNDSTATEINSHADOWMESSAGE": return "设备影子中没有STATE信息";
                    case "IOT.MESSAGEBROKER.NOTFOUNDVERSIONORNULLVERSIONVALUE": return "缺少VERSION信息或者VERSION值为空";
                    case "IOT.MESSAGEBROKER.NOTMATCHEDPRODUCTKEYWITHSRCTOPICOWNER": return "消息源TOPIC对应的产品ID不属于当前用户";
                    case "IOT.MESSAGEBROKER.NULLSHADOWMESSAGE": return "设备影子内容不能为空";
                    case "IOT.MESSAGEBROKER.NULLSRCTOPICNAME": return "消息源TOPIC名称不能为空";
                    case "IOT.MESSAGEBROKER.NULLTOPICNAME": return "TOPIC不能为空";
                    case "IOT.MESSAGEBROKER.NULLTOPICTEMPLATEID": return "TOPIC类ID不能为空";
                    case "IOT.MESSAGEBROKER.NULLTOPICTEMPLATEOPERATION": return "TOPIC类的操作权限不能为空";
                    case "IOT.MESSAGEBROKER.QUERYDEVICESHADOWACTIONERROR": return "查询设备影子失败";
                    case "IOT.MESSAGEBROKER.QUERYPRODUCTTOPICLISTACTIONERROR": return "获取TOPIC类列表失败";
                    case "IOT.MESSAGEBROKER.QUERYTOPICREVERSEROUTETABLELISTACTIONERROR": return "获取消息反向路由列表（即消息源TOPIC列表）失败";
                    case "IOT.MESSAGEBROKER.QUERYTOPICROUTETABLELISTACTIONERROR": return "获取消息路由列表失败";
                    case "IOT.MESSAGEBROKER.QUERYTOPICTEMPLATEACTIONERROR": return "查询TOPIC类失败";
                    case "IOT.MESSAGEBROKER.QUERYTOPICTEMPLATEEXCEPTION": return "获取TOPIC类过程发生异常";
                    case "IOT.MESSAGEBROKER.SHADOWMESSAGEISNOTJSON": return "设备影子不是JSON格式";
                    case "IOT.MESSAGEBROKER.SHADOWMESSAGELENGTHISLARGE": return "设备影子的长度超过最大限制";
                    case "IOT.MESSAGEBROKER.TOOMANYELEMENTINDESIRE": return "DESIRE信息中包含的属性总数超过最大限定数";
                    case "IOT.MESSAGEBROKER.TOOMANYELEMENTINREPORT": return "REPORT信息包含的属性总数不能限定最大数";
                    case "IOT.MESSAGEBROKER.TOPICALREADYFOUND": return "同一产品下TOPIC类名称重复";
                    case "IOT.MESSAGEBROKER.TOPICTEMPLATECOUNTEXCEEDMAX": return "产品的TOPIC类数量超过最大值";
                    case "IOT.MESSAGEBROKER.TOPICTEMPLATEISNOTFOUND": return "TOPIC类不存在";
                    case "IOT.MESSAGEBROKER.UPDATEDEVICESHADOWMESSAGEFAILED": return "更新设备影子失败";
                    case "IOT.MESSAGEBROKER.UPDATETOPICTEMPLATEEXCEPTION": return "更新TOPIC类过程发生异常";
                    case "IOT.MESSAGEBROKER.UPDATETOPICTEMPLATEFAILED": return "更新TOPIC类失败";
                    case "IOT.DEVICE.ADDTOPORELATIONFAILED": return "添加拓扑关系失败";
                    case "IOT.DEVICE.APPLYMANYDEVICESFAILED": return "申请批量创建设备失败";
                    case "IOT.DEVICE.CREATEDEVICETASKISRUNNING": return "创建设备的申请任务还在执行中";
                    case "IOT.DEVICE.DEVICEAPPLYISNOTFOUND": return "申请设备的申请单不存在";
                    case "IOT.DEVICE.DEVICECOUNTEXCEEDED": return "批量申请的设备数量超过最大值";
                    case "IOT.DEVICE.DELETEDEVICEPROPERTYFAILED": return "删除设备属性失败";
                    case "IOT.DEVICE.DISABLEDEVICEFAILED": return "禁用设备失败";
                    case "IOT.DEVICE.ENABLEDEVICEFAILED": return "启用设备失败";
                    case "IOT.DEVICE.INACTIVEDEVICE": return "设备未激活";
                    case "IOT.DEVICE.INVALIDFORMATTEDAPPLYID": return "创建设备的申请单ID值错误";
                    case "IOT.DEVICE.INCORRENTDEVICEAPPLYINFO": return "设备申请信息错误";
                    case "IOT.DEVICE.INVALIDFORMATTEDDEVICENAME": return "设备名称格式错误";
                    case "IOT.DEVICE.INVALIDFORMATTEDDEVICEPROPERTYKEY": return "设备属性名称格式错误";
                    case "IOT.DEVICE.INVALIDFORMATTEDDEVICEPROPERTIESSTRING": return "入参设备属性格式错误";
                    case "IOT.DEVICE.INVALIDIOTID": return "设备ID错误";
                    case "IOT.DEVICE.INVALIDTIMEBUCKET": return "指定的时间区间不合法";
                    case "IOT.DEVICE.INVOKETHINGSERVICEFAILED": return "调用设备服务失败";
                    case "IOT.DEVICE.LONGDEVICEPROPERTIESSTRING": return "入参设备属性长度超过最大值";
                    case "IOT.DEVICE.NONEDEVICENAMEELEMENT": return "设备名称列表为空";
                    case "IOT.DEVICE.NONEDEVICEPROPERTIES": return "没有有效的设备属性";
                    case "IOT.DEVICE.NULLAPPLYID": return "创建设备的申请ID不能为空";
                    case "IOT.DEVICE.NULLDEVICENAME": return "设备名称不能为空";
                    case "IOT.DEVICE.NULLDEVICEPROPERTYKEY": return "设备属性名称不能为空";
                    case "IOT.DEVICE.NULLDEVICEPROPERTIESSTRING": return "入参设备属性不能为空";
                    case "IOT.DEVICE.QUERYDEVICEAPPLYACTIONERROR": return "查询设备申请单信息出错";
                    case "IOT.DEVICE.QUERYDEVICEATTRDATAHISTORYFAILED": return "获取设备属性数据历史记录失败";
                    case "IOT.DEVICE.QUERYDEVICEATTRSTATUSFAILED": return "获取设备属性状态信息失败";
                    case "IOT.DEVICE.QUERYDEVICEEVENTHISTORYFAILED": return "获取设备事件调用记录失败";
                    case "IOT.DEVICE.QUERYDEVICELISTACTIONERROR": return "查询设备列表失败";
                    case "IOT.DEVICE.QUERYDEVICESERVICEHISTORYFAILED": return "获取设备服务调用记录失败";
                    case "IOT.DEVICE.QUERYDEVICESTATISTICSFAILED": return "查询设备统计信息失败";
                    case "IOT.DEVICE.QUERYDEVICESTATUSFAILED": return "查询设备状态信息失败";
                    case "IOT.DEVICE.QUERYTOPORELATIONFAILED": return "查询拓扑关系失败";
                    case "IOT.DEVICE.REMOVETOPORELATIONFAILED": return "移除拓扑关系失败";
                    case "IOT.DEVICE.SAVEORUPDATEDEVICEPROPERTIESFAILED": return "新增或者修改设备属性失败";
                    case "IOT.DEVICE.SETDEVICEPROPERTYFAILED": return "设置设备属性失败";
                    case "IOT.DEVICE.TOOMANYDEVICEPROPERTIESPERTIME": return "新增的设备属性个数加上已有属性个数超过限定值";
                    case "IOT.DEVICE.TOPORELATIONCOUNTEXCEEDED": return "拓扑关系数量过多";
                    case "IOT.DEVICE.VERIFYDEVICEFAILED": return "验证设备失败";
                    default:
                        return "消息处理失败";
                }
            }

            /// <summary>
            ///  0-人脸识别开门,1-公共密码开门,2-一键开门,3-刷卡开门,4-人证开门，5-住户二维码开门,6-访客二维码开门，7-访客密码开门
            /// </summary>
            public static string RecogModeName(string mode)
            {
                switch (mode)
                {
                    case "0":
                        return "人脸识别开门";
                    case "1":
                        return "人证比对";
                    case "2":
                        return "人卡识别";
                    case "3":
                        return "一键开门";
                    case "4":
                        return "公共密码";
                    case "5":
                        return "住户二维码开门";
                    case "6":
                        return "访客二维码开门";
                    case "7":
                        return "访客密码开门";
                    case "8":
                        return "人证库识别";
                    case "9":
                        return "刷卡通行";
                    default:
                        return "其他方式";
                }
            }

            /// <summary>
            ///  识别分组,0-白名单，1-陌生人，10-访客
            /// </summary>
            public static string RecogRecordGroup(int? mode)
            {
                switch (mode)
                {
                    case 0:
                        return "白名单";
                    case 1:
                        return "陌生人";
                    case 10:
                        return "访客";
                    default:
                        return "其他";
                }
            }
        }

        /// <summary>
        /// 【，分割字符串处理】将两个字符串用,拼接
        /// </summary>
        /// <param name="oldStr">源字符串</param>
        /// <param name="str2">需拼接的字符串</param>
        /// <returns></returns>
        public static string JoinString(string oldStr, string str2)
        {
            oldStr = oldStr ?? "";
            str2 = str2 ?? "";
            oldStr = oldStr.Trim(',') + "," + str2.Trim(',');
            oldStr = Regex.Replace(oldStr, ",+", ",");

            string[] arr = oldStr.Split(',');
            oldStr = string.Join(",", arr.Distinct().ToArray()).Trim(',');
            return oldStr;
        }

        /// <summary>
        /// 【，分割字符串处理】从字符串中删除某一段字符串
        /// </summary>
        /// <param name="oldStr">源字符串</param>
        /// <param name="str2">需删除的字符串</param>
        /// <returns></returns>
        public static string RemoveString(string oldStr, string str2)
        {
            if (string.IsNullOrEmpty(oldStr)) return "";
            if (string.IsNullOrEmpty(str2)) return oldStr;

            oldStr = $",{oldStr.Trim(',')},";
            str2 = $",{str2.Trim(',')},";

            oldStr = oldStr.Replace(str2, ",");

            string[] arr = oldStr.Split(',');
            oldStr = string.Join(",", arr.Distinct().ToArray()).Trim(',');
            return oldStr;
        }

        /// <summary>
        /// 修改拼接的字符串
        /// </summary>
        /// <param name="isadd">true：添加，false：移除</param>
        /// <param name="oldString">原字符串</param>
        /// <param name="value">需要添加或移除的字符串</param>
        /// <param name="charor">拼接字符‘,’</param>
        /// <param name="charotr">首尾是否添加拼接字符</param>
        /// <returns></returns>
        public static string ModifyString(bool isadd, string oldString, string value, char character = ',', bool istail = false)
        {
            if (string.IsNullOrEmpty(oldString) || string.IsNullOrEmpty(value)) return oldString;
            List<string> strList = oldString.Split(character).ToList();
            strList = strList.Distinct().ToList(); //去重
            strList.Remove(""); //去空值
            if (isadd)
            {
                strList.Add(value);
                string resullString = string.Join(",", strList);
                return istail ? $"{character}{resullString}{character}" : resullString;
            }
            else
            {
                strList.Remove(value);
                if (strList == null || strList.Count == 0) return "";
                string resullString = string.Join(",", strList);
                return istail ? $"{character}{resullString}{character}" : resullString;
            }
        }

        /// <summary>
        /// 获得当前绝对路径
        /// </summary>
        /// <param name="strPath">指定的相对路径</param>
        /// <returns>绝对路径</returns>
        public static string GetMapPath(string strPath)
        {
            strPath = strPath.Replace("/", "\\");
            if (strPath.StartsWith("\\"))
            {
                strPath = strPath.TrimStart('\\');
            }

            return AppDomain.CurrentDomain.BaseDirectory + strPath;
        }


        public static bool IsParam(JObject obj, string key, bool allowEmpty = false)
        {
            if (!obj.ContainsKey(key))
                return false;
            if (!allowEmpty && string.IsNullOrEmpty(obj[key].ToString().Trim()))
                return false;
            return true;
        }

        public static bool IsParam(Dictionary<string, object> obj, string key, bool allowEmpty = false)
        {
            if (!obj.ContainsKey(key))
                return false;
            if (!allowEmpty && string.IsNullOrEmpty(obj[key].ToString()))
                return false;
            return true;
        }

        public static bool IsParam(string obj, string key, bool allowEmpty = false)
        {
            JObject item = TyziTools.Json.ToObject<JObject>(obj);
            return IsParam(item, key, allowEmpty);
        }

        /// <summary>
        /// 对url字符串中的中文参数进行编码
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string UrlStringEncode(string url)
        {
            Regex reg = new Regex(@"[\u4e00-\u9fa5]"); //汉字正则的表达式。

            if (reg.IsMatch(url))
            {
                int i = url.IndexOf("?");
                string fast = url.Substring(0, i + 1); //+1包含?字符
                string can = url.Substring(i + 1, url.Length - (i + 1));
                string last = "";

                string[] param = can.Split('&');
                if (param != null && param.Length > 0)
                {
                    foreach (var par in param)
                    {
                        if (par.Contains("="))
                        {
                            var p = par.Split('=');
                            if (p.Length > 0 && p.Length == 2)
                            {
                                if (reg.IsMatch(p[1]))
                                    last += $"&{p[0]}={HttpUtility.UrlEncode(p[1])}";
                                else
                                    last += $"&{par}";
                            }
                        }
                    }

                    url = fast + last.Trim('&');
                }
            }

            return url;
        }

        /// <summary>
        /// 将传入的字符串中间部分字符替换成特殊字符
        /// </summary>
        /// <param name="value">需要替换的字符串</param>
        /// <param name="startLen">前保留长度</param>
        /// <param name="endLen">尾保留长度</param>
        /// <param name="replaceChar">特殊字符</param>
        /// <returns>被特殊字符替换的字符串</returns>
        public static string ReplaceWithSpecialChar(string value, int startLen = 4, int endLen = 4, char specialChar = '*')
        {
            try
            {
                int lenth = value.Length - startLen - endLen; //中段长度

                string Part1 = value.Substring(0, startLen); //前段
                string Part2 = value.Substring(startLen, lenth); //中段
                string Part3 = value.Substring(lenth + startLen, endLen); //尾段

                string specialStr = string.Empty;

                for (int i = 0; i < Part2.Length; i++)
                {
                    specialStr += specialChar;
                }

                //value = value.Replace(Part2, specialStr);
                value = Part1 + specialStr + Part3;
            }
            catch
            {
                return value;
            }

            return value;
        }

        #region SQL字符串处理

        /// <summary>
        /// 检测是否有Sql危险字符
        /// </summary>
        /// <param name="str">要判断字符串</param>
        /// <returns>判断结果</returns>
        public static bool IsSafeSqlString(string str)
        {
            return !Regex.IsMatch(str, @"[-|;|,|\/|\(|\)|\[|\]|\}|\{|%|@|\*|!|\']");
        }

        /// <summary>
        /// 改正sql语句中的转义字符
        /// </summary>
        public static string mashSQL(string str)
        {
            return (str == null) ? "" : str.Replace("\'", "'");
        }

        /// <summary>
        /// 替换sql语句中的有问题符号
        /// </summary>
        public static string ChkSQL(string str)
        {
            return (str == null) ? "" : str.Replace("'", "''");
        }

        /// <summary>
        /// 清除sql语句中的危险符号
        /// </summary>
        public static string ClearRiskSQL(string str)
        {
            return string.IsNullOrEmpty(str) ? "" : str.Replace("'", "").Replace("--", "");
        }

        /// <summary>
        /// 清除sql语句中的危险符号
        /// </summary>
        public static Dictionary<string, object> ClearRiskSQL<T>(T t)
        {
            try
            {
                if (t == null) return null;
                var dict = JsonConvert.DeserializeObject<Dictionary<object, object>>(TyziTools.Json.ToString(t, true));
                Dictionary<string, object> dic = new Dictionary<string, object>();
                string name;
                object value;
                foreach (var item in dict)
                {
                    name = item.Key.ToString();
                    value = item.Value;
                    if (value != null)
                    {
                        dic.Add(name, ClearRiskSQL(value.ToString()));
                    }
                    else
                    {
                        dic.Add(name, value);
                    }
                }

                return dic;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 清除sql语句中的危险符号
        /// </summary>
        public static JObject ClearRiskSQL(JObject t)
        {
            try
            {
                if (t == null) return null;
                var dict = JsonConvert.DeserializeObject<Dictionary<object, object>>(TyziTools.Json.ToString(t, true));
                Dictionary<string, object> dic = new Dictionary<string, object>();
                string name;
                object value;
                foreach (var item in dict)
                {
                    name = item.Key.ToString();
                    value = item.Value;
                    if (value != null)
                    {
                        dic.Add(name, ClearRiskSQL(value.ToString()));
                    }
                    else
                    {
                        dic.Add(name, value);
                    }
                }

                return TyziTools.Json.ToObject<JObject>(TyziTools.Json.ToString(dic, true));
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 清除sql语句中的危险符号
        /// </summary>
        public static JObject ClearModelRiskSQL(string str)
        {
            try
            {
                if (string.IsNullOrEmpty(str)) return null;
                var dict = JsonConvert.DeserializeObject<Dictionary<object, object>>(str);
                Dictionary<string, object> dic = new Dictionary<string, object>();
                string name;
                object value;
                foreach (var item in dict)
                {
                    name = item.Key.ToString();
                    value = item.Value;
                    if (value != null)
                    {
                        dic.Add(name, ClearRiskSQL(value.ToString()));
                    }
                    else
                    {
                        dic.Add(name, value);
                    }
                }

                return TyziTools.Json.ToObject<JObject>(TyziTools.Json.ToString(dic, true));
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 清除sql语句中的危险符号
        /// </summary>
        public static T ClearModelRiskSQL<T>(string str, bool convertObj = false)
        {
            try
            {
                if (string.IsNullOrEmpty(str)) return default(T);
                var dict = JsonConvert.DeserializeObject<Dictionary<object, object>>(str);
                Dictionary<string, object> dic = new Dictionary<string, object>();
                string name;
                object value;
                foreach (var item in dict)
                {
                    name = item.Key.ToString();
                    value = item.Value;
                    if (value != null)
                    {
                        //把值转换成字符串，再清除危险字符
                        var valueStr = value.ToString(); //ClearRiskSQL(value.ToString());

                        if (convertObj && (IsValidJson(valueStr) || valueStr == "[]"))
                        {
                            value = item.Value;
                        }
                        else
                        {
                            value = valueStr;
                        }

                        dic.Add(name, value);
                    }
                    else
                    {
                        dic.Add(name, value);
                    }
                }

                return TyziTools.Json.ToObject<T>(TyziTools.Json.ToString(dic, true));
            }
            catch
            {
                return default(T);
            }
        }

        /// <summary>
        /// 去除合法 HTML 标签，保留非标准标签字符（如 <imgxss）
        /// </summary>
        public static string StripHtmlSmart(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            // 匹配合法 HTML 标签（形如 <tag>、</tag>、<tag ...>、<tag/>）
            string pattern = @"</?(?!\d)[a-zA-Z][a-zA-Z0-9]*(\s[^<>]*)?/?>";

            // 替换合法标签为空
            string output = Regex.Replace(input, pattern, string.Empty, RegexOptions.IgnoreCase);

            // HTML 解码实体，如 &nbsp; -> 空格
            return WebUtility.HtmlDecode(output).Trim();
        }

        /// <summary>
        /// 判断字符串是否一个有效的 JSON 字符串
        /// </summary>
        /// <param name="strInput"></param>
        /// <returns></returns>
        public static bool IsValidJson(string strInput)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(strInput)) return false;
                JToken.Parse(strInput);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public static List<T> ClearListModelRiskSQL<T>(string str)
        {
            try
            {
                if (string.IsNullOrEmpty(str) || str == "[]" || str == "null") return default(List<T>);
                var dicts = JsonConvert.DeserializeObject<List<Dictionary<object, object>>>(str);
                List<Dictionary<string, object>> newDicts = new List<Dictionary<string, object>>();
                string name;
                object value;
                foreach (var dict in dicts)
                {
                    Dictionary<string, object> dic = new Dictionary<string, object>();
                    foreach (var item in dict)
                    {
                        name = item.Key.ToString();
                        value = item.Value;
                        if (value != null)
                        {
                            dic.Add(name, ClearRiskSQL(value.ToString()));
                        }
                        else
                        {
                            dic.Add(name, value);
                        }
                    }

                    newDicts.Add(dic);
                }

                return TyziTools.Json.ToObject<List<T>>(TyziTools.Json.ToString(newDicts, true));
            }
            catch
            {
                return default(List<T>);
            }
        }

        #endregion

        /// <summary>
        /// 获取服务安装路径
        /// </summary>
        /// <param name="ServiceName"></param>
        /// <returns></returns>
        public static string GetWindowsServiceInstallPath(string ServiceName)
        {
            string key = @"SYSTEM\CurrentControlSet\Services\" + ServiceName;
            string path = Registry.LocalMachine.OpenSubKey(key).GetValue("ImagePath").ToString();
            //替换掉双引号   
            path = path.Replace("\"", string.Empty);

            FileInfo fi = new FileInfo(path);
            return fi.Directory.ToString();
        }

        /// <summary>
        /// 判断系统是否64位
        /// </summary>
        /// <returns></returns>
        public static bool IsSystem64() => Environment.Is64BitOperatingSystem;

        /// <summary>
        /// 检测端口是否可用
        /// </summary>
        /// <param name="iPort">端口号</param>
        /// <returns>true:端口被占用
        ///         false:端口可用
        /// </returns>
        public static bool CheckPort(int iPort)
        {
            bool result = false;
            try
            {
                Process p = new Process();
                p.StartInfo = new ProcessStartInfo("netstat", "-an");
                p.StartInfo.CreateNoWindow = true;
                p.StartInfo.UseShellExecute = false;
                p.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                p.StartInfo.RedirectStandardOutput = true;
                p.Start();
                String output = p.StandardOutput.ReadToEnd().ToLower();
                string ip1 = "127.0.0.1";
                string ip2 = "0.0.0.0";
                System.Net.IPAddress[] addressList = Dns.GetHostEntry(Dns.GetHostName()).AddressList;
                List<string> ipList = new List<string>();
                ipList.Add(ip1);
                ipList.Add(ip2);
                for (int i = 0; i < addressList.Length; i++)
                {
                    ipList.Add(addressList[i].ToString());
                }

                for (int i = 0; i < ipList.Count; i++)
                {
                    if (output.IndexOf(ipList[i] + ":" + iPort.ToString()) >= 0)
                    {
                        result = true;
                        break;
                    }
                }
            }
            catch (Exception)
            {
            }

            return result;
        }

        /// <summary>
        /// 判断是否系统管理员Administrator登录的Windows
        /// </summary>
        /// <returns></returns>
        public static bool IsAdministrator()
        {
            WindowsIdentity identity = WindowsIdentity.GetCurrent();
            WindowsPrincipal principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        /// <summary>
        /// 按位处理字符串匹配度
        /// </summary>
        /// <param name="s1">字符串1</param>
        /// <param name="s2">字符串2</param>
        /// <returns></returns>
        public static int StringMatch(string s1, string s2)
        {
            int iMaxLen = Math.Max(s1.Length, s2.Length);
            char[] sc1 = s1.ToArray();
            char[] sc2 = s2.ToArray();
            int iDiff = 0;
            for (int i = 0; i < iMaxLen; i++)
            {
                char op1 = default;
                char op2 = default;
                if (i < sc1.Length)
                {
                    op1 = sc1[i];
                }

                if (i < sc2.Length)
                {
                    op2 = sc2[i];
                }

                if (op1 != op2)
                {
                    iDiff++;
                }
            }

            return iDiff;
        }


        /// <summary>
        /// 将Dictionary转化为字典序的QueryString,字符串最后会多一个&   key=value&....
        /// </summary>
        /// <param name="item"></param>
        /// <param name="notNull">是否包含null值---true:不能包含null值，false:需要包含null</param>
        /// <returns></returns>
        public static string ToQuery(Dictionary<string, object> item, bool isNull)
        {
            string result = string.Empty;
            ArrayList akeys = new ArrayList(item.Keys);
            akeys.Sort(); //按字母顺序进行排序
            foreach (string skey in akeys)
            {
                if (skey.ToLower().Equals("key") || skey.ToLower().Equals("sign")) continue;
                if (isNull)
                {
                    if (item[skey] != null || !string.IsNullOrEmpty(item[skey].ToString()))
                        result += string.Format("{0}={1}&", skey, item[skey]);
                }
                else
                {
                    result += string.Format("{0}={1}&", skey, item[skey]);
                }
            }

            return result;
        }

        /// <summary>
        /// 克隆对象
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="t"></param>
        /// <returns></returns>
        public static T DeepCloneObject<T>(T t)
        {
            T model = System.Activator.CreateInstance<T>(); //实例化一个T类型对象
            PropertyInfo[] propertyInfos = model.GetType().GetProperties(); //获取T对象的所有公共属性
            foreach (PropertyInfo propertyInfo in propertyInfos)
            {
                //判断值是否为空，如果空赋值为null见else
                if (propertyInfo.PropertyType.IsGenericType && propertyInfo.PropertyType.GetGenericTypeDefinition().Equals(typeof(Nullable<>)))
                {
                    //如果convertsionType为nullable类，声明一个NullableConverter类，该类提供从Nullable类到基础基元类型的转换
                    NullableConverter nullableConverter = new NullableConverter(propertyInfo.PropertyType);
                    //将convertsionType转换为nullable对的基础基元类型
                    propertyInfo.SetValue(model, (propertyInfo.GetValue(t) == null) ? null : Convert.ChangeType(propertyInfo.GetValue(t), nullableConverter.UnderlyingType ?? propertyInfo.PropertyType), null);
                }
                else
                {
                    propertyInfo.SetValue(model, (propertyInfo.GetValue(t) == null) ? null : Convert.ChangeType(propertyInfo.GetValue(t), propertyInfo.PropertyType), null);
                }
            }

            return model;
        }

        /// <summary>
        /// 克隆对象列表
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="tList"></param>
        /// <returns></returns>
        public static IList<T> DeepCloneList<T>(IList<T> tList)
        {
            IList<T> listNew = new List<T>();
            foreach (var item in tList)
            {
                T model = System.Activator.CreateInstance<T>(); //实例化一个T类型对象
                PropertyInfo[] propertyInfos = model.GetType().GetProperties(); //获取T对象的所有公共属性
                foreach (PropertyInfo propertyInfo in propertyInfos)
                {
                    //判断值是否为空，如果空赋值为null见else
                    if (propertyInfo.PropertyType.IsGenericType && propertyInfo.PropertyType.GetGenericTypeDefinition().Equals(typeof(Nullable<>)))
                    {
                        //如果convertsionType为nullable类，声明一个NullableConverter类，该类提供从Nullable类到基础基元类型的转换
                        NullableConverter nullableConverter = new NullableConverter(propertyInfo.PropertyType);
                        //将convertsionType转换为nullable对的基础基元类型 
                        propertyInfo.SetValue(model, (propertyInfo.GetValue(item) == null) ? null : Convert.ChangeType(propertyInfo.GetValue(item), nullableConverter.UnderlyingType ?? propertyInfo.PropertyType), null);
                    }
                    else
                    {
                        propertyInfo.SetValue(model, (propertyInfo.GetValue(item) == null) ? null : Convert.ChangeType(propertyInfo.GetValue(item), propertyInfo.PropertyType), null);
                    }
                }

                listNew.Add(model);
            }

            return listNew;
        }

        /// <summary>
        /// 获取星期名称
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="N">前缀,默认周</param>
        /// <returns></returns>
        public static string GetWeekName(DateTime dt, string N = null)
        {
            if (string.IsNullOrEmpty(N))
                N = "周";

            string M = "";
            switch (dt.DayOfWeek)
            {
                case DayOfWeek.Friday:
                    M = "五";
                    break;
                case DayOfWeek.Monday:
                    M = "一";
                    break;
                case DayOfWeek.Saturday:
                    M = "六";
                    break;
                case DayOfWeek.Sunday:
                    M = "日";
                    break;
                case DayOfWeek.Thursday:
                    M = "四";
                    break;
                case DayOfWeek.Tuesday:
                    M = "二";
                    break;
                case DayOfWeek.Wednesday:
                    M = "三";
                    break;
                default:
                    break;
            }

            return $"{N}{M}";
        }

        /// <summary>
        /// 截取字符串
        /// </summary>
        /// <param name="content">字符串</param>
        /// <param name="beginChat">开始字符</param>
        /// <param name="endChat">结束字符</param>
        /// <returns></returns>
        public static string GetTargetString(string content, string beginChat, string endChat)
        {
            if (string.IsNullOrWhiteSpace(content) || string.IsNullOrWhiteSpace(beginChat) || string.IsNullOrWhiteSpace(beginChat)) return content;

            // 找到"picture/"的索引
            int startIndex = content.IndexOf(beginChat);

            if (startIndex >= 0)
            {
                // 截取从"picture/"开始到字符串的末尾
                string result = content.Substring(startIndex);

                // 去除可能存在的查询参数
                int questionMarkIndex = result.IndexOf(endChat);
                if (questionMarkIndex >= 0)
                {
                    result = result.Substring(0, questionMarkIndex);
                }

                return result;
            }

            return content; // 如果未找到目标字符串，可以根据需要返回适当的值
        }

        /// <summary>
        ///  Levenshtein 距离算法来确定字符之间的相似性
        /// </summary>
        /// <param name="s">字符A</param>
        /// <param name="t">字符B</param>
        /// <returns>两个字符间的字符差异数</returns>
        public static int CalculateLevenshteinDistance(string s, string t)
        {
            int n = s.Length;
            int m = t.Length;
            int[,] d = new int[n + 1, m + 1];

            if (n == 0)
                return m;
            if (m == 0)
                return n;

            for (int i = 0; i <= n; d[i, 0] = i++)
            {
            }

            for (int j = 0; j <= m; d[0, j] = j++)
            {
            }

            for (int i = 1; i <= n; i++)
            {
                for (int j = 1; j <= m; j++)
                {
                    int cost = (t[j - 1] == s[i - 1]) ? 0 : 1;
                    d[i, j] = Math.Min(Math.Min(d[i - 1, j] + 1, d[i, j - 1] + 1), d[i - 1, j - 1] + cost);
                }
            }

            return d[n, m];
        }

        /// <summary>
        /// 获取实体的属性值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string GetEntityCode<T>(T entity, string key = "_No")
        {
            // 构造编码
            string code = typeof(T).Name + key;

            // 使用反射获取实体的编号属性值
            PropertyInfo property = typeof(T).GetProperty(code);

            if (property != null)
            {
                string value = property.GetValue(entity)?.ToString();

                if (!string.IsNullOrEmpty(value))
                {
                    return value;
                }
            }

            return null; // 如果获取属性值失败或属性值为空，则返回 null 或其他适当的值
        }

        /// <summary>
        /// 比较两个对象的值是否相同
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj1"></param>
        /// <param name="obj2"></param>
        /// <returns></returns>
        public static bool ArePropertiesEqual<T>(T obj1, T obj2)
        {
            Type type = typeof(T);
            PropertyInfo[] properties = type.GetProperties();

            foreach (PropertyInfo property in properties)
            {
                object value1 = property.GetValue(obj1, null);
                object value2 = property.GetValue(obj2, null);

                if (!object.Equals(value1, value2))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 将一个对象的属性值复制到另一个对象的相应属性(假定两个对象的类型相同或兼容，且它们的属性名称相同)
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destination"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public static void CopyProperties(object source, object destination)
        {
            if (source == null || destination == null)
            {
                return;
            }

            Type sourceType = source.GetType();
            Type destinationType = destination.GetType();

            PropertyInfo[] sourceProperties = sourceType.GetProperties();
            PropertyInfo[] destinationProperties = destinationType.GetProperties();

            foreach (PropertyInfo sourceProperty in sourceProperties)
            {
                PropertyInfo destinationProperty = Array.Find(destinationProperties, prop => prop.Name == sourceProperty.Name);

                if (destinationProperty != null && destinationProperty.PropertyType == sourceProperty.PropertyType && destinationProperty.CanWrite)
                {
                    object value = sourceProperty.GetValue(source);
                    destinationProperty.SetValue(destination, value);
                }
            }
        }

        /// <summary>
        /// 增加月(月租车充值)
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        public static DateTime AddMonths(DateTime date, int monthsToAdd)
        {
            if (monthsToAdd <= 0) return date;

            for (var i = 0; i < monthsToAdd; i++)
            {
                date = AddOneMonth(date);
            }

            return date;
        }

        public static DateTime AddOneMonth(DateTime date)
        {
            // 获取当前日期的年份、月份和日期
            int year = date.Year;
            int month = date.Month;
            int day = date.Day;
            DateTime currentDate = new DateTime(year, month, day);

            // 计算下一个月的年份和月份
            int nextMonthYear = year;
            int nextMonth = month + 1;

            if (nextMonth > 12)
            {
                // 如果是12月，年份增加1，月份重置为1月（0）
                nextMonthYear++;
                nextMonth = 1; // 1表示一月
            }

            // 获取下一个月的最后一天
            int lastDayOfMonth = DateTime.DaysInMonth(nextMonthYear, nextMonth);

            currentDate = currentDate.AddDays(1);

            if (currentDate.ToString("yyyy-MM-dd") == new DateTime(nextMonthYear, nextMonth, 1).ToString("yyyy-MM-dd"))
            {
                return new DateTime(nextMonthYear, nextMonth, lastDayOfMonth);
            }

            // 如果当前日期的天数大于下一个月的最后一天，将其设置为下一个月的最后一天
            day = Math.Min(day, lastDayOfMonth);

            // 创建新日期对象
            DateTime newDate = new DateTime(nextMonthYear, nextMonth, day);

            return newDate;
        }

        /// <summary>
        /// 只允许中文字母数字还有,，.。:：，\-/*+,其它都要替换成空字符串
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string RemoveInvalidStr(ref string str)
        {
            var pattern = @"[^(\u4e00-\u9fa5a-zA-Z0-9,，、.。:：一-龥\-/*+)]";
            var sanitizedInput = Regex.Replace(str, pattern, "");

            // 检查是否包含"--"
            sanitizedInput = sanitizedInput.Replace("--", "");
            return sanitizedInput;

            //// 正则表达式匹配中文字符、字母、数字、逗号、分号和句号
            //var regex = new Regex("[^a-zA-Z0-9,，.。:：一-龥]");
            //// 使用空字符串替换不匹配的字符
            //str = regex.Replace(str, "");
            //return str;
        }

        // 验证 IP 地址是否合法
        public static bool IsValidIpAddress(string ipAddress)
        {
            if (ipAddress == null || ipAddress == string.Empty)
                return false;

            string[] ipParts = ipAddress.Split('.');
            if (ipParts.Length != 4)
                return false;

            byte ipPart;
            return ipParts.All(part => byte.TryParse(part, out ipPart));
        }

        /// <summary>
        /// 直接从字符串里获取属性值
        /// </summary>
        /// <param name="jsonString">json字符串</param>
        /// <param name="fieldName">属性名</param>
        /// <returns></returns>
        public static bool GetFieldValue(string jsonString, string fieldName, out string value)
        {
            value = "";
            try
            {
                if (string.IsNullOrEmpty(jsonString)) return false;

                // 查找字段名的位置
                int startIndex = jsonString.IndexOf($"\"{fieldName.Trim()}\"");
                if (startIndex == -1)
                {
                    return false;
                }

                // 从字段名的位置向后查找值的位置
                startIndex = jsonString.IndexOf(':', startIndex);
                if (startIndex == -1)
                {
                    return false;
                }

                // 定位到值的起始位置
                int valueStartIndex = jsonString.IndexOfAny(new[] { '"', '-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' }, startIndex + 1);
                if (valueStartIndex == -1)
                {
                    return false;
                }

                // 定位到值的结束位置
                int valueEndIndex;
                char valueStartChar = jsonString[valueStartIndex];
                if (valueStartChar == '"')
                {
                    // 如果值的起始字符是双引号，则值是字符串类型
                    valueEndIndex = jsonString.IndexOf('"', valueStartIndex + 1);
                }
                else if (char.IsDigit(valueStartChar) || valueStartChar == '-')
                {
                    // 如果值的起始字符是数字或负号，则值是数字类型
                    valueEndIndex = jsonString.IndexOfAny(new[] { ',', '}', ' ' }, valueStartIndex + 1);
                }
                else
                {
                    // 其他情况下，返回空字符串
                    return false;
                }

                if (valueEndIndex == -1)
                {
                    return false;
                }

                string fieldValue = jsonString.Substring(valueStartIndex, valueEndIndex - valueStartIndex);
                value = fieldValue.Replace("\"", "");
                return true;
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "Utils GetFieldValue异常：" + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 直接从字符串里获取属性值
        /// </summary>
        /// <param name="jsonString">json字符串</param>
        /// <param name="fieldName">属性名</param>
        /// <returns></returns>
        public static string GetFieldValue(string jsonString, string fieldName)
        {
            try
            {

                if (string.IsNullOrEmpty(jsonString)) return "";

                // 查找字段名的位置
                int startIndex = jsonString.IndexOf($"\"{fieldName.Trim()}\"");
                if (startIndex == -1)
                {
                    return "";
                }

                // 从字段名的位置向后查找值的位置
                startIndex = jsonString.IndexOf(':', startIndex);
                if (startIndex == -1)
                {
                    return "";
                }

                // 定位到值的起始位置
                int valueStartIndex = jsonString.IndexOfAny(new[] { '"', '-', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' }, startIndex + 1);
                if (valueStartIndex == -1)
                {
                    return "";
                }

                // 定位到值的结束位置
                int valueEndIndex;
                char valueStartChar = jsonString[valueStartIndex];
                if (valueStartChar == '"')
                {
                    // 如果值的起始字符是双引号，则值是字符串类型
                    valueEndIndex = jsonString.IndexOf('"', valueStartIndex + 1);
                }
                else if (char.IsDigit(valueStartChar) || valueStartChar == '-')
                {
                    // 如果值的起始字符是数字或负号，则值是数字类型
                    valueEndIndex = jsonString.IndexOfAny(new[] { ',', '}', ' ' }, valueStartIndex + 1);
                }
                else
                {
                    // 其他情况下，返回空字符串
                    return "";
                }

                if (valueEndIndex == -1)
                {
                    return "";
                }

                // 截取字段值并返回，同时替换转义字符
                string fieldValue = jsonString.Substring(valueStartIndex, valueEndIndex - valueStartIndex);
                return fieldValue.Replace("\"", "");
            }
            catch (Exception ex)
            {
                LogManagementMap.WriteToFile(LoggerEnum.MainErrorLog, "Utils GetFieldValue异常：" + ex.Message);
                return "";
            }
        }

        /// <summary>
        /// t16机号（1&2&5&6）转线上机号ffffffff,翻译出错或者为空都返回00000000
        /// </summary>
        /// <param name="t16machineno"></param>
        /// <returns></returns>
        public static string MachineNoConvert(string t16machineno)
        {
            if (string.IsNullOrEmpty(t16machineno)) return "ffffffff";

            string mno = "00000000";
            try
            {
                if (!string.IsNullOrEmpty(t16machineno))
                {
                    if (t16machineno == "&&&&&")
                    {
                        return "ffffffff";
                    }
                    else
                    {
                        string str = "00000000000000000000000000000000"; //32个0
                        List<char> strl = str.ToList();
                        var mArry = t16machineno.Trim('&').Split('&');
                        foreach (var i in mArry)
                        {
                            int index = Convert.ToInt32(i);
                            if (index <= strl.Count)
                            {
                                strl[index - 1] = '1';
                            }
                        }
                        str = string.Join("", strl);
                        mno = "";
                        for (int i = 0; i < 8; i++)
                        {
                            mno += hex_to_bin(str.Substring(i * 4, 4));
                        }
                        return mno;
                    }
                }
            }
            catch (Exception)
            {
                return "00000000"; //报错返回
            }
            return mno;
        }

        public static string hex_to_bin(string str)
        {
            Dictionary<string, string> dic = new Dictionary<string, string> {
                { "0000","0" },{ "0001", "1" },{ "0010", "2" },{ "0011", "3" },
                { "0100","4" },{ "0101", "5" },{ "0110", "6" },{ "0111", "7" },
                { "1000","8" },{ "1001", "9" },{ "1010", "a" },{ "1011", "b" },
                { "1100","c" },{ "1101", "d" },{ "1110", "e" },{ "1111", "f" },
            };
            return dic[str];
        }

        public static bool IsStrongPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password) || password.Length < 6) { return false; }

            // 正则表达式检测不同类型的字符
            bool hasLetter = Regex.IsMatch(password, "[a-zA-Z]"); // 检测是否包含字母
            bool hasNumber = Regex.IsMatch(password, "[0-9]"); // 检测是否包含数字
            bool hasSpecialChar = Regex.IsMatch(password, @"[!@#$^,._*+%\-&;:?]"); // 检测是否包含特殊字符

            // 判断是否满足三种字符类型的组合
            return hasLetter && hasNumber && hasSpecialChar;
        }

        // 危险字符
        private static string[] blackList = {
            // 基础 SQL 关键字
            "alter", "begin", "cast", "create", "cursor", "declare", "delete", "drop",
            "end", "exec", "execute", "fetch", "insert", "kill", "merge",
            "open", "select", "sys", "sysobjects", "syscolumns", "table",
            "truncate", "update", "use", "waitfor", "shutdown",
    
            // SQL 注入攻击常用语法
            "union", "having", "or", "and", "like", "regexp", "exists", "case", "sleep", 

            // SQL 服务器特有的危险函数
            "information_schema", "xp_", "sp_", "sys.sp_", "sys.fn_", "@@", "@",

            // 注释绕过攻击
            "--", ";", "/*", "*/", "0x", "' or '1'='1", "\" or \"1\"=\"1",

            // 可能用于 SQL 注入的字符串操作
            "char", "nchar", "varchar", "nvarchar",

            // 特殊绕过字符
            "#", "--+", "' or '1'='1", "\" or \"1\"=\"1", "' or 'x'='x", "\" or \"x\"=\"x"
        };

        /// <summary>
        /// 过滤 where 子句中的危险 SQL 语法
        /// </summary>
        /// <param name="whereClause"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public static string SanitizeWhereClause(string whereClause)
        {
            if (string.IsNullOrWhiteSpace(whereClause))
                return string.Empty;

            // 转换为小写，防止大小写混淆
            string sanitized = whereClause.ToLower();

            foreach (string word in blackList)
            {
                if (sanitized.Contains(word))
                {
                    sanitized = sanitized.Replace(word, "");
                }
            }

            return sanitized;
        }

        /// <summary>
        /// 最后一个字符是字母
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsLastCharLetter(string str)
        {
            return Regex.IsMatch(str, "[a-zA-Z]$");
        }

        /// <summary>
        /// 是否带有特殊字符
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsSpecialChar(string str)
        {
            return Regex.IsMatch(str, @"[!@#$^,._*+%\-&;:?]");
        }


        public static bool IsOnlyExcelFiles(IFormFileCollection formFiles)
        {
            if (formFiles == null || formFiles.Count == 0)
                return false;

            // 支持的 Excel MIME 类型
            var allowedContentTypes = new[]
            {
        "application/vnd.ms-excel",                      // .xls
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" // .xlsx
    };

            // 支持的扩展名（用于进一步校验）
            var allowedExtensions = new[] { ".xls", ".xlsx" };

            foreach (var file in formFiles)
            {
                // 1. 检查 MIME 类型
                if (!allowedContentTypes.Contains(file.ContentType))
                    return false;

                // 2. 检查扩展名
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(extension))
                    return false;
            }

            return true;
        }

        public static bool IsOnlyTextFiles(IFormFileCollection formFiles)
        {
            if (formFiles == null || formFiles.Count == 0)
                return false;

            // 允许的 MIME 类型
            var allowedContentTypes = new[]
            {
        "text/plain" // .txt 文件的标准 MIME 类型
    };

            // 允许的扩展名
            var allowedExtensions = new[] { ".txt" };

            foreach (var file in formFiles)
            {
                // 检查 MIME 类型
                if (!allowedContentTypes.Contains(file.ContentType))
                    return false;

                // 检查扩展名
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(extension))
                    return false;
            }

            return true;
        }

        public static bool IsOnlyVideoFiles(IFormFileCollection formFiles)
        {
            if (formFiles == null || formFiles.Count == 0)
                return false;

            // 常见视频文件的 MIME 类型
            var allowedContentTypes = new[]
            {
        "video/mp4",
        "video/x-msvideo",          // .avi
        "video/quicktime",          // .mov
        "video/x-matroska",         // .mkv
        "video/webm",
        "video/3gpp",
        "video/3gpp2"
    };

            // 允许的扩展名（小写）
            var allowedExtensions = new[]
            {
        ".mp4", ".avi", ".mov", ".mkv", ".webm", ".3gp", ".3g2"
    };

            foreach (var file in formFiles)
            {
                // 检查 MIME 类型
                if (!allowedContentTypes.Contains(file.ContentType))
                    return false;

                // 检查扩展名
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(extension))
                    return false;
            }

            return true;
        }

        public static bool IsOnlyImageFiles(IFormFileCollection formFiles)
        {
            if (formFiles == null || formFiles.Count == 0)
                return false;

            // 常见图片的 MIME 类型
            var allowedContentTypes = new[]
            {
        "image/jpeg",   // .jpg, .jpeg
        "image/png",    // .png
        "image/gif",    // .gif
        "image/bmp",    // .bmp
        "image/webp",   // .webp
        "image/x-icon", // .ico
        "image/svg+xml" // .svg
    };

            // 允许的扩展名
            var allowedExtensions = new[]
            {
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".ico", ".svg"
    };

            foreach (var file in formFiles)
            {
                // 检查 MIME 类型
                if (!allowedContentTypes.Contains(file.ContentType))
                    return false;

                // 检查文件扩展名
                var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(extension))
                    return false;
            }

            return true;
        }


    }


}