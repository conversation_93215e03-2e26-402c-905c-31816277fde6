﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接收详情</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/css/style.min862f.css?v=4.1.0" rel="stylesheet">
    <link href="~/Static/css/myApp.css" rel="stylesheet" />
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <link href="~/Static/js/plugins/layer/skin/layer.css" rel="stylesheet" />
    <link href="~/Static/css/fishBone.css?v1.0" rel="stylesheet" />
    <link href="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.css" rel="stylesheet">
    <style>
        h3, span { color: black; font-weight: normal !important; }
        .ibox-content { padding-top: 0px !important; }
        body { margin-top: 0; }
        .fishBone { border: 1px solid #f5f5f5; }
        .form-group { margin-bottom: 10px; }
        .gray-bg { background-color: #fdf8f8; margin-top: 5px; }
        h3 { padding-top: 5px; }
        pre { padding: 10px; background-color: #f5f5f5; border-radius: 5px; border: 1px solid #ccc; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;max-height:300px;}
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="form-horizontal">
            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>数据信息</h3>
                </div>
            </div>
            <div class="form-group center-block">
                <div class="row col-sm-12">
                    <div class="col-sm-3">
                        <label class="control-label">数据类型：<span id="ReceiveHandle_DataType"></span></label>
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label">数据描述：<span id="ReceiveHandle_Desc"></span></label>
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label">数据状态：<span id="ReceiveHandle_Status"></span></label>
                    </div>
                </div>
                <div class="row col-sm-12">
                    <div class="col-sm-3">
                        <label class="control-label">创建时间：<span id="ReceiveHandle_Time"></span></label>
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label">分发岗亭编码：<span id="ReceiveHandle_SentryNo"></span></label>
                    </div>
                    <div class="col-sm-3">
                        <label class="control-label">数据编码：<span id="ReceiveHandle_ReceiveDataNo"></span></label>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="col-sm-12 gray-bg">
                    <h3>接收协议详情</h3>
                </div>
            </div>
            <div class="form-group center-block">
                <div class="row col-sm-12">
                    <div class="col-sm-12">
                        <div class="layui-content" style="padding-right:5px;">
                            <div class="layui-row">
                                <pre id="ReceiveData_Content"></pre>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
          
        </div>
    </div>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.min.js?v=2.1.4" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js?v=3.3.6" asp-append-version="true"></script>
    <script src="~/Static/js/content.min.js?v=1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/layer/layer.js?v=5" asp-append-version="true"></script>
    <script src="~/Static/js/fishBone.js?v1.0.0" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.SuperSlide.2.1.1.js" asp-append-version="true"></script>
    <script src="~/Static/js/plugins/fancybox3/dist/jquery.fancybox.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script>

        var dataTypedic = { 1: "车辆登记", 2: "黑名单管理", 3: "访客车辆", 4: "商家车辆", 5: "向导设置", 6: "场区设置", 7: "岗亭管理", 8: "车道管理", 9: "设备管理", 10: "车场设置", 11: "车道监控", 12: "计费规则", 13: "充值规则", 14: "通行控制", 15: "车牌类型", 16: "车牌颜色", 17: "尾号限行", 18: "特殊车辆", 19: "日期设置", 20: "城市平台上报", 21: "优惠设置", 22: "车牌优惠", 23: "出入场记录", 24: "缴费记录", 25: "缴费明细", 26: "优惠券记录", 27: "场内记录", 28: "车牌识别记录", 29: "人工开闸记录", 30: "开闸放行记录", 31: "交班记录", 32: "倒车记录", 33: "事件管理", 34: "健康码记录", 35: "车辆注销记录", 36: "白名单记录", 37: "账号管理", 38: "权限管理", 39: "系统设置", 40: "支付方式", 41: "支付费用", 42: "车辆入场", 43: "车辆出场",44: "异常放行",45: "无入场记录出场",46: "自助现金预交记录",48: "预入场",47: "车场信息", 999: "未知" };
        var dataStatusdic = { 0: "未处理", 1: "已处理" }
        var data3 = null;
        var ReceiveHandle_ID = decodeURIComponent($.getUrlParam("ReceiveHandle_ID"));
        var pager = {
            init: function () {
                this.bindData();
            },
            bindEvent: function () {
                //重传
                $("#data-view-sendSentrys").on('click', "button[name='send']", function () {
                    var dataNo = $(this).attr("data-datano");
                    var sentryNo = $(this).attr("data-sentryno");
                    layer.msg('处理中...', { icon: 16, time: 0 });
                    $.ajax({
                        type: 'post',
                        url: '/ReceiveHandle/PullSentryData',
                        dataType: 'json',
                        data: { SendSentry_SendDataNo: dataNo, SendSentry_SentryNo: sentryNo },
                        success: function (json) {
                            if (json.success) {
                                layer.msg("重传成功", { icon: 1, time: 1500 }, function () { pager.bindData(1); });
                            } else {
                                layer.msg('' + json.msg, { icon: 5 });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                 
                });
            },

            //数据绑定
            bindData: function () {
                layer.msg('加载中...', { icon: 16, time: 0 });

                if (ReceiveHandle_ID != null) {
                    $.ajax({
                        type: 'post',
                        url: '/ReceiveHandle/GetResult',
                        dataType: 'json',
                        data: { ReceiveHandle_ID: ReceiveHandle_ID },
                        success: function (json) {
                            if (json.success) {
                                //接收信息
                                if (json.data != null && json.data.model != null) {
                                    $("#ReceiveHandle_DataType").text(dataTypedic[json.data.model.ReceiveHandle_DataType]);
                                    $("#ReceiveHandle_Desc").text(json.data.model.ReceiveHandle_Desc);
                                    $("#ReceiveHandle_Status").text(dataStatusdic[json.data.model.ReceiveHandle_Status]);
                                    $("#ReceiveHandle_Time").text(json.data.model.ReceiveHandle_Time);
                                    $("#ReceiveHandle_SentryNo").text(json.data.model.ReceiveHandle_SentryNo);
                                    $("#ReceiveHandle_ReceiveDataNo").text(json.data.model.ReceiveHandle_ReceiveDataNo);
                                }

                                //分发信息
                                if (json.data != null && json.data.ReceiveData_Content != null) {
                                    $("#ReceiveData_Content").text(json.data.ReceiveData_Content);
                                }

                                $(".fishBone").html("");

                                pager.bindEvent();

                                layer.closeAll();
                            } else {
                                layer.msg('加载失败：' + json.msg, { icon: 5 });
                            }
                        },
                        error: function () {
                            layer.msg('系统错误', { icon: 2 });
                        }
                    });
                } else {
                    layer.msg('参数无效', { icon: 0 });
                }
            }
        };

        $(function () { pager.init() });
       
    </script>
</body>
</html>
