﻿<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>快速配置</title>
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css?v3" rel="stylesheet" />
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <style>
        html,
        body { width: 100%; height: 100%; margin: 0; padding: 0; min-width: 583px !important; background-color: #71bcff00; }
        .layui-tab { margin: 0px !important; }
        .layui-tab-title { user-select: none; }
        .box-table,
        .box-bottom { margin: 0 15px; overflow: auto; }
        .box-bottom { text-align: left; padding: 20px 0; }
        .layui-table,
        .layui-table-view { margin: 0 !important; }
        .layui-tab-content { margin: 0; padding: 0 !important; }
        .layui-table-cell { overflow: visible !important; }
        .tdSelect { width: 100%; max-width: 200px; height: 30px; border: 1px solid #d9d9d9; border-radius: 3px; color: #0094ff; font-size: 13px; }
        .layui-elem-quote { border-left: 0 !important; }
        .layui-nav-item t { color: #000 !important; padding: 0 20px; user-select: none; }
        .layui-this t { color: #5FB878 !important; }

        .divTop { margin: 0; padding: 0; width: 99%; height: 5%; /* border-bottom: 1px solid #ccc; */ text-align: left; line-height: 2.2rem; background-color: #04345fdb; color: #fff; }
        .divContent { margin: 0; padding: 0 0 4rem 0; width: 100%; display: flex; overflow: auto; }
        .divContentItem { height: 100%; }
        .divBottom { text-align: center; margin: 0; padding: 0; width: 100%; height: 3rem; background-color: #edf1f1; color: #fff; font-size: 1rem; line-height: 3rem; position: fixed; bottom: 0; }

        .divLeft { width: 18%; background-color: #ccc; display: flex; flex-direction: column; border: 1px solid #fff; border-left: 2px solid #04345fdb; min-width: 120px; }
        .leftItem { width: 100%; }
        .leftItem1 { background-color: #fff; flex: 3; }
        .leftItem2 { background-color: #fff; flex: 4; display: flex; flex-direction: column; align-items: center; justify-content: center; border-top: 1px solid #ccc; }
        .leftItem2 .divBtn { width: 100%; text-align: center; margin-top: 10px; }
        .leftItem2 .divBtn button { width: 80%; /* 设置按钮宽度 */ }
        .leftItem3 { background-color: #fff; flex: 3; display: flex; flex-direction: column; align-items: center; justify-content: center; border-top: 1px solid #ccc; }
        .leftItem3 b { color: #009688; font-size: 1.1rem; }
        .leftItem3 pre { color: #009688; margin: 1px; margin-top: 0.3rem; }
        .line { border-top: 1px solid #ccc; width: 100%; }

        .divRight { width: 82%; display: flex; background-color: #fff; border-bottom: 1px solid #fff; border-top: 1px solid #fff; }
        .rightContent { width: 100%; height: 100%; display: flex; flex-direction: column; }
        .rightItem { flex: 1; width: 100%; }
        .rightItem1 { background-color: #fff; }
        .rightItem2 { background-color: #fff; }
        .rightSidebar { width: 20%; height: 100%; border-left: 1px solid #ccc; border-right: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad); overflow: auto; }
        .rightSidebarItem { color: #000; font-size: .8rem; padding: 3px; }

        table > thead > tr > td { color: #4a4a4a !important; }
        table > thead > tr { font-weight: bolder !important; background-color: #edf1f1 !important; border: 1px solid #dbd3c9 !important; }
        table > thead > tr > td { background-color: #009688 !important; font-size: 1.2rem !important; color: #fff; height: 3rem; }
        table > thead > tr > td > div { color: #fff !important; }
        .head { text-align: center; }

        td > div > input { border-color: #fff !important; }
        input#Car_CarNo { border-color: #01aaed !important }
        .btndel { background-color: #ec971f; }
        .layui-table td, .layui-table th { padding: 2px 5px; }
        .firsttime-tr { background-color: #ccc !important; }

        .divSelectPop { width: 12rem; height: 10rem; overflow: auto; background-color: #fff; min-width: 200px; }
        .divSelect { padding: 0 10px; line-height: 2rem; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; cursor: pointer; }
        .divSelect.selected { background-color: #5FB878; color: #fff; }
        .divSelect:hover { background-color: #218cbf; color: #e9f3ec; }

        .layui-tree-entry { height: 1.5rem !important; }
        .layui-tree-entry:hover { background-color: #f9f9f9 !important; color: #218cbf !important; }
        .layui-tree-entry:hover * { color: #218cbf !important; }


        .layui-input, .layui-select, .layui-textarea { line-height: 2.3rem !important; height: 2.3rem !important; }
        .layui-table td, .layui-table th { padding: 1px 2px !important; min-height: 1.7rem !important; line-height: 1.7rem !important; }
        table .layui-btn { height: 1.7rem !important; line-height: 1.7rem !important; }
        .layui-btn { height: 28px; line-height: 28px; border: 1px solid #67d0d1f0; padding: 0 19px; background-color: #009688; color: #fff; white-space: nowrap; text-align: center; font-size: 14px; border-radius: 2px; cursor: pointer; }

        /* 修改图标的大小 */
        .layui-tree .layui-icon-edit { font-size: 1.5rem !important; border: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-icon-add { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-icon-add-1 { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-icon-delete { font-size: 1.5rem !important; border: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-tree-editInput { border: 2px solid #0f9ee9; color: #000; background-color: #ccc; height: 1.7rem; line-height: 1.7rem; }
        .layui-tree .layui-icon-edit { font-size: 1.5rem !important; border: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-icon-add { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-icon-add-1 { font-size: 1.5rem !important; border: 1px solid #ccc !important; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-tree .layui-icon-delete { font-size: 1.5rem !important; border: 1px solid #ccc; background: radial-gradient(circle, #b9b7b7, #ffffffad) !important; z-index: 9999; }
        .layui-btn-group.layui-tree-btnGroup { display: flex; justify-content: left; position: absolute; bottom: -1.4rem; width: 100%; margin-left: 3rem; }
        .layui-btn-group.layui-tree-btnGroup i { cursor: pointer; }

        .fastmode { display: inline-block; margin-top: 2px; margin-left: 90px !important; }
        .fastmode ul { overflow: auto; }
        .fastmode li { padding: 0; text-align: center; display: inline-block; width: 100px; }
        .fastmode li:hover { background-color: rgba(0,0,0,0.05); color: #000; }
        .fastmode li a { line-height: 1.5rem; display: inline-block; }
        .fastmode li a img { height: 1rem; }
        .fastmode li cite { height: 1.5rem; line-height: 1.5rem; text-align: left; display: inline-block; color: #fff; font-size: .9rem; }
        .fastmode li cite:hover { color: #0f9ee9; }

        .parkname { line-height: 2.5rem; }
        .layui-icon-file::before { content: '\e857'; font-size: 18px; color: #FF5722; }
        .clear-icon { position: absolute; top: 3px; right: 1px; font-size: 15px; cursor: pointer; color: #FF5722; z-index: 9999; height: 20px; line-height: 20px; background-color: #e1e1e1; border-radius: 5px; padding: 5px; }

        .layui-tree-entry:hover { box-shadow: 1px 1px 1px rgb(193 184 184 / 60%); background-color: #ededed !important; }

        .layui-input[disabled], .layui-input[readonly], fieldset[disabled] .layui-input { background-color: #fff; }
        button { width: 15%; height: 35px; line-height: 35px; font-weight: bold; }
        .layui-btn { height: 38px; line-height: 38px; border: 1px solid transparent; padding: 0 18px; background-color: #009688; color: #fff; white-space: nowrap; text-align: center; font-size: 14px; border-radius: 2px; cursor: pointer; }
        #Cancel { background-color: #FFB800; }
        .layui-table td { border-color: #fff; }
        td > div input { border-color: #bee1da !important; }
    </style>
</head>

<body class="animated">

    <!-- 内容 -->
    <div class="divContent">
        <div class="rightContent">
            <!-- 车道 -->
            <div class="rightItem">
                <div class="layui-row">
                    <div class="layui-col-x11" style="overflow:auto !important;">
                        <table class="layui-table table_passway" style="overflow:auto !important;">
                            <thead>
                                <tr>
                                    <td style="width:10rem;">
                                        <div class="edit-label layui-col-xs12 head"> 所属区域</div>
                                    </td>
                                    <td style="width:12rem;">
                                        <div class="edit-label layui-col-xs12 head"> 车道数</div>
                                    </td>
                                    <td style="width:6rem;">
                                        <div class="edit-label layui-col-xs12 head"> 出入口</div>
                                    </td>
                                    <td style="width:12rem;">
                                        <div class="layui-col-xs12 head"> <div style="width:100%;text-align: center;">关联区域</div><div style="width:100%;text-align: center;">(选填)</div></div>
                                    </td>
                                    <td style="width:8rem;">
                                        <div class="edit-label layui-col-xs12 head"> 设备型号</div>
                                    </td>
                                    @*  <td>
                                    <div class="head"> 操作</div>
                                    </td> *@
                                </tr>
                            </thead>
                            <tbody class="tbody">
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 底部内容 -->
    <div class="divBottom">
        <!-- 底部内容 -->
        <button class="layui-btn layui-btn-primary" id="Save"><i class="fa fa-check"></i> <t>确定</t></button>
        <button class="layui-btn layui-btn-warning" id="Cancel"><i class="fa fa-times"></i> <t>取消</t></button>

    </div>


    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>

    <script type="text/x-jquery-tmpl" id="tmpl_passway">
        {{if PasswayLink_GateType==1}}
        <tr class="firsttime-tr intr">
        {{/if}}
        {{if PasswayLink_GateType!=1}}
        <tr class="firsttime-tr">
        {{/if}}
             <td>
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${PasswayLink_ParkAreaName}" data-value="${PasswayLink_ParkAreaNo}" data-key="PasswayLink_ParkAreaNo" placeholder="所属区域" onclick="SetSelectDiv(this,1)" />
                </div>
            </td>
             <td>
                <div class="layui-col-xs12">
                    <input type="text" class="layui-input" maxlength="3" value="${PasswayLink_GateNum}" min="0" max="150"  oninput="checkValue(this)" data-value="${PasswayLink_ParkAreaNo}" data-key="PasswayLink_GateNum" placeholder="车道数（该区域有多少车道）" />
                </div>
            </td>
            <td>
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="20" value="${PasswayLink_GateTypeName}" data-value="${PasswayLink_GateType}" data-key="PasswayLink_GateType" placeholder="出入口"  onclick="SetSelectDiv(this,2)"/>
                </div>
            </td>
              <td>
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${PasswayLink_SubParkAreaName}" data-value="${PasswayLink_SubParkAreaNo}" data-key="PasswayLink_SubParkAreaNo" placeholder="内场的入口外场的出口" onclick="SetSelectDiv(this,1)" />
                </div>
            </td>
              <td>
                <div class="layui-col-xs12">
                    <input type="text" readonly class="layui-input" maxlength="80" value="${PasswayLink_DriveName}" data-value="${PasswayLink_DriveNo}" data-key="PasswayLink_DriveNo" placeholder="设备型号"  onclick="SetSelectDiv(this,3)"/>
                </div>
            </td>
        @*   <td>
                 <button class="layui-btn btnadd"><t> 添加</t></button>
                 <button class="layui-btn btndel"><t> 删除</t></button>
            </td> *@
        </tr>
    </script>

    <script>
        var tree = null;
        var layuiForm = null;

        var inoutData = [{
            title: '入口',
            id: 1,
            spread: true,
        }, {
            title: '出口',
            id: 0,
            spread: true,
        }];

        layui.config({ base: '../Static/admin/' }).extend({ index: 'lib/index' }).use(['index', 'tree', 'layer'], function () {
            tree = layui.tree, layer = layui.layer
            layuiForm = layui.form;

            pager.init()

        });


        var pager = {
            passways: null,
            parkareas: null,
            parkarealist: [],
            devices: null,
            drives: null,
            inoutdata: inoutData,
            model: null,
            init: function () {
                $.ajaxSettings.async = false; //同步执行
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                layuiForm.render("select");
            },
            //数据绑定
            bindData: function () {
                pager.parkareas = window.parent.pager.parkareas;
                pager.drives = window.parent.pager.drives;
                // 开始遍历树状结构
                $.each(pager.parkareas, function (index, rootNode) {
                    traverseTree(rootNode, 0, null);
                });
                Passway_Table.binddata(".table_passway", pager.parkarealist);
            },
            bindAreas: function () {

            },
            bindEvent: function () {
                $("#Cancel").click(function () { parent.layer.close(parent.layuiIndex); });
                //一键 保存
                $("#Save").click(function () {
                    layer.open({
                        title: false,
                        content: '确定生成配置吗？',
                        btn: ['确定', '取消'], // 自定义按钮
                        yes: function (index, layero) {
                            layer.msg("处理中", { icon: 16, time: 0 });
                            pager.passways = Passway_Table.getValues(".table_passway");
                            parent.pager.constomConfig(pager.passways);
                            parent.layer.close(parent.layuiIndex);
                        },
                        btn2: function (index, layero) {
                            // 取消按钮的回调
                            // 默认关闭弹出层，返回 true 可以阻止关闭
                            // return true;
                        }
                    });
                });
            },
        };


        // 遍历树状结构的函数
        function traverseTree(node, level, parentNode) {
            var result = pager.drives.find(x => x.title === "S1206");
            var parentid = '';
            var parentname = '';
            if (parentNode && parentNode != null) {
                if (parentNode.id != "0" && parentNode.id != "") { parentid = parentNode.id; parentname = parentNode.title; }
            }

            // 输出当前节点
            if (node.id != "0" && node.id != "") {
                console.log(node.id + "," + node.title)
                pager.parkarealist.push({
                    PasswayLink_GateNum: '',
                    PasswayLink_ParkAreaNo: node.id,
                    PasswayLink_ParkAreaName: node.title,
                    PasswayLink_SubParkAreaNo: parentid,
                    PasswayLink_SubParkAreaName: parentname,
                    PasswayLink_GateType: '1',
                    PasswayLink_GateTypeName: '入口',
                    PasswayLink_DriveNo: result == null ? '' : result.id,
                    PasswayLink_DriveName: result == null ? '' : result.title
                });
                pager.parkarealist.push({
                    PasswayLink_GateNum: '',
                    PasswayLink_ParkAreaNo: node.id,
                    PasswayLink_ParkAreaName: node.title,
                    PasswayLink_SubParkAreaNo: parentid,
                    PasswayLink_SubParkAreaName: parentname,
                    PasswayLink_GateType: '0',
                    PasswayLink_GateTypeName: '出口',
                    PasswayLink_DriveNo: result == null ? '' : result.id,
                    PasswayLink_DriveName: result == null ? '' : result.title
                });
            }

            // 递归遍历子节点
            if (node.children) {
                $.each(node.children, function (index, child) {
                    traverseTree(child, level + 1, node);
                });
            }
        }

        function checkValue(input) {

            // 如果输入非数字，则清空
            if (!/^\d+$/.test((input.value))) {
                input.value = '';
                return;
            }

            const min = 0;
            const max = 150;
            let value = parseInt(input.value);

            if (isNaN(value)) {
                input.value = min;
                return;
            }

            if (value < min) {
                input.value = min;
            } else if (value > max) {
                input.value = max;
            }
        }
    </script>
    @*车道信息*@
    <script>

        //车道信息
        var Passway_Table = {
            binddata: function (table, data) {
                if (!data) {
                    data = [];
                }

                if (data.length == 0) {
                    data.push({ PasswayLink_GateNum: '', PasswayLink_ParkAreaNo: "", PasswayLink_ParkAreaName: "外场", PasswayLink_SubParkAreaNo: '', PasswayLink_GateType: '1', PasswayLink_GateTypeName: '入口', PasswayLink_DriveNo: '', PasswayLink_DriveName: '' });
                    data.push({ PasswayLink_GateNum: '', PasswayLink_ParkAreaNo: "", PasswayLink_ParkAreaName: "外场", PasswayLink_SubParkAreaNo: '', PasswayLink_GateType: '0', PasswayLink_GateTypeName: '出口', PasswayLink_DriveNo: '', PasswayLink_DriveName: '' })
                }

                $(table).find("tbody").html($("#tmpl_passway").tmpl(data))
                Passway_Table.bindEvent(table);
            },
            additem: function (table) {
                var item = [{ PasswayLink_GateNum: '', PasswayLink_ParkAreaNo: node.id, PasswayLink_ParkAreaName: node.title, PasswayLink_SubParkAreaNo: '', PasswayLink_GateType: '1', PasswayLink_GateTypeName: '入口', PasswayLink_DriveNo: '', PasswayLink_DriveName: '' }];
                $(table).find("tbody").append($("#tmpl_passway").tmpl(item));
                Passway_Table.bindEvent(table);
            },
            delitem: function (table, obj) {
                $(obj).closest(".firsttime-tr").remove();
                pager.passways = Passway_Table.getValues(".table_passway");
                Passway_Table.bindEvent(table);
            },
            bindEvent: function (table) {
                pager.passways = Passway_Table.getValues(".table_passway");
                Passway_Table.bindInput(table);
            },
            getValues: function (table) {
                var data = [];
                $(table).find("tbody").find('tr').each(function (index, item) {
                    var PasswayLink_ParkAreaNo = $(item).find('td').eq(0).find('input').attr("data-value")
                    var PasswayLink_ParkAreaName = $(item).find('td').eq(0).find('input').val()

                    var PasswayLink_GateNum = $(item).find('td').eq(1).find('input').val()

                    var PasswayLink_GateType = $(item).find('td').eq(2).find('input').attr("data-value")
                    var PasswayLink_GateTypeName = $(item).find('td').eq(2).find('input').val()
                    var PasswayLink_SubParkAreaNo = $(item).find('td').eq(3).find('input').attr("data-value")
                    var PasswayLink_SubParkAreaName = $(item).find('td').eq(3).find('input').val()
                    var PasswayLink_DriveNo = $(item).find('td').eq(4).find('input').attr("data-value")
                    var PasswayLink_DriveName = $(item).find('td').eq(4).find('input').val()
                    var n = {
                        PasswayLink_ParkAreaNo: PasswayLink_ParkAreaNo,
                        PasswayLink_ParkAreaName: PasswayLink_ParkAreaName,
                        PasswayLink_GateType: PasswayLink_GateType,
                        PasswayLink_GateTypeName: PasswayLink_GateTypeName,
                        PasswayLink_SubParkAreaNo: PasswayLink_SubParkAreaNo,
                        PasswayLink_SubParkAreaName: PasswayLink_SubParkAreaName,
                        PasswayLink_GateNum: PasswayLink_GateNum,
                        PasswayLink_DriveNo: PasswayLink_DriveNo,
                        PasswayLink_DriveName: PasswayLink_DriveName,
                    };
                    data.push(n);
                });
                return data;

            },
            bindInput: function (table) {
                $(table).off('input');
                $(table).on('input', function () {
                    pager.passways = Passway_Table.getValues(".table_passway");
                });
            }
        };


        function createPasswayNumber(len, data) {
            var beginNo = '';
            if (data != null && data.length > 0) {
                for (let item of data) {
                    if (!isNaN(item.Passway_No) && (item.Passway_No.length == 1 || item.Passway_No.length == 2)) {
                        beginNo = item.Passway_No;
                        break;
                    }
                }
            }

            if (beginNo == '') {
                var d = '1';
                if (data != null && data.length > 0) {

                    var isexit = false;
                    for (let item of data) {
                        if (item.Passway_No == d) {
                            isexit = true;
                            break;
                        }
                    }

                    if (isexit) {
                        d = createPasswayNumber(len, data);
                    }
                }

                return d;
            } else {
                beginNo = parseInt(beginNo) + 1;
                while (isExitPasswayNo(beginNo, data)) {
                    beginNo = parseInt(beginNo) + 1;
                }
            }

            return beginNo;
        }

        function isExitPasswayNo(beginNo, data) {
            for (let item of data) {
                if (!isNaN(item.Passway_No) && item.Passway_No == beginNo) {
                    return true;
                    break;
                }
            }
            return false;
        }

    </script>
    @*下拉列表*@
    <script>

        //移除弹出的DIV
        var myClickHandler = function (k) {
            event.preventDefault();
            event.stopPropagation();
            setTimeout(function () {
                if (!k.target.classList.contains('layui-icon') && !k.target.classList.contains('layui-tree-icon')) {
                    $(".tdselect-div").remove();
                    $(".clear-icon").remove();
                }
            }, 0);
            return false;
        }
        //绑定window点击事件
        $(window).off('click', myClickHandler).on('click', myClickHandler);

        function SetSelectDiv(e, datatype) {
            event.preventDefault();
            event.stopPropagation();

            if ($("#test1").length > 0) $("#test1").remove();

            //获取当前行的索引
            var dom = $(e).get(0);
            var position = getPosition(dom);
            var top = position.top + dom.offsetHeight + 5;
            var left = position.left;
            var width = dom.clientWidth;

            //检查是否超出屏幕底部
            var divHeight = 192; // 12rem
            var screenBottom = window.scrollY + window.innerHeight;
            if ((top + divHeight) > screenBottom) {
                top = position.top - divHeight - 5;
            }

            //设置样式
            var css = '.tdselect-div {color: #3d3d3d;font-size:13px !important; position: absolute;z-index: 999; top:' + top + 'px; left:' + left + 'px; bottom:auto;width:14rem; height:12rem;overflow: auto;background-color: #fff;cursor: pointer; border: 1px solid #dcdee3;}';
            //添加样式
            var style = document.createElement("style");
            style.id = "tdselect-div-float";
            style.innerText = css;
            document.head.append(style);

            //创建DIV
            var div = document.createElement("div");
            div.className = "tdselect-div";
            div.id = "test1";
            //添加到body
            document.body.appendChild(div);

            var udata = [];
            if (datatype == 1) {
                var newdata = pager.parkareas[0].children;
                udata = newdata;
            } else if (datatype == 2) {
                udata = pager.inoutdata;
            } else if (datatype == 3) {
                udata = pager.drives;
            } else if (datatype == 4) {
                var convertedPassways = $.map(pager.passways, function (item) {
                    if (item.Passway_No && item.Passway_No != "") {
                        return {
                            title: item.Passway_Name,
                            id: item.Passway_No,
                            spread: true,
                            children: null
                        };
                    }
                });
                udata = convertedPassways;
            } else if (datatype == 5) {
                udata = pager.deviceiodata;
            }

            if (udata == undefined || udata == null) udata = [];

            var inst1 = tree.render({
                elem: '#test1'  //绑定元素
                , data: udata
                , click: function (obj) {
                    $(e).val(obj.data.title);
                    $(e).attr("data-value", obj.data.id);
                    $(".tdselect-div").remove();
                    $(".clear-icon").remove();
                    pager.passways = Passway_Table.getValues(".table_passway");
                    //pager.devices = Device_Table.getValues(".table_device");
                }
            });

            // 在 tree.render 完成后重新插入图标
            var clearIcon = document.createElement("i");
            clearIcon.className = "layui-icon layui-icon-delete clear-icon";
            clearIcon.title = "清空";  // 提示信息
            clearIcon.innerText = "清空";  // 提示信息
            clearIcon.onclick = function () {
                $(e).val('')
                $(e).attr("data-value", '');
                $(".tdselect-div").remove();
                $(".clear-icon").remove();
                pager.passways = Passway_Table.getValues(".table_passway");
                //pager.devices = Device_Table.getValues(".table_device");
            };

            //把clearIcon添加到e的父元素，先移除再添加
            $('.clear-icon').remove();
            $(e).parent().append(clearIcon);

            //为每个树节点添加悬浮效果和点击事件
            $('#test1 .layui-tree-entry').each(function () {
                var $entry = $(this);
                $entry.off('click').on('click', function (event) {
                    $(this).find('.layui-tree-txt').click();
                });
            });

            //设置滚动条
            var selectedDiv = $("div.divSelect.selected");
            if (selectedDiv && selectedDiv.length > 0) {
                var divSelectPop = selectedDiv.closest(".divSelectPop");
                divSelectPop.scrollTop(selectedDiv.position().top - 20);
                selectedDiv.focus();
            }
            e.click();
        }

        //查找元素的绝对位置
        function getPosition(element) {
            var actualLeft = element.offsetLeft,
                actualTop = element.offsetTop,
                current = element.offsetParent; // 取得元素的offsetParent
            // 一直循环直到根元素
            while (current !== null) {
                actualLeft += current.offsetLeft;
                actualTop += current.offsetTop;
                current = current.offsetParent;
            }
            // 返回包含left、top坐标的对象
            return {
                left: actualLeft,
                top: actualTop
            };
        }


    </script>
</body>

</html>
