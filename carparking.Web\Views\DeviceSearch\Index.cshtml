﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>设备搜索2304031444</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
         .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }

        .content { margin: 7px 19px 0px 19px !important; cursor: pointer; }

        .content:hover { color: #1E9FFF; font-weight: 600; font-size: 20px; }

        span.ss { font-size: 13px; text-align: justify; word-break: break-all; color: #61a8d1; background-color: #f5eeee; float: left; padding: 3px 5px; }
        .Disbtn { display: none; }
        .btnStop { color: #e11041; font-weight: 800; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>维护管理</cite></a>
                <a><cite>设备搜索</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input" name="UDPDevice_IP" id="UDPDevice_IP" autocomplete="off" placeholder="IP地址" value="" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="设备类型" class="form-control chosen-select " id="UDPDevice_Type" name="UDPDevice_Type" lay-search>
                                    <option value="">设备类型</option>
                                    <option value="0">06相机</option>
                                    <option value="1">15相机</option>
                                    <option value="2">通道机</option>
                                    <option value="3">智慧道闸</option>
                                    <option value="4">EPT</option>
                                    <option value="5">智慧盒子</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                                <button class="layui-btn" id="Open" style="line-height: 0;"><i class="layui-icon layui-icon-android inbtn"></i><t>自动刷新</t></button>
                            </div>
                        </div>
                    </div>

                    <div class="layui-card-body">
                        @* <label style="position: absolute;line-height: 50px;width: 500px;color: #e14c2a;height: 50px;left: 100px;">修改设备IP地址，修改余位屏参数、修改显示屏参数</label>*@
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Update" lay-event="Update"><i class="fa fa-edit"></i><t>修改IP</t></button>
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 模态框（Modal） -->
    <div class="modal fade" id="verifyCheck" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-progress layui-progress-big" lay-showpercent="true" lay-filter="ippro">
                                <div class="layui-progress-bar layui-bg-red" lay-percent="0%"></div>
                            </div>
                            <div class="layui-inline">
                                <div class="layui-btn" id="Stop"><t>停止搜索</t></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.3.3.7.js" asp-append-version="true"></script>
    <script type="text/html" id="TmplSuccess">
        {{#  if(d.PushResult_Success==0){ }}
        <span class="layui-badge layui-bg-orange ">失败</span>
        {{#  } else if(d.PushResult_Success==1) { }}
        <span class="layui-badge layui-bg-blue ">成功</span>
        {{#  } else if(d.PushResult_Success==3) { }}
        <span class="layui-badge layui-bg-gray ">取消</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-cyan ">未知</span>
        {{#  } }}
    </script>
    <script type="text/html" id="TmplOK">
        {{#  if(d.PushResult_Ok==0){ }}
        <span class="layui-badge layui-bg-gray ">未处理</span>

        {{#  } else if(d.PushResult_Ok==1) { }}
        <span class="layui-badge layui-bg-orange ">处理失败</span>
        {{#  } else if(d.PushResult_Ok==2) { }}
        <span class="layui-badge layui-bg-blue ">处理成功</span>
        {{#  } else { }}
        <span class="layui-badge layui-bg-cyan ">未知</span>
        {{#  } }}
    </script>
    <script>
        var loadFirst = true;
        var Power = window.parent.global.formPower;
        var comtable = null;
        var element = null;
        var actionList = [{ action: "carcardtype", name: "车牌类型" }, { action: "coupon", name: "优惠券" }, { action: "cartype", name: "车牌颜色" }, { action: "deletecar", name: "车辆注销" }
            , { action: "carin", name: "出入场" }, { action: "carout", name: "车辆出场" }, { action: "specialcar", name: "特殊车辆" }, { action: "car", name: "车主车辆" }, { action: "owner", name: "车主车位" }, { action: "accessauth", name: "出入权限" }
            , { action: "backcar", name: "倒车记录" }, { action: "chargerule", name: "计费规则" }, { action: "blacklist", name: "黑名单" }, { action: "boardauth", name: "控制板授权" }, { action: "businesscar", name: "商家车辆" }, { action: "dateset", name: "日期设置" }
            , { action: "device", name: "设备" }, { action: "drive", name: "设备型号" }, { action: "endnumauth", name: "尾号限行" }, { action: "monthrule", name: "充值规则" }, { action: "parkarea", name: "区域信息" }, { action: "parkdiscountset", name: "优惠设置" }, { action: "parkorder", name: "停车订单" }, { action: "passway", name: "车道管理" }
            , { action: "payorder", name: "缴费记录" }, { action: "paypart", name: "缴费明细" }, { action: "paytype", name: "支付方式" }, { action: "policy", name: "停车场设置" }, { action: "powergroup", name: "权限设置" }, { action: "reserve", name: "访客车辆" }, { action: "sysconfig", name: "系统设置" }, { action: "sentryhost", name: "岗亭管理" }
            , { action: "paysuccess", name: "支付信息" }, { action: "onspacepaycharge", name: "车位有效期变更" }];

        layui.use(['table', 'form', 'element'], function () {
            pager.init();
            var table = layui.table;
            var layuiForm = layui.form;
            element = layui.element;
            layuiForm.render("select");

            $("#startDate").val(new Date().Format("yyyy-MM-dd"));
            $("#endDate").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["startDate", "endDate"], { type: 'date', range: true });

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'UDPDevice_IP', title: 'IP地址' }
                , { field: 'UDPDevice_DeviceNo', title: '设备编码' }
                , { field: 'UDPDevice_DeviceType', title: '设备类型' }
                , {
                    field: 'UDPDevice_DeviceType', title: '操作', width: 180, templet: function (d) {
                        var actionHtml = "";
                        if (d.UDPDevice_DeviceType == "06相机" || d.UDPDevice_DeviceType == "15相机" || d.UDPDevice_DeviceType == "智慧盒子") {
                            return actionHtml + '<a href="http://' + d.UDPDevice_IP + ':80" target="_blank"><span class="layui-badge layui-bg-set"><i class="fa fa2" title="点击打开配置界面"></i>设备配置</span></a>';
                        } else if (d.UDPDevice_DeviceType == "通道机") {
                            return actionHtml + '<a href="http://' + d.UDPDevice_IP + ':8099" target="_blank"><span class="layui-badge layui-bg-set"><i class="fa fa2" title="点击打开配置界面"></i>设备配置</span></a>';
                        }
                        return "";
                    }
                }
            ]];

            cols = tb_page_cols(cols);
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/DeviceSearch/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns', defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limits: [10, 20, 50, 100]
                , done: function (res, curr, count) {
                    tb_page_set(res);
                    if (count > 0) {
                        var len = res.data.filter((item) => {
                            return item.UDPDevice_DeviceType === "智慧道闸";
                        }).length;
                        if (len > 0) count = count - len;
                    }
                    if (loadFirst && count <= 0) {
                        loadFirst = false;
                        // pager.beginUdp(1);
                    }
                    pager.bindPower();
                    //pager.startProgress();
                    //pager.searchData();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        if (data.length > 1) { layer.msg("请单选"); return; }

                        var type = data[0].UDPDevice_DeviceType;
                        console.log(JSON.stringify(data[0]))

                        layer.open({
                            type: 2, id: 1,
                            title: "修改设备IP地址",
                            content: "GateEdit?Act=Update&data=" + encodeURIComponent(JSON.stringify(data[0])),
                            area: getIframeArea(["500px", "400px"]),
                            maxmin: false
                        });

                        //var ipArray = [];
                        //for (var i = 0; i < data.length; i++) { ipArray.push(data[i].UDPDevice_IP); }
                        //if (type == "通道机" || type== "机器人"){
                        //    layer.open({
                        //        type: 2, id: 1,
                        //        title: "修改设备IP地址",
                        //        content: "GateEdit?Act=Update&data=" + encodeURIComponent(JSON.stringify(data[0])),
                        //        area: getIframeArea(["500px", "400px"]),
                        //        maxmin: false
                        //    });
                        //}else if (type == "智慧道闸"){
                        //        layer.msg("待完善");
                        //}else{
                        //     layer.open({
                        //        type: 2, id: 1,
                        //        title: "修改设备参数",
                        //        content: "CameraEdit?Act=Update&data=" + encodeURIComponent(JSON.stringify(data[0])),
                        //        area: getIframeArea(["90%", "90%"]),
                        //        maxmin: false
                        //    });
                        //}
                        break;
                };
            });

            table.on("tool(com-table-base)", function (obj) {
                var data = obj.data;
                switch (obj.event) {
                    case 'showContent':
                        layer.msg("处理中...", { icon: 16, time: 0 });
                        $.getJSON("/PushResult/GetResult", { PushResult_No: data.PushResult_No, PushResult_Time: data.PushResult_Time }, function (json) {
                            if (json.success)
                                try {
                                    layer.closeAll();
                                    var subData = JSON.parse(json.data);
                                    layer.open({
                                        type: 1,
                                        title: '消息内容',
                                        area: ['700px', '520px'], //宽高
                                        btn: ['关闭'],
                                        content:
                                            ' <div class="layui-content">' +
                                            '<div class="layui-row"><pre>' +
                                            JSON.stringify(JSON.parse(subData.data), null, 2) +
                                            '</pre></div>' +
                                            '</div>',
                                        success: function () {
                                        }
                                    });
                                } catch {
                                    layer.open({
                                        type: 1,
                                        title: '消息内容',
                                        area: ['700px', '520px'], //宽高
                                        btn: ['关闭'],
                                        content:
                                            ' <div class="layui-content">' +
                                            '<div class="layui-row"><pre>' +
                                            JSON.stringify(data, null, 2) +
                                            '</pre></div>' +
                                            '</div>',
                                        success: function () {
                                        }
                                    });
                                }
                            else
                                layer.msg(json.msg, { icon: 0, time: 1500 });
                        });
                        break;
                }
            });

            tb_row_radio(table);
        });
    </script>
    <script>

        var autoSearch = true;
        var timer = null;

        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/DeviceSearch/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });//$('#verifyCheck').modal('toggle');

                $("#Open").click(function () {
                    if (timer == null) {
                        timer = setInterval(function () {
                            pager.bindData(1);
                        }, 1500);
                        $(this).addClass("btnStop").html('<i class="layui-icon layui-icon-android inbtn"></i><t>停止刷新</t>')
                    } else {
                        clearInterval(timer);
                        timer = null;
                        $(this).removeClass("btnStop").html('<i class="layui-icon layui-icon-android inbtn"></i><t>自动刷新</t>');
                    }
                });
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            },
            // beginUdp: function (index) {
            //     index++;
            //     if (index < 4) {
            //         $.getJSON("/DeviceSearch/GetUDPMsg", {}, function (json) {
            //             if (json.success) { } else {
            //                 setTimeout(function () { pager.beginUdp(index); }, 1000);
            //             }
            //         });
            //     }
            // },
            startProgress: function () {
                var progress = 0;
                var timer = setInterval(function () {
                    progress += 1;
                    if (progress > 100) {
                        progress = 0;
                        layui.element.progress('ippro', '0%');
                    } else {
                        layui.element.progress('ippro', progress + '%');
                    }
                }, 60);
            },
            searchData: function () {
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/DeviceSearch/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: pager.pageIndex }
                    , done: function (res, curr, count) {
                        tb_page_set(res);
                        if (autoSearch) { setTimeout(function () { pager.searchData(); }, 500) }
                    }
                });
            }
        }
    </script>
</body>
</html>
