﻿
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/operationscenter/index.css?t=@DateTime.Now.Ticks" rel="stylesheet" />
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <style>
        label { padding: 10px 10px 0 0; float: right; }
        .layui-tab-title { background-color: #5868e0 !important; }

        .left { background-color: #fff !important; }
        .right { background-color: #5864e0 !important; }

        .monitor { background-color: #1064e0 !important; bottom: 49%; }
        .record { background-color: #2014e0 !important; top: 52%; }
        .content { top: .1rem; }
        .content:hover { cursor: pointer; }
        .content .right { width: 10rem !important; }
        .content .left { right: 10.8rem !important; }

        .hideDiv { opacity: .3; }
        .watermark-text { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 35px; color: #fff; pointer-events: none; }
    </style>
</head>
<body>
    <div class="ibox-content">
        <div class="content layui-form">
            <div class="left">
                <div class="monitor">
                    <p class="watermark-text">视频监控</p>
                </div>
                <div class="record">
                    <p class="watermark-text">场内记录</p>
                </div>
            </div>
            <div class="right">
                <p class="watermark-text">侧边栏</p>
            </div>
        </div>
    </div>
    <script>
        layui.use(["form"], function () {

            var con = parent.pager.getPart();
            if (con && con.hasOwnProperty("showModule")) {
                if (!con.showModule.right) {
                    $('.content .right').addClass("hideDiv");
                } 
                if (!con.showModule.monitor) {
                    $('.content .monitor').addClass("hideDiv");
                } 
                if (!con.showModule.record) {
                    $('.content .record').addClass("hideDiv");
                } 
            }

            $(".content").find("div").click(function () {
                event.stopPropagation();
                if ($(this).hasClass("hideDiv")) {
                    $(this).removeClass("hideDiv");
                    parent.pager.showPart($(this).attr('class').split(' ')[0]);
                } else {
                    $(this).addClass("hideDiv");
                    parent.pager.dispalyPart($(this).attr('class').split(' ')[0]);
                }
            });
        });
    </script>
</body>
</html>
