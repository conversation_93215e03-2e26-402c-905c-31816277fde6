﻿@addTagHelper "*, Microsoft.AspNetCore.Mvc.TagHelpers"
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>计费规则</title>
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <link href="~/Static/css/free.css" rel="stylesheet" />
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <style>
        html, body { height: 100%; width: 100%; overflow: auto; }
        body { height: auto; }
        .mainbody { padding: 15px; }
        .layui-select-title input { /*color: #0094ff;*/ }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.xsall { width: 70px; float: left; }
        .select-xsall { float: left; margin-right: 5px; }
        .layui-row.primary { padding: 1rem 0; }
        .layui-row.primary label { font-weight: bold; background-color: #18605a; color: #fff; padding: 3px 10px; }
        .layui-select-title input { }
        .layui-inline .layui-form-select .layui-input { width: 182px; }
        .rulebox .layui-row { /*margin-top: 10px;*/ }
        .rulebox label { float: left; padding: 9px 5px 0; }
        .layui-input.xsall { width: 70px; float: left; }
        .select-xsall { float: left; margin-right: 5px; }
        .layui-col-xs12 { margin-bottom: 5px; padding-bottom: 10px; }
        .layui-row.panel { border-bottom: 1px solid #bbb; }
        .workday_item, .workday_holidayItem { border-top: 1px solid #54c8d8; }
        .workday_item:last-child { border-bottom: 1px solid #bbb; }
        .workday_holidayItem:last-child, .tmplnoworkday_notimepart_spacetime_item:last-child, .tmplnoworkday_notimepart_rangtime_item:last-child, .tmplnoworkday_timepart_item:last-child { border-bottom: 1px solid #bbb; }
        .tmplnoworkday_timepart_item:not(:last-child) { border-bottom: 1px solid #54c8d8; }
        .divInLine { display: inline-block; margin-top: 5px; }
        .divInLine input { max-height: 30px; }
        .divInLine2 input { max-height: 38px !important; width: 100%; }
        .divInLine input.normal { max-width: 50px; max-height: 30px !important; text-align: center; padding-left: 3px; }
        .divInLine button { max-height: 32px !important; line-height: 30px; padding: 0px 10px; margin-bottom: 5px; }
        .divInLine label { height: 15px; padding: 0; }
        .workday_delitem, .workday_holidayDelitem, .tmplnoworkday_notimepart_rangtime_item_rule_delitem, .noworkday_timepart_delitem, .tmplworkday_rangtime_item_rule_delitem { background-color: #ff6a00 !important; }
        .workday_header, .workday_holiday_header { margin-bottom: 5px; }
        .workday_header { padding: 3px; }
        .workday_holiday_header { float: left; width: 100%; padding: 3px; }
        .workday_holiday_header span, .workday_header span { font-weight: 600; }
        input[type=time] { min-width: 100px; }
        .topheader { padding: 3px; }
        #ruleContent { }
        .tipMsg { color: #18605a; font-size: 15px; padding-left: 15px; margin-left: 20px; }
        .edit-label { float: left; }
        .edit-ipt-ban { float: left; }
        .label2 { color: #999; font-size: 12px; padding-bottom: 5px; clear: both; }
        .itemcontent { margin-bottom: 64px !important; }
        .checkResult { color: red; font-weight: 500; }
        .layui-row { margin-bottom: 15px; }
        .itemtitle { height: 28px; text-align: center; padding-top: 8px; font-weight: 700; color: #635f5f; background: linear-gradient(to right,#fbf9f9,#e9eefd85,#e9eefd85) !important; cursor: pointer; }
        .headitem { margin-bottom: 10px; margin-top: 10px; }
        .layui-btn { }
        .layui-btn-normal { background-color: #1ab394; }
        .iconItem { border: 1px dashed #bbb; height: 20px; border-radius: 20px; right: 10px; float: right; width: 20px; text-align: center; line-height: 20px; cursor: pointer; }
        .iconItem:hover { border: 1px dashed #424040; }
        .iconItem:hover i { color: #19be3b; }
        .iconItem i { color: #424040; font-size: 13px; }
        .iconItem2 { border: 1px dashed #bbb; height: 20px; border-radius: 20px; right: 10px; float: right; width: 20px; text-align: center; line-height: 20px; cursor: pointer; }
        .iconItem2:hover { border: 1px dashed #424040; }
        .iconItem2:hover i { color: #19be3b; }
        .iconItem2 i { color: #424040; font-size: 13px; }
        .btnUnit { border-color: #f2eeee !important; border-top-right-radius: 5px; border-bottom-right-radius: 5px; color: #5db587 }
        .layui-btn-outline { margin-left: 0 !important; }
        .input-group { position: relative; display: table; border-collapse: separate; }
        .input-group-addon, .input-group-btn { width: 1%; white-space: nowrap; vertical-align: middle; }
        .input-group .form-control, .input-group-addon, .input-group-btn { display: table-cell; }
        .help-btn { z-index: 999; position: absolute; width: 20px; margin-left: 7px; margin-top: 6px; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn:after { font-weight: bold; }
        .help-btn:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }
        .help-btn-tip { z-index: 999; position: absolute; width: 20px; right: -28px; margin-top: -27px !important; height: 20px; text-align: center; line-height: 22px; background-color: #f2f2f2; cursor: pointer; border-radius: 20px; font-style: normal; color: #999; }
        .help-btn-tip:after { font-weight: bold; }
        .help-btn-tip:hover { background-color: #1ab394; color: #fff; box-shadow: 0px 1px 10px #1080d4; }
        .help-tmpl { width: 100%; height: auto; min-height: 100px; position: relative; line-height: 24px; }
        .help-tmpl ul { margin: 10px; padding: 0; list-style: none; }
        .help-tmpl p { margin: 10px; padding: 0; text-indent: 0; }
        .help-tmpl .help-footer { margin: 10px; }
        .help-tmpl ul img { box-shadow: 0px 1px 10px #bbb; max-width: 100%; }
        .tipRequest { }
        .layui-layer-tips .layui-layer-content { position: relative; line-height: 22px; min-width: 12px; padding: 8px 15px; font-size: 12px; _float: left; border-radius: 2px; box-shadow: 1px 1px 3px rgb(0 0 0 / 20%); background: linear-gradient(to right,#080c15,#232f75,#010102); color: #fff; }
        .stime { max-width: 80px !important; width: 80px !important; }
        .merge-box .scrollbox .merge-list { padding-bottom: 5px; }
        .label-content { max-height: 120px; overflow-y: auto; }
        .hourmoresetting { cursor: pointer; text-align: center; color: #0ad1f1f0; border-bottom: 1px solid #b0dcf5; border-top: 0px solid #b0dcf5; height: 2rem; padding-top: .6rem; width: 90%; margin-left: 5%; margin-bottom: 0 !important; }
        .blue { color: #0ad1f1f0; }
        .customer-footer { position: fixed; margin: auto; bottom: 0; right: 0; height: 44px; line-height: 44px; width: 100%; background: #fff; padding: 10px 35px; text-align: right; box-shadow: 0 -1px 2px 0 rgb(0 0 0 / 5%); z-index: 1000; }
        .layui-hide { display: none !important; }
    </style>
</head>
<body>
    <div class="ibox-content layui-form">
        <span class="light"></span>
        <div id="verifyCheck">
            <div class="layui-row form-group itemtitle basetitle">基本设置 <div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>
            <div class="basesetting">
                <div class="layui-row">
                    <div class="edit-label layui-col-xs2">计费标准</div>
                    <div class="layui-col-xs8">
                        <div class="btnCombox" id="ChargeRules_Type">
                            <ul>
                                <li data-value="0" class="biaozhun layui-hide">标准版</li>
                                <li data-value="1" class="qijian layui-hide">旗舰版</li>
                                <li data-value="2" class="yinchuan layui-hide select">银川收费</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="layui-row">
                    <div class="edit-label  layui-col-xs2">规则名称</div>
                    <div class="edit-ipt-ban layui-col-xs8">
                        <input type="text" class="layui-input v-null" id="ChargeRules_Name" data-minlen="2" maxlength="50" value="" placeholder="请填写规则名称" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label ">规则有效期</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-submit" value="" id="ChargeRules_Time" data-key="ChargeRules_Time" placeholder="请选择规则有效期" readonly />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-xs2 edit-label ">车牌类型</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <div id="BillRuleTemp_CarCardType" class="v-null" style="min-width:210px;"></div>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label ">车牌颜色</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <div id="BillRuleTemp_CarType" class="v-null" style="min-width:210px;"></div>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-xs2 edit-label ">停车区域</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <div id="BillRuleTemp_ParkArea" class="v-null" style="min-width:210px;"></div>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <div class="edit-label  layui-col-xs2">规则描述</div>
                    <div class="edit-ipt-ban layui-col-xs8">
                        <textarea placeholder="请填写规则描述" class="layui-textarea v-null" maxlength="500" id="ChargeRules_Remark"></textarea>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
                <div class="layui-row">
                    <label class="layui-col-xs2 edit-label">免费分钟</label>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="number" class="layui-input v-null v-number" id="ChargeRules_FreeMin" name="ChargeRules_FreeMin" maxlength="5" value="0" />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-xs2 edit-label ">规则类型</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <select class="layui-select" lay-search id="ChargeRules_OverTime" name="ChargeRules_OverTime" lay-filter="ChargeRules_OverTime" data-key="ChargeRules_OverTime">
                            <option value="0">标准计费规则</option>
                            <option value="1">超时计费规则</option>
                            <option value="2">通用计费规则</option>
                        </select>
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
            </div>
            <div class="layui-row form-group itemtitle sectiontitle">时段设置<div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>
            <div class="layui-row sectionsetting">
                <div class="layui-row">
                    <div class="layui-col-xs2 edit-label">白天段</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-submit" value="" id="Section_White_Time" data-key="Section_White_Time" placeholder="HH:mm:ss - HH:mm:ss" readonly />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                    <div class="layui-col-xs2 edit-label ">夜间段</div>
                    <div class="layui-col-xs3 edit-ipt-ban">
                        <input type="text" class="layui-input v-null v-submit" value="" id="Section_Night_Time" data-key="Section_Night_Time" placeholder="HH:mm:ss - HH:mm:ss" readonly />
                    </div>
                    <div class="layui-col-xs1 red-mark">*</div>
                </div>
            </div>

            <div class="layui-row form-group itemtitle hourtitle">收费设置<div class="iconItem2"><i class="layui-icon layui-icon-down"></i></div></div>
            <div class="layui-row hoursetting" style="height:490px;">
                <div class="layui-row">
                    <div class="edit-label layui-col-xs1"></div>
                    <div class="layui-col-xs10">
                        <div class="layui-tab layui-tab-brief" lay-filter="tab-filter">
                            <ul class="layui-tab-title">
                                <li class="layui-this" data-value="0" lay-id="0">商业</li>
                                <li data-value="1" lay-id="1">社会公共</li>
                                <li data-value="2" lay-id="2">交通枢纽</li>
                                <li data-value="3" lay-id="3">公共（益）性单位</li>
                                <li data-value="4" lay-id="4">医院</li>
                                <li data-value="5" lay-id="5">路侧</li>
                            </ul>
                            <div class="layui-tab-content">
                                <div class="layui-tab-item layui-show">
                                    <div class="layui-row">
                                        <label class="layui-form-label">分类</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Biz_Level_1">
                                                <ul>
                                                    <li data-value="0" class="select">A类区商业场所</li>
                                                    <li data-value="1">B类区商业场所</li>
                                                    <li data-value="2">C类区商业场所</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label">位置</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Biz_Level_2">
                                                <ul>
                                                    <li data-value="0" class="select">地上</li>
                                                    <li data-value="1">地下及立体</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">白天</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:88px;">2小时内最高</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Biz_White_1" name="Biz_White_1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:198px;">&nbsp;&nbsp;元，2小时后每1小时最高加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Biz_White_2" name="Biz_White_2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">夜间</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Biz_Night" name="Biz_Night" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">优惠后全天24小时最高收费</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Biz_Max" name="Biz_Max" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-tab-item">
                                    <div class="layui-row">
                                        <label class="layui-form-label">分类</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Social_Level_1">
                                                <ul>
                                                    <li data-value="0" class="select">A类区</li>
                                                    <li data-value="1">B类区</li>
                                                    @*<li data-value="2">C类区</li>*@
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label">位置</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Social_Level_2">
                                                <ul>
                                                    <li data-value="0" class="select">地上</li>
                                                    <li data-value="1">地下及立体</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">白天</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;" class="Social_White_AB">
                                                <div style="display:table-cell;width:60px;">1小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-floatLimit" id="Social_White_0" name="Social_White_0" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:210px;">&nbsp;&nbsp;元(不填写或小于等于0则忽略)，</div>
                                                <div style="display:table-cell;width:60px;">2小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Social_White_1" name="Social_White_1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:100px;">&nbsp;&nbsp;元，</div>
                                            </div>
                                            <div style="display:table;margin-top:5px;" class="Social_White_AB">
                                                <div style="display:table-cell;width:220px;">2小时以上4小时以内每1小时加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Social_White_2" name="Social_White_2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元，</div>
                                            </div>
                                            <div style="display:table;margin-top:5px;" class="Social_White_AB">
                                                <div style="display:table-cell;width:140px;">4小时后每30分钟加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Social_White_3" name="Social_White_3" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                            <div style="display:table;" class="Social_White_C layui-hide">
                                                <div style="display:table-cell;width:85px;">2小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Social_White_C1" name="Social_White_C1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:198px;">&nbsp;&nbsp;元，2小时后每1小时加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Social_White_C2" name="Social_White_C2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">夜间</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Social_Night" name="Social_Night" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">优惠后全天24小时最高收费</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Social_Max" name="Social_Max" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-tab-item">
                                    <div class="layui-row">
                                        <label class="layui-form-label">分类</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Traffic_Level_1">
                                                <ul>
                                                    <li data-value="0" class="select">飞机场</li>
                                                    <li data-value="1">火车站</li>
                                                    <li data-value="2">汽车站</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label">位置</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Traffic_Level_2">
                                                <ul>
                                                    <li data-value="0" class="select">地上</li>
                                                    <li data-value="1">地下及立体</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">白天</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:65px;">2小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Traffic_White_1" name="Traffic_White_1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:172px;">&nbsp;&nbsp;元，2小时后每1小时加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Traffic_White_2" name="Traffic_White_2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">夜间</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Traffic_Night" name="Traffic_Night" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                    @*<div class="layui-row">
                                    <label class="layui-form-label required">优惠后全天24小时最高收费</label>
                                    <div class="layui-input-block">
                                    <div style="display:table;">
                                    <div style="display:table-cell;width:100px;">
                                    <input type="text" class="layui-input v-null v-floatLimit" id="Traffic_Max" name="Traffic_Max" maxlength="5" placeholder="￥" autocomplete="off" />
                                    </div>
                                    <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                    </div>
                                    </div>
                                    </div>*@
                                </div>
                                <div class="layui-tab-item">
                                    <div class="layui-row">
                                        <label class="layui-form-label">位置</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Public_Level_2">
                                                <ul>
                                                    <li data-value="0" class="select">地上</li>
                                                    <li data-value="1">地下及立体</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">白天</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:60px;">3小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Public_White_1" name="Public_White_1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:172px;">&nbsp;&nbsp;元，3小时后每1小时加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Public_White_2" name="Public_White_2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">夜间</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Public_Night" name="Public_Night" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                    @*<div class="layui-row">
                                    <label class="layui-form-label required">优惠后全天24小时最高收费</label>
                                    <div class="layui-input-block">
                                    <div style="display:table;">
                                    <div style="display:table-cell;width:100px;">
                                    <input type="text" class="layui-input v-null v-floatLimit" id="Public_Max" name="Public_Max" maxlength="5" placeholder="￥" autocomplete="off" />
                                    </div>
                                    <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                    </div>
                                    </div>
                                    </div>*@
                                </div>
                                <div class="layui-tab-item">
                                    <div class="layui-row">
                                        <label class="layui-form-label">分类</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="His_Level_1">
                                                <ul>
                                                    <li data-value="0" class="select">A类医院</li>
                                                    <li data-value="1">B类医院</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label">位置</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="His_Level_2">
                                                <ul>
                                                    <li data-value="0" class="select">地上</li>
                                                    <li data-value="1">地下及立体</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">白天</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:60px;">3小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="His_White_1" name="His_White_1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:172px;">&nbsp;&nbsp;元，3小时后每1小时加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="His_White_2" name="His_White_2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">夜间</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="His_Night" name="His_Night" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                    @*<div class="layui-row">
                                    <label class="layui-form-label required">优惠后全天24小时最高收费</label>
                                    <div class="layui-input-block">
                                    <div style="display:table;">
                                    <div style="display:table-cell;width:100px;">
                                    <input type="text" class="layui-input v-null v-floatLimit" id="His_Max" name="His_Max" maxlength="5" placeholder="￥" autocomplete="off" />
                                    </div>
                                    <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                    </div>
                                    </div>
                                    </div>*@
                                </div>
                                <div class="layui-tab-item">
                                    <div class="layui-row">
                                        <label class="layui-form-label">分类</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Roadside_Level_1">
                                                <ul>
                                                    <li data-value="0" class="select">A类区</li>
                                                    <li data-value="1">B类区</li>
                                                    <li data-value="2">C类区</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label">位置</label>
                                        <div class="layui-input-block">
                                            <div class="btnCombox" id="Roadside_Level_2">
                                                <ul>
                                                    <li data-value="0" class="select">地上</li>
                                                    <li data-value="1">地下及立体</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">白天</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:60px;">2小时内</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Roadside_White_1" name="Roadside_White_1" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;width:172px;">&nbsp;&nbsp;元，2小时后每1小时加收</div>
                                                <div style="display:table-cell;width:100px;"><input type="text" class="layui-input v-null v-floatLimit" id="Roadside_White_2" name="Roadside_White_2" maxlength="5" placeholder="￥" autocomplete="off" /></div>
                                                <div style="display:table-cell;">&nbsp;&nbsp;元</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-row">
                                        <label class="layui-form-label required">夜间</label>
                                        <div class="layui-input-block">
                                            <div style="display:table;">
                                                <div style="display:table-cell;width:100px;">
                                                    <input type="text" class="layui-input v-null v-floatLimit" id="Roadside_Night" name="Roadside_Night" maxlength="5" placeholder="￥" autocomplete="off" />
                                                </div>
                                                <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                            </div>
                                        </div>
                                    </div>
                                    @*<div class="layui-row">
                                    <label class="layui-form-label required">优惠后全天24小时最高收费</label>
                                    <div class="layui-input-block">
                                    <div style="display:table;">
                                    <div style="display:table-cell;width:100px;">
                                    <input type="text" class="layui-input v-null v-floatLimit" id="Roadside_Max" name="Roadside_Max" maxlength="5" placeholder="￥" autocomplete="off" />
                                    </div>
                                    <div style="display:table-cell;width:85px;">&nbsp;&nbsp;元 </div>
                                    </div>
                                    </div>
                                    </div>*@
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="edit-label layui-col-xs1"></div>
                </div>
            </div>

        </div>

        <div class="customer-footer">
            <button class="layui-btn layui-btn-md" id="Save"><i class="fa fa-check"></i> 保存</button>
            <button class="layui-btn layui-btn-md layui-btn-warm" style="background-color: #f0ad4e !important;" id="Cancel"><i class="fa fa-times"></i> 取消</button>
        </div>
    </div>

    <script src="~/Static/js/jquery.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/bootstrap.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true" asp-append-version="true"></script>
    <script>
        var vType = localStorage.getItem("versionType");
        $("#ChargeRules_Type ul li").removeClass("layui-hide");
        if (vType == 'simple') {
                $("#ChargeRules_Type ul li[data-value=1]").addClass("layui-hide");
            } else {
                $("#ChargeRules_Type ul li").removeClass("select");
                $("#ChargeRules_Type ul li[data-value=2]").addClass("select");
                $("#ChargeRules_Type ul li[data-value=0]").addClass("layui-hide");
            }


        var config = {
            ChargeRules_Type: 2,
            Biz_Level_1: 0,
            Biz_Level_2: 0,
            Social_Level_1: 0,
            Social_Level_2: 0,
            Traffic_Level_1: 0,
            Traffic_Level_2: 0,
            Public_Level_2: 0,
            His_Level_1: 0,
            His_Level_2: 0,
            Roadside_Level_1: 0,
            Roadside_Level_2: 0
        };
        $(function () {
            $(".btnCombox ul li").click(function () {
                if ($(this).hasClass("select")) return;
                var idName = $(this).parent().parent().attr("id");

                $(this).siblings().removeClass("select");
                $(this).addClass("select");
                config[idName] = $(this).attr("data-value");

                if (idName == "ChargeRules_Type") {
                    if ($(this).attr("data-value") == "0") { layer.msg("跳转中", { icon: 16, time: 0 }); window.location.href = "StandardRule"; }
                    if ($(this).attr("data-value") == "1") { layer.msg("跳转中", { icon: 16, time: 0 }); window.location.href = "TempRule"; }
                    if ($(this).attr("data-value") == "2") { layer.msg("跳转中", { icon: 16, time: 0 }); window.location.href = "YinChuanRule"; }
                }
            });
        });
    </script>
    <script type="text/javascript">
        var paramChargeRulesNo = $.getUrlParam("ChargeRules_No");
        var layuiForm = null;
        var xmSelect = null;
        var laydate = null;
        var element = null;

        var selParkArea = null;
        var selCarType = null;
        var selCarCardType = null;
        var index = parent.layer.getFrameIndex(window.name);
        var now = new Date($.ajax({ async: false }).getResponseHeader("Date"));
        layui.config({
            base: '../Static/admin/'
        }).extend({
            xmSelect: '/layui/lay/modules/xm-select'
        }).use(['element', 'form', 'xmSelect', 'laydate'], function () {
            layuiForm = layui.form;
            xmSelect = layui.xmSelect;
            laydate = layui.laydate;
            element = layui.element;

            layui.laydate.render({
                elem: '#ChargeRules_Time', range: true, done: function (value, date, endDate) {
                    pager.ChargeRules_BeginTime = new Date(date.year, date.month - 1, date.date).Format("yyyy-MM-dd");
                    pager.ChargeRules_EndTime = new Date(endDate.year, endDate.month - 1, endDate.date).Format("yyyy-MM-dd");
                }
            });
            layui.laydate.render({
                elem: '#Section_White_Time,#Section_Night_Time'
                , type: 'time'
                , range: true
            });

            pager.ChargeRules_BeginTime = now.Format("yyyy-MM-dd");
            pager.ChargeRules_EndTime = new Date(now.setFullYear(now.getFullYear() + 10)).Format("yyyy-MM-dd");
            $("#ChargeRules_Time").val(pager.ChargeRules_BeginTime + " - " + pager.ChargeRules_EndTime);

            pager.init();
        })

        var pager = {
            ChargeRules_BeginTime: null,
            ChargeRules_EndTime: null,
            ChargeRules_No: null,
            parkAreas: null,     //通道列表
            carCardTypes: null, //车牌类型列表
            carTypes: null,     //车牌颜色列表
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindData();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {

                $.post("GetSelectData", {}, function (json) {
                    if (json.success) {
                        pager.carCardTypes = json.data.carCardTypes;
                        pager.carTypes = json.data.carTypes;
                        pager.parkAreas = json.data.parkAreas;

                        if (pager.carTypes && pager.carTypes.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.carTypes.length; i++) {
                                data[i] = {
                                    "name": pager.carTypes[i].CarType_Name,
                                    "value": pager.carTypes[i].CarType_No
                                };
                            }
                            selCarType = xmSelect.render({
                                el: '#BillRuleTemp_CarType',
                                name: 'BillRuleTemp_CarType',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        if (pager.carCardTypes && pager.carCardTypes.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.carCardTypes.length; i++) {
                                data[i] = {
                                    "name": pager.carCardTypes[i].CarCardType_Name,
                                    "value": pager.carCardTypes[i].CarCardType_No
                                };
                            }
                            selCarCardType = xmSelect.render({
                                el: '#BillRuleTemp_CarCardType',
                                name: 'BillRuleTemp_CarCardType',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }
                        if (pager.parkAreas && pager.parkAreas.length > 0) {
                            var data = [];
                            for (var i = 0; i < pager.parkAreas.length; i++) {
                                data[i] = {
                                    "name": pager.parkAreas[i].ParkArea_Name,
                                    "value": pager.parkAreas[i].ParkArea_No
                                };
                            }
                            selParkArea = xmSelect.render({
                                el: '#BillRuleTemp_ParkArea',
                                name: 'BillRuleTemp_ParkArea',
                                layVerify: 'required',
                                layVerType: 'msg',
                                autoRow: true,
                                toolbar: { show: true },
                                data: data
                            })
                        }

                        layuiForm.render("select")
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            },
            bindData: function () {
                if (paramChargeRulesNo) {
                    $("#ChargeRules_Type ul li").removeClass("layui-hide");
                    $("#ChargeRules_Type ul li[data-value!=2]").addClass("layui-hide");
                }
                //获取计费规则数据
                var rules = pager.getRulesData();
            },
            bindEvent: function () {

                //保存规则
                $("#Save").click(function () {
                    var scene = $(".layui-tab-title li.layui-this").attr("data-value");
                    if (scene == 0) {

                    }
                    if (!myVerify.check()) return;
                    layer.msg("处理中", { icon: 16, time: 0 });
                    var list = pager.saveRules();
                    console.log(JSON.stringify(list));
                    var action = paramChargeRulesNo ? "UpdateChargeRules" : "AddChargeRules";
                    $.post(action, { jsonModel: JSON.stringify(list), rule_type: 2 }, function (json) {
                        $("#Save").removeAttr("disabled");
                        if (json.success) {
                            layer.msg("保存成功", { icon: 1, time: 1500 }, function () {
                                window.parent.pager.bindData(1);
                            });
                        } else {
                            var retMsg = json.msg.replace(/#/g, "<br/>");

                            layer.msg(retMsg, { icon: 0, btn: ['确定'], time: 0 });
                        }
                    }, "json");
                });

                $("#Cancel").click(function () {
                    parent.layer.close(index);
                });

                $(".basetitle").unbind("click").click(function () {
                    if ($(".basesetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".basesetting").toggle("normal");
                });
                $(".sectiontitle").unbind("click").click(function () {
                    if ($(".sectionsetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".sectionsetting").toggle("normal");
                });
                $(".hourtitle").unbind("click").click(function () {
                    if ($(".hoursetting").is(":hidden")) {
                        $(this).find("i").removeClass("layui-icon-up").addClass("layui-icon-down")
                    } else {
                        $(this).find("i").removeClass("layui-icon-down").addClass("layui-icon-up")
                    }
                    $(".hoursetting").toggle("normal");
                });
            },
            bindPower: function () {
                window.parent.parent.global.getBtnPower(window, function (pagePower) {
                    if (pagePower['Save']) {
                        $("button.save").removeClass("layui-hide");
                    }
                });
            },
            //保存计费规则
            saveRules: function () {
                var ChargeRules_CarCardTypeNo = selCarCardType.getValue('value');
                var ChargeRules_CarTypeNo = selCarType.getValue('value');
                var ChargeRules_ParkAreaNo = selParkArea.getValue('value');

                var ChargeRules_Name = $("#ChargeRules_Name").val();
                var ChargeRules_Remark = $("#ChargeRules_Remark").val();
                var ChargeRules_OverTime = $("#ChargeRules_OverTime").val();
                var ChargeRules_FreeMin = $("#ChargeRules_FreeMin").val();  //免费分钟

                var ResultModel = {};
                ResultModel.Logic_Sections = [];
                ResultModel.ChargeRules_FreeMin = ChargeRules_FreeMin;
                ResultModel.Section_ChargeMode = $(".layui-tab-title li.layui-this").attr("data-value");
                ResultModel.Section_White_Time = $("#Section_White_Time").val();
                ResultModel.Section_Night_Time = $("#Section_Night_Time").val();
                if (ResultModel.Section_ChargeMode == 0) {
                    ResultModel.Logic_Level_1 = config.Biz_Level_1;
                    ResultModel.Logic_Level_2 = config.Biz_Level_2;
                    ResultModel.Logic_White_1 = $("#Biz_White_1").val();
                    ResultModel.Logic_White_2 = $("#Biz_White_2").val();
                    ResultModel.Logic_Night = $("#Biz_Night").val();
                    ResultModel.Logic_Max = $("#Biz_Max").val();
                }
                else if (ResultModel.Section_ChargeMode == 1) {
                    ResultModel.Logic_Level_1 = config.Social_Level_1;
                    ResultModel.Logic_Level_2 = config.Social_Level_2;
                    ResultModel.Logic_White_0 = $("#Social_White_0").val();
                    ResultModel.Logic_White_1 = $("#Social_White_1").val();
                    ResultModel.Logic_White_2 = $("#Social_White_2").val();
                    ResultModel.Logic_White_3 = $("#Social_White_3").val();
                    ResultModel.Logic_Night = $("#Social_Night").val();
                    ResultModel.Logic_Max = $("#Social_Max").val();
                }
                else if (ResultModel.Section_ChargeMode == 2) {
                    ResultModel.Logic_Level_1 = config.Traffic_Level_1;
                    ResultModel.Logic_Level_2 = config.Traffic_Level_2;
                    ResultModel.Logic_White_1 = $("#Traffic_White_1").val();
                    ResultModel.Logic_White_2 = $("#Traffic_White_2").val();
                    ResultModel.Logic_Night = $("#Traffic_Night").val();
                    ResultModel.Logic_Max = $("#Traffic_Max").val();
                }
                else if (ResultModel.Section_ChargeMode == 3) {
                    ResultModel.Logic_Level_2 = config.Public_Level_2;
                    ResultModel.Logic_White_1 = $("#Public_White_1").val();
                    ResultModel.Logic_White_2 = $("#Public_White_2").val();
                    ResultModel.Logic_Night = $("#Public_Night").val();
                    ResultModel.Logic_Max = $("#Public_Max").val();
                }
                else if (ResultModel.Section_ChargeMode == 4) {
                    ResultModel.Logic_Level_1 = config.His_Level_1;
                    ResultModel.Logic_Level_2 = config.His_Level_2;
                    ResultModel.Logic_White_1 = $("#His_White_1").val();
                    ResultModel.Logic_White_2 = $("#His_White_2").val();
                    ResultModel.Logic_Night = $("#His_Night").val();
                    ResultModel.Logic_Max = $("#His_Max").val();
                }
                else if (ResultModel.Section_ChargeMode == 5) {
                    ResultModel.Logic_Level_1 = config.Roadside_Level_1;
                    ResultModel.Logic_Level_2 = config.Roadside_Level_2;
                    ResultModel.Logic_White_1 = $("#Roadside_White_1").val();
                    ResultModel.Logic_White_2 = $("#Roadside_White_2").val();
                    ResultModel.Logic_Night = $("#Roadside_Night").val();
                    ResultModel.Logic_Max = $("#Roadside_Max").val();
                }

                return {
                    ChargeRules_No: paramChargeRulesNo,
                    ChargeRules_CarCardTypeNo: ChargeRules_CarCardTypeNo,
                    ChargeRules_CarTypeNo: ChargeRules_CarTypeNo,
                    ChargeRules_ParkAreaNo: ChargeRules_ParkAreaNo,
                    ChargeRules_JsonData: ResultModel,
                    ChargeRules_BeginTime: pager.ChargeRules_BeginTime,
                    ChargeRules_EndTime: pager.ChargeRules_EndTime,
                    ChargeRules_Name: ChargeRules_Name,
                    ChargeRules_OverTime: ChargeRules_OverTime,
                    ChargeRules_Remark: ChargeRules_Remark,
                    ChargeRules_Type: 2
                };
            },

            //获取计费规则数据
            getRulesData: function () {

                if (!paramChargeRulesNo) {
                    layuiForm.render("select");
                    return;
                }

                $(".custom").addClass("layui-hide");

                $.post("GetChargeRules", { ChargeRules_No: paramChargeRulesNo }, function (json) {
                    if (json.success) {
                        var data = json.data;
                        if (data) {

                            $("#ChargeRules_Name").val(data.ChargeRules_Name);
                            $("#ChargeRules_Remark").val(data.ChargeRules_Remark);

                            if (data.ChargeRules_BeginTime && data.ChargeRules_EndTime)
                                $("#ChargeRules_Time").val(new Date(data.ChargeRules_BeginTime).Format("yyyy-MM-dd") + " - " + new Date(data.ChargeRules_EndTime).Format("yyyy-MM-dd"));

                            $("#BillRuleTemp_CarCardType").val(data.ChargeRules_CarCardTypeNo);
                            $("#BillRuleTemp_CarType").val(data.ChargeRules_CarTypeNo);
                            $("#BillRuleTemp_ParkArea").val(data.ChargeRules_ParkAreaNo);
                            $("#ChargeRules_OverTime").val(data.ChargeRules_OverTime);

                            $("#ChargeRules_Type ul li").removeClass("select");
                            $("#ChargeRules_Type ul li[data-value=" + data.ChargeRules_Type + "]").addClass("select");

                            if (data.ChargeRules_CarCardTypeNo && data.ChargeRules_CarCardTypeNo != "") {
                                if (data.ChargeRules_CarCardTypeNo.indexOf('[') == -1) { data.ChargeRules_CarCardTypeNo = '["' + data.ChargeRules_CarCardTypeNo + '"]'; }
                                selCarCardType.setValue(JSON.parse(data.ChargeRules_CarCardTypeNo))
                            }
                            if (data.ChargeRules_CarTypeNo && data.ChargeRules_CarTypeNo != "") {
                                selCarType.setValue(JSON.parse(data.ChargeRules_CarTypeNo))
                            }
                            if (data.ChargeRules_ParkAreaNo && data.ChargeRules_ParkAreaNo != "") {
                                selParkArea.setValue(JSON.parse(data.ChargeRules_ParkAreaNo))
                            }

                            layuiForm.render("select");

                            var d = data ? $.parseJSON(data.ChargeRules_JsonData) : {};
                            $("#ChargeRules_FreeMin").val(d.ChargeRules_FreeMin);
                            $("#Section_White_Time").val(d.Section_White_Time);
                            $("#Section_Night_Time").val(d.Section_Night_Time);
                            element.tabChange('tab-filter', d.Section_ChargeMode);
                            if (d.Section_ChargeMode == 0) {
                                config.Biz_Level_1 = d.Logic_Level_1;
                                config.Biz_Level_2 = d.Logic_Level_2;
                                $("#Biz_White_1").val(d.Logic_White_1);
                                $("#Biz_White_2").val(d.Logic_White_2);
                                $("#Biz_Night").val(d.Logic_Night);
                                $("#Biz_Max").val(d.Logic_Max);
                            }
                            else if (d.Section_ChargeMode == 1) {
                                config.Social_Level_1 = d.Logic_Level_1;
                                config.Social_Level_2 = d.Logic_Level_2;
                                $("#Social_White_0").val(d.Logic_White_0);
                                $("#Social_White_1").val(d.Logic_White_1);
                                $("#Social_White_2").val(d.Logic_White_2);
                                $("#Social_White_3").val(d.Logic_White_3);
                                $("#Social_Night").val(d.Logic_Night);
                                $("#Social_Max").val(d.Logic_Max);
                            }
                            else if (d.Section_ChargeMode == 2) {
                                config.Traffic_Level_1 = d.Logic_Level_1;
                                config.Traffic_Level_2 = d.Logic_Level_2;
                                $("#Traffic_White_1").val(d.Logic_White_1);
                                $("#Traffic_White_2").val(d.Logic_White_2);
                                $("#Traffic_Night").val(d.Logic_Night);
                                $("#Traffic_Max").val(d.Logic_Max);
                            }
                            else if (d.Section_ChargeMode == 3) {
                                config.Public_Level_2 = d.Logic_Level_2;
                                $("#Public_White_1").val(d.Logic_White_1);
                                $("#Public_White_2").val(d.Logic_White_2);
                                $("#Public_Night").val(d.Logic_Night);
                                $("#Public_Max").val(d.Logic_Max);
                            }
                            else if (d.Section_ChargeMode == 4) {
                                config.His_Level_1 = d.Logic_Level_1;
                                config.His_Level_2 = d.Logic_Level_2;
                                $("#His_White_1").val(d.Logic_White_1);
                                $("#His_White_2").val(d.Logic_White_2);
                                $("#His_Night").val(d.Logic_Night);
                                $("#His_Max").val(d.Logic_Max);
                            }
                            else if (d.Section_ChargeMode == 5) {
                                config.Roadside_Level_1 = d.Logic_Level_1;
                                config.Roadside_Level_2 = d.Logic_Level_2;
                                $("#Roadside_White_1").val(d.Logic_White_1);
                                $("#Roadside_White_2").val(d.Logic_White_2);
                                $("#Roadside_Night").val(d.Logic_Night);
                                $("#Roadside_Max").val(d.Logic_Max);
                            }
                        } else {

                        }

                        $(".btnCombox").each(function () {
                            var idName = $(this).attr("id");
                            $(this).find("li").removeClass("select");
                            $(this).find("ul li[data-value=" + config[idName] + "]").addClass("select");

                        });
                    } else {
                        layer.msg(json.msg);
                    }
                }, "json");
            }


        }

        //获取规则JSON
        var Rules = {

        }
    </script>


</body>
</html>
