﻿
<!DOCTYPE html>

<html>
<head>
    <meta charset="utf-8">
    <title>数据分发</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <link href="~/Static/css/jquery.verify.css?334" rel="stylesheet" />
    <style>
        .fa { margin: 7px 4px 0 0; float: left; font-size: 16px; }

        .layui-form-select .layui-input { width: 182px; }

        .content { margin: 7px 19px 0px 19px !important; cursor: pointer; }

        .content:hover { color: #1E9FFF; font-weight: 600; font-size: 20px; }

        .desc { display: block; font-size: 14px; font-weight: bolder; color: #f18042; text-align: center; width: calc(100% - 1px); }
        span.ss { font-size: 13px; text-align: justify; word-break: break-all; color: #61a8d1; background-color: #f5eeee; float: left; padding: 3px 5px; }
        @@media screen and (max-width: 1024px) {
            .layui-layer-setwin .layui-layer-close2 { right: -18px !important; top: -18px !important; }
        }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>记录查询</cite></a>
                <a><cite>识别未支付</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header  layui-form">
                        <div class="test-table-reload-btn" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input " name="UnpaidRecord_CarNo" id="UnpaidRecord_CarNo" autocomplete="off" placeholder="车牌号" maxlength="8" />
                            </div>
                            
                            <div class="layui-inline">
                                <select name="UnpaidRecord_PasswayNo" id="UnpaidRecord_PasswayNo" lay-search>
                                    <option value="">所有车道</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="startDate" id="startDate" autocomplete="off" placeholder="识别起" value="" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input" name="endDate" id="endDate" autocomplete="off" placeholder="识别止" />
                            </div>
                            <div class="layui-inline">
                                <div class="operabar-if">更多条件</div>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="Search"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>


                            <div class="layui-row search-more layui-hide">
                                <div class="layui-inline">
                                    <select data-placeholder="车牌类型" class="form-control chosen-select " id="UnpaidRecord_CarCardTypeNo" name="UnpaidRecord_CarCardTypeNo" lay-search>
                                        <option value="">车牌类型</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <select data-placeholder="车牌颜色" class="form-control chosen-select " id="UnpaidRecord_CarTypeNo" name="UnpaidRecord_CarTypeNo" lay-search>
                                        <option value="">车牌颜色</option>
                                    </select>
                                </div>
                                <div class="layui-inline">
                                    <select data-placeholder="识别方式" class="form-control chosen-select " id="UnpaidRecord_Mode" name="UnpaidRecord_Mode" lay-search>
                                        <option value="">识别方式</option>
                                        <option value="1">相机识别</option>
                                        <option value="2">扫码</option>
                                        <option value="3">ETC</option>
                                        <option value="4">刷卡</option>
                                        <option value="6">输入车牌</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="layui-card-body">

                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base">
                        </table>

                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                <button class="layui-btn layui-btn-sm layui-hide" id="Export" lay-event="Export"><i class="fa fa-download"></i><t>导出</t></button>
                                <button class="layui-btn layui-btn-sm layui-hide" id="Detail" lay-event="Detail"><i class="fa fa-list-alt"></i><t>详情</t></button>

                            </div>
                        </script>
                    </div>

                </div>
            </div>
        </div>

    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.download.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script type="text/x-jquery-tmpl" id="tmplmode">
        {{# if(d.UnpaidRecord_Mode==1){}}
        <span class="layui-badge layui-bg-blue">相机识别</span>
        {{# }else if(d.UnpaidRecord_Mode==2){ }}
        <span class="layui-badge layui-bg-green">扫码</span>
        {{# }else if(d.UnpaidRecord_Mode==3){ }}
        <span class="layui-badge layui-bg-blue">ETC</span>
        {{# }else if(d.UnpaidRecord_Mode==4){ }}
        <span class="layui-badge layui-bg-green">刷卡</span>
        {{# }else if(d.UnpaidRecord_Mode==6){ }}
        <span class="layui-badge layui-bg-red">输入车牌</span>
        {{# } }}
    </script>

    <script>
        myVerify.init();
        topBar.init();

        var Power = window.parent.global.formPower;
        var comtable = null;

        layui.use(['table', 'form'], function () {
            pager.init();
            var table = layui.table;
            var layuiForm = layui.form;
            layuiForm.render("select");

            $("#startDate").val(new Date().Format("yyyy-MM-dd"));
            $("#endDate").val(new Date().Format("yyyy-MM-dd"));
            _DATE.bind(layui.laydate, ["startDate", "endDate"], { type: 'date', range: true });
            var cols = [[
                { type: 'radio' }
                , { field: 'UnpaidRecord_No', title: '数据编码', hide: true }
                , { field: 'UnpaidRecord_CarNo', title: '车牌号' }
                , { field: 'UnpaidRecord_CarCardTypeName', title: '车牌类型' }
                , { field: 'UnpaidRecord_CarTypeName', title: '车牌颜色' }
                , { field: 'UnpaidRecord_PasswayName', title: '车道名称' }
                , { field: 'UnpaidRecord_RecogTime', title: '识别时间' }
                , { field: 'UnpaidRecord_Mode', title: '识别方式', toolbar: "#tmplmode" }
                , { field: 'UnpaidRecord_PayedAmount', title: '计费金额' }
                , { field: 'UnpaidRecord_ParkOrderNo', title: '停车订单编码', hide: true }
            ]];
            cols = tb_page_cols(cols);

            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
            comtable = table.render({
                elem: '#com-table-base'
                , url: '/UnpaidRecord/GetList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50]
                , done: function (d) {
                    // 获取当前日期
                    var currentDate = new Date();

                    // 获取当前年份和月份
                    var year = currentDate.getFullYear();
                    var month = currentDate.getMonth() + 1; // 月份从0开始，需要+1

                    // 获取本月的最后一天
                    var lastDayOfCurrentMonth = new Date(year, month, 0).getDate();
                    var endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDayOfCurrentMonth}`;

                    // 获取两个月前的第一天
                    var twoMonthsAgo = new Date(year, month - 2, 1);
                    var startYear = twoMonthsAgo.getFullYear();
                    var startMonth = twoMonthsAgo.getMonth() + 1;
                    var startDate = `${startYear}-${startMonth.toString().padStart(2, '0')}-01`;

                    // 动态插入描述信息
                    $(".layui-table-view .layui-table-box .desc").remove();
                    $(".layui-table-view .layui-table-box").prepend('<div class="desc">识别未支付记录仅保留近两个月的数据：' + startDate + ' ~ ' + endDate + '</div>');

                    tb_page_set(d);
                    pager.bindPower();
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                pager.pageIndex = $(".layui-laypage-curr").text();
                switch (obj.event) {
                    case 'Export':
                        var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                        if (pager.dataCount > 30000 || JSON.stringify(pager.conditionParam) != JSON.stringify(conditionParam)) {
                            if (conditionParam.startDate == null || conditionParam.endDate == null) { layer.msg("请选择识别起止时间", { icon: 0 }); return; }
                            if (_DATE.diffDay(new Date(conditionParam.startDate), new Date(conditionParam.endDate)) > 31) { layer.msg("限制导出一个月内的数据", { icon: 0 }); return; }
                        }
                        var field = pager.sortField == null ? "" : pager.sortField;
                        var order = pager.orderField == null ? "" : pager.orderField;

                        pager.dataField = [];
                        obj.config.cols[0].forEach((item) => {
                            if (item.title)
                                pager.dataField.push({ name: item.title, value: item.field, chk: !item.hide });
                        });
                        layer.open({
                            id: "x_edit_iframe",
                            type: 2,
                            title: "导出数据（<span style='color:red;font-weight:800;'>请勾选左边框要导出的字段，右边框字段可用鼠标按住拖拽调整导出的字段顺序</span>）",
                            content: '/ExportExcel/Index?Act=Update&Owner_No=',
                            area: getIframeArea(['1100px', '400px']),
                            maxmin: false,
                            end: function () {
                                if (pager.dataField && pager.dataField != null && pager.dataField != "" && pager.dataField.length > 0) {
                                    var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                                        return data;
                                    });
                                    conditionParam.SearchType = topBar.config.SearchType;

                                    //实现Ajax下载文件
                                    $.fileDownload('/UnpaidRecord/Export?' + "conditionParam=" + JSON.stringify(conditionParam) + "&chkfield=" + JSON.stringify(pager.dataField), {
                                        httpMethod: 'GET',
                                        headers: {},
                                        data: null,
                                        prepareCallback: function (url) {
                                            $("#Export").attr("disabled", true);
                                            layer.msg('正在导出，请稍候...', { icon: 16, time: 0 });
                                        },
                                        successCallback: function (url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出成功');
                                        },
                                        failCallback: function (html, url) {
                                            $("#Export").attr("disabled", false);
                                            layer.msg('导出失败', { icon: 0, time: 0, btn: ['确定'] });
                                        }
                                    });
                                }
                            }
                        });
                        break;
                    case 'Detail':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            title: false,
                            type: 2, id: 1,
                            area: ['750px', '380px'],
                            content: 'Detail?UnpaidRecord_ID=' + data[0].UnpaidRecord_ID + '&UnpaidRecord_RecogTime=' + data[0].UnpaidRecord_RecogTime
                        });
                        break;
                };
            });

            tb_row_radio(table)
        });
    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                this.bindPower();
                this.bindSelect();
                this.bindEvent();
                $.ajaxSettings.async = true;
            },
            bindSelect: function () {
                $.post("SltCarCardTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarCardType_No + '">' + d.CarCardType_Name + '</option>';
                            $("#UnpaidRecord_CarCardTypeNo").append(option)
                        });
                    }
                }, "json");
                $.post("SltPasswayList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (item, index) {
                            var option = '<option value="' + item.Passway_No + '">' + item.Passway_Name + '</option>';
                            $("#UnpaidRecord_PasswayNo").append(option);
                        });
                        layui.form.render("select");
                    }
                }, "json");
                $.post("SltCarTypeList", {}, function (json) {
                    if (json.success) {
                        json.data.forEach(function (d, i) {
                            var option = '<option value="' + d.CarType_No + '">' + d.CarType_Name + '</option>';
                            $("#UnpaidRecord_CarTypeNo").append(option)
                        });
                    }
                }, "json");
                layui.form.render();
            },
            //重新加载数据
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) {
                    return data;
                });

                if (conditionParam.startDate == null || conditionParam.endDate == null) { layer.msg("请选择识别起止时间", { icon: 0 }); return; }
                // 检查跨月查询限制
                var startDate = new Date(conditionParam.startDate);
                var endDate = new Date(conditionParam.endDate);
                if (startDate.getFullYear() !== endDate.getFullYear() || startDate.getMonth() !== endDate.getMonth()) {
                    layer.msg("不支持跨月查询，请选择同一个月份内的日期范围", { icon: 0 });
                    return;
                }

                comtable.reload({
                    url: '/UnpaidRecord/GetList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#Search").click(function () { pager.bindData(1); });
                s_carno_picker.init("UnpaidRecord_CarNo", function (text, carno) { }, "web").bindkeyup();
            },
            bindPower: function () {
                window.parent.global.getBtnPower(window, function (pagePower) { });
            }
        }
    </script>
</body>
</html>
