﻿
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>车牌类型名管理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css" rel="stylesheet" />
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css?v3.5" rel="stylesheet">
    <style>
        .fa { margin: 6px 4px; float: left; font-size: 16px; }
        .layui-form-select .layui-input { width: 182px; }
    </style>
</head>
<body>

    <div class="layui-fluid animated fadeInRight">
        <div class="layui-card layadmin-header height0"></div>
        <div class="layui-card layadmin-header">
            <div class="layui-breadcrumb" lay-filter="breadcrumb">
                <a><cite>车场管理</cite></a>
                <a><cite>车牌类型</cite></a>
            </div>
        </div>
        <div class="layui-row layui-col-space30">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <div class="test-table-reload-btn layui-form" id="searchForm">
                            <div class="layui-inline">
                                <input class="layui-input " name="CarCardType_No" id="CarCardType_No" autocomplete="off" placeholder="车牌类型编码" />
                            </div>
                            <div class="layui-inline">
                                <input class="layui-input " name="CarCardType_Name" id="CarCardType_Name" autocomplete="off" placeholder="车牌类型名称" />
                            </div>
                            <div class="layui-inline">
                                <select data-placeholder="车牌分类" class="layui-select" id="CarCardType_Category" name="CarCardType_Category" lay-search>
                                    <option value="">车牌分类</option>
                                </select>
                            </div>
                            <div class="layui-inline">
                                <button class="layui-btn" id="BtnSearch"><i class="layui-icon layui-icon-search inbtn"></i><t>搜索</t></button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body">
                        <table class="layui-hide" id="com-table-base" lay-filter="com-table-base"></table>
                        <script type="text/html" id="toolbar_btns">
                            <div class="layui-btn-container">
                                {{# if(Power.CarCardType.Add){}}<button class="layui-btn layui-btn-sm" lay-event="Add"><i class="fa fa-plus"></i><t>新增</t></button>{{# } }}
                                {{# if(Power.CarCardType.Update){}}<button class="layui-btn layui-btn-sm" lay-event="Update"><i class="fa fa-edit"></i><t>编辑</t></button>{{# } }}
                                {{# if(Power.CarCardType.Delete){}}<button class="layui-btn layui-btn-sm" lay-event="Delete"><i class="fa fa-trash-o"></i><t>删除</t></button>{{# } }}
                            </div>
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="TmplCategory">
        {{# if(d.CarCardType_Category==1){ }}
        <span class="layui-badge layui-bg-orange">临停类型</span>
        {{# }else if(d.CarCardType_Category==2){ }}
        <span class="layui-badge layui-bg-green">储值类型</span>
        {{# }else if(d.CarCardType_Category==3){ }}
        <span class="layui-badge layui-bg-blue">月租类型</span>
        {{# }else if(d.CarCardType_Category==4){ }}
        <span class="layui-badge layui-bg-gray">免费类型</span>
        {{# }else if(d.CarCardType_Category==5){ }}
        <span class="layui-badge layui-bg-black">访客类型</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplIsMoreCar">
        {{# if(d.CarCardType_IsMoreCar==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>
    <script type="text/html" id="TmplIsDefault">
        {{# if(d.CarCardType_IsDefault==1){ }}
        <span class="layui-badge layui-bg-blue ">是</span>
        {{# }else{ }}
        <span class="layui-badge layui-bg-orange ">否</span>
        {{# } }}
    </script>
    <script type="text/x-jquery-tmpl" id="tmplcartype">
        <option value="${VehicleType_No}">${VehicleType_Name}</option>
    </script>
    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>

    <script>
        var Power = window.parent.global.formPower;
        var comtable = null;
        var layuiForm = null;
        layui.use(['table', 'jquery', 'form'], function () {
            var admin = layui.admin, table = layui.table;
            layuiForm = layui.form;
            var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });

            var cols = [[
                { type: 'radio' }
                , { field: 'CarCardType_No', title: '车牌类型编码', hide: true }
                , { field: 'CarCardType_Name', title: '车牌类型名称' }
                , { field: 'CarCardType_CategoryName', title: '车牌分类' }
                , { field: 'CarCardType_IsMoreCar', title: '用于一位多车', toolbar: '#TmplIsMoreCar' }
                , {
                    field: 'CarCardType_WhiteEnable', title: '下发白名单', templet: function (d) {
                        if (d.CarCardType_WhiteEnable == 1) {
                            return '<span class="layui-badge layui-bg-blue ">启用</span>';
                        } else {
                            return ' <span class="layui-badge layui-bg-orange ">禁用</span>';
                        }
                    }
                }
                , { field: 'CarCardType_IsDefault', title: '系统数据', toolbar: '#TmplIsDefault' }
            ]];

            cols = tb_page_cols(cols);

            comtable = table.render({
                elem: '#com-table-base'
                , url: '/CarCardType/GetCarCardTypeList'
                , method: 'post'
                , toolbar: '#toolbar_btns'
                , defaultToolbar: ["filter"]
                , cols: cols
                , page: {
                    layout: ['count', 'prev', 'page', 'next', 'skip', 'limit'],
                    groups: 3,
                }
                , request: { pageName: 'pageIndex', limitName: 'pageSize' }
                , where: { conditionParam: JSON.stringify(conditionParam) }
                , limit: tb_page_limit(), limits: [10, 20, 50, 100]
                , done: function (d) {
                    tb_page_set(d);
                }
            });

            //头工具栏事件
            table.on('toolbar(com-table-base)', function (obj) {
                var checkStatus = table.checkStatus(obj.config.id); //获取选中行状态
                var data = checkStatus.data;  //获取选中行数据
                var pageIndex = $(".layui-laypage-curr").text();
                pager.pageIndex = parseInt(pageIndex);
                switch (obj.event) {
                    case 'Add':
                        layer.open({
                            type: 2, id: 1,
                            title: "新增",
                            content: 'Edit?Act=Add',
                            area: getIframeArea(['600px', '630px']),
                            maxmin: true
                        });
                        break;
                    case 'Update':
                        if (data.length == 0) { layer.msg("请选择"); return; }
                        layer.open({
                            type: 2, id: 1,
                            title: "编辑",
                            content: 'Edit?Act=Update&CarCardType_No=' + data[0].CarCardType_No,
                            area: getIframeArea(['600px', '630px']),
                            maxmin: true
                        });
                        break;
                    case 'Delete':
                        if (data.length == 0) { layer.msg("请选择"); return; }

                        var conText = '删除后造成影响：<br/>';
                        conText += '1.会导致关联的场内车匹配不到该车牌类型,<t style="color: red;">禁止入/出场</t><br/>';
                        conText += '2.云平台推送下来的固定车找不到类型导致推送失败<br/>';
                        conText += '确认不再使用此车牌类型时进行删除<br/>';
                        conText += '确定删除吗?';

                        layer.open({
                            type: 0,
                            title: "消息提示",
                            btn: ["确定", "取消"],
                            content: conText,
                            yes: function (res) {
                                layer.msg("处理中", { icon: 16, time: 0 });
                                $.getJSON("Delete", { CarCardType_No: data[0].CarCardType_No }, function (json) {
                                    if (json.Success)
                                        layer.msg("删除成功", { icon: 1, time: 1500 }, function () { pager.bindData(pager.pageIndex); });
                                    else
                                        layer.msg(json.Message, { icon: 0, time: 1500 });
                                });
                            },
                            btn2: function () { }
                        })
                        break;
                };
            });

            tb_row_radio(table);

            pager.init();
        });

    </script>
    <script>
        var pager = {
            pageIndex: 1,
            init: function () {
                $.ajaxSettings.async = false;
                pager.bindSelect();
                pager.bindEvent();
                $.ajaxSettings.async = true;
            },
            //重新加载数据
            bindSelect: function () {
                $.post("SltVehicleTypeList", {}, function (json) {
                    $("#CarCardType_Category").html("<option value=''>车牌分类</option>")
                    if (json.success) {
                        $("#CarCardType_Category").append($("#tmplcartype").tmpl(json.data));
                    }
                    else {
                        layer.msg(json.msg);
                    }
                }, "json");
                layuiForm.render("select");
            },
            bindData: function (index) {
                layer.closeAll();
                var conditionParam = $("#searchForm").formToJSON(true, function (data) { return data; });
                comtable.reload({
                    url: '/CarCardType/GetCarCardTypeList'
                    , where: { conditionParam: JSON.stringify(conditionParam) } //设定异步数据接口的额外参数
                    , page: { curr: index }
                });
            },
            bindEvent: function () {
                $("#BtnSearch").click(function () { pager.bindData(1); });
            }
        }
    </script>
</body>
</html>
