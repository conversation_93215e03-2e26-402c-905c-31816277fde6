﻿@model Dictionary<string, string>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>车位余位管理</title>
    <link href="~/Static/css/bootstrap.min14ed.css?v=3.3.6" rel="stylesheet">
    <link href="~/Static/css/font-awesome.min93e3.css?v=4.4.0" rel="stylesheet">
    <link href="~/Static/css/animate.min.css" rel="stylesheet">
    <link href="~/Static/admin/layui/css/layui.css" rel="stylesheet">
    <link href="~/Static/admin/style/admin.css" rel="stylesheet" />
    <link href="~/Static/css/com.ui.css" rel="stylesheet" />
    <style>
        html, body { height: 100%; margin: 0; padding: 0; overflow: hidden; }
        body { font-family: Arial, sans-serif; display: flex; flex-direction: column; }
        .container { width: 100%; height: 100%; margin: 0; padding: 20px; display: flex; flex-direction: column; box-sizing: border-box; }
        .table-container { flex: 1; overflow-y: auto; margin: 0 auto; width: 100%; max-width: 800px; }
        .parking-table { width: 100%; border-collapse: collapse; margin: 0 auto; table-layout: fixed; }
        .parking-table th, .parking-table td { padding: 8px 5px; text-align: center; border: 1px solid #ddd; font-size: 14px; line-height: 1.4; }
        .parking-table th { background-color: #f5f5f5; font-weight: bold; position: sticky; top: 0; z-index: 1; }
        .parking-table input[type="number"] { width: 60px; padding: 4px; text-align: center; border: 1px solid #ddd; border-radius: 3px; }
        .parking-table input[type="number"]:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 2px rgba(0,123,255,.25); }
        .parking-table input[type="number"].error { border-color: #ff0000; }
        .button-container { margin-top: 20px; padding: 15px 0; border-top: 1px solid #eee; text-align: right; background-color: #fff; }
        button { padding: 8px 15px; background-color: #007bff; color: white; border: none; cursor: pointer; font-weight: 800 !important; margin-left: 10px; border-radius: 4px; }
        button:hover { background-color: #0056b3; }
        #Cancel { border: 1px solid #dedede; background-color: #fff; color: #333; }
        .message { margin-top: 10px; }
        .message-success { color: green; }
        .message-error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <div class="table-container">
            <table class="parking-table">
                <thead>
                    <tr>
                        <th style="width: 25%">区域</th>
                        <th style="width: 25%">车位</th>
                        <th style="width: 25%">余位</th>
                        <th style="width: 25%">修改余位</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        <div class="button-container">
            <button type="button" id="reSetBtn" style="background-color:#343925;">重置</button>
            <button type="button" id="saveBtn">保存</button>
            <button id="Cancel">取消</button>
        </div>
        <div id="messageArea" class="message"></div>
    </div>

    <script src="~/Static/js/jquery-3.3.1.min.js" asp-append-version="true"></script>
    <script src="~/Static/admin/layui/layui.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.common.js?t=@DateTime.Now.Ticks" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.verify.js" asp-append-version="true"></script>
    <script src="~/Static/js/jquery.tmpl.min.js" asp-append-version="true"></script>
    <script src="~/Static/plugins/carnopicker/carno.tools.min.js?v3.5.1" asp-append-version="true"></script>
    <script src="~/Static/js/pm.utils.js?v3.5" asp-append-version="true"></script>
    <script>
        $(function () {
            // 渲染车位数据
            function getCarSpaceData() {
                return new Promise(function (resolve, reject) {
                    // if (parent.window.lastCarSpaceData && parent.window.lastCarSpaceData != null) {
                    //     resolve(parent.window.lastCarSpaceData);
                    // } else {
                        $.ajax({
                            url: "GetCurrentCarSpace?r=" + Math.random(),
                            method: "POST",
                            dataType: "json",
                            success: function (json) {
                                if (json.success) {
                                    parent.window.lastCarSpaceData = json.data;
                                    resolve(json.data);
                                } else {
                                    layer.msg("获取车位信息失败", { time: 1500, icon: 0 });
                                    reject("failed");
                                }
                            },
                            error: function () {
                                layer.msg("获取车位信息异常", { time: 1500, icon: 0 });
                                reject("error");
                            }
                        });
                    // }
                });
            }

            getCarSpaceData().then(function (data) {
                if (parent.window.lastCarSpaceData === undefined || parent.window.lastCarSpaceData === null) {
                    $.ajaxSettings.async = false;
                    $.ajax({
                        url: "GetCurrentCarSpace?r=" + Math.random(),
                        method: "POST",
                        dataType: "json",
                        success: function (json) {
                            if (json.success) {
                                parent.window.lastCarSpaceData = json.data;
                            } else {
                                layer.msg("获取车位信息失败", { time: 1500, icon: 0 });
                            }
                        },
                        error: function () {
                            layer.msg("获取车位信息异常", { time: 1500, icon: 0 });
                        }
                    });
                }

                if (parent.window.lastCarSpaceData && parent.window.lastCarSpaceData != null) {
                    var tbody = $(".parking-table tbody");
                    tbody.empty();

                    // 渲染车位信息
                    parent.window.lastCarSpaceData.Item4.forEach(function (item) {
                        var tr = $("<tr>");
                        tr.append($("<td>").text(item.Item4)); // 区域
                        tr.append($("<td>").text(item.Item2)); // 车位
                        tr.append($("<td>").text(item.Item3)); // 余位
                        tr.append($("<td>").html(
                            $("<input>")
                                .attr("type", "number")
                                .attr("class", "remaining-space")
                                .attr("data-parking", item.Item4)
                                .attr("data-current", item.Item3)
                                .attr("data-total", item.Item2)
                                .attr("min", "0")
                                .val(item.Item3)
                        )); // 修改余位
                        tbody.append(tr);
                    });
                }
            });
            // 输入验证
            $(".parking-table").on("input", "input[type='number']", function () {
                var totalSpaces = parseInt($(this).data("total"));
                var newValue = parseInt($(this).val());

                if (newValue < 0) {
                    $(this).addClass("error");
                    layer.msg("修改余位数必须大于等于0", { time: 1500, icon: 0 });
                    $(this).val(0);
                } else {
                    if (newValue > totalSpaces) {
                        $(this).addClass("error");
                        layer.msg("修改余位数不能大于车位数", { time: 1500, icon: 0 });
                        $(this).val(totalSpaces);
                    } else {
                        $(this).removeClass("error");
                    }
                }
            });

            $("#Cancel").click(function () {
                parent.window.CloseIndex(parent.window.frmSpaceIndex);
            });

            $("#saveBtn").click(function () {
                var updates = [];
                var hasError = false;

                $(".remaining-space").each(function () {
                    var areaName = $(this).data("parking");
                    var currentValue = parseInt($(this).data("current"));
                    var newValue = parseInt($(this).val());
                    var totalSpaces = parseInt($(this).data("total"));

                    if (newValue < 0) {
                        hasError = true;
                        $(this).addClass("error");
                        layer.msg(areaName + "修改余位数必须大于等于0", { time: 1500, icon: 0 });
                        return false;
                    } else {
                        if (newValue > totalSpaces) {
                            hasError = true;
                            $(this).addClass("error");
                            layer.msg(areaName + "的修改余位数不能大于车位数", { time: 1500, icon: 0 });
                            return false;
                        }
                    }

                    // 只有当值发生变化时才添加到更新列表
                    if (newValue !== currentValue) {
                        updates.push({
                            areaName: areaName,
                            currentValue: currentValue,
                            newValue: newValue
                        });
                    }
                });

                if (hasError) {
                    return;
                }

                if (updates.length === 0) {
                    layer.msg("没有需要保存的修改", { time: 1500, icon: 0 });
                    return;
                }


                layer.confirm("确定变更余位数吗？", { icon: 3, title: '确认操作' }, function (index) {
                    layer.close(index); // 关闭确认框
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#saveBtn").attr("disabled", true);
                    $.ajax({
                        url: '/Monitoring/SaveParkingSpaces',
                        method: 'POST',
                        dataType: 'json',
                        data: { updates: JSON.stringify(updates) },
                        success: function (response) {
                            if (response.success) {
                                layer.msg("保存成功", { time: 1500, icon: 1 }, function () {
                                    parent.window.GetCurrentCarSpace();
                                    parent.window.CloseIndex(parent.window.frmSpaceIndex);
                                });
                            } else {
                                layer.msg(response.msg || "保存失败", { time: 1500, icon: 0 });
                            }
                        },
                        error: function () {
                            layer.msg("保存失败，请重试", { time: 1500, icon: 0 });
                        },
                        complete: function () {
                            $("#saveBtn").removeAttr("disabled");
                        }
                    });
                });
            });

            $("#reSetBtn").click(function () {
                layer.confirm("是否确认撤销余位数的更改，并将余位数重置为系统默认统计？", { icon: 3, title: '确认操作' }, function (index) {
                    layer.close(index); // 关闭确认框
                    layer.msg("处理中", { icon: 16, time: 0 });
                    $("#reSetBtn").attr("disabled", true);

                    $.ajax({
                        url: '/Monitoring/ReSetParkingSpaces',
                        method: 'POST',
                        dataType: 'json',
                        data: {},
                        success: function (response) {
                            if (response.success) {
                                layer.msg("重置成功", { time: 1500, icon: 1 }, function () {
                                    parent.window.GetCurrentCarSpace();
                                    parent.window.CloseIndex(parent.window.frmSpaceIndex);
                                });
                            } else {
                                layer.msg(response.msg || "重置失败", { time: 1500, icon: 0 });
                            }
                        },
                        error: function () {
                            layer.msg("重置失败，请重试", { time: 1500, icon: 0 });
                        },
                        complete: function () {
                            $("#reSetBtn").removeAttr("disabled");
                        }
                    });
                });
            });

        });
    </script>
</body>
</html>