﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using carparking.BLL;
using carparking.BLL.Cache;
using carparking.Common;
using carparking.Config;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NPOI.Util;

namespace carparking.Web.Controllers;

public class AdminsController : BaseController
{
    private readonly ResultBase rb = new();

    private readonly Model.PageResult wayModel = new();

    //
    // GET: /Admins/
    public ActionResult Index()
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.View.ToString(), false, true, lgAdmins))
            return new EmptyResult();

        return View();
    }

    public ActionResult Edit()
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.Update.ToString(), false, true, lgAdmins))
            return new EmptyResult();
        ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
        return View();
    }

    public ActionResult Passway()
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.Bind.ToString(), false, true, lgAdmins))
            return Ok(oModel);
        ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
        return View();
    }

    public ActionResult PasswayBind()
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.Bind.ToString(), false, true, lgAdmins))
            return Ok(oModel);
        ViewBag.Admins = lgAdmins ?? new Model.AdminSession();
        return View();
    }

    /// <summary>
    /// 管理员列表
    /// </summary>
    public void GetAdminsList(int pageIndex, int pageSize, string conditionParam)
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.Search.ToString(), adminSession: lgAdmins))
            return;

        Response.WriteAsync(SearchAdminsList(pageIndex, pageSize, conditionParam).ParseJson());
    }

    /// <summary>
    /// 查询管理员列表
    /// </summary>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <param name="conditionParam"></param>
    private Model.PageResult SearchAdminsList(int pageIndex, int pageSize, string conditionParam)
    {
        try
        {
            if (pageSize > 1000)
                return new Model.PageResult(-1, "", 0, null);

            var sqlwhere = "";
            var model = JsonConvert.DeserializeObject<Model.AdminsExt>(conditionParam);

            if (!string.IsNullOrEmpty(model.Admins_Name))
                sqlwhere += string.Format(" and Admins_Name like @Admins_Name ");
            if (!string.IsNullOrEmpty(model.Admins_Account))
                sqlwhere += string.Format("and Admins_Account like @Admins_Account ");
            if (!string.IsNullOrEmpty(model.Admins_Phone))
                sqlwhere += string.Format("and Admins_Phone like @Admins_Phone ");

            if (model.Admins_Enable != null)
                sqlwhere += string.Format("and Admins_Enable=@Admins_Enable ");
            else
                sqlwhere += " and (Admins_Enable=0 or Admins_Enable=1) ";

            object parameters = new
            {
                Admins_Name = !string.IsNullOrEmpty(model.Admins_Name) ? "%" + model.Admins_Name + "%" : null,
                Admins_Account = !string.IsNullOrEmpty(model.Admins_Account) ? "%" + model.Admins_Account + "%" : null,
                Admins_Phone = !string.IsNullOrEmpty(model.Admins_Phone) ? "%" + model.Admins_Phone + "%" : null,
                Admins_Enable = model.Admins_Enable
            };

            int pageCount = 0, totalRecord = 0;
            var lst = Admins.GetExtList("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord, parameters);

            #region 读取绑定的车道名称

            var pDatas = BLL.Passway.GetAllEntity("Passway_No,Passway_Name", $"Passway_ParkNo='{parking.Parking_No}'");
            foreach (var x in lst)
            {
                var wayNos = TyziTools.Json.ToObject<List<string>>(x.Admins_PasswayNo) ?? new List<string>();
                var xps = pDatas?.FindAll(p => wayNos.Contains(p.Passway_No)) ?? new List<Model.Passway>();

                x.BindPasswayName = string.Join(",", xps?.Select(x => x.Passway_Name));
            }

            #endregion

            oModel.code = 0;
            oModel.data = lst;
            oModel.count = totalRecord;
            return oModel;
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询管理员列表", "查询管理员列表发生异常:" + ex);

            oModel.code = 4;
            oModel.msg = "异常错误";
        }

        return oModel;
    }

    /// <summary>
    /// 获取用户信息
    /// </summary>
    public void GetAdmins(int Admins_ID)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Update.ToString(), adminSession: lgAdmins))
                return;

            var admins = Admins.GetEntity(Admins_ID) ?? new Model.Admins();
            admins.Admins_Pwd = null; //密码不返回给前端
            MiniResponse.ResponseResult("读取成功", true, admins);
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "查询管理员详情", "获取用户信息发生异常:" + ex);
            MiniResponse.ResponseResult("异常错误", false);
        }
    }

    /// <summary>
    /// 新增
    /// </summary>
    /// <param name="jsonModel"></param>
    public void AddAdmins(string jsonModel)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Add.ToString(), adminSession: lgAdmins))
                return;

            if (PubVar.iParkingType == 1) //特惠款
            {
                var list = Admins.GetAllEntity("Admins_Account", " 1=1 ");
                if (list != null && list.Count >= 10)
                {
                    MiniResponse.ResponseResult("特惠款最多添加10个帐号", false);
                    return;
                }
            }

            var model = Utils.ClearModelRiskSQL<Model.Admins>(jsonModel);

            if (string.IsNullOrEmpty(model.Admins_PowerNo) ||
                string.IsNullOrEmpty(model.Admins_Account))
            {
                MiniResponse.ResponseResult("参数错误", false, null);
                return;
            }

            //由于手机号与邮箱有可能被他人恶意使用,固在次暂不判断手机号与邮箱唯一；当需要判断时，将下方语句添加至where条件中即可
            // or Admins_Phone='{admin.Admins_Phone}' or Admins_Email='{admin.Admins_Email}'
            var sltAdmin = Admins.GetEntity("Admins_Account,Admins_Phone,Admins_Email", $"Admins_Account=@Admins_Account", new { Admins_Account = model.Admins_Account });
            if (sltAdmin != null)
            {
                var errmsg = string.Empty;
                if (sltAdmin.Admins_Account == model.Admins_Account) errmsg = "账号已被注册";
                //else if (!string.IsNullOrEmpty(sltAdmin.Admins_Phone) && sltAdmin.Admins_Phone == model.Admins_Phone) errmsg = "手机号已被注册";
                //else if (!string.IsNullOrEmpty(sltAdmin.Admins_Email) && sltAdmin.Admins_Email == model.Admins_Email) errmsg = "邮箱号码已被注册";

                MiniResponse.ResponseResult(errmsg, false);
                return;
            }

            if (!string.IsNullOrEmpty(model.Admins_Pwd))
            {
                model.Admins_Pwd = RSAHelper.Decrypt(model.Admins_Pwd);
            }

            if (AppBasicCache.CurrentSysConfigContent.SysConfig_PwdType == 1)
            {
                if (!Utils.IsStrongPassword(model.Admins_Pwd)) { MiniResponse.ResponseResult("您的密码为弱密码，存在安全隐患，请修改", false, null); return; }
            }

            model.Admins_PowerType = 0;
            model.Admins_Pwd = TyziTools.ApiSign.MD5Encrypt(model.Admins_Pwd + Utils.passwordMD5String, Encoding.UTF8);
            model.Admins_Enable = 1;
            model.Admins_AddID = lgAdmins.Admins_ID;
            model.Admins_AddTime = DateTimeHelper.GetNowTime();

            var newId = Admins.Add(model);
            if (newId > 0)
            {
                rb.Success = true;
                rb.Code = "0000";
                rb.Message = "保存成功";

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"新增账号成功：{model.Admins_ID},{model.Admins_Name},{model.Admins_PowerNo}", SecondIndex.Admins);

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer) model.Admins_PasswayNo = null;
                Push(Model.API.PushAction.Add, model, dataType: DataTypeEnum.Admins, Desc: $"新增{model.Admins_Account}");
            }

            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
        catch (Exception ex)
        {
            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Insert, $"新增账号失败:{ex}", SecondIndex.Admins);
            MiniResponse.ResponseResult("异常错误", false);
        }
    }

    /// <summary>
    /// 更新
    /// </summary>
    public void UpdateAdmins(string jsonModel)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Update.ToString(), adminSession: lgAdmins))
                return;

            var model = Utils.ClearModelRiskSQL<Model.Admins>(jsonModel);
            if (model == null) { MiniResponse.ResponseResult("参数错误", false, null); return; }

            if (string.IsNullOrEmpty(model.Admins_PowerNo) ||
                string.IsNullOrEmpty(model.Admins_Account) ||
                model.Admins_ID == null)
            {
                MiniResponse.ResponseResult("参数错误", false, null);
                return;
            }

            if (!string.IsNullOrEmpty(model.Admins_Pwd))
            {
                model.Admins_Pwd = RSAHelper.Decrypt(model.Admins_Pwd);
            }

            if (model.Admins_Pwd.Equals("******")) model.Admins_Pwd = null; //清空假密码

            var oldAdmins = Admins.GetEntity(model.Admins_ID);
            if (oldAdmins == null || model.Admins_Account != oldAdmins.Admins_Account)
            {
                MiniResponse.ResponseResult("异常操作", false, null);
                return;
            }

            if (model.Admins_Pwd != null)
            {
                if (AppBasicCache.CurrentSysConfigContent.SysConfig_PwdType == 1)
                {
                    if (!Utils.IsStrongPassword(model.Admins_Pwd)) { MiniResponse.ResponseResult("您的密码为弱密码，存在安全隐患，请修改", false, null); return; }
                }

                model.Admins_Pwd = TyziTools.ApiSign.MD5Encrypt(model.Admins_Pwd + Utils.passwordMD5String, Encoding.UTF8);
            }

            var newModel = oldAdmins.Copy();
            newModel = Utils.Returnobj(newModel, model);

            if (Admins.UpdateByModel(newModel) > 0)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"修改账号[{model.Admins_Account}]:" + LogHelper.GetChanges(oldAdmins, model), SecondIndex.Admins);

                if (AppSettingConfig.SentryMode == VersionEnum.CloudServer) newModel.Admins_PasswayNo = null;
                Push(Model.API.PushAction.Edit, newModel, dataType: DataTypeEnum.Admins, Desc: $"更新{model.Admins_Account}");

                MiniResponse.ResponseResult("保存成功", true, null);
            }
            else
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Update, $"失败[{model.Admins_Account}]:" + LogHelper.GetChanges(oldAdmins, model), SecondIndex.Admins);
            }
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "修改账号", "更新管理员发生异常:" + ex);
            MiniResponse.ResponseResult("异常错误", false);
        }
    }

    /// <summary>
    /// 启用管理员
    /// </summary>
    /// <param name="Admins_ID"></param>
    public void EnableAdmins(int Admins_ID)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Enable.ToString(), adminSession: lgAdmins))
                return;

            var model = Admins.GetEntity(Admins_ID);
            model.Admins_Enable = 1;

            if (Admins.UpdateByModel(model) == 1)
            {
                rb.Success = true;
                rb.Message = "启用成功";
                rb.Data = "";
                rb.Code = "0000";

                Push(Model.API.PushAction.Edit, model, dataType: DataTypeEnum.Admins, Desc: $"启用{model.Admins_Account}");
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Enable, $"启用账号成功:[" + model.Admins_Account + "]", SecondIndex.Admins);
            }
            else
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Enable, $"失败[{model.Admins_Account}]", SecondIndex.Admins);
                rb.Success = false;
                rb.Code = "1000"; //1000 SQL类错误
                rb.Message = "Exception";
            }

            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "启用账号", "启用管理员发生异常:" + ex);
            rb.Success = false;
            rb.Code = "1000"; //1000 SQL类错误
            rb.Message = "Exception";
            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
    }

    /// <summary>
    /// 禁用管理员
    /// </summary>
    /// <param name="Admins_ID"></param>
    public void DisableAdmins(int Admins_ID)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Disable.ToString(), adminSession: lgAdmins))
                return;

            if (Admins_ID == lgAdmins.Admins_ID)
            {
                MiniResponse.ResponseResult("不能禁用当前登录账号", false, null);
                return;
            }

            var model = Admins.GetEntity(Admins_ID);
            if (model.Admins_AddID == 0)
            {
                MiniResponse.ResponseResult("默认账号不能禁用", false);
                return;
            }

            model.Admins_Enable = 0;

            if (Admins.UpdateByModel(model) == 1)
            {
                #region 更新缓存

                //Model.Admins admin = BLL.Admins.GetEntity(Admins_ID);
                //Model.PowerGroup pg = BLL.PowerGroup.GetEntity(admin.Admins_PowerNo);
                //Model.AdminSession adminSession = new Model.AdminSession();
                //adminSession.Admins_Account = admin.Admins_Account;
                //adminSession.Admins_ID = admin.Admins_ID;
                //adminSession.Admins_Name = admin.Admins_Name;
                //adminSession.PowerGroup_ID = pg.PowerGroup_ID;
                //adminSession.PowerGroup_Name = pg.PowerGroup_Name;
                //adminSession.PowerGroup_Value = pg.PowerGroup_Value;
                ////adminSession.Admins_IPAddress = Request.UserHostAddress;
                //adminSession.token = Guid.NewGuid().ToString();
                ////adminSession.Admins_CustomerID = admin.Admins_CustomerID;
                //adminSession.Admins_Enable = admin.Admins_Enable;
                //Sessions.SetAdminSession(adminSession);

                #endregion

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Disable, $"禁用账号成功:[" + model.Admins_Account + "]", SecondIndex.Admins);

                rb.Success = true;
                rb.Message = "禁用成功";
                rb.Data = "";
                rb.Code = "0000";
                //UserLogs.AddLog(lgAdmins, LogEnum.Backend, "禁用账号", "禁用账号成功:[" + model.Admins_Account + "]");

                Push(Model.API.PushAction.Edit, model, dataType: DataTypeEnum.Admins, Desc: $"禁用{model.Admins_Account}");
            }
            else
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Disable, $"失败[{model.Admins_Account}]", SecondIndex.Admins);

                rb.Success = false;
                rb.Code = "1000"; //1000 SQL类错误
                rb.Message = "Exception";
            }

            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "禁用账号", "禁用管理员发生异常:" + ex);
            rb.Success = false;
            rb.Code = "1000"; //1000 SQL类错误
            rb.Message = "Exception";
            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
    }

    /// <summary>
    /// 删除管理员
    /// </summary>
    /// <param name="Admins_ID"></param>
    public void DeleteAdmins(int Admins_ID)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Delete.ToString(), adminSession: lgAdmins))
                return;

            var model = Admins.GetEntity(Admins_ID);
            if (model == null)
            {
                MiniResponse.ResponseResult("账号不存在", false);
                return;
            }

            if (model.Admins_AddID == 0)
            {
                MiniResponse.ResponseResult("默认账号禁止删除", false);
                return;
            }

            if (model.Admins_Account == lgAdmins.Admins_Account)
            {
                MiniResponse.ResponseResult("不能删除当前登录账号", false);
                return;
            }

            if (Admins.Delete(Admins_ID) == 1)
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"成功:[" + model.Admins_Account + "]", SecondIndex.Admins);
                rb.Success = true;
                rb.Message = "保存成功";
                rb.Data = "";
                rb.Code = "0000";
                //UserLogs.AddLog(lgAdmins, LogEnum.Backend, "删除账号", "删除账号成功:" + JsonConvert.SerializeObject(model));

                Push(Model.API.PushAction.Delete, model, dataType: DataTypeEnum.Admins, Desc: $"删除{model.Admins_Account}");
            }
            else
            {
                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Delete, $"失败[{model.Admins_Account}]", SecondIndex.Admins);
                rb.Success = false;
                rb.Code = "1000"; //1000 SQL类错误
                rb.Message = "Exception";
            }

            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, LogEnum.Backend, "删除账号", "删除管理员发生异常:" + ex);
            rb.Success = false;
            rb.Code = "1000"; //1000 SQL类错误
            rb.Message = "Exception";
            Response.WriteAsync(JsonConvert.SerializeObject(rb));
        }
    }

    public void GetPasswayList(int pageIndex, int pageSize, string conditionParam)
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.Bind.ToString(), adminSession: lgAdmins))
            return;

        try
        {
            var sqlwhere = string.Empty;
            var jo = Utils.ClearModelRiskSQL(conditionParam);
            if (!jo.ContainsKey("Admins_ID") || string.IsNullOrEmpty(jo["Admins_ID"].ToString()))
            {
                wayModel.code = 4;
                wayModel.msg = "账号不存在";
            }
            else
            {
                var model = Admins.GetEntity(Convert.ToInt32(jo["Admins_ID"]));
                var wayNos = TyziTools.Json.ToObject<List<string>>(model?.Admins_PasswayNo ?? "[]");
                sqlwhere += $" AND Passway_No in ('{string.Join("','", wayNos)}') ";
                int pageCount = 0, totalRecord = 0;
                var lst = BLL.Passway.GetExtList("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord);

                #region 车道关联区域信息

                foreach (var item in lst)
                {
                    item.AreaPassways = PasswayLink.GetAreaPassway(item.Passway_No)?.OrderBy(x => x.PasswayLink_GateType).ToList();
                }

                #endregion

                wayModel.code = 0;
                wayModel.data = lst;
                wayModel.count = totalRecord;
            }
        }
        catch (Exception ex)
        {
            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"异常:{ex}", SecondIndex.Admins);
            wayModel.code = 4;
            wayModel.msg = "异常错误";
        }

        Response.WriteAsync(wayModel.ParseJson());
    }

    /// <summary>
    /// 获取所有车道（传参Admins_ID时，获取未绑定的车道）
    /// </summary>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <param name="conditionParam"></param>
    public void GetPasswayAll(int pageIndex, int pageSize, string conditionParam)
    {
        if (!Powermanage.PowerCheck("Admins", PowerEnum.Bind.ToString(), adminSession: lgAdmins))
            return;

        try
        {
            var sqlwhere = string.Empty;
            var jo = Utils.ClearModelRiskSQL(conditionParam);
            if (jo == null) { MiniResponse.ResponseResult("参数错误", false, null); return; }
            if (jo.ContainsKey("Admins_ID") && !string.IsNullOrEmpty(jo["Admins_ID"].ToString()))
            {
                var model = Admins.GetEntity(Convert.ToInt32(jo["Admins_ID"]));
                var wayNos = TyziTools.Json.ToObject<List<string>>(model?.Admins_PasswayNo ?? "[]");
                sqlwhere += $" AND Passway_No not in ('{string.Join("','", wayNos)}') ";
            }

            int pageCount = 0, totalRecord = 0;
            var lst = BLL.Passway.GetExtList("*", sqlwhere, pageIndex, pageSize, out pageCount, out totalRecord);

            #region 车道关联区域信息

            foreach (var item in lst)
            {
                item.AreaPassways = PasswayLink.GetAreaPassway(item.Passway_No)?.OrderBy(x => x.PasswayLink_GateType).ToList();
            }

            #endregion

            wayModel.code = 0;
            wayModel.data = lst;
            wayModel.count = totalRecord;
        }
        catch (Exception ex)
        {
            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Search, $"异常:{ex}", SecondIndex.Admins);
            wayModel.code = 4;
            wayModel.msg = "异常错误";
        }

        Response.WriteAsync(wayModel.ParseJson());
    }

    /// <summary>
    /// 绑定车道
    /// </summary>
    /// <param name="jsonNo"></param>
    /// <returns></returns>
    public IActionResult BindPassway(string Admins_IDs, string jsonNo)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Bind.ToString(), adminSession: lgAdmins))
                return Ok(oModel);

            Admins_IDs = System.Web.HttpUtility.UrlDecode(Admins_IDs);
            var IDs = Admins_IDs.Split(',').ToList();
            IDs?.RemoveAll(x => string.IsNullOrEmpty(x.Trim()));
            if (IDs.Count == 0) return ResOk(false, "账号错误");

            var admins = Admins.GetAllEntity("*", $"Admins_ID in @Admins_IDs", new { Admins_IDs = IDs });
            if (admins == null || admins.Count == 0) return ResOk(false, "账号ID不存在");

            var update = admins.Copy();
            update.ForEach(x =>
            {
                var bindNo = TyziTools.Json.ToObject<List<string>>(x.Admins_PasswayNo ?? "[]") ?? new List<string>();
                var nbindNo = TyziTools.Json.ToObject<List<string>>(jsonNo) ?? new List<string>();

                bindNo.AddRange(nbindNo);
                bindNo = bindNo.Distinct().ToList();

                x.Admins_PasswayNo = TyziTools.Json.ToString(bindNo);
            });

            var res = BaseBLL._Insert(update);
            if (res > 0)
            {
                Push(Model.API.PushAction.Edit, update);

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Bind, $"成功:[" + string.Join(",", admins.Select(x => x.Admins_Account)) + $"],车道={jsonNo}", SecondIndex.Admins);
                return ResOk(true, "绑定成功");
            }

            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.Bind, $"失败:[" + string.Join(",", admins.Select(x => x.Admins_Account)) + $"],车道={jsonNo}", SecondIndex.Admins);

            return ResOk(false, "绑定失败");
        }
        catch (Exception ex)
        {
            SystemLogs.AddLog(lgAdmins, "绑定车道异常", ex.ToString());
            return ResOk(false, ex.Message);
        }
    }

    /// <summary>
    /// 解绑车道
    /// </summary>
    /// <param name="Admins_ID"></param>
    /// <param name="jsonNo"></param>
    /// <returns></returns>
    public IActionResult UnBindPassway(int Admins_ID, string jsonNo)
    {
        try
        {
            if (!Powermanage.PowerCheck("Admins", PowerEnum.Bind.ToString(), adminSession: lgAdmins))
                return Ok(oModel);

            var model = Admins.GetEntity(Admins_ID);
            if (model == null) return ResOk(false, "账号不存在");

            var bindNo = TyziTools.Json.ToObject<List<string>>(model.Admins_PasswayNo ?? "[]");
            var unbindNo = TyziTools.Json.ToObject<List<string>>(jsonNo);

            bindNo.RemoveAll(x => unbindNo.Contains(x));

            model.Admins_PasswayNo = TyziTools.Json.ToString(bindNo);

            var res = Admins.UpdateByModel(model);
            if (res > 0)
            {
                Push(Model.API.PushAction.Edit, model, dataType: DataTypeEnum.Admins, Desc: $"解绑车道{model.Admins_Account}");

                BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.UnBind, $"成功:[" + model.Admins_Account + $"],车道={jsonNo}", SecondIndex.Admins);
                return ResOk(true, "解绑成功");
            }

            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.UnBind, $"失败:[" + model.Admins_Account + $"],车道={jsonNo}", SecondIndex.Admins);
            return ResOk(false, "解绑失败");
        }
        catch (Exception ex)
        {
            BLL.UserLogs.AddLog(lgAdmins, LogEnum.Backend, SecondOption.UnBind, $"异常:{ex}", SecondIndex.Admins);
            return ResOk(false, ex.Message);
        }
    }
}